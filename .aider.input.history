
# 2025-05-31 03:23:47.472882
+y

# 2025-05-31 03:23:55.998067
+n

# 2025-05-31 03:28:39.952027
+this is prisma. implement this in Drizzle ORM instead {// Prisma Schema for Knowledge OS
+generator client {
+  provider = "prisma-client-js"
+}
+
+datasource db {
+  provider = "postgresql"
+  url      = env("DATABASE_URL")
+}
+
+model User {
+  id                String              @id @default(cuid())
+  name              String
+  email             String              @unique
+  profileImage      String?
+  role              UserRole            @default(USER)
+  createdAt         DateTime            @default(now())
+  updatedAt         DateTime            @updatedAt
+  knowledgeBases    KnowledgeBase[]
+  agents            Agent[]
+  sessions          Session[]
+  queries           Query[]
+  teamMemberships   TeamMembership[]
+}
+
+model KnowledgeBase {
+  id          String        @id @default(cuid())
+  name        String
+  description String?
+  createdAt   DateTime      @default(now())
+  updatedAt   DateTime      @updatedAt
+  ownerId     String
+  owner       User          @relation(fields: [ownerId], references: [id], onDelete: Cascade)
+  documents   Document[]
+  agents      Agent[]
+  teamId      String?
+  team        Team?         @relation(fields: [teamId], references: [id])
+}
+
+model Document {
+  id              String        @id @default(cuid())
+  title           String
+  content         String
+  contentType     ContentType   @default(TEXT)
+  vectorId        String?       @unique
+  createdAt       DateTime      @default(now())
+  updatedAt       DateTime      @updatedAt
+  knowledgeBaseId String
+  knowledgeBase   KnowledgeBase @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)
+  chunks          Chunk[]
+  metadata        Json?
+}
+
+model Chunk {
+  id          String   @id @default(cuid())
+  content     String
+  vectorId    String   @unique
+  startIndex  Int
+  endIndex    Int
+  createdAt   DateTime @default(now())
+  updatedAt   DateTime @updatedAt
+  documentId  String
+  document    Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
+  embeddings  Float[]
+}
+
+model Agent {
+  id              String         @id @default(cuid())
+  name            String
+  description     String?
+  type            AgentType      @default(ASSISTANT)
+  capabilities    String[]
+  config          Json
+  createdAt       DateTime       @default(now())
+  updatedAt       DateTime       @updatedAt
+  ownerId         String
+  owner           User           @relation(fields: [ownerId], references: [id], onDelete: Cascade)
+  knowledgeBaseId String?
+  knowledgeBase   KnowledgeBase? @relation(fields: [knowledgeBaseId], references: [id])
+  sessions        Session[]
+}
+
+model Session {
+  id          String    @id @default(cuid())
+  name        String?
+  createdAt   DateTime  @default(now())
+  updatedAt   DateTime  @updatedAt
+  userId      String
+  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
+  agentId     String
+  agent       Agent     @relation(fields: [agentId], references: [id], onDelete: Cascade)
+  queries     Query[]
+}
+
+model Query {
+  id          String    @id @default(cuid())
+  content     String
+  response    String
+  metadata    Json?
+  createdAt   DateTime  @default(now())
+  sessionId   String
+  session     Session   @relation(fields: [sessionId], references: [id], onDelete: Cascade)
+  userId      String
+  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
+}
+
+model Team {
+  id              String            @id @default(cuid())
+  name            String
+  description     String?
+  createdAt       DateTime          @default(now())
+  updatedAt       DateTime          @updatedAt
+  memberships     TeamMembership[]
+  knowledgeBases  KnowledgeBase[]
+}
+
+model TeamMembership {
+  id        String      @id @default(cuid())
+  role      TeamRole    @default(MEMBER)
+  teamId    String
+  team      Team        @relation(fields: [teamId], references: [id], onDelete: Cascade)
+  userId    String
+  user      User        @relation(fields: [userId], references: [id], onDelete: Cascade)
+  createdAt DateTime    @default(now())
+  updatedAt DateTime    @updatedAt
+
+  @@unique([teamId, userId])
+}
+
+enum UserRole {
+  USER
+  ADMIN
+}
+
+enum TeamRole {
+  MEMBER
+  EDITOR
+  ADMIN
+  OWNER
+}
+
+enum ContentType {
+  TEXT
+  MARKDOWN
+  HTML
+  PDF
+  CODE
+  JSON
+}
+
+enum AgentType {
+  ASSISTANT
+  RESEARCHER
+  CODER
+  ANALYST
+  CREATIVE
+  CUSTOM
+}
