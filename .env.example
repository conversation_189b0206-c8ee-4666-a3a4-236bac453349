# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/knowledge_os"

# Next.js Configuration
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"

# AI Provider API Keys (Primary)
OPENROUTER_API_KEY="your-openrouter-api-key"
GOOGLE_API_KEY="your-google-gemini-api-key"
GROQ_API_KEY="your-groq-api-key"
CEREBRAS_API_KEY="your-cerebras-api-key"
CHUTES_API_KEY="your-chutes-api-key"
MISTRAL_API_KEY="your-mistral-api-key"

# AI Provider API Keys (Optional - for future users)
# OPENAI_API_KEY="your-openai-api-key"
# ANTHROPIC_API_KEY="your-anthropic-api-key"

# Optional: Vector Database (if using)
PINECONE_API_KEY="your-pinecone-api-key"
PINECONE_ENVIRONMENT="your-pinecone-environment"
