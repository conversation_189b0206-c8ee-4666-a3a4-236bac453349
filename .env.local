# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/knowledge_os"

# Next.js Configuration
NEXTAUTH_SECRET="l8cSXe2C(#3y&uk7LA5s"n&QlnYU@7Z.'bG-0#.vWfubnwQd,%k\`H+}jmNbY\%D8I!E[:op$IfpD=3!}rb*06.M?ioNe>:gRJ5}\&0dKh^Bc/h,BN*/SD}@bQ":9;q8"
NEXTAUTH_URL="http://localhost:3000"

# Optional: External API Keys
OPENROUTER_API_KEY="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"
GOOGLE_API_KEY="AIzaSyCrxLYiM2YPzFbDPEnjbwe_BZ1pMEwjWyY"
GROQ_API_KEY="********************************************************"
CEREBRAS_API_KEY="csk-y4ckvy6fpxnjp5m3mvh8f4pddpp8vxf8m9wr4ccthrk9vhyy"
MISTRAL_API_KEY="sk-ZN5f2x1P2rK+68MCOEkxgNr4B03IYbGzR2DbLOGt"
CHUTES_API_KEY="cpk_6174c1a8d93c4914899e7301ef8aa6ab.f8ebcaa34ae85d7e980c66b959020119.pjaaLfgCuPLXZ60pmXLFpk2xmH88DVhV"
#OPENAI_API_KEY="your-openai-api-key"
#ANTHROPIC_API_KEY="your-anthropic-api-key"

# Optional: Vector Database (if using)
PINECONE_API_KEY="pcsk_4JfPK_8hAcYUweycbp8q9RCqL5zTU2RWeKKtxGrxLtNTkxNsCRcvMYL2R1W66yVKw9iVE"
PINECONE_ENVIRONMENT="your-pinecone-environment"
