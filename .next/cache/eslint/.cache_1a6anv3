[{"/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/layout.tsx": "1", "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/network/page.tsx": "2", "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/page.tsx": "3", "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/agent-card.tsx": "4", "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/agent-network-graph-simple.tsx": "5", "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/navigation.tsx": "6", "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/ui/badge.tsx": "7", "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/ui/button.tsx": "8", "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/ui/card.tsx": "9", "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/lib/network-data.ts": "10"}, {"size": 795, "mtime": 1748678557301, "results": "11", "hashOfConfig": "12"}, {"size": 9488, "mtime": 1748678780841, "results": "13", "hashOfConfig": "12"}, {"size": 3073, "mtime": 1748678576035, "results": "14", "hashOfConfig": "12"}, {"size": 2837, "mtime": 1748678015665, "results": "15", "hashOfConfig": "12"}, {"size": 10584, "mtime": 1748678775172, "results": "16", "hashOfConfig": "12"}, {"size": 1441, "mtime": 1748678529171, "results": "17", "hashOfConfig": "12"}, {"size": 1078, "mtime": 1748677963506, "results": "18", "hashOfConfig": "12"}, {"size": 1603, "mtime": 1748677957881, "results": "19", "hashOfConfig": "12"}, {"size": 1789, "mtime": 1748677951003, "results": "20", "hashOfConfig": "12"}, {"size": 7974, "mtime": 1748678440206, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "oidx6m", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 24, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/layout.tsx", [], [], "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/network/page.tsx", ["52", "53", "54"], [], "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/page.tsx", [], [], "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/agent-card.tsx", [], [], "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/agent-network-graph-simple.tsx", ["55", "56", "57", "58", "59", "60", "61", "62", "63", "64", "65", "66", "67", "68", "69", "70", "71", "72", "73", "74", "75", "76", "77", "78"], [], "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/navigation.tsx", [], [], "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/ui/badge.tsx", [], [], "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/ui/button.tsx", [], [], "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/ui/card.tsx", [], [], "/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/lib/network-data.ts", [], [], {"ruleId": "79", "severity": 2, "message": "80", "line": 3, "column": 20, "nodeType": null, "messageId": "81", "endLine": 3, "endColumn": 29}, {"ruleId": "79", "severity": 2, "message": "82", "line": 26, "column": 10, "nodeType": null, "messageId": "81", "endLine": 26, "endColumn": 22}, {"ruleId": "79", "severity": 2, "message": "83", "line": 27, "column": 10, "nodeType": null, "messageId": "81", "endLine": 27, "endColumn": 21}, {"ruleId": "84", "severity": 2, "message": "85", "line": 77, "column": 54, "nodeType": "86", "messageId": "87", "endLine": 77, "endColumn": 57, "suggestions": "88"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 110, "column": 27, "nodeType": "86", "messageId": "87", "endLine": 110, "endColumn": 30, "suggestions": "89"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 119, "column": 33, "nodeType": "86", "messageId": "87", "endLine": 119, "endColumn": 36, "suggestions": "90"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 120, "column": 31, "nodeType": "86", "messageId": "87", "endLine": 120, "endColumn": 34, "suggestions": "91"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 134, "column": 22, "nodeType": "86", "messageId": "87", "endLine": 134, "endColumn": 25, "suggestions": "92"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 135, "column": 25, "nodeType": "86", "messageId": "87", "endLine": 135, "endColumn": 28, "suggestions": "93"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 146, "column": 43, "nodeType": "86", "messageId": "87", "endLine": 146, "endColumn": 46, "suggestions": "94"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 155, "column": 42, "nodeType": "86", "messageId": "87", "endLine": 155, "endColumn": 45, "suggestions": "95"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 164, "column": 39, "nodeType": "86", "messageId": "87", "endLine": 164, "endColumn": 42, "suggestions": "96"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 174, "column": 30, "nodeType": "86", "messageId": "87", "endLine": 174, "endColumn": 33, "suggestions": "97"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 177, "column": 17, "nodeType": "86", "messageId": "87", "endLine": 177, "endColumn": 20, "suggestions": "98"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 189, "column": 23, "nodeType": "86", "messageId": "87", "endLine": 189, "endColumn": 26, "suggestions": "99"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 195, "column": 17, "nodeType": "86", "messageId": "87", "endLine": 195, "endColumn": 20, "suggestions": "100"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 199, "column": 17, "nodeType": "86", "messageId": "87", "endLine": 199, "endColumn": 20, "suggestions": "101"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 204, "column": 25, "nodeType": "86", "messageId": "87", "endLine": 204, "endColumn": 28, "suggestions": "102"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 205, "column": 25, "nodeType": "86", "messageId": "87", "endLine": 205, "endColumn": 28, "suggestions": "103"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 206, "column": 25, "nodeType": "86", "messageId": "87", "endLine": 206, "endColumn": 28, "suggestions": "104"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 207, "column": 25, "nodeType": "86", "messageId": "87", "endLine": 207, "endColumn": 28, "suggestions": "105"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 209, "column": 34, "nodeType": "86", "messageId": "87", "endLine": 209, "endColumn": 37, "suggestions": "106"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 218, "column": 33, "nodeType": "86", "messageId": "87", "endLine": 218, "endColumn": 36, "suggestions": "107"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 218, "column": 38, "nodeType": "86", "messageId": "87", "endLine": 218, "endColumn": 41, "suggestions": "108"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 219, "column": 39, "nodeType": "86", "messageId": "87", "endLine": 219, "endColumn": 42, "suggestions": "109"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 225, "column": 38, "nodeType": "86", "messageId": "87", "endLine": 225, "endColumn": 41, "suggestions": "110"}, {"ruleId": "84", "severity": 2, "message": "85", "line": 229, "column": 37, "nodeType": "86", "messageId": "87", "endLine": 229, "endColumn": 40, "suggestions": "111"}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "unusedVar", "'selectedNode' is assigned a value but never used.", "'hoveredNode' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["112", "113"], ["114", "115"], ["116", "117"], ["118", "119"], ["120", "121"], ["122", "123"], ["124", "125"], ["126", "127"], ["128", "129"], ["130", "131"], ["132", "133"], ["134", "135"], ["136", "137"], ["138", "139"], ["140", "141"], ["142", "143"], ["144", "145"], ["146", "147"], ["148", "149"], ["150", "151"], ["152", "153"], ["154", "155"], ["156", "157"], ["158", "159"], {"messageId": "160", "fix": "161", "desc": "162"}, {"messageId": "163", "fix": "164", "desc": "165"}, {"messageId": "160", "fix": "166", "desc": "162"}, {"messageId": "163", "fix": "167", "desc": "165"}, {"messageId": "160", "fix": "168", "desc": "162"}, {"messageId": "163", "fix": "169", "desc": "165"}, {"messageId": "160", "fix": "170", "desc": "162"}, {"messageId": "163", "fix": "171", "desc": "165"}, {"messageId": "160", "fix": "172", "desc": "162"}, {"messageId": "163", "fix": "173", "desc": "165"}, {"messageId": "160", "fix": "174", "desc": "162"}, {"messageId": "163", "fix": "175", "desc": "165"}, {"messageId": "160", "fix": "176", "desc": "162"}, {"messageId": "163", "fix": "177", "desc": "165"}, {"messageId": "160", "fix": "178", "desc": "162"}, {"messageId": "163", "fix": "179", "desc": "165"}, {"messageId": "160", "fix": "180", "desc": "162"}, {"messageId": "163", "fix": "181", "desc": "165"}, {"messageId": "160", "fix": "182", "desc": "162"}, {"messageId": "163", "fix": "183", "desc": "165"}, {"messageId": "160", "fix": "184", "desc": "162"}, {"messageId": "163", "fix": "185", "desc": "165"}, {"messageId": "160", "fix": "186", "desc": "162"}, {"messageId": "163", "fix": "187", "desc": "165"}, {"messageId": "160", "fix": "188", "desc": "162"}, {"messageId": "163", "fix": "189", "desc": "165"}, {"messageId": "160", "fix": "190", "desc": "162"}, {"messageId": "163", "fix": "191", "desc": "165"}, {"messageId": "160", "fix": "192", "desc": "162"}, {"messageId": "163", "fix": "193", "desc": "165"}, {"messageId": "160", "fix": "194", "desc": "162"}, {"messageId": "163", "fix": "195", "desc": "165"}, {"messageId": "160", "fix": "196", "desc": "162"}, {"messageId": "163", "fix": "197", "desc": "165"}, {"messageId": "160", "fix": "198", "desc": "162"}, {"messageId": "163", "fix": "199", "desc": "165"}, {"messageId": "160", "fix": "200", "desc": "162"}, {"messageId": "163", "fix": "201", "desc": "165"}, {"messageId": "160", "fix": "202", "desc": "162"}, {"messageId": "163", "fix": "203", "desc": "165"}, {"messageId": "160", "fix": "204", "desc": "162"}, {"messageId": "163", "fix": "205", "desc": "165"}, {"messageId": "160", "fix": "206", "desc": "162"}, {"messageId": "163", "fix": "207", "desc": "165"}, {"messageId": "160", "fix": "208", "desc": "162"}, {"messageId": "163", "fix": "209", "desc": "165"}, {"messageId": "160", "fix": "210", "desc": "162"}, {"messageId": "163", "fix": "211", "desc": "165"}, "suggestUnknown", {"range": "212", "text": "213"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "214", "text": "215"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "216", "text": "213"}, {"range": "217", "text": "215"}, {"range": "218", "text": "213"}, {"range": "219", "text": "215"}, {"range": "220", "text": "213"}, {"range": "221", "text": "215"}, {"range": "222", "text": "213"}, {"range": "223", "text": "215"}, {"range": "224", "text": "213"}, {"range": "225", "text": "215"}, {"range": "226", "text": "213"}, {"range": "227", "text": "215"}, {"range": "228", "text": "213"}, {"range": "229", "text": "215"}, {"range": "230", "text": "213"}, {"range": "231", "text": "215"}, {"range": "232", "text": "213"}, {"range": "233", "text": "215"}, {"range": "234", "text": "213"}, {"range": "235", "text": "215"}, {"range": "236", "text": "213"}, {"range": "237", "text": "215"}, {"range": "238", "text": "213"}, {"range": "239", "text": "215"}, {"range": "240", "text": "213"}, {"range": "241", "text": "215"}, {"range": "242", "text": "213"}, {"range": "243", "text": "215"}, {"range": "244", "text": "213"}, {"range": "245", "text": "215"}, {"range": "246", "text": "213"}, {"range": "247", "text": "215"}, {"range": "248", "text": "213"}, {"range": "249", "text": "215"}, {"range": "250", "text": "213"}, {"range": "251", "text": "215"}, {"range": "252", "text": "213"}, {"range": "253", "text": "215"}, {"range": "254", "text": "213"}, {"range": "255", "text": "215"}, {"range": "256", "text": "213"}, {"range": "257", "text": "215"}, {"range": "258", "text": "213"}, {"range": "259", "text": "215"}, {"range": "260", "text": "213"}, {"range": "261", "text": "215"}, [2184, 2187], "unknown", [2184, 2187], "never", [3350, 3353], [3350, 3353], [3650, 3653], [3650, 3653], [3712, 3715], [3712, 3715], [4119, 4122], [4119, 4122], [4207, 4210], [4207, 4210], [4598, 4601], [4598, 4601], [4896, 4899], [4896, 4899], [5197, 5200], [5197, 5200], [5449, 5452], [5449, 5452], [5583, 5586], [5583, 5586], [5900, 5903], [5900, 5903], [6176, 6179], [6176, 6179], [6270, 6273], [6270, 6273], [6461, 6464], [6461, 6464], [6505, 6508], [6505, 6508], [6549, 6552], [6549, 6552], [6593, 6596], [6593, 6596], [6656, 6659], [6656, 6659], [6875, 6878], [6875, 6878], [6880, 6883], [6880, 6883], [6925, 6928], [6925, 6928], [7122, 7125], [7122, 7125], [7222, 7225], [7222, 7225]]