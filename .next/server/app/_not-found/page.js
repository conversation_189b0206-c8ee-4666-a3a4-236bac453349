(()=>{var e={};e.id=492,e.ids=[492],e.modules={788:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(1863).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/navigation.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1716:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>l});var n=r(1227),s=r(8180),o=r(4950),i=r.n(o),a=r(433),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,2434,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,9139)),"/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,2434,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,1779,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,3784,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],u={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},2463:()=>{},2659:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,5904,23)),Promise.resolve().then(r.t.bind(r,9350,23)),Promise.resolve().then(r.t.bind(r,4950,23)),Promise.resolve().then(r.t.bind(r,1745,23)),Promise.resolve().then(r.t.bind(r,9445,23)),Promise.resolve().then(r.t.bind(r,6103,23)),Promise.resolve().then(r.t.bind(r,1158,23)),Promise.resolve().then(r.t.bind(r,4876,23))},2849:(e,t,r)=>{"use strict";r.d(t,{$:()=>s});var n=r(5393);let s=r(5908).forwardRef(({className:e,variant:t="default",size:r="default",...s},o)=>(0,n.jsx)("button",{className:`inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"}[t]} ${{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[r]} ${e||""}`,ref:o,...s}));s.displayName="Button"},2907:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6960,23)),Promise.resolve().then(r.t.bind(r,4750,23)),Promise.resolve().then(r.t.bind(r,7150,23)),Promise.resolve().then(r.t.bind(r,7745,23)),Promise.resolve().then(r.t.bind(r,8917,23)),Promise.resolve().then(r.t.bind(r,6879,23)),Promise.resolve().then(r.t.bind(r,9630,23)),Promise.resolve().then(r.t.bind(r,2732,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},6484:(e,t,r)=>{Promise.resolve().then(r.bind(r,788))},6532:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var n=r(5393),s=r(4332),o=r.n(s),i=r(6259),a=r(2849);function d(){let e=(0,i.usePathname)();return(0,n.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm border-b",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-8 py-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,n.jsx)(o(),{href:"/",className:"text-xl font-bold text-gray-900 dark:text-white",children:"AI Agent Hub"}),(0,n.jsx)("div",{className:"flex space-x-4",children:[{href:"/",label:"Dashboard",icon:"\uD83C\uDFE0"},{href:"/network",label:"Network Graph",icon:"\uD83C\uDF10"}].map(t=>(0,n.jsx)(o(),{href:t.href,children:(0,n.jsxs)(a.$,{variant:e===t.href?"default":"ghost",className:"flex items-center gap-2",children:[(0,n.jsx)("span",{children:t.icon}),t.label]})},t.href))})]}),(0,n.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-300",children:"AI Agent Network Visualization"})]})})})}},7924:(e,t,r)=>{Promise.resolve().then(r.bind(r,6532))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9139:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>l});var n=r(3089),s=r(9044),o=r.n(s),i=r(7778),a=r.n(i);r(2463);var d=r(788);let l={title:"AI Agent Network Hub",description:"Interactive AI Agent Dashboard and Network Visualization"};function c({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsxs)("body",{className:`${o().variable} ${a().variable} antialiased`,children:[(0,n.jsx)(d.default,{}),e]})})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[163,436],()=>r(1716));module.exports=n})();