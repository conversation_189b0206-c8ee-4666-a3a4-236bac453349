<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/569ce4b8f30dc480-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/_next/static/media/93f479601ee12b01-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/2c0219244c082411.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-7389310be2e43b85.js"/><script src="/_next/static/chunks/748859cf-60a3273472781c12.js" async=""></script><script src="/_next/static/chunks/702-148151ee7063bae1.js" async=""></script><script src="/_next/static/chunks/main-app-36328e156e4b0673.js" async=""></script><script src="/_next/static/chunks/app/layout-68b6f41ac305d4e1.js" async=""></script><script src="/_next/static/chunks/270-7a1075732532aea8.js" async=""></script><script src="/_next/static/chunks/app/network/page-e73a20cdade81d98.js" async=""></script><meta name="next-size-adjust" content=""/><title>AI Agent Network Hub</title><meta name="description" content="Interactive AI Agent Dashboard and Network Visualization"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased"><nav class="bg-white dark:bg-gray-800 shadow-sm border-b"><div class="max-w-7xl mx-auto px-8 py-4"><div class="flex items-center justify-between"><div class="flex items-center space-x-8"><a class="text-xl font-bold text-gray-900 dark:text-white" href="/">AI Agent Hub</a><div class="flex space-x-4"><a href="/"><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 flex items-center gap-2"><span>🏠</span>Dashboard</button></a><a href="/network"><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 flex items-center gap-2"><span>🌐</span>Network Graph</button></a></div></div><div class="text-sm text-gray-600 dark:text-gray-300">AI Agent Network Visualization</div></div></div></nav><div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8"><div class="max-w-7xl mx-auto space-y-8"><div class="text-center"><h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">AI Agent Network Visualization</h1><p class="text-lg text-gray-600 dark:text-gray-300 mb-6">Explore the interconnected network of AI agents and their relationships</p><div class="flex justify-center gap-4 mb-6"><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 ">Simple Network</button><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 ">Full Network</button><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 ">Dynamic Network</button><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-secondary text-secondary-foreground hover:bg-secondary/80 h-10 px-4 py-2 ">Reset</button></div></div><div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"><div class="rounded-lg border bg-card text-card-foreground shadow-sm "><div class="flex flex-col space-y-1.5 p-6 pb-2"><h3 class="text-2xl font-semibold leading-none tracking-tight text-sm font-medium">Total Nodes</h3></div><div class="p-6 pt-0 "><div class="text-2xl font-bold">5</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm "><div class="flex flex-col space-y-1.5 p-6 pb-2"><h3 class="text-2xl font-semibold leading-none tracking-tight text-sm font-medium">Total Connections</h3></div><div class="p-6 pt-0 "><div class="text-2xl font-bold">6</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm "><div class="flex flex-col space-y-1.5 p-6 pb-2"><h3 class="text-2xl font-semibold leading-none tracking-tight text-sm font-medium">Active Agents</h3></div><div class="p-6 pt-0 "><div class="text-2xl font-bold">0</div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm "><div class="flex flex-col space-y-1.5 p-6 pb-2"><h3 class="text-2xl font-semibold leading-none tracking-tight text-sm font-medium">Network Type</h3></div><div class="p-6 pt-0 "><div class="text-lg font-semibold capitalize">simple</div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm w-full"><div class="flex flex-col space-y-1.5 p-6 "><div class="flex items-center justify-between"><h3 class="text-2xl font-semibold leading-none tracking-tight text-2xl font-bold">AI Agent Network</h3><div class="flex gap-2"><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 ">Reset Selection</button><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 ">Center View</button></div></div><div class="flex gap-2 mt-2"><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground bg-indigo-50 text-indigo-700">🤖 Primary Agents</div><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground bg-green-50 text-green-700">⚡ Secondary Agents</div><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground bg-red-50 text-red-700">🌐 Hub Nodes</div></div></div><div class="p-6 pt-0 "><div class="relative"><svg class="border rounded-lg shadow-lg"></svg><div class="absolute bottom-4 left-4"><div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-primary text-primary-foreground hover:bg-primary/80 ">Simulation Running</div></div></div></div></div><div class="grid grid-cols-1 lg:grid-cols-2 gap-6"><div class="rounded-lg border bg-card text-card-foreground shadow-sm "><div class="flex flex-col space-y-1.5 p-6 "><h3 class="text-2xl font-semibold leading-none tracking-tight ">Connection Types</h3><p class="text-sm text-muted-foreground ">Different types of relationships between agents</p></div><div class="p-6 pt-0 space-y-4"><div class="flex items-center gap-3"><div class="w-4 h-1 bg-green-500 rounded"></div><div><p class="font-medium">Collaboration</p><p class="text-sm text-gray-600 dark:text-gray-300">Agents working together on tasks</p></div></div><div class="flex items-center gap-3"><div class="w-4 h-1 bg-blue-500 rounded"></div><div><p class="font-medium">Data Flow</p><p class="text-sm text-gray-600 dark:text-gray-300">Information and data exchange</p></div></div><div class="flex items-center gap-3"><div class="w-4 h-1 bg-yellow-500 rounded"></div><div><p class="font-medium">Dependency</p><p class="text-sm text-gray-600 dark:text-gray-300">Service dependencies and requirements</p></div></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm "><div class="flex flex-col space-y-1.5 p-6 "><h3 class="text-2xl font-semibold leading-none tracking-tight ">Active Agents</h3><p class="text-sm text-muted-foreground ">Currently active agents in the network</p></div><div class="p-6 pt-0 "><p class="text-gray-600 dark:text-gray-300">No active agents in this network type</p></div></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm "><div class="flex flex-col space-y-1.5 p-6 "><h3 class="text-2xl font-semibold leading-none tracking-tight ">How to Use</h3></div><div class="p-6 pt-0 space-y-2"><p>• <strong>Click and drag</strong> nodes to reposition them</p><p>• <strong>Hover</strong> over nodes to see details</p><p>• <strong>Click</strong> nodes to select and highlight connections</p><p>• <strong>Zoom and pan</strong> to explore different areas</p><p>• <strong>Switch network types</strong> to see different configurations</p></div></div></div></div><script src="/_next/static/chunks/webpack-7389310be2e43b85.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[8650,[\"177\",\"static/chunks/app/layout-68b6f41ac305d4e1.js\"],\"default\"]\n3:I[7397,[],\"\"]\n4:I[8513,[],\"\"]\n5:I[4204,[],\"ClientPageRoot\"]\n6:I[8087,[\"270\",\"static/chunks/270-7a1075732532aea8.js\",\"707\",\"static/chunks/app/network/page-e73a20cdade81d98.js\"],\"default\"]\n9:I[3514,[],\"OutletBoundary\"]\nc:I[3514,[],\"ViewportBoundary\"]\ne:I[3514,[],\"MetadataBoundary\"]\n10:I[1612,[],\"\"]\n:HL[\"/_next/static/media/569ce4b8f30dc480-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/media/93f479601ee12b01-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/css/2c0219244c082411.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"frsPgbz0pMw4lh3NUEw0i\",\"p\":\"\",\"c\":[\"\",\"network\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"network\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/2c0219244c082411.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased\",\"children\":[[\"$\",\"$L2\",null,{}],[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"$undefined\",[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}]}]]}],{\"children\":[\"network\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L5\",null,{\"Component\":\"$6\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@7\",\"$@8\"]}],\"$undefined\",null,[\"$\",\"$L9\",null,{\"children\":[\"$La\",\"$Lb\",null]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"7PxuAZ5bVF6h3iKKmeaKQ\",{\"children\":[[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],[\"$\",\"$Le\",null,{\"children\":\"$Lf\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$10\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"7:{}\n8:{}\n"])</script><script>self.__next_f.push([1,"d:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\na:null\n"])</script><script>self.__next_f.push([1,"b:null\nf:[[\"$\",\"title\",\"0\",{\"children\":\"AI Agent Network Hub\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Interactive AI Agent Dashboard and Network Visualization\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]]\n"])</script></body></html>