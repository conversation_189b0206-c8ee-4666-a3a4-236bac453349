(()=>{var t={};t.id=974,t.ids=[974],t.modules={788:(t,e,i)=>{"use strict";i.d(e,{default:()=>s});let s=(0,i(1863).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/navigation.tsx","default")},846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2463:()=>{},2659:(t,e,i)=>{Promise.resolve().then(i.t.bind(i,5904,23)),Promise.resolve().then(i.t.bind(i,9350,23)),Promise.resolve().then(i.t.bind(i,4950,23)),Promise.resolve().then(i.t.bind(i,1745,23)),Promise.resolve().then(i.t.bind(i,9445,23)),Promise.resolve().then(i.t.bind(i,6103,23)),Promise.resolve().then(i.t.bind(i,1158,23)),Promise.resolve().then(i.t.bind(i,4876,23))},2849:(t,e,i)=>{"use strict";i.d(e,{$:()=>n});var s=i(5393);let n=i(5908).forwardRef(({className:t,variant:e="default",size:i="default",...n},r)=>(0,s.jsx)("button",{className:`inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"}[e]} ${{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[i]} ${t||""}`,ref:r,...n}));n.displayName="Button"},2907:(t,e,i)=>{Promise.resolve().then(i.t.bind(i,6960,23)),Promise.resolve().then(i.t.bind(i,4750,23)),Promise.resolve().then(i.t.bind(i,7150,23)),Promise.resolve().then(i.t.bind(i,7745,23)),Promise.resolve().then(i.t.bind(i,8917,23)),Promise.resolve().then(i.t.bind(i,6879,23)),Promise.resolve().then(i.t.bind(i,9630,23)),Promise.resolve().then(i.t.bind(i,2732,23))},3033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3768:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>s});let s=(0,i(1863).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/page.tsx","default")},3850:(t,e,i)=>{"use strict";let s;i.r(e),i.d(e,{default:()=>rD});var n,r,a=i(5393),o=i(5908);function l(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function h(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function u(t,e,i,s){if("function"==typeof e){let[n,r]=h(s);e=e(void 0!==i?i:t.custom,n,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,r]=h(s);e=e(void 0!==i?i:t.custom,n,r)}return e}function d(t,e,i){let s=t.getProps();return u(s,e,void 0!==i?i:s.custom,t)}function c(t,e){return t?.[e]??t?.default??t}let p=t=>t,m={},f=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],g={value:null,addProjectionMetrics:null};function v(t,e){let i=!1,s=!0,n={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,a=f.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,s=new Set,n=!1,r=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function h(e){a.has(e)&&(u.schedule(e),t()),l++,e(o)}let u={schedule:(t,e=!1,r=!1)=>{let o=r&&n?i:s;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{s.delete(t),a.delete(t)},process:t=>{if(o=t,n){r=!0;return}n=!0,[i,s]=[s,i],i.forEach(h),e&&g.value&&g.value.frameloop[e].push(l),l=0,i.clear(),n=!1,r&&(r=!1,u.process(t))}};return u}(r,e?i:void 0),t),{}),{setup:o,read:l,resolveKeyframes:h,preUpdate:u,update:d,preRender:c,render:p,postRender:v}=a,y=()=>{let r=m.useManualTiming?n.timestamp:performance.now();i=!1,m.useManualTiming||(n.delta=s?1e3/60:Math.max(Math.min(r-n.timestamp,40),1)),n.timestamp=r,n.isProcessing=!0,o.process(n),l.process(n),h.process(n),u.process(n),d.process(n),c.process(n),p.process(n),v.process(n),n.isProcessing=!1,i&&e&&(s=!1,t(y))},x=()=>{i=!0,s=!0,n.isProcessing||t(y)};return{schedule:f.reduce((t,e)=>{let s=a[e];return t[e]=(t,e=!1,n=!1)=>(i||x(),s.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<f.length;e++)a[f[e]].cancel(t)},state:n,steps:a}}let{schedule:y,cancel:x,state:b,steps:w}=v("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:p,!0),T=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],P=new Set(T),A=new Set(["width","height","top","left","right","bottom",...T]);function S(t,e){-1===t.indexOf(e)&&t.push(e)}function M(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class E{constructor(){this.subscriptions=[]}add(t){return S(this.subscriptions,t),()=>M(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s){if(1===s)this.subscriptions[0](t,e,i);else for(let n=0;n<s;n++){let s=this.subscriptions[n];s&&s(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function C(){s=void 0}let V={now:()=>(void 0===s&&V.set(b.isProcessing||m.useManualTiming?b.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(C)}},D=t=>!isNaN(parseFloat(t)),k={current:void 0};class j{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=V.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=V.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=D(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new E);let i=this.events[t].add(e);return"change"===t?()=>{i(),y.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return k.current&&k.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=V.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function R(t,e){return new j(t,e)}let F=t=>Array.isArray(t),L=t=>!!(t&&t.getVelocity);function B(t,e){let i=t.getValue("willChange");if(L(i)&&i.add)return i.add(e);if(!i&&m.WillChange){let i=new m.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let N=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),O="data-"+N("framerAppearId"),I=(t,e)=>i=>e(t(i)),U=(...t)=>t.reduce(I),$=(t,e,i)=>i>e?e:i<t?t:i,W=t=>1e3*t,_=t=>t/1e3,z={layout:0,mainThread:0,waapi:0},Y=()=>{},H=()=>{},X=t=>e=>"string"==typeof e&&e.startsWith(t),q=X("--"),K=X("var(--"),G=t=>!!K(t)&&Z.test(t.split("/*")[0].trim()),Z=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,J={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},Q={...J,transform:t=>$(0,1,t)},tt={...J,default:1},te=t=>Math.round(1e5*t)/1e5,ti=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,ts=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tn=(t,e)=>i=>!!("string"==typeof i&&ts.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tr=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[n,r,a,o]=s.match(ti);return{[t]:parseFloat(n),[e]:parseFloat(r),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},ta=t=>$(0,255,t),to={...J,transform:t=>Math.round(ta(t))},tl={test:tn("rgb","red"),parse:tr("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+to.transform(t)+", "+to.transform(e)+", "+to.transform(i)+", "+te(Q.transform(s))+")"},th={test:tn("#"),parse:function(t){let e="",i="",s="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}},transform:tl.transform},tu=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),td=tu("deg"),tc=tu("%"),tp=tu("px"),tm=tu("vh"),tf=tu("vw"),tg={...tc,parse:t=>tc.parse(t)/100,transform:t=>tc.transform(100*t)},tv={test:tn("hsl","hue"),parse:tr("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:s=1})=>"hsla("+Math.round(t)+", "+tc.transform(te(e))+", "+tc.transform(te(i))+", "+te(Q.transform(s))+")"},ty={test:t=>tl.test(t)||th.test(t)||tv.test(t),parse:t=>tl.test(t)?tl.parse(t):tv.test(t)?tv.parse(t):th.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tl.transform(t):tv.transform(t)},tx=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tb="number",tw="color",tT=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tP(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},n=[],r=0,a=e.replace(tT,t=>(ty.test(t)?(s.color.push(r),n.push(tw),i.push(ty.parse(t))):t.startsWith("var(")?(s.var.push(r),n.push("var"),i.push(t)):(s.number.push(r),n.push(tb),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:a,indexes:s,types:n}}function tA(t){return tP(t).values}function tS(t){let{split:e,types:i}=tP(t),s=e.length;return t=>{let n="";for(let r=0;r<s;r++)if(n+=e[r],void 0!==t[r]){let e=i[r];e===tb?n+=te(t[r]):e===tw?n+=ty.transform(t[r]):n+=t[r]}return n}}let tM=t=>"number"==typeof t?0:t,tE={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(ti)?.length||0)+(t.match(tx)?.length||0)>0},parse:tA,createTransformer:tS,getAnimatableNone:function(t){let e=tA(t);return tS(t)(e.map(tM))}};function tC(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tV(t,e){return i=>i>0?e:t}let tD=(t,e,i)=>t+(e-t)*i,tk=(t,e,i)=>{let s=t*t,n=i*(e*e-s)+s;return n<0?0:Math.sqrt(n)},tj=[th,tl,tv],tR=t=>tj.find(e=>e.test(t));function tF(t){let e=tR(t);if(Y(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tv&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let n=0,r=0,a=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,o=2*i-s;n=tC(o,s,t+1/3),r=tC(o,s,t),a=tC(o,s,t-1/3)}else n=r=a=i;return{red:Math.round(255*n),green:Math.round(255*r),blue:Math.round(255*a),alpha:s}}(i)),i}let tL=(t,e)=>{let i=tF(t),s=tF(e);if(!i||!s)return tV(t,e);let n={...i};return t=>(n.red=tk(i.red,s.red,t),n.green=tk(i.green,s.green,t),n.blue=tk(i.blue,s.blue,t),n.alpha=tD(i.alpha,s.alpha,t),tl.transform(n))},tB=new Set(["none","hidden"]);function tN(t,e){return i=>tD(t,e,i)}function tO(t){return"number"==typeof t?tN:"string"==typeof t?G(t)?tV:ty.test(t)?tL:t$:Array.isArray(t)?tI:"object"==typeof t?ty.test(t)?tL:tU:tV}function tI(t,e){let i=[...t],s=i.length,n=t.map((t,i)=>tO(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=n[e](t);return i}}function tU(t,e){let i={...t,...e},s={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(s[n]=tO(t[n])(t[n],e[n]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let t$=(t,e)=>{let i=tE.createTransformer(e),s=tP(t),n=tP(e);return s.indexes.var.length===n.indexes.var.length&&s.indexes.color.length===n.indexes.color.length&&s.indexes.number.length>=n.indexes.number.length?tB.has(t)&&!n.values.length||tB.has(e)&&!s.values.length?function(t,e){return tB.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):U(tI(function(t,e){let i=[],s={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let r=e.types[n],a=t.indexes[r][s[r]],o=t.values[a]??0;i[n]=o,s[r]++}return i}(s,n),n.values),i):(Y(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tV(t,e))};function tW(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tD(t,e,i):tO(t)(t,e)}let t_=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>y.update(e,t),stop:()=>x(e),now:()=>b.isProcessing?b.timestamp:V.now()}},tz=(t,e,i=10)=>{let s="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)s+=t(e/(n-1))+", ";return`linear(${s.substring(0,s.length-2)})`};function tY(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tH(t,e,i){var s,n;let r=Math.max(e-5,0);return s=i-t(r),(n=e-r)?1e3/n*s:0}let tX={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tq(t,e){return t*Math.sqrt(1-e*e)}let tK=["duration","bounce"],tG=["stiffness","damping","mass"];function tZ(t,e){return e.some(e=>void 0!==t[e])}function tJ(t=tX.visualDuration,e=tX.bounce){let i;let s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:r}=s,a=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],l={done:!1,value:a},{stiffness:h,damping:u,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:tX.velocity,stiffness:tX.stiffness,damping:tX.damping,mass:tX.mass,isResolvedFromDuration:!1,...t};if(!tZ(t,tG)&&tZ(t,tK)){if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,n=2*$(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:tX.mass,stiffness:s,damping:n}}else{let i=function({duration:t=tX.duration,bounce:e=tX.bounce,velocity:i=tX.velocity,mass:s=tX.mass}){let n,r;Y(t<=W(tX.maxDuration),"Spring duration must be 10 seconds or less");let a=1-e;a=$(tX.minDamping,tX.maxDamping,a),t=$(tX.minDuration,tX.maxDuration,_(t)),a<1?(n=e=>{let s=e*a,n=s*t;return .001-(s-i)/tq(e,a)*Math.exp(-n)},r=e=>{let s=e*a*t,r=Math.pow(a,2)*Math.pow(e,2)*t,o=Math.exp(-s),l=tq(Math.pow(e,2),a);return(s*i+i-r)*o*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let o=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(n,r,5/t);if(t=W(t),isNaN(o))return{stiffness:tX.stiffness,damping:tX.damping,duration:t};{let e=Math.pow(o,2)*s;return{stiffness:e,damping:2*a*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:tX.mass}).isResolvedFromDuration=!0}}return e}({...s,velocity:-_(s.velocity||0)}),f=p||0,g=u/(2*Math.sqrt(h*d)),v=o-a,y=_(Math.sqrt(h/d)),x=5>Math.abs(v);if(n||(n=x?tX.restSpeed.granular:tX.restSpeed.default),r||(r=x?tX.restDelta.granular:tX.restDelta.default),g<1){let t=tq(y,g);i=e=>o-Math.exp(-g*y*e)*((f+g*y*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}else if(1===g)i=t=>o-Math.exp(-y*t)*(v+(f+y*v)*t);else{let t=y*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*y*e),s=Math.min(t*e,300);return o-i*((f+g*y*v)*Math.sinh(s)+t*v*Math.cosh(s))/t}}let b={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let s=0===t?f:0;g<1&&(s=0===t?W(f):tH(i,t,e));let a=Math.abs(s)<=n,h=Math.abs(o-e)<=r;l.done=a&&h}return l.value=l.done?o:e,l},toString:()=>{let t=Math.min(tY(b),2e4),e=tz(e=>b.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return b}function tQ({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:n=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:h=.5,restSpeed:u}){let d,c;let p=t[0],m={done:!1,value:p},f=t=>void 0!==o&&t<o||void 0!==l&&t>l,g=t=>void 0===o?l:void 0===l?o:Math.abs(o-t)<Math.abs(l-t)?o:l,v=i*e,y=p+v,x=void 0===a?y:a(y);x!==y&&(v=x-p);let b=t=>-v*Math.exp(-t/s),w=t=>x+b(t),T=t=>{let e=b(t),i=w(t);m.done=Math.abs(e)<=h,m.value=m.done?x:i},P=t=>{f(m.value)&&(d=t,c=tJ({keyframes:[m.value,g(m.value)],velocity:tH(w,t,m.value),damping:n,stiffness:r,restDelta:h,restSpeed:u}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,T(t),P(t)),void 0!==d&&t>=d)?c.next(t-d):(e||T(t),m)}}}tJ.applyToOptions=t=>{let e=function(t,e=100,i){let s=i({...t,keyframes:[0,e]}),n=Math.min(tY(s),2e4);return{type:"keyframes",ease:t=>s.next(n*t).value/e,duration:_(n)}}(t,100,tJ);return t.ease=e.ease,t.duration=W(e.duration),t.type="keyframes",t};let t0=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function t1(t,e,i,s){if(t===e&&i===s)return p;let n=e=>(function(t,e,i,s,n){let r,a;let o=0;do(r=t0(a=e+(i-e)/2,s,n)-t)>0?i=a:e=a;while(Math.abs(r)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:t0(n(t),e,s)}let t3=t1(.42,0,1,1),t2=t1(0,0,.58,1),t5=t1(.42,0,.58,1),t9=t=>Array.isArray(t)&&"number"!=typeof t[0],t8=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t6=t=>e=>1-t(1-e),t4=t1(.33,1.53,.69,.99),t7=t6(t4),et=t8(t7),ee=t=>(t*=2)<1?.5*t7(t):.5*(2-Math.pow(2,-10*(t-1))),ei=t=>1-Math.sin(Math.acos(t)),es=t6(ei),en=t8(ei),er=t=>Array.isArray(t)&&"number"==typeof t[0],ea={linear:p,easeIn:t3,easeInOut:t5,easeOut:t2,circIn:ei,circInOut:en,circOut:es,backIn:t7,backInOut:et,backOut:t4,anticipate:ee},eo=t=>"string"==typeof t,el=t=>{if(er(t)){H(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,n]=t;return t1(e,i,s,n)}return eo(t)?(H(void 0!==ea[t],`Invalid easing type '${t}'`),ea[t]):t},eh=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s};function eu({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){let n=t9(s)?s.map(el):el(s),r={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:s,mixer:n}={}){let r=t.length;if(H(r===e.length,"Both input and output ranges must be the same length"),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];let a=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,i){let s=[],n=i||m.mix||tW,r=t.length-1;for(let i=0;i<r;i++){let r=n(t[i],t[i+1]);e&&(r=U(Array.isArray(e)?e[i]||p:e,r)),s.push(r)}return s}(e,s,n),l=o.length,h=i=>{if(a&&i<t[0])return e[0];let s=0;if(l>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let n=eh(t[s],t[s+1],i);return o[s](n)};return i?e=>h($(t[0],t[r-1],e)):h}((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let n=eh(0,e,s);t.push(tD(i,1,n))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(n)?n:e.map(()=>n||t5).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(r.value=a(e),r.done=e>=t,r)}}let ed=t=>null!==t;function ec(t,{repeat:e,repeatType:i="loop"},s,n=1){let r=t.filter(ed),a=n<0||e&&"loop"!==i&&e%2==1?0:r.length-1;return a&&void 0!==s?s:r[a]}let ep={decay:tQ,inertia:tQ,tween:eu,keyframes:eu,spring:tJ};function em(t){"string"==typeof t.type&&(t.type=ep[t.type])}class ef{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let eg=t=>t/100;class ev extends ef{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==V.now()&&this.tick(V.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},z.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;em(t);let{type:e=eu,repeat:i=0,repeatDelay:s=0,repeatType:n,velocity:r=0}=t,{keyframes:a}=t,o=e||eu;o!==eu&&"number"!=typeof a[0]&&(this.mixKeyframes=U(eg,tW(a[0],a[1])),a=[0,100]);let l=o({...t,keyframes:a});"mirror"===n&&(this.mirroredGenerator=o({...t,keyframes:[...a].reverse(),velocity:-r})),null===l.calculatedDuration&&(l.calculatedDuration=tY(l));let{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:s,mixKeyframes:n,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:h,repeat:u,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>s;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let y=this.currentTime,x=i;if(u){let t=Math.min(this.currentTime,s)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,u+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/a)):"mirror"===d&&(x=r)),y=$(0,1,i)*a}let b=v?{done:!1,value:h[0]}:x.next(y);n&&(b.value=n(b.value));let{done:w}=b;v||null===o||(w=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return T&&p!==tQ&&(b.value=ec(h,this.options,f,this.speed)),m&&m(b.value),T&&this.finish(),b}then(t,e){return this.finished.then(t,e)}get duration(){return _(this.calculatedDuration)}get time(){return _(this.currentTime)}set time(t){t=W(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(V.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=_(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=t_,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(V.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,z.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let ey=t=>180*t/Math.PI,ex=t=>ew(ey(Math.atan2(t[1],t[0]))),eb={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ex,rotateZ:ex,skewX:t=>ey(Math.atan(t[1])),skewY:t=>ey(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ew=t=>((t%=360)<0&&(t+=360),t),eT=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),eP=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),eA={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:eT,scaleY:eP,scale:t=>(eT(t)+eP(t))/2,rotateX:t=>ew(ey(Math.atan2(t[6],t[5]))),rotateY:t=>ew(ey(Math.atan2(-t[2],t[0]))),rotateZ:ex,rotate:ex,skewX:t=>ey(Math.atan(t[4])),skewY:t=>ey(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function eS(t){return+!!t.includes("scale")}function eM(t,e){let i,s;if(!t||"none"===t)return eS(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=eA,s=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=eb,s=e}if(!s)return eS(e);let r=i[e],a=s[1].split(",").map(eC);return"function"==typeof r?r(a):a[r]}let eE=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eM(i,e)};function eC(t){return parseFloat(t.trim())}let eV=t=>t===J||t===tp,eD=new Set(["x","y","z"]),ek=T.filter(t=>!eD.has(t)),ej={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eM(e,"x"),y:(t,{transform:e})=>eM(e,"y")};ej.translateX=ej.x,ej.translateY=ej.y;let eR=new Set,eF=!1,eL=!1,eB=!1;function eN(){if(eL){let t=Array.from(eR).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ek.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eL=!1,eF=!1,eR.forEach(t=>t.complete(eB)),eR.clear()}function eO(){eR.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eL=!0)})}class eI{constructor(t,e,i,s,n,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=n,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(eR.add(this),eF||(eF=!0,y.read(eO),y.resolveKeyframes(eN))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;if(null===t[0]){let n=s?.get(),r=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let s=i.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===n&&s.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eR.delete(this)}cancel(){"scheduled"===this.state&&(eR.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eU=t=>t.startsWith("--");function e$(t){let e;return()=>(void 0===e&&(e=t()),e)}let eW=e$(()=>void 0!==window.ScrollTimeline),e_={},ez=function(t,e){let i=e$(t);return()=>e_[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eY=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,eH={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eY([0,.65,.55,1]),circOut:eY([.55,0,1,.45]),backIn:eY([.31,.01,.66,-.59]),backOut:eY([.33,1.53,.69,.99])};function eX(t){return"function"==typeof t&&"applyToOptions"in t}class eq extends ef{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:s,pseudoElement:n,allowFlatten:r=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!n,this.allowFlatten=r,this.options=t,H("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return eX(t)&&ez()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:s=0,duration:n=300,repeat:r=0,repeatType:a="loop",ease:o="easeOut",times:l}={},h){let u={[e]:i};l&&(u.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?ez()?tz(e,i):"ease-out":er(e)?eY(e):Array.isArray(e)?e.map(e=>t(e,i)||eH.easeOut):eH[e]}(o,n);Array.isArray(d)&&(u.easing=d),g.value&&z.waapi++;let c={delay:s,duration:n,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:r+1,direction:"reverse"===a?"alternate":"normal"};h&&(c.pseudoElement=h);let p=t.animate(u,c);return g.value&&p.finished.finally(()=>{z.waapi--}),p}(e,i,s,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=ec(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eU(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return _(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return _(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=W(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eW())?(this.animation.timeline=t,p):e(this)}}let eK={anticipate:ee,backInOut:et,circInOut:en};class eG extends eq{constructor(t){(function(t){"string"==typeof t.ease&&t.ease in eK&&(t.ease=eK[t.ease])})(t),em(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:s,element:n,...r}=this.options;if(!e)return;if(void 0!==t){e.set(t);return}let a=new ev({...r,autoplay:!1}),o=W(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let eZ=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tE.test(t)||"0"===t)&&!t.startsWith("url("));function eJ(t){return"object"==typeof t&&null!==t}function eQ(t){return eJ(t)&&"offsetHeight"in t}let e0=new Set(["opacity","clipPath","filter","transform"]),e1=e$(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class e3 extends ef{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:r="loop",keyframes:a,name:o,motionValue:l,element:h,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=V.now();let d={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:r,name:o,motionValue:l,element:h,...u},c=h?.KeyframeResolver||eI;this.keyframeResolver=new c(a,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),o,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,s){this.keyframeResolver=void 0;let{name:n,type:r,velocity:a,delay:o,isHandoff:l,onUpdate:h}=i;this.resolvedAt=V.now(),!function(t,e,i,s){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],a=eZ(n,e),o=eZ(r,e);return Y(a===o,`You are trying to animate ${e} from "${n}" to "${r}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${r} via the \`style\` property.`),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||eX(i))&&s)}(t,n,r,a)&&((m.instantAnimations||!o)&&h?.(ec(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let u={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:r,type:a}=t;if(!eQ(e?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return e1()&&i&&e0.has(i)&&("transform"!==i||!l)&&!o&&!s&&"mirror"!==n&&0!==r&&"inertia"!==a}(u)?new eG({...u,element:u.motionValue.owner.current}):new ev(u);d.finished.then(()=>this.notifyFinished()).catch(p),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eB=!0,eO(),eN(),eB=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e2=t=>null!==t,e5={type:"spring",stiffness:500,damping:25,restSpeed:10},e9=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e8={type:"keyframes",duration:.8},e6={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e4=(t,{keyframes:e})=>e.length>2?e8:P.has(t)?t.startsWith("scale")?e9(e[1]):e5:e6,e7=(t,e,i,s={},n,r)=>a=>{let o=c(s,t)||{},l=o.delay||s.delay||0,{elapsed:h=0}=s;h-=W(l);let u={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...o,delay:-h,onUpdate:t=>{e.set(t),o.onUpdate&&o.onUpdate(t)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:t,motionValue:e,element:r?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(o)&&Object.assign(u,e4(t,u)),u.duration&&(u.duration=W(u.duration)),u.repeatDelay&&(u.repeatDelay=W(u.repeatDelay)),void 0!==u.from&&(u.keyframes[0]=u.from);let d=!1;if(!1!==u.type&&(0!==u.duration||u.repeatDelay)||(u.duration=0,0!==u.delay||(d=!0)),(m.instantAnimations||m.skipAnimations)&&(d=!0,u.duration=0,u.delay=0),u.allowFlatten=!o.type&&!o.ease,d&&!r&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},s){let n=t.filter(e2),r=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[r]}(u.keyframes,o);if(void 0!==t){y.update(()=>{u.onUpdate(t),u.onComplete()});return}}return o.isSync?new ev(u):new e3(u)};function it(t,e,{delay:i=0,transitionOverride:s,type:n}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:a,...o}=e;s&&(r=s);let l=[],h=n&&t.animationState&&t.animationState.getState()[n];for(let e in o){let s=t.getValue(e,t.latestValues[e]??null),n=o[e];if(void 0===n||h&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(h,e))continue;let a={delay:i,...c(r||{},e)},u=s.get();if(void 0!==u&&!s.isAnimating&&!Array.isArray(n)&&n===u&&!a.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=t.props[O];if(i){let t=window.MotionHandoffAnimation(i,e,y);null!==t&&(a.startTime=t,d=!0)}}B(t,e),s.start(e7(e,s,n,t.shouldReduceMotion&&A.has(e)?{type:!1}:a,t,d));let p=s.animation;p&&l.push(p)}return a&&Promise.all(l).then(()=>{y.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:s={},...n}=d(t,e)||{};for(let e in n={...n,...i}){var r;let i=F(r=n[e])?r[r.length-1]||0:r;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,R(i))}}(t,a)})}),l}function ie(t,e,i={}){let s=d(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(n=i.transitionOverride);let r=s?()=>Promise.all(it(t,s,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:r=0,staggerChildren:a,staggerDirection:o}=n;return function(t,e,i=0,s=0,n=1,r){let a=[],o=(t.variantChildren.size-1)*s,l=1===n?(t=0)=>t*s:(t=0)=>o-t*s;return Array.from(t.variantChildren).sort(ii).forEach((t,s)=>{t.notify("AnimationStart",e),a.push(ie(t,e,{...r,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,r+s,a,o,i)}:()=>Promise.resolve(),{when:o}=n;if(!o)return Promise.all([r(),a(i.delay)]);{let[t,e]="beforeChildren"===o?[r,a]:[a,r];return t().then(()=>e())}}function ii(t,e){return t.sortNodePosition(e)}function is(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function ir(t){return"string"==typeof t||Array.isArray(t)}let ia=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],io=["initial",...ia],il=io.length,ih=[...ia].reverse(),iu=ia.length;function id(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ic(){return{animate:id(!0),whileInView:id(),whileHover:id(),whileTap:id(),whileDrag:id(),whileFocus:id(),exit:id()}}class ip{constructor(t){this.isMounted=!1,this.node=t}update(){}}class im extends ip{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>ie(t,e,i)));else if("string"==typeof e)s=ie(t,e,i);else{let n="function"==typeof e?d(t,e,i.custom):e;s=Promise.all(it(t,n,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=ic(),s=!0,n=e=>(i,s)=>{let n=d(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...s}=n;i={...i,...s,...e}}return i};function r(r){let{props:a}=t,o=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<il;t++){let s=io[t],n=e.props[s];(ir(n)||!1===n)&&(i[s]=n)}return i}(t.parent)||{},h=[],u=new Set,c={},p=1/0;for(let e=0;e<iu;e++){var m,f;let d=ih[e],g=i[d],v=void 0!==a[d]?a[d]:o[d],y=ir(v),x=d===r?g.isActive:null;!1===x&&(p=e);let b=v===o[d]&&v!==a[d]&&y;if(b&&s&&t.manuallyAnimateOnMount&&(b=!1),g.protectedKeys={...c},!g.isActive&&null===x||!v&&!g.prevProp||l(v)||"boolean"==typeof v)continue;let w=(m=g.prevProp,"string"==typeof(f=v)?f!==m:!!Array.isArray(f)&&!is(f,m)),T=w||d===r&&g.isActive&&!b&&y||e>p&&y,P=!1,A=Array.isArray(v)?v:[v],S=A.reduce(n(d),{});!1===x&&(S={});let{prevResolvedValues:M={}}=g,E={...M,...S},C=e=>{T=!0,u.has(e)&&(P=!0,u.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in E){let e=S[t],i=M[t];if(c.hasOwnProperty(t))continue;let s=!1;(F(e)&&F(i)?is(e,i):e===i)?void 0!==e&&u.has(t)?C(t):g.protectedKeys[t]=!0:null!=e?C(t):u.add(t)}g.prevProp=v,g.prevResolvedValues=S,g.isActive&&(c={...c,...S}),s&&t.blockInitialAnimation&&(T=!1);let V=!(b&&w)||P;T&&V&&h.push(...A.map(t=>({animation:t,options:{type:d}})))}if(u.size){let e={};if("boolean"!=typeof a.initial){let i=d(t,Array.isArray(a.initial)?a.initial[0]:a.initial);i&&i.transition&&(e.transition=i.transition)}u.forEach(i=>{let s=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=s??null}),h.push({animation:e})}let g=!!h.length;return s&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(g=!1),s=!1,g?e(h):Promise.resolve()}return{animateChanges:r,setActive:function(e,s){if(i[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,s)),i[e].isActive=s;let n=r(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=ic(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();l(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ig=0;class iv extends ip{constructor(){super(...arguments),this.id=ig++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let iy={x:!1,y:!1};function ix(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}let ib=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iw(t){return{point:{x:t.pageX,y:t.pageY}}}let iT=t=>e=>ib(e)&&t(e,iw(e));function iP(t,e,i,s){return ix(t,e,iT(i),s)}function iA({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function iS(t){return t.max-t.min}function iM(t,e,i,s=.5){t.origin=s,t.originPoint=tD(e.min,e.max,t.origin),t.scale=iS(i)/iS(e),t.translate=tD(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iE(t,e,i,s){iM(t.x,e.x,i.x,s?s.originX:void 0),iM(t.y,e.y,i.y,s?s.originY:void 0)}function iC(t,e,i){t.min=i.min+e.min,t.max=t.min+iS(e)}function iV(t,e,i){t.min=e.min-i.min,t.max=t.min+iS(e)}function iD(t,e,i){iV(t.x,e.x,i.x),iV(t.y,e.y,i.y)}let ik=()=>({translate:0,scale:1,origin:0,originPoint:0}),ij=()=>({x:ik(),y:ik()}),iR=()=>({min:0,max:0}),iF=()=>({x:iR(),y:iR()});function iL(t){return[t("x"),t("y")]}function iB(t){return void 0===t||1===t}function iN({scale:t,scaleX:e,scaleY:i}){return!iB(t)||!iB(e)||!iB(i)}function iO(t){return iN(t)||iI(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iI(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iU(t,e,i,s,n){return void 0!==n&&(t=s+n*(t-s)),s+i*(t-s)+e}function i$(t,e=0,i=1,s,n){t.min=iU(t.min,e,i,s,n),t.max=iU(t.max,e,i,s,n)}function iW(t,{x:e,y:i}){i$(t.x,e.translate,e.scale,e.originPoint),i$(t.y,i.translate,i.scale,i.originPoint)}function i_(t,e){t.min=t.min+e,t.max=t.max+e}function iz(t,e,i,s,n=.5){let r=tD(t.min,t.max,n);i$(t,e,i,r,s)}function iY(t,e){iz(t.x,e.x,e.scaleX,e.scale,e.originX),iz(t.y,e.y,e.scaleY,e.scale,e.originY)}function iH(t,e){return iA(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let iX=({current:t})=>t?t.ownerDocument.defaultView:null;function iq(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iK=(t,e)=>Math.abs(t-e);class iG{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iQ(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iK(t.x,e.x)**2+iK(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:n}=b;this.history.push({...s,timestamp:n});let{onStart:r,onMove:a}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iZ(e,this.transformPagePoint),y.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=iQ("pointercancel"===t.type?this.lastMoveEventInfo:iZ(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!ib(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let r=iZ(iw(t),this.transformPagePoint),{point:a}=r,{timestamp:o}=b;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=e;l&&l(t,iQ(r,this.history)),this.removeListeners=U(iP(this.contextWindow,"pointermove",this.handlePointerMove),iP(this.contextWindow,"pointerup",this.handlePointerUp),iP(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),x(this.updatePoint)}}function iZ(t,e){return e?{point:e(t.point)}:t}function iJ(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iQ({point:t},e){return{point:t,delta:iJ(t,i0(e)),offset:iJ(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,n=i0(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>W(.1)));)i--;if(!s)return{x:0,y:0};let r=_(n.timestamp-s.timestamp);if(0===r)return{x:0,y:0};let a={x:(n.x-s.x)/r,y:(n.y-s.y)/r};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function i0(t){return t[t.length-1]}function i1(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function i3(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function i2(t,e,i){return{min:i5(t,e),max:i5(t,i)}}function i5(t,e){return"number"==typeof t?t:t[e]||0}let i9=new WeakMap;class i8{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iF(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new iG(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iw(t).point)},onStart:(t,e)=>{var i;let{drag:s,dragPropagation:n,onDragStart:r}=this.getProps();if(s&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(i=s)||"y"===i?iy[i]?null:(iy[i]=!0,()=>{iy[i]=!1}):iy.x||iy.y?null:(iy.x=iy.y=!0,()=>{iy.x=iy.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iL(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tc.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=iS(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&y.postRender(()=>r(t,e)),B(this.visualElement,"transform");let{animationState:a}=this.visualElement;a&&a.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:n,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iL(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:iX(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:n}=this.getProps();n&&y.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!i6(t,s,this.currentDirection))return;let n=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?tD(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?tD(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),n.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&iq(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:s,right:n}){return{x:i1(t.x,i,n),y:i1(t.y,e,s)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i2(t,"left","right"),y:i2(t,"top","bottom")}}(e),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iL(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iq(e))return!1;let s=e.current;H(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let r=function(t,e,i){let s=iH(t,i),{scroll:n}=e;return n&&(i_(s.x,n.offset.x),i_(s.y,n.offset.y)),s}(s,n.root,this.visualElement.getTransformPagePoint()),a={x:i3((t=n.layout.layoutBox).x,r.x),y:i3(t.y,r.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=iA(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(iL(a=>{if(!i6(a,e,this.currentDirection))return;let l=o&&o[a]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[a]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(a,h)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return B(this.visualElement,t),i.start(e7(t,i,0,e,this.visualElement,!1))}stopAnimation(){iL(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iL(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iL(e=>{let{drag:i}=this.getProps();if(!i6(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,n=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];n.set(t[e]-tD(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iq(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};iL(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=iS(t),n=iS(e);return n>s?i=eh(e.min,e.max-s,t.min):s>n&&(i=eh(t.min,t.max-n,e.min)),$(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iL(e=>{if(!i6(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:r}=this.constraints[e];i.set(tD(n,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;i9.set(this.visualElement,this);let t=iP(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iq(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),y.read(e);let n=ix(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iL(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:n=!1,dragElastic:r=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:n,dragElastic:r,dragMomentum:a}}}function i6(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i4 extends ip{constructor(t){super(t),this.removeGroupControls=p,this.removeListeners=p,this.controls=new i8(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||p}unmount(){this.removeGroupControls(),this.removeListeners()}}let i7=t=>(e,i)=>{t&&y.postRender(()=>t(e,i))};class st extends ip{constructor(){super(...arguments),this.removePointerDownListener=p}onPointerDown(t){this.session=new iG(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iX(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:i7(t),onStart:i7(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&y.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=iP(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let{schedule:se}=v(queueMicrotask,!1),si=(0,o.createContext)(null),ss=(0,o.createContext)({}),sn=(0,o.createContext)({}),sr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function sa(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let so={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!tp.test(t))return t;t=parseFloat(t)}let i=sa(t,e.target.x),s=sa(t,e.target.y);return`${i}% ${s}%`}},sl={};class sh extends o.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:n}=t;(function(t){for(let e in t)sl[e]=t[e],q(e)&&(sl[e].isCSSVariable=!0)})(sd),n&&(e.group&&e.group.add(n),i&&i.register&&s&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),sr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:n}=this.props,{projection:r}=i;return r&&(r.isPresent=n,s||t.layoutDependency!==e||void 0===e||t.isPresent!==n?r.willUpdate():this.safeToRemove(),t.isPresent===n||(n?r.promote():r.relegate()||y.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),se.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function su(t){let[e,i]=function(t=!0){let e=(0,o.useContext)(si);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:s,register:n}=e,r=(0,o.useId)();(0,o.useEffect)(()=>{if(t)return n(r)},[t]);let a=(0,o.useCallback)(()=>t&&s&&s(r),[r,s,t]);return!i&&s?[!1,a]:[!0]}(),s=(0,o.useContext)(ss);return(0,a.jsx)(sh,{...t,layoutGroup:s,switchLayoutGroup:(0,o.useContext)(sn),isPresent:e,safeToRemove:i})}let sd={borderRadius:{...so,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:so,borderTopRightRadius:so,borderBottomLeftRadius:so,borderBottomRightRadius:so,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=tE.parse(t);if(s.length>5)return t;let n=tE.createTransformer(t),r=+("number"!=typeof s[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;s[0+r]/=a,s[1+r]/=o;let l=tD(a,o,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),n(s)}}};function sc(t){return eJ(t)&&"ownerSVGElement"in t}let sp=(t,e)=>t.depth-e.depth;class sm{constructor(){this.children=[],this.isDirty=!1}add(t){S(this.children,t),this.isDirty=!0}remove(t){M(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(sp),this.isDirty=!1,this.children.forEach(t)}}function sf(t){return L(t)?t.get():t}let sg=["TopLeft","TopRight","BottomLeft","BottomRight"],sv=sg.length,sy=t=>"string"==typeof t?parseFloat(t):t,sx=t=>"number"==typeof t||tp.test(t);function sb(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let sw=sP(0,.5,es),sT=sP(.5,.95,p);function sP(t,e,i){return s=>s<t?0:s>e?1:i(eh(t,e,s))}function sA(t,e){t.min=e.min,t.max=e.max}function sS(t,e){sA(t.x,e.x),sA(t.y,e.y)}function sM(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function sE(t,e,i,s,n){return t-=e,t=s+1/i*(t-s),void 0!==n&&(t=s+1/n*(t-s)),t}function sC(t,e,[i,s,n],r,a){!function(t,e=0,i=1,s=.5,n,r=t,a=t){if(tc.test(e)&&(e=parseFloat(e),e=tD(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=tD(r.min,r.max,s);t===r&&(o-=e),t.min=sE(t.min,e,i,o,n),t.max=sE(t.max,e,i,o,n)}(t,e[i],e[s],e[n],e.scale,r,a)}let sV=["x","scaleX","originX"],sD=["y","scaleY","originY"];function sk(t,e,i,s){sC(t.x,e,sV,i?i.x:void 0,s?s.x:void 0),sC(t.y,e,sD,i?i.y:void 0,s?s.y:void 0)}function sj(t){return 0===t.translate&&1===t.scale}function sR(t){return sj(t.x)&&sj(t.y)}function sF(t,e){return t.min===e.min&&t.max===e.max}function sL(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function sB(t,e){return sL(t.x,e.x)&&sL(t.y,e.y)}function sN(t){return iS(t.x)/iS(t.y)}function sO(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class sI{constructor(){this.members=[]}add(t){S(this.members,t),t.scheduleRender()}remove(t){if(M(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let sU={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},s$=["","X","Y","Z"],sW={visibility:"hidden"},s_=0;function sz(t,e,i,s){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function sY({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=s_++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,g.value&&(sU.nodes=sU.calculatedTargetDeltas=sU.calculatedProjections=0),this.nodes.forEach(sq),this.nodes.forEach(s1),this.nodes.forEach(s3),this.nodes.forEach(sK),g.addProjectionMetrics&&g.addProjectionMetrics(sU)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new sm)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new E),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=sc(e)&&!(sc(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:s,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let i;let s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=V.now(),s=({timestamp:n})=>{let r=n-i;r>=250&&(x(s),t(r-e))};return y.setup(s,!0),()=>x(s)}(s,250),sr.hasAnimatedSinceResize&&(sr.hasAnimatedSinceResize=!1,this.nodes.forEach(s0))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||n.getDefaultTransition()||s4,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=n.getProps(),l=!this.targetLayout||!sB(this.targetLayout,s),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...c(r,"layout"),onPlay:a,onComplete:o};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||s0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),x(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(s2),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[O];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",y,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(sZ);return}this.isUpdating||this.nodes.forEach(sJ),this.isUpdating=!1,this.nodes.forEach(sQ),this.nodes.forEach(sH),this.nodes.forEach(sX),this.clearAllSnapshots();let t=V.now();b.delta=$(0,1e3/60,t-b.timestamp),b.timestamp=t,b.isProcessing=!0,w.update.process(b),w.preRender.process(b),w.render.process(b),b.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,se.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(sG),this.sharedNodes.forEach(s5)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,y.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){y.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||iS(this.snapshot.measuredBox.x)||iS(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iF(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!sR(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&this.instance&&(e||iO(this.latestValues)||r)&&(n(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),ne((e=s).x),ne(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iF();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(ns))){let{scroll:t}=this.root;t&&(i_(e.x,t.offset.x),i_(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iF();if(sS(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:n,options:r}=s;s!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&sS(e,t),i_(e.x,n.offset.x),i_(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=iF();sS(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&iY(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),iO(s.latestValues)&&iY(i,s.latestValues)}return iO(this.latestValues)&&iY(i,this.latestValues),i}removeTransform(t){let e=iF();sS(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iO(i.latestValues))continue;iN(i.latestValues)&&i.updateSnapshot();let s=iF();sS(s,i.measurePageBox()),sk(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return iO(this.latestValues)&&sk(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==b.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:n}=this.options;if(this.layout&&(s||n)){if(this.resolvedRelativeTargetAt=b.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iF(),this.relativeTargetOrigin=iF(),iD(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),sS(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iF(),this.targetWithTransforms=iF()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,a,o;this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,o=this.relativeParent.target,iC(r.x,a.x,o.x),iC(r.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sS(this.target,this.layout.layoutBox),iW(this.target,this.targetDelta)):sS(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iF(),this.relativeTargetOrigin=iF(),iD(this.relativeTargetOrigin,this.target,t.target),sS(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}g.value&&sU.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||iN(this.parent.latestValues)||iI(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===b.timestamp&&(i=!1),i)return;let{layout:s,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||n))return;sS(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,a=this.treeScale.y;(function(t,e,i,s=!1){let n,r;let a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){r=(n=i[o]).projectionDelta;let{visualElement:a}=n.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iY(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,iW(t,r)),s&&iO(n.latestValues)&&iY(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}})(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iF());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sM(this.prevProjectionDelta.x,this.projectionDelta.x),sM(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iE(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&sO(this.projectionDelta.x,this.prevProjectionDelta.x)&&sO(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),g.value&&sU.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ij(),this.projectionDelta=ij(),this.projectionDeltaWithTransform=ij()}setAnimationOrigin(t,e=!1){let i;let s=this.snapshot,n=s?s.latestValues:{},r={...this.latestValues},a=ij();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=iF(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,d=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(s6));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(s9(a.x,t.x,s),s9(a.y,t.y,s),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,c,p,m,f,g;if(iD(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,g=s,s8(p.x,m.x,f.x,g),s8(p.y,m.y,f.y,g),i&&(h=this.relativeTarget,c=i,sF(h.x,c.x)&&sF(h.y,c.y)))this.isProjectionDirty=!1;i||(i=iF()),sS(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,n,r){n?(t.opacity=tD(0,i.opacity??1,sw(s)),t.opacityExit=tD(e.opacity??1,0,sT(s))):r&&(t.opacity=tD(e.opacity??1,i.opacity??1,s));for(let n=0;n<sv;n++){let r=`border${sg[n]}Radius`,a=sb(e,r),o=sb(i,r);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||sx(a)===sx(o)?(t[r]=Math.max(tD(sy(a),sy(o),s),0),(tc.test(o)||tc.test(a))&&(t[r]+="%")):t[r]=o)}(e.rotate||i.rotate)&&(t.rotate=tD(e.rotate||0,i.rotate||0,s))}(r,n,this.latestValues,s,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(x(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=y.update(()=>{sr.hasAnimatedSinceResize=!0,z.layout++,this.motionValue||(this.motionValue=R(0)),this.currentAnimation=function(t,e,i){let s=L(t)?t:R(t);return s.start(e7("",s,e,i)),s.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{z.layout--},onComplete:()=>{z.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:n}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&ni(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||iF();let e=iS(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=iS(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}sS(e,i),iY(e,n),iE(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new sI),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&sz("z",t,s,this.animationValues);for(let e=0;e<s$.length;e++)sz(`rotate${s$[e]}`,t,s,this.animationValues),sz(`skew${s$[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return sW;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=sf(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=sf(t?.pointerEvents)||""),this.hasProjected&&!iO(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let n=s.animationValues||s.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let s="",n=t.x.translate/e.x,r=t.y.translate/e.y,a=i?.z||0;if((n||r||a)&&(s=`translate3d(${n}px, ${r}px, ${a}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:r,skewX:a,skewY:o}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),n&&(s+=`rotateX(${n}deg) `),r&&(s+=`rotateY(${r}deg) `),a&&(s+=`skewX(${a}deg) `),o&&(s+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(s+=`scale(${o}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(e.transform=i(n,e.transform));let{x:r,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*r.origin}% ${100*a.origin}% 0`,s.animationValues?e.opacity=s===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=s===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,sl){if(void 0===n[t])continue;let{correct:i,applyTo:r,isCSSVariable:a}=sl[t],o="none"===e.transform?n[t]:i(n[t],s);if(r){let t=r.length;for(let i=0;i<t;i++)e[r[i]]=o}else a?this.options.visualElement.renderState.vars[t]=o:e[t]=o}return this.options.layoutId&&(e.pointerEvents=s===this?sf(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(sZ),this.root.sharedNodes.clear()}}}function sH(t){t.updateLayout()}function sX(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=t.layout,{animationType:n}=t.options,r=e.source!==t.layout.source;"size"===n?iL(t=>{let s=r?e.measuredBox[t]:e.layoutBox[t],n=iS(s);s.min=i[t].min,s.max=s.min+n}):ni(n,e.layoutBox,i)&&iL(s=>{let n=r?e.measuredBox[s]:e.layoutBox[s],a=iS(i[s]);n.max=n.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+a)});let a=ij();iE(a,i,e.layoutBox);let o=ij();r?iE(o,t.applyTransform(s,!0),e.measuredBox):iE(o,i,e.layoutBox);let l=!sR(a),h=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:n,layout:r}=s;if(n&&r){let a=iF();iD(a,e.layoutBox,n.layoutBox);let o=iF();iD(o,i,r.layoutBox),sB(a,o)||(h=!0),s.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function sq(t){g.value&&sU.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function sK(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function sG(t){t.clearSnapshot()}function sZ(t){t.clearMeasurements()}function sJ(t){t.isLayoutDirty=!1}function sQ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function s0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function s1(t){t.resolveTargetDelta()}function s3(t){t.calcProjection()}function s2(t){t.resetSkewAndRotation()}function s5(t){t.removeLeadSnapshot()}function s9(t,e,i){t.translate=tD(e.translate,0,i),t.scale=tD(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function s8(t,e,i,s){t.min=tD(e.min,i.min,s),t.max=tD(e.max,i.max,s)}function s6(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let s4={duration:.45,ease:[.4,0,.1,1]},s7=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nt=s7("applewebkit/")&&!s7("chrome/")?Math.round:p;function ne(t){t.min=nt(t.min),t.max=nt(t.max)}function ni(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(sN(e)-sN(i)))}function ns(t){return t!==t.root&&t.scroll?.wasRoot}let nn=sY({attachResizeListener:(t,e)=>ix(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nr={current:void 0},na=sY({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!nr.current){let t=new nn({});t.mount(window),t.setOptions({layoutScroll:!0}),nr.current=t}return nr.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function no(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function nl(t){return!("touch"===t.pointerType||iy.x||iy.y)}function nh(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=s["onHover"+i];n&&y.postRender(()=>n(e,iw(e)))}class nu extends ip{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=no(t,i),a=t=>{if(!nl(t))return;let{target:i}=t,s=e(i,t);if("function"!=typeof s||!i)return;let r=t=>{nl(t)&&(s(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,n)};return s.forEach(t=>{t.addEventListener("pointerenter",a,n)}),r}(t,(t,e)=>(nh(this.node,e,"Start"),t=>nh(this.node,t,"End"))))}unmount(){}}class nd extends ip{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=U(ix(this.node.current,"focus",()=>this.onFocus()),ix(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let nc=(t,e)=>!!e&&(t===e||nc(t,e.parentElement)),np=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),nm=new WeakSet;function nf(t){return e=>{"Enter"===e.key&&t(e)}}function ng(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let nv=(t,e)=>{let i=t.currentTarget;if(!i)return;let s=nf(()=>{if(nm.has(i))return;ng(i,"down");let t=nf(()=>{ng(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>ng(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function ny(t){return ib(t)&&!(iy.x||iy.y)}function nx(t,e,i){let{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=s["onTap"+("End"===i?"":i)];n&&y.postRender(()=>n(e,iw(e)))}class nb extends ip{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=no(t,i),a=t=>{let s=t.currentTarget;if(!ny(t))return;nm.add(s);let r=e(s,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),nm.has(s)&&nm.delete(s),ny(t)&&"function"==typeof r&&r(t,{success:e})},o=t=>{a(t,s===window||s===document||i.useGlobalTarget||nc(s,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,n),window.addEventListener("pointercancel",l,n)};return s.forEach(t=>{if((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,n),eQ(t))t.addEventListener("focus",t=>nv(t,n)),!np.has(t.tagName)&&-1===t.tabIndex&&!t.hasAttribute("tabindex")&&(t.tabIndex=0)}),r}(t,(t,e)=>(nx(this.node,e,"Start"),(t,{success:e})=>nx(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nw=new WeakMap,nT=new WeakMap,nP=t=>{let e=nw.get(t.target);e&&e(t)},nA=t=>{t.forEach(nP)},nS={some:0,all:1};class nM extends ip{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:n}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:nS[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;nT.has(i)||nT.set(i,{});let s=nT.get(i),n=JSON.stringify(e);return s[n]||(s[n]=new IntersectionObserver(nA,{root:t,...e})),s[n]}(e);return nw.set(t,i),s.observe(t),()=>{nw.delete(t),s.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),r=e?i:s;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nE=(0,o.createContext)({strict:!1}),nC=(0,o.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),nV=(0,o.createContext)({});function nD(t){return l(t.animate)||io.some(e=>ir(t[e]))}function nk(t){return!!(nD(t)||t.variants)}function nj(t){return Array.isArray(t)?t.join(" "):t}let nR="undefined"!=typeof window,nF={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nL={};for(let t in nF)nL[t]={isEnabled:e=>nF[t].some(t=>!!e[t])};let nB=Symbol.for("motionComponentSymbol"),nN=nR?o.useLayoutEffect:o.useEffect;function nO(t,{layout:e,layoutId:i}){return P.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!sl[t]||"opacity"===t)}let nI=(t,e)=>e&&"number"==typeof t?e.transform(t):t,nU={...J,transform:Math.round},n$={borderWidth:tp,borderTopWidth:tp,borderRightWidth:tp,borderBottomWidth:tp,borderLeftWidth:tp,borderRadius:tp,radius:tp,borderTopLeftRadius:tp,borderTopRightRadius:tp,borderBottomRightRadius:tp,borderBottomLeftRadius:tp,width:tp,maxWidth:tp,height:tp,maxHeight:tp,top:tp,right:tp,bottom:tp,left:tp,padding:tp,paddingTop:tp,paddingRight:tp,paddingBottom:tp,paddingLeft:tp,margin:tp,marginTop:tp,marginRight:tp,marginBottom:tp,marginLeft:tp,backgroundPositionX:tp,backgroundPositionY:tp,rotate:td,rotateX:td,rotateY:td,rotateZ:td,scale:tt,scaleX:tt,scaleY:tt,scaleZ:tt,skew:td,skewX:td,skewY:td,distance:tp,translateX:tp,translateY:tp,translateZ:tp,x:tp,y:tp,z:tp,perspective:tp,transformPerspective:tp,opacity:Q,originX:tg,originY:tg,originZ:tp,zIndex:nU,fillOpacity:Q,strokeOpacity:Q,numOctaves:nU},nW={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},n_=T.length;function nz(t,e,i){let{style:s,vars:n,transformOrigin:r}=t,a=!1,o=!1;for(let t in e){let i=e[t];if(P.has(t)){a=!0;continue}if(q(t)){n[t]=i;continue}{let e=nI(i,n$[t]);t.startsWith("origin")?(o=!0,r[t]=e):s[t]=e}}if(!e.transform&&(a||i?s.transform=function(t,e,i){let s="",n=!0;for(let r=0;r<n_;r++){let a=T[r],o=t[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let t=nI(o,n$[a]);if(!l){n=!1;let e=nW[a]||a;s+=`${e}(${t}) `}i&&(e[a]=t)}}return s=s.trim(),i?s=i(e,n?"":s):n&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),o){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;s.transformOrigin=`${t} ${e} ${i}`}}let nY=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nH(t,e,i){for(let s in e)L(e[s])||nO(s,i)||(t[s]=e[s])}let nX={offset:"stroke-dashoffset",array:"stroke-dasharray"},nq={offset:"strokeDashoffset",array:"strokeDasharray"};function nK(t,{attrX:e,attrY:i,attrScale:s,pathLength:n,pathSpacing:r=1,pathOffset:a=0,...o},l,h,u){if(nz(t,o,h),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=u?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==s&&(d.scale=s),void 0!==n&&function(t,e,i=1,s=0,n=!0){t.pathLength=1;let r=n?nX:nq;t[r.offset]=tp.transform(-s);let a=tp.transform(e),o=tp.transform(i);t[r.array]=`${a} ${o}`}(d,n,r,a,!1)}let nG=()=>({...nY(),attrs:{}}),nZ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),nJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function nQ(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||nJ.has(t)}let n0=t=>!nQ(t);try{!function(t){t&&(n0=e=>e.startsWith("on")?!nQ(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let n1=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function n3(t){if("string"!=typeof t||t.includes("-"));else if(n1.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let n2=t=>(e,i)=>{let s=(0,o.useContext)(nV),n=(0,o.useContext)(si),r=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,s,n){return{latestValues:function(t,e,i,s){let n={},r=s(t,{});for(let t in r)n[t]=sf(r[t]);let{initial:a,animate:o}=t,h=nD(t),d=nk(t);e&&d&&!h&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===o&&(o=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===a)?o:a;if(p&&"boolean"!=typeof p&&!l(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let s=u(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(n[t]=e)}for(let e in t)n[e]=t[e]}}}return n}(i,s,n,t),renderState:e()}})(t,e,s,n);return i?r():function(t){let e=(0,o.useRef)(null);return null===e.current&&(e.current=t()),e.current}(r)};function n5(t,e,i){let{style:s}=t,n={};for(let r in s)(L(s[r])||e.style&&L(e.style[r])||nO(r,t)||i?.getValue(r)?.liveStyle!==void 0)&&(n[r]=s[r]);return n}let n9={useVisualState:n2({scrapeMotionValuesFromProps:n5,createRenderState:nY})};function n8(t,e,i){let s=n5(t,e,i);for(let i in t)(L(t[i])||L(e[i]))&&(s[-1!==T.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let n6={useVisualState:n2({scrapeMotionValuesFromProps:n8,createRenderState:nG})},n4=t=>e=>e.test(t),n7=[J,tp,tc,td,tf,tm,{test:t=>"auto"===t,parse:t=>t}],rt=t=>n7.find(n4(t)),re=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ri=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,rs=t=>/^0[^.\s]+$/u.test(t),rn=new Set(["brightness","contrast","saturate","opacity"]);function rr(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(ti)||[];if(!s)return t;let n=i.replace(s,""),r=+!!rn.has(e);return s!==i&&(r*=100),e+"("+r+n+")"}let ra=/\b([a-z-]*)\(.*?\)/gu,ro={...tE,getAnimatableNone:t=>{let e=t.match(ra);return e?e.map(rr).join(" "):t}},rl={...n$,color:ty,backgroundColor:ty,outlineColor:ty,fill:ty,stroke:ty,borderColor:ty,borderTopColor:ty,borderRightColor:ty,borderBottomColor:ty,borderLeftColor:ty,filter:ro,WebkitFilter:ro},rh=t=>rl[t];function ru(t,e){let i=rh(t);return i!==ro&&(i=tE),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let rd=new Set(["auto","none","0"]);class rc extends eI{constructor(t,e,i,s,n){super(t,e,i,s,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&G(s=s.trim())){let n=function t(e,i,s=1){H(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,r]=function(t){let e=ri.exec(t);if(!e)return[,];let[,i,s,n]=e;return[`--${i??s}`,n]}(e);if(!n)return;let a=window.getComputedStyle(i).getPropertyValue(n);if(a){let t=a.trim();return re(t)?parseFloat(t):t}return G(r)?t(r,i,s+1):r}(s,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!A.has(i)||2!==t.length)return;let[s,n]=t,r=rt(s),a=rt(n);if(r!==a){if(eV(r)&&eV(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else ej[i]&&(this.needsMeasurement=!0)}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;(null===t[e]||("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||rs(s)))&&i.push(e)}i.length&&function(t,e,i){let s,n=0;for(;n<t.length&&!s;){let e=t[n];"string"==typeof e&&!rd.has(e)&&tP(e).values.length&&(s=t[n]),n++}if(s&&i)for(let n of e)t[n]=ru(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ej[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);let n=i.length-1,r=i[n];i[n]=ej[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let rp=[...n7,ty,tE],rm=t=>rp.find(n4(t)),rf={current:null},rg={current:!1},rv=new WeakMap,ry=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class rx{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:n,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eI,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=V.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,y.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=r;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!n,this.isControllingVariants=nD(e),this.isVariantNode=nk(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in u){let e=u[t];void 0!==o[t]&&L(e)&&e.set(o[t],!1)}}mount(t){this.current=t,rv.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),rg.current||function(){if(rg.current=!0,nR){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>rf.current=t.matches;t.addListener(e),e()}else rf.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||rf.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),x(this.notifyUpdate),x(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=P.has(t);s&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&y.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nL){let e=nL[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iF()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<ry.length;e++){let i=ry[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let n=e[s],r=i[s];if(L(n))t.addValue(s,n);else if(L(r))t.addValue(s,R(n,{owner:t}));else if(r!==n){if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(s);t.addValue(s,R(void 0!==e?e:n,{owner:t}))}}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=R(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(re(i)||rs(i))?i=parseFloat(i):!rm(i)&&tE.test(e)&&(i=ru(t,e)),this.setBaseTarget(t,L(i)?i.get():i)),L(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e;let{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let s=u(this.props,i,this.presenceContext?.custom);s&&(e=s[t])}if(i&&void 0!==e)return e;let s=this.getBaseTargetFromProps(this.props,t);return void 0===s||L(s)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new E),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class rb extends rx{constructor(){super(...arguments),this.KeyframeResolver=rc}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;L(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function rw(t,{style:e,vars:i},s,n){for(let r in Object.assign(t.style,e,n&&n.getProjectionStyles(s)),i)t.style.setProperty(r,i[r])}class rT extends rb{constructor(){super(...arguments),this.type="html",this.renderInstance=rw}readValueFromInstance(t,e){if(P.has(e))return this.projection?.isProjecting?eS(e):eE(t,e);{let i=window.getComputedStyle(t),s=(q(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iH(t,e)}build(t,e,i){nz(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return n5(t,e,i)}}let rP=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class rA extends rb{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iF}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(P.has(e)){let t=rh(e);return t&&t.default||0}return e=rP.has(e)?e:N(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return n8(t,e,i)}build(t,e,i){nK(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,s){!function(t,e,i,s){for(let i in rw(t,e,void 0,s),e.attrs)t.setAttribute(rP.has(i)?i:N(i),e.attrs[i])}(t,e,0,s)}mount(t){this.isSVGTag=nZ(t.tagName),super.mount(t)}}let rS=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((n={animation:{Feature:im},exit:{Feature:iv},inView:{Feature:nM},tap:{Feature:nb},focus:{Feature:nd},hover:{Feature:nu},pan:{Feature:st},drag:{Feature:i4,ProjectionNode:na,MeasureLayout:su},layout:{ProjectionNode:na,MeasureLayout:su}},r=(t,e)=>n3(t)?new rA(e):new rT(e,{allowProjection:t!==o.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:s,Component:n}){function r(t,r){var l,h,u;let d;let c={...(0,o.useContext)(nC),...t,layoutId:function({layoutId:t}){let e=(0,o.useContext)(ss).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:p}=c,m=function(t){let{initial:e,animate:i}=function(t,e){if(nD(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ir(e)?e:void 0,animate:ir(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,o.useContext)(nV));return(0,o.useMemo)(()=>({initial:e,animate:i}),[nj(e),nj(i)])}(t),f=s(t,p);if(!p&&nR){h=0,u=0,(0,o.useContext)(nE).strict;let t=function(t){let{drag:e,layout:i}=nL;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(c);d=t.MeasureLayout,m.visualElement=function(t,e,i,s,n){let{visualElement:r}=(0,o.useContext)(nV),a=(0,o.useContext)(nE),l=(0,o.useContext)(si),h=(0,o.useContext)(nC).reducedMotion,u=(0,o.useRef)(null);s=s||a.renderer,!u.current&&s&&(u.current=s(t,{visualState:e,parent:r,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:h}));let d=u.current,c=(0,o.useContext)(sn);d&&!d.projection&&n&&("html"===d.type||"svg"===d.type)&&function(t,e,i,s){let{layoutId:n,layout:r,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:!!a||o&&iq(o),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:s,crossfade:u,layoutScroll:l,layoutRoot:h})}(u.current,i,n,c);let p=(0,o.useRef)(!1);(0,o.useInsertionEffect)(()=>{d&&p.current&&d.update(i,l)});let m=i[O],f=(0,o.useRef)(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return nN(()=>{d&&(p.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),se.render(d.render),f.current&&d.animationState&&d.animationState.animateChanges())}),(0,o.useEffect)(()=>{d&&(!f.current&&d.animationState&&d.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),f.current=!1))}),d}(n,f,c,e,t.ProjectionNode)}return(0,a.jsxs)(nV.Provider,{value:m,children:[d&&m.visualElement?(0,a.jsx)(d,{visualElement:m.visualElement,...c}):null,i(n,t,(l=m.visualElement,(0,o.useCallback)(t=>{t&&f.onMount&&f.onMount(t),l&&(t?l.mount(t):l.unmount()),r&&("function"==typeof r?r(t):iq(r)&&(r.current=t))},[l])),f,p,m.visualElement)]})}t&&function(t){for(let e in t)nL[e]={...nL[e],...t[e]}}(t),r.displayName=`motion.${"string"==typeof n?n:`create(${n.displayName??n.name??""})`}`;let l=(0,o.forwardRef)(r);return l[nB]=n,l}({...n3(t)?n6:n9,preloadedFeatures:n,useRender:function(t=!1){return(e,i,s,{latestValues:n},r)=>{let a=(n3(e)?function(t,e,i,s){let n=(0,o.useMemo)(()=>{let i=nG();return nK(i,e,nZ(s),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};nH(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return nH(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,o.useMemo)(()=>{let i=nY();return nz(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,n,r,e),l=function(t,e,i){let s={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(n0(n)||!0===i&&nQ(n)||!e&&!nQ(n)||t.draggable&&n.startsWith("onDrag"))&&(s[n]=t[n]);return s}(i,"string"==typeof e,t),h=e!==o.Fragment?{...l,...a,ref:s}:{},{children:u}=i,d=(0,o.useMemo)(()=>L(u)?u.get():u,[u]);return(0,o.createElement)(e,{...h,children:d})}}(e),createVisualElement:r,Component:t})}));var rM=i(9135),rE=i(2849),rC=i(8080);function rV({name:t,description:e,capabilities:i,avatarUrl:s,isActive:n,onActivate:r}){let[l,h]=(0,o.useState)(!1);return(0,a.jsx)(rS.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},whileHover:{scale:1.03},className:"w-full max-w-sm",onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),children:(0,a.jsxs)(rM.Zp,{className:`overflow-hidden border-2 ${n?"border-blue-500":"border-gray-200"} transition-all duration-300`,children:[(0,a.jsxs)(rM.aR,{className:"relative p-0",children:[(0,a.jsx)(rS.div,{className:"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600",initial:{opacity:.7},animate:{opacity:l?.9:.7}}),(0,a.jsxs)("div",{className:"relative p-6 flex items-center gap-4",children:[(0,a.jsx)(rS.img,{src:s,alt:`${t} avatar`,className:"w-16 h-16 rounded-full border-2 border-white",animate:{rotate:360*!!l},transition:{duration:2}}),(0,a.jsxs)("div",{children:[(0,a.jsx)(rM.ZB,{className:"text-white",children:t}),(0,a.jsx)(rM.BT,{className:"text-white/80",children:"AI Agent"})]})]})]}),(0,a.jsxs)(rM.Wu,{className:"p-6",children:[(0,a.jsx)("p",{className:"text-gray-700 dark:text-gray-300",children:e}),(0,a.jsx)("div",{className:"mt-4 flex flex-wrap gap-2",children:i.map((t,e)=>(0,a.jsx)(rC.E,{variant:"outline",className:"bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300",children:t},e))})]}),(0,a.jsx)(rM.wL,{className:"border-t p-6",children:(0,a.jsx)(rE.$,{onClick:r,className:`w-full ${n?"bg-green-600 hover:bg-green-700":"bg-blue-600 hover:bg-blue-700"}`,children:n?"Active":"Activate Agent"})})]})})}function rD(){let[t,e]=(0,o.useState)(null),i=[{id:"assistant",name:"AI Assistant",description:"A helpful AI assistant that can answer questions, provide information, and help with various tasks.",capabilities:["Q&A","Research","Writing","Analysis"],avatarUrl:"https://images.unsplash.com/photo-1677442136019-21780ecad995?w=100&h=100&fit=crop&crop=face"},{id:"coder",name:"Code Expert",description:"Specialized in programming, code review, debugging, and software development best practices.",capabilities:["Coding","Debugging","Code Review","Architecture"],avatarUrl:"https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=100&h=100&fit=crop&crop=face"},{id:"creative",name:"Creative Writer",description:"Expert in creative writing, storytelling, content creation, and artistic expression.",capabilities:["Writing","Storytelling","Content","Creativity"],avatarUrl:"https://images.unsplash.com/photo-1552058544-f2b08422138a?w=100&h=100&fit=crop&crop=face"}],s=i=>{e(t===i?null:i)};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"AI Agent Dashboard"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"Choose an AI agent to assist you with your tasks"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-center",children:i.map(e=>(0,a.jsx)(rV,{name:e.name,description:e.description,capabilities:e.capabilities,avatarUrl:e.avatarUrl,isActive:t===e.id,onActivate:()=>s(e.id)},e.id))}),t&&(0,a.jsx)("div",{className:"mt-12 text-center",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg max-w-md mx-auto",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Agent Activated!"}),(0,a.jsxs)("p",{className:"text-gray-600 dark:text-gray-300",children:[i.find(e=>e.id===t)?.name," is now ready to assist you."]})]})})]})})}},3873:t=>{"use strict";t.exports=require("path")},5878:(t,e,i)=>{"use strict";i.r(e),i.d(e,{GlobalError:()=>a.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>c,tree:()=>h});var s=i(1227),n=i(8180),r=i(4950),a=i.n(r),o=i(433),l={};for(let t in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(l[t]=()=>o[t]);i.d(e,l);let h={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,3768)),"/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/page.tsx"],metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,8736))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,9139)),"/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,2434,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,1779,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,3784,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,8736))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/page.tsx"],d={require:i,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:h}})},6484:(t,e,i)=>{Promise.resolve().then(i.bind(i,788))},6532:(t,e,i)=>{"use strict";i.d(e,{default:()=>l});var s=i(5393),n=i(4332),r=i.n(n),a=i(6259),o=i(2849);function l(){let t=(0,a.usePathname)();return(0,s.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm border-b",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-8 py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,s.jsx)(r(),{href:"/",className:"text-xl font-bold text-gray-900 dark:text-white",children:"AI Agent Hub"}),(0,s.jsx)("div",{className:"flex space-x-4",children:[{href:"/",label:"Dashboard",icon:"\uD83C\uDFE0"},{href:"/network",label:"Network Graph",icon:"\uD83C\uDF10"}].map(e=>(0,s.jsx)(r(),{href:e.href,children:(0,s.jsxs)(o.$,{variant:t===e.href?"default":"ghost",className:"flex items-center gap-2",children:[(0,s.jsx)("span",{children:e.icon}),e.label]})},e.href))})]}),(0,s.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-300",children:"AI Agent Network Visualization"})]})})})}},6687:(t,e,i)=>{Promise.resolve().then(i.bind(i,3850))},7367:(t,e,i)=>{Promise.resolve().then(i.bind(i,3768))},7924:(t,e,i)=>{Promise.resolve().then(i.bind(i,6532))},8080:(t,e,i)=>{"use strict";i.d(e,{E:()=>n});var s=i(5393);let n=i(5908).forwardRef(({className:t,variant:e="default",...i},n)=>(0,s.jsx)("div",{ref:n,className:`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 ${{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}[e]} ${t||""}`,...i}));n.displayName="Badge"},8736:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>n});var s=i(8318);let n=async t=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await t.params,"favicon.ico")+""}]},9121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9135:(t,e,i)=>{"use strict";i.d(e,{BT:()=>l,Wu:()=>h,ZB:()=>o,Zp:()=>r,aR:()=>a,wL:()=>u});var s=i(5393),n=i(5908);let r=n.forwardRef(({className:t,...e},i)=>(0,s.jsx)("div",{ref:i,className:`rounded-lg border bg-card text-card-foreground shadow-sm ${t||""}`,...e}));r.displayName="Card";let a=n.forwardRef(({className:t,...e},i)=>(0,s.jsx)("div",{ref:i,className:`flex flex-col space-y-1.5 p-6 ${t||""}`,...e}));a.displayName="CardHeader";let o=n.forwardRef(({className:t,...e},i)=>(0,s.jsx)("h3",{ref:i,className:`text-2xl font-semibold leading-none tracking-tight ${t||""}`,...e}));o.displayName="CardTitle";let l=n.forwardRef(({className:t,...e},i)=>(0,s.jsx)("p",{ref:i,className:`text-sm text-muted-foreground ${t||""}`,...e}));l.displayName="CardDescription";let h=n.forwardRef(({className:t,...e},i)=>(0,s.jsx)("div",{ref:i,className:`p-6 pt-0 ${t||""}`,...e}));h.displayName="CardContent";let u=n.forwardRef(({className:t,...e},i)=>(0,s.jsx)("div",{ref:i,className:`flex items-center p-6 pt-0 ${t||""}`,...e}));u.displayName="CardFooter"},9139:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>u,metadata:()=>h});var s=i(3089),n=i(9044),r=i.n(n),a=i(7778),o=i.n(a);i(2463);var l=i(788);let h={title:"AI Agent Network Hub",description:"Interactive AI Agent Dashboard and Network Visualization"};function u({children:t}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsxs)("body",{className:`${r().variable} ${o().variable} antialiased`,children:[(0,s.jsx)(l.default,{}),t]})})}},9294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:t=>{"use strict";t.exports=require("url")}};var e=require("../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),s=e.X(0,[163,436,318],()=>i(5878));module.exports=s})();