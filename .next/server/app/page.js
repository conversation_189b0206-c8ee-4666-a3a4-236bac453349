(()=>{var t={};t.id=974,t.ids=[974],t.modules={304:()=>{},734:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getPathMatch",{enumerable:!0,get:function(){return n}});let r=i(8614);function n(t,e){let i=[],n=(0,r.pathToRegexp)(t,i,{delimiter:"/",sensitive:"boolean"==typeof(null==e?void 0:e.sensitive)&&e.sensitive,strict:null==e?void 0:e.strict}),s=(0,r.regexpToFunction)((null==e?void 0:e.regexModifier)?new RegExp(e.regexModifier(n.source),n.flags):n,i);return(t,r)=>{if("string"!=typeof t)return!1;let n=s(t);if(!n)return!1;if(null==e?void 0:e.removeUnnamedParams)for(let t of i)"number"==typeof t.name&&delete n.params[t.name];return{...r,...n.params}}}},846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1265:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{INTERCEPTION_ROUTE_MARKERS:function(){return n},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return s}});let r=i(1430),n=["(..)(..)","(.)","(..)","(...)"];function s(t){return void 0!==t.split("/").find(t=>n.find(e=>t.startsWith(e)))}function a(t){let e,i,s;for(let r of t.split("/"))if(i=n.find(t=>r.startsWith(t))){[e,s]=t.split(i,2);break}if(!e||!i||!s)throw Object.defineProperty(Error(`Invalid interception route: ${t}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(e=(0,r.normalizeAppPath)(e),i){case"(.)":s="/"===e?`/${s}`:e+"/"+s;break;case"(..)":if("/"===e)throw Object.defineProperty(Error(`Invalid interception route: ${t}. Cannot use (..) marker at the root level, use (.) instead.`),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});s=e.split("/").slice(0,-1).concat(s).join("/");break;case"(...)":s="/"+s;break;case"(..)(..)":let a=e.split("/");if(a.length<=2)throw Object.defineProperty(Error(`Invalid interception route: ${t}. Cannot use (..)(..) marker at the root level or one level up.`),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});s=a.slice(0,-2).concat(s).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:e,interceptedRoute:s}}},1287:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return f},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return i},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return o},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return c},normalizeRepeatedSlashes:function(){return h},stringifyError:function(){return x}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function r(t){let e,i=!1;return function(){for(var r=arguments.length,n=Array(r),s=0;s<r;s++)n[s]=arguments[s];return i||(i=!0,e=t(...n)),e}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=t=>n.test(t);function a(){let{protocol:t,hostname:e,port:i}=window.location;return t+"//"+e+(i?":"+i:"")}function o(){let{href:t}=window.location,e=a();return t.substring(e.length)}function l(t){return"string"==typeof t?t:t.displayName||t.name||"Unknown"}function u(t){return t.finished||t.headersSent}function h(t){let e=t.split("?");return e[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(e[1]?"?"+e.slice(1).join("?"):"")}async function c(t,e){let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await c(e.Component,e.ctx)}:{};let r=await t.getInitialProps(e);if(i&&u(i))return r;if(!r)throw Object.defineProperty(Error('"'+l(t)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(t=>"function"==typeof performance[t]);class m extends Error{}class f extends Error{}class g extends Error{constructor(t){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+t}}class y extends Error{constructor(t,e){super(),this.message="Failed to load static file for page: "+t+" "+e}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(t){return JSON.stringify({message:t.message,stack:t.stack})}},1290:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{compileNonPath:function(){return c},matchHas:function(){return h},parseDestination:function(){return d},prepareDestination:function(){return p}});let r=i(8614),n=i(8529),s=i(3243),a=i(1265),o=i(1525),l=i(9952);function u(t){return t.replace(/__ESC_COLON_/gi,":")}function h(t,e,i,r){void 0===i&&(i=[]),void 0===r&&(r=[]);let n={},s=i=>{let r;let s=i.key;switch(i.type){case"header":s=s.toLowerCase(),r=t.headers[s];break;case"cookie":r="cookies"in t?t.cookies[i.key]:(0,l.getCookieParser)(t.headers)()[i.key];break;case"query":r=e[s];break;case"host":{let{host:e}=(null==t?void 0:t.headers)||{};r=null==e?void 0:e.split(":",1)[0].toLowerCase()}}if(!i.value&&r)return n[function(t){let e="";for(let i=0;i<t.length;i++){let r=t.charCodeAt(i);(r>64&&r<91||r>96&&r<123)&&(e+=t[i])}return e}(s)]=r,!0;if(r){let t=RegExp("^"+i.value+"$"),e=Array.isArray(r)?r.slice(-1)[0].match(t):r.match(t);if(e)return Array.isArray(e)&&(e.groups?Object.keys(e.groups).forEach(t=>{n[t]=e.groups[t]}):"host"===i.type&&e[0]&&(n.host=e[0])),!0}return!1};return!!i.every(t=>s(t))&&!r.some(t=>s(t))&&n}function c(t,e){if(!t.includes(":"))return t;for(let i of Object.keys(e))t.includes(":"+i)&&(t=t.replace(RegExp(":"+i+"\\*","g"),":"+i+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+i+"\\?","g"),":"+i+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+i+"\\+","g"),":"+i+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+i+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+i));return t=t.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,r.compile)("/"+t,{validate:!1})(e).slice(1)}function d(t){let e=t.destination;for(let i of Object.keys({...t.params,...t.query}))if(i)e=e.replace(RegExp(":"+(0,n.escapeStringRegexp)(i),"g"),"__ESC_COLON_"+i);let i=(0,s.parseUrl)(e),r=i.pathname;r&&(r=u(r));let a=i.href;a&&(a=u(a));let o=i.hostname;o&&(o=u(o));let l=i.hash;return l&&(l=u(l)),{...i,pathname:r,hostname:o,href:a,hash:l}}function p(t){let e,i;let n=Object.assign({},t.query);delete n[o.NEXT_RSC_UNION_QUERY];let s=d(t),{hostname:l,query:h}=s,p=s.pathname;s.hash&&(p=""+p+s.hash);let m=[],f=[];for(let t of((0,r.pathToRegexp)(p,f),f))m.push(t.name);if(l){let t=[];for(let e of((0,r.pathToRegexp)(l,t),t))m.push(e.name)}let g=(0,r.compile)(p,{validate:!1});for(let[i,n]of(l&&(e=(0,r.compile)(l,{validate:!1})),Object.entries(h)))Array.isArray(n)?h[i]=n.map(e=>c(u(e),t.params)):"string"==typeof n&&(h[i]=c(u(n),t.params));let y=Object.keys(t.params).filter(t=>"nextInternalLocale"!==t);if(t.appendParamsToQuery&&!y.some(t=>m.includes(t)))for(let e of y)e in h||(h[e]=t.params[e]);if((0,a.isInterceptionRouteAppPath)(p))for(let e of p.split("/")){let i=a.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t));if(i){"(..)(..)"===i?(t.params["0"]="(..)",t.params["1"]="(..)"):t.params["0"]=i;break}}try{let[r,n]=(i=g(t.params)).split("#",2);e&&(s.hostname=e(t.params)),s.pathname=r,s.hash=(n?"#":"")+(n||""),delete s.search}catch(t){if(t.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw t}return s.query={...n,...s.query},{newUrl:i,destQuery:h,parsedDestination:s}}},1430:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{normalizeAppPath:function(){return s},normalizeRscURL:function(){return a}});let r=i(7759),n=i(9855);function s(t){return(0,r.ensureLeadingSlash)(t.split("/").reduce((t,e,i,r)=>!e||(0,n.isGroupSegment)(e)||"@"===e[0]||("page"===e||"route"===e)&&i===r.length-1?t:t+"/"+e,""))}function a(t){return t.replace(/\.rsc($|\?)/,"$1")}},2463:()=>{},2659:(t,e,i)=>{Promise.resolve().then(i.t.bind(i,5904,23)),Promise.resolve().then(i.t.bind(i,9350,23)),Promise.resolve().then(i.t.bind(i,4950,23)),Promise.resolve().then(i.t.bind(i,1745,23)),Promise.resolve().then(i.t.bind(i,9445,23)),Promise.resolve().then(i.t.bind(i,6103,23)),Promise.resolve().then(i.t.bind(i,1158,23)),Promise.resolve().then(i.t.bind(i,4876,23))},2907:(t,e,i)=>{Promise.resolve().then(i.t.bind(i,6960,23)),Promise.resolve().then(i.t.bind(i,4750,23)),Promise.resolve().then(i.t.bind(i,7150,23)),Promise.resolve().then(i.t.bind(i,7745,23)),Promise.resolve().then(i.t.bind(i,8917,23)),Promise.resolve().then(i.t.bind(i,6879,23)),Promise.resolve().then(i.t.bind(i,9630,23)),Promise.resolve().then(i.t.bind(i,2732,23))},3033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3243:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"parseUrl",{enumerable:!0,get:function(){return s}});let r=i(9517),n=i(7796);function s(t){if(t.startsWith("/"))return(0,n.parseRelativeUrl)(t);let e=new URL(t);return{hash:e.hash,hostname:e.hostname,href:e.href,pathname:e.pathname,port:e.port,protocol:e.protocol,query:(0,r.searchParamsToUrlQuery)(e.searchParams),search:e.search}}},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3768:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>r});let r=(0,i(1863).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/page.tsx","default")},3873:t=>{"use strict";t.exports=require("path")},4851:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{getNamedMiddlewareRegex:function(){return f},getNamedRouteRegex:function(){return m},getRouteRegex:function(){return c},parseParameter:function(){return l}});let r=i(6203),n=i(1265),s=i(8529),a=i(6139),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(t){let e=t.match(o);return e?u(e[2]):u(t)}function u(t){let e=t.startsWith("[")&&t.endsWith("]");e&&(t=t.slice(1,-1));let i=t.startsWith("...");return i&&(t=t.slice(3)),{key:t,repeat:i,optional:e}}function h(t,e,i){let r={},l=1,h=[];for(let c of(0,a.removeTrailingSlash)(t).slice(1).split("/")){let t=n.INTERCEPTION_ROUTE_MARKERS.find(t=>c.startsWith(t)),a=c.match(o);if(t&&a&&a[2]){let{key:e,optional:i,repeat:n}=u(a[2]);r[e]={pos:l++,repeat:n,optional:i},h.push("/"+(0,s.escapeStringRegexp)(t)+"([^/]+?)")}else if(a&&a[2]){let{key:t,repeat:e,optional:n}=u(a[2]);r[t]={pos:l++,repeat:e,optional:n},i&&a[1]&&h.push("/"+(0,s.escapeStringRegexp)(a[1]));let o=e?n?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";i&&a[1]&&(o=o.substring(1)),h.push(o)}else h.push("/"+(0,s.escapeStringRegexp)(c));e&&a&&a[3]&&h.push((0,s.escapeStringRegexp)(a[3]))}return{parameterizedRoute:h.join(""),groups:r}}function c(t,e){let{includeSuffix:i=!1,includePrefix:r=!1,excludeOptionalTrailingSlash:n=!1}=void 0===e?{}:e,{parameterizedRoute:s,groups:a}=h(t,i,r),o=s;return n||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:a}}function d(t){let e,{interceptionMarker:i,getSafeRouteKey:r,segment:n,routeKeys:a,keyPrefix:o,backreferenceDuplicateKeys:l}=t,{key:h,optional:c,repeat:d}=u(n),p=h.replace(/\W/g,"");o&&(p=""+o+p);let m=!1;(0===p.length||p.length>30)&&(m=!0),isNaN(parseInt(p.slice(0,1)))||(m=!0),m&&(p=r());let f=p in a;o?a[p]=""+o+h:a[p]=h;let g=i?(0,s.escapeStringRegexp)(i):"";return e=f&&l?"\\k<"+p+">":d?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",c?"(?:/"+g+e+")?":"/"+g+e}function p(t,e,i,l,u){let h;let c=(h=0,()=>{let t="",e=++h;for(;e>0;)t+=String.fromCharCode(97+(e-1)%26),e=Math.floor((e-1)/26);return t}),p={},m=[];for(let h of(0,a.removeTrailingSlash)(t).slice(1).split("/")){let t=n.INTERCEPTION_ROUTE_MARKERS.some(t=>h.startsWith(t)),a=h.match(o);if(t&&a&&a[2])m.push(d({getSafeRouteKey:c,interceptionMarker:a[1],segment:a[2],routeKeys:p,keyPrefix:e?r.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(a&&a[2]){l&&a[1]&&m.push("/"+(0,s.escapeStringRegexp)(a[1]));let t=d({getSafeRouteKey:c,segment:a[2],routeKeys:p,keyPrefix:e?r.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&a[1]&&(t=t.substring(1)),m.push(t)}else m.push("/"+(0,s.escapeStringRegexp)(h));i&&a&&a[3]&&m.push((0,s.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:m.join(""),routeKeys:p}}function m(t,e){var i,r,n;let s=p(t,e.prefixRouteKeys,null!=(i=e.includeSuffix)&&i,null!=(r=e.includePrefix)&&r,null!=(n=e.backreferenceDuplicateKeys)&&n),a=s.namedParameterizedRoute;return e.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...c(t,e),namedRegex:"^"+a+"$",routeKeys:s.routeKeys}}function f(t,e){let{parameterizedRoute:i}=h(t,!1,!1),{catchAll:r=!0}=e;if("/"===i)return{namedRegex:"^/"+(r?".*":"")+"$"};let{namedParameterizedRoute:n}=p(t,!1,!1,!1,!1);return{namedRegex:"^"+n+(r?"(?:(/.*)?)":"")+"$"}}},4880:(t,e)=>{"use strict";function i(t){let e=5381;for(let i=0;i<t.length;i++)e=(e<<5)+e+t.charCodeAt(i)&0xffffffff;return e>>>0}function r(t){return i(t).toString(36).slice(0,5)}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{djb2Hash:function(){return i},hexHash:function(){return r}})},5878:(t,e,i)=>{"use strict";i.r(e),i.d(e,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>h,routeModule:()=>d,tree:()=>u});var r=i(1227),n=i(8180),s=i(4950),a=i.n(s),o=i(433),l={};for(let t in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(l[t]=()=>o[t]);i.d(e,l);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,3768)),"/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/page.tsx"],metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,8736))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,9139)),"/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,2434,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,1779,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,3784,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,8736))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,h=["/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/page.tsx"],c={require:i,loadChunk:()=>Promise.resolve()},d=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},6310:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getRouteMatcher",{enumerable:!0,get:function(){return n}});let r=i(1287);function n(t){let{re:e,groups:i}=t;return t=>{let n=e.exec(t);if(!n)return!1;let s=t=>{try{return decodeURIComponent(t)}catch(t){throw Object.defineProperty(new r.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[t,e]of Object.entries(i)){let i=n[e.pos];void 0!==i&&(e.repeat?a[t]=i.split("/").map(t=>s(t)):a[t]=s(i))}return a}}},6687:(t,e,i)=>{Promise.resolve().then(i.bind(i,9206))},7367:(t,e,i)=>{Promise.resolve().then(i.bind(i,3768))},7759:(t,e)=>{"use strict";function i(t){return t.startsWith("/")?t:"/"+t}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"ensureLeadingSlash",{enumerable:!0,get:function(){return i}})},7796:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"parseRelativeUrl",{enumerable:!0,get:function(){return n}}),i(1287);let r=i(9517);function n(t,e,i){void 0===i&&(i=!0);let n=new URL("http://n"),s=e?new URL(e,n):t.startsWith(".")?new URL("http://n"):n,{pathname:a,searchParams:o,search:l,hash:u,href:h,origin:c}=new URL(t,s);if(c!==n.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+t),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:a,query:i?(0,r.searchParamsToUrlQuery)(o):void 0,search:l,hash:u,href:h.slice(c.length)}}},8318:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{fillMetadataSegment:function(){return d},normalizeMetadataPageToRoute:function(){return m},normalizeMetadataRoute:function(){return p}});let r=i(9204),n=function(t){return t&&t.__esModule?t:{default:t}}(i(1099)),s=i(9857),a=i(4851),o=i(4880),l=i(1430),u=i(8378),h=i(9855);function c(t){let e=n.default.dirname(t);if(t.endsWith("/sitemap"))return"";let i="";return e.split("/").some(t=>(0,h.isGroupSegment)(t)||(0,h.isParallelRouteSegment)(t))&&(i=(0,o.djb2Hash)(e).toString(36).slice(0,6)),i}function d(t,e,i){let r=(0,l.normalizeAppPath)(t),o=(0,a.getNamedRouteRegex)(r,{prefixRouteKeys:!1}),h=(0,s.interpolateDynamicPath)(r,e,o),{name:d,ext:p}=n.default.parse(i),m=c(n.default.posix.join(t,d)),f=m?`-${m}`:"";return(0,u.normalizePathSep)(n.default.join(h,`${d}${f}${p}`))}function p(t){if(!(0,r.isMetadataRoute)(t))return t;let e=t,i="";if("/robots"===t?e+=".txt":"/manifest"===t?e+=".webmanifest":i=c(t),!e.endsWith("/route")){let{dir:t,name:r,ext:s}=n.default.parse(e);e=n.default.posix.join(t,`${r}${i?`-${i}`:""}${s}`,"route")}return e}function m(t,e){let i=t.endsWith("/route"),r=i?t.slice(0,-6):t,n=r.endsWith("/sitemap")?".xml":"";return(e?`${r}/[__metadata_id__]`:`${r}${n}`)+(i?"/route":"")}},8378:(t,e)=>{"use strict";function i(t){return t.replace(/\\/g,"/")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"normalizePathSep",{enumerable:!0,get:function(){return i}})},8529:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"escapeStringRegexp",{enumerable:!0,get:function(){return n}});let i=/[|\\{}()[\]^$+*?.-]/,r=/[|\\{}()[\]^$+*?.-]/g;function n(t){return i.test(t)?t.replace(r,"\\$&"):t}},8614:t=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var e={};(()=>{function t(t,e){void 0===e&&(e={});for(var i=function(t){for(var e=[],i=0;i<t.length;){var r=t[i];if("*"===r||"+"===r||"?"===r){e.push({type:"MODIFIER",index:i,value:t[i++]});continue}if("\\"===r){e.push({type:"ESCAPED_CHAR",index:i++,value:t[i++]});continue}if("{"===r){e.push({type:"OPEN",index:i,value:t[i++]});continue}if("}"===r){e.push({type:"CLOSE",index:i,value:t[i++]});continue}if(":"===r){for(var n="",s=i+1;s<t.length;){var a=t.charCodeAt(s);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){n+=t[s++];continue}break}if(!n)throw TypeError("Missing parameter name at "+i);e.push({type:"NAME",index:i,value:n}),i=s;continue}if("("===r){var o=1,l="",s=i+1;if("?"===t[s])throw TypeError('Pattern cannot start with "?" at '+s);for(;s<t.length;){if("\\"===t[s]){l+=t[s++]+t[s++];continue}if(")"===t[s]){if(0==--o){s++;break}}else if("("===t[s]&&(o++,"?"!==t[s+1]))throw TypeError("Capturing groups are not allowed at "+s);l+=t[s++]}if(o)throw TypeError("Unbalanced pattern at "+i);if(!l)throw TypeError("Missing pattern at "+i);e.push({type:"PATTERN",index:i,value:l}),i=s;continue}e.push({type:"CHAR",index:i,value:t[i++]})}return e.push({type:"END",index:i,value:""}),e}(t),r=e.prefixes,s=void 0===r?"./":r,a="[^"+n(e.delimiter||"/#?")+"]+?",o=[],l=0,u=0,h="",c=function(t){if(u<i.length&&i[u].type===t)return i[u++].value},d=function(t){var e=c(t);if(void 0!==e)return e;var r=i[u];throw TypeError("Unexpected "+r.type+" at "+r.index+", expected "+t)},p=function(){for(var t,e="";t=c("CHAR")||c("ESCAPED_CHAR");)e+=t;return e};u<i.length;){var m=c("CHAR"),f=c("NAME"),g=c("PATTERN");if(f||g){var y=m||"";-1===s.indexOf(y)&&(h+=y,y=""),h&&(o.push(h),h=""),o.push({name:f||l++,prefix:y,suffix:"",pattern:g||a,modifier:c("MODIFIER")||""});continue}var v=m||c("ESCAPED_CHAR");if(v){h+=v;continue}if(h&&(o.push(h),h=""),c("OPEN")){var y=p(),x=c("NAME")||"",b=c("PATTERN")||"",P=p();d("CLOSE"),o.push({name:x||(b?l++:""),pattern:x&&!b?a:b,prefix:y,suffix:P,modifier:c("MODIFIER")||""});continue}d("END")}return o}function i(t,e){void 0===e&&(e={});var i=s(e),r=e.encode,n=void 0===r?function(t){return t}:r,a=e.validate,o=void 0===a||a,l=t.map(function(t){if("object"==typeof t)return RegExp("^(?:"+t.pattern+")$",i)});return function(e){for(var i="",r=0;r<t.length;r++){var s=t[r];if("string"==typeof s){i+=s;continue}var a=e?e[s.name]:void 0,u="?"===s.modifier||"*"===s.modifier,h="*"===s.modifier||"+"===s.modifier;if(Array.isArray(a)){if(!h)throw TypeError('Expected "'+s.name+'" to not repeat, but got an array');if(0===a.length){if(u)continue;throw TypeError('Expected "'+s.name+'" to not be empty')}for(var c=0;c<a.length;c++){var d=n(a[c],s);if(o&&!l[r].test(d))throw TypeError('Expected all "'+s.name+'" to match "'+s.pattern+'", but got "'+d+'"');i+=s.prefix+d+s.suffix}continue}if("string"==typeof a||"number"==typeof a){var d=n(String(a),s);if(o&&!l[r].test(d))throw TypeError('Expected "'+s.name+'" to match "'+s.pattern+'", but got "'+d+'"');i+=s.prefix+d+s.suffix;continue}if(!u){var p=h?"an array":"a string";throw TypeError('Expected "'+s.name+'" to be '+p)}}return i}}function r(t,e,i){void 0===i&&(i={});var r=i.decode,n=void 0===r?function(t){return t}:r;return function(i){var r=t.exec(i);if(!r)return!1;for(var s=r[0],a=r.index,o=Object.create(null),l=1;l<r.length;l++)!function(t){if(void 0!==r[t]){var i=e[t-1];"*"===i.modifier||"+"===i.modifier?o[i.name]=r[t].split(i.prefix+i.suffix).map(function(t){return n(t,i)}):o[i.name]=n(r[t],i)}}(l);return{path:s,index:a,params:o}}}function n(t){return t.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function s(t){return t&&t.sensitive?"":"i"}function a(t,e,i){void 0===i&&(i={});for(var r=i.strict,a=void 0!==r&&r,o=i.start,l=i.end,u=i.encode,h=void 0===u?function(t){return t}:u,c="["+n(i.endsWith||"")+"]|$",d="["+n(i.delimiter||"/#?")+"]",p=void 0===o||o?"^":"",m=0;m<t.length;m++){var f=t[m];if("string"==typeof f)p+=n(h(f));else{var g=n(h(f.prefix)),y=n(h(f.suffix));if(f.pattern){if(e&&e.push(f),g||y){if("+"===f.modifier||"*"===f.modifier){var v="*"===f.modifier?"?":"";p+="(?:"+g+"((?:"+f.pattern+")(?:"+y+g+"(?:"+f.pattern+"))*)"+y+")"+v}else p+="(?:"+g+"("+f.pattern+")"+y+")"+f.modifier}else p+="("+f.pattern+")"+f.modifier}else p+="(?:"+g+y+")"+f.modifier}}if(void 0===l||l)a||(p+=d+"?"),p+=i.endsWith?"(?="+c+")":"$";else{var x=t[t.length-1],b="string"==typeof x?d.indexOf(x[x.length-1])>-1:void 0===x;a||(p+="(?:"+d+"(?="+c+"))?"),b||(p+="(?="+d+"|"+c+")")}return new RegExp(p,s(i))}function o(e,i,r){return e instanceof RegExp?function(t,e){if(!e)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var r=0;r<i.length;r++)e.push({name:r,prefix:"",suffix:"",modifier:"",pattern:""});return t}(e,i):Array.isArray(e)?RegExp("(?:"+e.map(function(t){return o(t,i,r).source}).join("|")+")",s(r)):a(t(e,r),i,r)}Object.defineProperty(e,"__esModule",{value:!0}),e.parse=t,e.compile=function(e,r){return i(t(e,r),r)},e.tokensToFunction=i,e.match=function(t,e){var i=[];return r(o(t,i,e),i,e)},e.regexpToFunction=r,e.tokensToRegexp=a,e.pathToRegexp=o})(),t.exports=e})()},8715:t=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var e={};(()=>{e.parse=function(e,i){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var n={},s=e.split(r),a=(i||{}).decode||t,o=0;o<s.length;o++){var l=s[o],u=l.indexOf("=");if(!(u<0)){var h=l.substr(0,u).trim(),c=l.substr(++u,l.length).trim();'"'==c[0]&&(c=c.slice(1,-1)),void 0==n[h]&&(n[h]=function(t,e){try{return e(t)}catch(e){return t}}(c,a))}}return n},e.serialize=function(t,e,r){var s=r||{},a=s.encode||i;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!n.test(t))throw TypeError("argument name is invalid");var o=a(e);if(o&&!n.test(o))throw TypeError("argument val is invalid");var l=t+"="+o;if(null!=s.maxAge){var u=s.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(s.domain){if(!n.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!n.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var t=decodeURIComponent,i=encodeURIComponent,r=/; */,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),t.exports=e})()},8736:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>n});var r=i(8318);let n=async t=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await t.params,"favicon.ico")+""}]},9121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9139:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>u,metadata:()=>l});var r=i(3089),n=i(9044),s=i.n(n),a=i(7778),o=i.n(a);i(2463);let l={title:"Create Next App",description:"Generated by create next app"};function u({children:t}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${s().variable} ${o().variable} antialiased`,children:t})})}},9204:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{STATIC_METADATA_IMAGES:function(){return n},getExtensionRegexString:function(){return a},isMetadataRoute:function(){return h},isMetadataRouteFile:function(){return o},isStaticMetadataRoute:function(){return u},isStaticMetadataRouteFile:function(){return l}});let r=i(8378),n={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},s=["js","jsx","ts","tsx"],a=(t,e)=>e?`(?:\\.(${t.join("|")})|((\\[\\])?\\.(${e.join("|")})))`:`\\.(?:${t.join("|")})`;function o(t,e,i){let s=[RegExp(`^[\\\\/]robots${i?`${a(e.concat("txt"),null)}$`:""}`),RegExp(`^[\\\\/]manifest${i?`${a(e.concat("webmanifest","json"),null)}$`:""}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${i?`${a(["xml"],e)}$`:""}`),RegExp(`[\\\\/]${n.icon.filename}\\d?${i?`${a(n.icon.extensions,e)}$`:""}`),RegExp(`[\\\\/]${n.apple.filename}\\d?${i?`${a(n.apple.extensions,e)}$`:""}`),RegExp(`[\\\\/]${n.openGraph.filename}\\d?${i?`${a(n.openGraph.extensions,e)}$`:""}`),RegExp(`[\\\\/]${n.twitter.filename}\\d?${i?`${a(n.twitter.extensions,e)}$`:""}`)],o=(0,r.normalizePathSep)(t);return s.some(t=>t.test(o))}function l(t){return o(t,[],!0)}function u(t){return"/robots"===t||"/manifest"===t||l(t)}function h(t){let e=t.replace(/^\/?app\//,"").replace(/\/route$/,"");return"/"!==e[0]&&(e="/"+e),!e.endsWith("/page")&&o(e,s,!1)}},9206:(t,e,i)=>{"use strict";let r;i.r(e),i.d(e,{default:()=>s_});var n,s,a=i(5393),o=i(5908);function l(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function u(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function h(t,e,i,r){if("function"==typeof e){let[n,s]=u(r);e=e(void 0!==i?i:t.custom,n,s)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,s]=u(r);e=e(void 0!==i?i:t.custom,n,s)}return e}function c(t,e,i){let r=t.getProps();return h(r,e,void 0!==i?i:r.custom,t)}function d(t,e){return t?.[e]??t?.default??t}let p=t=>t,m={},f=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],g={value:null,addProjectionMetrics:null};function y(t,e){let i=!1,r=!0,n={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,a=f.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,r=new Set,n=!1,s=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){a.has(e)&&(h.schedule(e),t()),l++,e(o)}let h={schedule:(t,e=!1,s=!1)=>{let o=s&&n?i:r;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{r.delete(t),a.delete(t)},process:t=>{if(o=t,n){s=!0;return}n=!0,[i,r]=[r,i],i.forEach(u),e&&g.value&&g.value.frameloop[e].push(l),l=0,i.clear(),n=!1,s&&(s=!1,h.process(t))}};return h}(s,e?i:void 0),t),{}),{setup:o,read:l,resolveKeyframes:u,preUpdate:h,update:c,preRender:d,render:p,postRender:y}=a,v=()=>{let s=m.useManualTiming?n.timestamp:performance.now();i=!1,m.useManualTiming||(n.delta=r?1e3/60:Math.max(Math.min(s-n.timestamp,40),1)),n.timestamp=s,n.isProcessing=!0,o.process(n),l.process(n),u.process(n),h.process(n),c.process(n),d.process(n),p.process(n),y.process(n),n.isProcessing=!1,i&&e&&(r=!1,t(v))},x=()=>{i=!0,r=!0,n.isProcessing||t(v)};return{schedule:f.reduce((t,e)=>{let r=a[e];return t[e]=(t,e=!1,n=!1)=>(i||x(),r.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<f.length;e++)a[f[e]].cancel(t)},state:n,steps:a}}let{schedule:v,cancel:x,state:b,steps:P}=y("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:p,!0),T=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],w=new Set(T),E=new Set(["width","height","top","left","right","bottom",...T]);function A(t,e){-1===t.indexOf(e)&&t.push(e)}function S(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class R{constructor(){this.subscriptions=[]}add(t){return A(this.subscriptions,t),()=>S(this.subscriptions,t)}notify(t,e,i){let r=this.subscriptions.length;if(r){if(1===r)this.subscriptions[0](t,e,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function M(){r=void 0}let C={now:()=>(void 0===r&&C.set(b.isProcessing||m.useManualTiming?b.timestamp:performance.now()),r),set:t=>{r=t,queueMicrotask(M)}},j=t=>!isNaN(parseFloat(t)),k={current:void 0};class D{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=C.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=C.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=j(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new R);let i=this.events[t].add(e);return"change"===t?()=>{i(),v.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return k.current&&k.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=C.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function O(t,e){return new D(t,e)}let V=t=>Array.isArray(t),_=t=>!!(t&&t.getVelocity);function N(t,e){let i=t.getValue("willChange");if(_(i)&&i.add)return i.add(e);if(!i&&m.WillChange){let i=new m.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let L=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),F="data-"+L("framerAppearId"),I=(t,e)=>i=>e(t(i)),$=(...t)=>t.reduce(I),U=(t,e,i)=>i>e?e:i<t?t:i,B=t=>1e3*t,W=t=>t/1e3,z={layout:0,mainThread:0,waapi:0},q=()=>{},X=()=>{},H=t=>e=>"string"==typeof e&&e.startsWith(t),K=H("--"),Y=H("var(--"),G=t=>!!Y(t)&&Z.test(t.split("/*")[0].trim()),Z=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Q={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},J={...Q,transform:t=>U(0,1,t)},tt={...Q,default:1},te=t=>Math.round(1e5*t)/1e5,ti=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tr=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tn=(t,e)=>i=>!!("string"==typeof i&&tr.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),ts=(t,e,i)=>r=>{if("string"!=typeof r)return r;let[n,s,a,o]=r.match(ti);return{[t]:parseFloat(n),[e]:parseFloat(s),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},ta=t=>U(0,255,t),to={...Q,transform:t=>Math.round(ta(t))},tl={test:tn("rgb","red"),parse:ts("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:r=1})=>"rgba("+to.transform(t)+", "+to.transform(e)+", "+to.transform(i)+", "+te(J.transform(r))+")"},tu={test:tn("#"),parse:function(t){let e="",i="",r="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),r=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),r=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,r+=r,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:tl.transform},th=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tc=th("deg"),td=th("%"),tp=th("px"),tm=th("vh"),tf=th("vw"),tg={...td,parse:t=>td.parse(t)/100,transform:t=>td.transform(100*t)},ty={test:tn("hsl","hue"),parse:ts("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:r=1})=>"hsla("+Math.round(t)+", "+td.transform(te(e))+", "+td.transform(te(i))+", "+te(J.transform(r))+")"},tv={test:t=>tl.test(t)||tu.test(t)||ty.test(t),parse:t=>tl.test(t)?tl.parse(t):ty.test(t)?ty.parse(t):tu.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tl.transform(t):ty.transform(t)},tx=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tb="number",tP="color",tT=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tw(t){let e=t.toString(),i=[],r={color:[],number:[],var:[]},n=[],s=0,a=e.replace(tT,t=>(tv.test(t)?(r.color.push(s),n.push(tP),i.push(tv.parse(t))):t.startsWith("var(")?(r.var.push(s),n.push("var"),i.push(t)):(r.number.push(s),n.push(tb),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:a,indexes:r,types:n}}function tE(t){return tw(t).values}function tA(t){let{split:e,types:i}=tw(t),r=e.length;return t=>{let n="";for(let s=0;s<r;s++)if(n+=e[s],void 0!==t[s]){let e=i[s];e===tb?n+=te(t[s]):e===tP?n+=tv.transform(t[s]):n+=t[s]}return n}}let tS=t=>"number"==typeof t?0:t,tR={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(ti)?.length||0)+(t.match(tx)?.length||0)>0},parse:tE,createTransformer:tA,getAnimatableNone:function(t){let e=tE(t);return tA(t)(e.map(tS))}};function tM(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tC(t,e){return i=>i>0?e:t}let tj=(t,e,i)=>t+(e-t)*i,tk=(t,e,i)=>{let r=t*t,n=i*(e*e-r)+r;return n<0?0:Math.sqrt(n)},tD=[tu,tl,ty],tO=t=>tD.find(e=>e.test(t));function tV(t){let e=tO(t);if(q(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===ty&&(i=function({hue:t,saturation:e,lightness:i,alpha:r}){t/=360,i/=100;let n=0,s=0,a=0;if(e/=100){let r=i<.5?i*(1+e):i+e-i*e,o=2*i-r;n=tM(o,r,t+1/3),s=tM(o,r,t),a=tM(o,r,t-1/3)}else n=s=a=i;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*a),alpha:r}}(i)),i}let t_=(t,e)=>{let i=tV(t),r=tV(e);if(!i||!r)return tC(t,e);let n={...i};return t=>(n.red=tk(i.red,r.red,t),n.green=tk(i.green,r.green,t),n.blue=tk(i.blue,r.blue,t),n.alpha=tj(i.alpha,r.alpha,t),tl.transform(n))},tN=new Set(["none","hidden"]);function tL(t,e){return i=>tj(t,e,i)}function tF(t){return"number"==typeof t?tL:"string"==typeof t?G(t)?tC:tv.test(t)?t_:tU:Array.isArray(t)?tI:"object"==typeof t?tv.test(t)?t_:t$:tC}function tI(t,e){let i=[...t],r=i.length,n=t.map((t,i)=>tF(t)(t,e[i]));return t=>{for(let e=0;e<r;e++)i[e]=n[e](t);return i}}function t$(t,e){let i={...t,...e},r={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(r[n]=tF(t[n])(t[n],e[n]));return t=>{for(let e in r)i[e]=r[e](t);return i}}let tU=(t,e)=>{let i=tR.createTransformer(e),r=tw(t),n=tw(e);return r.indexes.var.length===n.indexes.var.length&&r.indexes.color.length===n.indexes.color.length&&r.indexes.number.length>=n.indexes.number.length?tN.has(t)&&!n.values.length||tN.has(e)&&!r.values.length?function(t,e){return tN.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):$(tI(function(t,e){let i=[],r={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let s=e.types[n],a=t.indexes[s][r[s]],o=t.values[a]??0;i[n]=o,r[s]++}return i}(r,n),n.values),i):(q(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tC(t,e))};function tB(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tj(t,e,i):tF(t)(t,e)}let tW=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>v.update(e,t),stop:()=>x(e),now:()=>b.isProcessing?b.timestamp:C.now()}},tz=(t,e,i=10)=>{let r="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)r+=t(e/(n-1))+", ";return`linear(${r.substring(0,r.length-2)})`};function tq(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tX(t,e,i){var r,n;let s=Math.max(e-5,0);return r=i-t(s),(n=e-s)?1e3/n*r:0}let tH={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tK(t,e){return t*Math.sqrt(1-e*e)}let tY=["duration","bounce"],tG=["stiffness","damping","mass"];function tZ(t,e){return e.some(e=>void 0!==t[e])}function tQ(t=tH.visualDuration,e=tH.bounce){let i;let r="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:s}=r,a=r.keyframes[0],o=r.keyframes[r.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:h,mass:c,duration:d,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:tH.velocity,stiffness:tH.stiffness,damping:tH.damping,mass:tH.mass,isResolvedFromDuration:!1,...t};if(!tZ(t,tG)&&tZ(t,tY)){if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),r=i*i,n=2*U(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:tH.mass,stiffness:r,damping:n}}else{let i=function({duration:t=tH.duration,bounce:e=tH.bounce,velocity:i=tH.velocity,mass:r=tH.mass}){let n,s;q(t<=B(tH.maxDuration),"Spring duration must be 10 seconds or less");let a=1-e;a=U(tH.minDamping,tH.maxDamping,a),t=U(tH.minDuration,tH.maxDuration,W(t)),a<1?(n=e=>{let r=e*a,n=r*t;return .001-(r-i)/tK(e,a)*Math.exp(-n)},s=e=>{let r=e*a*t,s=Math.pow(a,2)*Math.pow(e,2)*t,o=Math.exp(-r),l=tK(Math.pow(e,2),a);return(r*i+i-s)*o*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let o=function(t,e,i){let r=i;for(let i=1;i<12;i++)r-=t(r)/e(r);return r}(n,s,5/t);if(t=B(t),isNaN(o))return{stiffness:tH.stiffness,damping:tH.damping,duration:t};{let e=Math.pow(o,2)*r;return{stiffness:e,damping:2*a*Math.sqrt(r*e),duration:t}}}(t);(e={...e,...i,mass:tH.mass}).isResolvedFromDuration=!0}}return e}({...r,velocity:-W(r.velocity||0)}),f=p||0,g=h/(2*Math.sqrt(u*c)),y=o-a,v=W(Math.sqrt(u/c)),x=5>Math.abs(y);if(n||(n=x?tH.restSpeed.granular:tH.restSpeed.default),s||(s=x?tH.restDelta.granular:tH.restDelta.default),g<1){let t=tK(v,g);i=e=>o-Math.exp(-g*v*e)*((f+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>o-Math.exp(-v*t)*(y+(f+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),r=Math.min(t*e,300);return o-i*((f+g*v*y)*Math.sinh(r)+t*y*Math.cosh(r))/t}}let b={calculatedDuration:m&&d||null,next:t=>{let e=i(t);if(m)l.done=t>=d;else{let r=0===t?f:0;g<1&&(r=0===t?B(f):tX(i,t,e));let a=Math.abs(r)<=n,u=Math.abs(o-e)<=s;l.done=a&&u}return l.value=l.done?o:e,l},toString:()=>{let t=Math.min(tq(b),2e4),e=tz(e=>b.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return b}function tJ({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:h}){let c,d;let p=t[0],m={done:!1,value:p},f=t=>void 0!==o&&t<o||void 0!==l&&t>l,g=t=>void 0===o?l:void 0===l?o:Math.abs(o-t)<Math.abs(l-t)?o:l,y=i*e,v=p+y,x=void 0===a?v:a(v);x!==v&&(y=x-p);let b=t=>-y*Math.exp(-t/r),P=t=>x+b(t),T=t=>{let e=b(t),i=P(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},w=t=>{f(m.value)&&(c=t,d=tQ({keyframes:[m.value,g(m.value)],velocity:tX(P,t,m.value),damping:n,stiffness:s,restDelta:u,restSpeed:h}))};return w(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,T(t),w(t)),void 0!==c&&t>=c)?d.next(t-c):(e||T(t),m)}}}tQ.applyToOptions=t=>{let e=function(t,e=100,i){let r=i({...t,keyframes:[0,e]}),n=Math.min(tq(r),2e4);return{type:"keyframes",ease:t=>r.next(n*t).value/e,duration:W(n)}}(t,100,tQ);return t.ease=e.ease,t.duration=B(e.duration),t.type="keyframes",t};let t0=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function t1(t,e,i,r){if(t===e&&i===r)return p;let n=e=>(function(t,e,i,r,n){let s,a;let o=0;do(s=t0(a=e+(i-e)/2,r,n)-t)>0?i=a:e=a;while(Math.abs(s)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:t0(n(t),e,r)}let t2=t1(.42,0,1,1),t3=t1(0,0,.58,1),t5=t1(.42,0,.58,1),t9=t=>Array.isArray(t)&&"number"!=typeof t[0],t6=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t8=t=>e=>1-t(1-e),t4=t1(.33,1.53,.69,.99),t7=t8(t4),et=t6(t7),ee=t=>(t*=2)<1?.5*t7(t):.5*(2-Math.pow(2,-10*(t-1))),ei=t=>1-Math.sin(Math.acos(t)),er=t8(ei),en=t6(ei),es=t=>Array.isArray(t)&&"number"==typeof t[0],ea={linear:p,easeIn:t2,easeInOut:t5,easeOut:t3,circIn:ei,circInOut:en,circOut:er,backIn:t7,backInOut:et,backOut:t4,anticipate:ee},eo=t=>"string"==typeof t,el=t=>{if(es(t)){X(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,r,n]=t;return t1(e,i,r,n)}return eo(t)?(X(void 0!==ea[t],`Invalid easing type '${t}'`),ea[t]):t},eu=(t,e,i)=>{let r=e-t;return 0===r?1:(i-t)/r};function eh({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){let n=t9(r)?r.map(el):el(r),s={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:r,mixer:n}={}){let s=t.length;if(X(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let a=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,i){let r=[],n=i||m.mix||tB,s=t.length-1;for(let i=0;i<s;i++){let s=n(t[i],t[i+1]);e&&(s=$(Array.isArray(e)?e[i]||p:e,s)),r.push(s)}return r}(e,r,n),l=o.length,u=i=>{if(a&&i<t[0])return e[0];let r=0;if(l>1)for(;r<t.length-2&&!(i<t[r+1]);r++);let n=eu(t[r],t[r+1],i);return o[r](n)};return i?e=>u(U(t[0],t[s-1],e)):u}((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let n=eu(0,e,r);t.push(tj(i,1,n))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(n)?n:e.map(()=>n||t5).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=a(e),s.done=e>=t,s)}}let ec=t=>null!==t;function ed(t,{repeat:e,repeatType:i="loop"},r,n=1){let s=t.filter(ec),a=n<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return a&&void 0!==r?r:s[a]}let ep={decay:tJ,inertia:tJ,tween:eh,keyframes:eh,spring:tQ};function em(t){"string"==typeof t.type&&(t.type=ep[t.type])}class ef{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let eg=t=>t/100;class ey extends ef{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==C.now()&&this.tick(C.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},z.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;em(t);let{type:e=eh,repeat:i=0,repeatDelay:r=0,repeatType:n,velocity:s=0}=t,{keyframes:a}=t,o=e||eh;o!==eh&&"number"!=typeof a[0]&&(this.mixKeyframes=$(eg,tB(a[0],a[1])),a=[0,100]);let l=o({...t,keyframes:a});"mirror"===n&&(this.mirroredGenerator=o({...t,keyframes:[...a].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=tq(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:r,mixKeyframes:n,mirroredGenerator:s,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:c,repeatDelay:d,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-r/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,x=i;if(h){let t=Math.min(this.currentTime,r)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===c?(i=1-i,d&&(i-=d/a)):"mirror"===c&&(x=s)),v=U(0,1,i)*a}let b=y?{done:!1,value:u[0]}:x.next(v);n&&(b.value=n(b.value));let{done:P}=b;y||null===o||(P=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return T&&p!==tJ&&(b.value=ed(u,this.options,f,this.speed)),m&&m(b.value),T&&this.finish(),b}then(t,e){return this.finished.then(t,e)}get duration(){return W(this.calculatedDuration)}get time(){return W(this.currentTime)}set time(t){t=B(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(C.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=W(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tW,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(C.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,z.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let ev=t=>180*t/Math.PI,ex=t=>eP(ev(Math.atan2(t[1],t[0]))),eb={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ex,rotateZ:ex,skewX:t=>ev(Math.atan(t[1])),skewY:t=>ev(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},eP=t=>((t%=360)<0&&(t+=360),t),eT=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ew=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),eE={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:eT,scaleY:ew,scale:t=>(eT(t)+ew(t))/2,rotateX:t=>eP(ev(Math.atan2(t[6],t[5]))),rotateY:t=>eP(ev(Math.atan2(-t[2],t[0]))),rotateZ:ex,rotate:ex,skewX:t=>ev(Math.atan(t[4])),skewY:t=>ev(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function eA(t){return+!!t.includes("scale")}function eS(t,e){let i,r;if(!t||"none"===t)return eA(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=eE,r=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=eb,r=e}if(!r)return eA(e);let s=i[e],a=r[1].split(",").map(eM);return"function"==typeof s?s(a):a[s]}let eR=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eS(i,e)};function eM(t){return parseFloat(t.trim())}let eC=t=>t===Q||t===tp,ej=new Set(["x","y","z"]),ek=T.filter(t=>!ej.has(t)),eD={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eS(e,"x"),y:(t,{transform:e})=>eS(e,"y")};eD.translateX=eD.x,eD.translateY=eD.y;let eO=new Set,eV=!1,e_=!1,eN=!1;function eL(){if(e_){let t=Array.from(eO).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ek.forEach(i=>{let r=t.getValue(i);void 0!==r&&(e.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}e_=!1,eV=!1,eO.forEach(t=>t.complete(eN)),eO.clear()}function eF(){eO.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(e_=!0)})}class eI{constructor(t,e,i,r,n,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=r,this.element=n,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(eO.add(this),eV||(eV=!0,v.read(eF),v.resolveKeyframes(eL))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:r}=this;if(null===t[0]){let n=r?.get(),s=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let r=i.readValue(e,s);null!=r&&(t[0]=r)}void 0===t[0]&&(t[0]=s),r&&void 0===n&&r.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eO.delete(this)}cancel(){"scheduled"===this.state&&(eO.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let e$=t=>t.startsWith("--");function eU(t){let e;return()=>(void 0===e&&(e=t()),e)}let eB=eU(()=>void 0!==window.ScrollTimeline),eW={},ez=function(t,e){let i=eU(t);return()=>eW[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eq=([t,e,i,r])=>`cubic-bezier(${t}, ${e}, ${i}, ${r})`,eX={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eq([0,.65,.55,1]),circOut:eq([.55,0,1,.45]),backIn:eq([.31,.01,.66,-.59]),backOut:eq([.33,1.53,.69,.99])};function eH(t){return"function"==typeof t&&"applyToOptions"in t}class eK extends ef{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:r,pseudoElement:n,allowFlatten:s=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!n,this.allowFlatten=s,this.options=t,X("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return eH(t)&&ez()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:r=0,duration:n=300,repeat:s=0,repeatType:a="loop",ease:o="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let c=function t(e,i){if(e)return"function"==typeof e?ez()?tz(e,i):"ease-out":es(e)?eq(e):Array.isArray(e)?e.map(e=>t(e,i)||eX.easeOut):eX[e]}(o,n);Array.isArray(c)&&(h.easing=c),g.value&&z.waapi++;let d={delay:r,duration:n,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===a?"alternate":"normal"};u&&(d.pseudoElement=u);let p=t.animate(h,d);return g.value&&p.finished.finally(()=>{z.waapi--}),p}(e,i,r,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=ed(r,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){e$(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return W(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return W(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=B(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eB())?(this.animation.timeline=t,p):e(this)}}let eY={anticipate:ee,backInOut:et,circInOut:en};class eG extends eK{constructor(t){(function(t){"string"==typeof t.ease&&t.ease in eY&&(t.ease=eY[t.ease])})(t),em(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:r,element:n,...s}=this.options;if(!e)return;if(void 0!==t){e.set(t);return}let a=new ey({...s,autoplay:!1}),o=B(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let eZ=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tR.test(t)||"0"===t)&&!t.startsWith("url("));function eQ(t){return"object"==typeof t&&null!==t}function eJ(t){return eQ(t)&&"offsetHeight"in t}let e0=new Set(["opacity","clipPath","filter","transform"]),e1=eU(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class e2 extends ef{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:s="loop",keyframes:a,name:o,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=C.now();let c={autoplay:t,delay:e,type:i,repeat:r,repeatDelay:n,repeatType:s,name:o,motionValue:l,element:u,...h},d=u?.KeyframeResolver||eI;this.keyframeResolver=new d(a,(t,e,i)=>this.onKeyframesResolved(t,e,c,!i),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,r){this.keyframeResolver=void 0;let{name:n,type:s,velocity:a,delay:o,isHandoff:l,onUpdate:u}=i;this.resolvedAt=C.now(),!function(t,e,i,r){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],a=eZ(n,e),o=eZ(s,e);return q(a===o,`You are trying to animate ${e} from "${n}" to "${s}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${s} via the \`style\` property.`),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||eH(i))&&r)}(t,n,s,a)&&((m.instantAnimations||!o)&&u?.(ed(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let h={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},c=!l&&function(t){let{motionValue:e,name:i,repeatDelay:r,repeatType:n,damping:s,type:a}=t;if(!eJ(e?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return e1()&&i&&e0.has(i)&&("transform"!==i||!l)&&!o&&!r&&"mirror"!==n&&0!==s&&"inertia"!==a}(h)?new eG({...h,element:h.motionValue.owner.current}):new ey(h);c.finished.then(()=>this.notifyFinished()).catch(p),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eN=!0,eF(),eL(),eN=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e3=t=>null!==t,e5={type:"spring",stiffness:500,damping:25,restSpeed:10},e9=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e6={type:"keyframes",duration:.8},e8={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e4=(t,{keyframes:e})=>e.length>2?e6:w.has(t)?t.startsWith("scale")?e9(e[1]):e5:e8,e7=(t,e,i,r={},n,s)=>a=>{let o=d(r,t)||{},l=o.delay||r.delay||0,{elapsed:u=0}=r;u-=B(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...o,delay:-u,onUpdate:t=>{e.set(t),o.onUpdate&&o.onUpdate(t)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:t,motionValue:e,element:s?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:r,staggerDirection:n,repeat:s,repeatType:a,repeatDelay:o,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(o)&&Object.assign(h,e4(t,h)),h.duration&&(h.duration=B(h.duration)),h.repeatDelay&&(h.repeatDelay=B(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let c=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0!==h.delay||(c=!0)),(m.instantAnimations||m.skipAnimations)&&(c=!0,h.duration=0,h.delay=0),h.allowFlatten=!o.type&&!o.ease,c&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},r){let n=t.filter(e3),s=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[s]}(h.keyframes,o);if(void 0!==t){v.update(()=>{h.onUpdate(t),h.onComplete()});return}}return o.isSync?new ey(h):new e2(h)};function it(t,e,{delay:i=0,transitionOverride:r,type:n}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:a,...o}=e;r&&(s=r);let l=[],u=n&&t.animationState&&t.animationState.getState()[n];for(let e in o){let r=t.getValue(e,t.latestValues[e]??null),n=o[e];if(void 0===n||u&&function({protectedKeys:t,needsAnimating:e},i){let r=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,r}(u,e))continue;let a={delay:i,...d(s||{},e)},h=r.get();if(void 0!==h&&!r.isAnimating&&!Array.isArray(n)&&n===h&&!a.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){let i=t.props[F];if(i){let t=window.MotionHandoffAnimation(i,e,v);null!==t&&(a.startTime=t,c=!0)}}N(t,e),r.start(e7(e,r,n,t.shouldReduceMotion&&E.has(e)?{type:!1}:a,t,c));let p=r.animation;p&&l.push(p)}return a&&Promise.all(l).then(()=>{v.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:r={},...n}=c(t,e)||{};for(let e in n={...n,...i}){var s;let i=V(s=n[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,O(i))}}(t,a)})}),l}function ie(t,e,i={}){let r=c(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(n=i.transitionOverride);let s=r?()=>Promise.all(it(t,r,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:s=0,staggerChildren:a,staggerDirection:o}=n;return function(t,e,i=0,r=0,n=1,s){let a=[],o=(t.variantChildren.size-1)*r,l=1===n?(t=0)=>t*r:(t=0)=>o-t*r;return Array.from(t.variantChildren).sort(ii).forEach((t,r)=>{t.notify("AnimationStart",e),a.push(ie(t,e,{...s,delay:i+l(r)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,s+r,a,o,i)}:()=>Promise.resolve(),{when:o}=n;if(!o)return Promise.all([s(),a(i.delay)]);{let[t,e]="beforeChildren"===o?[s,a]:[a,s];return t().then(()=>e())}}function ii(t,e){return t.sortNodePosition(e)}function ir(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let r=0;r<i;r++)if(e[r]!==t[r])return!1;return!0}function is(t){return"string"==typeof t||Array.isArray(t)}let ia=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],io=["initial",...ia],il=io.length,iu=[...ia].reverse(),ih=ia.length;function ic(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function id(){return{animate:ic(!0),whileInView:ic(),whileHover:ic(),whileTap:ic(),whileDrag:ic(),whileFocus:ic(),exit:ic()}}class ip{constructor(t){this.isMounted=!1,this.node=t}update(){}}class im extends ip{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>ie(t,e,i)));else if("string"==typeof e)r=ie(t,e,i);else{let n="function"==typeof e?c(t,e,i.custom):e;r=Promise.all(it(t,n,i))}return r.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=id(),r=!0,n=e=>(i,r)=>{let n=c(t,r,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...r}=n;i={...i,...r,...e}}return i};function s(s){let{props:a}=t,o=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<il;t++){let r=io[t],n=e.props[r];(is(n)||!1===n)&&(i[r]=n)}return i}(t.parent)||{},u=[],h=new Set,d={},p=1/0;for(let e=0;e<ih;e++){var m,f;let c=iu[e],g=i[c],y=void 0!==a[c]?a[c]:o[c],v=is(y),x=c===s?g.isActive:null;!1===x&&(p=e);let b=y===o[c]&&y!==a[c]&&v;if(b&&r&&t.manuallyAnimateOnMount&&(b=!1),g.protectedKeys={...d},!g.isActive&&null===x||!y&&!g.prevProp||l(y)||"boolean"==typeof y)continue;let P=(m=g.prevProp,"string"==typeof(f=y)?f!==m:!!Array.isArray(f)&&!ir(f,m)),T=P||c===s&&g.isActive&&!b&&v||e>p&&v,w=!1,E=Array.isArray(y)?y:[y],A=E.reduce(n(c),{});!1===x&&(A={});let{prevResolvedValues:S={}}=g,R={...S,...A},M=e=>{T=!0,h.has(e)&&(w=!0,h.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in R){let e=A[t],i=S[t];if(d.hasOwnProperty(t))continue;let r=!1;(V(e)&&V(i)?ir(e,i):e===i)?void 0!==e&&h.has(t)?M(t):g.protectedKeys[t]=!0:null!=e?M(t):h.add(t)}g.prevProp=y,g.prevResolvedValues=A,g.isActive&&(d={...d,...A}),r&&t.blockInitialAnimation&&(T=!1);let C=!(b&&P)||w;T&&C&&u.push(...E.map(t=>({animation:t,options:{type:c}})))}if(h.size){let e={};if("boolean"!=typeof a.initial){let i=c(t,Array.isArray(a.initial)?a.initial[0]:a.initial);i&&i.transition&&(e.transition=i.transition)}h.forEach(i=>{let r=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=r??null}),u.push({animation:e})}let g=!!u.length;return r&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(g=!1),r=!1,g?e(u):Promise.resolve()}return{animateChanges:s,setActive:function(e,r){if(i[e].isActive===r)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,r)),i[e].isActive=r;let n=s(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=id(),r=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();l(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ig=0;class iy extends ip{constructor(){super(...arguments),this.id=ig++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let iv={x:!1,y:!1};function ix(t,e,i,r={passive:!0}){return t.addEventListener(e,i,r),()=>t.removeEventListener(e,i)}let ib=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iP(t){return{point:{x:t.pageX,y:t.pageY}}}let iT=t=>e=>ib(e)&&t(e,iP(e));function iw(t,e,i,r){return ix(t,e,iT(i),r)}function iE({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}function iA(t){return t.max-t.min}function iS(t,e,i,r=.5){t.origin=r,t.originPoint=tj(e.min,e.max,t.origin),t.scale=iA(i)/iA(e),t.translate=tj(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iR(t,e,i,r){iS(t.x,e.x,i.x,r?r.originX:void 0),iS(t.y,e.y,i.y,r?r.originY:void 0)}function iM(t,e,i){t.min=i.min+e.min,t.max=t.min+iA(e)}function iC(t,e,i){t.min=e.min-i.min,t.max=t.min+iA(e)}function ij(t,e,i){iC(t.x,e.x,i.x),iC(t.y,e.y,i.y)}let ik=()=>({translate:0,scale:1,origin:0,originPoint:0}),iD=()=>({x:ik(),y:ik()}),iO=()=>({min:0,max:0}),iV=()=>({x:iO(),y:iO()});function i_(t){return[t("x"),t("y")]}function iN(t){return void 0===t||1===t}function iL({scale:t,scaleX:e,scaleY:i}){return!iN(t)||!iN(e)||!iN(i)}function iF(t){return iL(t)||iI(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iI(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function i$(t,e,i,r,n){return void 0!==n&&(t=r+n*(t-r)),r+i*(t-r)+e}function iU(t,e=0,i=1,r,n){t.min=i$(t.min,e,i,r,n),t.max=i$(t.max,e,i,r,n)}function iB(t,{x:e,y:i}){iU(t.x,e.translate,e.scale,e.originPoint),iU(t.y,i.translate,i.scale,i.originPoint)}function iW(t,e){t.min=t.min+e,t.max=t.max+e}function iz(t,e,i,r,n=.5){let s=tj(t.min,t.max,n);iU(t,e,i,s,r)}function iq(t,e){iz(t.x,e.x,e.scaleX,e.scale,e.originX),iz(t.y,e.y,e.scaleY,e.scale,e.originY)}function iX(t,e){return iE(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(t.getBoundingClientRect(),e))}let iH=({current:t})=>t?t.ownerDocument.defaultView:null;function iK(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iY=(t,e)=>Math.abs(t-e);class iG{constructor(t,e,{transformPagePoint:i,contextWindow:r,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iJ(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iY(t.x,e.x)**2+iY(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:r}=t,{timestamp:n}=b;this.history.push({...r,timestamp:n});let{onStart:s,onMove:a}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iZ(e,this.transformPagePoint),v.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iJ("pointercancel"===t.type?this.lastMoveEventInfo:iZ(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),r&&r(t,s)},!ib(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=r||window;let s=iZ(iP(t),this.transformPagePoint),{point:a}=s,{timestamp:o}=b;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=e;l&&l(t,iJ(s,this.history)),this.removeListeners=$(iw(this.contextWindow,"pointermove",this.handlePointerMove),iw(this.contextWindow,"pointerup",this.handlePointerUp),iw(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),x(this.updatePoint)}}function iZ(t,e){return e?{point:e(t.point)}:t}function iQ(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iJ({point:t},e){return{point:t,delta:iQ(t,i0(e)),offset:iQ(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,r=null,n=i0(t);for(;i>=0&&(r=t[i],!(n.timestamp-r.timestamp>B(.1)));)i--;if(!r)return{x:0,y:0};let s=W(n.timestamp-r.timestamp);if(0===s)return{x:0,y:0};let a={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function i0(t){return t[t.length-1]}function i1(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function i2(t,e){let i=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,r]=[r,i]),{min:i,max:r}}function i3(t,e,i){return{min:i5(t,e),max:i5(t,i)}}function i5(t,e){return"number"==typeof t?t:t[e]||0}let i9=new WeakMap;class i6{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iV(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new iG(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iP(t).point)},onStart:(t,e)=>{var i;let{drag:r,dragPropagation:n,onDragStart:s}=this.getProps();if(r&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(i=r)||"y"===i?iv[i]?null:(iv[i]=!0,()=>{iv[i]=!1}):iv.x||iv.y?null:(iv.x=iv.y=!0,()=>{iv.x=iv.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),i_(t=>{let e=this.getAxisMotionValue(t).get()||0;if(td.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[t];r&&(e=iA(r)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&v.postRender(()=>s(t,e)),N(this.visualElement,"transform");let{animationState:a}=this.visualElement;a&&a.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(r&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>i_(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:iH(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:r}=e;this.startAnimation(r);let{onDragEnd:n}=this.getProps();n&&v.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:r}=this.getProps();if(!i||!i8(t,r,this.currentDirection))return;let n=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},r){return void 0!==e&&t<e?t=r?tj(e,t,r.min):Math.max(t,e):void 0!==i&&t>i&&(t=r?tj(i,t,r.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),n.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;t&&iK(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:r,right:n}){return{x:i1(t.x,i,n),y:i1(t.y,e,r)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i3(t,"left","right"),y:i3(t,"top","bottom")}}(e),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&i_(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iK(e))return!1;let r=e.current;X(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(t,e,i){let r=iX(t,i),{scroll:n}=e;return n&&(iW(r.x,n.offset.x),iW(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),a={x:i2((t=n.layout.layoutBox).x,s.x),y:i2(t.y,s.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=iE(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(i_(a=>{if(!i8(a,e,this.currentDirection))return;let l=o&&o[a]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[a]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return N(this.visualElement,t),i.start(e7(t,i,0,e,this.visualElement,!1))}stopAnimation(){i_(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){i_(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){i_(e=>{let{drag:i}=this.getProps();if(!i8(e,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(e);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[e];n.set(t[e]-tj(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iK(e)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};i_(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();r[t]=function(t,e){let i=.5,r=iA(t),n=iA(e);return n>r?i=eu(e.min,e.max-r,t.min):r>n&&(i=eu(t.min,t.max-n,e.min)),U(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),i_(e=>{if(!i8(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:s}=this.constraints[e];i.set(tj(n,s,r[e]))})}addListeners(){if(!this.visualElement.current)return;i9.set(this.visualElement,this);let t=iw(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iK(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),v.read(e);let n=ix(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(i_(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),r(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:a}}}function i8(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i4 extends ip{constructor(t){super(t),this.removeGroupControls=p,this.removeListeners=p,this.controls=new i6(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||p}unmount(){this.removeGroupControls(),this.removeListeners()}}let i7=t=>(e,i)=>{t&&v.postRender(()=>t(e,i))};class rt extends ip{constructor(){super(...arguments),this.removePointerDownListener=p}onPointerDown(t){this.session=new iG(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iH(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:i7(t),onStart:i7(e),onMove:i,onEnd:(t,e)=>{delete this.session,r&&v.postRender(()=>r(t,e))}}}mount(){this.removePointerDownListener=iw(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let{schedule:re}=y(queueMicrotask,!1),ri=(0,o.createContext)(null),rr=(0,o.createContext)({}),rn=(0,o.createContext)({}),rs={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ra(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let ro={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!tp.test(t))return t;t=parseFloat(t)}let i=ra(t,e.target.x),r=ra(t,e.target.y);return`${i}% ${r}%`}},rl={};class ru extends o.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=t;(function(t){for(let e in t)rl[e]=t[e],K(e)&&(rl[e].isCSSVariable=!0)})(rc),n&&(e.group&&e.group.add(n),i&&i.register&&r&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),rs.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:r,isPresent:n}=this.props,{projection:s}=i;return s&&(s.isPresent=n,r||t.layoutDependency!==e||void 0===e||t.isPresent!==n?s.willUpdate():this.safeToRemove(),t.isPresent===n||(n?s.promote():s.relegate()||v.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),re.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function rh(t){let[e,i]=function(t=!0){let e=(0,o.useContext)(ri);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:r,register:n}=e,s=(0,o.useId)();(0,o.useEffect)(()=>{if(t)return n(s)},[t]);let a=(0,o.useCallback)(()=>t&&r&&r(s),[s,r,t]);return!i&&r?[!1,a]:[!0]}(),r=(0,o.useContext)(rr);return(0,a.jsx)(ru,{...t,layoutGroup:r,switchLayoutGroup:(0,o.useContext)(rn),isPresent:e,safeToRemove:i})}let rc={borderRadius:{...ro,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ro,borderTopRightRadius:ro,borderBottomLeftRadius:ro,borderBottomRightRadius:ro,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let r=tR.parse(t);if(r.length>5)return t;let n=tR.createTransformer(t),s=+("number"!=typeof r[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;r[0+s]/=a,r[1+s]/=o;let l=tj(a,o,.5);return"number"==typeof r[2+s]&&(r[2+s]/=l),"number"==typeof r[3+s]&&(r[3+s]/=l),n(r)}}};function rd(t){return eQ(t)&&"ownerSVGElement"in t}let rp=(t,e)=>t.depth-e.depth;class rm{constructor(){this.children=[],this.isDirty=!1}add(t){A(this.children,t),this.isDirty=!0}remove(t){S(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(rp),this.isDirty=!1,this.children.forEach(t)}}function rf(t){return _(t)?t.get():t}let rg=["TopLeft","TopRight","BottomLeft","BottomRight"],ry=rg.length,rv=t=>"string"==typeof t?parseFloat(t):t,rx=t=>"number"==typeof t||tp.test(t);function rb(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let rP=rw(0,.5,er),rT=rw(.5,.95,p);function rw(t,e,i){return r=>r<t?0:r>e?1:i(eu(t,e,r))}function rE(t,e){t.min=e.min,t.max=e.max}function rA(t,e){rE(t.x,e.x),rE(t.y,e.y)}function rS(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function rR(t,e,i,r,n){return t-=e,t=r+1/i*(t-r),void 0!==n&&(t=r+1/n*(t-r)),t}function rM(t,e,[i,r,n],s,a){!function(t,e=0,i=1,r=.5,n,s=t,a=t){if(td.test(e)&&(e=parseFloat(e),e=tj(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=tj(s.min,s.max,r);t===s&&(o-=e),t.min=rR(t.min,e,i,o,n),t.max=rR(t.max,e,i,o,n)}(t,e[i],e[r],e[n],e.scale,s,a)}let rC=["x","scaleX","originX"],rj=["y","scaleY","originY"];function rk(t,e,i,r){rM(t.x,e,rC,i?i.x:void 0,r?r.x:void 0),rM(t.y,e,rj,i?i.y:void 0,r?r.y:void 0)}function rD(t){return 0===t.translate&&1===t.scale}function rO(t){return rD(t.x)&&rD(t.y)}function rV(t,e){return t.min===e.min&&t.max===e.max}function r_(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function rN(t,e){return r_(t.x,e.x)&&r_(t.y,e.y)}function rL(t){return iA(t.x)/iA(t.y)}function rF(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class rI{constructor(){this.members=[]}add(t){A(this.members,t),t.scheduleRender()}remove(t){if(S(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let r$={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rU=["","X","Y","Z"],rB={visibility:"hidden"},rW=0;function rz(t,e,i,r){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),r&&(r[t]=0))}function rq({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:r,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=rW++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,g.value&&(r$.nodes=r$.calculatedTargetDeltas=r$.calculatedProjections=0),this.nodes.forEach(rK),this.nodes.forEach(r1),this.nodes.forEach(r2),this.nodes.forEach(rY),g.addProjectionMetrics&&g.addProjectionMetrics(r$)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new rm)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new R),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=rd(e)&&!(rd(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),t){let i;let r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=C.now(),r=({timestamp:n})=>{let s=n-i;s>=250&&(x(r),t(s-e))};return v.setup(r,!0),()=>x(r)}(r,250),rs.hasAnimatedSinceResize&&(rs.hasAnimatedSinceResize=!1,this.nodes.forEach(r0))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||r4,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=n.getProps(),l=!this.targetLayout||!rN(this.targetLayout,r),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...d(s,"layout"),onPlay:a,onComplete:o};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||r0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),x(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r3),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let r=i.props[F];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",v,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rZ);return}this.isUpdating||this.nodes.forEach(rQ),this.isUpdating=!1,this.nodes.forEach(rJ),this.nodes.forEach(rX),this.nodes.forEach(rH),this.clearAllSnapshots();let t=C.now();b.delta=U(0,1e3/60,t-b.timestamp),b.timestamp=t,b.isProcessing=!0,P.update.process(b),P.preRender.process(b),P.render.process(b),b.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,re.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rG),this.sharedNodes.forEach(r5)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,v.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){v.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||iA(this.snapshot.measuredBox.x)||iA(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iV(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!rO(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,s=r!==this.prevTransformTemplateValue;t&&this.instance&&(e||iF(this.latestValues)||s)&&(n(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),ne((e=r).x),ne(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iV();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(nr))){let{scroll:t}=this.root;t&&(iW(e.x,t.offset.x),iW(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iV();if(rA(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let r=this.path[i],{scroll:n,options:s}=r;r!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&rA(e,t),iW(e.x,n.offset.x),iW(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=iV();rA(i,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iq(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),iF(r.latestValues)&&iq(i,r.latestValues)}return iF(this.latestValues)&&iq(i,this.latestValues),i}removeTransform(t){let e=iV();rA(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iF(i.latestValues))continue;iL(i.latestValues)&&i.updateSnapshot();let r=iV();rA(r,i.measurePageBox()),rk(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return iF(this.latestValues)&&rk(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==b.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:n}=this.options;if(this.layout&&(r||n)){if(this.resolvedRelativeTargetAt=b.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iV(),this.relativeTargetOrigin=iV(),ij(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),rA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iV(),this.targetWithTransforms=iV()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,a,o;this.forceRelativeParentToResolveTarget(),s=this.target,a=this.relativeTarget,o=this.relativeParent.target,iM(s.x,a.x,o.x),iM(s.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rA(this.target,this.layout.layoutBox),iB(this.target,this.targetDelta)):rA(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iV(),this.relativeTargetOrigin=iV(),ij(this.relativeTargetOrigin,this.target,t.target),rA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}g.value&&r$.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||iL(this.parent.latestValues)||iI(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===b.timestamp&&(i=!1),i)return;let{layout:r,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||n))return;rA(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;(function(t,e,i,r=!1){let n,s;let a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){s=(n=i[o]).projectionDelta;let{visualElement:a}=n.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(r&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iq(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,iB(t,s)),r&&iF(n.latestValues)&&iq(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}})(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iV());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rS(this.prevProjectionDelta.x,this.projectionDelta.x),rS(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iR(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===s&&this.treeScale.y===a&&rF(this.projectionDelta.x,this.prevProjectionDelta.x)&&rF(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),g.value&&r$.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iD(),this.projectionDelta=iD(),this.projectionDeltaWithTransform=iD()}setAnimationOrigin(t,e=!1){let i;let r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},a=iD();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=iV(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(r8));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(r9(a.x,t.x,r),r9(a.y,t.y,r),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,m,f,g;if(ij(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,g=r,r6(p.x,m.x,f.x,g),r6(p.y,m.y,f.y,g),i&&(u=this.relativeTarget,d=i,rV(u.x,d.x)&&rV(u.y,d.y)))this.isProjectionDirty=!1;i||(i=iV()),rA(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,r,n,s){n?(t.opacity=tj(0,i.opacity??1,rP(r)),t.opacityExit=tj(e.opacity??1,0,rT(r))):s&&(t.opacity=tj(e.opacity??1,i.opacity??1,r));for(let n=0;n<ry;n++){let s=`border${rg[n]}Radius`,a=rb(e,s),o=rb(i,s);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||rx(a)===rx(o)?(t[s]=Math.max(tj(rv(a),rv(o),r),0),(td.test(o)||td.test(a))&&(t[s]+="%")):t[s]=o)}(e.rotate||i.rotate)&&(t.rotate=tj(e.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(x(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=v.update(()=>{rs.hasAnimatedSinceResize=!0,z.layout++,this.motionValue||(this.motionValue=O(0)),this.currentAnimation=function(t,e,i){let r=_(t)?t:O(t);return r.start(e7("",r,e,i)),r.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{z.layout--},onComplete:()=>{z.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:r,latestValues:n}=t;if(e&&i&&r){if(this!==t&&this.layout&&r&&ni(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||iV();let e=iA(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let r=iA(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+r}rA(e,i),iq(e,n),iR(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new rI),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let r={};i.z&&rz("z",t,r,this.animationValues);for(let e=0;e<rU.length;e++)rz(`rotate${rU[e]}`,t,r,this.animationValues),rz(`skew${rU[e]}`,t,r,this.animationValues);for(let e in t.render(),r)t.setStaticValue(e,r[e]),this.animationValues&&(this.animationValues[e]=r[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return rB;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=rf(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=rf(t?.pointerEvents)||""),this.hasProjected&&!iF(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let n=r.animationValues||r.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let r="",n=t.x.translate/e.x,s=t.y.translate/e.y,a=i?.z||0;if((n||s||a)&&(r=`translate3d(${n}px, ${s}px, ${a}px) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:s,skewX:a,skewY:o}=i;t&&(r=`perspective(${t}px) ${r}`),e&&(r+=`rotate(${e}deg) `),n&&(r+=`rotateX(${n}deg) `),s&&(r+=`rotateY(${s}deg) `),a&&(r+=`skewX(${a}deg) `),o&&(r+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(r+=`scale(${o}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(e.transform=i(n,e.transform));let{x:s,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*a.origin}% 0`,r.animationValues?e.opacity=r===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=r===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,rl){if(void 0===n[t])continue;let{correct:i,applyTo:s,isCSSVariable:a}=rl[t],o="none"===e.transform?n[t]:i(n[t],r);if(s){let t=s.length;for(let i=0;i<t;i++)e[s[i]]=o}else a?this.options.visualElement.renderState.vars[t]=o:e[t]=o}return this.options.layoutId&&(e.pointerEvents=r===this?rf(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(rZ),this.root.sharedNodes.clear()}}}function rX(t){t.updateLayout()}function rH(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:r}=t.layout,{animationType:n}=t.options,s=e.source!==t.layout.source;"size"===n?i_(t=>{let r=s?e.measuredBox[t]:e.layoutBox[t],n=iA(r);r.min=i[t].min,r.max=r.min+n}):ni(n,e.layoutBox,i)&&i_(r=>{let n=s?e.measuredBox[r]:e.layoutBox[r],a=iA(i[r]);n.max=n.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+a)});let a=iD();iR(a,i,e.layoutBox);let o=iD();s?iR(o,t.applyTransform(r,!0),e.measuredBox):iR(o,i,e.layoutBox);let l=!rO(a),u=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let a=iV();ij(a,e.layoutBox,n.layoutBox);let o=iV();ij(o,i,s.layoutBox),rN(a,o)||(u=!0),r.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function rK(t){g.value&&r$.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function rY(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function rG(t){t.clearSnapshot()}function rZ(t){t.clearMeasurements()}function rQ(t){t.isLayoutDirty=!1}function rJ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function r0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function r1(t){t.resolveTargetDelta()}function r2(t){t.calcProjection()}function r3(t){t.resetSkewAndRotation()}function r5(t){t.removeLeadSnapshot()}function r9(t,e,i){t.translate=tj(e.translate,0,i),t.scale=tj(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function r6(t,e,i,r){t.min=tj(e.min,i.min,r),t.max=tj(e.max,i.max,r)}function r8(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let r4={duration:.45,ease:[.4,0,.1,1]},r7=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nt=r7("applewebkit/")&&!r7("chrome/")?Math.round:p;function ne(t){t.min=nt(t.min),t.max=nt(t.max)}function ni(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(rL(e)-rL(i)))}function nr(t){return t!==t.root&&t.scroll?.wasRoot}let nn=rq({attachResizeListener:(t,e)=>ix(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ns={current:void 0},na=rq({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ns.current){let t=new nn({});t.mount(window),t.setOptions({layoutScroll:!0}),ns.current=t}return ns.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function no(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),r=new AbortController;return[i,{passive:!0,...e,signal:r.signal},()=>r.abort()]}function nl(t){return!("touch"===t.pointerType||iv.x||iv.y)}function nu(t,e,i){let{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&v.postRender(()=>n(e,iP(e)))}class nh extends ip{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=no(t,i),a=t=>{if(!nl(t))return;let{target:i}=t,r=e(i,t);if("function"!=typeof r||!i)return;let s=t=>{nl(t)&&(r(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,n)};return r.forEach(t=>{t.addEventListener("pointerenter",a,n)}),s}(t,(t,e)=>(nu(this.node,e,"Start"),t=>nu(this.node,t,"End"))))}unmount(){}}class nc extends ip{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=$(ix(this.node.current,"focus",()=>this.onFocus()),ix(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let nd=(t,e)=>!!e&&(t===e||nd(t,e.parentElement)),np=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),nm=new WeakSet;function nf(t){return e=>{"Enter"===e.key&&t(e)}}function ng(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let ny=(t,e)=>{let i=t.currentTarget;if(!i)return;let r=nf(()=>{if(nm.has(i))return;ng(i,"down");let t=nf(()=>{ng(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>ng(i,"cancel"),e)});i.addEventListener("keydown",r,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),e)};function nv(t){return ib(t)&&!(iv.x||iv.y)}function nx(t,e,i){let{props:r}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&v.postRender(()=>n(e,iP(e)))}class nb extends ip{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=no(t,i),a=t=>{let r=t.currentTarget;if(!nv(t))return;nm.add(r);let s=e(r,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),nm.has(r)&&nm.delete(r),nv(t)&&"function"==typeof s&&s(t,{success:e})},o=t=>{a(t,r===window||r===document||i.useGlobalTarget||nd(r,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,n),window.addEventListener("pointercancel",l,n)};return r.forEach(t=>{if((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,n),eJ(t))t.addEventListener("focus",t=>ny(t,n)),!np.has(t.tagName)&&-1===t.tabIndex&&!t.hasAttribute("tabindex")&&(t.tabIndex=0)}),s}(t,(t,e)=>(nx(this.node,e,"Start"),(t,{success:e})=>nx(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nP=new WeakMap,nT=new WeakMap,nw=t=>{let e=nP.get(t.target);e&&e(t)},nE=t=>{t.forEach(nw)},nA={some:0,all:1};class nS extends ip{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:n}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:nA[r]};return function(t,e,i){let r=function({root:t,...e}){let i=t||document;nT.has(i)||nT.set(i,{});let r=nT.get(i),n=JSON.stringify(e);return r[n]||(r[n]=new IntersectionObserver(nE,{root:t,...e})),r[n]}(e);return nP.set(t,i),r.observe(t),()=>{nP.delete(t),r.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=e?i:r;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nR=(0,o.createContext)({strict:!1}),nM=(0,o.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),nC=(0,o.createContext)({});function nj(t){return l(t.animate)||io.some(e=>is(t[e]))}function nk(t){return!!(nj(t)||t.variants)}function nD(t){return Array.isArray(t)?t.join(" "):t}let nO="undefined"!=typeof window,nV={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},n_={};for(let t in nV)n_[t]={isEnabled:e=>nV[t].some(t=>!!e[t])};let nN=Symbol.for("motionComponentSymbol"),nL=nO?o.useLayoutEffect:o.useEffect;function nF(t,{layout:e,layoutId:i}){return w.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!rl[t]||"opacity"===t)}let nI=(t,e)=>e&&"number"==typeof t?e.transform(t):t,n$={...Q,transform:Math.round},nU={borderWidth:tp,borderTopWidth:tp,borderRightWidth:tp,borderBottomWidth:tp,borderLeftWidth:tp,borderRadius:tp,radius:tp,borderTopLeftRadius:tp,borderTopRightRadius:tp,borderBottomRightRadius:tp,borderBottomLeftRadius:tp,width:tp,maxWidth:tp,height:tp,maxHeight:tp,top:tp,right:tp,bottom:tp,left:tp,padding:tp,paddingTop:tp,paddingRight:tp,paddingBottom:tp,paddingLeft:tp,margin:tp,marginTop:tp,marginRight:tp,marginBottom:tp,marginLeft:tp,backgroundPositionX:tp,backgroundPositionY:tp,rotate:tc,rotateX:tc,rotateY:tc,rotateZ:tc,scale:tt,scaleX:tt,scaleY:tt,scaleZ:tt,skew:tc,skewX:tc,skewY:tc,distance:tp,translateX:tp,translateY:tp,translateZ:tp,x:tp,y:tp,z:tp,perspective:tp,transformPerspective:tp,opacity:J,originX:tg,originY:tg,originZ:tp,zIndex:n$,fillOpacity:J,strokeOpacity:J,numOctaves:n$},nB={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nW=T.length;function nz(t,e,i){let{style:r,vars:n,transformOrigin:s}=t,a=!1,o=!1;for(let t in e){let i=e[t];if(w.has(t)){a=!0;continue}if(K(t)){n[t]=i;continue}{let e=nI(i,nU[t]);t.startsWith("origin")?(o=!0,s[t]=e):r[t]=e}}if(!e.transform&&(a||i?r.transform=function(t,e,i){let r="",n=!0;for(let s=0;s<nW;s++){let a=T[s],o=t[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let t=nI(o,nU[a]);if(!l){n=!1;let e=nB[a]||a;r+=`${e}(${t}) `}i&&(e[a]=t)}}return r=r.trim(),i?r=i(e,n?"":r):n&&(r="none"),r}(e,t.transform,i):r.transform&&(r.transform="none")),o){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;r.transformOrigin=`${t} ${e} ${i}`}}let nq=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nX(t,e,i){for(let r in e)_(e[r])||nF(r,i)||(t[r]=e[r])}let nH={offset:"stroke-dashoffset",array:"stroke-dasharray"},nK={offset:"strokeDashoffset",array:"strokeDasharray"};function nY(t,{attrX:e,attrY:i,attrScale:r,pathLength:n,pathSpacing:s=1,pathOffset:a=0,...o},l,u,h){if(nz(t,o,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=h?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==r&&(c.scale=r),void 0!==n&&function(t,e,i=1,r=0,n=!0){t.pathLength=1;let s=n?nH:nK;t[s.offset]=tp.transform(-r);let a=tp.transform(e),o=tp.transform(i);t[s.array]=`${a} ${o}`}(c,n,s,a,!1)}let nG=()=>({...nq(),attrs:{}}),nZ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),nQ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function nJ(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||nQ.has(t)}let n0=t=>!nJ(t);try{!function(t){t&&(n0=e=>e.startsWith("on")?!nJ(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let n1=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function n2(t){if("string"!=typeof t||t.includes("-"));else if(n1.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let n3=t=>(e,i)=>{let r=(0,o.useContext)(nC),n=(0,o.useContext)(ri),s=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,r,n){return{latestValues:function(t,e,i,r){let n={},s=r(t,{});for(let t in s)n[t]=rf(s[t]);let{initial:a,animate:o}=t,u=nj(t),c=nk(t);e&&c&&!u&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===o&&(o=e.animate));let d=!!i&&!1===i.initial,p=(d=d||!1===a)?o:a;if(p&&"boolean"!=typeof p&&!l(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let r=h(t,e[i]);if(r){let{transitionEnd:t,transition:e,...i}=r;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(n[t]=e)}for(let e in t)n[e]=t[e]}}}return n}(i,r,n,t),renderState:e()}})(t,e,r,n);return i?s():function(t){let e=(0,o.useRef)(null);return null===e.current&&(e.current=t()),e.current}(s)};function n5(t,e,i){let{style:r}=t,n={};for(let s in r)(_(r[s])||e.style&&_(e.style[s])||nF(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(n[s]=r[s]);return n}let n9={useVisualState:n3({scrapeMotionValuesFromProps:n5,createRenderState:nq})};function n6(t,e,i){let r=n5(t,e,i);for(let i in t)(_(t[i])||_(e[i]))&&(r[-1!==T.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}let n8={useVisualState:n3({scrapeMotionValuesFromProps:n6,createRenderState:nG})},n4=t=>e=>e.test(t),n7=[Q,tp,td,tc,tf,tm,{test:t=>"auto"===t,parse:t=>t}],st=t=>n7.find(n4(t)),se=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),si=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,sr=t=>/^0[^.\s]+$/u.test(t),sn=new Set(["brightness","contrast","saturate","opacity"]);function ss(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=i.match(ti)||[];if(!r)return t;let n=i.replace(r,""),s=+!!sn.has(e);return r!==i&&(s*=100),e+"("+s+n+")"}let sa=/\b([a-z-]*)\(.*?\)/gu,so={...tR,getAnimatableNone:t=>{let e=t.match(sa);return e?e.map(ss).join(" "):t}},sl={...nU,color:tv,backgroundColor:tv,outlineColor:tv,fill:tv,stroke:tv,borderColor:tv,borderTopColor:tv,borderRightColor:tv,borderBottomColor:tv,borderLeftColor:tv,filter:so,WebkitFilter:so},su=t=>sl[t];function sh(t,e){let i=su(t);return i!==so&&(i=tR),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let sc=new Set(["auto","none","0"]);class sd extends eI{constructor(t,e,i,r,n){super(t,e,i,r,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let r=t[i];if("string"==typeof r&&G(r=r.trim())){let n=function t(e,i,r=1){X(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,s]=function(t){let e=si.exec(t);if(!e)return[,];let[,i,r,n]=e;return[`--${i??r}`,n]}(e);if(!n)return;let a=window.getComputedStyle(i).getPropertyValue(n);if(a){let t=a.trim();return se(t)?parseFloat(t):t}return G(s)?t(s,i,r+1):s}(r,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!E.has(i)||2!==t.length)return;let[r,n]=t,s=st(r),a=st(n);if(s!==a){if(eC(s)&&eC(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eD[i]&&(this.needsMeasurement=!0)}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var r;(null===t[e]||("number"==typeof(r=t[e])?0===r:null===r||"none"===r||"0"===r||sr(r)))&&i.push(e)}i.length&&function(t,e,i){let r,n=0;for(;n<t.length&&!r;){let e=t[n];"string"==typeof e&&!sc.has(e)&&tw(e).values.length&&(r=t[n]),n++}if(r&&i)for(let n of e)t[n]=sh(i,r)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eD[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let r=e[e.length-1];void 0!==r&&t.getValue(i,r).jump(r,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let r=t.getValue(e);r&&r.jump(this.measuredOrigin,!1);let n=i.length-1,s=i[n];i[n]=eD[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let sp=[...n7,tv,tR],sm=t=>sp.find(n4(t)),sf={current:null},sg={current:!1},sy=new WeakMap,sv=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sx{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:n,visualState:s},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eI,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=C.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,v.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=s;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.blockInitialAnimation=!!n,this.isControllingVariants=nj(e),this.isVariantNode=nk(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==o[t]&&_(e)&&e.set(o[t],!1)}}mount(t){this.current=t,sy.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sg.current||function(){if(sg.current=!0,nO){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sf.current=t.matches;t.addListener(e),e()}else sf.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sf.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),x(this.notifyUpdate),x(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=w.has(t);r&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&v.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in n_){let e=n_[t];if(!e)continue;let{isEnabled:i,Feature:r}=e;if(!this.features[t]&&r&&i(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iV()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sv.length;e++){let i=sv[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){for(let r in e){let n=e[r],s=i[r];if(_(n))t.addValue(r,n);else if(_(s))t.addValue(r,O(n,{owner:t}));else if(s!==n){if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(r);t.addValue(r,O(void 0!==e?e:n,{owner:t}))}}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=O(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(se(i)||sr(i))?i=parseFloat(i):!sm(i)&&tR.test(e)&&(i=sh(t,e)),this.setBaseTarget(t,_(i)?i.get():i)),_(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e;let{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let r=h(this.props,i,this.presenceContext?.custom);r&&(e=r[t])}if(i&&void 0!==e)return e;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||_(r)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new R),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sb extends sx{constructor(){super(...arguments),this.KeyframeResolver=sd}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;_(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function sP(t,{style:e,vars:i},r,n){for(let s in Object.assign(t.style,e,n&&n.getProjectionStyles(r)),i)t.style.setProperty(s,i[s])}class sT extends sb{constructor(){super(...arguments),this.type="html",this.renderInstance=sP}readValueFromInstance(t,e){if(w.has(e))return this.projection?.isProjecting?eA(e):eR(t,e);{let i=window.getComputedStyle(t),r=(K(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iX(t,e)}build(t,e,i){nz(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return n5(t,e,i)}}let sw=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sE extends sb{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iV}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(w.has(e)){let t=su(e);return t&&t.default||0}return e=sw.has(e)?e:L(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return n6(t,e,i)}build(t,e,i){nY(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,r){!function(t,e,i,r){for(let i in sP(t,e,void 0,r),e.attrs)t.setAttribute(sw.has(i)?i:L(i),e.attrs[i])}(t,e,0,r)}mount(t){this.isSVGTag=nZ(t.tagName),super.mount(t)}}let sA=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,r)=>"create"===r?t:(e.has(r)||e.set(r,t(r)),e.get(r))})}((n={animation:{Feature:im},exit:{Feature:iy},inView:{Feature:nS},tap:{Feature:nb},focus:{Feature:nc},hover:{Feature:nh},pan:{Feature:rt},drag:{Feature:i4,ProjectionNode:na,MeasureLayout:rh},layout:{ProjectionNode:na,MeasureLayout:rh}},s=(t,e)=>n2(t)?new sE(e):new sT(e,{allowProjection:t!==o.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:r,Component:n}){function s(t,s){var l,u,h;let c;let d={...(0,o.useContext)(nM),...t,layoutId:function({layoutId:t}){let e=(0,o.useContext)(rr).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:p}=d,m=function(t){let{initial:e,animate:i}=function(t,e){if(nj(t)){let{initial:e,animate:i}=t;return{initial:!1===e||is(e)?e:void 0,animate:is(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,o.useContext)(nC));return(0,o.useMemo)(()=>({initial:e,animate:i}),[nD(e),nD(i)])}(t),f=r(t,p);if(!p&&nO){u=0,h=0,(0,o.useContext)(nR).strict;let t=function(t){let{drag:e,layout:i}=n_;if(!e&&!i)return{};let r={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(d);c=t.MeasureLayout,m.visualElement=function(t,e,i,r,n){let{visualElement:s}=(0,o.useContext)(nC),a=(0,o.useContext)(nR),l=(0,o.useContext)(ri),u=(0,o.useContext)(nM).reducedMotion,h=(0,o.useRef)(null);r=r||a.renderer,!h.current&&r&&(h.current=r(t,{visualState:e,parent:s,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));let c=h.current,d=(0,o.useContext)(rn);c&&!c.projection&&n&&("html"===c.type||"svg"===c.type)&&function(t,e,i,r){let{layoutId:n,layout:s,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!a||o&&iK(o),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,crossfade:h,layoutScroll:l,layoutRoot:u})}(h.current,i,n,d);let p=(0,o.useRef)(!1);(0,o.useInsertionEffect)(()=>{c&&p.current&&c.update(i,l)});let m=i[F],f=(0,o.useRef)(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return nL(()=>{c&&(p.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),re.render(c.render),f.current&&c.animationState&&c.animationState.animateChanges())}),(0,o.useEffect)(()=>{c&&(!f.current&&c.animationState&&c.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),f.current=!1))}),c}(n,f,d,e,t.ProjectionNode)}return(0,a.jsxs)(nC.Provider,{value:m,children:[c&&m.visualElement?(0,a.jsx)(c,{visualElement:m.visualElement,...d}):null,i(n,t,(l=m.visualElement,(0,o.useCallback)(t=>{t&&f.onMount&&f.onMount(t),l&&(t?l.mount(t):l.unmount()),s&&("function"==typeof s?s(t):iK(s)&&(s.current=t))},[l])),f,p,m.visualElement)]})}t&&function(t){for(let e in t)n_[e]={...n_[e],...t[e]}}(t),s.displayName=`motion.${"string"==typeof n?n:`create(${n.displayName??n.name??""})`}`;let l=(0,o.forwardRef)(s);return l[nN]=n,l}({...n2(t)?n8:n9,preloadedFeatures:n,useRender:function(t=!1){return(e,i,r,{latestValues:n},s)=>{let a=(n2(e)?function(t,e,i,r){let n=(0,o.useMemo)(()=>{let i=nG();return nY(i,e,nZ(r),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};nX(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return nX(r,i,t),Object.assign(r,function({transformTemplate:t},e){return(0,o.useMemo)(()=>{let i=nq();return nz(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(i,n,s,e),l=function(t,e,i){let r={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(n0(n)||!0===i&&nJ(n)||!e&&!nJ(n)||t.draggable&&n.startsWith("onDrag"))&&(r[n]=t[n]);return r}(i,"string"==typeof e,t),u=e!==o.Fragment?{...l,...a,ref:r}:{},{children:h}=i,c=(0,o.useMemo)(()=>_(h)?h.get():h,[h]);return(0,o.createElement)(e,{...u,children:c})}}(e),createVisualElement:s,Component:t})})),sS=o.forwardRef(({className:t,...e},i)=>(0,a.jsx)("div",{ref:i,className:`rounded-lg border bg-card text-card-foreground shadow-sm ${t||""}`,...e}));sS.displayName="Card";let sR=o.forwardRef(({className:t,...e},i)=>(0,a.jsx)("div",{ref:i,className:`flex flex-col space-y-1.5 p-6 ${t||""}`,...e}));sR.displayName="CardHeader";let sM=o.forwardRef(({className:t,...e},i)=>(0,a.jsx)("h3",{ref:i,className:`text-2xl font-semibold leading-none tracking-tight ${t||""}`,...e}));sM.displayName="CardTitle";let sC=o.forwardRef(({className:t,...e},i)=>(0,a.jsx)("p",{ref:i,className:`text-sm text-muted-foreground ${t||""}`,...e}));sC.displayName="CardDescription";let sj=o.forwardRef(({className:t,...e},i)=>(0,a.jsx)("div",{ref:i,className:`p-6 pt-0 ${t||""}`,...e}));sj.displayName="CardContent";let sk=o.forwardRef(({className:t,...e},i)=>(0,a.jsx)("div",{ref:i,className:`flex items-center p-6 pt-0 ${t||""}`,...e}));sk.displayName="CardFooter";let sD=o.forwardRef(({className:t,variant:e="default",size:i="default",...r},n)=>(0,a.jsx)("button",{className:`inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"}[e]} ${{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[i]} ${t||""}`,ref:n,...r}));sD.displayName="Button";let sO=o.forwardRef(({className:t,variant:e="default",...i},r)=>(0,a.jsx)("div",{ref:r,className:`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 ${{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}[e]} ${t||""}`,...i}));function sV({name:t,description:e,capabilities:i,avatarUrl:r,isActive:n,onActivate:s}){let[l,u]=(0,o.useState)(!1);return(0,a.jsx)(sA.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},whileHover:{scale:1.03},className:"w-full max-w-sm",onMouseEnter:()=>u(!0),onMouseLeave:()=>u(!1),children:(0,a.jsxs)(sS,{className:`overflow-hidden border-2 ${n?"border-blue-500":"border-gray-200"} transition-all duration-300`,children:[(0,a.jsxs)(sR,{className:"relative p-0",children:[(0,a.jsx)(sA.div,{className:"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600",initial:{opacity:.7},animate:{opacity:l?.9:.7}}),(0,a.jsxs)("div",{className:"relative p-6 flex items-center gap-4",children:[(0,a.jsx)(sA.img,{src:r,alt:`${t} avatar`,className:"w-16 h-16 rounded-full border-2 border-white",animate:{rotate:360*!!l},transition:{duration:2}}),(0,a.jsxs)("div",{children:[(0,a.jsx)(sM,{className:"text-white",children:t}),(0,a.jsx)(sC,{className:"text-white/80",children:"AI Agent"})]})]})]}),(0,a.jsxs)(sj,{className:"p-6",children:[(0,a.jsx)("p",{className:"text-gray-700 dark:text-gray-300",children:e}),(0,a.jsx)("div",{className:"mt-4 flex flex-wrap gap-2",children:i.map((t,e)=>(0,a.jsx)(sO,{variant:"outline",className:"bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300",children:t},e))})]}),(0,a.jsx)(sk,{className:"border-t p-6",children:(0,a.jsx)(sD,{onClick:s,className:`w-full ${n?"bg-green-600 hover:bg-green-700":"bg-blue-600 hover:bg-blue-700"}`,children:n?"Active":"Activate Agent"})})]})})}function s_(){let[t,e]=(0,o.useState)(null),i=[{id:"assistant",name:"AI Assistant",description:"A helpful AI assistant that can answer questions, provide information, and help with various tasks.",capabilities:["Q&A","Research","Writing","Analysis"],avatarUrl:"https://images.unsplash.com/photo-1677442136019-21780ecad995?w=100&h=100&fit=crop&crop=face"},{id:"coder",name:"Code Expert",description:"Specialized in programming, code review, debugging, and software development best practices.",capabilities:["Coding","Debugging","Code Review","Architecture"],avatarUrl:"https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=100&h=100&fit=crop&crop=face"},{id:"creative",name:"Creative Writer",description:"Expert in creative writing, storytelling, content creation, and artistic expression.",capabilities:["Writing","Storytelling","Content","Creativity"],avatarUrl:"https://images.unsplash.com/photo-1552058544-f2b08422138a?w=100&h=100&fit=crop&crop=face"}],r=i=>{e(t===i?null:i)};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"AI Agent Dashboard"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"Choose an AI agent to assist you with your tasks"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-center",children:i.map(e=>(0,a.jsx)(sV,{name:e.name,description:e.description,capabilities:e.capabilities,avatarUrl:e.avatarUrl,isActive:t===e.id,onActivate:()=>r(e.id)},e.id))}),t&&(0,a.jsx)("div",{className:"mt-12 text-center",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg max-w-md mx-auto",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Agent Activated!"}),(0,a.jsxs)("p",{className:"text-gray-600 dark:text-gray-300",children:[i.find(e=>e.id===t)?.name," is now ready to assist you."]})]})})]})})}sO.displayName="Badge"},9294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9517:(t,e)=>{"use strict";function i(t){let e={};for(let[i,r]of t.entries()){let t=e[i];void 0===t?e[i]=r:Array.isArray(t)?t.push(r):e[i]=[t,r]}return e}function r(t){return"string"==typeof t?t:("number"!=typeof t||isNaN(t))&&"boolean"!=typeof t?"":String(t)}function n(t){let e=new URLSearchParams;for(let[i,n]of Object.entries(t))if(Array.isArray(n))for(let t of n)e.append(i,r(t));else e.set(i,r(n));return e}function s(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];for(let e of i){for(let i of e.keys())t.delete(i);for(let[i,r]of e.entries())t.append(i,r)}return t}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{assign:function(){return s},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return n}})},9551:t=>{"use strict";t.exports=require("url")},9680:()=>{},9857:(t,e,i)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{getUtils:function(){return g},interpolateDynamicPath:function(){return m},normalizeDynamicRouteParams:function(){return f},normalizeVercelUrl:function(){return p}});let r=i(9551),n=i(5715),s=i(734),a=i(4851),o=i(6310),l=i(1290),u=i(6139),h=i(1430),c=i(6203),d=i(1892);function p(t,e,i){let n=(0,r.parse)(t.url,!0);for(let t of(delete n.search,Object.keys(n.query))){let r=t!==c.NEXT_QUERY_PARAM_PREFIX&&t.startsWith(c.NEXT_QUERY_PARAM_PREFIX),s=t!==c.NEXT_INTERCEPTION_MARKER_PREFIX&&t.startsWith(c.NEXT_INTERCEPTION_MARKER_PREFIX);(r||s||e.includes(t)||i&&Object.keys(i.groups).includes(t))&&delete n.query[t]}t.url=(0,r.format)(n)}function m(t,e,i){if(!i)return t;for(let r of Object.keys(i.groups)){let n;let{optional:s,repeat:a}=i.groups[r],o=`[${a?"...":""}${r}]`;s&&(o=`[${o}]`);let l=e[r];n=Array.isArray(l)?l.map(t=>t&&encodeURIComponent(t)).join("/"):l?encodeURIComponent(l):"",t=t.replaceAll(o,n)}return t}function f(t,e,i,r){let n={};for(let s of Object.keys(e.groups)){let a=t[s];"string"==typeof a?a=(0,h.normalizeRscURL)(a):Array.isArray(a)&&(a=a.map(h.normalizeRscURL));let o=i[s],l=e.groups[s].optional;if((Array.isArray(o)?o.some(t=>Array.isArray(a)?a.some(e=>e.includes(t)):null==a?void 0:a.includes(t)):null==a?void 0:a.includes(o))||void 0===a&&!(l&&r))return{params:{},hasValidParams:!1};l&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${s}]]`))&&(a=void 0,delete t[s]),a&&"string"==typeof a&&e.groups[s].repeat&&(a=a.split("/")),a&&(n[s]=a)}return{params:n,hasValidParams:!0}}function g({page:t,i18n:e,basePath:i,rewrites:r,pageIsDynamic:h,trailingSlash:c,caseSensitive:g}){let y,v,x;return h&&(y=(0,a.getNamedRouteRegex)(t,{prefixRouteKeys:!1}),x=(v=(0,o.getRouteMatcher)(y))(t)),{handleRewrites:function(a,o){let d={},p=o.pathname,m=r=>{let u=(0,s.getPathMatch)(r.source+(c?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!o.pathname)return!1;let m=u(o.pathname);if((r.has||r.missing)&&m){let t=(0,l.matchHas)(a,o.query,r.has,r.missing);t?Object.assign(m,t):m=!1}if(m){let{parsedDestination:s,destQuery:a}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:r.destination,params:m,query:o.query});if(s.protocol)return!0;if(Object.assign(d,a,m),Object.assign(o.query,s.query),delete s.query,Object.assign(o,s),!(p=o.pathname))return!1;if(i&&(p=p.replace(RegExp(`^${i}`),"")||"/"),e){let t=(0,n.normalizeLocalePath)(p,e.locales);p=t.pathname,o.query.nextInternalLocale=t.detectedLocale||m.nextInternalLocale}if(p===t)return!0;if(h&&v){let t=v(p);if(t)return o.query={...o.query,...t},!0}}return!1};for(let t of r.beforeFiles||[])m(t);if(p!==t){let e=!1;for(let t of r.afterFiles||[])if(e=m(t))break;if(!e&&!(()=>{let e=(0,u.removeTrailingSlash)(p||"");return e===(0,u.removeTrailingSlash)(t)||(null==v?void 0:v(e))})()){for(let t of r.fallback||[])if(e=m(t))break}}return d},defaultRouteRegex:y,dynamicRouteMatcher:v,defaultRouteMatches:x,getParamsFromRouteMatches:function(t){if(!y)return null;let{groups:e,routeKeys:i}=y,r=(0,o.getRouteMatcher)({re:{exec:t=>{let r=Object.fromEntries(new URLSearchParams(t));for(let[t,e]of Object.entries(r)){let i=(0,d.normalizeNextQueryParam)(t);i&&(r[i]=e,delete r[t])}let n={};for(let t of Object.keys(i)){let s=i[t];if(!s)continue;let a=e[s],o=r[t];if(!a.optional&&!o)return null;n[a.pos]=o}return n}},groups:e})(t);return r||null},normalizeDynamicRouteParams:(t,e)=>y&&x?f(t,y,x,e):{params:{},hasValidParams:!1},normalizeVercelUrl:(t,e)=>p(t,e,y),interpolateDynamicPath:(t,e)=>m(t,e,y)}}},9952:(t,e,i)=>{"use strict";function r(t){return function(){let{cookie:e}=t;if(!e)return{};let{parse:r}=i(8715);return r(Array.isArray(e)?e.join("; "):e)}}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getCookieParser",{enumerable:!0,get:function(){return r}})}};var e=require("../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),r=e.X(0,[163,575],()=>i(5878));module.exports=r})();