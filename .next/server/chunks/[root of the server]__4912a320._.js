module.exports = {

"[project]/.next-internal/server/app/api/agents/chat/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route.runtime.dev.js [external] (next/dist/compiled/next-server/app-route.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page.runtime.dev.js [external] (next/dist/compiled/next-server/app-page.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/lib/ai-providers.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
// AI Provider Configuration and Client Library
// Supports multiple AI providers with unified interface
__turbopack_context__.s({
    "AIClient": (()=>AIClient),
    "AI_PROVIDERS": (()=>AI_PROVIDERS),
    "createAIClient": (()=>createAIClient),
    "getAvailableModels": (()=>getAvailableModels),
    "getAvailableProviders": (()=>getAvailableProviders),
    "getBestModelForTask": (()=>getBestModelForTask),
    "getCostEffectiveModels": (()=>getCostEffectiveModels),
    "getFastestModels": (()=>getFastestModels),
    "getModelsByCapability": (()=>getModelsByCapability),
    "getProviderForModel": (()=>getProviderForModel)
});
const AI_PROVIDERS = {
    openrouter: {
        id: 'openrouter',
        name: 'OpenRouter',
        baseUrl: 'https://openrouter.ai/api/v1',
        apiKey: process.env.OPENROUTER_API_KEY,
        headers: {
            'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': process.env.NEXTAUTH_URL || 'http://localhost:3000',
            'X-Title': 'Knowledge OS'
        },
        models: [
            {
                id: 'anthropic/claude-3.5-sonnet',
                name: 'Claude 3.5 Sonnet',
                provider: 'openrouter',
                contextLength: 200000,
                inputCost: 3.0,
                outputCost: 15.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'analysis'
                ]
            },
            {
                id: 'openai/gpt-4o',
                name: 'GPT-4o',
                provider: 'openrouter',
                contextLength: 128000,
                inputCost: 2.5,
                outputCost: 10.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'vision'
                ]
            },
            {
                id: 'meta-llama/llama-3.1-405b-instruct',
                name: 'Llama 3.1 405B',
                provider: 'openrouter',
                contextLength: 131072,
                inputCost: 2.7,
                outputCost: 2.7,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding'
                ]
            },
            {
                id: 'google/gemini-pro-1.5',
                name: 'Gemini Pro 1.5',
                provider: 'openrouter',
                contextLength: 2000000,
                inputCost: 1.25,
                outputCost: 5.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'vision'
                ]
            }
        ]
    },
    google: {
        id: 'google',
        name: 'Google Gemini',
        baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
        apiKey: process.env.GOOGLE_API_KEY,
        headers: {
            'Content-Type': 'application/json'
        },
        models: [
            {
                id: 'gemini-1.5-pro',
                name: 'Gemini 1.5 Pro',
                provider: 'google',
                contextLength: 2000000,
                inputCost: 1.25,
                outputCost: 5.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'vision',
                    'long-context'
                ]
            },
            {
                id: 'gemini-1.5-flash',
                name: 'Gemini 1.5 Flash',
                provider: 'google',
                contextLength: 1000000,
                inputCost: 0.075,
                outputCost: 0.3,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'fast'
                ]
            },
            {
                id: 'gemini-pro',
                name: 'Gemini Pro',
                provider: 'google',
                contextLength: 32768,
                inputCost: 0.5,
                outputCost: 1.5,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding'
                ]
            }
        ]
    },
    groq: {
        id: 'groq',
        name: 'Groq',
        baseUrl: 'https://api.groq.com/openai/v1',
        apiKey: process.env.GROQ_API_KEY,
        headers: {
            'Authorization': `Bearer ${process.env.GROQ_API_KEY}`,
            'Content-Type': 'application/json'
        },
        models: [
            {
                id: 'llama-3.1-70b-versatile',
                name: 'Llama 3.1 70B',
                provider: 'groq',
                contextLength: 131072,
                inputCost: 0.59,
                outputCost: 0.79,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'fast'
                ]
            },
            {
                id: 'llama-3.1-8b-instant',
                name: 'Llama 3.1 8B',
                provider: 'groq',
                contextLength: 131072,
                inputCost: 0.05,
                outputCost: 0.08,
                capabilities: [
                    'chat',
                    'fast',
                    'efficient'
                ]
            },
            {
                id: 'mixtral-8x7b-32768',
                name: 'Mixtral 8x7B',
                provider: 'groq',
                contextLength: 32768,
                inputCost: 0.24,
                outputCost: 0.24,
                capabilities: [
                    'chat',
                    'reasoning',
                    'multilingual'
                ]
            },
            {
                id: 'gemma2-9b-it',
                name: 'Gemma 2 9B',
                provider: 'groq',
                contextLength: 8192,
                inputCost: 0.2,
                outputCost: 0.2,
                capabilities: [
                    'chat',
                    'fast',
                    'efficient'
                ]
            }
        ]
    },
    cerebras: {
        id: 'cerebras',
        name: 'Cerebras',
        baseUrl: 'https://api.cerebras.ai/v1',
        apiKey: process.env.CEREBRAS_API_KEY,
        headers: {
            'Authorization': `Bearer ${process.env.CEREBRAS_API_KEY}`,
            'Content-Type': 'application/json'
        },
        models: [
            {
                id: 'llama3.1-70b',
                name: 'Llama 3.1 70B',
                provider: 'cerebras',
                contextLength: 128000,
                inputCost: 0.6,
                outputCost: 0.6,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'ultra-fast'
                ]
            },
            {
                id: 'llama3.1-8b',
                name: 'Llama 3.1 8B',
                provider: 'cerebras',
                contextLength: 128000,
                inputCost: 0.1,
                outputCost: 0.1,
                capabilities: [
                    'chat',
                    'ultra-fast',
                    'efficient'
                ]
            }
        ]
    },
    chutes: {
        id: 'chutes',
        name: 'Chutes AI',
        baseUrl: 'https://api.chutes.ai/v1',
        apiKey: process.env.CHUTES_API_KEY,
        headers: {
            'Authorization': `Bearer ${process.env.CHUTES_API_KEY}`,
            'Content-Type': 'application/json'
        },
        models: [
            {
                id: 'gpt-4o',
                name: 'GPT-4o (Chutes)',
                provider: 'chutes',
                contextLength: 128000,
                inputCost: 2.5,
                outputCost: 10.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'vision'
                ]
            },
            {
                id: 'claude-3.5-sonnet',
                name: 'Claude 3.5 Sonnet (Chutes)',
                provider: 'chutes',
                contextLength: 200000,
                inputCost: 3.0,
                outputCost: 15.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'analysis'
                ]
            }
        ]
    },
    mistral: {
        id: 'mistral',
        name: 'Mistral AI',
        baseUrl: 'https://api.mistral.ai/v1',
        apiKey: process.env.MISTRAL_API_KEY,
        headers: {
            'Authorization': `Bearer ${process.env.MISTRAL_API_KEY}`,
            'Content-Type': 'application/json'
        },
        models: [
            {
                id: 'mistral-large-latest',
                name: 'Mistral Large',
                provider: 'mistral',
                contextLength: 128000,
                inputCost: 2.0,
                outputCost: 6.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'multilingual'
                ]
            },
            {
                id: 'mistral-medium-latest',
                name: 'Mistral Medium',
                provider: 'mistral',
                contextLength: 32768,
                inputCost: 0.7,
                outputCost: 2.1,
                capabilities: [
                    'chat',
                    'reasoning',
                    'efficient'
                ]
            },
            {
                id: 'mistral-small-latest',
                name: 'Mistral Small',
                provider: 'mistral',
                contextLength: 32768,
                inputCost: 0.2,
                outputCost: 0.6,
                capabilities: [
                    'chat',
                    'fast',
                    'efficient'
                ]
            }
        ]
    },
    // Optional providers (commented out by default)
    openai: {
        id: 'openai',
        name: 'OpenAI',
        baseUrl: 'https://api.openai.com/v1',
        apiKey: process.env.OPENAI_API_KEY,
        headers: {
            'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
            'Content-Type': 'application/json'
        },
        models: [
            {
                id: 'gpt-4o',
                name: 'GPT-4o',
                provider: 'openai',
                contextLength: 128000,
                inputCost: 2.5,
                outputCost: 10.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'vision'
                ]
            },
            {
                id: 'gpt-4o-mini',
                name: 'GPT-4o Mini',
                provider: 'openai',
                contextLength: 128000,
                inputCost: 0.15,
                outputCost: 0.6,
                capabilities: [
                    'chat',
                    'fast',
                    'efficient'
                ]
            }
        ]
    },
    anthropic: {
        id: 'anthropic',
        name: 'Anthropic',
        baseUrl: 'https://api.anthropic.com/v1',
        apiKey: process.env.ANTHROPIC_API_KEY,
        headers: {
            'x-api-key': process.env.ANTHROPIC_API_KEY || '',
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01'
        },
        models: [
            {
                id: 'claude-3-5-sonnet-20241022',
                name: 'Claude 3.5 Sonnet',
                provider: 'anthropic',
                contextLength: 200000,
                inputCost: 3.0,
                outputCost: 15.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'analysis'
                ]
            },
            {
                id: 'claude-3-haiku-20240307',
                name: 'Claude 3 Haiku',
                provider: 'anthropic',
                contextLength: 200000,
                inputCost: 0.25,
                outputCost: 1.25,
                capabilities: [
                    'chat',
                    'fast',
                    'efficient'
                ]
            }
        ]
    }
};
function getAvailableProviders() {
    return Object.values(AI_PROVIDERS).map((provider)=>({
            ...provider,
            isAvailable: !!provider.apiKey
        })).filter((provider)=>provider.isAvailable);
}
function getAvailableModels() {
    return getAvailableProviders().flatMap((provider)=>provider.models);
}
function getModelsByCapability(capability) {
    return getAvailableModels().filter((model)=>model.capabilities.includes(capability));
}
function getFastestModels() {
    return getModelsByCapability('fast').concat(getModelsByCapability('ultra-fast'));
}
function getCostEffectiveModels() {
    return getAvailableModels().filter((model)=>model.inputCost && model.inputCost < 1.0).sort((a, b)=>(a.inputCost || 0) - (b.inputCost || 0));
}
function getProviderForModel(modelId) {
    const providers = getAvailableProviders();
    for (const provider of providers){
        if (provider.models.some((model)=>model.id === modelId)) {
            return provider;
        }
    }
    return null;
}
class AIClient {
    provider;
    constructor(providerId){
        const provider = getAvailableProviders().find((p)=>p.id === providerId);
        if (!provider) {
            throw new Error(`Provider ${providerId} not available or not configured`);
        }
        this.provider = provider;
    }
    async chat(request) {
        const url = `${this.provider.baseUrl}/chat/completions`;
        // Handle provider-specific request formatting
        const body = this.formatRequest(request);
        const response = await fetch(url, {
            method: 'POST',
            headers: this.provider.headers,
            body: JSON.stringify(body)
        });
        if (!response.ok) {
            const error = await response.text();
            throw new Error(`AI API Error (${response.status}): ${error}`);
        }
        const data = await response.json();
        return this.formatResponse(data);
    }
    formatRequest(request) {
        // Handle provider-specific formatting
        switch(this.provider.id){
            case 'google':
                return this.formatGoogleRequest(request);
            case 'anthropic':
                return this.formatAnthropicRequest(request);
            default:
                // OpenAI-compatible format (most providers)
                return {
                    model: request.model,
                    messages: request.messages,
                    temperature: request.temperature || 0.7,
                    max_tokens: request.maxTokens || 1000,
                    stream: request.stream || false
                };
        }
    }
    formatGoogleRequest(request) {
        // Convert to Google's format
        const contents = request.messages.map((msg)=>({
                role: msg.role === 'assistant' ? 'model' : msg.role,
                parts: [
                    {
                        text: msg.content
                    }
                ]
            }));
        return {
            contents,
            generationConfig: {
                temperature: request.temperature || 0.7,
                maxOutputTokens: request.maxTokens || 1000
            }
        };
    }
    formatAnthropicRequest(request) {
        // Convert to Anthropic's format
        const systemMessage = request.messages.find((m)=>m.role === 'system');
        const messages = request.messages.filter((m)=>m.role !== 'system');
        return {
            model: request.model,
            messages,
            system: systemMessage?.content,
            max_tokens: request.maxTokens || 1000,
            temperature: request.temperature || 0.7
        };
    }
    formatResponse(data) {
        // Handle provider-specific response formatting
        switch(this.provider.id){
            case 'google':
                return this.formatGoogleResponse(data);
            case 'anthropic':
                return this.formatAnthropicResponse(data);
            default:
                // OpenAI-compatible format
                return data;
        }
    }
    formatGoogleResponse(data) {
        const candidate = data.candidates?.[0];
        return {
            id: data.id || 'google-' + Date.now(),
            model: 'google-model',
            choices: [
                {
                    message: {
                        role: 'assistant',
                        content: candidate?.content?.parts?.[0]?.text || ''
                    },
                    finishReason: candidate?.finishReason || 'stop'
                }
            ],
            usage: {
                promptTokens: data.usageMetadata?.promptTokenCount || 0,
                completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
                totalTokens: data.usageMetadata?.totalTokenCount || 0
            }
        };
    }
    formatAnthropicResponse(data) {
        return {
            id: data.id,
            model: data.model,
            choices: [
                {
                    message: {
                        role: 'assistant',
                        content: data.content?.[0]?.text || ''
                    },
                    finishReason: data.stop_reason || 'stop'
                }
            ],
            usage: {
                promptTokens: data.usage?.input_tokens || 0,
                completionTokens: data.usage?.output_tokens || 0,
                totalTokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0)
            }
        };
    }
}
function createAIClient(providerId) {
    // Use first available provider if none specified
    if (!providerId) {
        const available = getAvailableProviders();
        if (available.length === 0) {
            throw new Error('No AI providers configured');
        }
        providerId = available[0].id;
    }
    return new AIClient(providerId);
}
function getBestModelForTask(task) {
    const models = getAvailableModels();
    switch(task){
        case 'fast':
            return getFastestModels()[0] || null;
        case 'cost-effective':
            return getCostEffectiveModels()[0] || null;
        case 'coding':
            return models.find((m)=>m.capabilities.includes('coding')) || null;
        case 'reasoning':
            return models.find((m)=>m.capabilities.includes('reasoning')) || null;
        default:
            return models[0] || null;
    }
}
}}),
"[project]/lib/ai-agent-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
// AI Agent Service using configured providers
// Handles agent interactions with multiple AI providers
__turbopack_context__.s({
    "AGENT_CONFIGS": (()=>AGENT_CONFIGS),
    "AIAgentService": (()=>AIAgentService),
    "aiAgentService": (()=>aiAgentService),
    "chatWithAgent": (()=>chatWithAgent),
    "createAgentSession": (()=>createAgentSession),
    "getAvailableAgentTypes": (()=>getAvailableAgentTypes)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$providers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/ai-providers.ts [app-route] (ecmascript)");
;
const AGENT_CONFIGS = {
    assistant: {
        id: "assistant",
        name: "AI Assistant",
        type: "ASSISTANT",
        description: "A helpful AI assistant for general questions and tasks",
        systemPrompt: `You are a helpful, knowledgeable AI assistant. You provide accurate, concise, and helpful responses to user questions. You can help with a wide range of topics including general knowledge, explanations, problem-solving, and task assistance.

Key guidelines:
- Be helpful, accurate, and concise
- Ask clarifying questions when needed
- Provide step-by-step explanations for complex topics
- Admit when you don't know something
- Be friendly and professional`,
        preferredProvider: "openrouter",
        preferredModel: "anthropic/claude-3.5-sonnet",
        temperature: 0.7,
        maxTokens: 1000,
        capabilities: [
            "chat",
            "reasoning",
            "general-knowledge"
        ]
    },
    researcher: {
        id: "researcher",
        name: "Research Agent",
        type: "RESEARCHER",
        description: "Specialized in research, analysis, and information synthesis",
        systemPrompt: `You are a research specialist AI. You excel at analyzing information, conducting thorough research, synthesizing findings, and providing well-structured reports.

Your capabilities include:
- Literature review and analysis
- Data interpretation and insights
- Fact-checking and verification
- Research methodology guidance
- Citation and source evaluation
- Trend analysis and pattern recognition

Always provide:
- Well-structured responses with clear sections
- Evidence-based conclusions
- Relevant sources and references when possible
- Multiple perspectives on complex topics
- Actionable insights and recommendations`,
        preferredProvider: "google",
        preferredModel: "gemini-1.5-pro",
        temperature: 0.3,
        maxTokens: 2000,
        capabilities: [
            "research",
            "analysis",
            "long-context",
            "reasoning"
        ]
    },
    coder: {
        id: "coder",
        name: "Code Agent",
        type: "CODER",
        description: "Expert in programming, code review, and software development",
        systemPrompt: `You are an expert software engineer and coding assistant. You help with programming tasks, code review, debugging, architecture design, and best practices.

Your expertise includes:
- Multiple programming languages (Python, JavaScript, TypeScript, Go, Rust, etc.)
- Web development (React, Next.js, Node.js, etc.)
- Backend development and APIs
- Database design and optimization
- DevOps and deployment
- Code review and optimization
- Testing and debugging
- Software architecture and design patterns

Always provide:
- Clean, well-commented code
- Explanations of your approach
- Best practices and security considerations
- Testing suggestions
- Performance optimization tips
- Alternative solutions when applicable`,
        preferredProvider: "groq",
        preferredModel: "llama-3.1-70b-versatile",
        temperature: 0.2,
        maxTokens: 1500,
        capabilities: [
            "coding",
            "debugging",
            "architecture",
            "fast"
        ]
    },
    analyst: {
        id: "analyst",
        name: "Data Analyst",
        type: "ANALYST",
        description: "Specialized in data analysis, visualization, and insights",
        systemPrompt: `You are a data analysis expert. You help users understand data, create visualizations, perform statistical analysis, and derive actionable insights.

Your capabilities include:
- Statistical analysis and interpretation
- Data visualization recommendations
- Trend identification and forecasting
- A/B testing and experimentation
- Business intelligence and KPI analysis
- Data cleaning and preprocessing guidance
- Machine learning model recommendations
- Report generation and presentation

Always provide:
- Clear data interpretations
- Visualization suggestions
- Statistical significance assessments
- Actionable business insights
- Methodology explanations
- Assumptions and limitations
- Next steps and recommendations`,
        preferredProvider: "cerebras",
        preferredModel: "llama3.1-70b",
        temperature: 0.1,
        maxTokens: 1200,
        capabilities: [
            "analysis",
            "statistics",
            "visualization",
            "ultra-fast"
        ]
    },
    creative: {
        id: "creative",
        name: "Creative Agent",
        type: "CREATIVE",
        description: "Specialized in creative writing, brainstorming, and content creation",
        systemPrompt: `You are a creative AI assistant specializing in content creation, writing, and creative problem-solving. You help with various creative tasks and provide innovative solutions.

Your creative capabilities include:
- Creative writing (stories, articles, copy)
- Brainstorming and idea generation
- Content strategy and planning
- Marketing copy and messaging
- Creative problem-solving
- Storytelling and narrative development
- Brand voice and tone development
- Visual concept descriptions

Always provide:
- Original and engaging content
- Multiple creative options
- Clear explanations of creative choices
- Audience-appropriate tone and style
- Structured creative processes
- Inspiration and creative direction
- Practical implementation suggestions`,
        preferredProvider: "chutes",
        preferredModel: "claude-3.5-sonnet",
        temperature: 0.8,
        maxTokens: 1500,
        capabilities: [
            "creative",
            "writing",
            "brainstorming",
            "content"
        ]
    },
    multilingual: {
        id: "multilingual",
        name: "Multilingual Agent",
        type: "MULTILINGUAL",
        description: "Expert in translation, multilingual content, and cross-cultural communication",
        systemPrompt: `You are a multilingual AI specialist with expertise in translation, cross-cultural communication, and international content creation. You excel at working across languages and cultures.

Your multilingual capabilities include:
- High-quality translation between languages
- Cultural adaptation and localization
- Multilingual content creation
- Cross-cultural communication guidance
- Language learning assistance
- International business communication
- Cultural sensitivity and awareness
- Regional dialect and colloquialism understanding

Always provide:
- Accurate and culturally appropriate translations
- Context-aware language adaptations
- Cultural insights and explanations
- Multiple language options when relevant
- Proper formatting for different writing systems
- Regional variations and preferences
- Professional and respectful communication`,
        preferredProvider: "mistral",
        preferredModel: "mistral-large-latest",
        temperature: 0.4,
        maxTokens: 1500,
        capabilities: [
            "translation",
            "multilingual",
            "cultural",
            "localization"
        ]
    }
};
class AIAgentService {
    clients = new Map();
    sessions = new Map();
    constructor(){
        // Initialize clients for available providers
        const providers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$providers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAvailableProviders"])();
        for (const provider of providers){
            try {
                this.clients.set(provider.id, new __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$providers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AIClient"](provider.id));
            } catch (error) {
                console.warn(`Failed to initialize ${provider.name} client:`, error);
            }
        }
    }
    // Get available agents based on configured providers
    getAvailableAgents() {
        const availableProviders = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$providers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAvailableProviders"])().map((p)=>p.id);
        return Object.values(AGENT_CONFIGS).filter((agent)=>{
            // Check if agent's preferred provider is available
            if (agent.preferredProvider && availableProviders.includes(agent.preferredProvider)) {
                return true;
            }
            // Or if any provider is available for fallback
            return availableProviders.length > 0;
        });
    }
    // Get agent configuration
    getAgentConfig(agentId) {
        return AGENT_CONFIGS[agentId] || null;
    }
    // Create a new agent session
    createSession(agentId, userId) {
        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const session = {
            id: sessionId,
            agentId,
            userId,
            messages: [],
            createdAt: new Date(),
            updatedAt: new Date(),
            metadata: {}
        };
        this.sessions.set(sessionId, session);
        return session;
    }
    // Get session
    getSession(sessionId) {
        return this.sessions.get(sessionId) || null;
    }
    // Send message to agent
    async sendMessage(sessionId, userMessage, options) {
        const session = this.getSession(sessionId);
        if (!session) {
            throw new Error("Session not found");
        }
        const agentConfig = this.getAgentConfig(session.agentId);
        if (!agentConfig) {
            throw new Error("Agent configuration not found");
        }
        // Add user message to session
        const userMsg = {
            role: "user",
            content: userMessage
        };
        session.messages.push(userMsg);
        // Determine which client/model to use
        const { client, model } = this.selectClientAndModel(agentConfig, options);
        // Prepare messages for AI
        const messages = [
            {
                role: "system",
                content: agentConfig.systemPrompt
            },
            ...session.messages
        ];
        // Make AI request
        const request = {
            model: model.id,
            messages,
            temperature: options?.temperature ?? agentConfig.temperature ?? 0.7,
            maxTokens: options?.maxTokens ?? agentConfig.maxTokens ?? 1000
        };
        try {
            const response = await client.chat(request);
            const assistantMessage = response.choices[0]?.message;
            if (!assistantMessage) {
                throw new Error("No response from AI model");
            }
            // Add assistant response to session
            const assistantMsg = {
                role: "assistant",
                content: assistantMessage.content
            };
            session.messages.push(assistantMsg);
            session.updatedAt = new Date();
            return {
                content: assistantMessage.content,
                model: model.id,
                provider: model.provider,
                usage: response.usage,
                metadata: {
                    sessionId,
                    agentId: session.agentId,
                    messageCount: session.messages.length
                }
            };
        } catch (error) {
            console.error("AI Agent Error:", error);
            throw new Error(`Failed to get response from AI agent: ${error}`);
        }
    }
    // Select appropriate client and model
    selectClientAndModel(agentConfig, options) {
        // Use specified provider/model if provided
        if (options?.provider && options?.model) {
            const client = this.clients.get(options.provider);
            if (client) {
                const models = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$providers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAvailableModels"])();
                const model = models.find((m)=>m.id === options.model && m.provider === options.provider);
                if (model) {
                    return {
                        client,
                        model
                    };
                }
            }
        }
        // Use agent's preferred provider/model
        if (agentConfig.preferredProvider && agentConfig.preferredModel) {
            const client = this.clients.get(agentConfig.preferredProvider);
            if (client) {
                const models = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$providers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAvailableModels"])();
                const model = models.find((m)=>m.id === agentConfig.preferredModel && m.provider === agentConfig.preferredProvider);
                if (model) {
                    return {
                        client,
                        model
                    };
                }
            }
        }
        // Fallback to best available model for agent type
        const taskMap = {
            ASSISTANT: "chat",
            RESEARCHER: "reasoning",
            CODER: "coding",
            ANALYST: "reasoning",
            CREATIVE: "chat"
        };
        const task = taskMap[agentConfig.type] || "chat";
        const model = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$providers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getBestModelForTask"])(task);
        if (!model) {
            throw new Error("No suitable AI model available");
        }
        const client = this.clients.get(model.provider);
        if (!client) {
            throw new Error(`No client available for provider: ${model.provider}`);
        }
        return {
            client,
            model
        };
    }
    // Get session history
    getSessionHistory(sessionId) {
        const session = this.getSession(sessionId);
        return session ? session.messages : [];
    }
    // Clear session
    clearSession(sessionId) {
        return this.sessions.delete(sessionId);
    }
    // Get provider status
    getProviderStatus() {
        const providers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$providers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAvailableProviders"])();
        return providers.map((provider)=>({
                id: provider.id,
                name: provider.name,
                available: this.clients.has(provider.id),
                models: provider.models.length
            }));
    }
    // Test provider connection
    async testProvider(providerId) {
        const client = this.clients.get(providerId);
        if (!client) return false;
        try {
            const models = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$providers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAvailableModels"])().filter((m)=>m.provider === providerId);
            if (models.length === 0) return false;
            const response = await client.chat({
                model: models[0].id,
                messages: [
                    {
                        role: "user",
                        content: "Hello"
                    }
                ],
                maxTokens: 10
            });
            return !!response.choices[0]?.message?.content;
        } catch (error) {
            console.error(`Provider ${providerId} test failed:`, error);
            return false;
        }
    }
}
const aiAgentService = new AIAgentService();
function getAvailableAgentTypes() {
    return Object.keys(AGENT_CONFIGS);
}
function createAgentSession(agentId, userId) {
    return aiAgentService.createSession(agentId, userId);
}
async function chatWithAgent(sessionId, message, options) {
    return aiAgentService.sendMessage(sessionId, message, options);
}
}}),
"[project]/app/api/agents/chat/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
// API route for agent chat interactions
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$agent$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/ai-agent-service.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$providers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/ai-providers.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        const body = await request.json();
        const { sessionId, message, agentId, userId, options } = body;
        console.log('Chat request received:', {
            sessionId,
            agentId,
            userId,
            messageLength: message?.length
        });
        // Validate required fields
        if (!message) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Message is required'
            }, {
                status: 400
            });
        }
        let currentSessionId = sessionId;
        // Create new session if none provided
        if (!currentSessionId && agentId && userId) {
            const session = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$agent$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["aiAgentService"].createSession(agentId, userId);
            currentSessionId = session.id;
        }
        if (!currentSessionId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Session ID or agent/user IDs required'
            }, {
                status: 400
            });
        }
        // Send message to agent
        console.log('Sending message to agent service:', {
            currentSessionId,
            agentId
        });
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$agent$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["aiAgentService"].sendMessage(currentSessionId, message, options);
        console.log('Received response from agent service:', {
            model: response.model,
            provider: response.provider
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            sessionId: currentSessionId,
            response: response.content,
            model: response.model,
            provider: response.provider,
            usage: response.usage,
            metadata: response.metadata
        });
    } catch (error) {
        console.error('Agent chat error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to process agent chat',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const action = searchParams.get('action');
        switch(action){
            case 'agents':
                // Get available agents
                const agents = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$agent$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["aiAgentService"].getAvailableAgents();
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    agents
                });
            case 'providers':
                // Get provider status
                const providers = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$agent$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["aiAgentService"].getProviderStatus();
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    providers
                });
            case 'models':
                // Get available models
                const models = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$providers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAvailableModels"])();
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    models
                });
            case 'session':
                // Get session history
                const sessionId = searchParams.get('sessionId');
                if (!sessionId) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        error: 'Session ID required'
                    }, {
                        status: 400
                    });
                }
                const history = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$agent$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["aiAgentService"].getSessionHistory(sessionId);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    history
                });
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Invalid action'
                }, {
                    status: 400
                });
        }
    } catch (error) {
        console.error('Agent API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__4912a320._.js.map