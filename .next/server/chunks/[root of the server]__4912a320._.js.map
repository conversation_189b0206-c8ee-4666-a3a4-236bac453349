{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/lib/ai-providers.ts"], "sourcesContent": ["// AI Provider Configuration and Client Library\n// Supports multiple AI providers with unified interface\n\nexport interface AIProvider {\n  id: string;\n  name: string;\n  baseUrl: string;\n  apiKey: string | undefined;\n  models: AIModel[];\n  headers: Record<string, string>;\n  isAvailable: boolean;\n}\n\nexport interface AIModel {\n  id: string;\n  name: string;\n  provider: string;\n  contextLength: number;\n  inputCost?: number; // per 1M tokens\n  outputCost?: number; // per 1M tokens\n  capabilities: string[];\n}\n\nexport interface ChatMessage {\n  role: 'system' | 'user' | 'assistant';\n  content: string;\n}\n\nexport interface ChatRequest {\n  model: string;\n  messages: ChatMessage[];\n  temperature?: number;\n  maxTokens?: number;\n  stream?: boolean;\n}\n\nexport interface ChatResponse {\n  id: string;\n  model: string;\n  choices: {\n    message: {\n      role: string;\n      content: string;\n    };\n    finishReason: string;\n  }[];\n  usage: {\n    promptTokens: number;\n    completionTokens: number;\n    totalTokens: number;\n  };\n}\n\n// AI Provider Configurations\nexport const AI_PROVIDERS: Record<string, Omit<AIProvider, 'isAvailable'>> = {\n  openrouter: {\n    id: 'openrouter',\n    name: 'OpenRouter',\n    baseUrl: 'https://openrouter.ai/api/v1',\n    apiKey: process.env.OPENROUTER_API_KEY,\n    headers: {\n      'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,\n      'Content-Type': 'application/json',\n      'HTTP-Referer': process.env.NEXTAUTH_URL || 'http://localhost:3000',\n      'X-Title': 'Knowledge OS'\n    },\n    models: [\n      {\n        id: 'anthropic/claude-3.5-sonnet',\n        name: 'Claude 3.5 Sonnet',\n        provider: 'openrouter',\n        contextLength: 200000,\n        inputCost: 3.0,\n        outputCost: 15.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'analysis']\n      },\n      {\n        id: 'openai/gpt-4o',\n        name: 'GPT-4o',\n        provider: 'openrouter',\n        contextLength: 128000,\n        inputCost: 2.5,\n        outputCost: 10.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'vision']\n      },\n      {\n        id: 'meta-llama/llama-3.1-405b-instruct',\n        name: 'Llama 3.1 405B',\n        provider: 'openrouter',\n        contextLength: 131072,\n        inputCost: 2.7,\n        outputCost: 2.7,\n        capabilities: ['chat', 'reasoning', 'coding']\n      },\n      {\n        id: 'google/gemini-pro-1.5',\n        name: 'Gemini Pro 1.5',\n        provider: 'openrouter',\n        contextLength: 2000000,\n        inputCost: 1.25,\n        outputCost: 5.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'vision']\n      }\n    ]\n  },\n\n  google: {\n    id: 'google',\n    name: 'Google Gemini',\n    baseUrl: 'https://generativelanguage.googleapis.com/v1beta',\n    apiKey: process.env.GOOGLE_API_KEY,\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    models: [\n      {\n        id: 'gemini-1.5-pro',\n        name: 'Gemini 1.5 Pro',\n        provider: 'google',\n        contextLength: 2000000,\n        inputCost: 1.25,\n        outputCost: 5.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'vision', 'long-context']\n      },\n      {\n        id: 'gemini-1.5-flash',\n        name: 'Gemini 1.5 Flash',\n        provider: 'google',\n        contextLength: 1000000,\n        inputCost: 0.075,\n        outputCost: 0.3,\n        capabilities: ['chat', 'reasoning', 'coding', 'fast']\n      },\n      {\n        id: 'gemini-pro',\n        name: 'Gemini Pro',\n        provider: 'google',\n        contextLength: 32768,\n        inputCost: 0.5,\n        outputCost: 1.5,\n        capabilities: ['chat', 'reasoning', 'coding']\n      }\n    ]\n  },\n\n  groq: {\n    id: 'groq',\n    name: 'Groq',\n    baseUrl: 'https://api.groq.com/openai/v1',\n    apiKey: process.env.GROQ_API_KEY,\n    headers: {\n      'Authorization': `Bearer ${process.env.GROQ_API_KEY}`,\n      'Content-Type': 'application/json'\n    },\n    models: [\n      {\n        id: 'llama-3.1-70b-versatile',\n        name: 'Llama 3.1 70B',\n        provider: 'groq',\n        contextLength: 131072,\n        inputCost: 0.59,\n        outputCost: 0.79,\n        capabilities: ['chat', 'reasoning', 'coding', 'fast']\n      },\n      {\n        id: 'llama-3.1-8b-instant',\n        name: 'Llama 3.1 8B',\n        provider: 'groq',\n        contextLength: 131072,\n        inputCost: 0.05,\n        outputCost: 0.08,\n        capabilities: ['chat', 'fast', 'efficient']\n      },\n      {\n        id: 'mixtral-8x7b-32768',\n        name: 'Mixtral 8x7B',\n        provider: 'groq',\n        contextLength: 32768,\n        inputCost: 0.24,\n        outputCost: 0.24,\n        capabilities: ['chat', 'reasoning', 'multilingual']\n      },\n      {\n        id: 'gemma2-9b-it',\n        name: 'Gemma 2 9B',\n        provider: 'groq',\n        contextLength: 8192,\n        inputCost: 0.2,\n        outputCost: 0.2,\n        capabilities: ['chat', 'fast', 'efficient']\n      }\n    ]\n  },\n\n  cerebras: {\n    id: 'cerebras',\n    name: 'Cerebras',\n    baseUrl: 'https://api.cerebras.ai/v1',\n    apiKey: process.env.CEREBRAS_API_KEY,\n    headers: {\n      'Authorization': `Bearer ${process.env.CEREBRAS_API_KEY}`,\n      'Content-Type': 'application/json'\n    },\n    models: [\n      {\n        id: 'llama3.1-70b',\n        name: 'Llama 3.1 70B',\n        provider: 'cerebras',\n        contextLength: 128000,\n        inputCost: 0.6,\n        outputCost: 0.6,\n        capabilities: ['chat', 'reasoning', 'coding', 'ultra-fast']\n      },\n      {\n        id: 'llama3.1-8b',\n        name: 'Llama 3.1 8B',\n        provider: 'cerebras',\n        contextLength: 128000,\n        inputCost: 0.1,\n        outputCost: 0.1,\n        capabilities: ['chat', 'ultra-fast', 'efficient']\n      }\n    ]\n  },\n\n  chutes: {\n    id: 'chutes',\n    name: 'Chutes AI',\n    baseUrl: 'https://api.chutes.ai/v1',\n    apiKey: process.env.CHUTES_API_KEY,\n    headers: {\n      'Authorization': `Bearer ${process.env.CHUTES_API_KEY}`,\n      'Content-Type': 'application/json'\n    },\n    models: [\n      {\n        id: 'gpt-4o',\n        name: 'GPT-4o (Chutes)',\n        provider: 'chutes',\n        contextLength: 128000,\n        inputCost: 2.5,\n        outputCost: 10.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'vision']\n      },\n      {\n        id: 'claude-3.5-sonnet',\n        name: 'Claude 3.5 Sonnet (Chutes)',\n        provider: 'chutes',\n        contextLength: 200000,\n        inputCost: 3.0,\n        outputCost: 15.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'analysis']\n      }\n    ]\n  },\n\n  mistral: {\n    id: 'mistral',\n    name: 'Mistral AI',\n    baseUrl: 'https://api.mistral.ai/v1',\n    apiKey: process.env.MISTRAL_API_KEY,\n    headers: {\n      'Authorization': `Bearer ${process.env.MISTRAL_API_KEY}`,\n      'Content-Type': 'application/json'\n    },\n    models: [\n      {\n        id: 'mistral-large-latest',\n        name: 'Mistral Large',\n        provider: 'mistral',\n        contextLength: 128000,\n        inputCost: 2.0,\n        outputCost: 6.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'multilingual']\n      },\n      {\n        id: 'mistral-medium-latest',\n        name: 'Mistral Medium',\n        provider: 'mistral',\n        contextLength: 32768,\n        inputCost: 0.7,\n        outputCost: 2.1,\n        capabilities: ['chat', 'reasoning', 'efficient']\n      },\n      {\n        id: 'mistral-small-latest',\n        name: 'Mistral Small',\n        provider: 'mistral',\n        contextLength: 32768,\n        inputCost: 0.2,\n        outputCost: 0.6,\n        capabilities: ['chat', 'fast', 'efficient']\n      }\n    ]\n  },\n\n  // Optional providers (commented out by default)\n  openai: {\n    id: 'openai',\n    name: 'OpenAI',\n    baseUrl: 'https://api.openai.com/v1',\n    apiKey: process.env.OPENAI_API_KEY,\n    headers: {\n      'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,\n      'Content-Type': 'application/json'\n    },\n    models: [\n      {\n        id: 'gpt-4o',\n        name: 'GPT-4o',\n        provider: 'openai',\n        contextLength: 128000,\n        inputCost: 2.5,\n        outputCost: 10.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'vision']\n      },\n      {\n        id: 'gpt-4o-mini',\n        name: 'GPT-4o Mini',\n        provider: 'openai',\n        contextLength: 128000,\n        inputCost: 0.15,\n        outputCost: 0.6,\n        capabilities: ['chat', 'fast', 'efficient']\n      }\n    ]\n  },\n\n  anthropic: {\n    id: 'anthropic',\n    name: 'Anthropic',\n    baseUrl: 'https://api.anthropic.com/v1',\n    apiKey: process.env.ANTHROPIC_API_KEY,\n    headers: {\n      'x-api-key': process.env.ANTHROPIC_API_KEY || '',\n      'Content-Type': 'application/json',\n      'anthropic-version': '2023-06-01'\n    },\n    models: [\n      {\n        id: 'claude-3-5-sonnet-20241022',\n        name: 'Claude 3.5 Sonnet',\n        provider: 'anthropic',\n        contextLength: 200000,\n        inputCost: 3.0,\n        outputCost: 15.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'analysis']\n      },\n      {\n        id: 'claude-3-haiku-20240307',\n        name: 'Claude 3 Haiku',\n        provider: 'anthropic',\n        contextLength: 200000,\n        inputCost: 0.25,\n        outputCost: 1.25,\n        capabilities: ['chat', 'fast', 'efficient']\n      }\n    ]\n  }\n};\n\n// Get available providers (those with API keys)\nexport function getAvailableProviders(): AIProvider[] {\n  return Object.values(AI_PROVIDERS)\n    .map(provider => ({\n      ...provider,\n      isAvailable: !!provider.apiKey\n    }))\n    .filter(provider => provider.isAvailable);\n}\n\n// Get all models from available providers\nexport function getAvailableModels(): AIModel[] {\n  return getAvailableProviders()\n    .flatMap(provider => provider.models);\n}\n\n// Get models by capability\nexport function getModelsByCapability(capability: string): AIModel[] {\n  return getAvailableModels()\n    .filter(model => model.capabilities.includes(capability));\n}\n\n// Get fastest models\nexport function getFastestModels(): AIModel[] {\n  return getModelsByCapability('fast')\n    .concat(getModelsByCapability('ultra-fast'));\n}\n\n// Get most cost-effective models\nexport function getCostEffectiveModels(): AIModel[] {\n  return getAvailableModels()\n    .filter(model => model.inputCost && model.inputCost < 1.0)\n    .sort((a, b) => (a.inputCost || 0) - (b.inputCost || 0));\n}\n\n// Get provider by model ID\nexport function getProviderForModel(modelId: string): AIProvider | null {\n  const providers = getAvailableProviders();\n  for (const provider of providers) {\n    if (provider.models.some(model => model.id === modelId)) {\n      return provider;\n    }\n  }\n  return null;\n}\n\n// AI Client Class\nexport class AIClient {\n  private provider: AIProvider;\n\n  constructor(providerId: string) {\n    const provider = getAvailableProviders().find(p => p.id === providerId);\n    if (!provider) {\n      throw new Error(`Provider ${providerId} not available or not configured`);\n    }\n    this.provider = provider;\n  }\n\n  async chat(request: ChatRequest): Promise<ChatResponse> {\n    const url = `${this.provider.baseUrl}/chat/completions`;\n    \n    // Handle provider-specific request formatting\n    const body = this.formatRequest(request);\n    \n    const response = await fetch(url, {\n      method: 'POST',\n      headers: this.provider.headers,\n      body: JSON.stringify(body)\n    });\n\n    if (!response.ok) {\n      const error = await response.text();\n      throw new Error(`AI API Error (${response.status}): ${error}`);\n    }\n\n    const data = await response.json();\n    return this.formatResponse(data);\n  }\n\n  private formatRequest(request: ChatRequest): any {\n    // Handle provider-specific formatting\n    switch (this.provider.id) {\n      case 'google':\n        return this.formatGoogleRequest(request);\n      case 'anthropic':\n        return this.formatAnthropicRequest(request);\n      default:\n        // OpenAI-compatible format (most providers)\n        return {\n          model: request.model,\n          messages: request.messages,\n          temperature: request.temperature || 0.7,\n          max_tokens: request.maxTokens || 1000,\n          stream: request.stream || false\n        };\n    }\n  }\n\n  private formatGoogleRequest(request: ChatRequest): any {\n    // Convert to Google's format\n    const contents = request.messages.map(msg => ({\n      role: msg.role === 'assistant' ? 'model' : msg.role,\n      parts: [{ text: msg.content }]\n    }));\n\n    return {\n      contents,\n      generationConfig: {\n        temperature: request.temperature || 0.7,\n        maxOutputTokens: request.maxTokens || 1000\n      }\n    };\n  }\n\n  private formatAnthropicRequest(request: ChatRequest): any {\n    // Convert to Anthropic's format\n    const systemMessage = request.messages.find(m => m.role === 'system');\n    const messages = request.messages.filter(m => m.role !== 'system');\n\n    return {\n      model: request.model,\n      messages,\n      system: systemMessage?.content,\n      max_tokens: request.maxTokens || 1000,\n      temperature: request.temperature || 0.7\n    };\n  }\n\n  private formatResponse(data: any): ChatResponse {\n    // Handle provider-specific response formatting\n    switch (this.provider.id) {\n      case 'google':\n        return this.formatGoogleResponse(data);\n      case 'anthropic':\n        return this.formatAnthropicResponse(data);\n      default:\n        // OpenAI-compatible format\n        return data;\n    }\n  }\n\n  private formatGoogleResponse(data: any): ChatResponse {\n    const candidate = data.candidates?.[0];\n    return {\n      id: data.id || 'google-' + Date.now(),\n      model: 'google-model',\n      choices: [{\n        message: {\n          role: 'assistant',\n          content: candidate?.content?.parts?.[0]?.text || ''\n        },\n        finishReason: candidate?.finishReason || 'stop'\n      }],\n      usage: {\n        promptTokens: data.usageMetadata?.promptTokenCount || 0,\n        completionTokens: data.usageMetadata?.candidatesTokenCount || 0,\n        totalTokens: data.usageMetadata?.totalTokenCount || 0\n      }\n    };\n  }\n\n  private formatAnthropicResponse(data: any): ChatResponse {\n    return {\n      id: data.id,\n      model: data.model,\n      choices: [{\n        message: {\n          role: 'assistant',\n          content: data.content?.[0]?.text || ''\n        },\n        finishReason: data.stop_reason || 'stop'\n      }],\n      usage: {\n        promptTokens: data.usage?.input_tokens || 0,\n        completionTokens: data.usage?.output_tokens || 0,\n        totalTokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0)\n      }\n    };\n  }\n}\n\n// Utility functions\nexport function createAIClient(providerId?: string): AIClient {\n  // Use first available provider if none specified\n  if (!providerId) {\n    const available = getAvailableProviders();\n    if (available.length === 0) {\n      throw new Error('No AI providers configured');\n    }\n    providerId = available[0].id;\n  }\n  \n  return new AIClient(providerId);\n}\n\nexport function getBestModelForTask(task: 'chat' | 'coding' | 'reasoning' | 'fast' | 'cost-effective'): AIModel | null {\n  const models = getAvailableModels();\n  \n  switch (task) {\n    case 'fast':\n      return getFastestModels()[0] || null;\n    case 'cost-effective':\n      return getCostEffectiveModels()[0] || null;\n    case 'coding':\n      return models.find(m => m.capabilities.includes('coding')) || null;\n    case 'reasoning':\n      return models.find(m => m.capabilities.includes('reasoning')) || null;\n    default:\n      return models[0] || null;\n  }\n}\n"], "names": [], "mappings": "AAAA,+CAA+C;AAC/C,wDAAwD;;;;;;;;;;;;;AAqDjD,MAAM,eAAgE;IAC3E,YAAY;QACV,IAAI;QACJ,MAAM;QACN,SAAS;QACT,QAAQ,QAAQ,GAAG,CAAC,kBAAkB;QACtC,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,kBAAkB,EAAE;YAC3D,gBAAgB;YAChB,gBAAgB,QAAQ,GAAG,CAAC,YAAY,IAAI;YAC5C,WAAW;QACb;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAW;YAC3D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAS;YACzD;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;iBAAS;YAC/C;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAS;YACzD;SACD;IACH;IAEA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,SAAS;QACT,QAAQ,QAAQ,GAAG,CAAC,cAAc;QAClC,SAAS;YACP,gBAAgB;QAClB;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;oBAAU;iBAAe;YACzE;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAO;YACvD;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;iBAAS;YAC/C;SACD;IACH;IAEA,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,SAAS;QACT,QAAQ,QAAQ,GAAG,CAAC,YAAY;QAChC,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,YAAY,EAAE;YACrD,gBAAgB;QAClB;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAO;YACvD;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAQ;iBAAY;YAC7C;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;iBAAe;YACrD;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAQ;iBAAY;YAC7C;SACD;IACH;IAEA,UAAU;QACR,IAAI;QACJ,MAAM;QACN,SAAS;QACT,QAAQ,QAAQ,GAAG,CAAC,gBAAgB;QACpC,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,gBAAgB,EAAE;YACzD,gBAAgB;QAClB;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAa;YAC7D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAc;iBAAY;YACnD;SACD;IACH;IAEA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,SAAS;QACT,QAAQ,QAAQ,GAAG,CAAC,cAAc;QAClC,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,cAAc,EAAE;YACvD,gBAAgB;QAClB;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAS;YACzD;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAW;YAC3D;SACD;IACH;IAEA,SAAS;QACP,IAAI;QACJ,MAAM;QACN,SAAS;QACT,QAAQ,QAAQ,GAAG,CAAC,eAAe;QACnC,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,eAAe,EAAE;YACxD,gBAAgB;QAClB;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAe;YAC/D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;iBAAY;YAClD;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAQ;iBAAY;YAC7C;SACD;IACH;IAEA,gDAAgD;IAChD,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,SAAS;QACT,QAAQ,QAAQ,GAAG,CAAC,cAAc;QAClC,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,cAAc,EAAE;YACvD,gBAAgB;QAClB;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAS;YACzD;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAQ;iBAAY;YAC7C;SACD;IACH;IAEA,WAAW;QACT,IAAI;QACJ,MAAM;QACN,SAAS;QACT,QAAQ,QAAQ,GAAG,CAAC,iBAAiB;QACrC,SAAS;YACP,aAAa,QAAQ,GAAG,CAAC,iBAAiB,IAAI;YAC9C,gBAAgB;YAChB,qBAAqB;QACvB;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAW;YAC3D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAQ;iBAAY;YAC7C;SACD;IACH;AACF;AAGO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC,cAClB,GAAG,CAAC,CAAA,WAAY,CAAC;YAChB,GAAG,QAAQ;YACX,aAAa,CAAC,CAAC,SAAS,MAAM;QAChC,CAAC,GACA,MAAM,CAAC,CAAA,WAAY,SAAS,WAAW;AAC5C;AAGO,SAAS;IACd,OAAO,wBACJ,OAAO,CAAC,CAAA,WAAY,SAAS,MAAM;AACxC;AAGO,SAAS,sBAAsB,UAAkB;IACtD,OAAO,qBACJ,MAAM,CAAC,CAAA,QAAS,MAAM,YAAY,CAAC,QAAQ,CAAC;AACjD;AAGO,SAAS;IACd,OAAO,sBAAsB,QAC1B,MAAM,CAAC,sBAAsB;AAClC;AAGO,SAAS;IACd,OAAO,qBACJ,MAAM,CAAC,CAAA,QAAS,MAAM,SAAS,IAAI,MAAM,SAAS,GAAG,KACrD,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE,SAAS,IAAI,CAAC;AAC1D;AAGO,SAAS,oBAAoB,OAAe;IACjD,MAAM,YAAY;IAClB,KAAK,MAAM,YAAY,UAAW;QAChC,IAAI,SAAS,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,UAAU;YACvD,OAAO;QACT;IACF;IACA,OAAO;AACT;AAGO,MAAM;IACH,SAAqB;IAE7B,YAAY,UAAkB,CAAE;QAC9B,MAAM,WAAW,wBAAwB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC5D,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,WAAW,gCAAgC,CAAC;QAC1E;QACA,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA,MAAM,KAAK,OAAoB,EAAyB;QACtD,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC;QAEvD,8CAA8C;QAC9C,MAAM,OAAO,IAAI,CAAC,aAAa,CAAC;QAEhC,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO;YAC9B,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,OAAO;QAC/D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEQ,cAAc,OAAoB,EAAO;QAC/C,sCAAsC;QACtC,OAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE;YACtB,KAAK;gBACH,OAAO,IAAI,CAAC,mBAAmB,CAAC;YAClC,KAAK;gBACH,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACrC;gBACE,4CAA4C;gBAC5C,OAAO;oBACL,OAAO,QAAQ,KAAK;oBACpB,UAAU,QAAQ,QAAQ;oBAC1B,aAAa,QAAQ,WAAW,IAAI;oBACpC,YAAY,QAAQ,SAAS,IAAI;oBACjC,QAAQ,QAAQ,MAAM,IAAI;gBAC5B;QACJ;IACF;IAEQ,oBAAoB,OAAoB,EAAO;QACrD,6BAA6B;QAC7B,MAAM,WAAW,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC5C,MAAM,IAAI,IAAI,KAAK,cAAc,UAAU,IAAI,IAAI;gBACnD,OAAO;oBAAC;wBAAE,MAAM,IAAI,OAAO;oBAAC;iBAAE;YAChC,CAAC;QAED,OAAO;YACL;YACA,kBAAkB;gBAChB,aAAa,QAAQ,WAAW,IAAI;gBACpC,iBAAiB,QAAQ,SAAS,IAAI;YACxC;QACF;IACF;IAEQ,uBAAuB,OAAoB,EAAO;QACxD,gCAAgC;QAChC,MAAM,gBAAgB,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAC5D,MAAM,WAAW,QAAQ,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAEzD,OAAO;YACL,OAAO,QAAQ,KAAK;YACpB;YACA,QAAQ,eAAe;YACvB,YAAY,QAAQ,SAAS,IAAI;YACjC,aAAa,QAAQ,WAAW,IAAI;QACtC;IACF;IAEQ,eAAe,IAAS,EAAgB;QAC9C,+CAA+C;QAC/C,OAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE;YACtB,KAAK;gBACH,OAAO,IAAI,CAAC,oBAAoB,CAAC;YACnC,KAAK;gBACH,OAAO,IAAI,CAAC,uBAAuB,CAAC;YACtC;gBACE,2BAA2B;gBAC3B,OAAO;QACX;IACF;IAEQ,qBAAqB,IAAS,EAAgB;QACpD,MAAM,YAAY,KAAK,UAAU,EAAE,CAAC,EAAE;QACtC,OAAO;YACL,IAAI,KAAK,EAAE,IAAI,YAAY,KAAK,GAAG;YACnC,OAAO;YACP,SAAS;gBAAC;oBACR,SAAS;wBACP,MAAM;wBACN,SAAS,WAAW,SAAS,OAAO,CAAC,EAAE,EAAE,QAAQ;oBACnD;oBACA,cAAc,WAAW,gBAAgB;gBAC3C;aAAE;YACF,OAAO;gBACL,cAAc,KAAK,aAAa,EAAE,oBAAoB;gBACtD,kBAAkB,KAAK,aAAa,EAAE,wBAAwB;gBAC9D,aAAa,KAAK,aAAa,EAAE,mBAAmB;YACtD;QACF;IACF;IAEQ,wBAAwB,IAAS,EAAgB;QACvD,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,SAAS;gBAAC;oBACR,SAAS;wBACP,MAAM;wBACN,SAAS,KAAK,OAAO,EAAE,CAAC,EAAE,EAAE,QAAQ;oBACtC;oBACA,cAAc,KAAK,WAAW,IAAI;gBACpC;aAAE;YACF,OAAO;gBACL,cAAc,KAAK,KAAK,EAAE,gBAAgB;gBAC1C,kBAAkB,KAAK,KAAK,EAAE,iBAAiB;gBAC/C,aAAa,CAAC,KAAK,KAAK,EAAE,gBAAgB,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE,iBAAiB,CAAC;YAChF;QACF;IACF;AACF;AAGO,SAAS,eAAe,UAAmB;IAChD,iDAAiD;IACjD,IAAI,CAAC,YAAY;QACf,MAAM,YAAY;QAClB,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,MAAM,IAAI,MAAM;QAClB;QACA,aAAa,SAAS,CAAC,EAAE,CAAC,EAAE;IAC9B;IAEA,OAAO,IAAI,SAAS;AACtB;AAEO,SAAS,oBAAoB,IAAiE;IACnG,MAAM,SAAS;IAEf,OAAQ;QACN,KAAK;YACH,OAAO,kBAAkB,CAAC,EAAE,IAAI;QAClC,KAAK;YACH,OAAO,wBAAwB,CAAC,EAAE,IAAI;QACxC,KAAK;YACH,OAAO,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,YAAY,CAAC,QAAQ,CAAC,cAAc;QAChE,KAAK;YACH,OAAO,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,YAAY,CAAC,QAAQ,CAAC,iBAAiB;QACnE;YACE,OAAO,MAAM,CAAC,EAAE,IAAI;IACxB;AACF", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/lib/ai-agent-service.ts"], "sourcesContent": ["// AI Agent Service using configured providers\n// Handles agent interactions with multiple AI providers\n\nimport {\n  AI<PERSON>lient,\n  createAIClient,\n  getBestModelForTask,\n  getAvailableProviders,\n  getAvailableModels,\n  type ChatMessage,\n  type ChatRequest,\n  type ChatResponse,\n  type AIModel,\n} from \"./ai-providers\";\n\nexport interface AgentConfig {\n  id: string;\n  name: string;\n  type: \"ASSISTANT\" | \"RESEARCHER\" | \"CODER\" | \"ANALYST\" | \"CREATIVE\";\n  description: string;\n  systemPrompt: string;\n  preferredProvider?: string;\n  preferredModel?: string;\n  temperature?: number;\n  maxTokens?: number;\n  capabilities: string[];\n}\n\nexport interface AgentSession {\n  id: string;\n  agentId: string;\n  userId: string;\n  messages: ChatMessage[];\n  createdAt: Date;\n  updatedAt: Date;\n  metadata?: Record<string, any>;\n}\n\nexport interface AgentResponse {\n  content: string;\n  model: string;\n  provider: string;\n  usage: {\n    promptTokens: number;\n    completionTokens: number;\n    totalTokens: number;\n  };\n  metadata?: Record<string, any>;\n}\n\n// Predefined agent configurations\nexport const AGENT_CONFIGS: Record<string, AgentConfig> = {\n  assistant: {\n    id: \"assistant\",\n    name: \"AI Assistant\",\n    type: \"ASSISTANT\",\n    description: \"A helpful AI assistant for general questions and tasks\",\n    systemPrompt: `You are a helpful, knowledgeable AI assistant. You provide accurate, concise, and helpful responses to user questions. You can help with a wide range of topics including general knowledge, explanations, problem-solving, and task assistance.\n\nKey guidelines:\n- Be helpful, accurate, and concise\n- Ask clarifying questions when needed\n- Provide step-by-step explanations for complex topics\n- Admit when you don't know something\n- Be friendly and professional`,\n    preferredProvider: \"openrouter\",\n    preferredModel: \"anthropic/claude-3.5-sonnet\",\n    temperature: 0.7,\n    maxTokens: 1000,\n    capabilities: [\"chat\", \"reasoning\", \"general-knowledge\"],\n  },\n\n  researcher: {\n    id: \"researcher\",\n    name: \"Research Agent\",\n    type: \"RESEARCHER\",\n    description: \"Specialized in research, analysis, and information synthesis\",\n    systemPrompt: `You are a research specialist AI. You excel at analyzing information, conducting thorough research, synthesizing findings, and providing well-structured reports.\n\nYour capabilities include:\n- Literature review and analysis\n- Data interpretation and insights\n- Fact-checking and verification\n- Research methodology guidance\n- Citation and source evaluation\n- Trend analysis and pattern recognition\n\nAlways provide:\n- Well-structured responses with clear sections\n- Evidence-based conclusions\n- Relevant sources and references when possible\n- Multiple perspectives on complex topics\n- Actionable insights and recommendations`,\n    preferredProvider: \"google\",\n    preferredModel: \"gemini-1.5-pro\",\n    temperature: 0.3,\n    maxTokens: 2000,\n    capabilities: [\"research\", \"analysis\", \"long-context\", \"reasoning\"],\n  },\n\n  coder: {\n    id: \"coder\",\n    name: \"Code Agent\",\n    type: \"CODER\",\n    description: \"Expert in programming, code review, and software development\",\n    systemPrompt: `You are an expert software engineer and coding assistant. You help with programming tasks, code review, debugging, architecture design, and best practices.\n\nYour expertise includes:\n- Multiple programming languages (Python, JavaScript, TypeScript, Go, Rust, etc.)\n- Web development (React, Next.js, Node.js, etc.)\n- Backend development and APIs\n- Database design and optimization\n- DevOps and deployment\n- Code review and optimization\n- Testing and debugging\n- Software architecture and design patterns\n\nAlways provide:\n- Clean, well-commented code\n- Explanations of your approach\n- Best practices and security considerations\n- Testing suggestions\n- Performance optimization tips\n- Alternative solutions when applicable`,\n    preferredProvider: \"groq\",\n    preferredModel: \"llama-3.1-70b-versatile\",\n    temperature: 0.2,\n    maxTokens: 1500,\n    capabilities: [\"coding\", \"debugging\", \"architecture\", \"fast\"],\n  },\n\n  analyst: {\n    id: \"analyst\",\n    name: \"Data Analyst\",\n    type: \"ANALYST\",\n    description: \"Specialized in data analysis, visualization, and insights\",\n    systemPrompt: `You are a data analysis expert. You help users understand data, create visualizations, perform statistical analysis, and derive actionable insights.\n\nYour capabilities include:\n- Statistical analysis and interpretation\n- Data visualization recommendations\n- Trend identification and forecasting\n- A/B testing and experimentation\n- Business intelligence and KPI analysis\n- Data cleaning and preprocessing guidance\n- Machine learning model recommendations\n- Report generation and presentation\n\nAlways provide:\n- Clear data interpretations\n- Visualization suggestions\n- Statistical significance assessments\n- Actionable business insights\n- Methodology explanations\n- Assumptions and limitations\n- Next steps and recommendations`,\n    preferredProvider: \"cerebras\",\n    preferredModel: \"llama3.1-70b\",\n    temperature: 0.1,\n    maxTokens: 1200,\n    capabilities: [\"analysis\", \"statistics\", \"visualization\", \"ultra-fast\"],\n  },\n\n  creative: {\n    id: \"creative\",\n    name: \"Creative Agent\",\n    type: \"CREATIVE\",\n    description:\n      \"Specialized in creative writing, brainstorming, and content creation\",\n    systemPrompt: `You are a creative AI assistant specializing in content creation, writing, and creative problem-solving. You help with various creative tasks and provide innovative solutions.\n\nYour creative capabilities include:\n- Creative writing (stories, articles, copy)\n- Brainstorming and idea generation\n- Content strategy and planning\n- Marketing copy and messaging\n- Creative problem-solving\n- Storytelling and narrative development\n- Brand voice and tone development\n- Visual concept descriptions\n\nAlways provide:\n- Original and engaging content\n- Multiple creative options\n- Clear explanations of creative choices\n- Audience-appropriate tone and style\n- Structured creative processes\n- Inspiration and creative direction\n- Practical implementation suggestions`,\n    preferredProvider: \"chutes\",\n    preferredModel: \"claude-3.5-sonnet\",\n    temperature: 0.8,\n    maxTokens: 1500,\n    capabilities: [\"creative\", \"writing\", \"brainstorming\", \"content\"],\n  },\n\n  multilingual: {\n    id: \"multilingual\",\n    name: \"Multilingual Agent\",\n    type: \"MULTILINGUAL\",\n    description:\n      \"Expert in translation, multilingual content, and cross-cultural communication\",\n    systemPrompt: `You are a multilingual AI specialist with expertise in translation, cross-cultural communication, and international content creation. You excel at working across languages and cultures.\n\nYour multilingual capabilities include:\n- High-quality translation between languages\n- Cultural adaptation and localization\n- Multilingual content creation\n- Cross-cultural communication guidance\n- Language learning assistance\n- International business communication\n- Cultural sensitivity and awareness\n- Regional dialect and colloquialism understanding\n\nAlways provide:\n- Accurate and culturally appropriate translations\n- Context-aware language adaptations\n- Cultural insights and explanations\n- Multiple language options when relevant\n- Proper formatting for different writing systems\n- Regional variations and preferences\n- Professional and respectful communication`,\n    preferredProvider: \"mistral\",\n    preferredModel: \"mistral-large-latest\",\n    temperature: 0.4,\n    maxTokens: 1500,\n    capabilities: [\"translation\", \"multilingual\", \"cultural\", \"localization\"],\n  },\n};\n\nexport class AIAgentService {\n  private clients: Map<string, AIClient> = new Map();\n  private sessions: Map<string, AgentSession> = new Map();\n\n  constructor() {\n    // Initialize clients for available providers\n    const providers = getAvailableProviders();\n    for (const provider of providers) {\n      try {\n        this.clients.set(provider.id, new AIClient(provider.id));\n      } catch (error) {\n        console.warn(`Failed to initialize ${provider.name} client:`, error);\n      }\n    }\n  }\n\n  // Get available agents based on configured providers\n  getAvailableAgents(): AgentConfig[] {\n    const availableProviders = getAvailableProviders().map((p) => p.id);\n    return Object.values(AGENT_CONFIGS).filter((agent) => {\n      // Check if agent's preferred provider is available\n      if (\n        agent.preferredProvider &&\n        availableProviders.includes(agent.preferredProvider)\n      ) {\n        return true;\n      }\n      // Or if any provider is available for fallback\n      return availableProviders.length > 0;\n    });\n  }\n\n  // Get agent configuration\n  getAgentConfig(agentId: string): AgentConfig | null {\n    return AGENT_CONFIGS[agentId] || null;\n  }\n\n  // Create a new agent session\n  createSession(agentId: string, userId: string): AgentSession {\n    const sessionId = `session_${Date.now()}_${Math.random()\n      .toString(36)\n      .substr(2, 9)}`;\n    const session: AgentSession = {\n      id: sessionId,\n      agentId,\n      userId,\n      messages: [],\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      metadata: {},\n    };\n\n    this.sessions.set(sessionId, session);\n    return session;\n  }\n\n  // Get session\n  getSession(sessionId: string): AgentSession | null {\n    return this.sessions.get(sessionId) || null;\n  }\n\n  // Send message to agent\n  async sendMessage(\n    sessionId: string,\n    userMessage: string,\n    options?: {\n      temperature?: number;\n      maxTokens?: number;\n      model?: string;\n      provider?: string;\n    }\n  ): Promise<AgentResponse> {\n    const session = this.getSession(sessionId);\n    if (!session) {\n      throw new Error(\"Session not found\");\n    }\n\n    const agentConfig = this.getAgentConfig(session.agentId);\n    if (!agentConfig) {\n      throw new Error(\"Agent configuration not found\");\n    }\n\n    // Add user message to session\n    const userMsg: ChatMessage = { role: \"user\", content: userMessage };\n    session.messages.push(userMsg);\n\n    // Determine which client/model to use\n    const { client, model } = this.selectClientAndModel(agentConfig, options);\n\n    // Prepare messages for AI\n    const messages: ChatMessage[] = [\n      { role: \"system\", content: agentConfig.systemPrompt },\n      ...session.messages,\n    ];\n\n    // Make AI request\n    const request: ChatRequest = {\n      model: model.id,\n      messages,\n      temperature: options?.temperature ?? agentConfig.temperature ?? 0.7,\n      maxTokens: options?.maxTokens ?? agentConfig.maxTokens ?? 1000,\n    };\n\n    try {\n      const response = await client.chat(request);\n      const assistantMessage = response.choices[0]?.message;\n\n      if (!assistantMessage) {\n        throw new Error(\"No response from AI model\");\n      }\n\n      // Add assistant response to session\n      const assistantMsg: ChatMessage = {\n        role: \"assistant\",\n        content: assistantMessage.content,\n      };\n      session.messages.push(assistantMsg);\n      session.updatedAt = new Date();\n\n      return {\n        content: assistantMessage.content,\n        model: model.id,\n        provider: model.provider,\n        usage: response.usage,\n        metadata: {\n          sessionId,\n          agentId: session.agentId,\n          messageCount: session.messages.length,\n        },\n      };\n    } catch (error) {\n      console.error(\"AI Agent Error:\", error);\n      throw new Error(`Failed to get response from AI agent: ${error}`);\n    }\n  }\n\n  // Select appropriate client and model\n  private selectClientAndModel(\n    agentConfig: AgentConfig,\n    options?: { model?: string; provider?: string }\n  ): { client: AIClient; model: AIModel } {\n    // Use specified provider/model if provided\n    if (options?.provider && options?.model) {\n      const client = this.clients.get(options.provider);\n      if (client) {\n        const models = getAvailableModels();\n        const model = models.find(\n          (m) => m.id === options.model && m.provider === options.provider\n        );\n        if (model) {\n          return { client, model };\n        }\n      }\n    }\n\n    // Use agent's preferred provider/model\n    if (agentConfig.preferredProvider && agentConfig.preferredModel) {\n      const client = this.clients.get(agentConfig.preferredProvider);\n      if (client) {\n        const models = getAvailableModels();\n        const model = models.find(\n          (m) =>\n            m.id === agentConfig.preferredModel &&\n            m.provider === agentConfig.preferredProvider\n        );\n        if (model) {\n          return { client, model };\n        }\n      }\n    }\n\n    // Fallback to best available model for agent type\n    const taskMap: Record<string, string> = {\n      ASSISTANT: \"chat\",\n      RESEARCHER: \"reasoning\",\n      CODER: \"coding\",\n      ANALYST: \"reasoning\",\n      CREATIVE: \"chat\",\n    };\n\n    const task = taskMap[agentConfig.type] || \"chat\";\n    const model = getBestModelForTask(task as any);\n\n    if (!model) {\n      throw new Error(\"No suitable AI model available\");\n    }\n\n    const client = this.clients.get(model.provider);\n    if (!client) {\n      throw new Error(`No client available for provider: ${model.provider}`);\n    }\n\n    return { client, model };\n  }\n\n  // Get session history\n  getSessionHistory(sessionId: string): ChatMessage[] {\n    const session = this.getSession(sessionId);\n    return session ? session.messages : [];\n  }\n\n  // Clear session\n  clearSession(sessionId: string): boolean {\n    return this.sessions.delete(sessionId);\n  }\n\n  // Get provider status\n  getProviderStatus(): Array<{\n    id: string;\n    name: string;\n    available: boolean;\n    models: number;\n  }> {\n    const providers = getAvailableProviders();\n    return providers.map((provider) => ({\n      id: provider.id,\n      name: provider.name,\n      available: this.clients.has(provider.id),\n      models: provider.models.length,\n    }));\n  }\n\n  // Test provider connection\n  async testProvider(providerId: string): Promise<boolean> {\n    const client = this.clients.get(providerId);\n    if (!client) return false;\n\n    try {\n      const models = getAvailableModels().filter(\n        (m) => m.provider === providerId\n      );\n      if (models.length === 0) return false;\n\n      const response = await client.chat({\n        model: models[0].id,\n        messages: [{ role: \"user\", content: \"Hello\" }],\n        maxTokens: 10,\n      });\n\n      return !!response.choices[0]?.message?.content;\n    } catch (error) {\n      console.error(`Provider ${providerId} test failed:`, error);\n      return false;\n    }\n  }\n}\n\n// Singleton instance\nexport const aiAgentService = new AIAgentService();\n\n// Utility functions\nexport function getAvailableAgentTypes(): string[] {\n  return Object.keys(AGENT_CONFIGS);\n}\n\nexport function createAgentSession(\n  agentId: string,\n  userId: string\n): AgentSession {\n  return aiAgentService.createSession(agentId, userId);\n}\n\nexport async function chatWithAgent(\n  sessionId: string,\n  message: string,\n  options?: {\n    temperature?: number;\n    maxTokens?: number;\n    model?: string;\n    provider?: string;\n  }\n): Promise<AgentResponse> {\n  return aiAgentService.sendMessage(sessionId, message, options);\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;AAC9C,wDAAwD;;;;;;;;;AAExD;;AAgDO,MAAM,gBAA6C;IACxD,WAAW;QACT,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,cAAc,CAAC;;;;;;;8BAOW,CAAC;QAC3B,mBAAmB;QACnB,gBAAgB;QAChB,aAAa;QACb,WAAW;QACX,cAAc;YAAC;YAAQ;YAAa;SAAoB;IAC1D;IAEA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,cAAc,CAAC;;;;;;;;;;;;;;;yCAesB,CAAC;QACtC,mBAAmB;QACnB,gBAAgB;QAChB,aAAa;QACb,WAAW;QACX,cAAc;YAAC;YAAY;YAAY;YAAgB;SAAY;IACrE;IAEA,OAAO;QACL,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,cAAc,CAAC;;;;;;;;;;;;;;;;;;uCAkBoB,CAAC;QACpC,mBAAmB;QACnB,gBAAgB;QAChB,aAAa;QACb,WAAW;QACX,cAAc;YAAC;YAAU;YAAa;YAAgB;SAAO;IAC/D;IAEA,SAAS;QACP,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,cAAc,CAAC;;;;;;;;;;;;;;;;;;;gCAmBa,CAAC;QAC7B,mBAAmB;QACnB,gBAAgB;QAChB,aAAa;QACb,WAAW;QACX,cAAc;YAAC;YAAY;YAAc;YAAiB;SAAa;IACzE;IAEA,UAAU;QACR,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aACE;QACF,cAAc,CAAC;;;;;;;;;;;;;;;;;;;sCAmBmB,CAAC;QACnC,mBAAmB;QACnB,gBAAgB;QAChB,aAAa;QACb,WAAW;QACX,cAAc;YAAC;YAAY;YAAW;YAAiB;SAAU;IACnE;IAEA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aACE;QACF,cAAc,CAAC;;;;;;;;;;;;;;;;;;;2CAmBwB,CAAC;QACxC,mBAAmB;QACnB,gBAAgB;QAChB,aAAa;QACb,WAAW;QACX,cAAc;YAAC;YAAe;YAAgB;YAAY;SAAe;IAC3E;AACF;AAEO,MAAM;IACH,UAAiC,IAAI,MAAM;IAC3C,WAAsC,IAAI,MAAM;IAExD,aAAc;QACZ,6CAA6C;QAC7C,MAAM,YAAY,CAAA,GAAA,wHAAA,CAAA,wBAAqB,AAAD;QACtC,KAAK,MAAM,YAAY,UAAW;YAChC,IAAI;gBACF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,IAAI,wHAAA,CAAA,WAAQ,CAAC,SAAS,EAAE;YACxD,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,qBAAqB,EAAE,SAAS,IAAI,CAAC,QAAQ,CAAC,EAAE;YAChE;QACF;IACF;IAEA,qDAAqD;IACrD,qBAAoC;QAClC,MAAM,qBAAqB,CAAA,GAAA,wHAAA,CAAA,wBAAqB,AAAD,IAAI,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;QAClE,OAAO,OAAO,MAAM,CAAC,eAAe,MAAM,CAAC,CAAC;YAC1C,mDAAmD;YACnD,IACE,MAAM,iBAAiB,IACvB,mBAAmB,QAAQ,CAAC,MAAM,iBAAiB,GACnD;gBACA,OAAO;YACT;YACA,+CAA+C;YAC/C,OAAO,mBAAmB,MAAM,GAAG;QACrC;IACF;IAEA,0BAA0B;IAC1B,eAAe,OAAe,EAAsB;QAClD,OAAO,aAAa,CAAC,QAAQ,IAAI;IACnC;IAEA,6BAA6B;IAC7B,cAAc,OAAe,EAAE,MAAc,EAAgB;QAC3D,MAAM,YAAY,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GACnD,QAAQ,CAAC,IACT,MAAM,CAAC,GAAG,IAAI;QACjB,MAAM,UAAwB;YAC5B,IAAI;YACJ;YACA;YACA,UAAU,EAAE;YACZ,WAAW,IAAI;YACf,WAAW,IAAI;YACf,UAAU,CAAC;QACb;QAEA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW;QAC7B,OAAO;IACT;IAEA,cAAc;IACd,WAAW,SAAiB,EAAuB;QACjD,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc;IACzC;IAEA,wBAAwB;IACxB,MAAM,YACJ,SAAiB,EACjB,WAAmB,EACnB,OAKC,EACuB;QACxB,MAAM,UAAU,IAAI,CAAC,UAAU,CAAC;QAChC,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,cAAc,IAAI,CAAC,cAAc,CAAC,QAAQ,OAAO;QACvD,IAAI,CAAC,aAAa;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,8BAA8B;QAC9B,MAAM,UAAuB;YAAE,MAAM;YAAQ,SAAS;QAAY;QAClE,QAAQ,QAAQ,CAAC,IAAI,CAAC;QAEtB,sCAAsC;QACtC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa;QAEjE,0BAA0B;QAC1B,MAAM,WAA0B;YAC9B;gBAAE,MAAM;gBAAU,SAAS,YAAY,YAAY;YAAC;eACjD,QAAQ,QAAQ;SACpB;QAED,kBAAkB;QAClB,MAAM,UAAuB;YAC3B,OAAO,MAAM,EAAE;YACf;YACA,aAAa,SAAS,eAAe,YAAY,WAAW,IAAI;YAChE,WAAW,SAAS,aAAa,YAAY,SAAS,IAAI;QAC5D;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC;YACnC,MAAM,mBAAmB,SAAS,OAAO,CAAC,EAAE,EAAE;YAE9C,IAAI,CAAC,kBAAkB;gBACrB,MAAM,IAAI,MAAM;YAClB;YAEA,oCAAoC;YACpC,MAAM,eAA4B;gBAChC,MAAM;gBACN,SAAS,iBAAiB,OAAO;YACnC;YACA,QAAQ,QAAQ,CAAC,IAAI,CAAC;YACtB,QAAQ,SAAS,GAAG,IAAI;YAExB,OAAO;gBACL,SAAS,iBAAiB,OAAO;gBACjC,OAAO,MAAM,EAAE;gBACf,UAAU,MAAM,QAAQ;gBACxB,OAAO,SAAS,KAAK;gBACrB,UAAU;oBACR;oBACA,SAAS,QAAQ,OAAO;oBACxB,cAAc,QAAQ,QAAQ,CAAC,MAAM;gBACvC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,sCAAsC,EAAE,OAAO;QAClE;IACF;IAEA,sCAAsC;IAC9B,qBACN,WAAwB,EACxB,OAA+C,EACT;QACtC,2CAA2C;QAC3C,IAAI,SAAS,YAAY,SAAS,OAAO;YACvC,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,QAAQ;YAChD,IAAI,QAAQ;gBACV,MAAM,SAAS,CAAA,GAAA,wHAAA,CAAA,qBAAkB,AAAD;gBAChC,MAAM,QAAQ,OAAO,IAAI,CACvB,CAAC,IAAM,EAAE,EAAE,KAAK,QAAQ,KAAK,IAAI,EAAE,QAAQ,KAAK,QAAQ,QAAQ;gBAElE,IAAI,OAAO;oBACT,OAAO;wBAAE;wBAAQ;oBAAM;gBACzB;YACF;QACF;QAEA,uCAAuC;QACvC,IAAI,YAAY,iBAAiB,IAAI,YAAY,cAAc,EAAE;YAC/D,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,iBAAiB;YAC7D,IAAI,QAAQ;gBACV,MAAM,SAAS,CAAA,GAAA,wHAAA,CAAA,qBAAkB,AAAD;gBAChC,MAAM,QAAQ,OAAO,IAAI,CACvB,CAAC,IACC,EAAE,EAAE,KAAK,YAAY,cAAc,IACnC,EAAE,QAAQ,KAAK,YAAY,iBAAiB;gBAEhD,IAAI,OAAO;oBACT,OAAO;wBAAE;wBAAQ;oBAAM;gBACzB;YACF;QACF;QAEA,kDAAkD;QAClD,MAAM,UAAkC;YACtC,WAAW;YACX,YAAY;YACZ,OAAO;YACP,SAAS;YACT,UAAU;QACZ;QAEA,MAAM,OAAO,OAAO,CAAC,YAAY,IAAI,CAAC,IAAI;QAC1C,MAAM,QAAQ,CAAA,GAAA,wHAAA,CAAA,sBAAmB,AAAD,EAAE;QAElC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,QAAQ;QAC9C,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,MAAM,QAAQ,EAAE;QACvE;QAEA,OAAO;YAAE;YAAQ;QAAM;IACzB;IAEA,sBAAsB;IACtB,kBAAkB,SAAiB,EAAiB;QAClD,MAAM,UAAU,IAAI,CAAC,UAAU,CAAC;QAChC,OAAO,UAAU,QAAQ,QAAQ,GAAG,EAAE;IACxC;IAEA,gBAAgB;IAChB,aAAa,SAAiB,EAAW;QACvC,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;IAC9B;IAEA,sBAAsB;IACtB,oBAKG;QACD,MAAM,YAAY,CAAA,GAAA,wHAAA,CAAA,wBAAqB,AAAD;QACtC,OAAO,UAAU,GAAG,CAAC,CAAC,WAAa,CAAC;gBAClC,IAAI,SAAS,EAAE;gBACf,MAAM,SAAS,IAAI;gBACnB,WAAW,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE;gBACvC,QAAQ,SAAS,MAAM,CAAC,MAAM;YAChC,CAAC;IACH;IAEA,2BAA2B;IAC3B,MAAM,aAAa,UAAkB,EAAoB;QACvD,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAChC,IAAI,CAAC,QAAQ,OAAO;QAEpB,IAAI;YACF,MAAM,SAAS,CAAA,GAAA,wHAAA,CAAA,qBAAkB,AAAD,IAAI,MAAM,CACxC,CAAC,IAAM,EAAE,QAAQ,KAAK;YAExB,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;YAEhC,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC;gBACjC,OAAO,MAAM,CAAC,EAAE,CAAC,EAAE;gBACnB,UAAU;oBAAC;wBAAE,MAAM;wBAAQ,SAAS;oBAAQ;iBAAE;gBAC9C,WAAW;YACb;YAEA,OAAO,CAAC,CAAC,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,SAAS,EAAE,WAAW,aAAa,CAAC,EAAE;YACrD,OAAO;QACT;IACF;AACF;AAGO,MAAM,iBAAiB,IAAI;AAG3B,SAAS;IACd,OAAO,OAAO,IAAI,CAAC;AACrB;AAEO,SAAS,mBACd,OAAe,EACf,MAAc;IAEd,OAAO,eAAe,aAAa,CAAC,SAAS;AAC/C;AAEO,eAAe,cACpB,SAAiB,EACjB,OAAe,EACf,OAKC;IAED,OAAO,eAAe,WAAW,CAAC,WAAW,SAAS;AACxD", "debugId": null}}, {"offset": {"line": 1088, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1094, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/api/agents/chat/route.ts"], "sourcesContent": ["// API route for agent chat interactions\nimport { NextRequest, NextResponse } from 'next/server';\nimport { aiAgentService, type AgentResponse } from '@/lib/ai-agent-service';\nimport { getAvailableProviders, getAvailableModels } from '@/lib/ai-providers';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { sessionId, message, agentId, userId, options } = body;\n\n    console.log('Chat request received:', { sessionId, agentId, userId, messageLength: message?.length });\n\n    // Validate required fields\n    if (!message) {\n      return NextResponse.json(\n        { error: 'Message is required' },\n        { status: 400 }\n      );\n    }\n\n    let currentSessionId = sessionId;\n\n    // Create new session if none provided\n    if (!currentSessionId && agentId && userId) {\n      const session = aiAgentService.createSession(agentId, userId);\n      currentSessionId = session.id;\n    }\n\n    if (!currentSessionId) {\n      return NextResponse.json(\n        { error: 'Session ID or agent/user IDs required' },\n        { status: 400 }\n      );\n    }\n\n    // Send message to agent\n    console.log('Sending message to agent service:', { currentSessionId, agentId });\n    const response: AgentResponse = await aiAgentService.sendMessage(\n      currentSessionId,\n      message,\n      options\n    );\n    console.log('Received response from agent service:', { model: response.model, provider: response.provider });\n\n    return NextResponse.json({\n      success: true,\n      sessionId: currentSessionId,\n      response: response.content,\n      model: response.model,\n      provider: response.provider,\n      usage: response.usage,\n      metadata: response.metadata\n    });\n\n  } catch (error) {\n    console.error('Agent chat error:', error);\n    return NextResponse.json(\n      {\n        error: 'Failed to process agent chat',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const action = searchParams.get('action');\n\n    switch (action) {\n      case 'agents':\n        // Get available agents\n        const agents = aiAgentService.getAvailableAgents();\n        return NextResponse.json({ agents });\n\n      case 'providers':\n        // Get provider status\n        const providers = aiAgentService.getProviderStatus();\n        return NextResponse.json({ providers });\n\n      case 'models':\n        // Get available models\n        const models = getAvailableModels();\n        return NextResponse.json({ models });\n\n      case 'session':\n        // Get session history\n        const sessionId = searchParams.get('sessionId');\n        if (!sessionId) {\n          return NextResponse.json(\n            { error: 'Session ID required' },\n            { status: 400 }\n          );\n        }\n        const history = aiAgentService.getSessionHistory(sessionId);\n        return NextResponse.json({ history });\n\n      default:\n        return NextResponse.json(\n          { error: 'Invalid action' },\n          { status: 400 }\n        );\n    }\n  } catch (error) {\n    console.error('Agent API error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;AACxC;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG;QAEzD,QAAQ,GAAG,CAAC,0BAA0B;YAAE;YAAW;YAAS;YAAQ,eAAe,SAAS;QAAO;QAEnG,2BAA2B;QAC3B,IAAI,CAAC,SAAS;YACZ,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,mBAAmB;QAEvB,sCAAsC;QACtC,IAAI,CAAC,oBAAoB,WAAW,QAAQ;YAC1C,MAAM,UAAU,+HAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,SAAS;YACtD,mBAAmB,QAAQ,EAAE;QAC/B;QAEA,IAAI,CAAC,kBAAkB;YACrB,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwC,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,QAAQ,GAAG,CAAC,qCAAqC;YAAE;YAAkB;QAAQ;QAC7E,MAAM,WAA0B,MAAM,+HAAA,CAAA,iBAAc,CAAC,WAAW,CAC9D,kBACA,SACA;QAEF,QAAQ,GAAG,CAAC,yCAAyC;YAAE,OAAO,SAAS,KAAK;YAAE,UAAU,SAAS,QAAQ;QAAC;QAE1G,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,WAAW;YACX,UAAU,SAAS,OAAO;YAC1B,OAAO,SAAS,KAAK;YACrB,UAAU,SAAS,QAAQ;YAC3B,OAAO,SAAS,KAAK;YACrB,UAAU,SAAS,QAAQ;QAC7B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,OAAQ;YACN,KAAK;gBACH,uBAAuB;gBACvB,MAAM,SAAS,+HAAA,CAAA,iBAAc,CAAC,kBAAkB;gBAChD,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE;gBAAO;YAEpC,KAAK;gBACH,sBAAsB;gBACtB,MAAM,YAAY,+HAAA,CAAA,iBAAc,CAAC,iBAAiB;gBAClD,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE;gBAAU;YAEvC,KAAK;gBACH,uBAAuB;gBACvB,MAAM,SAAS,CAAA,GAAA,wHAAA,CAAA,qBAAkB,AAAD;gBAChC,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE;gBAAO;YAEpC,KAAK;gBACH,sBAAsB;gBACtB,MAAM,YAAY,aAAa,GAAG,CAAC;gBACnC,IAAI,CAAC,WAAW;oBACd,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CACtB;wBAAE,OAAO;oBAAsB,GAC/B;wBAAE,QAAQ;oBAAI;gBAElB;gBACA,MAAM,UAAU,+HAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC;gBACjD,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE;gBAAQ;YAErC;gBACE,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAiB,GAC1B;oBAAE,QAAQ;gBAAI;QAEpB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}, {"offset": {"line": 1218, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}