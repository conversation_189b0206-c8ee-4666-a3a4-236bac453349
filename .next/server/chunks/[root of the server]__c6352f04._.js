module.exports = {

"[project]/.next-internal/server/app/api/test-all-providers/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route.runtime.dev.js [external] (next/dist/compiled/next-server/app-route.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page.runtime.dev.js [external] (next/dist/compiled/next-server/app-page.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/lib/ai-providers.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
// AI Provider Configuration and Client Library
// Supports multiple AI providers with unified interface
__turbopack_context__.s({
    "AIClient": (()=>AIClient),
    "AI_PROVIDERS": (()=>AI_PROVIDERS),
    "createAIClient": (()=>createAIClient),
    "getAvailableModels": (()=>getAvailableModels),
    "getAvailableProviders": (()=>getAvailableProviders),
    "getBestModelForTask": (()=>getBestModelForTask),
    "getCostEffectiveModels": (()=>getCostEffectiveModels),
    "getFastestModels": (()=>getFastestModels),
    "getModelsByCapability": (()=>getModelsByCapability),
    "getProviderForModel": (()=>getProviderForModel)
});
const AI_PROVIDERS = {
    openrouter: {
        id: 'openrouter',
        name: 'OpenRouter',
        baseUrl: 'https://openrouter.ai/api/v1',
        apiKey: process.env.OPENROUTER_API_KEY,
        headers: {
            'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': process.env.NEXTAUTH_URL || 'http://localhost:3000',
            'X-Title': 'Knowledge OS'
        },
        models: [
            {
                id: 'anthropic/claude-3.5-sonnet',
                name: 'Claude 3.5 Sonnet',
                provider: 'openrouter',
                contextLength: 200000,
                inputCost: 3.0,
                outputCost: 15.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'analysis'
                ]
            },
            {
                id: 'openai/gpt-4o',
                name: 'GPT-4o',
                provider: 'openrouter',
                contextLength: 128000,
                inputCost: 2.5,
                outputCost: 10.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'vision'
                ]
            },
            {
                id: 'meta-llama/llama-3.1-405b-instruct',
                name: 'Llama 3.1 405B',
                provider: 'openrouter',
                contextLength: 131072,
                inputCost: 2.7,
                outputCost: 2.7,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding'
                ]
            },
            {
                id: 'google/gemini-pro-1.5',
                name: 'Gemini Pro 1.5',
                provider: 'openrouter',
                contextLength: 2000000,
                inputCost: 1.25,
                outputCost: 5.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'vision'
                ]
            }
        ]
    },
    google: {
        id: 'google',
        name: 'Google Gemini',
        baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
        apiKey: process.env.GOOGLE_API_KEY,
        headers: {
            'Content-Type': 'application/json'
        },
        models: [
            {
                id: 'gemini-1.5-pro',
                name: 'Gemini 1.5 Pro',
                provider: 'google',
                contextLength: 2000000,
                inputCost: 1.25,
                outputCost: 5.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'vision',
                    'long-context'
                ]
            },
            {
                id: 'gemini-1.5-flash',
                name: 'Gemini 1.5 Flash',
                provider: 'google',
                contextLength: 1000000,
                inputCost: 0.075,
                outputCost: 0.3,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'fast'
                ]
            },
            {
                id: 'gemini-pro',
                name: 'Gemini Pro',
                provider: 'google',
                contextLength: 32768,
                inputCost: 0.5,
                outputCost: 1.5,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding'
                ]
            }
        ]
    },
    groq: {
        id: 'groq',
        name: 'Groq',
        baseUrl: 'https://api.groq.com/openai/v1',
        apiKey: process.env.GROQ_API_KEY,
        headers: {
            'Authorization': `Bearer ${process.env.GROQ_API_KEY}`,
            'Content-Type': 'application/json'
        },
        models: [
            {
                id: 'llama-3.1-70b-versatile',
                name: 'Llama 3.1 70B',
                provider: 'groq',
                contextLength: 131072,
                inputCost: 0.59,
                outputCost: 0.79,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'fast'
                ]
            },
            {
                id: 'llama-3.1-8b-instant',
                name: 'Llama 3.1 8B',
                provider: 'groq',
                contextLength: 131072,
                inputCost: 0.05,
                outputCost: 0.08,
                capabilities: [
                    'chat',
                    'fast',
                    'efficient'
                ]
            },
            {
                id: 'mixtral-8x7b-32768',
                name: 'Mixtral 8x7B',
                provider: 'groq',
                contextLength: 32768,
                inputCost: 0.24,
                outputCost: 0.24,
                capabilities: [
                    'chat',
                    'reasoning',
                    'multilingual'
                ]
            },
            {
                id: 'gemma2-9b-it',
                name: 'Gemma 2 9B',
                provider: 'groq',
                contextLength: 8192,
                inputCost: 0.2,
                outputCost: 0.2,
                capabilities: [
                    'chat',
                    'fast',
                    'efficient'
                ]
            }
        ]
    },
    cerebras: {
        id: 'cerebras',
        name: 'Cerebras',
        baseUrl: 'https://api.cerebras.ai/v1',
        apiKey: process.env.CEREBRAS_API_KEY,
        headers: {
            'Authorization': `Bearer ${process.env.CEREBRAS_API_KEY}`,
            'Content-Type': 'application/json'
        },
        models: [
            {
                id: 'llama3.1-70b',
                name: 'Llama 3.1 70B',
                provider: 'cerebras',
                contextLength: 128000,
                inputCost: 0.6,
                outputCost: 0.6,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'ultra-fast'
                ]
            },
            {
                id: 'llama3.1-8b',
                name: 'Llama 3.1 8B',
                provider: 'cerebras',
                contextLength: 128000,
                inputCost: 0.1,
                outputCost: 0.1,
                capabilities: [
                    'chat',
                    'ultra-fast',
                    'efficient'
                ]
            }
        ]
    },
    chutes: {
        id: 'chutes',
        name: 'Chutes AI',
        baseUrl: 'https://api.chutes.ai/v1',
        apiKey: process.env.CHUTES_API_KEY,
        headers: {
            'Authorization': `Bearer ${process.env.CHUTES_API_KEY}`,
            'Content-Type': 'application/json'
        },
        models: [
            {
                id: 'gpt-4o',
                name: 'GPT-4o (Chutes)',
                provider: 'chutes',
                contextLength: 128000,
                inputCost: 2.5,
                outputCost: 10.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'vision'
                ]
            },
            {
                id: 'claude-3.5-sonnet',
                name: 'Claude 3.5 Sonnet (Chutes)',
                provider: 'chutes',
                contextLength: 200000,
                inputCost: 3.0,
                outputCost: 15.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'analysis'
                ]
            }
        ]
    },
    mistral: {
        id: 'mistral',
        name: 'Mistral AI',
        baseUrl: 'https://api.mistral.ai/v1',
        apiKey: process.env.MISTRAL_API_KEY,
        headers: {
            'Authorization': `Bearer ${process.env.MISTRAL_API_KEY}`,
            'Content-Type': 'application/json'
        },
        models: [
            {
                id: 'mistral-large-latest',
                name: 'Mistral Large',
                provider: 'mistral',
                contextLength: 128000,
                inputCost: 2.0,
                outputCost: 6.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'multilingual'
                ]
            },
            {
                id: 'mistral-medium-latest',
                name: 'Mistral Medium',
                provider: 'mistral',
                contextLength: 32768,
                inputCost: 0.7,
                outputCost: 2.1,
                capabilities: [
                    'chat',
                    'reasoning',
                    'efficient'
                ]
            },
            {
                id: 'mistral-small-latest',
                name: 'Mistral Small',
                provider: 'mistral',
                contextLength: 32768,
                inputCost: 0.2,
                outputCost: 0.6,
                capabilities: [
                    'chat',
                    'fast',
                    'efficient'
                ]
            }
        ]
    },
    // Optional providers (commented out by default)
    openai: {
        id: 'openai',
        name: 'OpenAI',
        baseUrl: 'https://api.openai.com/v1',
        apiKey: process.env.OPENAI_API_KEY,
        headers: {
            'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
            'Content-Type': 'application/json'
        },
        models: [
            {
                id: 'gpt-4o',
                name: 'GPT-4o',
                provider: 'openai',
                contextLength: 128000,
                inputCost: 2.5,
                outputCost: 10.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'vision'
                ]
            },
            {
                id: 'gpt-4o-mini',
                name: 'GPT-4o Mini',
                provider: 'openai',
                contextLength: 128000,
                inputCost: 0.15,
                outputCost: 0.6,
                capabilities: [
                    'chat',
                    'fast',
                    'efficient'
                ]
            }
        ]
    },
    anthropic: {
        id: 'anthropic',
        name: 'Anthropic',
        baseUrl: 'https://api.anthropic.com/v1',
        apiKey: process.env.ANTHROPIC_API_KEY,
        headers: {
            'x-api-key': process.env.ANTHROPIC_API_KEY || '',
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01'
        },
        models: [
            {
                id: 'claude-3-5-sonnet-20241022',
                name: 'Claude 3.5 Sonnet',
                provider: 'anthropic',
                contextLength: 200000,
                inputCost: 3.0,
                outputCost: 15.0,
                capabilities: [
                    'chat',
                    'reasoning',
                    'coding',
                    'analysis'
                ]
            },
            {
                id: 'claude-3-haiku-20240307',
                name: 'Claude 3 Haiku',
                provider: 'anthropic',
                contextLength: 200000,
                inputCost: 0.25,
                outputCost: 1.25,
                capabilities: [
                    'chat',
                    'fast',
                    'efficient'
                ]
            }
        ]
    }
};
function getAvailableProviders() {
    return Object.values(AI_PROVIDERS).map((provider)=>({
            ...provider,
            isAvailable: !!provider.apiKey
        })).filter((provider)=>provider.isAvailable);
}
function getAvailableModels() {
    return getAvailableProviders().flatMap((provider)=>provider.models);
}
function getModelsByCapability(capability) {
    return getAvailableModels().filter((model)=>model.capabilities.includes(capability));
}
function getFastestModels() {
    return getModelsByCapability('fast').concat(getModelsByCapability('ultra-fast'));
}
function getCostEffectiveModels() {
    return getAvailableModels().filter((model)=>model.inputCost && model.inputCost < 1.0).sort((a, b)=>(a.inputCost || 0) - (b.inputCost || 0));
}
function getProviderForModel(modelId) {
    const providers = getAvailableProviders();
    for (const provider of providers){
        if (provider.models.some((model)=>model.id === modelId)) {
            return provider;
        }
    }
    return null;
}
class AIClient {
    provider;
    constructor(providerId){
        const provider = getAvailableProviders().find((p)=>p.id === providerId);
        if (!provider) {
            throw new Error(`Provider ${providerId} not available or not configured`);
        }
        this.provider = provider;
    }
    async chat(request) {
        const url = `${this.provider.baseUrl}/chat/completions`;
        // Handle provider-specific request formatting
        const body = this.formatRequest(request);
        const response = await fetch(url, {
            method: 'POST',
            headers: this.provider.headers,
            body: JSON.stringify(body)
        });
        if (!response.ok) {
            const error = await response.text();
            throw new Error(`AI API Error (${response.status}): ${error}`);
        }
        const data = await response.json();
        return this.formatResponse(data);
    }
    formatRequest(request) {
        // Handle provider-specific formatting
        switch(this.provider.id){
            case 'google':
                return this.formatGoogleRequest(request);
            case 'anthropic':
                return this.formatAnthropicRequest(request);
            default:
                // OpenAI-compatible format (most providers)
                return {
                    model: request.model,
                    messages: request.messages,
                    temperature: request.temperature || 0.7,
                    max_tokens: request.maxTokens || 1000,
                    stream: request.stream || false
                };
        }
    }
    formatGoogleRequest(request) {
        // Convert to Google's format
        const contents = request.messages.map((msg)=>({
                role: msg.role === 'assistant' ? 'model' : msg.role,
                parts: [
                    {
                        text: msg.content
                    }
                ]
            }));
        return {
            contents,
            generationConfig: {
                temperature: request.temperature || 0.7,
                maxOutputTokens: request.maxTokens || 1000
            }
        };
    }
    formatAnthropicRequest(request) {
        // Convert to Anthropic's format
        const systemMessage = request.messages.find((m)=>m.role === 'system');
        const messages = request.messages.filter((m)=>m.role !== 'system');
        return {
            model: request.model,
            messages,
            system: systemMessage?.content,
            max_tokens: request.maxTokens || 1000,
            temperature: request.temperature || 0.7
        };
    }
    formatResponse(data) {
        // Handle provider-specific response formatting
        switch(this.provider.id){
            case 'google':
                return this.formatGoogleResponse(data);
            case 'anthropic':
                return this.formatAnthropicResponse(data);
            default:
                // OpenAI-compatible format
                return data;
        }
    }
    formatGoogleResponse(data) {
        const candidate = data.candidates?.[0];
        return {
            id: data.id || 'google-' + Date.now(),
            model: 'google-model',
            choices: [
                {
                    message: {
                        role: 'assistant',
                        content: candidate?.content?.parts?.[0]?.text || ''
                    },
                    finishReason: candidate?.finishReason || 'stop'
                }
            ],
            usage: {
                promptTokens: data.usageMetadata?.promptTokenCount || 0,
                completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
                totalTokens: data.usageMetadata?.totalTokenCount || 0
            }
        };
    }
    formatAnthropicResponse(data) {
        return {
            id: data.id,
            model: data.model,
            choices: [
                {
                    message: {
                        role: 'assistant',
                        content: data.content?.[0]?.text || ''
                    },
                    finishReason: data.stop_reason || 'stop'
                }
            ],
            usage: {
                promptTokens: data.usage?.input_tokens || 0,
                completionTokens: data.usage?.output_tokens || 0,
                totalTokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0)
            }
        };
    }
}
function createAIClient(providerId) {
    // Use first available provider if none specified
    if (!providerId) {
        const available = getAvailableProviders();
        if (available.length === 0) {
            throw new Error('No AI providers configured');
        }
        providerId = available[0].id;
    }
    return new AIClient(providerId);
}
function getBestModelForTask(task) {
    const models = getAvailableModels();
    switch(task){
        case 'fast':
            return getFastestModels()[0] || null;
        case 'cost-effective':
            return getCostEffectiveModels()[0] || null;
        case 'coding':
            return models.find((m)=>m.capabilities.includes('coding')) || null;
        case 'reasoning':
            return models.find((m)=>m.capabilities.includes('reasoning')) || null;
        default:
            return models[0] || null;
    }
}
}}),
"[project]/app/api/test-all-providers/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$providers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/ai-providers.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    const results = [];
    try {
        const providers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$providers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAvailableProviders"])();
        for (const provider of providers){
            const result = {
                id: provider.id,
                name: provider.name,
                hasApiKey: !!provider.apiKey,
                apiKeyFormat: provider.apiKey ? `${provider.apiKey.substring(0, 10)}...` : 'None',
                models: provider.models.length,
                status: 'unknown',
                error: null
            };
            try {
                // Try to create client
                const client = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$ai$2d$providers$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createAIClient"])(provider.id);
                result.status = 'client_created';
                // Try a simple chat request
                const model = provider.models[0];
                if (model) {
                    const response = await client.chat({
                        model: model.id,
                        messages: [
                            {
                                role: 'user',
                                content: 'Hi'
                            }
                        ],
                        maxTokens: 10,
                        temperature: 0.7
                    });
                    if (response.choices[0]?.message?.content) {
                        result.status = 'working';
                    } else {
                        result.status = 'no_response';
                    }
                }
            } catch (error) {
                result.status = 'error';
                result.error = error instanceof Error ? error.message : 'Unknown error';
            }
            results.push(result);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            totalProviders: providers.length,
            results
        });
    } catch (error) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            results
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__c6352f04._.js.map