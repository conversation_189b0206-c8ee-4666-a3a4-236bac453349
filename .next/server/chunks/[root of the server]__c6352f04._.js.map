{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/lib/ai-providers.ts"], "sourcesContent": ["// AI Provider Configuration and Client Library\n// Supports multiple AI providers with unified interface\n\nexport interface AIProvider {\n  id: string;\n  name: string;\n  baseUrl: string;\n  apiKey: string | undefined;\n  models: AIModel[];\n  headers: Record<string, string>;\n  isAvailable: boolean;\n}\n\nexport interface AIModel {\n  id: string;\n  name: string;\n  provider: string;\n  contextLength: number;\n  inputCost?: number; // per 1M tokens\n  outputCost?: number; // per 1M tokens\n  capabilities: string[];\n}\n\nexport interface ChatMessage {\n  role: 'system' | 'user' | 'assistant';\n  content: string;\n}\n\nexport interface ChatRequest {\n  model: string;\n  messages: ChatMessage[];\n  temperature?: number;\n  maxTokens?: number;\n  stream?: boolean;\n}\n\nexport interface ChatResponse {\n  id: string;\n  model: string;\n  choices: {\n    message: {\n      role: string;\n      content: string;\n    };\n    finishReason: string;\n  }[];\n  usage: {\n    promptTokens: number;\n    completionTokens: number;\n    totalTokens: number;\n  };\n}\n\n// AI Provider Configurations\nexport const AI_PROVIDERS: Record<string, Omit<AIProvider, 'isAvailable'>> = {\n  openrouter: {\n    id: 'openrouter',\n    name: 'OpenRouter',\n    baseUrl: 'https://openrouter.ai/api/v1',\n    apiKey: process.env.OPENROUTER_API_KEY,\n    headers: {\n      'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,\n      'Content-Type': 'application/json',\n      'HTTP-Referer': process.env.NEXTAUTH_URL || 'http://localhost:3000',\n      'X-Title': 'Knowledge OS'\n    },\n    models: [\n      {\n        id: 'anthropic/claude-3.5-sonnet',\n        name: 'Claude 3.5 Sonnet',\n        provider: 'openrouter',\n        contextLength: 200000,\n        inputCost: 3.0,\n        outputCost: 15.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'analysis']\n      },\n      {\n        id: 'openai/gpt-4o',\n        name: 'GPT-4o',\n        provider: 'openrouter',\n        contextLength: 128000,\n        inputCost: 2.5,\n        outputCost: 10.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'vision']\n      },\n      {\n        id: 'meta-llama/llama-3.1-405b-instruct',\n        name: 'Llama 3.1 405B',\n        provider: 'openrouter',\n        contextLength: 131072,\n        inputCost: 2.7,\n        outputCost: 2.7,\n        capabilities: ['chat', 'reasoning', 'coding']\n      },\n      {\n        id: 'google/gemini-pro-1.5',\n        name: 'Gemini Pro 1.5',\n        provider: 'openrouter',\n        contextLength: 2000000,\n        inputCost: 1.25,\n        outputCost: 5.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'vision']\n      }\n    ]\n  },\n\n  google: {\n    id: 'google',\n    name: 'Google Gemini',\n    baseUrl: 'https://generativelanguage.googleapis.com/v1beta',\n    apiKey: process.env.GOOGLE_API_KEY,\n    headers: {\n      'Content-Type': 'application/json'\n    },\n    models: [\n      {\n        id: 'gemini-1.5-pro',\n        name: 'Gemini 1.5 Pro',\n        provider: 'google',\n        contextLength: 2000000,\n        inputCost: 1.25,\n        outputCost: 5.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'vision', 'long-context']\n      },\n      {\n        id: 'gemini-1.5-flash',\n        name: 'Gemini 1.5 Flash',\n        provider: 'google',\n        contextLength: 1000000,\n        inputCost: 0.075,\n        outputCost: 0.3,\n        capabilities: ['chat', 'reasoning', 'coding', 'fast']\n      },\n      {\n        id: 'gemini-pro',\n        name: 'Gemini Pro',\n        provider: 'google',\n        contextLength: 32768,\n        inputCost: 0.5,\n        outputCost: 1.5,\n        capabilities: ['chat', 'reasoning', 'coding']\n      }\n    ]\n  },\n\n  groq: {\n    id: 'groq',\n    name: 'Groq',\n    baseUrl: 'https://api.groq.com/openai/v1',\n    apiKey: process.env.GROQ_API_KEY,\n    headers: {\n      'Authorization': `Bearer ${process.env.GROQ_API_KEY}`,\n      'Content-Type': 'application/json'\n    },\n    models: [\n      {\n        id: 'llama-3.1-70b-versatile',\n        name: 'Llama 3.1 70B',\n        provider: 'groq',\n        contextLength: 131072,\n        inputCost: 0.59,\n        outputCost: 0.79,\n        capabilities: ['chat', 'reasoning', 'coding', 'fast']\n      },\n      {\n        id: 'llama-3.1-8b-instant',\n        name: 'Llama 3.1 8B',\n        provider: 'groq',\n        contextLength: 131072,\n        inputCost: 0.05,\n        outputCost: 0.08,\n        capabilities: ['chat', 'fast', 'efficient']\n      },\n      {\n        id: 'mixtral-8x7b-32768',\n        name: 'Mixtral 8x7B',\n        provider: 'groq',\n        contextLength: 32768,\n        inputCost: 0.24,\n        outputCost: 0.24,\n        capabilities: ['chat', 'reasoning', 'multilingual']\n      },\n      {\n        id: 'gemma2-9b-it',\n        name: 'Gemma 2 9B',\n        provider: 'groq',\n        contextLength: 8192,\n        inputCost: 0.2,\n        outputCost: 0.2,\n        capabilities: ['chat', 'fast', 'efficient']\n      }\n    ]\n  },\n\n  cerebras: {\n    id: 'cerebras',\n    name: 'Cerebras',\n    baseUrl: 'https://api.cerebras.ai/v1',\n    apiKey: process.env.CEREBRAS_API_KEY,\n    headers: {\n      'Authorization': `Bearer ${process.env.CEREBRAS_API_KEY}`,\n      'Content-Type': 'application/json'\n    },\n    models: [\n      {\n        id: 'llama3.1-70b',\n        name: 'Llama 3.1 70B',\n        provider: 'cerebras',\n        contextLength: 128000,\n        inputCost: 0.6,\n        outputCost: 0.6,\n        capabilities: ['chat', 'reasoning', 'coding', 'ultra-fast']\n      },\n      {\n        id: 'llama3.1-8b',\n        name: 'Llama 3.1 8B',\n        provider: 'cerebras',\n        contextLength: 128000,\n        inputCost: 0.1,\n        outputCost: 0.1,\n        capabilities: ['chat', 'ultra-fast', 'efficient']\n      }\n    ]\n  },\n\n  chutes: {\n    id: 'chutes',\n    name: 'Chutes AI',\n    baseUrl: 'https://api.chutes.ai/v1',\n    apiKey: process.env.CHUTES_API_KEY,\n    headers: {\n      'Authorization': `Bearer ${process.env.CHUTES_API_KEY}`,\n      'Content-Type': 'application/json'\n    },\n    models: [\n      {\n        id: 'gpt-4o',\n        name: 'GPT-4o (Chutes)',\n        provider: 'chutes',\n        contextLength: 128000,\n        inputCost: 2.5,\n        outputCost: 10.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'vision']\n      },\n      {\n        id: 'claude-3.5-sonnet',\n        name: 'Claude 3.5 Sonnet (Chutes)',\n        provider: 'chutes',\n        contextLength: 200000,\n        inputCost: 3.0,\n        outputCost: 15.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'analysis']\n      }\n    ]\n  },\n\n  mistral: {\n    id: 'mistral',\n    name: 'Mistral AI',\n    baseUrl: 'https://api.mistral.ai/v1',\n    apiKey: process.env.MISTRAL_API_KEY,\n    headers: {\n      'Authorization': `Bearer ${process.env.MISTRAL_API_KEY}`,\n      'Content-Type': 'application/json'\n    },\n    models: [\n      {\n        id: 'mistral-large-latest',\n        name: 'Mistral Large',\n        provider: 'mistral',\n        contextLength: 128000,\n        inputCost: 2.0,\n        outputCost: 6.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'multilingual']\n      },\n      {\n        id: 'mistral-medium-latest',\n        name: 'Mistral Medium',\n        provider: 'mistral',\n        contextLength: 32768,\n        inputCost: 0.7,\n        outputCost: 2.1,\n        capabilities: ['chat', 'reasoning', 'efficient']\n      },\n      {\n        id: 'mistral-small-latest',\n        name: 'Mistral Small',\n        provider: 'mistral',\n        contextLength: 32768,\n        inputCost: 0.2,\n        outputCost: 0.6,\n        capabilities: ['chat', 'fast', 'efficient']\n      }\n    ]\n  },\n\n  // Optional providers (commented out by default)\n  openai: {\n    id: 'openai',\n    name: 'OpenAI',\n    baseUrl: 'https://api.openai.com/v1',\n    apiKey: process.env.OPENAI_API_KEY,\n    headers: {\n      'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,\n      'Content-Type': 'application/json'\n    },\n    models: [\n      {\n        id: 'gpt-4o',\n        name: 'GPT-4o',\n        provider: 'openai',\n        contextLength: 128000,\n        inputCost: 2.5,\n        outputCost: 10.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'vision']\n      },\n      {\n        id: 'gpt-4o-mini',\n        name: 'GPT-4o Mini',\n        provider: 'openai',\n        contextLength: 128000,\n        inputCost: 0.15,\n        outputCost: 0.6,\n        capabilities: ['chat', 'fast', 'efficient']\n      }\n    ]\n  },\n\n  anthropic: {\n    id: 'anthropic',\n    name: 'Anthropic',\n    baseUrl: 'https://api.anthropic.com/v1',\n    apiKey: process.env.ANTHROPIC_API_KEY,\n    headers: {\n      'x-api-key': process.env.ANTHROPIC_API_KEY || '',\n      'Content-Type': 'application/json',\n      'anthropic-version': '2023-06-01'\n    },\n    models: [\n      {\n        id: 'claude-3-5-sonnet-20241022',\n        name: 'Claude 3.5 Sonnet',\n        provider: 'anthropic',\n        contextLength: 200000,\n        inputCost: 3.0,\n        outputCost: 15.0,\n        capabilities: ['chat', 'reasoning', 'coding', 'analysis']\n      },\n      {\n        id: 'claude-3-haiku-20240307',\n        name: 'Claude 3 Haiku',\n        provider: 'anthropic',\n        contextLength: 200000,\n        inputCost: 0.25,\n        outputCost: 1.25,\n        capabilities: ['chat', 'fast', 'efficient']\n      }\n    ]\n  }\n};\n\n// Get available providers (those with API keys)\nexport function getAvailableProviders(): AIProvider[] {\n  return Object.values(AI_PROVIDERS)\n    .map(provider => ({\n      ...provider,\n      isAvailable: !!provider.apiKey\n    }))\n    .filter(provider => provider.isAvailable);\n}\n\n// Get all models from available providers\nexport function getAvailableModels(): AIModel[] {\n  return getAvailableProviders()\n    .flatMap(provider => provider.models);\n}\n\n// Get models by capability\nexport function getModelsByCapability(capability: string): AIModel[] {\n  return getAvailableModels()\n    .filter(model => model.capabilities.includes(capability));\n}\n\n// Get fastest models\nexport function getFastestModels(): AIModel[] {\n  return getModelsByCapability('fast')\n    .concat(getModelsByCapability('ultra-fast'));\n}\n\n// Get most cost-effective models\nexport function getCostEffectiveModels(): AIModel[] {\n  return getAvailableModels()\n    .filter(model => model.inputCost && model.inputCost < 1.0)\n    .sort((a, b) => (a.inputCost || 0) - (b.inputCost || 0));\n}\n\n// Get provider by model ID\nexport function getProviderForModel(modelId: string): AIProvider | null {\n  const providers = getAvailableProviders();\n  for (const provider of providers) {\n    if (provider.models.some(model => model.id === modelId)) {\n      return provider;\n    }\n  }\n  return null;\n}\n\n// AI Client Class\nexport class AIClient {\n  private provider: AIProvider;\n\n  constructor(providerId: string) {\n    const provider = getAvailableProviders().find(p => p.id === providerId);\n    if (!provider) {\n      throw new Error(`Provider ${providerId} not available or not configured`);\n    }\n    this.provider = provider;\n  }\n\n  async chat(request: ChatRequest): Promise<ChatResponse> {\n    const url = `${this.provider.baseUrl}/chat/completions`;\n    \n    // Handle provider-specific request formatting\n    const body = this.formatRequest(request);\n    \n    const response = await fetch(url, {\n      method: 'POST',\n      headers: this.provider.headers,\n      body: JSON.stringify(body)\n    });\n\n    if (!response.ok) {\n      const error = await response.text();\n      throw new Error(`AI API Error (${response.status}): ${error}`);\n    }\n\n    const data = await response.json();\n    return this.formatResponse(data);\n  }\n\n  private formatRequest(request: ChatRequest): any {\n    // Handle provider-specific formatting\n    switch (this.provider.id) {\n      case 'google':\n        return this.formatGoogleRequest(request);\n      case 'anthropic':\n        return this.formatAnthropicRequest(request);\n      default:\n        // OpenAI-compatible format (most providers)\n        return {\n          model: request.model,\n          messages: request.messages,\n          temperature: request.temperature || 0.7,\n          max_tokens: request.maxTokens || 1000,\n          stream: request.stream || false\n        };\n    }\n  }\n\n  private formatGoogleRequest(request: ChatRequest): any {\n    // Convert to Google's format\n    const contents = request.messages.map(msg => ({\n      role: msg.role === 'assistant' ? 'model' : msg.role,\n      parts: [{ text: msg.content }]\n    }));\n\n    return {\n      contents,\n      generationConfig: {\n        temperature: request.temperature || 0.7,\n        maxOutputTokens: request.maxTokens || 1000\n      }\n    };\n  }\n\n  private formatAnthropicRequest(request: ChatRequest): any {\n    // Convert to Anthropic's format\n    const systemMessage = request.messages.find(m => m.role === 'system');\n    const messages = request.messages.filter(m => m.role !== 'system');\n\n    return {\n      model: request.model,\n      messages,\n      system: systemMessage?.content,\n      max_tokens: request.maxTokens || 1000,\n      temperature: request.temperature || 0.7\n    };\n  }\n\n  private formatResponse(data: any): ChatResponse {\n    // Handle provider-specific response formatting\n    switch (this.provider.id) {\n      case 'google':\n        return this.formatGoogleResponse(data);\n      case 'anthropic':\n        return this.formatAnthropicResponse(data);\n      default:\n        // OpenAI-compatible format\n        return data;\n    }\n  }\n\n  private formatGoogleResponse(data: any): ChatResponse {\n    const candidate = data.candidates?.[0];\n    return {\n      id: data.id || 'google-' + Date.now(),\n      model: 'google-model',\n      choices: [{\n        message: {\n          role: 'assistant',\n          content: candidate?.content?.parts?.[0]?.text || ''\n        },\n        finishReason: candidate?.finishReason || 'stop'\n      }],\n      usage: {\n        promptTokens: data.usageMetadata?.promptTokenCount || 0,\n        completionTokens: data.usageMetadata?.candidatesTokenCount || 0,\n        totalTokens: data.usageMetadata?.totalTokenCount || 0\n      }\n    };\n  }\n\n  private formatAnthropicResponse(data: any): ChatResponse {\n    return {\n      id: data.id,\n      model: data.model,\n      choices: [{\n        message: {\n          role: 'assistant',\n          content: data.content?.[0]?.text || ''\n        },\n        finishReason: data.stop_reason || 'stop'\n      }],\n      usage: {\n        promptTokens: data.usage?.input_tokens || 0,\n        completionTokens: data.usage?.output_tokens || 0,\n        totalTokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0)\n      }\n    };\n  }\n}\n\n// Utility functions\nexport function createAIClient(providerId?: string): AIClient {\n  // Use first available provider if none specified\n  if (!providerId) {\n    const available = getAvailableProviders();\n    if (available.length === 0) {\n      throw new Error('No AI providers configured');\n    }\n    providerId = available[0].id;\n  }\n  \n  return new AIClient(providerId);\n}\n\nexport function getBestModelForTask(task: 'chat' | 'coding' | 'reasoning' | 'fast' | 'cost-effective'): AIModel | null {\n  const models = getAvailableModels();\n  \n  switch (task) {\n    case 'fast':\n      return getFastestModels()[0] || null;\n    case 'cost-effective':\n      return getCostEffectiveModels()[0] || null;\n    case 'coding':\n      return models.find(m => m.capabilities.includes('coding')) || null;\n    case 'reasoning':\n      return models.find(m => m.capabilities.includes('reasoning')) || null;\n    default:\n      return models[0] || null;\n  }\n}\n"], "names": [], "mappings": "AAAA,+CAA+C;AAC/C,wDAAwD;;;;;;;;;;;;;AAqDjD,MAAM,eAAgE;IAC3E,YAAY;QACV,IAAI;QACJ,MAAM;QACN,SAAS;QACT,QAAQ,QAAQ,GAAG,CAAC,kBAAkB;QACtC,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,kBAAkB,EAAE;YAC3D,gBAAgB;YAChB,gBAAgB,QAAQ,GAAG,CAAC,YAAY,IAAI;YAC5C,WAAW;QACb;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAW;YAC3D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAS;YACzD;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;iBAAS;YAC/C;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAS;YACzD;SACD;IACH;IAEA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,SAAS;QACT,QAAQ,QAAQ,GAAG,CAAC,cAAc;QAClC,SAAS;YACP,gBAAgB;QAClB;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;oBAAU;iBAAe;YACzE;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAO;YACvD;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;iBAAS;YAC/C;SACD;IACH;IAEA,MAAM;QACJ,IAAI;QACJ,MAAM;QACN,SAAS;QACT,QAAQ,QAAQ,GAAG,CAAC,YAAY;QAChC,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,YAAY,EAAE;YACrD,gBAAgB;QAClB;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAO;YACvD;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAQ;iBAAY;YAC7C;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;iBAAe;YACrD;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAQ;iBAAY;YAC7C;SACD;IACH;IAEA,UAAU;QACR,IAAI;QACJ,MAAM;QACN,SAAS;QACT,QAAQ,QAAQ,GAAG,CAAC,gBAAgB;QACpC,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,gBAAgB,EAAE;YACzD,gBAAgB;QAClB;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAa;YAC7D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAc;iBAAY;YACnD;SACD;IACH;IAEA,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,SAAS;QACT,QAAQ,QAAQ,GAAG,CAAC,cAAc;QAClC,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,cAAc,EAAE;YACvD,gBAAgB;QAClB;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAS;YACzD;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAW;YAC3D;SACD;IACH;IAEA,SAAS;QACP,IAAI;QACJ,MAAM;QACN,SAAS;QACT,QAAQ,QAAQ,GAAG,CAAC,eAAe;QACnC,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,eAAe,EAAE;YACxD,gBAAgB;QAClB;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAe;YAC/D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;iBAAY;YAClD;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAQ;iBAAY;YAC7C;SACD;IACH;IAEA,gDAAgD;IAChD,QAAQ;QACN,IAAI;QACJ,MAAM;QACN,SAAS;QACT,QAAQ,QAAQ,GAAG,CAAC,cAAc;QAClC,SAAS;YACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,cAAc,EAAE;YACvD,gBAAgB;QAClB;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAS;YACzD;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAQ;iBAAY;YAC7C;SACD;IACH;IAEA,WAAW;QACT,IAAI;QACJ,MAAM;QACN,SAAS;QACT,QAAQ,QAAQ,GAAG,CAAC,iBAAiB;QACrC,SAAS;YACP,aAAa,QAAQ,GAAG,CAAC,iBAAiB,IAAI;YAC9C,gBAAgB;YAChB,qBAAqB;QACvB;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAa;oBAAU;iBAAW;YAC3D;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,cAAc;oBAAC;oBAAQ;oBAAQ;iBAAY;YAC7C;SACD;IACH;AACF;AAGO,SAAS;IACd,OAAO,OAAO,MAAM,CAAC,cAClB,GAAG,CAAC,CAAA,WAAY,CAAC;YAChB,GAAG,QAAQ;YACX,aAAa,CAAC,CAAC,SAAS,MAAM;QAChC,CAAC,GACA,MAAM,CAAC,CAAA,WAAY,SAAS,WAAW;AAC5C;AAGO,SAAS;IACd,OAAO,wBACJ,OAAO,CAAC,CAAA,WAAY,SAAS,MAAM;AACxC;AAGO,SAAS,sBAAsB,UAAkB;IACtD,OAAO,qBACJ,MAAM,CAAC,CAAA,QAAS,MAAM,YAAY,CAAC,QAAQ,CAAC;AACjD;AAGO,SAAS;IACd,OAAO,sBAAsB,QAC1B,MAAM,CAAC,sBAAsB;AAClC;AAGO,SAAS;IACd,OAAO,qBACJ,MAAM,CAAC,CAAA,QAAS,MAAM,SAAS,IAAI,MAAM,SAAS,GAAG,KACrD,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE,SAAS,IAAI,CAAC;AAC1D;AAGO,SAAS,oBAAoB,OAAe;IACjD,MAAM,YAAY;IAClB,KAAK,MAAM,YAAY,UAAW;QAChC,IAAI,SAAS,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,UAAU;YACvD,OAAO;QACT;IACF;IACA,OAAO;AACT;AAGO,MAAM;IACH,SAAqB;IAE7B,YAAY,UAAkB,CAAE;QAC9B,MAAM,WAAW,wBAAwB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC5D,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,WAAW,gCAAgC,CAAC;QAC1E;QACA,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA,MAAM,KAAK,OAAoB,EAAyB;QACtD,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC;QAEvD,8CAA8C;QAC9C,MAAM,OAAO,IAAI,CAAC,aAAa,CAAC;QAEhC,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO;YAC9B,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,OAAO;QAC/D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B;IAEQ,cAAc,OAAoB,EAAO;QAC/C,sCAAsC;QACtC,OAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE;YACtB,KAAK;gBACH,OAAO,IAAI,CAAC,mBAAmB,CAAC;YAClC,KAAK;gBACH,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACrC;gBACE,4CAA4C;gBAC5C,OAAO;oBACL,OAAO,QAAQ,KAAK;oBACpB,UAAU,QAAQ,QAAQ;oBAC1B,aAAa,QAAQ,WAAW,IAAI;oBACpC,YAAY,QAAQ,SAAS,IAAI;oBACjC,QAAQ,QAAQ,MAAM,IAAI;gBAC5B;QACJ;IACF;IAEQ,oBAAoB,OAAoB,EAAO;QACrD,6BAA6B;QAC7B,MAAM,WAAW,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC5C,MAAM,IAAI,IAAI,KAAK,cAAc,UAAU,IAAI,IAAI;gBACnD,OAAO;oBAAC;wBAAE,MAAM,IAAI,OAAO;oBAAC;iBAAE;YAChC,CAAC;QAED,OAAO;YACL;YACA,kBAAkB;gBAChB,aAAa,QAAQ,WAAW,IAAI;gBACpC,iBAAiB,QAAQ,SAAS,IAAI;YACxC;QACF;IACF;IAEQ,uBAAuB,OAAoB,EAAO;QACxD,gCAAgC;QAChC,MAAM,gBAAgB,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAC5D,MAAM,WAAW,QAAQ,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAEzD,OAAO;YACL,OAAO,QAAQ,KAAK;YACpB;YACA,QAAQ,eAAe;YACvB,YAAY,QAAQ,SAAS,IAAI;YACjC,aAAa,QAAQ,WAAW,IAAI;QACtC;IACF;IAEQ,eAAe,IAAS,EAAgB;QAC9C,+CAA+C;QAC/C,OAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE;YACtB,KAAK;gBACH,OAAO,IAAI,CAAC,oBAAoB,CAAC;YACnC,KAAK;gBACH,OAAO,IAAI,CAAC,uBAAuB,CAAC;YACtC;gBACE,2BAA2B;gBAC3B,OAAO;QACX;IACF;IAEQ,qBAAqB,IAAS,EAAgB;QACpD,MAAM,YAAY,KAAK,UAAU,EAAE,CAAC,EAAE;QACtC,OAAO;YACL,IAAI,KAAK,EAAE,IAAI,YAAY,KAAK,GAAG;YACnC,OAAO;YACP,SAAS;gBAAC;oBACR,SAAS;wBACP,MAAM;wBACN,SAAS,WAAW,SAAS,OAAO,CAAC,EAAE,EAAE,QAAQ;oBACnD;oBACA,cAAc,WAAW,gBAAgB;gBAC3C;aAAE;YACF,OAAO;gBACL,cAAc,KAAK,aAAa,EAAE,oBAAoB;gBACtD,kBAAkB,KAAK,aAAa,EAAE,wBAAwB;gBAC9D,aAAa,KAAK,aAAa,EAAE,mBAAmB;YACtD;QACF;IACF;IAEQ,wBAAwB,IAAS,EAAgB;QACvD,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,SAAS;gBAAC;oBACR,SAAS;wBACP,MAAM;wBACN,SAAS,KAAK,OAAO,EAAE,CAAC,EAAE,EAAE,QAAQ;oBACtC;oBACA,cAAc,KAAK,WAAW,IAAI;gBACpC;aAAE;YACF,OAAO;gBACL,cAAc,KAAK,KAAK,EAAE,gBAAgB;gBAC1C,kBAAkB,KAAK,KAAK,EAAE,iBAAiB;gBAC/C,aAAa,CAAC,KAAK,KAAK,EAAE,gBAAgB,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE,iBAAiB,CAAC;YAChF;QACF;IACF;AACF;AAGO,SAAS,eAAe,UAAmB;IAChD,iDAAiD;IACjD,IAAI,CAAC,YAAY;QACf,MAAM,YAAY;QAClB,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,MAAM,IAAI,MAAM;QAClB;QACA,aAAa,SAAS,CAAC,EAAE,CAAC,EAAE;IAC9B;IAEA,OAAO,IAAI,SAAS;AACtB;AAEO,SAAS,oBAAoB,IAAiE;IACnG,MAAM,SAAS;IAEf,OAAQ;QACN,KAAK;YACH,OAAO,kBAAkB,CAAC,EAAE,IAAI;QAClC,KAAK;YACH,OAAO,wBAAwB,CAAC,EAAE,IAAI;QACxC,KAAK;YACH,OAAO,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,YAAY,CAAC,QAAQ,CAAC,cAAc;QAChE,KAAK;YACH,OAAO,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,YAAY,CAAC,QAAQ,CAAC,iBAAiB;QACnE;YACE,OAAO,MAAM,CAAC,EAAE,IAAI;IACxB;AACF", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/api/test-all-providers/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getAvailableProviders, createAIClient } from '@/lib/ai-providers';\n\nexport async function GET(request: NextRequest) {\n  const results = [];\n  \n  try {\n    const providers = getAvailableProviders();\n    \n    for (const provider of providers) {\n      const result = {\n        id: provider.id,\n        name: provider.name,\n        hasApiKey: !!provider.apiKey,\n        apiKeyFormat: provider.apiKey ? `${provider.apiKey.substring(0, 10)}...` : 'None',\n        models: provider.models.length,\n        status: 'unknown',\n        error: null as string | null\n      };\n      \n      try {\n        // Try to create client\n        const client = createAIClient(provider.id);\n        result.status = 'client_created';\n        \n        // Try a simple chat request\n        const model = provider.models[0];\n        if (model) {\n          const response = await client.chat({\n            model: model.id,\n            messages: [{ role: 'user', content: 'Hi' }],\n            maxTokens: 10,\n            temperature: 0.7\n          });\n          \n          if (response.choices[0]?.message?.content) {\n            result.status = 'working';\n          } else {\n            result.status = 'no_response';\n          }\n        }\n      } catch (error) {\n        result.status = 'error';\n        result.error = error instanceof Error ? error.message : 'Unknown error';\n      }\n      \n      results.push(result);\n    }\n    \n    return NextResponse.json({\n      success: true,\n      totalProviders: providers.length,\n      results\n    });\n    \n  } catch (error) {\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error',\n      results\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,UAAU,EAAE;IAElB,IAAI;QACF,MAAM,YAAY,CAAA,GAAA,wHAAA,CAAA,wBAAqB,AAAD;QAEtC,KAAK,MAAM,YAAY,UAAW;YAChC,MAAM,SAAS;gBACb,IAAI,SAAS,EAAE;gBACf,MAAM,SAAS,IAAI;gBACnB,WAAW,CAAC,CAAC,SAAS,MAAM;gBAC5B,cAAc,SAAS,MAAM,GAAG,GAAG,SAAS,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;gBAC3E,QAAQ,SAAS,MAAM,CAAC,MAAM;gBAC9B,QAAQ;gBACR,OAAO;YACT;YAEA,IAAI;gBACF,uBAAuB;gBACvB,MAAM,SAAS,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,EAAE;gBACzC,OAAO,MAAM,GAAG;gBAEhB,4BAA4B;gBAC5B,MAAM,QAAQ,SAAS,MAAM,CAAC,EAAE;gBAChC,IAAI,OAAO;oBACT,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC;wBACjC,OAAO,MAAM,EAAE;wBACf,UAAU;4BAAC;gCAAE,MAAM;gCAAQ,SAAS;4BAAK;yBAAE;wBAC3C,WAAW;wBACX,aAAa;oBACf;oBAEA,IAAI,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS,SAAS;wBACzC,OAAO,MAAM,GAAG;oBAClB,OAAO;wBACL,OAAO,MAAM,GAAG;oBAClB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,OAAO,MAAM,GAAG;gBAChB,OAAO,KAAK,GAAG,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC1D;YAEA,QAAQ,IAAI,CAAC;QACf;QAEA,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,gBAAgB,UAAU,MAAM;YAChC;QACF;IAEF,EAAE,OAAO,OAAO;QACd,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 724, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}