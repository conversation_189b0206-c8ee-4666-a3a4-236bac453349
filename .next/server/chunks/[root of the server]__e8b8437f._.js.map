{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/api/debug-env/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const envVars = {\n      OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY ? 'Set' : 'Not set',\n      GOOGLE_API_KEY: process.env.GOOGLE_API_KEY ? 'Set' : 'Not set',\n      GROQ_API_KEY: process.env.GROQ_API_KEY ? 'Set' : 'Not set',\n      CEREBRAS_API_KEY: process.env.CEREBRAS_API_KEY ? 'Set' : 'Not set',\n      MISTRAL_API_KEY: process.env.MISTRAL_API_KEY ? 'Set' : 'Not set',\n      CHUTES_API_KEY: process.env.CHUTES_API_KEY ? 'Set' : 'Not set',\n      OPENAI_API_KEY: process.env.OPENAI_API_KEY ? 'Set' : 'Not set',\n      ANTHROPIC_API_KEY: process.env.ANTHROPIC_API_KEY ? 'Set' : 'Not set',\n    };\n    \n    return NextResponse.json({\n      success: true,\n      environment: envVars,\n      nodeEnv: process.env.NODE_ENV\n    });\n  } catch (error) {\n    return NextResponse.json({\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error'\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU;YACd,oBAAoB,QAAQ,GAAG,CAAC,kBAAkB,GAAG,QAAQ;YAC7D,gBAAgB,QAAQ,GAAG,CAAC,cAAc,GAAG,QAAQ;YACrD,cAAc,QAAQ,GAAG,CAAC,YAAY,GAAG,QAAQ;YACjD,kBAAkB,QAAQ,GAAG,CAAC,gBAAgB,GAAG,QAAQ;YACzD,iBAAiB,QAAQ,GAAG,CAAC,eAAe,GAAG,QAAQ;YACvD,gBAAgB,QAAQ,GAAG,CAAC,cAAc,GAAG,QAAQ;YACrD,gBAAgB,QAAQ,GAAG,CAAC,cAAc,GAAG,QAAQ;YACrD,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB,GAAG,QAAQ;QAC7D;QAEA,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,aAAa;YACb,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,OAAO,+PAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}