{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/helpers/constants.js"], "sourcesContent": ["import { REVISION } from 'three';\n\nconst getVersion = () => parseInt(REVISION.replace(/\\D+/g, ''));\nconst version = /* @__PURE__ */getVersion();\n\nexport { version };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,aAAa,IAAM,SAAS,mMAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,QAAQ;AAC3D,MAAM,UAAU,aAAa,GAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/helpers/useEffectfulState.js"], "sourcesContent": ["import * as React from 'react';\n\nfunction call(ref, value) {\n  if (typeof ref === 'function') ref(value);else if (ref != null) ref.current = value;\n}\nfunction useEffectfulState(fn, deps = [], cb) {\n  const [state, set] = React.useState();\n  React.useLayoutEffect(() => {\n    const value = fn();\n    set(value);\n    call(cb, value);\n    return () => call(cb, null);\n  }, deps);\n  return state;\n}\n\nexport { useEffectfulState };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,KAAK,GAAG,EAAE,KAAK;IACtB,IAAI,OAAO,QAAQ,YAAY,IAAI;SAAY,IAAI,OAAO,MAAM,IAAI,OAAO,GAAG;AAChF;AACA,SAAS,kBAAkB,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;IAC1C,MAAM,CAAC,OAAO,IAAI,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAc,AAAD;IAClC,CAAA,GAAA,oUAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,MAAM,QAAQ;QACd,IAAI;QACJ,KAAK,IAAI;QACT,OAAO,IAAM,KAAK,IAAI;IACxB,GAAG;IACH,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/helpers/deprecated.js"], "sourcesContent": ["/**\n * Sets `BufferAttribute.updateRange` since r159.\n */\nconst setUpdateRange = (attribute, updateRange) => {\n  attribute.updateRanges[0] = updateRange;\n};\n\nexport { setUpdateRange };\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,MAAM,iBAAiB,CAAC,WAAW;IACjC,UAAU,YAAY,CAAC,EAAE,GAAG;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/helpers/glsl/distort.vert.glsl.js"], "sourcesContent": ["var distort = \"#define GLSLIFY 1\\nvec3 mod289(vec3 x){return x-floor(x*(1.0/289.0))*289.0;}vec4 mod289(vec4 x){return x-floor(x*(1.0/289.0))*289.0;}vec4 permute(vec4 x){return mod289(((x*34.0)+1.0)*x);}vec4 taylorInvSqrt(vec4 r){return 1.79284291400159-0.85373472095314*r;}float snoise(vec3 v){const vec2 C=vec2(1.0/6.0,1.0/3.0);const vec4 D=vec4(0.0,0.5,1.0,2.0);vec3 i=floor(v+dot(v,C.yyy));vec3 x0=v-i+dot(i,C.xxx);vec3 g=step(x0.yzx,x0.xyz);vec3 l=1.0-g;vec3 i1=min(g.xyz,l.zxy);vec3 i2=max(g.xyz,l.zxy);vec3 x1=x0-i1+C.xxx;vec3 x2=x0-i2+C.yyy;vec3 x3=x0-D.yyy;i=mod289(i);vec4 p=permute(permute(permute(i.z+vec4(0.0,i1.z,i2.z,1.0))+i.y+vec4(0.0,i1.y,i2.y,1.0))+i.x+vec4(0.0,i1.x,i2.x,1.0));float n_=0.142857142857;vec3 ns=n_*D.wyz-D.xzx;vec4 j=p-49.0*floor(p*ns.z*ns.z);vec4 x_=floor(j*ns.z);vec4 y_=floor(j-7.0*x_);vec4 x=x_*ns.x+ns.yyyy;vec4 y=y_*ns.x+ns.yyyy;vec4 h=1.0-abs(x)-abs(y);vec4 b0=vec4(x.xy,y.xy);vec4 b1=vec4(x.zw,y.zw);vec4 s0=floor(b0)*2.0+1.0;vec4 s1=floor(b1)*2.0+1.0;vec4 sh=-step(h,vec4(0.0));vec4 a0=b0.xzyw+s0.xzyw*sh.xxyy;vec4 a1=b1.xzyw+s1.xzyw*sh.zzww;vec3 p0=vec3(a0.xy,h.x);vec3 p1=vec3(a0.zw,h.y);vec3 p2=vec3(a1.xy,h.z);vec3 p3=vec3(a1.zw,h.w);vec4 norm=taylorInvSqrt(vec4(dot(p0,p0),dot(p1,p1),dot(p2,p2),dot(p3,p3)));p0*=norm.x;p1*=norm.y;p2*=norm.z;p3*=norm.w;vec4 m=max(0.6-vec4(dot(x0,x0),dot(x1,x1),dot(x2,x2),dot(x3,x3)),0.0);m=m*m;return 42.0*dot(m*m,vec4(dot(p0,x0),dot(p1,x1),dot(p2,x2),dot(p3,x3)));}\"; // eslint-disable-line\n\nexport { distort as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,UAAU,25CAA25C,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/helpers/environment-assets.js"], "sourcesContent": ["const presetsObj = {\n  apartment: 'lebombo_1k.hdr',\n  city: 'potsdamer_platz_1k.hdr',\n  dawn: 'kiara_1_dawn_1k.hdr',\n  forest: 'forest_slope_1k.hdr',\n  lobby: 'st_fagans_interior_1k.hdr',\n  night: 'dikhololo_night_1k.hdr',\n  park: 'rooitou_park_1k.hdr',\n  studio: 'studio_small_03_1k.hdr',\n  sunset: 'venice_sunset_1k.hdr',\n  warehouse: 'empty_warehouse_01_1k.hdr'\n};\n\nexport { presetsObj };\n"], "names": [], "mappings": ";;;AAAA,MAAM,aAAa;IACjB,WAAW;IACX,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,WAAW;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/helpers/glsl/DefaultSpotlightShadowShadows.glsl.js"], "sourcesContent": ["var SpotlightShadowShader = \"#define GLSLIFY 1\\nvarying vec2 vUv;uniform sampler2D uShadowMap;uniform float uTime;void main(){vec3 color=texture2D(uShadowMap,vUv).xyz;gl_FragColor=vec4(color,1.);}\"; // eslint-disable-line\n\nexport { SpotlightShadowShader as default };\n"], "names": [], "mappings": ";;;AAAA,IAAI,wBAAwB,2KAA2K,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/materials/ConvolutionMaterial.js"], "sourcesContent": ["import * as THREE from 'three';\nimport { version } from '../helpers/constants.js';\n\nclass ConvolutionMaterial extends THREE.ShaderMaterial {\n  constructor(texelSize = new THREE.Vector2()) {\n    super({\n      uniforms: {\n        inputBuffer: new THREE.Uniform(null),\n        depthBuffer: new THREE.Uniform(null),\n        resolution: new THREE.Uniform(new THREE.Vector2()),\n        texelSize: new THREE.Uniform(new THREE.Vector2()),\n        halfTexelSize: new THREE.Uniform(new THREE.Vector2()),\n        kernel: new THREE.Uniform(0.0),\n        scale: new THREE.Uniform(1.0),\n        cameraNear: new THREE.Uniform(0.0),\n        cameraFar: new THREE.Uniform(1.0),\n        minDepthThreshold: new THREE.Uniform(0.0),\n        maxDepthThreshold: new THREE.Uniform(1.0),\n        depthScale: new THREE.Uniform(0.0),\n        depthToBlurRatioBias: new THREE.Uniform(0.25)\n      },\n      fragmentShader: `#include <common>\n        #include <dithering_pars_fragment>      \n        uniform sampler2D inputBuffer;\n        uniform sampler2D depthBuffer;\n        uniform float cameraNear;\n        uniform float cameraFar;\n        uniform float minDepthThreshold;\n        uniform float maxDepthThreshold;\n        uniform float depthScale;\n        uniform float depthToBlurRatioBias;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          float depthFactor = 0.0;\n          \n          #ifdef USE_DEPTH\n            vec4 depth = texture2D(depthBuffer, vUv);\n            depthFactor = smoothstep(minDepthThreshold, maxDepthThreshold, 1.0-(depth.r * depth.a));\n            depthFactor *= depthScale;\n            depthFactor = max(0.0, min(1.0, depthFactor + 0.25));\n          #endif\n          \n          vec4 sum = texture2D(inputBuffer, mix(vUv0, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv1, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv2, vUv, depthFactor));\n          sum += texture2D(inputBuffer, mix(vUv3, vUv, depthFactor));\n          gl_FragColor = sum * 0.25 ;\n\n          #include <dithering_fragment>\n          #include <tonemapping_fragment>\n          #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n        }`,\n      vertexShader: `uniform vec2 texelSize;\n        uniform vec2 halfTexelSize;\n        uniform float kernel;\n        uniform float scale;\n        varying vec2 vUv;\n        varying vec2 vUv0;\n        varying vec2 vUv1;\n        varying vec2 vUv2;\n        varying vec2 vUv3;\n\n        void main() {\n          vec2 uv = position.xy * 0.5 + 0.5;\n          vUv = uv;\n\n          vec2 dUv = (texelSize * vec2(kernel) + halfTexelSize) * scale;\n          vUv0 = vec2(uv.x - dUv.x, uv.y + dUv.y);\n          vUv1 = vec2(uv.x + dUv.x, uv.y + dUv.y);\n          vUv2 = vec2(uv.x + dUv.x, uv.y - dUv.y);\n          vUv3 = vec2(uv.x - dUv.x, uv.y - dUv.y);\n\n          gl_Position = vec4(position.xy, 1.0, 1.0);\n        }`,\n      blending: THREE.NoBlending,\n      depthWrite: false,\n      depthTest: false\n    });\n    this.toneMapped = false;\n    this.setTexelSize(texelSize.x, texelSize.y);\n    this.kernel = new Float32Array([0.0, 1.0, 2.0, 2.0, 3.0]);\n  }\n  setTexelSize(x, y) {\n    this.uniforms.texelSize.value.set(x, y);\n    this.uniforms.halfTexelSize.value.set(x, y).multiplyScalar(0.5);\n  }\n  setResolution(resolution) {\n    this.uniforms.resolution.value.copy(resolution);\n  }\n}\n\nexport { ConvolutionMaterial };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,4BAA4B,mMAAA,CAAA,iBAAoB;IACpD,YAAY,YAAY,IAAI,mMAAA,CAAA,UAAa,EAAE,CAAE;QAC3C,KAAK,CAAC;YACJ,UAAU;gBACR,aAAa,IAAI,mMAAA,CAAA,UAAa,CAAC;gBAC/B,aAAa,IAAI,mMAAA,CAAA,UAAa,CAAC;gBAC/B,YAAY,IAAI,mMAAA,CAAA,UAAa,CAAC,IAAI,mMAAA,CAAA,UAAa;gBAC/C,WAAW,IAAI,mMAAA,CAAA,UAAa,CAAC,IAAI,mMAAA,CAAA,UAAa;gBAC9C,eAAe,IAAI,mMAAA,CAAA,UAAa,CAAC,IAAI,mMAAA,CAAA,UAAa;gBAClD,QAAQ,IAAI,mMAAA,CAAA,UAAa,CAAC;gBAC1B,OAAO,IAAI,mMAAA,CAAA,UAAa,CAAC;gBACzB,YAAY,IAAI,mMAAA,CAAA,UAAa,CAAC;gBAC9B,WAAW,IAAI,mMAAA,CAAA,UAAa,CAAC;gBAC7B,mBAAmB,IAAI,mMAAA,CAAA,UAAa,CAAC;gBACrC,mBAAmB,IAAI,mMAAA,CAAA,UAAa,CAAC;gBACrC,YAAY,IAAI,mMAAA,CAAA,UAAa,CAAC;gBAC9B,sBAAsB,IAAI,mMAAA,CAAA,UAAa,CAAC;YAC1C;YACA,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAkCH,EAAE,mXAAA,CAAA,UAAO,IAAI,MAAM,wBAAwB,qBAAqB;SAC3E,CAAC;YACJ,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;SAqBZ,CAAC;YACJ,UAAU,mMAAA,CAAA,aAAgB;YAC1B,YAAY;YACZ,WAAW;QACb;QACA,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC;QAC1C,IAAI,CAAC,MAAM,GAAG,IAAI,aAAa;YAAC;YAAK;YAAK;YAAK;YAAK;SAAI;IAC1D;IACA,aAAa,CAAC,EAAE,CAAC,EAAE;QACjB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG;QACrC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,cAAc,CAAC;IAC7D;IACA,cAAc,UAAU,EAAE;QACxB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC;IACtC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/materials/BlurPass.js"], "sourcesContent": ["import { WebGLRenderTarget, LinearFilter, HalfFloatType, Vector2, Scene, Camera, BufferGeometry, BufferAttribute, Mesh } from 'three';\nimport { ConvolutionMaterial } from './ConvolutionMaterial.js';\n\nclass BlurPass {\n  constructor({\n    gl,\n    resolution,\n    width = 500,\n    height = 500,\n    minDepthThreshold = 0,\n    maxDepthThreshold = 1,\n    depthScale = 0,\n    depthToBlurRatioBias = 0.25\n  }) {\n    this.renderToScreen = false;\n    this.renderTargetA = new WebGLRenderTarget(resolution, resolution, {\n      minFilter: LinearFilter,\n      magFilter: LinearFilter,\n      stencilBuffer: false,\n      depthBuffer: false,\n      type: HalfFloatType\n    });\n    this.renderTargetB = this.renderTargetA.clone();\n    this.convolutionMaterial = new ConvolutionMaterial();\n    this.convolutionMaterial.setTexelSize(1.0 / width, 1.0 / height);\n    this.convolutionMaterial.setResolution(new Vector2(width, height));\n    this.scene = new Scene();\n    this.camera = new Camera();\n    this.convolutionMaterial.uniforms.minDepthThreshold.value = minDepthThreshold;\n    this.convolutionMaterial.uniforms.maxDepthThreshold.value = maxDepthThreshold;\n    this.convolutionMaterial.uniforms.depthScale.value = depthScale;\n    this.convolutionMaterial.uniforms.depthToBlurRatioBias.value = depthToBlurRatioBias;\n    this.convolutionMaterial.defines.USE_DEPTH = depthScale > 0;\n    const vertices = new Float32Array([-1, -1, 0, 3, -1, 0, -1, 3, 0]);\n    const uvs = new Float32Array([0, 0, 2, 0, 0, 2]);\n    const geometry = new BufferGeometry();\n    geometry.setAttribute('position', new BufferAttribute(vertices, 3));\n    geometry.setAttribute('uv', new BufferAttribute(uvs, 2));\n    this.screen = new Mesh(geometry, this.convolutionMaterial);\n    this.screen.frustumCulled = false;\n    this.scene.add(this.screen);\n  }\n  render(renderer, inputBuffer, outputBuffer) {\n    const scene = this.scene;\n    const camera = this.camera;\n    const renderTargetA = this.renderTargetA;\n    const renderTargetB = this.renderTargetB;\n    let material = this.convolutionMaterial;\n    let uniforms = material.uniforms;\n    uniforms.depthBuffer.value = inputBuffer.depthTexture;\n    const kernel = material.kernel;\n    let lastRT = inputBuffer;\n    let destRT;\n    let i, l;\n    // Apply the multi-pass blur.\n    for (i = 0, l = kernel.length - 1; i < l; ++i) {\n      // Alternate between targets.\n      destRT = (i & 1) === 0 ? renderTargetA : renderTargetB;\n      uniforms.kernel.value = kernel[i];\n      uniforms.inputBuffer.value = lastRT.texture;\n      renderer.setRenderTarget(destRT);\n      renderer.render(scene, camera);\n      lastRT = destRT;\n    }\n    uniforms.kernel.value = kernel[i];\n    uniforms.inputBuffer.value = lastRT.texture;\n    renderer.setRenderTarget(this.renderToScreen ? null : outputBuffer);\n    renderer.render(scene, camera);\n  }\n}\n\nexport { BlurPass };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM;IACJ,YAAY,EACV,EAAE,EACF,UAAU,EACV,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,oBAAoB,CAAC,EACrB,oBAAoB,CAAC,EACrB,aAAa,CAAC,EACd,uBAAuB,IAAI,EAC5B,CAAE;QACD,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,aAAa,GAAG,IAAI,mMAAA,CAAA,oBAAiB,CAAC,YAAY,YAAY;YACjE,WAAW,mMAAA,CAAA,eAAY;YACvB,WAAW,mMAAA,CAAA,eAAY;YACvB,eAAe;YACf,aAAa;YACb,MAAM,mMAAA,CAAA,gBAAa;QACrB;QACA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK;QAC7C,IAAI,CAAC,mBAAmB,GAAG,IAAI,+XAAA,CAAA,sBAAmB;QAClD,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,MAAM,OAAO,MAAM;QACzD,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,IAAI,mMAAA,CAAA,UAAO,CAAC,OAAO;QAC1D,IAAI,CAAC,KAAK,GAAG,IAAI,mMAAA,CAAA,QAAK;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,mMAAA,CAAA,SAAM;QACxB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,KAAK,GAAG;QAC5D,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,KAAK,GAAG;QAC5D,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,GAAG;QACrD,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,oBAAoB,CAAC,KAAK,GAAG;QAC/D,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,SAAS,GAAG,aAAa;QAC1D,MAAM,WAAW,IAAI,aAAa;YAAC,CAAC;YAAG,CAAC;YAAG;YAAG;YAAG,CAAC;YAAG;YAAG,CAAC;YAAG;YAAG;SAAE;QACjE,MAAM,MAAM,IAAI,aAAa;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;SAAE;QAC/C,MAAM,WAAW,IAAI,mMAAA,CAAA,iBAAc;QACnC,SAAS,YAAY,CAAC,YAAY,IAAI,mMAAA,CAAA,kBAAe,CAAC,UAAU;QAChE,SAAS,YAAY,CAAC,MAAM,IAAI,mMAAA,CAAA,kBAAe,CAAC,KAAK;QACrD,IAAI,CAAC,MAAM,GAAG,IAAI,mMAAA,CAAA,OAAI,CAAC,UAAU,IAAI,CAAC,mBAAmB;QACzD,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG;QAC5B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM;IAC5B;IACA,OAAO,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE;QAC1C,MAAM,QAAQ,IAAI,CAAC,KAAK;QACxB,MAAM,SAAS,IAAI,CAAC,MAAM;QAC1B,MAAM,gBAAgB,IAAI,CAAC,aAAa;QACxC,MAAM,gBAAgB,IAAI,CAAC,aAAa;QACxC,IAAI,WAAW,IAAI,CAAC,mBAAmB;QACvC,IAAI,WAAW,SAAS,QAAQ;QAChC,SAAS,WAAW,CAAC,KAAK,GAAG,YAAY,YAAY;QACrD,MAAM,SAAS,SAAS,MAAM;QAC9B,IAAI,SAAS;QACb,IAAI;QACJ,IAAI,GAAG;QACP,6BAA6B;QAC7B,IAAK,IAAI,GAAG,IAAI,OAAO,MAAM,GAAG,GAAG,IAAI,GAAG,EAAE,EAAG;YAC7C,6BAA6B;YAC7B,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,gBAAgB;YACzC,SAAS,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,EAAE;YACjC,SAAS,WAAW,CAAC,KAAK,GAAG,OAAO,OAAO;YAC3C,SAAS,eAAe,CAAC;YACzB,SAAS,MAAM,CAAC,OAAO;YACvB,SAAS;QACX;QACA,SAAS,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,EAAE;QACjC,SAAS,WAAW,CAAC,KAAK,GAAG,OAAO,OAAO;QAC3C,SAAS,eAAe,CAAC,IAAI,CAAC,cAAc,GAAG,OAAO;QACtD,SAAS,MAAM,CAAC,OAAO;IACzB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/materials/MeshReflectorMaterial.js"], "sourcesContent": ["import { MeshStandardMaterial } from 'three';\n\nclass MeshReflectorMaterial extends MeshStandardMaterial {\n  constructor(parameters = {}) {\n    super(parameters);\n    this._tDepth = {\n      value: null\n    };\n    this._distortionMap = {\n      value: null\n    };\n    this._tDiffuse = {\n      value: null\n    };\n    this._tDiffuseBlur = {\n      value: null\n    };\n    this._textureMatrix = {\n      value: null\n    };\n    this._hasBlur = {\n      value: false\n    };\n    this._mirror = {\n      value: 0.0\n    };\n    this._mixBlur = {\n      value: 0.0\n    };\n    this._blurStrength = {\n      value: 0.5\n    };\n    this._minDepthThreshold = {\n      value: 0.9\n    };\n    this._maxDepthThreshold = {\n      value: 1\n    };\n    this._depthScale = {\n      value: 0\n    };\n    this._depthToBlurRatioBias = {\n      value: 0.25\n    };\n    this._distortion = {\n      value: 1\n    };\n    this._mixContrast = {\n      value: 1.0\n    };\n    this.setValues(parameters);\n  }\n  onBeforeCompile(shader) {\n    var _shader$defines;\n    if (!((_shader$defines = shader.defines) != null && _shader$defines.USE_UV)) {\n      shader.defines.USE_UV = '';\n    }\n    shader.uniforms.hasBlur = this._hasBlur;\n    shader.uniforms.tDiffuse = this._tDiffuse;\n    shader.uniforms.tDepth = this._tDepth;\n    shader.uniforms.distortionMap = this._distortionMap;\n    shader.uniforms.tDiffuseBlur = this._tDiffuseBlur;\n    shader.uniforms.textureMatrix = this._textureMatrix;\n    shader.uniforms.mirror = this._mirror;\n    shader.uniforms.mixBlur = this._mixBlur;\n    shader.uniforms.mixStrength = this._blurStrength;\n    shader.uniforms.minDepthThreshold = this._minDepthThreshold;\n    shader.uniforms.maxDepthThreshold = this._maxDepthThreshold;\n    shader.uniforms.depthScale = this._depthScale;\n    shader.uniforms.depthToBlurRatioBias = this._depthToBlurRatioBias;\n    shader.uniforms.distortion = this._distortion;\n    shader.uniforms.mixContrast = this._mixContrast;\n    shader.vertexShader = `\n        uniform mat4 textureMatrix;\n        varying vec4 my_vUv;\n      ${shader.vertexShader}`;\n    shader.vertexShader = shader.vertexShader.replace('#include <project_vertex>', `#include <project_vertex>\n        my_vUv = textureMatrix * vec4( position, 1.0 );\n        gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );`);\n    shader.fragmentShader = `\n        uniform sampler2D tDiffuse;\n        uniform sampler2D tDiffuseBlur;\n        uniform sampler2D tDepth;\n        uniform sampler2D distortionMap;\n        uniform float distortion;\n        uniform float cameraNear;\n\t\t\t  uniform float cameraFar;\n        uniform bool hasBlur;\n        uniform float mixBlur;\n        uniform float mirror;\n        uniform float mixStrength;\n        uniform float minDepthThreshold;\n        uniform float maxDepthThreshold;\n        uniform float mixContrast;\n        uniform float depthScale;\n        uniform float depthToBlurRatioBias;\n        varying vec4 my_vUv;\n        ${shader.fragmentShader}`;\n    shader.fragmentShader = shader.fragmentShader.replace('#include <emissivemap_fragment>', `#include <emissivemap_fragment>\n\n      float distortionFactor = 0.0;\n      #ifdef USE_DISTORTION\n        distortionFactor = texture2D(distortionMap, vUv).r * distortion;\n      #endif\n\n      vec4 new_vUv = my_vUv;\n      new_vUv.x += distortionFactor;\n      new_vUv.y += distortionFactor;\n\n      vec4 base = texture2DProj(tDiffuse, new_vUv);\n      vec4 blur = texture2DProj(tDiffuseBlur, new_vUv);\n\n      vec4 merge = base;\n\n      #ifdef USE_NORMALMAP\n        vec2 normal_uv = vec2(0.0);\n        vec4 normalColor = texture2D(normalMap, vUv * normalScale);\n        vec3 my_normal = normalize( vec3( normalColor.r * 2.0 - 1.0, normalColor.b,  normalColor.g * 2.0 - 1.0 ) );\n        vec3 coord = new_vUv.xyz / new_vUv.w;\n        normal_uv = coord.xy + coord.z * my_normal.xz * 0.05;\n        vec4 base_normal = texture2D(tDiffuse, normal_uv);\n        vec4 blur_normal = texture2D(tDiffuseBlur, normal_uv);\n        merge = base_normal;\n        blur = blur_normal;\n      #endif\n\n      float depthFactor = 0.0001;\n      float blurFactor = 0.0;\n\n      #ifdef USE_DEPTH\n        vec4 depth = texture2DProj(tDepth, new_vUv);\n        depthFactor = smoothstep(minDepthThreshold, maxDepthThreshold, 1.0-(depth.r * depth.a));\n        depthFactor *= depthScale;\n        depthFactor = max(0.0001, min(1.0, depthFactor));\n\n        #ifdef USE_BLUR\n          blur = blur * min(1.0, depthFactor + depthToBlurRatioBias);\n          merge = merge * min(1.0, depthFactor + 0.5);\n        #else\n          merge = merge * depthFactor;\n        #endif\n\n      #endif\n\n      float reflectorRoughnessFactor = roughness;\n      #ifdef USE_ROUGHNESSMAP\n        vec4 reflectorTexelRoughness = texture2D( roughnessMap, vUv );\n        reflectorRoughnessFactor *= reflectorTexelRoughness.g;\n      #endif\n\n      #ifdef USE_BLUR\n        blurFactor = min(1.0, mixBlur * reflectorRoughnessFactor);\n        merge = mix(merge, blur, blurFactor);\n      #endif\n\n      vec4 newMerge = vec4(0.0, 0.0, 0.0, 1.0);\n      newMerge.r = (merge.r - 0.5) * mixContrast + 0.5;\n      newMerge.g = (merge.g - 0.5) * mixContrast + 0.5;\n      newMerge.b = (merge.b - 0.5) * mixContrast + 0.5;\n\n      diffuseColor.rgb = diffuseColor.rgb * ((1.0 - min(1.0, mirror)) + newMerge.rgb * mixStrength);\n      `);\n  }\n  get tDiffuse() {\n    return this._tDiffuse.value;\n  }\n  set tDiffuse(v) {\n    this._tDiffuse.value = v;\n  }\n  get tDepth() {\n    return this._tDepth.value;\n  }\n  set tDepth(v) {\n    this._tDepth.value = v;\n  }\n  get distortionMap() {\n    return this._distortionMap.value;\n  }\n  set distortionMap(v) {\n    this._distortionMap.value = v;\n  }\n  get tDiffuseBlur() {\n    return this._tDiffuseBlur.value;\n  }\n  set tDiffuseBlur(v) {\n    this._tDiffuseBlur.value = v;\n  }\n  get textureMatrix() {\n    return this._textureMatrix.value;\n  }\n  set textureMatrix(v) {\n    this._textureMatrix.value = v;\n  }\n  get hasBlur() {\n    return this._hasBlur.value;\n  }\n  set hasBlur(v) {\n    this._hasBlur.value = v;\n  }\n  get mirror() {\n    return this._mirror.value;\n  }\n  set mirror(v) {\n    this._mirror.value = v;\n  }\n  get mixBlur() {\n    return this._mixBlur.value;\n  }\n  set mixBlur(v) {\n    this._mixBlur.value = v;\n  }\n  get mixStrength() {\n    return this._blurStrength.value;\n  }\n  set mixStrength(v) {\n    this._blurStrength.value = v;\n  }\n  get minDepthThreshold() {\n    return this._minDepthThreshold.value;\n  }\n  set minDepthThreshold(v) {\n    this._minDepthThreshold.value = v;\n  }\n  get maxDepthThreshold() {\n    return this._maxDepthThreshold.value;\n  }\n  set maxDepthThreshold(v) {\n    this._maxDepthThreshold.value = v;\n  }\n  get depthScale() {\n    return this._depthScale.value;\n  }\n  set depthScale(v) {\n    this._depthScale.value = v;\n  }\n  get depthToBlurRatioBias() {\n    return this._depthToBlurRatioBias.value;\n  }\n  set depthToBlurRatioBias(v) {\n    this._depthToBlurRatioBias.value = v;\n  }\n  get distortion() {\n    return this._distortion.value;\n  }\n  set distortion(v) {\n    this._distortion.value = v;\n  }\n  get mixContrast() {\n    return this._mixContrast.value;\n  }\n  set mixContrast(v) {\n    this._mixContrast.value = v;\n  }\n}\n\nexport { MeshReflectorMaterial };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,8BAA8B,mMAAA,CAAA,uBAAoB;IACtD,YAAY,aAAa,CAAC,CAAC,CAAE;QAC3B,KAAK,CAAC;QACN,IAAI,CAAC,OAAO,GAAG;YACb,OAAO;QACT;QACA,IAAI,CAAC,cAAc,GAAG;YACpB,OAAO;QACT;QACA,IAAI,CAAC,SAAS,GAAG;YACf,OAAO;QACT;QACA,IAAI,CAAC,aAAa,GAAG;YACnB,OAAO;QACT;QACA,IAAI,CAAC,cAAc,GAAG;YACpB,OAAO;QACT;QACA,IAAI,CAAC,QAAQ,GAAG;YACd,OAAO;QACT;QACA,IAAI,CAAC,OAAO,GAAG;YACb,OAAO;QACT;QACA,IAAI,CAAC,QAAQ,GAAG;YACd,OAAO;QACT;QACA,IAAI,CAAC,aAAa,GAAG;YACnB,OAAO;QACT;QACA,IAAI,CAAC,kBAAkB,GAAG;YACxB,OAAO;QACT;QACA,IAAI,CAAC,kBAAkB,GAAG;YACxB,OAAO;QACT;QACA,IAAI,CAAC,WAAW,GAAG;YACjB,OAAO;QACT;QACA,IAAI,CAAC,qBAAqB,GAAG;YAC3B,OAAO;QACT;QACA,IAAI,CAAC,WAAW,GAAG;YACjB,OAAO;QACT;QACA,IAAI,CAAC,YAAY,GAAG;YAClB,OAAO;QACT;QACA,IAAI,CAAC,SAAS,CAAC;IACjB;IACA,gBAAgB,MAAM,EAAE;QACtB,IAAI;QACJ,IAAI,CAAC,CAAC,CAAC,kBAAkB,OAAO,OAAO,KAAK,QAAQ,gBAAgB,MAAM,GAAG;YAC3E,OAAO,OAAO,CAAC,MAAM,GAAG;QAC1B;QACA,OAAO,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ;QACvC,OAAO,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS;QACzC,OAAO,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO;QACrC,OAAO,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc;QACnD,OAAO,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa;QACjD,OAAO,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc;QACnD,OAAO,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO;QACrC,OAAO,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ;QACvC,OAAO,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,aAAa;QAChD,OAAO,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC,kBAAkB;QAC3D,OAAO,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC,kBAAkB;QAC3D,OAAO,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW;QAC7C,OAAO,QAAQ,CAAC,oBAAoB,GAAG,IAAI,CAAC,qBAAqB;QACjE,OAAO,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW;QAC7C,OAAO,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY;QAC/C,OAAO,YAAY,GAAG,CAAC;;;MAGrB,EAAE,OAAO,YAAY,EAAE;QACzB,OAAO,YAAY,GAAG,OAAO,YAAY,CAAC,OAAO,CAAC,6BAA6B,CAAC;;iFAEH,CAAC;QAC9E,OAAO,cAAc,GAAG,CAAC;;;;;;;;;;;;;;;;;;QAkBrB,EAAE,OAAO,cAAc,EAAE;QAC7B,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,OAAO,CAAC,mCAAmC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA+DxF,CAAC;IACL;IACA,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK;IAC7B;IACA,IAAI,SAAS,CAAC,EAAE;QACd,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG;IACzB;IACA,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK;IAC3B;IACA,IAAI,OAAO,CAAC,EAAE;QACZ,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG;IACvB;IACA,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK;IAClC;IACA,IAAI,cAAc,CAAC,EAAE;QACnB,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG;IAC9B;IACA,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK;IACjC;IACA,IAAI,aAAa,CAAC,EAAE;QAClB,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG;IAC7B;IACA,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK;IAClC;IACA,IAAI,cAAc,CAAC,EAAE;QACnB,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG;IAC9B;IACA,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK;IAC5B;IACA,IAAI,QAAQ,CAAC,EAAE;QACb,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG;IACxB;IACA,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK;IAC3B;IACA,IAAI,OAAO,CAAC,EAAE;QACZ,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG;IACvB;IACA,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK;IAC5B;IACA,IAAI,QAAQ,CAAC,EAAE;QACb,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG;IACxB;IACA,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK;IACjC;IACA,IAAI,YAAY,CAAC,EAAE;QACjB,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG;IAC7B;IACA,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK;IACtC;IACA,IAAI,kBAAkB,CAAC,EAAE;QACvB,IAAI,CAAC,kBAAkB,CAAC,KAAK,GAAG;IAClC;IACA,IAAI,oBAAoB;QACtB,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK;IACtC;IACA,IAAI,kBAAkB,CAAC,EAAE;QACvB,IAAI,CAAC,kBAAkB,CAAC,KAAK,GAAG;IAClC;IACA,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK;IAC/B;IACA,IAAI,WAAW,CAAC,EAAE;QAChB,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG;IAC3B;IACA,IAAI,uBAAuB;QACzB,OAAO,IAAI,CAAC,qBAAqB,CAAC,KAAK;IACzC;IACA,IAAI,qBAAqB,CAAC,EAAE;QAC1B,IAAI,CAAC,qBAAqB,CAAC,KAAK,GAAG;IACrC;IACA,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK;IAC/B;IACA,IAAI,WAAW,CAAC,EAAE;QAChB,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG;IAC3B;IACA,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK;IAChC;IACA,IAAI,YAAY,CAAC,EAAE;QACjB,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 720, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 726, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/materials/MeshRefractionMaterial.js"], "sourcesContent": ["import * as THREE from 'three';\nimport { shaderMaterial } from '../core/shaderMaterial.js';\nimport { shaderStructs, shaderIntersectFunction, MeshBVHUniformStruct } from 'three-mesh-bvh';\nimport { version } from '../helpers/constants.js';\n\n// Author: N8Programs\nconst MeshRefractionMaterial = /* @__PURE__ */shaderMaterial({\n  envMap: null,\n  bounces: 3,\n  ior: 2.4,\n  correctMips: true,\n  aberrationStrength: 0.01,\n  fresnel: 0,\n  bvh: /* @__PURE__ */new MeshBVHUniformStruct(),\n  color: /* @__PURE__ */new THREE.Color('white'),\n  opacity: 1,\n  resolution: /* @__PURE__ */new THREE.Vector2(),\n  viewMatrixInverse: /* @__PURE__ */new THREE.Matrix4(),\n  projectionMatrixInverse: /* @__PURE__ */new THREE.Matrix4()\n}, /*glsl*/`\n  uniform mat4 viewMatrixInverse;\n\n  varying vec3 vWorldPosition;\n  varying vec3 vNormal;\n  varying mat4 vModelMatrixInverse;\n\n  #include <color_pars_vertex>\n\n  void main() {\n    #include <color_vertex>\n\n    vec4 transformedNormal = vec4(normal, 0.0);\n    vec4 transformedPosition = vec4(position, 1.0);\n    #ifdef USE_INSTANCING\n      transformedNormal = instanceMatrix * transformedNormal;\n      transformedPosition = instanceMatrix * transformedPosition;\n    #endif\n\n    #ifdef USE_INSTANCING\n      vModelMatrixInverse = inverse(modelMatrix * instanceMatrix);\n    #else\n      vModelMatrixInverse = inverse(modelMatrix);\n    #endif\n\n    vWorldPosition = (modelMatrix * transformedPosition).xyz;\n    vNormal = normalize((viewMatrixInverse * vec4(normalMatrix * transformedNormal.xyz, 0.0)).xyz);\n    gl_Position = projectionMatrix * viewMatrix * modelMatrix * transformedPosition;\n  }`, /*glsl*/`\n  #define ENVMAP_TYPE_CUBE_UV\n  precision highp isampler2D;\n  precision highp usampler2D;\n  varying vec3 vWorldPosition;\n  varying vec3 vNormal;\n  varying mat4 vModelMatrixInverse;\n\n  #include <color_pars_fragment>\n\n  #ifdef ENVMAP_TYPE_CUBEM\n    uniform samplerCube envMap;\n  #else\n    uniform sampler2D envMap;\n  #endif\n\n  uniform float bounces;\n  ${shaderStructs}\n  ${shaderIntersectFunction}\n  uniform BVH bvh;\n  uniform float ior;\n  uniform bool correctMips;\n  uniform vec2 resolution;\n  uniform float fresnel;\n  uniform mat4 modelMatrix;\n  uniform mat4 projectionMatrixInverse;\n  uniform mat4 viewMatrixInverse;\n  uniform float aberrationStrength;\n  uniform vec3 color;\n  uniform float opacity;\n\n  float fresnelFunc(vec3 viewDirection, vec3 worldNormal) {\n    return pow( 1.0 + dot( viewDirection, worldNormal), 10.0 );\n  }\n\n  vec3 totalInternalReflection(vec3 ro, vec3 rd, vec3 normal, float ior, mat4 modelMatrixInverse) {\n    vec3 rayOrigin = ro;\n    vec3 rayDirection = rd;\n    rayDirection = refract(rayDirection, normal, 1.0 / ior);\n    rayOrigin = vWorldPosition + rayDirection * 0.001;\n    rayOrigin = (modelMatrixInverse * vec4(rayOrigin, 1.0)).xyz;\n    rayDirection = normalize((modelMatrixInverse * vec4(rayDirection, 0.0)).xyz);\n    for(float i = 0.0; i < bounces; i++) {\n      uvec4 faceIndices = uvec4( 0u );\n      vec3 faceNormal = vec3( 0.0, 0.0, 1.0 );\n      vec3 barycoord = vec3( 0.0 );\n      float side = 1.0;\n      float dist = 0.0;\n      bvhIntersectFirstHit( bvh, rayOrigin, rayDirection, faceIndices, faceNormal, barycoord, side, dist );\n      vec3 hitPos = rayOrigin + rayDirection * max(dist - 0.001, 0.0);\n      vec3 tempDir = refract(rayDirection, faceNormal, ior);\n      if (length(tempDir) != 0.0) {\n        rayDirection = tempDir;\n        break;\n      }\n      rayDirection = reflect(rayDirection, faceNormal);\n      rayOrigin = hitPos + rayDirection * 0.01;\n    }\n    rayDirection = normalize((modelMatrix * vec4(rayDirection, 0.0)).xyz);\n    return rayDirection;\n  }\n\n  #include <common>\n  #include <cube_uv_reflection_fragment>\n\n  #ifdef ENVMAP_TYPE_CUBEM\n    vec4 textureGradient(samplerCube envMap, vec3 rayDirection, vec3 directionCamPerfect) {\n      return textureGrad(envMap, rayDirection, dFdx(correctMips ? directionCamPerfect: rayDirection), dFdy(correctMips ? directionCamPerfect: rayDirection));\n    }\n  #else\n    vec4 textureGradient(sampler2D envMap, vec3 rayDirection, vec3 directionCamPerfect) {\n      vec2 uvv = equirectUv( rayDirection );\n      vec2 smoothUv = equirectUv( directionCamPerfect );\n      return textureGrad(envMap, uvv, dFdx(correctMips ? smoothUv : uvv), dFdy(correctMips ? smoothUv : uvv));\n    }\n  #endif\n\n  void main() {\n    vec2 uv = gl_FragCoord.xy / resolution;\n    vec3 directionCamPerfect = (projectionMatrixInverse * vec4(uv * 2.0 - 1.0, 0.0, 1.0)).xyz;\n    directionCamPerfect = (viewMatrixInverse * vec4(directionCamPerfect, 0.0)).xyz;\n    directionCamPerfect = normalize(directionCamPerfect);\n    vec3 normal = vNormal;\n    vec3 rayOrigin = cameraPosition;\n    vec3 rayDirection = normalize(vWorldPosition - cameraPosition);\n\n    vec4 diffuseColor = vec4(color, opacity);\n    #include <color_fragment>\n\n    #ifdef CHROMATIC_ABERRATIONS\n      vec3 rayDirectionG = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior, 1.0), vModelMatrixInverse);\n      #ifdef FAST_CHROMA\n        vec3 rayDirectionR = normalize(rayDirectionG + 1.0 * vec3(aberrationStrength / 2.0));\n        vec3 rayDirectionB = normalize(rayDirectionG - 1.0 * vec3(aberrationStrength / 2.0));\n      #else\n        vec3 rayDirectionR = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior * (1.0 - aberrationStrength), 1.0), vModelMatrixInverse);\n        vec3 rayDirectionB = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior * (1.0 + aberrationStrength), 1.0), vModelMatrixInverse);\n      #endif\n      float finalColorR = textureGradient(envMap, rayDirectionR, directionCamPerfect).r;\n      float finalColorG = textureGradient(envMap, rayDirectionG, directionCamPerfect).g;\n      float finalColorB = textureGradient(envMap, rayDirectionB, directionCamPerfect).b;\n      diffuseColor.rgb *= vec3(finalColorR, finalColorG, finalColorB);\n    #else\n      rayDirection = totalInternalReflection(rayOrigin, rayDirection, normal, max(ior, 1.0), vModelMatrixInverse);\n      diffuseColor.rgb *= textureGradient(envMap, rayDirection, directionCamPerfect).rgb;\n    #endif\n\n    vec3 viewDirection = normalize(vWorldPosition - cameraPosition);\n    float nFresnel = fresnelFunc(viewDirection, normal) * fresnel;\n    gl_FragColor = vec4(mix(diffuseColor.rgb, vec3(1.0), nFresnel), diffuseColor.a);\n\n    #include <tonemapping_fragment>\n    #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n  }`);\n\nexport { MeshRefractionMaterial };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;AACA;;;;;AAEA,qBAAqB;AACrB,MAAM,yBAAyB,aAAa,GAAE,CAAA,GAAA,qXAAA,CAAA,iBAAc,AAAD,EAAE;IAC3D,QAAQ;IACR,SAAS;IACT,KAAK;IACL,aAAa;IACb,oBAAoB;IACpB,SAAS;IACT,KAAK,aAAa,GAAE,IAAI,kQAAA,CAAA,uBAAoB;IAC5C,OAAO,aAAa,GAAE,IAAI,mMAAA,CAAA,QAAW,CAAC;IACtC,SAAS;IACT,YAAY,aAAa,GAAE,IAAI,mMAAA,CAAA,UAAa;IAC5C,mBAAmB,aAAa,GAAE,IAAI,mMAAA,CAAA,UAAa;IACnD,yBAAyB,aAAa,GAAE,IAAI,mMAAA,CAAA,UAAa;AAC3D,GAAG,MAAM,GAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BT,CAAC,EAAE,MAAM,GAAE,CAAC;;;;;;;;;;;;;;;;;EAiBb,EAAE,4PAAA,CAAA,gBAAa,CAAC;EAChB,EAAE,4PAAA,CAAA,0BAAuB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;cA8Fd,EAAE,mXAAA,CAAA,UAAO,IAAI,MAAM,wBAAwB,qBAAqB;GAC3E,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 895, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 901, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/materials/DiscardMaterial.js"], "sourcesContent": ["import { shaderMaterial } from '../core/shaderMaterial.js';\n\nconst DiscardMaterial = /* @__PURE__ */shaderMaterial({}, 'void main() { }', 'void main() { gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0); discard;  }');\n\nexport { DiscardMaterial };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB,aAAa,GAAE,CAAA,GAAA,qXAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,GAAG,mBAAmB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 908, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 914, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/materials/SpotLightMaterial.js"], "sourcesContent": ["import * as THREE from 'three';\nimport { version } from '../helpers/constants.js';\n\nclass SpotLightMaterial extends THREE.ShaderMaterial {\n  constructor() {\n    super({\n      uniforms: {\n        depth: {\n          value: null\n        },\n        opacity: {\n          value: 1\n        },\n        attenuation: {\n          value: 2.5\n        },\n        anglePower: {\n          value: 12\n        },\n        spotPosition: {\n          value: new THREE.Vector3(0, 0, 0)\n        },\n        lightColor: {\n          value: new THREE.Color('white')\n        },\n        cameraNear: {\n          value: 0\n        },\n        cameraFar: {\n          value: 1\n        },\n        resolution: {\n          value: new THREE.Vector2(0, 0)\n        }\n      },\n      transparent: true,\n      depthWrite: false,\n      vertexShader: /* glsl */`\n        varying vec3 vNormal;\n        varying float vViewZ;\n        varying float vIntensity;\n        uniform vec3 spotPosition;\n        uniform float attenuation;\n\n        #include <common>\n        #include <logdepthbuf_pars_vertex>\n\n        void main() {\n          // compute intensity\n          vNormal = normalize(normalMatrix * normal);\n          vec4 worldPosition = modelMatrix * vec4(position, 1);\n          vec4 viewPosition = viewMatrix * worldPosition;\n          vViewZ = viewPosition.z;\n\n          vIntensity = 1.0 - saturate(distance(worldPosition.xyz, spotPosition) / attenuation);\n\n          gl_Position = projectionMatrix * viewPosition;\n\n          #include <logdepthbuf_vertex>\n        }\n      `,\n      fragmentShader: /* glsl */`\n        varying vec3 vNormal;\n        varying float vViewZ;\n        varying float vIntensity;\n\n        uniform vec3 lightColor;\n        uniform float anglePower;\n        uniform sampler2D depth;\n        uniform vec2 resolution;\n        uniform float cameraNear;\n        uniform float cameraFar;\n        uniform float opacity;\n\n        #include <packing>\n        #include <logdepthbuf_pars_fragment>\n\n        float readDepth(sampler2D depthSampler, vec2 uv) {\n          float fragCoordZ = texture(depthSampler, uv).r;\n\n          // https://github.com/mrdoob/three.js/issues/23072\n          #ifdef USE_LOGDEPTHBUF\n            float viewZ = 1.0 - exp2(fragCoordZ * log(cameraFar + 1.0) / log(2.0));\n          #else\n            float viewZ = perspectiveDepthToViewZ(fragCoordZ, cameraNear, cameraFar);\n          #endif\n\n          return viewZ;\n        }\n\n        void main() {\n          #include <logdepthbuf_fragment>\n\n          vec3 normal = vec3(vNormal.x, vNormal.y, abs(vNormal.z));\n          float angleIntensity = pow(dot(normal, vec3(0, 0, 1)), anglePower);\n          float intensity = vIntensity * angleIntensity;\n\n          // fades when z is close to sampled depth, meaning the cone is intersecting existing geometry\n          bool isSoft = resolution[0] > 0.0 && resolution[1] > 0.0;\n          if (isSoft) {\n            vec2 uv = gl_FragCoord.xy / resolution;\n            intensity *= smoothstep(0.0, 1.0, vViewZ - readDepth(depth, uv));\n          }\n\n          gl_FragColor = vec4(lightColor, intensity * opacity);\n\n          #include <tonemapping_fragment>\n          #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n        }\n      `\n    });\n  }\n}\n\nexport { SpotLightMaterial };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,0BAA0B,mMAAA,CAAA,iBAAoB;IAClD,aAAc;QACZ,KAAK,CAAC;YACJ,UAAU;gBACR,OAAO;oBACL,OAAO;gBACT;gBACA,SAAS;oBACP,OAAO;gBACT;gBACA,aAAa;oBACX,OAAO;gBACT;gBACA,YAAY;oBACV,OAAO;gBACT;gBACA,cAAc;oBACZ,OAAO,IAAI,mMAAA,CAAA,UAAa,CAAC,GAAG,GAAG;gBACjC;gBACA,YAAY;oBACV,OAAO,IAAI,mMAAA,CAAA,QAAW,CAAC;gBACzB;gBACA,YAAY;oBACV,OAAO;gBACT;gBACA,WAAW;oBACT,OAAO;gBACT;gBACA,YAAY;oBACV,OAAO,IAAI,mMAAA,CAAA,UAAa,CAAC,GAAG;gBAC9B;YACF;YACA,aAAa;YACb,YAAY;YACZ,cAAc,QAAQ,GAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;MAuBzB,CAAC;YACD,gBAAgB,QAAQ,GAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBA8Cb,EAAE,mXAAA,CAAA,UAAO,IAAI,MAAM,wBAAwB,qBAAqB;;MAE9E,CAAC;QACH;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1032, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1038, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/materials/WireframeMaterial.js"], "sourcesContent": ["import * as THREE from 'three';\nimport * as React from 'react';\nimport { shaderMaterial } from '../core/shaderMaterial.js';\n\nconst WireframeMaterialShaders = {\n  uniforms: {\n    strokeOpacity: 1,\n    fillOpacity: 0.25,\n    fillMix: 0,\n    thickness: 0.05,\n    colorBackfaces: false,\n    dashInvert: true,\n    dash: false,\n    dashRepeats: 4,\n    dashLength: 0.5,\n    squeeze: false,\n    squeezeMin: 0.2,\n    squeezeMax: 1,\n    stroke: /* @__PURE__ */new THREE.Color('#ff0000'),\n    backfaceStroke: /* @__PURE__ */new THREE.Color('#0000ff'),\n    fill: /* @__PURE__ */new THREE.Color('#00ff00')\n  },\n  vertex: /* glsl */`\n\t  attribute vec3 barycentric;\n\t\n\t\tvarying vec3 v_edges_Barycentric;\n\t\tvarying vec3 v_edges_Position;\n\n\t\tvoid initWireframe() {\n\t\t\tv_edges_Barycentric = barycentric;\n\t\t\tv_edges_Position = position.xyz;\n\t\t}\n\t  `,\n  fragment: /* glsl */`\n\t\t#ifndef PI\n\t  \t#define PI 3.1415926535897932384626433832795\n\t\t#endif\n  \n\t  varying vec3 v_edges_Barycentric;\n\t  varying vec3 v_edges_Position;\n  \n\t  uniform float strokeOpacity;\n\t  uniform float fillOpacity;\n\t  uniform float fillMix;\n\t  uniform float thickness;\n\t  uniform bool colorBackfaces;\n  \n\t  // Dash\n\t  uniform bool dashInvert;\n\t  uniform bool dash;\n\t  uniform bool dashOnly;\n\t  uniform float dashRepeats;\n\t  uniform float dashLength;\n  \n\t  // Squeeze\n\t  uniform bool squeeze;\n\t  uniform float squeezeMin;\n\t  uniform float squeezeMax;\n  \n\t  // Colors\n\t  uniform vec3 stroke;\n\t  uniform vec3 backfaceStroke;\n\t  uniform vec3 fill;\n  \n\t  // This is like\n\t  float wireframe_aastep(float threshold, float dist) {\n\t\t  float afwidth = fwidth(dist) * 0.5;\n\t\t  return smoothstep(threshold - afwidth, threshold + afwidth, dist);\n\t  }\n  \n\t  float wireframe_map(float value, float min1, float max1, float min2, float max2) {\n\t\t  return min2 + (value - min1) * (max2 - min2) / (max1 - min1);\n\t  }\n  \n\t  float getWireframe() {\n\t\t\tvec3 barycentric = v_edges_Barycentric;\n\t\t\n\t\t\t// Distance from center of each triangle to its edges.\n\t\t\tfloat d = min(min(barycentric.x, barycentric.y), barycentric.z);\n\n\t\t\t// for dashed rendering, we can use this to get the 0 .. 1 value of the line length\n\t\t\tfloat positionAlong = max(barycentric.x, barycentric.y);\n\t\t\tif (barycentric.y < barycentric.x && barycentric.y < barycentric.z) {\n\t\t\t\tpositionAlong = 1.0 - positionAlong;\n\t\t\t}\n\n\t\t\t// the thickness of the stroke\n\t\t\tfloat computedThickness = wireframe_map(thickness, 0.0, 1.0, 0.0, 0.34);\n\n\t\t\t// if we want to shrink the thickness toward the center of the line segment\n\t\t\tif (squeeze) {\n\t\t\t\tcomputedThickness *= mix(squeezeMin, squeezeMax, (1.0 - sin(positionAlong * PI)));\n\t\t\t}\n\n\t\t\t// Create dash pattern\n\t\t\tif (dash) {\n\t\t\t\t// here we offset the stroke position depending on whether it\n\t\t\t\t// should overlap or not\n\t\t\t\tfloat offset = 1.0 / dashRepeats * dashLength / 2.0;\n\t\t\t\tif (!dashInvert) {\n\t\t\t\t\toffset += 1.0 / dashRepeats / 2.0;\n\t\t\t\t}\n\n\t\t\t\t// if we should animate the dash or not\n\t\t\t\t// if (dashAnimate) {\n\t\t\t\t// \toffset += time * 0.22;\n\t\t\t\t// }\n\n\t\t\t\t// create the repeating dash pattern\n\t\t\t\tfloat pattern = fract((positionAlong + offset) * dashRepeats);\n\t\t\t\tcomputedThickness *= 1.0 - wireframe_aastep(dashLength, pattern);\n\t\t\t}\n\n\t\t\t// compute the anti-aliased stroke edge  \n\t\t\tfloat edge = 1.0 - wireframe_aastep(computedThickness, d);\n\n\t\t\treturn edge;\n\t  }\n\t  `\n};\nconst WireframeMaterial = /* @__PURE__ */shaderMaterial(WireframeMaterialShaders.uniforms, WireframeMaterialShaders.vertex + /* glsl */`\n  \tvoid main() {\n\t\tinitWireframe();\n\t\tgl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n\t}\n  `, WireframeMaterialShaders.fragment + /* glsl */`\n  void main () {\n\t\t// Compute color\n\n\t\tfloat edge = getWireframe();\n\t\tvec4 colorStroke = vec4(stroke, edge);\n\n\t\t#ifdef FLIP_SIDED\n\t\t\tcolorStroke.rgb = backfaceStroke;\n\t\t#endif\n    \n\t\tvec4 colorFill = vec4(fill, fillOpacity);\n\t\tvec4 outColor = mix(colorFill, colorStroke, edge * strokeOpacity);\n\n\t\tgl_FragColor = outColor;\n\t}\n  `);\nfunction setWireframeOverride(material, uniforms) {\n  material.onBeforeCompile = shader => {\n    shader.uniforms = {\n      ...shader.uniforms,\n      ...uniforms\n    };\n    shader.vertexShader = shader.vertexShader.replace('void main() {', `\n\t\t  ${WireframeMaterialShaders.vertex}\n\t\t  void main() {\n\t\t\tinitWireframe();\n\t\t`);\n    shader.fragmentShader = shader.fragmentShader.replace('void main() {', `\n\t\t  ${WireframeMaterialShaders.fragment}\n\t\t  void main() {\n\t\t`);\n    shader.fragmentShader = shader.fragmentShader.replace('#include <color_fragment>', /* glsl */`\n\t\t  #include <color_fragment>\n\t\t\t  float edge = getWireframe();\n\t\t  vec4 colorStroke = vec4(stroke, edge);\n\t\t  #ifdef FLIP_SIDED\n\t\t\tcolorStroke.rgb = backfaceStroke;\n\t\t  #endif\n\t\t  vec4 colorFill = vec4(mix(diffuseColor.rgb, fill, fillMix), mix(diffuseColor.a, fillOpacity, fillMix));\n\t\t  vec4 outColor = mix(colorFill, colorStroke, edge * strokeOpacity);\n\n\t\t  diffuseColor.rgb = outColor.rgb;\n\t\t  diffuseColor.a *= outColor.a;\n\t\t`);\n  };\n  material.side = THREE.DoubleSide;\n  material.transparent = true;\n}\nfunction useWireframeUniforms(uniforms, props) {\n  React.useEffect(() => {\n    var _props$fillOpacity;\n    return void (uniforms.fillOpacity.value = (_props$fillOpacity = props.fillOpacity) !== null && _props$fillOpacity !== void 0 ? _props$fillOpacity : uniforms.fillOpacity.value);\n  }, [props.fillOpacity]);\n  React.useEffect(() => {\n    var _props$fillMix;\n    return void (uniforms.fillMix.value = (_props$fillMix = props.fillMix) !== null && _props$fillMix !== void 0 ? _props$fillMix : uniforms.fillMix.value);\n  }, [props.fillMix]);\n  React.useEffect(() => {\n    var _props$strokeOpacity;\n    return void (uniforms.strokeOpacity.value = (_props$strokeOpacity = props.strokeOpacity) !== null && _props$strokeOpacity !== void 0 ? _props$strokeOpacity : uniforms.strokeOpacity.value);\n  }, [props.strokeOpacity]);\n  React.useEffect(() => {\n    var _props$thickness;\n    return void (uniforms.thickness.value = (_props$thickness = props.thickness) !== null && _props$thickness !== void 0 ? _props$thickness : uniforms.thickness.value);\n  }, [props.thickness]);\n  React.useEffect(() => void (uniforms.colorBackfaces.value = !!props.colorBackfaces), [props.colorBackfaces]);\n  React.useEffect(() => void (uniforms.dash.value = !!props.dash), [props.dash]);\n  React.useEffect(() => void (uniforms.dashInvert.value = !!props.dashInvert), [props.dashInvert]);\n  React.useEffect(() => {\n    var _props$dashRepeats;\n    return void (uniforms.dashRepeats.value = (_props$dashRepeats = props.dashRepeats) !== null && _props$dashRepeats !== void 0 ? _props$dashRepeats : uniforms.dashRepeats.value);\n  }, [props.dashRepeats]);\n  React.useEffect(() => {\n    var _props$dashLength;\n    return void (uniforms.dashLength.value = (_props$dashLength = props.dashLength) !== null && _props$dashLength !== void 0 ? _props$dashLength : uniforms.dashLength.value);\n  }, [props.dashLength]);\n  React.useEffect(() => void (uniforms.squeeze.value = !!props.squeeze), [props.squeeze]);\n  React.useEffect(() => {\n    var _props$squeezeMin;\n    return void (uniforms.squeezeMin.value = (_props$squeezeMin = props.squeezeMin) !== null && _props$squeezeMin !== void 0 ? _props$squeezeMin : uniforms.squeezeMin.value);\n  }, [props.squeezeMin]);\n  React.useEffect(() => {\n    var _props$squeezeMax;\n    return void (uniforms.squeezeMax.value = (_props$squeezeMax = props.squeezeMax) !== null && _props$squeezeMax !== void 0 ? _props$squeezeMax : uniforms.squeezeMax.value);\n  }, [props.squeezeMax]);\n  React.useEffect(() => void (uniforms.stroke.value = props.stroke ? new THREE.Color(props.stroke) : uniforms.stroke.value), [props.stroke]);\n  React.useEffect(() => void (uniforms.fill.value = props.fill ? new THREE.Color(props.fill) : uniforms.fill.value), [props.fill]);\n  React.useEffect(() => void (uniforms.backfaceStroke.value = props.backfaceStroke ? new THREE.Color(props.backfaceStroke) : uniforms.backfaceStroke.value), [props.backfaceStroke]);\n}\n\nexport { WireframeMaterial, WireframeMaterialShaders, setWireframeOverride, useWireframeUniforms };\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,2BAA2B;IAC/B,UAAU;QACR,eAAe;QACf,aAAa;QACb,SAAS;QACT,WAAW;QACX,gBAAgB;QAChB,YAAY;QACZ,MAAM;QACN,aAAa;QACb,YAAY;QACZ,SAAS;QACT,YAAY;QACZ,YAAY;QACZ,QAAQ,aAAa,GAAE,IAAI,mMAAA,CAAA,QAAW,CAAC;QACvC,gBAAgB,aAAa,GAAE,IAAI,mMAAA,CAAA,QAAW,CAAC;QAC/C,MAAM,aAAa,GAAE,IAAI,mMAAA,CAAA,QAAW,CAAC;IACvC;IACA,QAAQ,QAAQ,GAAE,CAAC;;;;;;;;;;GAUlB,CAAC;IACF,UAAU,QAAQ,GAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqFpB,CAAC;AACJ;AACA,MAAM,oBAAoB,aAAa,GAAE,CAAA,GAAA,qXAAA,CAAA,iBAAc,AAAD,EAAE,yBAAyB,QAAQ,EAAE,yBAAyB,MAAM,GAAG,QAAQ,GAAE,CAAC;;;;;EAKtI,CAAC,EAAE,yBAAyB,QAAQ,GAAG,QAAQ,GAAE,CAAC;;;;;;;;;;;;;;;;EAgBlD,CAAC;AACH,SAAS,qBAAqB,QAAQ,EAAE,QAAQ;IAC9C,SAAS,eAAe,GAAG,CAAA;QACzB,OAAO,QAAQ,GAAG;YAChB,GAAG,OAAO,QAAQ;YAClB,GAAG,QAAQ;QACb;QACA,OAAO,YAAY,GAAG,OAAO,YAAY,CAAC,OAAO,CAAC,iBAAiB,CAAC;IACpE,EAAE,yBAAyB,MAAM,CAAC;;;EAGpC,CAAC;QACC,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC;IACxE,EAAE,yBAAyB,QAAQ,CAAC;;EAEtC,CAAC;QACC,OAAO,cAAc,GAAG,OAAO,cAAc,CAAC,OAAO,CAAC,6BAA6B,QAAQ,GAAE,CAAC;;;;;;;;;;;;EAYhG,CAAC;IACD;IACA,SAAS,IAAI,GAAG,mMAAA,CAAA,aAAgB;IAChC,SAAS,WAAW,GAAG;AACzB;AACA,SAAS,qBAAqB,QAAQ,EAAE,KAAK;IAC3C,CAAA,GAAA,oUAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI;QACJ,OAAO,KAAK,CAAC,SAAS,WAAW,CAAC,KAAK,GAAG,CAAC,qBAAqB,MAAM,WAAW,MAAM,QAAQ,uBAAuB,KAAK,IAAI,qBAAqB,SAAS,WAAW,CAAC,KAAK;IAChL,GAAG;QAAC,MAAM,WAAW;KAAC;IACtB,CAAA,GAAA,oUAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI;QACJ,OAAO,KAAK,CAAC,SAAS,OAAO,CAAC,KAAK,GAAG,CAAC,iBAAiB,MAAM,OAAO,MAAM,QAAQ,mBAAmB,KAAK,IAAI,iBAAiB,SAAS,OAAO,CAAC,KAAK;IACxJ,GAAG;QAAC,MAAM,OAAO;KAAC;IAClB,CAAA,GAAA,oUAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI;QACJ,OAAO,KAAK,CAAC,SAAS,aAAa,CAAC,KAAK,GAAG,CAAC,uBAAuB,MAAM,aAAa,MAAM,QAAQ,yBAAyB,KAAK,IAAI,uBAAuB,SAAS,aAAa,CAAC,KAAK;IAC5L,GAAG;QAAC,MAAM,aAAa;KAAC;IACxB,CAAA,GAAA,oUAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI;QACJ,OAAO,KAAK,CAAC,SAAS,SAAS,CAAC,KAAK,GAAG,CAAC,mBAAmB,MAAM,SAAS,MAAM,QAAQ,qBAAqB,KAAK,IAAI,mBAAmB,SAAS,SAAS,CAAC,KAAK;IACpK,GAAG;QAAC,MAAM,SAAS;KAAC;IACpB,CAAA,GAAA,oUAAA,CAAA,YAAe,AAAD,EAAE,IAAM,KAAK,CAAC,SAAS,cAAc,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,cAAc,GAAG;QAAC,MAAM,cAAc;KAAC;IAC3G,CAAA,GAAA,oUAAA,CAAA,YAAe,AAAD,EAAE,IAAM,KAAK,CAAC,SAAS,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,IAAI,GAAG;QAAC,MAAM,IAAI;KAAC;IAC7E,CAAA,GAAA,oUAAA,CAAA,YAAe,AAAD,EAAE,IAAM,KAAK,CAAC,SAAS,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,UAAU,GAAG;QAAC,MAAM,UAAU;KAAC;IAC/F,CAAA,GAAA,oUAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI;QACJ,OAAO,KAAK,CAAC,SAAS,WAAW,CAAC,KAAK,GAAG,CAAC,qBAAqB,MAAM,WAAW,MAAM,QAAQ,uBAAuB,KAAK,IAAI,qBAAqB,SAAS,WAAW,CAAC,KAAK;IAChL,GAAG;QAAC,MAAM,WAAW;KAAC;IACtB,CAAA,GAAA,oUAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI;QACJ,OAAO,KAAK,CAAC,SAAS,UAAU,CAAC,KAAK,GAAG,CAAC,oBAAoB,MAAM,UAAU,MAAM,QAAQ,sBAAsB,KAAK,IAAI,oBAAoB,SAAS,UAAU,CAAC,KAAK;IAC1K,GAAG;QAAC,MAAM,UAAU;KAAC;IACrB,CAAA,GAAA,oUAAA,CAAA,YAAe,AAAD,EAAE,IAAM,KAAK,CAAC,SAAS,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,OAAO,GAAG;QAAC,MAAM,OAAO;KAAC;IACtF,CAAA,GAAA,oUAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI;QACJ,OAAO,KAAK,CAAC,SAAS,UAAU,CAAC,KAAK,GAAG,CAAC,oBAAoB,MAAM,UAAU,MAAM,QAAQ,sBAAsB,KAAK,IAAI,oBAAoB,SAAS,UAAU,CAAC,KAAK;IAC1K,GAAG;QAAC,MAAM,UAAU;KAAC;IACrB,CAAA,GAAA,oUAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI;QACJ,OAAO,KAAK,CAAC,SAAS,UAAU,CAAC,KAAK,GAAG,CAAC,oBAAoB,MAAM,UAAU,MAAM,QAAQ,sBAAsB,KAAK,IAAI,oBAAoB,SAAS,UAAU,CAAC,KAAK;IAC1K,GAAG;QAAC,MAAM,UAAU;KAAC;IACrB,CAAA,GAAA,oUAAA,CAAA,YAAe,AAAD,EAAE,IAAM,KAAK,CAAC,SAAS,MAAM,CAAC,KAAK,GAAG,MAAM,MAAM,GAAG,IAAI,mMAAA,CAAA,QAAW,CAAC,MAAM,MAAM,IAAI,SAAS,MAAM,CAAC,KAAK,GAAG;QAAC,MAAM,MAAM;KAAC;IACzI,CAAA,GAAA,oUAAA,CAAA,YAAe,AAAD,EAAE,IAAM,KAAK,CAAC,SAAS,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI,GAAG,IAAI,mMAAA,CAAA,QAAW,CAAC,MAAM,IAAI,IAAI,SAAS,IAAI,CAAC,KAAK,GAAG;QAAC,MAAM,IAAI;KAAC;IAC/H,CAAA,GAAA,oUAAA,CAAA,YAAe,AAAD,EAAE,IAAM,KAAK,CAAC,SAAS,cAAc,CAAC,KAAK,GAAG,MAAM,cAAc,GAAG,IAAI,mMAAA,CAAA,QAAW,CAAC,MAAM,cAAc,IAAI,SAAS,cAAc,CAAC,KAAK,GAAG;QAAC,MAAM,cAAc;KAAC;AACnL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1292, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}