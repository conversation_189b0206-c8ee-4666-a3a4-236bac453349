{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={`rounded-lg border bg-card text-card-foreground shadow-sm ${className || ''}`}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={`flex flex-col space-y-1.5 p-6 ${className || ''}`} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={`text-2xl font-semibold leading-none tracking-tight ${className || ''}`}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p ref={ref} className={`text-sm text-muted-foreground ${className || ''}`} {...props} />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={`p-6 pt-0 ${className || ''}`} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={`flex items-center p-6 pt-0 ${className || ''}`} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAEA,MAAM,qBAAO,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAC,yDAAyD,EAAE,aAAa,IAAI;QACvF,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAC,8BAA8B,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;AAEzF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAC,mDAAmD,EAAE,aAAa,IAAI;QACjF,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAE,KAAK;QAAK,WAAW,CAAC,8BAA8B,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;AAEvF,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAC,SAAS,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;AAEpE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAC,2BAA2B,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;AAEtF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'secondary' | 'destructive' | 'outline'\n}\n\nconst Badge = React.forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant = 'default', ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\"\n    \n    const variantClasses = {\n      default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n      secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n      destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n      outline: \"text-foreground\",\n    }\n    \n    return (\n      <div\n        ref={ref}\n        className={`${baseClasses} ${variantClasses[variant]} ${className || ''}`}\n        {...props}\n      />\n    )\n  }\n)\nBadge.displayName = \"Badge\"\n\nexport { Badge }\n"], "names": [], "mappings": ";;;;AAAA;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAO,EAAE;IAC7C,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,aAAa;QACb,SAAS;IACX;IAEA,qBACE,6WAAC;QACC,KAAK;QACL,WAAW,GAAG,YAAY,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,aAAa,IAAI;QACxE,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/agent-card.tsx"], "sourcesContent": ["// AI Agent Card Component with Framer Motion animations\n'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState } from 'react';\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\n\ninterface AgentCardProps {\n  name: string;\n  description: string;\n  capabilities: string[];\n  avatarUrl: string;\n  isActive: boolean;\n  onActivate: () => void;\n}\n\nexport default function AgentCard({ \n  name, \n  description, \n  capabilities, \n  avatarUrl, \n  isActive, \n  onActivate \n}: AgentCardProps) {\n  const [isHovered, setIsHovered] = useState(false);\n  \n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n      whileHover={{ scale: 1.03 }}\n      className=\"w-full max-w-sm\"\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n    >\n      <Card className={`overflow-hidden border-2 ${isActive ? 'border-blue-500' : 'border-gray-200'} transition-all duration-300`}>\n        <CardHeader className=\"relative p-0\">\n          <motion.div \n            className=\"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600\"\n            initial={{ opacity: 0.7 }}\n            animate={{ opacity: isHovered ? 0.9 : 0.7 }}\n          />\n          <div className=\"relative p-6 flex items-center gap-4\">\n            <motion.img \n              src={avatarUrl} \n              alt={`${name} avatar`}\n              className=\"w-16 h-16 rounded-full border-2 border-white\"\n              animate={{ \n                rotate: isHovered ? 360 : 0,\n              }}\n              transition={{ duration: 2 }}\n            />\n            <div>\n              <CardTitle className=\"text-white\">{name}</CardTitle>\n              <CardDescription className=\"text-white/80\">AI Agent</CardDescription>\n            </div>\n          </div>\n        </CardHeader>\n        <CardContent className=\"p-6\">\n          <p className=\"text-gray-700 dark:text-gray-300\">{description}</p>\n          <div className=\"mt-4 flex flex-wrap gap-2\">\n            {capabilities.map((capability, index) => (\n              <Badge key={index} variant=\"outline\" className=\"bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300\">\n                {capability}\n              </Badge>\n            ))}\n          </div>\n        </CardContent>\n        <CardFooter className=\"border-t p-6\">\n          <Button \n            onClick={onActivate} \n            className={`w-full ${isActive ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700'}`}\n          >\n            {isActive ? 'Active' : 'Activate Agent'}\n          </Button>\n        </CardFooter>\n      </Card>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": "AAAA,wDAAwD;;;;;AAGxD;AACA;AACA;AACA;AACA;AANA;;;;;;;AAiBe,SAAS,UAAU,EAChC,IAAI,EACJ,WAAW,EACX,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,UAAU,EACK;IACf,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,YAAY;YAAE,OAAO;QAAK;QAC1B,WAAU;QACV,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;kBAEjC,cAAA,6WAAC,yHAAA,CAAA,OAAI;YAAC,WAAW,CAAC,yBAAyB,EAAE,WAAW,oBAAoB,kBAAkB,4BAA4B,CAAC;;8BACzH,6WAAC,yHAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAI;4BACxB,SAAS;gCAAE,SAAS,YAAY,MAAM;4BAAI;;;;;;sCAE5C,6WAAC;4BAAI,WAAU;;8CACb,6WAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;oCACT,KAAK;oCACL,KAAK,GAAG,KAAK,OAAO,CAAC;oCACrB,WAAU;oCACV,SAAS;wCACP,QAAQ,YAAY,MAAM;oCAC5B;oCACA,YAAY;wCAAE,UAAU;oCAAE;;;;;;8CAE5B,6WAAC;;sDACC,6WAAC,yHAAA,CAAA,YAAS;4CAAC,WAAU;sDAAc;;;;;;sDACnC,6WAAC,yHAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;8BAIjD,6WAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6WAAC;4BAAE,WAAU;sCAAoC;;;;;;sCACjD,6WAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,YAAY,sBAC7B,6WAAC,0HAAA,CAAA,QAAK;oCAAa,SAAQ;oCAAU,WAAU;8CAC5C;mCADS;;;;;;;;;;;;;;;;8BAMlB,6WAAC,yHAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,6WAAC,2HAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAW,CAAC,OAAO,EAAE,WAAW,oCAAoC,iCAAiC;kCAEpG,WAAW,WAAW;;;;;;;;;;;;;;;;;;;;;;AAMnC", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport AgentCard from \"@/components/agent-card\";\n\nexport default function Home() {\n  const [activeAgent, setActiveAgent] = useState<string | null>(null);\n\n  const agents = [\n    {\n      id: \"assistant\",\n      name: \"AI Assistant\",\n      description:\n        \"A helpful AI assistant that can answer questions, provide information, and help with various tasks.\",\n      capabilities: [\"Q&A\", \"Research\", \"Writing\", \"Analysis\"],\n      avatarUrl:\n        \"https://images.unsplash.com/photo-1677442136019-21780ecad995?w=100&h=100&fit=crop&crop=face\",\n    },\n    {\n      id: \"coder\",\n      name: \"<PERSON> Expert\",\n      description:\n        \"Specialized in programming, code review, debugging, and software development best practices.\",\n      capabilities: [\"Coding\", \"Debugging\", \"Code Review\", \"Architecture\"],\n      avatarUrl:\n        \"https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=100&h=100&fit=crop&crop=face\",\n    },\n    {\n      id: \"creative\",\n      name: \"Creative Writer\",\n      description:\n        \"Expert in creative writing, storytelling, content creation, and artistic expression.\",\n      capabilities: [\"Writing\", \"Storytelling\", \"Content\", \"Creativity\"],\n      avatarUrl:\n        \"https://images.unsplash.com/photo-1552058544-f2b08422138a?w=100&h=100&fit=crop&crop=face\",\n    },\n  ];\n\n  const handleActivateAgent = (agentId: string) => {\n    setActiveAgent(activeAgent === agentId ? null : agentId);\n  };\n\n  return (\n    <div className='min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8'>\n      <div className='max-w-7xl mx-auto'>\n        <div className='text-center mb-12'>\n          <h1 className='text-4xl font-bold text-gray-900 dark:text-white mb-4'>\n            AI Agent Dashboard\n          </h1>\n          <p className='text-lg text-gray-600 dark:text-gray-300'>\n            Choose an AI agent to assist you with your tasks\n          </p>\n        </div>\n\n        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-center'>\n          {agents.map((agent) => (\n            <AgentCard\n              key={agent.id}\n              name={agent.name}\n              description={agent.description}\n              capabilities={agent.capabilities}\n              avatarUrl={agent.avatarUrl}\n              isActive={activeAgent === agent.id}\n              onActivate={() => handleActivateAgent(agent.id)}\n            />\n          ))}\n        </div>\n\n        {activeAgent && (\n          <div className='mt-12 text-center'>\n            <div className='bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg max-w-md mx-auto'>\n              <h3 className='text-xl font-semibold text-gray-900 dark:text-white mb-2'>\n                Agent Activated!\n              </h3>\n              <p className='text-gray-600 dark:text-gray-300'>\n                {agents.find((a) => a.id === activeAgent)?.name} is now ready to\n                assist you.\n              </p>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,SAAS;QACb;YACE,IAAI;YACJ,MAAM;YACN,aACE;YACF,cAAc;gBAAC;gBAAO;gBAAY;gBAAW;aAAW;YACxD,WACE;QACJ;QACA;YACE,IAAI;YACJ,MAAM;YACN,aACE;YACF,cAAc;gBAAC;gBAAU;gBAAa;gBAAe;aAAe;YACpE,WACE;QACJ;QACA;YACE,IAAI;YACJ,MAAM;YACN,aACE;YACF,cAAc;gBAAC;gBAAW;gBAAgB;gBAAW;aAAa;YAClE,WACE;QACJ;KACD;IAED,MAAM,sBAAsB,CAAC;QAC3B,eAAe,gBAAgB,UAAU,OAAO;IAClD;IAEA,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,6WAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;8BAK1D,6WAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6WAAC,4HAAA,CAAA,UAAS;4BAER,MAAM,MAAM,IAAI;4BAChB,aAAa,MAAM,WAAW;4BAC9B,cAAc,MAAM,YAAY;4BAChC,WAAW,MAAM,SAAS;4BAC1B,UAAU,gBAAgB,MAAM,EAAE;4BAClC,YAAY,IAAM,oBAAoB,MAAM,EAAE;2BANzC,MAAM,EAAE;;;;;;;;;;gBAWlB,6BACC,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6WAAC;gCAAE,WAAU;;oCACV,OAAO,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,cAAc;oCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShE", "debugId": null}}, {"offset": {"line": 445, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}