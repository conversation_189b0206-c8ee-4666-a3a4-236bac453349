{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={`rounded-lg border bg-card text-card-foreground shadow-sm ${className || ''}`}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={`flex flex-col space-y-1.5 p-6 ${className || ''}`} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={`text-2xl font-semibold leading-none tracking-tight ${className || ''}`}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p ref={ref} className={`text-sm text-muted-foreground ${className || ''}`} {...props} />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={`p-6 pt-0 ${className || ''}`} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={`flex items-center p-6 pt-0 ${className || ''}`} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAEA,MAAM,qBAAO,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAC,yDAAyD,EAAE,aAAa,IAAI;QACvF,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAC,8BAA8B,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;AAEzF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAC,mDAAmD,EAAE,aAAa,IAAI;QACjF,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAE,KAAK;QAAK,WAAW,CAAC,8BAA8B,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;AAEvF,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAC,SAAS,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;AAEpE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAC,2BAA2B,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;AAEtF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'secondary' | 'destructive' | 'outline'\n}\n\nconst Badge = React.forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant = 'default', ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\"\n    \n    const variantClasses = {\n      default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n      secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n      destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n      outline: \"text-foreground\",\n    }\n    \n    return (\n      <div\n        ref={ref}\n        className={`${baseClasses} ${variantClasses[variant]} ${className || ''}`}\n        {...props}\n      />\n    )\n  }\n)\nBadge.displayName = \"Badge\"\n\nexport { Badge }\n"], "names": [], "mappings": ";;;;AAAA;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAO,EAAE;IAC7C,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,aAAa;QACb,SAAS;IACX;IAEA,qBACE,6WAAC;QACC,KAAK;QACL,WAAW,GAAG,YAAY,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,aAAa,IAAI;QACxE,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/agent-network-graph.tsx"], "sourcesContent": ["// D3.js Force-Directed Graph for AI Agent Network (Simplified)\n'use client';\n\nimport * as d3 from 'd3';\nimport { useEffect, useRef, useState } from 'react';\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\n\ninterface Node {\n  id: string;\n  name: string;\n  type: 'primary' | 'secondary' | 'hub';\n  capabilities: string[];\n  x?: number;\n  y?: number;\n  fx?: number | null;\n  fy?: number | null;\n}\n\ninterface Link {\n  source: string | Node;\n  target: string | Node;\n  value: number;\n  type: 'collaboration' | 'data-flow' | 'dependency';\n}\n\ninterface NetworkData {\n  nodes: Node[];\n  links: Link[];\n}\n\ninterface AgentNetworkGraphProps {\n  data: NetworkData;\n  width?: number;\n  height?: number;\n  onNodeClick?: (node: Node) => void;\n  onNodeHover?: (node: Node | null) => void;\n}\n\nexport default function AgentNetworkGraph({ \n  data, \n  width = 800, \n  height = 600,\n  onNodeClick,\n  onNodeHover \n}: AgentNetworkGraphProps) {\n  const svgRef = useRef<SVGSVGElement>(null);\n  const [selectedNode, setSelectedNode] = useState<Node | null>(null);\n  const [hoveredNode, setHoveredNode] = useState<Node | null>(null);\n  const [isSimulationRunning, setIsSimulationRunning] = useState(true);\n  \n  useEffect(() => {\n    if (!data || !svgRef.current) return;\n    \n    // Clear previous graph\n    d3.select(svgRef.current).selectAll(\"*\").remove();\n    \n    // Create SVG with responsive viewBox\n    const svg = d3.select(svgRef.current)\n      .attr(\"width\", width)\n      .attr(\"height\", height)\n      .attr(\"viewBox\", [0, 0, width, height])\n      .attr(\"style\", \"max-width: 100%; height: auto; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px;\");\n      \n    // Add zoom behavior\n    const g = svg.append(\"g\");\n    const zoom = d3.zoom<SVGSVGElement, unknown>()\n      .scaleExtent([0.1, 4])\n      .on(\"zoom\", (event) => {\n        g.attr(\"transform\", event.transform);\n      });\n    svg.call(zoom);\n      \n    // Create simulation\n    const simulation = d3.forceSimulation(data.nodes)\n      .force(\"link\", d3.forceLink(data.links).id((d: any) => d.id).distance(120).strength(0.8))\n      .force(\"charge\", d3.forceManyBody().strength(-800).distanceMax(300))\n      .force(\"center\", d3.forceCenter(width / 2, height / 2))\n      .force(\"collision\", d3.forceCollide().radius(60).strength(0.9))\n      .force(\"x\", d3.forceX(width / 2).strength(0.1))\n      .force(\"y\", d3.forceY(height / 2).strength(0.1));\n      \n    // Create arrow markers for directed links\n    const defs = svg.append(\"defs\");\n    \n    const linkTypes = ['collaboration', 'data-flow', 'dependency'];\n    const linkColors = ['#10b981', '#3b82f6', '#f59e0b'];\n    \n    linkTypes.forEach((type, i) => {\n      defs.append(\"marker\")\n        .attr(\"id\", `arrow-${type}`)\n        .attr(\"viewBox\", \"0 -5 10 10\")\n        .attr(\"refX\", 35)\n        .attr(\"refY\", 0)\n        .attr(\"markerWidth\", 6)\n        .attr(\"markerHeight\", 6)\n        .attr(\"orient\", \"auto\")\n        .append(\"path\")\n        .attr(\"d\", \"M0,-5L10,0L0,5\")\n        .attr(\"fill\", linkColors[i]);\n    });\n      \n    // Create links\n    const link = g.append(\"g\")\n      .attr(\"class\", \"links\")\n      .selectAll(\"line\")\n      .data(data.links)\n      .join(\"line\")\n      .attr(\"stroke\", (d: any) => {\n        const colorMap: Record<string, string> = {\n          'collaboration': '#10b981',\n          'data-flow': '#3b82f6',\n          'dependency': '#f59e0b'\n        };\n        return colorMap[d.type] || '#6b7280';\n      })\n      .attr(\"stroke-opacity\", 0.8)\n      .attr(\"stroke-width\", (d: any) => Math.sqrt(d.value) * 2)\n      .attr(\"marker-end\", (d: any) => `url(#arrow-${d.type})`)\n      .style(\"filter\", \"drop-shadow(0 2px 4px rgba(0,0,0,0.1))\");\n      \n    // Create node groups\n    const node = g.append(\"g\")\n      .attr(\"class\", \"nodes\")\n      .selectAll(\"g\")\n      .data(data.nodes)\n      .join(\"g\")\n      .attr(\"class\", \"node\")\n      .style(\"cursor\", \"pointer\");\n      \n    // Add main circles to nodes\n    node.append(\"circle\")\n      .attr(\"r\", (d: any) => d.type === 'hub' ? 30 : d.type === 'primary' ? 25 : 20)\n      .attr(\"fill\", (d: any) => {\n        const colorMap: Record<string, string> = {\n          'primary': '#4f46e5',\n          'secondary': '#10b981',\n          'hub': '#dc2626'\n        };\n        return colorMap[d.type] || '#6b7280';\n      })\n      .attr(\"stroke\", \"#ffffff\")\n      .attr(\"stroke-width\", 3)\n      .style(\"filter\", \"drop-shadow(0 4px 8px rgba(0,0,0,0.2))\")\n      .on(\"mouseover\", function(event, d: any) {\n        d3.select(this)\n          .transition()\n          .duration(200)\n          .attr(\"r\", (d.type === 'hub' ? 35 : d.type === 'primary' ? 30 : 25))\n          .attr(\"stroke-width\", 4);\n        setHoveredNode(d);\n        onNodeHover?.(d);\n      })\n      .on(\"mouseout\", function(event, d: any) {\n        d3.select(this)\n          .transition()\n          .duration(200)\n          .attr(\"r\", (d.type === 'hub' ? 30 : d.type === 'primary' ? 25 : 20))\n          .attr(\"stroke-width\", 3);\n        setHoveredNode(null);\n        onNodeHover?.(null);\n      })\n      .on(\"click\", function(event, d: any) {\n        setSelectedNode(d);\n        onNodeClick?.(d);\n      });\n      \n    // Add icons to nodes\n    node.append(\"text\")\n      .attr(\"text-anchor\", \"middle\")\n      .attr(\"dy\", \".35em\")\n      .attr(\"fill\", \"#ffffff\")\n      .attr(\"font-size\", (d: any) => d.type === 'hub' ? \"16px\" : \"14px\")\n      .attr(\"font-weight\", \"bold\")\n      .style(\"pointer-events\", \"none\")\n      .text((d: any) => {\n        const iconMap: Record<string, string> = {\n          'primary': '🤖',\n          'secondary': '⚡',\n          'hub': '🌐'\n        };\n        return iconMap[d.type] || '●';\n      });\n      \n    // Add labels below nodes\n    node.append(\"text\")\n      .attr(\"text-anchor\", \"middle\")\n      .attr(\"dy\", (d: any) => (d.type === 'hub' ? 45 : d.type === 'primary' ? 40 : 35))\n      .attr(\"fill\", \"#ffffff\")\n      .attr(\"font-size\", \"12px\")\n      .attr(\"font-weight\", \"600\")\n      .style(\"pointer-events\", \"none\")\n      .style(\"text-shadow\", \"0 1px 2px rgba(0,0,0,0.8)\")\n      .text((d: any) => d.name);\n      \n    // Add title for tooltip\n    node.append(\"title\")\n      .text((d: any) => `${d.name}\\nType: ${d.type}\\nCapabilities: ${d.capabilities.join(\", \")}`);\n      \n    // Update positions on tick\n    simulation.on(\"tick\", () => {\n      link\n        .attr(\"x1\", (d: any) => d.source.x)\n        .attr(\"y1\", (d: any) => d.source.y)\n        .attr(\"x2\", (d: any) => d.target.x)\n        .attr(\"y2\", (d: any) => d.target.y);\n        \n      node.attr(\"transform\", (d: any) => `translate(${d.x},${d.y})`);\n    });\n    \n    // Simulation control\n    simulation.on(\"end\", () => {\n      setIsSimulationRunning(false);\n    });\n    \n    // Simple drag behavior\n    const dragHandler = d3.drag<any, any>()\n      .on(\"start\", function(event, d: any) {\n        if (!event.active) simulation.alphaTarget(0.3).restart();\n        d.fx = d.x;\n        d.fy = d.y;\n        setIsSimulationRunning(true);\n      })\n      .on(\"drag\", function(event, d: any) {\n        d.fx = event.x;\n        d.fy = event.y;\n      })\n      .on(\"end\", function(event, d: any) {\n        if (!event.active) simulation.alphaTarget(0);\n        d.fx = null;\n        d.fy = null;\n      });\n    \n    node.call(dragHandler);\n    \n    // Cleanup function\n    return () => {\n      simulation.stop();\n    };\n  }, [data, width, height, onNodeClick, onNodeHover]);\n  \n  const resetGraph = () => {\n    if (!svgRef.current) return;\n    \n    const svg = d3.select(svgRef.current);\n    const nodes = svg.selectAll(\".node circle\");\n    const links = svg.selectAll(\".links line\");\n    \n    // Reset all styles\n    nodes.attr(\"fill-opacity\", 1);\n    links.attr(\"stroke-opacity\", 0.8);\n    setSelectedNode(null);\n  };\n  \n  const centerGraph = () => {\n    if (!svgRef.current) return;\n    \n    const svg = d3.select(svgRef.current);\n    const zoom = d3.zoom<SVGSVGElement, unknown>();\n    \n    svg.transition()\n      .duration(750)\n      .call(zoom.transform, d3.zoomIdentity);\n  };\n  \n  return (\n    <Card className=\"w-full\">\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <CardTitle className=\"text-2xl font-bold\">AI Agent Network</CardTitle>\n          <div className=\"flex gap-2\">\n            <Button variant=\"outline\" size=\"sm\" onClick={resetGraph}>\n              Reset Selection\n            </Button>\n            <Button variant=\"outline\" size=\"sm\" onClick={centerGraph}>\n              Center View\n            </Button>\n          </div>\n        </div>\n        <div className=\"flex gap-2 mt-2\">\n          <Badge variant=\"outline\" className=\"bg-indigo-50 text-indigo-700\">\n            🤖 Primary Agents\n          </Badge>\n          <Badge variant=\"outline\" className=\"bg-green-50 text-green-700\">\n            ⚡ Secondary Agents\n          </Badge>\n          <Badge variant=\"outline\" className=\"bg-red-50 text-red-700\">\n            🌐 Hub Nodes\n          </Badge>\n        </div>\n      </CardHeader>\n      <CardContent>\n        <div className=\"relative\">\n          <svg ref={svgRef} className=\"border rounded-lg shadow-lg\" />\n          \n          {/* Node Details Panel */}\n          {(selectedNode || hoveredNode) && (\n            <div className=\"absolute top-4 right-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg border max-w-xs\">\n              <h3 className=\"font-semibold text-lg mb-2\">\n                {(selectedNode || hoveredNode)?.name}\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-300 mb-2\">\n                Type: <span className=\"capitalize\">{(selectedNode || hoveredNode)?.type}</span>\n              </p>\n              <div className=\"space-y-1\">\n                <p className=\"text-sm font-medium\">Capabilities:</p>\n                <div className=\"flex flex-wrap gap-1\">\n                  {(selectedNode || hoveredNode)?.capabilities.map((cap, index) => (\n                    <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                      {cap}\n                    </Badge>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n          \n          {/* Simulation Status */}\n          <div className=\"absolute bottom-4 left-4\">\n            <Badge variant={isSimulationRunning ? \"default\" : \"secondary\"}>\n              {isSimulationRunning ? \"Simulation Running\" : \"Simulation Stable\"}\n            </Badge>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA,+DAA+D;;;;;AAG/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAuCe,SAAS,kBAAkB,EACxC,IAAI,EACJ,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,WAAW,EACX,WAAW,EACY;IACvB,MAAM,SAAS,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAiB;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ,CAAC,OAAO,OAAO,EAAE;QAE9B,uBAAuB;QACvB,CAAA,GAAA,iPAAA,CAAA,SAAS,AAAD,EAAE,OAAO,OAAO,EAAE,SAAS,CAAC,KAAK,MAAM;QAE/C,qCAAqC;QACrC,MAAM,MAAM,CAAA,GAAA,iPAAA,CAAA,SAAS,AAAD,EAAE,OAAO,OAAO,EACjC,IAAI,CAAC,SAAS,OACd,IAAI,CAAC,UAAU,QACf,IAAI,CAAC,WAAW;YAAC;YAAG;YAAG;YAAO;SAAO,EACrC,IAAI,CAAC,SAAS;QAEjB,oBAAoB;QACpB,MAAM,IAAI,IAAI,MAAM,CAAC;QACrB,MAAM,OAAO,CAAA,GAAA,mOAAA,CAAA,OAAO,AAAD,IAChB,WAAW,CAAC;YAAC;YAAK;SAAE,EACpB,EAAE,CAAC,QAAQ,CAAC;YACX,EAAE,IAAI,CAAC,aAAa,MAAM,SAAS;QACrC;QACF,IAAI,IAAI,CAAC;QAET,oBAAoB;QACpB,MAAM,aAAa,CAAA,GAAA,sPAAA,CAAA,kBAAkB,AAAD,EAAE,KAAK,KAAK,EAC7C,KAAK,CAAC,QAAQ,CAAA,GAAA,0OAAA,CAAA,YAAY,AAAD,EAAE,KAAK,KAAK,EAAE,EAAE,CAAC,CAAC,IAAW,EAAE,EAAE,EAAE,QAAQ,CAAC,KAAK,QAAQ,CAAC,MACnF,KAAK,CAAC,UAAU,CAAA,GAAA,kPAAA,CAAA,gBAAgB,AAAD,IAAI,QAAQ,CAAC,CAAC,KAAK,WAAW,CAAC,MAC9D,KAAK,CAAC,UAAU,CAAA,GAAA,8OAAA,CAAA,cAAc,AAAD,EAAE,QAAQ,GAAG,SAAS,IACnD,KAAK,CAAC,aAAa,CAAA,GAAA,gPAAA,CAAA,eAAe,AAAD,IAAI,MAAM,CAAC,IAAI,QAAQ,CAAC,MACzD,KAAK,CAAC,KAAK,CAAA,GAAA,oOAAA,CAAA,SAAS,AAAD,EAAE,QAAQ,GAAG,QAAQ,CAAC,MACzC,KAAK,CAAC,KAAK,CAAA,GAAA,oOAAA,CAAA,SAAS,AAAD,EAAE,SAAS,GAAG,QAAQ,CAAC;QAE7C,0CAA0C;QAC1C,MAAM,OAAO,IAAI,MAAM,CAAC;QAExB,MAAM,YAAY;YAAC;YAAiB;YAAa;SAAa;QAC9D,MAAM,aAAa;YAAC;YAAW;YAAW;SAAU;QAEpD,UAAU,OAAO,CAAC,CAAC,MAAM;YACvB,KAAK,MAAM,CAAC,UACT,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAC1B,IAAI,CAAC,WAAW,cAChB,IAAI,CAAC,QAAQ,IACb,IAAI,CAAC,QAAQ,GACb,IAAI,CAAC,eAAe,GACpB,IAAI,CAAC,gBAAgB,GACrB,IAAI,CAAC,UAAU,QACf,MAAM,CAAC,QACP,IAAI,CAAC,KAAK,kBACV,IAAI,CAAC,QAAQ,UAAU,CAAC,EAAE;QAC/B;QAEA,eAAe;QACf,MAAM,OAAO,EAAE,MAAM,CAAC,KACnB,IAAI,CAAC,SAAS,SACd,SAAS,CAAC,QACV,IAAI,CAAC,KAAK,KAAK,EACf,IAAI,CAAC,QACL,IAAI,CAAC,UAAU,CAAC;YACf,MAAM,WAAmC;gBACvC,iBAAiB;gBACjB,aAAa;gBACb,cAAc;YAChB;YACA,OAAO,QAAQ,CAAC,EAAE,IAAI,CAAC,IAAI;QAC7B,GACC,IAAI,CAAC,kBAAkB,KACvB,IAAI,CAAC,gBAAgB,CAAC,IAAW,KAAK,IAAI,CAAC,EAAE,KAAK,IAAI,GACtD,IAAI,CAAC,cAAc,CAAC,IAAW,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,EACtD,KAAK,CAAC,UAAU;QAEnB,qBAAqB;QACrB,MAAM,OAAO,EAAE,MAAM,CAAC,KACnB,IAAI,CAAC,SAAS,SACd,SAAS,CAAC,KACV,IAAI,CAAC,KAAK,KAAK,EACf,IAAI,CAAC,KACL,IAAI,CAAC,SAAS,QACd,KAAK,CAAC,UAAU;QAEnB,4BAA4B;QAC5B,KAAK,MAAM,CAAC,UACT,IAAI,CAAC,KAAK,CAAC,IAAW,EAAE,IAAI,KAAK,QAAQ,KAAK,EAAE,IAAI,KAAK,YAAY,KAAK,IAC1E,IAAI,CAAC,QAAQ,CAAC;YACb,MAAM,WAAmC;gBACvC,WAAW;gBACX,aAAa;gBACb,OAAO;YACT;YACA,OAAO,QAAQ,CAAC,EAAE,IAAI,CAAC,IAAI;QAC7B,GACC,IAAI,CAAC,UAAU,WACf,IAAI,CAAC,gBAAgB,GACrB,KAAK,CAAC,UAAU,0CAChB,EAAE,CAAC,aAAa,SAAS,KAAK,EAAE,CAAM;YACrC,CAAA,GAAA,iPAAA,CAAA,SAAS,AAAD,EAAE,IAAI,EACX,UAAU,GACV,QAAQ,CAAC,KACT,IAAI,CAAC,KAAM,EAAE,IAAI,KAAK,QAAQ,KAAK,EAAE,IAAI,KAAK,YAAY,KAAK,IAC/D,IAAI,CAAC,gBAAgB;YACxB,eAAe;YACf,cAAc;QAChB,GACC,EAAE,CAAC,YAAY,SAAS,KAAK,EAAE,CAAM;YACpC,CAAA,GAAA,iPAAA,CAAA,SAAS,AAAD,EAAE,IAAI,EACX,UAAU,GACV,QAAQ,CAAC,KACT,IAAI,CAAC,KAAM,EAAE,IAAI,KAAK,QAAQ,KAAK,EAAE,IAAI,KAAK,YAAY,KAAK,IAC/D,IAAI,CAAC,gBAAgB;YACxB,eAAe;YACf,cAAc;QAChB,GACC,EAAE,CAAC,SAAS,SAAS,KAAK,EAAE,CAAM;YACjC,gBAAgB;YAChB,cAAc;QAChB;QAEF,qBAAqB;QACrB,KAAK,MAAM,CAAC,QACT,IAAI,CAAC,eAAe,UACpB,IAAI,CAAC,MAAM,SACX,IAAI,CAAC,QAAQ,WACb,IAAI,CAAC,aAAa,CAAC,IAAW,EAAE,IAAI,KAAK,QAAQ,SAAS,QAC1D,IAAI,CAAC,eAAe,QACpB,KAAK,CAAC,kBAAkB,QACxB,IAAI,CAAC,CAAC;YACL,MAAM,UAAkC;gBACtC,WAAW;gBACX,aAAa;gBACb,OAAO;YACT;YACA,OAAO,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI;QAC5B;QAEF,yBAAyB;QACzB,KAAK,MAAM,CAAC,QACT,IAAI,CAAC,eAAe,UACpB,IAAI,CAAC,MAAM,CAAC,IAAY,EAAE,IAAI,KAAK,QAAQ,KAAK,EAAE,IAAI,KAAK,YAAY,KAAK,IAC5E,IAAI,CAAC,QAAQ,WACb,IAAI,CAAC,aAAa,QAClB,IAAI,CAAC,eAAe,OACpB,KAAK,CAAC,kBAAkB,QACxB,KAAK,CAAC,eAAe,6BACrB,IAAI,CAAC,CAAC,IAAW,EAAE,IAAI;QAE1B,wBAAwB;QACxB,KAAK,MAAM,CAAC,SACT,IAAI,CAAC,CAAC,IAAW,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,gBAAgB,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,OAAO;QAE5F,2BAA2B;QAC3B,WAAW,EAAE,CAAC,QAAQ;YACpB,KACG,IAAI,CAAC,MAAM,CAAC,IAAW,EAAE,MAAM,CAAC,CAAC,EACjC,IAAI,CAAC,MAAM,CAAC,IAAW,EAAE,MAAM,CAAC,CAAC,EACjC,IAAI,CAAC,MAAM,CAAC,IAAW,EAAE,MAAM,CAAC,CAAC,EACjC,IAAI,CAAC,MAAM,CAAC,IAAW,EAAE,MAAM,CAAC,CAAC;YAEpC,KAAK,IAAI,CAAC,aAAa,CAAC,IAAW,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D;QAEA,qBAAqB;QACrB,WAAW,EAAE,CAAC,OAAO;YACnB,uBAAuB;QACzB;QAEA,uBAAuB;QACvB,MAAM,cAAc,CAAA,GAAA,mOAAA,CAAA,OAAO,AAAD,IACvB,EAAE,CAAC,SAAS,SAAS,KAAK,EAAE,CAAM;YACjC,IAAI,CAAC,MAAM,MAAM,EAAE,WAAW,WAAW,CAAC,KAAK,OAAO;YACtD,EAAE,EAAE,GAAG,EAAE,CAAC;YACV,EAAE,EAAE,GAAG,EAAE,CAAC;YACV,uBAAuB;QACzB,GACC,EAAE,CAAC,QAAQ,SAAS,KAAK,EAAE,CAAM;YAChC,EAAE,EAAE,GAAG,MAAM,CAAC;YACd,EAAE,EAAE,GAAG,MAAM,CAAC;QAChB,GACC,EAAE,CAAC,OAAO,SAAS,KAAK,EAAE,CAAM;YAC/B,IAAI,CAAC,MAAM,MAAM,EAAE,WAAW,WAAW,CAAC;YAC1C,EAAE,EAAE,GAAG;YACP,EAAE,EAAE,GAAG;QACT;QAEF,KAAK,IAAI,CAAC;QAEV,mBAAmB;QACnB,OAAO;YACL,WAAW,IAAI;QACjB;IACF,GAAG;QAAC;QAAM;QAAO;QAAQ;QAAa;KAAY;IAElD,MAAM,aAAa;QACjB,IAAI,CAAC,OAAO,OAAO,EAAE;QAErB,MAAM,MAAM,CAAA,GAAA,iPAAA,CAAA,SAAS,AAAD,EAAE,OAAO,OAAO;QACpC,MAAM,QAAQ,IAAI,SAAS,CAAC;QAC5B,MAAM,QAAQ,IAAI,SAAS,CAAC;QAE5B,mBAAmB;QACnB,MAAM,IAAI,CAAC,gBAAgB;QAC3B,MAAM,IAAI,CAAC,kBAAkB;QAC7B,gBAAgB;IAClB;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,OAAO,OAAO,EAAE;QAErB,MAAM,MAAM,CAAA,GAAA,iPAAA,CAAA,SAAS,AAAD,EAAE,OAAO,OAAO;QACpC,MAAM,OAAO,CAAA,GAAA,mOAAA,CAAA,OAAO,AAAD;QAEnB,IAAI,UAAU,GACX,QAAQ,CAAC,KACT,IAAI,CAAC,KAAK,SAAS,EAAE,iPAAA,CAAA,eAAe;IACzC;IAEA,qBACE,6WAAC,yHAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6WAAC,yHAAA,CAAA,aAAU;;kCACT,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,yHAAA,CAAA,YAAS;gCAAC,WAAU;0CAAqB;;;;;;0CAC1C,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;kDAAY;;;;;;kDAGzD,6WAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;kDAAa;;;;;;;;;;;;;;;;;;kCAK9D,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAA+B;;;;;;0CAGlE,6WAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAA6B;;;;;;0CAGhE,6WAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAAyB;;;;;;;;;;;;;;;;;;0BAKhE,6WAAC,yHAAA,CAAA,cAAW;0BACV,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,KAAK;4BAAQ,WAAU;;;;;;wBAG3B,CAAC,gBAAgB,WAAW,mBAC3B,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAG,WAAU;8CACX,CAAC,gBAAgB,WAAW,GAAG;;;;;;8CAElC,6WAAC;oCAAE,WAAU;;wCAAgD;sDACrD,6WAAC;4CAAK,WAAU;sDAAc,CAAC,gBAAgB,WAAW,GAAG;;;;;;;;;;;;8CAErE,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAE,WAAU;sDAAsB;;;;;;sDACnC,6WAAC;4CAAI,WAAU;sDACZ,CAAC,gBAAgB,WAAW,GAAG,aAAa,IAAI,CAAC,KAAK,sBACrD,6WAAC,0HAAA,CAAA,QAAK;oDAAa,SAAQ;oDAAY,WAAU;8DAC9C;mDADS;;;;;;;;;;;;;;;;;;;;;;sCAUtB,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC,0HAAA,CAAA,QAAK;gCAAC,SAAS,sBAAsB,YAAY;0CAC/C,sBAAsB,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5D", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/lib/network-data.ts"], "sourcesContent": ["// Network data generator for AI Agent Network Graph\n\nexport interface NetworkNode {\n  id: string;\n  name: string;\n  type: 'primary' | 'secondary' | 'hub';\n  capabilities: string[];\n}\n\nexport interface NetworkLink {\n  source: string;\n  target: string;\n  value: number;\n  type: 'collaboration' | 'data-flow' | 'dependency';\n}\n\nexport interface NetworkData {\n  nodes: NetworkNode[];\n  links: NetworkLink[];\n}\n\n// Generate sample network data based on existing agents\nexport function generateAgentNetworkData(): NetworkData {\n  const nodes: NetworkNode[] = [\n    // Primary AI Agents (from existing agent cards)\n    {\n      id: 'ai-assistant',\n      name: 'AI Assistant',\n      type: 'primary',\n      capabilities: ['Q&A', 'Research', 'Writing', 'Analysis']\n    },\n    {\n      id: 'code-expert',\n      name: 'Code Expert',\n      type: 'primary',\n      capabilities: ['Coding', 'Debugging', 'Code Review', 'Architecture']\n    },\n    {\n      id: 'creative-writer',\n      name: 'Creative Writer',\n      type: 'primary',\n      capabilities: ['Writing', 'Storytelling', 'Content', 'Creativity']\n    },\n    \n    // Hub Nodes (Central coordination points)\n    {\n      id: 'central-hub',\n      name: 'Central Hub',\n      type: 'hub',\n      capabilities: ['Coordination', 'Load Balancing', 'Task Distribution', 'Monitoring']\n    },\n    {\n      id: 'data-hub',\n      name: 'Data Hub',\n      type: 'hub',\n      capabilities: ['Data Storage', 'Data Processing', 'Analytics', 'Caching']\n    },\n    \n    // Secondary Agents (Specialized support agents)\n    {\n      id: 'language-processor',\n      name: 'Language Processor',\n      type: 'secondary',\n      capabilities: ['NLP', 'Translation', 'Sentiment Analysis', 'Text Processing']\n    },\n    {\n      id: 'image-analyzer',\n      name: 'Image Analyzer',\n      type: 'secondary',\n      capabilities: ['Computer Vision', 'Image Recognition', 'OCR', 'Visual Analysis']\n    },\n    {\n      id: 'task-scheduler',\n      name: 'Task Scheduler',\n      type: 'secondary',\n      capabilities: ['Scheduling', 'Priority Management', 'Resource Allocation', 'Workflow']\n    },\n    {\n      id: 'security-agent',\n      name: 'Security Agent',\n      type: 'secondary',\n      capabilities: ['Authentication', 'Authorization', 'Threat Detection', 'Encryption']\n    },\n    {\n      id: 'learning-optimizer',\n      name: 'Learning Optimizer',\n      type: 'secondary',\n      capabilities: ['Model Training', 'Hyperparameter Tuning', 'Performance Optimization', 'A/B Testing']\n    },\n    {\n      id: 'api-gateway',\n      name: 'API Gateway',\n      type: 'secondary',\n      capabilities: ['Request Routing', 'Rate Limiting', 'API Management', 'Load Balancing']\n    }\n  ];\n\n  const links: NetworkLink[] = [\n    // Central Hub connections (coordination)\n    { source: 'central-hub', target: 'ai-assistant', value: 8, type: 'collaboration' },\n    { source: 'central-hub', target: 'code-expert', value: 7, type: 'collaboration' },\n    { source: 'central-hub', target: 'creative-writer', value: 6, type: 'collaboration' },\n    { source: 'central-hub', target: 'task-scheduler', value: 9, type: 'dependency' },\n    { source: 'central-hub', target: 'api-gateway', value: 8, type: 'dependency' },\n    \n    // Data Hub connections (data flow)\n    { source: 'data-hub', target: 'ai-assistant', value: 7, type: 'data-flow' },\n    { source: 'data-hub', target: 'code-expert', value: 6, type: 'data-flow' },\n    { source: 'data-hub', target: 'creative-writer', value: 5, type: 'data-flow' },\n    { source: 'data-hub', target: 'language-processor', value: 8, type: 'data-flow' },\n    { source: 'data-hub', target: 'image-analyzer', value: 7, type: 'data-flow' },\n    { source: 'data-hub', target: 'learning-optimizer', value: 9, type: 'data-flow' },\n    \n    // Primary agent collaborations\n    { source: 'ai-assistant', target: 'language-processor', value: 6, type: 'collaboration' },\n    { source: 'ai-assistant', target: 'image-analyzer', value: 4, type: 'collaboration' },\n    { source: 'code-expert', target: 'security-agent', value: 7, type: 'collaboration' },\n    { source: 'code-expert', target: 'learning-optimizer', value: 5, type: 'collaboration' },\n    { source: 'creative-writer', target: 'language-processor', value: 8, type: 'collaboration' },\n    { source: 'creative-writer', target: 'image-analyzer', value: 6, type: 'collaboration' },\n    \n    // Secondary agent dependencies\n    { source: 'task-scheduler', target: 'ai-assistant', value: 5, type: 'dependency' },\n    { source: 'task-scheduler', target: 'code-expert', value: 5, type: 'dependency' },\n    { source: 'task-scheduler', target: 'creative-writer', value: 4, type: 'dependency' },\n    { source: 'security-agent', target: 'api-gateway', value: 8, type: 'dependency' },\n    { source: 'api-gateway', target: 'ai-assistant', value: 6, type: 'dependency' },\n    { source: 'api-gateway', target: 'code-expert', value: 6, type: 'dependency' },\n    { source: 'api-gateway', target: 'creative-writer', value: 5, type: 'dependency' },\n    \n    // Cross-secondary collaborations\n    { source: 'language-processor', target: 'image-analyzer', value: 4, type: 'collaboration' },\n    { source: 'security-agent', target: 'learning-optimizer', value: 3, type: 'collaboration' },\n    { source: 'task-scheduler', target: 'learning-optimizer', value: 5, type: 'collaboration' },\n    \n    // Hub interconnections\n    { source: 'central-hub', target: 'data-hub', value: 10, type: 'data-flow' }\n  ];\n\n  return { nodes, links };\n}\n\n// Generate a smaller, simpler network for testing\nexport function generateSimpleNetworkData(): NetworkData {\n  const nodes: NetworkNode[] = [\n    {\n      id: 'ai-assistant',\n      name: 'AI Assistant',\n      type: 'primary',\n      capabilities: ['Q&A', 'Research', 'Writing']\n    },\n    {\n      id: 'code-expert',\n      name: 'Code Expert',\n      type: 'primary',\n      capabilities: ['Coding', 'Debugging']\n    },\n    {\n      id: 'creative-writer',\n      name: 'Creative Writer',\n      type: 'primary',\n      capabilities: ['Writing', 'Creativity']\n    },\n    {\n      id: 'central-hub',\n      name: 'Central Hub',\n      type: 'hub',\n      capabilities: ['Coordination', 'Management']\n    },\n    {\n      id: 'data-processor',\n      name: 'Data Processor',\n      type: 'secondary',\n      capabilities: ['Data Processing', 'Analytics']\n    }\n  ];\n\n  const links: NetworkLink[] = [\n    { source: 'central-hub', target: 'ai-assistant', value: 5, type: 'collaboration' },\n    { source: 'central-hub', target: 'code-expert', value: 4, type: 'collaboration' },\n    { source: 'central-hub', target: 'creative-writer', value: 3, type: 'collaboration' },\n    { source: 'data-processor', target: 'ai-assistant', value: 6, type: 'data-flow' },\n    { source: 'data-processor', target: 'code-expert', value: 4, type: 'data-flow' },\n    { source: 'ai-assistant', target: 'creative-writer', value: 3, type: 'collaboration' }\n  ];\n\n  return { nodes, links };\n}\n\n// Generate dynamic network data based on agent activity\nexport function generateDynamicNetworkData(activeAgents: string[] = []): NetworkData {\n  const baseData = generateAgentNetworkData();\n  \n  // Modify link strengths based on active agents\n  const modifiedLinks = baseData.links.map(link => ({\n    ...link,\n    value: activeAgents.includes(link.source) || activeAgents.includes(link.target) \n      ? link.value * 1.5 \n      : link.value\n  }));\n  \n  // Add temporary nodes for active sessions\n  const sessionNodes: NetworkNode[] = activeAgents.map(agentId => ({\n    id: `${agentId}-session`,\n    name: `${agentId} Session`,\n    type: 'secondary' as const,\n    capabilities: ['Active Session', 'Real-time Processing']\n  }));\n  \n  // Add session links\n  const sessionLinks: NetworkLink[] = activeAgents.map(agentId => ({\n    source: agentId,\n    target: `${agentId}-session`,\n    value: 10,\n    type: 'data-flow' as const\n  }));\n  \n  return {\n    nodes: [...baseData.nodes, ...sessionNodes],\n    links: [...modifiedLinks, ...sessionLinks]\n  };\n}\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;;;AAsB7C,SAAS;IACd,MAAM,QAAuB;QAC3B,gDAAgD;QAChD;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,cAAc;gBAAC;gBAAO;gBAAY;gBAAW;aAAW;QAC1D;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,cAAc;gBAAC;gBAAU;gBAAa;gBAAe;aAAe;QACtE;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,cAAc;gBAAC;gBAAW;gBAAgB;gBAAW;aAAa;QACpE;QAEA,0CAA0C;QAC1C;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,cAAc;gBAAC;gBAAgB;gBAAkB;gBAAqB;aAAa;QACrF;QACA;YACE,IAAI;YAC<PERSON>,MAAM;YACN,MAAM;YACN,cAAc;gBAAC;gBAAgB;gBAAmB;gBAAa;aAAU;QAC3E;QAEA,gDAAgD;QAChD;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,cAAc;gBAAC;gBAAO;gBAAe;gBAAsB;aAAkB;QAC/E;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,cAAc;gBAAC;gBAAmB;gBAAqB;gBAAO;aAAkB;QAClF;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,cAAc;gBAAC;gBAAc;gBAAuB;gBAAuB;aAAW;QACxF;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,cAAc;gBAAC;gBAAkB;gBAAiB;gBAAoB;aAAa;QACrF;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,cAAc;gBAAC;gBAAkB;gBAAyB;gBAA4B;aAAc;QACtG;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,cAAc;gBAAC;gBAAmB;gBAAiB;gBAAkB;aAAiB;QACxF;KACD;IAED,MAAM,QAAuB;QAC3B,yCAAyC;QACzC;YAAE,QAAQ;YAAe,QAAQ;YAAgB,OAAO;YAAG,MAAM;QAAgB;QACjF;YAAE,QAAQ;YAAe,QAAQ;YAAe,OAAO;YAAG,MAAM;QAAgB;QAChF;YAAE,QAAQ;YAAe,QAAQ;YAAmB,OAAO;YAAG,MAAM;QAAgB;QACpF;YAAE,QAAQ;YAAe,QAAQ;YAAkB,OAAO;YAAG,MAAM;QAAa;QAChF;YAAE,QAAQ;YAAe,QAAQ;YAAe,OAAO;YAAG,MAAM;QAAa;QAE7E,mCAAmC;QACnC;YAAE,QAAQ;YAAY,QAAQ;YAAgB,OAAO;YAAG,MAAM;QAAY;QAC1E;YAAE,QAAQ;YAAY,QAAQ;YAAe,OAAO;YAAG,MAAM;QAAY;QACzE;YAAE,QAAQ;YAAY,QAAQ;YAAmB,OAAO;YAAG,MAAM;QAAY;QAC7E;YAAE,QAAQ;YAAY,QAAQ;YAAsB,OAAO;YAAG,MAAM;QAAY;QAChF;YAAE,QAAQ;YAAY,QAAQ;YAAkB,OAAO;YAAG,MAAM;QAAY;QAC5E;YAAE,QAAQ;YAAY,QAAQ;YAAsB,OAAO;YAAG,MAAM;QAAY;QAEhF,+BAA+B;QAC/B;YAAE,QAAQ;YAAgB,QAAQ;YAAsB,OAAO;YAAG,MAAM;QAAgB;QACxF;YAAE,QAAQ;YAAgB,QAAQ;YAAkB,OAAO;YAAG,MAAM;QAAgB;QACpF;YAAE,QAAQ;YAAe,QAAQ;YAAkB,OAAO;YAAG,MAAM;QAAgB;QACnF;YAAE,QAAQ;YAAe,QAAQ;YAAsB,OAAO;YAAG,MAAM;QAAgB;QACvF;YAAE,QAAQ;YAAmB,QAAQ;YAAsB,OAAO;YAAG,MAAM;QAAgB;QAC3F;YAAE,QAAQ;YAAmB,QAAQ;YAAkB,OAAO;YAAG,MAAM;QAAgB;QAEvF,+BAA+B;QAC/B;YAAE,QAAQ;YAAkB,QAAQ;YAAgB,OAAO;YAAG,MAAM;QAAa;QACjF;YAAE,QAAQ;YAAkB,QAAQ;YAAe,OAAO;YAAG,MAAM;QAAa;QAChF;YAAE,QAAQ;YAAkB,QAAQ;YAAmB,OAAO;YAAG,MAAM;QAAa;QACpF;YAAE,QAAQ;YAAkB,QAAQ;YAAe,OAAO;YAAG,MAAM;QAAa;QAChF;YAAE,QAAQ;YAAe,QAAQ;YAAgB,OAAO;YAAG,MAAM;QAAa;QAC9E;YAAE,QAAQ;YAAe,QAAQ;YAAe,OAAO;YAAG,MAAM;QAAa;QAC7E;YAAE,QAAQ;YAAe,QAAQ;YAAmB,OAAO;YAAG,MAAM;QAAa;QAEjF,iCAAiC;QACjC;YAAE,QAAQ;YAAsB,QAAQ;YAAkB,OAAO;YAAG,MAAM;QAAgB;QAC1F;YAAE,QAAQ;YAAkB,QAAQ;YAAsB,OAAO;YAAG,MAAM;QAAgB;QAC1F;YAAE,QAAQ;YAAkB,QAAQ;YAAsB,OAAO;YAAG,MAAM;QAAgB;QAE1F,uBAAuB;QACvB;YAAE,QAAQ;YAAe,QAAQ;YAAY,OAAO;YAAI,MAAM;QAAY;KAC3E;IAED,OAAO;QAAE;QAAO;IAAM;AACxB;AAGO,SAAS;IACd,MAAM,QAAuB;QAC3B;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,cAAc;gBAAC;gBAAO;gBAAY;aAAU;QAC9C;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,cAAc;gBAAC;gBAAU;aAAY;QACvC;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,cAAc;gBAAC;gBAAW;aAAa;QACzC;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,cAAc;gBAAC;gBAAgB;aAAa;QAC9C;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,cAAc;gBAAC;gBAAmB;aAAY;QAChD;KACD;IAED,MAAM,QAAuB;QAC3B;YAAE,QAAQ;YAAe,QAAQ;YAAgB,OAAO;YAAG,MAAM;QAAgB;QACjF;YAAE,QAAQ;YAAe,QAAQ;YAAe,OAAO;YAAG,MAAM;QAAgB;QAChF;YAAE,QAAQ;YAAe,QAAQ;YAAmB,OAAO;YAAG,MAAM;QAAgB;QACpF;YAAE,QAAQ;YAAkB,QAAQ;YAAgB,OAAO;YAAG,MAAM;QAAY;QAChF;YAAE,QAAQ;YAAkB,QAAQ;YAAe,OAAO;YAAG,MAAM;QAAY;QAC/E;YAAE,QAAQ;YAAgB,QAAQ;YAAmB,OAAO;YAAG,MAAM;QAAgB;KACtF;IAED,OAAO;QAAE;QAAO;IAAM;AACxB;AAGO,SAAS,2BAA2B,eAAyB,EAAE;IACpE,MAAM,WAAW;IAEjB,+CAA+C;IAC/C,MAAM,gBAAgB,SAAS,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;YAChD,GAAG,IAAI;YACP,OAAO,aAAa,QAAQ,CAAC,KAAK,MAAM,KAAK,aAAa,QAAQ,CAAC,KAAK,MAAM,IAC1E,KAAK,KAAK,GAAG,MACb,KAAK,KAAK;QAChB,CAAC;IAED,0CAA0C;IAC1C,MAAM,eAA8B,aAAa,GAAG,CAAC,CAAA,UAAW,CAAC;YAC/D,IAAI,GAAG,QAAQ,QAAQ,CAAC;YACxB,MAAM,GAAG,QAAQ,QAAQ,CAAC;YAC1B,MAAM;YACN,cAAc;gBAAC;gBAAkB;aAAuB;QAC1D,CAAC;IAED,oBAAoB;IACpB,MAAM,eAA8B,aAAa,GAAG,CAAC,CAAA,UAAW,CAAC;YAC/D,QAAQ;YACR,QAAQ,GAAG,QAAQ,QAAQ,CAAC;YAC5B,OAAO;YACP,MAAM;QACR,CAAC;IAED,OAAO;QACL,OAAO;eAAI,SAAS,KAAK;eAAK;SAAa;QAC3C,OAAO;eAAI;eAAkB;SAAa;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 937, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 943, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/network/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport {\n  <PERSON>,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport AgentNetworkGraph from \"@/components/agent-network-graph\";\nimport {\n  generateAgentNetworkData,\n  generateSimpleNetworkData,\n  generateDynamicNetworkData,\n  NetworkData,\n  NetworkNode,\n} from \"@/lib/network-data\";\n\nexport default function NetworkPage() {\n  const [networkData, setNetworkData] = useState<NetworkData>(\n    generateSimpleNetworkData()\n  );\n  const [selectedNode, setSelectedNode] = useState<NetworkNode | null>(null);\n  const [hoveredNode, setHoveredNode] = useState<NetworkNode | null>(null);\n  const [networkType, setNetworkType] = useState<\"simple\" | \"full\" | \"dynamic\">(\n    \"simple\"\n  );\n  const [activeAgents, setActiveAgents] = useState<string[]>([]);\n\n  const handleNetworkTypeChange = (type: \"simple\" | \"full\" | \"dynamic\") => {\n    setNetworkType(type);\n    switch (type) {\n      case \"simple\":\n        setNetworkData(generateSimpleNetworkData());\n        break;\n      case \"full\":\n        setNetworkData(generateAgentNetworkData());\n        break;\n      case \"dynamic\":\n        setNetworkData(generateDynamicNetworkData(activeAgents));\n        break;\n    }\n  };\n\n  const handleNodeClick = (node: NetworkNode) => {\n    setSelectedNode(node);\n\n    // Toggle agent active state for dynamic network\n    if (networkType === \"dynamic\") {\n      const newActiveAgents = activeAgents.includes(node.id)\n        ? activeAgents.filter((id) => id !== node.id)\n        : [...activeAgents, node.id];\n\n      setActiveAgents(newActiveAgents);\n      setNetworkData(generateDynamicNetworkData(newActiveAgents));\n    }\n  };\n\n  const handleNodeHover = (node: NetworkNode | null) => {\n    setHoveredNode(node);\n  };\n\n  const resetNetwork = () => {\n    setActiveAgents([]);\n    setSelectedNode(null);\n    setHoveredNode(null);\n    handleNetworkTypeChange(networkType);\n  };\n\n  return (\n    <div className='min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8'>\n      <div className='max-w-7xl mx-auto space-y-8'>\n        {/* Header */}\n        <div className='text-center'>\n          <h1 className='text-4xl font-bold text-gray-900 dark:text-white mb-4'>\n            AI Agent Network Visualization\n          </h1>\n          <p className='text-lg text-gray-600 dark:text-gray-300 mb-6'>\n            Explore the interconnected network of AI agents and their\n            relationships\n          </p>\n\n          {/* Network Type Controls */}\n          <div className='flex justify-center gap-4 mb-6'>\n            <Button\n              variant={networkType === \"simple\" ? \"default\" : \"outline\"}\n              onClick={() => handleNetworkTypeChange(\"simple\")}\n            >\n              Simple Network\n            </Button>\n            <Button\n              variant={networkType === \"full\" ? \"default\" : \"outline\"}\n              onClick={() => handleNetworkTypeChange(\"full\")}\n            >\n              Full Network\n            </Button>\n            <Button\n              variant={networkType === \"dynamic\" ? \"default\" : \"outline\"}\n              onClick={() => handleNetworkTypeChange(\"dynamic\")}\n            >\n              Dynamic Network\n            </Button>\n            <Button variant='secondary' onClick={resetNetwork}>\n              Reset\n            </Button>\n          </div>\n        </div>\n\n        {/* Network Statistics */}\n        <div className='grid grid-cols-1 md:grid-cols-4 gap-4 mb-6'>\n          <Card>\n            <CardHeader className='pb-2'>\n              <CardTitle className='text-sm font-medium'>Total Nodes</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className='text-2xl font-bold'>\n                {networkData.nodes.length}\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className='pb-2'>\n              <CardTitle className='text-sm font-medium'>\n                Total Connections\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className='text-2xl font-bold'>\n                {networkData.links.length}\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className='pb-2'>\n              <CardTitle className='text-sm font-medium'>\n                Active Agents\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className='text-2xl font-bold'>{activeAgents.length}</div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className='pb-2'>\n              <CardTitle className='text-sm font-medium'>\n                Network Type\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className='text-lg font-semibold capitalize'>\n                {networkType}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Main Network Graph */}\n        <AgentNetworkGraph\n          data={networkData}\n          width={1000}\n          height={700}\n          onNodeClick={handleNodeClick}\n          onNodeHover={handleNodeHover}\n        />\n\n        {/* Network Information Panel */}\n        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>\n          {/* Connection Types Legend */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Connection Types</CardTitle>\n              <CardDescription>\n                Different types of relationships between agents\n              </CardDescription>\n            </CardHeader>\n            <CardContent className='space-y-4'>\n              <div className='flex items-center gap-3'>\n                <div className='w-4 h-1 bg-green-500 rounded'></div>\n                <div>\n                  <p className='font-medium'>Collaboration</p>\n                  <p className='text-sm text-gray-600 dark:text-gray-300'>\n                    Agents working together on tasks\n                  </p>\n                </div>\n              </div>\n\n              <div className='flex items-center gap-3'>\n                <div className='w-4 h-1 bg-blue-500 rounded'></div>\n                <div>\n                  <p className='font-medium'>Data Flow</p>\n                  <p className='text-sm text-gray-600 dark:text-gray-300'>\n                    Information and data exchange\n                  </p>\n                </div>\n              </div>\n\n              <div className='flex items-center gap-3'>\n                <div className='w-4 h-1 bg-yellow-500 rounded'></div>\n                <div>\n                  <p className='font-medium'>Dependency</p>\n                  <p className='text-sm text-gray-600 dark:text-gray-300'>\n                    Service dependencies and requirements\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Active Agents Panel */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Active Agents</CardTitle>\n              <CardDescription>\n                Currently active agents in the network\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              {activeAgents.length > 0 ? (\n                <div className='space-y-2'>\n                  {activeAgents.map((agentId) => {\n                    const agent = networkData.nodes.find(\n                      (n) => n.id === agentId\n                    );\n                    return agent ? (\n                      <div\n                        key={agentId}\n                        className='flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded'\n                      >\n                        <span className='font-medium'>{agent.name}</span>\n                        <Badge variant='secondary'>{agent.type}</Badge>\n                      </div>\n                    ) : null;\n                  })}\n                </div>\n              ) : (\n                <p className='text-gray-600 dark:text-gray-300'>\n                  {networkType === \"dynamic\"\n                    ? \"Click on nodes to activate agents\"\n                    : \"No active agents in this network type\"}\n                </p>\n              )}\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Instructions */}\n        <Card>\n          <CardHeader>\n            <CardTitle>How to Use</CardTitle>\n          </CardHeader>\n          <CardContent className='space-y-2'>\n            <p>\n              • <strong>Click and drag</strong> nodes to reposition them\n            </p>\n            <p>\n              • <strong>Hover</strong> over nodes to see details\n            </p>\n            <p>\n              • <strong>Click</strong> nodes to select and highlight connections\n            </p>\n            <p>\n              • <strong>Zoom and pan</strong> to explore different areas\n            </p>\n            <p>\n              • <strong>Switch network types</strong> to see different\n              configurations\n            </p>\n            {networkType === \"dynamic\" && (\n              <p>\n                • <strong>Dynamic mode:</strong> Click nodes to\n                activate/deactivate agents\n              </p>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAOA;AACA;AACA;AAbA;;;;;;;;AAqBe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAC3C,CAAA,GAAA,sHAAA,CAAA,4BAAyB,AAAD;IAE1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAsB;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAC3C;IAEF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE7D,MAAM,0BAA0B,CAAC;QAC/B,eAAe;QACf,OAAQ;YACN,KAAK;gBACH,eAAe,CAAA,GAAA,sHAAA,CAAA,4BAAyB,AAAD;gBACvC;YACF,KAAK;gBACH,eAAe,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD;gBACtC;YACF,KAAK;gBACH,eAAe,CAAA,GAAA,sHAAA,CAAA,6BAA0B,AAAD,EAAE;gBAC1C;QACJ;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;QAEhB,gDAAgD;QAChD,IAAI,gBAAgB,WAAW;YAC7B,MAAM,kBAAkB,aAAa,QAAQ,CAAC,KAAK,EAAE,IACjD,aAAa,MAAM,CAAC,CAAC,KAAO,OAAO,KAAK,EAAE,IAC1C;mBAAI;gBAAc,KAAK,EAAE;aAAC;YAE9B,gBAAgB;YAChB,eAAe,CAAA,GAAA,sHAAA,CAAA,6BAA0B,AAAD,EAAE;QAC5C;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,eAAe;IACjB;IAEA,MAAM,eAAe;QACnB,gBAAgB,EAAE;QAClB,gBAAgB;QAChB,eAAe;QACf,wBAAwB;IAC1B;IAEA,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BAEb,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,6WAAC;4BAAE,WAAU;sCAAgD;;;;;;sCAM7D,6WAAC;4BAAI,WAAU;;8CACb,6WAAC,2HAAA,CAAA,SAAM;oCACL,SAAS,gBAAgB,WAAW,YAAY;oCAChD,SAAS,IAAM,wBAAwB;8CACxC;;;;;;8CAGD,6WAAC,2HAAA,CAAA,SAAM;oCACL,SAAS,gBAAgB,SAAS,YAAY;oCAC9C,SAAS,IAAM,wBAAwB;8CACxC;;;;;;8CAGD,6WAAC,2HAAA,CAAA,SAAM;oCACL,SAAS,gBAAgB,YAAY,YAAY;oCACjD,SAAS,IAAM,wBAAwB;8CACxC;;;;;;8CAGD,6WAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAY,SAAS;8CAAc;;;;;;;;;;;;;;;;;;8BAOvD,6WAAC;oBAAI,WAAU;;sCACb,6WAAC,yHAAA,CAAA,OAAI;;8CACH,6WAAC,yHAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,6WAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,6WAAC,yHAAA,CAAA,cAAW;8CACV,cAAA,6WAAC;wCAAI,WAAU;kDACZ,YAAY,KAAK,CAAC,MAAM;;;;;;;;;;;;;;;;;sCAK/B,6WAAC,yHAAA,CAAA,OAAI;;8CACH,6WAAC,yHAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,6WAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAI7C,6WAAC,yHAAA,CAAA,cAAW;8CACV,cAAA,6WAAC;wCAAI,WAAU;kDACZ,YAAY,KAAK,CAAC,MAAM;;;;;;;;;;;;;;;;;sCAK/B,6WAAC,yHAAA,CAAA,OAAI;;8CACH,6WAAC,yHAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,6WAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAI7C,6WAAC,yHAAA,CAAA,cAAW;8CACV,cAAA,6WAAC;wCAAI,WAAU;kDAAsB,aAAa,MAAM;;;;;;;;;;;;;;;;;sCAI5D,6WAAC,yHAAA,CAAA,OAAI;;8CACH,6WAAC,yHAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,6WAAC,yHAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAI7C,6WAAC,yHAAA,CAAA,cAAW;8CACV,cAAA,6WAAC;wCAAI,WAAU;kDACZ;;;;;;;;;;;;;;;;;;;;;;;8BAOT,6WAAC,wIAAA,CAAA,UAAiB;oBAChB,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,aAAa;oBACb,aAAa;;;;;;8BAIf,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC,yHAAA,CAAA,OAAI;;8CACH,6WAAC,yHAAA,CAAA,aAAU;;sDACT,6WAAC,yHAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6WAAC,yHAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6WAAC,yHAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAI,WAAU;;;;;;8DACf,6WAAC;;sEACC,6WAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,6WAAC;4DAAE,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;sDAM5D,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAI,WAAU;;;;;;8DACf,6WAAC;;sEACC,6WAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,6WAAC;4DAAE,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;sDAM5D,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAI,WAAU;;;;;;8DACf,6WAAC;;sEACC,6WAAC;4DAAE,WAAU;sEAAc;;;;;;sEAC3B,6WAAC;4DAAE,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAShE,6WAAC,yHAAA,CAAA,OAAI;;8CACH,6WAAC,yHAAA,CAAA,aAAU;;sDACT,6WAAC,yHAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6WAAC,yHAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6WAAC,yHAAA,CAAA,cAAW;8CACT,aAAa,MAAM,GAAG,kBACrB,6WAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC;4CACjB,MAAM,QAAQ,YAAY,KAAK,CAAC,IAAI,CAClC,CAAC,IAAM,EAAE,EAAE,KAAK;4CAElB,OAAO,sBACL,6WAAC;gDAEC,WAAU;;kEAEV,6WAAC;wDAAK,WAAU;kEAAe,MAAM,IAAI;;;;;;kEACzC,6WAAC,0HAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAa,MAAM,IAAI;;;;;;;+CAJjC;;;;uDAML;wCACN;;;;;6DAGF,6WAAC;wCAAE,WAAU;kDACV,gBAAgB,YACb,sCACA;;;;;;;;;;;;;;;;;;;;;;;8BAQd,6WAAC,yHAAA,CAAA,OAAI;;sCACH,6WAAC,yHAAA,CAAA,aAAU;sCACT,cAAA,6WAAC,yHAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6WAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6WAAC;;wCAAE;sDACC,6WAAC;sDAAO;;;;;;wCAAuB;;;;;;;8CAEnC,6WAAC;;wCAAE;sDACC,6WAAC;sDAAO;;;;;;wCAAc;;;;;;;8CAE1B,6WAAC;;wCAAE;sDACC,6WAAC;sDAAO;;;;;;wCAAc;;;;;;;8CAE1B,6WAAC;;wCAAE;sDACC,6WAAC;sDAAO;;;;;;wCAAqB;;;;;;;8CAEjC,6WAAC;;wCAAE;sDACC,6WAAC;sDAAO;;;;;;wCAA6B;;;;;;;gCAGxC,gBAAgB,2BACf,6WAAC;;wCAAE;sDACC,6WAAC;sDAAO;;;;;;wCAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD", "debugId": null}}, {"offset": {"line": 1635, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}