module.exports = {

"[project]/components/knowledge-graph-3d.tsx [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/b8192_three_build_three_core_94ccced5.js",
  "server/chunks/ssr/b8192_three_build_three_module_cc2bd29c.js",
  "server/chunks/ssr/b8192_three_build_three_module_5dd9cb4b.js",
  "server/chunks/ssr/09190_react-reconciler_f354e98a._.js",
  "server/chunks/ssr/2b4b7_@react-three_fiber_dist_2f06d031._.js",
  "server/chunks/ssr/35235_next_dist_compiled_b28858b8._.js",
  "server/chunks/ssr/bda96_@react-spring_core_dist_react-spring_core_modern_mjs_c9dccf00._.js",
  "server/chunks/ssr/node_modules__pnpm_0a62b2d0._.js",
  "server/chunks/ssr/components_knowledge-graph-3d_tsx_34d6f6ea._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/knowledge-graph-3d.tsx [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}}),

};