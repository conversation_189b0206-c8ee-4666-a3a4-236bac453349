module.exports = {

"[project]/components/knowledge-graph-3d.tsx [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules__pnpm_5b26f9ec._.js",
  "server/chunks/ssr/components_knowledge-graph-3d_tsx_34d6f6ea._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/knowledge-graph-3d.tsx [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}}),

};