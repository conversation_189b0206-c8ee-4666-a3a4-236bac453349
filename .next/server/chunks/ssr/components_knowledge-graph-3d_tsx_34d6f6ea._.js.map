{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/knowledge-graph-3d.tsx"], "sourcesContent": ["// 3D Knowledge Graph Visualization with React Three Fiber\n'use client';\n\nimport React, { useRef, useState, useEffect, Suspense } from 'react';\nimport { Canvas, useFrame } from '@react-three/fiber';\nimport { OrbitControls, Html, Line } from '@react-three/drei';\nimport * as THREE from 'three';\nimport { useSpring, animated } from '@react-spring/three';\n\n// Types for the knowledge graph\nexport interface GraphNode {\n  id: string;\n  label: string;\n  type: 'concept' | 'entity' | 'document' | 'agent' | 'user';\n  position: [number, number, number];\n  importance?: number;\n  description?: string;\n  properties?: Record<string, any>;\n  connections?: string[];\n}\n\nexport interface GraphEdge {\n  id: string;\n  source: string;\n  target: string;\n  type: 'contains' | 'related' | 'created' | 'uses';\n  weight?: number;\n  label?: string;\n}\n\nexport interface GraphData {\n  nodes: GraphNode[];\n  edges: GraphEdge[];\n}\n\n// Node component representing entities in the knowledge graph\nfunction Node({\n  node,\n  onSelect,\n  isSelected,\n  connectionCount\n}: {\n  node: GraphNode;\n  onSelect: () => void;\n  isSelected: boolean;\n  connectionCount: number;\n}) {\n  const meshRef = useRef<THREE.Mesh>(null);\n  const [hovered, setHovered] = useState(false);\n\n  // Animation for hover and selection effects\n  const { scale, emissive } = useSpring({\n    scale: hovered || isSelected ? 1.2 : 1,\n    emissive: hovered ? '#666' : isSelected ? '#00f' : '#000',\n    config: { tension: 300, friction: 10 }\n  });\n\n  // Subtle floating animation\n  useFrame((state) => {\n    if (meshRef.current && !isSelected) {\n      meshRef.current.position.y += Math.sin(state.clock.elapsedTime * 0.5 + node.position[0]) * 0.001;\n    }\n  });\n\n  const color = getNodeColor(node.type);\n  const size = getNodeSize(node.importance || 1);\n\n  return (\n    <group position={node.position}>\n      <animated.mesh\n        ref={meshRef}\n        scale={scale}\n        onClick={() => onSelect()}\n        onPointerOver={() => setHovered(true)}\n        onPointerOut={() => setHovered(false)}\n      >\n        <sphereGeometry args={[size, 32, 32]} />\n        <animated.meshStandardMaterial color={color} emissive={emissive} />\n      </animated.mesh>\n\n      <Html\n        position={[0, size + 0.3, 0]}\n        center\n        style={{\n          opacity: hovered || isSelected ? 1 : 0.7,\n          transition: 'opacity 0.2s',\n          background: 'rgba(0,0,0,0.5)',\n          padding: '2px 5px',\n          borderRadius: '3px',\n          color: 'white',\n          fontSize: '10px',\n          pointerEvents: 'none',\n          whiteSpace: 'nowrap'\n        }}\n      >\n        {node.label}\n      </Html>\n\n      {/* Connection indicator */}\n      {connectionCount > 0 && (\n        <Html\n          position={[size + 0.2, 0, 0]}\n          center\n          style={{\n            opacity: 0.8,\n            background: 'rgba(255,255,255,0.8)',\n            padding: '1px 3px',\n            borderRadius: '50%',\n            color: 'black',\n            fontSize: '8px',\n            pointerEvents: 'none'\n          }}\n        >\n          {connectionCount}\n        </Html>\n      )}\n    </group>\n  );\n}\n\n// Edge component representing relationships between nodes\nfunction Edge({\n  edge,\n  startPosition,\n  endPosition\n}: {\n  edge: GraphEdge;\n  startPosition: [number, number, number];\n  endPosition: [number, number, number];\n}) {\n  const points = [\n    new THREE.Vector3(...startPosition),\n    new THREE.Vector3(...endPosition)\n  ];\n\n  const color = getEdgeColor(edge.type);\n  const thickness = getEdgeThickness(edge.weight || 1);\n\n  return (\n    <Line\n      points={points}\n      color={color}\n      lineWidth={thickness}\n      opacity={0.6}\n      transparent\n    />\n  );\n}\n\n// Detail panel that appears when a node is selected\nfunction DetailPanel({\n  node,\n  onClose\n}: {\n  node: GraphNode | null;\n  onClose: () => void;\n}) {\n  if (!node) return null;\n\n  return (\n    <div\n      style={{\n        position: 'absolute',\n        bottom: '20px',\n        right: '20px',\n        width: '300px',\n        padding: '15px',\n        backgroundColor: 'rgba(0,0,0,0.9)',\n        color: 'white',\n        borderRadius: '8px',\n        zIndex: 100,\n        border: '1px solid rgba(255,255,255,0.2)',\n        backdropFilter: 'blur(10px)'\n      }}\n    >\n      <h3 style={{ margin: '0 0 10px 0', color: getNodeColor(node.type) }}>\n        {node.label}\n      </h3>\n      <p><strong>Type:</strong> {node.type}</p>\n      <p><strong>Connections:</strong> {node.connections?.length || 0}</p>\n      {node.description && <p><strong>Description:</strong> {node.description}</p>}\n\n      {node.properties && (\n        <div style={{ marginTop: '10px' }}>\n          <strong>Properties:</strong>\n          <div style={{ marginLeft: '10px', fontSize: '12px' }}>\n            {Object.entries(node.properties).map(([key, value]) => (\n              <div key={key} style={{ margin: '2px 0' }}>\n                <strong>{key}:</strong> {String(value)}\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      <button\n        onClick={onClose}\n        style={{\n          background: '#3498db',\n          border: 'none',\n          padding: '8px 15px',\n          marginTop: '15px',\n          color: 'white',\n          cursor: 'pointer',\n          borderRadius: '4px',\n          fontSize: '12px'\n        }}\n      >\n        Close\n      </button>\n    </div>\n  );\n}\n\n// Controls panel\nfunction ControlsPanel({\n  nodes,\n  edges,\n  onResetView,\n  onLayoutChange,\n  currentLayout\n}: {\n  nodes: GraphNode[];\n  edges: GraphEdge[];\n  onResetView: () => void;\n  onLayoutChange: (layout: string) => void;\n  currentLayout: string;\n}) {\n  return (\n    <div\n      style={{\n        position: 'absolute',\n        top: '20px',\n        left: '20px',\n        padding: '15px',\n        backgroundColor: 'rgba(0,0,0,0.8)',\n        color: 'white',\n        borderRadius: '8px',\n        border: '1px solid rgba(255,255,255,0.2)',\n        backdropFilter: 'blur(10px)',\n        minWidth: '200px'\n      }}\n    >\n      <h3 style={{ margin: '0 0 15px 0' }}>3D Knowledge Graph</h3>\n\n      <div style={{ marginBottom: '10px' }}>\n        <div>Nodes: {nodes.length}</div>\n        <div>Connections: {edges.length}</div>\n      </div>\n\n      <div style={{ marginBottom: '15px' }}>\n        <label style={{ display: 'block', marginBottom: '5px', fontSize: '12px' }}>\n          Layout:\n        </label>\n        <select\n          value={currentLayout}\n          onChange={(e) => onLayoutChange(e.target.value)}\n          style={{\n            background: 'rgba(255,255,255,0.1)',\n            border: '1px solid rgba(255,255,255,0.3)',\n            color: 'white',\n            padding: '5px',\n            borderRadius: '4px',\n            width: '100%'\n          }}\n        >\n          <option value=\"sphere\">Sphere</option>\n          <option value=\"cube\">Cube</option>\n          <option value=\"random\">Random</option>\n          <option value=\"force\">Force-Directed</option>\n        </select>\n      </div>\n\n      <button\n        onClick={onResetView}\n        style={{\n          background: '#3498db',\n          border: 'none',\n          padding: '8px 15px',\n          color: 'white',\n          cursor: 'pointer',\n          borderRadius: '4px',\n          width: '100%',\n          fontSize: '12px'\n        }}\n      >\n        Reset View\n      </button>\n    </div>\n  );\n}\n\n// Legend component\nfunction Legend() {\n  const nodeTypes = [\n    { type: 'concept', label: 'Concept', color: '#e74c3c' },\n    { type: 'entity', label: 'Entity', color: '#3498db' },\n    { type: 'document', label: 'Document', color: '#2ecc71' },\n    { type: 'agent', label: 'Agent', color: '#9b59b6' },\n    { type: 'user', label: 'User', color: '#f39c12' }\n  ];\n\n  const edgeTypes = [\n    { type: 'contains', label: 'Contains', color: '#3498db' },\n    { type: 'related', label: 'Related', color: '#95a5a6' },\n    { type: 'created', label: 'Created', color: '#2ecc71' },\n    { type: 'uses', label: 'Uses', color: '#e74c3c' }\n  ];\n\n  return (\n    <div\n      style={{\n        position: 'absolute',\n        top: '20px',\n        right: '20px',\n        padding: '15px',\n        backgroundColor: 'rgba(0,0,0,0.8)',\n        color: 'white',\n        borderRadius: '8px',\n        border: '1px solid rgba(255,255,255,0.2)',\n        backdropFilter: 'blur(10px)',\n        fontSize: '12px'\n      }}\n    >\n      <h4 style={{ margin: '0 0 10px 0' }}>Legend</h4>\n\n      <div style={{ marginBottom: '15px' }}>\n        <strong>Node Types:</strong>\n        {nodeTypes.map(({ type, label, color }) => (\n          <div key={type} style={{ display: 'flex', alignItems: 'center', margin: '5px 0' }}>\n            <div\n              style={{\n                width: '12px',\n                height: '12px',\n                borderRadius: '50%',\n                backgroundColor: color,\n                marginRight: '8px'\n              }}\n            />\n            {label}\n          </div>\n        ))}\n      </div>\n\n      <div>\n        <strong>Edge Types:</strong>\n        {edgeTypes.map(({ type, label, color }) => (\n          <div key={type} style={{ display: 'flex', alignItems: 'center', margin: '5px 0' }}>\n            <div\n              style={{\n                width: '20px',\n                height: '2px',\n                backgroundColor: color,\n                marginRight: '8px'\n              }}\n            />\n            {label}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n\n// Helper functions for visual properties\nconst getNodeColor = (type: string) => {\n  const colorMap: Record<string, string> = {\n    'concept': '#e74c3c',\n    'entity': '#3498db',\n    'document': '#2ecc71',\n    'agent': '#9b59b6',\n    'user': '#f39c12'\n  };\n  return colorMap[type] || '#95a5a6';\n};\n\nconst getNodeSize = (importance: number) => {\n  return 0.3 + (importance * 0.2);\n};\n\nconst getEdgeColor = (type: string) => {\n  const colorMap: Record<string, string> = {\n    'contains': '#3498db',\n    'related': '#95a5a6',\n    'created': '#2ecc71',\n    'uses': '#e74c3c'\n  };\n  return colorMap[type] || '#bdc3c7';\n};\n\nconst getEdgeThickness = (weight: number) => {\n  return 1 + (weight * 2);\n};\n\n// Layout algorithms\nconst generateLayout = (nodes: GraphNode[], layout: string): GraphNode[] => {\n  const radius = 8;\n\n  return nodes.map((node, index) => {\n    let position: [number, number, number];\n\n    switch (layout) {\n      case 'sphere':\n        const phi = Math.acos(-1 + (2 * index) / nodes.length);\n        const theta = Math.sqrt(nodes.length * Math.PI) * phi;\n        position = [\n          radius * Math.cos(theta) * Math.sin(phi),\n          radius * Math.sin(theta) * Math.sin(phi),\n          radius * Math.cos(phi)\n        ];\n        break;\n\n      case 'cube':\n        const size = Math.ceil(Math.cbrt(nodes.length));\n        const x = (index % size) - size / 2;\n        const y = Math.floor(index / size) % size - size / 2;\n        const z = Math.floor(index / (size * size)) - size / 2;\n        position = [x * 2, y * 2, z * 2];\n        break;\n\n      case 'random':\n        position = [\n          (Math.random() - 0.5) * radius * 2,\n          (Math.random() - 0.5) * radius * 2,\n          (Math.random() - 0.5) * radius * 2\n        ];\n        break;\n\n      case 'force':\n      default:\n        // Simple force-directed layout\n        const angle = (index / nodes.length) * Math.PI * 2;\n        const layer = Math.floor(index / 8);\n        const layerRadius = radius * (0.5 + layer * 0.3);\n        position = [\n          layerRadius * Math.cos(angle),\n          (Math.random() - 0.5) * 4,\n          layerRadius * Math.sin(angle)\n        ];\n        break;\n    }\n\n    return { ...node, position };\n  });\n};\n\n// Main knowledge graph component\nexport default function KnowledgeGraph3D({ data }: { data: GraphData }) {\n  const [nodes, setNodes] = useState<GraphNode[]>([]);\n  const [edges, setEdges] = useState<GraphEdge[]>([]);\n  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);\n  const [cameraTarget, setCameraTarget] = useState<[number, number, number]>([0, 0, 0]);\n  const [layout, setLayout] = useState('sphere');\n\n  // Process graph data when it changes\n  useEffect(() => {\n    if (!data) return;\n\n    // Apply layout to nodes\n    const layoutNodes = generateLayout(data.nodes, layout);\n    setNodes(layoutNodes);\n    setEdges(data.edges);\n  }, [data, layout]);\n\n  // Handle node selection\n  const handleNodeSelect = (node: GraphNode) => {\n    setSelectedNode(node);\n    setCameraTarget(node.position);\n  };\n\n  // Handle layout change\n  const handleLayoutChange = (newLayout: string) => {\n    setLayout(newLayout);\n  };\n\n  // Reset view\n  const handleResetView = () => {\n    setSelectedNode(null);\n    setCameraTarget([0, 0, 0]);\n  };\n\n  // Calculate connection count for each node\n  const getConnectionCount = (nodeId: string) => {\n    return edges.filter(edge => edge.source === nodeId || edge.target === nodeId).length;\n  };\n\n  return (\n    <div style={{ width: '100%', height: '100%', position: 'relative' }}>\n      <Canvas\n        camera={{ position: [0, 0, 15], fov: 60 }}\n        style={{ background: 'linear-gradient(to bottom, #1a1a2e, #16213e)' }}\n      >\n        <Suspense fallback={null}>\n          <ambientLight intensity={0.5} />\n          <pointLight position={[10, 10, 10]} intensity={1} />\n          <pointLight position={[-10, -10, -10]} intensity={0.5} />\n\n          {/* Render edges */}\n          {edges.map((edge) => {\n            const startNode = nodes.find(node => node.id === edge.source);\n            const endNode = nodes.find(node => node.id === edge.target);\n\n            if (!startNode || !endNode) return null;\n\n            return (\n              <Edge\n                key={edge.id}\n                edge={edge}\n                startPosition={startNode.position}\n                endPosition={endNode.position}\n              />\n            );\n          })}\n\n          {/* Render nodes */}\n          {nodes.map((node) => (\n            <Node\n              key={node.id}\n              node={node}\n              onSelect={() => handleNodeSelect(node)}\n              isSelected={selectedNode?.id === node.id}\n              connectionCount={getConnectionCount(node.id)}\n            />\n          ))}\n\n          {/* Camera controls */}\n          <OrbitControls\n            enableDamping\n            dampingFactor={0.1}\n            rotateSpeed={0.5}\n            target={new THREE.Vector3(...cameraTarget)}\n            maxDistance={50}\n            minDistance={5}\n          />\n\n          {/* Fog for depth effect */}\n          <fog attach=\"fog\" args={['#16213e', 10, 50]} />\n        </Suspense>\n      </Canvas>\n\n      {/* UI Overlays */}\n      <ControlsPanel\n        nodes={nodes}\n        edges={edges}\n        onResetView={handleResetView}\n        onLayoutChange={handleLayoutChange}\n        currentLayout={layout}\n      />\n\n      <Legend />\n\n      <DetailPanel\n        node={selectedNode}\n        onClose={() => setSelectedNode(null)}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;AAG1D;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AANA;;;;;;;AAkCA,8DAA8D;AAC9D,SAAS,KAAK,EACZ,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,eAAe,EAMhB;IACC,MAAM,UAAU,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAc;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,4CAA4C;IAC5C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iSAAA,CAAA,YAAS,AAAD,EAAE;QACpC,OAAO,WAAW,aAAa,MAAM;QACrC,UAAU,UAAU,SAAS,aAAa,SAAS;QACnD,QAAQ;YAAE,SAAS;YAAK,UAAU;QAAG;IACvC;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6ZAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,QAAQ,OAAO,IAAI,CAAC,YAAY;YAClC,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,MAAM,KAAK,QAAQ,CAAC,EAAE,IAAI;QAC7F;IACF;IAEA,MAAM,QAAQ,aAAa,KAAK,IAAI;IACpC,MAAM,OAAO,YAAY,KAAK,UAAU,IAAI;IAE5C,qBACE,6WAAC;QAAM,UAAU,KAAK,QAAQ;;0BAC5B,6WAAC,sZAAA,CAAA,WAAQ,CAAC,IAAI;gBACZ,KAAK;gBACL,OAAO;gBACP,SAAS,IAAM;gBACf,eAAe,IAAM,WAAW;gBAChC,cAAc,IAAM,WAAW;;kCAE/B,6WAAC;wBAAe,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;;;;;;kCACpC,6WAAC,sZAAA,CAAA,WAAQ,CAAC,oBAAoB;wBAAC,OAAO;wBAAO,UAAU;;;;;;;;;;;;0BAGzD,6WAAC,0WAAA,CAAA,OAAI;gBACH,UAAU;oBAAC;oBAAG,OAAO;oBAAK;iBAAE;gBAC5B,MAAM;gBACN,OAAO;oBACL,SAAS,WAAW,aAAa,IAAI;oBACrC,YAAY;oBACZ,YAAY;oBACZ,SAAS;oBACT,cAAc;oBACd,OAAO;oBACP,UAAU;oBACV,eAAe;oBACf,YAAY;gBACd;0BAEC,KAAK,KAAK;;;;;;YAIZ,kBAAkB,mBACjB,6WAAC,0WAAA,CAAA,OAAI;gBACH,UAAU;oBAAC,OAAO;oBAAK;oBAAG;iBAAE;gBAC5B,MAAM;gBACN,OAAO;oBACL,SAAS;oBACT,YAAY;oBACZ,SAAS;oBACT,cAAc;oBACd,OAAO;oBACP,UAAU;oBACV,eAAe;gBACjB;0BAEC;;;;;;;;;;;;AAKX;AAEA,0DAA0D;AAC1D,SAAS,KAAK,EACZ,IAAI,EACJ,aAAa,EACb,WAAW,EAKZ;IACC,MAAM,SAAS;QACb,IAAI,mMAAA,CAAA,UAAa,IAAI;QACrB,IAAI,mMAAA,CAAA,UAAa,IAAI;KACtB;IAED,MAAM,QAAQ,aAAa,KAAK,IAAI;IACpC,MAAM,YAAY,iBAAiB,KAAK,MAAM,IAAI;IAElD,qBACE,6WAAC,2WAAA,CAAA,OAAI;QACH,QAAQ;QACR,OAAO;QACP,WAAW;QACX,SAAS;QACT,WAAW;;;;;;AAGjB;AAEA,oDAAoD;AACpD,SAAS,YAAY,EACnB,IAAI,EACJ,OAAO,EAIR;IACC,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6WAAC;QACC,OAAO;YACL,UAAU;YACV,QAAQ;YACR,OAAO;YACP,OAAO;YACP,SAAS;YACT,iBAAiB;YACjB,OAAO;YACP,cAAc;YACd,QAAQ;YACR,QAAQ;YACR,gBAAgB;QAClB;;0BAEA,6WAAC;gBAAG,OAAO;oBAAE,QAAQ;oBAAc,OAAO,aAAa,KAAK,IAAI;gBAAE;0BAC/D,KAAK,KAAK;;;;;;0BAEb,6WAAC;;kCAAE,6WAAC;kCAAO;;;;;;oBAAc;oBAAE,KAAK,IAAI;;;;;;;0BACpC,6WAAC;;kCAAE,6WAAC;kCAAO;;;;;;oBAAqB;oBAAE,KAAK,WAAW,EAAE,UAAU;;;;;;;YAC7D,KAAK,WAAW,kBAAI,6WAAC;;kCAAE,6WAAC;kCAAO;;;;;;oBAAqB;oBAAE,KAAK,WAAW;;;;;;;YAEtE,KAAK,UAAU,kBACd,6WAAC;gBAAI,OAAO;oBAAE,WAAW;gBAAO;;kCAC9B,6WAAC;kCAAO;;;;;;kCACR,6WAAC;wBAAI,OAAO;4BAAE,YAAY;4BAAQ,UAAU;wBAAO;kCAChD,OAAO,OAAO,CAAC,KAAK,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAChD,6WAAC;gCAAc,OAAO;oCAAE,QAAQ;gCAAQ;;kDACtC,6WAAC;;4CAAQ;4CAAI;;;;;;;oCAAU;oCAAE,OAAO;;+BADxB;;;;;;;;;;;;;;;;0BAQlB,6WAAC;gBACC,SAAS;gBACT,OAAO;oBACL,YAAY;oBACZ,QAAQ;oBACR,SAAS;oBACT,WAAW;oBACX,OAAO;oBACP,QAAQ;oBACR,cAAc;oBACd,UAAU;gBACZ;0BACD;;;;;;;;;;;;AAKP;AAEA,iBAAiB;AACjB,SAAS,cAAc,EACrB,KAAK,EACL,KAAK,EACL,WAAW,EACX,cAAc,EACd,aAAa,EAOd;IACC,qBACE,6WAAC;QACC,OAAO;YACL,UAAU;YACV,KAAK;YACL,MAAM;YACN,SAAS;YACT,iBAAiB;YACjB,OAAO;YACP,cAAc;YACd,QAAQ;YACR,gBAAgB;YAChB,UAAU;QACZ;;0BAEA,6WAAC;gBAAG,OAAO;oBAAE,QAAQ;gBAAa;0BAAG;;;;;;0BAErC,6WAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;;kCACjC,6WAAC;;4BAAI;4BAAQ,MAAM,MAAM;;;;;;;kCACzB,6WAAC;;4BAAI;4BAAc,MAAM,MAAM;;;;;;;;;;;;;0BAGjC,6WAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;;kCACjC,6WAAC;wBAAM,OAAO;4BAAE,SAAS;4BAAS,cAAc;4BAAO,UAAU;wBAAO;kCAAG;;;;;;kCAG3E,6WAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wBAC9C,OAAO;4BACL,YAAY;4BACZ,QAAQ;4BACR,OAAO;4BACP,SAAS;4BACT,cAAc;4BACd,OAAO;wBACT;;0CAEA,6WAAC;gCAAO,OAAM;0CAAS;;;;;;0CACvB,6WAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,6WAAC;gCAAO,OAAM;0CAAS;;;;;;0CACvB,6WAAC;gCAAO,OAAM;0CAAQ;;;;;;;;;;;;;;;;;;0BAI1B,6WAAC;gBACC,SAAS;gBACT,OAAO;oBACL,YAAY;oBACZ,QAAQ;oBACR,SAAS;oBACT,OAAO;oBACP,QAAQ;oBACR,cAAc;oBACd,OAAO;oBACP,UAAU;gBACZ;0BACD;;;;;;;;;;;;AAKP;AAEA,mBAAmB;AACnB,SAAS;IACP,MAAM,YAAY;QAChB;YAAE,MAAM;YAAW,OAAO;YAAW,OAAO;QAAU;QACtD;YAAE,MAAM;YAAU,OAAO;YAAU,OAAO;QAAU;QACpD;YAAE,MAAM;YAAY,OAAO;YAAY,OAAO;QAAU;QACxD;YAAE,MAAM;YAAS,OAAO;YAAS,OAAO;QAAU;QAClD;YAAE,MAAM;YAAQ,OAAO;YAAQ,OAAO;QAAU;KACjD;IAED,MAAM,YAAY;QAChB;YAAE,MAAM;YAAY,OAAO;YAAY,OAAO;QAAU;QACxD;YAAE,MAAM;YAAW,OAAO;YAAW,OAAO;QAAU;QACtD;YAAE,MAAM;YAAW,OAAO;YAAW,OAAO;QAAU;QACtD;YAAE,MAAM;YAAQ,OAAO;YAAQ,OAAO;QAAU;KACjD;IAED,qBACE,6WAAC;QACC,OAAO;YACL,UAAU;YACV,KAAK;YACL,OAAO;YACP,SAAS;YACT,iBAAiB;YACjB,OAAO;YACP,cAAc;YACd,QAAQ;YACR,gBAAgB;YAChB,UAAU;QACZ;;0BAEA,6WAAC;gBAAG,OAAO;oBAAE,QAAQ;gBAAa;0BAAG;;;;;;0BAErC,6WAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;;kCACjC,6WAAC;kCAAO;;;;;;oBACP,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,iBACpC,6WAAC;4BAAe,OAAO;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,QAAQ;4BAAQ;;8CAC9E,6WAAC;oCACC,OAAO;wCACL,OAAO;wCACP,QAAQ;wCACR,cAAc;wCACd,iBAAiB;wCACjB,aAAa;oCACf;;;;;;gCAED;;2BAVO;;;;;;;;;;;0BAed,6WAAC;;kCACC,6WAAC;kCAAO;;;;;;oBACP,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,iBACpC,6WAAC;4BAAe,OAAO;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,QAAQ;4BAAQ;;8CAC9E,6WAAC;oCACC,OAAO;wCACL,OAAO;wCACP,QAAQ;wCACR,iBAAiB;wCACjB,aAAa;oCACf;;;;;;gCAED;;2BATO;;;;;;;;;;;;;;;;;AAepB;AAEA,yCAAyC;AACzC,MAAM,eAAe,CAAC;IACpB,MAAM,WAAmC;QACvC,WAAW;QACX,UAAU;QACV,YAAY;QACZ,SAAS;QACT,QAAQ;IACV;IACA,OAAO,QAAQ,CAAC,KAAK,IAAI;AAC3B;AAEA,MAAM,cAAc,CAAC;IACnB,OAAO,MAAO,aAAa;AAC7B;AAEA,MAAM,eAAe,CAAC;IACpB,MAAM,WAAmC;QACvC,YAAY;QACZ,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA,OAAO,QAAQ,CAAC,KAAK,IAAI;AAC3B;AAEA,MAAM,mBAAmB,CAAC;IACxB,OAAO,IAAK,SAAS;AACvB;AAEA,oBAAoB;AACpB,MAAM,iBAAiB,CAAC,OAAoB;IAC1C,MAAM,SAAS;IAEf,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM;QACtB,IAAI;QAEJ,OAAQ;YACN,KAAK;gBACH,MAAM,MAAM,KAAK,IAAI,CAAC,CAAC,IAAI,AAAC,IAAI,QAAS,MAAM,MAAM;gBACrD,MAAM,QAAQ,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG,KAAK,EAAE,IAAI;gBAClD,WAAW;oBACT,SAAS,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;oBACpC,SAAS,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;oBACpC,SAAS,KAAK,GAAG,CAAC;iBACnB;gBACD;YAEF,KAAK;gBACH,MAAM,OAAO,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,MAAM;gBAC7C,MAAM,IAAI,AAAC,QAAQ,OAAQ,OAAO;gBAClC,MAAM,IAAI,KAAK,KAAK,CAAC,QAAQ,QAAQ,OAAO,OAAO;gBACnD,MAAM,IAAI,KAAK,KAAK,CAAC,QAAQ,CAAC,OAAO,IAAI,KAAK,OAAO;gBACrD,WAAW;oBAAC,IAAI;oBAAG,IAAI;oBAAG,IAAI;iBAAE;gBAChC;YAEF,KAAK;gBACH,WAAW;oBACT,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,SAAS;oBACjC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,SAAS;oBACjC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,SAAS;iBAClC;gBACD;YAEF,KAAK;YACL;gBACE,+BAA+B;gBAC/B,MAAM,QAAQ,AAAC,QAAQ,MAAM,MAAM,GAAI,KAAK,EAAE,GAAG;gBACjD,MAAM,QAAQ,KAAK,KAAK,CAAC,QAAQ;gBACjC,MAAM,cAAc,SAAS,CAAC,MAAM,QAAQ,GAAG;gBAC/C,WAAW;oBACT,cAAc,KAAK,GAAG,CAAC;oBACvB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBACxB,cAAc,KAAK,GAAG,CAAC;iBACxB;gBACD;QACJ;QAEA,OAAO;YAAE,GAAG,IAAI;YAAE;QAAS;IAC7B;AACF;AAGe,SAAS,iBAAiB,EAAE,IAAI,EAAuB;IACpE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAClD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAoB;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAA4B;QAAC;QAAG;QAAG;KAAE;IACpF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qCAAqC;IACrC,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,MAAM;QAEX,wBAAwB;QACxB,MAAM,cAAc,eAAe,KAAK,KAAK,EAAE;QAC/C,SAAS;QACT,SAAS,KAAK,KAAK;IACrB,GAAG;QAAC;QAAM;KAAO;IAEjB,wBAAwB;IACxB,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,gBAAgB,KAAK,QAAQ;IAC/B;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,CAAC;QAC1B,UAAU;IACZ;IAEA,aAAa;IACb,MAAM,kBAAkB;QACtB,gBAAgB;QAChB,gBAAgB;YAAC;YAAG;YAAG;SAAE;IAC3B;IAEA,2CAA2C;IAC3C,MAAM,qBAAqB,CAAC;QAC1B,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,KAAK,QAAQ,MAAM;IACtF;IAEA,qBACE,6WAAC;QAAI,OAAO;YAAE,OAAO;YAAQ,QAAQ;YAAQ,UAAU;QAAW;;0BAChE,6WAAC,iZAAA,CAAA,SAAM;gBACL,QAAQ;oBAAE,UAAU;wBAAC;wBAAG;wBAAG;qBAAG;oBAAE,KAAK;gBAAG;gBACxC,OAAO;oBAAE,YAAY;gBAA+C;0BAEpE,cAAA,6WAAC,oUAAA,CAAA,WAAQ;oBAAC,UAAU;;sCAClB,6WAAC;4BAAa,WAAW;;;;;;sCACzB,6WAAC;4BAAW,UAAU;gCAAC;gCAAI;gCAAI;6BAAG;4BAAE,WAAW;;;;;;sCAC/C,6WAAC;4BAAW,UAAU;gCAAC,CAAC;gCAAI,CAAC;gCAAI,CAAC;6BAAG;4BAAE,WAAW;;;;;;wBAGjD,MAAM,GAAG,CAAC,CAAC;4BACV,MAAM,YAAY,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KAAK,MAAM;4BAC5D,MAAM,UAAU,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KAAK,MAAM;4BAE1D,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;4BAEnC,qBACE,6WAAC;gCAEC,MAAM;gCACN,eAAe,UAAU,QAAQ;gCACjC,aAAa,QAAQ,QAAQ;+BAHxB,KAAK,EAAE;;;;;wBAMlB;wBAGC,MAAM,GAAG,CAAC,CAAC,qBACV,6WAAC;gCAEC,MAAM;gCACN,UAAU,IAAM,iBAAiB;gCACjC,YAAY,cAAc,OAAO,KAAK,EAAE;gCACxC,iBAAiB,mBAAmB,KAAK,EAAE;+BAJtC,KAAK,EAAE;;;;;sCAShB,6WAAC,oXAAA,CAAA,gBAAa;4BACZ,aAAa;4BACb,eAAe;4BACf,aAAa;4BACb,QAAQ,IAAI,mMAAA,CAAA,UAAa,IAAI;4BAC7B,aAAa;4BACb,aAAa;;;;;;sCAIf,6WAAC;4BAAI,QAAO;4BAAM,MAAM;gCAAC;gCAAW;gCAAI;6BAAG;;;;;;;;;;;;;;;;;0BAK/C,6WAAC;gBACC,OAAO;gBACP,OAAO;gBACP,aAAa;gBACb,gBAAgB;gBAChB,eAAe;;;;;;0BAGjB,6WAAC;;;;;0BAED,6WAAC;gBACC,MAAM;gBACN,SAAS,IAAM,gBAAgB;;;;;;;;;;;;AAIvC", "debugId": null}}, {"offset": {"line": 901, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}