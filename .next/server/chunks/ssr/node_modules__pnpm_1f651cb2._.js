module.exports = {

"[project]/node_modules/.pnpm/hls.js@1.6.4/node_modules/hls.js/dist/hls.mjs [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/.pnpm/hls.js@1.6.4/node_modules/hls.js/dist/hls.mjs [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@mediapipe+tasks-vision@0.10.17/node_modules/@mediapipe/tasks-vision/vision_bundle.mjs [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/2809b_@mediapipe_tasks-vision_vision_bundle_mjs_c2626222._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@mediapipe+tasks-vision@0.10.17/node_modules/@mediapipe/tasks-vision/vision_bundle.mjs [app-ssr] (ecmascript)");
    });
});
}}),

};