"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[270],{3270:(t,n,e)=>{function r(){}function i(t){return null==t?r:function(){return this.querySelector(t)}}function o(){return[]}function u(t){return null==t?o:function(){return this.querySelectorAll(t)}}function a(t){return function(){return this.matches(t)}}function s(t){return function(n){return n.matches(t)}}e.d(n,{$Er:()=>nL,jTM:()=>nG,eRw:()=>n8,kJC:()=>n9,xJS:()=>er,tXi:()=>ee,KS8:()=>ei,TSS:()=>eo,Ltv:()=>nT,s_O:()=>ex,GSI:()=>ef});var l,c=Array.prototype.find;function f(){return this.firstElementChild}var h=Array.prototype.filter;function p(){return Array.from(this.children)}function v(t){return Array(t.length)}function d(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}function y(t,n,e,r,i,o){for(var u,a=0,s=n.length,l=o.length;a<l;++a)(u=n[a])?(u.__data__=o[a],r[a]=u):e[a]=new d(t,o[a]);for(;a<s;++a)(u=n[a])&&(i[a]=u)}function g(t,n,e,r,i,o,u){var a,s,l,c=new Map,f=n.length,h=o.length,p=Array(f);for(a=0;a<f;++a)(s=n[a])&&(p[a]=l=u.call(s,s.__data__,a,n)+"",c.has(l)?i[a]=s:c.set(l,s));for(a=0;a<h;++a)l=u.call(t,o[a],a,o)+"",(s=c.get(l))?(r[a]=s,s.__data__=o[a],c.delete(l)):e[a]=new d(t,o[a]);for(a=0;a<f;++a)(s=n[a])&&c.get(p[a])===s&&(i[a]=s)}function _(t){return t.__data__}function m(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}d.prototype={constructor:d,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var x="http://www.w3.org/1999/xhtml";let w={svg:"http://www.w3.org/2000/svg",xhtml:x,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function b(t){var n=t+="",e=n.indexOf(":");return e>=0&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),w.hasOwnProperty(n)?{space:w[n],local:t}:t}function N(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function k(t,n){return t.style.getPropertyValue(n)||N(t).getComputedStyle(t,null).getPropertyValue(n)}function M(t){return t.trim().split(/^|\s+/)}function A(t){return t.classList||new z(t)}function z(t){this._node=t,this._names=M(t.getAttribute("class")||"")}function E(t,n){for(var e=A(t),r=-1,i=n.length;++r<i;)e.add(n[r])}function S(t,n){for(var e=A(t),r=-1,i=n.length;++r<i;)e.remove(n[r])}function $(){this.textContent=""}function T(){this.innerHTML=""}function q(){this.nextSibling&&this.parentNode.appendChild(this)}function C(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function P(t){var n=b(t);return(n.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===x&&n.documentElement.namespaceURI===x?n.createElement(t):n.createElementNS(e,t)}})(n)}function X(){return null}function Y(){var t=this.parentNode;t&&t.removeChild(this)}function j(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function O(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function R(t){return function(){var n=this.__on;if(n){for(var e,r=0,i=-1,o=n.length;r<o;++r)(e=n[r],t.type&&e.type!==t.type||e.name!==t.name)?n[++i]=e:this.removeEventListener(e.type,e.listener,e.options);++i?n.length=i:delete this.__on}}}function D(t,n,e){return function(){var r,i=this.__on,o=function(t){n.call(this,t,this.__data__)};if(i){for(var u=0,a=i.length;u<a;++u)if((r=i[u]).type===t.type&&r.name===t.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=e),r.value=n;return}}this.addEventListener(t.type,o,e),r={type:t.type,name:t.name,value:n,listener:o,options:e},i?i.push(r):this.__on=[r]}}function I(t,n,e){var r=N(t),i=r.CustomEvent;"function"==typeof i?i=new i(n,e):(i=r.document.createEvent("Event"),e?(i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail):i.initEvent(n,!1,!1)),t.dispatchEvent(i)}z.prototype={add:function(t){0>this._names.indexOf(t)&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var V=[null];function B(t,n){this._groups=t,this._parents=n}function H(){return new B([[document.documentElement]],V)}B.prototype=H.prototype={constructor:B,select:function(t){"function"!=typeof t&&(t=i(t));for(var n=this._groups,e=n.length,r=Array(e),o=0;o<e;++o)for(var u,a,s=n[o],l=s.length,c=r[o]=Array(l),f=0;f<l;++f)(u=s[f])&&(a=t.call(u,u.__data__,f,s))&&("__data__"in u&&(a.__data__=u.__data__),c[f]=a);return new B(r,this._parents)},selectAll:function(t){if("function"==typeof t){var n;n=t,t=function(){var t;return t=n.apply(this,arguments),null==t?[]:Array.isArray(t)?t:Array.from(t)}}else t=u(t);for(var e=this._groups,r=e.length,i=[],o=[],a=0;a<r;++a)for(var s,l=e[a],c=l.length,f=0;f<c;++f)(s=l[f])&&(i.push(t.call(s,s.__data__,f,l)),o.push(s));return new B(i,o)},selectChild:function(t){var n;return this.select(null==t?f:(n="function"==typeof t?t:s(t),function(){return c.call(this.children,n)}))},selectChildren:function(t){var n;return this.selectAll(null==t?p:(n="function"==typeof t?t:s(t),function(){return h.call(this.children,n)}))},filter:function(t){"function"!=typeof t&&(t=a(t));for(var n=this._groups,e=n.length,r=Array(e),i=0;i<e;++i)for(var o,u=n[i],s=u.length,l=r[i]=[],c=0;c<s;++c)(o=u[c])&&t.call(o,o.__data__,c,u)&&l.push(o);return new B(r,this._parents)},data:function(t,n){if(!arguments.length)return Array.from(this,_);var e=n?g:y,r=this._parents,i=this._groups;"function"!=typeof t&&(w=t,t=function(){return w});for(var o=i.length,u=Array(o),a=Array(o),s=Array(o),l=0;l<o;++l){var c=r[l],f=i[l],h=f.length,p="object"==typeof(x=t.call(c,c&&c.__data__,l,r))&&"length"in x?x:Array.from(x),v=p.length,d=a[l]=Array(v),m=u[l]=Array(v);e(c,f,d,m,s[l]=Array(h),p,n);for(var x,w,b,N,k=0,M=0;k<v;++k)if(b=d[k]){for(k>=M&&(M=k+1);!(N=m[M])&&++M<v;);b._next=N||null}}return(u=new B(u,r))._enter=a,u._exit=s,u},enter:function(){return new B(this._enter||this._groups.map(v),this._parents)},exit:function(){return new B(this._exit||this._groups.map(v),this._parents)},join:function(t,n,e){var r=this.enter(),i=this,o=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=n&&(i=n(i))&&(i=i.selection()),null==e?o.remove():e(o),r&&i?r.merge(i).order():i},merge:function(t){for(var n=t.selection?t.selection():t,e=this._groups,r=n._groups,i=e.length,o=r.length,u=Math.min(i,o),a=Array(i),s=0;s<u;++s)for(var l,c=e[s],f=r[s],h=c.length,p=a[s]=Array(h),v=0;v<h;++v)(l=c[v]||f[v])&&(p[v]=l);for(;s<i;++s)a[s]=e[s];return new B(a,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r,i=t[n],o=i.length-1,u=i[o];--o>=0;)(r=i[o])&&(u&&4^r.compareDocumentPosition(u)&&u.parentNode.insertBefore(r,u),u=r);return this},sort:function(t){function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}t||(t=m);for(var e=this._groups,r=e.length,i=Array(r),o=0;o<r;++o){for(var u,a=e[o],s=a.length,l=i[o]=Array(s),c=0;c<s;++c)(u=a[c])&&(l[c]=u);l.sort(n)}return new B(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,o=r.length;i<o;++i){var u=r[i];if(u)return u}return null},size:function(){let t=0;for(let n of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var i,o=n[e],u=0,a=o.length;u<a;++u)(i=o[u])&&t.call(i,i.__data__,u,o);return this},attr:function(t,n){var e=b(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((null==n?e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof n?e.local?function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}:function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}:e.local?function(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}:function(t,n){return function(){this.setAttribute(t,n)}})(e,n))},style:function(t,n,e){return arguments.length>1?this.each((null==n?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof n?function(t,n,e){return function(){var r=n.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}:function(t,n,e){return function(){this.style.setProperty(t,n,e)}})(t,n,null==e?"":e)):k(this.node(),t)},property:function(t,n){return arguments.length>1?this.each((null==n?function(t){return function(){delete this[t]}}:"function"==typeof n?function(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}:function(t,n){return function(){this[t]=n}})(t,n)):this.node()[t]},classed:function(t,n){var e=M(t+"");if(arguments.length<2){for(var r=A(this.node()),i=-1,o=e.length;++i<o;)if(!r.contains(e[i]))return!1;return!0}return this.each(("function"==typeof n?function(t,n){return function(){(n.apply(this,arguments)?E:S)(this,t)}}:n?function(t){return function(){E(this,t)}}:function(t){return function(){S(this,t)}})(e,n))},text:function(t){return arguments.length?this.each(null==t?$:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?T:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(q)},lower:function(){return this.each(C)},append:function(t){var n="function"==typeof t?t:P(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})},insert:function(t,n){var e="function"==typeof t?t:P(t),r=null==n?X:"function"==typeof n?n:i(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),r.apply(this,arguments)||null)})},remove:function(){return this.each(Y)},clone:function(t){return this.select(t?O:j)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var r,i,o=(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");return e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}}),u=o.length;if(arguments.length<2){var a=this.node().__on;if(a){for(var s,l=0,c=a.length;l<c;++l)for(r=0,s=a[l];r<u;++r)if((i=o[r]).type===s.type&&i.name===s.name)return s.value}return}for(r=0,a=n?D:R;r<u;++r)this.each(a(o[r],n,e));return this},dispatch:function(t,n){return this.each(("function"==typeof n?function(t,n){return function(){return I(this,t,n.apply(this,arguments))}}:function(t,n){return function(){return I(this,t,n)}})(t,n))},[Symbol.iterator]:function*(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r,i=t[n],o=0,u=i.length;o<u;++o)(r=i[o])&&(yield r)}};var L={value:()=>{}};function G(){for(var t,n=0,e=arguments.length,r={};n<e;++n){if(!(t=arguments[n]+"")||t in r||/[\s.]/.test(t))throw Error("illegal type: "+t);r[t]=[]}return new K(r)}function K(t){this._=t}function U(t,n,e){for(var r=0,i=t.length;r<i;++r)if(t[r].name===n){t[r]=L,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=e&&t.push({name:n,value:e}),t}K.prototype=G.prototype={constructor:K,on:function(t,n){var e,r=this._,i=(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");if(e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),t&&!r.hasOwnProperty(t))throw Error("unknown type: "+t);return{type:t,name:n}}),o=-1,u=i.length;if(arguments.length<2){for(;++o<u;)if((e=(t=i[o]).type)&&(e=function(t,n){for(var e,r=0,i=t.length;r<i;++r)if((e=t[r]).name===n)return e.value}(r[e],t.name)))return e;return}if(null!=n&&"function"!=typeof n)throw Error("invalid callback: "+n);for(;++o<u;)if(e=(t=i[o]).type)r[e]=U(r[e],t.name,n);else if(null==n)for(e in r)r[e]=U(r[e],t.name,null);return this},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new K(t)},call:function(t,n){if((e=arguments.length-2)>0)for(var e,r,i=Array(e),o=0;o<e;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(r=this._[t],o=0,e=r.length;o<e;++o)r[o].value.apply(n,i)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(n,e)}};var F,J,Q=0,W=0,Z=0,tt=0,tn=0,te=0,tr="object"==typeof performance&&performance.now?performance:Date,ti="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function to(){return tn||(ti(tu),tn=tr.now()+te)}function tu(){tn=0}function ta(){this._call=this._time=this._next=null}function ts(t,n,e){var r=new ta;return r.restart(t,n,e),r}function tl(){tn=(tt=tr.now())+te,Q=W=0;try{!function(){to(),++Q;for(var t,n=F;n;)(t=tn-n._time)>=0&&n._call.call(void 0,t),n=n._next;--Q}()}finally{Q=0,function(){for(var t,n,e=F,r=1/0;e;)e._call?(r>e._time&&(r=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:F=n);J=t,tf(r)}(),tn=0}}function tc(){var t=tr.now(),n=t-tt;n>1e3&&(te-=n,tt=t)}function tf(t){!Q&&(W&&(W=clearTimeout(W)),t-tn>24?(t<1/0&&(W=setTimeout(tl,t-tr.now()-te)),Z&&(Z=clearInterval(Z))):(Z||(tt=tr.now(),Z=setInterval(tc,1e3)),Q=1,ti(tl)))}function th(t,n,e){var r=new ta;return n=null==n?0:+n,r.restart(e=>{r.stop(),t(e+n)},n,e),r}ta.prototype=ts.prototype={constructor:ta,restart:function(t,n,e){if("function"!=typeof t)throw TypeError("callback is not a function");e=(null==e?to():+e)+(null==n?0:+n),this._next||J===this||(J?J._next=this:F=this,J=this),this._call=t,this._time=e,tf()},stop:function(){this._call&&(this._call=null,this._time=1/0,tf())}};var tp=G("start","end","cancel","interrupt"),tv=[];function td(t,n,e,r,i,o){var u=t.__transition;if(u){if(e in u)return}else t.__transition={};!function(t,n,e){var r,i=t.__transition;function o(s){var l,c,f,h;if(1!==e.state)return a();for(l in i)if((h=i[l]).name===e.name){if(3===h.state)return th(o);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",t,t.__data__,h.index,h.group),delete i[l]):+l<n&&(h.state=6,h.timer.stop(),h.on.call("cancel",t,t.__data__,h.index,h.group),delete i[l])}if(th(function(){3===e.state&&(e.state=4,e.timer.restart(u,e.delay,e.time),u(s))}),e.state=2,e.on.call("start",t,t.__data__,e.index,e.group),2===e.state){for(l=0,e.state=3,r=Array(f=e.tween.length),c=-1;l<f;++l)(h=e.tween[l].value.call(t,t.__data__,e.index,e.group))&&(r[++c]=h);r.length=c+1}}function u(n){for(var i=n<e.duration?e.ease.call(null,n/e.duration):(e.timer.restart(a),e.state=5,1),o=-1,u=r.length;++o<u;)r[o].call(t,i);5===e.state&&(e.on.call("end",t,t.__data__,e.index,e.group),a())}function a(){for(var r in e.state=6,e.timer.stop(),delete i[n],i)return;delete t.__transition}i[n]=e,e.timer=ts(function(t){e.state=1,e.timer.restart(o,e.delay,e.time),e.delay<=t&&o(t-e.delay)},0,e.time)}(t,e,{name:n,index:r,group:i,on:tp,tween:tv,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:0})}function ty(t,n){var e=t_(t,n);if(e.state>0)throw Error("too late; already scheduled");return e}function tg(t,n){var e=t_(t,n);if(e.state>3)throw Error("too late; already running");return e}function t_(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw Error("transition not found");return e}function tm(t,n){var e,r,i,o=t.__transition,u=!0;if(o){for(i in n=null==n?null:n+"",o){if((e=o[i]).name!==n){u=!1;continue}r=e.state>2&&e.state<5,e.state=6,e.timer.stop(),e.on.call(r?"interrupt":"cancel",t,t.__data__,e.index,e.group),delete o[i]}u&&delete t.__transition}}function tx(t,n){return t*=1,n*=1,function(e){return t*(1-e)+n*e}}var tw=180/Math.PI,tb={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function tN(t,n,e,r,i,o){var u,a,s;return(u=Math.sqrt(t*t+n*n))&&(t/=u,n/=u),(s=t*e+n*r)&&(e-=t*s,r-=n*s),(a=Math.sqrt(e*e+r*r))&&(e/=a,r/=a,s/=a),t*r<n*e&&(t=-t,n=-n,s=-s,u=-u),{translateX:i,translateY:o,rotate:Math.atan2(n,t)*tw,skewX:Math.atan(s)*tw,scaleX:u,scaleY:a}}function tk(t,n,e,r){function i(t){return t.length?t.pop()+" ":""}return function(o,u){var a,s,l,c,f=[],h=[];return o=t(o),u=t(u),!function(t,r,i,o,u,a){if(t!==i||r!==o){var s=u.push("translate(",null,n,null,e);a.push({i:s-4,x:tx(t,i)},{i:s-2,x:tx(r,o)})}else(i||o)&&u.push("translate("+i+n+o+e)}(o.translateX,o.translateY,u.translateX,u.translateY,f,h),a=o.rotate,a!==(s=u.rotate)?(a-s>180?s+=360:s-a>180&&(a+=360),h.push({i:f.push(i(f)+"rotate(",null,r)-2,x:tx(a,s)})):s&&f.push(i(f)+"rotate("+s+r),l=o.skewX,l!==(c=u.skewX)?h.push({i:f.push(i(f)+"skewX(",null,r)-2,x:tx(l,c)}):c&&f.push(i(f)+"skewX("+c+r),!function(t,n,e,r,o,u){if(t!==e||n!==r){var a=o.push(i(o)+"scale(",null,",",null,")");u.push({i:a-4,x:tx(t,e)},{i:a-2,x:tx(n,r)})}else(1!==e||1!==r)&&o.push(i(o)+"scale("+e+","+r+")")}(o.scaleX,o.scaleY,u.scaleX,u.scaleY,f,h),o=u=null,function(t){for(var n,e=-1,r=h.length;++e<r;)f[(n=h[e]).i]=n.x(t);return f.join("")}}}var tM=tk(function(t){let n=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return n.isIdentity?tb:tN(n.a,n.b,n.c,n.d,n.e,n.f)},"px, ","px)","deg)"),tA=tk(function(t){return null==t?tb:(l||(l=document.createElementNS("http://www.w3.org/2000/svg","g")),l.setAttribute("transform",t),t=l.transform.baseVal.consolidate())?tN((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):tb},", ",")",")");function tz(t,n,e){var r=t._id;return t.each(function(){var t=tg(this,r);(t.value||(t.value={}))[n]=e.apply(this,arguments)}),function(t){return t_(t,r).value[n]}}function tE(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function tS(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function t$(){}var tT="\\s*([+-]?\\d+)\\s*",tq="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",tC="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",tP=/^#([0-9a-f]{3,8})$/,tX=RegExp(`^rgb\\(${tT},${tT},${tT}\\)$`),tY=RegExp(`^rgb\\(${tC},${tC},${tC}\\)$`),tj=RegExp(`^rgba\\(${tT},${tT},${tT},${tq}\\)$`),tO=RegExp(`^rgba\\(${tC},${tC},${tC},${tq}\\)$`),tR=RegExp(`^hsl\\(${tq},${tC},${tC}\\)$`),tD=RegExp(`^hsla\\(${tq},${tC},${tC},${tq}\\)$`),tI={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function tV(){return this.rgb().formatHex()}function tB(){return this.rgb().formatRgb()}function tH(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=tP.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?tL(n):3===e?new tU(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?tG(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?tG(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=tX.exec(t))?new tU(n[1],n[2],n[3],1):(n=tY.exec(t))?new tU(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=tj.exec(t))?tG(n[1],n[2],n[3],n[4]):(n=tO.exec(t))?tG(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=tR.exec(t))?t0(n[1],n[2]/100,n[3]/100,1):(n=tD.exec(t))?t0(n[1],n[2]/100,n[3]/100,n[4]):tI.hasOwnProperty(t)?tL(tI[t]):"transparent"===t?new tU(NaN,NaN,NaN,0):null}function tL(t){return new tU(t>>16&255,t>>8&255,255&t,1)}function tG(t,n,e,r){return r<=0&&(t=n=e=NaN),new tU(t,n,e,r)}function tK(t,n,e,r){var i;return 1==arguments.length?((i=t)instanceof t$||(i=tH(i)),i)?new tU((i=i.rgb()).r,i.g,i.b,i.opacity):new tU:new tU(t,n,e,null==r?1:r)}function tU(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function tF(){return`#${tZ(this.r)}${tZ(this.g)}${tZ(this.b)}`}function tJ(){let t=tQ(this.opacity);return`${1===t?"rgb(":"rgba("}${tW(this.r)}, ${tW(this.g)}, ${tW(this.b)}${1===t?")":`, ${t})`}`}function tQ(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function tW(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function tZ(t){return((t=tW(t))<16?"0":"")+t.toString(16)}function t0(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new t2(t,n,e,r)}function t1(t){if(t instanceof t2)return new t2(t.h,t.s,t.l,t.opacity);if(t instanceof t$||(t=tH(t)),!t)return new t2;if(t instanceof t2)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),o=Math.max(n,e,r),u=NaN,a=o-i,s=(o+i)/2;return a?(u=n===o?(e-r)/a+(e<r)*6:e===o?(r-n)/a+2:(n-e)/a+4,a/=s<.5?o+i:2-o-i,u*=60):a=s>0&&s<1?0:u,new t2(u,a,s,t.opacity)}function t2(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function t5(t){return(t=(t||0)%360)<0?t+360:t}function t3(t){return Math.max(0,Math.min(1,t||0))}function t8(t,n,e){return(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)*255}function t4(t,n,e,r,i){var o=t*t,u=o*t;return((1-3*t+3*o-u)*n+(4-6*o+3*u)*e+(1+3*t+3*o-3*u)*r+u*i)/6}tE(t$,tH,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:tV,formatHex:tV,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return t1(this).formatHsl()},formatRgb:tB,toString:tB}),tE(tU,tK,tS(t$,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new tU(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tU(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new tU(tW(this.r),tW(this.g),tW(this.b),tQ(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:tF,formatHex:tF,formatHex8:function(){return`#${tZ(this.r)}${tZ(this.g)}${tZ(this.b)}${tZ((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:tJ,toString:tJ})),tE(t2,function(t,n,e,r){return 1==arguments.length?t1(t):new t2(t,n,e,null==r?1:r)},tS(t$,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new t2(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new t2(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new tU(t8(t>=240?t-240:t+120,i,r),t8(t,i,r),t8(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new t2(t5(this.h),t3(this.s),t3(this.l),tQ(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=tQ(this.opacity);return`${1===t?"hsl(":"hsla("}${t5(this.h)}, ${100*t3(this.s)}%, ${100*t3(this.l)}%${1===t?")":`, ${t})`}`}}));let t6=t=>()=>t;function t9(t,n){var e,r,i=n-t;return i?(e=t,r=i,function(t){return e+t*r}):t6(isNaN(t)?n:t)}let t7=function t(n){var e,r=1==(e=+n)?t9:function(t,n){var r,i,o;return n-t?(r=t,i=n,r=Math.pow(r,o=e),i=Math.pow(i,o)-r,o=1/o,function(t){return Math.pow(r+t*i,o)}):t6(isNaN(t)?n:t)};function i(t,n){var e=r((t=tK(t)).r,(n=tK(n)).r),i=r(t.g,n.g),o=r(t.b,n.b),u=t9(t.opacity,n.opacity);return function(n){return t.r=e(n),t.g=i(n),t.b=o(n),t.opacity=u(n),t+""}}return i.gamma=t,i}(1);function nt(t){return function(n){var e,r,i=n.length,o=Array(i),u=Array(i),a=Array(i);for(e=0;e<i;++e)r=tK(n[e]),o[e]=r.r||0,u[e]=r.g||0,a[e]=r.b||0;return o=t(o),u=t(u),a=t(a),r.opacity=1,function(t){return r.r=o(t),r.g=u(t),r.b=a(t),r+""}}}nt(function(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),i=t[r],o=t[r+1],u=r>0?t[r-1]:2*i-o,a=r<n-1?t[r+2]:2*o-i;return t4((e-r/n)*n,u,i,o,a)}}),nt(function(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),i=t[(r+n-1)%n],o=t[r%n],u=t[(r+1)%n],a=t[(r+2)%n];return t4((e-r/n)*n,i,o,u,a)}});var nn=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ne=RegExp(nn.source,"g");function nr(t,n){var e;return("number"==typeof n?tx:n instanceof tH?t7:(e=tH(n))?(n=e,t7):function(t,n){var e,r,i,o,u,a=nn.lastIndex=ne.lastIndex=0,s=-1,l=[],c=[];for(t+="",n+="";(i=nn.exec(t))&&(o=ne.exec(n));)(u=o.index)>a&&(u=n.slice(a,u),l[s]?l[s]+=u:l[++s]=u),(i=i[0])===(o=o[0])?l[s]?l[s]+=o:l[++s]=o:(l[++s]=null,c.push({i:s,x:tx(i,o)})),a=ne.lastIndex;return a<n.length&&(u=n.slice(a),l[s]?l[s]+=u:l[++s]=u),l.length<2?c[0]?(e=c[0].x,function(t){return e(t)+""}):(r=n,function(){return r}):(n=c.length,function(t){for(var e,r=0;r<n;++r)l[(e=c[r]).i]=e.x(t);return l.join("")})})(t,n)}var ni=H.prototype.constructor;function no(t){return function(){this.style.removeProperty(t)}}var nu=0;function na(t,n,e,r){this._groups=t,this._parents=n,this._name=e,this._id=r}var ns=H.prototype;na.prototype=(function(t){return H().transition(t)}).prototype={constructor:na,select:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=i(t));for(var r=this._groups,o=r.length,u=Array(o),a=0;a<o;++a)for(var s,l,c=r[a],f=c.length,h=u[a]=Array(f),p=0;p<f;++p)(s=c[p])&&(l=t.call(s,s.__data__,p,c))&&("__data__"in s&&(l.__data__=s.__data__),h[p]=l,td(h[p],n,e,p,h,t_(s,e)));return new na(u,this._parents,n,e)},selectAll:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=u(t));for(var r=this._groups,i=r.length,o=[],a=[],s=0;s<i;++s)for(var l,c=r[s],f=c.length,h=0;h<f;++h)if(l=c[h]){for(var p,v=t.call(l,l.__data__,h,c),d=t_(l,e),y=0,g=v.length;y<g;++y)(p=v[y])&&td(p,n,e,y,v,d);o.push(v),a.push(l)}return new na(o,a,n,e)},selectChild:ns.selectChild,selectChildren:ns.selectChildren,filter:function(t){"function"!=typeof t&&(t=a(t));for(var n=this._groups,e=n.length,r=Array(e),i=0;i<e;++i)for(var o,u=n[i],s=u.length,l=r[i]=[],c=0;c<s;++c)(o=u[c])&&t.call(o,o.__data__,c,u)&&l.push(o);return new na(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw Error();for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),u=Array(r),a=0;a<o;++a)for(var s,l=n[a],c=e[a],f=l.length,h=u[a]=Array(f),p=0;p<f;++p)(s=l[p]||c[p])&&(h[p]=s);for(;a<r;++a)u[a]=n[a];return new na(u,this._parents,this._name,this._id)},selection:function(){return new ni(this._groups,this._parents)},transition:function(){for(var t=this._name,n=this._id,e=++nu,r=this._groups,i=r.length,o=0;o<i;++o)for(var u,a=r[o],s=a.length,l=0;l<s;++l)if(u=a[l]){var c=t_(u,n);td(u,t,e,l,a,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new na(r,this._parents,t,e)},call:ns.call,nodes:ns.nodes,node:ns.node,size:ns.size,empty:ns.empty,each:ns.each,on:function(t,n){var e,r,i,o=this._id;return arguments.length<2?t_(this.node(),o).on.on(t):this.each((i=(t+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||"start"===t})?ty:tg,function(){var u=i(this,o),a=u.on;a!==e&&(r=(e=a).copy()).on(t,n),u.on=r}))},attr:function(t,n){var e=b(t),r="transform"===e?tA:nr;return this.attrTween(t,"function"==typeof n?(e.local?function(t,n,e){var r,i,o;return function(){var u,a,s=e(this);return null==s?void this.removeAttributeNS(t.space,t.local):(u=this.getAttributeNS(t.space,t.local))===(a=s+"")?null:u===r&&a===i?o:(i=a,o=n(r=u,s))}}:function(t,n,e){var r,i,o;return function(){var u,a,s=e(this);return null==s?void this.removeAttribute(t):(u=this.getAttribute(t))===(a=s+"")?null:u===r&&a===i?o:(i=a,o=n(r=u,s))}})(e,r,tz(this,"attr."+t,n)):null==n?(e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(e):(e.local?function(t,n,e){var r,i,o=e+"";return function(){var u=this.getAttributeNS(t.space,t.local);return u===o?null:u===r?i:i=n(r=u,e)}}:function(t,n,e){var r,i,o=e+"";return function(){var u=this.getAttribute(t);return u===o?null:u===r?i:i=n(r=u,e)}})(e,r,n))},attrTween:function(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(null==n)return this.tween(e,null);if("function"!=typeof n)throw Error();var r=b(t);return this.tween(e,(r.local?function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(n){this.setAttributeNS(t.space,t.local,i.call(this,n))}),e}return i._value=n,i}:function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(n){this.setAttribute(t,i.call(this,n))}),e}return i._value=n,i})(r,n))},style:function(t,n,e){var r,i,o,u,a,s,l,c,f,h,p,v,d,y,g,_,m,x,w,b,N,M="transform"==(t+="")?tM:nr;return null==n?this.styleTween(t,(r=t,function(){var t=k(this,r),n=(this.style.removeProperty(r),k(this,r));return t===n?null:t===i&&n===o?u:u=M(i=t,o=n)})).on("end.style."+t,no(t)):"function"==typeof n?this.styleTween(t,(a=t,s=tz(this,"style."+t,n),function(){var t=k(this,a),n=s(this),e=n+"";return null==n&&(this.style.removeProperty(a),e=n=k(this,a)),t===e?null:t===l&&e===c?f:(c=e,f=M(l=t,n))})).each((h=this._id,m="end."+(_="style."+(p=t)),function(){var t=tg(this,h),n=t.on,e=null==t.value[_]?g||(g=no(p)):void 0;(n!==v||y!==e)&&(d=(v=n).copy()).on(m,y=e),t.on=d})):this.styleTween(t,(x=t,N=n+"",function(){var t=k(this,x);return t===N?null:t===w?b:b=M(w=t,n)}),e).on("end.style."+t,null)},styleTween:function(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==n)return this.tween(r,null);if("function"!=typeof n)throw Error();return this.tween(r,function(t,n,e){var r,i;function o(){var o=n.apply(this,arguments);return o!==i&&(r=(i=o)&&function(n){this.style.setProperty(t,o.call(this,n),e)}),r}return o._value=n,o}(t,n,null==e?"":e))},text:function(t){var n,e;return this.tween("text","function"==typeof t?(n=tz(this,"text",t),function(){var t=n(this);this.textContent=null==t?"":t}):(e=null==t?"":t+"",function(){this.textContent=e}))},textTween:function(t){var n="text";if(arguments.length<1)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw Error();return this.tween(n,function(t){var n,e;function r(){var r=t.apply(this,arguments);return r!==e&&(n=(e=r)&&function(t){this.textContent=r.call(this,t)}),n}return r._value=t,r}(t))},remove:function(){var t;return this.on("end.remove",(t=this._id,function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}))},tween:function(t,n){var e=this._id;if(t+="",arguments.length<2){for(var r,i=t_(this.node(),e).tween,o=0,u=i.length;o<u;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==n?function(t,n){var e,r;return function(){var i=tg(this,t),o=i.tween;if(o!==e){r=e=o;for(var u=0,a=r.length;u<a;++u)if(r[u].name===n){(r=r.slice()).splice(u,1);break}}i.tween=r}}:function(t,n,e){var r,i;if("function"!=typeof e)throw Error();return function(){var o=tg(this,t),u=o.tween;if(u!==r){i=(r=u).slice();for(var a={name:n,value:e},s=0,l=i.length;s<l;++s)if(i[s].name===n){i[s]=a;break}s===l&&i.push(a)}o.tween=i}})(e,t,n))},delay:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){ty(this,t).delay=+n.apply(this,arguments)}}:function(t,n){return n*=1,function(){ty(this,t).delay=n}})(n,t)):t_(this.node(),n).delay},duration:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){tg(this,t).duration=+n.apply(this,arguments)}}:function(t,n){return n*=1,function(){tg(this,t).duration=n}})(n,t)):t_(this.node(),n).duration},ease:function(t){var n=this._id;return arguments.length?this.each(function(t,n){if("function"!=typeof n)throw Error();return function(){tg(this,t).ease=n}}(n,t)):t_(this.node(),n).ease},easeVarying:function(t){var n;if("function"!=typeof t)throw Error();return this.each((n=this._id,function(){var e=t.apply(this,arguments);if("function"!=typeof e)throw Error();tg(this,n).ease=e}))},end:function(){var t,n,e=this,r=e._id,i=e.size();return new Promise(function(o,u){var a={value:u},s={value:function(){0==--i&&o()}};e.each(function(){var e=tg(this,r),i=e.on;i!==t&&((n=(t=i).copy())._.cancel.push(a),n._.interrupt.push(a),n._.end.push(s)),e.on=n}),0===i&&o()})},[Symbol.iterator]:ns[Symbol.iterator]};var nl={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};H.prototype.interrupt=function(t){return this.each(function(){tm(this,t)})},H.prototype.transition=function(t){var n,e;t instanceof na?(n=t._id,t=t._name):(n=++nu,(e=nl).time=to(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var u,a=r[o],s=a.length,l=0;l<s;++l)(u=a[l])&&td(u,t,n,l,a,e||function(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))throw Error(`transition ${n} not found`);return e}(u,n));return new na(r,this._parents,t,n)};var nc={name:"drag"},nf={name:"space"},nh={name:"handle"},np={name:"center"};let{abs:nv,max:nd,min:ny}=Math;function ng(t){return[+t[0],+t[1]]}function n_(t){return[ng(t[0]),ng(t[1])]}var nm={name:"x",handles:["w","e"].map(nA),input:function(t,n){return null==t?null:[[+t[0],n[0][1]],[+t[1],n[1][1]]]},output:function(t){return t&&[t[0][0],t[1][0]]}},nx={name:"y",handles:["n","s"].map(nA),input:function(t,n){return null==t?null:[[n[0][0],+t[0]],[n[1][0],+t[1]]]},output:function(t){return t&&[t[0][1],t[1][1]]}},nw=(["n","w","e","s","nw","ne","sw","se"].map(nA),{overlay:"crosshair",selection:"move",n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"}),nb={e:"w",w:"e",nw:"ne",ne:"nw",se:"sw",sw:"se"},nN={n:"s",s:"n",nw:"sw",ne:"se",se:"ne",sw:"nw"},nk={overlay:1,selection:1,n:null,e:1,s:null,w:-1,nw:-1,ne:1,se:1,sw:-1},nM={overlay:1,selection:1,n:-1,e:null,s:1,w:null,nw:-1,ne:-1,se:1,sw:1};function nA(t){return{type:t}}function nz(t){return!t.ctrlKey&&!t.button}function nE(){var t=this.ownerSVGElement||this;return t.hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]}function nS(){return navigator.maxTouchPoints||"ontouchstart"in this}function n$(t){for(;!t.__brush;)if(!(t=t.parentNode))return;return t.__brush}function nT(t){return"string"==typeof t?new B([[document.querySelector(t)]],[document.documentElement]):new B([[t]],V)}function nq(t,n){if(t=function(t){let n;for(;n=t.sourceEvent;)t=n;return t}(t),void 0===n&&(n=t.currentTarget),n){var e=n.ownerSVGElement||n;if(e.createSVGPoint){var r=e.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(n.getScreenCTM().inverse())).x,r.y]}if(n.getBoundingClientRect){var i=n.getBoundingClientRect();return[t.clientX-i.left-n.clientLeft,t.clientY-i.top-n.clientTop]}}return[t.pageX,t.pageY]}let nC={passive:!1},nP={capture:!0,passive:!1};function nX(t){t.stopImmediatePropagation()}function nY(t){t.preventDefault(),t.stopImmediatePropagation()}function nj(t){var n=t.document.documentElement,e=nT(t).on("dragstart.drag",nY,nP);"onselectstart"in n?e.on("selectstart.drag",nY,nP):(n.__noselect=n.style.MozUserSelect,n.style.MozUserSelect="none")}function nO(t,n){var e=t.document.documentElement,r=nT(t).on("dragstart.drag",null);n&&(r.on("click.drag",nY,nP),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in e?r.on("selectstart.drag",null):(e.style.MozUserSelect=e.__noselect,delete e.__noselect)}let nR=t=>()=>t;function nD(t,{sourceEvent:n,subject:e,target:r,identifier:i,active:o,x:u,y:a,dx:s,dy:l,dispatch:c}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},subject:{value:e,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:i,enumerable:!0,configurable:!0},active:{value:o,enumerable:!0,configurable:!0},x:{value:u,enumerable:!0,configurable:!0},y:{value:a,enumerable:!0,configurable:!0},dx:{value:s,enumerable:!0,configurable:!0},dy:{value:l,enumerable:!0,configurable:!0},_:{value:c}})}function nI(t){return!t.ctrlKey&&!t.button}function nV(){return this.parentNode}function nB(t,n){return null==n?{x:t.x,y:t.y}:n}function nH(){return navigator.maxTouchPoints||"ontouchstart"in this}function nL(){var t,n,e,r,i=nI,o=nV,u=nB,a=nH,s={},l=G("start","drag","end"),c=0,f=0;function h(t){t.on("mousedown.drag",p).filter(a).on("touchstart.drag",y).on("touchmove.drag",g,nC).on("touchend.drag touchcancel.drag",_).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function p(u,a){if(!r&&i.call(this,u,a)){var s=m(this,o.call(this,u,a),u,a,"mouse");s&&(nT(u.view).on("mousemove.drag",v,nP).on("mouseup.drag",d,nP),nj(u.view),nX(u),e=!1,t=u.clientX,n=u.clientY,s("start",u))}}function v(r){if(nY(r),!e){var i=r.clientX-t,o=r.clientY-n;e=i*i+o*o>f}s.mouse("drag",r)}function d(t){nT(t.view).on("mousemove.drag mouseup.drag",null),nO(t.view,e),nY(t),s.mouse("end",t)}function y(t,n){if(i.call(this,t,n)){var e,r,u=t.changedTouches,a=o.call(this,t,n),s=u.length;for(e=0;e<s;++e)(r=m(this,a,t,n,u[e].identifier,u[e]))&&(nX(t),r("start",t,u[e]))}}function g(t){var n,e,r=t.changedTouches,i=r.length;for(n=0;n<i;++n)(e=s[r[n].identifier])&&(nY(t),e("drag",t,r[n]))}function _(t){var n,e,i=t.changedTouches,o=i.length;for(r&&clearTimeout(r),r=setTimeout(function(){r=null},500),n=0;n<o;++n)(e=s[i[n].identifier])&&(nX(t),e("end",t,i[n]))}function m(t,n,e,r,i,o){var a,f,p,v=l.copy(),d=nq(o||e,n);if(null!=(p=u.call(t,new nD("beforestart",{sourceEvent:e,target:h,identifier:i,active:c,x:d[0],y:d[1],dx:0,dy:0,dispatch:v}),r)))return a=p.x-d[0]||0,f=p.y-d[1]||0,function e(o,u,l){var y,g=d;switch(o){case"start":s[i]=e,y=c++;break;case"end":delete s[i],--c;case"drag":d=nq(l||u,n),y=c}v.call(o,t,new nD(o,{sourceEvent:u,subject:p,target:h,identifier:i,active:y,x:d[0]+a,y:d[1]+f,dx:d[0]-g[0],dy:d[1]-g[1],dispatch:v}),r)}}return h.filter=function(t){return arguments.length?(i="function"==typeof t?t:nR(!!t),h):i},h.container=function(t){return arguments.length?(o="function"==typeof t?t:nR(t),h):o},h.subject=function(t){return arguments.length?(u="function"==typeof t?t:nR(t),h):u},h.touchable=function(t){return arguments.length?(a="function"==typeof t?t:nR(!!t),h):a},h.on=function(){var t=l.on.apply(l,arguments);return t===l?h:t},h.clickDistance=function(t){return arguments.length?(f=(t*=1)*t,h):Math.sqrt(f)},h}function nG(t,n){var e,r=1;function i(){var i,o,u=e.length,a=0,s=0;for(i=0;i<u;++i)a+=(o=e[i]).x,s+=o.y;for(a=(a/u-t)*r,s=(s/u-n)*r,i=0;i<u;++i)o=e[i],o.x-=a,o.y-=s}return null==t&&(t=0),null==n&&(n=0),i.initialize=function(t){e=t},i.x=function(n){return arguments.length?(t=+n,i):t},i.y=function(t){return arguments.length?(n=+t,i):n},i.strength=function(t){return arguments.length?(r=+t,i):r},i}function nK(t,n,e,r){if(isNaN(n)||isNaN(e))return t;var i,o,u,a,s,l,c,f,h,p=t._root,v={data:r},d=t._x0,y=t._y0,g=t._x1,_=t._y1;if(!p)return t._root=v,t;for(;p.length;)if((l=n>=(o=(d+g)/2))?d=o:g=o,(c=e>=(u=(y+_)/2))?y=u:_=u,i=p,!(p=p[f=c<<1|l]))return i[f]=v,t;if(a=+t._x.call(null,p.data),s=+t._y.call(null,p.data),n===a&&e===s)return v.next=p,i?i[f]=v:t._root=v,t;do i=i?i[f]=[,,,,]:t._root=[,,,,],(l=n>=(o=(d+g)/2))?d=o:g=o,(c=e>=(u=(y+_)/2))?y=u:_=u;while((f=c<<1|l)==(h=(s>=u)<<1|a>=o));return i[h]=p,i[f]=v,t}function nU(t,n,e,r,i){this.node=t,this.x0=n,this.y0=e,this.x1=r,this.y1=i}function nF(t){return t[0]}function nJ(t){return t[1]}function nQ(t,n,e){var r=new nW(null==n?nF:n,null==e?nJ:e,NaN,NaN,NaN,NaN);return null==t?r:r.addAll(t)}function nW(t,n,e,r,i,o){this._x=t,this._y=n,this._x0=e,this._y0=r,this._x1=i,this._y1=o,this._root=void 0}function nZ(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}nD.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t};var n0=nQ.prototype=nW.prototype;function n1(t){return function(){return t}}function n2(t){return(t()-.5)*1e-6}function n5(t){return t.x+t.vx}function n3(t){return t.y+t.vy}function n8(t){var n,e,r,i=1,o=1;function u(){for(var t,u,s,l,c,f,h,p=n.length,v=0;v<o;++v)for(t=0,u=nQ(n,n5,n3).visitAfter(a);t<p;++t)h=(f=e[(s=n[t]).index])*f,l=s.x+s.vx,c=s.y+s.vy,u.visit(d);function d(t,n,e,o,u){var a=t.data,p=t.r,v=f+p;if(a){if(a.index>s.index){var d=l-a.x-a.vx,y=c-a.y-a.vy,g=d*d+y*y;g<v*v&&(0===d&&(g+=(d=n2(r))*d),0===y&&(g+=(y=n2(r))*y),g=(v-(g=Math.sqrt(g)))/g*i,s.vx+=(d*=g)*(v=(p*=p)/(h+p)),s.vy+=(y*=g)*v,a.vx-=d*(v=1-v),a.vy-=y*v)}return}return n>l+v||o<l-v||e>c+v||u<c-v}}function a(t){if(t.data)return t.r=e[t.data.index];for(var n=t.r=0;n<4;++n)t[n]&&t[n].r>t.r&&(t.r=t[n].r)}function s(){if(n){var r,i,o=n.length;for(r=0,e=Array(o);r<o;++r)e[(i=n[r]).index]=+t(i,r,n)}}return"function"!=typeof t&&(t=n1(null==t?1:+t)),u.initialize=function(t,e){n=t,r=e,s()},u.iterations=function(t){return arguments.length?(o=+t,u):o},u.strength=function(t){return arguments.length?(i=+t,u):i},u.radius=function(n){return arguments.length?(t="function"==typeof n?n:n1(+n),s(),u):t},u}function n4(t){return t.index}function n6(t,n){var e=t.get(n);if(!e)throw Error("node not found: "+n);return e}function n9(t){var n,e,r,i,o,u,a=n4,s=function(t){return 1/Math.min(i[t.source.index],i[t.target.index])},l=n1(30),c=1;function f(r){for(var i=0,a=t.length;i<c;++i)for(var s,l,f,h,p,v,d,y=0;y<a;++y)l=(s=t[y]).source,v=((v=Math.sqrt((h=(f=s.target).x+f.vx-l.x-l.vx||n2(u))*h+(p=f.y+f.vy-l.y-l.vy||n2(u))*p))-e[y])/v*r*n[y],h*=v,p*=v,f.vx-=h*(d=o[y]),f.vy-=p*d,l.vx+=h*(d=1-d),l.vy+=p*d}function h(){if(r){var u,s,l=r.length,c=t.length,f=new Map(r.map((t,n)=>[a(t,n,r),t]));for(u=0,i=Array(l);u<c;++u)(s=t[u]).index=u,"object"!=typeof s.source&&(s.source=n6(f,s.source)),"object"!=typeof s.target&&(s.target=n6(f,s.target)),i[s.source.index]=(i[s.source.index]||0)+1,i[s.target.index]=(i[s.target.index]||0)+1;for(u=0,o=Array(c);u<c;++u)s=t[u],o[u]=i[s.source.index]/(i[s.source.index]+i[s.target.index]);n=Array(c),p(),e=Array(c),v()}}function p(){if(r)for(var e=0,i=t.length;e<i;++e)n[e]=+s(t[e],e,t)}function v(){if(r)for(var n=0,i=t.length;n<i;++n)e[n]=+l(t[n],n,t)}return null==t&&(t=[]),f.initialize=function(t,n){r=t,u=n,h()},f.links=function(n){return arguments.length?(t=n,h(),f):t},f.id=function(t){return arguments.length?(a=t,f):a},f.iterations=function(t){return arguments.length?(c=+t,f):c},f.strength=function(t){return arguments.length?(s="function"==typeof t?t:n1(+t),p(),f):s},f.distance=function(t){return arguments.length?(l="function"==typeof t?t:n1(+t),v(),f):l},f}function n7(t){return t.x}function et(t){return t.y}n0.copy=function(){var t,n,e=new nW(this._x,this._y,this._x0,this._y0,this._x1,this._y1),r=this._root;if(!r)return e;if(!r.length)return e._root=nZ(r),e;for(t=[{source:r,target:e._root=[,,,,]}];r=t.pop();)for(var i=0;i<4;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=[,,,,]}):r.target[i]=nZ(n));return e},n0.add=function(t){let n=+this._x.call(null,t),e=+this._y.call(null,t);return nK(this.cover(n,e),n,e,t)},n0.addAll=function(t){var n,e,r,i,o=t.length,u=Array(o),a=Array(o),s=1/0,l=1/0,c=-1/0,f=-1/0;for(e=0;e<o;++e)!(isNaN(r=+this._x.call(null,n=t[e]))||isNaN(i=+this._y.call(null,n)))&&(u[e]=r,a[e]=i,r<s&&(s=r),r>c&&(c=r),i<l&&(l=i),i>f&&(f=i));if(s>c||l>f)return this;for(this.cover(s,l).cover(c,f),e=0;e<o;++e)nK(this,u[e],a[e],t[e]);return this},n0.cover=function(t,n){if(isNaN(t*=1)||isNaN(n*=1))return this;var e=this._x0,r=this._y0,i=this._x1,o=this._y1;if(isNaN(e))i=(e=Math.floor(t))+1,o=(r=Math.floor(n))+1;else{for(var u,a,s=i-e||1,l=this._root;e>t||t>=i||r>n||n>=o;)switch(a=(n<r)<<1|t<e,(u=[,,,,])[a]=l,l=u,s*=2,a){case 0:i=e+s,o=r+s;break;case 1:e=i-s,o=r+s;break;case 2:i=e+s,r=o-s;break;case 3:e=i-s,r=o-s}this._root&&this._root.length&&(this._root=l)}return this._x0=e,this._y0=r,this._x1=i,this._y1=o,this},n0.data=function(){var t=[];return this.visit(function(n){if(!n.length)do t.push(n.data);while(n=n.next)}),t},n0.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]},n0.find=function(t,n,e){var r,i,o,u,a,s,l,c=this._x0,f=this._y0,h=this._x1,p=this._y1,v=[],d=this._root;for(d&&v.push(new nU(d,c,f,h,p)),null==e?e=1/0:(c=t-e,f=n-e,h=t+e,p=n+e,e*=e);s=v.pop();)if((d=s.node)&&!((i=s.x0)>h)&&!((o=s.y0)>p)&&!((u=s.x1)<c)&&!((a=s.y1)<f)){if(d.length){var y=(i+u)/2,g=(o+a)/2;v.push(new nU(d[3],y,g,u,a),new nU(d[2],i,g,y,a),new nU(d[1],y,o,u,g),new nU(d[0],i,o,y,g)),(l=(n>=g)<<1|t>=y)&&(s=v[v.length-1],v[v.length-1]=v[v.length-1-l],v[v.length-1-l]=s)}else{var _=t-+this._x.call(null,d.data),m=n-+this._y.call(null,d.data),x=_*_+m*m;if(x<e){var w=Math.sqrt(e=x);c=t-w,f=n-w,h=t+w,p=n+w,r=d.data}}}return r},n0.remove=function(t){if(isNaN(o=+this._x.call(null,t))||isNaN(u=+this._y.call(null,t)))return this;var n,e,r,i,o,u,a,s,l,c,f,h,p=this._root,v=this._x0,d=this._y0,y=this._x1,g=this._y1;if(!p)return this;if(p.length)for(;;){if((l=o>=(a=(v+y)/2))?v=a:y=a,(c=u>=(s=(d+g)/2))?d=s:g=s,n=p,!(p=p[f=c<<1|l]))return this;if(!p.length)break;(n[f+1&3]||n[f+2&3]||n[f+3&3])&&(e=n,h=f)}for(;p.data!==t;)if(r=p,!(p=p.next))return this;return((i=p.next)&&delete p.next,r)?i?r.next=i:delete r.next:n?(i?n[f]=i:delete n[f],(p=n[0]||n[1]||n[2]||n[3])&&p===(n[3]||n[2]||n[1]||n[0])&&!p.length&&(e?e[h]=p:this._root=p)):this._root=i,this},n0.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},n0.root=function(){return this._root},n0.size=function(){var t=0;return this.visit(function(n){if(!n.length)do++t;while(n=n.next)}),t},n0.visit=function(t){var n,e,r,i,o,u,a=[],s=this._root;for(s&&a.push(new nU(s,this._x0,this._y0,this._x1,this._y1));n=a.pop();)if(!t(s=n.node,r=n.x0,i=n.y0,o=n.x1,u=n.y1)&&s.length){var l=(r+o)/2,c=(i+u)/2;(e=s[3])&&a.push(new nU(e,l,c,o,u)),(e=s[2])&&a.push(new nU(e,r,c,l,u)),(e=s[1])&&a.push(new nU(e,l,i,o,c)),(e=s[0])&&a.push(new nU(e,r,i,l,c))}return this},n0.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new nU(this._root,this._x0,this._y0,this._x1,this._y1));n=e.pop();){var i=n.node;if(i.length){var o,u=n.x0,a=n.y0,s=n.x1,l=n.y1,c=(u+s)/2,f=(a+l)/2;(o=i[0])&&e.push(new nU(o,u,a,c,f)),(o=i[1])&&e.push(new nU(o,c,a,s,f)),(o=i[2])&&e.push(new nU(o,u,f,c,l)),(o=i[3])&&e.push(new nU(o,c,f,s,l))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.y0,n.x1,n.y1);return this},n0.x=function(t){return arguments.length?(this._x=t,this):this._x},n0.y=function(t){return arguments.length?(this._y=t,this):this._y};var en=Math.PI*(3-Math.sqrt(5));function ee(t){let n;var e,r=1,i=.001,o=1-Math.pow(.001,1/300),u=0,a=.6,s=new Map,l=ts(h),c=G("tick","end"),f=(n=1,()=>(n=(1664525*n+0x3c6ef35f)%0x100000000)/0x100000000);function h(){p(),c.call("tick",e),r<i&&(l.stop(),c.call("end",e))}function p(n){var i,l,c=t.length;void 0===n&&(n=1);for(var f=0;f<n;++f)for(r+=(u-r)*o,s.forEach(function(t){t(r)}),i=0;i<c;++i)null==(l=t[i]).fx?l.x+=l.vx*=a:(l.x=l.fx,l.vx=0),null==l.fy?l.y+=l.vy*=a:(l.y=l.fy,l.vy=0);return e}function v(){for(var n,e=0,r=t.length;e<r;++e){if((n=t[e]).index=e,null!=n.fx&&(n.x=n.fx),null!=n.fy&&(n.y=n.fy),isNaN(n.x)||isNaN(n.y)){var i=10*Math.sqrt(.5+e),o=e*en;n.x=i*Math.cos(o),n.y=i*Math.sin(o)}(isNaN(n.vx)||isNaN(n.vy))&&(n.vx=n.vy=0)}}function d(n){return n.initialize&&n.initialize(t,f),n}return null==t&&(t=[]),v(),e={tick:p,restart:function(){return l.restart(h),e},stop:function(){return l.stop(),e},nodes:function(n){return arguments.length?(t=n,v(),s.forEach(d),e):t},alpha:function(t){return arguments.length?(r=+t,e):r},alphaMin:function(t){return arguments.length?(i=+t,e):i},alphaDecay:function(t){return arguments.length?(o=+t,e):+o},alphaTarget:function(t){return arguments.length?(u=+t,e):u},velocityDecay:function(t){return arguments.length?(a=1-t,e):1-a},randomSource:function(t){return arguments.length?(f=t,s.forEach(d),e):f},force:function(t,n){return arguments.length>1?(null==n?s.delete(t):s.set(t,d(n)),e):s.get(t)},find:function(n,e,r){var i,o,u,a,s,l=0,c=t.length;for(null==r?r=1/0:r*=r,l=0;l<c;++l)(u=(i=n-(a=t[l]).x)*i+(o=e-a.y)*o)<r&&(s=a,r=u);return s},on:function(t,n){return arguments.length>1?(c.on(t,n),e):c.on(t)}}}function er(){var t,n,e,r,i,o=n1(-30),u=1,a=1/0,s=.81;function l(e){var i,o=t.length,u=nQ(t,n7,et).visitAfter(f);for(r=e,i=0;i<o;++i)n=t[i],u.visit(h)}function c(){if(t){var n,e,r=t.length;for(n=0,i=Array(r);n<r;++n)i[(e=t[n]).index]=+o(e,n,t)}}function f(t){var n,e,r,o,u,a=0,s=0;if(t.length){for(r=o=u=0;u<4;++u)(n=t[u])&&(e=Math.abs(n.value))&&(a+=n.value,s+=e,r+=e*n.x,o+=e*n.y);t.x=r/s,t.y=o/s}else{(n=t).x=n.data.x,n.y=n.data.y;do a+=i[n.data.index];while(n=n.next)}t.value=a}function h(t,o,l,c){if(!t.value)return!0;var f=t.x-n.x,h=t.y-n.y,p=c-o,v=f*f+h*h;if(p*p/s<v)return v<a&&(0===f&&(v+=(f=n2(e))*f),0===h&&(v+=(h=n2(e))*h),v<u&&(v=Math.sqrt(u*v)),n.vx+=f*t.value*r/v,n.vy+=h*t.value*r/v),!0;if(!t.length&&!(v>=a)){(t.data!==n||t.next)&&(0===f&&(v+=(f=n2(e))*f),0===h&&(v+=(h=n2(e))*h),v<u&&(v=Math.sqrt(u*v)));do t.data!==n&&(p=i[t.data.index]*r/v,n.vx+=f*p,n.vy+=h*p);while(t=t.next)}}return l.initialize=function(n,r){t=n,e=r,c()},l.strength=function(t){return arguments.length?(o="function"==typeof t?t:n1(+t),c(),l):o},l.distanceMin=function(t){return arguments.length?(u=t*t,l):Math.sqrt(u)},l.distanceMax=function(t){return arguments.length?(a=t*t,l):Math.sqrt(a)},l.theta=function(t){return arguments.length?(s=t*t,l):Math.sqrt(s)},l}function ei(t){var n,e,r,i=n1(.1);function o(t){for(var i,o=0,u=n.length;o<u;++o)i=n[o],i.vx+=(r[o]-i.x)*e[o]*t}function u(){if(n){var o,u=n.length;for(o=0,e=Array(u),r=Array(u);o<u;++o)e[o]=isNaN(r[o]=+t(n[o],o,n))?0:+i(n[o],o,n)}}return"function"!=typeof t&&(t=n1(null==t?0:+t)),o.initialize=function(t){n=t,u()},o.strength=function(t){return arguments.length?(i="function"==typeof t?t:n1(+t),u(),o):i},o.x=function(n){return arguments.length?(t="function"==typeof n?n:n1(+n),u(),o):t},o}function eo(t){var n,e,r,i=n1(.1);function o(t){for(var i,o=0,u=n.length;o<u;++o)i=n[o],i.vy+=(r[o]-i.y)*e[o]*t}function u(){if(n){var o,u=n.length;for(o=0,e=Array(u),r=Array(u);o<u;++o)e[o]=isNaN(r[o]=+t(n[o],o,n))?0:+i(n[o],o,n)}}return"function"!=typeof t&&(t=n1(null==t?0:+t)),o.initialize=function(t){n=t,u()},o.strength=function(t){return arguments.length?(i="function"==typeof t?t:n1(+t),u(),o):i},o.y=function(n){return arguments.length?(t="function"==typeof n?n:n1(+n),u(),o):t},o}function eu(t){return((t=Math.exp(t))+1/t)/2}let ea=function t(n,e,r){function i(t,i){var o,u,a=t[0],s=t[1],l=t[2],c=i[0],f=i[1],h=i[2],p=c-a,v=f-s,d=p*p+v*v;if(d<1e-12)u=Math.log(h/l)/n,o=function(t){return[a+t*p,s+t*v,l*Math.exp(n*t*u)]};else{var y=Math.sqrt(d),g=(h*h-l*l+r*d)/(2*l*e*y),_=(h*h-l*l-r*d)/(2*h*e*y),m=Math.log(Math.sqrt(g*g+1)-g);u=(Math.log(Math.sqrt(_*_+1)-_)-m)/n,o=function(t){var r,i,o=t*u,c=eu(m),f=l/(e*y)*(c*(((r=Math.exp(2*(r=n*o+m)))-1)/(r+1))-((i=Math.exp(i=m))-1/i)/2);return[a+f*p,s+f*v,l*c/eu(n*o+m)]}}return o.duration=1e3*u*n/Math.SQRT2,o}return i.rho=function(n){var e=Math.max(.001,+n),r=e*e;return t(e,r,r*r)},i}(Math.SQRT2,2,4),es=t=>()=>t;function el(t,{sourceEvent:n,target:e,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function ec(t,n,e){this.k=t,this.x=n,this.y=e}ec.prototype={constructor:ec,scale:function(t){return 1===t?this:new ec(this.k*t,this.x,this.y)},translate:function(t,n){return 0===t&0===n?this:new ec(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var ef=new ec(1,0,0);function eh(t){t.stopImmediatePropagation()}function ep(t){t.preventDefault(),t.stopImmediatePropagation()}function ev(t){return(!t.ctrlKey||"wheel"===t.type)&&!t.button}function ed(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function ey(){return this.__zoom||ef}function eg(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function e_(){return navigator.maxTouchPoints||"ontouchstart"in this}function em(t,n,e){var r=t.invertX(n[0][0])-e[0][0],i=t.invertX(n[1][0])-e[1][0],o=t.invertY(n[0][1])-e[0][1],u=t.invertY(n[1][1])-e[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),u>o?(o+u)/2:Math.min(0,o)||Math.max(0,u))}function ex(){var t,n,e,r=ev,i=ed,o=em,u=eg,a=e_,s=[0,1/0],l=[[-1/0,-1/0],[1/0,1/0]],c=250,f=ea,h=G("start","zoom","end"),p=0,v=10;function d(t){t.property("__zoom",ey).on("wheel.zoom",b,{passive:!1}).on("mousedown.zoom",N).on("dblclick.zoom",k).filter(a).on("touchstart.zoom",M).on("touchmove.zoom",A).on("touchend.zoom touchcancel.zoom",z).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function y(t,n){return(n=Math.max(s[0],Math.min(s[1],n)))===t.k?t:new ec(n,t.x,t.y)}function g(t,n,e){var r=n[0]-e[0]*t.k,i=n[1]-e[1]*t.k;return r===t.x&&i===t.y?t:new ec(t.k,r,i)}function _(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function m(t,n,e,r){t.on("start.zoom",function(){x(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){x(this,arguments).event(r).end()}).tween("zoom",function(){var t=arguments,o=x(this,t).event(r),u=i.apply(this,t),a=null==e?_(u):"function"==typeof e?e.apply(this,t):e,s=Math.max(u[1][0]-u[0][0],u[1][1]-u[0][1]),l=this.__zoom,c="function"==typeof n?n.apply(this,t):n,h=f(l.invert(a).concat(s/l.k),c.invert(a).concat(s/c.k));return function(t){if(1===t)t=c;else{var n=h(t),e=s/n[2];t=new ec(e,a[0]-n[0]*e,a[1]-n[1]*e)}o.zoom(null,t)}})}function x(t,n,e){return!e&&t.__zooming||new w(t,n)}function w(t,n){this.that=t,this.args=n,this.active=0,this.sourceEvent=null,this.extent=i.apply(t,n),this.taps=0}function b(t,...n){if(r.apply(this,arguments)){var e=x(this,n).event(t),i=this.__zoom,a=Math.max(s[0],Math.min(s[1],i.k*Math.pow(2,u.apply(this,arguments)))),c=nq(t);if(e.wheel)(e.mouse[0][0]!==c[0]||e.mouse[0][1]!==c[1])&&(e.mouse[1]=i.invert(e.mouse[0]=c)),clearTimeout(e.wheel);else{if(i.k===a)return;e.mouse=[c,i.invert(c)],tm(this),e.start()}ep(t),e.wheel=setTimeout(function(){e.wheel=null,e.end()},150),e.zoom("mouse",o(g(y(i,a),e.mouse[0],e.mouse[1]),e.extent,l))}}function N(t,...n){if(!e&&r.apply(this,arguments)){var i=t.currentTarget,u=x(this,n,!0).event(t),a=nT(t.view).on("mousemove.zoom",function(t){if(ep(t),!u.moved){var n=t.clientX-c,e=t.clientY-f;u.moved=n*n+e*e>p}u.event(t).zoom("mouse",o(g(u.that.__zoom,u.mouse[0]=nq(t,i),u.mouse[1]),u.extent,l))},!0).on("mouseup.zoom",function(t){a.on("mousemove.zoom mouseup.zoom",null),nO(t.view,u.moved),ep(t),u.event(t).end()},!0),s=nq(t,i),c=t.clientX,f=t.clientY;nj(t.view),eh(t),u.mouse=[s,this.__zoom.invert(s)],tm(this),u.start()}}function k(t,...n){if(r.apply(this,arguments)){var e=this.__zoom,u=nq(t.changedTouches?t.changedTouches[0]:t,this),a=e.invert(u),s=e.k*(t.shiftKey?.5:2),f=o(g(y(e,s),u,a),i.apply(this,n),l);ep(t),c>0?nT(this).transition().duration(c).call(m,f,u,t):nT(this).call(d.transform,f,u,t)}}function M(e,...i){if(r.apply(this,arguments)){var o,u,a,s,l=e.touches,c=l.length,f=x(this,i,e.changedTouches.length===c).event(e);for(eh(e),u=0;u<c;++u)s=[s=nq(a=l[u],this),this.__zoom.invert(s),a.identifier],f.touch0?f.touch1||f.touch0[2]===s[2]||(f.touch1=s,f.taps=0):(f.touch0=s,o=!0,f.taps=1+!!t);t&&(t=clearTimeout(t)),o&&(f.taps<2&&(n=s[0],t=setTimeout(function(){t=null},500)),tm(this),f.start())}}function A(t,...n){if(this.__zooming){var e,r,i,u,a=x(this,n).event(t),s=t.changedTouches,c=s.length;for(ep(t),e=0;e<c;++e)i=nq(r=s[e],this),a.touch0&&a.touch0[2]===r.identifier?a.touch0[0]=i:a.touch1&&a.touch1[2]===r.identifier&&(a.touch1[0]=i);if(r=a.that.__zoom,a.touch1){var f=a.touch0[0],h=a.touch0[1],p=a.touch1[0],v=a.touch1[1],d=(d=p[0]-f[0])*d+(d=p[1]-f[1])*d,_=(_=v[0]-h[0])*_+(_=v[1]-h[1])*_;r=y(r,Math.sqrt(d/_)),i=[(f[0]+p[0])/2,(f[1]+p[1])/2],u=[(h[0]+v[0])/2,(h[1]+v[1])/2]}else{if(!a.touch0)return;i=a.touch0[0],u=a.touch0[1]}a.zoom("touch",o(g(r,i,u),a.extent,l))}}function z(t,...r){if(this.__zooming){var i,o,u=x(this,r).event(t),a=t.changedTouches,s=a.length;for(eh(t),e&&clearTimeout(e),e=setTimeout(function(){e=null},500),i=0;i<s;++i)o=a[i],u.touch0&&u.touch0[2]===o.identifier?delete u.touch0:u.touch1&&u.touch1[2]===o.identifier&&delete u.touch1;if(u.touch1&&!u.touch0&&(u.touch0=u.touch1,delete u.touch1),u.touch0)u.touch0[1]=this.__zoom.invert(u.touch0[0]);else if(u.end(),2===u.taps&&(o=nq(o,this),Math.hypot(n[0]-o[0],n[1]-o[1])<v)){var l=nT(this).on("dblclick.zoom");l&&l.apply(this,arguments)}}}return d.transform=function(t,n,e,r){var i=t.selection?t.selection():t;i.property("__zoom",ey),t!==i?m(t,n,e,r):i.interrupt().each(function(){x(this,arguments).event(r).start().zoom(null,"function"==typeof n?n.apply(this,arguments):n).end()})},d.scaleBy=function(t,n,e,r){d.scaleTo(t,function(){var t=this.__zoom.k,e="function"==typeof n?n.apply(this,arguments):n;return t*e},e,r)},d.scaleTo=function(t,n,e,r){d.transform(t,function(){var t=i.apply(this,arguments),r=this.__zoom,u=null==e?_(t):"function"==typeof e?e.apply(this,arguments):e,a=r.invert(u),s="function"==typeof n?n.apply(this,arguments):n;return o(g(y(r,s),u,a),t,l)},e,r)},d.translateBy=function(t,n,e,r){d.transform(t,function(){return o(this.__zoom.translate("function"==typeof n?n.apply(this,arguments):n,"function"==typeof e?e.apply(this,arguments):e),i.apply(this,arguments),l)},null,r)},d.translateTo=function(t,n,e,r,u){d.transform(t,function(){var t=i.apply(this,arguments),u=this.__zoom,a=null==r?_(t):"function"==typeof r?r.apply(this,arguments):r;return o(ef.translate(a[0],a[1]).scale(u.k).translate("function"==typeof n?-n.apply(this,arguments):-n,"function"==typeof e?-e.apply(this,arguments):-e),t,l)},r,u)},w.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,n){return this.mouse&&"mouse"!==t&&(this.mouse[1]=n.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=n.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=n.invert(this.touch1[0])),this.that.__zoom=n,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var n=nT(this.that).datum();h.call(t,this.that,new el(t,{sourceEvent:this.sourceEvent,target:d,type:t,transform:this.that.__zoom,dispatch:h}),n)}},d.wheelDelta=function(t){return arguments.length?(u="function"==typeof t?t:es(+t),d):u},d.filter=function(t){return arguments.length?(r="function"==typeof t?t:es(!!t),d):r},d.touchable=function(t){return arguments.length?(a="function"==typeof t?t:es(!!t),d):a},d.extent=function(t){return arguments.length?(i="function"==typeof t?t:es([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),d):i},d.scaleExtent=function(t){return arguments.length?(s[0]=+t[0],s[1]=+t[1],d):[s[0],s[1]]},d.translateExtent=function(t){return arguments.length?(l[0][0]=+t[0][0],l[1][0]=+t[1][0],l[0][1]=+t[0][1],l[1][1]=+t[1][1],d):[[l[0][0],l[0][1]],[l[1][0],l[1][1]]]},d.constrain=function(t){return arguments.length?(o=t,d):o},d.duration=function(t){return arguments.length?(c=+t,d):c},d.interpolate=function(t){return arguments.length?(f=t,d):f},d.on=function(){var t=h.on.apply(h,arguments);return t===h?d:t},d.clickDistance=function(t){return arguments.length?(p=(t*=1)*t,d):Math.sqrt(p)},d.tapDistance=function(t){return arguments.length?(v=+t,d):v},d}ec.prototype}}]);