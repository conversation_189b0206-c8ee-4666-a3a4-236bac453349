{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/Html.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom/client';\nimport { Vector3, DoubleSide, OrthographicCamera, PerspectiveCamera, Vector2 } from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\n\nconst v1 = /* @__PURE__ */new Vector3();\nconst v2 = /* @__PURE__ */new Vector3();\nconst v3 = /* @__PURE__ */new Vector3();\nconst v4 = /* @__PURE__ */new Vector2();\nfunction defaultCalculatePosition(el, camera, size) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  objectPos.project(camera);\n  const widthHalf = size.width / 2;\n  const heightHalf = size.height / 2;\n  return [objectPos.x * widthHalf + widthHalf, -(objectPos.y * heightHalf) + heightHalf];\n}\nfunction isObjectBehindCamera(el, camera) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n  const deltaCamObj = objectPos.sub(cameraPos);\n  const camDir = camera.getWorldDirection(v3);\n  return deltaCamObj.angleTo(camDir) > Math.PI / 2;\n}\nfunction isObjectVisible(el, camera, raycaster, occlude) {\n  const elPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const screenPos = elPos.clone();\n  screenPos.project(camera);\n  v4.set(screenPos.x, screenPos.y);\n  raycaster.setFromCamera(v4, camera);\n  const intersects = raycaster.intersectObjects(occlude, true);\n  if (intersects.length) {\n    const intersectionDistance = intersects[0].distance;\n    const pointDistance = elPos.distanceTo(raycaster.ray.origin);\n    return pointDistance < intersectionDistance;\n  }\n  return true;\n}\nfunction objectScale(el, camera) {\n  if (camera instanceof OrthographicCamera) {\n    return camera.zoom;\n  } else if (camera instanceof PerspectiveCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const vFOV = camera.fov * Math.PI / 180;\n    const dist = objectPos.distanceTo(cameraPos);\n    const scaleFOV = 2 * Math.tan(vFOV / 2) * dist;\n    return 1 / scaleFOV;\n  } else {\n    return 1;\n  }\n}\nfunction objectZIndex(el, camera, zIndexRange) {\n  if (camera instanceof PerspectiveCamera || camera instanceof OrthographicCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const dist = objectPos.distanceTo(cameraPos);\n    const A = (zIndexRange[1] - zIndexRange[0]) / (camera.far - camera.near);\n    const B = zIndexRange[1] - A * camera.far;\n    return Math.round(A * dist + B);\n  }\n  return undefined;\n}\nconst epsilon = value => Math.abs(value) < 1e-10 ? 0 : value;\nfunction getCSSMatrix(matrix, multipliers, prepend = '') {\n  let matrix3d = 'matrix3d(';\n  for (let i = 0; i !== 16; i++) {\n    matrix3d += epsilon(multipliers[i] * matrix.elements[i]) + (i !== 15 ? ',' : ')');\n  }\n  return prepend + matrix3d;\n}\nconst getCameraCSSMatrix = (multipliers => {\n  return matrix => getCSSMatrix(matrix, multipliers);\n})([1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1]);\nconst getObjectCSSMatrix = (scaleMultipliers => {\n  return (matrix, factor) => getCSSMatrix(matrix, scaleMultipliers(factor), 'translate(-50%,-50%)');\n})(f => [1 / f, 1 / f, 1 / f, 1, -1 / f, -1 / f, -1 / f, -1, 1 / f, 1 / f, 1 / f, 1, 1, 1, 1, 1]);\nfunction isRefObject(ref) {\n  return ref && typeof ref === 'object' && 'current' in ref;\n}\nconst Html = /* @__PURE__ */React.forwardRef(({\n  children,\n  eps = 0.001,\n  style,\n  className,\n  prepend,\n  center,\n  fullscreen,\n  portal,\n  distanceFactor,\n  sprite = false,\n  transform = false,\n  occlude,\n  onOcclude,\n  castShadow,\n  receiveShadow,\n  material,\n  geometry,\n  zIndexRange = [16777271, 0],\n  calculatePosition = defaultCalculatePosition,\n  as = 'div',\n  wrapperClass,\n  pointerEvents = 'auto',\n  ...props\n}, ref) => {\n  const {\n    gl,\n    camera,\n    scene,\n    size,\n    raycaster,\n    events,\n    viewport\n  } = useThree();\n  const [el] = React.useState(() => document.createElement(as));\n  const root = React.useRef(null);\n  const group = React.useRef(null);\n  const oldZoom = React.useRef(0);\n  const oldPosition = React.useRef([0, 0]);\n  const transformOuterRef = React.useRef(null);\n  const transformInnerRef = React.useRef(null);\n  // Append to the connected element, which makes HTML work with views\n  const target = (portal == null ? void 0 : portal.current) || events.connected || gl.domElement.parentNode;\n  const occlusionMeshRef = React.useRef(null);\n  const isMeshSizeSet = React.useRef(false);\n  const isRayCastOcclusion = React.useMemo(() => {\n    return occlude && occlude !== 'blending' || Array.isArray(occlude) && occlude.length && isRefObject(occlude[0]);\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    const el = gl.domElement;\n    if (occlude && occlude === 'blending') {\n      el.style.zIndex = `${Math.floor(zIndexRange[0] / 2)}`;\n      el.style.position = 'absolute';\n      el.style.pointerEvents = 'none';\n    } else {\n      el.style.zIndex = null;\n      el.style.position = null;\n      el.style.pointerEvents = null;\n    }\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    if (group.current) {\n      const currentRoot = root.current = ReactDOM.createRoot(el);\n      scene.updateMatrixWorld();\n      if (transform) {\n        el.style.cssText = `position:absolute;top:0;left:0;pointer-events:none;overflow:hidden;`;\n      } else {\n        const vec = calculatePosition(group.current, camera, size);\n        el.style.cssText = `position:absolute;top:0;left:0;transform:translate3d(${vec[0]}px,${vec[1]}px,0);transform-origin:0 0;`;\n      }\n      if (target) {\n        if (prepend) target.prepend(el);else target.appendChild(el);\n      }\n      return () => {\n        if (target) target.removeChild(el);\n        currentRoot.unmount();\n      };\n    }\n  }, [target, transform]);\n  React.useLayoutEffect(() => {\n    if (wrapperClass) el.className = wrapperClass;\n  }, [wrapperClass]);\n  const styles = React.useMemo(() => {\n    if (transform) {\n      return {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: size.width,\n        height: size.height,\n        transformStyle: 'preserve-3d',\n        pointerEvents: 'none'\n      };\n    } else {\n      return {\n        position: 'absolute',\n        transform: center ? 'translate3d(-50%,-50%,0)' : 'none',\n        ...(fullscreen && {\n          top: -size.height / 2,\n          left: -size.width / 2,\n          width: size.width,\n          height: size.height\n        }),\n        ...style\n      };\n    }\n  }, [style, center, fullscreen, size, transform]);\n  const transformInnerStyles = React.useMemo(() => ({\n    position: 'absolute',\n    pointerEvents\n  }), [pointerEvents]);\n  React.useLayoutEffect(() => {\n    isMeshSizeSet.current = false;\n    if (transform) {\n      var _root$current;\n      (_root$current = root.current) == null || _root$current.render(/*#__PURE__*/React.createElement(\"div\", {\n        ref: transformOuterRef,\n        style: styles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: transformInnerRef,\n        style: transformInnerStyles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        className: className,\n        style: style,\n        children: children\n      }))));\n    } else {\n      var _root$current2;\n      (_root$current2 = root.current) == null || _root$current2.render(/*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        style: styles,\n        className: className,\n        children: children\n      }));\n    }\n  });\n  const visible = React.useRef(true);\n  useFrame(gl => {\n    if (group.current) {\n      camera.updateMatrixWorld();\n      group.current.updateWorldMatrix(true, false);\n      const vec = transform ? oldPosition.current : calculatePosition(group.current, camera, size);\n      if (transform || Math.abs(oldZoom.current - camera.zoom) > eps || Math.abs(oldPosition.current[0] - vec[0]) > eps || Math.abs(oldPosition.current[1] - vec[1]) > eps) {\n        const isBehindCamera = isObjectBehindCamera(group.current, camera);\n        let raytraceTarget = false;\n        if (isRayCastOcclusion) {\n          if (Array.isArray(occlude)) {\n            raytraceTarget = occlude.map(item => item.current);\n          } else if (occlude !== 'blending') {\n            raytraceTarget = [scene];\n          }\n        }\n        const previouslyVisible = visible.current;\n        if (raytraceTarget) {\n          const isvisible = isObjectVisible(group.current, camera, raycaster, raytraceTarget);\n          visible.current = isvisible && !isBehindCamera;\n        } else {\n          visible.current = !isBehindCamera;\n        }\n        if (previouslyVisible !== visible.current) {\n          if (onOcclude) onOcclude(!visible.current);else el.style.display = visible.current ? 'block' : 'none';\n        }\n        const halfRange = Math.floor(zIndexRange[0] / 2);\n        const zRange = occlude ? isRayCastOcclusion //\n        ? [zIndexRange[0], halfRange] : [halfRange - 1, 0] : zIndexRange;\n        el.style.zIndex = `${objectZIndex(group.current, camera, zRange)}`;\n        if (transform) {\n          const [widthHalf, heightHalf] = [size.width / 2, size.height / 2];\n          const fov = camera.projectionMatrix.elements[5] * heightHalf;\n          const {\n            isOrthographicCamera,\n            top,\n            left,\n            bottom,\n            right\n          } = camera;\n          const cameraMatrix = getCameraCSSMatrix(camera.matrixWorldInverse);\n          const cameraTransform = isOrthographicCamera ? `scale(${fov})translate(${epsilon(-(right + left) / 2)}px,${epsilon((top + bottom) / 2)}px)` : `translateZ(${fov}px)`;\n          let matrix = group.current.matrixWorld;\n          if (sprite) {\n            matrix = camera.matrixWorldInverse.clone().transpose().copyPosition(matrix).scale(group.current.scale);\n            matrix.elements[3] = matrix.elements[7] = matrix.elements[11] = 0;\n            matrix.elements[15] = 1;\n          }\n          el.style.width = size.width + 'px';\n          el.style.height = size.height + 'px';\n          el.style.perspective = isOrthographicCamera ? '' : `${fov}px`;\n          if (transformOuterRef.current && transformInnerRef.current) {\n            transformOuterRef.current.style.transform = `${cameraTransform}${cameraMatrix}translate(${widthHalf}px,${heightHalf}px)`;\n            transformInnerRef.current.style.transform = getObjectCSSMatrix(matrix, 1 / ((distanceFactor || 10) / 400));\n          }\n        } else {\n          const scale = distanceFactor === undefined ? 1 : objectScale(group.current, camera) * distanceFactor;\n          el.style.transform = `translate3d(${vec[0]}px,${vec[1]}px,0) scale(${scale})`;\n        }\n        oldPosition.current = vec;\n        oldZoom.current = camera.zoom;\n      }\n    }\n    if (!isRayCastOcclusion && occlusionMeshRef.current && !isMeshSizeSet.current) {\n      if (transform) {\n        if (transformOuterRef.current) {\n          const el = transformOuterRef.current.children[0];\n          if (el != null && el.clientWidth && el != null && el.clientHeight) {\n            const {\n              isOrthographicCamera\n            } = camera;\n            if (isOrthographicCamera || geometry) {\n              if (props.scale) {\n                if (!Array.isArray(props.scale)) {\n                  occlusionMeshRef.current.scale.setScalar(1 / props.scale);\n                } else if (props.scale instanceof Vector3) {\n                  occlusionMeshRef.current.scale.copy(props.scale.clone().divideScalar(1));\n                } else {\n                  occlusionMeshRef.current.scale.set(1 / props.scale[0], 1 / props.scale[1], 1 / props.scale[2]);\n                }\n              }\n            } else {\n              const ratio = (distanceFactor || 10) / 400;\n              const w = el.clientWidth * ratio;\n              const h = el.clientHeight * ratio;\n              occlusionMeshRef.current.scale.set(w, h, 1);\n            }\n            isMeshSizeSet.current = true;\n          }\n        }\n      } else {\n        const ele = el.children[0];\n        if (ele != null && ele.clientWidth && ele != null && ele.clientHeight) {\n          const ratio = 1 / viewport.factor;\n          const w = ele.clientWidth * ratio;\n          const h = ele.clientHeight * ratio;\n          occlusionMeshRef.current.scale.set(w, h, 1);\n          isMeshSizeSet.current = true;\n        }\n        occlusionMeshRef.current.lookAt(gl.camera.position);\n      }\n    }\n  });\n  const shaders = React.useMemo(() => ({\n    vertexShader: !transform ? /* glsl */`\n          /*\n            This shader is from the THREE's SpriteMaterial.\n            We need to turn the backing plane into a Sprite\n            (make it always face the camera) if \"transfrom\"\n            is false.\n          */\n          #include <common>\n\n          void main() {\n            vec2 center = vec2(0., 1.);\n            float rotation = 0.0;\n\n            // This is somewhat arbitrary, but it seems to work well\n            // Need to figure out how to derive this dynamically if it even matters\n            float size = 0.03;\n\n            vec4 mvPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );\n            vec2 scale;\n            scale.x = length( vec3( modelMatrix[ 0 ].x, modelMatrix[ 0 ].y, modelMatrix[ 0 ].z ) );\n            scale.y = length( vec3( modelMatrix[ 1 ].x, modelMatrix[ 1 ].y, modelMatrix[ 1 ].z ) );\n\n            bool isPerspective = isPerspectiveMatrix( projectionMatrix );\n            if ( isPerspective ) scale *= - mvPosition.z;\n\n            vec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale * size;\n            vec2 rotatedPosition;\n            rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;\n            rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;\n            mvPosition.xy += rotatedPosition;\n\n            gl_Position = projectionMatrix * mvPosition;\n          }\n      ` : undefined,\n    fragmentShader: /* glsl */`\n        void main() {\n          gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n        }\n      `\n  }), [transform]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({}, props, {\n    ref: group\n  }), occlude && !isRayCastOcclusion && /*#__PURE__*/React.createElement(\"mesh\", {\n    castShadow: castShadow,\n    receiveShadow: receiveShadow,\n    ref: occlusionMeshRef\n  }, geometry || /*#__PURE__*/React.createElement(\"planeGeometry\", null), material || /*#__PURE__*/React.createElement(\"shaderMaterial\", {\n    side: DoubleSide,\n    vertexShader: shaders.vertexShader,\n    fragmentShader: shaders.fragmentShader\n  })));\n});\n\nexport { Html };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;AAEA,MAAM,KAAK,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAO;AACrC,MAAM,KAAK,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAO;AACrC,MAAM,KAAK,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAO;AACrC,MAAM,KAAK,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAO;AACrC,SAAS,yBAAyB,EAAE,EAAE,MAAM,EAAE,IAAI;IAChD,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;IACzD,UAAU,OAAO,CAAC;IAClB,MAAM,YAAY,KAAK,KAAK,GAAG;IAC/B,MAAM,aAAa,KAAK,MAAM,GAAG;IACjC,OAAO;QAAC,UAAU,CAAC,GAAG,YAAY;QAAW,CAAC,CAAC,UAAU,CAAC,GAAG,UAAU,IAAI;KAAW;AACxF;AACA,SAAS,qBAAqB,EAAE,EAAE,MAAM;IACtC,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;IACzD,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,WAAW;IAC7D,MAAM,cAAc,UAAU,GAAG,CAAC;IAClC,MAAM,SAAS,OAAO,iBAAiB,CAAC;IACxC,OAAO,YAAY,OAAO,CAAC,UAAU,KAAK,EAAE,GAAG;AACjD;AACA,SAAS,gBAAgB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO;IACrD,MAAM,QAAQ,GAAG,qBAAqB,CAAC,GAAG,WAAW;IACrD,MAAM,YAAY,MAAM,KAAK;IAC7B,UAAU,OAAO,CAAC;IAClB,GAAG,GAAG,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC;IAC/B,UAAU,aAAa,CAAC,IAAI;IAC5B,MAAM,aAAa,UAAU,gBAAgB,CAAC,SAAS;IACvD,IAAI,WAAW,MAAM,EAAE;QACrB,MAAM,uBAAuB,UAAU,CAAC,EAAE,CAAC,QAAQ;QACnD,MAAM,gBAAgB,MAAM,UAAU,CAAC,UAAU,GAAG,CAAC,MAAM;QAC3D,OAAO,gBAAgB;IACzB;IACA,OAAO;AACT;AACA,SAAS,YAAY,EAAE,EAAE,MAAM;IAC7B,IAAI,kBAAkB,sMAAA,CAAA,qBAAkB,EAAE;QACxC,OAAO,OAAO,IAAI;IACpB,OAAO,IAAI,kBAAkB,sMAAA,CAAA,oBAAiB,EAAE;QAC9C,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;QACzD,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,WAAW;QAC7D,MAAM,OAAO,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG;QACpC,MAAM,OAAO,UAAU,UAAU,CAAC;QAClC,MAAM,WAAW,IAAI,KAAK,GAAG,CAAC,OAAO,KAAK;QAC1C,OAAO,IAAI;IACb,OAAO;QACL,OAAO;IACT;AACF;AACA,SAAS,aAAa,EAAE,EAAE,MAAM,EAAE,WAAW;IAC3C,IAAI,kBAAkB,sMAAA,CAAA,oBAAiB,IAAI,kBAAkB,sMAAA,CAAA,qBAAkB,EAAE;QAC/E,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;QACzD,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,WAAW;QAC7D,MAAM,OAAO,UAAU,UAAU,CAAC;QAClC,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,GAAG,OAAO,IAAI;QACvE,MAAM,IAAI,WAAW,CAAC,EAAE,GAAG,IAAI,OAAO,GAAG;QACzC,OAAO,KAAK,KAAK,CAAC,IAAI,OAAO;IAC/B;IACA,OAAO;AACT;AACA,MAAM,UAAU,CAAA,QAAS,KAAK,GAAG,CAAC,SAAS,QAAQ,IAAI;AACvD,SAAS,aAAa,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE;IACrD,IAAI,WAAW;IACf,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,IAAK;QAC7B,YAAY,QAAQ,WAAW,CAAC,EAAE,GAAG,OAAO,QAAQ,CAAC,EAAE,IAAI,CAAC,MAAM,KAAK,MAAM,GAAG;IAClF;IACA,OAAO,UAAU;AACnB;AACA,MAAM,qBAAqB,CAAC,CAAA;IAC1B,OAAO,CAAA,SAAU,aAAa,QAAQ;AACxC,CAAC,EAAE;IAAC;IAAG,CAAC;IAAG;IAAG;IAAG;IAAG,CAAC;IAAG;IAAG;IAAG;IAAG,CAAC;IAAG;IAAG;IAAG;IAAG,CAAC;IAAG;IAAG;CAAE;AACvD,MAAM,qBAAqB,CAAC,CAAA;IAC1B,OAAO,CAAC,QAAQ,SAAW,aAAa,QAAQ,iBAAiB,SAAS;AAC5E,CAAC,EAAE,CAAA,IAAK;QAAC,IAAI;QAAG,IAAI;QAAG,IAAI;QAAG;QAAG,CAAC,IAAI;QAAG,CAAC,IAAI;QAAG,CAAC,IAAI;QAAG,CAAC;QAAG,IAAI;QAAG,IAAI;QAAG,IAAI;QAAG;QAAG;QAAG;QAAG;QAAG;KAAE;AAChG,SAAS,YAAY,GAAG;IACtB,OAAO,OAAO,OAAO,QAAQ,YAAY,aAAa;AACxD;AACA,MAAM,OAAO,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAC5C,QAAQ,EACR,MAAM,KAAK,EACX,KAAK,EACL,SAAS,EACT,OAAO,EACP,MAAM,EACN,UAAU,EACV,MAAM,EACN,cAAc,EACd,SAAS,KAAK,EACd,YAAY,KAAK,EACjB,OAAO,EACP,SAAS,EACT,UAAU,EACV,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,cAAc;IAAC;IAAU;CAAE,EAC3B,oBAAoB,wBAAwB,EAC5C,KAAK,KAAK,EACV,YAAY,EACZ,gBAAgB,MAAM,EACtB,GAAG,OACJ,EAAE;IACD,MAAM,EACJ,EAAE,EACF,MAAM,EACN,KAAK,EACL,IAAI,EACJ,SAAS,EACT,MAAM,EACN,QAAQ,EACT,GAAG,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;IACX,MAAM,CAAC,GAAG,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD;yBAAE,IAAM,SAAS,aAAa,CAAC;;IACzD,MAAM,OAAO,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC1B,MAAM,QAAQ,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC3B,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC7B,MAAM,cAAc,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;QAAC;QAAG;KAAE;IACvC,MAAM,oBAAoB,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IACvC,MAAM,oBAAoB,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IACvC,oEAAoE;IACpE,MAAM,SAAS,CAAC,UAAU,OAAO,KAAK,IAAI,OAAO,OAAO,KAAK,OAAO,SAAS,IAAI,GAAG,UAAU,CAAC,UAAU;IACzG,MAAM,mBAAmB,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IACtC,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IACnC,MAAM,qBAAqB,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;4CAAE;YACvC,OAAO,WAAW,YAAY,cAAc,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,IAAI,YAAY,OAAO,CAAC,EAAE;QAChH;2CAAG;QAAC;KAAQ;IACZ,CAAA,GAAA,4RAAA,CAAA,kBAAqB,AAAD;gCAAE;YACpB,MAAM,KAAK,GAAG,UAAU;YACxB,IAAI,WAAW,YAAY,YAAY;gBACrC,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG,KAAK,KAAK,CAAC,WAAW,CAAC,EAAE,GAAG,IAAI;gBACrD,GAAG,KAAK,CAAC,QAAQ,GAAG;gBACpB,GAAG,KAAK,CAAC,aAAa,GAAG;YAC3B,OAAO;gBACL,GAAG,KAAK,CAAC,MAAM,GAAG;gBAClB,GAAG,KAAK,CAAC,QAAQ,GAAG;gBACpB,GAAG,KAAK,CAAC,aAAa,GAAG;YAC3B;QACF;+BAAG;QAAC;KAAQ;IACZ,CAAA,GAAA,4RAAA,CAAA,kBAAqB,AAAD;gCAAE;YACpB,IAAI,MAAM,OAAO,EAAE;gBACjB,MAAM,cAAc,KAAK,OAAO,GAAG,CAAA,GAAA,oSAAA,CAAA,aAAmB,AAAD,EAAE;gBACvD,MAAM,iBAAiB;gBACvB,IAAI,WAAW;oBACb,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,mEAAmE,CAAC;gBAC1F,OAAO;oBACL,MAAM,MAAM,kBAAkB,MAAM,OAAO,EAAE,QAAQ;oBACrD,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,qDAAqD,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,2BAA2B,CAAC;gBAC5H;gBACA,IAAI,QAAQ;oBACV,IAAI,SAAS,OAAO,OAAO,CAAC;yBAAS,OAAO,WAAW,CAAC;gBAC1D;gBACA;4CAAO;wBACL,IAAI,QAAQ,OAAO,WAAW,CAAC;wBAC/B,YAAY,OAAO;oBACrB;;YACF;QACF;+BAAG;QAAC;QAAQ;KAAU;IACtB,CAAA,GAAA,4RAAA,CAAA,kBAAqB,AAAD;gCAAE;YACpB,IAAI,cAAc,GAAG,SAAS,GAAG;QACnC;+BAAG;QAAC;KAAa;IACjB,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;gCAAE;YAC3B,IAAI,WAAW;gBACb,OAAO;oBACL,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO,KAAK,KAAK;oBACjB,QAAQ,KAAK,MAAM;oBACnB,gBAAgB;oBAChB,eAAe;gBACjB;YACF,OAAO;gBACL,OAAO;oBACL,UAAU;oBACV,WAAW,SAAS,6BAA6B;oBACjD,GAAI,cAAc;wBAChB,KAAK,CAAC,KAAK,MAAM,GAAG;wBACpB,MAAM,CAAC,KAAK,KAAK,GAAG;wBACpB,OAAO,KAAK,KAAK;wBACjB,QAAQ,KAAK,MAAM;oBACrB,CAAC;oBACD,GAAG,KAAK;gBACV;YACF;QACF;+BAAG;QAAC;QAAO;QAAQ;QAAY;QAAM;KAAU;IAC/C,MAAM,uBAAuB,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;8CAAE,IAAM,CAAC;gBAChD,UAAU;gBACV;YACF,CAAC;6CAAG;QAAC;KAAc;IACnB,CAAA,GAAA,4RAAA,CAAA,kBAAqB,AAAD;gCAAE;YACpB,cAAc,OAAO,GAAG;YACxB,IAAI,WAAW;gBACb,IAAI;gBACJ,CAAC,gBAAgB,KAAK,OAAO,KAAK,QAAQ,cAAc,MAAM,CAAC,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;oBACrG,KAAK;oBACL,OAAO;gBACT,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;oBACzC,KAAK;oBACL,OAAO;gBACT,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;oBACzC,KAAK;oBACL,WAAW;oBACX,OAAO;oBACP,UAAU;gBACZ;YACF,OAAO;gBACL,IAAI;gBACJ,CAAC,iBAAiB,KAAK,OAAO,KAAK,QAAQ,eAAe,MAAM,CAAC,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;oBACvG,KAAK;oBACL,OAAO;oBACP,WAAW;oBACX,UAAU;gBACZ;YACF;QACF;;IACA,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC7B,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;yBAAE,CAAA;YACP,IAAI,MAAM,OAAO,EAAE;gBACjB,OAAO,iBAAiB;gBACxB,MAAM,OAAO,CAAC,iBAAiB,CAAC,MAAM;gBACtC,MAAM,MAAM,YAAY,YAAY,OAAO,GAAG,kBAAkB,MAAM,OAAO,EAAE,QAAQ;gBACvF,IAAI,aAAa,KAAK,GAAG,CAAC,QAAQ,OAAO,GAAG,OAAO,IAAI,IAAI,OAAO,KAAK,GAAG,CAAC,YAAY,OAAO,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,OAAO,KAAK,GAAG,CAAC,YAAY,OAAO,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,KAAK;oBACpK,MAAM,iBAAiB,qBAAqB,MAAM,OAAO,EAAE;oBAC3D,IAAI,iBAAiB;oBACrB,IAAI,oBAAoB;wBACtB,IAAI,MAAM,OAAO,CAAC,UAAU;4BAC1B,iBAAiB,QAAQ,GAAG;iDAAC,CAAA,OAAQ,KAAK,OAAO;;wBACnD,OAAO,IAAI,YAAY,YAAY;4BACjC,iBAAiB;gCAAC;6BAAM;wBAC1B;oBACF;oBACA,MAAM,oBAAoB,QAAQ,OAAO;oBACzC,IAAI,gBAAgB;wBAClB,MAAM,YAAY,gBAAgB,MAAM,OAAO,EAAE,QAAQ,WAAW;wBACpE,QAAQ,OAAO,GAAG,aAAa,CAAC;oBAClC,OAAO;wBACL,QAAQ,OAAO,GAAG,CAAC;oBACrB;oBACA,IAAI,sBAAsB,QAAQ,OAAO,EAAE;wBACzC,IAAI,WAAW,UAAU,CAAC,QAAQ,OAAO;6BAAO,GAAG,KAAK,CAAC,OAAO,GAAG,QAAQ,OAAO,GAAG,UAAU;oBACjG;oBACA,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW,CAAC,EAAE,GAAG;oBAC9C,MAAM,SAAS,UAAU,mBAAmB,EAAE;uBAC5C;wBAAC,WAAW,CAAC,EAAE;wBAAE;qBAAU,GAAG;wBAAC,YAAY;wBAAG;qBAAE,GAAG;oBACrD,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG,aAAa,MAAM,OAAO,EAAE,QAAQ,SAAS;oBAClE,IAAI,WAAW;wBACb,MAAM,CAAC,WAAW,WAAW,GAAG;4BAAC,KAAK,KAAK,GAAG;4BAAG,KAAK,MAAM,GAAG;yBAAE;wBACjE,MAAM,MAAM,OAAO,gBAAgB,CAAC,QAAQ,CAAC,EAAE,GAAG;wBAClD,MAAM,EACJ,oBAAoB,EACpB,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACN,GAAG;wBACJ,MAAM,eAAe,mBAAmB,OAAO,kBAAkB;wBACjE,MAAM,kBAAkB,uBAAuB,CAAC,MAAM,EAAE,IAAI,WAAW,EAAE,QAAQ,CAAC,CAAC,QAAQ,IAAI,IAAI,GAAG,GAAG,EAAE,QAAQ,CAAC,MAAM,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC;wBACpK,IAAI,SAAS,MAAM,OAAO,CAAC,WAAW;wBACtC,IAAI,QAAQ;4BACV,SAAS,OAAO,kBAAkB,CAAC,KAAK,GAAG,SAAS,GAAG,YAAY,CAAC,QAAQ,KAAK,CAAC,MAAM,OAAO,CAAC,KAAK;4BACrG,OAAO,QAAQ,CAAC,EAAE,GAAG,OAAO,QAAQ,CAAC,EAAE,GAAG,OAAO,QAAQ,CAAC,GAAG,GAAG;4BAChE,OAAO,QAAQ,CAAC,GAAG,GAAG;wBACxB;wBACA,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,KAAK,GAAG;wBAC9B,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,MAAM,GAAG;wBAChC,GAAG,KAAK,CAAC,WAAW,GAAG,uBAAuB,KAAK,GAAG,IAAI,EAAE,CAAC;wBAC7D,IAAI,kBAAkB,OAAO,IAAI,kBAAkB,OAAO,EAAE;4BAC1D,kBAAkB,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,kBAAkB,aAAa,UAAU,EAAE,UAAU,GAAG,EAAE,WAAW,GAAG,CAAC;4BACxH,kBAAkB,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,mBAAmB,QAAQ,IAAI,CAAC,CAAC,kBAAkB,EAAE,IAAI,GAAG;wBAC1G;oBACF,OAAO;wBACL,MAAM,QAAQ,mBAAmB,YAAY,IAAI,YAAY,MAAM,OAAO,EAAE,UAAU;wBACtF,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;oBAC/E;oBACA,YAAY,OAAO,GAAG;oBACtB,QAAQ,OAAO,GAAG,OAAO,IAAI;gBAC/B;YACF;YACA,IAAI,CAAC,sBAAsB,iBAAiB,OAAO,IAAI,CAAC,cAAc,OAAO,EAAE;gBAC7E,IAAI,WAAW;oBACb,IAAI,kBAAkB,OAAO,EAAE;wBAC7B,MAAM,KAAK,kBAAkB,OAAO,CAAC,QAAQ,CAAC,EAAE;wBAChD,IAAI,MAAM,QAAQ,GAAG,WAAW,IAAI,MAAM,QAAQ,GAAG,YAAY,EAAE;4BACjE,MAAM,EACJ,oBAAoB,EACrB,GAAG;4BACJ,IAAI,wBAAwB,UAAU;gCACpC,IAAI,MAAM,KAAK,EAAE;oCACf,IAAI,CAAC,MAAM,OAAO,CAAC,MAAM,KAAK,GAAG;wCAC/B,iBAAiB,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,MAAM,KAAK;oCAC1D,OAAO,IAAI,MAAM,KAAK,YAAY,sMAAA,CAAA,UAAO,EAAE;wCACzC,iBAAiB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,KAAK,GAAG,YAAY,CAAC;oCACvE,OAAO;wCACL,iBAAiB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE;oCAC/F;gCACF;4BACF,OAAO;gCACL,MAAM,QAAQ,CAAC,kBAAkB,EAAE,IAAI;gCACvC,MAAM,IAAI,GAAG,WAAW,GAAG;gCAC3B,MAAM,IAAI,GAAG,YAAY,GAAG;gCAC5B,iBAAiB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG;4BAC3C;4BACA,cAAc,OAAO,GAAG;wBAC1B;oBACF;gBACF,OAAO;oBACL,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE;oBAC1B,IAAI,OAAO,QAAQ,IAAI,WAAW,IAAI,OAAO,QAAQ,IAAI,YAAY,EAAE;wBACrE,MAAM,QAAQ,IAAI,SAAS,MAAM;wBACjC,MAAM,IAAI,IAAI,WAAW,GAAG;wBAC5B,MAAM,IAAI,IAAI,YAAY,GAAG;wBAC7B,iBAAiB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG;wBACzC,cAAc,OAAO,GAAG;oBAC1B;oBACA,iBAAiB,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,QAAQ;gBACpD;YACF;QACF;;IACA,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;iCAAE,IAAM,CAAC;gBACnC,cAAc,CAAC,YAAY,QAAQ,GAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAiCpC,CAAC,GAAG;gBACN,gBAAgB,QAAQ,GAAE,CAAC;;;;MAIzB,CAAC;YACL,CAAC;gCAAG;QAAC;KAAU;IACf,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QACnE,KAAK;IACP,IAAI,WAAW,CAAC,sBAAsB,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC7E,YAAY;QACZ,eAAe;QACf,KAAK;IACP,GAAG,YAAY,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,iBAAiB,OAAO,YAAY,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,kBAAkB;QACrI,MAAM,sMAAA,CAAA,aAAU;QAChB,cAAc,QAAQ,YAAY;QAClC,gBAAgB,QAAQ,cAAc;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 454, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/CycleRaycast.js"], "sourcesContent": ["import * as React from 'react';\nimport { useThree } from '@react-three/fiber';\n\nfunction CycleRaycast({\n  onChanged,\n  portal,\n  preventDefault = true,\n  scroll = true,\n  keyCode = 9\n}) {\n  const cycle = React.useRef(0);\n  const setEvents = useThree(state => state.setEvents);\n  const get = useThree(state => state.get);\n  const gl = useThree(state => state.gl);\n  React.useEffect(() => {\n    var _portal$current;\n    let hits = [];\n    let lastEvent = undefined;\n    const prev = get().events.filter;\n    const target = (_portal$current = portal == null ? void 0 : portal.current) !== null && _portal$current !== void 0 ? _portal$current : gl.domElement.parentNode;\n\n    // Render custom status\n    const renderStatus = () => target && onChanged && onChanged(hits, Math.round(cycle.current) % hits.length);\n\n    // Overwrite the raycasters custom filter (this only exists in r3f)\n    setEvents({\n      filter: (intersections, state) => {\n        // Reset cycle when the intersections change\n        let clone = [...intersections];\n        if (clone.length !== hits.length || !hits.every(hit => clone.map(e => e.object.uuid).includes(hit.object.uuid))) {\n          cycle.current = 0;\n          hits = clone;\n          renderStatus();\n        }\n        // Run custom filter if there is one\n        if (prev) clone = prev(clone, state);\n        // Cycle through the actual raycast intersects\n        for (let i = 0; i < Math.round(cycle.current) % clone.length; i++) {\n          const first = clone.shift();\n          clone = [...clone, first];\n        }\n        return clone;\n      }\n    });\n\n    // Cycle, refresh events and render status\n    const refresh = fn => {\n      var _get$events$handlers, _get$events$handlers2;\n      cycle.current = fn(cycle.current);\n      // Cancel hovered elements and fake a pointer-move\n      (_get$events$handlers = get().events.handlers) == null || _get$events$handlers.onPointerCancel(undefined);\n      (_get$events$handlers2 = get().events.handlers) == null || _get$events$handlers2.onPointerMove(lastEvent);\n      renderStatus();\n    };\n\n    // Key events\n    const tabEvent = event => {\n      if ((event.keyCode || event.which) === keyCode) {\n        if (preventDefault) event.preventDefault();\n        if (hits.length > 1) refresh(current => current + 1);\n      }\n    };\n\n    // Wheel events\n    const wheelEvent = event => {\n      if (preventDefault) event.preventDefault();\n      let delta = 0;\n      if (!event) event = window.event;\n      if (event.wheelDelta) delta = event.wheelDelta / 120;else if (event.detail) delta = -event.detail / 3;\n      if (hits.length > 1) refresh(current => Math.abs(current - delta));\n    };\n\n    // Catch last move event and position custom status\n    const moveEvent = event => lastEvent = event;\n    document.addEventListener('pointermove', moveEvent, {\n      passive: true\n    });\n    if (scroll) document.addEventListener('wheel', wheelEvent);\n    if (keyCode !== undefined) document.addEventListener('keydown', tabEvent);\n    return () => {\n      // Clean up\n      setEvents({\n        filter: prev\n      });\n      if (keyCode !== undefined) document.removeEventListener('keydown', tabEvent);\n      if (scroll) document.removeEventListener('wheel', wheelEvent);\n      document.removeEventListener('pointermove', moveEvent);\n    };\n  }, [gl, get, setEvents, preventDefault, scroll, keyCode]);\n  return null;\n}\n\nexport { CycleRaycast };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,MAAM,EACN,iBAAiB,IAAI,EACrB,SAAS,IAAI,EACb,UAAU,CAAC,EACZ;IACC,MAAM,QAAQ,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC3B,MAAM,YAAY,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;4CAAE,CAAA,QAAS,MAAM,SAAS;;IACnD,MAAM,MAAM,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;sCAAE,CAAA,QAAS,MAAM,GAAG;;IACvC,MAAM,KAAK,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;qCAAE,CAAA,QAAS,MAAM,EAAE;;IACrC,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;kCAAE;YACd,IAAI;YACJ,IAAI,OAAO,EAAE;YACb,IAAI,YAAY;YAChB,MAAM,OAAO,MAAM,MAAM,CAAC,MAAM;YAChC,MAAM,SAAS,CAAC,kBAAkB,UAAU,OAAO,KAAK,IAAI,OAAO,OAAO,MAAM,QAAQ,oBAAoB,KAAK,IAAI,kBAAkB,GAAG,UAAU,CAAC,UAAU;YAE/J,uBAAuB;YACvB,MAAM;uDAAe,IAAM,UAAU,aAAa,UAAU,MAAM,KAAK,KAAK,CAAC,MAAM,OAAO,IAAI,KAAK,MAAM;;YAEzG,mEAAmE;YACnE,UAAU;gBACR,MAAM;8CAAE,CAAC,eAAe;wBACtB,4CAA4C;wBAC5C,IAAI,QAAQ;+BAAI;yBAAc;wBAC9B,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,IAAI,CAAC,KAAK,KAAK;sDAAC,CAAA,MAAO,MAAM,GAAG;8DAAC,CAAA,IAAK,EAAE,MAAM,CAAC,IAAI;6DAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,IAAI;sDAAI;4BAC/G,MAAM,OAAO,GAAG;4BAChB,OAAO;4BACP;wBACF;wBACA,oCAAoC;wBACpC,IAAI,MAAM,QAAQ,KAAK,OAAO;wBAC9B,8CAA8C;wBAC9C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,CAAC,MAAM,OAAO,IAAI,MAAM,MAAM,EAAE,IAAK;4BACjE,MAAM,QAAQ,MAAM,KAAK;4BACzB,QAAQ;mCAAI;gCAAO;6BAAM;wBAC3B;wBACA,OAAO;oBACT;;YACF;YAEA,0CAA0C;YAC1C,MAAM;kDAAU,CAAA;oBACd,IAAI,sBAAsB;oBAC1B,MAAM,OAAO,GAAG,GAAG,MAAM,OAAO;oBAChC,kDAAkD;oBAClD,CAAC,uBAAuB,MAAM,MAAM,CAAC,QAAQ,KAAK,QAAQ,qBAAqB,eAAe,CAAC;oBAC/F,CAAC,wBAAwB,MAAM,MAAM,CAAC,QAAQ,KAAK,QAAQ,sBAAsB,aAAa,CAAC;oBAC/F;gBACF;;YAEA,aAAa;YACb,MAAM;mDAAW,CAAA;oBACf,IAAI,CAAC,MAAM,OAAO,IAAI,MAAM,KAAK,MAAM,SAAS;wBAC9C,IAAI,gBAAgB,MAAM,cAAc;wBACxC,IAAI,KAAK,MAAM,GAAG,GAAG;+DAAQ,CAAA,UAAW,UAAU;;oBACpD;gBACF;;YAEA,eAAe;YACf,MAAM;qDAAa,CAAA;oBACjB,IAAI,gBAAgB,MAAM,cAAc;oBACxC,IAAI,QAAQ;oBACZ,IAAI,CAAC,OAAO,QAAQ,OAAO,KAAK;oBAChC,IAAI,MAAM,UAAU,EAAE,QAAQ,MAAM,UAAU,GAAG;yBAAS,IAAI,MAAM,MAAM,EAAE,QAAQ,CAAC,MAAM,MAAM,GAAG;oBACpG,IAAI,KAAK,MAAM,GAAG,GAAG;6DAAQ,CAAA,UAAW,KAAK,GAAG,CAAC,UAAU;;gBAC7D;;YAEA,mDAAmD;YACnD,MAAM;oDAAY,CAAA,QAAS,YAAY;;YACvC,SAAS,gBAAgB,CAAC,eAAe,WAAW;gBAClD,SAAS;YACX;YACA,IAAI,QAAQ,SAAS,gBAAgB,CAAC,SAAS;YAC/C,IAAI,YAAY,WAAW,SAAS,gBAAgB,CAAC,WAAW;YAChE;0CAAO;oBACL,WAAW;oBACX,UAAU;wBACR,QAAQ;oBACV;oBACA,IAAI,YAAY,WAAW,SAAS,mBAAmB,CAAC,WAAW;oBACnE,IAAI,QAAQ,SAAS,mBAAmB,CAAC,SAAS;oBAClD,SAAS,mBAAmB,CAAC,eAAe;gBAC9C;;QACF;iCAAG;QAAC;QAAI;QAAK;QAAW;QAAgB;QAAQ;KAAQ;IACxD,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/useCursor.js"], "sourcesContent": ["import * as React from 'react';\n\nfunction useCursor(hovered, onPointerOver = 'pointer', onPointerOut = 'auto', container = document.body) {\n  React.useEffect(() => {\n    if (hovered) {\n      container.style.cursor = onPointerOver;\n      return () => void (container.style.cursor = onPointerOut);\n    }\n  }, [hovered]);\n}\n\nexport { useCursor };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,UAAU,OAAO,EAAE,gBAAgB,SAAS,EAAE,eAAe,MAAM,EAAE,YAAY,SAAS,IAAI;IACrG,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;+BAAE;YACd,IAAI,SAAS;gBACX,UAAU,KAAK,CAAC,MAAM,GAAG;gBACzB;2CAAO,IAAM,KAAK,CAAC,UAAU,KAAK,CAAC,MAAM,GAAG,YAAY;;YAC1D;QACF;8BAAG;QAAC;KAAQ;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 613, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/Loader.js"], "sourcesContent": ["import * as React from 'react';\nimport { useProgress } from '../core/Progress.js';\n\nconst defaultDataInterpolation = p => `Loading ${p.toFixed(2)}%`;\nfunction Loader({\n  containerStyles,\n  innerStyles,\n  barStyles,\n  dataStyles,\n  dataInterpolation = defaultDataInterpolation,\n  initialState = active => active\n}) {\n  const {\n    active,\n    progress\n  } = useProgress();\n  const progressRef = React.useRef(0);\n  const rafRef = React.useRef(0);\n  const progressSpanRef = React.useRef(null);\n  const [shown, setShown] = React.useState(initialState(active));\n  React.useEffect(() => {\n    let t;\n    if (active !== shown) t = setTimeout(() => setShown(active), 300);\n    return () => clearTimeout(t);\n  }, [shown, active]);\n  const updateProgress = React.useCallback(() => {\n    if (!progressSpanRef.current) return;\n    progressRef.current += (progress - progressRef.current) / 2;\n    if (progressRef.current > 0.95 * progress || progress === 100) progressRef.current = progress;\n    progressSpanRef.current.innerText = dataInterpolation(progressRef.current);\n    if (progressRef.current < progress) rafRef.current = requestAnimationFrame(updateProgress);\n  }, [dataInterpolation, progress]);\n  React.useEffect(() => {\n    updateProgress();\n    return () => cancelAnimationFrame(rafRef.current);\n  }, [updateProgress]);\n  return shown ? /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...styles.container,\n      opacity: active ? 1 : 0,\n      ...containerStyles\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", null, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...styles.inner,\n      ...innerStyles\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...styles.bar,\n      transform: `scaleX(${progress / 100})`,\n      ...barStyles\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    ref: progressSpanRef,\n    style: {\n      ...styles.data,\n      ...dataStyles\n    }\n  })))) : null;\n}\nconst styles = {\n  container: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    width: '100%',\n    height: '100%',\n    background: '#171717',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    transition: 'opacity 300ms ease',\n    zIndex: 1000\n  },\n  inner: {\n    width: 100,\n    height: 3,\n    background: '#272727',\n    textAlign: 'center'\n  },\n  bar: {\n    height: 3,\n    width: '100%',\n    background: 'white',\n    transition: 'transform 200ms',\n    transformOrigin: 'left center'\n  },\n  data: {\n    display: 'inline-block',\n    position: 'relative',\n    fontVariantNumeric: 'tabular-nums',\n    marginTop: '0.8em',\n    color: '#f0f0f0',\n    fontSize: '0.6em',\n    fontFamily: `-apple-system, BlinkMacSystemFont, \"Inter\", \"Segoe UI\", \"Helvetica Neue\", Helvetica, Arial, Roboto, Ubuntu, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\"`,\n    whiteSpace: 'nowrap'\n  }\n};\n\nexport { Loader };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,2BAA2B,CAAA,IAAK,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AAChE,SAAS,OAAO,EACd,eAAe,EACf,WAAW,EACX,SAAS,EACT,UAAU,EACV,oBAAoB,wBAAwB,EAC5C,eAAe,CAAA,SAAU,MAAM,EAChC;IACC,MAAM,EACJ,MAAM,EACN,QAAQ,EACT,GAAG,CAAA,GAAA,kXAAA,CAAA,cAAW,AAAD;IACd,MAAM,cAAc,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IACjC,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC5B,MAAM,kBAAkB,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD,EAAE,aAAa;IACtD,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;4BAAE;YACd,IAAI;YACJ,IAAI,WAAW,OAAO,IAAI;oCAAW,IAAM,SAAS;mCAAS;YAC7D;oCAAO,IAAM,aAAa;;QAC5B;2BAAG;QAAC;QAAO;KAAO;IAClB,MAAM,iBAAiB,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;8CAAE;YACvC,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC9B,YAAY,OAAO,IAAI,CAAC,WAAW,YAAY,OAAO,IAAI;YAC1D,IAAI,YAAY,OAAO,GAAG,OAAO,YAAY,aAAa,KAAK,YAAY,OAAO,GAAG;YACrF,gBAAgB,OAAO,CAAC,SAAS,GAAG,kBAAkB,YAAY,OAAO;YACzE,IAAI,YAAY,OAAO,GAAG,UAAU,OAAO,OAAO,GAAG,sBAAsB;QAC7E;6CAAG;QAAC;QAAmB;KAAS;IAChC,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;4BAAE;YACd;YACA;oCAAO,IAAM,qBAAqB,OAAO,OAAO;;QAClD;2BAAG;QAAC;KAAe;IACnB,OAAO,QAAQ,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;QACrD,OAAO;YACL,GAAG,OAAO,SAAS;YACnB,SAAS,SAAS,IAAI;YACtB,GAAG,eAAe;QACpB;IACF,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,MAAM,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;QACvF,OAAO;YACL,GAAG,OAAO,KAAK;YACf,GAAG,WAAW;QAChB;IACF,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;QACzC,OAAO;YACL,GAAG,OAAO,GAAG;YACb,WAAW,CAAC,OAAO,EAAE,WAAW,IAAI,CAAC,CAAC;YACtC,GAAG,SAAS;QACd;IACF,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC3C,KAAK;QACL,OAAO;YACL,GAAG,OAAO,IAAI;YACd,GAAG,UAAU;QACf;IACF,QAAQ;AACV;AACA,MAAM,SAAS;IACb,WAAW;QACT,UAAU;QACV,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,YAAY;QACZ,QAAQ;IACV;IACA,OAAO;QACL,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,WAAW;IACb;IACA,KAAK;QACH,QAAQ;QACR,OAAO;QACP,YAAY;QACZ,YAAY;QACZ,iBAAiB;IACnB;IACA,MAAM;QACJ,SAAS;QACT,UAAU;QACV,oBAAoB;QACpB,WAAW;QACX,OAAO;QACP,UAAU;QACV,YAAY,CAAC,gLAAgL,CAAC;QAC9L,YAAY;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 733, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/DragControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { useGesture } from '@use-gesture/react';\n\nconst initialModelPosition = /* @__PURE__ */new THREE.Vector3();\nconst mousePosition2D = /* @__PURE__ */new THREE.Vector2();\nconst mousePosition3D = /* @__PURE__ */new THREE.Vector3();\nconst dragOffset = /* @__PURE__ */new THREE.Vector3();\nconst dragPlaneNormal = /* @__PURE__ */new THREE.Vector3();\nconst dragPlane = /* @__PURE__ */new THREE.Plane();\nconst DragControls = /*#__PURE__*/React.forwardRef(({\n  autoTransform = true,\n  matrix,\n  axisLock,\n  dragLimits,\n  onHover,\n  onDragStart,\n  onDrag,\n  onDragEnd,\n  children,\n  dragConfig,\n  ...props\n}, fRef) => {\n  const defaultControls = useThree(state => state.controls);\n  const {\n    camera,\n    size,\n    raycaster,\n    invalidate\n  } = useThree();\n  const ref = React.useRef(null);\n  const bind = useGesture({\n    onHover: ({\n      hovering\n    }) => onHover && onHover(hovering !== null && hovering !== void 0 ? hovering : false),\n    onDragStart: ({\n      event\n    }) => {\n      if (defaultControls) defaultControls.enabled = false;\n      const {\n        point\n      } = event;\n      ref.current.matrix.decompose(initialModelPosition, new THREE.Quaternion(), new THREE.Vector3());\n      mousePosition3D.copy(point);\n      dragOffset.copy(mousePosition3D).sub(initialModelPosition);\n      onDragStart && onDragStart(initialModelPosition);\n      invalidate();\n    },\n    onDrag: ({\n      xy: [dragX, dragY],\n      intentional\n    }) => {\n      if (!intentional) return;\n      const normalizedMouseX = (dragX - size.left) / size.width * 2 - 1;\n      const normalizedMouseY = -((dragY - size.top) / size.height) * 2 + 1;\n      mousePosition2D.set(normalizedMouseX, normalizedMouseY);\n      raycaster.setFromCamera(mousePosition2D, camera);\n      if (!axisLock) {\n        camera.getWorldDirection(dragPlaneNormal).negate();\n      } else {\n        switch (axisLock) {\n          case 'x':\n            dragPlaneNormal.set(1, 0, 0);\n            break;\n          case 'y':\n            dragPlaneNormal.set(0, 1, 0);\n            break;\n          case 'z':\n            dragPlaneNormal.set(0, 0, 1);\n            break;\n        }\n      }\n      dragPlane.setFromNormalAndCoplanarPoint(dragPlaneNormal, mousePosition3D);\n      raycaster.ray.intersectPlane(dragPlane, mousePosition3D);\n      const previousLocalMatrix = ref.current.matrix.clone();\n      const previousWorldMatrix = ref.current.matrixWorld.clone();\n      const intendedNewPosition = new THREE.Vector3(mousePosition3D.x - dragOffset.x, mousePosition3D.y - dragOffset.y, mousePosition3D.z - dragOffset.z);\n      if (dragLimits) {\n        intendedNewPosition.x = dragLimits[0] ? Math.max(Math.min(intendedNewPosition.x, dragLimits[0][1]), dragLimits[0][0]) : intendedNewPosition.x;\n        intendedNewPosition.y = dragLimits[1] ? Math.max(Math.min(intendedNewPosition.y, dragLimits[1][1]), dragLimits[1][0]) : intendedNewPosition.y;\n        intendedNewPosition.z = dragLimits[2] ? Math.max(Math.min(intendedNewPosition.z, dragLimits[2][1]), dragLimits[2][0]) : intendedNewPosition.z;\n      }\n      if (autoTransform) {\n        ref.current.matrix.setPosition(intendedNewPosition);\n        const deltaLocalMatrix = ref.current.matrix.clone().multiply(previousLocalMatrix.invert());\n        const deltaWorldMatrix = ref.current.matrix.clone().multiply(previousWorldMatrix.invert());\n        onDrag && onDrag(ref.current.matrix, deltaLocalMatrix, ref.current.matrixWorld, deltaWorldMatrix);\n      } else {\n        const tempMatrix = new THREE.Matrix4().copy(ref.current.matrix);\n        tempMatrix.setPosition(intendedNewPosition);\n        const deltaLocalMatrix = tempMatrix.clone().multiply(previousLocalMatrix.invert());\n        const deltaWorldMatrix = tempMatrix.clone().multiply(previousWorldMatrix.invert());\n        onDrag && onDrag(tempMatrix, deltaLocalMatrix, ref.current.matrixWorld, deltaWorldMatrix);\n      }\n      invalidate();\n    },\n    onDragEnd: () => {\n      if (defaultControls) defaultControls.enabled = true;\n      onDragEnd && onDragEnd();\n      invalidate();\n    }\n  }, {\n    drag: {\n      filterTaps: true,\n      threshold: 1,\n      ...(typeof dragConfig === 'object' ? dragConfig : {})\n    }\n  });\n  React.useImperativeHandle(fRef, () => ref.current, []);\n  React.useLayoutEffect(() => {\n    if (!matrix) return;\n\n    // If the matrix is a real matrix4 it means that the user wants to control the gizmo\n    // In that case it should just be set, as a bare prop update would merely copy it\n    ref.current.matrix = matrix;\n  }, [matrix]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, bind(), {\n    matrix: matrix,\n    matrixAutoUpdate: false\n  }, props), children);\n});\n\nexport { DragControls };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,MAAM,uBAAuB,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC7D,MAAM,kBAAkB,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AACxD,MAAM,kBAAkB,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AACxD,MAAM,aAAa,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AACnD,MAAM,kBAAkB,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AACxD,MAAM,YAAY,aAAa,GAAE,IAAI,sMAAA,CAAA,QAAW;AAChD,MAAM,eAAe,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAClD,gBAAgB,IAAI,EACpB,MAAM,EACN,QAAQ,EACR,UAAU,EACV,OAAO,EACP,WAAW,EACX,MAAM,EACN,SAAS,EACT,QAAQ,EACR,UAAU,EACV,GAAG,OACJ,EAAE;IACD,MAAM,kBAAkB,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;kDAAE,CAAA,QAAS,MAAM,QAAQ;;IACxD,MAAM,EACJ,MAAM,EACN,IAAI,EACJ,SAAS,EACT,UAAU,EACX,GAAG,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;IACX,MAAM,MAAM,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IACzB,MAAM,OAAO,CAAA,GAAA,mSAAA,CAAA,aAAU,AAAD,EAAE;QACtB,OAAO;6CAAE,CAAC,EACR,QAAQ,EACT,GAAK,WAAW,QAAQ,aAAa,QAAQ,aAAa,KAAK,IAAI,WAAW;;QAC/E,WAAW;6CAAE,CAAC,EACZ,KAAK,EACN;gBACC,IAAI,iBAAiB,gBAAgB,OAAO,GAAG;gBAC/C,MAAM,EACJ,KAAK,EACN,GAAG;gBACJ,IAAI,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,sBAAsB,IAAI,sMAAA,CAAA,aAAgB,IAAI,IAAI,sMAAA,CAAA,UAAa;gBAC5F,gBAAgB,IAAI,CAAC;gBACrB,WAAW,IAAI,CAAC,iBAAiB,GAAG,CAAC;gBACrC,eAAe,YAAY;gBAC3B;YACF;;QACA,MAAM;6CAAE,CAAC,EACP,IAAI,CAAC,OAAO,MAAM,EAClB,WAAW,EACZ;gBACC,IAAI,CAAC,aAAa;gBAClB,MAAM,mBAAmB,CAAC,QAAQ,KAAK,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI;gBAChE,MAAM,mBAAmB,CAAC,CAAC,CAAC,QAAQ,KAAK,GAAG,IAAI,KAAK,MAAM,IAAI,IAAI;gBACnE,gBAAgB,GAAG,CAAC,kBAAkB;gBACtC,UAAU,aAAa,CAAC,iBAAiB;gBACzC,IAAI,CAAC,UAAU;oBACb,OAAO,iBAAiB,CAAC,iBAAiB,MAAM;gBAClD,OAAO;oBACL,OAAQ;wBACN,KAAK;4BACH,gBAAgB,GAAG,CAAC,GAAG,GAAG;4BAC1B;wBACF,KAAK;4BACH,gBAAgB,GAAG,CAAC,GAAG,GAAG;4BAC1B;wBACF,KAAK;4BACH,gBAAgB,GAAG,CAAC,GAAG,GAAG;4BAC1B;oBACJ;gBACF;gBACA,UAAU,6BAA6B,CAAC,iBAAiB;gBACzD,UAAU,GAAG,CAAC,cAAc,CAAC,WAAW;gBACxC,MAAM,sBAAsB,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK;gBACpD,MAAM,sBAAsB,IAAI,OAAO,CAAC,WAAW,CAAC,KAAK;gBACzD,MAAM,sBAAsB,IAAI,sMAAA,CAAA,UAAa,CAAC,gBAAgB,CAAC,GAAG,WAAW,CAAC,EAAE,gBAAgB,CAAC,GAAG,WAAW,CAAC,EAAE,gBAAgB,CAAC,GAAG,WAAW,CAAC;gBAClJ,IAAI,YAAY;oBACd,oBAAoB,CAAC,GAAG,UAAU,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,oBAAoB,CAAC,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,oBAAoB,CAAC;oBAC7I,oBAAoB,CAAC,GAAG,UAAU,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,oBAAoB,CAAC,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,oBAAoB,CAAC;oBAC7I,oBAAoB,CAAC,GAAG,UAAU,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,oBAAoB,CAAC,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,oBAAoB,CAAC;gBAC/I;gBACA,IAAI,eAAe;oBACjB,IAAI,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC;oBAC/B,MAAM,mBAAmB,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,oBAAoB,MAAM;oBACvF,MAAM,mBAAmB,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,oBAAoB,MAAM;oBACvF,UAAU,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,kBAAkB,IAAI,OAAO,CAAC,WAAW,EAAE;gBAClF,OAAO;oBACL,MAAM,aAAa,IAAI,sMAAA,CAAA,UAAa,GAAG,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM;oBAC9D,WAAW,WAAW,CAAC;oBACvB,MAAM,mBAAmB,WAAW,KAAK,GAAG,QAAQ,CAAC,oBAAoB,MAAM;oBAC/E,MAAM,mBAAmB,WAAW,KAAK,GAAG,QAAQ,CAAC,oBAAoB,MAAM;oBAC/E,UAAU,OAAO,YAAY,kBAAkB,IAAI,OAAO,CAAC,WAAW,EAAE;gBAC1E;gBACA;YACF;;QACA,SAAS;6CAAE;gBACT,IAAI,iBAAiB,gBAAgB,OAAO,GAAG;gBAC/C,aAAa;gBACb;YACF;;IACF,GAAG;QACD,MAAM;YACJ,YAAY;YACZ,WAAW;YACX,GAAI,OAAO,eAAe,WAAW,aAAa,CAAC,CAAC;QACtD;IACF;IACA,CAAA,GAAA,4RAAA,CAAA,sBAAyB,AAAD,EAAE;4CAAM,IAAM,IAAI,OAAO;2CAAE,EAAE;IACrD,CAAA,GAAA,4RAAA,CAAA,kBAAqB,AAAD;wCAAE;YACpB,IAAI,CAAC,QAAQ;YAEb,oFAAoF;YACpF,iFAAiF;YACjF,IAAI,OAAO,CAAC,MAAM,GAAG;QACvB;uCAAG;QAAC;KAAO;IACX,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;QACxD,KAAK;IACP,GAAG,QAAQ;QACT,QAAQ;QACR,kBAAkB;IACpB,GAAG,QAAQ;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 855, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 861, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/ScrollControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom/client';\nimport { useThree, useFrame, context as context$1 } from '@react-three/fiber';\nimport { easing } from 'maath';\n\nconst context = /* @__PURE__ */React.createContext(null);\nfunction useScroll() {\n  return React.useContext(context);\n}\nfunction ScrollControls({\n  eps = 0.00001,\n  enabled = true,\n  infinite,\n  horizontal,\n  pages = 1,\n  distance = 1,\n  damping = 0.25,\n  maxSpeed = Infinity,\n  prepend = false,\n  style = {},\n  children\n}) {\n  const {\n    get,\n    setEvents,\n    gl,\n    size,\n    invalidate,\n    events\n  } = useThree();\n  const [el] = React.useState(() => document.createElement('div'));\n  const [fill] = React.useState(() => document.createElement('div'));\n  const [fixed] = React.useState(() => document.createElement('div'));\n  const target = gl.domElement.parentNode;\n  const scroll = React.useRef(0);\n  const state = React.useMemo(() => {\n    const state = {\n      el,\n      eps,\n      fill,\n      fixed,\n      horizontal,\n      damping,\n      offset: 0,\n      delta: 0,\n      scroll,\n      pages,\n      // 0-1 for a range between from -> from + distance\n      range(from, distance, margin = 0) {\n        const start = from - margin;\n        const end = start + distance + margin * 2;\n        return this.offset < start ? 0 : this.offset > end ? 1 : (this.offset - start) / (end - start);\n      },\n      // 0-1-0 for a range between from -> from + distance\n      curve(from, distance, margin = 0) {\n        return Math.sin(this.range(from, distance, margin) * Math.PI);\n      },\n      // true/false for a range between from -> from + distance\n      visible(from, distance, margin = 0) {\n        const start = from - margin;\n        const end = start + distance + margin * 2;\n        return this.offset >= start && this.offset <= end;\n      }\n    };\n    return state;\n  }, [eps, damping, horizontal, pages]);\n  React.useEffect(() => {\n    el.style.position = 'absolute';\n    el.style.width = '100%';\n    el.style.height = '100%';\n    el.style[horizontal ? 'overflowX' : 'overflowY'] = 'auto';\n    el.style[horizontal ? 'overflowY' : 'overflowX'] = 'hidden';\n    el.style.top = '0px';\n    el.style.left = '0px';\n    for (const key in style) {\n      el.style[key] = style[key];\n    }\n    fixed.style.position = 'sticky';\n    fixed.style.top = '0px';\n    fixed.style.left = '0px';\n    fixed.style.width = '100%';\n    fixed.style.height = '100%';\n    fixed.style.overflow = 'hidden';\n    el.appendChild(fixed);\n    fill.style.height = horizontal ? '100%' : `${pages * distance * 100}%`;\n    fill.style.width = horizontal ? `${pages * distance * 100}%` : '100%';\n    fill.style.pointerEvents = 'none';\n    el.appendChild(fill);\n    if (prepend) target.prepend(el);else target.appendChild(el);\n\n    // Init scroll one pixel in to allow upward/leftward scroll\n    el[horizontal ? 'scrollLeft' : 'scrollTop'] = 1;\n    const oldTarget = events.connected || gl.domElement;\n    requestAnimationFrame(() => events.connect == null ? void 0 : events.connect(el));\n    const oldCompute = get().events.compute;\n    setEvents({\n      compute(event, state) {\n        // we are using boundingClientRect because we could not rely on target.offsetTop as canvas could be positioned anywhere in dom\n        const {\n          left,\n          top\n        } = target.getBoundingClientRect();\n        const offsetX = event.clientX - left;\n        const offsetY = event.clientY - top;\n        state.pointer.set(offsetX / state.size.width * 2 - 1, -(offsetY / state.size.height) * 2 + 1);\n        state.raycaster.setFromCamera(state.pointer, state.camera);\n      }\n    });\n    return () => {\n      target.removeChild(el);\n      setEvents({\n        compute: oldCompute\n      });\n      events.connect == null || events.connect(oldTarget);\n    };\n  }, [pages, distance, horizontal, el, fill, fixed, target]);\n  React.useEffect(() => {\n    if (events.connected === el) {\n      const containerLength = size[horizontal ? 'width' : 'height'];\n      const scrollLength = el[horizontal ? 'scrollWidth' : 'scrollHeight'];\n      const scrollThreshold = scrollLength - containerLength;\n      let current = 0;\n      let disableScroll = true;\n      let firstRun = true;\n      const onScroll = () => {\n        // Prevent first scroll because it is indirectly caused by the one pixel offset\n        if (!enabled || firstRun) return;\n        invalidate();\n        current = el[horizontal ? 'scrollLeft' : 'scrollTop'];\n        scroll.current = current / scrollThreshold;\n        if (infinite) {\n          if (!disableScroll) {\n            if (current >= scrollThreshold) {\n              const damp = 1 - state.offset;\n              el[horizontal ? 'scrollLeft' : 'scrollTop'] = 1;\n              scroll.current = state.offset = -damp;\n              disableScroll = true;\n            } else if (current <= 0) {\n              const damp = 1 + state.offset;\n              el[horizontal ? 'scrollLeft' : 'scrollTop'] = scrollLength;\n              scroll.current = state.offset = damp;\n              disableScroll = true;\n            }\n          }\n          if (disableScroll) setTimeout(() => disableScroll = false, 40);\n        }\n      };\n      el.addEventListener('scroll', onScroll, {\n        passive: true\n      });\n      requestAnimationFrame(() => firstRun = false);\n      const onWheel = e => el.scrollLeft += e.deltaY / 2;\n      if (horizontal) el.addEventListener('wheel', onWheel, {\n        passive: true\n      });\n      return () => {\n        el.removeEventListener('scroll', onScroll);\n        if (horizontal) el.removeEventListener('wheel', onWheel);\n      };\n    }\n  }, [el, events, size, infinite, state, invalidate, horizontal, enabled]);\n  let last = 0;\n  useFrame((_, delta) => {\n    last = state.offset;\n    easing.damp(state, 'offset', scroll.current, damping, delta, maxSpeed, undefined, eps);\n    easing.damp(state, 'delta', Math.abs(last - state.offset), damping, delta, maxSpeed, undefined, eps);\n    if (state.delta > eps) invalidate();\n  });\n  return /*#__PURE__*/React.createElement(context.Provider, {\n    value: state\n  }, children);\n}\nconst ScrollCanvas = /* @__PURE__ */React.forwardRef(({\n  children\n}, ref) => {\n  const group = React.useRef(null);\n  React.useImperativeHandle(ref, () => group.current, []);\n  const state = useScroll();\n  const {\n    width,\n    height\n  } = useThree(state => state.viewport);\n  useFrame(() => {\n    group.current.position.x = state.horizontal ? -width * (state.pages - 1) * state.offset : 0;\n    group.current.position.y = state.horizontal ? 0 : height * (state.pages - 1) * state.offset;\n  });\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: group\n  }, children);\n});\nconst ScrollHtml = /*#__PURE__*/React.forwardRef(({\n  children,\n  style,\n  ...props\n}, ref) => {\n  const state = useScroll();\n  const group = React.useRef(null);\n  React.useImperativeHandle(ref, () => group.current, []);\n  const {\n    width,\n    height\n  } = useThree(state => state.size);\n  const fiberState = React.useContext(context$1);\n  const root = React.useMemo(() => ReactDOM.createRoot(state.fixed), [state.fixed]);\n  useFrame(() => {\n    if (state.delta > state.eps) {\n      group.current.style.transform = `translate3d(${state.horizontal ? -width * (state.pages - 1) * state.offset : 0}px,${state.horizontal ? 0 : height * (state.pages - 1) * -state.offset}px,0)`;\n    }\n  });\n  root.render(/*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: group,\n    style: {\n      ...style,\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      willChange: 'transform'\n    }\n  }, props), /*#__PURE__*/React.createElement(context.Provider, {\n    value: state\n  }, /*#__PURE__*/React.createElement(context$1.Provider, {\n    value: fiberState\n  }, children))));\n  return null;\n});\nconst Scroll = /* @__PURE__ */React.forwardRef(({\n  html,\n  ...props\n}, ref) => {\n  const El = html ? ScrollHtml : ScrollCanvas;\n  return /*#__PURE__*/React.createElement(El, _extends({\n    ref: ref\n  }, props));\n});\n\nexport { Scroll, ScrollControls, useScroll };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;;;;;;AAEA,MAAM,UAAU,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE;AACnD,SAAS;IACP,OAAO,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE;AAC1B;AACA,SAAS,eAAe,EACtB,MAAM,OAAO,EACb,UAAU,IAAI,EACd,QAAQ,EACR,UAAU,EACV,QAAQ,CAAC,EACT,WAAW,CAAC,EACZ,UAAU,IAAI,EACd,WAAW,QAAQ,EACnB,UAAU,KAAK,EACf,QAAQ,CAAC,CAAC,EACV,QAAQ,EACT;IACC,MAAM,EACJ,GAAG,EACH,SAAS,EACT,EAAE,EACF,IAAI,EACJ,UAAU,EACV,MAAM,EACP,GAAG,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;IACX,MAAM,CAAC,GAAG,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD;mCAAE,IAAM,SAAS,aAAa,CAAC;;IACzD,MAAM,CAAC,KAAK,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD;mCAAE,IAAM,SAAS,aAAa,CAAC;;IAC3D,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD;mCAAE,IAAM,SAAS,aAAa,CAAC;;IAC5D,MAAM,SAAS,GAAG,UAAU,CAAC,UAAU;IACvC,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC5B,MAAM,QAAQ,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;yCAAE;YAC1B,MAAM,QAAQ;gBACZ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,QAAQ;gBACR,OAAO;gBACP;gBACA;gBACA,kDAAkD;gBAClD,OAAM,IAAI,EAAE,QAAQ,EAAE,SAAS,CAAC;oBAC9B,MAAM,QAAQ,OAAO;oBACrB,MAAM,MAAM,QAAQ,WAAW,SAAS;oBACxC,OAAO,IAAI,CAAC,MAAM,GAAG,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,MAAM,KAAK;gBAC/F;gBACA,oDAAoD;gBACpD,OAAM,IAAI,EAAE,QAAQ,EAAE,SAAS,CAAC;oBAC9B,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,UAAU,UAAU,KAAK,EAAE;gBAC9D;gBACA,yDAAyD;gBACzD,SAAQ,IAAI,EAAE,QAAQ,EAAE,SAAS,CAAC;oBAChC,MAAM,QAAQ,OAAO;oBACrB,MAAM,MAAM,QAAQ,WAAW,SAAS;oBACxC,OAAO,IAAI,CAAC,MAAM,IAAI,SAAS,IAAI,CAAC,MAAM,IAAI;gBAChD;YACF;YACA,OAAO;QACT;wCAAG;QAAC;QAAK;QAAS;QAAY;KAAM;IACpC,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;oCAAE;YACd,GAAG,KAAK,CAAC,QAAQ,GAAG;YACpB,GAAG,KAAK,CAAC,KAAK,GAAG;YACjB,GAAG,KAAK,CAAC,MAAM,GAAG;YAClB,GAAG,KAAK,CAAC,aAAa,cAAc,YAAY,GAAG;YACnD,GAAG,KAAK,CAAC,aAAa,cAAc,YAAY,GAAG;YACnD,GAAG,KAAK,CAAC,GAAG,GAAG;YACf,GAAG,KAAK,CAAC,IAAI,GAAG;YAChB,IAAK,MAAM,OAAO,MAAO;gBACvB,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;YAC5B;YACA,MAAM,KAAK,CAAC,QAAQ,GAAG;YACvB,MAAM,KAAK,CAAC,GAAG,GAAG;YAClB,MAAM,KAAK,CAAC,IAAI,GAAG;YACnB,MAAM,KAAK,CAAC,KAAK,GAAG;YACpB,MAAM,KAAK,CAAC,MAAM,GAAG;YACrB,MAAM,KAAK,CAAC,QAAQ,GAAG;YACvB,GAAG,WAAW,CAAC;YACf,KAAK,KAAK,CAAC,MAAM,GAAG,aAAa,SAAS,GAAG,QAAQ,WAAW,IAAI,CAAC,CAAC;YACtE,KAAK,KAAK,CAAC,KAAK,GAAG,aAAa,GAAG,QAAQ,WAAW,IAAI,CAAC,CAAC,GAAG;YAC/D,KAAK,KAAK,CAAC,aAAa,GAAG;YAC3B,GAAG,WAAW,CAAC;YACf,IAAI,SAAS,OAAO,OAAO,CAAC;iBAAS,OAAO,WAAW,CAAC;YAExD,2DAA2D;YAC3D,EAAE,CAAC,aAAa,eAAe,YAAY,GAAG;YAC9C,MAAM,YAAY,OAAO,SAAS,IAAI,GAAG,UAAU;YACnD;4CAAsB,IAAM,OAAO,OAAO,IAAI,OAAO,KAAK,IAAI,OAAO,OAAO,CAAC;;YAC7E,MAAM,aAAa,MAAM,MAAM,CAAC,OAAO;YACvC,UAAU;gBACR,SAAQ,KAAK,EAAE,KAAK;oBAClB,8HAA8H;oBAC9H,MAAM,EACJ,IAAI,EACJ,GAAG,EACJ,GAAG,OAAO,qBAAqB;oBAChC,MAAM,UAAU,MAAM,OAAO,GAAG;oBAChC,MAAM,UAAU,MAAM,OAAO,GAAG;oBAChC,MAAM,OAAO,CAAC,GAAG,CAAC,UAAU,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,UAAU,MAAM,IAAI,CAAC,MAAM,IAAI,IAAI;oBAC3F,MAAM,SAAS,CAAC,aAAa,CAAC,MAAM,OAAO,EAAE,MAAM,MAAM;gBAC3D;YACF;YACA;4CAAO;oBACL,OAAO,WAAW,CAAC;oBACnB,UAAU;wBACR,SAAS;oBACX;oBACA,OAAO,OAAO,IAAI,QAAQ,OAAO,OAAO,CAAC;gBAC3C;;QACF;mCAAG;QAAC;QAAO;QAAU;QAAY;QAAI;QAAM;QAAO;KAAO;IACzD,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;oCAAE;YACd,IAAI,OAAO,SAAS,KAAK,IAAI;gBAC3B,MAAM,kBAAkB,IAAI,CAAC,aAAa,UAAU,SAAS;gBAC7D,MAAM,eAAe,EAAE,CAAC,aAAa,gBAAgB,eAAe;gBACpE,MAAM,kBAAkB,eAAe;gBACvC,IAAI,UAAU;gBACd,IAAI,gBAAgB;gBACpB,IAAI,WAAW;gBACf,MAAM;yDAAW;wBACf,+EAA+E;wBAC/E,IAAI,CAAC,WAAW,UAAU;wBAC1B;wBACA,UAAU,EAAE,CAAC,aAAa,eAAe,YAAY;wBACrD,OAAO,OAAO,GAAG,UAAU;wBAC3B,IAAI,UAAU;4BACZ,IAAI,CAAC,eAAe;gCAClB,IAAI,WAAW,iBAAiB;oCAC9B,MAAM,OAAO,IAAI,MAAM,MAAM;oCAC7B,EAAE,CAAC,aAAa,eAAe,YAAY,GAAG;oCAC9C,OAAO,OAAO,GAAG,MAAM,MAAM,GAAG,CAAC;oCACjC,gBAAgB;gCAClB,OAAO,IAAI,WAAW,GAAG;oCACvB,MAAM,OAAO,IAAI,MAAM,MAAM;oCAC7B,EAAE,CAAC,aAAa,eAAe,YAAY,GAAG;oCAC9C,OAAO,OAAO,GAAG,MAAM,MAAM,GAAG;oCAChC,gBAAgB;gCAClB;4BACF;4BACA,IAAI,eAAe;qEAAW,IAAM,gBAAgB;oEAAO;wBAC7D;oBACF;;gBACA,GAAG,gBAAgB,CAAC,UAAU,UAAU;oBACtC,SAAS;gBACX;gBACA;gDAAsB,IAAM,WAAW;;gBACvC,MAAM;wDAAU,CAAA,IAAK,GAAG,UAAU,IAAI,EAAE,MAAM,GAAG;;gBACjD,IAAI,YAAY,GAAG,gBAAgB,CAAC,SAAS,SAAS;oBACpD,SAAS;gBACX;gBACA;gDAAO;wBACL,GAAG,mBAAmB,CAAC,UAAU;wBACjC,IAAI,YAAY,GAAG,mBAAmB,CAAC,SAAS;oBAClD;;YACF;QACF;mCAAG;QAAC;QAAI;QAAQ;QAAM;QAAU;QAAO;QAAY;QAAY;KAAQ;IACvE,IAAI,OAAO;IACX,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;mCAAE,CAAC,GAAG;YACX,OAAO,MAAM,MAAM;YACnB,0SAAA,CAAA,SAAM,CAAC,IAAI,CAAC,OAAO,UAAU,OAAO,OAAO,EAAE,SAAS,OAAO,UAAU,WAAW;YAClF,0SAAA,CAAA,SAAM,CAAC,IAAI,CAAC,OAAO,SAAS,KAAK,GAAG,CAAC,OAAO,MAAM,MAAM,GAAG,SAAS,OAAO,UAAU,WAAW;YAChG,IAAI,MAAM,KAAK,GAAG,KAAK;QACzB;;IACA,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ,QAAQ,EAAE;QACxD,OAAO;IACT,GAAG;AACL;AACA,MAAM,eAAe,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EACpD,QAAQ,EACT,EAAE;IACD,MAAM,QAAQ,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC3B,CAAA,GAAA,4RAAA,CAAA,sBAAyB,AAAD,EAAE;4CAAK,IAAM,MAAM,OAAO;2CAAE,EAAE;IACtD,MAAM,QAAQ;IACd,MAAM,EACJ,KAAK,EACL,MAAM,EACP,GAAG,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;iCAAE,CAAA,QAAS,MAAM,QAAQ;;IACpC,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;iCAAE;YACP,MAAM,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,UAAU,GAAG,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG;YAC1F,MAAM,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,UAAU,GAAG,IAAI,SAAS,CAAC,MAAM,KAAK,GAAG,CAAC,IAAI,MAAM,MAAM;QAC7F;;IACA,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC/C,KAAK;IACP,GAAG;AACL;AACA,MAAM,aAAa,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAChD,QAAQ,EACR,KAAK,EACL,GAAG,OACJ,EAAE;IACD,MAAM,QAAQ;IACd,MAAM,QAAQ,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC3B,CAAA,GAAA,4RAAA,CAAA,sBAAyB,AAAD,EAAE;0CAAK,IAAM,MAAM,OAAO;yCAAE,EAAE;IACtD,MAAM,EACJ,KAAK,EACL,MAAM,EACP,GAAG,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;+BAAE,CAAA,QAAS,MAAM,IAAI;;IAChC,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,+ZAAA,CAAA,UAAS;IAC7C,MAAM,OAAO,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;oCAAE,IAAM,CAAA,GAAA,oSAAA,CAAA,aAAmB,AAAD,EAAE,MAAM,KAAK;mCAAG;QAAC,MAAM,KAAK;KAAC;IAChF,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;+BAAE;YACP,IAAI,MAAM,KAAK,GAAG,MAAM,GAAG,EAAE;gBAC3B,MAAM,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,YAAY,EAAE,MAAM,UAAU,GAAG,CAAC,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAC,IAAI,MAAM,MAAM,GAAG,EAAE,GAAG,EAAE,MAAM,UAAU,GAAG,IAAI,SAAS,CAAC,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC;YAC/L;QACF;;IACA,KAAK,MAAM,CAAC,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;QAC3D,KAAK;QACL,OAAO;YACL,GAAG,KAAK;YACR,UAAU;YACV,KAAK;YACL,MAAM;YACN,YAAY;QACd;IACF,GAAG,QAAQ,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ,QAAQ,EAAE;QAC5D,OAAO;IACT,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,+ZAAA,CAAA,UAAS,CAAC,QAAQ,EAAE;QACtD,OAAO;IACT,GAAG;IACH,OAAO;AACT;AACA,MAAM,SAAS,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAC9C,IAAI,EACJ,GAAG,OACJ,EAAE;IACD,MAAM,KAAK,OAAO,aAAa;IAC/B,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,IAAI,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;QACnD,KAAK;IACP,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1138, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1144, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/PresentationControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { MathUtils } from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { useGesture } from '@use-gesture/react';\nimport { easing } from 'maath';\n\nfunction PresentationControls({\n  enabled = true,\n  snap,\n  global,\n  domElement,\n  cursor = true,\n  children,\n  speed = 1,\n  rotation = [0, 0, 0],\n  zoom = 1,\n  polar = [0, Math.PI / 2],\n  azimuth = [-Infinity, Infinity],\n  damping = 0.25\n}) {\n  const events = useThree(state => state.events);\n  const gl = useThree(state => state.gl);\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const {\n    size\n  } = useThree();\n  const rPolar = React.useMemo(() => [rotation[0] + polar[0], rotation[0] + polar[1]], [rotation[0], polar[0], polar[1]]);\n  const rAzimuth = React.useMemo(() => [rotation[1] + azimuth[0], rotation[1] + azimuth[1]], [rotation[1], azimuth[0], azimuth[1]]);\n  const rInitial = React.useMemo(() => [MathUtils.clamp(rotation[0], ...rPolar), MathUtils.clamp(rotation[1], ...rAzimuth), rotation[2]], [rotation[0], rotation[1], rotation[2], rPolar, rAzimuth]);\n  React.useEffect(() => {\n    if (global && cursor && enabled) {\n      explDomElement.style.cursor = 'grab';\n      gl.domElement.style.cursor = '';\n      return () => {\n        explDomElement.style.cursor = 'default';\n        gl.domElement.style.cursor = 'default';\n      };\n    }\n  }, [global, cursor, explDomElement, enabled]);\n  const [animation] = React.useState({\n    scale: 1,\n    rotation: rInitial,\n    damping\n  });\n  const ref = React.useRef(null);\n  useFrame((state, delta) => {\n    easing.damp3(ref.current.scale, animation.scale, animation.damping, delta);\n    easing.dampE(ref.current.rotation, animation.rotation, animation.damping, delta);\n  });\n  const bind = useGesture({\n    onHover: ({\n      last\n    }) => {\n      if (cursor && !global && enabled) explDomElement.style.cursor = last ? 'auto' : 'grab';\n    },\n    onDrag: ({\n      down,\n      delta: [x, y],\n      memo: [oldY, oldX] = animation.rotation || rInitial\n    }) => {\n      if (!enabled) return [y, x];\n      if (cursor) explDomElement.style.cursor = down ? 'grabbing' : 'grab';\n      x = MathUtils.clamp(oldX + x / size.width * Math.PI * speed, ...rAzimuth);\n      y = MathUtils.clamp(oldY + y / size.height * Math.PI * speed, ...rPolar);\n      animation.scale = down && y > rPolar[1] / 2 ? zoom : 1;\n      animation.rotation = snap && !down ? rInitial : [y, x, 0];\n      animation.damping = snap && !down && typeof snap !== 'boolean' ? snap : damping;\n      return [y, x];\n    }\n  }, {\n    target: global ? explDomElement : undefined\n  });\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref\n  }, bind == null ? void 0 : bind()), children);\n}\n\nexport { PresentationControls };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;;;;;;;AAEA,SAAS,qBAAqB,EAC5B,UAAU,IAAI,EACd,IAAI,EACJ,MAAM,EACN,UAAU,EACV,SAAS,IAAI,EACb,QAAQ,EACR,QAAQ,CAAC,EACT,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,OAAO,CAAC,EACR,QAAQ;IAAC;IAAG,KAAK,EAAE,GAAG;CAAE,EACxB,UAAU;IAAC,CAAC;IAAU;CAAS,EAC/B,UAAU,IAAI,EACf;IACC,MAAM,SAAS,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;iDAAE,CAAA,QAAS,MAAM,MAAM;;IAC7C,MAAM,KAAK,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;6CAAE,CAAA,QAAS,MAAM,EAAE;;IACrC,MAAM,iBAAiB,cAAc,OAAO,SAAS,IAAI,GAAG,UAAU;IACtE,MAAM,EACJ,IAAI,EACL,GAAG,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;IACX,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;gDAAE,IAAM;gBAAC,QAAQ,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;gBAAE,QAAQ,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;aAAC;+CAAE;QAAC,QAAQ,CAAC,EAAE;QAAE,KAAK,CAAC,EAAE;QAAE,KAAK,CAAC,EAAE;KAAC;IACtH,MAAM,WAAW,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;kDAAE,IAAM;gBAAC,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;gBAAE,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;aAAC;iDAAE;QAAC,QAAQ,CAAC,EAAE;QAAE,OAAO,CAAC,EAAE;QAAE,OAAO,CAAC,EAAE;KAAC;IAChI,MAAM,WAAW,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;kDAAE,IAAM;gBAAC,sMAAA,CAAA,YAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,KAAK;gBAAS,sMAAA,CAAA,YAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,KAAK;gBAAW,QAAQ,CAAC,EAAE;aAAC;iDAAE;QAAC,QAAQ,CAAC,EAAE;QAAE,QAAQ,CAAC,EAAE;QAAE,QAAQ,CAAC,EAAE;QAAE;QAAQ;KAAS;IACjM,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;0CAAE;YACd,IAAI,UAAU,UAAU,SAAS;gBAC/B,eAAe,KAAK,CAAC,MAAM,GAAG;gBAC9B,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG;gBAC7B;sDAAO;wBACL,eAAe,KAAK,CAAC,MAAM,GAAG;wBAC9B,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG;oBAC/B;;YACF;QACF;yCAAG;QAAC;QAAQ;QAAQ;QAAgB;KAAQ;IAC5C,MAAM,CAAC,UAAU,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD,EAAE;QACjC,OAAO;QACP,UAAU;QACV;IACF;IACA,MAAM,MAAM,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IACzB,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;yCAAE,CAAC,OAAO;YACf,0SAAA,CAAA,SAAM,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,EAAE,UAAU,KAAK,EAAE,UAAU,OAAO,EAAE;YACpE,0SAAA,CAAA,SAAM,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,QAAQ,EAAE,UAAU,QAAQ,EAAE,UAAU,OAAO,EAAE;QAC5E;;IACA,MAAM,OAAO,CAAA,GAAA,mSAAA,CAAA,aAAU,AAAD,EAAE;QACtB,OAAO;qDAAE,CAAC,EACR,IAAI,EACL;gBACC,IAAI,UAAU,CAAC,UAAU,SAAS,eAAe,KAAK,CAAC,MAAM,GAAG,OAAO,SAAS;YAClF;;QACA,MAAM;qDAAE,CAAC,EACP,IAAI,EACJ,OAAO,CAAC,GAAG,EAAE,EACb,MAAM,CAAC,MAAM,KAAK,GAAG,UAAU,QAAQ,IAAI,QAAQ,EACpD;gBACC,IAAI,CAAC,SAAS,OAAO;oBAAC;oBAAG;iBAAE;gBAC3B,IAAI,QAAQ,eAAe,KAAK,CAAC,MAAM,GAAG,OAAO,aAAa;gBAC9D,IAAI,sMAAA,CAAA,YAAS,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,KAAK,GAAG,KAAK,EAAE,GAAG,UAAU;gBAChE,IAAI,sMAAA,CAAA,YAAS,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,MAAM,GAAG,KAAK,EAAE,GAAG,UAAU;gBACjE,UAAU,KAAK,GAAG,QAAQ,IAAI,MAAM,CAAC,EAAE,GAAG,IAAI,OAAO;gBACrD,UAAU,QAAQ,GAAG,QAAQ,CAAC,OAAO,WAAW;oBAAC;oBAAG;oBAAG;iBAAE;gBACzD,UAAU,OAAO,GAAG,QAAQ,CAAC,QAAQ,OAAO,SAAS,YAAY,OAAO;gBACxE,OAAO;oBAAC;oBAAG;iBAAE;YACf;;IACF,GAAG;QACD,QAAQ,SAAS,iBAAiB;IACpC;IACA,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;QACxD,KAAK;IACP,GAAG,QAAQ,OAAO,KAAK,IAAI,SAAS;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1280, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1286, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/KeyboardControls.js"], "sourcesContent": ["import * as React from 'react';\nimport { create } from 'zustand';\nimport { subscribeWithSelector } from 'zustand/middleware';\n\n// These are removed in Zustand v4\n// unknown\n\n// Zustand v3 marked deprecations in 3.x, but there's no visible upgrade path\n\nconst context = /* @__PURE__ */React.createContext(null);\nfunction KeyboardControls({\n  map,\n  children,\n  onChange,\n  domElement\n}) {\n  const key = map.map(item => item.name + item.keys).join('-');\n  const useControls = React.useMemo(() => {\n    return create(subscribeWithSelector(() => map.reduce((prev, cur) => ({\n      ...prev,\n      [cur.name]: false\n    }), {})));\n  }, [key]);\n  const api = React.useMemo(() => [useControls.subscribe, useControls.getState, useControls], [key]);\n  const set = useControls.setState;\n  React.useEffect(() => {\n    const config = map.map(({\n      name,\n      keys,\n      up\n    }) => ({\n      keys,\n      up,\n      fn: value => {\n        // Set zustand state\n        set({\n          [name]: value\n        });\n        // Inform callback\n        if (onChange) onChange(name, value, api[1]());\n      }\n    }));\n    const keyMap = config.reduce((out, {\n      keys,\n      fn,\n      up = true\n    }) => {\n      keys.forEach(key => out[key] = {\n        fn,\n        pressed: false,\n        up\n      });\n      return out;\n    }, {});\n    const downHandler = ({\n      key,\n      code\n    }) => {\n      const obj = keyMap[key] || keyMap[code];\n      if (!obj) return;\n      const {\n        fn,\n        pressed,\n        up\n      } = obj;\n      obj.pressed = true;\n      if (up || !pressed) fn(true);\n    };\n    const upHandler = ({\n      key,\n      code\n    }) => {\n      const obj = keyMap[key] || keyMap[code];\n      if (!obj) return;\n      const {\n        fn,\n        up\n      } = obj;\n      obj.pressed = false;\n      if (up) fn(false);\n    };\n    const source = domElement || window;\n    source.addEventListener('keydown', downHandler, {\n      passive: true\n    });\n    source.addEventListener('keyup', upHandler, {\n      passive: true\n    });\n    return () => {\n      source.removeEventListener('keydown', downHandler);\n      source.removeEventListener('keyup', upHandler);\n    };\n  }, [domElement, key]);\n  return /*#__PURE__*/React.createElement(context.Provider, {\n    value: api,\n    children: children\n  });\n}\nfunction useKeyboardControls(sel) {\n  const [sub, get, store] = React.useContext(context);\n  if (sel) return store(sel);else return [sub, get];\n}\n\nexport { KeyboardControls, useKeyboardControls };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,kCAAkC;AAClC,UAAU;AAEV,6EAA6E;AAE7E,MAAM,UAAU,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE;AACnD,SAAS,iBAAiB,EACxB,GAAG,EACH,QAAQ,EACR,QAAQ,EACR,UAAU,EACX;IACC,MAAM,MAAM,IAAI,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,EAAE,IAAI,CAAC;IACxD,MAAM,cAAc,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;iDAAE;YAChC,OAAO,CAAA,GAAA,gUAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,qUAAA,CAAA,wBAAqB,AAAD;yDAAE,IAAM,IAAI,MAAM;iEAAC,CAAC,MAAM,MAAQ,CAAC;gCACnE,GAAG,IAAI;gCACP,CAAC,IAAI,IAAI,CAAC,EAAE;4BACd,CAAC;gEAAG,CAAC;;QACP;gDAAG;QAAC;KAAI;IACR,MAAM,MAAM,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;yCAAE,IAAM;gBAAC,YAAY,SAAS;gBAAE,YAAY,QAAQ;gBAAE;aAAY;wCAAE;QAAC;KAAI;IACjG,MAAM,MAAM,YAAY,QAAQ;IAChC,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;sCAAE;YACd,MAAM,SAAS,IAAI,GAAG;qDAAC,CAAC,EACtB,IAAI,EACJ,IAAI,EACJ,EAAE,EACH,GAAK,CAAC;wBACL;wBACA;wBACA,EAAE;iEAAE,CAAA;gCACF,oBAAoB;gCACpB,IAAI;oCACF,CAAC,KAAK,EAAE;gCACV;gCACA,kBAAkB;gCAClB,IAAI,UAAU,SAAS,MAAM,OAAO,GAAG,CAAC,EAAE;4BAC5C;;oBACF,CAAC;;YACD,MAAM,SAAS,OAAO,MAAM;qDAAC,CAAC,KAAK,EACjC,IAAI,EACJ,EAAE,EACF,KAAK,IAAI,EACV;oBACC,KAAK,OAAO;6DAAC,CAAA,MAAO,GAAG,CAAC,IAAI,GAAG;gCAC7B;gCACA,SAAS;gCACT;4BACF;;oBACA,OAAO;gBACT;oDAAG,CAAC;YACJ,MAAM;0DAAc,CAAC,EACnB,GAAG,EACH,IAAI,EACL;oBACC,MAAM,MAAM,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,KAAK;oBACvC,IAAI,CAAC,KAAK;oBACV,MAAM,EACJ,EAAE,EACF,OAAO,EACP,EAAE,EACH,GAAG;oBACJ,IAAI,OAAO,GAAG;oBACd,IAAI,MAAM,CAAC,SAAS,GAAG;gBACzB;;YACA,MAAM;wDAAY,CAAC,EACjB,GAAG,EACH,IAAI,EACL;oBACC,MAAM,MAAM,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,KAAK;oBACvC,IAAI,CAAC,KAAK;oBACV,MAAM,EACJ,EAAE,EACF,EAAE,EACH,GAAG;oBACJ,IAAI,OAAO,GAAG;oBACd,IAAI,IAAI,GAAG;gBACb;;YACA,MAAM,SAAS,cAAc;YAC7B,OAAO,gBAAgB,CAAC,WAAW,aAAa;gBAC9C,SAAS;YACX;YACA,OAAO,gBAAgB,CAAC,SAAS,WAAW;gBAC1C,SAAS;YACX;YACA;8CAAO;oBACL,OAAO,mBAAmB,CAAC,WAAW;oBACtC,OAAO,mBAAmB,CAAC,SAAS;gBACtC;;QACF;qCAAG;QAAC;QAAY;KAAI;IACpB,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ,QAAQ,EAAE;QACxD,OAAO;QACP,UAAU;IACZ;AACF;AACA,SAAS,oBAAoB,GAAG;IAC9B,MAAM,CAAC,KAAK,KAAK,MAAM,GAAG,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE;IAC3C,IAAI,KAAK,OAAO,MAAM;SAAU,OAAO;QAAC;QAAK;KAAI;AACnD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1406, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1412, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/Select.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { SelectionBox } from 'three-stdlib';\nimport { useThree } from '@react-three/fiber';\nimport { shallow } from 'zustand/shallow';\n\nconst context = /* @__PURE__ */React.createContext([]);\nfunction Select({\n  box,\n  multiple,\n  children,\n  onChange,\n  onChangePointerUp,\n  border = '1px solid #55aaff',\n  backgroundColor = 'rgba(75, 160, 255, 0.1)',\n  filter: customFilter = item => item,\n  ...props\n}) {\n  const [downed, down] = React.useState(false);\n  const {\n    setEvents,\n    camera,\n    raycaster,\n    gl,\n    controls,\n    size,\n    get\n  } = useThree();\n  const [hovered, hover] = React.useState(false);\n  const [active, dispatch] = React.useReducer(\n  // @ts-expect-error\n  (state, {\n    object,\n    shift\n  }) => {\n    if (object === undefined) return [];else if (Array.isArray(object)) return object;else if (!shift) return state[0] === object ? [] : [object];\n    // @ts-expect-error\n    else if (state.includes(object)) return state.filter(o => o !== object);else return [object, ...state];\n  }, []);\n  React.useEffect(() => {\n    if (downed) onChange == null || onChange(active);else onChangePointerUp == null || onChangePointerUp(active);\n  }, [active, downed]);\n  const onClick = React.useCallback(e => {\n    e.stopPropagation();\n    dispatch({\n      object: customFilter([e.object])[0],\n      shift: multiple && e.shiftKey\n    });\n  }, []);\n  const onPointerMissed = React.useCallback(e => !hovered && dispatch({}), [hovered]);\n  const ref = React.useRef(null);\n  React.useEffect(() => {\n    if (!box || !multiple) return;\n    const selBox = new SelectionBox(camera, ref.current);\n    const element = document.createElement('div');\n    element.style.pointerEvents = 'none';\n    element.style.border = border;\n    element.style.backgroundColor = backgroundColor;\n    element.style.position = 'fixed';\n    const startPoint = new THREE.Vector2();\n    const pointTopLeft = new THREE.Vector2();\n    const pointBottomRight = new THREE.Vector2();\n    const oldRaycasterEnabled = get().events.enabled;\n    const oldControlsEnabled = controls == null ? void 0 : controls.enabled;\n    let isDown = false;\n    function prepareRay(event, vec) {\n      const {\n        offsetX,\n        offsetY\n      } = event;\n      const {\n        width,\n        height\n      } = size;\n      vec.set(offsetX / width * 2 - 1, -(offsetY / height) * 2 + 1);\n    }\n    function onSelectStart(event) {\n      var _gl$domElement$parent;\n      if (controls) controls.enabled = false;\n      setEvents({\n        enabled: false\n      });\n      down(isDown = true);\n      (_gl$domElement$parent = gl.domElement.parentElement) == null || _gl$domElement$parent.appendChild(element);\n      element.style.left = `${event.clientX}px`;\n      element.style.top = `${event.clientY}px`;\n      element.style.width = '0px';\n      element.style.height = '0px';\n      startPoint.x = event.clientX;\n      startPoint.y = event.clientY;\n    }\n    function onSelectMove(event) {\n      pointBottomRight.x = Math.max(startPoint.x, event.clientX);\n      pointBottomRight.y = Math.max(startPoint.y, event.clientY);\n      pointTopLeft.x = Math.min(startPoint.x, event.clientX);\n      pointTopLeft.y = Math.min(startPoint.y, event.clientY);\n      element.style.left = `${pointTopLeft.x}px`;\n      element.style.top = `${pointTopLeft.y}px`;\n      element.style.width = `${pointBottomRight.x - pointTopLeft.x}px`;\n      element.style.height = `${pointBottomRight.y - pointTopLeft.y}px`;\n    }\n    function onSelectOver() {\n      if (isDown) {\n        var _element$parentElemen;\n        if (controls) controls.enabled = oldControlsEnabled;\n        setEvents({\n          enabled: oldRaycasterEnabled\n        });\n        down(isDown = false);\n        (_element$parentElemen = element.parentElement) == null || _element$parentElemen.removeChild(element);\n      }\n    }\n    function pointerDown(event) {\n      if (event.shiftKey) {\n        onSelectStart(event);\n        prepareRay(event, selBox.startPoint);\n      }\n    }\n    let previous = [];\n    function pointerMove(event) {\n      if (isDown) {\n        onSelectMove(event);\n        prepareRay(event, selBox.endPoint);\n        const allSelected = selBox.select().sort(o => o.uuid).filter(o => o.isMesh);\n        if (!shallow(allSelected, previous)) {\n          previous = allSelected;\n          dispatch({\n            object: customFilter(allSelected)\n          });\n        }\n      }\n    }\n    function pointerUp(event) {\n      if (isDown) onSelectOver();\n    }\n    document.addEventListener('pointerdown', pointerDown, {\n      passive: true\n    });\n    document.addEventListener('pointermove', pointerMove, {\n      passive: true,\n      capture: true\n    });\n    document.addEventListener('pointerup', pointerUp, {\n      passive: true\n    });\n    return () => {\n      document.removeEventListener('pointerdown', pointerDown);\n      document.removeEventListener('pointermove', pointerMove, true);\n      document.removeEventListener('pointerup', pointerUp);\n    };\n  }, [size.width, size.height, raycaster, camera, controls, gl]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref,\n    onClick: onClick,\n    onPointerOver: () => hover(true),\n    onPointerOut: () => hover(false),\n    onPointerMissed: onPointerMissed\n  }, props), /*#__PURE__*/React.createElement(context.Provider, {\n    value: active\n  }, children));\n}\n\n// The return type is explicitly declared here because otherwise TypeScript will emit `THREE.Object3D<THREE.Event>[]`.\n// The meaning of the generic parameter for `Object3D` was changed in r156, so it should not be included so that it\n// works with all versions of @types/three.\nfunction useSelect() {\n  return React.useContext(context);\n}\n\nexport { Select, useSelect };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,UAAU,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,EAAE;AACrD,SAAS,OAAO,EACd,GAAG,EACH,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,iBAAiB,EACjB,SAAS,mBAAmB,EAC5B,kBAAkB,yBAAyB,EAC3C,QAAQ,eAAe,CAAA,OAAQ,IAAI,EACnC,GAAG,OACJ;IACC,MAAM,CAAC,QAAQ,KAAK,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD,EAAE;IACtC,MAAM,EACJ,SAAS,EACT,MAAM,EACN,SAAS,EACT,EAAE,EACF,QAAQ,EACR,IAAI,EACJ,GAAG,EACJ,GAAG,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;IACX,MAAM,CAAC,SAAS,MAAM,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD,EAAE;IACxC,MAAM,CAAC,QAAQ,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD;6BAE1C,AADA,mBAAmB;QACnB,CAAC,OAAO,EACN,MAAM,EACN,KAAK,EACN;YACC,IAAI,WAAW,WAAW,OAAO,EAAE;iBAAM,IAAI,MAAM,OAAO,CAAC,SAAS,OAAO;iBAAY,IAAI,CAAC,OAAO,OAAO,KAAK,CAAC,EAAE,KAAK,SAAS,EAAE,GAAG;gBAAC;aAAO;iBAExI,IAAI,MAAM,QAAQ,CAAC,SAAS,OAAO,MAAM,MAAM;qCAAC,CAAA,IAAK,MAAM;;iBAAa,OAAO;gBAAC;mBAAW;aAAM;QACxG;4BAAG,EAAE;IACL,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;4BAAE;YACd,IAAI,QAAQ,YAAY,QAAQ,SAAS;iBAAa,qBAAqB,QAAQ,kBAAkB;QACvG;2BAAG;QAAC;QAAQ;KAAO;IACnB,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;uCAAE,CAAA;YAChC,EAAE,eAAe;YACjB,SAAS;gBACP,QAAQ,aAAa;oBAAC,EAAE,MAAM;iBAAC,CAAC,CAAC,EAAE;gBACnC,OAAO,YAAY,EAAE,QAAQ;YAC/B;QACF;sCAAG,EAAE;IACL,MAAM,kBAAkB,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;+CAAE,CAAA,IAAK,CAAC,WAAW,SAAS,CAAC;8CAAI;QAAC;KAAQ;IAClF,MAAM,MAAM,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IACzB,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;4BAAE;YACd,IAAI,CAAC,OAAO,CAAC,UAAU;YACvB,MAAM,SAAS,IAAI,qPAAA,CAAA,eAAY,CAAC,QAAQ,IAAI,OAAO;YACnD,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,QAAQ,KAAK,CAAC,aAAa,GAAG;YAC9B,QAAQ,KAAK,CAAC,MAAM,GAAG;YACvB,QAAQ,KAAK,CAAC,eAAe,GAAG;YAChC,QAAQ,KAAK,CAAC,QAAQ,GAAG;YACzB,MAAM,aAAa,IAAI,sMAAA,CAAA,UAAa;YACpC,MAAM,eAAe,IAAI,sMAAA,CAAA,UAAa;YACtC,MAAM,mBAAmB,IAAI,sMAAA,CAAA,UAAa;YAC1C,MAAM,sBAAsB,MAAM,MAAM,CAAC,OAAO;YAChD,MAAM,qBAAqB,YAAY,OAAO,KAAK,IAAI,SAAS,OAAO;YACvE,IAAI,SAAS;YACb,SAAS,WAAW,KAAK,EAAE,GAAG;gBAC5B,MAAM,EACJ,OAAO,EACP,OAAO,EACR,GAAG;gBACJ,MAAM,EACJ,KAAK,EACL,MAAM,EACP,GAAG;gBACJ,IAAI,GAAG,CAAC,UAAU,QAAQ,IAAI,GAAG,CAAC,CAAC,UAAU,MAAM,IAAI,IAAI;YAC7D;YACA,SAAS,cAAc,KAAK;gBAC1B,IAAI;gBACJ,IAAI,UAAU,SAAS,OAAO,GAAG;gBACjC,UAAU;oBACR,SAAS;gBACX;gBACA,KAAK,SAAS;gBACd,CAAC,wBAAwB,GAAG,UAAU,CAAC,aAAa,KAAK,QAAQ,sBAAsB,WAAW,CAAC;gBACnG,QAAQ,KAAK,CAAC,IAAI,GAAG,GAAG,MAAM,OAAO,CAAC,EAAE,CAAC;gBACzC,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,MAAM,OAAO,CAAC,EAAE,CAAC;gBACxC,QAAQ,KAAK,CAAC,KAAK,GAAG;gBACtB,QAAQ,KAAK,CAAC,MAAM,GAAG;gBACvB,WAAW,CAAC,GAAG,MAAM,OAAO;gBAC5B,WAAW,CAAC,GAAG,MAAM,OAAO;YAC9B;YACA,SAAS,aAAa,KAAK;gBACzB,iBAAiB,CAAC,GAAG,KAAK,GAAG,CAAC,WAAW,CAAC,EAAE,MAAM,OAAO;gBACzD,iBAAiB,CAAC,GAAG,KAAK,GAAG,CAAC,WAAW,CAAC,EAAE,MAAM,OAAO;gBACzD,aAAa,CAAC,GAAG,KAAK,GAAG,CAAC,WAAW,CAAC,EAAE,MAAM,OAAO;gBACrD,aAAa,CAAC,GAAG,KAAK,GAAG,CAAC,WAAW,CAAC,EAAE,MAAM,OAAO;gBACrD,QAAQ,KAAK,CAAC,IAAI,GAAG,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC;gBAC1C,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC;gBACzC,QAAQ,KAAK,CAAC,KAAK,GAAG,GAAG,iBAAiB,CAAC,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC;gBAChE,QAAQ,KAAK,CAAC,MAAM,GAAG,GAAG,iBAAiB,CAAC,GAAG,aAAa,CAAC,CAAC,EAAE,CAAC;YACnE;YACA,SAAS;gBACP,IAAI,QAAQ;oBACV,IAAI;oBACJ,IAAI,UAAU,SAAS,OAAO,GAAG;oBACjC,UAAU;wBACR,SAAS;oBACX;oBACA,KAAK,SAAS;oBACd,CAAC,wBAAwB,QAAQ,aAAa,KAAK,QAAQ,sBAAsB,WAAW,CAAC;gBAC/F;YACF;YACA,SAAS,YAAY,KAAK;gBACxB,IAAI,MAAM,QAAQ,EAAE;oBAClB,cAAc;oBACd,WAAW,OAAO,OAAO,UAAU;gBACrC;YACF;YACA,IAAI,WAAW,EAAE;YACjB,SAAS,YAAY,KAAK;gBACxB,IAAI,QAAQ;oBACV,aAAa;oBACb,WAAW,OAAO,OAAO,QAAQ;oBACjC,MAAM,cAAc,OAAO,MAAM,GAAG,IAAI;oEAAC,CAAA,IAAK,EAAE,IAAI;mEAAE,MAAM;oEAAC,CAAA,IAAK,EAAE,MAAM;;oBAC1E,IAAI,CAAC,CAAA,GAAA,6UAAA,CAAA,UAAO,AAAD,EAAE,aAAa,WAAW;wBACnC,WAAW;wBACX,SAAS;4BACP,QAAQ,aAAa;wBACvB;oBACF;gBACF;YACF;YACA,SAAS,UAAU,KAAK;gBACtB,IAAI,QAAQ;YACd;YACA,SAAS,gBAAgB,CAAC,eAAe,aAAa;gBACpD,SAAS;YACX;YACA,SAAS,gBAAgB,CAAC,eAAe,aAAa;gBACpD,SAAS;gBACT,SAAS;YACX;YACA,SAAS,gBAAgB,CAAC,aAAa,WAAW;gBAChD,SAAS;YACX;YACA;oCAAO;oBACL,SAAS,mBAAmB,CAAC,eAAe;oBAC5C,SAAS,mBAAmB,CAAC,eAAe,aAAa;oBACzD,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG;QAAC,KAAK,KAAK;QAAE,KAAK,MAAM;QAAE;QAAW;QAAQ;QAAU;KAAG;IAC7D,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;QACxD,KAAK;QACL,SAAS;QACT,eAAe,IAAM,MAAM;QAC3B,cAAc,IAAM,MAAM;QAC1B,iBAAiB;IACnB,GAAG,QAAQ,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ,QAAQ,EAAE;QAC5D,OAAO;IACT,GAAG;AACL;AAEA,sHAAsH;AACtH,mHAAmH;AACnH,2CAA2C;AAC3C,SAAS;IACP,OAAO,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1602, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1608, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/View.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { context, useThree, createPortal, useFrame } from '@react-three/fiber';\nimport tunnel from 'tunnel-rat';\n\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\nconst col = /* @__PURE__ */new THREE.Color();\nconst tracked = /* @__PURE__ */tunnel();\nfunction computeContainerPosition(canvasSize, trackRect) {\n  const {\n    right,\n    top,\n    left: trackLeft,\n    bottom: trackBottom,\n    width,\n    height\n  } = trackRect;\n  const isOffscreen = trackRect.bottom < 0 || top > canvasSize.height || right < 0 || trackRect.left > canvasSize.width;\n  const canvasBottom = canvasSize.top + canvasSize.height;\n  const bottom = canvasBottom - trackBottom;\n  const left = trackLeft - canvasSize.left;\n  return {\n    position: {\n      width,\n      height,\n      left,\n      top,\n      bottom,\n      right\n    },\n    isOffscreen\n  };\n}\nfunction prepareSkissor(state, {\n  left,\n  bottom,\n  width,\n  height\n}) {\n  let autoClear;\n  const aspect = width / height;\n  if (isOrthographicCamera(state.camera)) {\n    if (!state.camera.manual) {\n      if (state.camera.left !== width / -2 || state.camera.right !== width / 2 || state.camera.top !== height / 2 || state.camera.bottom !== height / -2) {\n        Object.assign(state.camera, {\n          left: width / -2,\n          right: width / 2,\n          top: height / 2,\n          bottom: height / -2\n        });\n        state.camera.updateProjectionMatrix();\n      }\n    } else {\n      state.camera.updateProjectionMatrix();\n    }\n  } else if (state.camera.aspect !== aspect) {\n    state.camera.aspect = aspect;\n    state.camera.updateProjectionMatrix();\n  }\n  autoClear = state.gl.autoClear;\n  state.gl.autoClear = false;\n  state.gl.setViewport(left, bottom, width, height);\n  state.gl.setScissor(left, bottom, width, height);\n  state.gl.setScissorTest(true);\n  return autoClear;\n}\nfunction finishSkissor(state, autoClear) {\n  // Restore the default state\n  state.gl.setScissorTest(false);\n  state.gl.autoClear = autoClear;\n}\nfunction clear(state) {\n  state.gl.getClearColor(col);\n  state.gl.setClearColor(col, state.gl.getClearAlpha());\n  state.gl.clear(true, true);\n}\nfunction Container({\n  visible = true,\n  canvasSize,\n  scene,\n  index,\n  children,\n  frames,\n  rect,\n  track\n}) {\n  const rootState = useThree();\n  const [isOffscreen, setOffscreen] = React.useState(false);\n  let frameCount = 0;\n  useFrame(state => {\n    if (frames === Infinity || frameCount <= frames) {\n      var _track$current;\n      if (track) rect.current = (_track$current = track.current) == null ? void 0 : _track$current.getBoundingClientRect();\n      frameCount++;\n    }\n    if (rect.current) {\n      const {\n        position,\n        isOffscreen: _isOffscreen\n      } = computeContainerPosition(canvasSize, rect.current);\n      if (isOffscreen !== _isOffscreen) setOffscreen(_isOffscreen);\n      if (visible && !isOffscreen && rect.current) {\n        const autoClear = prepareSkissor(state, position);\n        // When children are present render the portalled scene, otherwise the default scene\n        state.gl.render(children ? state.scene : scene, state.camera);\n        finishSkissor(state, autoClear);\n      }\n    }\n  }, index);\n  React.useLayoutEffect(() => {\n    const curRect = rect.current;\n    if (curRect && (!visible || !isOffscreen)) {\n      // If the view is not visible clear it once, but stop rendering afterwards!\n      const {\n        position\n      } = computeContainerPosition(canvasSize, curRect);\n      const autoClear = prepareSkissor(rootState, position);\n      clear(rootState);\n      finishSkissor(rootState, autoClear);\n    }\n  }, [visible, isOffscreen]);\n  React.useEffect(() => {\n    if (!track) return;\n    const curRect = rect.current;\n    // Connect the event layer to the tracking element\n    const old = rootState.get().events.connected;\n    rootState.setEvents({\n      connected: track.current\n    });\n    return () => {\n      if (curRect) {\n        const {\n          position\n        } = computeContainerPosition(canvasSize, curRect);\n        const autoClear = prepareSkissor(rootState, position);\n        clear(rootState);\n        finishSkissor(rootState, autoClear);\n      }\n      rootState.setEvents({\n        connected: old\n      });\n    };\n  }, [track]);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children, /*#__PURE__*/React.createElement(\"group\", {\n    onPointerOver: () => null\n  }));\n}\nconst CanvasView = /* @__PURE__ */React.forwardRef(({\n  track,\n  visible = true,\n  index = 1,\n  id,\n  style,\n  className,\n  frames = Infinity,\n  children,\n  ...props\n}, fref) => {\n  var _rect$current, _rect$current2, _rect$current3, _rect$current4;\n  const rect = React.useRef(null);\n  const {\n    size,\n    scene\n  } = useThree();\n  const [virtualScene] = React.useState(() => new THREE.Scene());\n  const [ready, toggle] = React.useReducer(() => true, false);\n  const compute = React.useCallback((event, state) => {\n    if (rect.current && track && track.current && event.target === track.current) {\n      const {\n        width,\n        height,\n        left,\n        top\n      } = rect.current;\n      const x = event.clientX - left;\n      const y = event.clientY - top;\n      state.pointer.set(x / width * 2 - 1, -(y / height) * 2 + 1);\n      state.raycaster.setFromCamera(state.pointer, state.camera);\n    }\n  }, [rect, track]);\n  React.useEffect(() => {\n    var _track$current2;\n    // We need the tracking elements bounds beforehand in order to inject it into the portal\n    if (track) rect.current = (_track$current2 = track.current) == null ? void 0 : _track$current2.getBoundingClientRect();\n    // And now we can proceed\n    toggle();\n  }, [track]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: fref\n  }, props), ready && createPortal(/*#__PURE__*/React.createElement(Container, {\n    visible: visible,\n    canvasSize: size,\n    frames: frames,\n    scene: scene,\n    track: track,\n    rect: rect,\n    index: index\n  }, children), virtualScene, {\n    events: {\n      compute,\n      priority: index\n    },\n    size: {\n      width: (_rect$current = rect.current) == null ? void 0 : _rect$current.width,\n      height: (_rect$current2 = rect.current) == null ? void 0 : _rect$current2.height,\n      // @ts-ignore\n      top: (_rect$current3 = rect.current) == null ? void 0 : _rect$current3.top,\n      // @ts-ignore\n      left: (_rect$current4 = rect.current) == null ? void 0 : _rect$current4.left\n    }\n  }));\n});\nconst HtmlView = /* @__PURE__ */React.forwardRef(({\n  as: El = 'div',\n  id,\n  visible,\n  className,\n  style,\n  index = 1,\n  track,\n  frames = Infinity,\n  children,\n  ...props\n}, fref) => {\n  const uuid = React.useId();\n  const ref = React.useRef(null);\n  React.useImperativeHandle(fref, () => ref.current);\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(El, _extends({\n    ref: ref,\n    id: id,\n    className: className,\n    style: style\n  }, props)), /*#__PURE__*/React.createElement(tracked.In, null, /*#__PURE__*/React.createElement(CanvasView, {\n    visible: visible,\n    key: uuid,\n    track: ref,\n    frames: frames,\n    index: index\n  }, children)));\n});\nconst View = /* @__PURE__ */(() => {\n  const _View = /*#__PURE__*/React.forwardRef((props, fref) => {\n    // If we're inside a canvas we should be able to access the context store\n    const store = React.useContext(context);\n    // If that's not the case we render a tunnel\n    if (!store) return /*#__PURE__*/React.createElement(HtmlView, _extends({\n      ref: fref\n    }, props));\n    // Otherwise a plain canvas-view\n    else return /*#__PURE__*/React.createElement(CanvasView, _extends({\n      ref: fref\n    }, props));\n  });\n  _View.Port = () => /*#__PURE__*/React.createElement(tracked.Out, null);\n  return _View;\n})();\n\nexport { View };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;AAEA,MAAM,uBAAuB,CAAA,MAAO,OAAO,IAAI,oBAAoB;AACnE,MAAM,MAAM,aAAa,GAAE,IAAI,sMAAA,CAAA,QAAW;AAC1C,MAAM,UAAU,aAAa,GAAE,CAAA,GAAA,qQAAA,CAAA,UAAM,AAAD;AACpC,SAAS,yBAAyB,UAAU,EAAE,SAAS;IACrD,MAAM,EACJ,KAAK,EACL,GAAG,EACH,MAAM,SAAS,EACf,QAAQ,WAAW,EACnB,KAAK,EACL,MAAM,EACP,GAAG;IACJ,MAAM,cAAc,UAAU,MAAM,GAAG,KAAK,MAAM,WAAW,MAAM,IAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,WAAW,KAAK;IACrH,MAAM,eAAe,WAAW,GAAG,GAAG,WAAW,MAAM;IACvD,MAAM,SAAS,eAAe;IAC9B,MAAM,OAAO,YAAY,WAAW,IAAI;IACxC,OAAO;QACL,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;QACF;QACA;IACF;AACF;AACA,SAAS,eAAe,KAAK,EAAE,EAC7B,IAAI,EACJ,MAAM,EACN,KAAK,EACL,MAAM,EACP;IACC,IAAI;IACJ,MAAM,SAAS,QAAQ;IACvB,IAAI,qBAAqB,MAAM,MAAM,GAAG;QACtC,IAAI,CAAC,MAAM,MAAM,CAAC,MAAM,EAAE;YACxB,IAAI,MAAM,MAAM,CAAC,IAAI,KAAK,QAAQ,CAAC,KAAK,MAAM,MAAM,CAAC,KAAK,KAAK,QAAQ,KAAK,MAAM,MAAM,CAAC,GAAG,KAAK,SAAS,KAAK,MAAM,MAAM,CAAC,MAAM,KAAK,SAAS,CAAC,GAAG;gBAClJ,OAAO,MAAM,CAAC,MAAM,MAAM,EAAE;oBAC1B,MAAM,QAAQ,CAAC;oBACf,OAAO,QAAQ;oBACf,KAAK,SAAS;oBACd,QAAQ,SAAS,CAAC;gBACpB;gBACA,MAAM,MAAM,CAAC,sBAAsB;YACrC;QACF,OAAO;YACL,MAAM,MAAM,CAAC,sBAAsB;QACrC;IACF,OAAO,IAAI,MAAM,MAAM,CAAC,MAAM,KAAK,QAAQ;QACzC,MAAM,MAAM,CAAC,MAAM,GAAG;QACtB,MAAM,MAAM,CAAC,sBAAsB;IACrC;IACA,YAAY,MAAM,EAAE,CAAC,SAAS;IAC9B,MAAM,EAAE,CAAC,SAAS,GAAG;IACrB,MAAM,EAAE,CAAC,WAAW,CAAC,MAAM,QAAQ,OAAO;IAC1C,MAAM,EAAE,CAAC,UAAU,CAAC,MAAM,QAAQ,OAAO;IACzC,MAAM,EAAE,CAAC,cAAc,CAAC;IACxB,OAAO;AACT;AACA,SAAS,cAAc,KAAK,EAAE,SAAS;IACrC,4BAA4B;IAC5B,MAAM,EAAE,CAAC,cAAc,CAAC;IACxB,MAAM,EAAE,CAAC,SAAS,GAAG;AACvB;AACA,SAAS,MAAM,KAAK;IAClB,MAAM,EAAE,CAAC,aAAa,CAAC;IACvB,MAAM,EAAE,CAAC,aAAa,CAAC,KAAK,MAAM,EAAE,CAAC,aAAa;IAClD,MAAM,EAAE,CAAC,KAAK,CAAC,MAAM;AACvB;AACA,SAAS,UAAU,EACjB,UAAU,IAAI,EACd,UAAU,EACV,KAAK,EACL,KAAK,EACL,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,KAAK,EACN;IACC,MAAM,YAAY,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,aAAa,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD,EAAE;IACnD,IAAI,aAAa;IACjB,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;8BAAE,CAAA;YACP,IAAI,WAAW,YAAY,cAAc,QAAQ;gBAC/C,IAAI;gBACJ,IAAI,OAAO,KAAK,OAAO,GAAG,CAAC,iBAAiB,MAAM,OAAO,KAAK,OAAO,KAAK,IAAI,eAAe,qBAAqB;gBAClH;YACF;YACA,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,EACJ,QAAQ,EACR,aAAa,YAAY,EAC1B,GAAG,yBAAyB,YAAY,KAAK,OAAO;gBACrD,IAAI,gBAAgB,cAAc,aAAa;gBAC/C,IAAI,WAAW,CAAC,eAAe,KAAK,OAAO,EAAE;oBAC3C,MAAM,YAAY,eAAe,OAAO;oBACxC,oFAAoF;oBACpF,MAAM,EAAE,CAAC,MAAM,CAAC,WAAW,MAAM,KAAK,GAAG,OAAO,MAAM,MAAM;oBAC5D,cAAc,OAAO;gBACvB;YACF;QACF;6BAAG;IACH,CAAA,GAAA,4RAAA,CAAA,kBAAqB,AAAD;qCAAE;YACpB,MAAM,UAAU,KAAK,OAAO;YAC5B,IAAI,WAAW,CAAC,CAAC,WAAW,CAAC,WAAW,GAAG;gBACzC,2EAA2E;gBAC3E,MAAM,EACJ,QAAQ,EACT,GAAG,yBAAyB,YAAY;gBACzC,MAAM,YAAY,eAAe,WAAW;gBAC5C,MAAM;gBACN,cAAc,WAAW;YAC3B;QACF;oCAAG;QAAC;QAAS;KAAY;IACzB,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;+BAAE;YACd,IAAI,CAAC,OAAO;YACZ,MAAM,UAAU,KAAK,OAAO;YAC5B,kDAAkD;YAClD,MAAM,MAAM,UAAU,GAAG,GAAG,MAAM,CAAC,SAAS;YAC5C,UAAU,SAAS,CAAC;gBAClB,WAAW,MAAM,OAAO;YAC1B;YACA;uCAAO;oBACL,IAAI,SAAS;wBACX,MAAM,EACJ,QAAQ,EACT,GAAG,yBAAyB,YAAY;wBACzC,MAAM,YAAY,eAAe,WAAW;wBAC5C,MAAM;wBACN,cAAc,WAAW;oBAC3B;oBACA,UAAU,SAAS,CAAC;wBAClB,WAAW;oBACb;gBACF;;QACF;8BAAG;QAAC;KAAM;IACV,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,4RAAA,CAAA,WAAc,EAAE,MAAM,UAAU,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAChH,eAAe,IAAM;IACvB;AACF;AACA,MAAM,aAAa,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAClD,KAAK,EACL,UAAU,IAAI,EACd,QAAQ,CAAC,EACT,EAAE,EACF,KAAK,EACL,SAAS,EACT,SAAS,QAAQ,EACjB,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,IAAI,eAAe,gBAAgB,gBAAgB;IACnD,MAAM,OAAO,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC1B,MAAM,EACJ,IAAI,EACJ,KAAK,EACN,GAAG,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;IACX,MAAM,CAAC,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD;+BAAE,IAAM,IAAI,sMAAA,CAAA,QAAW;;IAC3D,MAAM,CAAC,OAAO,OAAO,GAAG,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD;iCAAE,IAAM;gCAAM;IACrD,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;2CAAE,CAAC,OAAO;YACxC,IAAI,KAAK,OAAO,IAAI,SAAS,MAAM,OAAO,IAAI,MAAM,MAAM,KAAK,MAAM,OAAO,EAAE;gBAC5E,MAAM,EACJ,KAAK,EACL,MAAM,EACN,IAAI,EACJ,GAAG,EACJ,GAAG,KAAK,OAAO;gBAChB,MAAM,IAAI,MAAM,OAAO,GAAG;gBAC1B,MAAM,IAAI,MAAM,OAAO,GAAG;gBAC1B,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,QAAQ,IAAI,GAAG,CAAC,CAAC,IAAI,MAAM,IAAI,IAAI;gBACzD,MAAM,SAAS,CAAC,aAAa,CAAC,MAAM,OAAO,EAAE,MAAM,MAAM;YAC3D;QACF;0CAAG;QAAC;QAAM;KAAM;IAChB,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;gCAAE;YACd,IAAI;YACJ,wFAAwF;YACxF,IAAI,OAAO,KAAK,OAAO,GAAG,CAAC,kBAAkB,MAAM,OAAO,KAAK,OAAO,KAAK,IAAI,gBAAgB,qBAAqB;YACpH,yBAAyB;YACzB;QACF;+BAAG;QAAC;KAAM;IACV,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;QACxD,KAAK;IACP,GAAG,QAAQ,SAAS,CAAA,GAAA,oaAAA,CAAA,eAAY,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,WAAW;QAC3E,SAAS;QACT,YAAY;QACZ,QAAQ;QACR,OAAO;QACP,OAAO;QACP,MAAM;QACN,OAAO;IACT,GAAG,WAAW,cAAc;QAC1B,QAAQ;YACN;YACA,UAAU;QACZ;QACA,MAAM;YACJ,OAAO,CAAC,gBAAgB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,cAAc,KAAK;YAC5E,QAAQ,CAAC,iBAAiB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,eAAe,MAAM;YAChF,aAAa;YACb,KAAK,CAAC,iBAAiB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,eAAe,GAAG;YAC1E,aAAa;YACb,MAAM,CAAC,iBAAiB,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,eAAe,IAAI;QAC9E;IACF;AACF;AACA,MAAM,WAAW,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAChD,IAAI,KAAK,KAAK,EACd,EAAE,EACF,OAAO,EACP,SAAS,EACT,KAAK,EACL,QAAQ,CAAC,EACT,KAAK,EACL,SAAS,QAAQ,EACjB,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,OAAO,CAAA,GAAA,4RAAA,CAAA,QAAW,AAAD;IACvB,MAAM,MAAM,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IACzB,CAAA,GAAA,4RAAA,CAAA,sBAAyB,AAAD,EAAE;wCAAM,IAAM,IAAI,OAAO;;IACjD,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,4RAAA,CAAA,WAAc,EAAE,MAAM,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,IAAI,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;QAC1G,KAAK;QACL,IAAI;QACJ,WAAW;QACX,OAAO;IACT,GAAG,SAAS,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ,EAAE,EAAE,MAAM,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,YAAY;QAC1G,SAAS;QACT,KAAK;QACL,OAAO;QACP,QAAQ;QACR,OAAO;IACT,GAAG;AACL;AACA,MAAM,OAAO,aAAa,GAAE,CAAC;IAC3B,MAAM,QAAQ,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,OAAO;QAClD,yEAAyE;QACzE,MAAM,QAAQ,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,+ZAAA,CAAA,UAAO;QACtC,4CAA4C;QAC5C,IAAI,CAAC,OAAO,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,UAAU,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;YACrE,KAAK;QACP,GAAG;aAEE,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,YAAY,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;YAChE,KAAK;QACP,GAAG;IACL;IACA,MAAM,IAAI,GAAG,IAAM,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ,GAAG,EAAE;IACjE,OAAO;AACT,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1846, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1852, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/pivotControls/context.js"], "sourcesContent": ["import * as React from 'react';\n\nconst context = /* @__PURE__ */React.createContext(null);\n\nexport { context };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,UAAU,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1859, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1865, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/pivotControls/AxisArrow.js"], "sourcesContent": ["import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { Line } from '../../core/Line.js';\nimport { Html } from '../Html.js';\nimport { context } from './context.js';\n\nconst vec1 = /* @__PURE__ */new THREE.Vector3();\nconst vec2 = /* @__PURE__ */new THREE.Vector3();\nconst calculateOffset = (clickPoint, normal, rayStart, rayDir) => {\n  const e1 = normal.dot(normal);\n  const e2 = normal.dot(clickPoint) - normal.dot(rayStart);\n  const e3 = normal.dot(rayDir);\n  if (e3 === 0) {\n    return -e2 / e1;\n  }\n  vec1.copy(rayDir).multiplyScalar(e1 / e3).sub(normal);\n  vec2.copy(rayDir).multiplyScalar(e2 / e3).add(rayStart).sub(clickPoint);\n  const offset = -vec1.dot(vec2) / vec1.dot(vec1);\n  return offset;\n};\nconst upV = /* @__PURE__ */new THREE.Vector3(0, 1, 0);\nconst offsetMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst AxisArrow = ({\n  direction,\n  axis\n}) => {\n  const {\n    translation,\n    translationLimits,\n    annotations,\n    annotationsClass,\n    depthTest,\n    scale,\n    lineWidth,\n    fixed,\n    axisColors,\n    hoveredColor,\n    opacity,\n    onDragStart,\n    onDrag,\n    onDragEnd,\n    userData\n  } = React.useContext(context);\n  const camControls = useThree(state => state.controls);\n  const divRef = React.useRef(null);\n  const objRef = React.useRef(null);\n  const clickInfo = React.useRef(null);\n  const offset0 = React.useRef(0);\n  const [isHovered, setIsHovered] = React.useState(false);\n  const onPointerDown = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.innerText = `${translation.current[axis].toFixed(2)}`;\n      divRef.current.style.display = 'block';\n    }\n    e.stopPropagation();\n    const rotation = new THREE.Matrix4().extractRotation(objRef.current.matrixWorld);\n    const clickPoint = e.point.clone();\n    const origin = new THREE.Vector3().setFromMatrixPosition(objRef.current.matrixWorld);\n    const dir = direction.clone().applyMatrix4(rotation).normalize();\n    clickInfo.current = {\n      clickPoint,\n      dir\n    };\n    offset0.current = translation.current[axis];\n    onDragStart({\n      component: 'Arrow',\n      axis,\n      origin,\n      directions: [dir]\n    });\n    camControls && (camControls.enabled = false);\n    // @ts-ignore - setPointerCapture is not in the type definition\n    e.target.setPointerCapture(e.pointerId);\n  }, [annotations, direction, camControls, onDragStart, translation, axis]);\n  const onPointerMove = React.useCallback(e => {\n    e.stopPropagation();\n    if (!isHovered) setIsHovered(true);\n    if (clickInfo.current) {\n      const {\n        clickPoint,\n        dir\n      } = clickInfo.current;\n      const [min, max] = (translationLimits == null ? void 0 : translationLimits[axis]) || [undefined, undefined];\n      let offset = calculateOffset(clickPoint, dir, e.ray.origin, e.ray.direction);\n      if (min !== undefined) {\n        offset = Math.max(offset, min - offset0.current);\n      }\n      if (max !== undefined) {\n        offset = Math.min(offset, max - offset0.current);\n      }\n      translation.current[axis] = offset0.current + offset;\n      if (annotations) {\n        divRef.current.innerText = `${translation.current[axis].toFixed(2)}`;\n      }\n      offsetMatrix.makeTranslation(dir.x * offset, dir.y * offset, dir.z * offset);\n      onDrag(offsetMatrix);\n    }\n  }, [annotations, onDrag, isHovered, translation, translationLimits, axis]);\n  const onPointerUp = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.style.display = 'none';\n    }\n    e.stopPropagation();\n    clickInfo.current = null;\n    onDragEnd();\n    camControls && (camControls.enabled = true);\n    // @ts-ignore - releasePointerCapture & PointerEvent#pointerId is not in the type definition\n    e.target.releasePointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragEnd]);\n  const onPointerOut = React.useCallback(e => {\n    e.stopPropagation();\n    setIsHovered(false);\n  }, []);\n  const {\n    cylinderLength,\n    coneWidth,\n    coneLength,\n    matrixL\n  } = React.useMemo(() => {\n    const coneWidth = fixed ? lineWidth / scale * 1.6 : scale / 20;\n    const coneLength = fixed ? 0.2 : scale / 5;\n    const cylinderLength = fixed ? 1 - coneLength : scale - coneLength;\n    const quaternion = new THREE.Quaternion().setFromUnitVectors(upV, direction.clone().normalize());\n    const matrixL = new THREE.Matrix4().makeRotationFromQuaternion(quaternion);\n    return {\n      cylinderLength,\n      coneWidth,\n      coneLength,\n      matrixL\n    };\n  }, [direction, scale, lineWidth, fixed]);\n  const color_ = isHovered ? hoveredColor : axisColors[axis];\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: objRef\n  }, /*#__PURE__*/React.createElement(\"group\", {\n    matrix: matrixL,\n    matrixAutoUpdate: false,\n    onPointerDown: onPointerDown,\n    onPointerMove: onPointerMove,\n    onPointerUp: onPointerUp,\n    onPointerOut: onPointerOut\n  }, annotations && /*#__PURE__*/React.createElement(Html, {\n    position: [0, -coneLength, 0]\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'none',\n      background: '#151520',\n      color: 'white',\n      padding: '6px 8px',\n      borderRadius: 7,\n      whiteSpace: 'nowrap'\n    },\n    className: annotationsClass,\n    ref: divRef\n  })), /*#__PURE__*/React.createElement(\"mesh\", {\n    visible: false,\n    position: [0, (cylinderLength + coneLength) / 2.0, 0],\n    userData: userData\n  }, /*#__PURE__*/React.createElement(\"cylinderGeometry\", {\n    args: [coneWidth * 1.4, coneWidth * 1.4, cylinderLength + coneLength, 8, 1]\n  })), /*#__PURE__*/React.createElement(Line, {\n    transparent: true,\n    raycast: () => null,\n    depthTest: depthTest,\n    points: [0, 0, 0, 0, cylinderLength, 0],\n    lineWidth: lineWidth,\n    side: THREE.DoubleSide,\n    color: color_,\n    opacity: opacity,\n    polygonOffset: true,\n    renderOrder: 1,\n    polygonOffsetFactor: -10,\n    fog: false\n  }), /*#__PURE__*/React.createElement(\"mesh\", {\n    raycast: () => null,\n    position: [0, cylinderLength + coneLength / 2.0, 0],\n    renderOrder: 500\n  }, /*#__PURE__*/React.createElement(\"coneGeometry\", {\n    args: [coneWidth, coneLength, 24, 1]\n  }), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    depthTest: depthTest,\n    color: color_,\n    opacity: opacity,\n    polygonOffset: true,\n    polygonOffsetFactor: -10,\n    fog: false\n  }))));\n};\n\nexport { AxisArrow, calculateOffset };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,OAAO,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC7C,MAAM,OAAO,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC7C,MAAM,kBAAkB,CAAC,YAAY,QAAQ,UAAU;IACrD,MAAM,KAAK,OAAO,GAAG,CAAC;IACtB,MAAM,KAAK,OAAO,GAAG,CAAC,cAAc,OAAO,GAAG,CAAC;IAC/C,MAAM,KAAK,OAAO,GAAG,CAAC;IACtB,IAAI,OAAO,GAAG;QACZ,OAAO,CAAC,KAAK;IACf;IACA,KAAK,IAAI,CAAC,QAAQ,cAAc,CAAC,KAAK,IAAI,GAAG,CAAC;IAC9C,KAAK,IAAI,CAAC,QAAQ,cAAc,CAAC,KAAK,IAAI,GAAG,CAAC,UAAU,GAAG,CAAC;IAC5D,MAAM,SAAS,CAAC,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC;IAC1C,OAAO;AACT;AACA,MAAM,MAAM,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa,CAAC,GAAG,GAAG;AACnD,MAAM,eAAe,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AACrD,MAAM,YAAY,CAAC,EACjB,SAAS,EACT,IAAI,EACL;IACC,MAAM,EACJ,WAAW,EACX,iBAAiB,EACjB,WAAW,EACX,gBAAgB,EAChB,SAAS,EACT,KAAK,EACL,SAAS,EACT,KAAK,EACL,UAAU,EACV,YAAY,EACZ,OAAO,EACP,WAAW,EACX,MAAM,EACN,SAAS,EACT,QAAQ,EACT,GAAG,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,iYAAA,CAAA,UAAO;IAC5B,MAAM,cAAc,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;2CAAE,CAAA,QAAS,MAAM,QAAQ;;IACpD,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC5B,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC5B,MAAM,YAAY,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC/B,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC7B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD,EAAE;IACjD,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;gDAAE,CAAA;YACtC,IAAI,aAAa;gBACf,OAAO,OAAO,CAAC,SAAS,GAAG,GAAG,YAAY,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI;gBACpE,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG;YACjC;YACA,EAAE,eAAe;YACjB,MAAM,WAAW,IAAI,sMAAA,CAAA,UAAa,GAAG,eAAe,CAAC,OAAO,OAAO,CAAC,WAAW;YAC/E,MAAM,aAAa,EAAE,KAAK,CAAC,KAAK;YAChC,MAAM,SAAS,IAAI,sMAAA,CAAA,UAAa,GAAG,qBAAqB,CAAC,OAAO,OAAO,CAAC,WAAW;YACnF,MAAM,MAAM,UAAU,KAAK,GAAG,YAAY,CAAC,UAAU,SAAS;YAC9D,UAAU,OAAO,GAAG;gBAClB;gBACA;YACF;YACA,QAAQ,OAAO,GAAG,YAAY,OAAO,CAAC,KAAK;YAC3C,YAAY;gBACV,WAAW;gBACX;gBACA;gBACA,YAAY;oBAAC;iBAAI;YACnB;YACA,eAAe,CAAC,YAAY,OAAO,GAAG,KAAK;YAC3C,+DAA+D;YAC/D,EAAE,MAAM,CAAC,iBAAiB,CAAC,EAAE,SAAS;QACxC;+CAAG;QAAC;QAAa;QAAW;QAAa;QAAa;QAAa;KAAK;IACxE,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;gDAAE,CAAA;YACtC,EAAE,eAAe;YACjB,IAAI,CAAC,WAAW,aAAa;YAC7B,IAAI,UAAU,OAAO,EAAE;gBACrB,MAAM,EACJ,UAAU,EACV,GAAG,EACJ,GAAG,UAAU,OAAO;gBACrB,MAAM,CAAC,KAAK,IAAI,GAAG,CAAC,qBAAqB,OAAO,KAAK,IAAI,iBAAiB,CAAC,KAAK,KAAK;oBAAC;oBAAW;iBAAU;gBAC3G,IAAI,SAAS,gBAAgB,YAAY,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,SAAS;gBAC3E,IAAI,QAAQ,WAAW;oBACrB,SAAS,KAAK,GAAG,CAAC,QAAQ,MAAM,QAAQ,OAAO;gBACjD;gBACA,IAAI,QAAQ,WAAW;oBACrB,SAAS,KAAK,GAAG,CAAC,QAAQ,MAAM,QAAQ,OAAO;gBACjD;gBACA,YAAY,OAAO,CAAC,KAAK,GAAG,QAAQ,OAAO,GAAG;gBAC9C,IAAI,aAAa;oBACf,OAAO,OAAO,CAAC,SAAS,GAAG,GAAG,YAAY,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI;gBACtE;gBACA,aAAa,eAAe,CAAC,IAAI,CAAC,GAAG,QAAQ,IAAI,CAAC,GAAG,QAAQ,IAAI,CAAC,GAAG;gBACrE,OAAO;YACT;QACF;+CAAG;QAAC;QAAa;QAAQ;QAAW;QAAa;QAAmB;KAAK;IACzE,MAAM,cAAc,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;8CAAE,CAAA;YACpC,IAAI,aAAa;gBACf,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG;YACjC;YACA,EAAE,eAAe;YACjB,UAAU,OAAO,GAAG;YACpB;YACA,eAAe,CAAC,YAAY,OAAO,GAAG,IAAI;YAC1C,4FAA4F;YAC5F,EAAE,MAAM,CAAC,qBAAqB,CAAC,EAAE,SAAS;QAC5C;6CAAG;QAAC;QAAa;QAAa;KAAU;IACxC,MAAM,eAAe,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;+CAAE,CAAA;YACrC,EAAE,eAAe;YACjB,aAAa;QACf;8CAAG,EAAE;IACL,MAAM,EACJ,cAAc,EACd,SAAS,EACT,UAAU,EACV,OAAO,EACR,GAAG,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;6BAAE;YAChB,MAAM,YAAY,QAAQ,YAAY,QAAQ,MAAM,QAAQ;YAC5D,MAAM,aAAa,QAAQ,MAAM,QAAQ;YACzC,MAAM,iBAAiB,QAAQ,IAAI,aAAa,QAAQ;YACxD,MAAM,aAAa,IAAI,sMAAA,CAAA,aAAgB,GAAG,kBAAkB,CAAC,KAAK,UAAU,KAAK,GAAG,SAAS;YAC7F,MAAM,UAAU,IAAI,sMAAA,CAAA,UAAa,GAAG,0BAA0B,CAAC;YAC/D,OAAO;gBACL;gBACA;gBACA;gBACA;YACF;QACF;4BAAG;QAAC;QAAW;QAAO;QAAW;KAAM;IACvC,MAAM,SAAS,YAAY,eAAe,UAAU,CAAC,KAAK;IAC1D,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC/C,KAAK;IACP,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3C,QAAQ;QACR,kBAAkB;QAClB,eAAe;QACf,eAAe;QACf,aAAa;QACb,cAAc;IAChB,GAAG,eAAe,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,6WAAA,CAAA,OAAI,EAAE;QACvD,UAAU;YAAC;YAAG,CAAC;YAAY;SAAE;IAC/B,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;QACzC,OAAO;YACL,SAAS;YACT,YAAY;YACZ,OAAO;YACP,SAAS;YACT,cAAc;YACd,YAAY;QACd;QACA,WAAW;QACX,KAAK;IACP,KAAK,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC5C,SAAS;QACT,UAAU;YAAC;YAAG,CAAC,iBAAiB,UAAU,IAAI;YAAK;SAAE;QACrD,UAAU;IACZ,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,oBAAoB;QACtD,MAAM;YAAC,YAAY;YAAK,YAAY;YAAK,iBAAiB;YAAY;YAAG;SAAE;IAC7E,KAAK,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,8WAAA,CAAA,OAAI,EAAE;QAC1C,aAAa;QACb,SAAS,IAAM;QACf,WAAW;QACX,QAAQ;YAAC;YAAG;YAAG;YAAG;YAAG;YAAgB;SAAE;QACvC,WAAW;QACX,MAAM,sMAAA,CAAA,aAAgB;QACtB,OAAO;QACP,SAAS;QACT,eAAe;QACf,aAAa;QACb,qBAAqB,CAAC;QACtB,KAAK;IACP,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC3C,SAAS,IAAM;QACf,UAAU;YAAC;YAAG,iBAAiB,aAAa;YAAK;SAAE;QACnD,aAAa;IACf,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,gBAAgB;QAClD,MAAM;YAAC;YAAW;YAAY;YAAI;SAAE;IACtC,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,qBAAqB;QACxD,aAAa;QACb,WAAW;QACX,OAAO;QACP,SAAS;QACT,eAAe;QACf,qBAAqB,CAAC;QACtB,KAAK;IACP;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2108, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/pivotControls/AxisRotator.js"], "sourcesContent": ["import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { Line } from '../../core/Line.js';\nimport { Html } from '../Html.js';\nimport { context } from './context.js';\n\nconst clickDir = /* @__PURE__ */new THREE.Vector3();\nconst intersectionDir = /* @__PURE__ */new THREE.Vector3();\nconst toDegrees = radians => radians * 180 / Math.PI;\nconst toRadians = degrees => degrees * Math.PI / 180;\nconst calculateAngle = (clickPoint, intersectionPoint, origin, e1, e2) => {\n  clickDir.copy(clickPoint).sub(origin);\n  intersectionDir.copy(intersectionPoint).sub(origin);\n  const dote1e1 = e1.dot(e1);\n  const dote2e2 = e2.dot(e2);\n  const uClick = clickDir.dot(e1) / dote1e1;\n  const vClick = clickDir.dot(e2) / dote2e2;\n  const uIntersection = intersectionDir.dot(e1) / dote1e1;\n  const vIntersection = intersectionDir.dot(e2) / dote2e2;\n  const angleClick = Math.atan2(vClick, uClick);\n  const angleIntersection = Math.atan2(vIntersection, uIntersection);\n  return angleIntersection - angleClick;\n};\nconst fmod = (num, denom) => {\n  let k = Math.floor(num / denom);\n  k = k < 0 ? k + 1 : k;\n  return num - k * denom;\n};\nconst minimizeAngle = angle => {\n  let result = fmod(angle, 2 * Math.PI);\n  if (Math.abs(result) < 1e-6) {\n    return 0.0;\n  }\n  if (result < 0.0) {\n    result += 2 * Math.PI;\n  }\n  return result;\n};\nconst rotMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst posNew = /* @__PURE__ */new THREE.Vector3();\nconst ray = /* @__PURE__ */new THREE.Ray();\nconst intersection = /* @__PURE__ */new THREE.Vector3();\nconst AxisRotator = ({\n  dir1,\n  dir2,\n  axis\n}) => {\n  const {\n    rotationLimits,\n    annotations,\n    annotationsClass,\n    depthTest,\n    scale,\n    lineWidth,\n    fixed,\n    axisColors,\n    hoveredColor,\n    opacity,\n    onDragStart,\n    onDrag,\n    onDragEnd,\n    userData\n  } = React.useContext(context);\n  const camControls = useThree(state => state.controls);\n  const divRef = React.useRef(null);\n  const objRef = React.useRef(null);\n  const angle0 = React.useRef(0);\n  const angle = React.useRef(0);\n  const clickInfo = React.useRef(null);\n  const [isHovered, setIsHovered] = React.useState(false);\n  const onPointerDown = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.innerText = `${toDegrees(angle.current).toFixed(0)}º`;\n      divRef.current.style.display = 'block';\n    }\n    e.stopPropagation();\n    const clickPoint = e.point.clone();\n    const origin = new THREE.Vector3().setFromMatrixPosition(objRef.current.matrixWorld);\n    const e1 = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 0).normalize();\n    const e2 = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 1).normalize();\n    const normal = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 2).normalize();\n    const plane = new THREE.Plane().setFromNormalAndCoplanarPoint(normal, origin);\n    clickInfo.current = {\n      clickPoint,\n      origin,\n      e1,\n      e2,\n      normal,\n      plane\n    };\n    onDragStart({\n      component: 'Rotator',\n      axis,\n      origin,\n      directions: [e1, e2, normal]\n    });\n    camControls && (camControls.enabled = false);\n    // @ts-ignore\n    e.target.setPointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragStart, axis]);\n  const onPointerMove = React.useCallback(e => {\n    e.stopPropagation();\n    if (!isHovered) setIsHovered(true);\n    if (clickInfo.current) {\n      const {\n        clickPoint,\n        origin,\n        e1,\n        e2,\n        normal,\n        plane\n      } = clickInfo.current;\n      const [min, max] = (rotationLimits == null ? void 0 : rotationLimits[axis]) || [undefined, undefined];\n      ray.copy(e.ray);\n      ray.intersectPlane(plane, intersection);\n      ray.direction.negate();\n      ray.intersectPlane(plane, intersection);\n      let deltaAngle = calculateAngle(clickPoint, intersection, origin, e1, e2);\n      let degrees = toDegrees(deltaAngle);\n\n      // @ts-ignore\n      if (e.shiftKey) {\n        degrees = Math.round(degrees / 10) * 10;\n        deltaAngle = toRadians(degrees);\n      }\n      if (min !== undefined && max !== undefined && max - min < 2 * Math.PI) {\n        deltaAngle = minimizeAngle(deltaAngle);\n        deltaAngle = deltaAngle > Math.PI ? deltaAngle - 2 * Math.PI : deltaAngle;\n        deltaAngle = THREE.MathUtils.clamp(deltaAngle, min - angle0.current, max - angle0.current);\n        angle.current = angle0.current + deltaAngle;\n      } else {\n        angle.current = minimizeAngle(angle0.current + deltaAngle);\n        angle.current = angle.current > Math.PI ? angle.current - 2 * Math.PI : angle.current;\n      }\n      if (annotations) {\n        degrees = toDegrees(angle.current);\n        divRef.current.innerText = `${degrees.toFixed(0)}º`;\n      }\n      rotMatrix.makeRotationAxis(normal, deltaAngle);\n      posNew.copy(origin).applyMatrix4(rotMatrix).sub(origin).negate();\n      rotMatrix.setPosition(posNew);\n      onDrag(rotMatrix);\n    }\n  }, [annotations, onDrag, isHovered, rotationLimits, axis]);\n  const onPointerUp = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.style.display = 'none';\n    }\n    e.stopPropagation();\n    angle0.current = angle.current;\n    clickInfo.current = null;\n    onDragEnd();\n    camControls && (camControls.enabled = true);\n    // @ts-ignore\n    e.target.releasePointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragEnd]);\n  const onPointerOut = React.useCallback(e => {\n    e.stopPropagation();\n    setIsHovered(false);\n  }, []);\n  const matrixL = React.useMemo(() => {\n    const dir1N = dir1.clone().normalize();\n    const dir2N = dir2.clone().normalize();\n    return new THREE.Matrix4().makeBasis(dir1N, dir2N, dir1N.clone().cross(dir2N));\n  }, [dir1, dir2]);\n  const r = fixed ? 0.65 : scale * 0.65;\n  const arc = React.useMemo(() => {\n    const segments = 32;\n    const points = [];\n    for (let j = 0; j <= segments; j++) {\n      const angle = j * (Math.PI / 2) / segments;\n      points.push(new THREE.Vector3(Math.cos(angle) * r, Math.sin(angle) * r, 0));\n    }\n    return points;\n  }, [r]);\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: objRef,\n    onPointerDown: onPointerDown,\n    onPointerMove: onPointerMove,\n    onPointerUp: onPointerUp,\n    onPointerOut: onPointerOut,\n    matrix: matrixL,\n    matrixAutoUpdate: false\n  }, annotations && /*#__PURE__*/React.createElement(Html, {\n    position: [r, r, 0]\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'none',\n      background: '#151520',\n      color: 'white',\n      padding: '6px 8px',\n      borderRadius: 7,\n      whiteSpace: 'nowrap'\n    },\n    className: annotationsClass,\n    ref: divRef\n  })), /*#__PURE__*/React.createElement(Line, {\n    points: arc,\n    lineWidth: lineWidth * 4,\n    visible: false,\n    userData: userData\n  }), /*#__PURE__*/React.createElement(Line, {\n    transparent: true,\n    raycast: () => null,\n    depthTest: depthTest,\n    points: arc,\n    lineWidth: lineWidth,\n    side: THREE.DoubleSide,\n    color: isHovered ? hoveredColor : axisColors[axis],\n    opacity: opacity,\n    polygonOffset: true,\n    polygonOffsetFactor: -10,\n    fog: false\n  }));\n};\n\nexport { AxisRotator };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,WAAW,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AACjD,MAAM,kBAAkB,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AACxD,MAAM,YAAY,CAAA,UAAW,UAAU,MAAM,KAAK,EAAE;AACpD,MAAM,YAAY,CAAA,UAAW,UAAU,KAAK,EAAE,GAAG;AACjD,MAAM,iBAAiB,CAAC,YAAY,mBAAmB,QAAQ,IAAI;IACjE,SAAS,IAAI,CAAC,YAAY,GAAG,CAAC;IAC9B,gBAAgB,IAAI,CAAC,mBAAmB,GAAG,CAAC;IAC5C,MAAM,UAAU,GAAG,GAAG,CAAC;IACvB,MAAM,UAAU,GAAG,GAAG,CAAC;IACvB,MAAM,SAAS,SAAS,GAAG,CAAC,MAAM;IAClC,MAAM,SAAS,SAAS,GAAG,CAAC,MAAM;IAClC,MAAM,gBAAgB,gBAAgB,GAAG,CAAC,MAAM;IAChD,MAAM,gBAAgB,gBAAgB,GAAG,CAAC,MAAM;IAChD,MAAM,aAAa,KAAK,KAAK,CAAC,QAAQ;IACtC,MAAM,oBAAoB,KAAK,KAAK,CAAC,eAAe;IACpD,OAAO,oBAAoB;AAC7B;AACA,MAAM,OAAO,CAAC,KAAK;IACjB,IAAI,IAAI,KAAK,KAAK,CAAC,MAAM;IACzB,IAAI,IAAI,IAAI,IAAI,IAAI;IACpB,OAAO,MAAM,IAAI;AACnB;AACA,MAAM,gBAAgB,CAAA;IACpB,IAAI,SAAS,KAAK,OAAO,IAAI,KAAK,EAAE;IACpC,IAAI,KAAK,GAAG,CAAC,UAAU,MAAM;QAC3B,OAAO;IACT;IACA,IAAI,SAAS,KAAK;QAChB,UAAU,IAAI,KAAK,EAAE;IACvB;IACA,OAAO;AACT;AACA,MAAM,YAAY,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAClD,MAAM,SAAS,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC/C,MAAM,MAAM,aAAa,GAAE,IAAI,sMAAA,CAAA,MAAS;AACxC,MAAM,eAAe,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AACrD,MAAM,cAAc,CAAC,EACnB,IAAI,EACJ,IAAI,EACJ,IAAI,EACL;IACC,MAAM,EACJ,cAAc,EACd,WAAW,EACX,gBAAgB,EAChB,SAAS,EACT,KAAK,EACL,SAAS,EACT,KAAK,EACL,UAAU,EACV,YAAY,EACZ,OAAO,EACP,WAAW,EACX,MAAM,EACN,SAAS,EACT,QAAQ,EACT,GAAG,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,iYAAA,CAAA,UAAO;IAC5B,MAAM,cAAc,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;6CAAE,CAAA,QAAS,MAAM,QAAQ;;IACpD,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC5B,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC5B,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC5B,MAAM,QAAQ,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC3B,MAAM,YAAY,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD,EAAE;IACjD,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;kDAAE,CAAA;YACtC,IAAI,aAAa;gBACf,OAAO,OAAO,CAAC,SAAS,GAAG,GAAG,UAAU,MAAM,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;gBACpE,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG;YACjC;YACA,EAAE,eAAe;YACjB,MAAM,aAAa,EAAE,KAAK,CAAC,KAAK;YAChC,MAAM,SAAS,IAAI,sMAAA,CAAA,UAAa,GAAG,qBAAqB,CAAC,OAAO,OAAO,CAAC,WAAW;YACnF,MAAM,KAAK,IAAI,sMAAA,CAAA,UAAa,GAAG,mBAAmB,CAAC,OAAO,OAAO,CAAC,WAAW,EAAE,GAAG,SAAS;YAC3F,MAAM,KAAK,IAAI,sMAAA,CAAA,UAAa,GAAG,mBAAmB,CAAC,OAAO,OAAO,CAAC,WAAW,EAAE,GAAG,SAAS;YAC3F,MAAM,SAAS,IAAI,sMAAA,CAAA,UAAa,GAAG,mBAAmB,CAAC,OAAO,OAAO,CAAC,WAAW,EAAE,GAAG,SAAS;YAC/F,MAAM,QAAQ,IAAI,sMAAA,CAAA,QAAW,GAAG,6BAA6B,CAAC,QAAQ;YACtE,UAAU,OAAO,GAAG;gBAClB;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;YACA,YAAY;gBACV,WAAW;gBACX;gBACA;gBACA,YAAY;oBAAC;oBAAI;oBAAI;iBAAO;YAC9B;YACA,eAAe,CAAC,YAAY,OAAO,GAAG,KAAK;YAC3C,aAAa;YACb,EAAE,MAAM,CAAC,iBAAiB,CAAC,EAAE,SAAS;QACxC;iDAAG;QAAC;QAAa;QAAa;QAAa;KAAK;IAChD,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;kDAAE,CAAA;YACtC,EAAE,eAAe;YACjB,IAAI,CAAC,WAAW,aAAa;YAC7B,IAAI,UAAU,OAAO,EAAE;gBACrB,MAAM,EACJ,UAAU,EACV,MAAM,EACN,EAAE,EACF,EAAE,EACF,MAAM,EACN,KAAK,EACN,GAAG,UAAU,OAAO;gBACrB,MAAM,CAAC,KAAK,IAAI,GAAG,CAAC,kBAAkB,OAAO,KAAK,IAAI,cAAc,CAAC,KAAK,KAAK;oBAAC;oBAAW;iBAAU;gBACrG,IAAI,IAAI,CAAC,EAAE,GAAG;gBACd,IAAI,cAAc,CAAC,OAAO;gBAC1B,IAAI,SAAS,CAAC,MAAM;gBACpB,IAAI,cAAc,CAAC,OAAO;gBAC1B,IAAI,aAAa,eAAe,YAAY,cAAc,QAAQ,IAAI;gBACtE,IAAI,UAAU,UAAU;gBAExB,aAAa;gBACb,IAAI,EAAE,QAAQ,EAAE;oBACd,UAAU,KAAK,KAAK,CAAC,UAAU,MAAM;oBACrC,aAAa,UAAU;gBACzB;gBACA,IAAI,QAAQ,aAAa,QAAQ,aAAa,MAAM,MAAM,IAAI,KAAK,EAAE,EAAE;oBACrE,aAAa,cAAc;oBAC3B,aAAa,aAAa,KAAK,EAAE,GAAG,aAAa,IAAI,KAAK,EAAE,GAAG;oBAC/D,aAAa,sMAAA,CAAA,YAAe,CAAC,KAAK,CAAC,YAAY,MAAM,OAAO,OAAO,EAAE,MAAM,OAAO,OAAO;oBACzF,MAAM,OAAO,GAAG,OAAO,OAAO,GAAG;gBACnC,OAAO;oBACL,MAAM,OAAO,GAAG,cAAc,OAAO,OAAO,GAAG;oBAC/C,MAAM,OAAO,GAAG,MAAM,OAAO,GAAG,KAAK,EAAE,GAAG,MAAM,OAAO,GAAG,IAAI,KAAK,EAAE,GAAG,MAAM,OAAO;gBACvF;gBACA,IAAI,aAAa;oBACf,UAAU,UAAU,MAAM,OAAO;oBACjC,OAAO,OAAO,CAAC,SAAS,GAAG,GAAG,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC;gBACrD;gBACA,UAAU,gBAAgB,CAAC,QAAQ;gBACnC,OAAO,IAAI,CAAC,QAAQ,YAAY,CAAC,WAAW,GAAG,CAAC,QAAQ,MAAM;gBAC9D,UAAU,WAAW,CAAC;gBACtB,OAAO;YACT;QACF;iDAAG;QAAC;QAAa;QAAQ;QAAW;QAAgB;KAAK;IACzD,MAAM,cAAc,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;gDAAE,CAAA;YACpC,IAAI,aAAa;gBACf,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG;YACjC;YACA,EAAE,eAAe;YACjB,OAAO,OAAO,GAAG,MAAM,OAAO;YAC9B,UAAU,OAAO,GAAG;YACpB;YACA,eAAe,CAAC,YAAY,OAAO,GAAG,IAAI;YAC1C,aAAa;YACb,EAAE,MAAM,CAAC,qBAAqB,CAAC,EAAE,SAAS;QAC5C;+CAAG;QAAC;QAAa;QAAa;KAAU;IACxC,MAAM,eAAe,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;iDAAE,CAAA;YACrC,EAAE,eAAe;YACjB,aAAa;QACf;gDAAG,EAAE;IACL,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;wCAAE;YAC5B,MAAM,QAAQ,KAAK,KAAK,GAAG,SAAS;YACpC,MAAM,QAAQ,KAAK,KAAK,GAAG,SAAS;YACpC,OAAO,IAAI,sMAAA,CAAA,UAAa,GAAG,SAAS,CAAC,OAAO,OAAO,MAAM,KAAK,GAAG,KAAK,CAAC;QACzE;uCAAG;QAAC;QAAM;KAAK;IACf,MAAM,IAAI,QAAQ,OAAO,QAAQ;IACjC,MAAM,MAAM,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;oCAAE;YACxB,MAAM,WAAW;YACjB,MAAM,SAAS,EAAE;YACjB,IAAK,IAAI,IAAI,GAAG,KAAK,UAAU,IAAK;gBAClC,MAAM,QAAQ,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI;gBAClC,OAAO,IAAI,CAAC,IAAI,sMAAA,CAAA,UAAa,CAAC,KAAK,GAAG,CAAC,SAAS,GAAG,KAAK,GAAG,CAAC,SAAS,GAAG;YAC1E;YACA,OAAO;QACT;mCAAG;QAAC;KAAE;IACN,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC/C,KAAK;QACL,eAAe;QACf,eAAe;QACf,aAAa;QACb,cAAc;QACd,QAAQ;QACR,kBAAkB;IACpB,GAAG,eAAe,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,6WAAA,CAAA,OAAI,EAAE;QACvD,UAAU;YAAC;YAAG;YAAG;SAAE;IACrB,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;QACzC,OAAO;YACL,SAAS;YACT,YAAY;YACZ,OAAO;YACP,SAAS;YACT,cAAc;YACd,YAAY;QACd;QACA,WAAW;QACX,KAAK;IACP,KAAK,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,8WAAA,CAAA,OAAI,EAAE;QAC1C,QAAQ;QACR,WAAW,YAAY;QACvB,SAAS;QACT,UAAU;IACZ,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,8WAAA,CAAA,OAAI,EAAE;QACzC,aAAa;QACb,SAAS,IAAM;QACf,WAAW;QACX,QAAQ;QACR,WAAW;QACX,MAAM,sMAAA,CAAA,aAAgB;QACtB,OAAO,YAAY,eAAe,UAAU,CAAC,KAAK;QAClD,SAAS;QACT,eAAe;QACf,qBAAqB,CAAC;QACtB,KAAK;IACP;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2357, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2363, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/pivotControls/PlaneSlider.js"], "sourcesContent": ["import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { Line } from '../../core/Line.js';\nimport { Html } from '../Html.js';\nimport { context } from './context.js';\n\nconst decomposeIntoBasis = (e1, e2, offset) => {\n  const i1 = Math.abs(e1.x) >= Math.abs(e1.y) && Math.abs(e1.x) >= Math.abs(e1.z) ? 0 : Math.abs(e1.y) >= Math.abs(e1.x) && Math.abs(e1.y) >= Math.abs(e1.z) ? 1 : 2;\n  const e2DegrowthOrder = [0, 1, 2].sort((a, b) => Math.abs(e2.getComponent(b)) - Math.abs(e2.getComponent(a)));\n  const i2 = i1 === e2DegrowthOrder[0] ? e2DegrowthOrder[1] : e2DegrowthOrder[0];\n  const a1 = e1.getComponent(i1);\n  const a2 = e1.getComponent(i2);\n  const b1 = e2.getComponent(i1);\n  const b2 = e2.getComponent(i2);\n  const c1 = offset.getComponent(i1);\n  const c2 = offset.getComponent(i2);\n  const y = (c2 - c1 * (a2 / a1)) / (b2 - b1 * (a2 / a1));\n  const x = (c1 - y * b1) / a1;\n  return [x, y];\n};\nconst ray = /* @__PURE__ */new THREE.Ray();\nconst intersection = /* @__PURE__ */new THREE.Vector3();\nconst offsetMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst PlaneSlider = ({\n  dir1,\n  dir2,\n  axis\n}) => {\n  const {\n    translation,\n    translationLimits,\n    annotations,\n    annotationsClass,\n    depthTest,\n    scale,\n    lineWidth,\n    fixed,\n    axisColors,\n    hoveredColor,\n    opacity,\n    onDragStart,\n    onDrag,\n    onDragEnd,\n    userData\n  } = React.useContext(context);\n  const camControls = useThree(state => state.controls);\n  const divRef = React.useRef(null);\n  const objRef = React.useRef(null);\n  const clickInfo = React.useRef(null);\n  const offsetX0 = React.useRef(0);\n  const offsetY0 = React.useRef(0);\n  const [isHovered, setIsHovered] = React.useState(false);\n  const onPointerDown = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.innerText = `${translation.current[(axis + 1) % 3].toFixed(2)}, ${translation.current[(axis + 2) % 3].toFixed(2)}`;\n      divRef.current.style.display = 'block';\n    }\n    e.stopPropagation();\n    const clickPoint = e.point.clone();\n    const origin = new THREE.Vector3().setFromMatrixPosition(objRef.current.matrixWorld);\n    const e1 = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 0).normalize();\n    const e2 = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 1).normalize();\n    const normal = new THREE.Vector3().setFromMatrixColumn(objRef.current.matrixWorld, 2).normalize();\n    const plane = new THREE.Plane().setFromNormalAndCoplanarPoint(normal, origin);\n    clickInfo.current = {\n      clickPoint,\n      e1,\n      e2,\n      plane\n    };\n    offsetX0.current = translation.current[(axis + 1) % 3];\n    offsetY0.current = translation.current[(axis + 2) % 3];\n    onDragStart({\n      component: 'Slider',\n      axis,\n      origin,\n      directions: [e1, e2, normal]\n    });\n    camControls && (camControls.enabled = false);\n    // @ts-ignore\n    e.target.setPointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragStart, axis]);\n  const onPointerMove = React.useCallback(e => {\n    e.stopPropagation();\n    if (!isHovered) setIsHovered(true);\n    if (clickInfo.current) {\n      const {\n        clickPoint,\n        e1,\n        e2,\n        plane\n      } = clickInfo.current;\n      const [minX, maxX] = (translationLimits == null ? void 0 : translationLimits[(axis + 1) % 3]) || [undefined, undefined];\n      const [minY, maxY] = (translationLimits == null ? void 0 : translationLimits[(axis + 2) % 3]) || [undefined, undefined];\n      ray.copy(e.ray);\n      ray.intersectPlane(plane, intersection);\n      ray.direction.negate();\n      ray.intersectPlane(plane, intersection);\n      intersection.sub(clickPoint);\n      let [offsetX, offsetY] = decomposeIntoBasis(e1, e2, intersection);\n      /* let offsetY = (intersection.y - (intersection.x * e1.y) / e1.x) / (e2.y - (e2.x * e1.y) / e1.x)\n      let offsetX = (intersection.x - offsetY * e2.x) / e1.x */\n      if (minX !== undefined) {\n        offsetX = Math.max(offsetX, minX - offsetX0.current);\n      }\n      if (maxX !== undefined) {\n        offsetX = Math.min(offsetX, maxX - offsetX0.current);\n      }\n      if (minY !== undefined) {\n        offsetY = Math.max(offsetY, minY - offsetY0.current);\n      }\n      if (maxY !== undefined) {\n        offsetY = Math.min(offsetY, maxY - offsetY0.current);\n      }\n      translation.current[(axis + 1) % 3] = offsetX0.current + offsetX;\n      translation.current[(axis + 2) % 3] = offsetY0.current + offsetY;\n      if (annotations) {\n        divRef.current.innerText = `${translation.current[(axis + 1) % 3].toFixed(2)}, ${translation.current[(axis + 2) % 3].toFixed(2)}`;\n      }\n      offsetMatrix.makeTranslation(offsetX * e1.x + offsetY * e2.x, offsetX * e1.y + offsetY * e2.y, offsetX * e1.z + offsetY * e2.z);\n      onDrag(offsetMatrix);\n    }\n  }, [annotations, onDrag, isHovered, translation, translationLimits, axis]);\n  const onPointerUp = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.style.display = 'none';\n    }\n    e.stopPropagation();\n    clickInfo.current = null;\n    onDragEnd();\n    camControls && (camControls.enabled = true);\n    // @ts-ignore\n    e.target.releasePointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragEnd]);\n  const onPointerOut = React.useCallback(e => {\n    e.stopPropagation();\n    setIsHovered(false);\n  }, []);\n  const matrixL = React.useMemo(() => {\n    const dir1N = dir1.clone().normalize();\n    const dir2N = dir2.clone().normalize();\n    return new THREE.Matrix4().makeBasis(dir1N, dir2N, dir1N.clone().cross(dir2N));\n  }, [dir1, dir2]);\n  const pos1 = fixed ? 1 / 7 : scale / 7;\n  const length = fixed ? 0.225 : scale * 0.225;\n  const color = isHovered ? hoveredColor : axisColors[axis];\n  const points = React.useMemo(() => [new THREE.Vector3(0, 0, 0), new THREE.Vector3(0, length, 0), new THREE.Vector3(length, length, 0), new THREE.Vector3(length, 0, 0), new THREE.Vector3(0, 0, 0)], [length]);\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: objRef,\n    matrix: matrixL,\n    matrixAutoUpdate: false\n  }, annotations && /*#__PURE__*/React.createElement(Html, {\n    position: [0, 0, 0]\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'none',\n      background: '#151520',\n      color: 'white',\n      padding: '6px 8px',\n      borderRadius: 7,\n      whiteSpace: 'nowrap'\n    },\n    className: annotationsClass,\n    ref: divRef\n  })), /*#__PURE__*/React.createElement(\"group\", {\n    position: [pos1 * 1.7, pos1 * 1.7, 0]\n  }, /*#__PURE__*/React.createElement(\"mesh\", {\n    visible: true,\n    onPointerDown: onPointerDown,\n    onPointerMove: onPointerMove,\n    onPointerUp: onPointerUp,\n    onPointerOut: onPointerOut,\n    scale: length,\n    userData: userData\n  }, /*#__PURE__*/React.createElement(\"planeGeometry\", null), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    depthTest: depthTest,\n    color: color,\n    polygonOffset: true,\n    polygonOffsetFactor: -10,\n    side: THREE.DoubleSide,\n    fog: false\n  })), /*#__PURE__*/React.createElement(Line, {\n    position: [-length / 2, -length / 2, 0],\n    transparent: true,\n    depthTest: depthTest,\n    points: points,\n    lineWidth: lineWidth,\n    color: color,\n    opacity: opacity,\n    polygonOffset: true,\n    polygonOffsetFactor: -10,\n    userData: userData,\n    fog: false\n  })));\n};\n\nexport { PlaneSlider };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,qBAAqB,CAAC,IAAI,IAAI;IAClC,MAAM,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI;IACjK,MAAM,kBAAkB;QAAC;QAAG;QAAG;KAAE,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,KAAK,GAAG,CAAC,GAAG,YAAY,CAAC,MAAM,KAAK,GAAG,CAAC,GAAG,YAAY,CAAC;IACzG,MAAM,KAAK,OAAO,eAAe,CAAC,EAAE,GAAG,eAAe,CAAC,EAAE,GAAG,eAAe,CAAC,EAAE;IAC9E,MAAM,KAAK,GAAG,YAAY,CAAC;IAC3B,MAAM,KAAK,GAAG,YAAY,CAAC;IAC3B,MAAM,KAAK,GAAG,YAAY,CAAC;IAC3B,MAAM,KAAK,GAAG,YAAY,CAAC;IAC3B,MAAM,KAAK,OAAO,YAAY,CAAC;IAC/B,MAAM,KAAK,OAAO,YAAY,CAAC;IAC/B,MAAM,IAAI,CAAC,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC;IACtD,MAAM,IAAI,CAAC,KAAK,IAAI,EAAE,IAAI;IAC1B,OAAO;QAAC;QAAG;KAAE;AACf;AACA,MAAM,MAAM,aAAa,GAAE,IAAI,sMAAA,CAAA,MAAS;AACxC,MAAM,eAAe,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AACrD,MAAM,eAAe,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AACrD,MAAM,cAAc,CAAC,EACnB,IAAI,EACJ,IAAI,EACJ,IAAI,EACL;IACC,MAAM,EACJ,WAAW,EACX,iBAAiB,EACjB,WAAW,EACX,gBAAgB,EAChB,SAAS,EACT,KAAK,EACL,SAAS,EACT,KAAK,EACL,UAAU,EACV,YAAY,EACZ,OAAO,EACP,WAAW,EACX,MAAM,EACN,SAAS,EACT,QAAQ,EACT,GAAG,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,iYAAA,CAAA,UAAO;IAC5B,MAAM,cAAc,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;6CAAE,CAAA,QAAS,MAAM,QAAQ;;IACpD,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC5B,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC5B,MAAM,YAAY,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC/B,MAAM,WAAW,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC9B,MAAM,WAAW,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC9B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD,EAAE;IACjD,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;kDAAE,CAAA;YACtC,IAAI,aAAa;gBACf,OAAO,OAAO,CAAC,SAAS,GAAG,GAAG,YAAY,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,YAAY,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI;gBACjI,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG;YACjC;YACA,EAAE,eAAe;YACjB,MAAM,aAAa,EAAE,KAAK,CAAC,KAAK;YAChC,MAAM,SAAS,IAAI,sMAAA,CAAA,UAAa,GAAG,qBAAqB,CAAC,OAAO,OAAO,CAAC,WAAW;YACnF,MAAM,KAAK,IAAI,sMAAA,CAAA,UAAa,GAAG,mBAAmB,CAAC,OAAO,OAAO,CAAC,WAAW,EAAE,GAAG,SAAS;YAC3F,MAAM,KAAK,IAAI,sMAAA,CAAA,UAAa,GAAG,mBAAmB,CAAC,OAAO,OAAO,CAAC,WAAW,EAAE,GAAG,SAAS;YAC3F,MAAM,SAAS,IAAI,sMAAA,CAAA,UAAa,GAAG,mBAAmB,CAAC,OAAO,OAAO,CAAC,WAAW,EAAE,GAAG,SAAS;YAC/F,MAAM,QAAQ,IAAI,sMAAA,CAAA,QAAW,GAAG,6BAA6B,CAAC,QAAQ;YACtE,UAAU,OAAO,GAAG;gBAClB;gBACA;gBACA;gBACA;YACF;YACA,SAAS,OAAO,GAAG,YAAY,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE;YACtD,SAAS,OAAO,GAAG,YAAY,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE;YACtD,YAAY;gBACV,WAAW;gBACX;gBACA;gBACA,YAAY;oBAAC;oBAAI;oBAAI;iBAAO;YAC9B;YACA,eAAe,CAAC,YAAY,OAAO,GAAG,KAAK;YAC3C,aAAa;YACb,EAAE,MAAM,CAAC,iBAAiB,CAAC,EAAE,SAAS;QACxC;iDAAG;QAAC;QAAa;QAAa;QAAa;KAAK;IAChD,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;kDAAE,CAAA;YACtC,EAAE,eAAe;YACjB,IAAI,CAAC,WAAW,aAAa;YAC7B,IAAI,UAAU,OAAO,EAAE;gBACrB,MAAM,EACJ,UAAU,EACV,EAAE,EACF,EAAE,EACF,KAAK,EACN,GAAG,UAAU,OAAO;gBACrB,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,qBAAqB,OAAO,KAAK,IAAI,iBAAiB,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK;oBAAC;oBAAW;iBAAU;gBACvH,MAAM,CAAC,MAAM,KAAK,GAAG,CAAC,qBAAqB,OAAO,KAAK,IAAI,iBAAiB,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK;oBAAC;oBAAW;iBAAU;gBACvH,IAAI,IAAI,CAAC,EAAE,GAAG;gBACd,IAAI,cAAc,CAAC,OAAO;gBAC1B,IAAI,SAAS,CAAC,MAAM;gBACpB,IAAI,cAAc,CAAC,OAAO;gBAC1B,aAAa,GAAG,CAAC;gBACjB,IAAI,CAAC,SAAS,QAAQ,GAAG,mBAAmB,IAAI,IAAI;gBACpD;6DACuD,GACvD,IAAI,SAAS,WAAW;oBACtB,UAAU,KAAK,GAAG,CAAC,SAAS,OAAO,SAAS,OAAO;gBACrD;gBACA,IAAI,SAAS,WAAW;oBACtB,UAAU,KAAK,GAAG,CAAC,SAAS,OAAO,SAAS,OAAO;gBACrD;gBACA,IAAI,SAAS,WAAW;oBACtB,UAAU,KAAK,GAAG,CAAC,SAAS,OAAO,SAAS,OAAO;gBACrD;gBACA,IAAI,SAAS,WAAW;oBACtB,UAAU,KAAK,GAAG,CAAC,SAAS,OAAO,SAAS,OAAO;gBACrD;gBACA,YAAY,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,SAAS,OAAO,GAAG;gBACzD,YAAY,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,SAAS,OAAO,GAAG;gBACzD,IAAI,aAAa;oBACf,OAAO,OAAO,CAAC,SAAS,GAAG,GAAG,YAAY,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,YAAY,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI;gBACnI;gBACA,aAAa,eAAe,CAAC,UAAU,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC;gBAC9H,OAAO;YACT;QACF;iDAAG;QAAC;QAAa;QAAQ;QAAW;QAAa;QAAmB;KAAK;IACzE,MAAM,cAAc,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;gDAAE,CAAA;YACpC,IAAI,aAAa;gBACf,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG;YACjC;YACA,EAAE,eAAe;YACjB,UAAU,OAAO,GAAG;YACpB;YACA,eAAe,CAAC,YAAY,OAAO,GAAG,IAAI;YAC1C,aAAa;YACb,EAAE,MAAM,CAAC,qBAAqB,CAAC,EAAE,SAAS;QAC5C;+CAAG;QAAC;QAAa;QAAa;KAAU;IACxC,MAAM,eAAe,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;iDAAE,CAAA;YACrC,EAAE,eAAe;YACjB,aAAa;QACf;gDAAG,EAAE;IACL,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;wCAAE;YAC5B,MAAM,QAAQ,KAAK,KAAK,GAAG,SAAS;YACpC,MAAM,QAAQ,KAAK,KAAK,GAAG,SAAS;YACpC,OAAO,IAAI,sMAAA,CAAA,UAAa,GAAG,SAAS,CAAC,OAAO,OAAO,MAAM,KAAK,GAAG,KAAK,CAAC;QACzE;uCAAG;QAAC;QAAM;KAAK;IACf,MAAM,OAAO,QAAQ,IAAI,IAAI,QAAQ;IACrC,MAAM,SAAS,QAAQ,QAAQ,QAAQ;IACvC,MAAM,QAAQ,YAAY,eAAe,UAAU,CAAC,KAAK;IACzD,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;uCAAE,IAAM;gBAAC,IAAI,sMAAA,CAAA,UAAa,CAAC,GAAG,GAAG;gBAAI,IAAI,sMAAA,CAAA,UAAa,CAAC,GAAG,QAAQ;gBAAI,IAAI,sMAAA,CAAA,UAAa,CAAC,QAAQ,QAAQ;gBAAI,IAAI,sMAAA,CAAA,UAAa,CAAC,QAAQ,GAAG;gBAAI,IAAI,sMAAA,CAAA,UAAa,CAAC,GAAG,GAAG;aAAG;sCAAE;QAAC;KAAO;IAC7M,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC/C,KAAK;QACL,QAAQ;QACR,kBAAkB;IACpB,GAAG,eAAe,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,6WAAA,CAAA,OAAI,EAAE;QACvD,UAAU;YAAC;YAAG;YAAG;SAAE;IACrB,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;QACzC,OAAO;YACL,SAAS;YACT,YAAY;YACZ,OAAO;YACP,SAAS;YACT,cAAc;YACd,YAAY;QACd;QACA,WAAW;QACX,KAAK;IACP,KAAK,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC7C,UAAU;YAAC,OAAO;YAAK,OAAO;YAAK;SAAE;IACvC,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC1C,SAAS;QACT,eAAe;QACf,eAAe;QACf,aAAa;QACb,cAAc;QACd,OAAO;QACP,UAAU;IACZ,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,iBAAiB,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,qBAAqB;QAChH,aAAa;QACb,WAAW;QACX,OAAO;QACP,eAAe;QACf,qBAAqB,CAAC;QACtB,MAAM,sMAAA,CAAA,aAAgB;QACtB,KAAK;IACP,KAAK,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,8WAAA,CAAA,OAAI,EAAE;QAC1C,UAAU;YAAC,CAAC,SAAS;YAAG,CAAC,SAAS;YAAG;SAAE;QACvC,aAAa;QACb,WAAW;QACX,QAAQ;QACR,WAAW;QACX,OAAO;QACP,SAAS;QACT,eAAe;QACf,qBAAqB,CAAC;QACtB,UAAU;QACV,KAAK;IACP;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2613, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2619, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/pivotControls/ScalingSphere.js"], "sourcesContent": ["import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { Html } from '../Html.js';\nimport { context } from './context.js';\nimport { calculateScaleFactor } from '../../core/calculateScaleFactor.js';\n\nconst vec1 = /* @__PURE__ */new THREE.Vector3();\nconst vec2 = /* @__PURE__ */new THREE.Vector3();\nconst calculateOffset = (clickPoint, normal, rayStart, rayDir) => {\n  const e1 = normal.dot(normal);\n  const e2 = normal.dot(clickPoint) - normal.dot(rayStart);\n  const e3 = normal.dot(rayDir);\n  if (e3 === 0) {\n    return -e2 / e1;\n  }\n  vec1.copy(rayDir).multiplyScalar(e1 / e3).sub(normal);\n  vec2.copy(rayDir).multiplyScalar(e2 / e3).add(rayStart).sub(clickPoint);\n  const offset = -vec1.dot(vec2) / vec1.dot(vec1);\n  return offset;\n};\nconst upV = /* @__PURE__ */new THREE.Vector3(0, 1, 0);\nconst scaleV = /* @__PURE__ */new THREE.Vector3();\nconst scaleMatrix = /* @__PURE__ */new THREE.Matrix4();\nconst ScalingSphere = ({\n  direction,\n  axis\n}) => {\n  const {\n    scaleLimits,\n    annotations,\n    annotationsClass,\n    depthTest,\n    scale,\n    lineWidth,\n    fixed,\n    axisColors,\n    hoveredColor,\n    opacity,\n    onDragStart,\n    onDrag,\n    onDragEnd,\n    userData\n  } = React.useContext(context);\n  const size = useThree(state => state.size);\n  const camControls = useThree(state => state.controls);\n  const divRef = React.useRef(null);\n  const objRef = React.useRef(null);\n  const meshRef = React.useRef(null);\n  const scale0 = React.useRef(1);\n  const scaleCur = React.useRef(1);\n  const clickInfo = React.useRef(null);\n  const [isHovered, setIsHovered] = React.useState(false);\n  const position = fixed ? 1.2 : 1.2 * scale;\n  const onPointerDown = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.innerText = `${scaleCur.current.toFixed(2)}`;\n      divRef.current.style.display = 'block';\n    }\n    e.stopPropagation();\n    const rotation = new THREE.Matrix4().extractRotation(objRef.current.matrixWorld);\n    const clickPoint = e.point.clone();\n    const origin = new THREE.Vector3().setFromMatrixPosition(objRef.current.matrixWorld);\n    const dir = direction.clone().applyMatrix4(rotation).normalize();\n    const mPLG = objRef.current.matrixWorld.clone();\n    const mPLGInv = mPLG.clone().invert();\n    const offsetMultiplier = fixed ? 1 / calculateScaleFactor(objRef.current.getWorldPosition(vec1), scale, e.camera, size) : 1;\n    clickInfo.current = {\n      clickPoint,\n      dir,\n      mPLG,\n      mPLGInv,\n      offsetMultiplier\n    };\n    onDragStart({\n      component: 'Sphere',\n      axis,\n      origin,\n      directions: [dir]\n    });\n    camControls && (camControls.enabled = false);\n    // @ts-ignore - setPointerCapture is not in the type definition\n    e.target.setPointerCapture(e.pointerId);\n  }, [annotations, camControls, direction, onDragStart, axis, fixed, scale, size]);\n  const onPointerMove = React.useCallback(e => {\n    e.stopPropagation();\n    if (!isHovered) setIsHovered(true);\n    if (clickInfo.current) {\n      const {\n        clickPoint,\n        dir,\n        mPLG,\n        mPLGInv,\n        offsetMultiplier\n      } = clickInfo.current;\n      const [min, max] = (scaleLimits == null ? void 0 : scaleLimits[axis]) || [1e-5, undefined]; // always limit the minimal value, since setting it very low might break the transform\n\n      const offsetW = calculateOffset(clickPoint, dir, e.ray.origin, e.ray.direction);\n      const offsetL = offsetW * offsetMultiplier;\n      const offsetH = fixed ? offsetL : offsetL / scale;\n      let upscale = Math.pow(2, offsetH * 0.2);\n\n      // @ts-ignore\n      if (e.shiftKey) {\n        upscale = Math.round(upscale * 10) / 10;\n      }\n      upscale = Math.max(upscale, min / scale0.current);\n      if (max !== undefined) {\n        upscale = Math.min(upscale, max / scale0.current);\n      }\n      scaleCur.current = scale0.current * upscale;\n      meshRef.current.position.set(0, position + offsetL, 0);\n      if (annotations) {\n        divRef.current.innerText = `${scaleCur.current.toFixed(2)}`;\n      }\n      scaleV.set(1, 1, 1);\n      scaleV.setComponent(axis, upscale);\n      scaleMatrix.makeScale(scaleV.x, scaleV.y, scaleV.z).premultiply(mPLG).multiply(mPLGInv);\n      onDrag(scaleMatrix);\n    }\n  }, [annotations, position, onDrag, isHovered, scaleLimits, axis]);\n  const onPointerUp = React.useCallback(e => {\n    if (annotations) {\n      divRef.current.style.display = 'none';\n    }\n    e.stopPropagation();\n    scale0.current = scaleCur.current;\n    clickInfo.current = null;\n    meshRef.current.position.set(0, position, 0);\n    onDragEnd();\n    camControls && (camControls.enabled = true);\n    // @ts-ignore - releasePointerCapture & PointerEvent#pointerId is not in the type definition\n    e.target.releasePointerCapture(e.pointerId);\n  }, [annotations, camControls, onDragEnd, position]);\n  const onPointerOut = React.useCallback(e => {\n    e.stopPropagation();\n    setIsHovered(false);\n  }, []);\n  const {\n    radius,\n    matrixL\n  } = React.useMemo(() => {\n    const radius = fixed ? lineWidth / scale * 1.8 : scale / 22.5;\n    const quaternion = new THREE.Quaternion().setFromUnitVectors(upV, direction.clone().normalize());\n    const matrixL = new THREE.Matrix4().makeRotationFromQuaternion(quaternion);\n    return {\n      radius,\n      matrixL\n    };\n  }, [direction, scale, lineWidth, fixed]);\n  const color = isHovered ? hoveredColor : axisColors[axis];\n  return /*#__PURE__*/React.createElement(\"group\", {\n    ref: objRef\n  }, /*#__PURE__*/React.createElement(\"group\", {\n    matrix: matrixL,\n    matrixAutoUpdate: false,\n    onPointerDown: onPointerDown,\n    onPointerMove: onPointerMove,\n    onPointerUp: onPointerUp,\n    onPointerOut: onPointerOut\n  }, annotations && /*#__PURE__*/React.createElement(Html, {\n    position: [0, position / 2, 0]\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'none',\n      background: '#151520',\n      color: 'white',\n      padding: '6px 8px',\n      borderRadius: 7,\n      whiteSpace: 'nowrap'\n    },\n    className: annotationsClass,\n    ref: divRef\n  })), /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: meshRef,\n    position: [0, position, 0],\n    renderOrder: 500,\n    userData: userData\n  }, /*#__PURE__*/React.createElement(\"sphereGeometry\", {\n    args: [radius, 12, 12]\n  }), /*#__PURE__*/React.createElement(\"meshBasicMaterial\", {\n    transparent: true,\n    depthTest: depthTest,\n    color: color,\n    opacity: opacity,\n    polygonOffset: true,\n    polygonOffsetFactor: -10\n  }))));\n};\n\nexport { ScalingSphere, calculateOffset };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,OAAO,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC7C,MAAM,OAAO,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC7C,MAAM,kBAAkB,CAAC,YAAY,QAAQ,UAAU;IACrD,MAAM,KAAK,OAAO,GAAG,CAAC;IACtB,MAAM,KAAK,OAAO,GAAG,CAAC,cAAc,OAAO,GAAG,CAAC;IAC/C,MAAM,KAAK,OAAO,GAAG,CAAC;IACtB,IAAI,OAAO,GAAG;QACZ,OAAO,CAAC,KAAK;IACf;IACA,KAAK,IAAI,CAAC,QAAQ,cAAc,CAAC,KAAK,IAAI,GAAG,CAAC;IAC9C,KAAK,IAAI,CAAC,QAAQ,cAAc,CAAC,KAAK,IAAI,GAAG,CAAC,UAAU,GAAG,CAAC;IAC5D,MAAM,SAAS,CAAC,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC;IAC1C,OAAO;AACT;AACA,MAAM,MAAM,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa,CAAC,GAAG,GAAG;AACnD,MAAM,SAAS,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC/C,MAAM,cAAc,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AACpD,MAAM,gBAAgB,CAAC,EACrB,SAAS,EACT,IAAI,EACL;IACC,MAAM,EACJ,WAAW,EACX,WAAW,EACX,gBAAgB,EAChB,SAAS,EACT,KAAK,EACL,SAAS,EACT,KAAK,EACL,UAAU,EACV,YAAY,EACZ,OAAO,EACP,WAAW,EACX,MAAM,EACN,SAAS,EACT,QAAQ,EACT,GAAG,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,iYAAA,CAAA,UAAO;IAC5B,MAAM,OAAO,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;wCAAE,CAAA,QAAS,MAAM,IAAI;;IACzC,MAAM,cAAc,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;+CAAE,CAAA,QAAS,MAAM,QAAQ;;IACpD,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC5B,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC5B,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC7B,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC5B,MAAM,WAAW,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC9B,MAAM,YAAY,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD,EAAE;IACjD,MAAM,WAAW,QAAQ,MAAM,MAAM;IACrC,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;oDAAE,CAAA;YACtC,IAAI,aAAa;gBACf,OAAO,OAAO,CAAC,SAAS,GAAG,GAAG,SAAS,OAAO,CAAC,OAAO,CAAC,IAAI;gBAC3D,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG;YACjC;YACA,EAAE,eAAe;YACjB,MAAM,WAAW,IAAI,sMAAA,CAAA,UAAa,GAAG,eAAe,CAAC,OAAO,OAAO,CAAC,WAAW;YAC/E,MAAM,aAAa,EAAE,KAAK,CAAC,KAAK;YAChC,MAAM,SAAS,IAAI,sMAAA,CAAA,UAAa,GAAG,qBAAqB,CAAC,OAAO,OAAO,CAAC,WAAW;YACnF,MAAM,MAAM,UAAU,KAAK,GAAG,YAAY,CAAC,UAAU,SAAS;YAC9D,MAAM,OAAO,OAAO,OAAO,CAAC,WAAW,CAAC,KAAK;YAC7C,MAAM,UAAU,KAAK,KAAK,GAAG,MAAM;YACnC,MAAM,mBAAmB,QAAQ,IAAI,CAAA,GAAA,8XAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,OAAO,CAAC,gBAAgB,CAAC,OAAO,OAAO,EAAE,MAAM,EAAE,QAAQ;YAC1H,UAAU,OAAO,GAAG;gBAClB;gBACA;gBACA;gBACA;gBACA;YACF;YACA,YAAY;gBACV,WAAW;gBACX;gBACA;gBACA,YAAY;oBAAC;iBAAI;YACnB;YACA,eAAe,CAAC,YAAY,OAAO,GAAG,KAAK;YAC3C,+DAA+D;YAC/D,EAAE,MAAM,CAAC,iBAAiB,CAAC,EAAE,SAAS;QACxC;mDAAG;QAAC;QAAa;QAAa;QAAW;QAAa;QAAM;QAAO;QAAO;KAAK;IAC/E,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;oDAAE,CAAA;YACtC,EAAE,eAAe;YACjB,IAAI,CAAC,WAAW,aAAa;YAC7B,IAAI,UAAU,OAAO,EAAE;gBACrB,MAAM,EACJ,UAAU,EACV,GAAG,EACH,IAAI,EACJ,OAAO,EACP,gBAAgB,EACjB,GAAG,UAAU,OAAO;gBACrB,MAAM,CAAC,KAAK,IAAI,GAAG,CAAC,eAAe,OAAO,KAAK,IAAI,WAAW,CAAC,KAAK,KAAK;oBAAC;oBAAM;iBAAU,EAAE,sFAAsF;gBAElL,MAAM,UAAU,gBAAgB,YAAY,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,SAAS;gBAC9E,MAAM,UAAU,UAAU;gBAC1B,MAAM,UAAU,QAAQ,UAAU,UAAU;gBAC5C,IAAI,UAAU,KAAK,GAAG,CAAC,GAAG,UAAU;gBAEpC,aAAa;gBACb,IAAI,EAAE,QAAQ,EAAE;oBACd,UAAU,KAAK,KAAK,CAAC,UAAU,MAAM;gBACvC;gBACA,UAAU,KAAK,GAAG,CAAC,SAAS,MAAM,OAAO,OAAO;gBAChD,IAAI,QAAQ,WAAW;oBACrB,UAAU,KAAK,GAAG,CAAC,SAAS,MAAM,OAAO,OAAO;gBAClD;gBACA,SAAS,OAAO,GAAG,OAAO,OAAO,GAAG;gBACpC,QAAQ,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,WAAW,SAAS;gBACpD,IAAI,aAAa;oBACf,OAAO,OAAO,CAAC,SAAS,GAAG,GAAG,SAAS,OAAO,CAAC,OAAO,CAAC,IAAI;gBAC7D;gBACA,OAAO,GAAG,CAAC,GAAG,GAAG;gBACjB,OAAO,YAAY,CAAC,MAAM;gBAC1B,YAAY,SAAS,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,WAAW,CAAC,MAAM,QAAQ,CAAC;gBAC/E,OAAO;YACT;QACF;mDAAG;QAAC;QAAa;QAAU;QAAQ;QAAW;QAAa;KAAK;IAChE,MAAM,cAAc,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;kDAAE,CAAA;YACpC,IAAI,aAAa;gBACf,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG;YACjC;YACA,EAAE,eAAe;YACjB,OAAO,OAAO,GAAG,SAAS,OAAO;YACjC,UAAU,OAAO,GAAG;YACpB,QAAQ,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU;YAC1C;YACA,eAAe,CAAC,YAAY,OAAO,GAAG,IAAI;YAC1C,4FAA4F;YAC5F,EAAE,MAAM,CAAC,qBAAqB,CAAC,EAAE,SAAS;QAC5C;iDAAG;QAAC;QAAa;QAAa;QAAW;KAAS;IAClD,MAAM,eAAe,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;mDAAE,CAAA;YACrC,EAAE,eAAe;YACjB,aAAa;QACf;kDAAG,EAAE;IACL,MAAM,EACJ,MAAM,EACN,OAAO,EACR,GAAG,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;iCAAE;YAChB,MAAM,SAAS,QAAQ,YAAY,QAAQ,MAAM,QAAQ;YACzD,MAAM,aAAa,IAAI,sMAAA,CAAA,aAAgB,GAAG,kBAAkB,CAAC,KAAK,UAAU,KAAK,GAAG,SAAS;YAC7F,MAAM,UAAU,IAAI,sMAAA,CAAA,UAAa,GAAG,0BAA0B,CAAC;YAC/D,OAAO;gBACL;gBACA;YACF;QACF;gCAAG;QAAC;QAAW;QAAO;QAAW;KAAM;IACvC,MAAM,QAAQ,YAAY,eAAe,UAAU,CAAC,KAAK;IACzD,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC/C,KAAK;IACP,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3C,QAAQ;QACR,kBAAkB;QAClB,eAAe;QACf,eAAe;QACf,aAAa;QACb,cAAc;IAChB,GAAG,eAAe,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,6WAAA,CAAA,OAAI,EAAE;QACvD,UAAU;YAAC;YAAG,WAAW;YAAG;SAAE;IAChC,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;QACzC,OAAO;YACL,SAAS;YACT,YAAY;YACZ,OAAO;YACP,SAAS;YACT,cAAc;YACd,YAAY;QACd;QACA,WAAW;QACX,KAAK;IACP,KAAK,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC5C,KAAK;QACL,UAAU;YAAC;YAAG;YAAU;SAAE;QAC1B,aAAa;QACb,UAAU;IACZ,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,kBAAkB;QACpD,MAAM;YAAC;YAAQ;YAAI;SAAG;IACxB,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,qBAAqB;QACxD,aAAa;QACb,WAAW;QACX,OAAO;QACP,SAAS;QACT,eAAe;QACf,qBAAqB,CAAC;IACxB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2846, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2852, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/pivotControls/index.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport { AxisArrow } from './AxisArrow.js';\nimport { AxisRotator } from './AxisRotator.js';\nimport { PlaneSlider } from './PlaneSlider.js';\nimport { ScalingSphere } from './ScalingSphere.js';\nimport { context } from './context.js';\nimport { calculateScaleFactor } from '../../core/calculateScaleFactor.js';\n\nconst mL0 = /* @__PURE__ */new THREE.Matrix4();\nconst mW0 = /* @__PURE__ */new THREE.Matrix4();\nconst mP = /* @__PURE__ */new THREE.Matrix4();\nconst mPInv = /* @__PURE__ */new THREE.Matrix4();\nconst mW = /* @__PURE__ */new THREE.Matrix4();\nconst mL = /* @__PURE__ */new THREE.Matrix4();\nconst mL0Inv = /* @__PURE__ */new THREE.Matrix4();\nconst mdL = /* @__PURE__ */new THREE.Matrix4();\nconst mG = /* @__PURE__ */new THREE.Matrix4();\nconst bb = /* @__PURE__ */new THREE.Box3();\nconst bbObj = /* @__PURE__ */new THREE.Box3();\nconst vCenter = /* @__PURE__ */new THREE.Vector3();\nconst vSize = /* @__PURE__ */new THREE.Vector3();\nconst vAnchorOffset = /* @__PURE__ */new THREE.Vector3();\nconst vPosition = /* @__PURE__ */new THREE.Vector3();\nconst vScale = /* @__PURE__ */new THREE.Vector3();\nconst xDir = /* @__PURE__ */new THREE.Vector3(1, 0, 0);\nconst yDir = /* @__PURE__ */new THREE.Vector3(0, 1, 0);\nconst zDir = /* @__PURE__ */new THREE.Vector3(0, 0, 1);\nconst PivotControls = /* @__PURE__ */React.forwardRef(({\n  enabled = true,\n  matrix,\n  onDragStart,\n  onDrag,\n  onDragEnd,\n  autoTransform = true,\n  anchor,\n  disableAxes = false,\n  disableSliders = false,\n  disableRotations = false,\n  disableScaling = false,\n  activeAxes = [true, true, true],\n  offset = [0, 0, 0],\n  rotation = [0, 0, 0],\n  scale = 1,\n  lineWidth = 4,\n  fixed = false,\n  translationLimits,\n  rotationLimits,\n  scaleLimits,\n  depthTest = true,\n  axisColors = ['#ff2060', '#20df80', '#2080ff'],\n  hoveredColor = '#ffff40',\n  annotations = false,\n  annotationsClass,\n  opacity = 1,\n  visible = true,\n  userData,\n  children,\n  ...props\n}, fRef) => {\n  const invalidate = useThree(state => state.invalidate);\n  const parentRef = React.useRef(null);\n  const ref = React.useRef(null);\n  const gizmoRef = React.useRef(null);\n  const childrenRef = React.useRef(null);\n  const translation = React.useRef([0, 0, 0]);\n  const cameraScale = React.useRef(new THREE.Vector3(1, 1, 1));\n  const gizmoScale = React.useRef(new THREE.Vector3(1, 1, 1));\n  React.useLayoutEffect(() => {\n    if (!anchor) return;\n    childrenRef.current.updateWorldMatrix(true, true);\n    mPInv.copy(childrenRef.current.matrixWorld).invert();\n    bb.makeEmpty();\n    childrenRef.current.traverse(obj => {\n      if (!obj.geometry) return;\n      if (!obj.geometry.boundingBox) obj.geometry.computeBoundingBox();\n      mL.copy(obj.matrixWorld).premultiply(mPInv);\n      bbObj.copy(obj.geometry.boundingBox);\n      bbObj.applyMatrix4(mL);\n      bb.union(bbObj);\n    });\n    vCenter.copy(bb.max).add(bb.min).multiplyScalar(0.5);\n    vSize.copy(bb.max).sub(bb.min).multiplyScalar(0.5);\n    vAnchorOffset.copy(vSize).multiply(new THREE.Vector3(...anchor)).add(vCenter);\n    vPosition.set(...offset).add(vAnchorOffset);\n    gizmoRef.current.position.copy(vPosition);\n    invalidate();\n  });\n  const config = React.useMemo(() => ({\n    onDragStart: props => {\n      mL0.copy(ref.current.matrix);\n      mW0.copy(ref.current.matrixWorld);\n      onDragStart && onDragStart(props);\n      invalidate();\n    },\n    onDrag: mdW => {\n      mP.copy(parentRef.current.matrixWorld);\n      mPInv.copy(mP).invert();\n      // After applying the delta\n      mW.copy(mW0).premultiply(mdW);\n      mL.copy(mW).premultiply(mPInv);\n      mL0Inv.copy(mL0).invert();\n      mdL.copy(mL).multiply(mL0Inv);\n      if (autoTransform) {\n        ref.current.matrix.copy(mL);\n      }\n      onDrag && onDrag(mL, mdL, mW, mdW);\n      invalidate();\n    },\n    onDragEnd: () => {\n      if (onDragEnd) onDragEnd();\n      invalidate();\n    },\n    translation,\n    translationLimits,\n    rotationLimits,\n    axisColors,\n    hoveredColor,\n    opacity,\n    scale,\n    lineWidth,\n    fixed,\n    depthTest,\n    userData,\n    annotations,\n    annotationsClass\n  }), [onDragStart, onDrag, onDragEnd, translation, translationLimits, rotationLimits, scaleLimits, depthTest, scale, lineWidth, fixed, ...axisColors, hoveredColor, opacity, userData, autoTransform, annotations, annotationsClass]);\n  const vec = new THREE.Vector3();\n  useFrame(state => {\n    if (fixed) {\n      const sf = calculateScaleFactor(gizmoRef.current.getWorldPosition(vec), scale, state.camera, state.size);\n      cameraScale.current.setScalar(sf);\n    }\n    if (matrix && matrix instanceof THREE.Matrix4) {\n      ref.current.matrix = matrix;\n    }\n    // Update gizmo scale in accordance with matrix changes\n    // Without this, there might be noticable turbulences if scaling happens fast enough\n    ref.current.updateWorldMatrix(true, true);\n    mG.makeRotationFromEuler(gizmoRef.current.rotation).setPosition(gizmoRef.current.position).premultiply(ref.current.matrixWorld);\n    gizmoScale.current.setFromMatrixScale(mG);\n    vScale.copy(cameraScale.current).divide(gizmoScale.current);\n    if (Math.abs(gizmoRef.current.scale.x - vScale.x) > 1e-4 || Math.abs(gizmoRef.current.scale.y - vScale.y) > 1e-4 || Math.abs(gizmoRef.current.scale.z - vScale.z) > 1e-4) {\n      gizmoRef.current.scale.copy(vScale);\n      state.invalidate();\n    }\n  });\n  React.useImperativeHandle(fRef, () => ref.current, []);\n  return /*#__PURE__*/React.createElement(context.Provider, {\n    value: config\n  }, /*#__PURE__*/React.createElement(\"group\", {\n    ref: parentRef\n  }, /*#__PURE__*/React.createElement(\"group\", _extends({\n    ref: ref,\n    matrix: matrix,\n    matrixAutoUpdate: false\n  }, props), /*#__PURE__*/React.createElement(\"group\", {\n    visible: visible,\n    ref: gizmoRef,\n    position: offset,\n    rotation: rotation\n  }, enabled && /*#__PURE__*/React.createElement(React.Fragment, null, !disableAxes && activeAxes[0] && /*#__PURE__*/React.createElement(AxisArrow, {\n    axis: 0,\n    direction: xDir\n  }), !disableAxes && activeAxes[1] && /*#__PURE__*/React.createElement(AxisArrow, {\n    axis: 1,\n    direction: yDir\n  }), !disableAxes && activeAxes[2] && /*#__PURE__*/React.createElement(AxisArrow, {\n    axis: 2,\n    direction: zDir\n  }), !disableSliders && activeAxes[0] && activeAxes[1] && /*#__PURE__*/React.createElement(PlaneSlider, {\n    axis: 2,\n    dir1: xDir,\n    dir2: yDir\n  }), !disableSliders && activeAxes[0] && activeAxes[2] && /*#__PURE__*/React.createElement(PlaneSlider, {\n    axis: 1,\n    dir1: zDir,\n    dir2: xDir\n  }), !disableSliders && activeAxes[2] && activeAxes[1] && /*#__PURE__*/React.createElement(PlaneSlider, {\n    axis: 0,\n    dir1: yDir,\n    dir2: zDir\n  }), !disableRotations && activeAxes[0] && activeAxes[1] && /*#__PURE__*/React.createElement(AxisRotator, {\n    axis: 2,\n    dir1: xDir,\n    dir2: yDir\n  }), !disableRotations && activeAxes[0] && activeAxes[2] && /*#__PURE__*/React.createElement(AxisRotator, {\n    axis: 1,\n    dir1: zDir,\n    dir2: xDir\n  }), !disableRotations && activeAxes[2] && activeAxes[1] && /*#__PURE__*/React.createElement(AxisRotator, {\n    axis: 0,\n    dir1: yDir,\n    dir2: zDir\n  }), !disableScaling && activeAxes[0] && /*#__PURE__*/React.createElement(ScalingSphere, {\n    axis: 0,\n    direction: xDir\n  }), !disableScaling && activeAxes[1] && /*#__PURE__*/React.createElement(ScalingSphere, {\n    axis: 1,\n    direction: yDir\n  }), !disableScaling && activeAxes[2] && /*#__PURE__*/React.createElement(ScalingSphere, {\n    axis: 2,\n    direction: zDir\n  }))), /*#__PURE__*/React.createElement(\"group\", {\n    ref: childrenRef\n  }, children))));\n});\n\nexport { PivotControls };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEA,MAAM,MAAM,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC5C,MAAM,MAAM,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC5C,MAAM,KAAK,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC3C,MAAM,QAAQ,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC9C,MAAM,KAAK,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC3C,MAAM,KAAK,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC3C,MAAM,SAAS,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC/C,MAAM,MAAM,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC5C,MAAM,KAAK,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC3C,MAAM,KAAK,aAAa,GAAE,IAAI,sMAAA,CAAA,OAAU;AACxC,MAAM,QAAQ,aAAa,GAAE,IAAI,sMAAA,CAAA,OAAU;AAC3C,MAAM,UAAU,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAChD,MAAM,QAAQ,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC9C,MAAM,gBAAgB,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AACtD,MAAM,YAAY,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAClD,MAAM,SAAS,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa;AAC/C,MAAM,OAAO,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa,CAAC,GAAG,GAAG;AACpD,MAAM,OAAO,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa,CAAC,GAAG,GAAG;AACpD,MAAM,OAAO,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa,CAAC,GAAG,GAAG;AACpD,MAAM,gBAAgB,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EACrD,UAAU,IAAI,EACd,MAAM,EACN,WAAW,EACX,MAAM,EACN,SAAS,EACT,gBAAgB,IAAI,EACpB,MAAM,EACN,cAAc,KAAK,EACnB,iBAAiB,KAAK,EACtB,mBAAmB,KAAK,EACxB,iBAAiB,KAAK,EACtB,aAAa;IAAC;IAAM;IAAM;CAAK,EAC/B,SAAS;IAAC;IAAG;IAAG;CAAE,EAClB,WAAW;IAAC;IAAG;IAAG;CAAE,EACpB,QAAQ,CAAC,EACT,YAAY,CAAC,EACb,QAAQ,KAAK,EACb,iBAAiB,EACjB,cAAc,EACd,WAAW,EACX,YAAY,IAAI,EAChB,aAAa;IAAC;IAAW;IAAW;CAAU,EAC9C,eAAe,SAAS,EACxB,cAAc,KAAK,EACnB,gBAAgB,EAChB,UAAU,CAAC,EACX,UAAU,IAAI,EACd,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;8CAAE,CAAA,QAAS,MAAM,UAAU;;IACrD,MAAM,YAAY,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC/B,MAAM,MAAM,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IACzB,MAAM,WAAW,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC9B,MAAM,cAAc,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IACjC,MAAM,cAAc,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;QAAC;QAAG;QAAG;KAAE;IAC1C,MAAM,cAAc,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE,IAAI,sMAAA,CAAA,UAAa,CAAC,GAAG,GAAG;IACzD,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE,IAAI,sMAAA,CAAA,UAAa,CAAC,GAAG,GAAG;IACxD,CAAA,GAAA,4RAAA,CAAA,kBAAqB,AAAD;yCAAE;YACpB,IAAI,CAAC,QAAQ;YACb,YAAY,OAAO,CAAC,iBAAiB,CAAC,MAAM;YAC5C,MAAM,IAAI,CAAC,YAAY,OAAO,CAAC,WAAW,EAAE,MAAM;YAClD,GAAG,SAAS;YACZ,YAAY,OAAO,CAAC,QAAQ;iDAAC,CAAA;oBAC3B,IAAI,CAAC,IAAI,QAAQ,EAAE;oBACnB,IAAI,CAAC,IAAI,QAAQ,CAAC,WAAW,EAAE,IAAI,QAAQ,CAAC,kBAAkB;oBAC9D,GAAG,IAAI,CAAC,IAAI,WAAW,EAAE,WAAW,CAAC;oBACrC,MAAM,IAAI,CAAC,IAAI,QAAQ,CAAC,WAAW;oBACnC,MAAM,YAAY,CAAC;oBACnB,GAAG,KAAK,CAAC;gBACX;;YACA,QAAQ,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,cAAc,CAAC;YAChD,MAAM,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,cAAc,CAAC;YAC9C,cAAc,IAAI,CAAC,OAAO,QAAQ,CAAC,IAAI,sMAAA,CAAA,UAAa,IAAI,SAAS,GAAG,CAAC;YACrE,UAAU,GAAG,IAAI,QAAQ,GAAG,CAAC;YAC7B,SAAS,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;YAC/B;QACF;;IACA,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;yCAAE,IAAM,CAAC;gBAClC,WAAW;qDAAE,CAAA;wBACX,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM;wBAC3B,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC,WAAW;wBAChC,eAAe,YAAY;wBAC3B;oBACF;;gBACA,MAAM;qDAAE,CAAA;wBACN,GAAG,IAAI,CAAC,UAAU,OAAO,CAAC,WAAW;wBACrC,MAAM,IAAI,CAAC,IAAI,MAAM;wBACrB,2BAA2B;wBAC3B,GAAG,IAAI,CAAC,KAAK,WAAW,CAAC;wBACzB,GAAG,IAAI,CAAC,IAAI,WAAW,CAAC;wBACxB,OAAO,IAAI,CAAC,KAAK,MAAM;wBACvB,IAAI,IAAI,CAAC,IAAI,QAAQ,CAAC;wBACtB,IAAI,eAAe;4BACjB,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;wBAC1B;wBACA,UAAU,OAAO,IAAI,KAAK,IAAI;wBAC9B;oBACF;;gBACA,SAAS;qDAAE;wBACT,IAAI,WAAW;wBACf;oBACF;;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;wCAAG;QAAC;QAAa;QAAQ;QAAW;QAAa;QAAmB;QAAgB;QAAa;QAAW;QAAO;QAAW;WAAU;QAAY;QAAc;QAAS;QAAU;QAAe;QAAa;KAAiB;IACnO,MAAM,MAAM,IAAI,sMAAA,CAAA,UAAa;IAC7B,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;kCAAE,CAAA;YACP,IAAI,OAAO;gBACT,MAAM,KAAK,CAAA,GAAA,8XAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,OAAO,CAAC,gBAAgB,CAAC,MAAM,OAAO,MAAM,MAAM,EAAE,MAAM,IAAI;gBACvG,YAAY,OAAO,CAAC,SAAS,CAAC;YAChC;YACA,IAAI,UAAU,kBAAkB,sMAAA,CAAA,UAAa,EAAE;gBAC7C,IAAI,OAAO,CAAC,MAAM,GAAG;YACvB;YACA,uDAAuD;YACvD,oFAAoF;YACpF,IAAI,OAAO,CAAC,iBAAiB,CAAC,MAAM;YACpC,GAAG,qBAAqB,CAAC,SAAS,OAAO,CAAC,QAAQ,EAAE,WAAW,CAAC,SAAS,OAAO,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,OAAO,CAAC,WAAW;YAC9H,WAAW,OAAO,CAAC,kBAAkB,CAAC;YACtC,OAAO,IAAI,CAAC,YAAY,OAAO,EAAE,MAAM,CAAC,WAAW,OAAO;YAC1D,IAAI,KAAK,GAAG,CAAC,SAAS,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,QAAQ,KAAK,GAAG,CAAC,SAAS,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,QAAQ,KAAK,GAAG,CAAC,SAAS,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,MAAM;gBACxK,SAAS,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC5B,MAAM,UAAU;YAClB;QACF;;IACA,CAAA,GAAA,4RAAA,CAAA,sBAAyB,AAAD,EAAE;6CAAM,IAAM,IAAI,OAAO;4CAAE,EAAE;IACrD,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,iYAAA,CAAA,UAAO,CAAC,QAAQ,EAAE;QACxD,OAAO;IACT,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3C,KAAK;IACP,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;QACpD,KAAK;QACL,QAAQ;QACR,kBAAkB;IACpB,GAAG,QAAQ,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QACnD,SAAS;QACT,KAAK;QACL,UAAU;QACV,UAAU;IACZ,GAAG,WAAW,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,4RAAA,CAAA,WAAc,EAAE,MAAM,CAAC,eAAe,UAAU,CAAC,EAAE,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,mYAAA,CAAA,YAAS,EAAE;QAChJ,MAAM;QACN,WAAW;IACb,IAAI,CAAC,eAAe,UAAU,CAAC,EAAE,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,mYAAA,CAAA,YAAS,EAAE;QAC/E,MAAM;QACN,WAAW;IACb,IAAI,CAAC,eAAe,UAAU,CAAC,EAAE,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,mYAAA,CAAA,YAAS,EAAE;QAC/E,MAAM;QACN,WAAW;IACb,IAAI,CAAC,kBAAkB,UAAU,CAAC,EAAE,IAAI,UAAU,CAAC,EAAE,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,qYAAA,CAAA,cAAW,EAAE;QACrG,MAAM;QACN,MAAM;QACN,MAAM;IACR,IAAI,CAAC,kBAAkB,UAAU,CAAC,EAAE,IAAI,UAAU,CAAC,EAAE,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,qYAAA,CAAA,cAAW,EAAE;QACrG,MAAM;QACN,MAAM;QACN,MAAM;IACR,IAAI,CAAC,kBAAkB,UAAU,CAAC,EAAE,IAAI,UAAU,CAAC,EAAE,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,qYAAA,CAAA,cAAW,EAAE;QACrG,MAAM;QACN,MAAM;QACN,MAAM;IACR,IAAI,CAAC,oBAAoB,UAAU,CAAC,EAAE,IAAI,UAAU,CAAC,EAAE,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,qYAAA,CAAA,cAAW,EAAE;QACvG,MAAM;QACN,MAAM;QACN,MAAM;IACR,IAAI,CAAC,oBAAoB,UAAU,CAAC,EAAE,IAAI,UAAU,CAAC,EAAE,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,qYAAA,CAAA,cAAW,EAAE;QACvG,MAAM;QACN,MAAM;QACN,MAAM;IACR,IAAI,CAAC,oBAAoB,UAAU,CAAC,EAAE,IAAI,UAAU,CAAC,EAAE,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,qYAAA,CAAA,cAAW,EAAE;QACvG,MAAM;QACN,MAAM;QACN,MAAM;IACR,IAAI,CAAC,kBAAkB,UAAU,CAAC,EAAE,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,uYAAA,CAAA,gBAAa,EAAE;QACtF,MAAM;QACN,WAAW;IACb,IAAI,CAAC,kBAAkB,UAAU,CAAC,EAAE,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,uYAAA,CAAA,gBAAa,EAAE;QACtF,MAAM;QACN,WAAW;IACb,IAAI,CAAC,kBAAkB,UAAU,CAAC,EAAE,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,uYAAA,CAAA,gBAAa,EAAE;QACtF,MAAM;QACN,WAAW;IACb,MAAM,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC9C,KAAK;IACP,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3101, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3107, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/ScreenVideoTexture.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { forwardRef, useEffect } from 'react';\nimport { suspend, clear } from 'suspend-react';\nimport { VideoTexture } from '../core/VideoTexture.js';\n\n/**\n * Create a video texture from [`getDisplayMedia`](https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getDisplayMedia)\n */\nconst ScreenVideoTexture = /* @__PURE__ */forwardRef(({\n  options = {\n    video: true\n  },\n  ...props\n}, fref) => {\n  const mediaStream = suspend(() => navigator.mediaDevices.getDisplayMedia(options), []);\n  useEffect(() => {\n    return () => {\n      mediaStream == null || mediaStream.getTracks().forEach(track => track.stop());\n      clear([]);\n    };\n  }, [mediaStream]);\n  return /*#__PURE__*/React.createElement(VideoTexture, _extends({\n    ref: fref\n  }, props, {\n    src: mediaStream\n  }));\n});\n\nexport { ScreenVideoTexture };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;AACA;;;;;;AAEA;;CAEC,GACD,MAAM,qBAAqB,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EACpD,UAAU;IACR,OAAO;AACT,CAAC,EACD,GAAG,OACJ,EAAE;IACD,MAAM,cAAc,CAAA,GAAA,+NAAA,CAAA,UAAO,AAAD,EAAE,IAAM,UAAU,YAAY,CAAC,eAAe,CAAC,UAAU,EAAE;IACrF,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;wCAAE;YACR;gDAAO;oBACL,eAAe,QAAQ,YAAY,SAAS,GAAG,OAAO;wDAAC,CAAA,QAAS,MAAM,IAAI;;oBAC1E,CAAA,GAAA,+NAAA,CAAA,QAAK,AAAD,EAAE,EAAE;gBACV;;QACF;uCAAG;QAAC;KAAY;IAChB,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,sXAAA,CAAA,eAAY,EAAE,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;QAC7D,KAAK;IACP,GAAG,OAAO;QACR,KAAK;IACP;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3146, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3152, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/WebcamVideoTexture.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { forwardRef, useEffect } from 'react';\nimport { suspend, clear } from 'suspend-react';\nimport { VideoTexture } from '../core/VideoTexture.js';\n\n/**\n * Create a video texture from [`getUserMedia`](https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia)\n */\nconst WebcamVideoTexture = /* @__PURE__ */forwardRef(({\n  constraints = {\n    audio: false,\n    video: {\n      facingMode: 'user'\n    }\n  },\n  ...props\n}, fref) => {\n  const mediaStream = suspend(() => navigator.mediaDevices.getUserMedia(constraints), []);\n  useEffect(() => {\n    return () => {\n      mediaStream == null || mediaStream.getTracks().forEach(track => track.stop());\n      clear([]);\n    };\n  }, [mediaStream]);\n  return /*#__PURE__*/React.createElement(VideoTexture, _extends({\n    ref: fref\n  }, props, {\n    src: mediaStream\n  }));\n});\n\nexport { WebcamVideoTexture };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;AACA;;;;;;AAEA;;CAEC,GACD,MAAM,qBAAqB,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EACpD,cAAc;IACZ,OAAO;IACP,OAAO;QACL,YAAY;IACd;AACF,CAAC,EACD,GAAG,OACJ,EAAE;IACD,MAAM,cAAc,CAAA,GAAA,+NAAA,CAAA,UAAO,AAAD,EAAE,IAAM,UAAU,YAAY,CAAC,YAAY,CAAC,cAAc,EAAE;IACtF,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;wCAAE;YACR;gDAAO;oBACL,eAAe,QAAQ,YAAY,SAAS,GAAG,OAAO;wDAAC,CAAA,QAAS,MAAM,IAAI;;oBAC1E,CAAA,GAAA,+NAAA,CAAA,QAAK,AAAD,EAAE,EAAE;gBACV;;QACF;uCAAG;QAAC;KAAY;IAChB,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,sXAAA,CAAA,eAAY,EAAE,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;QAC7D,KAAK;IACP,GAAG,OAAO;QACR,KAAK;IACP;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3194, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3200, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/Facemesh.js"], "sourcesContent": ["import * as React from 'react';\nimport * as THREE from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { Line } from '../core/Line.js';\n\n/* eslint react-hooks/exhaustive-deps: 1 */\nconst defaultLookAt = /* @__PURE__ */new THREE.Vector3(0, 0, -1);\nconst normal = /* @__PURE__ */function () {\n  const a = new THREE.Vector3();\n  const b = new THREE.Vector3();\n  const c = new THREE.Vector3();\n  const ab = new THREE.Vector3();\n  const ac = new THREE.Vector3();\n  return function (v1, v2, v3, v) {\n    a.copy(v1);\n    b.copy(v2);\n    c.copy(v3);\n    ab.copy(b).sub(a);\n    ac.copy(c).sub(a);\n    return v.crossVectors(ac, ab).normalize();\n  };\n}();\nfunction mean(v1, v2) {\n  return v1.clone().add(v2).multiplyScalar(0.5);\n}\nconst Facemesh = /* @__PURE__ */React.forwardRef(({\n  points = FacemeshDatas.SAMPLE_FACELANDMARKER_RESULT.faceLandmarks[0],\n  face,\n  facialTransformationMatrix,\n  faceBlendshapes,\n  offset,\n  offsetScalar = 80,\n  width,\n  height,\n  depth = 1,\n  verticalTri = [159, 386, 152],\n  origin,\n  eyes = true,\n  eyesAsOrigin = false,\n  debug = false,\n  children,\n  ...props\n}, fref) => {\n  var _meshRef$current3;\n  if (face) {\n    points = face.keypoints;\n    console.warn('Facemesh `face` prop is deprecated: use `points` instead');\n  }\n  const offsetRef = React.useRef(null);\n  const scaleRef = React.useRef(null);\n  const originRef = React.useRef(null);\n  const outerRef = React.useRef(null);\n  const meshRef = React.useRef(null);\n  const eyeRightRef = React.useRef(null);\n  const eyeLeftRef = React.useRef(null);\n  const [sightDir] = React.useState(() => new THREE.Vector3());\n  const [transform] = React.useState(() => new THREE.Object3D());\n  const [sightDirQuaternion] = React.useState(() => new THREE.Quaternion());\n  const [_origin] = React.useState(() => new THREE.Vector3());\n  const {\n    invalidate\n  } = useThree();\n  React.useEffect(() => {\n    var _meshRef$current;\n    (_meshRef$current = meshRef.current) == null || _meshRef$current.geometry.setIndex(FacemeshDatas.TRIANGULATION);\n  }, []);\n  const [bboxSize] = React.useState(() => new THREE.Vector3());\n  React.useEffect(() => {\n    var _meshRef$current2, _outerRef$current;\n    const faceGeometry = (_meshRef$current2 = meshRef.current) == null ? void 0 : _meshRef$current2.geometry;\n    if (!faceGeometry) return;\n    faceGeometry.setFromPoints(points);\n    faceGeometry.setDrawRange(0, FacemeshDatas.TRIANGULATION.length);\n\n    //\n    // A. compute sightDir vector\n    //\n    //  - either from `facialTransformationMatrix` if available\n    //  - or from `verticalTri`\n    //\n\n    if (facialTransformationMatrix) {\n      // from facialTransformationMatrix\n      transform.matrix.fromArray(facialTransformationMatrix.data);\n      transform.matrix.decompose(transform.position, transform.quaternion, transform.scale);\n\n      // Rotation: y and z axes are inverted\n      transform.rotation.y *= -1;\n      transform.rotation.z *= -1;\n      sightDirQuaternion.setFromEuler(transform.rotation);\n\n      // Offset: y and z axes are inverted\n      if (offset) {\n        var _offsetRef$current;\n        transform.position.y *= -1;\n        transform.position.z *= -1;\n        (_offsetRef$current = offsetRef.current) == null || _offsetRef$current.position.copy(transform.position.divideScalar(offsetScalar));\n      } else {\n        var _offsetRef$current2;\n        (_offsetRef$current2 = offsetRef.current) == null || _offsetRef$current2.position.set(0, 0, 0); // reset\n      }\n    } else {\n      // normal to verticalTri\n      normal(points[verticalTri[0]], points[verticalTri[1]], points[verticalTri[2]], sightDir);\n      sightDirQuaternion.setFromUnitVectors(defaultLookAt, sightDir);\n    }\n    const sightDirQuaternionInverse = sightDirQuaternion.clone().invert();\n\n    //\n    // B. geometry (straightened)\n    //\n\n    // 1. center (before rotate back)\n    faceGeometry.computeBoundingBox();\n    if (debug) invalidate(); // invalidate to force re-render for box3Helper (after .computeBoundingBox())\n    faceGeometry.center();\n\n    // 2. rotate back + rotate outerRef (once 1.)\n    faceGeometry.applyQuaternion(sightDirQuaternionInverse);\n    (_outerRef$current = outerRef.current) == null || _outerRef$current.setRotationFromQuaternion(sightDirQuaternion);\n\n    // 3. 👀 eyes\n    if (eyes) {\n      if (!faceBlendshapes) {\n        console.warn('Facemesh `eyes` option only works if `faceBlendshapes` is provided: skipping.');\n      } else {\n        if (eyeRightRef.current && eyeLeftRef.current && originRef.current) {\n          if (eyesAsOrigin) {\n            // compute the middle of the 2 eyes as the `origin`\n            const eyeRightSphere = eyeRightRef.current._computeSphere(faceGeometry);\n            const eyeLeftSphere = eyeLeftRef.current._computeSphere(faceGeometry);\n            const eyesCenter = mean(eyeRightSphere.center, eyeLeftSphere.center);\n            origin = eyesCenter.negate(); // eslint-disable-line react-hooks/exhaustive-deps\n\n            eyeRightRef.current._update(faceGeometry, faceBlendshapes, eyeRightSphere);\n            eyeLeftRef.current._update(faceGeometry, faceBlendshapes, eyeLeftSphere);\n          } else {\n            eyeRightRef.current._update(faceGeometry, faceBlendshapes);\n            eyeLeftRef.current._update(faceGeometry, faceBlendshapes);\n          }\n        }\n      }\n    }\n\n    // 3. origin\n    if (originRef.current) {\n      if (origin !== undefined) {\n        if (typeof origin === 'number') {\n          const position = faceGeometry.getAttribute('position');\n          _origin.set(-position.getX(origin), -position.getY(origin), -position.getZ(origin));\n        } else if (origin.isVector3) {\n          _origin.copy(origin);\n        }\n      } else {\n        _origin.setScalar(0);\n      }\n      originRef.current.position.copy(_origin);\n    }\n\n    // 4. re-scale\n    if (scaleRef.current) {\n      let scale = 1;\n      if (width || height || depth) {\n        faceGeometry.boundingBox.getSize(bboxSize);\n        if (width) scale = width / bboxSize.x; // fit in width\n        if (height) scale = height / bboxSize.y; // fit in height\n        if (depth) scale = depth / bboxSize.z; // fit in depth\n      }\n      scaleRef.current.scale.setScalar(scale !== 1 ? scale : 1);\n    }\n    faceGeometry.computeVertexNormals();\n    faceGeometry.attributes.position.needsUpdate = true;\n  }, [points, facialTransformationMatrix, faceBlendshapes, transform, offset, offsetScalar, width, height, depth, verticalTri, origin, eyes, debug, invalidate, sightDir, sightDirQuaternion, bboxSize, _origin]);\n\n  //\n  // API\n  //\n\n  const api = React.useMemo(() => ({\n    outerRef,\n    meshRef,\n    eyeRightRef,\n    eyeLeftRef\n  }), []);\n  React.useImperativeHandle(fref, () => api, [api]);\n  const [meshBboxSize] = React.useState(() => new THREE.Vector3());\n  const bbox = (_meshRef$current3 = meshRef.current) == null ? void 0 : _meshRef$current3.geometry.boundingBox;\n  const one = (bbox == null ? void 0 : bbox.getSize(meshBboxSize).z) || 1;\n  return /*#__PURE__*/React.createElement(\"group\", props, /*#__PURE__*/React.createElement(\"group\", {\n    ref: offsetRef\n  }, /*#__PURE__*/React.createElement(\"group\", {\n    ref: outerRef\n  }, /*#__PURE__*/React.createElement(\"group\", {\n    ref: scaleRef\n  }, debug ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"axesHelper\", {\n    args: [one]\n  }), /*#__PURE__*/React.createElement(Line, {\n    points: [[0, 0, 0], [0, 0, -one]],\n    color: 0x00ffff\n  })) : null, /*#__PURE__*/React.createElement(\"group\", {\n    ref: originRef\n  }, eyes && faceBlendshapes && /*#__PURE__*/React.createElement(\"group\", {\n    name: \"eyes\"\n  }, /*#__PURE__*/React.createElement(FacemeshEye, {\n    side: \"left\",\n    ref: eyeRightRef,\n    debug: debug\n  }), /*#__PURE__*/React.createElement(FacemeshEye, {\n    side: \"right\",\n    ref: eyeLeftRef,\n    debug: debug\n  })), /*#__PURE__*/React.createElement(\"mesh\", {\n    ref: meshRef,\n    name: \"face\"\n  }, children, debug ? /*#__PURE__*/React.createElement(React.Fragment, null, bbox && /*#__PURE__*/React.createElement(\"box3Helper\", {\n    args: [bbox]\n  })) : null))))));\n});\n\n//\n// 👁️ FacemeshEye\n//\n\nconst FacemeshEyeDefaults = {\n  contourLandmarks: {\n    right: [33, 133, 159, 145, 153],\n    left: [263, 362, 386, 374, 380]\n  },\n  blendshapes: {\n    right: [14, 16, 18, 12],\n    // lookIn,lookOut, lookUp,lookDown\n    left: [13, 15, 17, 11] // lookIn,lookOut, lookUp,lookDown\n  },\n  color: {\n    right: 'red',\n    left: '#00ff00'\n  },\n  fov: {\n    horizontal: 100,\n    vertical: 90\n  }\n};\nconst FacemeshEye = /* @__PURE__ */React.forwardRef(({\n  side,\n  debug = true\n}, fref) => {\n  const eyeMeshRef = React.useRef(null);\n  const irisDirRef = React.useRef(null);\n\n  //\n  // _computeSphere()\n  //\n  // Compute eye's sphere .position and .radius\n  //\n\n  const [sphere] = React.useState(() => new THREE.Sphere());\n  const _computeSphere = React.useCallback(faceGeometry => {\n    const position = faceGeometry.getAttribute('position');\n\n    // get some eye contour landmarks points (from geometry)\n    const eyeContourLandmarks = FacemeshEyeDefaults.contourLandmarks[side];\n    const eyeContourPoints = eyeContourLandmarks.map(i => new THREE.Vector3(position.getX(i), position.getY(i), position.getZ(i))); // prettier-ignore\n\n    // compute center (centroid from eyeContourPoints)\n    sphere.center.set(0, 0, 0);\n    eyeContourPoints.forEach(v => sphere.center.add(v));\n    sphere.center.divideScalar(eyeContourPoints.length);\n\n    // radius (eye half-width)\n    sphere.radius = eyeContourPoints[0].sub(eyeContourPoints[1]).length() / 2;\n    return sphere;\n  }, [sphere, side]);\n\n  //\n  // _update()\n  //\n  // Update:\n  //   - A. eye's mesh (according to sphere)\n  //   - B. iris direction (according to \"look*\" blendshapes)\n  //\n\n  const [rotation] = React.useState(() => new THREE.Euler());\n  const _update = React.useCallback((faceGeometry, faceBlendshapes, sphere) => {\n    // A.\n    if (eyeMeshRef.current) {\n      var _sphere;\n      (_sphere = sphere) !== null && _sphere !== void 0 ? _sphere : sphere = _computeSphere(faceGeometry); // compute sphere dims (if not passed)\n      eyeMeshRef.current.position.copy(sphere.center);\n      eyeMeshRef.current.scale.setScalar(sphere.radius);\n    }\n\n    // B.\n    if (faceBlendshapes && irisDirRef.current) {\n      const blendshapes = FacemeshEyeDefaults.blendshapes[side];\n      const lookIn = faceBlendshapes.categories[blendshapes[0]].score;\n      const lookOut = faceBlendshapes.categories[blendshapes[1]].score;\n      const lookUp = faceBlendshapes.categories[blendshapes[2]].score;\n      const lookDown = faceBlendshapes.categories[blendshapes[3]].score;\n      const hfov = FacemeshEyeDefaults.fov.horizontal * THREE.MathUtils.DEG2RAD;\n      const vfov = FacemeshEyeDefaults.fov.vertical * THREE.MathUtils.DEG2RAD;\n      const rx = hfov * 0.5 * (lookDown - lookUp);\n      const ry = vfov * 0.5 * (lookIn - lookOut) * (side === 'left' ? 1 : -1);\n      rotation.set(rx, ry, 0);\n      irisDirRef.current.setRotationFromEuler(rotation);\n    }\n  }, [_computeSphere, side, rotation]);\n\n  //\n  // API\n  //\n\n  const api = React.useMemo(() => ({\n    eyeMeshRef: eyeMeshRef,\n    irisDirRef: irisDirRef,\n    _computeSphere,\n    _update\n  }), [_computeSphere, _update]);\n  React.useImperativeHandle(fref, () => api, [api]);\n  const color = FacemeshEyeDefaults.color[side];\n  return /*#__PURE__*/React.createElement(\"group\", null, /*#__PURE__*/React.createElement(\"group\", {\n    ref: eyeMeshRef\n  }, debug && /*#__PURE__*/React.createElement(\"axesHelper\", null), /*#__PURE__*/React.createElement(\"group\", {\n    ref: irisDirRef\n  }, /*#__PURE__*/React.createElement(React.Fragment, null, debug && /*#__PURE__*/React.createElement(Line, {\n    points: [[0, 0, 0], [0, 0, -2]],\n    lineWidth: 1,\n    color: color\n  })))));\n});\n\n//\n// Sample datas\n//\n\nconst FacemeshDatas = {\n  // Extracted from: https://github.com/tensorflow/tfjs-models/blob/a8f500809f5afe38feea27870c77e7ba03a6ece4/face-landmarks-detection/demos/shared/triangulation.js\n  // prettier-ignore\n  TRIANGULATION: [127, 34, 139, 11, 0, 37, 232, 231, 120, 72, 37, 39, 128, 121, 47, 232, 121, 128, 104, 69, 67, 175, 171, 148, 157, 154, 155, 118, 50, 101, 73, 39, 40, 9, 151, 108, 48, 115, 131, 194, 204, 211, 74, 40, 185, 80, 42, 183, 40, 92, 186, 230, 229, 118, 202, 212, 214, 83, 18, 17, 76, 61, 146, 160, 29, 30, 56, 157, 173, 106, 204, 194, 135, 214, 192, 203, 165, 98, 21, 71, 68, 51, 45, 4, 144, 24, 23, 77, 146, 91, 205, 50, 187, 201, 200, 18, 91, 106, 182, 90, 91, 181, 85, 84, 17, 206, 203, 36, 148, 171, 140, 92, 40, 39, 193, 189, 244, 159, 158, 28, 247, 246, 161, 236, 3, 196, 54, 68, 104, 193, 168, 8, 117, 228, 31, 189, 193, 55, 98, 97, 99, 126, 47, 100, 166, 79, 218, 155, 154, 26, 209, 49, 131, 135, 136, 150, 47, 126, 217, 223, 52, 53, 45, 51, 134, 211, 170, 140, 67, 69, 108, 43, 106, 91, 230, 119, 120, 226, 130, 247, 63, 53, 52, 238, 20, 242, 46, 70, 156, 78, 62, 96, 46, 53, 63, 143, 34, 227, 173, 155, 133, 123, 117, 111, 44, 125, 19, 236, 134, 51, 216, 206, 205, 154, 153, 22, 39, 37, 167, 200, 201, 208, 36, 142, 100, 57, 212, 202, 20, 60, 99, 28, 158, 157, 35, 226, 113, 160, 159, 27, 204, 202, 210, 113, 225, 46, 43, 202, 204, 62, 76, 77, 137, 123, 116, 41, 38, 72, 203, 129, 142, 64, 98, 240, 49, 102, 64, 41, 73, 74, 212, 216, 207, 42, 74, 184, 169, 170, 211, 170, 149, 176, 105, 66, 69, 122, 6, 168, 123, 147, 187, 96, 77, 90, 65, 55, 107, 89, 90, 180, 101, 100, 120, 63, 105, 104, 93, 137, 227, 15, 86, 85, 129, 102, 49, 14, 87, 86, 55, 8, 9, 100, 47, 121, 145, 23, 22, 88, 89, 179, 6, 122, 196, 88, 95, 96, 138, 172, 136, 215, 58, 172, 115, 48, 219, 42, 80, 81, 195, 3, 51, 43, 146, 61, 171, 175, 199, 81, 82, 38, 53, 46, 225, 144, 163, 110, 246, 33, 7, 52, 65, 66, 229, 228, 117, 34, 127, 234, 107, 108, 69, 109, 108, 151, 48, 64, 235, 62, 78, 191, 129, 209, 126, 111, 35, 143, 163, 161, 246, 117, 123, 50, 222, 65, 52, 19, 125, 141, 221, 55, 65, 3, 195, 197, 25, 7, 33, 220, 237, 44, 70, 71, 139, 122, 193, 245, 247, 130, 33, 71, 21, 162, 153, 158, 159, 170, 169, 150, 188, 174, 196, 216, 186, 92, 144, 160, 161, 2, 97, 167, 141, 125, 241, 164, 167, 37, 72, 38, 12, 145, 159, 160, 38, 82, 13, 63, 68, 71, 226, 35, 111, 158, 153, 154, 101, 50, 205, 206, 92, 165, 209, 198, 217, 165, 167, 97, 220, 115, 218, 133, 112, 243, 239, 238, 241, 214, 135, 169, 190, 173, 133, 171, 208, 32, 125, 44, 237, 86, 87, 178, 85, 86, 179, 84, 85, 180, 83, 84, 181, 201, 83, 182, 137, 93, 132, 76, 62, 183, 61, 76, 184, 57, 61, 185, 212, 57, 186, 214, 207, 187, 34, 143, 156, 79, 239, 237, 123, 137, 177, 44, 1, 4, 201, 194, 32, 64, 102, 129, 213, 215, 138, 59, 166, 219, 242, 99, 97, 2, 94, 141, 75, 59, 235, 24, 110, 228, 25, 130, 226, 23, 24, 229, 22, 23, 230, 26, 22, 231, 112, 26, 232, 189, 190, 243, 221, 56, 190, 28, 56, 221, 27, 28, 222, 29, 27, 223, 30, 29, 224, 247, 30, 225, 238, 79, 20, 166, 59, 75, 60, 75, 240, 147, 177, 215, 20, 79, 166, 187, 147, 213, 112, 233, 244, 233, 128, 245, 128, 114, 188, 114, 217, 174, 131, 115, 220, 217, 198, 236, 198, 131, 134, 177, 132, 58, 143, 35, 124, 110, 163, 7, 228, 110, 25, 356, 389, 368, 11, 302, 267, 452, 350, 349, 302, 303, 269, 357, 343, 277, 452, 453, 357, 333, 332, 297, 175, 152, 377, 384, 398, 382, 347, 348, 330, 303, 304, 270, 9, 336, 337, 278, 279, 360, 418, 262, 431, 304, 408, 409, 310, 415, 407, 270, 409, 410, 450, 348, 347, 422, 430, 434, 313, 314, 17, 306, 307, 375, 387, 388, 260, 286, 414, 398, 335, 406, 418, 364, 367, 416, 423, 358, 327, 251, 284, 298, 281, 5, 4, 373, 374, 253, 307, 320, 321, 425, 427, 411, 421, 313, 18, 321, 405, 406, 320, 404, 405, 315, 16, 17, 426, 425, 266, 377, 400, 369, 322, 391, 269, 417, 465, 464, 386, 257, 258, 466, 260, 388, 456, 399, 419, 284, 332, 333, 417, 285, 8, 346, 340, 261, 413, 441, 285, 327, 460, 328, 355, 371, 329, 392, 439, 438, 382, 341, 256, 429, 420, 360, 364, 394, 379, 277, 343, 437, 443, 444, 283, 275, 440, 363, 431, 262, 369, 297, 338, 337, 273, 375, 321, 450, 451, 349, 446, 342, 467, 293, 334, 282, 458, 461, 462, 276, 353, 383, 308, 324, 325, 276, 300, 293, 372, 345, 447, 382, 398, 362, 352, 345, 340, 274, 1, 19, 456, 248, 281, 436, 427, 425, 381, 256, 252, 269, 391, 393, 200, 199, 428, 266, 330, 329, 287, 273, 422, 250, 462, 328, 258, 286, 384, 265, 353, 342, 387, 259, 257, 424, 431, 430, 342, 353, 276, 273, 335, 424, 292, 325, 307, 366, 447, 345, 271, 303, 302, 423, 266, 371, 294, 455, 460, 279, 278, 294, 271, 272, 304, 432, 434, 427, 272, 407, 408, 394, 430, 431, 395, 369, 400, 334, 333, 299, 351, 417, 168, 352, 280, 411, 325, 319, 320, 295, 296, 336, 319, 403, 404, 330, 348, 349, 293, 298, 333, 323, 454, 447, 15, 16, 315, 358, 429, 279, 14, 15, 316, 285, 336, 9, 329, 349, 350, 374, 380, 252, 318, 402, 403, 6, 197, 419, 318, 319, 325, 367, 364, 365, 435, 367, 397, 344, 438, 439, 272, 271, 311, 195, 5, 281, 273, 287, 291, 396, 428, 199, 311, 271, 268, 283, 444, 445, 373, 254, 339, 263, 466, 249, 282, 334, 296, 449, 347, 346, 264, 447, 454, 336, 296, 299, 338, 10, 151, 278, 439, 455, 292, 407, 415, 358, 371, 355, 340, 345, 372, 390, 249, 466, 346, 347, 280, 442, 443, 282, 19, 94, 370, 441, 442, 295, 248, 419, 197, 263, 255, 359, 440, 275, 274, 300, 383, 368, 351, 412, 465, 263, 467, 466, 301, 368, 389, 380, 374, 386, 395, 378, 379, 412, 351, 419, 436, 426, 322, 373, 390, 388, 2, 164, 393, 370, 462, 461, 164, 0, 267, 302, 11, 12, 374, 373, 387, 268, 12, 13, 293, 300, 301, 446, 261, 340, 385, 384, 381, 330, 266, 425, 426, 423, 391, 429, 355, 437, 391, 327, 326, 440, 457, 438, 341, 382, 362, 459, 457, 461, 434, 430, 394, 414, 463, 362, 396, 369, 262, 354, 461, 457, 316, 403, 402, 315, 404, 403, 314, 405, 404, 313, 406, 405, 421, 418, 406, 366, 401, 361, 306, 408, 407, 291, 409, 408, 287, 410, 409, 432, 436, 410, 434, 416, 411, 264, 368, 383, 309, 438, 457, 352, 376, 401, 274, 275, 4, 421, 428, 262, 294, 327, 358, 433, 416, 367, 289, 455, 439, 462, 370, 326, 2, 326, 370, 305, 460, 455, 254, 449, 448, 255, 261, 446, 253, 450, 449, 252, 451, 450, 256, 452, 451, 341, 453, 452, 413, 464, 463, 441, 413, 414, 258, 442, 441, 257, 443, 442, 259, 444, 443, 260, 445, 444, 467, 342, 445, 459, 458, 250, 289, 392, 290, 290, 328, 460, 376, 433, 435, 250, 290, 392, 411, 416, 433, 341, 463, 464, 453, 464, 465, 357, 465, 412, 343, 412, 399, 360, 363, 440, 437, 399, 456, 420, 456, 363, 401, 435, 288, 372, 383, 353, 339, 255, 249, 448, 261, 255, 133, 243, 190, 133, 155, 112, 33, 246, 247, 33, 130, 25, 398, 384, 286, 362, 398, 414, 362, 463, 341, 263, 359, 467, 263, 249, 255, 466, 467, 260, 75, 60, 166, 238, 239, 79, 162, 127, 139, 72, 11, 37, 121, 232, 120, 73, 72, 39, 114, 128, 47, 233, 232, 128, 103, 104, 67, 152, 175, 148, 173, 157, 155, 119, 118, 101, 74, 73, 40, 107, 9, 108, 49, 48, 131, 32, 194, 211, 184, 74, 185, 191, 80, 183, 185, 40, 186, 119, 230, 118, 210, 202, 214, 84, 83, 17, 77, 76, 146, 161, 160, 30, 190, 56, 173, 182, 106, 194, 138, 135, 192, 129, 203, 98, 54, 21, 68, 5, 51, 4, 145, 144, 23, 90, 77, 91, 207, 205, 187, 83, 201, 18, 181, 91, 182, 180, 90, 181, 16, 85, 17, 205, 206, 36, 176, 148, 140, 165, 92, 39, 245, 193, 244, 27, 159, 28, 30, 247, 161, 174, 236, 196, 103, 54, 104, 55, 193, 8, 111, 117, 31, 221, 189, 55, 240, 98, 99, 142, 126, 100, 219, 166, 218, 112, 155, 26, 198, 209, 131, 169, 135, 150, 114, 47, 217, 224, 223, 53, 220, 45, 134, 32, 211, 140, 109, 67, 108, 146, 43, 91, 231, 230, 120, 113, 226, 247, 105, 63, 52, 241, 238, 242, 124, 46, 156, 95, 78, 96, 70, 46, 63, 116, 143, 227, 116, 123, 111, 1, 44, 19, 3, 236, 51, 207, 216, 205, 26, 154, 22, 165, 39, 167, 199, 200, 208, 101, 36, 100, 43, 57, 202, 242, 20, 99, 56, 28, 157, 124, 35, 113, 29, 160, 27, 211, 204, 210, 124, 113, 46, 106, 43, 204, 96, 62, 77, 227, 137, 116, 73, 41, 72, 36, 203, 142, 235, 64, 240, 48, 49, 64, 42, 41, 74, 214, 212, 207, 183, 42, 184, 210, 169, 211, 140, 170, 176, 104, 105, 69, 193, 122, 168, 50, 123, 187, 89, 96, 90, 66, 65, 107, 179, 89, 180, 119, 101, 120, 68, 63, 104, 234, 93, 227, 16, 15, 85, 209, 129, 49, 15, 14, 86, 107, 55, 9, 120, 100, 121, 153, 145, 22, 178, 88, 179, 197, 6, 196, 89, 88, 96, 135, 138, 136, 138, 215, 172, 218, 115, 219, 41, 42, 81, 5, 195, 51, 57, 43, 61, 208, 171, 199, 41, 81, 38, 224, 53, 225, 24, 144, 110, 105, 52, 66, 118, 229, 117, 227, 34, 234, 66, 107, 69, 10, 109, 151, 219, 48, 235, 183, 62, 191, 142, 129, 126, 116, 111, 143, 7, 163, 246, 118, 117, 50, 223, 222, 52, 94, 19, 141, 222, 221, 65, 196, 3, 197, 45, 220, 44, 156, 70, 139, 188, 122, 245, 139, 71, 162, 145, 153, 159, 149, 170, 150, 122, 188, 196, 206, 216, 92, 163, 144, 161, 164, 2, 167, 242, 141, 241, 0, 164, 37, 11, 72, 12, 144, 145, 160, 12, 38, 13, 70, 63, 71, 31, 226, 111, 157, 158, 154, 36, 101, 205, 203, 206, 165, 126, 209, 217, 98, 165, 97, 237, 220, 218, 237, 239, 241, 210, 214, 169, 140, 171, 32, 241, 125, 237, 179, 86, 178, 180, 85, 179, 181, 84, 180, 182, 83, 181, 194, 201, 182, 177, 137, 132, 184, 76, 183, 185, 61, 184, 186, 57, 185, 216, 212, 186, 192, 214, 187, 139, 34, 156, 218, 79, 237, 147, 123, 177, 45, 44, 4, 208, 201, 32, 98, 64, 129, 192, 213, 138, 235, 59, 219, 141, 242, 97, 97, 2, 141, 240, 75, 235, 229, 24, 228, 31, 25, 226, 230, 23, 229, 231, 22, 230, 232, 26, 231, 233, 112, 232, 244, 189, 243, 189, 221, 190, 222, 28, 221, 223, 27, 222, 224, 29, 223, 225, 30, 224, 113, 247, 225, 99, 60, 240, 213, 147, 215, 60, 20, 166, 192, 187, 213, 243, 112, 244, 244, 233, 245, 245, 128, 188, 188, 114, 174, 134, 131, 220, 174, 217, 236, 236, 198, 134, 215, 177, 58, 156, 143, 124, 25, 110, 7, 31, 228, 25, 264, 356, 368, 0, 11, 267, 451, 452, 349, 267, 302, 269, 350, 357, 277, 350, 452, 357, 299, 333, 297, 396, 175, 377, 381, 384, 382, 280, 347, 330, 269, 303, 270, 151, 9, 337, 344, 278, 360, 424, 418, 431, 270, 304, 409, 272, 310, 407, 322, 270, 410, 449, 450, 347, 432, 422, 434, 18, 313, 17, 291, 306, 375, 259, 387, 260, 424, 335, 418, 434, 364, 416, 391, 423, 327, 301, 251, 298, 275, 281, 4, 254, 373, 253, 375, 307, 321, 280, 425, 411, 200, 421, 18, 335, 321, 406, 321, 320, 405, 314, 315, 17, 423, 426, 266, 396, 377, 369, 270, 322, 269, 413, 417, 464, 385, 386, 258, 248, 456, 419, 298, 284, 333, 168, 417, 8, 448, 346, 261, 417, 413, 285, 326, 327, 328, 277, 355, 329, 309, 392, 438, 381, 382, 256, 279, 429, 360, 365, 364, 379, 355, 277, 437, 282, 443, 283, 281, 275, 363, 395, 431, 369, 299, 297, 337, 335, 273, 321, 348, 450, 349, 359, 446, 467, 283, 293, 282, 250, 458, 462, 300, 276, 383, 292, 308, 325, 283, 276, 293, 264, 372, 447, 346, 352, 340, 354, 274, 19, 363, 456, 281, 426, 436, 425, 380, 381, 252, 267, 269, 393, 421, 200, 428, 371, 266, 329, 432, 287, 422, 290, 250, 328, 385, 258, 384, 446, 265, 342, 386, 387, 257, 422, 424, 430, 445, 342, 276, 422, 273, 424, 306, 292, 307, 352, 366, 345, 268, 271, 302, 358, 423, 371, 327, 294, 460, 331, 279, 294, 303, 271, 304, 436, 432, 427, 304, 272, 408, 395, 394, 431, 378, 395, 400, 296, 334, 299, 6, 351, 168, 376, 352, 411, 307, 325, 320, 285, 295, 336, 320, 319, 404, 329, 330, 349, 334, 293, 333, 366, 323, 447, 316, 15, 315, 331, 358, 279, 317, 14, 316, 8, 285, 9, 277, 329, 350, 253, 374, 252, 319, 318, 403, 351, 6, 419, 324, 318, 325, 397, 367, 365, 288, 435, 397, 278, 344, 439, 310, 272, 311, 248, 195, 281, 375, 273, 291, 175, 396, 199, 312, 311, 268, 276, 283, 445, 390, 373, 339, 295, 282, 296, 448, 449, 346, 356, 264, 454, 337, 336, 299, 337, 338, 151, 294, 278, 455, 308, 292, 415, 429, 358, 355, 265, 340, 372, 388, 390, 466, 352, 346, 280, 295, 442, 282, 354, 19, 370, 285, 441, 295, 195, 248, 197, 457, 440, 274, 301, 300, 368, 417, 351, 465, 251, 301, 389, 385, 380, 386, 394, 395, 379, 399, 412, 419, 410, 436, 322, 387, 373, 388, 326, 2, 393, 354, 370, 461, 393, 164, 267, 268, 302, 12, 386, 374, 387, 312, 268, 13, 298, 293, 301, 265, 446, 340, 380, 385, 381, 280, 330, 425, 322, 426, 391, 420, 429, 437, 393, 391, 326, 344, 440, 438, 458, 459, 461, 364, 434, 394, 428, 396, 262, 274, 354, 457, 317, 316, 402, 316, 315, 403, 315, 314, 404, 314, 313, 405, 313, 421, 406, 323, 366, 361, 292, 306, 407, 306, 291, 408, 291, 287, 409, 287, 432, 410, 427, 434, 411, 372, 264, 383, 459, 309, 457, 366, 352, 401, 1, 274, 4, 418, 421, 262, 331, 294, 358, 435, 433, 367, 392, 289, 439, 328, 462, 326, 94, 2, 370, 289, 305, 455, 339, 254, 448, 359, 255, 446, 254, 253, 449, 253, 252, 450, 252, 256, 451, 256, 341, 452, 414, 413, 463, 286, 441, 414, 286, 258, 441, 258, 257, 442, 257, 259, 443, 259, 260, 444, 260, 467, 445, 309, 459, 250, 305, 289, 290, 305, 290, 460, 401, 376, 435, 309, 250, 392, 376, 411, 433, 453, 341, 464, 357, 453, 465, 343, 357, 412, 437, 343, 399, 344, 360, 440, 420, 437, 456, 360, 420, 363, 361, 401, 288, 265, 372, 353, 390, 339, 249, 339, 448, 255],\n  // My face as default (captured with a 640x480 webcam)\n  // prettier-ignore\n  SAMPLE_FACE: {\n    \"keypoints\": [{\n      \"x\": 356.2804412841797,\n      \"y\": 295.1960563659668,\n      \"z\": -23.786449432373047,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 354.8859405517578,\n      \"y\": 264.69520568847656,\n      \"z\": -36.718435287475586\n    }, {\n      \"x\": 355.2180862426758,\n      \"y\": 275.3360366821289,\n      \"z\": -21.183712482452393\n    }, {\n      \"x\": 347.349853515625,\n      \"y\": 242.4400234222412,\n      \"z\": -25.093655586242676\n    }, {\n      \"x\": 354.40135955810547,\n      \"y\": 256.67933464050293,\n      \"z\": -38.23572635650635\n    }, {\n      \"x\": 353.7689971923828,\n      \"y\": 247.54886627197266,\n      \"z\": -34.5475435256958\n    }, {\n      \"x\": 352.1288299560547,\n      \"y\": 227.34312057495117,\n      \"z\": -13.095386028289795\n    }, {\n      \"x\": 303.5013198852539,\n      \"y\": 234.67002868652344,\n      \"z\": 12.500141859054565,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 351.09378814697266,\n      \"y\": 211.87547206878662,\n      \"z\": -6.413471698760986\n    }, {\n      \"x\": 350.7115936279297,\n      \"y\": 202.1251630783081,\n      \"z\": -6.413471698760986\n    }, {\n      \"x\": 348.33667755126953,\n      \"y\": 168.7741756439209,\n      \"z\": 6.483500003814697,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 356.4806365966797,\n      \"y\": 299.2995357513428,\n      \"z\": -23.144519329071045\n    }, {\n      \"x\": 356.5511703491211,\n      \"y\": 302.66146659851074,\n      \"z\": -21.020312309265137\n    }, {\n      \"x\": 356.6239547729492,\n      \"y\": 304.1536331176758,\n      \"z\": -18.137459754943848,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 356.5807342529297,\n      \"y\": 305.1840591430664,\n      \"z\": -18.767719268798828,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 356.8241500854492,\n      \"y\": 308.25711250305176,\n      \"z\": -20.16829490661621\n    }, {\n      \"x\": 357.113037109375,\n      \"y\": 312.26277351379395,\n      \"z\": -22.10575819015503\n    }, {\n      \"x\": 357.34962463378906,\n      \"y\": 317.1123218536377,\n      \"z\": -21.837315559387207,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 357.6658630371094,\n      \"y\": 325.51036834716797,\n      \"z\": -16.27002477645874\n    }, {\n      \"x\": 355.0201416015625,\n      \"y\": 269.36279296875,\n      \"z\": -33.73054027557373\n    }, {\n      \"x\": 348.5237503051758,\n      \"y\": 270.33411026000977,\n      \"z\": -24.93025302886963\n    }, {\n      \"x\": 279.97331619262695,\n      \"y\": 213.24176788330078,\n      \"z\": 47.759642601013184,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 322.66529083251953,\n      \"y\": 238.5027265548706,\n      \"z\": 5.535193085670471\n    }, {\n      \"x\": 316.0983657836914,\n      \"y\": 239.94489669799805,\n      \"z\": 5.777376294136047\n    }, {\n      \"x\": 309.9431610107422,\n      \"y\": 240.24518966674805,\n      \"z\": 7.510589361190796\n    }, {\n      \"x\": 301.31994247436523,\n      \"y\": 237.86138534545898,\n      \"z\": 13.118728399276733\n    }, {\n      \"x\": 328.14266204833984,\n      \"y\": 235.80496788024902,\n      \"z\": 6.646900177001953\n    }, {\n      \"x\": 313.7326431274414,\n      \"y\": 222.11161136627197,\n      \"z\": 3.9887237548828125\n    }, {\n      \"x\": 320.45196533203125,\n      \"y\": 221.87729358673096,\n      \"z\": 4.601476192474365\n    }, {\n      \"x\": 307.35679626464844,\n      \"y\": 223.63793849945068,\n      \"z\": 5.932023525238037\n    }, {\n      \"x\": 303.0031204223633,\n      \"y\": 226.3743782043457,\n      \"z\": 8.479321002960205\n    }, {\n      \"x\": 296.80023193359375,\n      \"y\": 242.94299125671387,\n      \"z\": 15.931552648544312\n    }, {\n      \"x\": 332.2352981567383,\n      \"y\": 340.77341079711914,\n      \"z\": -10.165848731994629\n    }, {\n      \"x\": 301.38587951660156,\n      \"y\": 233.46447944641113,\n      \"z\": 14.764405488967896,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 279.0147018432617,\n      \"y\": 244.37155723571777,\n      \"z\": 45.77549457550049\n    }, {\n      \"x\": 289.60548400878906,\n      \"y\": 239.1807460784912,\n      \"z\": 23.191204071044922\n    }, {\n      \"x\": 320.32257080078125,\n      \"y\": 267.1292781829834,\n      \"z\": -4.954537749290466\n    }, {\n      \"x\": 347.64583587646484,\n      \"y\": 294.4955062866211,\n      \"z\": -23.062820434570312,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 349.28138732910156,\n      \"y\": 303.1095886230469,\n      \"z\": -20.238323211669922\n    }, {\n      \"x\": 338.9453125,\n      \"y\": 298.19186210632324,\n      \"z\": -19.456336498260498,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 333.36788177490234,\n      \"y\": 302.6706790924072,\n      \"z\": -14.776077270507812,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 342.89188385009766,\n      \"y\": 304.3561363220215,\n      \"z\": -17.752301692962646\n    }, {\n      \"x\": 337.7375030517578,\n      \"y\": 306.0098361968994,\n      \"z\": -13.410515785217285\n    }, {\n      \"x\": 325.6159210205078,\n      \"y\": 316.22995376586914,\n      \"z\": -6.681914925575256\n    }, {\n      \"x\": 349.0104675292969,\n      \"y\": 264.9818515777588,\n      \"z\": -36.274919509887695\n    }, {\n      \"x\": 347.7138900756836,\n      \"y\": 257.5664806365967,\n      \"z\": -37.67549514770508\n    }, {\n      \"x\": 291.79357528686523,\n      \"y\": 218.88171672821045,\n      \"z\": 11.578094959259033,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 332.2689437866211,\n      \"y\": 247.56946563720703,\n      \"z\": -3.3730539679527283\n    }, {\n      \"x\": 332.0074462890625,\n      \"y\": 267.1201229095459,\n      \"z\": -19.969879388809204\n    }, {\n      \"x\": 331.27952575683594,\n      \"y\": 263.6967658996582,\n      \"z\": -17.47218608856201\n    }, {\n      \"x\": 301.04373931884766,\n      \"y\": 269.56552505493164,\n      \"z\": 3.61815482378006\n    }, {\n      \"x\": 347.4863815307617,\n      \"y\": 249.0706443786621,\n      \"z\": -32.633421421051025\n    }, {\n      \"x\": 307.26118087768555,\n      \"y\": 208.2646894454956,\n      \"z\": 1.1591226607561111,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 297.91919708251953,\n      \"y\": 212.22604751586914,\n      \"z\": 5.914516448974609,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 285.1651382446289,\n      \"y\": 197.98450469970703,\n      \"z\": 36.391637325286865,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 337.04097747802734,\n      \"y\": 211.25229835510254,\n      \"z\": -4.548954665660858\n    }, {\n      \"x\": 326.5912628173828,\n      \"y\": 223.16698551177979,\n      \"z\": 6.670243740081787\n    }, {\n      \"x\": 320.05664825439453,\n      \"y\": 309.5834255218506,\n      \"z\": -4.055835008621216\n    }, {\n      \"x\": 289.6866226196289,\n      \"y\": 314.617395401001,\n      \"z\": 53.875489234924316,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 337.4256896972656,\n      \"y\": 270.8755302429199,\n      \"z\": -17.67060160636902\n    }, {\n      \"x\": 343.69922637939453,\n      \"y\": 273.0000400543213,\n      \"z\": -18.756048679351807\n    }, {\n      \"x\": 327.4242401123047,\n      \"y\": 309.22399520874023,\n      \"z\": -4.703601002693176,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 330.37220001220703,\n      \"y\": 308.3323001861572,\n      \"z\": -6.442649960517883\n    }, {\n      \"x\": 293.87027740478516,\n      \"y\": 207.7961826324463,\n      \"z\": 9.821539521217346,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 332.11437225341797,\n      \"y\": 271.22812271118164,\n      \"z\": -16.64351224899292\n    }, {\n      \"x\": 320.1197814941406,\n      \"y\": 207.40366458892822,\n      \"z\": -2.48164564371109,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 318.59575271606445,\n      \"y\": 201.07443809509277,\n      \"z\": -3.110446035861969,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 310.72303771972656,\n      \"y\": 175.75075149536133,\n      \"z\": 13.328815698623657,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 289.67578887939453,\n      \"y\": 202.29835510253906,\n      \"z\": 21.370456218719482\n    }, {\n      \"x\": 315.30879974365234,\n      \"y\": 187.35260009765625,\n      \"z\": 5.0304025411605835\n    }, {\n      \"x\": 287.8936767578125,\n      \"y\": 216.54793739318848,\n      \"z\": 17.81065821647644,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 283.9391899108887,\n      \"y\": 215.01142501831055,\n      \"z\": 32.04984903335571\n    }, {\n      \"x\": 348.35330963134766,\n      \"y\": 299.4155788421631,\n      \"z\": -22.47924566268921\n    }, {\n      \"x\": 341.1790466308594,\n      \"y\": 301.8221855163574,\n      \"z\": -18.977805376052856\n    }, {\n      \"x\": 335.69713592529297,\n      \"y\": 304.4266891479492,\n      \"z\": -14.682706594467163\n    }, {\n      \"x\": 339.4615173339844,\n      \"y\": 272.3654365539551,\n      \"z\": -16.38674020767212\n    }, {\n      \"x\": 328.99600982666016,\n      \"y\": 308.86685371398926,\n      \"z\": -5.616893768310547\n    }, {\n      \"x\": 332.00313568115234,\n      \"y\": 309.1875743865967,\n      \"z\": -10.335084199905396\n    }, {\n      \"x\": 331.0068130493164,\n      \"y\": 307.9274368286133,\n      \"z\": -6.681914925575256,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 341.13792419433594,\n      \"y\": 266.4876937866211,\n      \"z\": -26.56425952911377\n    }, {\n      \"x\": 339.02950286865234,\n      \"y\": 305.6663703918457,\n      \"z\": -12.33674168586731,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 344.22935485839844,\n      \"y\": 304.9452781677246,\n      \"z\": -15.161235332489014,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 350.1844024658203,\n      \"y\": 304.374303817749,\n      \"z\": -17.5305438041687,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 348.52630615234375,\n      \"y\": 325.9562301635742,\n      \"z\": -16.164982318878174\n    }, {\n      \"x\": 348.6581802368164,\n      \"y\": 317.1624183654785,\n      \"z\": -21.510512828826904,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 348.9766311645508,\n      \"y\": 312.1923065185547,\n      \"z\": -21.708929538726807\n    }, {\n      \"x\": 349.2427444458008,\n      \"y\": 308.0660820007324,\n      \"z\": -19.643079042434692\n    }, {\n      \"x\": 349.67491149902344,\n      \"y\": 305.42747497558594,\n      \"z\": -18.16080331802368,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 337.95589447021484,\n      \"y\": 306.6535949707031,\n      \"z\": -12.803598642349243,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 337.06878662109375,\n      \"y\": 307.63169288635254,\n      \"z\": -14.274203777313232\n    }, {\n      \"x\": 335.77449798583984,\n      \"y\": 309.8449516296387,\n      \"z\": -15.698124170303345\n    }, {\n      \"x\": 334.6099090576172,\n      \"y\": 312.7997016906738,\n      \"z\": -14.764405488967896,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 327.2330856323242,\n      \"y\": 293.80866050720215,\n      \"z\": -11.864047050476074\n    }, {\n      \"x\": 280.97679138183594,\n      \"y\": 279.79928970336914,\n      \"z\": 68.90834331512451,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 355.13843536376953,\n      \"y\": 271.7875671386719,\n      \"z\": -25.350427627563477\n    }, {\n      \"x\": 334.7235870361328,\n      \"y\": 307.4656391143799,\n      \"z\": -9.302158951759338,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 333.5293960571289,\n      \"y\": 307.89782524108887,\n      \"z\": -10.200862884521484\n    }, {\n      \"x\": 346.29688262939453,\n      \"y\": 276.4256286621094,\n      \"z\": -19.748122692108154\n    }, {\n      \"x\": 335.16246795654297,\n      \"y\": 276.22097969055176,\n      \"z\": -12.313398122787476\n    }, {\n      \"x\": 345.09132385253906,\n      \"y\": 274.7082996368408,\n      \"z\": -19.304605722427368\n    }, {\n      \"x\": 325.4267883300781,\n      \"y\": 252.95130729675293,\n      \"z\": -1.6661019623279572\n    }, {\n      \"x\": 315.347843170166,\n      \"y\": 259.05200958251953,\n      \"z\": -0.25604281574487686\n    }, {\n      \"x\": 330.44933319091797,\n      \"y\": 267.7570152282715,\n      \"z\": -14.017432928085327\n    }, {\n      \"x\": 294.96768951416016,\n      \"y\": 185.26001930236816,\n      \"z\": 23.903164863586426,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 299.63531494140625,\n      \"y\": 192.7913761138916,\n      \"z\": 12.640198469161987\n    }, {\n      \"x\": 304.5452117919922,\n      \"y\": 202.4142837524414,\n      \"z\": 3.244667649269104,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 331.6915512084961,\n      \"y\": 320.0467872619629,\n      \"z\": -10.632705688476562\n    }, {\n      \"x\": 334.5911407470703,\n      \"y\": 201.27566814422607,\n      \"z\": -6.133356094360352,\n      \"name\": \"rightEyebrow\"\n    }, {\n      \"x\": 331.4815902709961,\n      \"y\": 185.44180870056152,\n      \"z\": 0.6627205014228821\n    }, {\n      \"x\": 328.05816650390625,\n      \"y\": 170.8385467529297,\n      \"z\": 7.358860373497009,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 304.49764251708984,\n      \"y\": 239.76297855377197,\n      \"z\": 10.387605428695679\n    }, {\n      \"x\": 290.6382179260254,\n      \"y\": 248.85257720947266,\n      \"z\": 19.03616428375244\n    }, {\n      \"x\": 331.5682601928711,\n      \"y\": 233.20727348327637,\n      \"z\": 7.837390303611755\n    }, {\n      \"x\": 295.5115509033203,\n      \"y\": 228.9834451675415,\n      \"z\": 14.41426157951355\n    }, {\n      \"x\": 336.94332122802734,\n      \"y\": 241.8259334564209,\n      \"z\": -5.27842104434967\n    }, {\n      \"x\": 336.2792205810547,\n      \"y\": 262.7049922943115,\n      \"z\": -26.12074375152588\n    }, {\n      \"x\": 284.4102478027344,\n      \"y\": 255.3262710571289,\n      \"z\": 25.467140674591064\n    }, {\n      \"x\": 295.1420593261719,\n      \"y\": 253.02655220031738,\n      \"z\": 12.430112361907959\n    }, {\n      \"x\": 303.5196113586426,\n      \"y\": 254.20703887939453,\n      \"z\": 6.139191389083862\n    }, {\n      \"x\": 315.73450088500977,\n      \"y\": 251.64799690246582,\n      \"z\": 3.3788898587226868\n    }, {\n      \"x\": 324.69661712646484,\n      \"y\": 247.56494522094727,\n      \"z\": 2.3328344523906708\n    }, {\n      \"x\": 331.57970428466797,\n      \"y\": 243.02241325378418,\n      \"z\": 1.1423448473215103\n    }, {\n      \"x\": 345.6210708618164,\n      \"y\": 229.9976634979248,\n      \"z\": -10.825285911560059\n    }, {\n      \"x\": 286.26644134521484,\n      \"y\": 270.37991523742676,\n      \"z\": 21.708929538726807\n    }, {\n      \"x\": 290.2525520324707,\n      \"y\": 228.4921360015869,\n      \"z\": 17.71728754043579\n    }, {\n      \"x\": 351.65367126464844,\n      \"y\": 269.3400764465332,\n      \"z\": -33.450424671173096\n    }, {\n      \"x\": 333.1378936767578,\n      \"y\": 253.88388633728027,\n      \"z\": -7.230473756790161\n    }, {\n      \"x\": 277.8318977355957,\n      \"y\": 246.95331573486328,\n      \"z\": 68.20805549621582,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 336.6680908203125,\n      \"y\": 238.10003757476807,\n      \"z\": 0.7688578963279724\n    }, {\n      \"x\": 329.95800018310547,\n      \"y\": 269.18323516845703,\n      \"z\": -7.207130789756775\n    }, {\n      \"x\": 299.17491912841797,\n      \"y\": 234.13324356079102,\n      \"z\": 15.95489501953125\n    }, {\n      \"x\": 335.61729431152344,\n      \"y\": 258.71752738952637,\n      \"z\": -23.016133308410645\n    }, {\n      \"x\": 284.1079330444336,\n      \"y\": 297.0343494415283,\n      \"z\": 63.25934886932373,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 331.44542694091797,\n      \"y\": 230.6892442703247,\n      \"z\": 9.92658257484436,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 341.41536712646484,\n      \"y\": 253.01264762878418,\n      \"z\": -29.038610458374023\n    }, {\n      \"x\": 303.5472869873047,\n      \"y\": 327.5896739959717,\n      \"z\": 16.725212335586548\n    }, {\n      \"x\": 304.7756576538086,\n      \"y\": 337.4389457702637,\n      \"z\": 27.38126277923584,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 280.80501556396484,\n      \"y\": 275.32050132751465,\n      \"z\": 45.0752067565918\n    }, {\n      \"x\": 295.43582916259766,\n      \"y\": 318.4501647949219,\n      \"z\": 26.2608003616333\n    }, {\n      \"x\": 281.4303207397461,\n      \"y\": 228.7355661392212,\n      \"z\": 40.94350814819336\n    }, {\n      \"x\": 331.2549591064453,\n      \"y\": 349.4216537475586,\n      \"z\": -7.376367449760437\n    }, {\n      \"x\": 352.4247741699219,\n      \"y\": 271.7330074310303,\n      \"z\": -24.953596591949463\n    }, {\n      \"x\": 327.5672912597656,\n      \"y\": 260.41900634765625,\n      \"z\": -5.456410646438599\n    }, {\n      \"x\": 284.5432472229004,\n      \"y\": 241.7647933959961,\n      \"z\": 29.668869972229004\n    }, {\n      \"x\": 310,\n      \"y\": 235.66174507141113,\n      \"z\": 8.502663969993591,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 315.7071113586426,\n      \"y\": 235.7572603225708,\n      \"z\": 6.938687562942505,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 330.41088104248047,\n      \"y\": 311.04143142700195,\n      \"z\": -9.325502514839172,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 288.5377502441406,\n      \"y\": 285.31983375549316,\n      \"z\": 21.837315559387207\n    }, {\n      \"x\": 344.55039978027344,\n      \"y\": 359.4300842285156,\n      \"z\": -6.705257892608643,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 323.41880798339844,\n      \"y\": 351.67362213134766,\n      \"z\": 7.802375555038452,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 314.64088439941406,\n      \"y\": 346.11894607543945,\n      \"z\": 16.36339783668518,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 349.4945526123047,\n      \"y\": 184.8434829711914,\n      \"z\": -0.21847527474164963\n    }, {\n      \"x\": 359.24694061279297,\n      \"y\": 359.8348903656006,\n      \"z\": -8.403456211090088,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 321.26182556152344,\n      \"y\": 234.64492321014404,\n      \"z\": 6.90950870513916,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 326.318359375,\n      \"y\": 232.90250301361084,\n      \"z\": 8.029969334602356,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 329.6211624145508,\n      \"y\": 231.6195774078369,\n      \"z\": 9.722331762313843,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 285.9398078918457,\n      \"y\": 228.2351303100586,\n      \"z\": 24.650139808654785\n    }, {\n      \"x\": 325.79288482666016,\n      \"y\": 227.88007736206055,\n      \"z\": 7.469738721847534,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 320.1699447631836,\n      \"y\": 227.5934886932373,\n      \"z\": 6.168370842933655,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 314.85408782958984,\n      \"y\": 227.85282611846924,\n      \"z\": 6.2675780057907104,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 309.3084907531738,\n      \"y\": 229.1516876220703,\n      \"z\": 7.7031683921813965,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 305.5621337890625,\n      \"y\": 230.92366218566895,\n      \"z\": 9.722331762313843,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 277.8681945800781,\n      \"y\": 228.5354232788086,\n      \"z\": 59.71122741699219,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 306.1444664001465,\n      \"y\": 235.1954698562622,\n      \"z\": 10.603528022766113,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 355.4478454589844,\n      \"y\": 281.96210861206055,\n      \"z\": -20.565123558044434\n    }, {\n      \"x\": 333.02661895751953,\n      \"y\": 288.0105400085449,\n      \"z\": -14.72939133644104\n    }, {\n      \"x\": 337.15728759765625,\n      \"y\": 269.2059516906738,\n      \"z\": -19.8414945602417\n    }, {\n      \"x\": 345.9898376464844,\n      \"y\": 283.5453128814697,\n      \"z\": -20.4834246635437\n    }, {\n      \"x\": 351.48963928222656,\n      \"y\": 219.98916149139404,\n      \"z\": -7.0378947257995605\n    }, {\n      \"x\": 312.39574432373047,\n      \"y\": 336.50628089904785,\n      \"z\": 8.671900033950806\n    }, {\n      \"x\": 321.32152557373047,\n      \"y\": 343.1755256652832,\n      \"z\": 0.9067271649837494\n    }, {\n      \"x\": 343.78379821777344,\n      \"y\": 353.2975959777832,\n      \"z\": -14.355905055999756\n    }, {\n      \"x\": 296.8791389465332,\n      \"y\": 327.91497230529785,\n      \"z\": 41.01353645324707,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 329.6939468383789,\n      \"y\": 229.27897453308105,\n      \"z\": 8.934508562088013,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 341.6905212402344,\n      \"y\": 241.4073657989502,\n      \"z\": -14.589333534240723\n    }, {\n      \"x\": 359.03079986572266,\n      \"y\": 353.48859786987305,\n      \"z\": -15.803166627883911\n    }, {\n      \"x\": 333.1861877441406,\n      \"y\": 356.43213272094727,\n      \"z\": -1.0234417766332626,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 283.97483825683594,\n      \"y\": 291.4318656921387,\n      \"z\": 41.94725513458252\n    }, {\n      \"x\": 343.33770751953125,\n      \"y\": 305.830135345459,\n      \"z\": -15.756480693817139,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 342.40283966064453,\n      \"y\": 307.7453899383545,\n      \"z\": -17.4021577835083\n    }, {\n      \"x\": 341.53621673583984,\n      \"y\": 311.0595703125,\n      \"z\": -19.047834873199463\n    }, {\n      \"x\": 340.9107208251953,\n      \"y\": 315.4837703704834,\n      \"z\": -18.5576331615448,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 339.1478729248047,\n      \"y\": 323.42233657836914,\n      \"z\": -14.367576837539673\n    }, {\n      \"x\": 333.3201599121094,\n      \"y\": 307.4406337738037,\n      \"z\": -9.617288708686829\n    }, {\n      \"x\": 331.2411117553711,\n      \"y\": 306.9811820983887,\n      \"z\": -9.669809937477112\n    }, {\n      \"x\": 329.23255920410156,\n      \"y\": 306.0508346557617,\n      \"z\": -9.582273960113525,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 322.4586486816406,\n      \"y\": 301.33323669433594,\n      \"z\": -7.720675468444824\n    }, {\n      \"x\": 297.1712112426758,\n      \"y\": 286.9552803039551,\n      \"z\": 8.240055441856384\n    }, {\n      \"x\": 341.3060760498047,\n      \"y\": 235.4432201385498,\n      \"z\": -7.504753470420837\n    }, {\n      \"x\": 336.9318389892578,\n      \"y\": 224.3451976776123,\n      \"z\": 5.829898118972778\n    }, {\n      \"x\": 332.65323638916016,\n      \"y\": 226.70403957366943,\n      \"z\": 8.105834126472473\n    }, {\n      \"x\": 334.67357635498047,\n      \"y\": 306.4397621154785,\n      \"z\": -8.981193900108337,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 297.4601936340332,\n      \"y\": 306.29210472106934,\n      \"z\": 15.476365089416504\n    }, {\n      \"x\": 342.9119110107422,\n      \"y\": 222.37077713012695,\n      \"z\": -2.754466235637665\n    }, {\n      \"x\": 335.4629898071289,\n      \"y\": 332.20250129699707,\n      \"z\": -11.823196411132812\n    }, {\n      \"x\": 353.2412338256836,\n      \"y\": 240.56339263916016,\n      \"z\": -27.147831916809082\n    }, {\n      \"x\": 346.3080596923828,\n      \"y\": 236.41446590423584,\n      \"z\": -18.452589511871338\n    }, {\n      \"x\": 352.6475143432617,\n      \"y\": 234.1420555114746,\n      \"z\": -19.748122692108154\n    }, {\n      \"x\": 337.3209762573242,\n      \"y\": 253.39937210083008,\n      \"z\": -16.024924516677856\n    }, {\n      \"x\": 358.6122131347656,\n      \"y\": 344.90861892700195,\n      \"z\": -18.592647314071655\n    }, {\n      \"x\": 358.1117248535156,\n      \"y\": 334.64990615844727,\n      \"z\": -17.49552845954895\n    }, {\n      \"x\": 346.4450454711914,\n      \"y\": 335.0321102142334,\n      \"z\": -16.32838249206543\n    }, {\n      \"x\": 319.17640686035156,\n      \"y\": 320.2833938598633,\n      \"z\": -3.276764452457428\n    }, {\n      \"x\": 325.2540588378906,\n      \"y\": 276.2369728088379,\n      \"z\": -6.460157036781311\n    }, {\n      \"x\": 326.7214584350586,\n      \"y\": 327.3939514160156,\n      \"z\": -7.417217493057251\n    }, {\n      \"x\": 310.7190132141113,\n      \"y\": 277.2265148162842,\n      \"z\": -3.5452082753181458\n    }, {\n      \"x\": 319.78355407714844,\n      \"y\": 284.8238182067871,\n      \"z\": -6.4543211460113525\n    }, {\n      \"x\": 305.773983001709,\n      \"y\": 290.83580017089844,\n      \"z\": 0.06907138042151928\n    }, {\n      \"x\": 344.4001770019531,\n      \"y\": 344.85408782958984,\n      \"z\": -16.946970224380493\n    }, {\n      \"x\": 333.1879425048828,\n      \"y\": 258.74256134033203,\n      \"z\": -11.90489649772644\n    }, {\n      \"x\": 313.80598068237305,\n      \"y\": 327.08919525146484,\n      \"z\": 2.2277912497520447\n    }, {\n      \"x\": 322.9637908935547,\n      \"y\": 334.6819496154785,\n      \"z\": -3.3643004298210144\n    }, {\n      \"x\": 313.4055519104004,\n      \"y\": 311.2166690826416,\n      \"z\": -1.1175429821014404\n    }, {\n      \"x\": 291.0865783691406,\n      \"y\": 298.2831001281738,\n      \"z\": 22.467575073242188\n    }, {\n      \"x\": 305.6580924987793,\n      \"y\": 313.3707904815674,\n      \"z\": 5.561453700065613\n    }, {\n      \"x\": 288.23760986328125,\n      \"y\": 305.9941864013672,\n      \"z\": 36.765122413635254\n    }, {\n      \"x\": 315.10692596435547,\n      \"y\": 296.26991271972656,\n      \"z\": -4.604393839836121\n    }, {\n      \"x\": 337.50518798828125,\n      \"y\": 247.5944423675537,\n      \"z\": -10.597691535949707\n    }, {\n      \"x\": 338.8450622558594,\n      \"y\": 265.47778129577637,\n      \"z\": -27.778091430664062\n    }, {\n      \"x\": 334.25254821777344,\n      \"y\": 269.0671920776367,\n      \"z\": -20.938611030578613\n    }, {\n      \"x\": 341.64512634277344,\n      \"y\": 259.6387195587158,\n      \"z\": -32.189905643463135\n    }, {\n      \"x\": 331.44081115722656,\n      \"y\": 219.0976095199585,\n      \"z\": 4.207563698291779\n    }, {\n      \"x\": 320.56339263916016,\n      \"y\": 216.49658203125,\n      \"z\": 2.930997312068939\n    }, {\n      \"x\": 311.21912002563477,\n      \"y\": 216.57853603363037,\n      \"z\": 2.9674705862998962\n    }, {\n      \"x\": 303.46256256103516,\n      \"y\": 218.54614734649658,\n      \"z\": 5.357203483581543\n    }, {\n      \"x\": 297.99999237060547,\n      \"y\": 222.505202293396,\n      \"z\": 9.325502514839172\n    }, {\n      \"x\": 294.93839263916016,\n      \"y\": 236.39654159545898,\n      \"z\": 18.534289598464966\n    }, {\n      \"x\": 278.87489318847656,\n      \"y\": 259.7095584869385,\n      \"z\": 45.68212032318115\n    }, {\n      \"x\": 300.3782653808594,\n      \"y\": 245.38593292236328,\n      \"z\": 12.278382778167725\n    }, {\n      \"x\": 307.06348419189453,\n      \"y\": 246.36857986450195,\n      \"z\": 8.164191246032715\n    }, {\n      \"x\": 315.5229187011719,\n      \"y\": 245.3949737548828,\n      \"z\": 5.503097176551819\n    }, {\n      \"x\": 323.71395111083984,\n      \"y\": 242.75178909301758,\n      \"z\": 4.6335723996162415\n    }, {\n      \"x\": 330.2785873413086,\n      \"y\": 239.34658527374268,\n      \"z\": 4.937030673027039\n    }, {\n      \"x\": 334.6982192993164,\n      \"y\": 236.0460376739502,\n      \"z\": 4.823233783245087\n    }, {\n      \"x\": 279.3412208557129,\n      \"y\": 263.5196113586426,\n      \"z\": 70.91583728790283,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 334.65972900390625,\n      \"y\": 271.6648578643799,\n      \"z\": -17.775644063949585\n    }, {\n      \"x\": 342.05677032470703,\n      \"y\": 246.99846267700195,\n      \"z\": -20.84523916244507\n    }, {\n      \"x\": 344.0357971191406,\n      \"y\": 264.5701503753662,\n      \"z\": -32.936880588531494\n    }, {\n      \"x\": 348.25531005859375,\n      \"y\": 268.6645030975342,\n      \"z\": -30.695960521697998\n    }, {\n      \"x\": 344.12227630615234,\n      \"y\": 266.34212493896484,\n      \"z\": -29.808926582336426\n    }, {\n      \"x\": 337.12318420410156,\n      \"y\": 274.2556858062744,\n      \"z\": -15.768152475357056\n    }, {\n      \"x\": 349.49047088623047,\n      \"y\": 269.071683883667,\n      \"z\": -32.51670837402344\n    }, {\n      \"x\": 350.1683044433594,\n      \"y\": 271.4691352844238,\n      \"z\": -24.93025302886963\n    }, {\n      \"x\": 333.9634704589844,\n      \"y\": 230.56639194488525,\n      \"z\": 8.89949381351471\n    }, {\n      \"x\": 338.2147979736328,\n      \"y\": 231.4807891845703,\n      \"z\": 4.6715047955513\n    }, {\n      \"x\": 340.4712677001953,\n      \"y\": 231.74463272094727,\n      \"z\": -0.34996166825294495\n    }, {\n      \"x\": 303.28975677490234,\n      \"y\": 232.24980354309082,\n      \"z\": 11.916568279266357,\n      \"name\": \"rightEye\"\n    }, {\n      \"x\": 299.4649124145508,\n      \"y\": 229.53842639923096,\n      \"z\": 12.325069904327393\n    }, {\n      \"x\": 359.09618377685547,\n      \"y\": 241.77349090576172,\n      \"z\": -24.650139808654785\n    }, {\n      \"x\": 399.46216583251953,\n      \"y\": 229.89503860473633,\n      \"z\": 15.919880867004395,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 361.38919830322266,\n      \"y\": 269.6129894256592,\n      \"z\": -24.510080814361572\n    }, {\n      \"x\": 416.9973373413086,\n      \"y\": 206.0895538330078,\n      \"z\": 53.26857566833496,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 381.32179260253906,\n      \"y\": 235.5476474761963,\n      \"z\": 7.6214683055877686\n    }, {\n      \"x\": 387.8068542480469,\n      \"y\": 236.25958442687988,\n      \"z\": 8.345099091529846\n    }, {\n      \"x\": 393.95751953125,\n      \"y\": 235.8660364151001,\n      \"z\": 10.475142002105713\n    }, {\n      \"x\": 401.84600830078125,\n      \"y\": 232.77019500732422,\n      \"z\": 16.760226488113403\n    }, {\n      \"x\": 375.70568084716797,\n      \"y\": 233.48456382751465,\n      \"z\": 8.234220147132874\n    }, {\n      \"x\": 388.17752838134766,\n      \"y\": 218.94717693328857,\n      \"z\": 6.810300946235657\n    }, {\n      \"x\": 381.64928436279297,\n      \"y\": 219.2656660079956,\n      \"z\": 6.711093783378601\n    }, {\n      \"x\": 394.4760513305664,\n      \"y\": 219.66821193695068,\n      \"z\": 9.173773527145386\n    }, {\n      \"x\": 398.8843536376953,\n      \"y\": 221.8837022781372,\n      \"z\": 12.03328251838684\n    }, {\n      \"x\": 406.5454864501953,\n      \"y\": 237.12156772613525,\n      \"z\": 19.7131085395813\n    }, {\n      \"x\": 383.87447357177734,\n      \"y\": 337.6932907104492,\n      \"z\": -8.631049990653992\n    }, {\n      \"x\": 401.2682342529297,\n      \"y\": 228.5916566848755,\n      \"z\": 18.359217643737793,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 422.0449447631836,\n      \"y\": 236.73934936523438,\n      \"z\": 51.16771221160889\n    }, {\n      \"x\": 412.69153594970703,\n      \"y\": 232.80198097229004,\n      \"z\": 27.52131938934326\n    }, {\n      \"x\": 387.3497772216797,\n      \"y\": 263.298397064209,\n      \"z\": -2.8609684109687805\n    }, {\n      \"x\": 364.5124053955078,\n      \"y\": 293.39221000671387,\n      \"z\": -22.397546768188477,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 363.62987518310547,\n      \"y\": 302.1291446685791,\n      \"z\": -19.643079042434692\n    }, {\n      \"x\": 373.2334518432617,\n      \"y\": 295.8647060394287,\n      \"z\": -18.125789165496826,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 378.83365631103516,\n      \"y\": 299.5177745819092,\n      \"z\": -13.153743743896484,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 369.91477966308594,\n      \"y\": 302.5704002380371,\n      \"z\": -16.65518283843994\n    }, {\n      \"x\": 374.9167251586914,\n      \"y\": 303.5416603088379,\n      \"z\": -11.963253021240234\n    }, {\n      \"x\": 387.58888244628906,\n      \"y\": 312.2716999053955,\n      \"z\": -4.680258631706238\n    }, {\n      \"x\": 360.6635284423828,\n      \"y\": 264.31986808776855,\n      \"z\": -35.94811677932739\n    }, {\n      \"x\": 361.04564666748047,\n      \"y\": 256.8225860595703,\n      \"z\": -37.278664112091064\n    }, {\n      \"x\": 408.3855438232422,\n      \"y\": 213.52088928222656,\n      \"z\": 15.756480693817139,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 373.2946014404297,\n      \"y\": 245.38101196289062,\n      \"z\": -1.9316278398036957\n    }, {\n      \"x\": 376.83860778808594,\n      \"y\": 264.3721103668213,\n      \"z\": -18.510947227478027\n    }, {\n      \"x\": 376.9546127319336,\n      \"y\": 261.0010528564453,\n      \"z\": -15.989909172058105\n    }, {\n      \"x\": 406.1498260498047,\n      \"y\": 263.5030174255371,\n      \"z\": 7.072908878326416\n    }, {\n      \"x\": 360.07205963134766,\n      \"y\": 248.3631706237793,\n      \"z\": -32.16656446456909\n    }, {\n      \"x\": 393.11119079589844,\n      \"y\": 205.10473251342773,\n      \"z\": 3.7786373496055603,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 402.12791442871094,\n      \"y\": 207.89000988006592,\n      \"z\": 9.383859634399414,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 410.8693313598633,\n      \"y\": 191.6182279586792,\n      \"z\": 41.27030849456787,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 364.9509811401367,\n      \"y\": 210.40483474731445,\n      \"z\": -3.758212625980377\n    }, {\n      \"x\": 375.94444274902344,\n      \"y\": 221.1331844329834,\n      \"z\": 8.368442058563232\n    }, {\n      \"x\": 392.1904754638672,\n      \"y\": 305.0360298156738,\n      \"z\": -1.752179116010666\n    }, {\n      \"x\": 419.50225830078125,\n      \"y\": 307.25592613220215,\n      \"z\": 58.96425247192383,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 372.0027160644531,\n      \"y\": 268.7212657928467,\n      \"z\": -16.631840467453003\n    }, {\n      \"x\": 366.1614227294922,\n      \"y\": 271.6237449645996,\n      \"z\": -18.219159841537476\n    }, {\n      \"x\": 385.00938415527344,\n      \"y\": 305.3863334655762,\n      \"z\": -2.567722797393799\n    }, {\n      \"x\": 381.99771881103516,\n      \"y\": 304.9723720550537,\n      \"z\": -4.575215280056\n    }, {\n      \"x\": 405.078125,\n      \"y\": 203.21216583251953,\n      \"z\": 13.713973760604858,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 377.13207244873047,\n      \"y\": 268.4710121154785,\n      \"z\": -15.266278982162476\n    }, {\n      \"x\": 380.9713363647461,\n      \"y\": 205.36980628967285,\n      \"z\": -0.7250899076461792,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 381.7788314819336,\n      \"y\": 198.9268398284912,\n      \"z\": -1.184653863310814,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 385.5204772949219,\n      \"y\": 172.1484375,\n      \"z\": 16.04826807975769,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 407.94189453125,\n      \"y\": 196.76236152648926,\n      \"z\": 25.723915100097656\n    }, {\n      \"x\": 383.03890228271484,\n      \"y\": 184.5157527923584,\n      \"z\": 7.393874526023865\n    }, {\n      \"x\": 411.61781311035156,\n      \"y\": 210.79241752624512,\n      \"z\": 22.315845489501953,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 414.30870056152344,\n      \"y\": 208.4643030166626,\n      \"z\": 37.021894454956055\n    }, {\n      \"x\": 364.28722381591797,\n      \"y\": 298.35777282714844,\n      \"z\": -21.86065673828125\n    }, {\n      \"x\": 371.3682556152344,\n      \"y\": 299.78848457336426,\n      \"z\": -17.834001779556274\n    }, {\n      \"x\": 376.88201904296875,\n      \"y\": 301.6696071624756,\n      \"z\": -13.153743743896484\n    }, {\n      \"x\": 370.2193832397461,\n      \"y\": 270.49095153808594,\n      \"z\": -15.569736957550049\n    }, {\n      \"x\": 383.5081100463867,\n      \"y\": 305.2726364135742,\n      \"z\": -3.673594295978546\n    }, {\n      \"x\": 380.73760986328125,\n      \"y\": 305.96869468688965,\n      \"z\": -8.660228252410889\n    }, {\n      \"x\": 381.2334442138672,\n      \"y\": 304.63574409484863,\n      \"z\": -4.820316135883331,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 368.1698989868164,\n      \"y\": 264.8884963989258,\n      \"z\": -25.653886795043945\n    }, {\n      \"x\": 373.5087203979492,\n      \"y\": 303.4233856201172,\n      \"z\": -10.95950722694397,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 368.4544372558594,\n      \"y\": 303.29601287841797,\n      \"z\": -14.169161319732666,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 362.76554107666016,\n      \"y\": 303.5735607147217,\n      \"z\": -16.911956071853638,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 366.60980224609375,\n      \"y\": 324.8870658874512,\n      \"z\": -15.616422891616821\n    }, {\n      \"x\": 365.7067108154297,\n      \"y\": 315.95678329467773,\n      \"z\": -20.903596878051758,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 365.0083923339844,\n      \"y\": 311.2232208251953,\n      \"z\": -21.066999435424805\n    }, {\n      \"x\": 364.1508102416992,\n      \"y\": 307.0583438873291,\n      \"z\": -18.907777070999146\n    }, {\n      \"x\": 363.37512969970703,\n      \"y\": 304.5721435546875,\n      \"z\": -17.42550015449524,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 374.580078125,\n      \"y\": 304.3059539794922,\n      \"z\": -11.40302300453186,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 375.55362701416016,\n      \"y\": 305.0998020172119,\n      \"z\": -12.861957550048828\n    }, {\n      \"x\": 377.2437286376953,\n      \"y\": 307.1674346923828,\n      \"z\": -14.215847253799438\n    }, {\n      \"x\": 378.68587493896484,\n      \"y\": 309.9015712738037,\n      \"z\": -13.223772048950195,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 383.8992691040039,\n      \"y\": 290.29629707336426,\n      \"z\": -9.97326910495758\n    }, {\n      \"x\": 423.3871841430664,\n      \"y\": 271.91688537597656,\n      \"z\": 74.37058925628662,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 377.68043518066406,\n      \"y\": 304.62209701538086,\n      \"z\": -7.603961229324341,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 379.00428771972656,\n      \"y\": 304.9314594268799,\n      \"z\": -8.57852816581726\n    }, {\n      \"x\": 364.00279998779297,\n      \"y\": 275.2813911437988,\n      \"z\": -19.25792098045349\n    }, {\n      \"x\": 374.68231201171875,\n      \"y\": 273.82555961608887,\n      \"z\": -11.28047227859497\n    }, {\n      \"x\": 365.0354766845703,\n      \"y\": 273.4548568725586,\n      \"z\": -18.791062831878662\n    }, {\n      \"x\": 380.61901092529297,\n      \"y\": 249.8848056793213,\n      \"z\": 0.15501167625188828\n    }, {\n      \"x\": 391.14158630371094,\n      \"y\": 254.7934627532959,\n      \"z\": 2.0906515419483185\n    }, {\n      \"x\": 378.1761169433594,\n      \"y\": 264.9612236022949,\n      \"z\": -12.605184316635132\n    }, {\n      \"x\": 400.9540557861328,\n      \"y\": 179.99592304229736,\n      \"z\": 27.82477855682373,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 398.0038833618164,\n      \"y\": 188.50656509399414,\n      \"z\": 16.094952821731567\n    }, {\n      \"x\": 394.8717498779297,\n      \"y\": 199.0359592437744,\n      \"z\": 6.226727366447449,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 382.10926055908203,\n      \"y\": 316.83926582336426,\n      \"z\": -8.946179747581482\n    }, {\n      \"x\": 366.51588439941406,\n      \"y\": 200.32583713531494,\n      \"z\": -5.24632453918457,\n      \"name\": \"leftEyebrow\"\n    }, {\n      \"x\": 367.4893569946289,\n      \"y\": 183.87210845947266,\n      \"z\": 1.9039081037044525\n    }, {\n      \"x\": 368.6243438720703,\n      \"y\": 168.8127565383911,\n      \"z\": 8.736093044281006,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 398.96175384521484,\n      \"y\": 234.9675178527832,\n      \"z\": 13.713973760604858\n    }, {\n      \"x\": 412.9645538330078,\n      \"y\": 242.23042488098145,\n      \"z\": 23.272905349731445\n    }, {\n      \"x\": 372.05257415771484,\n      \"y\": 231.41919136047363,\n      \"z\": 9.226294755935669\n    }, {\n      \"x\": 406.0722351074219,\n      \"y\": 223.58965873718262,\n      \"z\": 18.370890617370605\n    }, {\n      \"x\": 368.27442169189453,\n      \"y\": 240.2039337158203,\n      \"z\": -4.166713654994965\n    }, {\n      \"x\": 372.3575210571289,\n      \"y\": 260.66442489624023,\n      \"z\": -24.976940155029297\n    }, {\n      \"x\": 419.2244338989258,\n      \"y\": 247.9079246520996,\n      \"z\": 30.299127101898193\n    }, {\n      \"x\": 409.43885803222656,\n      \"y\": 246.60913467407227,\n      \"z\": 16.398411989212036\n    }, {\n      \"x\": 401.69139862060547,\n      \"y\": 248.76328468322754,\n      \"z\": 9.395531415939331\n    }, {\n      \"x\": 389.7608184814453,\n      \"y\": 247.56915092468262,\n      \"z\": 5.841569304466248\n    }, {\n      \"x\": 380.5461883544922,\n      \"y\": 244.55984115600586,\n      \"z\": 4.263003468513489\n    }, {\n      \"x\": 373.25817108154297,\n      \"y\": 240.80214500427246,\n      \"z\": 2.5356262922286987\n    }, {\n      \"x\": 358.77086639404297,\n      \"y\": 229.35615062713623,\n      \"z\": -10.387605428695679\n    }, {\n      \"x\": 419.5793914794922,\n      \"y\": 262.8478717803955,\n      \"z\": 26.5175724029541\n    }, {\n      \"x\": 410.8808898925781,\n      \"y\": 222.51372814178467,\n      \"z\": 22.199130058288574\n    }, {\n      \"x\": 358.45714569091797,\n      \"y\": 268.91467094421387,\n      \"z\": -33.17030906677246\n    }, {\n      \"x\": 373.4129333496094,\n      \"y\": 251.6385841369629,\n      \"z\": -5.771540403366089\n    }, {\n      \"x\": 422.5408172607422,\n      \"y\": 239.23919677734375,\n      \"z\": 74.04378890991211,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 367.8171920776367,\n      \"y\": 236.58040523529053,\n      \"z\": 1.820748895406723\n    }, {\n      \"x\": 378.51959228515625,\n      \"y\": 266.2532329559326,\n      \"z\": -5.74819803237915\n    }, {\n      \"x\": 403.3472442626953,\n      \"y\": 229.05112266540527,\n      \"z\": 19.689764976501465\n    }, {\n      \"x\": 372.34840393066406,\n      \"y\": 256.6451168060303,\n      \"z\": -21.872329711914062\n    }, {\n      \"x\": 422.54566192626953,\n      \"y\": 289.1587829589844,\n      \"z\": 68.67491245269775,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 371.9297409057617,\n      \"y\": 228.90116214752197,\n      \"z\": 11.432201862335205,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 366.21360778808594,\n      \"y\": 251.6158962249756,\n      \"z\": -28.19826364517212\n    }, {\n      \"x\": 409.1571807861328,\n      \"y\": 321.3156223297119,\n      \"z\": 20.2266526222229\n    }, {\n      \"x\": 408.52943420410156,\n      \"y\": 331.44238471984863,\n      \"z\": 31.09278917312622,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 424.2788314819336,\n      \"y\": 267.1992301940918,\n      \"z\": 50.467424392700195\n    }, {\n      \"x\": 415.60352325439453,\n      \"y\": 311.6528606414795,\n      \"z\": 30.579242706298828\n    }, {\n      \"x\": 418.12793731689453,\n      \"y\": 221.59927368164062,\n      \"z\": 46.26569747924805\n    }, {\n      \"x\": 385.68286895751953,\n      \"y\": 346.0184955596924,\n      \"z\": -5.70151150226593\n    }, {\n      \"x\": 357.82936096191406,\n      \"y\": 271.3758373260498,\n      \"z\": -24.836881160736084\n    }, {\n      \"x\": 379.588623046875,\n      \"y\": 257.5071716308594,\n      \"z\": -3.755294680595398\n    }, {\n      \"x\": 417.4592590332031,\n      \"y\": 234.71948146820068,\n      \"z\": 34.5475435256958\n    }, {\n      \"x\": 393.4684371948242,\n      \"y\": 231.58967971801758,\n      \"z\": 11.408859491348267,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 387.8864288330078,\n      \"y\": 232.14245796203613,\n      \"z\": 9.51808214187622,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 382.4981689453125,\n      \"y\": 307.5654888153076,\n      \"z\": -7.522260546684265,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 419.00169372558594,\n      \"y\": 277.8332805633545,\n      \"z\": 26.424202919006348\n    }, {\n      \"x\": 373.62953186035156,\n      \"y\": 357.6375102996826,\n      \"z\": -5.75986921787262,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 392.8708267211914,\n      \"y\": 347.72446632385254,\n      \"z\": 10.154176950454712,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 400.3953552246094,\n      \"y\": 341.0005187988281,\n      \"z\": 19.39797878265381,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 382.25440979003906,\n      \"y\": 231.66935920715332,\n      \"z\": 8.998700976371765,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 377.14550018310547,\n      \"y\": 230.4228687286377,\n      \"z\": 9.804032444953918,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 373.8358688354492,\n      \"y\": 229.64950561523438,\n      \"z\": 11.292144060134888,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 414.5794677734375,\n      \"y\": 221.67891025543213,\n      \"z\": 29.412097930908203\n    }, {\n      \"x\": 377.00672149658203,\n      \"y\": 225.66201210021973,\n      \"z\": 9.360517263412476,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 382.29530334472656,\n      \"y\": 224.8431158065796,\n      \"z\": 8.32175612449646,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 387.5133514404297,\n      \"y\": 224.49507236480713,\n      \"z\": 8.917000889778137,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 393.15906524658203,\n      \"y\": 225.24795055389404,\n      \"z\": 10.737749338150024,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 397.05554962158203,\n      \"y\": 226.55359268188477,\n      \"z\": 13.002015352249146,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 420.5299377441406,\n      \"y\": 221.014666557312,\n      \"z\": 65.40690422058105,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 397.06920623779297,\n      \"y\": 230.6661558151245,\n      \"z\": 13.807345628738403,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 377.94647216796875,\n      \"y\": 285.1647090911865,\n      \"z\": -13.305472135543823\n    }, {\n      \"x\": 372.1118927001953,\n      \"y\": 267.1267318725586,\n      \"z\": -18.83774757385254\n    }, {\n      \"x\": 364.9968719482422,\n      \"y\": 282.24411964416504,\n      \"z\": -19.818150997161865\n    }, {\n      \"x\": 401.973876953125,\n      \"y\": 331.20131492614746,\n      \"z\": 11.566424369812012\n    }, {\n      \"x\": 394.3083190917969,\n      \"y\": 338.86693954467773,\n      \"z\": 3.142542541027069\n    }, {\n      \"x\": 373.9820861816406,\n      \"y\": 351.4504623413086,\n      \"z\": -13.50388765335083\n    }, {\n      \"x\": 414.3888854980469,\n      \"y\": 321.24735832214355,\n      \"z\": 45.51872253417969,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 373.44234466552734,\n      \"y\": 227.33163356781006,\n      \"z\": 10.626870393753052,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 364.0731430053711,\n      \"y\": 240.31539916992188,\n      \"z\": -13.807345628738403\n    }, {\n      \"x\": 384.2658233642578,\n      \"y\": 353.3793067932129,\n      \"z\": 0.7385850697755814,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 423.20526123046875,\n      \"y\": 283.5176181793213,\n      \"z\": 47.152724266052246\n    }, {\n      \"x\": 369.42798614501953,\n      \"y\": 304.0898895263672,\n      \"z\": -14.647691249847412,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 370.63812255859375,\n      \"y\": 305.90051651000977,\n      \"z\": -16.211668252944946\n    }, {\n      \"x\": 371.91192626953125,\n      \"y\": 309.0167713165283,\n      \"z\": -17.84567356109619\n    }, {\n      \"x\": 373.0583953857422,\n      \"y\": 313.3545398712158,\n      \"z\": -17.378815412521362,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 375.39905548095703,\n      \"y\": 321.09289169311523,\n      \"z\": -13.118728399276733\n    }, {\n      \"x\": 379.2567825317383,\n      \"y\": 304.3582534790039,\n      \"z\": -7.924926280975342\n    }, {\n      \"x\": 381.18797302246094,\n      \"y\": 303.7031364440918,\n      \"z\": -7.843226194381714\n    }, {\n      \"x\": 383.0918502807617,\n      \"y\": 302.4884605407715,\n      \"z\": -7.6506465673446655,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 389.09461975097656,\n      \"y\": 297.1475315093994,\n      \"z\": -5.5497825145721436\n    }, {\n      \"x\": 411.6408920288086,\n      \"y\": 280.24898529052734,\n      \"z\": 12.02161192893982\n    }, {\n      \"x\": 363.3110809326172,\n      \"y\": 234.27620887756348,\n      \"z\": -6.775286793708801\n    }, {\n      \"x\": 366.0474395751953,\n      \"y\": 223.29872131347656,\n      \"z\": 6.827808618545532\n    }, {\n      \"x\": 370.34427642822266,\n      \"y\": 225.1457118988037,\n      \"z\": 9.558931589126587\n    }, {\n      \"x\": 377.5371551513672,\n      \"y\": 303.60079765319824,\n      \"z\": -7.358860373497009,\n      \"name\": \"lips\"\n    }, {\n      \"x\": 412.9557800292969,\n      \"y\": 299.53579902648926,\n      \"z\": 19.39797878265381\n    }, {\n      \"x\": 360.0810241699219,\n      \"y\": 221.72012329101562,\n      \"z\": -2.153385728597641\n    }, {\n      \"x\": 379.82784271240234,\n      \"y\": 329.47723388671875,\n      \"z\": -10.48097848892212\n    }, {\n      \"x\": 359.08477783203125,\n      \"y\": 235.7911491394043,\n      \"z\": -18.079102039337158\n    }, {\n      \"x\": 369.6688461303711,\n      \"y\": 251.5407943725586,\n      \"z\": -14.962821006774902\n    }, {\n      \"x\": 369.5555114746094,\n      \"y\": 333.5307312011719,\n      \"z\": -15.67478060722351\n    }, {\n      \"x\": 394.0193176269531,\n      \"y\": 315.6973171234131,\n      \"z\": -0.9920747578144073\n    }, {\n      \"x\": 383.78997802734375,\n      \"y\": 272.7268695831299,\n      \"z\": -4.689012169837952\n    }, {\n      \"x\": 387.67765045166016,\n      \"y\": 323.6722755432129,\n      \"z\": -5.640236139297485\n    }, {\n      \"x\": 397.8769302368164,\n      \"y\": 272.1331214904785,\n      \"z\": -0.9395531564950943\n    }, {\n      \"x\": 389.87476348876953,\n      \"y\": 280.5630111694336,\n      \"z\": -4.29218202829361\n    }, {\n      \"x\": 403.83888244628906,\n      \"y\": 285.1167869567871,\n      \"z\": 3.0229100584983826\n    }, {\n      \"x\": 372.5467300415039,\n      \"y\": 343.1070327758789,\n      \"z\": -16.153310537338257\n    }, {\n      \"x\": 374.1112518310547,\n      \"y\": 256.3721466064453,\n      \"z\": -10.574349164962769\n    }, {\n      \"x\": 399.73785400390625,\n      \"y\": 321.77515983581543,\n      \"z\": 4.849494695663452\n    }, {\n      \"x\": 392.03365325927734,\n      \"y\": 330.56447982788086,\n      \"z\": -1.3407598435878754\n    }, {\n      \"x\": 398.59134674072266,\n      \"y\": 305.93902587890625,\n      \"z\": 1.517290621995926\n    }, {\n      \"x\": 417.95997619628906,\n      \"y\": 290.9716987609863,\n      \"z\": 26.89105987548828\n    }, {\n      \"x\": 406.04541778564453,\n      \"y\": 307.35154151916504,\n      \"z\": 8.666064143180847\n    }, {\n      \"x\": 420.75328826904297,\n      \"y\": 298.40752601623535,\n      \"z\": 41.78385257720947\n    }, {\n      \"x\": 395.4522705078125,\n      \"y\": 291.4153575897217,\n      \"z\": -2.1752697229385376\n    }, {\n      \"x\": 368.6452102661133,\n      \"y\": 245.8882999420166,\n      \"z\": -9.453888535499573\n    }, {\n      \"x\": 370.34900665283203,\n      \"y\": 263.56690406799316,\n      \"z\": -26.75100326538086\n    }, {\n      \"x\": 374.98477935791016,\n      \"y\": 266.6126346588135,\n      \"z\": -19.77146625518799\n    }, {\n      \"x\": 366.99840545654297,\n      \"y\": 258.12140464782715,\n      \"z\": -31.372904777526855\n    }, {\n      \"x\": 371.00616455078125,\n      \"y\": 217.63479709625244,\n      \"z\": 5.60522198677063\n    }, {\n      \"x\": 381.30577087402344,\n      \"y\": 214.14087295532227,\n      \"z\": 4.983716309070587\n    }, {\n      \"x\": 390.1496124267578,\n      \"y\": 213.38221549987793,\n      \"z\": 5.593550801277161\n    }, {\n      \"x\": 397.7696990966797,\n      \"y\": 214.3659782409668,\n      \"z\": 8.57852816581726\n    }, {\n      \"x\": 403.1652069091797,\n      \"y\": 217.65509605407715,\n      \"z\": 13.013685941696167\n    }, {\n      \"x\": 407.3551940917969,\n      \"y\": 230.72525024414062,\n      \"z\": 22.444231510162354\n    }, {\n      \"x\": 424.0876770019531,\n      \"y\": 251.7839241027832,\n      \"z\": 51.16771221160889\n    }, {\n      \"x\": 403.50196838378906,\n      \"y\": 239.88757610321045,\n      \"z\": 15.803166627883911\n    }, {\n      \"x\": 397.31719970703125,\n      \"y\": 241.49806022644043,\n      \"z\": 11.233787536621094\n    }, {\n      \"x\": 388.99425506591797,\n      \"y\": 241.4366912841797,\n      \"z\": 7.948269248008728\n    }, {\n      \"x\": 380.7804489135742,\n      \"y\": 239.78078842163086,\n      \"z\": 6.600214838981628\n    }, {\n      \"x\": 374.01336669921875,\n      \"y\": 237.11946487426758,\n      \"z\": 6.349278092384338\n    }, {\n      \"x\": 369.39125061035156,\n      \"y\": 234.35351371765137,\n      \"z\": 5.987462401390076\n    }, {\n      \"x\": 422.9730987548828,\n      \"y\": 255.76455116271973,\n      \"z\": 76.61150932312012,\n      \"name\": \"faceOval\"\n    }, {\n      \"x\": 374.73915100097656,\n      \"y\": 269.24214363098145,\n      \"z\": -16.608498096466064\n    }, {\n      \"x\": 364.61681365966797,\n      \"y\": 245.71088790893555,\n      \"z\": -20.02823829650879\n    }, {\n      \"x\": 365.3834533691406,\n      \"y\": 263.34174156188965,\n      \"z\": -32.32996463775635\n    }, {\n      \"x\": 361.58252716064453,\n      \"y\": 267.8273677825928,\n      \"z\": -30.345816612243652\n    }, {\n      \"x\": 365.37208557128906,\n      \"y\": 265.0249671936035,\n      \"z\": -29.178667068481445\n    }, {\n      \"x\": 372.72605895996094,\n      \"y\": 272.05135345458984,\n      \"z\": -14.834434986114502\n    }, {\n      \"x\": 360.48614501953125,\n      \"y\": 268.34827423095703,\n      \"z\": -32.189905643463135\n    }, {\n      \"x\": 359.9516296386719,\n      \"y\": 270.8049201965332,\n      \"z\": -24.650139808654785\n    }, {\n      \"x\": 369.5049285888672,\n      \"y\": 229.01945114135742,\n      \"z\": 10.107489824295044\n    }, {\n      \"x\": 365.5447769165039,\n      \"y\": 230.24096488952637,\n      \"z\": 5.593550801277161\n    }, {\n      \"x\": 363.50669860839844,\n      \"y\": 230.6208372116089,\n      \"z\": 0.43622106313705444\n    }, {\n      \"x\": 399.3529510498047,\n      \"y\": 227.65677452087402,\n      \"z\": 15.35965085029602,\n      \"name\": \"leftEye\"\n    }, {\n      \"x\": 402.5693130493164,\n      \"y\": 224.60190296173096,\n      \"z\": 15.931552648544312\n    }],\n    \"box\": {\n      \"xMin\": 277.8318977355957,\n      \"yMin\": 168.7741756439209,\n      \"xMax\": 424.2788314819336,\n      \"yMax\": 359.8348903656006,\n      \"width\": 146.4469337463379,\n      \"height\": 191.0607147216797\n    }\n  },\n  // Tasks-vision: https://developers.google.com/mediapipe/solutions/vision/face_landmarker/web_js\n  // prettier-ignore\n  SAMPLE_FACELANDMARKER_RESULT: {\n    \"faceLandmarks\": [[{\n      \"x\": 0.5760777592658997,\n      \"y\": 0.8639070391654968,\n      \"z\": -0.030997956171631813\n    }, {\n      \"x\": 0.572094738483429,\n      \"y\": 0.7886289358139038,\n      \"z\": -0.07189624011516571\n    }, {\n      \"x\": 0.5723551511764526,\n      \"y\": 0.8075382709503174,\n      \"z\": -0.03578168898820877\n    }, {\n      \"x\": 0.5548420548439026,\n      \"y\": 0.7188365459442139,\n      \"z\": -0.057787876576185226\n    }, {\n      \"x\": 0.5706077814102173,\n      \"y\": 0.7674974799156189,\n      \"z\": -0.07740399986505508\n    }, {\n      \"x\": 0.5681378245353699,\n      \"y\": 0.7387768030166626,\n      \"z\": -0.07356284558773041\n    }, {\n      \"x\": 0.5621535181999207,\n      \"y\": 0.6681165099143982,\n      \"z\": -0.04189874976873398\n    }, {\n      \"x\": 0.46613582968711853,\n      \"y\": 0.6679812073707581,\n      \"z\": 0.011289681307971478\n    }, {\n      \"x\": 0.5579932928085327,\n      \"y\": 0.6174106597900391,\n      \"z\": -0.03502821549773216\n    }, {\n      \"x\": 0.5563451647758484,\n      \"y\": 0.5905600190162659,\n      \"z\": -0.03928658738732338\n    }, {\n      \"x\": 0.5487832427024841,\n      \"y\": 0.4900572597980499,\n      \"z\": -0.029898937791585922\n    }, {\n      \"x\": 0.5765544176101685,\n      \"y\": 0.8692144751548767,\n      \"z\": -0.02831427752971649\n    }, {\n      \"x\": 0.5771114230155945,\n      \"y\": 0.873644232749939,\n      \"z\": -0.02345779910683632\n    }, {\n      \"x\": 0.5771905779838562,\n      \"y\": 0.877016007900238,\n      \"z\": -0.016658689826726913\n    }, {\n      \"x\": 0.5778058767318726,\n      \"y\": 0.8770116567611694,\n      \"z\": -0.014505492523312569\n    }, {\n      \"x\": 0.5783766508102417,\n      \"y\": 0.8835000991821289,\n      \"z\": -0.015996402129530907\n    }, {\n      \"x\": 0.5792440176010132,\n      \"y\": 0.8913810849189758,\n      \"z\": -0.01924579218029976\n    }, {\n      \"x\": 0.5796768069267273,\n      \"y\": 0.8996334671974182,\n      \"z\": -0.018261712044477463\n    }, {\n      \"x\": 0.5817288160324097,\n      \"y\": 0.9255813956260681,\n      \"z\": -0.007126849144697189\n    }, {\n      \"x\": 0.5726592540740967,\n      \"y\": 0.7992473244667053,\n      \"z\": -0.0643521398305893\n    }, {\n      \"x\": 0.5579419136047363,\n      \"y\": 0.7996989488601685,\n      \"z\": -0.04566684365272522\n    }, {\n      \"x\": 0.4216199815273285,\n      \"y\": 0.5958762764930725,\n      \"z\": 0.06776496022939682\n    }, {\n      \"x\": 0.5052269697189331,\n      \"y\": 0.6796539425849915,\n      \"z\": -0.0010737782577052712\n    }, {\n      \"x\": 0.49243026971817017,\n      \"y\": 0.6838865876197815,\n      \"z\": -0.0005227324436418712\n    }, {\n      \"x\": 0.4796970784664154,\n      \"y\": 0.6856290102005005,\n      \"z\": 0.002684245817363262\n    }, {\n      \"x\": 0.4618356227874756,\n      \"y\": 0.6764569878578186,\n      \"z\": 0.013439622707664967\n    }, {\n      \"x\": 0.5160380601882935,\n      \"y\": 0.6737282276153564,\n      \"z\": -0.000017607348127057776\n    }, {\n      \"x\": 0.48070961236953735,\n      \"y\": 0.6255870461463928,\n      \"z\": -0.008339674212038517\n    }, {\n      \"x\": 0.49719780683517456,\n      \"y\": 0.6256808042526245,\n      \"z\": -0.008027955889701843\n    }, {\n      \"x\": 0.46674346923828125,\n      \"y\": 0.6317623853683472,\n      \"z\": -0.004460199736058712\n    }, {\n      \"x\": 0.4582492709159851,\n      \"y\": 0.641118049621582,\n      \"z\": 0.0011905613355338573\n    }, {\n      \"x\": 0.45408669114112854,\n      \"y\": 0.6911458969116211,\n      \"z\": 0.020514748990535736\n    }, {\n      \"x\": 0.535312294960022,\n      \"y\": 0.9619986414909363,\n      \"z\": 0.012499462813138962\n    }, {\n      \"x\": 0.4608460068702698,\n      \"y\": 0.6628725528717041,\n      \"z\": 0.01517564244568348\n    }, {\n      \"x\": 0.4206731915473938,\n      \"y\": 0.6828458309173584,\n      \"z\": 0.07848648726940155\n    }, {\n      \"x\": 0.4390624463558197,\n      \"y\": 0.6796106696128845,\n      \"z\": 0.03283142298460007\n    }, {\n      \"x\": 0.5029968619346619,\n      \"y\": 0.7701570391654968,\n      \"z\": -0.009734481573104858\n    }, {\n      \"x\": 0.5595027208328247,\n      \"y\": 0.8607323169708252,\n      \"z\": -0.030043255537748337\n    }, {\n      \"x\": 0.5621269941329956,\n      \"y\": 0.8738374710083008,\n      \"z\": -0.021709579974412918\n    }, {\n      \"x\": 0.5451499819755554,\n      \"y\": 0.865527331829071,\n      \"z\": -0.022014077752828598\n    }, {\n      \"x\": 0.5351184010505676,\n      \"y\": 0.8705098032951355,\n      \"z\": -0.011602800339460373\n    }, {\n      \"x\": 0.5495014190673828,\n      \"y\": 0.8744956254959106,\n      \"z\": -0.016490943729877472\n    }, {\n      \"x\": 0.5395170450210571,\n      \"y\": 0.8759440779685974,\n      \"z\": -0.007333362940698862\n    }, {\n      \"x\": 0.5183624029159546,\n      \"y\": 0.8959754705429077,\n      \"z\": 0.010520773939788342\n    }, {\n      \"x\": 0.5604349374771118,\n      \"y\": 0.7895449995994568,\n      \"z\": -0.07082037627696991\n    }, {\n      \"x\": 0.557381272315979,\n      \"y\": 0.7687489986419678,\n      \"z\": -0.07590588927268982\n    }, {\n      \"x\": 0.4432901442050934,\n      \"y\": 0.6308897733688354,\n      \"z\": 0.0027153254486620426\n    }, {\n      \"x\": 0.5258325338363647,\n      \"y\": 0.7151225805282593,\n      \"z\": -0.014676518738269806\n    }, {\n      \"x\": 0.5271827578544617,\n      \"y\": 0.7833116054534912,\n      \"z\": -0.037643320858478546\n    }, {\n      \"x\": 0.5257382988929749,\n      \"y\": 0.7717816233634949,\n      \"z\": -0.03401920944452286\n    }, {\n      \"x\": 0.46516409516334534,\n      \"y\": 0.7705106735229492,\n      \"z\": 0.0065747760236263275\n    }, {\n      \"x\": 0.5558893084526062,\n      \"y\": 0.7420997619628906,\n      \"z\": -0.0694495290517807\n    }, {\n      \"x\": 0.4720408320426941,\n      \"y\": 0.6066038608551025,\n      \"z\": -0.021204356104135513\n    }, {\n      \"x\": 0.45432573556900024,\n      \"y\": 0.6158540844917297,\n      \"z\": -0.011054684408009052\n    }, {\n      \"x\": 0.4305151402950287,\n      \"y\": 0.5608053803443909,\n      \"z\": 0.0396830290555954\n    }, {\n      \"x\": 0.5310865640640259,\n      \"y\": 0.6157484650611877,\n      \"z\": -0.03081176057457924\n    }, {\n      \"x\": 0.5114666223526001,\n      \"y\": 0.6329749226570129,\n      \"z\": -0.00335998204536736\n    }, {\n      \"x\": 0.506435751914978,\n      \"y\": 0.8786543607711792,\n      \"z\": 0.012980876490473747\n    }, {\n      \"x\": 0.4480472207069397,\n      \"y\": 0.8640613555908203,\n      \"z\": 0.12569651007652283\n    }, {\n      \"x\": 0.5372058153152466,\n      \"y\": 0.7942581176757812,\n      \"z\": -0.03168361634016037\n    }, {\n      \"x\": 0.5488379597663879,\n      \"y\": 0.8001630306243896,\n      \"z\": -0.03280917927622795\n    }, {\n      \"x\": 0.5213388204574585,\n      \"y\": 0.8794381618499756,\n      \"z\": 0.011892606504261494\n    }, {\n      \"x\": 0.5242055654525757,\n      \"y\": 0.8789222240447998,\n      \"z\": 0.008370225317776203\n    }, {\n      \"x\": 0.4477175176143646,\n      \"y\": 0.6039950251579285,\n      \"z\": -0.0050799972377717495\n    }, {\n      \"x\": 0.526964008808136,\n      \"y\": 0.7916748523712158,\n      \"z\": -0.02968614175915718\n    }, {\n      \"x\": 0.4971255660057068,\n      \"y\": 0.6050706505775452,\n      \"z\": -0.028175678104162216\n    }, {\n      \"x\": 0.4938119053840637,\n      \"y\": 0.5882453918457031,\n      \"z\": -0.03210941329598427\n    }, {\n      \"x\": 0.4757143557071686,\n      \"y\": 0.5094879865646362,\n      \"z\": -0.01300730835646391\n    }, {\n      \"x\": 0.43947282433509827,\n      \"y\": 0.5816648006439209,\n      \"z\": 0.01415177434682846\n    }, {\n      \"x\": 0.485664039850235,\n      \"y\": 0.5477864146232605,\n      \"z\": -0.023685332387685776\n    }, {\n      \"x\": 0.43635931611061096,\n      \"y\": 0.6226438283920288,\n      \"z\": 0.013606148771941662\n    }, {\n      \"x\": 0.42910251021385193,\n      \"y\": 0.6102726459503174,\n      \"z\": 0.03926564007997513\n    }, {\n      \"x\": 0.5605402588844299,\n      \"y\": 0.8680099248886108,\n      \"z\": -0.027318159118294716\n    }, {\n      \"x\": 0.5474816560745239,\n      \"y\": 0.8702861070632935,\n      \"z\": -0.019686367362737656\n    }, {\n      \"x\": 0.5373021364212036,\n      \"y\": 0.8728838562965393,\n      \"z\": -0.010484928265213966\n    }, {\n      \"x\": 0.540735125541687,\n      \"y\": 0.7979167103767395,\n      \"z\": -0.029073253273963928\n    }, {\n      \"x\": 0.5228585004806519,\n      \"y\": 0.87913578748703,\n      \"z\": 0.009915109723806381\n    }, {\n      \"x\": 0.530497670173645,\n      \"y\": 0.8815253973007202,\n      \"z\": 0.0020524784922599792\n    }, {\n      \"x\": 0.5259912610054016,\n      \"y\": 0.8790552616119385,\n      \"z\": 0.007895970717072487\n    }, {\n      \"x\": 0.5433906316757202,\n      \"y\": 0.7882310748100281,\n      \"z\": -0.05121905356645584\n    }, {\n      \"x\": 0.541388213634491,\n      \"y\": 0.8777219653129578,\n      \"z\": -0.00466804439201951\n    }, {\n      \"x\": 0.5515822172164917,\n      \"y\": 0.8767023086547852,\n      \"z\": -0.010475946590304375\n    }, {\n      \"x\": 0.5637003779411316,\n      \"y\": 0.877059817314148,\n      \"z\": -0.015273625031113625\n    }, {\n      \"x\": 0.5640299320220947,\n      \"y\": 0.9263423085212708,\n      \"z\": -0.00658724969252944\n    }, {\n      \"x\": 0.5642300248146057,\n      \"y\": 0.8993074893951416,\n      \"z\": -0.017653480172157288\n    }, {\n      \"x\": 0.5637336373329163,\n      \"y\": 0.8910360932350159,\n      \"z\": -0.01852807030081749\n    }, {\n      \"x\": 0.5637134313583374,\n      \"y\": 0.8837276697158813,\n      \"z\": -0.01482592523097992\n    }, {\n      \"x\": 0.564205527305603,\n      \"y\": 0.8768964409828186,\n      \"z\": -0.01331155002117157\n    }, {\n      \"x\": 0.5419867634773254,\n      \"y\": 0.8778373599052429,\n      \"z\": -0.0037720394320786\n    }, {\n      \"x\": 0.5404468774795532,\n      \"y\": 0.880696177482605,\n      \"z\": -0.005610354244709015\n    }, {\n      \"x\": 0.5392338633537292,\n      \"y\": 0.8845721483230591,\n      \"z\": -0.007352025713771582\n    }, {\n      \"x\": 0.538469672203064,\n      \"y\": 0.8891173601150513,\n      \"z\": -0.005154991988092661\n    }, {\n      \"x\": 0.5189250111579895,\n      \"y\": 0.8452741503715515,\n      \"z\": -0.009755070321261883\n    }, {\n      \"x\": 0.4258975088596344,\n      \"y\": 0.7662280797958374,\n      \"z\": 0.1387351155281067\n    }, {\n      \"x\": 0.5725725293159485,\n      \"y\": 0.8041572570800781,\n      \"z\": -0.04583907872438431\n    }, {\n      \"x\": 0.5342061519622803,\n      \"y\": 0.8785833120346069,\n      \"z\": 0.002659974154084921\n    }, {\n      \"x\": 0.5324031114578247,\n      \"y\": 0.8804071545600891,\n      \"z\": 0.0017832003068178892\n    }, {\n      \"x\": 0.5538818836212158,\n      \"y\": 0.8078407645225525,\n      \"z\": -0.03254539892077446\n    }, {\n      \"x\": 0.5325431823730469,\n      \"y\": 0.8026832938194275,\n      \"z\": -0.019140373915433884\n    }, {\n      \"x\": 0.5514076948165894,\n      \"y\": 0.8043903112411499,\n      \"z\": -0.03313535451889038\n    }, {\n      \"x\": 0.5131856203079224,\n      \"y\": 0.7284771800041199,\n      \"z\": -0.009399853646755219\n    }, {\n      \"x\": 0.49331504106521606,\n      \"y\": 0.7443980574607849,\n      \"z\": -0.005225230939686298\n    }, {\n      \"x\": 0.5239617824554443,\n      \"y\": 0.7807451486587524,\n      \"z\": -0.025881027802824974\n    }, {\n      \"x\": 0.4473606050014496,\n      \"y\": 0.5315827131271362,\n      \"z\": 0.011164786294102669\n    }, {\n      \"x\": 0.45718759298324585,\n      \"y\": 0.5604941248893738,\n      \"z\": -0.005943301599472761\n    }, {\n      \"x\": 0.4670005738735199,\n      \"y\": 0.5909327268600464,\n      \"z\": -0.019681761041283607\n    }, {\n      \"x\": 0.5311570167541504,\n      \"y\": 0.9076261520385742,\n      \"z\": 0.00389476353302598\n    }, {\n      \"x\": 0.5249923467636108,\n      \"y\": 0.5893563628196716,\n      \"z\": -0.037981919944286346\n    }, {\n      \"x\": 0.5166932344436646,\n      \"y\": 0.5429551005363464,\n      \"z\": -0.03319704160094261\n    }, {\n      \"x\": 0.5085030198097229,\n      \"y\": 0.49676206707954407,\n      \"z\": -0.02691275253891945\n    }, {\n      \"x\": 0.4687720239162445,\n      \"y\": 0.6834565997123718,\n      \"z\": 0.008113506250083447\n    }, {\n      \"x\": 0.4426414966583252,\n      \"y\": 0.7069531679153442,\n      \"z\": 0.028577271848917007\n    }, {\n      \"x\": 0.5230373740196228,\n      \"y\": 0.6675713658332825,\n      \"z\": 0.001773772411979735\n    }, {\n      \"x\": 0.4481240212917328,\n      \"y\": 0.6527872085571289,\n      \"z\": 0.012414850294589996\n    }, {\n      \"x\": 0.5339856743812561,\n      \"y\": 0.7012367844581604,\n      \"z\": -0.020220188423991203\n    }, {\n      \"x\": 0.5347223281860352,\n      \"y\": 0.7761190533638,\n      \"z\": -0.05141595005989075\n    }, {\n      \"x\": 0.4315067231655121,\n      \"y\": 0.7211957573890686,\n      \"z\": 0.04381405934691429\n    }, {\n      \"x\": 0.45203351974487305,\n      \"y\": 0.7206180095672607,\n      \"z\": 0.017288070172071457\n    }, {\n      \"x\": 0.46892452239990234,\n      \"y\": 0.7265436053276062,\n      \"z\": 0.005602988880127668\n    }, {\n      \"x\": 0.49314674735069275,\n      \"y\": 0.7202282547950745,\n      \"z\": -0.0006408205372281373\n    }, {\n      \"x\": 0.5104925632476807,\n      \"y\": 0.7091827392578125,\n      \"z\": -0.00362918758764863\n    }, {\n      \"x\": 0.5232142210006714,\n      \"y\": 0.698553740978241,\n      \"z\": -0.00787867046892643\n    }, {\n      \"x\": 0.5497883558273315,\n      \"y\": 0.6743605136871338,\n      \"z\": -0.036349106580019\n    }, {\n      \"x\": 0.43658503890037537,\n      \"y\": 0.7627100348472595,\n      \"z\": 0.042555369436740875\n    }, {\n      \"x\": 0.4397648870944977,\n      \"y\": 0.6528646349906921,\n      \"z\": 0.017956094816327095\n    }, {\n      \"x\": 0.5653332471847534,\n      \"y\": 0.7992802858352661,\n      \"z\": -0.06365057826042175\n    }, {\n      \"x\": 0.5285563468933105,\n      \"y\": 0.736810564994812,\n      \"z\": -0.018836988136172295\n    }, {\n      \"x\": 0.4180678725242615,\n      \"y\": 0.6792560815811157,\n      \"z\": 0.12284679710865021\n    }, {\n      \"x\": 0.5328429937362671,\n      \"y\": 0.6865872144699097,\n      \"z\": -0.010484723374247551\n    }, {\n      \"x\": 0.5230283141136169,\n      \"y\": 0.7809416055679321,\n      \"z\": -0.011922398582100868\n    }, {\n      \"x\": 0.4551771283149719,\n      \"y\": 0.6650775074958801,\n      \"z\": 0.01774493046104908\n    }, {\n      \"x\": 0.5337203741073608,\n      \"y\": 0.7618928551673889,\n      \"z\": -0.04697106033563614\n    }, {\n      \"x\": 0.43463975191116333,\n      \"y\": 0.8133478164672852,\n      \"z\": 0.1354849934577942\n    }, {\n      \"x\": 0.5225707292556763,\n      \"y\": 0.6605283617973328,\n      \"z\": 0.004980515688657761\n    }, {\n      \"x\": 0.5441933870315552,\n      \"y\": 0.7497199773788452,\n      \"z\": -0.06091512367129326\n    }, {\n      \"x\": 0.4774007797241211,\n      \"y\": 0.9159183502197266,\n      \"z\": 0.059622734785079956\n    }, {\n      \"x\": 0.48068761825561523,\n      \"y\": 0.9364941716194153,\n      \"z\": 0.08404944837093353\n    }, {\n      \"x\": 0.4268292486667633,\n      \"y\": 0.7657528519630432,\n      \"z\": 0.09051097184419632\n    }, {\n      \"x\": 0.46051913499832153,\n      \"y\": 0.8880485892295837,\n      \"z\": 0.0738474428653717\n    }, {\n      \"x\": 0.4243420660495758,\n      \"y\": 0.6434382200241089,\n      \"z\": 0.06230505183339119\n    }, {\n      \"x\": 0.5342157483100891,\n      \"y\": 0.9835634231567383,\n      \"z\": 0.021662971004843712\n    }, {\n      \"x\": 0.5668109655380249,\n      \"y\": 0.8042187094688416,\n      \"z\": -0.044937074184417725\n    }, {\n      \"x\": 0.5176341533660889,\n      \"y\": 0.7530587315559387,\n      \"z\": -0.012967454269528389\n    }, {\n      \"x\": 0.430206298828125,\n      \"y\": 0.6835605502128601,\n      \"z\": 0.04612284153699875\n    }, {\n      \"x\": 0.4794231951236725,\n      \"y\": 0.6732114553451538,\n      \"z\": 0.003970044665038586\n    }, {\n      \"x\": 0.49073347449302673,\n      \"y\": 0.6722435355186462,\n      \"z\": 0.0008692514384165406\n    }, {\n      \"x\": 0.5294116139411926,\n      \"y\": 0.884677529335022,\n      \"z\": 0.004413890186697245\n    }, {\n      \"x\": 0.4430122375488281,\n      \"y\": 0.80235356092453,\n      \"z\": 0.04987282305955887\n    }, {\n      \"x\": 0.5603825449943542,\n      \"y\": 1.0092442035675049,\n      \"z\": 0.026417359709739685\n    }, {\n      \"x\": 0.5186598300933838,\n      \"y\": 0.9828659892082214,\n      \"z\": 0.0513598807156086\n    }, {\n      \"x\": 0.5010536909103394,\n      \"y\": 0.9640932679176331,\n      \"z\": 0.06591596454381943\n    }, {\n      \"x\": 0.5524769425392151,\n      \"y\": 0.539441704750061,\n      \"z\": -0.035816047340631485\n    }, {\n      \"x\": 0.5879997611045837,\n      \"y\": 1.0091472864151,\n      \"z\": 0.02285068854689598\n    }, {\n      \"x\": 0.5016193985939026,\n      \"y\": 0.6684437990188599,\n      \"z\": 0.00028415941051207483\n    }, {\n      \"x\": 0.511952817440033,\n      \"y\": 0.6642197370529175,\n      \"z\": 0.0021144719794392586\n    }, {\n      \"x\": 0.5194343328475952,\n      \"y\": 0.6623469591140747,\n      \"z\": 0.004674181342124939\n    }, {\n      \"x\": 0.4321230351924896,\n      \"y\": 0.6496355533599854,\n      \"z\": 0.03124697133898735\n    }, {\n      \"x\": 0.508686363697052,\n      \"y\": 0.6479565501213074,\n      \"z\": -0.00044765998609364033\n    }, {\n      \"x\": 0.4963986277580261,\n      \"y\": 0.6431032419204712,\n      \"z\": -0.0032507688738405704\n    }, {\n      \"x\": 0.4845542013645172,\n      \"y\": 0.6430778503417969,\n      \"z\": -0.002903624437749386\n    }, {\n      \"x\": 0.4733612537384033,\n      \"y\": 0.647506833076477,\n      \"z\": 0.00023347247042693198\n    }, {\n      \"x\": 0.4668654501438141,\n      \"y\": 0.653346598148346,\n      \"z\": 0.004762572236359119\n    }, {\n      \"x\": 0.41815051436424255,\n      \"y\": 0.633708119392395,\n      \"z\": 0.09809435904026031\n    }, {\n      \"x\": 0.47159942984580994,\n      \"y\": 0.6711485385894775,\n      \"z\": 0.007849935442209244\n    }, {\n      \"x\": 0.5734396576881409,\n      \"y\": 0.8256140351295471,\n      \"z\": -0.03155219927430153\n    }, {\n      \"x\": 0.5306524038314819,\n      \"y\": 0.8337990641593933,\n      \"z\": -0.018351426348090172\n    }, {\n      \"x\": 0.5371729135513306,\n      \"y\": 0.7910830974578857,\n      \"z\": -0.037286680191755295\n    }, {\n      \"x\": 0.5549534559249878,\n      \"y\": 0.8275275826454163,\n      \"z\": -0.030664825811982155\n    }, {\n      \"x\": 0.5597432255744934,\n      \"y\": 0.6418541669845581,\n      \"z\": -0.03318847343325615\n    }, {\n      \"x\": 0.4958484172821045,\n      \"y\": 0.9429569244384766,\n      \"z\": 0.048340678215026855\n    }, {\n      \"x\": 0.5140507817268372,\n      \"y\": 0.9634028077125549,\n      \"z\": 0.03589847311377525\n    }, {\n      \"x\": 0.5587693452835083,\n      \"y\": 0.9951097369194031,\n      \"z\": 0.00908728688955307\n    }, {\n      \"x\": 0.46411189436912537,\n      \"y\": 0.9051855206489563,\n      \"z\": 0.10601935535669327\n    }, {\n      \"x\": 0.5181609392166138,\n      \"y\": 0.6554316878318787,\n      \"z\": 0.002546071307733655\n    }, {\n      \"x\": 0.5436590909957886,\n      \"y\": 0.7085841298103333,\n      \"z\": -0.03844436630606651\n    }, {\n      \"x\": 0.5872187614440918,\n      \"y\": 0.9960382580757141,\n      \"z\": 0.0063423276878893375\n    }, {\n      \"x\": 0.5379653573036194,\n      \"y\": 0.9989125728607178,\n      \"z\": 0.03636329993605614\n    }, {\n      \"x\": 0.4350326955318451,\n      \"y\": 0.8088565468788147,\n      \"z\": 0.09147704392671585\n    }, {\n      \"x\": 0.5523084998130798,\n      \"y\": 0.8773422837257385,\n      \"z\": -0.009068487212061882\n    }, {\n      \"x\": 0.5510149598121643,\n      \"y\": 0.8816931843757629,\n      \"z\": -0.011043853126466274\n    }, {\n      \"x\": 0.5503793954849243,\n      \"y\": 0.88776695728302,\n      \"z\": -0.01348799467086792\n    }, {\n      \"x\": 0.5501549243927002,\n      \"y\": 0.8954370617866516,\n      \"z\": -0.012142189778387547\n    }, {\n      \"x\": 0.546072781085968,\n      \"y\": 0.9192524552345276,\n      \"z\": -0.003157563041895628\n    }, {\n      \"x\": 0.5314661860466003,\n      \"y\": 0.8771666884422302,\n      \"z\": 0.0005075141089037061\n    }, {\n      \"x\": 0.5293324589729309,\n      \"y\": 0.8762547969818115,\n      \"z\": 0.00039177737198770046\n    }, {\n      \"x\": 0.5275698900222778,\n      \"y\": 0.8750609755516052,\n      \"z\": 0.000047732755774632096\n    }, {\n      \"x\": 0.5104271173477173,\n      \"y\": 0.8607332110404968,\n      \"z\": 0.0012934643309563398\n    }, {\n      \"x\": 0.45938700437545776,\n      \"y\": 0.8134918212890625,\n      \"z\": 0.023569690063595772\n    }, {\n      \"x\": 0.5418947339057922,\n      \"y\": 0.6864100694656372,\n      \"z\": -0.027333909645676613\n    }, {\n      \"x\": 0.531914234161377,\n      \"y\": 0.6456130743026733,\n      \"z\": -0.005434140563011169\n    }, {\n      \"x\": 0.523697018623352,\n      \"y\": 0.647885262966156,\n      \"z\": -0.0002466466394253075\n    }, {\n      \"x\": 0.5338191390037537,\n      \"y\": 0.8783687353134155,\n      \"z\": 0.002268768846988678\n    }, {\n      \"x\": 0.46226605772972107,\n      \"y\": 0.8610277771949768,\n      \"z\": 0.04718952998518944\n    }, {\n      \"x\": 0.5434442758560181,\n      \"y\": 0.6456181406974792,\n      \"z\": -0.02327350154519081\n    }, {\n      \"x\": 0.5399754643440247,\n      \"y\": 0.940219521522522,\n      \"z\": 0.005075343884527683\n    }, {\n      \"x\": 0.5661457777023315,\n      \"y\": 0.71457839012146,\n      \"z\": -0.06242101639509201\n    }, {\n      \"x\": 0.5523148775100708,\n      \"y\": 0.6974870562553406,\n      \"z\": -0.04863070324063301\n    }, {\n      \"x\": 0.5639959573745728,\n      \"y\": 0.6923378109931946,\n      \"z\": -0.05180761218070984\n    }, {\n      \"x\": 0.5367592573165894,\n      \"y\": 0.7423217296600342,\n      \"z\": -0.03623027727007866\n    }, {\n      \"x\": 0.5853689908981323,\n      \"y\": 0.9752064943313599,\n      \"z\": -0.002361974213272333\n    }, {\n      \"x\": 0.5835235118865967,\n      \"y\": 0.9493685960769653,\n      \"z\": -0.003941743168979883\n    }, {\n      \"x\": 0.5615018606185913,\n      \"y\": 0.949194610118866,\n      \"z\": -0.0015953965485095978\n    }, {\n      \"x\": 0.5068561434745789,\n      \"y\": 0.9048219323158264,\n      \"z\": 0.01862684078514576\n    }, {\n      \"x\": 0.5134067535400391,\n      \"y\": 0.7971825003623962,\n      \"z\": -0.008485661819577217\n    }, {\n      \"x\": 0.5223897099494934,\n      \"y\": 0.925589919090271,\n      \"z\": 0.01249657291918993\n    }, {\n      \"x\": 0.48500555753707886,\n      \"y\": 0.7959478497505188,\n      \"z\": -0.0032065745908766985\n    }, {\n      \"x\": 0.5037734508514404,\n      \"y\": 0.8184596300125122,\n      \"z\": -0.004932103678584099\n    }, {\n      \"x\": 0.4766361117362976,\n      \"y\": 0.828806459903717,\n      \"z\": 0.01027688942849636\n    }, {\n      \"x\": 0.5589827299118042,\n      \"y\": 0.974656343460083,\n      \"z\": 0.0009666886180639267\n    }, {\n      \"x\": 0.5294582843780518,\n      \"y\": 0.7541216611862183,\n      \"z\": -0.025603046640753746\n    }, {\n      \"x\": 0.4973002076148987,\n      \"y\": 0.9208990931510925,\n      \"z\": 0.031931452453136444\n    }, {\n      \"x\": 0.5163551568984985,\n      \"y\": 0.9432790875434875,\n      \"z\": 0.024321340024471283\n    }, {\n      \"x\": 0.49399662017822266,\n      \"y\": 0.8814862370491028,\n      \"z\": 0.018687399104237556\n    }, {\n      \"x\": 0.44948166608810425,\n      \"y\": 0.836137592792511,\n      \"z\": 0.05702034756541252\n    }, {\n      \"x\": 0.47898444533348083,\n      \"y\": 0.8836610913276672,\n      \"z\": 0.03150695189833641\n    }, {\n      \"x\": 0.4454479217529297,\n      \"y\": 0.8499438166618347,\n      \"z\": 0.08868525922298431\n    }, {\n      \"x\": 0.49572959542274475,\n      \"y\": 0.8452823758125305,\n      \"z\": 0.0036111653316766024\n    }, {\n      \"x\": 0.5362502336502075,\n      \"y\": 0.7222585678100586,\n      \"z\": -0.027912352234125137\n    }, {\n      \"x\": 0.5393770337104797,\n      \"y\": 0.7850722074508667,\n      \"z\": -0.05415399745106697\n    }, {\n      \"x\": 0.531399667263031,\n      \"y\": 0.7898418307304382,\n      \"z\": -0.03883346915245056\n    }, {\n      \"x\": 0.5451627373695374,\n      \"y\": 0.7717036604881287,\n      \"z\": -0.06480253487825394\n    }, {\n      \"x\": 0.5206395983695984,\n      \"y\": 0.6287745833396912,\n      \"z\": -0.010521138086915016\n    }, {\n      \"x\": 0.4974782466888428,\n      \"y\": 0.6191938519477844,\n      \"z\": -0.014098240062594414\n    }, {\n      \"x\": 0.4774145185947418,\n      \"y\": 0.6193130612373352,\n      \"z\": -0.013643337413668633\n    }, {\n      \"x\": 0.4616098403930664,\n      \"y\": 0.6259890198707581,\n      \"z\": -0.008448202162981033\n    }, {\n      \"x\": 0.4516478478908539,\n      \"y\": 0.6368461847305298,\n      \"z\": 0.00009050309745362028\n    }, {\n      \"x\": 0.4485096037387848,\n      \"y\": 0.6719120740890503,\n      \"z\": 0.022984720766544342\n    }, {\n      \"x\": 0.42177659273147583,\n      \"y\": 0.7240667343139648,\n      \"z\": 0.08511673659086227\n    }, {\n      \"x\": 0.4616215229034424,\n      \"y\": 0.6988231539726257,\n      \"z\": 0.014238474890589714\n    }, {\n      \"x\": 0.4755798876285553,\n      \"y\": 0.7034608721733093,\n      \"z\": 0.00625590980052948\n    }, {\n      \"x\": 0.4924992024898529,\n      \"y\": 0.7005885243415833,\n      \"z\": 0.0009391739731654525\n    }, {\n      \"x\": 0.5082254409790039,\n      \"y\": 0.693384051322937,\n      \"z\": -0.0009464038303121924\n    }, {\n      \"x\": 0.5203112959861755,\n      \"y\": 0.6849707961082458,\n      \"z\": -0.0022114769089967012\n    }, {\n      \"x\": 0.52867591381073,\n      \"y\": 0.6779075860977173,\n      \"z\": -0.002962538506835699\n    }, {\n      \"x\": 0.4213953912258148,\n      \"y\": 0.7219811677932739,\n      \"z\": 0.1350894570350647\n    }, {\n      \"x\": 0.5320829749107361,\n      \"y\": 0.794858992099762,\n      \"z\": -0.03181503340601921\n    }, {\n      \"x\": 0.5452795028686523,\n      \"y\": 0.7286570072174072,\n      \"z\": -0.04771539941430092\n    }, {\n      \"x\": 0.5496407747268677,\n      \"y\": 0.7866933345794678,\n      \"z\": -0.06452003121376038\n    }, {\n      \"x\": 0.557040274143219,\n      \"y\": 0.7962084412574768,\n      \"z\": -0.05837344378232956\n    }, {\n      \"x\": 0.549176812171936,\n      \"y\": 0.7895247936248779,\n      \"z\": -0.057761140167713165\n    }, {\n      \"x\": 0.5362890362739563,\n      \"y\": 0.8005836606025696,\n      \"z\": -0.026903774589300156\n    }, {\n      \"x\": 0.560200035572052,\n      \"y\": 0.7983731031417847,\n      \"z\": -0.06172555685043335\n    }, {\n      \"x\": 0.5616944432258606,\n      \"y\": 0.8022753596305847,\n      \"z\": -0.045200999826192856\n    }, {\n      \"x\": 0.5273328423500061,\n      \"y\": 0.6611284017562866,\n      \"z\": 0.0029021520167589188\n    }, {\n      \"x\": 0.534850537776947,\n      \"y\": 0.6660012006759644,\n      \"z\": -0.005215510260313749\n    }, {\n      \"x\": 0.5394860506057739,\n      \"y\": 0.6701375246047974,\n      \"z\": -0.014931917190551758\n    }, {\n      \"x\": 0.4634307324886322,\n      \"y\": 0.658291757106781,\n      \"z\": 0.009295716881752014\n    }, {\n      \"x\": 0.4538393020629883,\n      \"y\": 0.6519932150840759,\n      \"z\": 0.00930330716073513\n    }, {\n      \"x\": 0.5776031613349915,\n      \"y\": 0.7159298658370972,\n      \"z\": -0.057365912944078445\n    }, {\n      \"x\": 0.6504855155944824,\n      \"y\": 0.6461779475212097,\n      \"z\": 0.014184834435582161\n    }, {\n      \"x\": 0.5860154032707214,\n      \"y\": 0.7962266206741333,\n      \"z\": -0.04522843658924103\n    }, {\n      \"x\": 0.6842049360275269,\n      \"y\": 0.5631637573242188,\n      \"z\": 0.07207967340946198\n    }, {\n      \"x\": 0.6152560710906982,\n      \"y\": 0.6674962639808655,\n      \"z\": 0.0007529259892180562\n    }, {\n      \"x\": 0.6280948519706726,\n      \"y\": 0.6684326529502869,\n      \"z\": 0.0016892586136236787\n    }, {\n      \"x\": 0.6408625245094299,\n      \"y\": 0.6663892269134521,\n      \"z\": 0.005331226624548435\n    }, {\n      \"x\": 0.6557814478874207,\n      \"y\": 0.6534678936004639,\n      \"z\": 0.01646413467824459\n    }, {\n      \"x\": 0.6035663485527039,\n      \"y\": 0.6639701724052429,\n      \"z\": 0.0013799630105495453\n    }, {\n      \"x\": 0.6329053044319153,\n      \"y\": 0.608010470867157,\n      \"z\": -0.006195899099111557\n    }, {\n      \"x\": 0.6167260408401489,\n      \"y\": 0.6117533445358276,\n      \"z\": -0.006319951266050339\n    }, {\n      \"x\": 0.6471013426780701,\n      \"y\": 0.6112449765205383,\n      \"z\": -0.0017843559617176652\n    }, {\n      \"x\": 0.6560901999473572,\n      \"y\": 0.6185776591300964,\n      \"z\": 0.004047257360070944\n    }, {\n      \"x\": 0.6666946411132812,\n      \"y\": 0.6651176810264587,\n      \"z\": 0.023647578433156013\n    }, {\n      \"x\": 0.6311345100402832,\n      \"y\": 0.9495396018028259,\n      \"z\": 0.014004078693687916\n    }, {\n      \"x\": 0.6544655561447144,\n      \"y\": 0.6397901773452759,\n      \"z\": 0.01809609681367874\n    }, {\n      \"x\": 0.6965808868408203,\n      \"y\": 0.6482675075531006,\n      \"z\": 0.08304904401302338\n    }, {\n      \"x\": 0.679817259311676,\n      \"y\": 0.650188148021698,\n      \"z\": 0.03632688894867897\n    }, {\n      \"x\": 0.6336516737937927,\n      \"y\": 0.7541458010673523,\n      \"z\": -0.007742783520370722\n    }, {\n      \"x\": 0.5921701192855835,\n      \"y\": 0.8567668199539185,\n      \"z\": -0.029399123042821884\n    }, {\n      \"x\": 0.591663658618927,\n      \"y\": 0.870215654373169,\n      \"z\": -0.02103729173541069\n    }, {\n      \"x\": 0.6068367958068848,\n      \"y\": 0.8584195375442505,\n      \"z\": -0.020668085664510727\n    }, {\n      \"x\": 0.6176617741584778,\n      \"y\": 0.860965371131897,\n      \"z\": -0.009790095500648022\n    }, {\n      \"x\": 0.6040634512901306,\n      \"y\": 0.8686612844467163,\n      \"z\": -0.015289564616978168\n    }, {\n      \"x\": 0.6143736839294434,\n      \"y\": 0.8671170473098755,\n      \"z\": -0.005712216719985008\n    }, {\n      \"x\": 0.6373105049133301,\n      \"y\": 0.8815656900405884,\n      \"z\": 0.012672550976276398\n    }, {\n      \"x\": 0.5832505822181702,\n      \"y\": 0.7866312861442566,\n      \"z\": -0.07051534950733185\n    }, {\n      \"x\": 0.5836675763130188,\n      \"y\": 0.7658692598342896,\n      \"z\": -0.07566110789775848\n    }, {\n      \"x\": 0.6709531545639038,\n      \"y\": 0.604898989200592,\n      \"z\": 0.005951565690338612\n    }, {\n      \"x\": 0.6029891967773438,\n      \"y\": 0.705652117729187,\n      \"z\": -0.013388276100158691\n    }, {\n      \"x\": 0.6131622195243835,\n      \"y\": 0.7728396058082581,\n      \"z\": -0.036248479038476944\n    }, {\n      \"x\": 0.6123163104057312,\n      \"y\": 0.7612020373344421,\n      \"z\": -0.03264721855521202\n    }, {\n      \"x\": 0.6696187853813171,\n      \"y\": 0.744706928730011,\n      \"z\": 0.009673702530562878\n    }, {\n      \"x\": 0.5803102254867554,\n      \"y\": 0.7385968565940857,\n      \"z\": -0.0689152330160141\n    }, {\n      \"x\": 0.6404349207878113,\n      \"y\": 0.5877999663352966,\n      \"z\": -0.01929756999015808\n    }, {\n      \"x\": 0.6588467955589294,\n      \"y\": 0.5929454565048218,\n      \"z\": -0.008487257175147533\n    }, {\n      \"x\": 0.6720337867736816,\n      \"y\": 0.530631422996521,\n      \"z\": 0.043437421321868896\n    }, {\n      \"x\": 0.584305465221405,\n      \"y\": 0.6099005341529846,\n      \"z\": -0.030301367864012718\n    }, {\n      \"x\": 0.6034283638000488,\n      \"y\": 0.6217452883720398,\n      \"z\": -0.001970183802768588\n    }, {\n      \"x\": 0.6460927724838257,\n      \"y\": 0.8608663082122803,\n      \"z\": 0.015541625209152699\n    }, {\n      \"x\": 0.6957815289497375,\n      \"y\": 0.8326103091239929,\n      \"z\": 0.13015234470367432\n    }, {\n      \"x\": 0.6043362617492676,\n      \"y\": 0.7861682772636414,\n      \"z\": -0.030476901680231094\n    }, {\n      \"x\": 0.594293475151062,\n      \"y\": 0.7942103147506714,\n      \"z\": -0.032218821346759796\n    }, {\n      \"x\": 0.6324057579040527,\n      \"y\": 0.8665139675140381,\n      \"z\": 0.014255806803703308\n    }, {\n      \"x\": 0.6296147704124451,\n      \"y\": 0.8667733669281006,\n      \"z\": 0.010388285852968693\n    }, {\n      \"x\": 0.663644552230835,\n      \"y\": 0.5798642635345459,\n      \"z\": -0.0022301070857793093\n    }, {\n      \"x\": 0.6140630841255188,\n      \"y\": 0.7809288501739502,\n      \"z\": -0.02835679054260254\n    }, {\n      \"x\": 0.615908145904541,\n      \"y\": 0.5921698212623596,\n      \"z\": -0.026804860681295395\n    }, {\n      \"x\": 0.617181122303009,\n      \"y\": 0.5748661756515503,\n      \"z\": -0.03060605563223362\n    }, {\n      \"x\": 0.6222207546234131,\n      \"y\": 0.49137672781944275,\n      \"z\": -0.011151673272252083\n    }, {\n      \"x\": 0.6669357419013977,\n      \"y\": 0.5541607141494751,\n      \"z\": 0.017466170713305473\n    }, {\n      \"x\": 0.6182981729507446,\n      \"y\": 0.5320425629615784,\n      \"z\": -0.021793590858578682\n    }, {\n      \"x\": 0.6760554313659668,\n      \"y\": 0.595052182674408,\n      \"z\": 0.017115700989961624\n    }, {\n      \"x\": 0.6801463961601257,\n      \"y\": 0.5800720453262329,\n      \"z\": 0.043127160519361496\n    }, {\n      \"x\": 0.5922210812568665,\n      \"y\": 0.8644017577171326,\n      \"z\": -0.02662893570959568\n    }, {\n      \"x\": 0.6054555177688599,\n      \"y\": 0.8637874722480774,\n      \"z\": -0.018363753333687782\n    }, {\n      \"x\": 0.6161889433860779,\n      \"y\": 0.8641164898872375,\n      \"z\": -0.008808949030935764\n    }, {\n      \"x\": 0.6017249822616577,\n      \"y\": 0.7901403307914734,\n      \"z\": -0.028126630932092667\n    }, {\n      \"x\": 0.631446123123169,\n      \"y\": 0.8664817810058594,\n      \"z\": 0.012112865224480629\n    }, {\n      \"x\": 0.6249198913574219,\n      \"y\": 0.8716511130332947,\n      \"z\": 0.003882825840264559\n    }, {\n      \"x\": 0.6281915903091431,\n      \"y\": 0.867301881313324,\n      \"z\": 0.009891441091895103\n    }, {\n      \"x\": 0.5986843109130859,\n      \"y\": 0.7813931703567505,\n      \"z\": -0.050227612257003784\n    }, {\n      \"x\": 0.6126407384872437,\n      \"y\": 0.869275689125061,\n      \"z\": -0.0031255714129656553\n    }, {\n      \"x\": 0.6027271151542664,\n      \"y\": 0.8711842894554138,\n      \"z\": -0.009324162267148495\n    }, {\n      \"x\": 0.59088134765625,\n      \"y\": 0.8742044568061829,\n      \"z\": -0.014608660712838173\n    }, {\n      \"x\": 0.5984604358673096,\n      \"y\": 0.9216185212135315,\n      \"z\": -0.005981989670544863\n    }, {\n      \"x\": 0.5950398445129395,\n      \"y\": 0.8964707255363464,\n      \"z\": -0.01703473925590515\n    }, {\n      \"x\": 0.5941568613052368,\n      \"y\": 0.8882410526275635,\n      \"z\": -0.017784785479307175\n    }, {\n      \"x\": 0.5928806662559509,\n      \"y\": 0.8803883194923401,\n      \"z\": -0.014153128489851952\n    }, {\n      \"x\": 0.5909661054611206,\n      \"y\": 0.8748103976249695,\n      \"z\": -0.012609979137778282\n    }, {\n      \"x\": 0.6128016710281372,\n      \"y\": 0.8702545762062073,\n      \"z\": -0.0022550546564161777\n    }, {\n      \"x\": 0.6150846481323242,\n      \"y\": 0.8726804256439209,\n      \"z\": -0.00414019962772727\n    }, {\n      \"x\": 0.6173093914985657,\n      \"y\": 0.8770190477371216,\n      \"z\": -0.005970994010567665\n    }, {\n      \"x\": 0.619335412979126,\n      \"y\": 0.8814800977706909,\n      \"z\": -0.0036864024586975574\n    }, {\n      \"x\": 0.6292637586593628,\n      \"y\": 0.8314558267593384,\n      \"z\": -0.007714875973761082\n    }, {\n      \"x\": 0.702275276184082,\n      \"y\": 0.7320667505264282,\n      \"z\": 0.1433621346950531\n    }, {\n      \"x\": 0.6204835176467896,\n      \"y\": 0.8689177632331848,\n      \"z\": 0.0044869170524179935\n    }, {\n      \"x\": 0.6223508715629578,\n      \"y\": 0.8704851269721985,\n      \"z\": 0.00352082890458405\n    }, {\n      \"x\": 0.590448260307312,\n      \"y\": 0.8029727935791016,\n      \"z\": -0.03200828656554222\n    }, {\n      \"x\": 0.6097423434257507,\n      \"y\": 0.7933741211891174,\n      \"z\": -0.018042555078864098\n    }, {\n      \"x\": 0.59229576587677,\n      \"y\": 0.7993767261505127,\n      \"z\": -0.032564569264650345\n    }, {\n      \"x\": 0.6171364188194275,\n      \"y\": 0.7153720259666443,\n      \"z\": -0.007672437466681004\n    }, {\n      \"x\": 0.6389747858047485,\n      \"y\": 0.726390540599823,\n      \"z\": -0.002999067772179842\n    }, {\n      \"x\": 0.6151940226554871,\n      \"y\": 0.769412100315094,\n      \"z\": -0.024427521973848343\n    }, {\n      \"x\": 0.6526776552200317,\n      \"y\": 0.505868136882782,\n      \"z\": 0.01412637997418642\n    }, {\n      \"x\": 0.6475822329521179,\n      \"y\": 0.5375454425811768,\n      \"z\": -0.0033899128902703524\n    }, {\n      \"x\": 0.6433356404304504,\n      \"y\": 0.5714520215988159,\n      \"z\": -0.017428796738386154\n    }, {\n      \"x\": 0.626949667930603,\n      \"y\": 0.8962116837501526,\n      \"z\": 0.005602736957371235\n    }, {\n      \"x\": 0.5868416428565979,\n      \"y\": 0.5829002261161804,\n      \"z\": -0.03727729618549347\n    }, {\n      \"x\": 0.5877229571342468,\n      \"y\": 0.5345035791397095,\n      \"z\": -0.032396964728832245\n    }, {\n      \"x\": 0.5887066125869751,\n      \"y\": 0.48655083775520325,\n      \"z\": -0.025856535881757736\n    }, {\n      \"x\": 0.6507197618484497,\n      \"y\": 0.6612282991409302,\n      \"z\": 0.011114613153040409\n    }, {\n      \"x\": 0.6803066730499268,\n      \"y\": 0.677992045879364,\n      \"z\": 0.032125361263751984\n    }, {\n      \"x\": 0.5963194370269775,\n      \"y\": 0.6598632335662842,\n      \"z\": 0.002976928371936083\n    }, {\n      \"x\": 0.667536199092865,\n      \"y\": 0.6274255514144897,\n      \"z\": 0.015618261881172657\n    }, {\n      \"x\": 0.5930740833282471,\n      \"y\": 0.6940041780471802,\n      \"z\": -0.019217798486351967\n    }, {\n      \"x\": 0.6053346395492554,\n      \"y\": 0.7676517963409424,\n      \"z\": -0.050308309495449066\n    }, {\n      \"x\": 0.6934473514556885,\n      \"y\": 0.6884298920631409,\n      \"z\": 0.04794462397694588\n    }, {\n      \"x\": 0.6738007664680481,\n      \"y\": 0.6934011578559875,\n      \"z\": 0.020697161555290222\n    }, {\n      \"x\": 0.6588084697723389,\n      \"y\": 0.7033141851425171,\n      \"z\": 0.008462334051728249\n    }, {\n      \"x\": 0.6346072554588318,\n      \"y\": 0.7029502391815186,\n      \"z\": 0.001542167621664703\n    }, {\n      \"x\": 0.6157816648483276,\n      \"y\": 0.6966525912284851,\n      \"z\": -0.002009218093007803\n    }, {\n      \"x\": 0.6015574336051941,\n      \"y\": 0.688928484916687,\n      \"z\": -0.006588225718587637\n    }, {\n      \"x\": 0.5746836066246033,\n      \"y\": 0.6711069345474243,\n      \"z\": -0.03597589209675789\n    }, {\n      \"x\": 0.6947521567344666,\n      \"y\": 0.7309479117393494,\n      \"z\": 0.046707939356565475\n    }, {\n      \"x\": 0.6759101152420044,\n      \"y\": 0.6249120831489563,\n      \"z\": 0.021654341369867325\n    }, {\n      \"x\": 0.5794773101806641,\n      \"y\": 0.7971615195274353,\n      \"z\": -0.06339326500892639\n    }, {\n      \"x\": 0.6041849851608276,\n      \"y\": 0.727514922618866,\n      \"z\": -0.017512541264295578\n    }, {\n      \"x\": 0.6968844532966614,\n      \"y\": 0.6440950036048889,\n      \"z\": 0.12727996706962585\n    }, {\n      \"x\": 0.5910853147506714,\n      \"y\": 0.679325520992279,\n      \"z\": -0.009497715160250664\n    }, {\n      \"x\": 0.6157375574111938,\n      \"y\": 0.7695677280426025,\n      \"z\": -0.010624290443956852\n    }, {\n      \"x\": 0.6606494784355164,\n      \"y\": 0.6410489678382874,\n      \"z\": 0.0208158977329731\n    }, {\n      \"x\": 0.6040687561035156,\n      \"y\": 0.7531470656394958,\n      \"z\": -0.045887019485235214\n    }, {\n      \"x\": 0.7012156248092651,\n      \"y\": 0.780247151851654,\n      \"z\": 0.14028730988502502\n    }, {\n      \"x\": 0.595149576663971,\n      \"y\": 0.6527782678604126,\n      \"z\": 0.006308757700026035\n    }, {\n      \"x\": 0.5925500392913818,\n      \"y\": 0.7436665892601013,\n      \"z\": -0.060151755809783936\n    }, {\n      \"x\": 0.6780198812484741,\n      \"y\": 0.8905693888664246,\n      \"z\": 0.0626060739159584\n    }, {\n      \"x\": 0.676746666431427,\n      \"y\": 0.9113880395889282,\n      \"z\": 0.08726003766059875\n    }, {\n      \"x\": 0.7030686140060425,\n      \"y\": 0.7312687635421753,\n      \"z\": 0.09529774636030197\n    }, {\n      \"x\": 0.688987135887146,\n      \"y\": 0.8588417172431946,\n      \"z\": 0.07752864807844162\n    }, {\n      \"x\": 0.6883691549301147,\n      \"y\": 0.6109960675239563,\n      \"z\": 0.06669612973928452\n    }, {\n      \"x\": 0.6358906030654907,\n      \"y\": 0.9702065587043762,\n      \"z\": 0.023120900616049767\n    }, {\n      \"x\": 0.5781539678573608,\n      \"y\": 0.8023634552955627,\n      \"z\": -0.044763918966054916\n    }, {\n      \"x\": 0.6170316934585571,\n      \"y\": 0.7408350706100464,\n      \"z\": -0.011375460773706436\n    }, {\n      \"x\": 0.688542366027832,\n      \"y\": 0.6516284346580505,\n      \"z\": 0.050206027925014496\n    }, {\n      \"x\": 0.6385149359703064,\n      \"y\": 0.6540714502334595,\n      \"z\": 0.006462941411882639\n    }, {\n      \"x\": 0.6279382109642029,\n      \"y\": 0.6563615798950195,\n      \"z\": 0.003062846139073372\n    }, {\n      \"x\": 0.6268895268440247,\n      \"y\": 0.8736732006072998,\n      \"z\": 0.00627936702221632\n    }, {\n      \"x\": 0.6944946050643921,\n      \"y\": 0.7709181308746338,\n      \"z\": 0.053824134171009064\n    }, {\n      \"x\": 0.614617109298706,\n      \"y\": 1.0022112131118774,\n      \"z\": 0.02719894051551819\n    }, {\n      \"x\": 0.6493719220161438,\n      \"y\": 0.9665167927742004,\n      \"z\": 0.053563784807920456\n    }, {\n      \"x\": 0.6624587178230286,\n      \"y\": 0.943530797958374,\n      \"z\": 0.068605437874794\n    }, {\n      \"x\": 0.6162528991699219,\n      \"y\": 0.6558693051338196,\n      \"z\": 0.002187855076044798\n    }, {\n      \"x\": 0.6058168411254883,\n      \"y\": 0.654328465461731,\n      \"z\": 0.0036193584091961384\n    }, {\n      \"x\": 0.5987918972969055,\n      \"y\": 0.6536934971809387,\n      \"z\": 0.006134530063718557\n    }, {\n      \"x\": 0.6831037402153015,\n      \"y\": 0.6195642948150635,\n      \"z\": 0.03511790186166763\n    }, {\n      \"x\": 0.6062582731246948,\n      \"y\": 0.6356398463249207,\n      \"z\": 0.001280312892049551\n    }, {\n      \"x\": 0.6174948811531067,\n      \"y\": 0.62776118516922,\n      \"z\": -0.0013642468256875873\n    }, {\n      \"x\": 0.6297246217727661,\n      \"y\": 0.6253792643547058,\n      \"z\": -0.0007034156005829573\n    }, {\n      \"x\": 0.6407091617584229,\n      \"y\": 0.627578616142273,\n      \"z\": 0.0028144705574959517\n    }, {\n      \"x\": 0.6479622721672058,\n      \"y\": 0.6322650909423828,\n      \"z\": 0.00750273372977972\n    }, {\n      \"x\": 0.6915091276168823,\n      \"y\": 0.5990704298019409,\n      \"z\": 0.10270945727825165\n    }, {\n      \"x\": 0.6457163095474243,\n      \"y\": 0.6504453420639038,\n      \"z\": 0.010696077719330788\n    }, {\n      \"x\": 0.6164222955703735,\n      \"y\": 0.8231936097145081,\n      \"z\": -0.016772059723734856\n    }, {\n      \"x\": 0.6042401194572449,\n      \"y\": 0.7830976843833923,\n      \"z\": -0.03630910441279411\n    }, {\n      \"x\": 0.5922216773033142,\n      \"y\": 0.8228387236595154,\n      \"z\": -0.029992375522851944\n    }, {\n      \"x\": 0.6646111011505127,\n      \"y\": 0.92097008228302,\n      \"z\": 0.050967294722795486\n    }, {\n      \"x\": 0.651232898235321,\n      \"y\": 0.9460107088088989,\n      \"z\": 0.038000158965587616\n    }, {\n      \"x\": 0.6140977144241333,\n      \"y\": 0.9882472157478333,\n      \"z\": 0.009882091544568539\n    }, {\n      \"x\": 0.6870781183242798,\n      \"y\": 0.8768675327301025,\n      \"z\": 0.10980932414531708\n    }, {\n      \"x\": 0.5986856818199158,\n      \"y\": 0.6456438899040222,\n      \"z\": 0.003999010659754276\n    }, {\n      \"x\": 0.585981547832489,\n      \"y\": 0.7034481763839722,\n      \"z\": -0.0377722829580307\n    }, {\n      \"x\": 0.6342031359672546,\n      \"y\": 0.9867448806762695,\n      \"z\": 0.03786521404981613\n    }, {\n      \"x\": 0.7013950943946838,\n      \"y\": 0.776049017906189,\n      \"z\": 0.09598205983638763\n    }, {\n      \"x\": 0.6030206680297852,\n      \"y\": 0.8719133138656616,\n      \"z\": -0.007931148633360863\n    }, {\n      \"x\": 0.6050592064857483,\n      \"y\": 0.8767156004905701,\n      \"z\": -0.009791925549507141\n    }, {\n      \"x\": 0.6073468923568726,\n      \"y\": 0.8831382393836975,\n      \"z\": -0.012361008673906326\n    }, {\n      \"x\": 0.6087977290153503,\n      \"y\": 0.890143632888794,\n      \"z\": -0.01098148338496685\n    }, {\n      \"x\": 0.6147705316543579,\n      \"y\": 0.9110084772109985,\n      \"z\": -0.0018823575228452682\n    }, {\n      \"x\": 0.622577965259552,\n      \"y\": 0.8670604825019836,\n      \"z\": 0.002609190298244357\n    }, {\n      \"x\": 0.6241236329078674,\n      \"y\": 0.8651344180107117,\n      \"z\": 0.0025534380692988634\n    }, {\n      \"x\": 0.6257084608078003,\n      \"y\": 0.8638408184051514,\n      \"z\": 0.0023300074972212315\n    }, {\n      \"x\": 0.639931321144104,\n      \"y\": 0.8449671268463135,\n      \"z\": 0.0038123116828501225\n    }, {\n      \"x\": 0.6810906529426575,\n      \"y\": 0.7856625318527222,\n      \"z\": 0.02717764675617218\n    }, {\n      \"x\": 0.583532452583313,\n      \"y\": 0.6811994910240173,\n      \"z\": -0.026588857173919678\n    }, {\n      \"x\": 0.5855660438537598,\n      \"y\": 0.6393819451332092,\n      \"z\": -0.004512844607234001\n    }, {\n      \"x\": 0.5932201743125916,\n      \"y\": 0.6398029327392578,\n      \"z\": 0.0008020466193556786\n    }, {\n      \"x\": 0.6200879812240601,\n      \"y\": 0.8683351874351501,\n      \"z\": 0.00417016725987196\n    }, {\n      \"x\": 0.6842559576034546,\n      \"y\": 0.8330534100532532,\n      \"z\": 0.050836317241191864\n    }, {\n      \"x\": 0.5754412412643433,\n      \"y\": 0.6418221592903137,\n      \"z\": -0.022838059812784195\n    }, {\n      \"x\": 0.6232790350914001,\n      \"y\": 0.9295297265052795,\n      \"z\": 0.006339520215988159\n    }, {\n      \"x\": 0.5764067769050598,\n      \"y\": 0.694546639919281,\n      \"z\": -0.04825803264975548\n    }, {\n      \"x\": 0.59778892993927,\n      \"y\": 0.7343927621841431,\n      \"z\": -0.035004377365112305\n    }, {\n      \"x\": 0.6042810678482056,\n      \"y\": 0.9441440105438232,\n      \"z\": -0.0010970570147037506\n    }, {\n      \"x\": 0.6496372222900391,\n      \"y\": 0.8869078159332275,\n      \"z\": 0.021036235615611076\n    }, {\n      \"x\": 0.6274012327194214,\n      \"y\": 0.7830310463905334,\n      \"z\": -0.006658440921455622\n    }, {\n      \"x\": 0.637792706489563,\n      \"y\": 0.9104999899864197,\n      \"z\": 0.014290250837802887\n    }, {\n      \"x\": 0.6549934148788452,\n      \"y\": 0.7748609185218811,\n      \"z\": -0.0006672973395325243\n    }, {\n      \"x\": 0.6404005289077759,\n      \"y\": 0.801220715045929,\n      \"z\": -0.0026642554439604282\n    }, {\n      \"x\": 0.6671456694602966,\n      \"y\": 0.8045546412467957,\n      \"z\": 0.013180811889469624\n    }, {\n      \"x\": 0.6107483506202698,\n      \"y\": 0.9680658578872681,\n      \"z\": 0.001778992242179811\n    }, {\n      \"x\": 0.6060343980789185,\n      \"y\": 0.744587242603302,\n      \"z\": -0.024382334202528\n    }, {\n      \"x\": 0.6602751612663269,\n      \"y\": 0.8998945355415344,\n      \"z\": 0.0344940721988678\n    }, {\n      \"x\": 0.6463775038719177,\n      \"y\": 0.9262562394142151,\n      \"z\": 0.02617623284459114\n    }, {\n      \"x\": 0.6579852104187012,\n      \"y\": 0.8602304458618164,\n      \"z\": 0.021586716175079346\n    }, {\n      \"x\": 0.6926165223121643,\n      \"y\": 0.8053340315818787,\n      \"z\": 0.061075080186128616\n    }, {\n      \"x\": 0.6724731922149658,\n      \"y\": 0.8594399690628052,\n      \"z\": 0.03457934781908989\n    }, {\n      \"x\": 0.6975721716880798,\n      \"y\": 0.8183245062828064,\n      \"z\": 0.09300774335861206\n    }, {\n      \"x\": 0.6512877941131592,\n      \"y\": 0.8258221745491028,\n      \"z\": 0.006324059329926968\n    }, {\n      \"x\": 0.594887375831604,\n      \"y\": 0.7148372530937195,\n      \"z\": -0.026898479089140892\n    }, {\n      \"x\": 0.6017440557479858,\n      \"y\": 0.7773507833480835,\n      \"z\": -0.05312420800328255\n    }, {\n      \"x\": 0.6096571683883667,\n      \"y\": 0.7806998491287231,\n      \"z\": -0.037646256387233734\n    }, {\n      \"x\": 0.5952993035316467,\n      \"y\": 0.7654367685317993,\n      \"z\": -0.06398405134677887\n    }, {\n      \"x\": 0.5950021147727966,\n      \"y\": 0.6201304793357849,\n      \"z\": -0.009297547861933708\n    }, {\n      \"x\": 0.6165438890457153,\n      \"y\": 0.6052900552749634,\n      \"z\": -0.012455573305487633\n    }, {\n      \"x\": 0.6362661719322205,\n      \"y\": 0.6015968918800354,\n      \"z\": -0.011649220250546932\n    }, {\n      \"x\": 0.6522727608680725,\n      \"y\": 0.6046400666236877,\n      \"z\": -0.005903332494199276\n    }, {\n      \"x\": 0.6625409722328186,\n      \"y\": 0.6128141283988953,\n      \"z\": 0.0030042496509850025\n    }, {\n      \"x\": 0.6688099503517151,\n      \"y\": 0.6457712054252625,\n      \"z\": 0.026322703808546066\n    }, {\n      \"x\": 0.7013440728187561,\n      \"y\": 0.6893666386604309,\n      \"z\": 0.08984331786632538\n    }, {\n      \"x\": 0.6608623266220093,\n      \"y\": 0.6749406456947327,\n      \"z\": 0.0172116681933403\n    }, {\n      \"x\": 0.6482325196266174,\n      \"y\": 0.6823726296424866,\n      \"z\": 0.008881398476660252\n    }, {\n      \"x\": 0.6313265562057495,\n      \"y\": 0.6842025518417358,\n      \"z\": 0.0031308617908507586\n    }, {\n      \"x\": 0.6147016286849976,\n      \"y\": 0.6809731721878052,\n      \"z\": 0.0007630771724507213\n    }, {\n      \"x\": 0.6018834114074707,\n      \"y\": 0.6755372285842896,\n      \"z\": -0.0008834321051836014\n    }, {\n      \"x\": 0.5925027132034302,\n      \"y\": 0.670681357383728,\n      \"z\": -0.001968748401850462\n    }, {\n      \"x\": 0.700127363204956,\n      \"y\": 0.6871103644371033,\n      \"z\": 0.13980500400066376\n    }, {\n      \"x\": 0.6095665693283081,\n      \"y\": 0.7853189706802368,\n      \"z\": -0.03074747882783413\n    }, {\n      \"x\": 0.5880423784255981,\n      \"y\": 0.7229287028312683,\n      \"z\": -0.04691500961780548\n    }, {\n      \"x\": 0.5930182337760925,\n      \"y\": 0.7811514139175415,\n      \"z\": -0.06398335844278336\n    }, {\n      \"x\": 0.5867722034454346,\n      \"y\": 0.7922660112380981,\n      \"z\": -0.05794971063733101\n    }, {\n      \"x\": 0.5933279991149902,\n      \"y\": 0.7842848896980286,\n      \"z\": -0.05714067071676254\n    }, {\n      \"x\": 0.6063535809516907,\n      \"y\": 0.7920218706130981,\n      \"z\": -0.02590685710310936\n    }, {\n      \"x\": 0.5839452743530273,\n      \"y\": 0.794978141784668,\n      \"z\": -0.0615212507545948\n    }, {\n      \"x\": 0.5828126072883606,\n      \"y\": 0.8000800013542175,\n      \"z\": -0.0449722595512867\n    }, {\n      \"x\": 0.5909603834152222,\n      \"y\": 0.6541213393211365,\n      \"z\": 0.003991890233010054\n    }, {\n      \"x\": 0.5852181911468506,\n      \"y\": 0.6602938771247864,\n      \"z\": -0.004428438376635313\n    }, {\n      \"x\": 0.5825737714767456,\n      \"y\": 0.6651063561439514,\n      \"z\": -0.014345290139317513\n    }, {\n      \"x\": 0.6517343521118164,\n      \"y\": 0.6362385153770447,\n      \"z\": 0.012151890434324741\n    }, {\n      \"x\": 0.6615052819252014,\n      \"y\": 0.6281577944755554,\n      \"z\": 0.0123682152479887\n    }, {\n      \"x\": 0.4856873154640198,\n      \"y\": 0.6568945646286011,\n      \"z\": 0.000720038078725338\n    }, {\n      \"x\": 0.49988406896591187,\n      \"y\": 0.6547410488128662,\n      \"z\": 0.0006949726957827806\n    }, {\n      \"x\": 0.48438939452171326,\n      \"y\": 0.6392973065376282,\n      \"z\": 0.000705525919329375\n    }, {\n      \"x\": 0.47143134474754333,\n      \"y\": 0.6589511632919312,\n      \"z\": 0.0006980331381782889\n    }, {\n      \"x\": 0.48704618215560913,\n      \"y\": 0.6752797961235046,\n      \"z\": 0.0006921177846379578\n    }, {\n      \"x\": 0.6243702173233032,\n      \"y\": 0.640461802482605,\n      \"z\": -0.00006592126737814397\n    }, {\n      \"x\": 0.6390967965126038,\n      \"y\": 0.6385173797607422,\n      \"z\": -0.00016105435497593135\n    }, {\n      \"x\": 0.6230536699295044,\n      \"y\": 0.6224825382232666,\n      \"z\": -0.00016136496560648084\n    }, {\n      \"x\": 0.6095397472381592,\n      \"y\": 0.641917884349823,\n      \"z\": -0.0001803556369850412\n    }, {\n      \"x\": 0.6250996589660645,\n      \"y\": 0.6586247682571411,\n      \"z\": -0.0001785515050869435\n    }]],\n    \"faceBlendshapes\": [{\n      \"categories\": [{\n        \"index\": 0,\n        \"score\": 0.000005187174338061595,\n        \"categoryName\": \"_neutral\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 1,\n        \"score\": 0.24521504342556,\n        \"categoryName\": \"browDownLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 2,\n        \"score\": 0.1987743377685547,\n        \"categoryName\": \"browDownRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 3,\n        \"score\": 0.013400448486208916,\n        \"categoryName\": \"browInnerUp\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 4,\n        \"score\": 0.012361560948193073,\n        \"categoryName\": \"browOuterUpLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 5,\n        \"score\": 0.019305096939206123,\n        \"categoryName\": \"browOuterUpRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 6,\n        \"score\": 0.000028426356948330067,\n        \"categoryName\": \"cheekPuff\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 7,\n        \"score\": 3.4500112633395474e-7,\n        \"categoryName\": \"cheekSquintLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 8,\n        \"score\": 4.83789051486383e-7,\n        \"categoryName\": \"cheekSquintRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 9,\n        \"score\": 0.07650448381900787,\n        \"categoryName\": \"eyeBlinkLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 10,\n        \"score\": 0.05070012807846069,\n        \"categoryName\": \"eyeBlinkRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 11,\n        \"score\": 0.13978900015354156,\n        \"categoryName\": \"eyeLookDownLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 12,\n        \"score\": 0.14198613166809082,\n        \"categoryName\": \"eyeLookDownRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 13,\n        \"score\": 0.2177766114473343,\n        \"categoryName\": \"eyeLookInLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 14,\n        \"score\": 0.014739357866346836,\n        \"categoryName\": \"eyeLookInRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 15,\n        \"score\": 0.02361512929201126,\n        \"categoryName\": \"eyeLookOutLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 16,\n        \"score\": 0.19679604470729828,\n        \"categoryName\": \"eyeLookOutRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 17,\n        \"score\": 0.04874616861343384,\n        \"categoryName\": \"eyeLookUpLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 18,\n        \"score\": 0.049392376095056534,\n        \"categoryName\": \"eyeLookUpRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 19,\n        \"score\": 0.34944331645965576,\n        \"categoryName\": \"eyeSquintLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 20,\n        \"score\": 0.2939716875553131,\n        \"categoryName\": \"eyeSquintRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 21,\n        \"score\": 0.005955042317509651,\n        \"categoryName\": \"eyeWideLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 22,\n        \"score\": 0.006776117719709873,\n        \"categoryName\": \"eyeWideRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 23,\n        \"score\": 0.000016942436559475027,\n        \"categoryName\": \"jawForward\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 24,\n        \"score\": 0.0045165494084358215,\n        \"categoryName\": \"jawLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 25,\n        \"score\": 0.07803940027952194,\n        \"categoryName\": \"jawOpen\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 26,\n        \"score\": 0.00002090057751047425,\n        \"categoryName\": \"jawRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 27,\n        \"score\": 0.06032035872340202,\n        \"categoryName\": \"mouthClose\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 28,\n        \"score\": 0.00228882092051208,\n        \"categoryName\": \"mouthDimpleLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 29,\n        \"score\": 0.00781762320548296,\n        \"categoryName\": \"mouthDimpleRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 30,\n        \"score\": 0.0017093931091949344,\n        \"categoryName\": \"mouthFrownLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 31,\n        \"score\": 0.0019319106359034777,\n        \"categoryName\": \"mouthFrownRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 32,\n        \"score\": 0.00008485237776767462,\n        \"categoryName\": \"mouthFunnel\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 33,\n        \"score\": 0.0009051355300471187,\n        \"categoryName\": \"mouthLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 34,\n        \"score\": 0.0003630454302765429,\n        \"categoryName\": \"mouthLowerDownLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 35,\n        \"score\": 0.00017601238505449146,\n        \"categoryName\": \"mouthLowerDownRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 36,\n        \"score\": 0.12865161895751953,\n        \"categoryName\": \"mouthPressLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 37,\n        \"score\": 0.20137207210063934,\n        \"categoryName\": \"mouthPressRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 38,\n        \"score\": 0.0022203284315764904,\n        \"categoryName\": \"mouthPucker\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 39,\n        \"score\": 0.0009096377179957926,\n        \"categoryName\": \"mouthRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 40,\n        \"score\": 0.34189721941947937,\n        \"categoryName\": \"mouthRollLower\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 41,\n        \"score\": 0.11409689486026764,\n        \"categoryName\": \"mouthRollUpper\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 42,\n        \"score\": 0.17172536253929138,\n        \"categoryName\": \"mouthShrugLower\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 43,\n        \"score\": 0.004038424696773291,\n        \"categoryName\": \"mouthShrugUpper\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 44,\n        \"score\": 0.00023205230536404997,\n        \"categoryName\": \"mouthSmileLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 45,\n        \"score\": 0.00019313619122840464,\n        \"categoryName\": \"mouthSmileRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 46,\n        \"score\": 0.0018571305554360151,\n        \"categoryName\": \"mouthStretchLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 47,\n        \"score\": 0.0023813238367438316,\n        \"categoryName\": \"mouthStretchRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 48,\n        \"score\": 0.000024323100660694763,\n        \"categoryName\": \"mouthUpperUpLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 49,\n        \"score\": 0.00003161552012898028,\n        \"categoryName\": \"mouthUpperUpRight\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 50,\n        \"score\": 1.08198406678639e-7,\n        \"categoryName\": \"noseSneerLeft\",\n        \"displayName\": \"\"\n      }, {\n        \"index\": 51,\n        \"score\": 0.0000012652527630052646,\n        \"categoryName\": \"noseSneerRight\",\n        \"displayName\": \"\"\n      }],\n      \"headIndex\": -1,\n      \"headName\": \"\"\n    }],\n    \"facialTransformationMatrixes\": [{\n      \"rows\": 4,\n      \"columns\": 4,\n      \"data\": [0.9947517514228821, 0.10230544209480286, 0.0013679931871592999, 0, -0.10230997204780579, 0.9947447776794434, 0.003816320328041911, 0, -0.000970348424743861, -0.0039362297393381596, 0.9999914169311523, 0, 2.8888821601867676, -7.808934211730957, -30.52109146118164, 1]\n    }]\n  }\n};\n\nexport { Facemesh, FacemeshDatas, FacemeshEye, FacemeshEyeDefaults };\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;;;;;AAEA,yCAAyC,GACzC,MAAM,gBAAgB,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAa,CAAC,GAAG,GAAG,CAAC;AAC9D,MAAM,SAAS,aAAa,GAAE;IAC5B,MAAM,IAAI,IAAI,sMAAA,CAAA,UAAa;IAC3B,MAAM,IAAI,IAAI,sMAAA,CAAA,UAAa;IAC3B,MAAM,IAAI,IAAI,sMAAA,CAAA,UAAa;IAC3B,MAAM,KAAK,IAAI,sMAAA,CAAA,UAAa;IAC5B,MAAM,KAAK,IAAI,sMAAA,CAAA,UAAa;IAC5B,OAAO,SAAU,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QAC5B,EAAE,IAAI,CAAC;QACP,EAAE,IAAI,CAAC;QACP,EAAE,IAAI,CAAC;QACP,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,OAAO,EAAE,YAAY,CAAC,IAAI,IAAI,SAAS;IACzC;AACF;AACA,SAAS,KAAK,EAAE,EAAE,EAAE;IAClB,OAAO,GAAG,KAAK,GAAG,GAAG,CAAC,IAAI,cAAc,CAAC;AAC3C;AACA,MAAM,WAAW,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAChD,SAAS,cAAc,4BAA4B,CAAC,aAAa,CAAC,EAAE,EACpE,IAAI,EACJ,0BAA0B,EAC1B,eAAe,EACf,MAAM,EACN,eAAe,EAAE,EACjB,KAAK,EACL,MAAM,EACN,QAAQ,CAAC,EACT,cAAc;IAAC;IAAK;IAAK;CAAI,EAC7B,MAAM,EACN,OAAO,IAAI,EACX,eAAe,KAAK,EACpB,QAAQ,KAAK,EACb,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,IAAI;IACJ,IAAI,MAAM;QACR,SAAS,KAAK,SAAS;QACvB,QAAQ,IAAI,CAAC;IACf;IACA,MAAM,YAAY,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC/B,MAAM,WAAW,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC9B,MAAM,YAAY,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC/B,MAAM,WAAW,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC9B,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC7B,MAAM,cAAc,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IACjC,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAChC,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD;6BAAE,IAAM,IAAI,sMAAA,CAAA,UAAa;;IACzD,MAAM,CAAC,UAAU,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD;6BAAE,IAAM,IAAI,sMAAA,CAAA,WAAc;;IAC3D,MAAM,CAAC,mBAAmB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD;6BAAE,IAAM,IAAI,sMAAA,CAAA,aAAgB;;IACtE,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD;6BAAE,IAAM,IAAI,sMAAA,CAAA,UAAa;;IACxD,MAAM,EACJ,UAAU,EACX,GAAG,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;IACX,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;8BAAE;YACd,IAAI;YACJ,CAAC,mBAAmB,QAAQ,OAAO,KAAK,QAAQ,iBAAiB,QAAQ,CAAC,QAAQ,CAAC,cAAc,aAAa;QAChH;6BAAG,EAAE;IACL,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD;6BAAE,IAAM,IAAI,sMAAA,CAAA,UAAa;;IACzD,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;8BAAE;YACd,IAAI,mBAAmB;YACvB,MAAM,eAAe,CAAC,oBAAoB,QAAQ,OAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,QAAQ;YACxG,IAAI,CAAC,cAAc;YACnB,aAAa,aAAa,CAAC;YAC3B,aAAa,YAAY,CAAC,GAAG,cAAc,aAAa,CAAC,MAAM;YAE/D,EAAE;YACF,6BAA6B;YAC7B,EAAE;YACF,2DAA2D;YAC3D,2BAA2B;YAC3B,EAAE;YAEF,IAAI,4BAA4B;gBAC9B,kCAAkC;gBAClC,UAAU,MAAM,CAAC,SAAS,CAAC,2BAA2B,IAAI;gBAC1D,UAAU,MAAM,CAAC,SAAS,CAAC,UAAU,QAAQ,EAAE,UAAU,UAAU,EAAE,UAAU,KAAK;gBAEpF,sCAAsC;gBACtC,UAAU,QAAQ,CAAC,CAAC,IAAI,CAAC;gBACzB,UAAU,QAAQ,CAAC,CAAC,IAAI,CAAC;gBACzB,mBAAmB,YAAY,CAAC,UAAU,QAAQ;gBAElD,oCAAoC;gBACpC,IAAI,QAAQ;oBACV,IAAI;oBACJ,UAAU,QAAQ,CAAC,CAAC,IAAI,CAAC;oBACzB,UAAU,QAAQ,CAAC,CAAC,IAAI,CAAC;oBACzB,CAAC,qBAAqB,UAAU,OAAO,KAAK,QAAQ,mBAAmB,QAAQ,CAAC,IAAI,CAAC,UAAU,QAAQ,CAAC,YAAY,CAAC;gBACvH,OAAO;oBACL,IAAI;oBACJ,CAAC,sBAAsB,UAAU,OAAO,KAAK,QAAQ,oBAAoB,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,QAAQ;gBAC1G;YACF,OAAO;gBACL,wBAAwB;gBACxB,OAAO,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE;gBAC/E,mBAAmB,kBAAkB,CAAC,eAAe;YACvD;YACA,MAAM,4BAA4B,mBAAmB,KAAK,GAAG,MAAM;YAEnE,EAAE;YACF,6BAA6B;YAC7B,EAAE;YAEF,iCAAiC;YACjC,aAAa,kBAAkB;YAC/B,IAAI,OAAO,cAAc,6EAA6E;YACtG,aAAa,MAAM;YAEnB,6CAA6C;YAC7C,aAAa,eAAe,CAAC;YAC7B,CAAC,oBAAoB,SAAS,OAAO,KAAK,QAAQ,kBAAkB,yBAAyB,CAAC;YAE9F,aAAa;YACb,IAAI,MAAM;gBACR,IAAI,CAAC,iBAAiB;oBACpB,QAAQ,IAAI,CAAC;gBACf,OAAO;oBACL,IAAI,YAAY,OAAO,IAAI,WAAW,OAAO,IAAI,UAAU,OAAO,EAAE;wBAClE,IAAI,cAAc;4BAChB,mDAAmD;4BACnD,MAAM,iBAAiB,YAAY,OAAO,CAAC,cAAc,CAAC;4BAC1D,MAAM,gBAAgB,WAAW,OAAO,CAAC,cAAc,CAAC;4BACxD,MAAM,aAAa,KAAK,eAAe,MAAM,EAAE,cAAc,MAAM;4BACnE,SAAS,WAAW,MAAM,IAAI,kDAAkD;4BAEhF,YAAY,OAAO,CAAC,OAAO,CAAC,cAAc,iBAAiB;4BAC3D,WAAW,OAAO,CAAC,OAAO,CAAC,cAAc,iBAAiB;wBAC5D,OAAO;4BACL,YAAY,OAAO,CAAC,OAAO,CAAC,cAAc;4BAC1C,WAAW,OAAO,CAAC,OAAO,CAAC,cAAc;wBAC3C;oBACF;gBACF;YACF;YAEA,YAAY;YACZ,IAAI,UAAU,OAAO,EAAE;gBACrB,IAAI,WAAW,WAAW;oBACxB,IAAI,OAAO,WAAW,UAAU;wBAC9B,MAAM,WAAW,aAAa,YAAY,CAAC;wBAC3C,QAAQ,GAAG,CAAC,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,CAAC;oBAC7E,OAAO,IAAI,OAAO,SAAS,EAAE;wBAC3B,QAAQ,IAAI,CAAC;oBACf;gBACF,OAAO;oBACL,QAAQ,SAAS,CAAC;gBACpB;gBACA,UAAU,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;YAClC;YAEA,cAAc;YACd,IAAI,SAAS,OAAO,EAAE;gBACpB,IAAI,QAAQ;gBACZ,IAAI,SAAS,UAAU,OAAO;oBAC5B,aAAa,WAAW,CAAC,OAAO,CAAC;oBACjC,IAAI,OAAO,QAAQ,QAAQ,SAAS,CAAC,EAAE,eAAe;oBACtD,IAAI,QAAQ,QAAQ,SAAS,SAAS,CAAC,EAAE,gBAAgB;oBACzD,IAAI,OAAO,QAAQ,QAAQ,SAAS,CAAC,EAAE,eAAe;gBACxD;gBACA,SAAS,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,IAAI,QAAQ;YACzD;YACA,aAAa,oBAAoB;YACjC,aAAa,UAAU,CAAC,QAAQ,CAAC,WAAW,GAAG;QACjD;6BAAG;QAAC;QAAQ;QAA4B;QAAiB;QAAW;QAAQ;QAAc;QAAO;QAAQ;QAAO;QAAa;QAAQ;QAAM;QAAO;QAAY;QAAU;QAAoB;QAAU;KAAQ;IAE9M,EAAE;IACF,MAAM;IACN,EAAE;IAEF,MAAM,MAAM,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;iCAAE,IAAM,CAAC;gBAC/B;gBACA;gBACA;gBACA;YACF,CAAC;gCAAG,EAAE;IACN,CAAA,GAAA,4RAAA,CAAA,sBAAyB,AAAD,EAAE;wCAAM,IAAM;uCAAK;QAAC;KAAI;IAChD,MAAM,CAAC,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD;6BAAE,IAAM,IAAI,sMAAA,CAAA,UAAa;;IAC7D,MAAM,OAAO,CAAC,oBAAoB,QAAQ,OAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,QAAQ,CAAC,WAAW;IAC5G,MAAM,MAAM,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,OAAO,CAAC,cAAc,CAAC,KAAK;IACtE,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAChG,KAAK;IACP,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3C,KAAK;IACP,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3C,KAAK;IACP,GAAG,QAAQ,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,4RAAA,CAAA,WAAc,EAAE,MAAM,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,cAAc;QAC/G,MAAM;YAAC;SAAI;IACb,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,8WAAA,CAAA,OAAI,EAAE;QACzC,QAAQ;YAAC;gBAAC;gBAAG;gBAAG;aAAE;YAAE;gBAAC;gBAAG;gBAAG,CAAC;aAAI;SAAC;QACjC,OAAO;IACT,MAAM,MAAM,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QACpD,KAAK;IACP,GAAG,QAAQ,mBAAmB,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QACtE,MAAM;IACR,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,aAAa;QAC/C,MAAM;QACN,KAAK;QACL,OAAO;IACT,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,aAAa;QAChD,MAAM;QACN,KAAK;QACL,OAAO;IACT,KAAK,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC5C,KAAK;QACL,MAAM;IACR,GAAG,UAAU,QAAQ,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,4RAAA,CAAA,WAAc,EAAE,MAAM,QAAQ,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,cAAc;QACjI,MAAM;YAAC;SAAK;IACd,MAAM;AACR;AAEA,EAAE;AACF,kBAAkB;AAClB,EAAE;AAEF,MAAM,sBAAsB;IAC1B,kBAAkB;QAChB,OAAO;YAAC;YAAI;YAAK;YAAK;YAAK;SAAI;QAC/B,MAAM;YAAC;YAAK;YAAK;YAAK;YAAK;SAAI;IACjC;IACA,aAAa;QACX,OAAO;YAAC;YAAI;YAAI;YAAI;SAAG;QACvB,kCAAkC;QAClC,MAAM;YAAC;YAAI;YAAI;YAAI;SAAG,CAAC,kCAAkC;IAC3D;IACA,OAAO;QACL,OAAO;QACP,MAAM;IACR;IACA,KAAK;QACH,YAAY;QACZ,UAAU;IACZ;AACF;AACA,MAAM,cAAc,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EACnD,IAAI,EACJ,QAAQ,IAAI,EACb,EAAE;IACD,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAChC,MAAM,aAAa,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAEhC,EAAE;IACF,mBAAmB;IACnB,EAAE;IACF,6CAA6C;IAC7C,EAAE;IAEF,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD;gCAAE,IAAM,IAAI,sMAAA,CAAA,SAAY;;IACtD,MAAM,iBAAiB,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;mDAAE,CAAA;YACvC,MAAM,WAAW,aAAa,YAAY,CAAC;YAE3C,wDAAwD;YACxD,MAAM,sBAAsB,oBAAoB,gBAAgB,CAAC,KAAK;YACtE,MAAM,mBAAmB,oBAAoB,GAAG;4EAAC,CAAA,IAAK,IAAI,sMAAA,CAAA,UAAa,CAAC,SAAS,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC;4EAAM,kBAAkB;YAElJ,kDAAkD;YAClD,OAAO,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG;YACxB,iBAAiB,OAAO;2DAAC,CAAA,IAAK,OAAO,MAAM,CAAC,GAAG,CAAC;;YAChD,OAAO,MAAM,CAAC,YAAY,CAAC,iBAAiB,MAAM;YAElD,0BAA0B;YAC1B,OAAO,MAAM,GAAG,gBAAgB,CAAC,EAAE,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,EAAE,MAAM,KAAK;YACxE,OAAO;QACT;kDAAG;QAAC;QAAQ;KAAK;IAEjB,EAAE;IACF,YAAY;IACZ,EAAE;IACF,UAAU;IACV,0CAA0C;IAC1C,2DAA2D;IAC3D,EAAE;IAEF,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD;gCAAE,IAAM,IAAI,sMAAA,CAAA,QAAW;;IACvD,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,cAAiB,AAAD;4CAAE,CAAC,cAAc,iBAAiB;YAChE,KAAK;YACL,IAAI,WAAW,OAAO,EAAE;gBACtB,IAAI;gBACJ,CAAC,UAAU,MAAM,MAAM,QAAQ,YAAY,KAAK,IAAI,UAAU,SAAS,eAAe,eAAe,sCAAsC;gBAC3I,WAAW,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,MAAM;gBAC9C,WAAW,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,MAAM;YAClD;YAEA,KAAK;YACL,IAAI,mBAAmB,WAAW,OAAO,EAAE;gBACzC,MAAM,cAAc,oBAAoB,WAAW,CAAC,KAAK;gBACzD,MAAM,SAAS,gBAAgB,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,KAAK;gBAC/D,MAAM,UAAU,gBAAgB,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,KAAK;gBAChE,MAAM,SAAS,gBAAgB,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,KAAK;gBAC/D,MAAM,WAAW,gBAAgB,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,KAAK;gBACjE,MAAM,OAAO,oBAAoB,GAAG,CAAC,UAAU,GAAG,sMAAA,CAAA,YAAe,CAAC,OAAO;gBACzE,MAAM,OAAO,oBAAoB,GAAG,CAAC,QAAQ,GAAG,sMAAA,CAAA,YAAe,CAAC,OAAO;gBACvE,MAAM,KAAK,OAAO,MAAM,CAAC,WAAW,MAAM;gBAC1C,MAAM,KAAK,OAAO,MAAM,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,CAAC;gBACtE,SAAS,GAAG,CAAC,IAAI,IAAI;gBACrB,WAAW,OAAO,CAAC,oBAAoB,CAAC;YAC1C;QACF;2CAAG;QAAC;QAAgB;QAAM;KAAS;IAEnC,EAAE;IACF,MAAM;IACN,EAAE;IAEF,MAAM,MAAM,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;oCAAE,IAAM,CAAC;gBAC/B,YAAY;gBACZ,YAAY;gBACZ;gBACA;YACF,CAAC;mCAAG;QAAC;QAAgB;KAAQ;IAC7B,CAAA,GAAA,4RAAA,CAAA,sBAAyB,AAAD,EAAE;2CAAM,IAAM;0CAAK;QAAC;KAAI;IAChD,MAAM,QAAQ,oBAAoB,KAAK,CAAC,KAAK;IAC7C,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC/F,KAAK;IACP,GAAG,SAAS,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,cAAc,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC1G,KAAK;IACP,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,4RAAA,CAAA,WAAc,EAAE,MAAM,SAAS,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,8WAAA,CAAA,OAAI,EAAE;QACxG,QAAQ;YAAC;gBAAC;gBAAG;gBAAG;aAAE;YAAE;gBAAC;gBAAG;gBAAG,CAAC;aAAE;SAAC;QAC/B,WAAW;QACX,OAAO;IACT;AACF;AAEA,EAAE;AACF,eAAe;AACf,EAAE;AAEF,MAAM,gBAAgB;IACpB,iKAAiK;IACjK,kBAAkB;IAClB,eAAe;QAAC;QAAK;QAAI;QAAK;QAAI;QAAG;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAI;QAAI;QAAG;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAI;QAAI;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAG;QAAK;QAAI;QAAI;QAAI;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAI;QAAI;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAI;QAAK;QAAI;QAAI;QAAK;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAI;QAAK;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAI;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAI;QAAG;QAAG;QAAK;QAAI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAK;QAAG;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAI;QAAI;QAAI;QAAK;QAAG;QAAI;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAG;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAG;QAAK;QAAK;QAAI;QAAG;QAAI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAG;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAI;QAAI;QAAK;QAAI;QAAI;QAAK;QAAI;QAAI;QAAK;QAAI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAI;QAAI;QAAK;QAAI;QAAI;QAAK;QAAI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAG;QAAG;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAG;QAAI;QAAK;QAAI;QAAI;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAI;QAAK;QAAI;QAAI;QAAK;QAAI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAI;QAAK;QAAI;QAAI;QAAK;QAAI;QAAI;QAAK;QAAI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAI;QAAK;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAG;QAAK;QAAI;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAG;QAAI;QAAG;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAG;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAI;QAAI;QAAG;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAK;QAAI;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAG;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAG;QAAK;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAG;QAAK;QAAI;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAG;QAAK;QAAK;QAAI;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAI;QAAG;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAI;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAI;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAI;QAAK;QAAG;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAG;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAG;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAG;QAAK;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAG;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KAAI;IACzzY,sDAAsD;IACtD,kBAAkB;IAClB,aAAa;QACX,aAAa;YAAC;gBACZ,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;gBACN,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK,CAAC;YACR;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,QAAQ;YACV;YAAG;gBACD,KAAK;gBACL,KAAK;gBACL,KAAK;YACP;SAAE;QACF,OAAO;YACL,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,UAAU;QACZ;IACF;IACA,gGAAgG;IAChG,kBAAkB;IAClB,8BAA8B;QAC5B,iBAAiB;YAAC;gBAAC;oBACjB,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;gBAAG;oBACD,KAAK;oBACL,KAAK;oBACL,KAAK,CAAC;gBACR;aAAE;SAAC;QACH,mBAAmB;YAAC;gBAClB,cAAc;oBAAC;wBACb,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;oBAAG;wBACD,SAAS;wBACT,SAAS;wBACT,gBAAgB;wBAChB,eAAe;oBACjB;iBAAE;gBACF,aAAa,CAAC;gBACd,YAAY;YACd;SAAE;QACF,gCAAgC;YAAC;gBAC/B,QAAQ;gBACR,WAAW;gBACX,QAAQ;oBAAC;oBAAoB;oBAAqB;oBAAuB;oBAAG,CAAC;oBAAqB;oBAAoB;oBAAsB;oBAAG,CAAC;oBAAsB,CAAC;oBAAuB;oBAAoB;oBAAG;oBAAoB,CAAC;oBAAmB,CAAC;oBAAmB;iBAAE;YACrR;SAAE;IACJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 11482, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 11488, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/FaceLandmarker.js"], "sourcesContent": ["import * as React from 'react';\nimport { forwardRef, useEffect, useImperativeHandle, useContext, createContext } from 'react';\nimport { suspend, clear } from 'suspend-react';\n\n/* eslint react-hooks/exhaustive-deps: 1 */\nconst FaceLandmarkerContext = /* @__PURE__ */createContext({});\nconst FaceLandmarkerDefaults = {\n  basePath: 'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.17/wasm',\n  options: {\n    baseOptions: {\n      modelAssetPath: 'https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task',\n      delegate: 'GPU'\n    },\n    runningMode: 'VIDEO',\n    outputFaceBlendshapes: true,\n    outputFacialTransformationMatrixes: true\n  }\n};\nconst FaceLandmarker = /*#__PURE__*/forwardRef(({\n  basePath = FaceLandmarkerDefaults.basePath,\n  options = FaceLandmarkerDefaults.options,\n  children\n}, fref) => {\n  const opts = JSON.stringify(options);\n  const faceLandmarker = suspend(async () => {\n    const {\n      FilesetResolver,\n      FaceLandmarker\n    } = await import('@mediapipe/tasks-vision');\n    const vision = await FilesetResolver.forVisionTasks(basePath);\n    return FaceLandmarker.createFromOptions(vision, options);\n  }, [basePath, opts]);\n  useEffect(() => {\n    return () => {\n      faceLandmarker == null || faceLandmarker.close();\n      clear([basePath, opts]);\n    };\n  }, [faceLandmarker, basePath, opts]);\n  useImperativeHandle(fref, () => faceLandmarker, [faceLandmarker]); // expose faceLandmarker through ref\n\n  return /*#__PURE__*/React.createElement(FaceLandmarkerContext.Provider, {\n    value: faceLandmarker\n  }, children);\n});\nfunction useFaceLandmarker() {\n  return useContext(FaceLandmarkerContext);\n}\n\nexport { FaceLandmarker, FaceLandmarkerDefaults, useFaceLandmarker };\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAEA,yCAAyC,GACzC,MAAM,wBAAwB,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAa,AAAD,EAAE,CAAC;AAC5D,MAAM,yBAAyB;IAC7B,UAAU;IACV,SAAS;QACP,aAAa;YACX,gBAAgB;YAChB,UAAU;QACZ;QACA,aAAa;QACb,uBAAuB;QACvB,oCAAoC;IACtC;AACF;AACA,MAAM,iBAAiB,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAC9C,WAAW,uBAAuB,QAAQ,EAC1C,UAAU,uBAAuB,OAAO,EACxC,QAAQ,EACT,EAAE;IACD,MAAM,OAAO,KAAK,SAAS,CAAC;IAC5B,MAAM,iBAAiB,CAAA,GAAA,+NAAA,CAAA,UAAO,AAAD,EAAE;QAC7B,MAAM,EACJ,eAAe,EACf,cAAc,EACf,GAAG;QACJ,MAAM,SAAS,MAAM,gBAAgB,cAAc,CAAC;QACpD,OAAO,eAAe,iBAAiB,CAAC,QAAQ;IAClD,GAAG;QAAC;QAAU;KAAK;IACnB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;oCAAE;YACR;4CAAO;oBACL,kBAAkB,QAAQ,eAAe,KAAK;oBAC9C,CAAA,GAAA,+NAAA,CAAA,QAAK,AAAD,EAAE;wBAAC;wBAAU;qBAAK;gBACxB;;QACF;mCAAG;QAAC;QAAgB;QAAU;KAAK;IACnC,CAAA,GAAA,4RAAA,CAAA,sBAAmB,AAAD,EAAE;8CAAM,IAAM;6CAAgB;QAAC;KAAe,GAAG,oCAAoC;IAEvG,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,sBAAsB,QAAQ,EAAE;QACtE,OAAO;IACT,GAAG;AACL;AACA,SAAS;IACP,OAAO,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,EAAE;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 11551, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 11557, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/FaceControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as THREE from 'three';\nimport * as React from 'react';\nimport { forwardRef, useRef, useState, useCallback, useMemo, useImperativeHandle, useEffect, Suspense, useContext, createContext } from 'react';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport { easing } from 'maath';\nimport { VideoTexture } from '../core/VideoTexture.js';\nimport { WebcamVideoTexture } from './WebcamVideoTexture.js';\nimport { Facemesh } from './Facemesh.js';\nimport { useFaceLandmarker } from './FaceLandmarker.js';\n\nfunction mean(v1, v2) {\n  return v1.clone().add(v2).multiplyScalar(0.5);\n}\nfunction localToLocal(objSrc, v, objDst) {\n  // see: https://discourse.threejs.org/t/object3d-localtolocal/51564\n  const v_world = objSrc.localToWorld(v);\n  return objDst.worldToLocal(v_world);\n}\n\n//\n//\n//\n\nconst FaceControlsContext = /* @__PURE__ */createContext({});\n\n/**\n * The camera follows your face.\n *\n * Pre-requisite: wrap into a `FaceLandmarker` provider:\n *\n * ```jsx\n * <FaceLandmarker>...</FaceLandmarker>\n * ```\n */\n\nconst FaceControls = /* @__PURE__ */forwardRef(({\n  camera,\n  videoTexture = {\n    start: true\n  },\n  manualDetect = false,\n  faceLandmarkerResult,\n  manualUpdate = false,\n  makeDefault,\n  smoothTime = 0.25,\n  offset = true,\n  offsetScalar = 80,\n  eyes = false,\n  eyesAsOrigin = true,\n  depth = 0.15,\n  debug = false,\n  facemesh\n}, fref) => {\n  var _result$facialTransfo, _result$faceBlendshap;\n  const scene = useThree(state => state.scene);\n  const defaultCamera = useThree(state => state.camera);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const explCamera = camera || defaultCamera;\n  const facemeshApiRef = useRef(null);\n\n  //\n  // computeTarget()\n  //\n  // Compute `target` position and rotation for the camera (according to <Facemesh>)\n  //\n  //  1. 👀 either following the 2 eyes\n  //  2. 👤 or just the head mesh\n  //\n\n  const [target] = useState(() => new THREE.Object3D());\n  const [irisRightDirPos] = useState(() => new THREE.Vector3());\n  const [irisLeftDirPos] = useState(() => new THREE.Vector3());\n  const [irisRightLookAt] = useState(() => new THREE.Vector3());\n  const [irisLeftLookAt] = useState(() => new THREE.Vector3());\n  const computeTarget = useCallback(() => {\n    // same parent as the camera\n    target.parent = explCamera.parent;\n    const facemeshApi = facemeshApiRef.current;\n    if (facemeshApi) {\n      const {\n        outerRef,\n        eyeRightRef,\n        eyeLeftRef\n      } = facemeshApi;\n      if (eyeRightRef.current && eyeLeftRef.current) {\n        // 1. 👀\n\n        const {\n          irisDirRef: irisRightDirRef\n        } = eyeRightRef.current;\n        const {\n          irisDirRef: irisLeftDirRef\n        } = eyeLeftRef.current;\n        if (irisRightDirRef.current && irisLeftDirRef.current && outerRef.current) {\n          //\n          // position: mean of irisRightDirPos,irisLeftDirPos\n          //\n          irisRightDirPos.copy(localToLocal(irisRightDirRef.current, new THREE.Vector3(0, 0, 0), outerRef.current));\n          irisLeftDirPos.copy(localToLocal(irisLeftDirRef.current, new THREE.Vector3(0, 0, 0), outerRef.current));\n          target.position.copy(localToLocal(outerRef.current, mean(irisRightDirPos, irisLeftDirPos), explCamera.parent || scene));\n\n          //\n          // lookAt: mean of irisRightLookAt,irisLeftLookAt\n          //\n          irisRightLookAt.copy(localToLocal(irisRightDirRef.current, new THREE.Vector3(0, 0, 1), outerRef.current));\n          irisLeftLookAt.copy(localToLocal(irisLeftDirRef.current, new THREE.Vector3(0, 0, 1), outerRef.current));\n          target.lookAt(outerRef.current.localToWorld(mean(irisRightLookAt, irisLeftLookAt)));\n        }\n      } else {\n        // 2. 👤\n\n        if (outerRef.current) {\n          target.position.copy(localToLocal(outerRef.current, new THREE.Vector3(0, 0, 0), explCamera.parent || scene));\n          target.lookAt(outerRef.current.localToWorld(new THREE.Vector3(0, 0, 1)));\n        }\n      }\n    }\n    return target;\n  }, [explCamera, irisLeftDirPos, irisLeftLookAt, irisRightDirPos, irisRightLookAt, scene, target]);\n\n  //\n  // update()\n  //\n  // Updating the camera `current` position and rotation, following `target`\n  //\n\n  const [current] = useState(() => new THREE.Object3D());\n  const update = useCallback(function (delta, target) {\n    if (explCamera) {\n      var _target;\n      (_target = target) !== null && _target !== void 0 ? _target : target = computeTarget();\n      if (smoothTime > 0) {\n        // damping current\n        const eps = 1e-9;\n        easing.damp3(current.position, target.position, smoothTime, delta, undefined, undefined, eps);\n        easing.dampE(current.rotation, target.rotation, smoothTime, delta, undefined, undefined, eps);\n      } else {\n        // instant\n        current.position.copy(target.position);\n        current.rotation.copy(target.rotation);\n      }\n      explCamera.position.copy(current.position);\n      explCamera.rotation.copy(current.rotation);\n    }\n  }, [explCamera, computeTarget, smoothTime, current.position, current.rotation]);\n  useFrame((_, delta) => {\n    if (manualUpdate) return;\n    update(delta);\n  });\n\n  //\n  // onVideoFrame (only used if !manualDetect)\n  //\n\n  const videoTextureRef = useRef(null);\n  const [_faceLandmarkerResult, setFaceLandmarkerResult] = useState();\n  const faceLandmarker = useFaceLandmarker();\n  const onVideoFrame = useCallback((now, metadata) => {\n    const texture = videoTextureRef.current;\n    if (!texture) return;\n    const videoFrame = texture.source.data;\n    const result = faceLandmarker == null ? void 0 : faceLandmarker.detectForVideo(videoFrame, now);\n    setFaceLandmarkerResult(result);\n  }, [faceLandmarker]);\n\n  //\n  // Ref API\n  //\n\n  const api = useMemo(() => Object.assign(Object.create(THREE.EventDispatcher.prototype), {\n    computeTarget,\n    update,\n    facemeshApiRef\n  }), [computeTarget, update]);\n  useImperativeHandle(fref, () => api, [api]);\n\n  //\n  // makeDefault (`controls` global state)\n  //\n\n  useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      set({\n        controls: api\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, api, get, set]);\n\n  //\n  //\n  //\n\n  const result = faceLandmarkerResult !== null && faceLandmarkerResult !== void 0 ? faceLandmarkerResult : _faceLandmarkerResult;\n  const points = result == null ? void 0 : result.faceLandmarks[0];\n  const facialTransformationMatrix = result == null || (_result$facialTransfo = result.facialTransformationMatrixes) == null ? void 0 : _result$facialTransfo[0];\n  const faceBlendshapes = result == null || (_result$faceBlendshap = result.faceBlendshapes) == null ? void 0 : _result$faceBlendshap[0];\n  const videoTextureProps = {\n    onVideoFrame,\n    ...videoTexture\n  };\n  return /*#__PURE__*/React.createElement(FaceControlsContext.Provider, {\n    value: api\n  }, !manualDetect && /*#__PURE__*/React.createElement(Suspense, {\n    fallback: null\n  }, 'src' in videoTextureProps ? /*#__PURE__*/React.createElement(VideoTexture, _extends({\n    ref: videoTextureRef\n  }, videoTextureProps)) : /*#__PURE__*/React.createElement(WebcamVideoTexture, _extends({\n    ref: videoTextureRef\n  }, videoTextureProps))), /*#__PURE__*/React.createElement(Facemesh, _extends({\n    ref: facemeshApiRef,\n    children: /*#__PURE__*/React.createElement(\"meshNormalMaterial\", {\n      side: THREE.DoubleSide\n    })\n  }, facemesh, {\n    points: points,\n    depth: depth,\n    facialTransformationMatrix: facialTransformationMatrix,\n    faceBlendshapes: faceBlendshapes,\n    eyes: eyes,\n    eyesAsOrigin: eyesAsOrigin,\n    offset: offset,\n    offsetScalar: offsetScalar,\n    debug: debug,\n    \"rotation-z\": Math.PI,\n    visible: debug\n  })));\n});\nconst useFaceControls = () => useContext(FaceControlsContext);\n\nexport { FaceControls, useFaceControls };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEA,SAAS,KAAK,EAAE,EAAE,EAAE;IAClB,OAAO,GAAG,KAAK,GAAG,GAAG,CAAC,IAAI,cAAc,CAAC;AAC3C;AACA,SAAS,aAAa,MAAM,EAAE,CAAC,EAAE,MAAM;IACrC,mEAAmE;IACnE,MAAM,UAAU,OAAO,YAAY,CAAC;IACpC,OAAO,OAAO,YAAY,CAAC;AAC7B;AAEA,EAAE;AACF,EAAE;AACF,EAAE;AAEF,MAAM,sBAAsB,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAa,AAAD,EAAE,CAAC;AAE1D;;;;;;;;CAQC,GAED,MAAM,eAAe,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAC9C,MAAM,EACN,eAAe;IACb,OAAO;AACT,CAAC,EACD,eAAe,KAAK,EACpB,oBAAoB,EACpB,eAAe,KAAK,EACpB,WAAW,EACX,aAAa,IAAI,EACjB,SAAS,IAAI,EACb,eAAe,EAAE,EACjB,OAAO,KAAK,EACZ,eAAe,IAAI,EACnB,QAAQ,IAAI,EACZ,QAAQ,KAAK,EACb,QAAQ,EACT,EAAE;IACD,IAAI,uBAAuB;IAC3B,MAAM,QAAQ,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;wCAAE,CAAA,QAAS,MAAM,KAAK;;IAC3C,MAAM,gBAAgB,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;gDAAE,CAAA,QAAS,MAAM,MAAM;;IACpD,MAAM,MAAM,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;sCAAE,CAAA,QAAS,MAAM,GAAG;;IACvC,MAAM,MAAM,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;sCAAE,CAAA,QAAS,MAAM,GAAG;;IACvC,MAAM,aAAa,UAAU;IAC7B,MAAM,iBAAiB,CAAA,GAAA,4RAAA,CAAA,SAAM,AAAD,EAAE;IAE9B,EAAE;IACF,kBAAkB;IAClB,EAAE;IACF,kFAAkF;IAClF,EAAE;IACF,qCAAqC;IACrC,+BAA+B;IAC/B,EAAE;IAEF,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD;iCAAE,IAAM,IAAI,sMAAA,CAAA,WAAc;;IAClD,MAAM,CAAC,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD;iCAAE,IAAM,IAAI,sMAAA,CAAA,UAAa;;IAC1D,MAAM,CAAC,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD;iCAAE,IAAM,IAAI,sMAAA,CAAA,UAAa;;IACzD,MAAM,CAAC,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD;iCAAE,IAAM,IAAI,sMAAA,CAAA,UAAa;;IAC1D,MAAM,CAAC,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD;iCAAE,IAAM,IAAI,sMAAA,CAAA,UAAa;;IACzD,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;mDAAE;YAChC,4BAA4B;YAC5B,OAAO,MAAM,GAAG,WAAW,MAAM;YACjC,MAAM,cAAc,eAAe,OAAO;YAC1C,IAAI,aAAa;gBACf,MAAM,EACJ,QAAQ,EACR,WAAW,EACX,UAAU,EACX,GAAG;gBACJ,IAAI,YAAY,OAAO,IAAI,WAAW,OAAO,EAAE;oBAC7C,QAAQ;oBAER,MAAM,EACJ,YAAY,eAAe,EAC5B,GAAG,YAAY,OAAO;oBACvB,MAAM,EACJ,YAAY,cAAc,EAC3B,GAAG,WAAW,OAAO;oBACtB,IAAI,gBAAgB,OAAO,IAAI,eAAe,OAAO,IAAI,SAAS,OAAO,EAAE;wBACzE,EAAE;wBACF,mDAAmD;wBACnD,EAAE;wBACF,gBAAgB,IAAI,CAAC,aAAa,gBAAgB,OAAO,EAAE,IAAI,sMAAA,CAAA,UAAa,CAAC,GAAG,GAAG,IAAI,SAAS,OAAO;wBACvG,eAAe,IAAI,CAAC,aAAa,eAAe,OAAO,EAAE,IAAI,sMAAA,CAAA,UAAa,CAAC,GAAG,GAAG,IAAI,SAAS,OAAO;wBACrG,OAAO,QAAQ,CAAC,IAAI,CAAC,aAAa,SAAS,OAAO,EAAE,KAAK,iBAAiB,iBAAiB,WAAW,MAAM,IAAI;wBAEhH,EAAE;wBACF,iDAAiD;wBACjD,EAAE;wBACF,gBAAgB,IAAI,CAAC,aAAa,gBAAgB,OAAO,EAAE,IAAI,sMAAA,CAAA,UAAa,CAAC,GAAG,GAAG,IAAI,SAAS,OAAO;wBACvG,eAAe,IAAI,CAAC,aAAa,eAAe,OAAO,EAAE,IAAI,sMAAA,CAAA,UAAa,CAAC,GAAG,GAAG,IAAI,SAAS,OAAO;wBACrG,OAAO,MAAM,CAAC,SAAS,OAAO,CAAC,YAAY,CAAC,KAAK,iBAAiB;oBACpE;gBACF,OAAO;oBACL,QAAQ;oBAER,IAAI,SAAS,OAAO,EAAE;wBACpB,OAAO,QAAQ,CAAC,IAAI,CAAC,aAAa,SAAS,OAAO,EAAE,IAAI,sMAAA,CAAA,UAAa,CAAC,GAAG,GAAG,IAAI,WAAW,MAAM,IAAI;wBACrG,OAAO,MAAM,CAAC,SAAS,OAAO,CAAC,YAAY,CAAC,IAAI,sMAAA,CAAA,UAAa,CAAC,GAAG,GAAG;oBACtE;gBACF;YACF;YACA,OAAO;QACT;kDAAG;QAAC;QAAY;QAAgB;QAAgB;QAAiB;QAAiB;QAAO;KAAO;IAEhG,EAAE;IACF,WAAW;IACX,EAAE;IACF,0EAA0E;IAC1E,EAAE;IAEF,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD;iCAAE,IAAM,IAAI,sMAAA,CAAA,WAAc;;IACnD,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;4CAAE,SAAU,KAAK,EAAE,MAAM;YAChD,IAAI,YAAY;gBACd,IAAI;gBACJ,CAAC,UAAU,MAAM,MAAM,QAAQ,YAAY,KAAK,IAAI,UAAU,SAAS;gBACvE,IAAI,aAAa,GAAG;oBAClB,kBAAkB;oBAClB,MAAM,MAAM;oBACZ,0SAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,QAAQ,EAAE,OAAO,QAAQ,EAAE,YAAY,OAAO,WAAW,WAAW;oBACzF,0SAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,QAAQ,EAAE,OAAO,QAAQ,EAAE,YAAY,OAAO,WAAW,WAAW;gBAC3F,OAAO;oBACL,UAAU;oBACV,QAAQ,QAAQ,CAAC,IAAI,CAAC,OAAO,QAAQ;oBACrC,QAAQ,QAAQ,CAAC,IAAI,CAAC,OAAO,QAAQ;gBACvC;gBACA,WAAW,QAAQ,CAAC,IAAI,CAAC,QAAQ,QAAQ;gBACzC,WAAW,QAAQ,CAAC,IAAI,CAAC,QAAQ,QAAQ;YAC3C;QACF;2CAAG;QAAC;QAAY;QAAe;QAAY,QAAQ,QAAQ;QAAE,QAAQ,QAAQ;KAAC;IAC9E,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;iCAAE,CAAC,GAAG;YACX,IAAI,cAAc;YAClB,OAAO;QACT;;IAEA,EAAE;IACF,4CAA4C;IAC5C,EAAE;IAEF,MAAM,kBAAkB,CAAA,GAAA,4RAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,CAAC,uBAAuB,wBAAwB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD;IAChE,MAAM,iBAAiB,CAAA,GAAA,uXAAA,CAAA,oBAAiB,AAAD;IACvC,MAAM,eAAe,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;kDAAE,CAAC,KAAK;YACrC,MAAM,UAAU,gBAAgB,OAAO;YACvC,IAAI,CAAC,SAAS;YACd,MAAM,aAAa,QAAQ,MAAM,CAAC,IAAI;YACtC,MAAM,SAAS,kBAAkB,OAAO,KAAK,IAAI,eAAe,cAAc,CAAC,YAAY;YAC3F,wBAAwB;QAC1B;iDAAG;QAAC;KAAe;IAEnB,EAAE;IACF,UAAU;IACV,EAAE;IAEF,MAAM,MAAM,CAAA,GAAA,4RAAA,CAAA,UAAO,AAAD;qCAAE,IAAM,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,sMAAA,CAAA,kBAAqB,CAAC,SAAS,GAAG;gBACtF;gBACA;gBACA;YACF;oCAAI;QAAC;QAAe;KAAO;IAC3B,CAAA,GAAA,4RAAA,CAAA,sBAAmB,AAAD,EAAE;4CAAM,IAAM;2CAAK;QAAC;KAAI;IAE1C,EAAE;IACF,wCAAwC;IACxC,EAAE;IAEF,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,aAAa;gBACf,MAAM,MAAM,MAAM,QAAQ;gBAC1B,IAAI;oBACF,UAAU;gBACZ;gBACA;8CAAO,IAAM,IAAI;4BACf,UAAU;wBACZ;;YACF;QACF;iCAAG;QAAC;QAAa;QAAK;QAAK;KAAI;IAE/B,EAAE;IACF,EAAE;IACF,EAAE;IAEF,MAAM,SAAS,yBAAyB,QAAQ,yBAAyB,KAAK,IAAI,uBAAuB;IACzG,MAAM,SAAS,UAAU,OAAO,KAAK,IAAI,OAAO,aAAa,CAAC,EAAE;IAChE,MAAM,6BAA6B,UAAU,QAAQ,CAAC,wBAAwB,OAAO,4BAA4B,KAAK,OAAO,KAAK,IAAI,qBAAqB,CAAC,EAAE;IAC9J,MAAM,kBAAkB,UAAU,QAAQ,CAAC,wBAAwB,OAAO,eAAe,KAAK,OAAO,KAAK,IAAI,qBAAqB,CAAC,EAAE;IACtI,MAAM,oBAAoB;QACxB;QACA,GAAG,YAAY;IACjB;IACA,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,oBAAoB,QAAQ,EAAE;QACpE,OAAO;IACT,GAAG,CAAC,gBAAgB,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,4RAAA,CAAA,WAAQ,EAAE;QAC7D,UAAU;IACZ,GAAG,SAAS,oBAAoB,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,sXAAA,CAAA,eAAY,EAAE,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;QACtF,KAAK;IACP,GAAG,sBAAsB,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,2XAAA,CAAA,qBAAkB,EAAE,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;QACrF,KAAK;IACP,GAAG,sBAAsB,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,iXAAA,CAAA,WAAQ,EAAE,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;QAC3E,KAAK;QACL,UAAU,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,sBAAsB;YAC/D,MAAM,sMAAA,CAAA,aAAgB;QACxB;IACF,GAAG,UAAU;QACX,QAAQ;QACR,OAAO;QACP,4BAA4B;QAC5B,iBAAiB;QACjB,MAAM;QACN,cAAc;QACd,QAAQ;QACR,cAAc;QACd,OAAO;QACP,cAAc,KAAK,EAAE;QACrB,SAAS;IACX;AACF;AACA,MAAM,kBAAkB,IAAM,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 11825, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}