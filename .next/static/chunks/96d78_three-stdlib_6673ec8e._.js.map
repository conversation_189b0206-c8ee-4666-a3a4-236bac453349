{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "SelectionBox.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/interactive/SelectionBox.js"], "sourcesContent": ["import { Frustum, Vector3 } from 'three'\n\nconst frustum = /* @__PURE__ */ new Frustum()\nconst center = /* @__PURE__ */ new Vector3()\n\nconst tmpPoint = /* @__PURE__ */ new Vector3()\n\nconst vecNear = /* @__PURE__ */ new Vector3()\nconst vecTopLeft = /* @__PURE__ */ new Vector3()\nconst vecTopRight = /* @__PURE__ */ new Vector3()\nconst vecDownRight = /* @__PURE__ */ new Vector3()\nconst vecDownLeft = /* @__PURE__ */ new Vector3()\n\nconst vecFarTopLeft = /* @__PURE__ */ new Vector3()\nconst vecFarTopRight = /* @__PURE__ */ new Vector3()\nconst vecFarDownRight = /* @__PURE__ */ new Vector3()\nconst vecFarDownLeft = /* @__PURE__ */ new Vector3()\n\nconst vectemp1 = /* @__PURE__ */ new Vector3()\nconst vectemp2 = /* @__PURE__ */ new Vector3()\nconst vectemp3 = /* @__PURE__ */ new Vector3()\n\nclass SelectionBox {\n  constructor(camera, scene, deep) {\n    this.camera = camera\n    this.scene = scene\n    this.startPoint = new Vector3()\n    this.endPoint = new Vector3()\n    this.collection = []\n    this.deep = deep || Number.MAX_VALUE\n  }\n\n  select(startPoint, endPoint) {\n    this.startPoint = startPoint || this.startPoint\n    this.endPoint = endPoint || this.endPoint\n    this.collection = []\n\n    this.updateFrustum(this.startPoint, this.endPoint)\n    this.searchChildInFrustum(frustum, this.scene)\n\n    return this.collection\n  }\n\n  updateFrustum(startPoint, endPoint) {\n    startPoint = startPoint || this.startPoint\n    endPoint = endPoint || this.endPoint\n\n    // Avoid invalid frustum\n\n    if (startPoint.x === endPoint.x) {\n      endPoint.x += Number.EPSILON\n    }\n\n    if (startPoint.y === endPoint.y) {\n      endPoint.y += Number.EPSILON\n    }\n\n    this.camera.updateProjectionMatrix()\n    this.camera.updateMatrixWorld()\n\n    if (this.camera.isPerspectiveCamera) {\n      tmpPoint.copy(startPoint)\n      tmpPoint.x = Math.min(startPoint.x, endPoint.x)\n      tmpPoint.y = Math.max(startPoint.y, endPoint.y)\n      endPoint.x = Math.max(startPoint.x, endPoint.x)\n      endPoint.y = Math.min(startPoint.y, endPoint.y)\n\n      vecNear.setFromMatrixPosition(this.camera.matrixWorld)\n      vecTopLeft.copy(tmpPoint)\n      vecTopRight.set(endPoint.x, tmpPoint.y, 0)\n      vecDownRight.copy(endPoint)\n      vecDownLeft.set(tmpPoint.x, endPoint.y, 0)\n\n      vecTopLeft.unproject(this.camera)\n      vecTopRight.unproject(this.camera)\n      vecDownRight.unproject(this.camera)\n      vecDownLeft.unproject(this.camera)\n\n      vectemp1.copy(vecTopLeft).sub(vecNear)\n      vectemp2.copy(vecTopRight).sub(vecNear)\n      vectemp3.copy(vecDownRight).sub(vecNear)\n      vectemp1.normalize()\n      vectemp2.normalize()\n      vectemp3.normalize()\n\n      vectemp1.multiplyScalar(this.deep)\n      vectemp2.multiplyScalar(this.deep)\n      vectemp3.multiplyScalar(this.deep)\n      vectemp1.add(vecNear)\n      vectemp2.add(vecNear)\n      vectemp3.add(vecNear)\n\n      var planes = frustum.planes\n\n      planes[0].setFromCoplanarPoints(vecNear, vecTopLeft, vecTopRight)\n      planes[1].setFromCoplanarPoints(vecNear, vecTopRight, vecDownRight)\n      planes[2].setFromCoplanarPoints(vecDownRight, vecDownLeft, vecNear)\n      planes[3].setFromCoplanarPoints(vecDownLeft, vecTopLeft, vecNear)\n      planes[4].setFromCoplanarPoints(vecTopRight, vecDownRight, vecDownLeft)\n      planes[5].setFromCoplanarPoints(vectemp3, vectemp2, vectemp1)\n      planes[5].normal.multiplyScalar(-1)\n    } else if (this.camera.isOrthographicCamera) {\n      const left = Math.min(startPoint.x, endPoint.x)\n      const top = Math.max(startPoint.y, endPoint.y)\n      const right = Math.max(startPoint.x, endPoint.x)\n      const down = Math.min(startPoint.y, endPoint.y)\n\n      vecTopLeft.set(left, top, -1)\n      vecTopRight.set(right, top, -1)\n      vecDownRight.set(right, down, -1)\n      vecDownLeft.set(left, down, -1)\n\n      vecFarTopLeft.set(left, top, 1)\n      vecFarTopRight.set(right, top, 1)\n      vecFarDownRight.set(right, down, 1)\n      vecFarDownLeft.set(left, down, 1)\n\n      vecTopLeft.unproject(this.camera)\n      vecTopRight.unproject(this.camera)\n      vecDownRight.unproject(this.camera)\n      vecDownLeft.unproject(this.camera)\n\n      vecFarTopLeft.unproject(this.camera)\n      vecFarTopRight.unproject(this.camera)\n      vecFarDownRight.unproject(this.camera)\n      vecFarDownLeft.unproject(this.camera)\n\n      var planes = frustum.planes\n\n      planes[0].setFromCoplanarPoints(vecTopLeft, vecFarTopLeft, vecFarTopRight)\n      planes[1].setFromCoplanarPoints(vecTopRight, vecFarTopRight, vecFarDownRight)\n      planes[2].setFromCoplanarPoints(vecFarDownRight, vecFarDownLeft, vecDownLeft)\n      planes[3].setFromCoplanarPoints(vecFarDownLeft, vecFarTopLeft, vecTopLeft)\n      planes[4].setFromCoplanarPoints(vecTopRight, vecDownRight, vecDownLeft)\n      planes[5].setFromCoplanarPoints(vecFarDownRight, vecFarTopRight, vecFarTopLeft)\n      planes[5].normal.multiplyScalar(-1)\n    } else {\n      console.error('THREE.SelectionBox: Unsupported camera type.')\n    }\n  }\n\n  searchChildInFrustum(frustum, object) {\n    if (object.isMesh || object.isLine || object.isPoints) {\n      if (object.material !== undefined) {\n        if (object.geometry.boundingSphere === null) object.geometry.computeBoundingSphere()\n\n        center.copy(object.geometry.boundingSphere.center)\n\n        center.applyMatrix4(object.matrixWorld)\n\n        if (frustum.containsPoint(center)) {\n          this.collection.push(object)\n        }\n      }\n    }\n\n    if (object.children.length > 0) {\n      for (let x = 0; x < object.children.length; x++) {\n        this.searchChildInFrustum(frustum, object.children[x])\n      }\n    }\n  }\n}\n\nexport { SelectionBox }\n"], "names": ["frustum"], "mappings": ";;;;;AAEA,MAAM,UAA0B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAC7C,MAAM,SAAyB,aAAA,GAAA,2MAAI,UAAA,CAAS;AAE5C,MAAM,WAA2B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAE9C,MAAM,UAA0B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAC7C,MAAM,aAA6B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAChD,MAAM,cAA8B,aAAA,GAAA,2MAAI,UAAA,CAAS;AACjD,MAAM,eAA+B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAClD,MAAM,cAA8B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAEjD,MAAM,gBAAgC,aAAA,GAAA,2MAAI,UAAA,CAAS;AACnD,MAAM,iBAAiC,aAAA,GAAA,2MAAI,UAAA,CAAS;AACpD,MAAM,kBAAkC,aAAA,GAAA,2MAAI,UAAA,CAAS;AACrD,MAAM,iBAAiC,aAAA,GAAA,2MAAI,UAAA,CAAS;AAEpD,MAAM,WAA2B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAC9C,MAAM,WAA2B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAC9C,MAAM,WAA2B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAE9C,MAAM,aAAa;IACjB,YAAY,MAAA,EAAQ,KAAA,EAAO,IAAA,CAAM;QAC/B,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,KAAA,GAAQ;QACb,IAAA,CAAK,UAAA,GAAa,2MAAI,UAAA,CAAS;QAC/B,IAAA,CAAK,QAAA,GAAW,2MAAI,UAAA,CAAS;QAC7B,IAAA,CAAK,UAAA,GAAa,CAAE,CAAA;QACpB,IAAA,CAAK,IAAA,GAAO,QAAQ,OAAO,SAAA;IAC5B;IAED,OAAO,UAAA,EAAY,QAAA,EAAU;QAC3B,IAAA,CAAK,UAAA,GAAa,cAAc,IAAA,CAAK,UAAA;QACrC,IAAA,CAAK,QAAA,GAAW,YAAY,IAAA,CAAK,QAAA;QACjC,IAAA,CAAK,UAAA,GAAa,CAAE,CAAA;QAEpB,IAAA,CAAK,aAAA,CAAc,IAAA,CAAK,UAAA,EAAY,IAAA,CAAK,QAAQ;QACjD,IAAA,CAAK,oBAAA,CAAqB,SAAS,IAAA,CAAK,KAAK;QAE7C,OAAO,IAAA,CAAK,UAAA;IACb;IAED,cAAc,UAAA,EAAY,QAAA,EAAU;QAClC,aAAa,cAAc,IAAA,CAAK,UAAA;QAChC,WAAW,YAAY,IAAA,CAAK,QAAA;QAI5B,IAAI,WAAW,CAAA,KAAM,SAAS,CAAA,EAAG;YAC/B,SAAS,CAAA,IAAK,OAAO,OAAA;QACtB;QAED,IAAI,WAAW,CAAA,KAAM,SAAS,CAAA,EAAG;YAC/B,SAAS,CAAA,IAAK,OAAO,OAAA;QACtB;QAED,IAAA,CAAK,MAAA,CAAO,sBAAA,CAAwB;QACpC,IAAA,CAAK,MAAA,CAAO,iBAAA,CAAmB;QAE/B,IAAI,IAAA,CAAK,MAAA,CAAO,mBAAA,EAAqB;YACnC,SAAS,IAAA,CAAK,UAAU;YACxB,SAAS,CAAA,GAAI,KAAK,GAAA,CAAI,WAAW,CAAA,EAAG,SAAS,CAAC;YAC9C,SAAS,CAAA,GAAI,KAAK,GAAA,CAAI,WAAW,CAAA,EAAG,SAAS,CAAC;YAC9C,SAAS,CAAA,GAAI,KAAK,GAAA,CAAI,WAAW,CAAA,EAAG,SAAS,CAAC;YAC9C,SAAS,CAAA,GAAI,KAAK,GAAA,CAAI,WAAW,CAAA,EAAG,SAAS,CAAC;YAE9C,QAAQ,qBAAA,CAAsB,IAAA,CAAK,MAAA,CAAO,WAAW;YACrD,WAAW,IAAA,CAAK,QAAQ;YACxB,YAAY,GAAA,CAAI,SAAS,CAAA,EAAG,SAAS,CAAA,EAAG,CAAC;YACzC,aAAa,IAAA,CAAK,QAAQ;YAC1B,YAAY,GAAA,CAAI,SAAS,CAAA,EAAG,SAAS,CAAA,EAAG,CAAC;YAEzC,WAAW,SAAA,CAAU,IAAA,CAAK,MAAM;YAChC,YAAY,SAAA,CAAU,IAAA,CAAK,MAAM;YACjC,aAAa,SAAA,CAAU,IAAA,CAAK,MAAM;YAClC,YAAY,SAAA,CAAU,IAAA,CAAK,MAAM;YAEjC,SAAS,IAAA,CAAK,UAAU,EAAE,GAAA,CAAI,OAAO;YACrC,SAAS,IAAA,CAAK,WAAW,EAAE,GAAA,CAAI,OAAO;YACtC,SAAS,IAAA,CAAK,YAAY,EAAE,GAAA,CAAI,OAAO;YACvC,SAAS,SAAA,CAAW;YACpB,SAAS,SAAA,CAAW;YACpB,SAAS,SAAA,CAAW;YAEpB,SAAS,cAAA,CAAe,IAAA,CAAK,IAAI;YACjC,SAAS,cAAA,CAAe,IAAA,CAAK,IAAI;YACjC,SAAS,cAAA,CAAe,IAAA,CAAK,IAAI;YACjC,SAAS,GAAA,CAAI,OAAO;YACpB,SAAS,GAAA,CAAI,OAAO;YACpB,SAAS,GAAA,CAAI,OAAO;YAEpB,IAAI,SAAS,QAAQ,MAAA;YAErB,MAAA,CAAO,CAAC,CAAA,CAAE,qBAAA,CAAsB,SAAS,YAAY,WAAW;YAChE,MAAA,CAAO,CAAC,CAAA,CAAE,qBAAA,CAAsB,SAAS,aAAa,YAAY;YAClE,MAAA,CAAO,CAAC,CAAA,CAAE,qBAAA,CAAsB,cAAc,aAAa,OAAO;YAClE,MAAA,CAAO,CAAC,CAAA,CAAE,qBAAA,CAAsB,aAAa,YAAY,OAAO;YAChE,MAAA,CAAO,CAAC,CAAA,CAAE,qBAAA,CAAsB,aAAa,cAAc,WAAW;YACtE,MAAA,CAAO,CAAC,CAAA,CAAE,qBAAA,CAAsB,UAAU,UAAU,QAAQ;YAC5D,MAAA,CAAO,CAAC,CAAA,CAAE,MAAA,CAAO,cAAA,CAAe,CAAA,CAAE;QACxC,OAAA,IAAe,IAAA,CAAK,MAAA,CAAO,oBAAA,EAAsB;YAC3C,MAAM,OAAO,KAAK,GAAA,CAAI,WAAW,CAAA,EAAG,SAAS,CAAC;YAC9C,MAAM,MAAM,KAAK,GAAA,CAAI,WAAW,CAAA,EAAG,SAAS,CAAC;YAC7C,MAAM,QAAQ,KAAK,GAAA,CAAI,WAAW,CAAA,EAAG,SAAS,CAAC;YAC/C,MAAM,OAAO,KAAK,GAAA,CAAI,WAAW,CAAA,EAAG,SAAS,CAAC;YAE9C,WAAW,GAAA,CAAI,MAAM,KAAK,CAAA,CAAE;YAC5B,YAAY,GAAA,CAAI,OAAO,KAAK,CAAA,CAAE;YAC9B,aAAa,GAAA,CAAI,OAAO,MAAM,CAAA,CAAE;YAChC,YAAY,GAAA,CAAI,MAAM,MAAM,CAAA,CAAE;YAE9B,cAAc,GAAA,CAAI,MAAM,KAAK,CAAC;YAC9B,eAAe,GAAA,CAAI,OAAO,KAAK,CAAC;YAChC,gBAAgB,GAAA,CAAI,OAAO,MAAM,CAAC;YAClC,eAAe,GAAA,CAAI,MAAM,MAAM,CAAC;YAEhC,WAAW,SAAA,CAAU,IAAA,CAAK,MAAM;YAChC,YAAY,SAAA,CAAU,IAAA,CAAK,MAAM;YACjC,aAAa,SAAA,CAAU,IAAA,CAAK,MAAM;YAClC,YAAY,SAAA,CAAU,IAAA,CAAK,MAAM;YAEjC,cAAc,SAAA,CAAU,IAAA,CAAK,MAAM;YACnC,eAAe,SAAA,CAAU,IAAA,CAAK,MAAM;YACpC,gBAAgB,SAAA,CAAU,IAAA,CAAK,MAAM;YACrC,eAAe,SAAA,CAAU,IAAA,CAAK,MAAM;YAEpC,IAAI,SAAS,QAAQ,MAAA;YAErB,MAAA,CAAO,CAAC,CAAA,CAAE,qBAAA,CAAsB,YAAY,eAAe,cAAc;YACzE,MAAA,CAAO,CAAC,CAAA,CAAE,qBAAA,CAAsB,aAAa,gBAAgB,eAAe;YAC5E,MAAA,CAAO,CAAC,CAAA,CAAE,qBAAA,CAAsB,iBAAiB,gBAAgB,WAAW;YAC5E,MAAA,CAAO,CAAC,CAAA,CAAE,qBAAA,CAAsB,gBAAgB,eAAe,UAAU;YACzE,MAAA,CAAO,CAAC,CAAA,CAAE,qBAAA,CAAsB,aAAa,cAAc,WAAW;YACtE,MAAA,CAAO,CAAC,CAAA,CAAE,qBAAA,CAAsB,iBAAiB,gBAAgB,aAAa;YAC9E,MAAA,CAAO,CAAC,CAAA,CAAE,MAAA,CAAO,cAAA,CAAe,CAAA,CAAE;QACxC,OAAW;YACL,QAAQ,KAAA,CAAM,8CAA8C;QAC7D;IACF;IAED,qBAAqBA,QAAAA,EAAS,MAAA,EAAQ;QACpC,IAAI,OAAO,MAAA,IAAU,OAAO,MAAA,IAAU,OAAO,QAAA,EAAU;YACrD,IAAI,OAAO,QAAA,KAAa,KAAA,GAAW;gBACjC,IAAI,OAAO,QAAA,CAAS,cAAA,KAAmB,MAAM,OAAO,QAAA,CAAS,qBAAA,CAAuB;gBAEpF,OAAO,IAAA,CAAK,OAAO,QAAA,CAAS,cAAA,CAAe,MAAM;gBAEjD,OAAO,YAAA,CAAa,OAAO,WAAW;gBAEtC,IAAIA,SAAQ,aAAA,CAAc,MAAM,GAAG;oBACjC,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,MAAM;gBAC5B;YACF;QACF;QAED,IAAI,OAAO,QAAA,CAAS,MAAA,GAAS,GAAG;YAC9B,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,QAAA,CAAS,MAAA,EAAQ,IAAK;gBAC/C,IAAA,CAAK,oBAAA,CAAqBA,UAAS,OAAO,QAAA,CAAS,CAAC,CAAC;YACtD;QACF;IACF;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "file": "LineSegmentsGeometry.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/lines/LineSegmentsGeometry.js"], "sourcesContent": ["import {\n  Box3,\n  Float32<PERSON><PERSON>er<PERSON>ttribute,\n  InstancedBufferGeometry,\n  InstancedInterleavedBuffer,\n  InterleavedBufferAttribute,\n  Sphere,\n  Vector3,\n  WireframeGeometry,\n} from 'three'\n\nconst _box = /* @__PURE__ */ new Box3()\nconst _vector = /* @__PURE__ */ new Vector3()\n\nclass LineSegmentsGeometry extends InstancedBufferGeometry {\n  constructor() {\n    super()\n\n    this.isLineSegmentsGeometry = true\n\n    this.type = 'LineSegmentsGeometry'\n\n    const positions = [-1, 2, 0, 1, 2, 0, -1, 1, 0, 1, 1, 0, -1, 0, 0, 1, 0, 0, -1, -1, 0, 1, -1, 0]\n    const uvs = [-1, 2, 1, 2, -1, 1, 1, 1, -1, -1, 1, -1, -1, -2, 1, -2]\n    const index = [0, 2, 1, 2, 3, 1, 2, 4, 3, 4, 5, 3, 4, 6, 5, 6, 7, 5]\n\n    this.setIndex(index)\n    this.setAttribute('position', new Float32BufferAttribute(positions, 3))\n    this.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n  }\n\n  applyMatrix4(matrix) {\n    const start = this.attributes.instanceStart\n    const end = this.attributes.instanceEnd\n\n    if (start !== undefined) {\n      start.applyMatrix4(matrix)\n\n      end.applyMatrix4(matrix)\n\n      start.needsUpdate = true\n    }\n\n    if (this.boundingBox !== null) {\n      this.computeBoundingBox()\n    }\n\n    if (this.boundingSphere !== null) {\n      this.computeBoundingSphere()\n    }\n\n    return this\n  }\n\n  setPositions(array) {\n    let lineSegments\n\n    if (array instanceof Float32Array) {\n      lineSegments = array\n    } else if (Array.isArray(array)) {\n      lineSegments = new Float32Array(array)\n    }\n\n    const instanceBuffer = new InstancedInterleavedBuffer(lineSegments, 6, 1) // xyz, xyz\n\n    this.setAttribute('instanceStart', new InterleavedBufferAttribute(instanceBuffer, 3, 0)) // xyz\n    this.setAttribute('instanceEnd', new InterleavedBufferAttribute(instanceBuffer, 3, 3)) // xyz\n\n    //\n\n    this.computeBoundingBox()\n    this.computeBoundingSphere()\n\n    return this\n  }\n\n  setColors(array, itemSize = 3) {\n    let colors\n\n    if (array instanceof Float32Array) {\n      colors = array\n    } else if (Array.isArray(array)) {\n      colors = new Float32Array(array)\n    }\n\n    const instanceColorBuffer = new InstancedInterleavedBuffer(colors, itemSize * 2, 1) // rgb(a), rgb(a)\n\n    this.setAttribute('instanceColorStart', new InterleavedBufferAttribute(instanceColorBuffer, itemSize, 0)) // rgb(a)\n    this.setAttribute('instanceColorEnd', new InterleavedBufferAttribute(instanceColorBuffer, itemSize, itemSize)) // rgb(a)\n\n    return this\n  }\n\n  fromWireframeGeometry(geometry) {\n    this.setPositions(geometry.attributes.position.array)\n\n    return this\n  }\n\n  fromEdgesGeometry(geometry) {\n    this.setPositions(geometry.attributes.position.array)\n\n    return this\n  }\n\n  fromMesh(mesh) {\n    this.fromWireframeGeometry(new WireframeGeometry(mesh.geometry))\n\n    // set colors, maybe\n\n    return this\n  }\n\n  fromLineSegments(lineSegments) {\n    const geometry = lineSegments.geometry\n\n    this.setPositions(geometry.attributes.position.array) // assumes non-indexed\n\n    // set colors, maybe\n\n    return this\n  }\n\n  computeBoundingBox() {\n    if (this.boundingBox === null) {\n      this.boundingBox = new Box3()\n    }\n\n    const start = this.attributes.instanceStart\n    const end = this.attributes.instanceEnd\n\n    if (start !== undefined && end !== undefined) {\n      this.boundingBox.setFromBufferAttribute(start)\n\n      _box.setFromBufferAttribute(end)\n\n      this.boundingBox.union(_box)\n    }\n  }\n\n  computeBoundingSphere() {\n    if (this.boundingSphere === null) {\n      this.boundingSphere = new Sphere()\n    }\n\n    if (this.boundingBox === null) {\n      this.computeBoundingBox()\n    }\n\n    const start = this.attributes.instanceStart\n    const end = this.attributes.instanceEnd\n\n    if (start !== undefined && end !== undefined) {\n      const center = this.boundingSphere.center\n\n      this.boundingBox.getCenter(center)\n\n      let maxRadiusSq = 0\n\n      for (let i = 0, il = start.count; i < il; i++) {\n        _vector.fromBufferAttribute(start, i)\n        maxRadiusSq = Math.max(maxRadiusSq, center.distanceToSquared(_vector))\n\n        _vector.fromBufferAttribute(end, i)\n        maxRadiusSq = Math.max(maxRadiusSq, center.distanceToSquared(_vector))\n      }\n\n      this.boundingSphere.radius = Math.sqrt(maxRadiusSq)\n\n      if (isNaN(this.boundingSphere.radius)) {\n        console.error(\n          'THREE.LineSegmentsGeometry.computeBoundingSphere(): Computed radius is NaN. The instanced position data is likely to have NaN values.',\n          this,\n        )\n      }\n    }\n  }\n\n  toJSON() {\n    // todo\n  }\n\n  applyMatrix(matrix) {\n    console.warn('THREE.LineSegmentsGeometry: applyMatrix() has been renamed to applyMatrix4().')\n\n    return this.applyMatrix4(matrix)\n  }\n}\n\nexport { LineSegmentsGeometry }\n"], "names": [], "mappings": ";;;;;AAWA,MAAM,OAAuB,aAAA,GAAA,2MAAI,OAAA,CAAM;AACvC,MAAM,UAA0B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAE7C,MAAM,oOAA6B,0BAAA,CAAwB;IACzD,aAAc;QACZ,KAAA,CAAO;QAEP,IAAA,CAAK,sBAAA,GAAyB;QAE9B,IAAA,CAAK,IAAA,GAAO;QAEZ,MAAM,YAAY;YAAC,CAAA;YAAI;YAAG;YAAG;YAAG;YAAG;YAAG,CAAA;YAAI;YAAG;YAAG;YAAG;YAAG;YAAG,CAAA;YAAI;YAAG;YAAG;YAAG;YAAG;YAAG,CAAA;YAAI,CAAA;YAAI;YAAG;YAAG,CAAA;YAAI,CAAC;SAAA;QAC/F,MAAM,MAAM;YAAC,CAAA;YAAI;YAAG;YAAG;YAAG,CAAA;YAAI;YAAG;YAAG;YAAG,CAAA;YAAI,CAAA;YAAI;YAAG,CAAA;YAAI,CAAA;YAAI,CAAA;YAAI;YAAG,CAAA,CAAE;SAAA;QACnE,MAAM,QAAQ;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG,CAAC;SAAA;QAEnE,IAAA,CAAK,QAAA,CAAS,KAAK;QACnB,IAAA,CAAK,YAAA,CAAa,YAAY,2MAAI,yBAAA,CAAuB,WAAW,CAAC,CAAC;QACtE,IAAA,CAAK,YAAA,CAAa,MAAM,2MAAI,yBAAA,CAAuB,KAAK,CAAC,CAAC;IAC3D;IAED,aAAa,MAAA,EAAQ;QACnB,MAAM,QAAQ,IAAA,CAAK,UAAA,CAAW,aAAA;QAC9B,MAAM,MAAM,IAAA,CAAK,UAAA,CAAW,WAAA;QAE5B,IAAI,UAAU,KAAA,GAAW;YACvB,MAAM,YAAA,CAAa,MAAM;YAEzB,IAAI,YAAA,CAAa,MAAM;YAEvB,MAAM,WAAA,GAAc;QACrB;QAED,IAAI,IAAA,CAAK,WAAA,KAAgB,MAAM;YAC7B,IAAA,CAAK,kBAAA,CAAoB;QAC1B;QAED,IAAI,IAAA,CAAK,cAAA,KAAmB,MAAM;YAChC,IAAA,CAAK,qBAAA,CAAuB;QAC7B;QAED,OAAO,IAAA;IACR;IAED,aAAa,KAAA,EAAO;QAClB,IAAI;QAEJ,IAAI,iBAAiB,cAAc;YACjC,eAAe;QAChB,OAAA,IAAU,MAAM,OAAA,CAAQ,KAAK,GAAG;YAC/B,eAAe,IAAI,aAAa,KAAK;QACtC;QAED,MAAM,iBAAiB,2MAAI,6BAAA,CAA2B,cAAc,GAAG,CAAC;QAExE,IAAA,CAAK,YAAA,CAAa,iBAAiB,2MAAI,6BAAA,CAA2B,gBAAgB,GAAG,CAAC,CAAC;QACvF,IAAA,CAAK,YAAA,CAAa,eAAe,2MAAI,6BAAA,CAA2B,gBAAgB,GAAG,CAAC,CAAC;QAIrF,IAAA,CAAK,kBAAA,CAAoB;QACzB,IAAA,CAAK,qBAAA,CAAuB;QAE5B,OAAO,IAAA;IACR;IAED,UAAU,KAAA,EAAO,WAAW,CAAA,EAAG;QAC7B,IAAI;QAEJ,IAAI,iBAAiB,cAAc;YACjC,SAAS;QACV,OAAA,IAAU,MAAM,OAAA,CAAQ,KAAK,GAAG;YAC/B,SAAS,IAAI,aAAa,KAAK;QAChC;QAED,MAAM,sBAAsB,2MAAI,6BAAA,CAA2B,QAAQ,WAAW,GAAG,CAAC;QAElF,IAAA,CAAK,YAAA,CAAa,sBAAsB,2MAAI,6BAAA,CAA2B,qBAAqB,UAAU,CAAC,CAAC;QACxG,IAAA,CAAK,YAAA,CAAa,oBAAoB,2MAAI,6BAAA,CAA2B,qBAAqB,UAAU,QAAQ,CAAC;QAE7G,OAAO,IAAA;IACR;IAED,sBAAsB,QAAA,EAAU;QAC9B,IAAA,CAAK,YAAA,CAAa,SAAS,UAAA,CAAW,QAAA,CAAS,KAAK;QAEpD,OAAO,IAAA;IACR;IAED,kBAAkB,QAAA,EAAU;QAC1B,IAAA,CAAK,YAAA,CAAa,SAAS,UAAA,CAAW,QAAA,CAAS,KAAK;QAEpD,OAAO,IAAA;IACR;IAED,SAAS,IAAA,EAAM;QACb,IAAA,CAAK,qBAAA,CAAsB,2MAAI,oBAAA,CAAkB,KAAK,QAAQ,CAAC;QAI/D,OAAO,IAAA;IACR;IAED,iBAAiB,YAAA,EAAc;QAC7B,MAAM,WAAW,aAAa,QAAA;QAE9B,IAAA,CAAK,YAAA,CAAa,SAAS,UAAA,CAAW,QAAA,CAAS,KAAK;QAIpD,OAAO,IAAA;IACR;IAED,qBAAqB;QACnB,IAAI,IAAA,CAAK,WAAA,KAAgB,MAAM;YAC7B,IAAA,CAAK,WAAA,GAAc,2MAAI,OAAA,CAAM;QAC9B;QAED,MAAM,QAAQ,IAAA,CAAK,UAAA,CAAW,aAAA;QAC9B,MAAM,MAAM,IAAA,CAAK,UAAA,CAAW,WAAA;QAE5B,IAAI,UAAU,KAAA,KAAa,QAAQ,KAAA,GAAW;YAC5C,IAAA,CAAK,WAAA,CAAY,sBAAA,CAAuB,KAAK;YAE7C,KAAK,sBAAA,CAAuB,GAAG;YAE/B,IAAA,CAAK,WAAA,CAAY,KAAA,CAAM,IAAI;QAC5B;IACF;IAED,wBAAwB;QACtB,IAAI,IAAA,CAAK,cAAA,KAAmB,MAAM;YAChC,IAAA,CAAK,cAAA,GAAiB,2MAAI,SAAA,CAAQ;QACnC;QAED,IAAI,IAAA,CAAK,WAAA,KAAgB,MAAM;YAC7B,IAAA,CAAK,kBAAA,CAAoB;QAC1B;QAED,MAAM,QAAQ,IAAA,CAAK,UAAA,CAAW,aAAA;QAC9B,MAAM,MAAM,IAAA,CAAK,UAAA,CAAW,WAAA;QAE5B,IAAI,UAAU,KAAA,KAAa,QAAQ,KAAA,GAAW;YAC5C,MAAM,SAAS,IAAA,CAAK,cAAA,CAAe,MAAA;YAEnC,IAAA,CAAK,WAAA,CAAY,SAAA,CAAU,MAAM;YAEjC,IAAI,cAAc;YAElB,IAAA,IAAS,IAAI,GAAG,KAAK,MAAM,KAAA,EAAO,IAAI,IAAI,IAAK;gBAC7C,QAAQ,mBAAA,CAAoB,OAAO,CAAC;gBACpC,cAAc,KAAK,GAAA,CAAI,aAAa,OAAO,iBAAA,CAAkB,OAAO,CAAC;gBAErE,QAAQ,mBAAA,CAAoB,KAAK,CAAC;gBAClC,cAAc,KAAK,GAAA,CAAI,aAAa,OAAO,iBAAA,CAAkB,OAAO,CAAC;YACtE;YAED,IAAA,CAAK,cAAA,CAAe,MAAA,GAAS,KAAK,IAAA,CAAK,WAAW;YAElD,IAAI,MAAM,IAAA,CAAK,cAAA,CAAe,MAAM,GAAG;gBACrC,QAAQ,KAAA,CACN,yIACA,IAAA;YAEH;QACF;IACF;IAED,SAAS,CAER;IAED,YAAY,MAAA,EAAQ;QAClB,QAAQ,IAAA,CAAK,+EAA+E;QAE5F,OAAO,IAAA,CAAK,YAAA,CAAa,MAAM;IAChC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "file": "LineMaterial.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/lines/LineMaterial.js"], "sourcesContent": ["/**\n * parameters = {\n *  color: <hex>,\n *  linewidth: <float>,\n *  dashed: <boolean>,\n *  dashScale: <float>,\n *  dashSize: <float>,\n *  dashOffset: <float>,\n *  gapSize: <float>,\n * }\n */\n\nimport { ShaderMaterial, UniformsLib, UniformsUtils, Vector2 } from 'three'\nimport { version } from '../_polyfill/constants'\n\nclass LineMaterial extends ShaderMaterial {\n  constructor(parameters) {\n    super({\n      type: 'LineMaterial',\n\n      uniforms: UniformsUtils.clone(\n        UniformsUtils.merge([\n          UniformsLib.common,\n          UniformsLib.fog,\n          {\n            worldUnits: { value: 1 },\n            linewidth: { value: 1 },\n            resolution: { value: new Vector2(1, 1) },\n            dashOffset: { value: 0 },\n            dashScale: { value: 1 },\n            dashSize: { value: 1 },\n            gapSize: { value: 1 }, // todo FIX - maybe change to totalSize\n          },\n        ]),\n      ),\n\n      vertexShader: /* glsl */ `\n\t\t\t\t#include <common>\n\t\t\t\t#include <fog_pars_vertex>\n\t\t\t\t#include <logdepthbuf_pars_vertex>\n\t\t\t\t#include <clipping_planes_pars_vertex>\n\n\t\t\t\tuniform float linewidth;\n\t\t\t\tuniform vec2 resolution;\n\n\t\t\t\tattribute vec3 instanceStart;\n\t\t\t\tattribute vec3 instanceEnd;\n\n\t\t\t\t#ifdef USE_COLOR\n\t\t\t\t\t#ifdef USE_LINE_COLOR_ALPHA\n\t\t\t\t\t\tvarying vec4 vLineColor;\n\t\t\t\t\t\tattribute vec4 instanceColorStart;\n\t\t\t\t\t\tattribute vec4 instanceColorEnd;\n\t\t\t\t\t#else\n\t\t\t\t\t\tvarying vec3 vLineColor;\n\t\t\t\t\t\tattribute vec3 instanceColorStart;\n\t\t\t\t\t\tattribute vec3 instanceColorEnd;\n\t\t\t\t\t#endif\n\t\t\t\t#endif\n\n\t\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\t\tvarying vec4 worldPos;\n\t\t\t\t\tvarying vec3 worldStart;\n\t\t\t\t\tvarying vec3 worldEnd;\n\n\t\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\t\tvarying vec2 vUv;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t#else\n\n\t\t\t\t\tvarying vec2 vUv;\n\n\t\t\t\t#endif\n\n\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\tuniform float dashScale;\n\t\t\t\t\tattribute float instanceDistanceStart;\n\t\t\t\t\tattribute float instanceDistanceEnd;\n\t\t\t\t\tvarying float vLineDistance;\n\n\t\t\t\t#endif\n\n\t\t\t\tvoid trimSegment( const in vec4 start, inout vec4 end ) {\n\n\t\t\t\t\t// trim end segment so it terminates between the camera plane and the near plane\n\n\t\t\t\t\t// conservative estimate of the near plane\n\t\t\t\t\tfloat a = projectionMatrix[ 2 ][ 2 ]; // 3nd entry in 3th column\n\t\t\t\t\tfloat b = projectionMatrix[ 3 ][ 2 ]; // 3nd entry in 4th column\n\t\t\t\t\tfloat nearEstimate = - 0.5 * b / a;\n\n\t\t\t\t\tfloat alpha = ( nearEstimate - start.z ) / ( end.z - start.z );\n\n\t\t\t\t\tend.xyz = mix( start.xyz, end.xyz, alpha );\n\n\t\t\t\t}\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\t#ifdef USE_COLOR\n\n\t\t\t\t\t\tvLineColor = ( position.y < 0.5 ) ? instanceColorStart : instanceColorEnd;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\t\tvLineDistance = ( position.y < 0.5 ) ? dashScale * instanceDistanceStart : dashScale * instanceDistanceEnd;\n\t\t\t\t\t\tvUv = uv;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\tfloat aspect = resolution.x / resolution.y;\n\n\t\t\t\t\t// camera space\n\t\t\t\t\tvec4 start = modelViewMatrix * vec4( instanceStart, 1.0 );\n\t\t\t\t\tvec4 end = modelViewMatrix * vec4( instanceEnd, 1.0 );\n\n\t\t\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\t\t\tworldStart = start.xyz;\n\t\t\t\t\t\tworldEnd = end.xyz;\n\n\t\t\t\t\t#else\n\n\t\t\t\t\t\tvUv = uv;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\t// special case for perspective projection, and segments that terminate either in, or behind, the camera plane\n\t\t\t\t\t// clearly the gpu firmware has a way of addressing this issue when projecting into ndc space\n\t\t\t\t\t// but we need to perform ndc-space calculations in the shader, so we must address this issue directly\n\t\t\t\t\t// perhaps there is a more elegant solution -- WestLangley\n\n\t\t\t\t\tbool perspective = ( projectionMatrix[ 2 ][ 3 ] == - 1.0 ); // 4th entry in the 3rd column\n\n\t\t\t\t\tif ( perspective ) {\n\n\t\t\t\t\t\tif ( start.z < 0.0 && end.z >= 0.0 ) {\n\n\t\t\t\t\t\t\ttrimSegment( start, end );\n\n\t\t\t\t\t\t} else if ( end.z < 0.0 && start.z >= 0.0 ) {\n\n\t\t\t\t\t\t\ttrimSegment( end, start );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\t// clip space\n\t\t\t\t\tvec4 clipStart = projectionMatrix * start;\n\t\t\t\t\tvec4 clipEnd = projectionMatrix * end;\n\n\t\t\t\t\t// ndc space\n\t\t\t\t\tvec3 ndcStart = clipStart.xyz / clipStart.w;\n\t\t\t\t\tvec3 ndcEnd = clipEnd.xyz / clipEnd.w;\n\n\t\t\t\t\t// direction\n\t\t\t\t\tvec2 dir = ndcEnd.xy - ndcStart.xy;\n\n\t\t\t\t\t// account for clip-space aspect ratio\n\t\t\t\t\tdir.x *= aspect;\n\t\t\t\t\tdir = normalize( dir );\n\n\t\t\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\t\t\t// get the offset direction as perpendicular to the view vector\n\t\t\t\t\t\tvec3 worldDir = normalize( end.xyz - start.xyz );\n\t\t\t\t\t\tvec3 offset;\n\t\t\t\t\t\tif ( position.y < 0.5 ) {\n\n\t\t\t\t\t\t\toffset = normalize( cross( start.xyz, worldDir ) );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\toffset = normalize( cross( end.xyz, worldDir ) );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// sign flip\n\t\t\t\t\t\tif ( position.x < 0.0 ) offset *= - 1.0;\n\n\t\t\t\t\t\tfloat forwardOffset = dot( worldDir, vec3( 0.0, 0.0, 1.0 ) );\n\n\t\t\t\t\t\t// don't extend the line if we're rendering dashes because we\n\t\t\t\t\t\t// won't be rendering the endcaps\n\t\t\t\t\t\t#ifndef USE_DASH\n\n\t\t\t\t\t\t\t// extend the line bounds to encompass  endcaps\n\t\t\t\t\t\t\tstart.xyz += - worldDir * linewidth * 0.5;\n\t\t\t\t\t\t\tend.xyz += worldDir * linewidth * 0.5;\n\n\t\t\t\t\t\t\t// shift the position of the quad so it hugs the forward edge of the line\n\t\t\t\t\t\t\toffset.xy -= dir * forwardOffset;\n\t\t\t\t\t\t\toffset.z += 0.5;\n\n\t\t\t\t\t\t#endif\n\n\t\t\t\t\t\t// endcaps\n\t\t\t\t\t\tif ( position.y > 1.0 || position.y < 0.0 ) {\n\n\t\t\t\t\t\t\toffset.xy += dir * 2.0 * forwardOffset;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// adjust for linewidth\n\t\t\t\t\t\toffset *= linewidth * 0.5;\n\n\t\t\t\t\t\t// set the world position\n\t\t\t\t\t\tworldPos = ( position.y < 0.5 ) ? start : end;\n\t\t\t\t\t\tworldPos.xyz += offset;\n\n\t\t\t\t\t\t// project the worldpos\n\t\t\t\t\t\tvec4 clip = projectionMatrix * worldPos;\n\n\t\t\t\t\t\t// shift the depth of the projected points so the line\n\t\t\t\t\t\t// segments overlap neatly\n\t\t\t\t\t\tvec3 clipPose = ( position.y < 0.5 ) ? ndcStart : ndcEnd;\n\t\t\t\t\t\tclip.z = clipPose.z * clip.w;\n\n\t\t\t\t\t#else\n\n\t\t\t\t\t\tvec2 offset = vec2( dir.y, - dir.x );\n\t\t\t\t\t\t// undo aspect ratio adjustment\n\t\t\t\t\t\tdir.x /= aspect;\n\t\t\t\t\t\toffset.x /= aspect;\n\n\t\t\t\t\t\t// sign flip\n\t\t\t\t\t\tif ( position.x < 0.0 ) offset *= - 1.0;\n\n\t\t\t\t\t\t// endcaps\n\t\t\t\t\t\tif ( position.y < 0.0 ) {\n\n\t\t\t\t\t\t\toffset += - dir;\n\n\t\t\t\t\t\t} else if ( position.y > 1.0 ) {\n\n\t\t\t\t\t\t\toffset += dir;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// adjust for linewidth\n\t\t\t\t\t\toffset *= linewidth;\n\n\t\t\t\t\t\t// adjust for clip-space to screen-space conversion // maybe resolution should be based on viewport ...\n\t\t\t\t\t\toffset /= resolution.y;\n\n\t\t\t\t\t\t// select end\n\t\t\t\t\t\tvec4 clip = ( position.y < 0.5 ) ? clipStart : clipEnd;\n\n\t\t\t\t\t\t// back to clip space\n\t\t\t\t\t\toffset *= clip.w;\n\n\t\t\t\t\t\tclip.xy += offset;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\tgl_Position = clip;\n\n\t\t\t\t\tvec4 mvPosition = ( position.y < 0.5 ) ? start : end; // this is an approximation\n\n\t\t\t\t\t#include <logdepthbuf_vertex>\n\t\t\t\t\t#include <clipping_planes_vertex>\n\t\t\t\t\t#include <fog_vertex>\n\n\t\t\t\t}\n\t\t\t`,\n      fragmentShader: /* glsl */ `\n\t\t\t\tuniform vec3 diffuse;\n\t\t\t\tuniform float opacity;\n\t\t\t\tuniform float linewidth;\n\n\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\tuniform float dashOffset;\n\t\t\t\t\tuniform float dashSize;\n\t\t\t\t\tuniform float gapSize;\n\n\t\t\t\t#endif\n\n\t\t\t\tvarying float vLineDistance;\n\n\t\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\t\tvarying vec4 worldPos;\n\t\t\t\t\tvarying vec3 worldStart;\n\t\t\t\t\tvarying vec3 worldEnd;\n\n\t\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\t\tvarying vec2 vUv;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t#else\n\n\t\t\t\t\tvarying vec2 vUv;\n\n\t\t\t\t#endif\n\n\t\t\t\t#include <common>\n\t\t\t\t#include <fog_pars_fragment>\n\t\t\t\t#include <logdepthbuf_pars_fragment>\n\t\t\t\t#include <clipping_planes_pars_fragment>\n\n\t\t\t\t#ifdef USE_COLOR\n\t\t\t\t\t#ifdef USE_LINE_COLOR_ALPHA\n\t\t\t\t\t\tvarying vec4 vLineColor;\n\t\t\t\t\t#else\n\t\t\t\t\t\tvarying vec3 vLineColor;\n\t\t\t\t\t#endif\n\t\t\t\t#endif\n\n\t\t\t\tvec2 closestLineToLine(vec3 p1, vec3 p2, vec3 p3, vec3 p4) {\n\n\t\t\t\t\tfloat mua;\n\t\t\t\t\tfloat mub;\n\n\t\t\t\t\tvec3 p13 = p1 - p3;\n\t\t\t\t\tvec3 p43 = p4 - p3;\n\n\t\t\t\t\tvec3 p21 = p2 - p1;\n\n\t\t\t\t\tfloat d1343 = dot( p13, p43 );\n\t\t\t\t\tfloat d4321 = dot( p43, p21 );\n\t\t\t\t\tfloat d1321 = dot( p13, p21 );\n\t\t\t\t\tfloat d4343 = dot( p43, p43 );\n\t\t\t\t\tfloat d2121 = dot( p21, p21 );\n\n\t\t\t\t\tfloat denom = d2121 * d4343 - d4321 * d4321;\n\n\t\t\t\t\tfloat numer = d1343 * d4321 - d1321 * d4343;\n\n\t\t\t\t\tmua = numer / denom;\n\t\t\t\t\tmua = clamp( mua, 0.0, 1.0 );\n\t\t\t\t\tmub = ( d1343 + d4321 * ( mua ) ) / d4343;\n\t\t\t\t\tmub = clamp( mub, 0.0, 1.0 );\n\n\t\t\t\t\treturn vec2( mua, mub );\n\n\t\t\t\t}\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\t#include <clipping_planes_fragment>\n\n\t\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\t\tif ( vUv.y < - 1.0 || vUv.y > 1.0 ) discard; // discard endcaps\n\n\t\t\t\t\t\tif ( mod( vLineDistance + dashOffset, dashSize + gapSize ) > dashSize ) discard; // todo - FIX\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\tfloat alpha = opacity;\n\n\t\t\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\t\t\t// Find the closest points on the view ray and the line segment\n\t\t\t\t\t\tvec3 rayEnd = normalize( worldPos.xyz ) * 1e5;\n\t\t\t\t\t\tvec3 lineDir = worldEnd - worldStart;\n\t\t\t\t\t\tvec2 params = closestLineToLine( worldStart, worldEnd, vec3( 0.0, 0.0, 0.0 ), rayEnd );\n\n\t\t\t\t\t\tvec3 p1 = worldStart + lineDir * params.x;\n\t\t\t\t\t\tvec3 p2 = rayEnd * params.y;\n\t\t\t\t\t\tvec3 delta = p1 - p2;\n\t\t\t\t\t\tfloat len = length( delta );\n\t\t\t\t\t\tfloat norm = len / linewidth;\n\n\t\t\t\t\t\t#ifndef USE_DASH\n\n\t\t\t\t\t\t\t#ifdef USE_ALPHA_TO_COVERAGE\n\n\t\t\t\t\t\t\t\tfloat dnorm = fwidth( norm );\n\t\t\t\t\t\t\t\talpha = 1.0 - smoothstep( 0.5 - dnorm, 0.5 + dnorm, norm );\n\n\t\t\t\t\t\t\t#else\n\n\t\t\t\t\t\t\t\tif ( norm > 0.5 ) {\n\n\t\t\t\t\t\t\t\t\tdiscard;\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t#endif\n\n\t\t\t\t\t\t#endif\n\n\t\t\t\t\t#else\n\n\t\t\t\t\t\t#ifdef USE_ALPHA_TO_COVERAGE\n\n\t\t\t\t\t\t\t// artifacts appear on some hardware if a derivative is taken within a conditional\n\t\t\t\t\t\t\tfloat a = vUv.x;\n\t\t\t\t\t\t\tfloat b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;\n\t\t\t\t\t\t\tfloat len2 = a * a + b * b;\n\t\t\t\t\t\t\tfloat dlen = fwidth( len2 );\n\n\t\t\t\t\t\t\tif ( abs( vUv.y ) > 1.0 ) {\n\n\t\t\t\t\t\t\t\talpha = 1.0 - smoothstep( 1.0 - dlen, 1.0 + dlen, len2 );\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t#else\n\n\t\t\t\t\t\t\tif ( abs( vUv.y ) > 1.0 ) {\n\n\t\t\t\t\t\t\t\tfloat a = vUv.x;\n\t\t\t\t\t\t\t\tfloat b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;\n\t\t\t\t\t\t\t\tfloat len2 = a * a + b * b;\n\n\t\t\t\t\t\t\t\tif ( len2 > 1.0 ) discard;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t#endif\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\tvec4 diffuseColor = vec4( diffuse, alpha );\n\t\t\t\t\t#ifdef USE_COLOR\n\t\t\t\t\t\t#ifdef USE_LINE_COLOR_ALPHA\n\t\t\t\t\t\t\tdiffuseColor *= vLineColor;\n\t\t\t\t\t\t#else\n\t\t\t\t\t\t\tdiffuseColor.rgb *= vLineColor;\n\t\t\t\t\t\t#endif\n\t\t\t\t\t#endif\n\n\t\t\t\t\t#include <logdepthbuf_fragment>\n\n\t\t\t\t\tgl_FragColor = diffuseColor;\n\n\t\t\t\t\t#include <tonemapping_fragment>\n\t\t\t\t\t#include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n\t\t\t\t\t#include <fog_fragment>\n\t\t\t\t\t#include <premultiplied_alpha_fragment>\n\n\t\t\t\t}\n\t\t\t`,\n      clipping: true, // required for clipping support\n    })\n\n    this.isLineMaterial = true\n\n    this.onBeforeCompile = function () {\n      if (this.transparent) {\n        this.defines.USE_LINE_COLOR_ALPHA = '1'\n      } else {\n        delete this.defines.USE_LINE_COLOR_ALPHA\n      }\n    }\n\n    Object.defineProperties(this, {\n      color: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.diffuse.value\n        },\n\n        set: function (value) {\n          this.uniforms.diffuse.value = value\n        },\n      },\n\n      worldUnits: {\n        enumerable: true,\n\n        get: function () {\n          return 'WORLD_UNITS' in this.defines\n        },\n\n        set: function (value) {\n          if (value === true) {\n            this.defines.WORLD_UNITS = ''\n          } else {\n            delete this.defines.WORLD_UNITS\n          }\n        },\n      },\n\n      linewidth: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.linewidth.value\n        },\n\n        set: function (value) {\n          this.uniforms.linewidth.value = value\n        },\n      },\n\n      dashed: {\n        enumerable: true,\n\n        get: function () {\n          return Boolean('USE_DASH' in this.defines)\n        },\n\n        set(value) {\n          if (Boolean(value) !== Boolean('USE_DASH' in this.defines)) {\n            this.needsUpdate = true\n          }\n\n          if (value === true) {\n            this.defines.USE_DASH = ''\n          } else {\n            delete this.defines.USE_DASH\n          }\n        },\n      },\n\n      dashScale: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.dashScale.value\n        },\n\n        set: function (value) {\n          this.uniforms.dashScale.value = value\n        },\n      },\n\n      dashSize: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.dashSize.value\n        },\n\n        set: function (value) {\n          this.uniforms.dashSize.value = value\n        },\n      },\n\n      dashOffset: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.dashOffset.value\n        },\n\n        set: function (value) {\n          this.uniforms.dashOffset.value = value\n        },\n      },\n\n      gapSize: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.gapSize.value\n        },\n\n        set: function (value) {\n          this.uniforms.gapSize.value = value\n        },\n      },\n\n      opacity: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.opacity.value\n        },\n\n        set: function (value) {\n          this.uniforms.opacity.value = value\n        },\n      },\n\n      resolution: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.resolution.value\n        },\n\n        set: function (value) {\n          this.uniforms.resolution.value.copy(value)\n        },\n      },\n\n      alphaToCoverage: {\n        enumerable: true,\n\n        get: function () {\n          return Boolean('USE_ALPHA_TO_COVERAGE' in this.defines)\n        },\n\n        set: function (value) {\n          if (Boolean(value) !== Boolean('USE_ALPHA_TO_COVERAGE' in this.defines)) {\n            this.needsUpdate = true\n          }\n\n          if (value === true) {\n            this.defines.USE_ALPHA_TO_COVERAGE = ''\n            this.extensions.derivatives = true\n          } else {\n            delete this.defines.USE_ALPHA_TO_COVERAGE\n            this.extensions.derivatives = false\n          }\n        },\n      },\n    })\n\n    this.setValues(parameters)\n  }\n}\n\nexport { LineMaterial }\n"], "names": [], "mappings": ";;;;;;;;AAeA,MAAM,4NAAqB,iBAAA,CAAe;IACxC,YAAY,UAAA,CAAY;QACtB,KAAA,CAAM;YACJ,MAAM;YAEN,iNAAU,gBAAA,CAAc,KAAA,wMACtB,gBAAA,CAAc,KAAA,CAAM;yOAClB,cAAA,CAAY,MAAA;yOACZ,cAAA,CAAY,GAAA;gBACZ;oBACE,YAAY;wBAAE,OAAO;oBAAG;oBACxB,WAAW;wBAAE,OAAO;oBAAG;oBACvB,YAAY;wBAAE,OAAO,2MAAI,UAAA,CAAQ,GAAG,CAAC;oBAAG;oBACxC,YAAY;wBAAE,OAAO;oBAAG;oBACxB,WAAW;wBAAE,OAAO;oBAAG;oBACvB,UAAU;wBAAE,OAAO;oBAAG;oBACtB,SAAS;wBAAE,OAAO;oBAAG;gBACtB;aACF;YAGH,cAAA,QAAA,GAAyB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,CAAA;YA6OzB,gBAAA,QAAA,GAA2B,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAAA,mPAuKhB,UAAA,IAAW,MAAM,wBAAwB,qBAAA;;;;;GAAA,CAAA;YAMpD,UAAU;QAChB,CAAK;QAED,IAAA,CAAK,cAAA,GAAiB;QAEtB,IAAA,CAAK,eAAA,GAAkB,WAAY;YACjC,IAAI,IAAA,CAAK,WAAA,EAAa;gBACpB,IAAA,CAAK,OAAA,CAAQ,oBAAA,GAAuB;YAC5C,OAAa;gBACL,OAAO,IAAA,CAAK,OAAA,CAAQ,oBAAA;YACrB;QACF;QAED,OAAO,gBAAA,CAAiB,IAAA,EAAM;YAC5B,OAAO;gBACL,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,IAAA,CAAK,QAAA,CAAS,OAAA,CAAQ,KAAA;gBAC9B;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAA,CAAK,QAAA,CAAS,OAAA,CAAQ,KAAA,GAAQ;gBAC/B;YACF;YAED,YAAY;gBACV,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,iBAAiB,IAAA,CAAK,OAAA;gBAC9B;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAI,UAAU,MAAM;wBAClB,IAAA,CAAK,OAAA,CAAQ,WAAA,GAAc;oBACvC,OAAiB;wBACL,OAAO,IAAA,CAAK,OAAA,CAAQ,WAAA;oBACrB;gBACF;YACF;YAED,WAAW;gBACT,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,IAAA,CAAK,QAAA,CAAS,SAAA,CAAU,KAAA;gBAChC;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAA,CAAK,QAAA,CAAS,SAAA,CAAU,KAAA,GAAQ;gBACjC;YACF;YAED,QAAQ;gBACN,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,QAAQ,cAAc,IAAA,CAAK,OAAO;gBAC1C;gBAED,KAAI,KAAA,EAAO;oBACT,IAAI,QAAQ,KAAK,MAAM,QAAQ,cAAc,IAAA,CAAK,OAAO,GAAG;wBAC1D,IAAA,CAAK,WAAA,GAAc;oBACpB;oBAED,IAAI,UAAU,MAAM;wBAClB,IAAA,CAAK,OAAA,CAAQ,QAAA,GAAW;oBACpC,OAAiB;wBACL,OAAO,IAAA,CAAK,OAAA,CAAQ,QAAA;oBACrB;gBACF;YACF;YAED,WAAW;gBACT,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,IAAA,CAAK,QAAA,CAAS,SAAA,CAAU,KAAA;gBAChC;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAA,CAAK,QAAA,CAAS,SAAA,CAAU,KAAA,GAAQ;gBACjC;YACF;YAED,UAAU;gBACR,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,IAAA,CAAK,QAAA,CAAS,QAAA,CAAS,KAAA;gBAC/B;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAA,CAAK,QAAA,CAAS,QAAA,CAAS,KAAA,GAAQ;gBAChC;YACF;YAED,YAAY;gBACV,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,IAAA,CAAK,QAAA,CAAS,UAAA,CAAW,KAAA;gBACjC;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAA,CAAK,QAAA,CAAS,UAAA,CAAW,KAAA,GAAQ;gBAClC;YACF;YAED,SAAS;gBACP,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,IAAA,CAAK,QAAA,CAAS,OAAA,CAAQ,KAAA;gBAC9B;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAA,CAAK,QAAA,CAAS,OAAA,CAAQ,KAAA,GAAQ;gBAC/B;YACF;YAED,SAAS;gBACP,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,IAAA,CAAK,QAAA,CAAS,OAAA,CAAQ,KAAA;gBAC9B;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAA,CAAK,QAAA,CAAS,OAAA,CAAQ,KAAA,GAAQ;gBAC/B;YACF;YAED,YAAY;gBACV,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,IAAA,CAAK,QAAA,CAAS,UAAA,CAAW,KAAA;gBACjC;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAA,CAAK,QAAA,CAAS,UAAA,CAAW,KAAA,CAAM,IAAA,CAAK,KAAK;gBAC1C;YACF;YAED,iBAAiB;gBACf,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,QAAQ,2BAA2B,IAAA,CAAK,OAAO;gBACvD;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAI,QAAQ,KAAK,MAAM,QAAQ,2BAA2B,IAAA,CAAK,OAAO,GAAG;wBACvE,IAAA,CAAK,WAAA,GAAc;oBACpB;oBAED,IAAI,UAAU,MAAM;wBAClB,IAAA,CAAK,OAAA,CAAQ,qBAAA,GAAwB;wBACrC,IAAA,CAAK,UAAA,CAAW,WAAA,GAAc;oBAC1C,OAAiB;wBACL,OAAO,IAAA,CAAK,OAAA,CAAQ,qBAAA;wBACpB,IAAA,CAAK,UAAA,CAAW,WAAA,GAAc;oBAC/B;gBACF;YACF;QACP,CAAK;QAED,IAAA,CAAK,SAAA,CAAU,UAAU;IAC1B;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 930, "column": 0}, "map": {"version": 3, "file": "LineSegments2.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/lines/LineSegments2.js"], "sourcesContent": ["import {\n  Box3,\n  InstancedInterleavedBuffer,\n  InterleavedBuffer<PERSON><PERSON><PERSON><PERSON><PERSON>,\n  Line3,\n  MathUtils,\n  Matrix4,\n  Mesh,\n  Sphere,\n  Vector3,\n  Vector4,\n} from 'three'\nimport { LineSegmentsGeometry } from '../lines/LineSegmentsGeometry'\nimport { LineMaterial } from '../lines/LineMaterial'\nimport { UV1 } from '../_polyfill/uv1'\n\nconst _viewport = /* @__PURE__ */ new Vector4()\n\nconst _start = /* @__PURE__ */ new Vector3()\nconst _end = /* @__PURE__ */ new Vector3()\n\nconst _start4 = /* @__PURE__ */ new Vector4()\nconst _end4 = /* @__PURE__ */ new Vector4()\n\nconst _ssOrigin = /* @__PURE__ */ new Vector4()\nconst _ssOrigin3 = /* @__PURE__ */ new Vector3()\nconst _mvMatrix = /* @__PURE__ */ new Matrix4()\nconst _line = /* @__PURE__ */ new Line3()\nconst _closestPoint = /* @__PURE__ */ new Vector3()\n\nconst _box = /* @__PURE__ */ new Box3()\nconst _sphere = /* @__PURE__ */ new Sphere()\nconst _clipToWorldVector = /* @__PURE__ */ new Vector4()\n\nlet _ray, _lineWidth\n\n// Returns the margin required to expand by in world space given the distance from the camera,\n// line width, resolution, and camera projection\nfunction getWorldSpaceHalfWidth(camera, distance, resolution) {\n  // transform into clip space, adjust the x and y values by the pixel width offset, then\n  // transform back into world space to get world offset. Note clip space is [-1, 1] so full\n  // width does not need to be halved.\n  _clipToWorldVector.set(0, 0, -distance, 1.0).applyMatrix4(camera.projectionMatrix)\n  _clipToWorldVector.multiplyScalar(1.0 / _clipToWorldVector.w)\n  _clipToWorldVector.x = _lineWidth / resolution.width\n  _clipToWorldVector.y = _lineWidth / resolution.height\n  _clipToWorldVector.applyMatrix4(camera.projectionMatrixInverse)\n  _clipToWorldVector.multiplyScalar(1.0 / _clipToWorldVector.w)\n\n  return Math.abs(Math.max(_clipToWorldVector.x, _clipToWorldVector.y))\n}\n\nfunction raycastWorldUnits(lineSegments, intersects) {\n  const matrixWorld = lineSegments.matrixWorld\n  const geometry = lineSegments.geometry\n  const instanceStart = geometry.attributes.instanceStart\n  const instanceEnd = geometry.attributes.instanceEnd\n  const segmentCount = Math.min(geometry.instanceCount, instanceStart.count)\n\n  for (let i = 0, l = segmentCount; i < l; i++) {\n    _line.start.fromBufferAttribute(instanceStart, i)\n    _line.end.fromBufferAttribute(instanceEnd, i)\n\n    _line.applyMatrix4(matrixWorld)\n\n    const pointOnLine = new Vector3()\n    const point = new Vector3()\n\n    _ray.distanceSqToSegment(_line.start, _line.end, point, pointOnLine)\n    const isInside = point.distanceTo(pointOnLine) < _lineWidth * 0.5\n\n    if (isInside) {\n      intersects.push({\n        point,\n        pointOnLine,\n        distance: _ray.origin.distanceTo(point),\n        object: lineSegments,\n        face: null,\n        faceIndex: i,\n        uv: null,\n        [UV1]: null,\n      })\n    }\n  }\n}\n\nfunction raycastScreenSpace(lineSegments, camera, intersects) {\n  const projectionMatrix = camera.projectionMatrix\n  const material = lineSegments.material\n  const resolution = material.resolution\n  const matrixWorld = lineSegments.matrixWorld\n\n  const geometry = lineSegments.geometry\n  const instanceStart = geometry.attributes.instanceStart\n  const instanceEnd = geometry.attributes.instanceEnd\n  const segmentCount = Math.min(geometry.instanceCount, instanceStart.count)\n\n  const near = -camera.near\n\n  //\n\n  // pick a point 1 unit out along the ray to avoid the ray origin\n  // sitting at the camera origin which will cause \"w\" to be 0 when\n  // applying the projection matrix.\n  _ray.at(1, _ssOrigin)\n\n  // ndc space [ - 1.0, 1.0 ]\n  _ssOrigin.w = 1\n  _ssOrigin.applyMatrix4(camera.matrixWorldInverse)\n  _ssOrigin.applyMatrix4(projectionMatrix)\n  _ssOrigin.multiplyScalar(1 / _ssOrigin.w)\n\n  // screen space\n  _ssOrigin.x *= resolution.x / 2\n  _ssOrigin.y *= resolution.y / 2\n  _ssOrigin.z = 0\n\n  _ssOrigin3.copy(_ssOrigin)\n\n  _mvMatrix.multiplyMatrices(camera.matrixWorldInverse, matrixWorld)\n\n  for (let i = 0, l = segmentCount; i < l; i++) {\n    _start4.fromBufferAttribute(instanceStart, i)\n    _end4.fromBufferAttribute(instanceEnd, i)\n\n    _start4.w = 1\n    _end4.w = 1\n\n    // camera space\n    _start4.applyMatrix4(_mvMatrix)\n    _end4.applyMatrix4(_mvMatrix)\n\n    // skip the segment if it's entirely behind the camera\n    const isBehindCameraNear = _start4.z > near && _end4.z > near\n    if (isBehindCameraNear) {\n      continue\n    }\n\n    // trim the segment if it extends behind camera near\n    if (_start4.z > near) {\n      const deltaDist = _start4.z - _end4.z\n      const t = (_start4.z - near) / deltaDist\n      _start4.lerp(_end4, t)\n    } else if (_end4.z > near) {\n      const deltaDist = _end4.z - _start4.z\n      const t = (_end4.z - near) / deltaDist\n      _end4.lerp(_start4, t)\n    }\n\n    // clip space\n    _start4.applyMatrix4(projectionMatrix)\n    _end4.applyMatrix4(projectionMatrix)\n\n    // ndc space [ - 1.0, 1.0 ]\n    _start4.multiplyScalar(1 / _start4.w)\n    _end4.multiplyScalar(1 / _end4.w)\n\n    // screen space\n    _start4.x *= resolution.x / 2\n    _start4.y *= resolution.y / 2\n\n    _end4.x *= resolution.x / 2\n    _end4.y *= resolution.y / 2\n\n    // create 2d segment\n    _line.start.copy(_start4)\n    _line.start.z = 0\n\n    _line.end.copy(_end4)\n    _line.end.z = 0\n\n    // get closest point on ray to segment\n    const param = _line.closestPointToPointParameter(_ssOrigin3, true)\n    _line.at(param, _closestPoint)\n\n    // check if the intersection point is within clip space\n    const zPos = MathUtils.lerp(_start4.z, _end4.z, param)\n    const isInClipSpace = zPos >= -1 && zPos <= 1\n\n    const isInside = _ssOrigin3.distanceTo(_closestPoint) < _lineWidth * 0.5\n\n    if (isInClipSpace && isInside) {\n      _line.start.fromBufferAttribute(instanceStart, i)\n      _line.end.fromBufferAttribute(instanceEnd, i)\n\n      _line.start.applyMatrix4(matrixWorld)\n      _line.end.applyMatrix4(matrixWorld)\n\n      const pointOnLine = new Vector3()\n      const point = new Vector3()\n\n      _ray.distanceSqToSegment(_line.start, _line.end, point, pointOnLine)\n\n      intersects.push({\n        point: point,\n        pointOnLine: pointOnLine,\n        distance: _ray.origin.distanceTo(point),\n        object: lineSegments,\n        face: null,\n        faceIndex: i,\n        uv: null,\n        [UV1]: null,\n      })\n    }\n  }\n}\n\nclass LineSegments2 extends Mesh {\n  constructor(geometry = new LineSegmentsGeometry(), material = new LineMaterial({ color: Math.random() * 0xffffff })) {\n    super(geometry, material)\n\n    this.isLineSegments2 = true\n\n    this.type = 'LineSegments2'\n  }\n\n  // for backwards-compatibility, but could be a method of LineSegmentsGeometry...\n\n  computeLineDistances() {\n    const geometry = this.geometry\n\n    const instanceStart = geometry.attributes.instanceStart\n    const instanceEnd = geometry.attributes.instanceEnd\n    const lineDistances = new Float32Array(2 * instanceStart.count)\n\n    for (let i = 0, j = 0, l = instanceStart.count; i < l; i++, j += 2) {\n      _start.fromBufferAttribute(instanceStart, i)\n      _end.fromBufferAttribute(instanceEnd, i)\n\n      lineDistances[j] = j === 0 ? 0 : lineDistances[j - 1]\n      lineDistances[j + 1] = lineDistances[j] + _start.distanceTo(_end)\n    }\n\n    const instanceDistanceBuffer = new InstancedInterleavedBuffer(lineDistances, 2, 1) // d0, d1\n\n    geometry.setAttribute('instanceDistanceStart', new InterleavedBufferAttribute(instanceDistanceBuffer, 1, 0)) // d0\n    geometry.setAttribute('instanceDistanceEnd', new InterleavedBufferAttribute(instanceDistanceBuffer, 1, 1)) // d1\n\n    return this\n  }\n\n  raycast(raycaster, intersects) {\n    const worldUnits = this.material.worldUnits\n    const camera = raycaster.camera\n\n    if (camera === null && !worldUnits) {\n      console.error(\n        'LineSegments2: \"Raycaster.camera\" needs to be set in order to raycast against LineSegments2 while worldUnits is set to false.',\n      )\n    }\n\n    const threshold = raycaster.params.Line2 !== undefined ? raycaster.params.Line2.threshold || 0 : 0\n\n    _ray = raycaster.ray\n\n    const matrixWorld = this.matrixWorld\n    const geometry = this.geometry\n    const material = this.material\n\n    _lineWidth = material.linewidth + threshold\n\n    // check if we intersect the sphere bounds\n    if (geometry.boundingSphere === null) {\n      geometry.computeBoundingSphere()\n    }\n\n    _sphere.copy(geometry.boundingSphere).applyMatrix4(matrixWorld)\n\n    // increase the sphere bounds by the worst case line screen space width\n    let sphereMargin\n    if (worldUnits) {\n      sphereMargin = _lineWidth * 0.5\n    } else {\n      const distanceToSphere = Math.max(camera.near, _sphere.distanceToPoint(_ray.origin))\n      sphereMargin = getWorldSpaceHalfWidth(camera, distanceToSphere, material.resolution)\n    }\n\n    _sphere.radius += sphereMargin\n\n    if (_ray.intersectsSphere(_sphere) === false) {\n      return\n    }\n\n    // check if we intersect the box bounds\n    if (geometry.boundingBox === null) {\n      geometry.computeBoundingBox()\n    }\n\n    _box.copy(geometry.boundingBox).applyMatrix4(matrixWorld)\n\n    // increase the box bounds by the worst case line width\n    let boxMargin\n    if (worldUnits) {\n      boxMargin = _lineWidth * 0.5\n    } else {\n      const distanceToBox = Math.max(camera.near, _box.distanceToPoint(_ray.origin))\n      boxMargin = getWorldSpaceHalfWidth(camera, distanceToBox, material.resolution)\n    }\n\n    _box.expandByScalar(boxMargin)\n\n    if (_ray.intersectsBox(_box) === false) {\n      return\n    }\n\n    if (worldUnits) {\n      raycastWorldUnits(this, intersects)\n    } else {\n      raycastScreenSpace(this, camera, intersects)\n    }\n  }\n\n  onBeforeRender(renderer) {\n    const uniforms = this.material.uniforms\n\n    if (uniforms && uniforms.resolution) {\n      renderer.getViewport(_viewport)\n      this.material.uniforms.resolution.value.set(_viewport.z, _viewport.w)\n    }\n  }\n}\n\nexport { LineSegments2 }\n"], "names": [], "mappings": ";;;;;;;;;;;AAgBA,MAAM,YAA4B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAE/C,MAAM,SAAyB,aAAA,GAAA,2MAAI,UAAA,CAAS;AAC5C,MAAM,OAAuB,aAAA,GAAA,2MAAI,UAAA,CAAS;AAE1C,MAAM,UAA0B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAC7C,MAAM,QAAwB,aAAA,GAAA,2MAAI,UAAA,CAAS;AAE3C,MAAM,YAA4B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAC/C,MAAM,aAA6B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAChD,MAAM,YAA4B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAC/C,MAAM,QAAwB,aAAA,GAAA,2MAAI,QAAA,CAAO;AACzC,MAAM,gBAAgC,aAAA,GAAA,2MAAI,UAAA,CAAS;AAEnD,MAAM,OAAuB,aAAA,GAAA,2MAAI,OAAA,CAAM;AACvC,MAAM,UAA0B,aAAA,GAAA,2MAAI,SAAA,CAAQ;AAC5C,MAAM,qBAAqC,aAAA,GAAA,2MAAI,UAAA,CAAS;AAExD,IAAI,MAAM;AAIV,SAAS,uBAAuB,MAAA,EAAQ,QAAA,EAAU,UAAA,EAAY;IAI5D,mBAAmB,GAAA,CAAI,GAAG,GAAG,CAAC,UAAU,CAAG,EAAE,YAAA,CAAa,OAAO,gBAAgB;IACjF,mBAAmB,cAAA,CAAe,IAAM,mBAAmB,CAAC;IAC5D,mBAAmB,CAAA,GAAI,aAAa,WAAW,KAAA;IAC/C,mBAAmB,CAAA,GAAI,aAAa,WAAW,MAAA;IAC/C,mBAAmB,YAAA,CAAa,OAAO,uBAAuB;IAC9D,mBAAmB,cAAA,CAAe,IAAM,mBAAmB,CAAC;IAE5D,OAAO,KAAK,GAAA,CAAI,KAAK,GAAA,CAAI,mBAAmB,CAAA,EAAG,mBAAmB,CAAC,CAAC;AACtE;AAEA,SAAS,kBAAkB,YAAA,EAAc,UAAA,EAAY;IACnD,MAAM,cAAc,aAAa,WAAA;IACjC,MAAM,WAAW,aAAa,QAAA;IAC9B,MAAM,gBAAgB,SAAS,UAAA,CAAW,aAAA;IAC1C,MAAM,cAAc,SAAS,UAAA,CAAW,WAAA;IACxC,MAAM,eAAe,KAAK,GAAA,CAAI,SAAS,aAAA,EAAe,cAAc,KAAK;IAEzE,IAAA,IAAS,IAAI,GAAG,IAAI,cAAc,IAAI,GAAG,IAAK;QAC5C,MAAM,KAAA,CAAM,mBAAA,CAAoB,eAAe,CAAC;QAChD,MAAM,GAAA,CAAI,mBAAA,CAAoB,aAAa,CAAC;QAE5C,MAAM,YAAA,CAAa,WAAW;QAE9B,MAAM,cAAc,2MAAI,UAAA,CAAS;QACjC,MAAM,QAAQ,2MAAI,UAAA,CAAS;QAE3B,KAAK,mBAAA,CAAoB,MAAM,KAAA,EAAO,MAAM,GAAA,EAAK,OAAO,WAAW;QACnE,MAAM,WAAW,MAAM,UAAA,CAAW,WAAW,IAAI,aAAa;QAE9D,IAAI,UAAU;YACZ,WAAW,IAAA,CAAK;gBACd;gBACA;gBACA,UAAU,KAAK,MAAA,CAAO,UAAA,CAAW,KAAK;gBACtC,QAAQ;gBACR,MAAM;gBACN,WAAW;gBACX,IAAI;gBACJ,4OAAC,MAAG,CAAA,EAAG;YACf,CAAO;QACF;IACF;AACH;AAEA,SAAS,mBAAmB,YAAA,EAAc,MAAA,EAAQ,UAAA,EAAY;IAC5D,MAAM,mBAAmB,OAAO,gBAAA;IAChC,MAAM,WAAW,aAAa,QAAA;IAC9B,MAAM,aAAa,SAAS,UAAA;IAC5B,MAAM,cAAc,aAAa,WAAA;IAEjC,MAAM,WAAW,aAAa,QAAA;IAC9B,MAAM,gBAAgB,SAAS,UAAA,CAAW,aAAA;IAC1C,MAAM,cAAc,SAAS,UAAA,CAAW,WAAA;IACxC,MAAM,eAAe,KAAK,GAAA,CAAI,SAAS,aAAA,EAAe,cAAc,KAAK;IAEzE,MAAM,OAAO,CAAC,OAAO,IAAA;IAOrB,KAAK,EAAA,CAAG,GAAG,SAAS;IAGpB,UAAU,CAAA,GAAI;IACd,UAAU,YAAA,CAAa,OAAO,kBAAkB;IAChD,UAAU,YAAA,CAAa,gBAAgB;IACvC,UAAU,cAAA,CAAe,IAAI,UAAU,CAAC;IAGxC,UAAU,CAAA,IAAK,WAAW,CAAA,GAAI;IAC9B,UAAU,CAAA,IAAK,WAAW,CAAA,GAAI;IAC9B,UAAU,CAAA,GAAI;IAEd,WAAW,IAAA,CAAK,SAAS;IAEzB,UAAU,gBAAA,CAAiB,OAAO,kBAAA,EAAoB,WAAW;IAEjE,IAAA,IAAS,IAAI,GAAG,IAAI,cAAc,IAAI,GAAG,IAAK;QAC5C,QAAQ,mBAAA,CAAoB,eAAe,CAAC;QAC5C,MAAM,mBAAA,CAAoB,aAAa,CAAC;QAExC,QAAQ,CAAA,GAAI;QACZ,MAAM,CAAA,GAAI;QAGV,QAAQ,YAAA,CAAa,SAAS;QAC9B,MAAM,YAAA,CAAa,SAAS;QAG5B,MAAM,qBAAqB,QAAQ,CAAA,GAAI,QAAQ,MAAM,CAAA,GAAI;QACzD,IAAI,oBAAoB;YACtB;QACD;QAGD,IAAI,QAAQ,CAAA,GAAI,MAAM;YACpB,MAAM,YAAY,QAAQ,CAAA,GAAI,MAAM,CAAA;YACpC,MAAM,IAAA,CAAK,QAAQ,CAAA,GAAI,IAAA,IAAQ;YAC/B,QAAQ,IAAA,CAAK,OAAO,CAAC;QAC3B,OAAA,IAAe,MAAM,CAAA,GAAI,MAAM;YACzB,MAAM,YAAY,MAAM,CAAA,GAAI,QAAQ,CAAA;YACpC,MAAM,IAAA,CAAK,MAAM,CAAA,GAAI,IAAA,IAAQ;YAC7B,MAAM,IAAA,CAAK,SAAS,CAAC;QACtB;QAGD,QAAQ,YAAA,CAAa,gBAAgB;QACrC,MAAM,YAAA,CAAa,gBAAgB;QAGnC,QAAQ,cAAA,CAAe,IAAI,QAAQ,CAAC;QACpC,MAAM,cAAA,CAAe,IAAI,MAAM,CAAC;QAGhC,QAAQ,CAAA,IAAK,WAAW,CAAA,GAAI;QAC5B,QAAQ,CAAA,IAAK,WAAW,CAAA,GAAI;QAE5B,MAAM,CAAA,IAAK,WAAW,CAAA,GAAI;QAC1B,MAAM,CAAA,IAAK,WAAW,CAAA,GAAI;QAG1B,MAAM,KAAA,CAAM,IAAA,CAAK,OAAO;QACxB,MAAM,KAAA,CAAM,CAAA,GAAI;QAEhB,MAAM,GAAA,CAAI,IAAA,CAAK,KAAK;QACpB,MAAM,GAAA,CAAI,CAAA,GAAI;QAGd,MAAM,QAAQ,MAAM,4BAAA,CAA6B,YAAY,IAAI;QACjE,MAAM,EAAA,CAAG,OAAO,aAAa;QAG7B,MAAM,8MAAO,YAAA,CAAU,IAAA,CAAK,QAAQ,CAAA,EAAG,MAAM,CAAA,EAAG,KAAK;QACrD,MAAM,gBAAgB,QAAQ,CAAA,KAAM,QAAQ;QAE5C,MAAM,WAAW,WAAW,UAAA,CAAW,aAAa,IAAI,aAAa;QAErE,IAAI,iBAAiB,UAAU;YAC7B,MAAM,KAAA,CAAM,mBAAA,CAAoB,eAAe,CAAC;YAChD,MAAM,GAAA,CAAI,mBAAA,CAAoB,aAAa,CAAC;YAE5C,MAAM,KAAA,CAAM,YAAA,CAAa,WAAW;YACpC,MAAM,GAAA,CAAI,YAAA,CAAa,WAAW;YAElC,MAAM,cAAc,2MAAI,UAAA,CAAS;YACjC,MAAM,QAAQ,2MAAI,UAAA,CAAS;YAE3B,KAAK,mBAAA,CAAoB,MAAM,KAAA,EAAO,MAAM,GAAA,EAAK,OAAO,WAAW;YAEnE,WAAW,IAAA,CAAK;gBACd;gBACA;gBACA,UAAU,KAAK,MAAA,CAAO,UAAA,CAAW,KAAK;gBACtC,QAAQ;gBACR,MAAM;gBACN,WAAW;gBACX,IAAI;gBACJ,4OAAC,MAAG,CAAA,EAAG;YACf,CAAO;QACF;IACF;AACH;AAEA,MAAM,6NAAsB,OAAA,CAAK;IAC/B,YAAY,WAAW,4PAAI,uBAAA,CAAsB,CAAA,EAAE,WAAW,oPAAI,eAAA,CAAa;QAAE,OAAO,KAAK,MAAA,KAAW;IAAU,CAAA,CAAA,CAAG;QACnH,KAAA,CAAM,UAAU,QAAQ;QAExB,IAAA,CAAK,eAAA,GAAkB;QAEvB,IAAA,CAAK,IAAA,GAAO;IACb;IAAA,gFAAA;IAID,uBAAuB;QACrB,MAAM,WAAW,IAAA,CAAK,QAAA;QAEtB,MAAM,gBAAgB,SAAS,UAAA,CAAW,aAAA;QAC1C,MAAM,cAAc,SAAS,UAAA,CAAW,WAAA;QACxC,MAAM,gBAAgB,IAAI,aAAa,IAAI,cAAc,KAAK;QAE9D,IAAA,IAAS,IAAI,GAAG,IAAI,GAAG,IAAI,cAAc,KAAA,EAAO,IAAI,GAAG,KAAK,KAAK,EAAG;YAClE,OAAO,mBAAA,CAAoB,eAAe,CAAC;YAC3C,KAAK,mBAAA,CAAoB,aAAa,CAAC;YAEvC,aAAA,CAAc,CAAC,CAAA,GAAI,MAAM,IAAI,IAAI,aAAA,CAAc,IAAI,CAAC,CAAA;YACpD,aAAA,CAAc,IAAI,CAAC,CAAA,GAAI,aAAA,CAAc,CAAC,CAAA,GAAI,OAAO,UAAA,CAAW,IAAI;QACjE;QAED,MAAM,yBAAyB,2MAAI,6BAAA,CAA2B,eAAe,GAAG,CAAC;QAEjF,SAAS,YAAA,CAAa,yBAAyB,2MAAI,6BAAA,CAA2B,wBAAwB,GAAG,CAAC,CAAC;QAC3G,SAAS,YAAA,CAAa,uBAAuB,2MAAI,6BAAA,CAA2B,wBAAwB,GAAG,CAAC,CAAC;QAEzG,OAAO,IAAA;IACR;IAED,QAAQ,SAAA,EAAW,UAAA,EAAY;QAC7B,MAAM,aAAa,IAAA,CAAK,QAAA,CAAS,UAAA;QACjC,MAAM,SAAS,UAAU,MAAA;QAEzB,IAAI,WAAW,QAAQ,CAAC,YAAY;YAClC,QAAQ,KAAA,CACN;QAEH;QAED,MAAM,YAAY,UAAU,MAAA,CAAO,KAAA,KAAU,KAAA,IAAY,UAAU,MAAA,CAAO,KAAA,CAAM,SAAA,IAAa,IAAI;QAEjG,OAAO,UAAU,GAAA;QAEjB,MAAM,cAAc,IAAA,CAAK,WAAA;QACzB,MAAM,WAAW,IAAA,CAAK,QAAA;QACtB,MAAM,WAAW,IAAA,CAAK,QAAA;QAEtB,aAAa,SAAS,SAAA,GAAY;QAGlC,IAAI,SAAS,cAAA,KAAmB,MAAM;YACpC,SAAS,qBAAA,CAAuB;QACjC;QAED,QAAQ,IAAA,CAAK,SAAS,cAAc,EAAE,YAAA,CAAa,WAAW;QAG9D,IAAI;QACJ,IAAI,YAAY;YACd,eAAe,aAAa;QAClC,OAAW;YACL,MAAM,mBAAmB,KAAK,GAAA,CAAI,OAAO,IAAA,EAAM,QAAQ,eAAA,CAAgB,KAAK,MAAM,CAAC;YACnF,eAAe,uBAAuB,QAAQ,kBAAkB,SAAS,UAAU;QACpF;QAED,QAAQ,MAAA,IAAU;QAElB,IAAI,KAAK,gBAAA,CAAiB,OAAO,MAAM,OAAO;YAC5C;QACD;QAGD,IAAI,SAAS,WAAA,KAAgB,MAAM;YACjC,SAAS,kBAAA,CAAoB;QAC9B;QAED,KAAK,IAAA,CAAK,SAAS,WAAW,EAAE,YAAA,CAAa,WAAW;QAGxD,IAAI;QACJ,IAAI,YAAY;YACd,YAAY,aAAa;QAC/B,OAAW;YACL,MAAM,gBAAgB,KAAK,GAAA,CAAI,OAAO,IAAA,EAAM,KAAK,eAAA,CAAgB,KAAK,MAAM,CAAC;YAC7E,YAAY,uBAAuB,QAAQ,eAAe,SAAS,UAAU;QAC9E;QAED,KAAK,cAAA,CAAe,SAAS;QAE7B,IAAI,KAAK,aAAA,CAAc,IAAI,MAAM,OAAO;YACtC;QACD;QAED,IAAI,YAAY;YACd,kBAAkB,IAAA,EAAM,UAAU;QACxC,OAAW;YACL,mBAAmB,IAAA,EAAM,QAAQ,UAAU;QAC5C;IACF;IAED,eAAe,QAAA,EAAU;QACvB,MAAM,WAAW,IAAA,CAAK,QAAA,CAAS,QAAA;QAE/B,IAAI,YAAY,SAAS,UAAA,EAAY;YACnC,SAAS,WAAA,CAAY,SAAS;YAC9B,IAAA,CAAK,QAAA,CAAS,QAAA,CAAS,UAAA,CAAW,KAAA,CAAM,GAAA,CAAI,UAAU,CAAA,EAAG,UAAU,CAAC;QACrE;IACF;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1153, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1159, "column": 0}, "map": {"version": 3, "file": "LineGeometry.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/lines/LineGeometry.js"], "sourcesContent": ["import { LineSegmentsGeometry } from '../lines/LineSegmentsGeometry'\n\nclass LineGeometry extends LineSegmentsGeometry {\n  constructor() {\n    super()\n\n    this.isLineGeometry = true\n\n    this.type = 'LineGeometry'\n  }\n\n  setPositions(array) {\n    // converts [ x1, y1, z1,  x2, y2, z2, ... ] to pairs format\n\n    const length = array.length - 3\n    const points = new Float32Array(2 * length)\n\n    for (let i = 0; i < length; i += 3) {\n      points[2 * i] = array[i]\n      points[2 * i + 1] = array[i + 1]\n      points[2 * i + 2] = array[i + 2]\n\n      points[2 * i + 3] = array[i + 3]\n      points[2 * i + 4] = array[i + 4]\n      points[2 * i + 5] = array[i + 5]\n    }\n\n    super.setPositions(points)\n\n    return this\n  }\n\n  setColors(array, itemSize = 3) {\n    // converts [ r1, g1, b1, (a1),  r2, g2, b2, (a2), ... ] to pairs format\n\n    const length = array.length - itemSize\n    const colors = new Float32Array(2 * length)\n\n    if (itemSize === 3) {\n      for (let i = 0; i < length; i += itemSize) {\n        colors[2 * i] = array[i]\n        colors[2 * i + 1] = array[i + 1]\n        colors[2 * i + 2] = array[i + 2]\n\n        colors[2 * i + 3] = array[i + 3]\n        colors[2 * i + 4] = array[i + 4]\n        colors[2 * i + 5] = array[i + 5]\n      }\n    } else {\n      for (let i = 0; i < length; i += itemSize) {\n        colors[2 * i] = array[i]\n        colors[2 * i + 1] = array[i + 1]\n        colors[2 * i + 2] = array[i + 2]\n        colors[2 * i + 3] = array[i + 3]\n\n        colors[2 * i + 4] = array[i + 4]\n        colors[2 * i + 5] = array[i + 5]\n        colors[2 * i + 6] = array[i + 6]\n        colors[2 * i + 7] = array[i + 7]\n      }\n    }\n\n    super.setColors(colors, itemSize)\n\n    return this\n  }\n\n  fromLine(line) {\n    const geometry = line.geometry\n\n    this.setPositions(geometry.attributes.position.array) // assumes non-indexed\n\n    // set colors, maybe\n\n    return this\n  }\n}\n\nexport { LineGeometry }\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,6QAAqB,uBAAA,CAAqB;IAC9C,aAAc;QACZ,KAAA,CAAO;QAEP,IAAA,CAAK,cAAA,GAAiB;QAEtB,IAAA,CAAK,IAAA,GAAO;IACb;IAED,aAAa,KAAA,EAAO;QAGlB,MAAM,SAAS,MAAM,MAAA,GAAS;QAC9B,MAAM,SAAS,IAAI,aAAa,IAAI,MAAM;QAE1C,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,EAAG;YAClC,MAAA,CAAO,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;YACvB,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;YAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;YAE/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;YAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;YAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;QAChC;QAED,KAAA,CAAM,aAAa,MAAM;QAEzB,OAAO,IAAA;IACR;IAED,UAAU,KAAA,EAAO,WAAW,CAAA,EAAG;QAG7B,MAAM,SAAS,MAAM,MAAA,GAAS;QAC9B,MAAM,SAAS,IAAI,aAAa,IAAI,MAAM;QAE1C,IAAI,aAAa,GAAG;YAClB,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,SAAU;gBACzC,MAAA,CAAO,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;gBACvB,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAE/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;YAChC;QACP,OAAW;YACL,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,SAAU;gBACzC,MAAA,CAAO,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;gBACvB,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAE/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;YAChC;QACF;QAED,KAAA,CAAM,UAAU,QAAQ,QAAQ;QAEhC,OAAO,IAAA;IACR;IAED,SAAS,IAAA,EAAM;QACb,MAAM,WAAW,KAAK,QAAA;QAEtB,IAAA,CAAK,YAAA,CAAa,SAAS,UAAA,CAAW,QAAA,CAAS,KAAK;QAIpD,OAAO,IAAA;IACR;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1219, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1225, "column": 0}, "map": {"version": 3, "file": "Line2.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/lines/Line2.js"], "sourcesContent": ["import { LineSegments2 } from '../lines/LineSegments2'\nimport { LineGeometry } from '../lines/LineGeometry'\nimport { LineMaterial } from '../lines/LineMaterial'\n\nclass Line2 extends LineSegments2 {\n  constructor(geometry = new LineGeometry(), material = new LineMaterial({ color: Math.random() * 0xffffff })) {\n    super(geometry, material)\n\n    this.isLine2 = true\n\n    this.type = 'Line2'\n  }\n}\n\nexport { Line2 }\n"], "names": [], "mappings": ";;;;;;;;;AAIA,MAAM,+PAAc,gBAAA,CAAc;IAChC,YAAY,WAAW,oPAAI,eAAA,CAAc,CAAA,EAAE,WAAW,oPAAI,eAAA,CAAa;QAAE,OAAO,KAAK,MAAA,KAAW;IAAU,CAAA,CAAA,CAAG;QAC3G,KAAA,CAAM,UAAU,QAAQ;QAExB,IAAA,CAAK,OAAA,GAAU;QAEf,IAAA,CAAK,IAAA,GAAO;IACb;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1245, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1251, "column": 0}, "map": {"version": 3, "file": "constants.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/_polyfill/constants.ts"], "sourcesContent": ["import { REVISION } from 'three'\n\nexport const version = /* @__PURE__ */ (() => parseInt(REVISION.replace(/\\D+/g, '')))()\n"], "names": [], "mappings": ";;;;;AAEa,MAAA,UAAA,aAAA,GAAA,CAAA,IAAiC,gNAAS,WAAA,CAAS,OAAA,CAAQ,QAAQ,EAAE,CAAC,CAAA,EAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1259, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1265, "column": 0}, "map": {"version": 3, "file": "uv1.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/_polyfill/uv1.ts"], "sourcesContent": ["import { version } from \"./constants\";\n\n/** uv2 renamed to uv1 in r125\n * \n * https://github.com/mrdoob/three.js/pull/25943\n*/\nexport const UV1 = version >= 125 ? 'uv1' : 'uv2'"], "names": [], "mappings": ";;;;;AAMa,MAAA,uPAAM,UAAA,IAAW,MAAM,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1273, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1279, "column": 0}, "map": {"version": 3, "file": "LoaderUtils.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/_polyfill/LoaderUtils.js"], "sourcesContent": ["export function decodeText(array) {\n  if (typeof TextDecoder !== 'undefined') {\n    return new TextDecoder().decode(array)\n  }\n\n  // Avoid the String.fromCharCode.apply(null, array) shortcut, which\n  // throws a \"maximum call stack size exceeded\" error for large arrays.\n\n  let s = ''\n\n  for (let i = 0, il = array.length; i < il; i++) {\n    // Implicitly assumes little-endian.\n    s += String.fromCharCode(array[i])\n  }\n\n  try {\n    // merges multi-byte utf-8 characters.\n\n    return decodeURIComponent(escape(s))\n  } catch (e) {\n    // see https://github.com/mrdoob/three.js/issues/16358\n\n    return s\n  }\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS,WAAW,KAAA,EAAO;IAChC,IAAI,OAAO,gBAAgB,aAAa;QACtC,OAAO,IAAI,YAAW,EAAG,MAAA,CAAO,KAAK;IACtC;IAKD,IAAI,IAAI;IAER,IAAA,IAAS,IAAI,GAAG,KAAK,MAAM,MAAA,EAAQ,IAAI,IAAI,IAAK;QAE9C,KAAK,OAAO,YAAA,CAAa,KAAA,CAAM,CAAC,CAAC;IAClC;IAED,IAAI;QAGF,OAAO,mBAAmB,OAAO,CAAC,CAAC;IACpC,EAAA,OAAQ,GAAP;QAGA,OAAO;IACR;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1298, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1304, "column": 0}, "map": {"version": 3, "file": "CompressedCubeTexture.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/_polyfill/CompressedCubeTexture.js"], "sourcesContent": ["import { CompressedTexture, CubeReflectionMapping } from 'three'\n\nclass CompressedCubeTexture extends CompressedTexture {\n  constructor(images, format, type) {\n    super(undefined, images[0].width, images[0].height, format, type, CubeReflectionMapping)\n\n    this.isCompressedCubeTexture = true\n    this.isCubeTexture = true\n\n    this.image = images\n  }\n}\n\nexport { CompressedCubeTexture }\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,qOAA8B,oBAAA,CAAkB;IACpD,YAAY,MAAA,EAAQ,MAAA,EAAQ,IAAA,CAAM;QAChC,KAAA,CAAM,KAAA,GAAW,MAAA,CAAO,CAAC,CAAA,CAAE,KAAA,EAAO,MAAA,CAAO,CAAC,CAAA,CAAE,MAAA,EAAQ,QAAQ,6MAAM,wBAAqB;QAEvF,IAAA,CAAK,uBAAA,GAA0B;QAC/B,IAAA,CAAK,aAAA,GAAgB;QAErB,IAAA,CAAK,KAAA,GAAQ;IACd;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1319, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1325, "column": 0}, "map": {"version": 3, "file": "CompressedArrayTexture.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/_polyfill/CompressedArrayTexture.js"], "sourcesContent": ["import { CompressedTexture, ClampToEdgeWrapping } from 'three'\n\nclass CompressedArrayTexture extends CompressedTexture {\n  constructor(mipmaps, width, height, depth, format, type) {\n    super(mipmaps, width, height, format, type)\n    this.isCompressedArrayTexture = true\n    this.image.depth = depth\n    this.wrapR = ClampToEdgeWrapping\n  }\n}\n\nexport { CompressedArrayTexture }\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,sOAA+B,oBAAA,CAAkB;IACrD,YAAY,OAAA,EAAS,KAAA,EAAO,MAAA,EAAQ,KAAA,EAAO,MAAA,EAAQ,IAAA,CAAM;QACvD,KAAA,CAAM,SAAS,OAAO,QAAQ,QAAQ,IAAI;QAC1C,IAAA,CAAK,wBAAA,GAA2B;QAChC,IAAA,CAAK,KAAA,CAAM,KAAA,GAAQ;QACnB,IAAA,CAAK,KAAA,0MAAQ,sBAAA;IACd;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1340, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1346, "column": 0}, "map": {"version": 3, "file": "Data3DTexture.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/_polyfill/Data3DTexture.js"], "sourcesContent": ["import { Texture, ClampToEdgeWrapping, NearestFilter } from 'three'\n\nclass Data3DTexture extends Texture {\n  constructor(data = null, width = 1, height = 1, depth = 1) {\n    super(null)\n\n    this.isData3DTexture = true\n\n    this.image = { data, width, height, depth }\n\n    this.magFilter = NearestFilter\n    this.minFilter = NearestFilter\n\n    this.wrapR = ClampToEdgeWrapping\n\n    this.generateMipmaps = false\n    this.flipY = false\n    this.unpackAlignment = 1\n  }\n}\n\nexport { Data3DTexture }\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,6NAAsB,UAAA,CAAQ;IAClC,YAAY,OAAO,IAAA,EAAM,QAAQ,CAAA,EAAG,SAAS,CAAA,EAAG,QAAQ,CAAA,CAAG;QACzD,KAAA,CAAM,IAAI;QAEV,IAAA,CAAK,eAAA,GAAkB;QAEvB,IAAA,CAAK,KAAA,GAAQ;YAAE;YAAM;YAAO;YAAQ;QAAO;QAE3C,IAAA,CAAK,SAAA,0MAAY,gBAAA;QACjB,IAAA,CAAK,SAAA,0MAAY,gBAAA;QAEjB,IAAA,CAAK,KAAA,0MAAQ,sBAAA;QAEb,IAAA,CAAK,eAAA,GAAkB;QACvB,IAAA,CAAK,KAAA,GAAQ;QACb,IAAA,CAAK,eAAA,GAAkB;IACxB;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1371, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1377, "column": 0}, "map": {"version": 3, "file": "TextGeometry.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/geometries/TextGeometry.ts"], "sourcesContent": ["import { ExtrudeGeometry } from 'three'\n\nimport type { Font } from '../loaders/FontLoader'\n\nexport type TextGeometryParameters = {\n  bevelEnabled?: boolean\n  bevelOffset?: number\n  bevelSize?: number\n  bevelThickness?: number\n  curveSegments?: number\n  font: Font\n  height?: number\n  size?: number\n  lineHeight?: number\n  letterSpacing?: number\n}\n\nexport class TextGeometry extends ExtrudeGeometry {\n  constructor(text: string, parameters: TextGeometryParameters = {} as TextGeometryParameters) {\n    const {\n      bevelEnabled = false,\n      bevelSize = 8,\n      bevelThickness = 10,\n      font,\n      height = 50,\n      size = 100,\n      lineHeight = 1,\n      letterSpacing = 0,\n      ...rest\n    } = parameters\n\n    if (font === undefined) {\n      // @ts-ignore\n      super() // generate default extrude geometry\n    } else {\n      const shapes = font.generateShapes(text, size, { lineHeight, letterSpacing })\n      super(shapes, { ...rest, bevelEnabled, bevelSize, bevelThickness, depth: height })\n    }\n    // @ts-ignore\n    this.type = 'TextGeometry'\n  }\n}\n\nexport { TextGeometry as TextBufferGeometry }\n"], "names": [], "mappings": ";;;;;;AAiBO,MAAM,4NAAqB,kBAAA,CAAgB;IAChD,YAAY,IAAA,EAAc,aAAqC,CAAA,CAAA,CAA8B;QACrF,MAAA,EACJ,eAAe,KAAA,EACf,YAAY,CAAA,EACZ,iBAAiB,EAAA,EACjB,IAAA,EACA,SAAS,EAAA,EACT,OAAO,GAAA,EACP,aAAa,CAAA,EACb,gBAAgB,CAAA,EAChB,GAAG,MACD,GAAA;QAEJ,IAAI,SAAS,KAAA,GAAW;YAEhB,KAAA;QAAA,OACD;YACC,MAAA,SAAS,KAAK,cAAA,CAAe,MAAM,MAAM;gBAAE;gBAAY;YAAA,CAAe;YACtE,KAAA,CAAA,QAAQ;gBAAE,GAAG,IAAA;gBAAM;gBAAc;gBAAW;gBAAgB,OAAO;YAAA,CAAQ;QACnF;QAEA,IAAA,CAAK,IAAA,GAAO;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1406, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1412, "column": 0}, "map": {"version": 3, "file": "DecalGeometry.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/geometries/DecalGeometry.js"], "sourcesContent": ["import { BufferGeometry, Float32BufferAttribute, Matrix4, Vector3 } from 'three'\n\n/**\n * You can use this geometry to create a decal mesh, that serves different kinds of purposes.\n * e.g. adding unique details to models, performing dynamic visual environmental changes or covering seams.\n *\n * Constructor parameter:\n *\n * mesh — Any mesh object\n * position — Position of the decal projector\n * orientation — Orientation of the decal projector\n * size — Size of the decal projector\n *\n * reference: http://blog.wolfire.com/2009/06/how-to-project-decals/\n *\n */\n\nclass DecalGeometry extends BufferGeometry {\n  constructor(mesh, position, orientation, size) {\n    super()\n\n    // buffers\n\n    const vertices = []\n    const normals = []\n    const uvs = []\n\n    // helpers\n\n    const plane = new Vector3()\n\n    // this matrix represents the transformation of the decal projector\n\n    const projectorMatrix = new Matrix4()\n    projectorMatrix.makeRotationFromEuler(orientation)\n    projectorMatrix.setPosition(position)\n\n    const projectorMatrixInverse = new Matrix4()\n    projectorMatrixInverse.copy(projectorMatrix).invert()\n\n    // generate buffers\n\n    generate()\n\n    // build geometry\n\n    this.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n    this.setAttribute('normal', new Float32BufferAttribute(normals, 3))\n    this.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n\n    function generate() {\n      let i\n\n      let decalVertices = []\n\n      const vertex = new Vector3()\n      const normal = new Vector3()\n\n      // handle different geometry types\n\n      if (mesh.geometry.isGeometry === true) {\n        console.error('THREE.DecalGeometry no longer supports THREE.Geometry. Use BufferGeometry instead.')\n        return\n      }\n\n      const geometry = mesh.geometry\n\n      const positionAttribute = geometry.attributes.position\n      const normalAttribute = geometry.attributes.normal\n\n      // first, create an array of 'DecalVertex' objects\n      // three consecutive 'DecalVertex' objects represent a single face\n      //\n      // this data structure will be later used to perform the clipping\n\n      if (geometry.index !== null) {\n        // indexed BufferGeometry\n\n        const index = geometry.index\n\n        for (i = 0; i < index.count; i++) {\n          vertex.fromBufferAttribute(positionAttribute, index.getX(i))\n          normal.fromBufferAttribute(normalAttribute, index.getX(i))\n\n          pushDecalVertex(decalVertices, vertex, normal)\n        }\n      } else {\n        // non-indexed BufferGeometry\n\n        for (i = 0; i < positionAttribute.count; i++) {\n          vertex.fromBufferAttribute(positionAttribute, i)\n          normal.fromBufferAttribute(normalAttribute, i)\n\n          pushDecalVertex(decalVertices, vertex, normal)\n        }\n      }\n\n      // second, clip the geometry so that it doesn't extend out from the projector\n\n      decalVertices = clipGeometry(decalVertices, plane.set(1, 0, 0))\n      decalVertices = clipGeometry(decalVertices, plane.set(-1, 0, 0))\n      decalVertices = clipGeometry(decalVertices, plane.set(0, 1, 0))\n      decalVertices = clipGeometry(decalVertices, plane.set(0, -1, 0))\n      decalVertices = clipGeometry(decalVertices, plane.set(0, 0, 1))\n      decalVertices = clipGeometry(decalVertices, plane.set(0, 0, -1))\n\n      // third, generate final vertices, normals and uvs\n\n      for (i = 0; i < decalVertices.length; i++) {\n        const decalVertex = decalVertices[i]\n\n        // create texture coordinates (we are still in projector space)\n\n        uvs.push(0.5 + decalVertex.position.x / size.x, 0.5 + decalVertex.position.y / size.y)\n\n        // transform the vertex back to world space\n\n        decalVertex.position.applyMatrix4(projectorMatrix)\n\n        // now create vertex and normal buffer data\n\n        vertices.push(decalVertex.position.x, decalVertex.position.y, decalVertex.position.z)\n        normals.push(decalVertex.normal.x, decalVertex.normal.y, decalVertex.normal.z)\n      }\n    }\n\n    function pushDecalVertex(decalVertices, vertex, normal) {\n      // transform the vertex to world space, then to projector space\n\n      vertex.applyMatrix4(mesh.matrixWorld)\n      vertex.applyMatrix4(projectorMatrixInverse)\n\n      normal.transformDirection(mesh.matrixWorld)\n\n      decalVertices.push(new DecalVertex(vertex.clone(), normal.clone()))\n    }\n\n    function clipGeometry(inVertices, plane) {\n      const outVertices = []\n\n      const s = 0.5 * Math.abs(size.dot(plane))\n\n      // a single iteration clips one face,\n      // which consists of three consecutive 'DecalVertex' objects\n\n      for (let i = 0; i < inVertices.length; i += 3) {\n        let v1Out,\n          v2Out,\n          v3Out,\n          total = 0\n        let nV1, nV2, nV3, nV4\n\n        const d1 = inVertices[i + 0].position.dot(plane) - s\n        const d2 = inVertices[i + 1].position.dot(plane) - s\n        const d3 = inVertices[i + 2].position.dot(plane) - s\n\n        v1Out = d1 > 0\n        v2Out = d2 > 0\n        v3Out = d3 > 0\n\n        // calculate, how many vertices of the face lie outside of the clipping plane\n\n        total = (v1Out ? 1 : 0) + (v2Out ? 1 : 0) + (v3Out ? 1 : 0)\n\n        switch (total) {\n          case 0: {\n            // the entire face lies inside of the plane, no clipping needed\n\n            outVertices.push(inVertices[i])\n            outVertices.push(inVertices[i + 1])\n            outVertices.push(inVertices[i + 2])\n            break\n          }\n\n          case 1: {\n            // one vertex lies outside of the plane, perform clipping\n\n            if (v1Out) {\n              nV1 = inVertices[i + 1]\n              nV2 = inVertices[i + 2]\n              nV3 = clip(inVertices[i], nV1, plane, s)\n              nV4 = clip(inVertices[i], nV2, plane, s)\n            }\n\n            if (v2Out) {\n              nV1 = inVertices[i]\n              nV2 = inVertices[i + 2]\n              nV3 = clip(inVertices[i + 1], nV1, plane, s)\n              nV4 = clip(inVertices[i + 1], nV2, plane, s)\n\n              outVertices.push(nV3)\n              outVertices.push(nV2.clone())\n              outVertices.push(nV1.clone())\n\n              outVertices.push(nV2.clone())\n              outVertices.push(nV3.clone())\n              outVertices.push(nV4)\n              break\n            }\n\n            if (v3Out) {\n              nV1 = inVertices[i]\n              nV2 = inVertices[i + 1]\n              nV3 = clip(inVertices[i + 2], nV1, plane, s)\n              nV4 = clip(inVertices[i + 2], nV2, plane, s)\n            }\n\n            outVertices.push(nV1.clone())\n            outVertices.push(nV2.clone())\n            outVertices.push(nV3)\n\n            outVertices.push(nV4)\n            outVertices.push(nV3.clone())\n            outVertices.push(nV2.clone())\n\n            break\n          }\n\n          case 2: {\n            // two vertices lies outside of the plane, perform clipping\n\n            if (!v1Out) {\n              nV1 = inVertices[i].clone()\n              nV2 = clip(nV1, inVertices[i + 1], plane, s)\n              nV3 = clip(nV1, inVertices[i + 2], plane, s)\n              outVertices.push(nV1)\n              outVertices.push(nV2)\n              outVertices.push(nV3)\n            }\n\n            if (!v2Out) {\n              nV1 = inVertices[i + 1].clone()\n              nV2 = clip(nV1, inVertices[i + 2], plane, s)\n              nV3 = clip(nV1, inVertices[i], plane, s)\n              outVertices.push(nV1)\n              outVertices.push(nV2)\n              outVertices.push(nV3)\n            }\n\n            if (!v3Out) {\n              nV1 = inVertices[i + 2].clone()\n              nV2 = clip(nV1, inVertices[i], plane, s)\n              nV3 = clip(nV1, inVertices[i + 1], plane, s)\n              outVertices.push(nV1)\n              outVertices.push(nV2)\n              outVertices.push(nV3)\n            }\n\n            break\n          }\n\n          case 3: {\n            // the entire face lies outside of the plane, so let's discard the corresponding vertices\n\n            break\n          }\n        }\n      }\n\n      return outVertices\n    }\n\n    function clip(v0, v1, p, s) {\n      const d0 = v0.position.dot(p) - s\n      const d1 = v1.position.dot(p) - s\n\n      const s0 = d0 / (d0 - d1)\n\n      const v = new DecalVertex(\n        new Vector3(\n          v0.position.x + s0 * (v1.position.x - v0.position.x),\n          v0.position.y + s0 * (v1.position.y - v0.position.y),\n          v0.position.z + s0 * (v1.position.z - v0.position.z),\n        ),\n        new Vector3(\n          v0.normal.x + s0 * (v1.normal.x - v0.normal.x),\n          v0.normal.y + s0 * (v1.normal.y - v0.normal.y),\n          v0.normal.z + s0 * (v1.normal.z - v0.normal.z),\n        ),\n      )\n\n      // need to clip more values (texture coordinates)? do it this way:\n      // intersectpoint.value = a.value + s * ( b.value - a.value );\n\n      return v\n    }\n  }\n}\n\n// helper\n\nclass DecalVertex {\n  constructor(position, normal) {\n    this.position = position\n    this.normal = normal\n  }\n\n  clone() {\n    return new this.constructor(this.position.clone(), this.normal.clone())\n  }\n}\n\nexport { DecalGeometry, DecalVertex }\n"], "names": ["plane"], "mappings": ";;;;;;AAiBA,MAAM,6NAAsB,iBAAA,CAAe;IACzC,YAAY,IAAA,EAAM,QAAA,EAAU,WAAA,EAAa,IAAA,CAAM;QAC7C,KAAA,CAAO;QAIP,MAAM,WAAW,CAAE,CAAA;QACnB,MAAM,UAAU,CAAE,CAAA;QAClB,MAAM,MAAM,CAAE,CAAA;QAId,MAAM,QAAQ,2MAAI,UAAA,CAAS;QAI3B,MAAM,kBAAkB,2MAAI,UAAA,CAAS;QACrC,gBAAgB,qBAAA,CAAsB,WAAW;QACjD,gBAAgB,WAAA,CAAY,QAAQ;QAEpC,MAAM,yBAAyB,2MAAI,UAAA,CAAS;QAC5C,uBAAuB,IAAA,CAAK,eAAe,EAAE,MAAA,CAAQ;QAIrD,SAAU;QAIV,IAAA,CAAK,YAAA,CAAa,YAAY,2MAAI,yBAAA,CAAuB,UAAU,CAAC,CAAC;QACrE,IAAA,CAAK,YAAA,CAAa,UAAU,2MAAI,yBAAA,CAAuB,SAAS,CAAC,CAAC;QAClE,IAAA,CAAK,YAAA,CAAa,MAAM,2MAAI,yBAAA,CAAuB,KAAK,CAAC,CAAC;QAE1D,SAAS,WAAW;YAClB,IAAI;YAEJ,IAAI,gBAAgB,CAAE,CAAA;YAEtB,MAAM,SAAS,2MAAI,UAAA,CAAS;YAC5B,MAAM,SAAS,2MAAI,UAAA,CAAS;YAI5B,IAAI,KAAK,QAAA,CAAS,UAAA,KAAe,MAAM;gBACrC,QAAQ,KAAA,CAAM,oFAAoF;gBAClG;YACD;YAED,MAAM,WAAW,KAAK,QAAA;YAEtB,MAAM,oBAAoB,SAAS,UAAA,CAAW,QAAA;YAC9C,MAAM,kBAAkB,SAAS,UAAA,CAAW,MAAA;YAO5C,IAAI,SAAS,KAAA,KAAU,MAAM;gBAG3B,MAAM,QAAQ,SAAS,KAAA;gBAEvB,IAAK,IAAI,GAAG,IAAI,MAAM,KAAA,EAAO,IAAK;oBAChC,OAAO,mBAAA,CAAoB,mBAAmB,MAAM,IAAA,CAAK,CAAC,CAAC;oBAC3D,OAAO,mBAAA,CAAoB,iBAAiB,MAAM,IAAA,CAAK,CAAC,CAAC;oBAEzD,gBAAgB,eAAe,QAAQ,MAAM;gBAC9C;YACT,OAAa;gBAGL,IAAK,IAAI,GAAG,IAAI,kBAAkB,KAAA,EAAO,IAAK;oBAC5C,OAAO,mBAAA,CAAoB,mBAAmB,CAAC;oBAC/C,OAAO,mBAAA,CAAoB,iBAAiB,CAAC;oBAE7C,gBAAgB,eAAe,QAAQ,MAAM;gBAC9C;YACF;YAID,gBAAgB,aAAa,eAAe,MAAM,GAAA,CAAI,GAAG,GAAG,CAAC,CAAC;YAC9D,gBAAgB,aAAa,eAAe,MAAM,GAAA,CAAI,CAAA,GAAI,GAAG,CAAC,CAAC;YAC/D,gBAAgB,aAAa,eAAe,MAAM,GAAA,CAAI,GAAG,GAAG,CAAC,CAAC;YAC9D,gBAAgB,aAAa,eAAe,MAAM,GAAA,CAAI,GAAG,CAAA,GAAI,CAAC,CAAC;YAC/D,gBAAgB,aAAa,eAAe,MAAM,GAAA,CAAI,GAAG,GAAG,CAAC,CAAC;YAC9D,gBAAgB,aAAa,eAAe,MAAM,GAAA,CAAI,GAAG,GAAG,CAAA,CAAE,CAAC;YAI/D,IAAK,IAAI,GAAG,IAAI,cAAc,MAAA,EAAQ,IAAK;gBACzC,MAAM,cAAc,aAAA,CAAc,CAAC,CAAA;gBAInC,IAAI,IAAA,CAAK,MAAM,YAAY,QAAA,CAAS,CAAA,GAAI,KAAK,CAAA,EAAG,MAAM,YAAY,QAAA,CAAS,CAAA,GAAI,KAAK,CAAC;gBAIrF,YAAY,QAAA,CAAS,YAAA,CAAa,eAAe;gBAIjD,SAAS,IAAA,CAAK,YAAY,QAAA,CAAS,CAAA,EAAG,YAAY,QAAA,CAAS,CAAA,EAAG,YAAY,QAAA,CAAS,CAAC;gBACpF,QAAQ,IAAA,CAAK,YAAY,MAAA,CAAO,CAAA,EAAG,YAAY,MAAA,CAAO,CAAA,EAAG,YAAY,MAAA,CAAO,CAAC;YAC9E;QACF;QAED,SAAS,gBAAgB,aAAA,EAAe,MAAA,EAAQ,MAAA,EAAQ;YAGtD,OAAO,YAAA,CAAa,KAAK,WAAW;YACpC,OAAO,YAAA,CAAa,sBAAsB;YAE1C,OAAO,kBAAA,CAAmB,KAAK,WAAW;YAE1C,cAAc,IAAA,CAAK,IAAI,YAAY,OAAO,KAAA,IAAS,OAAO,KAAA,CAAK,CAAE,CAAC;QACnE;QAED,SAAS,aAAa,UAAA,EAAYA,MAAAA,EAAO;YACvC,MAAM,cAAc,CAAE,CAAA;YAEtB,MAAM,IAAI,MAAM,KAAK,GAAA,CAAI,KAAK,GAAA,CAAIA,MAAK,CAAC;YAKxC,IAAA,IAAS,IAAI,GAAG,IAAI,WAAW,MAAA,EAAQ,KAAK,EAAG;gBAC7C,IAAI,OACF,OACA,OACA,QAAQ;gBACV,IAAI,KAAK,KAAK,KAAK;gBAEnB,MAAM,KAAK,UAAA,CAAW,IAAI,CAAC,CAAA,CAAE,QAAA,CAAS,GAAA,CAAIA,MAAK,IAAI;gBACnD,MAAM,KAAK,UAAA,CAAW,IAAI,CAAC,CAAA,CAAE,QAAA,CAAS,GAAA,CAAIA,MAAK,IAAI;gBACnD,MAAM,KAAK,UAAA,CAAW,IAAI,CAAC,CAAA,CAAE,QAAA,CAAS,GAAA,CAAIA,MAAK,IAAI;gBAEnD,QAAQ,KAAK;gBACb,QAAQ,KAAK;gBACb,QAAQ,KAAK;gBAIb,QAAA,CAAS,QAAQ,IAAI,CAAA,IAAA,CAAM,QAAQ,IAAI,CAAA,IAAA,CAAM,QAAQ,IAAI,CAAA;gBAEzD,OAAQ,OAAK;oBACX,KAAK;wBAAG;4BAGN,YAAY,IAAA,CAAK,UAAA,CAAW,CAAC,CAAC;4BAC9B,YAAY,IAAA,CAAK,UAAA,CAAW,IAAI,CAAC,CAAC;4BAClC,YAAY,IAAA,CAAK,UAAA,CAAW,IAAI,CAAC,CAAC;4BAClC;wBACD;oBAED,KAAK;wBAAG;4BAGN,IAAI,OAAO;gCACT,MAAM,UAAA,CAAW,IAAI,CAAC,CAAA;gCACtB,MAAM,UAAA,CAAW,IAAI,CAAC,CAAA;gCACtB,MAAM,KAAK,UAAA,CAAW,CAAC,CAAA,EAAG,KAAKA,QAAO,CAAC;gCACvC,MAAM,KAAK,UAAA,CAAW,CAAC,CAAA,EAAG,KAAKA,QAAO,CAAC;4BACxC;4BAED,IAAI,OAAO;gCACT,MAAM,UAAA,CAAW,CAAC,CAAA;gCAClB,MAAM,UAAA,CAAW,IAAI,CAAC,CAAA;gCACtB,MAAM,KAAK,UAAA,CAAW,IAAI,CAAC,CAAA,EAAG,KAAKA,QAAO,CAAC;gCAC3C,MAAM,KAAK,UAAA,CAAW,IAAI,CAAC,CAAA,EAAG,KAAKA,QAAO,CAAC;gCAE3C,YAAY,IAAA,CAAK,GAAG;gCACpB,YAAY,IAAA,CAAK,IAAI,KAAA,EAAO;gCAC5B,YAAY,IAAA,CAAK,IAAI,KAAA,EAAO;gCAE5B,YAAY,IAAA,CAAK,IAAI,KAAA,EAAO;gCAC5B,YAAY,IAAA,CAAK,IAAI,KAAA,EAAO;gCAC5B,YAAY,IAAA,CAAK,GAAG;gCACpB;4BACD;4BAED,IAAI,OAAO;gCACT,MAAM,UAAA,CAAW,CAAC,CAAA;gCAClB,MAAM,UAAA,CAAW,IAAI,CAAC,CAAA;gCACtB,MAAM,KAAK,UAAA,CAAW,IAAI,CAAC,CAAA,EAAG,KAAKA,QAAO,CAAC;gCAC3C,MAAM,KAAK,UAAA,CAAW,IAAI,CAAC,CAAA,EAAG,KAAKA,QAAO,CAAC;4BAC5C;4BAED,YAAY,IAAA,CAAK,IAAI,KAAA,EAAO;4BAC5B,YAAY,IAAA,CAAK,IAAI,KAAA,EAAO;4BAC5B,YAAY,IAAA,CAAK,GAAG;4BAEpB,YAAY,IAAA,CAAK,GAAG;4BACpB,YAAY,IAAA,CAAK,IAAI,KAAA,EAAO;4BAC5B,YAAY,IAAA,CAAK,IAAI,KAAA,EAAO;4BAE5B;wBACD;oBAED,KAAK;wBAAG;4BAGN,IAAI,CAAC,OAAO;gCACV,MAAM,UAAA,CAAW,CAAC,CAAA,CAAE,KAAA,CAAO;gCAC3B,MAAM,KAAK,KAAK,UAAA,CAAW,IAAI,CAAC,CAAA,EAAGA,QAAO,CAAC;gCAC3C,MAAM,KAAK,KAAK,UAAA,CAAW,IAAI,CAAC,CAAA,EAAGA,QAAO,CAAC;gCAC3C,YAAY,IAAA,CAAK,GAAG;gCACpB,YAAY,IAAA,CAAK,GAAG;gCACpB,YAAY,IAAA,CAAK,GAAG;4BACrB;4BAED,IAAI,CAAC,OAAO;gCACV,MAAM,UAAA,CAAW,IAAI,CAAC,CAAA,CAAE,KAAA,CAAO;gCAC/B,MAAM,KAAK,KAAK,UAAA,CAAW,IAAI,CAAC,CAAA,EAAGA,QAAO,CAAC;gCAC3C,MAAM,KAAK,KAAK,UAAA,CAAW,CAAC,CAAA,EAAGA,QAAO,CAAC;gCACvC,YAAY,IAAA,CAAK,GAAG;gCACpB,YAAY,IAAA,CAAK,GAAG;gCACpB,YAAY,IAAA,CAAK,GAAG;4BACrB;4BAED,IAAI,CAAC,OAAO;gCACV,MAAM,UAAA,CAAW,IAAI,CAAC,CAAA,CAAE,KAAA,CAAO;gCAC/B,MAAM,KAAK,KAAK,UAAA,CAAW,CAAC,CAAA,EAAGA,QAAO,CAAC;gCACvC,MAAM,KAAK,KAAK,UAAA,CAAW,IAAI,CAAC,CAAA,EAAGA,QAAO,CAAC;gCAC3C,YAAY,IAAA,CAAK,GAAG;gCACpB,YAAY,IAAA,CAAK,GAAG;gCACpB,YAAY,IAAA,CAAK,GAAG;4BACrB;4BAED;wBACD;gBAOF;YACF;YAED,OAAO;QACR;QAED,SAAS,KAAK,EAAA,EAAI,EAAA,EAAI,CAAA,EAAG,CAAA,EAAG;YAC1B,MAAM,KAAK,GAAG,QAAA,CAAS,GAAA,CAAI,CAAC,IAAI;YAChC,MAAM,KAAK,GAAG,QAAA,CAAS,GAAA,CAAI,CAAC,IAAI;YAEhC,MAAM,KAAK,KAAA,CAAM,KAAK,EAAA;YAEtB,MAAM,IAAI,IAAI,YACZ,2MAAI,UAAA,CACF,GAAG,QAAA,CAAS,CAAA,GAAI,KAAA,CAAM,GAAG,QAAA,CAAS,CAAA,GAAI,GAAG,QAAA,CAAS,CAAA,GAClD,GAAG,QAAA,CAAS,CAAA,GAAI,KAAA,CAAM,GAAG,QAAA,CAAS,CAAA,GAAI,GAAG,QAAA,CAAS,CAAA,GAClD,GAAG,QAAA,CAAS,CAAA,GAAI,KAAA,CAAM,GAAG,QAAA,CAAS,CAAA,GAAI,GAAG,QAAA,CAAS,CAAA,IAEpD,2MAAI,UAAA,CACF,GAAG,MAAA,CAAO,CAAA,GAAI,KAAA,CAAM,GAAG,MAAA,CAAO,CAAA,GAAI,GAAG,MAAA,CAAO,CAAA,GAC5C,GAAG,MAAA,CAAO,CAAA,GAAI,KAAA,CAAM,GAAG,MAAA,CAAO,CAAA,GAAI,GAAG,MAAA,CAAO,CAAA,GAC5C,GAAG,MAAA,CAAO,CAAA,GAAI,KAAA,CAAM,GAAG,MAAA,CAAO,CAAA,GAAI,GAAG,MAAA,CAAO,CAAA;YAOhD,OAAO;QACR;IACF;AACH;AAIA,MAAM,YAAY;IAChB,YAAY,QAAA,EAAU,MAAA,CAAQ;QAC5B,IAAA,CAAK,QAAA,GAAW;QAChB,IAAA,CAAK,MAAA,GAAS;IACf;IAED,QAAQ;QACN,OAAO,IAAI,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,QAAA,CAAS,KAAA,CAAK,GAAI,IAAA,CAAK,MAAA,CAAO,KAAA,EAAO;IACvE;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1588, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1594, "column": 0}, "map": {"version": 3, "file": "helpers.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/types/helpers.ts"], "sourcesContent": ["export const getWithKey = <T, K extends keyof T>(obj: T, key: K): T[K] => obj[key]\n"], "names": [], "mappings": ";;;AAAO,MAAM,aAAa,CAAuB,KAAQ,MAAiB,GAAA,CAAI,GAAG,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1600, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1606, "column": 0}, "map": {"version": 3, "file": "BufferGeometryUtils.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/utils/BufferGeometryUtils.ts"], "sourcesContent": ["import {\n  BufferAttribute,\n  BufferGeometry,\n  Float32BufferAttribute,\n  InterleavedBuffer,\n  InterleavedBufferAttribute,\n  TriangleFanDrawMode,\n  TriangleStripDrawMode,\n  TrianglesDrawMode,\n  Vector3,\n  Mesh,\n  Line,\n  Points,\n  Material,\n  SkinnedMesh,\n} from 'three'\n\nimport { getWithKey } from '../types/helpers'\nimport type { TypedArrayConstructors, TypedArray } from '../types/shared'\n\n/**\n * @param  {Array<BufferGeometry>} geometries\n * @param  {Boolean} useGroups\n * @return {BufferGeometry}\n */\nexport const mergeBufferGeometries = (geometries: BufferGeometry[], useGroups?: boolean): BufferGeometry | null => {\n  const isIndexed = geometries[0].index !== null\n\n  const attributesUsed = new Set(Object.keys(geometries[0].attributes))\n  const morphAttributesUsed = new Set(Object.keys(geometries[0].morphAttributes))\n\n  const attributes: { [key: string]: Array<InterleavedBufferAttribute | BufferAttribute> } = {}\n  const morphAttributes: { [key: string]: Array<BufferAttribute | InterleavedBufferAttribute>[] } = {}\n\n  const morphTargetsRelative = geometries[0].morphTargetsRelative\n\n  const mergedGeometry = new BufferGeometry()\n\n  let offset = 0\n\n  geometries.forEach((geom, i) => {\n    let attributesCount = 0\n\n    // ensure that all geometries are indexed, or none\n\n    if (isIndexed !== (geom.index !== null)) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n          i +\n          '. All geometries must have compatible attributes; make sure index attribute exists among all geometries, or in none of them.',\n      )\n      return null\n    }\n\n    // gather attributes, exit early if they're different\n\n    for (let name in geom.attributes) {\n      if (!attributesUsed.has(name)) {\n        console.error(\n          'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n            i +\n            '. All geometries must have compatible attributes; make sure \"' +\n            name +\n            '\" attribute exists among all geometries, or in none of them.',\n        )\n        return null\n      }\n\n      if (attributes[name] === undefined) {\n        attributes[name] = []\n      }\n\n      attributes[name].push(geom.attributes[name])\n\n      attributesCount++\n    }\n\n    // ensure geometries have the same number of attributes\n\n    if (attributesCount !== attributesUsed.size) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n          i +\n          '. Make sure all geometries have the same number of attributes.',\n      )\n      return null\n    }\n\n    // gather morph attributes, exit early if they're different\n\n    if (morphTargetsRelative !== geom.morphTargetsRelative) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n          i +\n          '. .morphTargetsRelative must be consistent throughout all geometries.',\n      )\n      return null\n    }\n\n    for (let name in geom.morphAttributes) {\n      if (!morphAttributesUsed.has(name)) {\n        console.error(\n          'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n            i +\n            '.  .morphAttributes must be consistent throughout all geometries.',\n        )\n        return null\n      }\n\n      if (morphAttributes[name] === undefined) morphAttributes[name] = []\n\n      morphAttributes[name].push(geom.morphAttributes[name])\n    }\n\n    // gather .userData\n\n    mergedGeometry.userData.mergedUserData = mergedGeometry.userData.mergedUserData || []\n    mergedGeometry.userData.mergedUserData.push(geom.userData)\n\n    if (useGroups) {\n      let count\n\n      if (geom.index) {\n        count = geom.index.count\n      } else if (geom.attributes.position !== undefined) {\n        count = geom.attributes.position.count\n      } else {\n        console.error(\n          'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed with geometry at index ' +\n            i +\n            '. The geometry must have either an index or a position attribute',\n        )\n        return null\n      }\n\n      mergedGeometry.addGroup(offset, count, i)\n\n      offset += count\n    }\n  })\n\n  // merge indices\n\n  if (isIndexed) {\n    let indexOffset = 0\n    const mergedIndex: number[] = []\n\n    geometries.forEach((geom) => {\n      const index = geom.index as BufferAttribute\n\n      for (let j = 0; j < index.count; ++j) {\n        mergedIndex.push(index.getX(j) + indexOffset)\n      }\n\n      indexOffset += geom.attributes.position.count\n    })\n\n    mergedGeometry.setIndex(mergedIndex)\n  }\n\n  // merge attributes\n\n  for (let name in attributes) {\n    const mergedAttribute = mergeBufferAttributes(attributes[name] as BufferAttribute[])\n\n    if (!mergedAttribute) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed while trying to merge the ' + name + ' attribute.',\n      )\n      return null\n    }\n\n    mergedGeometry.setAttribute(name, mergedAttribute)\n  }\n\n  // merge morph attributes\n\n  for (let name in morphAttributes) {\n    const numMorphTargets = morphAttributes[name][0].length\n\n    if (numMorphTargets === 0) break\n\n    mergedGeometry.morphAttributes = mergedGeometry.morphAttributes || {}\n    mergedGeometry.morphAttributes[name] = []\n\n    for (let i = 0; i < numMorphTargets; ++i) {\n      const morphAttributesToMerge = []\n\n      for (let j = 0; j < morphAttributes[name].length; ++j) {\n        morphAttributesToMerge.push(morphAttributes[name][j][i])\n      }\n\n      const mergedMorphAttribute = mergeBufferAttributes(morphAttributesToMerge as BufferAttribute[])\n\n      if (!mergedMorphAttribute) {\n        console.error(\n          'THREE.BufferGeometryUtils: .mergeBufferGeometries() failed while trying to merge the ' +\n            name +\n            ' morphAttribute.',\n        )\n        return null\n      }\n\n      mergedGeometry.morphAttributes[name].push(mergedMorphAttribute)\n    }\n  }\n\n  return mergedGeometry\n}\n\n/**\n * @param {Array<BufferAttribute>} attributes\n * @return {BufferAttribute}\n */\nexport const mergeBufferAttributes = (attributes: BufferAttribute[]): BufferAttribute | null | undefined => {\n  let TypedArray: TypedArrayConstructors | undefined = undefined\n  let itemSize: number | undefined = undefined\n  let normalized: boolean | undefined = undefined\n  let arrayLength = 0\n\n  attributes.forEach((attr) => {\n    if (TypedArray === undefined) {\n      TypedArray = attr.array.constructor\n    }\n    if (TypedArray !== attr.array.constructor) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferAttributes() failed. BufferAttribute.array must be of consistent array types across matching attributes.',\n      )\n      return null\n    }\n\n    if (itemSize === undefined) itemSize = attr.itemSize\n    if (itemSize !== attr.itemSize) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferAttributes() failed. BufferAttribute.itemSize must be consistent across matching attributes.',\n      )\n      return null\n    }\n\n    if (normalized === undefined) normalized = attr.normalized\n    if (normalized !== attr.normalized) {\n      console.error(\n        'THREE.BufferGeometryUtils: .mergeBufferAttributes() failed. BufferAttribute.normalized must be consistent across matching attributes.',\n      )\n      return null\n    }\n\n    arrayLength += attr.array.length\n  })\n\n  if (TypedArray && itemSize) {\n    // @ts-ignore this works in JS and TS is complaining but it's such a tiny thing I can live with the guilt\n    const array = new TypedArray(arrayLength)\n    let offset = 0\n\n    attributes.forEach((attr) => {\n      array.set(attr.array, offset)\n      offset += attr.array.length\n    })\n\n    return new BufferAttribute(array, itemSize, normalized)\n  }\n}\n\n/**\n * @param {Array<BufferAttribute>} attributes\n * @return {Array<InterleavedBufferAttribute>}\n */\nexport const interleaveAttributes = (attributes: BufferAttribute[]): InterleavedBufferAttribute[] | null => {\n  // Interleaves the provided attributes into an InterleavedBuffer and returns\n  // a set of InterleavedBufferAttributes for each attribute\n  let TypedArray: TypedArrayConstructors | undefined = undefined\n  let arrayLength = 0\n  let stride = 0\n\n  // calculate the the length and type of the interleavedBuffer\n  for (let i = 0, l = attributes.length; i < l; ++i) {\n    const attribute = attributes[i]\n\n    if (TypedArray === undefined) TypedArray = attribute.array.constructor\n    if (TypedArray !== attribute.array.constructor) {\n      console.error('AttributeBuffers of different types cannot be interleaved')\n      return null\n    }\n\n    arrayLength += attribute.array.length\n    stride += attribute.itemSize\n  }\n\n  // Create the set of buffer attributes\n  // @ts-ignore this works in JS and TS is complaining but it's such a tiny thing I can live with the guilt\n  const interleavedBuffer = new InterleavedBuffer(new TypedArray(arrayLength), stride)\n  let offset = 0\n  const res = []\n  const getters = ['getX', 'getY', 'getZ', 'getW']\n  const setters = ['setX', 'setY', 'setZ', 'setW']\n\n  for (let j = 0, l = attributes.length; j < l; j++) {\n    const attribute = attributes[j]\n    const itemSize = attribute.itemSize\n    const count = attribute.count\n    const iba = new InterleavedBufferAttribute(interleavedBuffer, itemSize, offset, attribute.normalized)\n    res.push(iba)\n\n    offset += itemSize\n\n    // Move the data for each attribute into the new interleavedBuffer\n    // at the appropriate offset\n    for (let c = 0; c < count; c++) {\n      for (let k = 0; k < itemSize; k++) {\n        const set = getWithKey(iba, setters[k] as keyof InterleavedBufferAttribute) as InterleavedBufferAttribute[\n          | 'setX'\n          | 'setY'\n          | 'setZ'\n          | 'setW']\n        const get = getWithKey(attribute, getters[k] as keyof BufferAttribute) as BufferAttribute[\n          | 'getX'\n          | 'getY'\n          | 'getZ'\n          | 'getW']\n        set(c, get(c))\n      }\n    }\n  }\n\n  return res\n}\n\n/**\n * @param {Array<BufferGeometry>} geometry\n * @return {number}\n */\nexport function estimateBytesUsed(geometry: BufferGeometry): number {\n  // Return the estimated memory used by this geometry in bytes\n  // Calculate using itemSize, count, and BYTES_PER_ELEMENT to account\n  // for InterleavedBufferAttributes.\n  let mem = 0\n  for (let name in geometry.attributes) {\n    const attr = geometry.getAttribute(name)\n    mem += attr.count * attr.itemSize * (attr.array as TypedArray).BYTES_PER_ELEMENT\n  }\n\n  const indices = geometry.getIndex()\n  mem += indices ? indices.count * indices.itemSize * (indices.array as TypedArray).BYTES_PER_ELEMENT : 0\n  return mem\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {number} tolerance\n * @return {BufferGeometry>}\n */\nexport function mergeVertices(geometry: BufferGeometry, tolerance = 1e-4): BufferGeometry {\n  tolerance = Math.max(tolerance, Number.EPSILON)\n\n  // Generate an index buffer if the geometry doesn't have one, or optimize it\n  // if it's already available.\n  const hashToIndex: {\n    [key: string]: number\n  } = {}\n  const indices = geometry.getIndex()\n  const positions = geometry.getAttribute('position')\n  const vertexCount = indices ? indices.count : positions.count\n\n  // next value for triangle indices\n  let nextIndex = 0\n\n  // attributes and new attribute arrays\n  const attributeNames = Object.keys(geometry.attributes)\n  const attrArrays: {\n    [key: string]: []\n  } = {}\n  const morphAttrsArrays: {\n    [key: string]: Array<Array<BufferAttribute | InterleavedBufferAttribute>>\n  } = {}\n  const newIndices = []\n  const getters = ['getX', 'getY', 'getZ', 'getW']\n\n  // initialize the arrays\n  for (let i = 0, l = attributeNames.length; i < l; i++) {\n    const name = attributeNames[i]\n\n    attrArrays[name] = []\n\n    const morphAttr = geometry.morphAttributes[name]\n    if (morphAttr) {\n      morphAttrsArrays[name] = new Array(morphAttr.length).fill(0).map(() => [])\n    }\n  }\n\n  // convert the error tolerance to an amount of decimal places to truncate to\n  const decimalShift = Math.log10(1 / tolerance)\n  const shiftMultiplier = Math.pow(10, decimalShift)\n  for (let i = 0; i < vertexCount; i++) {\n    const index = indices ? indices.getX(i) : i\n\n    // Generate a hash for the vertex attributes at the current index 'i'\n    let hash = ''\n    for (let j = 0, l = attributeNames.length; j < l; j++) {\n      const name = attributeNames[j]\n      const attribute = geometry.getAttribute(name)\n      const itemSize = attribute.itemSize\n\n      for (let k = 0; k < itemSize; k++) {\n        // double tilde truncates the decimal value\n        // @ts-ignore no\n        hash += `${~~(attribute[getters[k]](index) * shiftMultiplier)},`\n      }\n    }\n\n    // Add another reference to the vertex if it's already\n    // used by another index\n    if (hash in hashToIndex) {\n      newIndices.push(hashToIndex[hash])\n    } else {\n      // copy data to the new index in the attribute arrays\n      for (let j = 0, l = attributeNames.length; j < l; j++) {\n        const name = attributeNames[j]\n        const attribute = geometry.getAttribute(name)\n        const morphAttr = geometry.morphAttributes[name]\n        const itemSize = attribute.itemSize\n        const newarray = attrArrays[name]\n        const newMorphArrays = morphAttrsArrays[name]\n\n        for (let k = 0; k < itemSize; k++) {\n          const getterFunc = getters[k]\n          // @ts-ignore\n          newarray.push(attribute[getterFunc](index))\n\n          if (morphAttr) {\n            for (let m = 0, ml = morphAttr.length; m < ml; m++) {\n              // @ts-ignore\n              newMorphArrays[m].push(morphAttr[m][getterFunc](index))\n            }\n          }\n        }\n      }\n\n      hashToIndex[hash] = nextIndex\n      newIndices.push(nextIndex)\n      nextIndex++\n    }\n  }\n\n  // Generate typed arrays from new attribute arrays and update\n  // the attributeBuffers\n  const result = geometry.clone()\n  for (let i = 0, l = attributeNames.length; i < l; i++) {\n    const name = attributeNames[i]\n    const oldAttribute = geometry.getAttribute(name)\n    //@ts-expect-error  something to do with functions and constructors and new\n    const buffer = new (oldAttribute.array as TypedArray).constructor(attrArrays[name])\n    const attribute = new BufferAttribute(buffer, oldAttribute.itemSize, oldAttribute.normalized)\n\n    result.setAttribute(name, attribute)\n\n    // Update the attribute arrays\n    if (name in morphAttrsArrays) {\n      for (let j = 0; j < morphAttrsArrays[name].length; j++) {\n        const oldMorphAttribute = geometry.morphAttributes[name][j]\n        //@ts-expect-error something to do with functions and constructors and new\n        const buffer = new (oldMorphAttribute.array as TypedArray).constructor(morphAttrsArrays[name][j])\n        const morphAttribute = new BufferAttribute(buffer, oldMorphAttribute.itemSize, oldMorphAttribute.normalized)\n        result.morphAttributes[name][j] = morphAttribute\n      }\n    }\n  }\n\n  // indices\n\n  result.setIndex(newIndices)\n\n  return result\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {number} drawMode\n * @return {BufferGeometry}\n */\nexport function toTrianglesDrawMode(geometry: BufferGeometry, drawMode: number): BufferGeometry {\n  if (drawMode === TrianglesDrawMode) {\n    console.warn('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Geometry already defined as triangles.')\n    return geometry\n  }\n\n  if (drawMode === TriangleFanDrawMode || drawMode === TriangleStripDrawMode) {\n    let index = geometry.getIndex()\n\n    // generate index if not present\n\n    if (index === null) {\n      const indices = []\n\n      const position = geometry.getAttribute('position')\n\n      if (position !== undefined) {\n        for (let i = 0; i < position.count; i++) {\n          indices.push(i)\n        }\n\n        geometry.setIndex(indices)\n        index = geometry.getIndex()\n      } else {\n        console.error(\n          'THREE.BufferGeometryUtils.toTrianglesDrawMode(): Undefined position attribute. Processing not possible.',\n        )\n        return geometry\n      }\n    }\n\n    //\n\n    const numberOfTriangles = (index as BufferAttribute).count - 2\n    const newIndices = []\n\n    if (index) {\n      if (drawMode === TriangleFanDrawMode) {\n        // gl.TRIANGLE_FAN\n\n        for (let i = 1; i <= numberOfTriangles; i++) {\n          newIndices.push(index.getX(0))\n          newIndices.push(index.getX(i))\n          newIndices.push(index.getX(i + 1))\n        }\n      } else {\n        // gl.TRIANGLE_STRIP\n\n        for (let i = 0; i < numberOfTriangles; i++) {\n          if (i % 2 === 0) {\n            newIndices.push(index.getX(i))\n            newIndices.push(index.getX(i + 1))\n            newIndices.push(index.getX(i + 2))\n          } else {\n            newIndices.push(index.getX(i + 2))\n            newIndices.push(index.getX(i + 1))\n            newIndices.push(index.getX(i))\n          }\n        }\n      }\n    }\n\n    if (newIndices.length / 3 !== numberOfTriangles) {\n      console.error('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unable to generate correct amount of triangles.')\n    }\n\n    // build final geometry\n\n    const newGeometry = geometry.clone()\n    newGeometry.setIndex(newIndices)\n    newGeometry.clearGroups()\n\n    return newGeometry\n  } else {\n    console.error('THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unknown draw mode:', drawMode)\n    return geometry\n  }\n}\n\n/**\n * Calculates the morphed attributes of a morphed/skinned BufferGeometry.\n * Helpful for Raytracing or Decals.\n * @param {Mesh | Line | Points} object An instance of Mesh, Line or Points.\n * @return {Object} An Object with original position/normal attributes and morphed ones.\n */\nexport type ComputedMorphedAttribute = {\n  positionAttribute: BufferAttribute | InterleavedBufferAttribute\n  normalAttribute: BufferAttribute | InterleavedBufferAttribute\n  morphedPositionAttribute: Float32BufferAttribute\n  morphedNormalAttribute: Float32BufferAttribute\n}\n\nexport function computeMorphedAttributes(object: Mesh | Line | Points): ComputedMorphedAttribute | null {\n  if (object.geometry.isBufferGeometry !== true) {\n    console.error('THREE.BufferGeometryUtils: Geometry is not of type BufferGeometry.')\n    return null\n  }\n\n  const _vA = new Vector3()\n  const _vB = new Vector3()\n  const _vC = new Vector3()\n\n  const _tempA = new Vector3()\n  const _tempB = new Vector3()\n  const _tempC = new Vector3()\n\n  const _morphA = new Vector3()\n  const _morphB = new Vector3()\n  const _morphC = new Vector3()\n\n  function _calculateMorphedAttributeData(\n    object: Mesh | Line | Points,\n    material: Material,\n    attribute: BufferAttribute | InterleavedBufferAttribute,\n    morphAttribute: (BufferAttribute | InterleavedBufferAttribute)[],\n    morphTargetsRelative: boolean,\n    a: number,\n    b: number,\n    c: number,\n    modifiedAttributeArray: Float32Array,\n  ): void {\n    _vA.fromBufferAttribute(attribute, a)\n    _vB.fromBufferAttribute(attribute, b)\n    _vC.fromBufferAttribute(attribute, c)\n\n    const morphInfluences = object.morphTargetInfluences\n\n    if (\n      // @ts-ignore\n      material.morphTargets &&\n      morphAttribute &&\n      morphInfluences\n    ) {\n      _morphA.set(0, 0, 0)\n      _morphB.set(0, 0, 0)\n      _morphC.set(0, 0, 0)\n\n      for (let i = 0, il = morphAttribute.length; i < il; i++) {\n        const influence = morphInfluences[i]\n        const morph = morphAttribute[i]\n\n        if (influence === 0) continue\n\n        _tempA.fromBufferAttribute(morph, a)\n        _tempB.fromBufferAttribute(morph, b)\n        _tempC.fromBufferAttribute(morph, c)\n\n        if (morphTargetsRelative) {\n          _morphA.addScaledVector(_tempA, influence)\n          _morphB.addScaledVector(_tempB, influence)\n          _morphC.addScaledVector(_tempC, influence)\n        } else {\n          _morphA.addScaledVector(_tempA.sub(_vA), influence)\n          _morphB.addScaledVector(_tempB.sub(_vB), influence)\n          _morphC.addScaledVector(_tempC.sub(_vC), influence)\n        }\n      }\n\n      _vA.add(_morphA)\n      _vB.add(_morphB)\n      _vC.add(_morphC)\n    }\n\n    if ((object as SkinnedMesh).isSkinnedMesh) {\n      // @ts-ignore – https://github.com/three-types/three-ts-types/issues/37\n      object.boneTransform(a, _vA)\n      // @ts-ignore – https://github.com/three-types/three-ts-types/issues/37\n      object.boneTransform(b, _vB)\n      // @ts-ignore – https://github.com/three-types/three-ts-types/issues/37\n      object.boneTransform(c, _vC)\n    }\n\n    modifiedAttributeArray[a * 3 + 0] = _vA.x\n    modifiedAttributeArray[a * 3 + 1] = _vA.y\n    modifiedAttributeArray[a * 3 + 2] = _vA.z\n    modifiedAttributeArray[b * 3 + 0] = _vB.x\n    modifiedAttributeArray[b * 3 + 1] = _vB.y\n    modifiedAttributeArray[b * 3 + 2] = _vB.z\n    modifiedAttributeArray[c * 3 + 0] = _vC.x\n    modifiedAttributeArray[c * 3 + 1] = _vC.y\n    modifiedAttributeArray[c * 3 + 2] = _vC.z\n  }\n\n  const geometry = object.geometry\n  const material = object.material\n\n  let a, b, c\n  const index = geometry.index\n  const positionAttribute = geometry.attributes.position\n  const morphPosition = geometry.morphAttributes.position\n  const morphTargetsRelative = geometry.morphTargetsRelative\n  const normalAttribute = geometry.attributes.normal\n  const morphNormal = geometry.morphAttributes.position\n\n  const groups = geometry.groups\n  const drawRange = geometry.drawRange\n  let i, j, il, jl\n  let group, groupMaterial\n  let start, end\n\n  const modifiedPosition = new Float32Array(positionAttribute.count * positionAttribute.itemSize)\n  const modifiedNormal = new Float32Array(normalAttribute.count * normalAttribute.itemSize)\n\n  if (index !== null) {\n    // indexed buffer geometry\n\n    if (Array.isArray(material)) {\n      for (i = 0, il = groups.length; i < il; i++) {\n        group = groups[i]\n        groupMaterial = material[group.materialIndex as number]\n\n        start = Math.max(group.start, drawRange.start)\n        end = Math.min(group.start + group.count, drawRange.start + drawRange.count)\n\n        for (j = start, jl = end; j < jl; j += 3) {\n          a = index.getX(j)\n          b = index.getX(j + 1)\n          c = index.getX(j + 2)\n\n          _calculateMorphedAttributeData(\n            object,\n            groupMaterial,\n            positionAttribute,\n            morphPosition,\n            morphTargetsRelative,\n            a,\n            b,\n            c,\n            modifiedPosition,\n          )\n\n          _calculateMorphedAttributeData(\n            object,\n            groupMaterial,\n            normalAttribute,\n            morphNormal,\n            morphTargetsRelative,\n            a,\n            b,\n            c,\n            modifiedNormal,\n          )\n        }\n      }\n    } else {\n      start = Math.max(0, drawRange.start)\n      end = Math.min(index.count, drawRange.start + drawRange.count)\n\n      for (i = start, il = end; i < il; i += 3) {\n        a = index.getX(i)\n        b = index.getX(i + 1)\n        c = index.getX(i + 2)\n\n        _calculateMorphedAttributeData(\n          object,\n          material,\n          positionAttribute,\n          morphPosition,\n          morphTargetsRelative,\n          a,\n          b,\n          c,\n          modifiedPosition,\n        )\n\n        _calculateMorphedAttributeData(\n          object,\n          material,\n          normalAttribute,\n          morphNormal,\n          morphTargetsRelative,\n          a,\n          b,\n          c,\n          modifiedNormal,\n        )\n      }\n    }\n  } else if (positionAttribute !== undefined) {\n    // non-indexed buffer geometry\n\n    if (Array.isArray(material)) {\n      for (i = 0, il = groups.length; i < il; i++) {\n        group = groups[i]\n        groupMaterial = material[group.materialIndex as number]\n\n        start = Math.max(group.start, drawRange.start)\n        end = Math.min(group.start + group.count, drawRange.start + drawRange.count)\n\n        for (j = start, jl = end; j < jl; j += 3) {\n          a = j\n          b = j + 1\n          c = j + 2\n\n          _calculateMorphedAttributeData(\n            object,\n            groupMaterial,\n            positionAttribute,\n            morphPosition,\n            morphTargetsRelative,\n            a,\n            b,\n            c,\n            modifiedPosition,\n          )\n\n          _calculateMorphedAttributeData(\n            object,\n            groupMaterial,\n            normalAttribute,\n            morphNormal,\n            morphTargetsRelative,\n            a,\n            b,\n            c,\n            modifiedNormal,\n          )\n        }\n      }\n    } else {\n      start = Math.max(0, drawRange.start)\n      end = Math.min(positionAttribute.count, drawRange.start + drawRange.count)\n\n      for (i = start, il = end; i < il; i += 3) {\n        a = i\n        b = i + 1\n        c = i + 2\n\n        _calculateMorphedAttributeData(\n          object,\n          material,\n          positionAttribute,\n          morphPosition,\n          morphTargetsRelative,\n          a,\n          b,\n          c,\n          modifiedPosition,\n        )\n\n        _calculateMorphedAttributeData(\n          object,\n          material,\n          normalAttribute,\n          morphNormal,\n          morphTargetsRelative,\n          a,\n          b,\n          c,\n          modifiedNormal,\n        )\n      }\n    }\n  }\n\n  const morphedPositionAttribute = new Float32BufferAttribute(modifiedPosition, 3)\n  const morphedNormalAttribute = new Float32BufferAttribute(modifiedNormal, 3)\n\n  return {\n    positionAttribute: positionAttribute,\n    normalAttribute: normalAttribute,\n    morphedPositionAttribute: morphedPositionAttribute,\n    morphedNormalAttribute: morphedNormalAttribute,\n  }\n}\n\n/**\n * Modifies the supplied geometry if it is non-indexed, otherwise creates a new,\n * non-indexed geometry. Returns the geometry with smooth normals everywhere except\n * faces that meet at an angle greater than the crease angle.\n *\n * Backwards compatible with code such as @react-three/drei's `<RoundedBox>`\n * which uses this method to operate on the original geometry.\n *\n * As of this writing, BufferGeometry.toNonIndexed() warns if the geometry is\n * non-indexed and returns `this`, i.e. the same geometry on which it was called:\n * `BufferGeometry is already non-indexed.`\n *\n * @param geometry\n * @param creaseAngle\n */\nexport function toCreasedNormals(geometry: BufferGeometry, creaseAngle = Math.PI / 3 /* 60 degrees */): BufferGeometry {\n  const creaseDot = Math.cos(creaseAngle)\n  const hashMultiplier = (1 + 1e-10) * 1e2\n\n  // reusable vectors\n  const verts = [new Vector3(), new Vector3(), new Vector3()]\n  const tempVec1 = new Vector3()\n  const tempVec2 = new Vector3()\n  const tempNorm = new Vector3()\n  const tempNorm2 = new Vector3()\n\n  // hashes a vector\n  function hashVertex(v: Vector3): string {\n    const x = ~~(v.x * hashMultiplier)\n    const y = ~~(v.y * hashMultiplier)\n    const z = ~~(v.z * hashMultiplier)\n    return `${x},${y},${z}`\n  }\n\n  const resultGeometry = geometry.index ? geometry.toNonIndexed() : geometry\n  const posAttr = resultGeometry.attributes.position\n  const vertexMap: { [key: string]: Vector3[] } = {}\n\n  // find all the normals shared by commonly located vertices\n  for (let i = 0, l = posAttr.count / 3; i < l; i++) {\n    const i3 = 3 * i\n    const a = verts[0].fromBufferAttribute(posAttr, i3 + 0)\n    const b = verts[1].fromBufferAttribute(posAttr, i3 + 1)\n    const c = verts[2].fromBufferAttribute(posAttr, i3 + 2)\n\n    tempVec1.subVectors(c, b)\n    tempVec2.subVectors(a, b)\n\n    // add the normal to the map for all vertices\n    const normal = new Vector3().crossVectors(tempVec1, tempVec2).normalize()\n    for (let n = 0; n < 3; n++) {\n      const vert = verts[n]\n      const hash = hashVertex(vert)\n      if (!(hash in vertexMap)) {\n        vertexMap[hash] = []\n      }\n\n      vertexMap[hash].push(normal)\n    }\n  }\n\n  // average normals from all vertices that share a common location if they are within the\n  // provided crease threshold\n  const normalArray = new Float32Array(posAttr.count * 3)\n  const normAttr = new BufferAttribute(normalArray, 3, false)\n  for (let i = 0, l = posAttr.count / 3; i < l; i++) {\n    // get the face normal for this vertex\n    const i3 = 3 * i\n    const a = verts[0].fromBufferAttribute(posAttr, i3 + 0)\n    const b = verts[1].fromBufferAttribute(posAttr, i3 + 1)\n    const c = verts[2].fromBufferAttribute(posAttr, i3 + 2)\n\n    tempVec1.subVectors(c, b)\n    tempVec2.subVectors(a, b)\n\n    tempNorm.crossVectors(tempVec1, tempVec2).normalize()\n\n    // average all normals that meet the threshold and set the normal value\n    for (let n = 0; n < 3; n++) {\n      const vert = verts[n]\n      const hash = hashVertex(vert)\n      const otherNormals = vertexMap[hash]\n      tempNorm2.set(0, 0, 0)\n\n      for (let k = 0, lk = otherNormals.length; k < lk; k++) {\n        const otherNorm = otherNormals[k]\n        if (tempNorm.dot(otherNorm) > creaseDot) {\n          tempNorm2.add(otherNorm)\n        }\n      }\n\n      tempNorm2.normalize()\n      normAttr.setXYZ(i3 + n, tempNorm2.x, tempNorm2.y, tempNorm2.z)\n    }\n  }\n\n  resultGeometry.setAttribute('normal', normAttr)\n  return resultGeometry\n}\n"], "names": ["buffer", "object", "material", "morphTargetsRelative", "a", "b", "c", "i", "il"], "mappings": ";;;;;;;;;;;;;;AAyBa,MAAA,wBAAwB,CAAC,YAA8B,cAA+C;IACjH,MAAM,YAAY,UAAA,CAAW,CAAC,CAAA,CAAE,KAAA,KAAU;IAEpC,MAAA,iBAAiB,IAAI,IAAI,OAAO,IAAA,CAAK,UAAA,CAAW,CAAC,CAAA,CAAE,UAAU,CAAC;IAC9D,MAAA,sBAAsB,IAAI,IAAI,OAAO,IAAA,CAAK,UAAA,CAAW,CAAC,CAAA,CAAE,eAAe,CAAC;IAE9E,MAAM,aAAqF,CAAA;IAC3F,MAAM,kBAA4F,CAAA;IAE5F,MAAA,uBAAuB,UAAA,CAAW,CAAC,CAAA,CAAE,oBAAA;IAErC,MAAA,iBAAiB,2MAAI,iBAAA;IAE3B,IAAI,SAAS;IAEF,WAAA,OAAA,CAAQ,CAAC,MAAM,MAAM;QAC9B,IAAI,kBAAkB;QAIlB,IAAA,cAAA,CAAe,KAAK,KAAA,KAAU,IAAA,GAAO;YAC/B,QAAA,KAAA,CACN,uFACE,IACA;YAEG,OAAA;QACT;QAIS,IAAA,IAAA,QAAQ,KAAK,UAAA,CAAY;YAChC,IAAI,CAAC,eAAe,GAAA,CAAI,IAAI,GAAG;gBACrB,QAAA,KAAA,CACN,uFACE,IACA,kEACA,OACA;gBAEG,OAAA;YACT;YAEI,IAAA,UAAA,CAAW,IAAI,CAAA,KAAM,KAAA,GAAW;gBACvB,UAAA,CAAA,IAAI,CAAA,GAAI,EAAA;YACrB;YAEA,UAAA,CAAW,IAAI,CAAA,CAAE,IAAA,CAAK,KAAK,UAAA,CAAW,IAAI,CAAC;YAE3C;QACF;QAII,IAAA,oBAAoB,eAAe,IAAA,EAAM;YACnC,QAAA,KAAA,CACN,uFACE,IACA;YAEG,OAAA;QACT;QAII,IAAA,yBAAyB,KAAK,oBAAA,EAAsB;YAC9C,QAAA,KAAA,CACN,uFACE,IACA;YAEG,OAAA;QACT;QAES,IAAA,IAAA,QAAQ,KAAK,eAAA,CAAiB;YACrC,IAAI,CAAC,oBAAoB,GAAA,CAAI,IAAI,GAAG;gBAC1B,QAAA,KAAA,CACN,uFACE,IACA;gBAEG,OAAA;YACT;YAEI,IAAA,eAAA,CAAgB,IAAI,CAAA,KAAM,KAAA,GAA2B,eAAA,CAAA,IAAI,CAAA,GAAI,EAAA;YAEjE,eAAA,CAAgB,IAAI,CAAA,CAAE,IAAA,CAAK,KAAK,eAAA,CAAgB,IAAI,CAAC;QACvD;QAIA,eAAe,QAAA,CAAS,cAAA,GAAiB,eAAe,QAAA,CAAS,cAAA,IAAkB,EAAA;QACnF,eAAe,QAAA,CAAS,cAAA,CAAe,IAAA,CAAK,KAAK,QAAQ;QAEzD,IAAI,WAAW;YACT,IAAA;YAEJ,IAAI,KAAK,KAAA,EAAO;gBACd,QAAQ,KAAK,KAAA,CAAM,KAAA;YACV,OAAA,IAAA,KAAK,UAAA,CAAW,QAAA,KAAa,KAAA,GAAW;gBACzC,QAAA,KAAK,UAAA,CAAW,QAAA,CAAS,KAAA;YAAA,OAC5B;gBACG,QAAA,KAAA,CACN,uFACE,IACA;gBAEG,OAAA;YACT;YAEe,eAAA,QAAA,CAAS,QAAQ,OAAO,CAAC;YAE9B,UAAA;QACZ;IAAA,CACD;IAID,IAAI,WAAW;QACb,IAAI,cAAc;QAClB,MAAM,cAAwB,CAAA,CAAA;QAEnB,WAAA,OAAA,CAAQ,CAAC,SAAS;YAC3B,MAAM,QAAQ,KAAK,KAAA;YAEnB,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,KAAA,EAAO,EAAE,EAAG;gBACpC,YAAY,IAAA,CAAK,MAAM,IAAA,CAAK,CAAC,IAAI,WAAW;YAC9C;YAEe,eAAA,KAAK,UAAA,CAAW,QAAA,CAAS,KAAA;QAAA,CACzC;QAED,eAAe,QAAA,CAAS,WAAW;IACrC;IAIA,IAAA,IAAS,QAAQ,WAAY;QAC3B,MAAM,kBAAkB,sBAAsB,UAAA,CAAW,IAAI,CAAsB;QAEnF,IAAI,CAAC,iBAAiB;YACZ,QAAA,KAAA,CACN,0FAA0F,OAAO;YAE5F,OAAA;QACT;QAEe,eAAA,YAAA,CAAa,MAAM,eAAe;IACnD;IAIA,IAAA,IAAS,QAAQ,gBAAiB;QAChC,MAAM,kBAAkB,eAAA,CAAgB,IAAI,CAAA,CAAE,CAAC,CAAA,CAAE,MAAA;QAEjD,IAAI,oBAAoB,GAAG;QAEZ,eAAA,eAAA,GAAkB,eAAe,eAAA,IAAmB,CAAA;QACpD,eAAA,eAAA,CAAgB,IAAI,CAAA,GAAI,EAAA;QAEvC,IAAA,IAAS,IAAI,GAAG,IAAI,iBAAiB,EAAE,EAAG;YACxC,MAAM,yBAAyB,CAAA,CAAA;YAEtB,IAAA,IAAA,IAAI,GAAG,IAAI,eAAA,CAAgB,IAAI,CAAA,CAAE,MAAA,EAAQ,EAAE,EAAG;gBACrD,uBAAuB,IAAA,CAAK,eAAA,CAAgB,IAAI,CAAA,CAAE,CAAC,CAAA,CAAE,CAAC,CAAC;YACzD;YAEM,MAAA,uBAAuB,sBAAsB,sBAA2C;YAE9F,IAAI,CAAC,sBAAsB;gBACjB,QAAA,KAAA,CACN,0FACE,OACA;gBAEG,OAAA;YACT;YAEA,eAAe,eAAA,CAAgB,IAAI,CAAA,CAAE,IAAA,CAAK,oBAAoB;QAChE;IACF;IAEO,OAAA;AACT;AAMa,MAAA,wBAAwB,CAAC,eAAsE;IAC1G,IAAI,aAAiD,KAAA;IACrD,IAAI,WAA+B,KAAA;IACnC,IAAI,aAAkC,KAAA;IACtC,IAAI,cAAc;IAEP,WAAA,OAAA,CAAQ,CAAC,SAAS;QAC3B,IAAI,eAAe,KAAA,GAAW;YAC5B,aAAa,KAAK,KAAA,CAAM,WAAA;QAC1B;QACI,IAAA,eAAe,KAAK,KAAA,CAAM,WAAA,EAAa;YACjC,QAAA,KAAA,CACN;YAEK,OAAA;QACT;QAEA,IAAI,aAAa,KAAA,GAAW,WAAW,KAAK,QAAA;QACxC,IAAA,aAAa,KAAK,QAAA,EAAU;YACtB,QAAA,KAAA,CACN;YAEK,OAAA;QACT;QAEA,IAAI,eAAe,KAAA,GAAW,aAAa,KAAK,UAAA;QAC5C,IAAA,eAAe,KAAK,UAAA,EAAY;YAC1B,QAAA,KAAA,CACN;YAEK,OAAA;QACT;QAEA,eAAe,KAAK,KAAA,CAAM,MAAA;IAAA,CAC3B;IAED,IAAI,cAAc,UAAU;QAEpB,MAAA,QAAQ,IAAI,WAAW,WAAW;QACxC,IAAI,SAAS;QAEF,WAAA,OAAA,CAAQ,CAAC,SAAS;YACrB,MAAA,GAAA,CAAI,KAAK,KAAA,EAAO,MAAM;YAC5B,UAAU,KAAK,KAAA,CAAM,MAAA;QAAA,CACtB;QAED,OAAO,IAAI,yNAAA,CAAgB,OAAO,UAAU,UAAU;IACxD;AACF;AAMa,MAAA,uBAAuB,CAAC,eAAuE;IAG1G,IAAI,aAAiD,KAAA;IACrD,IAAI,cAAc;IAClB,IAAI,SAAS;IAGJ,IAAA,IAAA,IAAI,GAAG,IAAI,WAAW,MAAA,EAAQ,IAAI,GAAG,EAAE,EAAG;QAC3C,MAAA,YAAY,UAAA,CAAW,CAAC,CAAA;QAE9B,IAAI,eAAe,KAAA,GAAW,aAAa,UAAU,KAAA,CAAM,WAAA;QACvD,IAAA,eAAe,UAAU,KAAA,CAAM,WAAA,EAAa;YAC9C,QAAQ,KAAA,CAAM,2DAA2D;YAClE,OAAA;QACT;QAEA,eAAe,UAAU,KAAA,CAAM,MAAA;QAC/B,UAAU,UAAU,QAAA;IACtB;IAIA,MAAM,oBAAoB,IAAI,2NAAA,CAAkB,IAAI,WAAW,WAAW,GAAG,MAAM;IACnF,IAAI,SAAS;IACb,MAAM,MAAM,CAAA,CAAA;IACZ,MAAM,UAAU;QAAC;QAAQ;QAAQ;QAAQ,MAAM;KAAA;IAC/C,MAAM,UAAU;QAAC;QAAQ;QAAQ;QAAQ,MAAM;KAAA;IAE/C,IAAA,IAAS,IAAI,GAAG,IAAI,WAAW,MAAA,EAAQ,IAAI,GAAG,IAAK;QAC3C,MAAA,YAAY,UAAA,CAAW,CAAC,CAAA;QAC9B,MAAM,WAAW,UAAU,QAAA;QAC3B,MAAM,QAAQ,UAAU,KAAA;QACxB,MAAM,MAAM,2MAAI,6BAAA,CAA2B,mBAAmB,UAAU,QAAQ,UAAU,UAAU;QACpG,IAAI,IAAA,CAAK,GAAG;QAEF,UAAA;QAIV,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,IAAA,IAAS,IAAI,GAAG,IAAI,UAAU,IAAK;gBACjC,MAAM,MAAM,4PAAA,EAAW,KAAK,OAAA,CAAQ,CAAC,CAAqC;gBAK1E,MAAM,qPAAM,aAAA,EAAW,WAAW,OAAA,CAAQ,CAAC,CAA0B;gBAKjE,IAAA,GAAG,IAAI,CAAC,CAAC;YACf;QACF;IACF;IAEO,OAAA;AACT;AAMO,SAAS,kBAAkB,QAAA,EAAkC;IAIlE,IAAI,MAAM;IACD,IAAA,IAAA,QAAQ,SAAS,UAAA,CAAY;QAC9B,MAAA,OAAO,SAAS,YAAA,CAAa,IAAI;QACvC,OAAO,KAAK,KAAA,GAAQ,KAAK,QAAA,GAAY,KAAK,KAAA,CAAqB,iBAAA;IACjE;IAEM,MAAA,UAAU,SAAS,QAAA;IACzB,OAAO,UAAU,QAAQ,KAAA,GAAQ,QAAQ,QAAA,GAAY,QAAQ,KAAA,CAAqB,iBAAA,GAAoB;IAC/F,OAAA;AACT;AAOgB,SAAA,cAAc,QAAA,EAA0B,YAAY,IAAA,EAAsB;IACxF,YAAY,KAAK,GAAA,CAAI,WAAW,OAAO,OAAO;IAI9C,MAAM,cAEF,CAAA;IACE,MAAA,UAAU,SAAS,QAAA;IACnB,MAAA,YAAY,SAAS,YAAA,CAAa,UAAU;IAClD,MAAM,cAAc,UAAU,QAAQ,KAAA,GAAQ,UAAU,KAAA;IAGxD,IAAI,YAAY;IAGhB,MAAM,iBAAiB,OAAO,IAAA,CAAK,SAAS,UAAU;IACtD,MAAM,aAEF,CAAA;IACJ,MAAM,mBAEF,CAAA;IACJ,MAAM,aAAa,CAAA,CAAA;IACnB,MAAM,UAAU;QAAC;QAAQ;QAAQ;QAAQ,MAAM;KAAA;IAG/C,IAAA,IAAS,IAAI,GAAG,IAAI,eAAe,MAAA,EAAQ,IAAI,GAAG,IAAK;QAC/C,MAAA,OAAO,cAAA,CAAe,CAAC,CAAA;QAElB,UAAA,CAAA,IAAI,CAAA,GAAI,EAAA;QAEb,MAAA,YAAY,SAAS,eAAA,CAAgB,IAAI,CAAA;QAC/C,IAAI,WAAW;YACb,gBAAA,CAAiB,IAAI,CAAA,GAAI,IAAI,MAAM,UAAU,MAAM,EAAE,IAAA,CAAK,CAAC,EAAE,GAAA,CAAI,IAAM,CAAE,CAAA;QAC3E;IACF;IAGA,MAAM,eAAe,KAAK,KAAA,CAAM,IAAI,SAAS;IAC7C,MAAM,kBAAkB,KAAK,GAAA,CAAI,IAAI,YAAY;IACjD,IAAA,IAAS,IAAI,GAAG,IAAI,aAAa,IAAK;QACpC,MAAM,QAAQ,UAAU,QAAQ,IAAA,CAAK,CAAC,IAAI;QAG1C,IAAI,OAAO;QACX,IAAA,IAAS,IAAI,GAAG,IAAI,eAAe,MAAA,EAAQ,IAAI,GAAG,IAAK;YAC/C,MAAA,OAAO,cAAA,CAAe,CAAC,CAAA;YACvB,MAAA,YAAY,SAAS,YAAA,CAAa,IAAI;YAC5C,MAAM,WAAW,UAAU,QAAA;YAE3B,IAAA,IAAS,IAAI,GAAG,IAAI,UAAU,IAAK;gBAGzB,QAAA,GAAG,CAAC,CAAA,CAAE,SAAA,CAAU,OAAA,CAAQ,CAAC,CAAC,CAAA,CAAE,KAAK,IAAI,eAAA,EAAA,CAAA,CAAA;YAC/C;QACF;QAIA,IAAI,QAAQ,aAAa;YACZ,WAAA,IAAA,CAAK,WAAA,CAAY,IAAI,CAAC;QAAA,OAC5B;YAEL,IAAA,IAAS,IAAI,GAAG,IAAI,eAAe,MAAA,EAAQ,IAAI,GAAG,IAAK;gBAC/C,MAAA,OAAO,cAAA,CAAe,CAAC,CAAA;gBACvB,MAAA,YAAY,SAAS,YAAA,CAAa,IAAI;gBACtC,MAAA,YAAY,SAAS,eAAA,CAAgB,IAAI,CAAA;gBAC/C,MAAM,WAAW,UAAU,QAAA;gBACrB,MAAA,WAAW,UAAA,CAAW,IAAI,CAAA;gBAC1B,MAAA,iBAAiB,gBAAA,CAAiB,IAAI,CAAA;gBAE5C,IAAA,IAAS,IAAI,GAAG,IAAI,UAAU,IAAK;oBAC3B,MAAA,aAAa,OAAA,CAAQ,CAAC,CAAA;oBAE5B,SAAS,IAAA,CAAK,SAAA,CAAU,UAAU,CAAA,CAAE,KAAK,CAAC;oBAE1C,IAAI,WAAW;wBACb,IAAA,IAAS,IAAI,GAAG,KAAK,UAAU,MAAA,EAAQ,IAAI,IAAI,IAAK;4BAEnC,cAAA,CAAA,CAAC,CAAA,CAAE,IAAA,CAAK,SAAA,CAAU,CAAC,CAAA,CAAE,UAAU,CAAA,CAAE,KAAK,CAAC;wBACxD;oBACF;gBACF;YACF;YAEA,WAAA,CAAY,IAAI,CAAA,GAAI;YACpB,WAAW,IAAA,CAAK,SAAS;YACzB;QACF;IACF;IAIM,MAAA,SAAS,SAAS,KAAA;IACxB,IAAA,IAAS,IAAI,GAAG,IAAI,eAAe,MAAA,EAAQ,IAAI,GAAG,IAAK;QAC/C,MAAA,OAAO,cAAA,CAAe,CAAC,CAAA;QACvB,MAAA,eAAe,SAAS,YAAA,CAAa,IAAI;QAE/C,MAAM,SAAS,IAAK,aAAa,KAAA,CAAqB,WAAA,CAAY,UAAA,CAAW,IAAI,CAAC;QAClF,MAAM,YAAY,2MAAI,kBAAA,CAAgB,QAAQ,aAAa,QAAA,EAAU,aAAa,UAAU;QAErF,OAAA,YAAA,CAAa,MAAM,SAAS;QAGnC,IAAI,QAAQ,kBAAkB;YAC5B,IAAA,IAAS,IAAI,GAAG,IAAI,gBAAA,CAAiB,IAAI,CAAA,CAAE,MAAA,EAAQ,IAAK;gBACtD,MAAM,oBAAoB,SAAS,eAAA,CAAgB,IAAI,CAAA,CAAE,CAAC,CAAA;gBAEpDA,MAAAA,UAAS,IAAK,kBAAkB,KAAA,CAAqB,WAAA,CAAY,gBAAA,CAAiB,IAAI,CAAA,CAAE,CAAC,CAAC;gBAChG,MAAM,iBAAiB,2MAAI,kBAAA,CAAgBA,SAAQ,kBAAkB,QAAA,EAAU,kBAAkB,UAAU;gBAC3G,OAAO,eAAA,CAAgB,IAAI,CAAA,CAAE,CAAC,CAAA,GAAI;YACpC;QACF;IACF;IAIA,OAAO,QAAA,CAAS,UAAU;IAEnB,OAAA;AACT;AAOgB,SAAA,oBAAoB,QAAA,EAA0B,QAAA,EAAkC;IAC9F,IAAI,oNAAa,oBAAA,EAAmB;QAClC,QAAQ,IAAA,CAAK,yFAAyF;QAC/F,OAAA;IACT;IAEI,IAAA,oNAAa,sBAAA,IAAuB,oNAAa,wBAAA,EAAuB;QACtE,IAAA,QAAQ,SAAS,QAAA;QAIrB,IAAI,UAAU,MAAM;YAClB,MAAM,UAAU,CAAA,CAAA;YAEV,MAAA,WAAW,SAAS,YAAA,CAAa,UAAU;YAEjD,IAAI,aAAa,KAAA,GAAW;gBAC1B,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,KAAA,EAAO,IAAK;oBACvC,QAAQ,IAAA,CAAK,CAAC;gBAChB;gBAEA,SAAS,QAAA,CAAS,OAAO;gBACzB,QAAQ,SAAS,QAAA;YAAS,OACrB;gBACG,QAAA,KAAA,CACN;gBAEK,OAAA;YACT;QACF;QAIM,MAAA,oBAAqB,MAA0B,KAAA,GAAQ;QAC7D,MAAM,aAAa,CAAA,CAAA;QAEnB,IAAI,OAAO;YACT,IAAI,oNAAa,sBAAA,EAAqB;gBAGpC,IAAA,IAAS,IAAI,GAAG,KAAK,mBAAmB,IAAK;oBAC3C,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,CAAC,CAAC;oBAC7B,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,CAAC,CAAC;oBAC7B,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,IAAI,CAAC,CAAC;gBACnC;YAAA,OACK;gBAGL,IAAA,IAAS,IAAI,GAAG,IAAI,mBAAmB,IAAK;oBACtC,IAAA,IAAI,MAAM,GAAG;wBACf,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,CAAC,CAAC;wBAC7B,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,IAAI,CAAC,CAAC;wBACjC,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,IAAI,CAAC,CAAC;oBAAA,OAC5B;wBACL,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,IAAI,CAAC,CAAC;wBACjC,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,IAAI,CAAC,CAAC;wBACjC,WAAW,IAAA,CAAK,MAAM,IAAA,CAAK,CAAC,CAAC;oBAC/B;gBACF;YACF;QACF;QAEI,IAAA,WAAW,MAAA,GAAS,MAAM,mBAAmB;YAC/C,QAAQ,KAAA,CAAM,kGAAkG;QAClH;QAIM,MAAA,cAAc,SAAS,KAAA;QAC7B,YAAY,QAAA,CAAS,UAAU;QAC/B,YAAY,WAAA,CAAY;QAEjB,OAAA;IAAA,OACF;QACG,QAAA,KAAA,CAAM,uEAAuE,QAAQ;QACtF,OAAA;IACT;AACF;AAeO,SAAS,yBAAyB,MAAA,EAA+D;IAClG,IAAA,OAAO,QAAA,CAAS,gBAAA,KAAqB,MAAM;QAC7C,QAAQ,KAAA,CAAM,oEAAoE;QAC3E,OAAA;IACT;IAEM,MAAA,MAAM,2MAAI,UAAA;IACV,MAAA,MAAM,2MAAI,UAAA;IACV,MAAA,MAAM,2MAAI,UAAA;IAEV,MAAA,SAAS,2MAAI,UAAA;IACb,MAAA,SAAS,2MAAI,UAAA;IACb,MAAA,SAAS,2MAAI,UAAA;IAEb,MAAA,UAAU,2MAAI,UAAA;IACd,MAAA,UAAU,2MAAI,UAAA;IACd,MAAA,UAAU,2MAAI,UAAA;IAEX,SAAA,+BACPC,OAAAA,EACAC,SAAAA,EACA,SAAA,EACA,cAAA,EACAC,qBAAAA,EACAC,EAAAA,EACAC,EAAAA,EACAC,EAAAA,EACA,sBAAA,EACM;QACF,IAAA,mBAAA,CAAoB,WAAWF,EAAC;QAChC,IAAA,mBAAA,CAAoB,WAAWC,EAAC;QAChC,IAAA,mBAAA,CAAoB,WAAWC,EAAC;QAEpC,MAAM,kBAAkBL,QAAO,qBAAA;QAE/B,IAAA,aAAA;QAEEC,UAAS,YAAA,IACT,kBACA,iBACA;YACQ,QAAA,GAAA,CAAI,GAAG,GAAG,CAAC;YACX,QAAA,GAAA,CAAI,GAAG,GAAG,CAAC;YACX,QAAA,GAAA,CAAI,GAAG,GAAG,CAAC;YAEnB,IAAA,IAASK,KAAI,GAAGC,MAAK,eAAe,MAAA,EAAQD,KAAIC,KAAID,KAAK;gBACjD,MAAA,YAAY,eAAA,CAAgBA,EAAC,CAAA;gBAC7B,MAAA,QAAQ,cAAA,CAAeA,EAAC,CAAA;gBAE9B,IAAI,cAAc,GAAG;gBAEd,OAAA,mBAAA,CAAoB,OAAOH,EAAC;gBAC5B,OAAA,mBAAA,CAAoB,OAAOC,EAAC;gBAC5B,OAAA,mBAAA,CAAoB,OAAOC,EAAC;gBAEnC,IAAIH,uBAAsB;oBAChB,QAAA,eAAA,CAAgB,QAAQ,SAAS;oBACjC,QAAA,eAAA,CAAgB,QAAQ,SAAS;oBACjC,QAAA,eAAA,CAAgB,QAAQ,SAAS;gBAAA,OACpC;oBACL,QAAQ,eAAA,CAAgB,OAAO,GAAA,CAAI,GAAG,GAAG,SAAS;oBAClD,QAAQ,eAAA,CAAgB,OAAO,GAAA,CAAI,GAAG,GAAG,SAAS;oBAClD,QAAQ,eAAA,CAAgB,OAAO,GAAA,CAAI,GAAG,GAAG,SAAS;gBACpD;YACF;YAEA,IAAI,GAAA,CAAI,OAAO;YACf,IAAI,GAAA,CAAI,OAAO;YACf,IAAI,GAAA,CAAI,OAAO;QACjB;QAEA,IAAKF,QAAuB,aAAA,EAAe;YAEzCA,QAAO,aAAA,CAAcG,IAAG,GAAG;YAE3BH,QAAO,aAAA,CAAcI,IAAG,GAAG;YAE3BJ,QAAO,aAAA,CAAcK,IAAG,GAAG;QAC7B;QAEA,sBAAA,CAAuBF,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBC,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBC,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;QACxC,sBAAA,CAAuBA,KAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAA;IAC1C;IAEA,MAAM,WAAW,OAAO,QAAA;IACxB,MAAM,WAAW,OAAO,QAAA;IAExB,IAAI,GAAG,GAAG;IACV,MAAM,QAAQ,SAAS,KAAA;IACjB,MAAA,oBAAoB,SAAS,UAAA,CAAW,QAAA;IACxC,MAAA,gBAAgB,SAAS,eAAA,CAAgB,QAAA;IAC/C,MAAM,uBAAuB,SAAS,oBAAA;IAChC,MAAA,kBAAkB,SAAS,UAAA,CAAW,MAAA;IACtC,MAAA,cAAc,SAAS,eAAA,CAAgB,QAAA;IAE7C,MAAM,SAAS,SAAS,MAAA;IACxB,MAAM,YAAY,SAAS,SAAA;IACvB,IAAA,GAAG,GAAG,IAAI;IACd,IAAI,OAAO;IACX,IAAI,OAAO;IAEX,MAAM,mBAAmB,IAAI,aAAa,kBAAkB,KAAA,GAAQ,kBAAkB,QAAQ;IAC9F,MAAM,iBAAiB,IAAI,aAAa,gBAAgB,KAAA,GAAQ,gBAAgB,QAAQ;IAExF,IAAI,UAAU,MAAM;QAGd,IAAA,MAAM,OAAA,CAAQ,QAAQ,GAAG;YAC3B,IAAK,IAAI,GAAG,KAAK,OAAO,MAAA,EAAQ,IAAI,IAAI,IAAK;gBAC3C,QAAQ,MAAA,CAAO,CAAC,CAAA;gBACA,gBAAA,QAAA,CAAS,MAAM,aAAuB,CAAA;gBAEtD,QAAQ,KAAK,GAAA,CAAI,MAAM,KAAA,EAAO,UAAU,KAAK;gBACvC,MAAA,KAAK,GAAA,CAAI,MAAM,KAAA,GAAQ,MAAM,KAAA,EAAO,UAAU,KAAA,GAAQ,UAAU,KAAK;gBAE3E,IAAK,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,EAAG;oBACpC,IAAA,MAAM,IAAA,CAAK,CAAC;oBACZ,IAAA,MAAM,IAAA,CAAK,IAAI,CAAC;oBAChB,IAAA,MAAM,IAAA,CAAK,IAAI,CAAC;oBAEpB,+BACE,QACA,eACA,mBACA,eACA,sBACA,GACA,GACA,GACA;oBAGF,+BACE,QACA,eACA,iBACA,aACA,sBACA,GACA,GACA,GACA;gBAEJ;YACF;QAAA,OACK;YACL,QAAQ,KAAK,GAAA,CAAI,GAAG,UAAU,KAAK;YACnC,MAAM,KAAK,GAAA,CAAI,MAAM,KAAA,EAAO,UAAU,KAAA,GAAQ,UAAU,KAAK;YAE7D,IAAK,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,EAAG;gBACpC,IAAA,MAAM,IAAA,CAAK,CAAC;gBACZ,IAAA,MAAM,IAAA,CAAK,IAAI,CAAC;gBAChB,IAAA,MAAM,IAAA,CAAK,IAAI,CAAC;gBAEpB,+BACE,QACA,UACA,mBACA,eACA,sBACA,GACA,GACA,GACA;gBAGF,+BACE,QACA,UACA,iBACA,aACA,sBACA,GACA,GACA,GACA;YAEJ;QACF;IAAA,OAAA,IACS,sBAAsB,KAAA,GAAW;QAGtC,IAAA,MAAM,OAAA,CAAQ,QAAQ,GAAG;YAC3B,IAAK,IAAI,GAAG,KAAK,OAAO,MAAA,EAAQ,IAAI,IAAI,IAAK;gBAC3C,QAAQ,MAAA,CAAO,CAAC,CAAA;gBACA,gBAAA,QAAA,CAAS,MAAM,aAAuB,CAAA;gBAEtD,QAAQ,KAAK,GAAA,CAAI,MAAM,KAAA,EAAO,UAAU,KAAK;gBACvC,MAAA,KAAK,GAAA,CAAI,MAAM,KAAA,GAAQ,MAAM,KAAA,EAAO,UAAU,KAAA,GAAQ,UAAU,KAAK;gBAE3E,IAAK,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,EAAG;oBACpC,IAAA;oBACJ,IAAI,IAAI;oBACR,IAAI,IAAI;oBAER,+BACE,QACA,eACA,mBACA,eACA,sBACA,GACA,GACA,GACA;oBAGF,+BACE,QACA,eACA,iBACA,aACA,sBACA,GACA,GACA,GACA;gBAEJ;YACF;QAAA,OACK;YACL,QAAQ,KAAK,GAAA,CAAI,GAAG,UAAU,KAAK;YACnC,MAAM,KAAK,GAAA,CAAI,kBAAkB,KAAA,EAAO,UAAU,KAAA,GAAQ,UAAU,KAAK;YAEzE,IAAK,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,EAAG;gBACpC,IAAA;gBACJ,IAAI,IAAI;gBACR,IAAI,IAAI;gBAER,+BACE,QACA,UACA,mBACA,eACA,sBACA,GACA,GACA,GACA;gBAGF,+BACE,QACA,UACA,iBACA,aACA,sBACA,GACA,GACA,GACA;YAEJ;QACF;IACF;IAEA,MAAM,2BAA2B,2MAAI,yBAAA,CAAuB,kBAAkB,CAAC;IAC/E,MAAM,yBAAyB,IAAI,gOAAA,CAAuB,gBAAgB,CAAC;IAEpE,OAAA;QACL;QACA;QACA;QACA;IAAA;AAEJ;AAiBO,SAAS,iBAAiB,QAAA,EAA0B,cAAc,KAAK,EAAA,GAAK,CAAA,EAAoC;IAC/G,MAAA,YAAY,KAAK,GAAA,CAAI,WAAW;IAChC,MAAA,iBAAA,CAAkB,IAAI,KAAA,IAAS;IAG/B,MAAA,QAAQ;QAAC,IAAI,iNAAA,CAAQ;QAAG,2MAAI,UAAA;QAAW,2MAAI,UAAA,CAAA,CAAS;KAAA;IACpD,MAAA,WAAW,2MAAI,UAAA;IACf,MAAA,WAAW,2MAAI,UAAA;IACf,MAAA,WAAW,IAAI,iNAAA;IACf,MAAA,YAAY,2MAAI,UAAA;IAGtB,SAAS,WAAW,CAAA,EAAoB;QACtC,MAAM,IAAI,CAAC,CAAA,CAAE,EAAE,CAAA,GAAI,cAAA;QACnB,MAAM,IAAI,CAAC,CAAA,CAAE,EAAE,CAAA,GAAI,cAAA;QACnB,MAAM,IAAI,CAAC,CAAA,CAAE,EAAE,CAAA,GAAI,cAAA;QACZ,OAAA,GAAG,EAAA,CAAA,EAAK,EAAA,CAAA,EAAK,GAAA;IACtB;IAEA,MAAM,iBAAiB,SAAS,KAAA,GAAQ,SAAS,YAAA,CAAiB,IAAA;IAC5D,MAAA,UAAU,eAAe,UAAA,CAAW,QAAA;IAC1C,MAAM,YAA0C,CAAA;IAGvC,IAAA,IAAA,IAAI,GAAG,IAAI,QAAQ,KAAA,GAAQ,GAAG,IAAI,GAAG,IAAK;QACjD,MAAM,KAAK,IAAI;QACf,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QACtD,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QACtD,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QAE7C,SAAA,UAAA,CAAW,GAAG,CAAC;QACf,SAAA,UAAA,CAAW,GAAG,CAAC;QAGlB,MAAA,SAAS,2MAAI,UAAA,GAAU,YAAA,CAAa,UAAU,QAAQ,EAAE,SAAA;QAC9D,IAAA,IAAS,IAAI,GAAG,IAAI,GAAG,IAAK;YACpB,MAAA,OAAO,KAAA,CAAM,CAAC,CAAA;YACd,MAAA,OAAO,WAAW,IAAI;YACxB,IAAA,CAAA,CAAE,QAAQ,SAAA,GAAY;gBACd,SAAA,CAAA,IAAI,CAAA,GAAI,EAAA;YACpB;YAEU,SAAA,CAAA,IAAI,CAAA,CAAE,IAAA,CAAK,MAAM;QAC7B;IACF;IAIA,MAAM,cAAc,IAAI,aAAa,QAAQ,KAAA,GAAQ,CAAC;IACtD,MAAM,WAAW,IAAI,yNAAA,CAAgB,aAAa,GAAG,KAAK;IACjD,IAAA,IAAA,IAAI,GAAG,IAAI,QAAQ,KAAA,GAAQ,GAAG,IAAI,GAAG,IAAK;QAEjD,MAAM,KAAK,IAAI;QACf,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QACtD,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QACtD,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,mBAAA,CAAoB,SAAS,KAAK,CAAC;QAE7C,SAAA,UAAA,CAAW,GAAG,CAAC;QACf,SAAA,UAAA,CAAW,GAAG,CAAC;QAExB,SAAS,YAAA,CAAa,UAAU,QAAQ,EAAE,SAAA,CAAU;QAGpD,IAAA,IAAS,IAAI,GAAG,IAAI,GAAG,IAAK;YACpB,MAAA,OAAO,KAAA,CAAM,CAAC,CAAA;YACd,MAAA,OAAO,WAAW,IAAI;YACtB,MAAA,eAAe,SAAA,CAAU,IAAI,CAAA;YACzB,UAAA,GAAA,CAAI,GAAG,GAAG,CAAC;YAErB,IAAA,IAAS,IAAI,GAAG,KAAK,aAAa,MAAA,EAAQ,IAAI,IAAI,IAAK;gBAC/C,MAAA,YAAY,YAAA,CAAa,CAAC,CAAA;gBAChC,IAAI,SAAS,GAAA,CAAI,SAAS,IAAI,WAAW;oBACvC,UAAU,GAAA,CAAI,SAAS;gBACzB;YACF;YAEA,UAAU,SAAA,CAAU;YACX,SAAA,MAAA,CAAO,KAAK,GAAG,UAAU,CAAA,EAAG,UAAU,CAAA,EAAG,UAAU,CAAC;QAC/D;IACF;IAEe,eAAA,YAAA,CAAa,UAAU,QAAQ;IACvC,OAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2153, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2159, "column": 0}, "map": {"version": 3, "file": "SkeletonUtils.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/utils/SkeletonUtils.js"], "sourcesContent": ["import {\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  QuaternionKeyframeTrack,\n  SkeletonHelper,\n  Vector3,\n  VectorKeyframeTrack,\n} from 'three'\n\nfunction retarget(target, source, options = {}) {\n  const pos = new Vector3(),\n    quat = new Quaternion(),\n    scale = new Vector3(),\n    bindBoneMatrix = new Matrix4(),\n    relativeMatrix = new Matrix4(),\n    globalMatrix = new Matrix4()\n\n  options.preserveMatrix = options.preserveMatrix !== undefined ? options.preserveMatrix : true\n  options.preservePosition = options.preservePosition !== undefined ? options.preservePosition : true\n  options.preserveHipPosition = options.preserveHipPosition !== undefined ? options.preserveHipPosition : false\n  options.useTargetMatrix = options.useTargetMatrix !== undefined ? options.useTargetMatrix : false\n  options.hip = options.hip !== undefined ? options.hip : 'hip'\n  options.names = options.names || {}\n\n  const sourceBones = source.isObject3D ? source.skeleton.bones : getBones(source),\n    bones = target.isObject3D ? target.skeleton.bones : getBones(target)\n\n  let bindBones, bone, name, boneTo, bonesPosition\n\n  // reset bones\n\n  if (target.isObject3D) {\n    target.skeleton.pose()\n  } else {\n    options.useTargetMatrix = true\n    options.preserveMatrix = false\n  }\n\n  if (options.preservePosition) {\n    bonesPosition = []\n\n    for (let i = 0; i < bones.length; i++) {\n      bonesPosition.push(bones[i].position.clone())\n    }\n  }\n\n  if (options.preserveMatrix) {\n    // reset matrix\n\n    target.updateMatrixWorld()\n\n    target.matrixWorld.identity()\n\n    // reset children matrix\n\n    for (let i = 0; i < target.children.length; ++i) {\n      target.children[i].updateMatrixWorld(true)\n    }\n  }\n\n  if (options.offsets) {\n    bindBones = []\n\n    for (let i = 0; i < bones.length; ++i) {\n      bone = bones[i]\n      name = options.names[bone.name] || bone.name\n\n      if (options.offsets[name]) {\n        bone.matrix.multiply(options.offsets[name])\n\n        bone.matrix.decompose(bone.position, bone.quaternion, bone.scale)\n\n        bone.updateMatrixWorld()\n      }\n\n      bindBones.push(bone.matrixWorld.clone())\n    }\n  }\n\n  for (let i = 0; i < bones.length; ++i) {\n    bone = bones[i]\n    name = options.names[bone.name] || bone.name\n\n    boneTo = getBoneByName(name, sourceBones)\n\n    globalMatrix.copy(bone.matrixWorld)\n\n    if (boneTo) {\n      boneTo.updateMatrixWorld()\n\n      if (options.useTargetMatrix) {\n        relativeMatrix.copy(boneTo.matrixWorld)\n      } else {\n        relativeMatrix.copy(target.matrixWorld).invert()\n        relativeMatrix.multiply(boneTo.matrixWorld)\n      }\n\n      // ignore scale to extract rotation\n\n      scale.setFromMatrixScale(relativeMatrix)\n      relativeMatrix.scale(scale.set(1 / scale.x, 1 / scale.y, 1 / scale.z))\n\n      // apply to global matrix\n\n      globalMatrix.makeRotationFromQuaternion(quat.setFromRotationMatrix(relativeMatrix))\n\n      if (target.isObject3D) {\n        const boneIndex = bones.indexOf(bone),\n          wBindMatrix = bindBones\n            ? bindBones[boneIndex]\n            : bindBoneMatrix.copy(target.skeleton.boneInverses[boneIndex]).invert()\n\n        globalMatrix.multiply(wBindMatrix)\n      }\n\n      globalMatrix.copyPosition(relativeMatrix)\n    }\n\n    if (bone.parent && bone.parent.isBone) {\n      bone.matrix.copy(bone.parent.matrixWorld).invert()\n      bone.matrix.multiply(globalMatrix)\n    } else {\n      bone.matrix.copy(globalMatrix)\n    }\n\n    if (options.preserveHipPosition && name === options.hip) {\n      bone.matrix.setPosition(pos.set(0, bone.position.y, 0))\n    }\n\n    bone.matrix.decompose(bone.position, bone.quaternion, bone.scale)\n\n    bone.updateMatrixWorld()\n  }\n\n  if (options.preservePosition) {\n    for (let i = 0; i < bones.length; ++i) {\n      bone = bones[i]\n      name = options.names[bone.name] || bone.name\n\n      if (name !== options.hip) {\n        bone.position.copy(bonesPosition[i])\n      }\n    }\n  }\n\n  if (options.preserveMatrix) {\n    // restore matrix\n\n    target.updateMatrixWorld(true)\n  }\n}\n\nfunction retargetClip(target, source, clip, options = {}) {\n  options.useFirstFramePosition = options.useFirstFramePosition !== undefined ? options.useFirstFramePosition : false\n  options.fps = options.fps !== undefined ? options.fps : 30\n  options.names = options.names || []\n\n  if (!source.isObject3D) {\n    source = getHelperFromSkeleton(source)\n  }\n\n  const numFrames = Math.round(clip.duration * (options.fps / 1000) * 1000),\n    delta = 1 / options.fps,\n    convertedTracks = [],\n    mixer = new AnimationMixer(source),\n    bones = getBones(target.skeleton),\n    boneDatas = []\n  let positionOffset, bone, boneTo, boneData, name\n\n  mixer.clipAction(clip).play()\n  mixer.update(0)\n\n  source.updateMatrixWorld()\n\n  for (let i = 0; i < numFrames; ++i) {\n    const time = i * delta\n\n    retarget(target, source, options)\n\n    for (let j = 0; j < bones.length; ++j) {\n      name = options.names[bones[j].name] || bones[j].name\n\n      boneTo = getBoneByName(name, source.skeleton)\n\n      if (boneTo) {\n        bone = bones[j]\n        boneData = boneDatas[j] = boneDatas[j] || { bone: bone }\n\n        if (options.hip === name) {\n          if (!boneData.pos) {\n            boneData.pos = {\n              times: new Float32Array(numFrames),\n              values: new Float32Array(numFrames * 3),\n            }\n          }\n\n          if (options.useFirstFramePosition) {\n            if (i === 0) {\n              positionOffset = bone.position.clone()\n            }\n\n            bone.position.sub(positionOffset)\n          }\n\n          boneData.pos.times[i] = time\n\n          bone.position.toArray(boneData.pos.values, i * 3)\n        }\n\n        if (!boneData.quat) {\n          boneData.quat = {\n            times: new Float32Array(numFrames),\n            values: new Float32Array(numFrames * 4),\n          }\n        }\n\n        boneData.quat.times[i] = time\n\n        bone.quaternion.toArray(boneData.quat.values, i * 4)\n      }\n    }\n\n    mixer.update(delta)\n\n    source.updateMatrixWorld()\n  }\n\n  for (let i = 0; i < boneDatas.length; ++i) {\n    boneData = boneDatas[i]\n\n    if (boneData) {\n      if (boneData.pos) {\n        convertedTracks.push(\n          new VectorKeyframeTrack(\n            '.bones[' + boneData.bone.name + '].position',\n            boneData.pos.times,\n            boneData.pos.values,\n          ),\n        )\n      }\n\n      convertedTracks.push(\n        new QuaternionKeyframeTrack(\n          '.bones[' + boneData.bone.name + '].quaternion',\n          boneData.quat.times,\n          boneData.quat.values,\n        ),\n      )\n    }\n  }\n\n  mixer.uncacheAction(clip)\n\n  return new AnimationClip(clip.name, -1, convertedTracks)\n}\n\nfunction clone(source) {\n  const sourceLookup = new Map()\n  const cloneLookup = new Map()\n\n  const clone = source.clone()\n\n  parallelTraverse(source, clone, function (sourceNode, clonedNode) {\n    sourceLookup.set(clonedNode, sourceNode)\n    cloneLookup.set(sourceNode, clonedNode)\n  })\n\n  clone.traverse(function (node) {\n    if (!node.isSkinnedMesh) return\n\n    const clonedMesh = node\n    const sourceMesh = sourceLookup.get(node)\n    const sourceBones = sourceMesh.skeleton.bones\n\n    clonedMesh.skeleton = sourceMesh.skeleton.clone()\n    clonedMesh.bindMatrix.copy(sourceMesh.bindMatrix)\n\n    clonedMesh.skeleton.bones = sourceBones.map(function (bone) {\n      return cloneLookup.get(bone)\n    })\n\n    clonedMesh.bind(clonedMesh.skeleton, clonedMesh.bindMatrix)\n  })\n\n  return clone\n}\n\n// internal helper\n\nfunction getBoneByName(name, skeleton) {\n  for (let i = 0, bones = getBones(skeleton); i < bones.length; i++) {\n    if (name === bones[i].name) return bones[i]\n  }\n}\n\nfunction getBones(skeleton) {\n  return Array.isArray(skeleton) ? skeleton : skeleton.bones\n}\n\nfunction getHelperFromSkeleton(skeleton) {\n  const source = new SkeletonHelper(skeleton.bones[0])\n  source.skeleton = skeleton\n\n  return source\n}\n\nfunction parallelTraverse(a, b, callback) {\n  callback(a, b)\n\n  for (let i = 0; i < a.children.length; i++) {\n    parallelTraverse(a.children[i], b.children[i], callback)\n  }\n}\n\nexport const SkeletonUtils = { retarget, retargetClip, clone }\n"], "names": ["clone"], "mappings": ";;;;;AAWA,SAAS,SAAS,MAAA,EAAQ,MAAA,EAAQ,UAAU,CAAA,CAAA,EAAI;IAC9C,MAAM,MAAM,2MAAI,UAAA,CAAS,GACvB,OAAO,2MAAI,aAAA,CAAY,GACvB,QAAQ,2MAAI,UAAA,CAAS,GACrB,iBAAiB,2MAAI,UAAA,CAAS,GAC9B,iBAAiB,2MAAI,UAAA,CAAS,GAC9B,eAAe,2MAAI,UAAA,CAAS;IAE9B,QAAQ,cAAA,GAAiB,QAAQ,cAAA,KAAmB,KAAA,IAAY,QAAQ,cAAA,GAAiB;IACzF,QAAQ,gBAAA,GAAmB,QAAQ,gBAAA,KAAqB,KAAA,IAAY,QAAQ,gBAAA,GAAmB;IAC/F,QAAQ,mBAAA,GAAsB,QAAQ,mBAAA,KAAwB,KAAA,IAAY,QAAQ,mBAAA,GAAsB;IACxG,QAAQ,eAAA,GAAkB,QAAQ,eAAA,KAAoB,KAAA,IAAY,QAAQ,eAAA,GAAkB;IAC5F,QAAQ,GAAA,GAAM,QAAQ,GAAA,KAAQ,KAAA,IAAY,QAAQ,GAAA,GAAM;IACxD,QAAQ,KAAA,GAAQ,QAAQ,KAAA,IAAS,CAAE;IAEnC,MAAM,cAAc,OAAO,UAAA,GAAa,OAAO,QAAA,CAAS,KAAA,GAAQ,SAAS,MAAM,GAC7E,QAAQ,OAAO,UAAA,GAAa,OAAO,QAAA,CAAS,KAAA,GAAQ,SAAS,MAAM;IAErE,IAAI,WAAW,MAAM,MAAM,QAAQ;IAInC,IAAI,OAAO,UAAA,EAAY;QACrB,OAAO,QAAA,CAAS,IAAA,CAAM;IAC1B,OAAS;QACL,QAAQ,eAAA,GAAkB;QAC1B,QAAQ,cAAA,GAAiB;IAC1B;IAED,IAAI,QAAQ,gBAAA,EAAkB;QAC5B,gBAAgB,CAAE,CAAA;QAElB,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAK;YACrC,cAAc,IAAA,CAAK,KAAA,CAAM,CAAC,CAAA,CAAE,QAAA,CAAS,KAAA,EAAO;QAC7C;IACF;IAED,IAAI,QAAQ,cAAA,EAAgB;QAG1B,OAAO,iBAAA,CAAmB;QAE1B,OAAO,WAAA,CAAY,QAAA,CAAU;QAI7B,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,QAAA,CAAS,MAAA,EAAQ,EAAE,EAAG;YAC/C,OAAO,QAAA,CAAS,CAAC,CAAA,CAAE,iBAAA,CAAkB,IAAI;QAC1C;IACF;IAED,IAAI,QAAQ,OAAA,EAAS;QACnB,YAAY,CAAE,CAAA;QAEd,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,EAAE,EAAG;YACrC,OAAO,KAAA,CAAM,CAAC,CAAA;YACd,OAAO,QAAQ,KAAA,CAAM,KAAK,IAAI,CAAA,IAAK,KAAK,IAAA;YAExC,IAAI,QAAQ,OAAA,CAAQ,IAAI,CAAA,EAAG;gBACzB,KAAK,MAAA,CAAO,QAAA,CAAS,QAAQ,OAAA,CAAQ,IAAI,CAAC;gBAE1C,KAAK,MAAA,CAAO,SAAA,CAAU,KAAK,QAAA,EAAU,KAAK,UAAA,EAAY,KAAK,KAAK;gBAEhE,KAAK,iBAAA,CAAmB;YACzB;YAED,UAAU,IAAA,CAAK,KAAK,WAAA,CAAY,KAAA,CAAK,CAAE;QACxC;IACF;IAED,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,EAAE,EAAG;QACrC,OAAO,KAAA,CAAM,CAAC,CAAA;QACd,OAAO,QAAQ,KAAA,CAAM,KAAK,IAAI,CAAA,IAAK,KAAK,IAAA;QAExC,SAAS,cAAc,MAAM,WAAW;QAExC,aAAa,IAAA,CAAK,KAAK,WAAW;QAElC,IAAI,QAAQ;YACV,OAAO,iBAAA,CAAmB;YAE1B,IAAI,QAAQ,eAAA,EAAiB;gBAC3B,eAAe,IAAA,CAAK,OAAO,WAAW;YAC9C,OAAa;gBACL,eAAe,IAAA,CAAK,OAAO,WAAW,EAAE,MAAA,CAAQ;gBAChD,eAAe,QAAA,CAAS,OAAO,WAAW;YAC3C;YAID,MAAM,kBAAA,CAAmB,cAAc;YACvC,eAAe,KAAA,CAAM,MAAM,GAAA,CAAI,IAAI,MAAM,CAAA,EAAG,IAAI,MAAM,CAAA,EAAG,IAAI,MAAM,CAAC,CAAC;YAIrE,aAAa,0BAAA,CAA2B,KAAK,qBAAA,CAAsB,cAAc,CAAC;YAElF,IAAI,OAAO,UAAA,EAAY;gBACrB,MAAM,YAAY,MAAM,OAAA,CAAQ,IAAI,GAClC,cAAc,YACV,SAAA,CAAU,SAAS,CAAA,GACnB,eAAe,IAAA,CAAK,OAAO,QAAA,CAAS,YAAA,CAAa,SAAS,CAAC,EAAE,MAAA,CAAQ;gBAE3E,aAAa,QAAA,CAAS,WAAW;YAClC;YAED,aAAa,YAAA,CAAa,cAAc;QACzC;QAED,IAAI,KAAK,MAAA,IAAU,KAAK,MAAA,CAAO,MAAA,EAAQ;YACrC,KAAK,MAAA,CAAO,IAAA,CAAK,KAAK,MAAA,CAAO,WAAW,EAAE,MAAA,CAAQ;YAClD,KAAK,MAAA,CAAO,QAAA,CAAS,YAAY;QACvC,OAAW;YACL,KAAK,MAAA,CAAO,IAAA,CAAK,YAAY;QAC9B;QAED,IAAI,QAAQ,mBAAA,IAAuB,SAAS,QAAQ,GAAA,EAAK;YACvD,KAAK,MAAA,CAAO,WAAA,CAAY,IAAI,GAAA,CAAI,GAAG,KAAK,QAAA,CAAS,CAAA,EAAG,CAAC,CAAC;QACvD;QAED,KAAK,MAAA,CAAO,SAAA,CAAU,KAAK,QAAA,EAAU,KAAK,UAAA,EAAY,KAAK,KAAK;QAEhE,KAAK,iBAAA,CAAmB;IACzB;IAED,IAAI,QAAQ,gBAAA,EAAkB;QAC5B,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,EAAE,EAAG;YACrC,OAAO,KAAA,CAAM,CAAC,CAAA;YACd,OAAO,QAAQ,KAAA,CAAM,KAAK,IAAI,CAAA,IAAK,KAAK,IAAA;YAExC,IAAI,SAAS,QAAQ,GAAA,EAAK;gBACxB,KAAK,QAAA,CAAS,IAAA,CAAK,aAAA,CAAc,CAAC,CAAC;YACpC;QACF;IACF;IAED,IAAI,QAAQ,cAAA,EAAgB;QAG1B,OAAO,iBAAA,CAAkB,IAAI;IAC9B;AACH;AAEA,SAAS,aAAa,MAAA,EAAQ,MAAA,EAAQ,IAAA,EAAM,UAAU,CAAA,CAAA,EAAI;IACxD,QAAQ,qBAAA,GAAwB,QAAQ,qBAAA,KAA0B,KAAA,IAAY,QAAQ,qBAAA,GAAwB;IAC9G,QAAQ,GAAA,GAAM,QAAQ,GAAA,KAAQ,KAAA,IAAY,QAAQ,GAAA,GAAM;IACxD,QAAQ,KAAA,GAAQ,QAAQ,KAAA,IAAS,CAAE,CAAA;IAEnC,IAAI,CAAC,OAAO,UAAA,EAAY;QACtB,SAAS,sBAAsB,MAAM;IACtC;IAED,MAAM,YAAY,KAAK,KAAA,CAAM,KAAK,QAAA,GAAA,CAAY,QAAQ,GAAA,GAAM,GAAA,IAAQ,GAAI,GACtE,QAAQ,IAAI,QAAQ,GAAA,EACpB,kBAAkB,CAAE,CAAA,EACpB,QAAQ,2MAAI,iBAAA,CAAe,MAAM,GACjC,QAAQ,SAAS,OAAO,QAAQ,GAChC,YAAY,CAAE,CAAA;IAChB,IAAI,gBAAgB,MAAM,QAAQ,UAAU;IAE5C,MAAM,UAAA,CAAW,IAAI,EAAE,IAAA,CAAM;IAC7B,MAAM,MAAA,CAAO,CAAC;IAEd,OAAO,iBAAA,CAAmB;IAE1B,IAAA,IAAS,IAAI,GAAG,IAAI,WAAW,EAAE,EAAG;QAClC,MAAM,OAAO,IAAI;QAEjB,SAAS,QAAQ,QAAQ,OAAO;QAEhC,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,EAAE,EAAG;YACrC,OAAO,QAAQ,KAAA,CAAM,KAAA,CAAM,CAAC,CAAA,CAAE,IAAI,CAAA,IAAK,KAAA,CAAM,CAAC,CAAA,CAAE,IAAA;YAEhD,SAAS,cAAc,MAAM,OAAO,QAAQ;YAE5C,IAAI,QAAQ;gBACV,OAAO,KAAA,CAAM,CAAC,CAAA;gBACd,WAAW,SAAA,CAAU,CAAC,CAAA,GAAI,SAAA,CAAU,CAAC,CAAA,IAAK;oBAAE;gBAAY;gBAExD,IAAI,QAAQ,GAAA,KAAQ,MAAM;oBACxB,IAAI,CAAC,SAAS,GAAA,EAAK;wBACjB,SAAS,GAAA,GAAM;4BACb,OAAO,IAAI,aAAa,SAAS;4BACjC,QAAQ,IAAI,aAAa,YAAY,CAAC;wBACvC;oBACF;oBAED,IAAI,QAAQ,qBAAA,EAAuB;wBACjC,IAAI,MAAM,GAAG;4BACX,iBAAiB,KAAK,QAAA,CAAS,KAAA,CAAO;wBACvC;wBAED,KAAK,QAAA,CAAS,GAAA,CAAI,cAAc;oBACjC;oBAED,SAAS,GAAA,CAAI,KAAA,CAAM,CAAC,CAAA,GAAI;oBAExB,KAAK,QAAA,CAAS,OAAA,CAAQ,SAAS,GAAA,CAAI,MAAA,EAAQ,IAAI,CAAC;gBACjD;gBAED,IAAI,CAAC,SAAS,IAAA,EAAM;oBAClB,SAAS,IAAA,GAAO;wBACd,OAAO,IAAI,aAAa,SAAS;wBACjC,QAAQ,IAAI,aAAa,YAAY,CAAC;oBACvC;gBACF;gBAED,SAAS,IAAA,CAAK,KAAA,CAAM,CAAC,CAAA,GAAI;gBAEzB,KAAK,UAAA,CAAW,OAAA,CAAQ,SAAS,IAAA,CAAK,MAAA,EAAQ,IAAI,CAAC;YACpD;QACF;QAED,MAAM,MAAA,CAAO,KAAK;QAElB,OAAO,iBAAA,CAAmB;IAC3B;IAED,IAAA,IAAS,IAAI,GAAG,IAAI,UAAU,MAAA,EAAQ,EAAE,EAAG;QACzC,WAAW,SAAA,CAAU,CAAC,CAAA;QAEtB,IAAI,UAAU;YACZ,IAAI,SAAS,GAAA,EAAK;gBAChB,gBAAgB,IAAA,CACd,2MAAI,sBAAA,CACF,YAAY,SAAS,IAAA,CAAK,IAAA,GAAO,cACjC,SAAS,GAAA,CAAI,KAAA,EACb,SAAS,GAAA,CAAI,MAAA;YAGlB;YAED,gBAAgB,IAAA,CACd,2MAAI,0BAAA,CACF,YAAY,SAAS,IAAA,CAAK,IAAA,GAAO,gBACjC,SAAS,IAAA,CAAK,KAAA,EACd,SAAS,IAAA,CAAK,MAAA;QAGnB;IACF;IAED,MAAM,aAAA,CAAc,IAAI;IAExB,OAAO,2MAAI,gBAAA,CAAc,KAAK,IAAA,EAAM,CAAA,GAAI,eAAe;AACzD;AAEA,SAAS,MAAM,MAAA,EAAQ;IACrB,MAAM,eAAe,aAAA,GAAA,IAAI,IAAK;IAC9B,MAAM,cAAc,aAAA,GAAA,IAAI,IAAK;IAE7B,MAAMA,SAAQ,OAAO,KAAA,CAAO;IAE5B,iBAAiB,QAAQA,QAAO,SAAU,UAAA,EAAY,UAAA,EAAY;QAChE,aAAa,GAAA,CAAI,YAAY,UAAU;QACvC,YAAY,GAAA,CAAI,YAAY,UAAU;IAC1C,CAAG;IAEDA,OAAM,QAAA,CAAS,SAAU,IAAA,EAAM;QAC7B,IAAI,CAAC,KAAK,aAAA,EAAe;QAEzB,MAAM,aAAa;QACnB,MAAM,aAAa,aAAa,GAAA,CAAI,IAAI;QACxC,MAAM,cAAc,WAAW,QAAA,CAAS,KAAA;QAExC,WAAW,QAAA,GAAW,WAAW,QAAA,CAAS,KAAA,CAAO;QACjD,WAAW,UAAA,CAAW,IAAA,CAAK,WAAW,UAAU;QAEhD,WAAW,QAAA,CAAS,KAAA,GAAQ,YAAY,GAAA,CAAI,SAAU,IAAA,EAAM;YAC1D,OAAO,YAAY,GAAA,CAAI,IAAI;QACjC,CAAK;QAED,WAAW,IAAA,CAAK,WAAW,QAAA,EAAU,WAAW,UAAU;IAC9D,CAAG;IAED,OAAOA;AACT;AAIA,SAAS,cAAc,IAAA,EAAM,QAAA,EAAU;IACrC,IAAA,IAAS,IAAI,GAAG,QAAQ,SAAS,QAAQ,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAK;QACjE,IAAI,SAAS,KAAA,CAAM,CAAC,CAAA,CAAE,IAAA,EAAM,OAAO,KAAA,CAAM,CAAC,CAAA;IAC3C;AACH;AAEA,SAAS,SAAS,QAAA,EAAU;IAC1B,OAAO,MAAM,OAAA,CAAQ,QAAQ,IAAI,WAAW,SAAS,KAAA;AACvD;AAEA,SAAS,sBAAsB,QAAA,EAAU;IACvC,MAAM,SAAS,2MAAI,iBAAA,CAAe,SAAS,KAAA,CAAM,CAAC,CAAC;IACnD,OAAO,QAAA,GAAW;IAElB,OAAO;AACT;AAEA,SAAS,iBAAiB,CAAA,EAAG,CAAA,EAAG,QAAA,EAAU;IACxC,SAAS,GAAG,CAAC;IAEb,IAAA,IAAS,IAAI,GAAG,IAAI,EAAE,QAAA,CAAS,MAAA,EAAQ,IAAK;QAC1C,iBAAiB,EAAE,QAAA,CAAS,CAAC,CAAA,EAAG,EAAE,QAAA,CAAS,CAAC,CAAA,EAAG,QAAQ;IACxD;AACH;AAEY,MAAC,gBAAgB;IAAE;IAAU;IAAc;AAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2365, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2371, "column": 0}, "map": {"version": 3, "file": "WorkerPool.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/utils/WorkerPool.js"], "sourcesContent": ["/**\n * <AUTHOR> / https://github.com/deepkolos\n */\n\nexport class WorkerPool {\n  constructor(pool = 4) {\n    this.pool = pool\n    this.queue = []\n    this.workers = []\n    this.workersResolve = []\n    this.workerStatus = 0\n  }\n\n  _initWorker(workerId) {\n    if (!this.workers[workerId]) {\n      const worker = this.workerCreator()\n      worker.addEventListener('message', this._onMessage.bind(this, workerId))\n      this.workers[workerId] = worker\n    }\n  }\n\n  _getIdleWorker() {\n    for (let i = 0; i < this.pool; i++) if (!(this.workerStatus & (1 << i))) return i\n\n    return -1\n  }\n\n  _onMessage(workerId, msg) {\n    const resolve = this.workersResolve[workerId]\n    resolve && resolve(msg)\n\n    if (this.queue.length) {\n      const { resolve, msg, transfer } = this.queue.shift()\n      this.workersResolve[workerId] = resolve\n      this.workers[workerId].postMessage(msg, transfer)\n    } else {\n      this.workerStatus ^= 1 << workerId\n    }\n  }\n\n  setWorkerCreator(workerCreator) {\n    this.workerCreator = workerCreator\n  }\n\n  setWorkerLimit(pool) {\n    this.pool = pool\n  }\n\n  postMessage(msg, transfer) {\n    return new Promise((resolve) => {\n      const workerId = this._getIdleWorker()\n\n      if (workerId !== -1) {\n        this._initWorker(workerId)\n        this.workerStatus |= 1 << workerId\n        this.workersResolve[workerId] = resolve\n        this.workers[workerId].postMessage(msg, transfer)\n      } else {\n        this.queue.push({ resolve, msg, transfer })\n      }\n    })\n  }\n\n  dispose() {\n    this.workers.forEach((worker) => worker.terminate())\n    this.workersResolve.length = 0\n    this.workers.length = 0\n    this.queue.length = 0\n    this.workerStatus = 0\n  }\n}\n"], "names": ["resolve", "msg"], "mappings": ";;;AAIO,MAAM,WAAW;IACtB,YAAY,OAAO,CAAA,CAAG;QACpB,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,KAAA,GAAQ,CAAE,CAAA;QACf,IAAA,CAAK,OAAA,GAAU,CAAE,CAAA;QACjB,IAAA,CAAK,cAAA,GAAiB,CAAE,CAAA;QACxB,IAAA,CAAK,YAAA,GAAe;IACrB;IAED,YAAY,QAAA,EAAU;QACpB,IAAI,CAAC,IAAA,CAAK,OAAA,CAAQ,QAAQ,CAAA,EAAG;YAC3B,MAAM,SAAS,IAAA,CAAK,aAAA,CAAe;YACnC,OAAO,gBAAA,CAAiB,WAAW,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,IAAA,EAAM,QAAQ,CAAC;YACvE,IAAA,CAAK,OAAA,CAAQ,QAAQ,CAAA,GAAI;QAC1B;IACF;IAED,iBAAiB;QACf,IAAA,IAAS,IAAI,GAAG,IAAI,IAAA,CAAK,IAAA,EAAM,IAAK,IAAI,CAAA,CAAE,IAAA,CAAK,YAAA,GAAgB,KAAK,CAAA,GAAK,OAAO;QAEhF,OAAO,CAAA;IACR;IAED,WAAW,QAAA,EAAU,GAAA,EAAK;QACxB,MAAM,UAAU,IAAA,CAAK,cAAA,CAAe,QAAQ,CAAA;QAC5C,WAAW,QAAQ,GAAG;QAEtB,IAAI,IAAA,CAAK,KAAA,CAAM,MAAA,EAAQ;YACrB,MAAM,EAAE,SAAAA,QAAAA,EAAS,KAAAC,IAAAA,EAAK,QAAA,CAAU,CAAA,GAAG,IAAA,CAAK,KAAA,CAAM,KAAA,CAAO;YACrD,IAAA,CAAK,cAAA,CAAe,QAAQ,CAAA,GAAID;YAChC,IAAA,CAAK,OAAA,CAAQ,QAAQ,CAAA,CAAE,WAAA,CAAYC,MAAK,QAAQ;QACtD,OAAW;YACL,IAAA,CAAK,YAAA,IAAgB,KAAK;QAC3B;IACF;IAED,iBAAiB,aAAA,EAAe;QAC9B,IAAA,CAAK,aAAA,GAAgB;IACtB;IAED,eAAe,IAAA,EAAM;QACnB,IAAA,CAAK,IAAA,GAAO;IACb;IAED,YAAY,GAAA,EAAK,QAAA,EAAU;QACzB,OAAO,IAAI,QAAQ,CAAC,YAAY;YAC9B,MAAM,WAAW,IAAA,CAAK,cAAA,CAAgB;YAEtC,IAAI,aAAa,CAAA,GAAI;gBACnB,IAAA,CAAK,WAAA,CAAY,QAAQ;gBACzB,IAAA,CAAK,YAAA,IAAgB,KAAK;gBAC1B,IAAA,CAAK,cAAA,CAAe,QAAQ,CAAA,GAAI;gBAChC,IAAA,CAAK,OAAA,CAAQ,QAAQ,CAAA,CAAE,WAAA,CAAY,KAAK,QAAQ;YACxD,OAAa;gBACL,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK;oBAAE;oBAAS;oBAAK;gBAAA,CAAU;YAC3C;QACP,CAAK;IACF;IAED,UAAU;QACR,IAAA,CAAK,OAAA,CAAQ,OAAA,CAAQ,CAAC,SAAW,OAAO,SAAA,EAAW;QACnD,IAAA,CAAK,cAAA,CAAe,MAAA,GAAS;QAC7B,IAAA,CAAK,OAAA,CAAQ,MAAA,GAAS;QACtB,IAAA,CAAK,KAAA,CAAM,MAAA,GAAS;QACpB,IAAA,CAAK,YAAA,GAAe;IACrB;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2437, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2443, "column": 0}, "map": {"version": 3, "file": "CopyShader.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/shaders/CopyShader.ts"], "sourcesContent": ["/**\n * Full-screen textured quad shader\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type CopyShaderUniforms = {\n  opacity: IUniform<number>\n  tDiffuse: IUniform<Texture | null>\n}\n\nexport interface ICopyShader extends IShader<CopyShaderUniforms> {}\n\nexport const CopyShader: ICopyShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    opacity: { value: 1.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float opacity;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 texel = texture2D( tDiffuse, vUv );\n    \tgl_FragColor = opacity * texel;\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";;;AAcO,MAAM,aAA0B;IACrC,UAAU;QACR,UAAU;YAAE,OAAO;QAAK;QACxB,SAAS;YAAE,OAAO;QAAI;IACxB;IAEA,cAAA,QAAA,GAAyB,CAAA;;;;;;;;;EAAA,CAAA;IAWzB,gBAAA,QAAA,GAA2B,CAAA;;;;;;;;;;;;;EAAA,CAAA;AAc7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2482, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2488, "column": 0}, "map": {"version": 3, "file": "GammaCorrectionShader.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/shaders/GammaCorrectionShader.ts"], "sourcesContent": ["/**\n * Gamma Correction Shader\n * http://en.wikipedia.org/wiki/gamma_correction\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type GammaCorrectionShaderUniforms = {\n  tDiffuse: IUniform<Texture | null>\n}\n\nexport interface IGammaCorrectionShader extends IShader<GammaCorrectionShaderUniforms> {}\n\nexport const GammaCorrectionShader: IGammaCorrectionShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 tex = texture2D( tDiffuse, vUv );\n\n    \t#ifdef LinearTosRGB\n    \t\tgl_FragColor = LinearTosRGB( tex );\n    \t#else\n    \t\tgl_FragColor = sRGBTransferOETF( tex );\n    \t#endif\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";;;AAcO,MAAM,wBAAgD;IAC3D,UAAU;QACR,UAAU;YAAE,OAAO;QAAK;IAC1B;IAEA,cAAA,QAAA,GAAyB,CAAA;;;;;;;;;EAAA,CAAA;IAWzB,gBAAA,QAAA,GAA2B,CAAA;;;;;;;;;;;;;;;;EAAA,CAAA;AAiB7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2527, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2533, "column": 0}, "map": {"version": 3, "file": "HorizontalBlurShader.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/shaders/HorizontalBlurShader.ts"], "sourcesContent": ["/**\n * Two pass Gaussian blur filter (horizontal and vertical blur shaders)\n * - described in http://www.gamerendering.com/2008/10/11/gaussian-blur-filter-shader/\n *   and used in http://www.cake23.de/traveling-wavefronts-lit-up.html\n *\n * - 9 samples per pass\n * - standard deviation 2.7\n * - \"h\" and \"v\" parameters should be set to \"1 / width\" and \"1 / height\"\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type HorizontalBlurShaderUniforms = {\n  tDiffuse: IUniform<Texture | null>\n  h: IUniform<number>\n}\n\nexport interface IHorizontalBlurShader extends IShader<HorizontalBlurShaderUniforms> {}\n\nexport const HorizontalBlurShader: IHorizontalBlurShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    h: { value: 1.0 / 512.0 },\n  },\n  vertexShader: /* glsl */ `\n      varying vec2 vUv;\n\n      void main() {\n\n        vUv = uv;\n        gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n      }\n  `,\n  fragmentShader: /* glsl */ `\n    uniform sampler2D tDiffuse;\n    uniform float h;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 sum = vec4( 0.0 );\n\n    \tsum += texture2D( tDiffuse, vec2( vUv.x - 4.0 * h, vUv.y ) ) * 0.051;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x - 3.0 * h, vUv.y ) ) * 0.0918;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x - 2.0 * h, vUv.y ) ) * 0.12245;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x - 1.0 * h, vUv.y ) ) * 0.1531;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x, vUv.y ) ) * 0.1633;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x + 1.0 * h, vUv.y ) ) * 0.1531;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x + 2.0 * h, vUv.y ) ) * 0.12245;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x + 3.0 * h, vUv.y ) ) * 0.0918;\n    \tsum += texture2D( tDiffuse, vec2( vUv.x + 4.0 * h, vUv.y ) ) * 0.051;\n\n    \tgl_FragColor = sum;\n\n    }\n  `,\n}\n"], "names": [], "mappings": ";;;AAoBO,MAAM,uBAA8C;IACzD,UAAU;QACR,UAAU;YAAE,OAAO;QAAK;QACxB,GAAG;YAAE,OAAO,IAAM;QAAM;IAC1B;IACA,cAAA,QAAA,GAAyB,CAAA;;;;;;;;;EAAA,CAAA;IAUzB,gBAAA,QAAA,GAA2B,CAAA;;;;;;;;;;;;;;;;;;;;;;;EAAA,CAAA;AAwB7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2582, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2588, "column": 0}, "map": {"version": 3, "file": "VerticalBlurShader.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/shaders/VerticalBlurShader.ts"], "sourcesContent": ["/**\n * Two pass Gaussian blur filter (horizontal and vertical blur shaders)\n * - described in http://www.gamerendering.com/2008/10/11/gaussian-blur-filter-shader/\n *   and used in http://www.cake23.de/traveling-wavefronts-lit-up.html\n *\n * - 9 samples per pass\n * - standard deviation 2.7\n * - \"h\" and \"v\" parameters should be set to \"1 / width\" and \"1 / height\"\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type VerticalBlurShaderUniforms = {\n  tDiffuse: IUniform<Texture | null>\n  v: IUniform<number>\n}\n\nexport interface IVerticalBlurShader extends IShader<VerticalBlurShaderUniforms> {}\n\nexport const VerticalBlurShader: IVerticalBlurShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    v: { value: 1.0 / 512.0 },\n  },\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n      vUv = uv;\n      gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n  fragmentShader: /* glsl */ `\n\n  uniform sampler2D tDiffuse;\n  uniform float v;\n\n  varying vec2 vUv;\n\n  void main() {\n\n    vec4 sum = vec4( 0.0 );\n\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y - 4.0 * v ) ) * 0.051;\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y - 3.0 * v ) ) * 0.0918;\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y - 2.0 * v ) ) * 0.12245;\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y - 1.0 * v ) ) * 0.1531;\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y ) ) * 0.1633;\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y + 1.0 * v ) ) * 0.1531;\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y + 2.0 * v ) ) * 0.12245;\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y + 3.0 * v ) ) * 0.0918;\n    sum += texture2D( tDiffuse, vec2( vUv.x, vUv.y + 4.0 * v ) ) * 0.051;\n\n    gl_FragColor = sum;\n\n  }\n  `,\n}\n"], "names": [], "mappings": ";;;AAoBO,MAAM,qBAA0C;IACrD,UAAU;QACR,UAAU;YAAE,OAAO;QAAK;QACxB,GAAG;YAAE,OAAO,IAAM;QAAM;IAC1B;IACA,cAAA,QAAA,GAAyB,CAAA;;;;;;;;;EAAA,CAAA;IAUzB,gBAAA,QAAA,GAA2B,CAAA;;;;;;;;;;;;;;;;;;;;;;;;EAAA,CAAA;AAyB7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2638, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2644, "column": 0}, "map": {"version": 3, "file": "Pass.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/postprocessing/Pass.ts"], "sourcesContent": ["import { OrthographicCamera, PlaneGeometry, Mesh, Material, WebGLRenderer, WebGLRenderTarget } from 'three'\n\nclass Pass {\n  // if set to true, the pass is processed by the composer\n  public enabled = true\n\n  // if set to true, the pass indicates to swap read and write buffer after rendering\n  public needsSwap = true\n\n  // if set to true, the pass clears its buffer before rendering\n  public clear = false\n\n  // if set to true, the result of the pass is rendered to screen. This is set automatically by EffectComposer.\n  public renderToScreen = false\n\n  public setSize(width: number, height: number): void {}\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget,\n    deltaTime: number,\n    maskActive?: unknown,\n  ): void {\n    console.error('THREE.Pass: .render() must be implemented in derived pass.')\n  }\n\n  public dispose() {}\n}\n\n// Helper for passes that need to fill the viewport with a single quad.\nclass FullScreenQuad<TMaterial extends Material = Material> {\n  public camera = new OrthographicCamera(-1, 1, 1, -1, 0, 1)\n  public geometry = new PlaneGeometry(2, 2)\n  private mesh: Mesh<PlaneGeometry, TMaterial>\n\n  constructor(material: TMaterial) {\n    this.mesh = new Mesh(this.geometry, material)\n  }\n\n  public get material(): TMaterial {\n    return this.mesh.material\n  }\n\n  public set material(value: TMaterial) {\n    this.mesh.material = value\n  }\n\n  public dispose(): void {\n    this.mesh.geometry.dispose()\n  }\n\n  public render(renderer: WebGLRenderer): void {\n    renderer.render(this.mesh, this.camera)\n  }\n}\n\nexport { Pass, FullScreenQuad }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA,MAAM,KAAK;IAAX,aAAA;QAES,wDAAA;QAAA,cAAA,IAAA,EAAA,WAAU;QAGV,mFAAA;QAAA,cAAA,IAAA,EAAA,aAAY;QAGZ,8DAAA;QAAA,cAAA,IAAA,EAAA,SAAQ;QAGR,6GAAA;QAAA,cAAA,IAAA,EAAA,kBAAiB;IAAA;IAEjB,QAAQ,KAAA,EAAe,MAAA,EAAsB,CAAC;IAE9C,OACL,QAAA,EACA,WAAA,EACA,UAAA,EACA,SAAA,EACA,UAAA,EACM;QACN,QAAQ,KAAA,CAAM,4DAA4D;IAC5E;IAEO,UAAU,CAAC;AACpB;AAGA,MAAM,eAAsD;IAK1D,YAAY,QAAA,CAAqB;QAJ1B,cAAA,IAAA,EAAA,UAAS,IAAI,4NAAA,CAAmB,CAAA,GAAI,GAAG,GAAG,CAAA,GAAI,GAAG,CAAC;QAClD,cAAA,IAAA,EAAA,YAAW,2MAAI,gBAAA,CAAc,GAAG,CAAC;QAChC,cAAA,IAAA,EAAA;QAGN,IAAA,CAAK,IAAA,GAAO,2MAAI,OAAA,CAAK,IAAA,CAAK,QAAA,EAAU,QAAQ;IAC9C;IAEA,IAAW,WAAsB;QAC/B,OAAO,IAAA,CAAK,IAAA,CAAK,QAAA;IACnB;IAEA,IAAW,SAAS,KAAA,EAAkB;QACpC,IAAA,CAAK,IAAA,CAAK,QAAA,GAAW;IACvB;IAEO,UAAgB;QAChB,IAAA,CAAA,IAAA,CAAK,QAAA,CAAS,OAAA;IACrB;IAEO,OAAO,QAAA,EAA+B;QAC3C,SAAS,MAAA,CAAO,IAAA,CAAK,IAAA,EAAM,IAAA,CAAK,MAAM;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2700, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2706, "column": 0}, "map": {"version": 3, "file": "ShaderPass.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/postprocessing/ShaderPass.ts"], "sourcesContent": ["import { ShaderMaterial, UniformsUtils, WebG<PERSON>enderer, WebGLRenderTarget } from 'three'\nimport { Pass, FullScreenQuad } from './Pass'\nimport { Defines, IShader, Uniforms } from '../shaders/types'\n\nclass ShaderPass extends Pass {\n  public textureID: string\n  public uniforms: Uniforms\n  public material: ShaderMaterial\n  public fsQuad: FullScreenQuad\n\n  constructor(shader: ShaderMaterial | IShader<Uniforms, Defines | undefined>, textureID = 'tDiffuse') {\n    super()\n\n    this.textureID = textureID\n\n    if (shader instanceof ShaderMaterial) {\n      this.uniforms = shader.uniforms\n\n      this.material = shader\n    } else {\n      this.uniforms = UniformsUtils.clone(shader.uniforms)\n\n      this.material = new ShaderMaterial({\n        defines: Object.assign({}, shader.defines),\n        uniforms: this.uniforms,\n        vertexShader: shader.vertexShader,\n        fragmentShader: shader.fragmentShader,\n      })\n    }\n\n    this.fsQuad = new FullScreenQuad(this.material)\n  }\n\n  public render(\n    renderer: <PERSON><PERSON><PERSON>enderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget /*, deltaTime, maskActive */,\n  ): void {\n    if (this.uniforms[this.textureID]) {\n      this.uniforms[this.textureID].value = readBuffer.texture\n    }\n\n    this.fsQuad.material = this.material\n\n    if (this.renderToScreen) {\n      renderer.setRenderTarget(null)\n      this.fsQuad.render(renderer)\n    } else {\n      renderer.setRenderTarget(writeBuffer)\n      // TODO: Avoid using autoClear properties, see https://github.com/mrdoob/three.js/pull/15571#issuecomment-465669600\n      if (this.clear) renderer.clear(renderer.autoClearColor, renderer.autoClearDepth, renderer.autoClearStencil)\n      this.fsQuad.render(renderer)\n    }\n  }\n\n  public dispose() {\n    this.fsQuad.dispose()\n    this.material.dispose()\n  }\n}\n\nexport { ShaderPass }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAIA,MAAM,oQAAmB,OAAA,CAAK;IAM5B,YAAY,MAAA,EAAiE,YAAY,UAAA,CAAY;QAC7F,KAAA;QAND,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAKL,IAAA,CAAK,SAAA,GAAY;QAEjB,IAAI,kBAAkB,wNAAA,EAAgB;YACpC,IAAA,CAAK,QAAA,GAAW,OAAO,QAAA;YAEvB,IAAA,CAAK,QAAA,GAAW;QAAA,OACX;YACL,IAAA,CAAK,QAAA,0MAAW,gBAAA,CAAc,KAAA,CAAM,OAAO,QAAQ;YAE9C,IAAA,CAAA,QAAA,GAAW,2MAAI,iBAAA,CAAe;gBACjC,SAAS,OAAO,MAAA,CAAO,CAAA,GAAI,OAAO,OAAO;gBACzC,UAAU,IAAA,CAAK,QAAA;gBACf,cAAc,OAAO,YAAA;gBACrB,gBAAgB,OAAO,cAAA;YAAA,CACxB;QACH;QAEA,IAAA,CAAK,MAAA,GAAS,qPAAI,iBAAA,CAAe,IAAA,CAAK,QAAQ;IAChD;IAEO,OACL,QAAA,EACA,WAAA,EACA,UAAA,EACM;QACN,IAAI,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,SAAS,CAAA,EAAG;YACjC,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,SAAS,CAAA,CAAE,KAAA,GAAQ,WAAW,OAAA;QACnD;QAEK,IAAA,CAAA,MAAA,CAAO,QAAA,GAAW,IAAA,CAAK,QAAA;QAE5B,IAAI,IAAA,CAAK,cAAA,EAAgB;YACvB,SAAS,eAAA,CAAgB,IAAI;YACxB,IAAA,CAAA,MAAA,CAAO,MAAA,CAAO,QAAQ;QAAA,OACtB;YACL,SAAS,eAAA,CAAgB,WAAW;YAEpC,IAAI,IAAA,CAAK,KAAA,EAAO,SAAS,KAAA,CAAM,SAAS,cAAA,EAAgB,SAAS,cAAA,EAAgB,SAAS,gBAAgB;YACrG,IAAA,CAAA,MAAA,CAAO,MAAA,CAAO,QAAQ;QAC7B;IACF;IAEO,UAAU;QACf,IAAA,CAAK,MAAA,CAAO,OAAA;QACZ,IAAA,CAAK,QAAA,CAAS,OAAA;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2767, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2773, "column": 0}, "map": {"version": 3, "file": "MaskPass.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/postprocessing/MaskPass.ts"], "sourcesContent": ["import { Camera, Scene, WebGLRenderer, WebGLRenderTarget } from 'three'\nimport { Pass } from './Pass'\n\nclass MaskPass extends Pass {\n  public scene: Scene\n  public camera: Camera\n  public inverse: boolean\n\n  constructor(scene: Scene, camera: Camera) {\n    super()\n\n    this.scene = scene\n    this.camera = camera\n\n    this.clear = true\n    this.needsSwap = false\n\n    this.inverse = false\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget /*, deltaTime, maskActive */,\n  ): void {\n    const context = renderer.getContext()\n    const state = renderer.state\n\n    // don't update color or depth\n\n    state.buffers.color.setMask(false)\n    state.buffers.depth.setMask(false)\n\n    // lock buffers\n\n    state.buffers.color.setLocked(true)\n    state.buffers.depth.setLocked(true)\n\n    // set up stencil\n\n    let writeValue, clearValue\n\n    if (this.inverse) {\n      writeValue = 0\n      clearValue = 1\n    } else {\n      writeValue = 1\n      clearValue = 0\n    }\n\n    state.buffers.stencil.setTest(true)\n    state.buffers.stencil.setOp(context.REPLACE, context.REPLACE, context.REPLACE)\n    state.buffers.stencil.setFunc(context.ALWAYS, writeValue, 0xffffffff)\n    state.buffers.stencil.setClear(clearValue)\n    state.buffers.stencil.setLocked(true)\n\n    // draw into the stencil buffer\n\n    renderer.setRenderTarget(readBuffer)\n    if (this.clear) renderer.clear()\n    renderer.render(this.scene, this.camera)\n\n    renderer.setRenderTarget(writeBuffer)\n    if (this.clear) renderer.clear()\n    renderer.render(this.scene, this.camera)\n\n    // unlock color and depth buffer for subsequent rendering\n\n    state.buffers.color.setLocked(false)\n    state.buffers.depth.setLocked(false)\n\n    // only render where stencil is set to 1\n\n    state.buffers.stencil.setLocked(false)\n    state.buffers.stencil.setFunc(context.EQUAL, 1, 0xffffffff) // draw if == 1\n    state.buffers.stencil.setOp(context.KEEP, context.KEEP, context.KEEP)\n    state.buffers.stencil.setLocked(true)\n  }\n}\n\nclass ClearMaskPass extends Pass {\n  constructor() {\n    super()\n    this.needsSwap = false\n  }\n\n  public render(renderer: WebGLRenderer /*, writeBuffer, readBuffer, deltaTime, maskActive */): void {\n    renderer.state.buffers.stencil.setLocked(false)\n    renderer.state.buffers.stencil.setTest(false)\n  }\n}\n\nexport { MaskPass, ClearMaskPass }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAGA,MAAM,kQAAiB,OAAA,CAAK;IAK1B,YAAY,KAAA,EAAc,MAAA,CAAgB;QAClC,KAAA;QALD,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAKL,IAAA,CAAK,KAAA,GAAQ;QACb,IAAA,CAAK,MAAA,GAAS;QAEd,IAAA,CAAK,KAAA,GAAQ;QACb,IAAA,CAAK,SAAA,GAAY;QAEjB,IAAA,CAAK,OAAA,GAAU;IACjB;IAEO,OACL,QAAA,EACA,WAAA,EACA,UAAA,EACM;QACA,MAAA,UAAU,SAAS,UAAA;QACzB,MAAM,QAAQ,SAAS,KAAA;QAIjB,MAAA,OAAA,CAAQ,KAAA,CAAM,OAAA,CAAQ,KAAK;QAC3B,MAAA,OAAA,CAAQ,KAAA,CAAM,OAAA,CAAQ,KAAK;QAI3B,MAAA,OAAA,CAAQ,KAAA,CAAM,SAAA,CAAU,IAAI;QAC5B,MAAA,OAAA,CAAQ,KAAA,CAAM,SAAA,CAAU,IAAI;QAIlC,IAAI,YAAY;QAEhB,IAAI,IAAA,CAAK,OAAA,EAAS;YACH,aAAA;YACA,aAAA;QAAA,OACR;YACQ,aAAA;YACA,aAAA;QACf;QAEM,MAAA,OAAA,CAAQ,OAAA,CAAQ,OAAA,CAAQ,IAAI;QAC5B,MAAA,OAAA,CAAQ,OAAA,CAAQ,KAAA,CAAM,QAAQ,OAAA,EAAS,QAAQ,OAAA,EAAS,QAAQ,OAAO;QAC7E,MAAM,OAAA,CAAQ,OAAA,CAAQ,OAAA,CAAQ,QAAQ,MAAA,EAAQ,YAAY,UAAU;QAC9D,MAAA,OAAA,CAAQ,OAAA,CAAQ,QAAA,CAAS,UAAU;QACnC,MAAA,OAAA,CAAQ,OAAA,CAAQ,SAAA,CAAU,IAAI;QAIpC,SAAS,eAAA,CAAgB,UAAU;QACnC,IAAI,IAAA,CAAK,KAAA,EAAO,SAAS,KAAA,CAAM;QAC/B,SAAS,MAAA,CAAO,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,MAAM;QAEvC,SAAS,eAAA,CAAgB,WAAW;QACpC,IAAI,IAAA,CAAK,KAAA,EAAO,SAAS,KAAA,CAAM;QAC/B,SAAS,MAAA,CAAO,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,MAAM;QAIjC,MAAA,OAAA,CAAQ,KAAA,CAAM,SAAA,CAAU,KAAK;QAC7B,MAAA,OAAA,CAAQ,KAAA,CAAM,SAAA,CAAU,KAAK;QAI7B,MAAA,OAAA,CAAQ,OAAA,CAAQ,SAAA,CAAU,KAAK;QACrC,MAAM,OAAA,CAAQ,OAAA,CAAQ,OAAA,CAAQ,QAAQ,KAAA,EAAO,GAAG,UAAU;QACpD,MAAA,OAAA,CAAQ,OAAA,CAAQ,KAAA,CAAM,QAAQ,IAAA,EAAM,QAAQ,IAAA,EAAM,QAAQ,IAAI;QAC9D,MAAA,OAAA,CAAQ,OAAA,CAAQ,SAAA,CAAU,IAAI;IACtC;AACF;AAEA,MAAM,uQAAsB,OAAA,CAAK;IAC/B,aAAc;QACN,KAAA;QACN,IAAA,CAAK,SAAA,GAAY;IACnB;IAEO,OAAO,QAAA,EAAqF;QACjG,SAAS,KAAA,CAAM,OAAA,CAAQ,OAAA,CAAQ,SAAA,CAAU,KAAK;QAC9C,SAAS,KAAA,CAAM,OAAA,CAAQ,OAAA,CAAQ,OAAA,CAAQ,KAAK;IAC9C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2848, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2854, "column": 0}, "map": {"version": 3, "file": "EffectComposer.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/postprocessing/EffectComposer.ts"], "sourcesContent": ["import { Clock, LinearFilter, RGBAFormat, NoBlending, Vector2, WebG<PERSON>enderer, WebGLRenderTarget } from 'three'\nimport { CopyShader } from '../shaders/CopyShader'\nimport { ShaderPass } from './ShaderPass'\nimport { MaskPass, ClearMaskPass } from './MaskPass'\nimport { Pass } from './Pass'\n\nclass EffectComposer<TRenderTarget extends WebGLRenderTarget = WebGLRenderTarget> {\n  public renderer: WebGLRenderer\n  private _pixelRatio: number\n  private _width: number\n  private _height: number\n  public renderTarget1: WebGLRenderTarget\n  public renderTarget2: WebGLRenderTarget\n  public writeBuffer: WebGLRenderTarget\n  public readBuffer: WebGLRenderTarget\n  public renderToScreen: boolean\n  public passes: Pass[] = []\n  public copyPass: Pass\n  public clock: Clock\n\n  constructor(renderer: WebGLRenderer, renderTarget?: TRenderTarget) {\n    this.renderer = renderer\n\n    if (renderTarget === undefined) {\n      const parameters = {\n        minFilter: LinearFilter,\n        magFilter: LinearFilter,\n        format: RGBAFormat,\n      }\n\n      const size = renderer.getSize(new Vector2())\n      this._pixelRatio = renderer.getPixelRatio()\n      this._width = size.width\n      this._height = size.height\n\n      renderTarget = new WebGLRenderTarget(\n        this._width * this._pixelRatio,\n        this._height * this._pixelRatio,\n        parameters,\n      ) as TRenderTarget\n      renderTarget.texture.name = 'EffectComposer.rt1'\n    } else {\n      this._pixelRatio = 1\n      this._width = renderTarget.width\n      this._height = renderTarget.height\n    }\n\n    this.renderTarget1 = renderTarget\n    this.renderTarget2 = renderTarget.clone()\n    this.renderTarget2.texture.name = 'EffectComposer.rt2'\n\n    this.writeBuffer = this.renderTarget1\n    this.readBuffer = this.renderTarget2\n\n    this.renderToScreen = true\n\n    // dependencies\n\n    if (CopyShader === undefined) {\n      console.error('THREE.EffectComposer relies on CopyShader')\n    }\n\n    if (ShaderPass === undefined) {\n      console.error('THREE.EffectComposer relies on ShaderPass')\n    }\n\n    this.copyPass = new ShaderPass(CopyShader)\n    // @ts-ignore\n    this.copyPass.material.blending = NoBlending\n\n    this.clock = new Clock()\n  }\n\n  public swapBuffers(): void {\n    const tmp = this.readBuffer\n    this.readBuffer = this.writeBuffer\n    this.writeBuffer = tmp\n  }\n\n  public addPass(pass: Pass): void {\n    this.passes.push(pass)\n    pass.setSize(this._width * this._pixelRatio, this._height * this._pixelRatio)\n  }\n\n  public insertPass(pass: Pass, index: number): void {\n    this.passes.splice(index, 0, pass)\n    pass.setSize(this._width * this._pixelRatio, this._height * this._pixelRatio)\n  }\n\n  public removePass(pass: Pass): void {\n    const index = this.passes.indexOf(pass)\n\n    if (index !== -1) {\n      this.passes.splice(index, 1)\n    }\n  }\n\n  public isLastEnabledPass(passIndex: number): boolean {\n    for (let i = passIndex + 1; i < this.passes.length; i++) {\n      if (this.passes[i].enabled) {\n        return false\n      }\n    }\n\n    return true\n  }\n\n  public render(deltaTime?: number): void {\n    // deltaTime value is in seconds\n\n    if (deltaTime === undefined) {\n      deltaTime = this.clock.getDelta()\n    }\n\n    const currentRenderTarget = this.renderer.getRenderTarget()\n\n    let maskActive = false\n\n    const il = this.passes.length\n\n    for (let i = 0; i < il; i++) {\n      const pass = this.passes[i]\n\n      if (pass.enabled === false) continue\n\n      pass.renderToScreen = this.renderToScreen && this.isLastEnabledPass(i)\n      pass.render(this.renderer, this.writeBuffer, this.readBuffer, deltaTime, maskActive)\n\n      if (pass.needsSwap) {\n        if (maskActive) {\n          const context = this.renderer.getContext()\n          const stencil = this.renderer.state.buffers.stencil\n\n          //context.stencilFunc( context.NOTEQUAL, 1, 0xffffffff );\n          stencil.setFunc(context.NOTEQUAL, 1, 0xffffffff)\n\n          this.copyPass.render(this.renderer, this.writeBuffer, this.readBuffer, deltaTime)\n\n          //context.stencilFunc( context.EQUAL, 1, 0xffffffff );\n          stencil.setFunc(context.EQUAL, 1, 0xffffffff)\n        }\n\n        this.swapBuffers()\n      }\n\n      if (MaskPass !== undefined) {\n        if (pass instanceof MaskPass) {\n          maskActive = true\n        } else if (pass instanceof ClearMaskPass) {\n          maskActive = false\n        }\n      }\n    }\n\n    this.renderer.setRenderTarget(currentRenderTarget)\n  }\n\n  public reset(renderTarget: WebGLRenderTarget): void {\n    if (renderTarget === undefined) {\n      const size = this.renderer.getSize(new Vector2())\n      this._pixelRatio = this.renderer.getPixelRatio()\n      this._width = size.width\n      this._height = size.height\n\n      renderTarget = this.renderTarget1.clone()\n      renderTarget.setSize(this._width * this._pixelRatio, this._height * this._pixelRatio)\n    }\n\n    this.renderTarget1.dispose()\n    this.renderTarget2.dispose()\n    this.renderTarget1 = renderTarget\n    this.renderTarget2 = renderTarget.clone()\n\n    this.writeBuffer = this.renderTarget1\n    this.readBuffer = this.renderTarget2\n  }\n\n  public setSize(width: number, height: number): void {\n    this._width = width\n    this._height = height\n\n    const effectiveWidth = this._width * this._pixelRatio\n    const effectiveHeight = this._height * this._pixelRatio\n\n    this.renderTarget1.setSize(effectiveWidth, effectiveHeight)\n    this.renderTarget2.setSize(effectiveWidth, effectiveHeight)\n\n    for (let i = 0; i < this.passes.length; i++) {\n      this.passes[i].setSize(effectiveWidth, effectiveHeight)\n    }\n  }\n\n  public setPixelRatio(pixelRatio: number): void {\n    this._pixelRatio = pixelRatio\n\n    this.setSize(this._width, this._height)\n  }\n\n  public dispose() {\n    this.renderTarget1.dispose()\n    this.renderTarget2.dispose()\n\n    this.copyPass.dispose()\n  }\n}\n\nexport { EffectComposer }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAMA,MAAM,eAA4E;IAchF,YAAY,QAAA,EAAyB,YAAA,CAA8B;QAb5D,cAAA,IAAA,EAAA;QACC,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACD,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA,UAAiB,CAAA,CAAA;QACjB,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAGL,IAAA,CAAK,QAAA,GAAW;QAEhB,IAAI,iBAAiB,KAAA,GAAW;YAC9B,MAAM,aAAa;gBACjB,kNAAW,eAAA;gBACX,WAAW,sNAAA;gBACX,+MAAQ,aAAA;YAAA;YAGV,MAAM,OAAO,SAAS,OAAA,CAAQ,2MAAI,UAAA,CAAS,CAAA;YACtC,IAAA,CAAA,WAAA,GAAc,SAAS,aAAA;YAC5B,IAAA,CAAK,MAAA,GAAS,KAAK,KAAA;YACnB,IAAA,CAAK,OAAA,GAAU,KAAK,MAAA;YAEpB,eAAe,2MAAI,oBAAA,CACjB,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,WAAA,EACnB,IAAA,CAAK,OAAA,GAAU,IAAA,CAAK,WAAA,EACpB;YAEF,aAAa,OAAA,CAAQ,IAAA,GAAO;QAAA,OACvB;YACL,IAAA,CAAK,WAAA,GAAc;YACnB,IAAA,CAAK,MAAA,GAAS,aAAa,KAAA;YAC3B,IAAA,CAAK,OAAA,GAAU,aAAa,MAAA;QAC9B;QAEA,IAAA,CAAK,aAAA,GAAgB;QAChB,IAAA,CAAA,aAAA,GAAgB,aAAa,KAAA;QAC7B,IAAA,CAAA,aAAA,CAAc,OAAA,CAAQ,IAAA,GAAO;QAElC,IAAA,CAAK,WAAA,GAAc,IAAA,CAAK,aAAA;QACxB,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,aAAA;QAEvB,IAAA,CAAK,cAAA,GAAiB;QAItB,oPAAI,aAAA,KAAe,KAAA,GAAW;YAC5B,QAAQ,KAAA,CAAM,2CAA2C;QAC3D;QAEA,2PAAI,aAAA,KAAe,KAAA,GAAW;YAC5B,QAAQ,KAAA,CAAM,2CAA2C;QAC3D;QAEK,IAAA,CAAA,QAAA,GAAW,IAAI,oQAAA,CAAW,6PAAU;QAEpC,IAAA,CAAA,QAAA,CAAS,QAAA,CAAS,QAAA,0MAAW,aAAA;QAE7B,IAAA,CAAA,KAAA,GAAQ,2MAAI,QAAA;IACnB;IAEO,cAAoB;QACzB,MAAM,MAAM,IAAA,CAAK,UAAA;QACjB,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,WAAA;QACvB,IAAA,CAAK,WAAA,GAAc;IACrB;IAEO,QAAQ,IAAA,EAAkB;QAC1B,IAAA,CAAA,MAAA,CAAO,IAAA,CAAK,IAAI;QAChB,KAAA,OAAA,CAAQ,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,WAAA,EAAa,IAAA,CAAK,OAAA,GAAU,IAAA,CAAK,WAAW;IAC9E;IAEO,WAAW,IAAA,EAAY,KAAA,EAAqB;QACjD,IAAA,CAAK,MAAA,CAAO,MAAA,CAAO,OAAO,GAAG,IAAI;QAC5B,KAAA,OAAA,CAAQ,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,WAAA,EAAa,IAAA,CAAK,OAAA,GAAU,IAAA,CAAK,WAAW;IAC9E;IAEO,WAAW,IAAA,EAAkB;QAClC,MAAM,QAAQ,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,IAAI;QAEtC,IAAI,UAAU,CAAA,GAAI;YACX,IAAA,CAAA,MAAA,CAAO,MAAA,CAAO,OAAO,CAAC;QAC7B;IACF;IAEO,kBAAkB,SAAA,EAA4B;QACnD,IAAA,IAAS,IAAI,YAAY,GAAG,IAAI,IAAA,CAAK,MAAA,CAAO,MAAA,EAAQ,IAAK;YACvD,IAAI,IAAA,CAAK,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,EAAS;gBACnB,OAAA;YACT;QACF;QAEO,OAAA;IACT;IAEO,OAAO,SAAA,EAA0B;QAGtC,IAAI,cAAc,KAAA,GAAW;YACf,YAAA,IAAA,CAAK,KAAA,CAAM,QAAA;QACzB;QAEM,MAAA,sBAAsB,IAAA,CAAK,QAAA,CAAS,eAAA,CAAgB;QAE1D,IAAI,aAAa;QAEX,MAAA,KAAK,IAAA,CAAK,MAAA,CAAO,MAAA;QAEvB,IAAA,IAAS,IAAI,GAAG,IAAI,IAAI,IAAK;YACrB,MAAA,OAAO,IAAA,CAAK,MAAA,CAAO,CAAC,CAAA;YAE1B,IAAI,KAAK,OAAA,KAAY,OAAO;YAE5B,KAAK,cAAA,GAAiB,IAAA,CAAK,cAAA,IAAkB,IAAA,CAAK,iBAAA,CAAkB,CAAC;YAChE,KAAA,MAAA,CAAO,IAAA,CAAK,QAAA,EAAU,IAAA,CAAK,WAAA,EAAa,IAAA,CAAK,UAAA,EAAY,WAAW,UAAU;YAEnF,IAAI,KAAK,SAAA,EAAW;gBAClB,IAAI,YAAY;oBACR,MAAA,UAAU,IAAA,CAAK,QAAA,CAAS,UAAA,CAAW;oBACzC,MAAM,UAAU,IAAA,CAAK,QAAA,CAAS,KAAA,CAAM,OAAA,CAAQ,OAAA;oBAG5C,QAAQ,OAAA,CAAQ,QAAQ,QAAA,EAAU,GAAG,UAAU;oBAE1C,IAAA,CAAA,QAAA,CAAS,MAAA,CAAO,IAAA,CAAK,QAAA,EAAU,IAAA,CAAK,WAAA,EAAa,IAAA,CAAK,UAAA,EAAY,SAAS;oBAGhF,QAAQ,OAAA,CAAQ,QAAQ,KAAA,EAAO,GAAG,UAAU;gBAC9C;gBAEA,IAAA,CAAK,WAAA,CAAY;YACnB;YAEA,wPAAI,YAAA,KAAa,KAAA,GAAW;gBAC1B,IAAI,qQAAgB,WAAA,EAAU;oBACf,aAAA;gBAAA,OAAA,IACJ,qQAAgB,gBAAA,EAAe;oBAC3B,aAAA;gBACf;YACF;QACF;QAEK,IAAA,CAAA,QAAA,CAAS,eAAA,CAAgB,mBAAmB;IACnD;IAEO,MAAM,YAAA,EAAuC;QAClD,IAAI,iBAAiB,KAAA,GAAW;YAC9B,MAAM,OAAO,IAAA,CAAK,QAAA,CAAS,OAAA,CAAQ,IAAI,iNAAA,EAAS;YAC3C,IAAA,CAAA,WAAA,GAAc,IAAA,CAAK,QAAA,CAAS,aAAA,CAAc;YAC/C,IAAA,CAAK,MAAA,GAAS,KAAK,KAAA;YACnB,IAAA,CAAK,OAAA,GAAU,KAAK,MAAA;YAEL,eAAA,IAAA,CAAK,aAAA,CAAc,KAAA;YACrB,aAAA,OAAA,CAAQ,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,WAAA,EAAa,IAAA,CAAK,OAAA,GAAU,IAAA,CAAK,WAAW;QACtF;QAEA,IAAA,CAAK,aAAA,CAAc,OAAA;QACnB,IAAA,CAAK,aAAA,CAAc,OAAA;QACnB,IAAA,CAAK,aAAA,GAAgB;QAChB,IAAA,CAAA,aAAA,GAAgB,aAAa,KAAA;QAElC,IAAA,CAAK,WAAA,GAAc,IAAA,CAAK,aAAA;QACxB,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,aAAA;IACzB;IAEO,QAAQ,KAAA,EAAe,MAAA,EAAsB;QAClD,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,OAAA,GAAU;QAET,MAAA,iBAAiB,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,WAAA;QACpC,MAAA,kBAAkB,IAAA,CAAK,OAAA,GAAU,IAAA,CAAK,WAAA;QAEvC,IAAA,CAAA,aAAA,CAAc,OAAA,CAAQ,gBAAgB,eAAe;QACrD,IAAA,CAAA,aAAA,CAAc,OAAA,CAAQ,gBAAgB,eAAe;QAE1D,IAAA,IAAS,IAAI,GAAG,IAAI,IAAA,CAAK,MAAA,CAAO,MAAA,EAAQ,IAAK;YAC3C,IAAA,CAAK,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,CAAQ,gBAAgB,eAAe;QACxD;IACF;IAEO,cAAc,UAAA,EAA0B;QAC7C,IAAA,CAAK,WAAA,GAAc;QAEnB,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,OAAO;IACxC;IAEO,UAAU;QACf,IAAA,CAAK,aAAA,CAAc,OAAA;QACnB,IAAA,CAAK,aAAA,CAAc,OAAA;QAEnB,IAAA,CAAK,QAAA,CAAS,OAAA;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3022, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3028, "column": 0}, "map": {"version": 3, "file": "RenderPass.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/postprocessing/RenderPass.ts"], "sourcesContent": ["import { Camera, Color, Material, Scene, WebGLRenderTarget, WebGLRenderer } from 'three'\nimport { Pass } from './Pass'\n\nclass RenderPass extends Pass {\n  public scene: Scene\n  public camera: Camera\n  public overrideMaterial: Material | undefined\n  public clearColor: Color | undefined\n  public clearAlpha: number\n  public clearDepth = false\n  private _oldClearColor = new Color()\n\n  constructor(scene: Scene, camera: Camera, overrideMaterial?: Material, clearColor?: Color, clearAlpha = 0) {\n    super()\n\n    this.scene = scene\n    this.camera = camera\n\n    this.overrideMaterial = overrideMaterial\n\n    this.clearColor = clearColor\n    this.clearAlpha = clearAlpha\n\n    this.clear = true\n    this.needsSwap = false\n  }\n\n  public render(\n    renderer: WebGLRenderer,\n    writeBuffer: WebGLRenderTarget,\n    readBuffer: WebGLRenderTarget /*, deltaTime, maskActive */,\n  ): void {\n    let oldAutoClear = renderer.autoClear\n    renderer.autoClear = false\n\n    let oldClearAlpha\n    let oldOverrideMaterial: Material | null = null\n\n    if (this.overrideMaterial !== undefined) {\n      oldOverrideMaterial = this.scene.overrideMaterial\n\n      this.scene.overrideMaterial = this.overrideMaterial\n    }\n\n    if (this.clearColor) {\n      renderer.getClearColor(this._oldClearColor)\n      oldClearAlpha = renderer.getClearAlpha()\n\n      renderer.setClearColor(this.clearColor, this.clearAlpha)\n    }\n\n    if (this.clearDepth) {\n      renderer.clearDepth()\n    }\n\n    renderer.setRenderTarget(this.renderToScreen ? null : readBuffer)\n\n    // TODO: Avoid using autoClear properties, see https://github.com/mrdoob/three.js/pull/15571#issuecomment-465669600\n    if (this.clear) renderer.clear(renderer.autoClearColor, renderer.autoClearDepth, renderer.autoClearStencil)\n    renderer.render(this.scene, this.camera)\n\n    if (this.clearColor) {\n      renderer.setClearColor(this._oldClearColor, oldClearAlpha)\n    }\n\n    if (this.overrideMaterial !== undefined) {\n      this.scene.overrideMaterial = oldOverrideMaterial\n    }\n\n    renderer.autoClear = oldAutoClear\n  }\n}\n\nexport { RenderPass }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA,MAAM,mBAAmB,wPAAA,CAAK;IAS5B,YAAY,KAAA,EAAc,MAAA,EAAgB,gBAAA,EAA6B,UAAA,EAAoB,aAAa,CAAA,CAAG;QACnG,KAAA;QATD,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA,cAAa;QACZ,cAAA,IAAA,EAAA,kBAAiB,2MAAI,QAAA;QAK3B,IAAA,CAAK,KAAA,GAAQ;QACb,IAAA,CAAK,MAAA,GAAS;QAEd,IAAA,CAAK,gBAAA,GAAmB;QAExB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,UAAA,GAAa;QAElB,IAAA,CAAK,KAAA,GAAQ;QACb,IAAA,CAAK,SAAA,GAAY;IACnB;IAEO,OACL,QAAA,EACA,WAAA,EACA,UAAA,EACM;QACN,IAAI,eAAe,SAAS,SAAA;QAC5B,SAAS,SAAA,GAAY;QAEjB,IAAA;QACJ,IAAI,sBAAuC;QAEvC,IAAA,IAAA,CAAK,gBAAA,KAAqB,KAAA,GAAW;YACvC,sBAAsB,IAAA,CAAK,KAAA,CAAM,gBAAA;YAE5B,IAAA,CAAA,KAAA,CAAM,gBAAA,GAAmB,IAAA,CAAK,gBAAA;QACrC;QAEA,IAAI,IAAA,CAAK,UAAA,EAAY;YACV,SAAA,aAAA,CAAc,IAAA,CAAK,cAAc;YAC1C,gBAAgB,SAAS,aAAA;YAEzB,SAAS,aAAA,CAAc,IAAA,CAAK,UAAA,EAAY,IAAA,CAAK,UAAU;QACzD;QAEA,IAAI,IAAA,CAAK,UAAA,EAAY;YACnB,SAAS,UAAA,CAAW;QACtB;QAEA,SAAS,eAAA,CAAgB,IAAA,CAAK,cAAA,GAAiB,OAAO,UAAU;QAGhE,IAAI,IAAA,CAAK,KAAA,EAAO,SAAS,KAAA,CAAM,SAAS,cAAA,EAAgB,SAAS,cAAA,EAAgB,SAAS,gBAAgB;QAC1G,SAAS,MAAA,CAAO,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,MAAM;QAEvC,IAAI,IAAA,CAAK,UAAA,EAAY;YACV,SAAA,aAAA,CAAc,IAAA,CAAK,cAAA,EAAgB,aAAa;QAC3D;QAEI,IAAA,IAAA,CAAK,gBAAA,KAAqB,KAAA,GAAW;YACvC,IAAA,CAAK,KAAA,CAAM,gBAAA,GAAmB;QAChC;QAEA,SAAS,SAAA,GAAY;IACvB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3095, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3101, "column": 0}, "map": {"version": 3, "file": "MeshSurfaceSampler.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/math/MeshSurfaceSampler.js"], "sourcesContent": ["import { Triangle, Vector3 } from 'three'\n\n/**\n * Utility class for sampling weighted random points on the surface of a mesh.\n *\n * Building the sampler is a one-time O(n) operation. Once built, any number of\n * random samples may be selected in O(logn) time. Memory usage is O(n).\n *\n * References:\n * - http://www.joesfer.com/?p=84\n * - https://stackoverflow.com/a/4322940/1314762\n */\n\nconst _face = /* @__PURE__ */ new Triangle()\nconst _color = /* @__PURE__ */ new Vector3()\n\nclass MeshSurfaceSampler {\n  constructor(mesh) {\n    let geometry = mesh.geometry\n\n    if (geometry.index) {\n      console.warn('THREE.MeshSurfaceSampler: Converting geometry to non-indexed BufferGeometry.')\n\n      geometry = geometry.toNonIndexed()\n    }\n\n    this.geometry = geometry\n    this.randomFunction = Math.random\n\n    this.positionAttribute = this.geometry.getAttribute('position')\n    this.colorAttribute = this.geometry.getAttribute('color')\n    this.weightAttribute = null\n\n    this.distribution = null\n  }\n\n  setWeightAttribute(name) {\n    this.weightAttribute = name ? this.geometry.getAttribute(name) : null\n\n    return this\n  }\n\n  build() {\n    const positionAttribute = this.positionAttribute\n    const weightAttribute = this.weightAttribute\n\n    const faceWeights = new Float32Array(positionAttribute.count / 3)\n\n    // Accumulate weights for each mesh face.\n\n    for (let i = 0; i < positionAttribute.count; i += 3) {\n      let faceWeight = 1\n\n      if (weightAttribute) {\n        faceWeight = weightAttribute.getX(i) + weightAttribute.getX(i + 1) + weightAttribute.getX(i + 2)\n      }\n\n      _face.a.fromBufferAttribute(positionAttribute, i)\n      _face.b.fromBufferAttribute(positionAttribute, i + 1)\n      _face.c.fromBufferAttribute(positionAttribute, i + 2)\n      faceWeight *= _face.getArea()\n\n      faceWeights[i / 3] = faceWeight\n    }\n\n    // Store cumulative total face weights in an array, where weight index\n    // corresponds to face index.\n\n    this.distribution = new Float32Array(positionAttribute.count / 3)\n\n    let cumulativeTotal = 0\n\n    for (let i = 0; i < faceWeights.length; i++) {\n      cumulativeTotal += faceWeights[i]\n\n      this.distribution[i] = cumulativeTotal\n    }\n\n    return this\n  }\n\n  setRandomGenerator(randomFunction) {\n    this.randomFunction = randomFunction\n    return this\n  }\n\n  sample(targetPosition, targetNormal, targetColor) {\n    const faceIndex = this.sampleFaceIndex()\n    return this.sampleFace(faceIndex, targetPosition, targetNormal, targetColor)\n  }\n\n  sampleFaceIndex() {\n    const cumulativeTotal = this.distribution[this.distribution.length - 1]\n    return this.binarySearch(this.randomFunction() * cumulativeTotal)\n  }\n\n  binarySearch(x) {\n    const dist = this.distribution\n    let start = 0\n    let end = dist.length - 1\n\n    let index = -1\n\n    while (start <= end) {\n      const mid = Math.ceil((start + end) / 2)\n\n      if (mid === 0 || (dist[mid - 1] <= x && dist[mid] > x)) {\n        index = mid\n\n        break\n      } else if (x < dist[mid]) {\n        end = mid - 1\n      } else {\n        start = mid + 1\n      }\n    }\n\n    return index\n  }\n\n  sampleFace(faceIndex, targetPosition, targetNormal, targetColor) {\n    let u = this.randomFunction()\n    let v = this.randomFunction()\n\n    if (u + v > 1) {\n      u = 1 - u\n      v = 1 - v\n    }\n\n    _face.a.fromBufferAttribute(this.positionAttribute, faceIndex * 3)\n    _face.b.fromBufferAttribute(this.positionAttribute, faceIndex * 3 + 1)\n    _face.c.fromBufferAttribute(this.positionAttribute, faceIndex * 3 + 2)\n\n    targetPosition\n      .set(0, 0, 0)\n      .addScaledVector(_face.a, u)\n      .addScaledVector(_face.b, v)\n      .addScaledVector(_face.c, 1 - (u + v))\n\n    if (targetNormal !== undefined) {\n      _face.getNormal(targetNormal)\n    }\n\n    if (targetColor !== undefined && this.colorAttribute !== undefined) {\n      _face.a.fromBufferAttribute(this.colorAttribute, faceIndex * 3)\n      _face.b.fromBufferAttribute(this.colorAttribute, faceIndex * 3 + 1)\n      _face.c.fromBufferAttribute(this.colorAttribute, faceIndex * 3 + 2)\n\n      _color\n        .set(0, 0, 0)\n        .addScaledVector(_face.a, u)\n        .addScaledVector(_face.b, v)\n        .addScaledVector(_face.c, 1 - (u + v))\n\n      targetColor.r = _color.x\n      targetColor.g = _color.y\n      targetColor.b = _color.z\n    }\n\n    return this\n  }\n}\n\nexport { MeshSurfaceSampler }\n"], "names": [], "mappings": ";;;;;AAaA,MAAM,QAAwB,aAAA,GAAA,2MAAI,WAAA,CAAU;AAC5C,MAAM,SAAyB,aAAA,GAAA,2MAAI,UAAA,CAAS;AAE5C,MAAM,mBAAmB;IACvB,YAAY,IAAA,CAAM;QAChB,IAAI,WAAW,KAAK,QAAA;QAEpB,IAAI,SAAS,KAAA,EAAO;YAClB,QAAQ,IAAA,CAAK,8EAA8E;YAE3F,WAAW,SAAS,YAAA,CAAc;QACnC;QAED,IAAA,CAAK,QAAA,GAAW;QAChB,IAAA,CAAK,cAAA,GAAiB,KAAK,MAAA;QAE3B,IAAA,CAAK,iBAAA,GAAoB,IAAA,CAAK,QAAA,CAAS,YAAA,CAAa,UAAU;QAC9D,IAAA,CAAK,cAAA,GAAiB,IAAA,CAAK,QAAA,CAAS,YAAA,CAAa,OAAO;QACxD,IAAA,CAAK,eAAA,GAAkB;QAEvB,IAAA,CAAK,YAAA,GAAe;IACrB;IAED,mBAAmB,IAAA,EAAM;QACvB,IAAA,CAAK,eAAA,GAAkB,OAAO,IAAA,CAAK,QAAA,CAAS,YAAA,CAAa,IAAI,IAAI;QAEjE,OAAO,IAAA;IACR;IAED,QAAQ;QACN,MAAM,oBAAoB,IAAA,CAAK,iBAAA;QAC/B,MAAM,kBAAkB,IAAA,CAAK,eAAA;QAE7B,MAAM,cAAc,IAAI,aAAa,kBAAkB,KAAA,GAAQ,CAAC;QAIhE,IAAA,IAAS,IAAI,GAAG,IAAI,kBAAkB,KAAA,EAAO,KAAK,EAAG;YACnD,IAAI,aAAa;YAEjB,IAAI,iBAAiB;gBACnB,aAAa,gBAAgB,IAAA,CAAK,CAAC,IAAI,gBAAgB,IAAA,CAAK,IAAI,CAAC,IAAI,gBAAgB,IAAA,CAAK,IAAI,CAAC;YAChG;YAED,MAAM,CAAA,CAAE,mBAAA,CAAoB,mBAAmB,CAAC;YAChD,MAAM,CAAA,CAAE,mBAAA,CAAoB,mBAAmB,IAAI,CAAC;YACpD,MAAM,CAAA,CAAE,mBAAA,CAAoB,mBAAmB,IAAI,CAAC;YACpD,cAAc,MAAM,OAAA,CAAS;YAE7B,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI;QACtB;QAKD,IAAA,CAAK,YAAA,GAAe,IAAI,aAAa,kBAAkB,KAAA,GAAQ,CAAC;QAEhE,IAAI,kBAAkB;QAEtB,IAAA,IAAS,IAAI,GAAG,IAAI,YAAY,MAAA,EAAQ,IAAK;YAC3C,mBAAmB,WAAA,CAAY,CAAC,CAAA;YAEhC,IAAA,CAAK,YAAA,CAAa,CAAC,CAAA,GAAI;QACxB;QAED,OAAO,IAAA;IACR;IAED,mBAAmB,cAAA,EAAgB;QACjC,IAAA,CAAK,cAAA,GAAiB;QACtB,OAAO,IAAA;IACR;IAED,OAAO,cAAA,EAAgB,YAAA,EAAc,WAAA,EAAa;QAChD,MAAM,YAAY,IAAA,CAAK,eAAA,CAAiB;QACxC,OAAO,IAAA,CAAK,UAAA,CAAW,WAAW,gBAAgB,cAAc,WAAW;IAC5E;IAED,kBAAkB;QAChB,MAAM,kBAAkB,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,YAAA,CAAa,MAAA,GAAS,CAAC,CAAA;QACtE,OAAO,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,cAAA,CAAc,IAAK,eAAe;IACjE;IAED,aAAa,CAAA,EAAG;QACd,MAAM,OAAO,IAAA,CAAK,YAAA;QAClB,IAAI,QAAQ;QACZ,IAAI,MAAM,KAAK,MAAA,GAAS;QAExB,IAAI,QAAQ,CAAA;QAEZ,MAAO,SAAS,IAAK;YACnB,MAAM,MAAM,KAAK,IAAA,CAAA,CAAM,QAAQ,GAAA,IAAO,CAAC;YAEvC,IAAI,QAAQ,KAAM,IAAA,CAAK,MAAM,CAAC,CAAA,IAAK,KAAK,IAAA,CAAK,GAAG,CAAA,GAAI,GAAI;gBACtD,QAAQ;gBAER;YACD,OAAA,IAAU,IAAI,IAAA,CAAK,GAAG,CAAA,EAAG;gBACxB,MAAM,MAAM;YACpB,OAAa;gBACL,QAAQ,MAAM;YACf;QACF;QAED,OAAO;IACR;IAED,WAAW,SAAA,EAAW,cAAA,EAAgB,YAAA,EAAc,WAAA,EAAa;QAC/D,IAAI,IAAI,IAAA,CAAK,cAAA,CAAgB;QAC7B,IAAI,IAAI,IAAA,CAAK,cAAA,CAAgB;QAE7B,IAAI,IAAI,IAAI,GAAG;YACb,IAAI,IAAI;YACR,IAAI,IAAI;QACT;QAED,MAAM,CAAA,CAAE,mBAAA,CAAoB,IAAA,CAAK,iBAAA,EAAmB,YAAY,CAAC;QACjE,MAAM,CAAA,CAAE,mBAAA,CAAoB,IAAA,CAAK,iBAAA,EAAmB,YAAY,IAAI,CAAC;QACrE,MAAM,CAAA,CAAE,mBAAA,CAAoB,IAAA,CAAK,iBAAA,EAAmB,YAAY,IAAI,CAAC;QAErE,eACG,GAAA,CAAI,GAAG,GAAG,CAAC,EACX,eAAA,CAAgB,MAAM,CAAA,EAAG,CAAC,EAC1B,eAAA,CAAgB,MAAM,CAAA,EAAG,CAAC,EAC1B,eAAA,CAAgB,MAAM,CAAA,EAAG,IAAA,CAAK,IAAI,CAAA,CAAE;QAEvC,IAAI,iBAAiB,KAAA,GAAW;YAC9B,MAAM,SAAA,CAAU,YAAY;QAC7B;QAED,IAAI,gBAAgB,KAAA,KAAa,IAAA,CAAK,cAAA,KAAmB,KAAA,GAAW;YAClE,MAAM,CAAA,CAAE,mBAAA,CAAoB,IAAA,CAAK,cAAA,EAAgB,YAAY,CAAC;YAC9D,MAAM,CAAA,CAAE,mBAAA,CAAoB,IAAA,CAAK,cAAA,EAAgB,YAAY,IAAI,CAAC;YAClE,MAAM,CAAA,CAAE,mBAAA,CAAoB,IAAA,CAAK,cAAA,EAAgB,YAAY,IAAI,CAAC;YAElE,OACG,GAAA,CAAI,GAAG,GAAG,CAAC,EACX,eAAA,CAAgB,MAAM,CAAA,EAAG,CAAC,EAC1B,eAAA,CAAgB,MAAM,CAAA,EAAG,CAAC,EAC1B,eAAA,CAAgB,MAAM,CAAA,EAAG,IAAA,CAAK,IAAI,CAAA,CAAE;YAEvC,YAAY,CAAA,GAAI,OAAO,CAAA;YACvB,YAAY,CAAA,GAAI,OAAO,CAAA;YACvB,YAAY,CAAA,GAAI,OAAO,CAAA;QACxB;QAED,OAAO,IAAA;IACR;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3207, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3213, "column": 0}, "map": {"version": 3, "file": "SimplexNoise.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/math/SimplexNoise.ts"], "sourcesContent": ["// Ported from <PERSON>'s java implementation\n// http://staffwww.itn.liu.se/~stegu/simplexnoise/simplexnoise.pdf\n// Read <PERSON>'s excellent paper for details on how this code works.\n//\n// <PERSON> <EMAIL>\n//\n\nexport interface NumberGenerator {\n  random: () => number\n}\n\n// Added 4D noise\nexport class SimplexNoise {\n  private grad3 = [\n    [1, 1, 0],\n    [-1, 1, 0],\n    [1, -1, 0],\n    [-1, -1, 0],\n    [1, 0, 1],\n    [-1, 0, 1],\n    [1, 0, -1],\n    [-1, 0, -1],\n    [0, 1, 1],\n    [0, -1, 1],\n    [0, 1, -1],\n    [0, -1, -1],\n  ]\n\n  private grad4 = [\n    [0, 1, 1, 1],\n    [0, 1, 1, -1],\n    [0, 1, -1, 1],\n    [0, 1, -1, -1],\n    [0, -1, 1, 1],\n    [0, -1, 1, -1],\n    [0, -1, -1, 1],\n    [0, -1, -1, -1],\n    [1, 0, 1, 1],\n    [1, 0, 1, -1],\n    [1, 0, -1, 1],\n    [1, 0, -1, -1],\n    [-1, 0, 1, 1],\n    [-1, 0, 1, -1],\n    [-1, 0, -1, 1],\n    [-1, 0, -1, -1],\n    [1, 1, 0, 1],\n    [1, 1, 0, -1],\n    [1, -1, 0, 1],\n    [1, -1, 0, -1],\n    [-1, 1, 0, 1],\n    [-1, 1, 0, -1],\n    [-1, -1, 0, 1],\n    [-1, -1, 0, -1],\n    [1, 1, 1, 0],\n    [1, 1, -1, 0],\n    [1, -1, 1, 0],\n    [1, -1, -1, 0],\n    [-1, 1, 1, 0],\n    [-1, 1, -1, 0],\n    [-1, -1, 1, 0],\n    [-1, -1, -1, 0],\n  ]\n\n  private p: number[] = []\n\n  // To remove the need for index wrapping, double the permutation table length\n  private perm: number[] = []\n\n  // A lookup table to traverse the simplex around a given point in 4D.\n  // Details can be found where this table is used, in the 4D noise method.\n  private simplex = [\n    [0, 1, 2, 3],\n    [0, 1, 3, 2],\n    [0, 0, 0, 0],\n    [0, 2, 3, 1],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [1, 2, 3, 0],\n    [0, 2, 1, 3],\n    [0, 0, 0, 0],\n    [0, 3, 1, 2],\n    [0, 3, 2, 1],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [1, 3, 2, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [1, 2, 0, 3],\n    [0, 0, 0, 0],\n    [1, 3, 0, 2],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [2, 3, 0, 1],\n    [2, 3, 1, 0],\n    [1, 0, 2, 3],\n    [1, 0, 3, 2],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [2, 0, 3, 1],\n    [0, 0, 0, 0],\n    [2, 1, 3, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [2, 0, 1, 3],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [3, 0, 1, 2],\n    [3, 0, 2, 1],\n    [0, 0, 0, 0],\n    [3, 1, 2, 0],\n    [2, 1, 0, 3],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [0, 0, 0, 0],\n    [3, 1, 0, 2],\n    [0, 0, 0, 0],\n    [3, 2, 0, 1],\n    [3, 2, 1, 0],\n  ]\n\n  /**\n   * You can pass in a random number generator object if you like.\n   * It is assumed to have a random() method.\n   */\n  constructor(r: NumberGenerator = Math) {\n    for (let i = 0; i < 256; i++) {\n      this.p[i] = Math.floor(r.random() * 256)\n    }\n\n    for (let i = 0; i < 512; i++) {\n      this.perm[i] = this.p[i & 255]\n    }\n  }\n\n  public dot = (g: number[], x: number, y: number): number => {\n    return g[0] * x + g[1] * y\n  }\n\n  public dot3 = (g: number[], x: number, y: number, z: number): number => {\n    return g[0] * x + g[1] * y + g[2] * z\n  }\n\n  public dot4 = (g: number[], x: number, y: number, z: number, w: number): number => {\n    return g[0] * x + g[1] * y + g[2] * z + g[3] * w\n  }\n\n  public noise = (xin: number, yin: number): number => {\n    let n0\n    let n1\n    let n2 // Noise contributions from the three corners\n    // Skew the input space to determine which simplex cell we're in\n    const F2 = 0.5 * (Math.sqrt(3.0) - 1.0)\n    const s = (xin + yin) * F2 // Hairy factor for 2D\n    const i = Math.floor(xin + s)\n    const j = Math.floor(yin + s)\n    const G2 = (3.0 - Math.sqrt(3.0)) / 6.0\n    const t = (i + j) * G2\n    const X0 = i - t // Unskew the cell origin back to (x,y) space\n    const Y0 = j - t\n    const x0 = xin - X0 // The x,y distances from the cell origin\n    const y0 = yin - Y0\n    // For the 2D case, the simplex shape is an equilateral triangle.\n    // Determine which simplex we are in.\n    // upper triangle, YX order: (0,0)->(0,1)->(1,1)\n    let i1 = 0\n    // Offsets for second (middle) corner of simplex in (i,j) coords\n    let j1 = 1\n    if (x0 > y0) {\n      i1 = 1\n      j1 = 0\n    }\n\n    // A step of (1,0) in (i,j) means a step of (1-c,-c) in (x,y), and\n    // a step of (0,1) in (i,j) means a step of (-c,1-c) in (x,y), where\n    // c = (3-sqrt(3))/6\n    const x1 = x0 - i1 + G2 // Offsets for middle corner in (x,y) unskewed coords\n    const y1 = y0 - j1 + G2\n    const x2 = x0 - 1.0 + 2.0 * G2 // Offsets for last corner in (x,y) unskewed coords\n    const y2 = y0 - 1.0 + 2.0 * G2\n    // Work out the hashed gradient indices of the three simplex corners\n    const ii = i & 255\n    const jj = j & 255\n    const gi0 = this.perm[ii + this.perm[jj]] % 12\n    const gi1 = this.perm[ii + i1 + this.perm[jj + j1]] % 12\n    const gi2 = this.perm[ii + 1 + this.perm[jj + 1]] % 12\n    // Calculate the contribution from the three corners\n    let t0 = 0.5 - x0 * x0 - y0 * y0\n    if (t0 < 0) {\n      n0 = 0.0\n    } else {\n      t0 *= t0\n      n0 = t0 * t0 * this.dot(this.grad3[gi0], x0, y0) // (x,y) of grad3 used for 2D gradient\n    }\n\n    let t1 = 0.5 - x1 * x1 - y1 * y1\n    if (t1 < 0) {\n      n1 = 0.0\n    } else {\n      t1 *= t1\n      n1 = t1 * t1 * this.dot(this.grad3[gi1], x1, y1)\n    }\n\n    let t2 = 0.5 - x2 * x2 - y2 * y2\n    if (t2 < 0) {\n      n2 = 0.0\n    } else {\n      t2 *= t2\n      n2 = t2 * t2 * this.dot(this.grad3[gi2], x2, y2)\n    }\n\n    // Add contributions from each corner to get the final noise value.\n    // The result is scaled to return values in the interval [-1,1].\n    return 70.0 * (n0 + n1 + n2)\n  }\n\n  // 3D simplex noise\n  private noise3d = (xin: number, yin: number, zin: number): number => {\n    // Noise contributions from the four corners\n    let n0\n    let n1\n    let n2\n    let n3\n    // Skew the input space to determine which simplex cell we're in\n    const F3 = 1.0 / 3.0\n    const s = (xin + yin + zin) * F3 // Very nice and simple skew factor for 3D\n    const i = Math.floor(xin + s)\n    const j = Math.floor(yin + s)\n    const k = Math.floor(zin + s)\n    const G3 = 1.0 / 6.0 // Very nice and simple unskew factor, too\n    const t = (i + j + k) * G3\n    const X0 = i - t // Unskew the cell origin back to (x,y,z) space\n    const Y0 = j - t\n    const Z0 = k - t\n    const x0 = xin - X0 // The x,y,z distances from the cell origin\n    const y0 = yin - Y0\n    const z0 = zin - Z0\n    // For the 3D case, the simplex shape is a slightly irregular tetrahedron.\n    // Determine which simplex we are in.\n    let i1\n    let j1\n    let k1 // Offsets for second corner of simplex in (i,j,k) coords\n    let i2\n    let j2\n    let k2 // Offsets for third corner of simplex in (i,j,k) coords\n    if (x0 >= y0) {\n      if (y0 >= z0) {\n        i1 = 1\n        j1 = 0\n        k1 = 0\n        i2 = 1\n        j2 = 1\n        k2 = 0\n\n        // X Y Z order\n      } else if (x0 >= z0) {\n        i1 = 1\n        j1 = 0\n        k1 = 0\n        i2 = 1\n        j2 = 0\n        k2 = 1\n\n        // X Z Y order\n      } else {\n        i1 = 0\n        j1 = 0\n        k1 = 1\n        i2 = 1\n        j2 = 0\n        k2 = 1\n      } // Z X Y order\n    } else {\n      // x0<y0\n\n      if (y0 < z0) {\n        i1 = 0\n        j1 = 0\n        k1 = 1\n        i2 = 0\n        j2 = 1\n        k2 = 1\n\n        // Z Y X order\n      } else if (x0 < z0) {\n        i1 = 0\n        j1 = 1\n        k1 = 0\n        i2 = 0\n        j2 = 1\n        k2 = 1\n\n        // Y Z X order\n      } else {\n        i1 = 0\n        j1 = 1\n        k1 = 0\n        i2 = 1\n        j2 = 1\n        k2 = 0\n      } // Y X Z order\n    }\n\n    // A step of (1,0,0) in (i,j,k) means a step of (1-c,-c,-c) in (x,y,z),\n    // a step of (0,1,0) in (i,j,k) means a step of (-c,1-c,-c) in (x,y,z), and\n    // a step of (0,0,1) in (i,j,k) means a step of (-c,-c,1-c) in (x,y,z), where\n    // c = 1/6.\n    const x1 = x0 - i1 + G3 // Offsets for second corner in (x,y,z) coords\n    const y1 = y0 - j1 + G3\n    const z1 = z0 - k1 + G3\n    const x2 = x0 - i2 + 2.0 * G3 // Offsets for third corner in (x,y,z) coords\n    const y2 = y0 - j2 + 2.0 * G3\n    const z2 = z0 - k2 + 2.0 * G3\n    const x3 = x0 - 1.0 + 3.0 * G3 // Offsets for last corner in (x,y,z) coords\n    const y3 = y0 - 1.0 + 3.0 * G3\n    const z3 = z0 - 1.0 + 3.0 * G3\n    // Work out the hashed gradient indices of the four simplex corners\n    const ii = i & 255\n    const jj = j & 255\n    const kk = k & 255\n    const gi0 = this.perm[ii + this.perm[jj + this.perm[kk]]] % 12\n    const gi1 = this.perm[ii + i1 + this.perm[jj + j1 + this.perm[kk + k1]]] % 12\n    const gi2 = this.perm[ii + i2 + this.perm[jj + j2 + this.perm[kk + k2]]] % 12\n    const gi3 = this.perm[ii + 1 + this.perm[jj + 1 + this.perm[kk + 1]]] % 12\n    // Calculate the contribution from the four corners\n    let t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0\n    if (t0 < 0) {\n      n0 = 0.0\n    } else {\n      t0 *= t0\n      n0 = t0 * t0 * this.dot3(this.grad3[gi0], x0, y0, z0)\n    }\n\n    let t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1\n    if (t1 < 0) {\n      n1 = 0.0\n    } else {\n      t1 *= t1\n      n1 = t1 * t1 * this.dot3(this.grad3[gi1], x1, y1, z1)\n    }\n\n    let t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2\n    if (t2 < 0) {\n      n2 = 0.0\n    } else {\n      t2 *= t2\n      n2 = t2 * t2 * this.dot3(this.grad3[gi2], x2, y2, z2)\n    }\n\n    let t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3\n    if (t3 < 0) {\n      n3 = 0.0\n    } else {\n      t3 *= t3\n      n3 = t3 * t3 * this.dot3(this.grad3[gi3], x3, y3, z3)\n    }\n\n    // Add contributions from each corner to get the final noise value.\n    // The result is scaled to stay just inside [-1,1]\n    return 32.0 * (n0 + n1 + n2 + n3)\n  }\n\n  // 4D simplex noise\n  public noise4d = (x: number, y: number, z: number, w: number): number => {\n    // For faster and easier lookups\n    const grad4 = this.grad4\n    const simplex = this.simplex\n    const perm = this.perm\n\n    // The skewing and unskewing factors are hairy again for the 4D case\n    const F4 = (Math.sqrt(5.0) - 1.0) / 4.0\n    const G4 = (5.0 - Math.sqrt(5.0)) / 20.0\n    let n0\n    let n1\n    let n2\n    let n3\n    let n4 // Noise contributions from the five corners\n    // Skew the (x,y,z,w) space to determine which cell of 24 simplices we're in\n    const s = (x + y + z + w) * F4 // Factor for 4D skewing\n    const i = Math.floor(x + s)\n    const j = Math.floor(y + s)\n    const k = Math.floor(z + s)\n    const l = Math.floor(w + s)\n    const t = (i + j + k + l) * G4 // Factor for 4D unskewing\n    const X0 = i - t // Unskew the cell origin back to (x,y,z,w) space\n    const Y0 = j - t\n    const Z0 = k - t\n    const W0 = l - t\n    const x0 = x - X0 // The x,y,z,w distances from the cell origin\n    const y0 = y - Y0\n    const z0 = z - Z0\n    const w0 = w - W0\n\n    // For the 4D case, the simplex is a 4D shape I won't even try to describe.\n    // To find out which of the 24 possible simplices we're in, we need to\n    // determine the magnitude ordering of x0, y0, z0 and w0.\n    // The method below is a good way of finding the ordering of x,y,z,w and\n    // then find the correct traversal order for the simplex we’re in.\n    // First, six pair-wise comparisons are performed between each possible pair\n    // of the four coordinates, and the results are used to add up binary bits\n    // for an integer index.\n    const c1 = x0 > y0 ? 32 : 0\n    const c2 = x0 > z0 ? 16 : 0\n    const c3 = y0 > z0 ? 8 : 0\n    const c4 = x0 > w0 ? 4 : 0\n    const c5 = y0 > w0 ? 2 : 0\n    const c6 = z0 > w0 ? 1 : 0\n    const c = c1 + c2 + c3 + c4 + c5 + c6\n    // The integer offsets for the second simplex corner\n    let i1\n    let j1\n    let k1\n    let l1\n\n    // The integer offsets for the third simplex corner\n    let i2\n    let j2\n    let k2\n    let l2\n\n    // The integer offsets for the fourth simplex corner\n    let i3\n    let j3\n    let k3\n    let l3\n    // simplex[c] is a 4-vector with the numbers 0, 1, 2 and 3 in some order.\n    // Many values of c will never occur, since e.g. x>y>z>w makes x<z, y<w and x<w\n    // impossible. Only the 24 indices which have non-zero entries make any sense.\n    // We use a thresholding to set the coordinates in turn from the largest magnitude.\n    // The number 3 in the \"simplex\" array is at the position of the largest coordinate.\n    i1 = simplex[c][0] >= 3 ? 1 : 0\n    j1 = simplex[c][1] >= 3 ? 1 : 0\n    k1 = simplex[c][2] >= 3 ? 1 : 0\n    l1 = simplex[c][3] >= 3 ? 1 : 0\n    // The number 2 in the \"simplex\" array is at the second largest coordinate.\n    i2 = simplex[c][0] >= 2 ? 1 : 0\n    j2 = simplex[c][1] >= 2 ? 1 : 0\n    k2 = simplex[c][2] >= 2 ? 1 : 0\n    l2 = simplex[c][3] >= 2 ? 1 : 0\n    // The number 1 in the \"simplex\" array is at the second smallest coordinate.\n    i3 = simplex[c][0] >= 1 ? 1 : 0\n    j3 = simplex[c][1] >= 1 ? 1 : 0\n    k3 = simplex[c][2] >= 1 ? 1 : 0\n    l3 = simplex[c][3] >= 1 ? 1 : 0\n    // The fifth corner has all coordinate offsets = 1, so no need to look that up.\n    const x1 = x0 - i1 + G4 // Offsets for second corner in (x,y,z,w) coords\n    const y1 = y0 - j1 + G4\n    const z1 = z0 - k1 + G4\n    const w1 = w0 - l1 + G4\n    const x2 = x0 - i2 + 2.0 * G4 // Offsets for third corner in (x,y,z,w) coords\n    const y2 = y0 - j2 + 2.0 * G4\n    const z2 = z0 - k2 + 2.0 * G4\n    const w2 = w0 - l2 + 2.0 * G4\n    const x3 = x0 - i3 + 3.0 * G4 // Offsets for fourth corner in (x,y,z,w) coords\n    const y3 = y0 - j3 + 3.0 * G4\n    const z3 = z0 - k3 + 3.0 * G4\n    const w3 = w0 - l3 + 3.0 * G4\n    const x4 = x0 - 1.0 + 4.0 * G4 // Offsets for last corner in (x,y,z,w) coords\n    const y4 = y0 - 1.0 + 4.0 * G4\n    const z4 = z0 - 1.0 + 4.0 * G4\n    const w4 = w0 - 1.0 + 4.0 * G4\n    // Work out the hashed gradient indices of the five simplex corners\n    const ii = i & 255\n    const jj = j & 255\n    const kk = k & 255\n    const ll = l & 255\n    const gi0 = perm[ii + perm[jj + perm[kk + perm[ll]]]] % 32\n    const gi1 = perm[ii + i1 + perm[jj + j1 + perm[kk + k1 + perm[ll + l1]]]] % 32\n    const gi2 = perm[ii + i2 + perm[jj + j2 + perm[kk + k2 + perm[ll + l2]]]] % 32\n    const gi3 = perm[ii + i3 + perm[jj + j3 + perm[kk + k3 + perm[ll + l3]]]] % 32\n    const gi4 = perm[ii + 1 + perm[jj + 1 + perm[kk + 1 + perm[ll + 1]]]] % 32\n    // Calculate the contribution from the five corners\n    let t0 = 0.6 - x0 * x0 - y0 * y0 - z0 * z0 - w0 * w0\n    if (t0 < 0) {\n      n0 = 0.0\n    } else {\n      t0 *= t0\n      n0 = t0 * t0 * this.dot4(grad4[gi0], x0, y0, z0, w0)\n    }\n\n    let t1 = 0.6 - x1 * x1 - y1 * y1 - z1 * z1 - w1 * w1\n    if (t1 < 0) {\n      n1 = 0.0\n    } else {\n      t1 *= t1\n      n1 = t1 * t1 * this.dot4(grad4[gi1], x1, y1, z1, w1)\n    }\n\n    let t2 = 0.6 - x2 * x2 - y2 * y2 - z2 * z2 - w2 * w2\n    if (t2 < 0) {\n      n2 = 0.0\n    } else {\n      t2 *= t2\n      n2 = t2 * t2 * this.dot4(grad4[gi2], x2, y2, z2, w2)\n    }\n\n    let t3 = 0.6 - x3 * x3 - y3 * y3 - z3 * z3 - w3 * w3\n    if (t3 < 0) {\n      n3 = 0.0\n    } else {\n      t3 *= t3\n      n3 = t3 * t3 * this.dot4(grad4[gi3], x3, y3, z3, w3)\n    }\n\n    let t4 = 0.6 - x4 * x4 - y4 * y4 - z4 * z4 - w4 * w4\n    if (t4 < 0) {\n      n4 = 0.0\n    } else {\n      t4 *= t4\n      n4 = t4 * t4 * this.dot4(grad4[gi4], x4, y4, z4, w4)\n    }\n\n    // Sum up and scale the result to cover the range [-1,1]\n    return 27.0 * (n0 + n1 + n2 + n3 + n4)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAYO,MAAM,aAAa;IAAA;;;GAAA,GAiIxB,YAAY,IAAqB,IAAA,CAAM;QAhI/B,cAAA,IAAA,EAAA,SAAQ;YACd;gBAAC;gBAAG;gBAAG,CAAC;aAAA;YACR;gBAAC,CAAA;gBAAI;gBAAG,CAAC;aAAA;YACT;gBAAC;gBAAG,CAAA;gBAAI,CAAC;aAAA;YACT;gBAAC,CAAA;gBAAI,CAAA;gBAAI,CAAC;aAAA;YACV;gBAAC;gBAAG;gBAAG,CAAC;aAAA;YACR;gBAAC,CAAA;gBAAI;gBAAG,CAAC;aAAA;YACT;gBAAC;gBAAG;gBAAG,CAAA,CAAE;aAAA;YACT;gBAAC,CAAA;gBAAI;gBAAG,CAAA,CAAE;aAAA;YACV;gBAAC;gBAAG;gBAAG,CAAC;aAAA;YACR;gBAAC;gBAAG,CAAA;gBAAI,CAAC;aAAA;YACT;gBAAC;gBAAG;gBAAG,CAAA,CAAE;aAAA;YACT;gBAAC;gBAAG,CAAA;gBAAI,CAAA,CAAE;aAAA;SAAA;QAGJ,cAAA,IAAA,EAAA,SAAQ;YACd;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAA,CAAE;aAAA;YACZ;gBAAC;gBAAG;gBAAG,CAAA;gBAAI,CAAC;aAAA;YACZ;gBAAC;gBAAG;gBAAG,CAAA;gBAAI,CAAA,CAAE;aAAA;YACb;gBAAC;gBAAG,CAAA;gBAAI;gBAAG,CAAC;aAAA;YACZ;gBAAC;gBAAG,CAAA;gBAAI;gBAAG,CAAA,CAAE;aAAA;YACb;gBAAC;gBAAG,CAAA;gBAAI,CAAA;gBAAI,CAAC;aAAA;YACb;gBAAC;gBAAG,CAAA;gBAAI,CAAA;gBAAI,CAAA,CAAE;aAAA;YACd;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAA,CAAE;aAAA;YACZ;gBAAC;gBAAG;gBAAG,CAAA;gBAAI,CAAC;aAAA;YACZ;gBAAC;gBAAG;gBAAG,CAAA;gBAAI,CAAA,CAAE;aAAA;YACb;gBAAC,CAAA;gBAAI;gBAAG;gBAAG,CAAC;aAAA;YACZ;gBAAC,CAAA;gBAAI;gBAAG;gBAAG,CAAA,CAAE;aAAA;YACb;gBAAC,CAAA;gBAAI;gBAAG,CAAA;gBAAI,CAAC;aAAA;YACb;gBAAC,CAAA;gBAAI;gBAAG,CAAA;gBAAI,CAAA,CAAE;aAAA;YACd;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAA,CAAE;aAAA;YACZ;gBAAC;gBAAG,CAAA;gBAAI;gBAAG,CAAC;aAAA;YACZ;gBAAC;gBAAG,CAAA;gBAAI;gBAAG,CAAA,CAAE;aAAA;YACb;gBAAC,CAAA;gBAAI;gBAAG;gBAAG,CAAC;aAAA;YACZ;gBAAC,CAAA;gBAAI;gBAAG;gBAAG,CAAA,CAAE;aAAA;YACb;gBAAC,CAAA;gBAAI,CAAA;gBAAI;gBAAG,CAAC;aAAA;YACb;gBAAC,CAAA;gBAAI,CAAA;gBAAI;gBAAG,CAAA,CAAE;aAAA;YACd;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG,CAAA;gBAAI,CAAC;aAAA;YACZ;gBAAC;gBAAG,CAAA;gBAAI;gBAAG,CAAC;aAAA;YACZ;gBAAC;gBAAG,CAAA;gBAAI,CAAA;gBAAI,CAAC;aAAA;YACb;gBAAC,CAAA;gBAAI;gBAAG;gBAAG,CAAC;aAAA;YACZ;gBAAC,CAAA;gBAAI;gBAAG,CAAA;gBAAI,CAAC;aAAA;YACb;gBAAC,CAAA;gBAAI,CAAA;gBAAI;gBAAG,CAAC;aAAA;YACb;gBAAC,CAAA;gBAAI,CAAA;gBAAI,CAAA;gBAAI,CAAC;aAAA;SAAA;QAGR,cAAA,IAAA,EAAA,KAAc,CAAA,CAAA;QAGd,6EAAA;QAAA,cAAA,IAAA,EAAA,QAAiB,CAAA,CAAA;QAIjB,qEAAA;QAAA,yEAAA;QAAA,cAAA,IAAA,EAAA,WAAU;YAChB;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;YACX;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA;SAAA;QAiBN,cAAA,IAAA,EAAA,OAAM,CAAC,GAAa,GAAW,MAAsB;YAC1D,OAAO,CAAA,CAAE,CAAC,CAAA,GAAI,IAAI,CAAA,CAAE,CAAC,CAAA,GAAI;QAAA;QAGpB,cAAA,IAAA,EAAA,QAAO,CAAC,GAAa,GAAW,GAAW,MAAsB;YAC/D,OAAA,CAAA,CAAE,CAAC,CAAA,GAAI,IAAI,CAAA,CAAE,CAAC,CAAA,GAAI,IAAI,CAAA,CAAE,CAAC,CAAA,GAAI;QAAA;QAG/B,cAAA,IAAA,EAAA,QAAO,CAAC,GAAa,GAAW,GAAW,GAAW,MAAsB;YACjF,OAAO,CAAA,CAAE,CAAC,CAAA,GAAI,IAAI,CAAA,CAAE,CAAC,CAAA,GAAI,IAAI,CAAA,CAAE,CAAC,CAAA,GAAI,IAAI,CAAA,CAAE,CAAC,CAAA,GAAI;QAAA;QAG1C,cAAA,IAAA,EAAA,SAAQ,CAAC,KAAa,QAAwB;YAC/C,IAAA;YACA,IAAA;YACA,IAAA;YAEJ,MAAM,KAAK,MAAA,CAAO,KAAK,IAAA,CAAK,CAAG,IAAI,CAAA;YAC7B,MAAA,IAAA,CAAK,MAAM,GAAA,IAAO;YACxB,MAAM,IAAI,KAAK,KAAA,CAAM,MAAM,CAAC;YAC5B,MAAM,IAAI,KAAK,KAAA,CAAM,MAAM,CAAC;YAC5B,MAAM,KAAA,CAAM,IAAM,KAAK,IAAA,CAAK,CAAG,CAAA,IAAK;YAC9B,MAAA,IAAA,CAAK,IAAI,CAAA,IAAK;YACpB,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,MAAM;YACjB,MAAM,KAAK,MAAM;YAIjB,IAAI,KAAK;YAET,IAAI,KAAK;YACT,IAAI,KAAK,IAAI;gBACN,KAAA;gBACA,KAAA;YACP;YAKM,MAAA,KAAK,KAAK,KAAK;YACf,MAAA,KAAK,KAAK,KAAK;YACf,MAAA,KAAK,KAAK,IAAM,IAAM;YACtB,MAAA,KAAK,KAAK,IAAM,IAAM;YAE5B,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACT,MAAA,MAAM,IAAA,CAAK,IAAA,CAAK,KAAK,IAAA,CAAK,IAAA,CAAK,EAAE,CAAC,CAAA,GAAI;YACtC,MAAA,MAAM,IAAA,CAAK,IAAA,CAAK,KAAK,KAAK,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,CAAC,CAAA,GAAI;YAChD,MAAA,MAAM,IAAA,CAAK,IAAA,CAAK,KAAK,IAAI,IAAA,CAAK,IAAA,CAAK,KAAK,CAAC,CAAC,CAAA,GAAI;YAEpD,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK;YAC9B,IAAI,KAAK,GAAG;gBACL,KAAA;YAAA,OACA;gBACC,MAAA;gBACD,KAAA,KAAK,KAAK,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA,EAAG,IAAI,EAAE;YACjD;YAEA,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK;YAC9B,IAAI,KAAK,GAAG;gBACL,KAAA;YAAA,OACA;gBACC,MAAA;gBACD,KAAA,KAAK,KAAK,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA,EAAG,IAAI,EAAE;YACjD;YAEA,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK;YAC9B,IAAI,KAAK,GAAG;gBACL,KAAA;YAAA,OACA;gBACC,MAAA;gBACD,KAAA,KAAK,KAAK,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA,EAAG,IAAI,EAAE;YACjD;YAIO,OAAA,KAAA,CAAQ,KAAK,KAAK,EAAA;QAAA;QAInB,mBAAA;QAAA,cAAA,IAAA,EAAA,WAAU,CAAC,KAAa,KAAa,QAAwB;YAE/D,IAAA;YACA,IAAA;YACA,IAAA;YACA,IAAA;YAEJ,MAAM,KAAK,IAAM;YACX,MAAA,IAAA,CAAK,MAAM,MAAM,GAAA,IAAO;YAC9B,MAAM,IAAI,KAAK,KAAA,CAAM,MAAM,CAAC;YAC5B,MAAM,IAAI,KAAK,KAAA,CAAM,MAAM,CAAC;YAC5B,MAAM,IAAI,KAAK,KAAA,CAAM,MAAM,CAAC;YAC5B,MAAM,KAAK,IAAM;YACX,MAAA,IAAA,CAAK,IAAI,IAAI,CAAA,IAAK;YACxB,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,MAAM;YACjB,MAAM,KAAK,MAAM;YACjB,MAAM,KAAK,MAAM;YAGb,IAAA;YACA,IAAA;YACA,IAAA;YACA,IAAA;YACA,IAAA;YACA,IAAA;YACJ,IAAI,MAAM,IAAI;gBACZ,IAAI,MAAM,IAAI;oBACP,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;gBAAA,OAAA,IAGI,MAAM,IAAI;oBACd,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;gBAAA,OAGA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;gBACP;YAAA,OACK;gBAGL,IAAI,KAAK,IAAI;oBACN,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;gBAAA,OAAA,IAGI,KAAK,IAAI;oBACb,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;gBAAA,OAGA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;oBACA,KAAA;gBACP;YACF;YAMM,MAAA,KAAK,KAAK,KAAK;YACf,MAAA,KAAK,KAAK,KAAK;YACf,MAAA,KAAK,KAAK,KAAK;YACf,MAAA,KAAK,KAAK,KAAK,IAAM;YACrB,MAAA,KAAK,KAAK,KAAK,IAAM;YACrB,MAAA,KAAK,KAAK,KAAK,IAAM;YACrB,MAAA,KAAK,KAAK,IAAM,IAAM;YACtB,MAAA,KAAK,KAAK,IAAM,IAAM;YACtB,MAAA,KAAK,KAAK,IAAM,IAAM;YAE5B,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,MAAM,IAAA,CAAK,IAAA,CAAK,KAAK,IAAA,CAAK,IAAA,CAAK,KAAK,IAAA,CAAK,IAAA,CAAK,EAAE,CAAC,CAAC,CAAA,GAAI;YAC5D,MAAM,MAAM,IAAA,CAAK,IAAA,CAAK,KAAK,KAAK,IAAA,CAAK,IAAA,CAAK,KAAK,KAAK,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,CAAC,CAAC,CAAA,GAAI;YAC3E,MAAM,MAAM,IAAA,CAAK,IAAA,CAAK,KAAK,KAAK,IAAA,CAAK,IAAA,CAAK,KAAK,KAAK,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,CAAC,CAAC,CAAA,GAAI;YAC3E,MAAM,MAAM,IAAA,CAAK,IAAA,CAAK,KAAK,IAAI,IAAA,CAAK,IAAA,CAAK,KAAK,IAAI,IAAA,CAAK,IAAA,CAAK,KAAK,CAAC,CAAC,CAAC,CAAA,GAAI;YAExE,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;YACxC,IAAI,KAAK,GAAG;gBACL,KAAA;YAAA,OACA;gBACC,MAAA;gBACD,KAAA,KAAK,KAAK,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA,EAAG,IAAI,IAAI,EAAE;YACtD;YAEA,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;YACxC,IAAI,KAAK,GAAG;gBACL,KAAA;YAAA,OACA;gBACC,MAAA;gBACD,KAAA,KAAK,KAAK,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA,EAAG,IAAI,IAAI,EAAE;YACtD;YAEA,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;YACxC,IAAI,KAAK,GAAG;gBACL,KAAA;YAAA,OACA;gBACC,MAAA;gBACD,KAAA,KAAK,KAAK,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA,EAAG,IAAI,IAAI,EAAE;YACtD;YAEA,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;YACxC,IAAI,KAAK,GAAG;gBACL,KAAA;YAAA,OACA;gBACC,MAAA;gBACD,KAAA,KAAK,KAAK,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA,EAAG,IAAI,IAAI,EAAE;YACtD;YAIO,OAAA,KAAA,CAAQ,KAAK,KAAK,KAAK,EAAA;QAAA;QAIzB,mBAAA;QAAA,cAAA,IAAA,EAAA,WAAU,CAAC,GAAW,GAAW,GAAW,MAAsB;YAEvE,MAAM,QAAQ,IAAA,CAAK,KAAA;YACnB,MAAM,UAAU,IAAA,CAAK,OAAA;YACrB,MAAM,OAAO,IAAA,CAAK,IAAA;YAGlB,MAAM,KAAA,CAAM,KAAK,IAAA,CAAK,CAAG,IAAI,CAAA,IAAO;YACpC,MAAM,KAAA,CAAM,IAAM,KAAK,IAAA,CAAK,CAAG,CAAA,IAAK;YAChC,IAAA;YACA,IAAA;YACA,IAAA;YACA,IAAA;YACA,IAAA;YAEJ,MAAM,IAAA,CAAK,IAAI,IAAI,IAAI,CAAA,IAAK;YAC5B,MAAM,IAAI,KAAK,KAAA,CAAM,IAAI,CAAC;YAC1B,MAAM,IAAI,KAAK,KAAA,CAAM,IAAI,CAAC;YAC1B,MAAM,IAAI,KAAK,KAAA,CAAM,IAAI,CAAC;YAC1B,MAAM,IAAI,KAAK,KAAA,CAAM,IAAI,CAAC;YAC1B,MAAM,IAAA,CAAK,IAAI,IAAI,IAAI,CAAA,IAAK;YAC5B,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YAUT,MAAA,KAAK,KAAK,KAAK,KAAK;YACpB,MAAA,KAAK,KAAK,KAAK,KAAK;YACpB,MAAA,KAAK,KAAK,KAAK,IAAI;YACnB,MAAA,KAAK,KAAK,KAAK,IAAI;YACnB,MAAA,KAAK,KAAK,KAAK,IAAI;YACnB,MAAA,KAAK,KAAK,KAAK,IAAI;YACzB,MAAM,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK;YAE/B,IAAA;YACA,IAAA;YACA,IAAA;YACA,IAAA;YAGA,IAAA;YACA,IAAA;YACA,IAAA;YACA,IAAA;YAGA,IAAA;YACA,IAAA;YACA,IAAA;YACA,IAAA;YAMJ,KAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,CAAA,IAAK,IAAI,IAAI;YAC9B,KAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,CAAA,IAAK,IAAI,IAAI;YAC9B,KAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,CAAA,IAAK,IAAI,IAAI;YAC9B,KAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,CAAA,IAAK,IAAI,IAAI;YAE9B,KAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,CAAA,IAAK,IAAI,IAAI;YAC9B,KAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,CAAA,IAAK,IAAI,IAAI;YAC9B,KAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,CAAA,IAAK,IAAI,IAAI;YAC9B,KAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,CAAA,IAAK,IAAI,IAAI;YAE9B,KAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,CAAA,IAAK,IAAI,IAAI;YAC9B,KAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,CAAA,IAAK,IAAI,IAAI;YAC9B,KAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,CAAA,IAAK,IAAI,IAAI;YAC9B,KAAK,OAAA,CAAQ,CAAC,CAAA,CAAE,CAAC,CAAA,IAAK,IAAI,IAAI;YAExB,MAAA,KAAK,KAAK,KAAK;YACf,MAAA,KAAK,KAAK,KAAK;YACf,MAAA,KAAK,KAAK,KAAK;YACf,MAAA,KAAK,KAAK,KAAK;YACf,MAAA,KAAK,KAAK,KAAK,IAAM;YACrB,MAAA,KAAK,KAAK,KAAK,IAAM;YACrB,MAAA,KAAK,KAAK,KAAK,IAAM;YACrB,MAAA,KAAK,KAAK,KAAK,IAAM;YACrB,MAAA,KAAK,KAAK,KAAK,IAAM;YACrB,MAAA,KAAK,KAAK,KAAK,IAAM;YACrB,MAAA,KAAK,KAAK,KAAK,IAAM;YACrB,MAAA,KAAK,KAAK,KAAK,IAAM;YACrB,MAAA,KAAK,KAAK,IAAM,IAAM;YACtB,MAAA,KAAK,KAAK,IAAM,IAAM;YACtB,MAAA,KAAK,KAAK,IAAM,IAAM;YACtB,MAAA,KAAK,KAAK,IAAM,IAAM;YAE5B,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,MAAM,IAAA,CAAK,KAAK,IAAA,CAAK,KAAK,IAAA,CAAK,KAAK,IAAA,CAAK,EAAE,CAAC,CAAC,CAAC,CAAA,GAAI;YACxD,MAAM,MAAM,IAAA,CAAK,KAAK,KAAK,IAAA,CAAK,KAAK,KAAK,IAAA,CAAK,KAAK,KAAK,IAAA,CAAK,KAAK,EAAE,CAAC,CAAC,CAAC,CAAA,GAAI;YAC5E,MAAM,MAAM,IAAA,CAAK,KAAK,KAAK,IAAA,CAAK,KAAK,KAAK,IAAA,CAAK,KAAK,KAAK,IAAA,CAAK,KAAK,EAAE,CAAC,CAAC,CAAC,CAAA,GAAI;YAC5E,MAAM,MAAM,IAAA,CAAK,KAAK,KAAK,IAAA,CAAK,KAAK,KAAK,IAAA,CAAK,KAAK,KAAK,IAAA,CAAK,KAAK,EAAE,CAAC,CAAC,CAAC,CAAA,GAAI;YAC5E,MAAM,MAAM,IAAA,CAAK,KAAK,IAAI,IAAA,CAAK,KAAK,IAAI,IAAA,CAAK,KAAK,IAAI,IAAA,CAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA,GAAI;YAEpE,IAAA,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;YAClD,IAAI,KAAK,GAAG;gBACL,KAAA;YAAA,OACA;gBACC,MAAA;gBACD,KAAA,KAAK,KAAK,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA,EAAG,IAAI,IAAI,IAAI,EAAE;YACrD;YAEI,IAAA,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;YAClD,IAAI,KAAK,GAAG;gBACL,KAAA;YAAA,OACA;gBACC,MAAA;gBACD,KAAA,KAAK,KAAK,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA,EAAG,IAAI,IAAI,IAAI,EAAE;YACrD;YAEI,IAAA,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;YAClD,IAAI,KAAK,GAAG;gBACL,KAAA;YAAA,OACA;gBACC,MAAA;gBACD,KAAA,KAAK,KAAK,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA,EAAG,IAAI,IAAI,IAAI,EAAE;YACrD;YAEI,IAAA,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;YAClD,IAAI,KAAK,GAAG;gBACL,KAAA;YAAA,OACA;gBACC,MAAA;gBACD,KAAA,KAAK,KAAK,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA,EAAG,IAAI,IAAI,IAAI,EAAE;YACrD;YAEI,IAAA,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;YAClD,IAAI,KAAK,GAAG;gBACL,KAAA;YAAA,OACA;gBACC,MAAA;gBACD,KAAA,KAAK,KAAK,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA,EAAG,IAAI,IAAI,IAAI,EAAE;YACrD;YAGA,OAAO,KAAA,CAAQ,KAAK,KAAK,KAAK,KAAK,EAAA;QAAA;QAnYnC,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,IAAK;YACvB,IAAA,CAAA,CAAA,CAAE,CAAC,CAAA,GAAI,KAAK,KAAA,CAAM,EAAE,MAAA,KAAW,GAAG;QACzC;QAEA,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,IAAA,CAAK,IAAA,CAAK,CAAC,CAAA,GAAI,IAAA,CAAK,CAAA,CAAE,IAAI,GAAG,CAAA;QAC/B;IACF;AA8XF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4187, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4193, "column": 0}, "map": {"version": 3, "file": "MarchingCubes.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/objects/MarchingCubes.js"], "sourcesContent": ["import { BufferAttribute, BufferGeometry, Color, DynamicDrawUsage, Mesh, Sphere, Vector3 } from 'three'\n\n/**\n * Port of http://webglsamples.org/blob/blob.html\n */\n\nclass MarchingCubes extends Mesh {\n  constructor(resolution, material, enableUvs = false, enableColors = false, maxPolyCount = 10000) {\n    const geometry = new BufferGeometry()\n\n    super(geometry, material)\n\n    this.isMarchingCubes = true\n\n    const scope = this\n\n    // temp buffers used in polygonize\n\n    const vlist = new Float32Array(12 * 3)\n    const nlist = new Float32Array(12 * 3)\n    const clist = new Float32Array(12 * 3)\n\n    this.enableUvs = enableUvs\n    this.enableColors = enableColors\n\n    // functions have to be object properties\n    // prototype functions kill performance\n    // (tested and it was 4x slower !!!)\n\n    this.init = function (resolution) {\n      this.resolution = resolution\n\n      // parameters\n\n      this.isolation = 80.0\n\n      // size of field, 32 is pushing it in Javascript :)\n\n      this.size = resolution\n      this.size2 = this.size * this.size\n      this.size3 = this.size2 * this.size\n      this.halfsize = this.size / 2.0\n\n      // deltas\n\n      this.delta = 2.0 / this.size\n      this.yd = this.size\n      this.zd = this.size2\n\n      this.field = new Float32Array(this.size3)\n      this.normal_cache = new Float32Array(this.size3 * 3)\n      this.palette = new Float32Array(this.size3 * 3)\n\n      //\n\n      this.count = 0\n\n      const maxVertexCount = maxPolyCount * 3\n\n      this.positionArray = new Float32Array(maxVertexCount * 3)\n      const positionAttribute = new BufferAttribute(this.positionArray, 3)\n      positionAttribute.setUsage(DynamicDrawUsage)\n      geometry.setAttribute('position', positionAttribute)\n\n      this.normalArray = new Float32Array(maxVertexCount * 3)\n      const normalAttribute = new BufferAttribute(this.normalArray, 3)\n      normalAttribute.setUsage(DynamicDrawUsage)\n      geometry.setAttribute('normal', normalAttribute)\n\n      if (this.enableUvs) {\n        this.uvArray = new Float32Array(maxVertexCount * 2)\n        const uvAttribute = new BufferAttribute(this.uvArray, 2)\n        uvAttribute.setUsage(DynamicDrawUsage)\n        geometry.setAttribute('uv', uvAttribute)\n      }\n\n      if (this.enableColors) {\n        this.colorArray = new Float32Array(maxVertexCount * 3)\n        const colorAttribute = new BufferAttribute(this.colorArray, 3)\n        colorAttribute.setUsage(DynamicDrawUsage)\n        geometry.setAttribute('color', colorAttribute)\n      }\n\n      geometry.boundingSphere = new Sphere(new Vector3(), 1)\n    }\n\n    ///////////////////////\n    // Polygonization\n    ///////////////////////\n\n    function lerp(a, b, t) {\n      return a + (b - a) * t\n    }\n\n    function VIntX(q, offset, isol, x, y, z, valp1, valp2, c_offset1, c_offset2) {\n      const mu = (isol - valp1) / (valp2 - valp1),\n        nc = scope.normal_cache\n\n      vlist[offset + 0] = x + mu * scope.delta\n      vlist[offset + 1] = y\n      vlist[offset + 2] = z\n\n      nlist[offset + 0] = lerp(nc[q + 0], nc[q + 3], mu)\n      nlist[offset + 1] = lerp(nc[q + 1], nc[q + 4], mu)\n      nlist[offset + 2] = lerp(nc[q + 2], nc[q + 5], mu)\n\n      clist[offset + 0] = lerp(scope.palette[c_offset1 * 3 + 0], scope.palette[c_offset2 * 3 + 0], mu)\n      clist[offset + 1] = lerp(scope.palette[c_offset1 * 3 + 1], scope.palette[c_offset2 * 3 + 1], mu)\n      clist[offset + 2] = lerp(scope.palette[c_offset1 * 3 + 2], scope.palette[c_offset2 * 3 + 2], mu)\n    }\n\n    function VIntY(q, offset, isol, x, y, z, valp1, valp2, c_offset1, c_offset2) {\n      const mu = (isol - valp1) / (valp2 - valp1),\n        nc = scope.normal_cache\n\n      vlist[offset + 0] = x\n      vlist[offset + 1] = y + mu * scope.delta\n      vlist[offset + 2] = z\n\n      const q2 = q + scope.yd * 3\n\n      nlist[offset + 0] = lerp(nc[q + 0], nc[q2 + 0], mu)\n      nlist[offset + 1] = lerp(nc[q + 1], nc[q2 + 1], mu)\n      nlist[offset + 2] = lerp(nc[q + 2], nc[q2 + 2], mu)\n\n      clist[offset + 0] = lerp(scope.palette[c_offset1 * 3 + 0], scope.palette[c_offset2 * 3 + 0], mu)\n      clist[offset + 1] = lerp(scope.palette[c_offset1 * 3 + 1], scope.palette[c_offset2 * 3 + 1], mu)\n      clist[offset + 2] = lerp(scope.palette[c_offset1 * 3 + 2], scope.palette[c_offset2 * 3 + 2], mu)\n    }\n\n    function VIntZ(q, offset, isol, x, y, z, valp1, valp2, c_offset1, c_offset2) {\n      const mu = (isol - valp1) / (valp2 - valp1),\n        nc = scope.normal_cache\n\n      vlist[offset + 0] = x\n      vlist[offset + 1] = y\n      vlist[offset + 2] = z + mu * scope.delta\n\n      const q2 = q + scope.zd * 3\n\n      nlist[offset + 0] = lerp(nc[q + 0], nc[q2 + 0], mu)\n      nlist[offset + 1] = lerp(nc[q + 1], nc[q2 + 1], mu)\n      nlist[offset + 2] = lerp(nc[q + 2], nc[q2 + 2], mu)\n\n      clist[offset + 0] = lerp(scope.palette[c_offset1 * 3 + 0], scope.palette[c_offset2 * 3 + 0], mu)\n      clist[offset + 1] = lerp(scope.palette[c_offset1 * 3 + 1], scope.palette[c_offset2 * 3 + 1], mu)\n      clist[offset + 2] = lerp(scope.palette[c_offset1 * 3 + 2], scope.palette[c_offset2 * 3 + 2], mu)\n    }\n\n    function compNorm(q) {\n      const q3 = q * 3\n\n      if (scope.normal_cache[q3] === 0.0) {\n        scope.normal_cache[q3 + 0] = scope.field[q - 1] - scope.field[q + 1]\n        scope.normal_cache[q3 + 1] = scope.field[q - scope.yd] - scope.field[q + scope.yd]\n        scope.normal_cache[q3 + 2] = scope.field[q - scope.zd] - scope.field[q + scope.zd]\n      }\n    }\n\n    // Returns total number of triangles. Fills triangles.\n    // (this is where most of time is spent - it's inner work of O(n3) loop )\n\n    function polygonize(fx, fy, fz, q, isol) {\n      // cache indices\n      const q1 = q + 1,\n        qy = q + scope.yd,\n        qz = q + scope.zd,\n        q1y = q1 + scope.yd,\n        q1z = q1 + scope.zd,\n        qyz = q + scope.yd + scope.zd,\n        q1yz = q1 + scope.yd + scope.zd\n\n      let cubeindex = 0\n      const field0 = scope.field[q],\n        field1 = scope.field[q1],\n        field2 = scope.field[qy],\n        field3 = scope.field[q1y],\n        field4 = scope.field[qz],\n        field5 = scope.field[q1z],\n        field6 = scope.field[qyz],\n        field7 = scope.field[q1yz]\n\n      if (field0 < isol) cubeindex |= 1\n      if (field1 < isol) cubeindex |= 2\n      if (field2 < isol) cubeindex |= 8\n      if (field3 < isol) cubeindex |= 4\n      if (field4 < isol) cubeindex |= 16\n      if (field5 < isol) cubeindex |= 32\n      if (field6 < isol) cubeindex |= 128\n      if (field7 < isol) cubeindex |= 64\n\n      // if cube is entirely in/out of the surface - bail, nothing to draw\n\n      const bits = edgeTable[cubeindex]\n      if (bits === 0) return 0\n\n      const d = scope.delta,\n        fx2 = fx + d,\n        fy2 = fy + d,\n        fz2 = fz + d\n\n      // top of the cube\n\n      if (bits & 1) {\n        compNorm(q)\n        compNorm(q1)\n        VIntX(q * 3, 0, isol, fx, fy, fz, field0, field1, q, q1)\n      }\n\n      if (bits & 2) {\n        compNorm(q1)\n        compNorm(q1y)\n        VIntY(q1 * 3, 3, isol, fx2, fy, fz, field1, field3, q1, q1y)\n      }\n\n      if (bits & 4) {\n        compNorm(qy)\n        compNorm(q1y)\n        VIntX(qy * 3, 6, isol, fx, fy2, fz, field2, field3, qy, q1y)\n      }\n\n      if (bits & 8) {\n        compNorm(q)\n        compNorm(qy)\n        VIntY(q * 3, 9, isol, fx, fy, fz, field0, field2, q, qy)\n      }\n\n      // bottom of the cube\n\n      if (bits & 16) {\n        compNorm(qz)\n        compNorm(q1z)\n        VIntX(qz * 3, 12, isol, fx, fy, fz2, field4, field5, qz, q1z)\n      }\n\n      if (bits & 32) {\n        compNorm(q1z)\n        compNorm(q1yz)\n        VIntY(q1z * 3, 15, isol, fx2, fy, fz2, field5, field7, q1z, q1yz)\n      }\n\n      if (bits & 64) {\n        compNorm(qyz)\n        compNorm(q1yz)\n        VIntX(qyz * 3, 18, isol, fx, fy2, fz2, field6, field7, qyz, q1yz)\n      }\n\n      if (bits & 128) {\n        compNorm(qz)\n        compNorm(qyz)\n        VIntY(qz * 3, 21, isol, fx, fy, fz2, field4, field6, qz, qyz)\n      }\n\n      // vertical lines of the cube\n      if (bits & 256) {\n        compNorm(q)\n        compNorm(qz)\n        VIntZ(q * 3, 24, isol, fx, fy, fz, field0, field4, q, qz)\n      }\n\n      if (bits & 512) {\n        compNorm(q1)\n        compNorm(q1z)\n        VIntZ(q1 * 3, 27, isol, fx2, fy, fz, field1, field5, q1, q1z)\n      }\n\n      if (bits & 1024) {\n        compNorm(q1y)\n        compNorm(q1yz)\n        VIntZ(q1y * 3, 30, isol, fx2, fy2, fz, field3, field7, q1y, q1yz)\n      }\n\n      if (bits & 2048) {\n        compNorm(qy)\n        compNorm(qyz)\n        VIntZ(qy * 3, 33, isol, fx, fy2, fz, field2, field6, qy, qyz)\n      }\n\n      cubeindex <<= 4 // re-purpose cubeindex into an offset into triTable\n\n      let o1,\n        o2,\n        o3,\n        numtris = 0,\n        i = 0\n\n      // here is where triangles are created\n\n      while (triTable[cubeindex + i] != -1) {\n        o1 = cubeindex + i\n        o2 = o1 + 1\n        o3 = o1 + 2\n\n        posnormtriv(vlist, nlist, clist, 3 * triTable[o1], 3 * triTable[o2], 3 * triTable[o3])\n\n        i += 3\n        numtris++\n      }\n\n      return numtris\n    }\n\n    function posnormtriv(pos, norm, colors, o1, o2, o3) {\n      const c = scope.count * 3\n\n      // positions\n\n      scope.positionArray[c + 0] = pos[o1]\n      scope.positionArray[c + 1] = pos[o1 + 1]\n      scope.positionArray[c + 2] = pos[o1 + 2]\n\n      scope.positionArray[c + 3] = pos[o2]\n      scope.positionArray[c + 4] = pos[o2 + 1]\n      scope.positionArray[c + 5] = pos[o2 + 2]\n\n      scope.positionArray[c + 6] = pos[o3]\n      scope.positionArray[c + 7] = pos[o3 + 1]\n      scope.positionArray[c + 8] = pos[o3 + 2]\n\n      // normals\n\n      if (scope.material.flatShading === true) {\n        const nx = (norm[o1 + 0] + norm[o2 + 0] + norm[o3 + 0]) / 3\n        const ny = (norm[o1 + 1] + norm[o2 + 1] + norm[o3 + 1]) / 3\n        const nz = (norm[o1 + 2] + norm[o2 + 2] + norm[o3 + 2]) / 3\n\n        scope.normalArray[c + 0] = nx\n        scope.normalArray[c + 1] = ny\n        scope.normalArray[c + 2] = nz\n\n        scope.normalArray[c + 3] = nx\n        scope.normalArray[c + 4] = ny\n        scope.normalArray[c + 5] = nz\n\n        scope.normalArray[c + 6] = nx\n        scope.normalArray[c + 7] = ny\n        scope.normalArray[c + 8] = nz\n      } else {\n        scope.normalArray[c + 0] = norm[o1 + 0]\n        scope.normalArray[c + 1] = norm[o1 + 1]\n        scope.normalArray[c + 2] = norm[o1 + 2]\n\n        scope.normalArray[c + 3] = norm[o2 + 0]\n        scope.normalArray[c + 4] = norm[o2 + 1]\n        scope.normalArray[c + 5] = norm[o2 + 2]\n\n        scope.normalArray[c + 6] = norm[o3 + 0]\n        scope.normalArray[c + 7] = norm[o3 + 1]\n        scope.normalArray[c + 8] = norm[o3 + 2]\n      }\n\n      // uvs\n\n      if (scope.enableUvs) {\n        const d = scope.count * 2\n\n        scope.uvArray[d + 0] = pos[o1 + 0]\n        scope.uvArray[d + 1] = pos[o1 + 2]\n\n        scope.uvArray[d + 2] = pos[o2 + 0]\n        scope.uvArray[d + 3] = pos[o2 + 2]\n\n        scope.uvArray[d + 4] = pos[o3 + 0]\n        scope.uvArray[d + 5] = pos[o3 + 2]\n      }\n\n      // colors\n\n      if (scope.enableColors) {\n        scope.colorArray[c + 0] = colors[o1 + 0]\n        scope.colorArray[c + 1] = colors[o1 + 1]\n        scope.colorArray[c + 2] = colors[o1 + 2]\n\n        scope.colorArray[c + 3] = colors[o2 + 0]\n        scope.colorArray[c + 4] = colors[o2 + 1]\n        scope.colorArray[c + 5] = colors[o2 + 2]\n\n        scope.colorArray[c + 6] = colors[o3 + 0]\n        scope.colorArray[c + 7] = colors[o3 + 1]\n        scope.colorArray[c + 8] = colors[o3 + 2]\n      }\n\n      scope.count += 3\n    }\n\n    /////////////////////////////////////\n    // Metaballs\n    /////////////////////////////////////\n\n    // Adds a reciprocal ball (nice and blobby) that, to be fast, fades to zero after\n    // a fixed distance, determined by strength and subtract.\n\n    this.addBall = function (ballx, bally, ballz, strength, subtract, colors) {\n      const sign = Math.sign(strength)\n      strength = Math.abs(strength)\n      const userDefineColor = !(colors === undefined || colors === null)\n      let ballColor = new Color(ballx, bally, ballz)\n\n      if (userDefineColor) {\n        try {\n          ballColor =\n            colors instanceof Color\n              ? colors\n              : Array.isArray(colors)\n              ? new Color(\n                  Math.min(Math.abs(colors[0]), 1),\n                  Math.min(Math.abs(colors[1]), 1),\n                  Math.min(Math.abs(colors[2]), 1),\n                )\n              : new Color(colors)\n        } catch (err) {\n          ballColor = new Color(ballx, bally, ballz)\n        }\n      }\n\n      // Let's solve the equation to find the radius:\n      // 1.0 / (0.000001 + radius^2) * strength - subtract = 0\n      // strength / (radius^2) = subtract\n      // strength = subtract * radius^2\n      // radius^2 = strength / subtract\n      // radius = sqrt(strength / subtract)\n\n      const radius = this.size * Math.sqrt(strength / subtract),\n        zs = ballz * this.size,\n        ys = bally * this.size,\n        xs = ballx * this.size\n\n      let min_z = Math.floor(zs - radius)\n      if (min_z < 1) min_z = 1\n      let max_z = Math.floor(zs + radius)\n      if (max_z > this.size - 1) max_z = this.size - 1\n      let min_y = Math.floor(ys - radius)\n      if (min_y < 1) min_y = 1\n      let max_y = Math.floor(ys + radius)\n      if (max_y > this.size - 1) max_y = this.size - 1\n      let min_x = Math.floor(xs - radius)\n      if (min_x < 1) min_x = 1\n      let max_x = Math.floor(xs + radius)\n      if (max_x > this.size - 1) max_x = this.size - 1\n\n      // Don't polygonize in the outer layer because normals aren't\n      // well-defined there.\n\n      let x, y, z, y_offset, z_offset, fx, fy, fz, fz2, fy2, val\n\n      for (z = min_z; z < max_z; z++) {\n        z_offset = this.size2 * z\n        fz = z / this.size - ballz\n        fz2 = fz * fz\n\n        for (y = min_y; y < max_y; y++) {\n          y_offset = z_offset + this.size * y\n          fy = y / this.size - bally\n          fy2 = fy * fy\n\n          for (x = min_x; x < max_x; x++) {\n            fx = x / this.size - ballx\n            val = strength / (0.000001 + fx * fx + fy2 + fz2) - subtract\n            if (val > 0.0) {\n              this.field[y_offset + x] += val * sign\n\n              // optimization\n              // http://www.geisswerks.com/ryan/BLOBS/blobs.html\n              const ratio = Math.sqrt((x - xs) * (x - xs) + (y - ys) * (y - ys) + (z - zs) * (z - zs)) / radius\n              const contrib = 1 - ratio * ratio * ratio * (ratio * (ratio * 6 - 15) + 10)\n              this.palette[(y_offset + x) * 3 + 0] += ballColor.r * contrib\n              this.palette[(y_offset + x) * 3 + 1] += ballColor.g * contrib\n              this.palette[(y_offset + x) * 3 + 2] += ballColor.b * contrib\n            }\n          }\n        }\n      }\n    }\n\n    this.addPlaneX = function (strength, subtract) {\n      // cache attribute lookups\n      const size = this.size,\n        yd = this.yd,\n        zd = this.zd,\n        field = this.field\n\n      let x,\n        y,\n        z,\n        xx,\n        val,\n        xdiv,\n        cxy,\n        dist = size * Math.sqrt(strength / subtract)\n\n      if (dist > size) dist = size\n\n      for (x = 0; x < dist; x++) {\n        xdiv = x / size\n        xx = xdiv * xdiv\n        val = strength / (0.0001 + xx) - subtract\n\n        if (val > 0.0) {\n          for (y = 0; y < size; y++) {\n            cxy = x + y * yd\n\n            for (z = 0; z < size; z++) {\n              field[zd * z + cxy] += val\n            }\n          }\n        }\n      }\n    }\n\n    this.addPlaneY = function (strength, subtract) {\n      // cache attribute lookups\n      const size = this.size,\n        yd = this.yd,\n        zd = this.zd,\n        field = this.field\n\n      let x,\n        y,\n        z,\n        yy,\n        val,\n        ydiv,\n        cy,\n        cxy,\n        dist = size * Math.sqrt(strength / subtract)\n\n      if (dist > size) dist = size\n\n      for (y = 0; y < dist; y++) {\n        ydiv = y / size\n        yy = ydiv * ydiv\n        val = strength / (0.0001 + yy) - subtract\n\n        if (val > 0.0) {\n          cy = y * yd\n\n          for (x = 0; x < size; x++) {\n            cxy = cy + x\n\n            for (z = 0; z < size; z++) field[zd * z + cxy] += val\n          }\n        }\n      }\n    }\n\n    this.addPlaneZ = function (strength, subtract) {\n      // cache attribute lookups\n\n      const size = this.size,\n        yd = this.yd,\n        zd = this.zd,\n        field = this.field\n\n      let x,\n        y,\n        z,\n        zz,\n        val,\n        zdiv,\n        cz,\n        cyz,\n        dist = size * Math.sqrt(strength / subtract)\n\n      if (dist > size) dist = size\n\n      for (z = 0; z < dist; z++) {\n        zdiv = z / size\n        zz = zdiv * zdiv\n        val = strength / (0.0001 + zz) - subtract\n        if (val > 0.0) {\n          cz = zd * z\n\n          for (y = 0; y < size; y++) {\n            cyz = cz + y * yd\n\n            for (x = 0; x < size; x++) field[cyz + x] += val\n          }\n        }\n      }\n    }\n\n    /////////////////////////////////////\n    // Updates\n    /////////////////////////////////////\n\n    this.setCell = function (x, y, z, value) {\n      const index = this.size2 * z + this.size * y + x\n      this.field[index] = value\n    }\n\n    this.getCell = function (x, y, z) {\n      const index = this.size2 * z + this.size * y + x\n      return this.field[index]\n    }\n\n    this.blur = function (intensity = 1) {\n      const field = this.field\n      const fieldCopy = field.slice()\n      const size = this.size\n      const size2 = this.size2\n      for (let x = 0; x < size; x++) {\n        for (let y = 0; y < size; y++) {\n          for (let z = 0; z < size; z++) {\n            const index = size2 * z + size * y + x\n            let val = fieldCopy[index]\n            let count = 1\n\n            for (let x2 = -1; x2 <= 1; x2 += 2) {\n              const x3 = x2 + x\n              if (x3 < 0 || x3 >= size) continue\n\n              for (let y2 = -1; y2 <= 1; y2 += 2) {\n                const y3 = y2 + y\n                if (y3 < 0 || y3 >= size) continue\n\n                for (let z2 = -1; z2 <= 1; z2 += 2) {\n                  const z3 = z2 + z\n                  if (z3 < 0 || z3 >= size) continue\n\n                  const index2 = size2 * z3 + size * y3 + x3\n                  const val2 = fieldCopy[index2]\n\n                  count++\n                  val += (intensity * (val2 - val)) / count\n                }\n              }\n            }\n\n            field[index] = val\n          }\n        }\n      }\n    }\n\n    this.reset = function () {\n      // wipe the normal cache\n\n      for (let i = 0; i < this.size3; i++) {\n        this.normal_cache[i * 3] = 0.0\n        this.field[i] = 0.0\n        this.palette[i * 3] = this.palette[i * 3 + 1] = this.palette[i * 3 + 2] = 0.0\n      }\n    }\n\n    this.update = function () {\n      this.count = 0\n\n      // Triangulate. Yeah, this is slow.\n\n      const smin2 = this.size - 2\n\n      for (let z = 1; z < smin2; z++) {\n        const z_offset = this.size2 * z\n        const fz = (z - this.halfsize) / this.halfsize //+ 1\n\n        for (let y = 1; y < smin2; y++) {\n          const y_offset = z_offset + this.size * y\n          const fy = (y - this.halfsize) / this.halfsize //+ 1\n\n          for (let x = 1; x < smin2; x++) {\n            const fx = (x - this.halfsize) / this.halfsize //+ 1\n            const q = y_offset + x\n\n            polygonize(fx, fy, fz, q, this.isolation)\n          }\n        }\n      }\n\n      // set the draw range to only the processed triangles\n\n      this.geometry.setDrawRange(0, this.count)\n\n      // update geometry data\n\n      geometry.getAttribute('position').needsUpdate = true\n      geometry.getAttribute('normal').needsUpdate = true\n\n      if (this.enableUvs) geometry.getAttribute('uv').needsUpdate = true\n      if (this.enableColors) geometry.getAttribute('color').needsUpdate = true\n\n      // safety check\n\n      if (this.count / 3 > maxPolyCount)\n        console.warn(\n          'THREE.MarchingCubes: Geometry buffers too small for rendering. Please create an instance with a higher poly count.',\n        )\n    }\n\n    this.init(resolution)\n  }\n}\n\n/////////////////////////////////////\n// Marching cubes lookup tables\n/////////////////////////////////////\n\n// These tables are straight from Paul Bourke's page:\n// http://paulbourke.net/geometry/polygonise/\n// who in turn got them from Cory Gene Bloyd.\n\n// prettier-ignore\nconst edgeTable = new Int32Array( [\n\t0x0, 0x109, 0x203, 0x30a, 0x406, 0x50f, 0x605, 0x70c,\n\t0x80c, 0x905, 0xa0f, 0xb06, 0xc0a, 0xd03, 0xe09, 0xf00,\n\t0x190, 0x99, 0x393, 0x29a, 0x596, 0x49f, 0x795, 0x69c,\n\t0x99c, 0x895, 0xb9f, 0xa96, 0xd9a, 0xc93, 0xf99, 0xe90,\n\t0x230, 0x339, 0x33, 0x13a, 0x636, 0x73f, 0x435, 0x53c,\n\t0xa3c, 0xb35, 0x83f, 0x936, 0xe3a, 0xf33, 0xc39, 0xd30,\n\t0x3a0, 0x2a9, 0x1a3, 0xaa, 0x7a6, 0x6af, 0x5a5, 0x4ac,\n\t0xbac, 0xaa5, 0x9af, 0x8a6, 0xfaa, 0xea3, 0xda9, 0xca0,\n\t0x460, 0x569, 0x663, 0x76a, 0x66, 0x16f, 0x265, 0x36c,\n\t0xc6c, 0xd65, 0xe6f, 0xf66, 0x86a, 0x963, 0xa69, 0xb60,\n\t0x5f0, 0x4f9, 0x7f3, 0x6fa, 0x1f6, 0xff, 0x3f5, 0x2fc,\n\t0xdfc, 0xcf5, 0xfff, 0xef6, 0x9fa, 0x8f3, 0xbf9, 0xaf0,\n\t0x650, 0x759, 0x453, 0x55a, 0x256, 0x35f, 0x55, 0x15c,\n\t0xe5c, 0xf55, 0xc5f, 0xd56, 0xa5a, 0xb53, 0x859, 0x950,\n\t0x7c0, 0x6c9, 0x5c3, 0x4ca, 0x3c6, 0x2cf, 0x1c5, 0xcc,\n\t0xfcc, 0xec5, 0xdcf, 0xcc6, 0xbca, 0xac3, 0x9c9, 0x8c0,\n\t0x8c0, 0x9c9, 0xac3, 0xbca, 0xcc6, 0xdcf, 0xec5, 0xfcc,\n\t0xcc, 0x1c5, 0x2cf, 0x3c6, 0x4ca, 0x5c3, 0x6c9, 0x7c0,\n\t0x950, 0x859, 0xb53, 0xa5a, 0xd56, 0xc5f, 0xf55, 0xe5c,\n\t0x15c, 0x55, 0x35f, 0x256, 0x55a, 0x453, 0x759, 0x650,\n\t0xaf0, 0xbf9, 0x8f3, 0x9fa, 0xef6, 0xfff, 0xcf5, 0xdfc,\n\t0x2fc, 0x3f5, 0xff, 0x1f6, 0x6fa, 0x7f3, 0x4f9, 0x5f0,\n\t0xb60, 0xa69, 0x963, 0x86a, 0xf66, 0xe6f, 0xd65, 0xc6c,\n\t0x36c, 0x265, 0x16f, 0x66, 0x76a, 0x663, 0x569, 0x460,\n\t0xca0, 0xda9, 0xea3, 0xfaa, 0x8a6, 0x9af, 0xaa5, 0xbac,\n\t0x4ac, 0x5a5, 0x6af, 0x7a6, 0xaa, 0x1a3, 0x2a9, 0x3a0,\n\t0xd30, 0xc39, 0xf33, 0xe3a, 0x936, 0x83f, 0xb35, 0xa3c,\n\t0x53c, 0x435, 0x73f, 0x636, 0x13a, 0x33, 0x339, 0x230,\n\t0xe90, 0xf99, 0xc93, 0xd9a, 0xa96, 0xb9f, 0x895, 0x99c,\n\t0x69c, 0x795, 0x49f, 0x596, 0x29a, 0x393, 0x99, 0x190,\n\t0xf00, 0xe09, 0xd03, 0xc0a, 0xb06, 0xa0f, 0x905, 0x80c,\n\t0x70c, 0x605, 0x50f, 0x406, 0x30a, 0x203, 0x109, 0x0 ] );\n\n// prettier-ignore\nconst triTable = new Int32Array( [\n\t- 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 1, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 8, 3, 9, 8, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 3, 1, 2, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 2, 10, 0, 2, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 8, 3, 2, 10, 8, 10, 9, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 11, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 11, 2, 8, 11, 0, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 9, 0, 2, 3, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 11, 2, 1, 9, 11, 9, 8, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 10, 1, 11, 10, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 10, 1, 0, 8, 10, 8, 11, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 9, 0, 3, 11, 9, 11, 10, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 8, 10, 10, 8, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 7, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 3, 0, 7, 3, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 1, 9, 8, 4, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 1, 9, 4, 7, 1, 7, 3, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 10, 8, 4, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 4, 7, 3, 0, 4, 1, 2, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 2, 10, 9, 0, 2, 8, 4, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 10, 9, 2, 9, 7, 2, 7, 3, 7, 9, 4, - 1, - 1, - 1, - 1,\n\t8, 4, 7, 3, 11, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t11, 4, 7, 11, 2, 4, 2, 0, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 0, 1, 8, 4, 7, 2, 3, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 7, 11, 9, 4, 11, 9, 11, 2, 9, 2, 1, - 1, - 1, - 1, - 1,\n\t3, 10, 1, 3, 11, 10, 7, 8, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 11, 10, 1, 4, 11, 1, 0, 4, 7, 11, 4, - 1, - 1, - 1, - 1,\n\t4, 7, 8, 9, 0, 11, 9, 11, 10, 11, 0, 3, - 1, - 1, - 1, - 1,\n\t4, 7, 11, 4, 11, 9, 9, 11, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 5, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 5, 4, 0, 8, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 5, 4, 1, 5, 0, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t8, 5, 4, 8, 3, 5, 3, 1, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 10, 9, 5, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 0, 8, 1, 2, 10, 4, 9, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 2, 10, 5, 4, 2, 4, 0, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 10, 5, 3, 2, 5, 3, 5, 4, 3, 4, 8, - 1, - 1, - 1, - 1,\n\t9, 5, 4, 2, 3, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 11, 2, 0, 8, 11, 4, 9, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 5, 4, 0, 1, 5, 2, 3, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 1, 5, 2, 5, 8, 2, 8, 11, 4, 8, 5, - 1, - 1, - 1, - 1,\n\t10, 3, 11, 10, 1, 3, 9, 5, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 9, 5, 0, 8, 1, 8, 10, 1, 8, 11, 10, - 1, - 1, - 1, - 1,\n\t5, 4, 0, 5, 0, 11, 5, 11, 10, 11, 0, 3, - 1, - 1, - 1, - 1,\n\t5, 4, 8, 5, 8, 10, 10, 8, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 7, 8, 5, 7, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 3, 0, 9, 5, 3, 5, 7, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 7, 8, 0, 1, 7, 1, 5, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 5, 3, 3, 5, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 7, 8, 9, 5, 7, 10, 1, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 1, 2, 9, 5, 0, 5, 3, 0, 5, 7, 3, - 1, - 1, - 1, - 1,\n\t8, 0, 2, 8, 2, 5, 8, 5, 7, 10, 5, 2, - 1, - 1, - 1, - 1,\n\t2, 10, 5, 2, 5, 3, 3, 5, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t7, 9, 5, 7, 8, 9, 3, 11, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 5, 7, 9, 7, 2, 9, 2, 0, 2, 7, 11, - 1, - 1, - 1, - 1,\n\t2, 3, 11, 0, 1, 8, 1, 7, 8, 1, 5, 7, - 1, - 1, - 1, - 1,\n\t11, 2, 1, 11, 1, 7, 7, 1, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 5, 8, 8, 5, 7, 10, 1, 3, 10, 3, 11, - 1, - 1, - 1, - 1,\n\t5, 7, 0, 5, 0, 9, 7, 11, 0, 1, 0, 10, 11, 10, 0, - 1,\n\t11, 10, 0, 11, 0, 3, 10, 5, 0, 8, 0, 7, 5, 7, 0, - 1,\n\t11, 10, 5, 7, 11, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 6, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 3, 5, 10, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 0, 1, 5, 10, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 8, 3, 1, 9, 8, 5, 10, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 6, 5, 2, 6, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 6, 5, 1, 2, 6, 3, 0, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 6, 5, 9, 0, 6, 0, 2, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 9, 8, 5, 8, 2, 5, 2, 6, 3, 2, 8, - 1, - 1, - 1, - 1,\n\t2, 3, 11, 10, 6, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t11, 0, 8, 11, 2, 0, 10, 6, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 1, 9, 2, 3, 11, 5, 10, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 10, 6, 1, 9, 2, 9, 11, 2, 9, 8, 11, - 1, - 1, - 1, - 1,\n\t6, 3, 11, 6, 5, 3, 5, 1, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 11, 0, 11, 5, 0, 5, 1, 5, 11, 6, - 1, - 1, - 1, - 1,\n\t3, 11, 6, 0, 3, 6, 0, 6, 5, 0, 5, 9, - 1, - 1, - 1, - 1,\n\t6, 5, 9, 6, 9, 11, 11, 9, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 10, 6, 4, 7, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 3, 0, 4, 7, 3, 6, 5, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 9, 0, 5, 10, 6, 8, 4, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 6, 5, 1, 9, 7, 1, 7, 3, 7, 9, 4, - 1, - 1, - 1, - 1,\n\t6, 1, 2, 6, 5, 1, 4, 7, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 5, 5, 2, 6, 3, 0, 4, 3, 4, 7, - 1, - 1, - 1, - 1,\n\t8, 4, 7, 9, 0, 5, 0, 6, 5, 0, 2, 6, - 1, - 1, - 1, - 1,\n\t7, 3, 9, 7, 9, 4, 3, 2, 9, 5, 9, 6, 2, 6, 9, - 1,\n\t3, 11, 2, 7, 8, 4, 10, 6, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 10, 6, 4, 7, 2, 4, 2, 0, 2, 7, 11, - 1, - 1, - 1, - 1,\n\t0, 1, 9, 4, 7, 8, 2, 3, 11, 5, 10, 6, - 1, - 1, - 1, - 1,\n\t9, 2, 1, 9, 11, 2, 9, 4, 11, 7, 11, 4, 5, 10, 6, - 1,\n\t8, 4, 7, 3, 11, 5, 3, 5, 1, 5, 11, 6, - 1, - 1, - 1, - 1,\n\t5, 1, 11, 5, 11, 6, 1, 0, 11, 7, 11, 4, 0, 4, 11, - 1,\n\t0, 5, 9, 0, 6, 5, 0, 3, 6, 11, 6, 3, 8, 4, 7, - 1,\n\t6, 5, 9, 6, 9, 11, 4, 7, 9, 7, 11, 9, - 1, - 1, - 1, - 1,\n\t10, 4, 9, 6, 4, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 10, 6, 4, 9, 10, 0, 8, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 0, 1, 10, 6, 0, 6, 4, 0, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t8, 3, 1, 8, 1, 6, 8, 6, 4, 6, 1, 10, - 1, - 1, - 1, - 1,\n\t1, 4, 9, 1, 2, 4, 2, 6, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 0, 8, 1, 2, 9, 2, 4, 9, 2, 6, 4, - 1, - 1, - 1, - 1,\n\t0, 2, 4, 4, 2, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t8, 3, 2, 8, 2, 4, 4, 2, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 4, 9, 10, 6, 4, 11, 2, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 2, 2, 8, 11, 4, 9, 10, 4, 10, 6, - 1, - 1, - 1, - 1,\n\t3, 11, 2, 0, 1, 6, 0, 6, 4, 6, 1, 10, - 1, - 1, - 1, - 1,\n\t6, 4, 1, 6, 1, 10, 4, 8, 1, 2, 1, 11, 8, 11, 1, - 1,\n\t9, 6, 4, 9, 3, 6, 9, 1, 3, 11, 6, 3, - 1, - 1, - 1, - 1,\n\t8, 11, 1, 8, 1, 0, 11, 6, 1, 9, 1, 4, 6, 4, 1, - 1,\n\t3, 11, 6, 3, 6, 0, 0, 6, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t6, 4, 8, 11, 6, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t7, 10, 6, 7, 8, 10, 8, 9, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 7, 3, 0, 10, 7, 0, 9, 10, 6, 7, 10, - 1, - 1, - 1, - 1,\n\t10, 6, 7, 1, 10, 7, 1, 7, 8, 1, 8, 0, - 1, - 1, - 1, - 1,\n\t10, 6, 7, 10, 7, 1, 1, 7, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 6, 1, 6, 8, 1, 8, 9, 8, 6, 7, - 1, - 1, - 1, - 1,\n\t2, 6, 9, 2, 9, 1, 6, 7, 9, 0, 9, 3, 7, 3, 9, - 1,\n\t7, 8, 0, 7, 0, 6, 6, 0, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t7, 3, 2, 6, 7, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 3, 11, 10, 6, 8, 10, 8, 9, 8, 6, 7, - 1, - 1, - 1, - 1,\n\t2, 0, 7, 2, 7, 11, 0, 9, 7, 6, 7, 10, 9, 10, 7, - 1,\n\t1, 8, 0, 1, 7, 8, 1, 10, 7, 6, 7, 10, 2, 3, 11, - 1,\n\t11, 2, 1, 11, 1, 7, 10, 6, 1, 6, 7, 1, - 1, - 1, - 1, - 1,\n\t8, 9, 6, 8, 6, 7, 9, 1, 6, 11, 6, 3, 1, 3, 6, - 1,\n\t0, 9, 1, 11, 6, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t7, 8, 0, 7, 0, 6, 3, 11, 0, 11, 6, 0, - 1, - 1, - 1, - 1,\n\t7, 11, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t7, 6, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 0, 8, 11, 7, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 1, 9, 11, 7, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t8, 1, 9, 8, 3, 1, 11, 7, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 1, 2, 6, 11, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 10, 3, 0, 8, 6, 11, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 9, 0, 2, 10, 9, 6, 11, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t6, 11, 7, 2, 10, 3, 10, 8, 3, 10, 9, 8, - 1, - 1, - 1, - 1,\n\t7, 2, 3, 6, 2, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t7, 0, 8, 7, 6, 0, 6, 2, 0, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 7, 6, 2, 3, 7, 0, 1, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 6, 2, 1, 8, 6, 1, 9, 8, 8, 7, 6, - 1, - 1, - 1, - 1,\n\t10, 7, 6, 10, 1, 7, 1, 3, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 7, 6, 1, 7, 10, 1, 8, 7, 1, 0, 8, - 1, - 1, - 1, - 1,\n\t0, 3, 7, 0, 7, 10, 0, 10, 9, 6, 10, 7, - 1, - 1, - 1, - 1,\n\t7, 6, 10, 7, 10, 8, 8, 10, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t6, 8, 4, 11, 8, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 6, 11, 3, 0, 6, 0, 4, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t8, 6, 11, 8, 4, 6, 9, 0, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 4, 6, 9, 6, 3, 9, 3, 1, 11, 3, 6, - 1, - 1, - 1, - 1,\n\t6, 8, 4, 6, 11, 8, 2, 10, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 10, 3, 0, 11, 0, 6, 11, 0, 4, 6, - 1, - 1, - 1, - 1,\n\t4, 11, 8, 4, 6, 11, 0, 2, 9, 2, 10, 9, - 1, - 1, - 1, - 1,\n\t10, 9, 3, 10, 3, 2, 9, 4, 3, 11, 3, 6, 4, 6, 3, - 1,\n\t8, 2, 3, 8, 4, 2, 4, 6, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 4, 2, 4, 6, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 9, 0, 2, 3, 4, 2, 4, 6, 4, 3, 8, - 1, - 1, - 1, - 1,\n\t1, 9, 4, 1, 4, 2, 2, 4, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t8, 1, 3, 8, 6, 1, 8, 4, 6, 6, 10, 1, - 1, - 1, - 1, - 1,\n\t10, 1, 0, 10, 0, 6, 6, 0, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 6, 3, 4, 3, 8, 6, 10, 3, 0, 3, 9, 10, 9, 3, - 1,\n\t10, 9, 4, 6, 10, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 9, 5, 7, 6, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 3, 4, 9, 5, 11, 7, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 0, 1, 5, 4, 0, 7, 6, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t11, 7, 6, 8, 3, 4, 3, 5, 4, 3, 1, 5, - 1, - 1, - 1, - 1,\n\t9, 5, 4, 10, 1, 2, 7, 6, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t6, 11, 7, 1, 2, 10, 0, 8, 3, 4, 9, 5, - 1, - 1, - 1, - 1,\n\t7, 6, 11, 5, 4, 10, 4, 2, 10, 4, 0, 2, - 1, - 1, - 1, - 1,\n\t3, 4, 8, 3, 5, 4, 3, 2, 5, 10, 5, 2, 11, 7, 6, - 1,\n\t7, 2, 3, 7, 6, 2, 5, 4, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 5, 4, 0, 8, 6, 0, 6, 2, 6, 8, 7, - 1, - 1, - 1, - 1,\n\t3, 6, 2, 3, 7, 6, 1, 5, 0, 5, 4, 0, - 1, - 1, - 1, - 1,\n\t6, 2, 8, 6, 8, 7, 2, 1, 8, 4, 8, 5, 1, 5, 8, - 1,\n\t9, 5, 4, 10, 1, 6, 1, 7, 6, 1, 3, 7, - 1, - 1, - 1, - 1,\n\t1, 6, 10, 1, 7, 6, 1, 0, 7, 8, 7, 0, 9, 5, 4, - 1,\n\t4, 0, 10, 4, 10, 5, 0, 3, 10, 6, 10, 7, 3, 7, 10, - 1,\n\t7, 6, 10, 7, 10, 8, 5, 4, 10, 4, 8, 10, - 1, - 1, - 1, - 1,\n\t6, 9, 5, 6, 11, 9, 11, 8, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 6, 11, 0, 6, 3, 0, 5, 6, 0, 9, 5, - 1, - 1, - 1, - 1,\n\t0, 11, 8, 0, 5, 11, 0, 1, 5, 5, 6, 11, - 1, - 1, - 1, - 1,\n\t6, 11, 3, 6, 3, 5, 5, 3, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 10, 9, 5, 11, 9, 11, 8, 11, 5, 6, - 1, - 1, - 1, - 1,\n\t0, 11, 3, 0, 6, 11, 0, 9, 6, 5, 6, 9, 1, 2, 10, - 1,\n\t11, 8, 5, 11, 5, 6, 8, 0, 5, 10, 5, 2, 0, 2, 5, - 1,\n\t6, 11, 3, 6, 3, 5, 2, 10, 3, 10, 5, 3, - 1, - 1, - 1, - 1,\n\t5, 8, 9, 5, 2, 8, 5, 6, 2, 3, 8, 2, - 1, - 1, - 1, - 1,\n\t9, 5, 6, 9, 6, 0, 0, 6, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 5, 8, 1, 8, 0, 5, 6, 8, 3, 8, 2, 6, 2, 8, - 1,\n\t1, 5, 6, 2, 1, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 3, 6, 1, 6, 10, 3, 8, 6, 5, 6, 9, 8, 9, 6, - 1,\n\t10, 1, 0, 10, 0, 6, 9, 5, 0, 5, 6, 0, - 1, - 1, - 1, - 1,\n\t0, 3, 8, 5, 6, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 5, 6, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t11, 5, 10, 7, 5, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t11, 5, 10, 11, 7, 5, 8, 3, 0, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 11, 7, 5, 10, 11, 1, 9, 0, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t10, 7, 5, 10, 11, 7, 9, 8, 1, 8, 3, 1, - 1, - 1, - 1, - 1,\n\t11, 1, 2, 11, 7, 1, 7, 5, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 3, 1, 2, 7, 1, 7, 5, 7, 2, 11, - 1, - 1, - 1, - 1,\n\t9, 7, 5, 9, 2, 7, 9, 0, 2, 2, 11, 7, - 1, - 1, - 1, - 1,\n\t7, 5, 2, 7, 2, 11, 5, 9, 2, 3, 2, 8, 9, 8, 2, - 1,\n\t2, 5, 10, 2, 3, 5, 3, 7, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t8, 2, 0, 8, 5, 2, 8, 7, 5, 10, 2, 5, - 1, - 1, - 1, - 1,\n\t9, 0, 1, 5, 10, 3, 5, 3, 7, 3, 10, 2, - 1, - 1, - 1, - 1,\n\t9, 8, 2, 9, 2, 1, 8, 7, 2, 10, 2, 5, 7, 5, 2, - 1,\n\t1, 3, 5, 3, 7, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 7, 0, 7, 1, 1, 7, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 0, 3, 9, 3, 5, 5, 3, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 8, 7, 5, 9, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 8, 4, 5, 10, 8, 10, 11, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t5, 0, 4, 5, 11, 0, 5, 10, 11, 11, 3, 0, - 1, - 1, - 1, - 1,\n\t0, 1, 9, 8, 4, 10, 8, 10, 11, 10, 4, 5, - 1, - 1, - 1, - 1,\n\t10, 11, 4, 10, 4, 5, 11, 3, 4, 9, 4, 1, 3, 1, 4, - 1,\n\t2, 5, 1, 2, 8, 5, 2, 11, 8, 4, 5, 8, - 1, - 1, - 1, - 1,\n\t0, 4, 11, 0, 11, 3, 4, 5, 11, 2, 11, 1, 5, 1, 11, - 1,\n\t0, 2, 5, 0, 5, 9, 2, 11, 5, 4, 5, 8, 11, 8, 5, - 1,\n\t9, 4, 5, 2, 11, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 5, 10, 3, 5, 2, 3, 4, 5, 3, 8, 4, - 1, - 1, - 1, - 1,\n\t5, 10, 2, 5, 2, 4, 4, 2, 0, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 10, 2, 3, 5, 10, 3, 8, 5, 4, 5, 8, 0, 1, 9, - 1,\n\t5, 10, 2, 5, 2, 4, 1, 9, 2, 9, 4, 2, - 1, - 1, - 1, - 1,\n\t8, 4, 5, 8, 5, 3, 3, 5, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 4, 5, 1, 0, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t8, 4, 5, 8, 5, 3, 9, 0, 5, 0, 3, 5, - 1, - 1, - 1, - 1,\n\t9, 4, 5, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 11, 7, 4, 9, 11, 9, 10, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 8, 3, 4, 9, 7, 9, 11, 7, 9, 10, 11, - 1, - 1, - 1, - 1,\n\t1, 10, 11, 1, 11, 4, 1, 4, 0, 7, 4, 11, - 1, - 1, - 1, - 1,\n\t3, 1, 4, 3, 4, 8, 1, 10, 4, 7, 4, 11, 10, 11, 4, - 1,\n\t4, 11, 7, 9, 11, 4, 9, 2, 11, 9, 1, 2, - 1, - 1, - 1, - 1,\n\t9, 7, 4, 9, 11, 7, 9, 1, 11, 2, 11, 1, 0, 8, 3, - 1,\n\t11, 7, 4, 11, 4, 2, 2, 4, 0, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t11, 7, 4, 11, 4, 2, 8, 3, 4, 3, 2, 4, - 1, - 1, - 1, - 1,\n\t2, 9, 10, 2, 7, 9, 2, 3, 7, 7, 4, 9, - 1, - 1, - 1, - 1,\n\t9, 10, 7, 9, 7, 4, 10, 2, 7, 8, 7, 0, 2, 0, 7, - 1,\n\t3, 7, 10, 3, 10, 2, 7, 4, 10, 1, 10, 0, 4, 0, 10, - 1,\n\t1, 10, 2, 8, 7, 4, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 9, 1, 4, 1, 7, 7, 1, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 9, 1, 4, 1, 7, 0, 8, 1, 8, 7, 1, - 1, - 1, - 1, - 1,\n\t4, 0, 3, 7, 4, 3, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t4, 8, 7, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 10, 8, 10, 11, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 0, 9, 3, 9, 11, 11, 9, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 1, 10, 0, 10, 8, 8, 10, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 1, 10, 11, 3, 10, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 2, 11, 1, 11, 9, 9, 11, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 0, 9, 3, 9, 11, 1, 2, 9, 2, 11, 9, - 1, - 1, - 1, - 1,\n\t0, 2, 11, 8, 0, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t3, 2, 11, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 3, 8, 2, 8, 10, 10, 8, 9, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t9, 10, 2, 0, 9, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t2, 3, 8, 2, 8, 10, 0, 1, 8, 1, 10, 8, - 1, - 1, - 1, - 1,\n\t1, 10, 2, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t1, 3, 8, 9, 1, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 9, 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t0, 3, 8, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1,\n\t- 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1, - 1 ] );\n\nexport { MarchingCubes, edgeTable, triTable }\n"], "names": ["resolution"], "mappings": ";;;;;;;AAMA,MAAM,6NAAsB,OAAA,CAAK;IAC/B,YAAY,UAAA,EAAY,QAAA,EAAU,YAAY,KAAA,EAAO,eAAe,KAAA,EAAO,eAAe,GAAA,CAAO;QAC/F,MAAM,WAAW,2MAAI,iBAAA,CAAgB;QAErC,KAAA,CAAM,UAAU,QAAQ;QAExB,IAAA,CAAK,eAAA,GAAkB;QAEvB,MAAM,QAAQ,IAAA;QAId,MAAM,QAAQ,IAAI,aAAa,KAAK,CAAC;QACrC,MAAM,QAAQ,IAAI,aAAa,KAAK,CAAC;QACrC,MAAM,QAAQ,IAAI,aAAa,KAAK,CAAC;QAErC,IAAA,CAAK,SAAA,GAAY;QACjB,IAAA,CAAK,YAAA,GAAe;QAMpB,IAAA,CAAK,IAAA,GAAO,SAAUA,WAAAA,EAAY;YAChC,IAAA,CAAK,UAAA,GAAaA;YAIlB,IAAA,CAAK,SAAA,GAAY;YAIjB,IAAA,CAAK,IAAA,GAAOA;YACZ,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,IAAA,GAAO,IAAA,CAAK,IAAA;YAC9B,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,IAAA;YAC/B,IAAA,CAAK,QAAA,GAAW,IAAA,CAAK,IAAA,GAAO;YAI5B,IAAA,CAAK,KAAA,GAAQ,IAAM,IAAA,CAAK,IAAA;YACxB,IAAA,CAAK,EAAA,GAAK,IAAA,CAAK,IAAA;YACf,IAAA,CAAK,EAAA,GAAK,IAAA,CAAK,KAAA;YAEf,IAAA,CAAK,KAAA,GAAQ,IAAI,aAAa,IAAA,CAAK,KAAK;YACxC,IAAA,CAAK,YAAA,GAAe,IAAI,aAAa,IAAA,CAAK,KAAA,GAAQ,CAAC;YACnD,IAAA,CAAK,OAAA,GAAU,IAAI,aAAa,IAAA,CAAK,KAAA,GAAQ,CAAC;YAI9C,IAAA,CAAK,KAAA,GAAQ;YAEb,MAAM,iBAAiB,eAAe;YAEtC,IAAA,CAAK,aAAA,GAAgB,IAAI,aAAa,iBAAiB,CAAC;YACxD,MAAM,oBAAoB,2MAAI,kBAAA,CAAgB,IAAA,CAAK,aAAA,EAAe,CAAC;YACnE,kBAAkB,QAAA,wMAAS,mBAAgB;YAC3C,SAAS,YAAA,CAAa,YAAY,iBAAiB;YAEnD,IAAA,CAAK,WAAA,GAAc,IAAI,aAAa,iBAAiB,CAAC;YACtD,MAAM,kBAAkB,2MAAI,kBAAA,CAAgB,IAAA,CAAK,WAAA,EAAa,CAAC;YAC/D,gBAAgB,QAAA,wMAAS,mBAAgB;YACzC,SAAS,YAAA,CAAa,UAAU,eAAe;YAE/C,IAAI,IAAA,CAAK,SAAA,EAAW;gBAClB,IAAA,CAAK,OAAA,GAAU,IAAI,aAAa,iBAAiB,CAAC;gBAClD,MAAM,cAAc,2MAAI,kBAAA,CAAgB,IAAA,CAAK,OAAA,EAAS,CAAC;gBACvD,YAAY,QAAA,wMAAS,mBAAgB;gBACrC,SAAS,YAAA,CAAa,MAAM,WAAW;YACxC;YAED,IAAI,IAAA,CAAK,YAAA,EAAc;gBACrB,IAAA,CAAK,UAAA,GAAa,IAAI,aAAa,iBAAiB,CAAC;gBACrD,MAAM,iBAAiB,2MAAI,kBAAA,CAAgB,IAAA,CAAK,UAAA,EAAY,CAAC;gBAC7D,eAAe,QAAA,wMAAS,mBAAgB;gBACxC,SAAS,YAAA,CAAa,SAAS,cAAc;YAC9C;YAED,SAAS,cAAA,GAAiB,2MAAI,SAAA,CAAO,2MAAI,UAAA,CAAO,GAAI,CAAC;QACtD;QAMD,SAAS,KAAK,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;YACrB,OAAO,IAAA,CAAK,IAAI,CAAA,IAAK;QACtB;QAED,SAAS,MAAM,CAAA,EAAG,MAAA,EAAQ,IAAA,EAAM,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG,KAAA,EAAO,KAAA,EAAO,SAAA,EAAW,SAAA,EAAW;YAC3E,MAAM,KAAA,CAAM,OAAO,KAAA,IAAA,CAAU,QAAQ,KAAA,GACnC,KAAK,MAAM,YAAA;YAEb,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,IAAI,KAAK,MAAM,KAAA;YACnC,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI;YACpB,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI;YAEpB,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,EAAA,CAAG,IAAI,CAAC,CAAA,EAAG,EAAA,CAAG,IAAI,CAAC,CAAA,EAAG,EAAE;YACjD,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,EAAA,CAAG,IAAI,CAAC,CAAA,EAAG,EAAA,CAAG,IAAI,CAAC,CAAA,EAAG,EAAE;YACjD,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,EAAA,CAAG,IAAI,CAAC,CAAA,EAAG,EAAA,CAAG,IAAI,CAAC,CAAA,EAAG,EAAE;YAEjD,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,EAAE;YAC/F,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,EAAE;YAC/F,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,EAAE;QAChG;QAED,SAAS,MAAM,CAAA,EAAG,MAAA,EAAQ,IAAA,EAAM,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG,KAAA,EAAO,KAAA,EAAO,SAAA,EAAW,SAAA,EAAW;YAC3E,MAAM,KAAA,CAAM,OAAO,KAAA,IAAA,CAAU,QAAQ,KAAA,GACnC,KAAK,MAAM,YAAA;YAEb,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI;YACpB,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,IAAI,KAAK,MAAM,KAAA;YACnC,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI;YAEpB,MAAM,KAAK,IAAI,MAAM,EAAA,GAAK;YAE1B,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,EAAA,CAAG,IAAI,CAAC,CAAA,EAAG,EAAA,CAAG,KAAK,CAAC,CAAA,EAAG,EAAE;YAClD,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,EAAA,CAAG,IAAI,CAAC,CAAA,EAAG,EAAA,CAAG,KAAK,CAAC,CAAA,EAAG,EAAE;YAClD,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,EAAA,CAAG,IAAI,CAAC,CAAA,EAAG,EAAA,CAAG,KAAK,CAAC,CAAA,EAAG,EAAE;YAElD,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,EAAE;YAC/F,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,EAAE;YAC/F,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,EAAE;QAChG;QAED,SAAS,MAAM,CAAA,EAAG,MAAA,EAAQ,IAAA,EAAM,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG,KAAA,EAAO,KAAA,EAAO,SAAA,EAAW,SAAA,EAAW;YAC3E,MAAM,KAAA,CAAM,OAAO,KAAA,IAAA,CAAU,QAAQ,KAAA,GACnC,KAAK,MAAM,YAAA;YAEb,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI;YACpB,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI;YACpB,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,IAAI,KAAK,MAAM,KAAA;YAEnC,MAAM,KAAK,IAAI,MAAM,EAAA,GAAK;YAE1B,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,EAAA,CAAG,IAAI,CAAC,CAAA,EAAG,EAAA,CAAG,KAAK,CAAC,CAAA,EAAG,EAAE;YAClD,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,EAAA,CAAG,IAAI,CAAC,CAAA,EAAG,EAAA,CAAG,KAAK,CAAC,CAAA,EAAG,EAAE;YAClD,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,EAAA,CAAG,IAAI,CAAC,CAAA,EAAG,EAAA,CAAG,KAAK,CAAC,CAAA,EAAG,EAAE;YAElD,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,EAAE;YAC/F,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,EAAE;YAC/F,KAAA,CAAM,SAAS,CAAC,CAAA,GAAI,KAAK,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,MAAM,OAAA,CAAQ,YAAY,IAAI,CAAC,CAAA,EAAG,EAAE;QAChG;QAED,SAAS,SAAS,CAAA,EAAG;YACnB,MAAM,KAAK,IAAI;YAEf,IAAI,MAAM,YAAA,CAAa,EAAE,CAAA,KAAM,GAAK;gBAClC,MAAM,YAAA,CAAa,KAAK,CAAC,CAAA,GAAI,MAAM,KAAA,CAAM,IAAI,CAAC,CAAA,GAAI,MAAM,KAAA,CAAM,IAAI,CAAC,CAAA;gBACnE,MAAM,YAAA,CAAa,KAAK,CAAC,CAAA,GAAI,MAAM,KAAA,CAAM,IAAI,MAAM,EAAE,CAAA,GAAI,MAAM,KAAA,CAAM,IAAI,MAAM,EAAE,CAAA;gBACjF,MAAM,YAAA,CAAa,KAAK,CAAC,CAAA,GAAI,MAAM,KAAA,CAAM,IAAI,MAAM,EAAE,CAAA,GAAI,MAAM,KAAA,CAAM,IAAI,MAAM,EAAE,CAAA;YAClF;QACF;QAKD,SAAS,WAAW,EAAA,EAAI,EAAA,EAAI,EAAA,EAAI,CAAA,EAAG,IAAA,EAAM;YAEvC,MAAM,KAAK,IAAI,GACb,KAAK,IAAI,MAAM,EAAA,EACf,KAAK,IAAI,MAAM,EAAA,EACf,MAAM,KAAK,MAAM,EAAA,EACjB,MAAM,KAAK,MAAM,EAAA,EACjB,MAAM,IAAI,MAAM,EAAA,GAAK,MAAM,EAAA,EAC3B,OAAO,KAAK,MAAM,EAAA,GAAK,MAAM,EAAA;YAE/B,IAAI,YAAY;YAChB,MAAM,SAAS,MAAM,KAAA,CAAM,CAAC,CAAA,EAC1B,SAAS,MAAM,KAAA,CAAM,EAAE,CAAA,EACvB,SAAS,MAAM,KAAA,CAAM,EAAE,CAAA,EACvB,SAAS,MAAM,KAAA,CAAM,GAAG,CAAA,EACxB,SAAS,MAAM,KAAA,CAAM,EAAE,CAAA,EACvB,SAAS,MAAM,KAAA,CAAM,GAAG,CAAA,EACxB,SAAS,MAAM,KAAA,CAAM,GAAG,CAAA,EACxB,SAAS,MAAM,KAAA,CAAM,IAAI,CAAA;YAE3B,IAAI,SAAS,MAAM,aAAa;YAChC,IAAI,SAAS,MAAM,aAAa;YAChC,IAAI,SAAS,MAAM,aAAa;YAChC,IAAI,SAAS,MAAM,aAAa;YAChC,IAAI,SAAS,MAAM,aAAa;YAChC,IAAI,SAAS,MAAM,aAAa;YAChC,IAAI,SAAS,MAAM,aAAa;YAChC,IAAI,SAAS,MAAM,aAAa;YAIhC,MAAM,OAAO,SAAA,CAAU,SAAS,CAAA;YAChC,IAAI,SAAS,GAAG,OAAO;YAEvB,MAAM,IAAI,MAAM,KAAA,EACd,MAAM,KAAK,GACX,MAAM,KAAK,GACX,MAAM,KAAK;YAIb,IAAI,OAAO,GAAG;gBACZ,SAAS,CAAC;gBACV,SAAS,EAAE;gBACX,MAAM,IAAI,GAAG,GAAG,MAAM,IAAI,IAAI,IAAI,QAAQ,QAAQ,GAAG,EAAE;YACxD;YAED,IAAI,OAAO,GAAG;gBACZ,SAAS,EAAE;gBACX,SAAS,GAAG;gBACZ,MAAM,KAAK,GAAG,GAAG,MAAM,KAAK,IAAI,IAAI,QAAQ,QAAQ,IAAI,GAAG;YAC5D;YAED,IAAI,OAAO,GAAG;gBACZ,SAAS,EAAE;gBACX,SAAS,GAAG;gBACZ,MAAM,KAAK,GAAG,GAAG,MAAM,IAAI,KAAK,IAAI,QAAQ,QAAQ,IAAI,GAAG;YAC5D;YAED,IAAI,OAAO,GAAG;gBACZ,SAAS,CAAC;gBACV,SAAS,EAAE;gBACX,MAAM,IAAI,GAAG,GAAG,MAAM,IAAI,IAAI,IAAI,QAAQ,QAAQ,GAAG,EAAE;YACxD;YAID,IAAI,OAAO,IAAI;gBACb,SAAS,EAAE;gBACX,SAAS,GAAG;gBACZ,MAAM,KAAK,GAAG,IAAI,MAAM,IAAI,IAAI,KAAK,QAAQ,QAAQ,IAAI,GAAG;YAC7D;YAED,IAAI,OAAO,IAAI;gBACb,SAAS,GAAG;gBACZ,SAAS,IAAI;gBACb,MAAM,MAAM,GAAG,IAAI,MAAM,KAAK,IAAI,KAAK,QAAQ,QAAQ,KAAK,IAAI;YACjE;YAED,IAAI,OAAO,IAAI;gBACb,SAAS,GAAG;gBACZ,SAAS,IAAI;gBACb,MAAM,MAAM,GAAG,IAAI,MAAM,IAAI,KAAK,KAAK,QAAQ,QAAQ,KAAK,IAAI;YACjE;YAED,IAAI,OAAO,KAAK;gBACd,SAAS,EAAE;gBACX,SAAS,GAAG;gBACZ,MAAM,KAAK,GAAG,IAAI,MAAM,IAAI,IAAI,KAAK,QAAQ,QAAQ,IAAI,GAAG;YAC7D;YAGD,IAAI,OAAO,KAAK;gBACd,SAAS,CAAC;gBACV,SAAS,EAAE;gBACX,MAAM,IAAI,GAAG,IAAI,MAAM,IAAI,IAAI,IAAI,QAAQ,QAAQ,GAAG,EAAE;YACzD;YAED,IAAI,OAAO,KAAK;gBACd,SAAS,EAAE;gBACX,SAAS,GAAG;gBACZ,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,IAAI,IAAI,QAAQ,QAAQ,IAAI,GAAG;YAC7D;YAED,IAAI,OAAO,MAAM;gBACf,SAAS,GAAG;gBACZ,SAAS,IAAI;gBACb,MAAM,MAAM,GAAG,IAAI,MAAM,KAAK,KAAK,IAAI,QAAQ,QAAQ,KAAK,IAAI;YACjE;YAED,IAAI,OAAO,MAAM;gBACf,SAAS,EAAE;gBACX,SAAS,GAAG;gBACZ,MAAM,KAAK,GAAG,IAAI,MAAM,IAAI,KAAK,IAAI,QAAQ,QAAQ,IAAI,GAAG;YAC7D;YAED,cAAc;YAEd,IAAI,IACF,IACA,IACA,UAAU,GACV,IAAI;YAIN,MAAO,QAAA,CAAS,YAAY,CAAC,CAAA,IAAK,CAAA,EAAI;gBACpC,KAAK,YAAY;gBACjB,KAAK,KAAK;gBACV,KAAK,KAAK;gBAEV,YAAY,OAAO,OAAO,OAAO,IAAI,QAAA,CAAS,EAAE,CAAA,EAAG,IAAI,QAAA,CAAS,EAAE,CAAA,EAAG,IAAI,QAAA,CAAS,EAAE,CAAC;gBAErF,KAAK;gBACL;YACD;YAED,OAAO;QACR;QAED,SAAS,YAAY,GAAA,EAAK,IAAA,EAAM,MAAA,EAAQ,EAAA,EAAI,EAAA,EAAI,EAAA,EAAI;YAClD,MAAM,IAAI,MAAM,KAAA,GAAQ;YAIxB,MAAM,aAAA,CAAc,IAAI,CAAC,CAAA,GAAI,GAAA,CAAI,EAAE,CAAA;YACnC,MAAM,aAAA,CAAc,IAAI,CAAC,CAAA,GAAI,GAAA,CAAI,KAAK,CAAC,CAAA;YACvC,MAAM,aAAA,CAAc,IAAI,CAAC,CAAA,GAAI,GAAA,CAAI,KAAK,CAAC,CAAA;YAEvC,MAAM,aAAA,CAAc,IAAI,CAAC,CAAA,GAAI,GAAA,CAAI,EAAE,CAAA;YACnC,MAAM,aAAA,CAAc,IAAI,CAAC,CAAA,GAAI,GAAA,CAAI,KAAK,CAAC,CAAA;YACvC,MAAM,aAAA,CAAc,IAAI,CAAC,CAAA,GAAI,GAAA,CAAI,KAAK,CAAC,CAAA;YAEvC,MAAM,aAAA,CAAc,IAAI,CAAC,CAAA,GAAI,GAAA,CAAI,EAAE,CAAA;YACnC,MAAM,aAAA,CAAc,IAAI,CAAC,CAAA,GAAI,GAAA,CAAI,KAAK,CAAC,CAAA;YACvC,MAAM,aAAA,CAAc,IAAI,CAAC,CAAA,GAAI,GAAA,CAAI,KAAK,CAAC,CAAA;YAIvC,IAAI,MAAM,QAAA,CAAS,WAAA,KAAgB,MAAM;gBACvC,MAAM,KAAA,CAAM,IAAA,CAAK,KAAK,CAAC,CAAA,GAAI,IAAA,CAAK,KAAK,CAAC,CAAA,GAAI,IAAA,CAAK,KAAK,CAAC,CAAA,IAAK;gBAC1D,MAAM,KAAA,CAAM,IAAA,CAAK,KAAK,CAAC,CAAA,GAAI,IAAA,CAAK,KAAK,CAAC,CAAA,GAAI,IAAA,CAAK,KAAK,CAAC,CAAA,IAAK;gBAC1D,MAAM,KAAA,CAAM,IAAA,CAAK,KAAK,CAAC,CAAA,GAAI,IAAA,CAAK,KAAK,CAAC,CAAA,GAAI,IAAA,CAAK,KAAK,CAAC,CAAA,IAAK;gBAE1D,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI;gBAC3B,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI;gBAC3B,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI;gBAE3B,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI;gBAC3B,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI;gBAC3B,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI;gBAE3B,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI;gBAC3B,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI;gBAC3B,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI;YACnC,OAAa;gBACL,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI,IAAA,CAAK,KAAK,CAAC,CAAA;gBACtC,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI,IAAA,CAAK,KAAK,CAAC,CAAA;gBACtC,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI,IAAA,CAAK,KAAK,CAAC,CAAA;gBAEtC,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI,IAAA,CAAK,KAAK,CAAC,CAAA;gBACtC,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI,IAAA,CAAK,KAAK,CAAC,CAAA;gBACtC,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI,IAAA,CAAK,KAAK,CAAC,CAAA;gBAEtC,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI,IAAA,CAAK,KAAK,CAAC,CAAA;gBACtC,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI,IAAA,CAAK,KAAK,CAAC,CAAA;gBACtC,MAAM,WAAA,CAAY,IAAI,CAAC,CAAA,GAAI,IAAA,CAAK,KAAK,CAAC,CAAA;YACvC;YAID,IAAI,MAAM,SAAA,EAAW;gBACnB,MAAM,IAAI,MAAM,KAAA,GAAQ;gBAExB,MAAM,OAAA,CAAQ,IAAI,CAAC,CAAA,GAAI,GAAA,CAAI,KAAK,CAAC,CAAA;gBACjC,MAAM,OAAA,CAAQ,IAAI,CAAC,CAAA,GAAI,GAAA,CAAI,KAAK,CAAC,CAAA;gBAEjC,MAAM,OAAA,CAAQ,IAAI,CAAC,CAAA,GAAI,GAAA,CAAI,KAAK,CAAC,CAAA;gBACjC,MAAM,OAAA,CAAQ,IAAI,CAAC,CAAA,GAAI,GAAA,CAAI,KAAK,CAAC,CAAA;gBAEjC,MAAM,OAAA,CAAQ,IAAI,CAAC,CAAA,GAAI,GAAA,CAAI,KAAK,CAAC,CAAA;gBACjC,MAAM,OAAA,CAAQ,IAAI,CAAC,CAAA,GAAI,GAAA,CAAI,KAAK,CAAC,CAAA;YAClC;YAID,IAAI,MAAM,YAAA,EAAc;gBACtB,MAAM,UAAA,CAAW,IAAI,CAAC,CAAA,GAAI,MAAA,CAAO,KAAK,CAAC,CAAA;gBACvC,MAAM,UAAA,CAAW,IAAI,CAAC,CAAA,GAAI,MAAA,CAAO,KAAK,CAAC,CAAA;gBACvC,MAAM,UAAA,CAAW,IAAI,CAAC,CAAA,GAAI,MAAA,CAAO,KAAK,CAAC,CAAA;gBAEvC,MAAM,UAAA,CAAW,IAAI,CAAC,CAAA,GAAI,MAAA,CAAO,KAAK,CAAC,CAAA;gBACvC,MAAM,UAAA,CAAW,IAAI,CAAC,CAAA,GAAI,MAAA,CAAO,KAAK,CAAC,CAAA;gBACvC,MAAM,UAAA,CAAW,IAAI,CAAC,CAAA,GAAI,MAAA,CAAO,KAAK,CAAC,CAAA;gBAEvC,MAAM,UAAA,CAAW,IAAI,CAAC,CAAA,GAAI,MAAA,CAAO,KAAK,CAAC,CAAA;gBACvC,MAAM,UAAA,CAAW,IAAI,CAAC,CAAA,GAAI,MAAA,CAAO,KAAK,CAAC,CAAA;gBACvC,MAAM,UAAA,CAAW,IAAI,CAAC,CAAA,GAAI,MAAA,CAAO,KAAK,CAAC,CAAA;YACxC;YAED,MAAM,KAAA,IAAS;QAChB;QASD,IAAA,CAAK,OAAA,GAAU,SAAU,KAAA,EAAO,KAAA,EAAO,KAAA,EAAO,QAAA,EAAU,QAAA,EAAU,MAAA,EAAQ;YACxE,MAAM,OAAO,KAAK,IAAA,CAAK,QAAQ;YAC/B,WAAW,KAAK,GAAA,CAAI,QAAQ;YAC5B,MAAM,kBAAkB,CAAA,CAAE,WAAW,KAAA,KAAa,WAAW,IAAA;YAC7D,IAAI,YAAY,2MAAI,QAAA,CAAM,OAAO,OAAO,KAAK;YAE7C,IAAI,iBAAiB;gBACnB,IAAI;oBACF,YACE,yNAAkB,QAAA,GACd,SACA,MAAM,OAAA,CAAQ,MAAM,IACpB,2MAAI,QAAA,CACF,KAAK,GAAA,CAAI,KAAK,GAAA,CAAI,MAAA,CAAO,CAAC,CAAC,GAAG,CAAC,GAC/B,KAAK,GAAA,CAAI,KAAK,GAAA,CAAI,MAAA,CAAO,CAAC,CAAC,GAAG,CAAC,GAC/B,KAAK,GAAA,CAAI,KAAK,GAAA,CAAI,MAAA,CAAO,CAAC,CAAC,GAAG,CAAC,KAEjC,2MAAI,QAAA,CAAM,MAAM;gBACvB,EAAA,OAAQ,KAAP;oBACA,YAAY,2MAAI,QAAA,CAAM,OAAO,OAAO,KAAK;gBAC1C;YACF;YASD,MAAM,SAAS,IAAA,CAAK,IAAA,GAAO,KAAK,IAAA,CAAK,WAAW,QAAQ,GACtD,KAAK,QAAQ,IAAA,CAAK,IAAA,EAClB,KAAK,QAAQ,IAAA,CAAK,IAAA,EAClB,KAAK,QAAQ,IAAA,CAAK,IAAA;YAEpB,IAAI,QAAQ,KAAK,KAAA,CAAM,KAAK,MAAM;YAClC,IAAI,QAAQ,GAAG,QAAQ;YACvB,IAAI,QAAQ,KAAK,KAAA,CAAM,KAAK,MAAM;YAClC,IAAI,QAAQ,IAAA,CAAK,IAAA,GAAO,GAAG,QAAQ,IAAA,CAAK,IAAA,GAAO;YAC/C,IAAI,QAAQ,KAAK,KAAA,CAAM,KAAK,MAAM;YAClC,IAAI,QAAQ,GAAG,QAAQ;YACvB,IAAI,QAAQ,KAAK,KAAA,CAAM,KAAK,MAAM;YAClC,IAAI,QAAQ,IAAA,CAAK,IAAA,GAAO,GAAG,QAAQ,IAAA,CAAK,IAAA,GAAO;YAC/C,IAAI,QAAQ,KAAK,KAAA,CAAM,KAAK,MAAM;YAClC,IAAI,QAAQ,GAAG,QAAQ;YACvB,IAAI,QAAQ,KAAK,KAAA,CAAM,KAAK,MAAM;YAClC,IAAI,QAAQ,IAAA,CAAK,IAAA,GAAO,GAAG,QAAQ,IAAA,CAAK,IAAA,GAAO;YAK/C,IAAI,GAAG,GAAG,GAAG,UAAU,UAAU,IAAI,IAAI,IAAI,KAAK,KAAK;YAEvD,IAAK,IAAI,OAAO,IAAI,OAAO,IAAK;gBAC9B,WAAW,IAAA,CAAK,KAAA,GAAQ;gBACxB,KAAK,IAAI,IAAA,CAAK,IAAA,GAAO;gBACrB,MAAM,KAAK;gBAEX,IAAK,IAAI,OAAO,IAAI,OAAO,IAAK;oBAC9B,WAAW,WAAW,IAAA,CAAK,IAAA,GAAO;oBAClC,KAAK,IAAI,IAAA,CAAK,IAAA,GAAO;oBACrB,MAAM,KAAK;oBAEX,IAAK,IAAI,OAAO,IAAI,OAAO,IAAK;wBAC9B,KAAK,IAAI,IAAA,CAAK,IAAA,GAAO;wBACrB,MAAM,WAAA,CAAY,OAAW,KAAK,KAAK,MAAM,GAAA,IAAO;wBACpD,IAAI,MAAM,GAAK;4BACb,IAAA,CAAK,KAAA,CAAM,WAAW,CAAC,CAAA,IAAK,MAAM;4BAIlC,MAAM,QAAQ,KAAK,IAAA,CAAA,CAAM,IAAI,EAAA,IAAA,CAAO,IAAI,EAAA,IAAA,CAAO,IAAI,EAAA,IAAA,CAAO,IAAI,EAAA,IAAA,CAAO,IAAI,EAAA,IAAA,CAAO,IAAI,EAAA,CAAG,IAAI;4BAC3F,MAAM,UAAU,IAAI,QAAQ,QAAQ,QAAA,CAAS,QAAA,CAAS,QAAQ,IAAI,EAAA,IAAM,EAAA;4BACxE,IAAA,CAAK,OAAA,CAAA,CAAS,WAAW,CAAA,IAAK,IAAI,CAAC,CAAA,IAAK,UAAU,CAAA,GAAI;4BACtD,IAAA,CAAK,OAAA,CAAA,CAAS,WAAW,CAAA,IAAK,IAAI,CAAC,CAAA,IAAK,UAAU,CAAA,GAAI;4BACtD,IAAA,CAAK,OAAA,CAAA,CAAS,WAAW,CAAA,IAAK,IAAI,CAAC,CAAA,IAAK,UAAU,CAAA,GAAI;wBACvD;oBACF;gBACF;YACF;QACF;QAED,IAAA,CAAK,SAAA,GAAY,SAAU,QAAA,EAAU,QAAA,EAAU;YAE7C,MAAM,OAAO,IAAA,CAAK,IAAA,EAChB,KAAK,IAAA,CAAK,EAAA,EACV,KAAK,IAAA,CAAK,EAAA,EACV,QAAQ,IAAA,CAAK,KAAA;YAEf,IAAI,GACF,GACA,GACA,IACA,KACA,MACA,KACA,OAAO,OAAO,KAAK,IAAA,CAAK,WAAW,QAAQ;YAE7C,IAAI,OAAO,MAAM,OAAO;YAExB,IAAK,IAAI,GAAG,IAAI,MAAM,IAAK;gBACzB,OAAO,IAAI;gBACX,KAAK,OAAO;gBACZ,MAAM,WAAA,CAAY,OAAS,EAAA,IAAM;gBAEjC,IAAI,MAAM,GAAK;oBACb,IAAK,IAAI,GAAG,IAAI,MAAM,IAAK;wBACzB,MAAM,IAAI,IAAI;wBAEd,IAAK,IAAI,GAAG,IAAI,MAAM,IAAK;4BACzB,KAAA,CAAM,KAAK,IAAI,GAAG,CAAA,IAAK;wBACxB;oBACF;gBACF;YACF;QACF;QAED,IAAA,CAAK,SAAA,GAAY,SAAU,QAAA,EAAU,QAAA,EAAU;YAE7C,MAAM,OAAO,IAAA,CAAK,IAAA,EAChB,KAAK,IAAA,CAAK,EAAA,EACV,KAAK,IAAA,CAAK,EAAA,EACV,QAAQ,IAAA,CAAK,KAAA;YAEf,IAAI,GACF,GACA,GACA,IACA,KACA,MACA,IACA,KACA,OAAO,OAAO,KAAK,IAAA,CAAK,WAAW,QAAQ;YAE7C,IAAI,OAAO,MAAM,OAAO;YAExB,IAAK,IAAI,GAAG,IAAI,MAAM,IAAK;gBACzB,OAAO,IAAI;gBACX,KAAK,OAAO;gBACZ,MAAM,WAAA,CAAY,OAAS,EAAA,IAAM;gBAEjC,IAAI,MAAM,GAAK;oBACb,KAAK,IAAI;oBAET,IAAK,IAAI,GAAG,IAAI,MAAM,IAAK;wBACzB,MAAM,KAAK;wBAEX,IAAK,IAAI,GAAG,IAAI,MAAM,IAAK,KAAA,CAAM,KAAK,IAAI,GAAG,CAAA,IAAK;oBACnD;gBACF;YACF;QACF;QAED,IAAA,CAAK,SAAA,GAAY,SAAU,QAAA,EAAU,QAAA,EAAU;YAG7C,MAAM,OAAO,IAAA,CAAK,IAAA,EAChB,KAAK,IAAA,CAAK,EAAA,EACV,KAAK,IAAA,CAAK,EAAA,EACV,QAAQ,IAAA,CAAK,KAAA;YAEf,IAAI,GACF,GACA,GACA,IACA,KACA,MACA,IACA,KACA,OAAO,OAAO,KAAK,IAAA,CAAK,WAAW,QAAQ;YAE7C,IAAI,OAAO,MAAM,OAAO;YAExB,IAAK,IAAI,GAAG,IAAI,MAAM,IAAK;gBACzB,OAAO,IAAI;gBACX,KAAK,OAAO;gBACZ,MAAM,WAAA,CAAY,OAAS,EAAA,IAAM;gBACjC,IAAI,MAAM,GAAK;oBACb,KAAK,KAAK;oBAEV,IAAK,IAAI,GAAG,IAAI,MAAM,IAAK;wBACzB,MAAM,KAAK,IAAI;wBAEf,IAAK,IAAI,GAAG,IAAI,MAAM,IAAK,KAAA,CAAM,MAAM,CAAC,CAAA,IAAK;oBAC9C;gBACF;YACF;QACF;QAMD,IAAA,CAAK,OAAA,GAAU,SAAU,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG,KAAA,EAAO;YACvC,MAAM,QAAQ,IAAA,CAAK,KAAA,GAAQ,IAAI,IAAA,CAAK,IAAA,GAAO,IAAI;YAC/C,IAAA,CAAK,KAAA,CAAM,KAAK,CAAA,GAAI;QACrB;QAED,IAAA,CAAK,OAAA,GAAU,SAAU,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;YAChC,MAAM,QAAQ,IAAA,CAAK,KAAA,GAAQ,IAAI,IAAA,CAAK,IAAA,GAAO,IAAI;YAC/C,OAAO,IAAA,CAAK,KAAA,CAAM,KAAK,CAAA;QACxB;QAED,IAAA,CAAK,IAAA,GAAO,SAAU,YAAY,CAAA,EAAG;YACnC,MAAM,QAAQ,IAAA,CAAK,KAAA;YACnB,MAAM,YAAY,MAAM,KAAA,CAAO;YAC/B,MAAM,OAAO,IAAA,CAAK,IAAA;YAClB,MAAM,QAAQ,IAAA,CAAK,KAAA;YACnB,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,IAAK;gBAC7B,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,IAAK;oBAC7B,IAAA,IAAS,IAAI,GAAG,IAAI,MAAM,IAAK;wBAC7B,MAAM,QAAQ,QAAQ,IAAI,OAAO,IAAI;wBACrC,IAAI,MAAM,SAAA,CAAU,KAAK,CAAA;wBACzB,IAAI,QAAQ;wBAEZ,IAAA,IAAS,KAAK,CAAA,GAAI,MAAM,GAAG,MAAM,EAAG;4BAClC,MAAM,KAAK,KAAK;4BAChB,IAAI,KAAK,KAAK,MAAM,MAAM;4BAE1B,IAAA,IAAS,KAAK,CAAA,GAAI,MAAM,GAAG,MAAM,EAAG;gCAClC,MAAM,KAAK,KAAK;gCAChB,IAAI,KAAK,KAAK,MAAM,MAAM;gCAE1B,IAAA,IAAS,KAAK,CAAA,GAAI,MAAM,GAAG,MAAM,EAAG;oCAClC,MAAM,KAAK,KAAK;oCAChB,IAAI,KAAK,KAAK,MAAM,MAAM;oCAE1B,MAAM,SAAS,QAAQ,KAAK,OAAO,KAAK;oCACxC,MAAM,OAAO,SAAA,CAAU,MAAM,CAAA;oCAE7B;oCACA,OAAQ,YAAA,CAAa,OAAO,GAAA,IAAQ;gCACrC;4BACF;wBACF;wBAED,KAAA,CAAM,KAAK,CAAA,GAAI;oBAChB;gBACF;YACF;QACF;QAED,IAAA,CAAK,KAAA,GAAQ,WAAY;YAGvB,IAAA,IAAS,IAAI,GAAG,IAAI,IAAA,CAAK,KAAA,EAAO,IAAK;gBACnC,IAAA,CAAK,YAAA,CAAa,IAAI,CAAC,CAAA,GAAI;gBAC3B,IAAA,CAAK,KAAA,CAAM,CAAC,CAAA,GAAI;gBAChB,IAAA,CAAK,OAAA,CAAQ,IAAI,CAAC,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,IAAI,IAAI,CAAC,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,IAAI,IAAI,CAAC,CAAA,GAAI;YAC3E;QACF;QAED,IAAA,CAAK,MAAA,GAAS,WAAY;YACxB,IAAA,CAAK,KAAA,GAAQ;YAIb,MAAM,QAAQ,IAAA,CAAK,IAAA,GAAO;YAE1B,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC9B,MAAM,WAAW,IAAA,CAAK,KAAA,GAAQ;gBAC9B,MAAM,KAAA,CAAM,IAAI,IAAA,CAAK,QAAA,IAAY,IAAA,CAAK,QAAA;gBAEtC,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,IAAK;oBAC9B,MAAM,WAAW,WAAW,IAAA,CAAK,IAAA,GAAO;oBACxC,MAAM,KAAA,CAAM,IAAI,IAAA,CAAK,QAAA,IAAY,IAAA,CAAK,QAAA;oBAEtC,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,IAAK;wBAC9B,MAAM,KAAA,CAAM,IAAI,IAAA,CAAK,QAAA,IAAY,IAAA,CAAK,QAAA;wBACtC,MAAM,IAAI,WAAW;wBAErB,WAAW,IAAI,IAAI,IAAI,GAAG,IAAA,CAAK,SAAS;oBACzC;gBACF;YACF;YAID,IAAA,CAAK,QAAA,CAAS,YAAA,CAAa,GAAG,IAAA,CAAK,KAAK;YAIxC,SAAS,YAAA,CAAa,UAAU,EAAE,WAAA,GAAc;YAChD,SAAS,YAAA,CAAa,QAAQ,EAAE,WAAA,GAAc;YAE9C,IAAI,IAAA,CAAK,SAAA,EAAW,SAAS,YAAA,CAAa,IAAI,EAAE,WAAA,GAAc;YAC9D,IAAI,IAAA,CAAK,YAAA,EAAc,SAAS,YAAA,CAAa,OAAO,EAAE,WAAA,GAAc;YAIpE,IAAI,IAAA,CAAK,KAAA,GAAQ,IAAI,cACnB,QAAQ,IAAA,CACN;QAEL;QAED,IAAA,CAAK,IAAA,CAAK,UAAU;IACrB;AACH;AAWK,MAAC,YAAY,IAAI,WAAY;IACjC;IAAK;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAC/C;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IACjD;IAAO;IAAM;IAAO;IAAO;IAAO;IAAO;IAAO;IAChD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IACjD;IAAO;IAAO;IAAM;IAAO;IAAO;IAAO;IAAO;IAChD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IACjD;IAAO;IAAO;IAAO;IAAM;IAAO;IAAO;IAAO;IAChD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IACjD;IAAO;IAAO;IAAO;IAAO;IAAM;IAAO;IAAO;IAChD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IACjD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAM;IAAO;IAChD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IACjD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAM;IAChD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IACjD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IACjD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IACjD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IACjD;IAAM;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAChD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IACjD;IAAO;IAAM;IAAO;IAAO;IAAO;IAAO;IAAO;IAChD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IACjD;IAAO;IAAO;IAAM;IAAO;IAAO;IAAO;IAAO;IAChD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IACjD;IAAO;IAAO;IAAO;IAAM;IAAO;IAAO;IAAO;IAChD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IACjD;IAAO;IAAO;IAAO;IAAO;IAAM;IAAO;IAAO;IAChD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IACjD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAM;IAAO;IAChD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IACjD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAM;IAChD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IACjD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;CAAQ;AAGrD,MAAC,WAAW,IAAI,WAAY;IAChC,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3E;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrE;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtE;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACjE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC5D;IAAG;IAAI;IAAG;IAAI;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAClE;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC7D;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC5D;IAAG;IAAG;IAAI;IAAI;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAClE;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAG;IAAI;IAAG;IAAG;IAAI;IAAI;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC5D;IAAG;IAAI;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACvD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAI;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACvD;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAI;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC7D;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAI;IAAG;IAAI;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC5D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAI;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACvD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAI;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC5D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAI;IAAI;IAAG,CAAA;IACjD;IAAI;IAAI;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IACjD;IAAI;IAAI;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAClE;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtE;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACnD;IAAG;IAAG;IAAI;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACjE;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC5D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACnD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACnD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAC7C;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrD;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG,CAAA;IACjD;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrD;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI,CAAA;IAClD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAC9C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrD;IAAI;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACjE;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACnD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC5D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG,CAAA;IAChD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAC/C;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC5D;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrD;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACnD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAC7C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAI;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG,CAAA;IAChD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI,CAAA;IAChD;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAC9C;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrD;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtE;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtE;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACjE;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACvD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACnD;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAI;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC5D;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAChD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACnD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAC/C;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACjE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrD;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAC/C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACnD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACnD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAC7C;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAC9C;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI,CAAA;IAClD;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACvD;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACvD;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAChD;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAChD;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACnD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAC7C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAC9C;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtE;IAAI;IAAG;IAAI;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAClE;IAAI;IAAG;IAAI;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC5D;IAAG;IAAI;IAAG;IAAG;IAAI;IAAI;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC5D;IAAI;IAAG;IAAG;IAAI;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAC9C;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAC9C;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC5D;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI;IAAI;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACvD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAI;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACvD;IAAI;IAAI;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IACjD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI,CAAA;IAClD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG,CAAA;IAC/C;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC1D;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAC/C;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACnD;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrE;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC7D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAG;IAAI;IAAI;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACvD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAI;IAAI;IAAG,CAAA;IACjD;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtD;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG,CAAA;IAChD;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAI;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrD;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACpD;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAC/C;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAG;IAAI,CAAA;IAClD;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACzD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACnD;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrE;IAAG;IAAI;IAAG;IAAI;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAClE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAI;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC5D;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAI;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC7D;IAAG;IAAG;IAAI;IAAI;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAClE;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC5D;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrD;IAAG;IAAG;IAAI;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACjE;IAAG;IAAG;IAAI,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAI;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC3D;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAChE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrD;IAAG;IAAI;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACtE;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAC/D;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrE;IAAG;IAAG;IAAG,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IACrE,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;IAAK,CAAA;CAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8973, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8979, "column": 0}, "map": {"version": 3, "file": "GroundProjectedEnv.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/objects/GroundProjectedEnv.ts"], "sourcesContent": ["import { Mesh, IcosahedronGeometry, ShaderMaterial, DoubleSide, Texture, CubeTexture, BufferGeometry } from 'three'\nimport { version } from '../_polyfill/constants'\n\nexport interface GroundProjectedEnvParameters {\n  height?: number\n  radius?: number\n}\n\nconst isCubeTexture = (def: CubeTexture | Texture): def is CubeTexture => def && (def as CubeTexture).isCubeTexture\n\nexport class GroundProjectedEnv extends Mesh<BufferGeometry, ShaderMaterial> {\n  constructor(texture: CubeTexture | Texture, options?: GroundProjectedEnvParameters) {\n    const isCubeMap = isCubeTexture(texture)\n    const w = (isCubeMap ? texture.image[0]?.width : texture.image.width) ?? 1024\n    const cubeSize = w / 4\n    const _lodMax = Math.floor(Math.log2(cubeSize))\n    const _cubeSize = Math.pow(2, _lodMax)\n    const width = 3 * Math.max(_cubeSize, 16 * 7)\n    const height = 4 * _cubeSize\n\n    const defines = [\n      isCubeMap ? '#define ENVMAP_TYPE_CUBE' : '',\n      `#define CUBEUV_TEXEL_WIDTH ${1.0 / width}`,\n      `#define CUBEUV_TEXEL_HEIGHT ${1.0 / height}`,\n      `#define CUBEUV_MAX_MIP ${_lodMax}.0`,\n    ]\n\n    const vertexShader = /* glsl */ `\n        varying vec3 vWorldPosition;\n        void main() \n        {\n            vec4 worldPosition = ( modelMatrix * vec4( position, 1.0 ) );\n            vWorldPosition = worldPosition.xyz;\n            \n            gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }\n        `\n    const fragmentShader =\n      defines.join('\\n') +\n      /* glsl */ `\n        #define ENVMAP_TYPE_CUBE_UV\n        varying vec3 vWorldPosition;\n        uniform float radius;\n        uniform float height;\n        uniform float angle;\n        #ifdef ENVMAP_TYPE_CUBE\n            uniform samplerCube map;\n        #else\n            uniform sampler2D map;\n        #endif\n        // From: https://www.shadertoy.com/view/4tsBD7\n        float diskIntersectWithBackFaceCulling( vec3 ro, vec3 rd, vec3 c, vec3 n, float r ) \n        {\n            float d = dot ( rd, n );\n            \n            if( d > 0.0 ) { return 1e6; }\n            \n            vec3  o = ro - c;\n            float t = - dot( n, o ) / d;\n            vec3  q = o + rd * t;\n            \n            return ( dot( q, q ) < r * r ) ? t : 1e6;\n        }\n        // From: https://www.iquilezles.org/www/articles/intersectors/intersectors.htm\n        float sphereIntersect( vec3 ro, vec3 rd, vec3 ce, float ra ) \n        {\n            vec3 oc = ro - ce;\n            float b = dot( oc, rd );\n            float c = dot( oc, oc ) - ra * ra;\n            float h = b * b - c;\n            \n            if( h < 0.0 ) { return -1.0; }\n            \n            h = sqrt( h );\n            \n            return - b + h;\n        }\n        vec3 project() \n        {\n            vec3 p = normalize( vWorldPosition );\n            vec3 camPos = cameraPosition;\n            camPos.y -= height;\n            float intersection = sphereIntersect( camPos, p, vec3( 0.0 ), radius );\n            if( intersection > 0.0 ) {\n                \n                vec3 h = vec3( 0.0, - height, 0.0 );\n                float intersection2 = diskIntersectWithBackFaceCulling( camPos, p, h, vec3( 0.0, 1.0, 0.0 ), radius );\n                p = ( camPos + min( intersection, intersection2 ) * p ) / radius;\n            } else {\n                p = vec3( 0.0, 1.0, 0.0 );\n            }\n            return p;\n        }\n        #include <common>\n        #include <cube_uv_reflection_fragment>\n        void main() \n        {\n            vec3 projectedWorldPosition = project();\n            \n            #ifdef ENVMAP_TYPE_CUBE\n                vec3 outcolor = textureCube( map, projectedWorldPosition ).rgb;\n            #else\n                vec3 direction = normalize( projectedWorldPosition );\n                vec2 uv = equirectUv( direction );\n                vec3 outcolor = texture2D( map, uv ).rgb;\n            #endif\n            gl_FragColor = vec4( outcolor, 1.0 );\n            #include <tonemapping_fragment>\n            #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n        }\n        `\n\n    const uniforms = {\n      map: { value: texture },\n      height: { value: options?.height || 15 },\n      radius: { value: options?.radius || 100 },\n    }\n\n    const geometry = new IcosahedronGeometry(1, 16)\n    const material = new ShaderMaterial({\n      uniforms,\n      fragmentShader,\n      vertexShader,\n      side: DoubleSide,\n    })\n\n    super(geometry, material)\n  }\n\n  set radius(radius: number) {\n    this.material.uniforms.radius.value = radius\n  }\n\n  get radius(): number {\n    return this.material.uniforms.radius.value\n  }\n\n  set height(height: number) {\n    this.material.uniforms.height.value = height\n  }\n\n  get height(): number {\n    return this.material.uniforms.height.value\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAQA,MAAM,gBAAgB,CAAC,MAAmD,OAAQ,IAAoB,aAAA;AAE/F,MAAM,kOAA2B,OAAA,CAAqC;IAC3E,YAAY,OAAA,EAAgC,OAAA,CAAwC;;QAC5E,MAAA,YAAY,cAAc,OAAO;QACjC,MAAA,IAAA,CAAK,KAAA,YAAA,CAAY,KAAA,QAAQ,KAAA,CAAM,CAAC,CAAA,KAAf,OAAA,KAAA,IAAA,GAAkB,KAAA,GAAQ,QAAQ,KAAA,CAAM,KAAA,KAApD,OAAA,KAA8D;QACzE,MAAM,WAAW,IAAI;QACrB,MAAM,UAAU,KAAK,KAAA,CAAM,KAAK,IAAA,CAAK,QAAQ,CAAC;QAC9C,MAAM,YAAY,KAAK,GAAA,CAAI,GAAG,OAAO;QACrC,MAAM,QAAQ,IAAI,KAAK,GAAA,CAAI,WAAW,KAAK,CAAC;QAC5C,MAAM,SAAS,IAAI;QAEnB,MAAM,UAAU;YACd,YAAY,6BAA6B;YACzC,CAAA,2BAAA,EAA8B,IAAM,OAAA;YACpC,CAAA,4BAAA,EAA+B,IAAM,QAAA;YACrC,CAAA,uBAAA,EAA0B,QAAA,EAAA,CAAA;SAAA;QAGtB,MAAA,eAAA,QAAA,GAA0B,CAAA;;;;;;;;;QAAA,CAAA;QAU1B,MAAA,iBACJ,QAAQ,IAAA,CAAK,IAAI,IAAA,QAAA,GACN,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sBAAA,mPAqEO,UAAA,IAAW,MAAM,wBAAwB,qBAAA;;QAAA,CAAA;QAI7D,MAAM,WAAW;YACf,KAAK;gBAAE,OAAO;YAAQ;YACtB,QAAQ;gBAAE,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,MAAA,KAAU;YAAG;YACvC,QAAQ;gBAAE,OAAA,CAAO,WAAA,OAAA,KAAA,IAAA,QAAS,MAAA,KAAU;YAAI;QAAA;QAG1C,MAAM,WAAW,2MAAI,sBAAA,CAAoB,GAAG,EAAE;QACxC,MAAA,WAAW,2MAAI,iBAAA,CAAe;YAClC;YACA;YACA;YACA,6MAAM,aAAA;QAAA,CACP;QAED,KAAA,CAAM,UAAU,QAAQ;IAC1B;IAEA,IAAI,OAAO,MAAA,EAAgB;QACpB,IAAA,CAAA,QAAA,CAAS,QAAA,CAAS,MAAA,CAAO,KAAA,GAAQ;IACxC;IAEA,IAAI,SAAiB;QACZ,OAAA,IAAA,CAAK,QAAA,CAAS,QAAA,CAAS,MAAA,CAAO,KAAA;IACvC;IAEA,IAAI,OAAO,MAAA,EAAgB;QACpB,IAAA,CAAA,QAAA,CAAS,QAAA,CAAS,MAAA,CAAO,KAAA,GAAQ;IACxC;IAEA,IAAI,SAAiB;QACZ,OAAA,IAAA,CAAK,QAAA,CAAS,QAAA,CAAS,MAAA,CAAO,KAAA;IACvC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9120, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9126, "column": 0}, "map": {"version": 3, "file": "Sky.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/objects/Sky.js"], "sourcesContent": ["import { BackSide, BoxGeometry, Mesh, ShaderMaterial, UniformsUtils, Vector3 } from 'three'\nimport { version } from '../_polyfill/constants'\n\n/**\n * Based on \"A Practical Analytic Model for Daylight\"\n * aka The Preetham Model, the de facto standard analytic skydome model\n * https://www.researchgate.net/publication/220720443_A_Practical_Analytic_Model_for_Daylight\n *\n * First implemented by <PERSON>\n * http://www.simonwallner.at/projects/atmospheric-scattering\n *\n * Improved by <PERSON>\n * http://blenderartists.org/forum/showthread.php?245954-preethams-sky-impementation-HDR\n *\n * Three.js integration by zz85 http://twitter.com/blurspline\n */\nconst Sky = /* @__PURE__ */ (() => {\n  const SkyShader = {\n    uniforms: {\n      turbidity: { value: 2 },\n      rayleigh: { value: 1 },\n      mieCoefficient: { value: 0.005 },\n      mieDirectionalG: { value: 0.8 },\n      sunPosition: { value: new Vector3() },\n      up: { value: new Vector3(0, 1, 0) },\n    },\n\n    vertexShader: /* glsl */ `\n      uniform vec3 sunPosition;\n      uniform float rayleigh;\n      uniform float turbidity;\n      uniform float mieCoefficient;\n      uniform vec3 up;\n\n      varying vec3 vWorldPosition;\n      varying vec3 vSunDirection;\n      varying float vSunfade;\n      varying vec3 vBetaR;\n      varying vec3 vBetaM;\n      varying float vSunE;\n\n      // constants for atmospheric scattering\n      const float e = 2.71828182845904523536028747135266249775724709369995957;\n      const float pi = 3.141592653589793238462643383279502884197169;\n\n      // wavelength of used primaries, according to preetham\n      const vec3 lambda = vec3( 680E-9, 550E-9, 450E-9 );\n      // this pre-calcuation replaces older TotalRayleigh(vec3 lambda) function:\n      // (8.0 * pow(pi, 3.0) * pow(pow(n, 2.0) - 1.0, 2.0) * (6.0 + 3.0 * pn)) / (3.0 * N * pow(lambda, vec3(4.0)) * (6.0 - 7.0 * pn))\n      const vec3 totalRayleigh = vec3( 5.804542996261093E-6, 1.3562911419845635E-5, 3.0265902468824876E-5 );\n\n      // mie stuff\n      // K coefficient for the primaries\n      const float v = 4.0;\n      const vec3 K = vec3( 0.686, 0.678, 0.666 );\n      // MieConst = pi * pow( ( 2.0 * pi ) / lambda, vec3( v - 2.0 ) ) * K\n      const vec3 MieConst = vec3( 1.8399918514433978E14, 2.7798023919660528E14, 4.0790479543861094E14 );\n\n      // earth shadow hack\n      // cutoffAngle = pi / 1.95;\n      const float cutoffAngle = 1.6110731556870734;\n      const float steepness = 1.5;\n      const float EE = 1000.0;\n\n      float sunIntensity( float zenithAngleCos ) {\n        zenithAngleCos = clamp( zenithAngleCos, -1.0, 1.0 );\n        return EE * max( 0.0, 1.0 - pow( e, -( ( cutoffAngle - acos( zenithAngleCos ) ) / steepness ) ) );\n      }\n\n      vec3 totalMie( float T ) {\n        float c = ( 0.2 * T ) * 10E-18;\n        return 0.434 * c * MieConst;\n      }\n\n      void main() {\n\n        vec4 worldPosition = modelMatrix * vec4( position, 1.0 );\n        vWorldPosition = worldPosition.xyz;\n\n        gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        gl_Position.z = gl_Position.w; // set z to camera.far\n\n        vSunDirection = normalize( sunPosition );\n\n        vSunE = sunIntensity( dot( vSunDirection, up ) );\n\n        vSunfade = 1.0 - clamp( 1.0 - exp( ( sunPosition.y / 450000.0 ) ), 0.0, 1.0 );\n\n        float rayleighCoefficient = rayleigh - ( 1.0 * ( 1.0 - vSunfade ) );\n\n      // extinction (absorbtion + out scattering)\n      // rayleigh coefficients\n        vBetaR = totalRayleigh * rayleighCoefficient;\n\n      // mie coefficients\n        vBetaM = totalMie( turbidity ) * mieCoefficient;\n\n      }\n    `,\n\n    fragmentShader: /* glsl */ `\n      varying vec3 vWorldPosition;\n      varying vec3 vSunDirection;\n      varying float vSunfade;\n      varying vec3 vBetaR;\n      varying vec3 vBetaM;\n      varying float vSunE;\n\n      uniform float mieDirectionalG;\n      uniform vec3 up;\n\n      const vec3 cameraPos = vec3( 0.0, 0.0, 0.0 );\n\n      // constants for atmospheric scattering\n      const float pi = 3.141592653589793238462643383279502884197169;\n\n      const float n = 1.0003; // refractive index of air\n      const float N = 2.545E25; // number of molecules per unit volume for air at 288.15K and 1013mb (sea level -45 celsius)\n\n      // optical length at zenith for molecules\n      const float rayleighZenithLength = 8.4E3;\n      const float mieZenithLength = 1.25E3;\n      // 66 arc seconds -> degrees, and the cosine of that\n      const float sunAngularDiameterCos = 0.999956676946448443553574619906976478926848692873900859324;\n\n      // 3.0 / ( 16.0 * pi )\n      const float THREE_OVER_SIXTEENPI = 0.05968310365946075;\n      // 1.0 / ( 4.0 * pi )\n      const float ONE_OVER_FOURPI = 0.07957747154594767;\n\n      float rayleighPhase( float cosTheta ) {\n        return THREE_OVER_SIXTEENPI * ( 1.0 + pow( cosTheta, 2.0 ) );\n      }\n\n      float hgPhase( float cosTheta, float g ) {\n        float g2 = pow( g, 2.0 );\n        float inverse = 1.0 / pow( 1.0 - 2.0 * g * cosTheta + g2, 1.5 );\n        return ONE_OVER_FOURPI * ( ( 1.0 - g2 ) * inverse );\n      }\n\n      void main() {\n\n        vec3 direction = normalize( vWorldPosition - cameraPos );\n\n      // optical length\n      // cutoff angle at 90 to avoid singularity in next formula.\n        float zenithAngle = acos( max( 0.0, dot( up, direction ) ) );\n        float inverse = 1.0 / ( cos( zenithAngle ) + 0.15 * pow( 93.885 - ( ( zenithAngle * 180.0 ) / pi ), -1.253 ) );\n        float sR = rayleighZenithLength * inverse;\n        float sM = mieZenithLength * inverse;\n\n      // combined extinction factor\n        vec3 Fex = exp( -( vBetaR * sR + vBetaM * sM ) );\n\n      // in scattering\n        float cosTheta = dot( direction, vSunDirection );\n\n        float rPhase = rayleighPhase( cosTheta * 0.5 + 0.5 );\n        vec3 betaRTheta = vBetaR * rPhase;\n\n        float mPhase = hgPhase( cosTheta, mieDirectionalG );\n        vec3 betaMTheta = vBetaM * mPhase;\n\n        vec3 Lin = pow( vSunE * ( ( betaRTheta + betaMTheta ) / ( vBetaR + vBetaM ) ) * ( 1.0 - Fex ), vec3( 1.5 ) );\n        Lin *= mix( vec3( 1.0 ), pow( vSunE * ( ( betaRTheta + betaMTheta ) / ( vBetaR + vBetaM ) ) * Fex, vec3( 1.0 / 2.0 ) ), clamp( pow( 1.0 - dot( up, vSunDirection ), 5.0 ), 0.0, 1.0 ) );\n\n      // nightsky\n        float theta = acos( direction.y ); // elevation --> y-axis, [-pi/2, pi/2]\n        float phi = atan( direction.z, direction.x ); // azimuth --> x-axis [-pi/2, pi/2]\n        vec2 uv = vec2( phi, theta ) / vec2( 2.0 * pi, pi ) + vec2( 0.5, 0.0 );\n        vec3 L0 = vec3( 0.1 ) * Fex;\n\n      // composition + solar disc\n        float sundisk = smoothstep( sunAngularDiameterCos, sunAngularDiameterCos + 0.00002, cosTheta );\n        L0 += ( vSunE * 19000.0 * Fex ) * sundisk;\n\n        vec3 texColor = ( Lin + L0 ) * 0.04 + vec3( 0.0, 0.0003, 0.00075 );\n\n        vec3 retColor = pow( texColor, vec3( 1.0 / ( 1.2 + ( 1.2 * vSunfade ) ) ) );\n\n        gl_FragColor = vec4( retColor, 1.0 );\n\n      #include <tonemapping_fragment>\n      #include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n\n      }\n    `,\n  }\n\n  const material = new ShaderMaterial({\n    name: 'SkyShader',\n    fragmentShader: SkyShader.fragmentShader,\n    vertexShader: SkyShader.vertexShader,\n    uniforms: UniformsUtils.clone(SkyShader.uniforms),\n    side: BackSide,\n    depthWrite: false,\n  })\n\n  class Sky extends Mesh {\n    constructor() {\n      super(new BoxGeometry(1, 1, 1), material)\n    }\n\n    static SkyShader = SkyShader\n    static material = material\n  }\n\n  return Sky\n})()\n\nexport { Sky }\n"], "names": ["Sky"], "mappings": ";;;;;;;;;;;;;;;;;;AAgBK,MAAC,MAAuB,aAAA,GAAA,CAAA,MAAM;IACjC,MAAM,YAAY;QAChB,UAAU;YACR,WAAW;gBAAE,OAAO;YAAG;YACvB,UAAU;gBAAE,OAAO;YAAG;YACtB,gBAAgB;gBAAE,OAAO;YAAO;YAChC,iBAAiB;gBAAE,OAAO;YAAK;YAC/B,aAAa;gBAAE,OAAO,2MAAI,UAAA;YAAW;YACrC,IAAI;gBAAE,OAAO,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;YAAG;QACpC;QAED,cAAA,QAAA,GAAyB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAA,CAAA;QAyEzB,gBAAA,QAAA,GAA2B,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAAA,mPAmFb,UAAA,IAAW,MAAM,wBAAwB,qBAAA;;;IAAA,CAAA;IAIxD;IAED,MAAM,WAAW,0MAAI,kBAAA,CAAe;QAClC,MAAM;QACN,gBAAgB,UAAU,cAAA;QAC1B,cAAc,UAAU,YAAA;QACxB,iNAAU,gBAAA,CAAc,KAAA,CAAM,UAAU,QAAQ;QAChD,MAAM,kNAAA;QACN,YAAY;IAChB,CAAG;IAED,MAAMA,oNAAY,OAAA,CAAK;QACrB,aAAc;YACZ,KAAA,CAAM,2MAAI,cAAA,CAAY,GAAG,GAAG,CAAC,GAAG,QAAQ;QACzC;IAIF;IAFC,cALIA,MAKG,aAAY;IACnB,cANIA,MAMG,YAAW;IAGpB,OAAOA;AACT,CAAA,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9345, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9351, "column": 0}, "map": {"version": 3, "file": "MeshoptDecoder.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/libs/MeshoptDecoder.ts"], "sourcesContent": ["// This file is part of meshoptimizer library and is distributed under the terms of MIT License.\n// Copyright (C) 2016-2020, by <PERSON><PERSON><PERSON> (<EMAIL>)\n\ntype API = {\n  ready: Promise<void>\n  supported: boolean\n  decodeVertexBuffer: (target: Uint8Array, count: number, size: number, source: Uint8Array, filter?: string) => void\n  decodeIndexBuffer: (target: Uint8Array, count: number, size: number, source: Uint8Array) => void\n  decodeIndexSequence: (target: Uint8Array, count: number, size: number, source: Uint8Array) => void\n  decodeGltfBuffer: (\n    target: Uint8Array,\n    count: number,\n    size: number,\n    source: Uint8Array,\n    mode: string,\n    filter?: string,\n  ) => void\n}\n\nlet generated: API\n\nconst MeshoptDecoder = () => {\n  if (generated) return generated\n\n  // Built with clang version 11.0.0 (https://github.com/llvm/llvm-project.git 0160ad802e899c2922bc9b29564080c22eb0908c)\n  // Built from meshoptimizer 0.14\n  const wasm_base =\n    '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'\n  const wasm_simd =\n    '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'\n\n  // Uses bulk-memory and simd extensions\n  const detector = new Uint8Array([\n    0,\n    97,\n    115,\n    109,\n    1,\n    0,\n    0,\n    0,\n    1,\n    4,\n    1,\n    96,\n    0,\n    0,\n    3,\n    3,\n    2,\n    0,\n    0,\n    5,\n    3,\n    1,\n    0,\n    1,\n    12,\n    1,\n    0,\n    10,\n    22,\n    2,\n    12,\n    0,\n    65,\n    0,\n    65,\n    0,\n    65,\n    0,\n    252,\n    10,\n    0,\n    0,\n    11,\n    7,\n    0,\n    65,\n    0,\n    253,\n    15,\n    26,\n    11,\n  ])\n\n  // Used to unpack wasm\n  const wasmpack = new Uint8Array([\n    32,\n    0,\n    65,\n    253,\n    3,\n    1,\n    2,\n    34,\n    4,\n    106,\n    6,\n    5,\n    11,\n    8,\n    7,\n    20,\n    13,\n    33,\n    12,\n    16,\n    128,\n    9,\n    116,\n    64,\n    19,\n    113,\n    127,\n    15,\n    10,\n    21,\n    22,\n    14,\n    255,\n    66,\n    24,\n    54,\n    136,\n    107,\n    18,\n    23,\n    192,\n    26,\n    114,\n    118,\n    132,\n    17,\n    77,\n    101,\n    130,\n    144,\n    27,\n    87,\n    131,\n    44,\n    45,\n    74,\n    156,\n    154,\n    70,\n    167,\n  ])\n\n  if (typeof WebAssembly !== 'object') {\n    // This module requires WebAssembly to function\n    return {\n      supported: false,\n    }\n  }\n\n  let wasm = wasm_base\n\n  if (WebAssembly.validate(detector)) {\n    wasm = wasm_simd\n  }\n\n  let instance: any // WebAssembly.Instance\n\n  const promise = WebAssembly.instantiate(unpack(wasm), {}).then((result) => {\n    instance = result.instance\n    instance.exports.__wasm_call_ctors()\n  })\n\n  function unpack(data: string) {\n    const result = new Uint8Array(data.length)\n    for (let i = 0; i < data.length; ++i) {\n      const ch = data.charCodeAt(i)\n      result[i] = ch > 96 ? ch - 71 : ch > 64 ? ch - 65 : ch > 47 ? ch + 4 : ch > 46 ? 63 : 62\n    }\n    let write = 0\n    for (let i = 0; i < data.length; ++i) {\n      result[write++] = result[i] < 60 ? wasmpack[result[i]] : (result[i] - 60) * 64 + result[++i]\n    }\n    return result.buffer.slice(0, write)\n  }\n\n  function decode(\n    fun: Function,\n    target: Uint8Array,\n    count: number,\n    size: number,\n    source: Uint8Array,\n    filter?: Function,\n  ) {\n    const sbrk = instance.exports.sbrk\n    const count4 = (count + 3) & ~3 // pad for SIMD filter\n    const tp = sbrk(count4 * size)\n    const sp = sbrk(source.length)\n    const heap = new Uint8Array(instance.exports.memory.buffer)\n    heap.set(source, sp)\n    const res = fun(tp, count, size, sp, source.length)\n    if (res === 0 && filter) {\n      filter(tp, count4, size)\n    }\n    target.set(heap.subarray(tp, tp + count * size))\n    sbrk(tp - sbrk(0))\n    if (res !== 0) {\n      throw new Error(`Malformed buffer data: ${res}`)\n    }\n  }\n\n  const filters = {\n    // legacy index-based enums for glTF\n    0: '',\n    1: 'meshopt_decodeFilterOct',\n    2: 'meshopt_decodeFilterQuat',\n    3: 'meshopt_decodeFilterExp',\n    // string-based enums for glTF\n    NONE: '',\n    OCTAHEDRAL: 'meshopt_decodeFilterOct',\n    QUATERNION: 'meshopt_decodeFilterQuat',\n    EXPONENTIAL: 'meshopt_decodeFilterExp',\n  }\n\n  const decoders = {\n    // legacy index-based enums for glTF\n    0: 'meshopt_decodeVertexBuffer',\n    1: 'meshopt_decodeIndexBuffer',\n    2: 'meshopt_decodeIndexSequence',\n    // string-based enums for glTF\n    ATTRIBUTES: 'meshopt_decodeVertexBuffer',\n    TRIANGLES: 'meshopt_decodeIndexBuffer',\n    INDICES: 'meshopt_decodeIndexSequence',\n  }\n\n  generated = {\n    ready: promise,\n    supported: true,\n    decodeVertexBuffer(target, count, size, source, filter) {\n      decode(\n        instance.exports.meshopt_decodeVertexBuffer,\n        target,\n        count,\n        size,\n        source,\n        instance.exports[filters[filter as keyof typeof filters]],\n      )\n    },\n    decodeIndexBuffer(target, count, size, source) {\n      decode(instance.exports.meshopt_decodeIndexBuffer, target, count, size, source)\n    },\n    decodeIndexSequence(target, count, size, source) {\n      decode(instance.exports.meshopt_decodeIndexSequence, target, count, size, source)\n    },\n    decodeGltfBuffer(target, count, size, source, mode, filter) {\n      decode(\n        instance.exports[decoders[mode as keyof typeof decoders]],\n        target,\n        count,\n        size,\n        source,\n        instance.exports[filters[filter as keyof typeof filters]],\n      )\n    },\n  }\n\n  return generated\n}\n\nexport { MeshoptDecoder }\n"], "names": [], "mappings": ";;;AAmBA,IAAI;AAEJ,MAAM,iBAAiB,MAAM;IACvB,IAAA,WAAkB,OAAA;IAItB,MAAM,YACJ;IACF,MAAM,YACJ;IAGI,MAAA,WAAW,IAAI,WAAW;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAGK,MAAA,WAAW,IAAI,WAAW;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAEG,IAAA,OAAO,gBAAgB,UAAU;QAE5B,OAAA;YACL,WAAW;QAAA;IAEf;IAEA,IAAI,OAAO;IAEP,IAAA,YAAY,QAAA,CAAS,QAAQ,GAAG;QAC3B,OAAA;IACT;IAEI,IAAA;IAEE,MAAA,UAAU,YAAY,WAAA,CAAY,OAAO,IAAI,GAAG,CAAA,CAAE,EAAE,IAAA,CAAK,CAAC,WAAW;QACzE,WAAW,OAAO,QAAA;QAClB,SAAS,OAAA,CAAQ,iBAAA;IAAkB,CACpC;IAED,SAAS,OAAO,IAAA,EAAc;QAC5B,MAAM,SAAS,IAAI,WAAW,KAAK,MAAM;QACzC,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,MAAA,EAAQ,EAAE,EAAG;YAC9B,MAAA,KAAK,KAAK,UAAA,CAAW,CAAC;YAC5B,MAAA,CAAO,CAAC,CAAA,GAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK;QACxF;QACA,IAAI,QAAQ;QACZ,IAAA,IAAS,IAAI,GAAG,IAAI,KAAK,MAAA,EAAQ,EAAE,EAAG;YACpC,MAAA,CAAO,OAAO,CAAA,GAAI,MAAA,CAAO,CAAC,CAAA,GAAI,KAAK,QAAA,CAAS,MAAA,CAAO,CAAC,CAAC,CAAA,GAAA,CAAK,MAAA,CAAO,CAAC,CAAA,GAAI,EAAA,IAAM,KAAK,MAAA,CAAO,EAAE,CAAC,CAAA;QAC7F;QACA,OAAO,OAAO,MAAA,CAAO,KAAA,CAAM,GAAG,KAAK;IACrC;IAEA,SAAS,OACP,GAAA,EACA,MAAA,EACA,KAAA,EACA,IAAA,EACA,MAAA,EACA,MAAA,EACA;QACM,MAAA,OAAO,SAAS,OAAA,CAAQ,IAAA;QACxB,MAAA,SAAU,QAAQ,IAAK,CAAC;QACxB,MAAA,KAAK,KAAK,SAAS,IAAI;QACvB,MAAA,KAAK,KAAK,OAAO,MAAM;QAC7B,MAAM,OAAO,IAAI,WAAW,SAAS,OAAA,CAAQ,MAAA,CAAO,MAAM;QACrD,KAAA,GAAA,CAAI,QAAQ,EAAE;QACnB,MAAM,MAAM,IAAI,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM;QAC9C,IAAA,QAAQ,KAAK,QAAQ;YAChB,OAAA,IAAI,QAAQ,IAAI;QACzB;QACA,OAAO,GAAA,CAAI,KAAK,QAAA,CAAS,IAAI,KAAK,QAAQ,IAAI,CAAC;QAC1C,KAAA,KAAK,KAAK,CAAC,CAAC;QACjB,IAAI,QAAQ,GAAG;YACP,MAAA,IAAI,MAAM,CAAA,uBAAA,EAA0B,KAAK;QACjD;IACF;IAEA,MAAM,UAAU;QAAA,oCAAA;QAEd,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;QAAA,8BAAA;QAEH,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,aAAa;IAAA;IAGf,MAAM,WAAW;QAAA,oCAAA;QAEf,GAAG;QACH,GAAG;QACH,GAAG;QAAA,8BAAA;QAEH,YAAY;QACZ,WAAW;QACX,SAAS;IAAA;IAGC,YAAA;QACV,OAAO;QACP,WAAW;QACX,oBAAmB,MAAA,EAAQ,KAAA,EAAO,IAAA,EAAM,MAAA,EAAQ,MAAA,EAAQ;YACtD,OACE,SAAS,OAAA,CAAQ,0BAAA,EACjB,QACA,OACA,MACA,QACA,SAAS,OAAA,CAAQ,OAAA,CAAQ,MAA8B,CAAC,CAAA;QAE5D;QACA,mBAAkB,MAAA,EAAQ,KAAA,EAAO,IAAA,EAAM,MAAA,EAAQ;YAC7C,OAAO,SAAS,OAAA,CAAQ,yBAAA,EAA2B,QAAQ,OAAO,MAAM,MAAM;QAChF;QACA,qBAAoB,MAAA,EAAQ,KAAA,EAAO,IAAA,EAAM,MAAA,EAAQ;YAC/C,OAAO,SAAS,OAAA,CAAQ,2BAAA,EAA6B,QAAQ,OAAO,MAAM,MAAM;QAClF;QACA,kBAAiB,MAAA,EAAQ,KAAA,EAAO,IAAA,EAAM,MAAA,EAAQ,IAAA,EAAM,MAAA,EAAQ;YAC1D,OACE,SAAS,OAAA,CAAQ,QAAA,CAAS,IAA6B,CAAC,CAAA,EACxD,QACA,OACA,MACA,QACA,SAAS,OAAA,CAAQ,OAAA,CAAQ,MAA8B,CAAC,CAAA;QAE5D;IAAA;IAGK,OAAA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9559, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9565, "column": 0}, "map": {"version": 3, "file": "ktx-parse.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/libs/ktx-parse.js"], "sourcesContent": ["///////////////////////////////////////////////////\n// KTX2 Header.\n///////////////////////////////////////////////////\nconst KHR_SUPERCOMPRESSION_NONE = 0\nconst KHR_SUPERCOMPRESSION_BASISLZ = 1\nconst KHR_SUPERCOMPRESSION_ZSTD = 2\nconst KHR_SUPERCOMPRESSION_ZLIB = 3 ///////////////////////////////////////////////////\n// Data Format Descriptor (DFD).\n///////////////////////////////////////////////////\n\nconst KHR_DF_KHR_DESCRIPTORTYPE_BASICFORMAT = 0\nconst KHR_DF_VENDORID_KHRONOS = 0\nconst KHR_DF_VERSION = 2\nconst KHR_DF_MODEL_UNSPECIFIED = 0\nconst KHR_DF_MODEL_RGBSDA = 1 // ...\n\nconst KHR_DF_MODEL_ETC1 = 160\nconst KHR_DF_MODEL_ETC2 = 161\nconst KHR_DF_MODEL_ASTC = 162\nconst KHR_DF_MODEL_ETC1S = 163\nconst KHR_DF_MODEL_UASTC = 166\nconst KHR_DF_FLAG_ALPHA_STRAIGHT = 0\nconst KHR_DF_FLAG_ALPHA_PREMULTIPLIED = 1\nconst KHR_DF_TRANSFER_UNSPECIFIED = 0\nconst KHR_DF_TRANSFER_LINEAR = 1\nconst KHR_DF_TRANSFER_SRGB = 2\nconst KHR_DF_TRANSFER_ITU = 3\nconst KHR_DF_TRANSFER_NTSC = 4\nconst KHR_DF_TRANSFER_SLOG = 5\nconst KHR_DF_TRANSFER_SLOG2 = 6\nconst KHR_DF_TRANSFER_BT1886 = 7\nconst KHR_DF_TRANSFER_HLG_OETF = 8\nconst KHR_DF_TRANSFER_HLG_EOTF = 9\nconst KHR_DF_TRANSFER_PQ_EOTF = 10\nconst KHR_DF_TRANSFER_PQ_OETF = 11\nconst KHR_DF_TRANSFER_DCIP3 = 12\nconst KHR_DF_TRANSFER_PAL_OETF = 13\nconst KHR_DF_TRANSFER_PAL625_EOTF = 14\nconst KHR_DF_TRANSFER_ST240 = 15\nconst KHR_DF_TRANSFER_ACESCC = 16\nconst KHR_DF_TRANSFER_ACESCCT = 17\nconst KHR_DF_TRANSFER_ADOBERGB = 18\nconst KHR_DF_PRIMARIES_UNSPECIFIED = 0\nconst KHR_DF_PRIMARIES_BT709 = 1\nconst KHR_DF_PRIMARIES_BT601_EBU = 2\nconst KHR_DF_PRIMARIES_BT601_SMPTE = 3\nconst KHR_DF_PRIMARIES_BT2020 = 4\nconst KHR_DF_PRIMARIES_CIEXYZ = 5\nconst KHR_DF_PRIMARIES_ACES = 6\nconst KHR_DF_PRIMARIES_ACESCC = 7\nconst KHR_DF_PRIMARIES_NTSC1953 = 8\nconst KHR_DF_PRIMARIES_PAL525 = 9\nconst KHR_DF_PRIMARIES_DISPLAYP3 = 10\nconst KHR_DF_PRIMARIES_ADOBERGB = 11\nconst KHR_DF_CHANNEL_RGBSDA_RED = 0\nconst KHR_DF_CHANNEL_RGBSDA_GREEN = 1\nconst KHR_DF_CHANNEL_RGBSDA_BLUE = 2\nconst KHR_DF_CHANNEL_RGBSDA_STENCIL = 13\nconst KHR_DF_CHANNEL_RGBSDA_DEPTH = 14\nconst KHR_DF_CHANNEL_RGBSDA_ALPHA = 15\nconst KHR_DF_SAMPLE_DATATYPE_FLOAT = 0x80\nconst KHR_DF_SAMPLE_DATATYPE_SIGNED = 0x40\nconst KHR_DF_SAMPLE_DATATYPE_EXPONENT = 0x20\nconst KHR_DF_SAMPLE_DATATYPE_LINEAR = 0x10 ///////////////////////////////////////////////////\n// VK FORMAT.\n///////////////////////////////////////////////////\n\nconst VK_FORMAT_UNDEFINED = 0\nconst VK_FORMAT_R4G4_UNORM_PACK8 = 1\nconst VK_FORMAT_R4G4B4A4_UNORM_PACK16 = 2\nconst VK_FORMAT_B4G4R4A4_UNORM_PACK16 = 3\nconst VK_FORMAT_R5G6B5_UNORM_PACK16 = 4\nconst VK_FORMAT_B5G6R5_UNORM_PACK16 = 5\nconst VK_FORMAT_R5G5B5A1_UNORM_PACK16 = 6\nconst VK_FORMAT_B5G5R5A1_UNORM_PACK16 = 7\nconst VK_FORMAT_A1R5G5B5_UNORM_PACK16 = 8\nconst VK_FORMAT_R8_UNORM = 9\nconst VK_FORMAT_R8_SNORM = 10\nconst VK_FORMAT_R8_UINT = 13\nconst VK_FORMAT_R8_SINT = 14\nconst VK_FORMAT_R8_SRGB = 15\nconst VK_FORMAT_R8G8_UNORM = 16\nconst VK_FORMAT_R8G8_SNORM = 17\nconst VK_FORMAT_R8G8_UINT = 20\nconst VK_FORMAT_R8G8_SINT = 21\nconst VK_FORMAT_R8G8_SRGB = 22\nconst VK_FORMAT_R8G8B8_UNORM = 23\nconst VK_FORMAT_R8G8B8_SNORM = 24\nconst VK_FORMAT_R8G8B8_UINT = 27\nconst VK_FORMAT_R8G8B8_SINT = 28\nconst VK_FORMAT_R8G8B8_SRGB = 29\nconst VK_FORMAT_B8G8R8_UNORM = 30\nconst VK_FORMAT_B8G8R8_SNORM = 31\nconst VK_FORMAT_B8G8R8_UINT = 34\nconst VK_FORMAT_B8G8R8_SINT = 35\nconst VK_FORMAT_B8G8R8_SRGB = 36\nconst VK_FORMAT_R8G8B8A8_UNORM = 37\nconst VK_FORMAT_R8G8B8A8_SNORM = 38\nconst VK_FORMAT_R8G8B8A8_UINT = 41\nconst VK_FORMAT_R8G8B8A8_SINT = 42\nconst VK_FORMAT_R8G8B8A8_SRGB = 43\nconst VK_FORMAT_B8G8R8A8_UNORM = 44\nconst VK_FORMAT_B8G8R8A8_SNORM = 45\nconst VK_FORMAT_B8G8R8A8_UINT = 48\nconst VK_FORMAT_B8G8R8A8_SINT = 49\nconst VK_FORMAT_B8G8R8A8_SRGB = 50\nconst VK_FORMAT_A2R10G10B10_UNORM_PACK32 = 58\nconst VK_FORMAT_A2R10G10B10_SNORM_PACK32 = 59\nconst VK_FORMAT_A2R10G10B10_UINT_PACK32 = 62\nconst VK_FORMAT_A2R10G10B10_SINT_PACK32 = 63\nconst VK_FORMAT_A2B10G10R10_UNORM_PACK32 = 64\nconst VK_FORMAT_A2B10G10R10_SNORM_PACK32 = 65\nconst VK_FORMAT_A2B10G10R10_UINT_PACK32 = 68\nconst VK_FORMAT_A2B10G10R10_SINT_PACK32 = 69\nconst VK_FORMAT_R16_UNORM = 70\nconst VK_FORMAT_R16_SNORM = 71\nconst VK_FORMAT_R16_UINT = 74\nconst VK_FORMAT_R16_SINT = 75\nconst VK_FORMAT_R16_SFLOAT = 76\nconst VK_FORMAT_R16G16_UNORM = 77\nconst VK_FORMAT_R16G16_SNORM = 78\nconst VK_FORMAT_R16G16_UINT = 81\nconst VK_FORMAT_R16G16_SINT = 82\nconst VK_FORMAT_R16G16_SFLOAT = 83\nconst VK_FORMAT_R16G16B16_UNORM = 84\nconst VK_FORMAT_R16G16B16_SNORM = 85\nconst VK_FORMAT_R16G16B16_UINT = 88\nconst VK_FORMAT_R16G16B16_SINT = 89\nconst VK_FORMAT_R16G16B16_SFLOAT = 90\nconst VK_FORMAT_R16G16B16A16_UNORM = 91\nconst VK_FORMAT_R16G16B16A16_SNORM = 92\nconst VK_FORMAT_R16G16B16A16_UINT = 95\nconst VK_FORMAT_R16G16B16A16_SINT = 96\nconst VK_FORMAT_R16G16B16A16_SFLOAT = 97\nconst VK_FORMAT_R32_UINT = 98\nconst VK_FORMAT_R32_SINT = 99\nconst VK_FORMAT_R32_SFLOAT = 100\nconst VK_FORMAT_R32G32_UINT = 101\nconst VK_FORMAT_R32G32_SINT = 102\nconst VK_FORMAT_R32G32_SFLOAT = 103\nconst VK_FORMAT_R32G32B32_UINT = 104\nconst VK_FORMAT_R32G32B32_SINT = 105\nconst VK_FORMAT_R32G32B32_SFLOAT = 106\nconst VK_FORMAT_R32G32B32A32_UINT = 107\nconst VK_FORMAT_R32G32B32A32_SINT = 108\nconst VK_FORMAT_R32G32B32A32_SFLOAT = 109\nconst VK_FORMAT_R64_UINT = 110\nconst VK_FORMAT_R64_SINT = 111\nconst VK_FORMAT_R64_SFLOAT = 112\nconst VK_FORMAT_R64G64_UINT = 113\nconst VK_FORMAT_R64G64_SINT = 114\nconst VK_FORMAT_R64G64_SFLOAT = 115\nconst VK_FORMAT_R64G64B64_UINT = 116\nconst VK_FORMAT_R64G64B64_SINT = 117\nconst VK_FORMAT_R64G64B64_SFLOAT = 118\nconst VK_FORMAT_R64G64B64A64_UINT = 119\nconst VK_FORMAT_R64G64B64A64_SINT = 120\nconst VK_FORMAT_R64G64B64A64_SFLOAT = 121\nconst VK_FORMAT_B10G11R11_UFLOAT_PACK32 = 122\nconst VK_FORMAT_E5B9G9R9_UFLOAT_PACK32 = 123\nconst VK_FORMAT_D16_UNORM = 124\nconst VK_FORMAT_X8_D24_UNORM_PACK32 = 125\nconst VK_FORMAT_D32_SFLOAT = 126\nconst VK_FORMAT_S8_UINT = 127\nconst VK_FORMAT_D16_UNORM_S8_UINT = 128\nconst VK_FORMAT_D24_UNORM_S8_UINT = 129\nconst VK_FORMAT_D32_SFLOAT_S8_UINT = 130\nconst VK_FORMAT_BC1_RGB_UNORM_BLOCK = 131\nconst VK_FORMAT_BC1_RGB_SRGB_BLOCK = 132\nconst VK_FORMAT_BC1_RGBA_UNORM_BLOCK = 133\nconst VK_FORMAT_BC1_RGBA_SRGB_BLOCK = 134\nconst VK_FORMAT_BC2_UNORM_BLOCK = 135\nconst VK_FORMAT_BC2_SRGB_BLOCK = 136\nconst VK_FORMAT_BC3_UNORM_BLOCK = 137\nconst VK_FORMAT_BC3_SRGB_BLOCK = 138\nconst VK_FORMAT_BC4_UNORM_BLOCK = 139\nconst VK_FORMAT_BC4_SNORM_BLOCK = 140\nconst VK_FORMAT_BC5_UNORM_BLOCK = 141\nconst VK_FORMAT_BC5_SNORM_BLOCK = 142\nconst VK_FORMAT_BC6H_UFLOAT_BLOCK = 143\nconst VK_FORMAT_BC6H_SFLOAT_BLOCK = 144\nconst VK_FORMAT_BC7_UNORM_BLOCK = 145\nconst VK_FORMAT_BC7_SRGB_BLOCK = 146\nconst VK_FORMAT_ETC2_R8G8B8_UNORM_BLOCK = 147\nconst VK_FORMAT_ETC2_R8G8B8_SRGB_BLOCK = 148\nconst VK_FORMAT_ETC2_R8G8B8A1_UNORM_BLOCK = 149\nconst VK_FORMAT_ETC2_R8G8B8A1_SRGB_BLOCK = 150\nconst VK_FORMAT_ETC2_R8G8B8A8_UNORM_BLOCK = 151\nconst VK_FORMAT_ETC2_R8G8B8A8_SRGB_BLOCK = 152\nconst VK_FORMAT_EAC_R11_UNORM_BLOCK = 153\nconst VK_FORMAT_EAC_R11_SNORM_BLOCK = 154\nconst VK_FORMAT_EAC_R11G11_UNORM_BLOCK = 155\nconst VK_FORMAT_EAC_R11G11_SNORM_BLOCK = 156\nconst VK_FORMAT_ASTC_4x4_UNORM_BLOCK = 157\nconst VK_FORMAT_ASTC_4x4_SRGB_BLOCK = 158\nconst VK_FORMAT_ASTC_5x4_UNORM_BLOCK = 159\nconst VK_FORMAT_ASTC_5x4_SRGB_BLOCK = 160\nconst VK_FORMAT_ASTC_5x5_UNORM_BLOCK = 161\nconst VK_FORMAT_ASTC_5x5_SRGB_BLOCK = 162\nconst VK_FORMAT_ASTC_6x5_UNORM_BLOCK = 163\nconst VK_FORMAT_ASTC_6x5_SRGB_BLOCK = 164\nconst VK_FORMAT_ASTC_6x6_UNORM_BLOCK = 165\nconst VK_FORMAT_ASTC_6x6_SRGB_BLOCK = 166\nconst VK_FORMAT_ASTC_8x5_UNORM_BLOCK = 167\nconst VK_FORMAT_ASTC_8x5_SRGB_BLOCK = 168\nconst VK_FORMAT_ASTC_8x6_UNORM_BLOCK = 169\nconst VK_FORMAT_ASTC_8x6_SRGB_BLOCK = 170\nconst VK_FORMAT_ASTC_8x8_UNORM_BLOCK = 171\nconst VK_FORMAT_ASTC_8x8_SRGB_BLOCK = 172\nconst VK_FORMAT_ASTC_10x5_UNORM_BLOCK = 173\nconst VK_FORMAT_ASTC_10x5_SRGB_BLOCK = 174\nconst VK_FORMAT_ASTC_10x6_UNORM_BLOCK = 175\nconst VK_FORMAT_ASTC_10x6_SRGB_BLOCK = 176\nconst VK_FORMAT_ASTC_10x8_UNORM_BLOCK = 177\nconst VK_FORMAT_ASTC_10x8_SRGB_BLOCK = 178\nconst VK_FORMAT_ASTC_10x10_UNORM_BLOCK = 179\nconst VK_FORMAT_ASTC_10x10_SRGB_BLOCK = 180\nconst VK_FORMAT_ASTC_12x10_UNORM_BLOCK = 181\nconst VK_FORMAT_ASTC_12x10_SRGB_BLOCK = 182\nconst VK_FORMAT_ASTC_12x12_UNORM_BLOCK = 183\nconst VK_FORMAT_ASTC_12x12_SRGB_BLOCK = 184\nconst VK_FORMAT_R10X6_UNORM_PACK16 = 1000156007\nconst VK_FORMAT_R10X6G10X6_UNORM_2PACK16 = 1000156008\nconst VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16 = 1000156009\nconst VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16 = 1000156010\nconst VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16 = 1000156011\nconst VK_FORMAT_R12X4_UNORM_PACK16 = 1000156017\nconst VK_FORMAT_R12X4G12X4_UNORM_2PACK16 = 1000156018\nconst VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16 = 1000156019\nconst VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16 = 1000156020\nconst VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16 = 1000156021\nconst VK_FORMAT_PVRTC1_2BPP_UNORM_BLOCK_IMG = 1000054000\nconst VK_FORMAT_PVRTC1_4BPP_UNORM_BLOCK_IMG = 1000054001\nconst VK_FORMAT_PVRTC2_2BPP_UNORM_BLOCK_IMG = 1000054002\nconst VK_FORMAT_PVRTC2_4BPP_UNORM_BLOCK_IMG = 1000054003\nconst VK_FORMAT_PVRTC1_2BPP_SRGB_BLOCK_IMG = 1000054004\nconst VK_FORMAT_PVRTC1_4BPP_SRGB_BLOCK_IMG = 1000054005\nconst VK_FORMAT_PVRTC2_2BPP_SRGB_BLOCK_IMG = 1000054006\nconst VK_FORMAT_PVRTC2_4BPP_SRGB_BLOCK_IMG = 1000054007\nconst VK_FORMAT_ASTC_4x4_SFLOAT_BLOCK_EXT = 1000066000\nconst VK_FORMAT_ASTC_5x4_SFLOAT_BLOCK_EXT = 1000066001\nconst VK_FORMAT_ASTC_5x5_SFLOAT_BLOCK_EXT = 1000066002\nconst VK_FORMAT_ASTC_6x5_SFLOAT_BLOCK_EXT = 1000066003\nconst VK_FORMAT_ASTC_6x6_SFLOAT_BLOCK_EXT = 1000066004\nconst VK_FORMAT_ASTC_8x5_SFLOAT_BLOCK_EXT = 1000066005\nconst VK_FORMAT_ASTC_8x6_SFLOAT_BLOCK_EXT = 1000066006\nconst VK_FORMAT_ASTC_8x8_SFLOAT_BLOCK_EXT = 1000066007\nconst VK_FORMAT_ASTC_10x5_SFLOAT_BLOCK_EXT = 1000066008\nconst VK_FORMAT_ASTC_10x6_SFLOAT_BLOCK_EXT = 1000066009\nconst VK_FORMAT_ASTC_10x8_SFLOAT_BLOCK_EXT = 1000066010\nconst VK_FORMAT_ASTC_10x10_SFLOAT_BLOCK_EXT = 1000066011\nconst VK_FORMAT_ASTC_12x10_SFLOAT_BLOCK_EXT = 1000066012\nconst VK_FORMAT_ASTC_12x12_SFLOAT_BLOCK_EXT = 1000066013\nconst VK_FORMAT_A4R4G4B4_UNORM_PACK16_EXT = 1000340000\nconst VK_FORMAT_A4B4G4R4_UNORM_PACK16_EXT = 1000340001\n\n/**\n * Represents an unpacked KTX 2.0 texture container. Data for individual mip levels are stored in\n * the `.levels` array, typically compressed in Basis Universal formats. Additional properties\n * provide metadata required to process, transcode, and upload these textures.\n */\n\nclass KTX2Container {\n  constructor() {\n    this.vkFormat = VK_FORMAT_UNDEFINED\n    this.typeSize = 1\n    this.pixelWidth = 0\n    this.pixelHeight = 0\n    this.pixelDepth = 0\n    this.layerCount = 0\n    this.faceCount = 1\n    this.supercompressionScheme = KHR_SUPERCOMPRESSION_NONE\n    this.levels = []\n    this.dataFormatDescriptor = [\n      {\n        vendorId: KHR_DF_VENDORID_KHRONOS,\n        descriptorType: KHR_DF_KHR_DESCRIPTORTYPE_BASICFORMAT,\n        descriptorBlockSize: 0,\n        versionNumber: KHR_DF_VERSION,\n        colorModel: KHR_DF_MODEL_UNSPECIFIED,\n        colorPrimaries: KHR_DF_PRIMARIES_BT709,\n        transferFunction: KHR_DF_TRANSFER_SRGB,\n        flags: KHR_DF_FLAG_ALPHA_STRAIGHT,\n        texelBlockDimension: [0, 0, 0, 0],\n        bytesPlane: [0, 0, 0, 0, 0, 0, 0, 0],\n        samples: [],\n      },\n    ]\n    this.keyValue = {}\n    this.globalData = null\n  }\n}\n\nclass BufferReader {\n  constructor(data, byteOffset, byteLength, littleEndian) {\n    this._dataView = void 0\n    this._littleEndian = void 0\n    this._offset = void 0\n    this._dataView = new DataView(data.buffer, data.byteOffset + byteOffset, byteLength)\n    this._littleEndian = littleEndian\n    this._offset = 0\n  }\n\n  _nextUint8() {\n    const value = this._dataView.getUint8(this._offset)\n\n    this._offset += 1\n    return value\n  }\n\n  _nextUint16() {\n    const value = this._dataView.getUint16(this._offset, this._littleEndian)\n\n    this._offset += 2\n    return value\n  }\n\n  _nextUint32() {\n    const value = this._dataView.getUint32(this._offset, this._littleEndian)\n\n    this._offset += 4\n    return value\n  }\n\n  _nextUint64() {\n    const left = this._dataView.getUint32(this._offset, this._littleEndian)\n\n    const right = this._dataView.getUint32(this._offset + 4, this._littleEndian) // TODO(cleanup): Just test this...\n    // const value = this._littleEndian ? left + (2 ** 32 * right) : (2 ** 32 * left) + right;\n\n    const value = left + 2 ** 32 * right\n    this._offset += 8\n    return value\n  }\n\n  _nextInt32() {\n    const value = this._dataView.getInt32(this._offset, this._littleEndian)\n\n    this._offset += 4\n    return value\n  }\n\n  _nextUint8Array(len) {\n    const value = new Uint8Array(this._dataView.buffer, this._dataView.byteOffset + this._offset, len)\n    this._offset += len\n    return value\n  }\n\n  _skip(bytes) {\n    this._offset += bytes\n    return this\n  }\n\n  _scan(maxByteLength, term) {\n    if (term === void 0) {\n      term = 0x00\n    }\n\n    const byteOffset = this._offset\n    let byteLength = 0\n\n    while (this._dataView.getUint8(this._offset) !== term && byteLength < maxByteLength) {\n      byteLength++\n      this._offset++\n    }\n\n    if (byteLength < maxByteLength) this._offset++\n    return new Uint8Array(this._dataView.buffer, this._dataView.byteOffset + byteOffset, byteLength)\n  }\n}\n\n///////////////////////////////////////////////////\n// Common.\n///////////////////////////////////////////////////\nconst KTX_WRITER = 'KTX-Parse v' + '0.6.0'\nconst NUL = new Uint8Array([0x00]) ///////////////////////////////////////////////////\n// KTX2 Header.\n///////////////////////////////////////////////////\n\nconst KTX2_ID = [\n  // '´', 'K', 'T', 'X', '2', '0', 'ª', '\\r', '\\n', '\\x1A', '\\n'\n  0xab,\n  0x4b,\n  0x54,\n  0x58,\n  0x20,\n  0x32,\n  0x30,\n  0xbb,\n  0x0d,\n  0x0a,\n  0x1a,\n  0x0a,\n]\nconst HEADER_BYTE_LENGTH = 68 // 13 * 4 + 2 * 8\n\n/** Encodes text to an ArrayBuffer. */\nfunction encodeText(text) {\n  if (typeof TextEncoder !== 'undefined') {\n    return new TextEncoder().encode(text)\n  }\n\n  return Buffer.from(text)\n}\n/** Decodes an ArrayBuffer to text. */\n\nfunction decodeText(buffer) {\n  if (typeof TextDecoder !== 'undefined') {\n    return new TextDecoder().decode(buffer)\n  }\n\n  return Buffer.from(buffer).toString('utf8')\n}\n/** Concatenates N ArrayBuffers. */\n\nfunction concat(buffers) {\n  let totalByteLength = 0\n\n  for (const buffer of buffers) {\n    totalByteLength += buffer.byteLength\n  }\n\n  const result = new Uint8Array(totalByteLength)\n  let byteOffset = 0\n\n  for (const buffer of buffers) {\n    result.set(new Uint8Array(buffer), byteOffset)\n    byteOffset += buffer.byteLength\n  }\n\n  return result\n}\n\n/**\n * Parses a KTX 2.0 file, returning an unpacked {@link KTX2Container} instance with all associated\n * data. The container's mip levels and other binary data are pointers into the original file, not\n * copies, so the original file should not be overwritten after reading.\n *\n * @param data Bytes of KTX 2.0 file, as Uint8Array or Buffer.\n */\n\nfunction read(data) {\n  ///////////////////////////////////////////////////\n  // KTX 2.0 Identifier.\n  ///////////////////////////////////////////////////\n  const id = new Uint8Array(data.buffer, data.byteOffset, KTX2_ID.length)\n\n  if (\n    id[0] !== KTX2_ID[0] || // '´'\n    id[1] !== KTX2_ID[1] || // 'K'\n    id[2] !== KTX2_ID[2] || // 'T'\n    id[3] !== KTX2_ID[3] || // 'X'\n    id[4] !== KTX2_ID[4] || // ' '\n    id[5] !== KTX2_ID[5] || // '2'\n    id[6] !== KTX2_ID[6] || // '0'\n    id[7] !== KTX2_ID[7] || // 'ª'\n    id[8] !== KTX2_ID[8] || // '\\r'\n    id[9] !== KTX2_ID[9] || // '\\n'\n    id[10] !== KTX2_ID[10] || // '\\x1A'\n    id[11] !== KTX2_ID[11] // '\\n'\n  ) {\n    throw new Error('Missing KTX 2.0 identifier.')\n  }\n\n  const container = new KTX2Container() ///////////////////////////////////////////////////\n  // Header.\n  ///////////////////////////////////////////////////\n\n  const headerByteLength = 17 * Uint32Array.BYTES_PER_ELEMENT\n  const headerReader = new BufferReader(data, KTX2_ID.length, headerByteLength, true)\n  container.vkFormat = headerReader._nextUint32()\n  container.typeSize = headerReader._nextUint32()\n  container.pixelWidth = headerReader._nextUint32()\n  container.pixelHeight = headerReader._nextUint32()\n  container.pixelDepth = headerReader._nextUint32()\n  container.layerCount = headerReader._nextUint32()\n  container.faceCount = headerReader._nextUint32()\n\n  const levelCount = headerReader._nextUint32()\n\n  container.supercompressionScheme = headerReader._nextUint32()\n\n  const dfdByteOffset = headerReader._nextUint32()\n\n  const dfdByteLength = headerReader._nextUint32()\n\n  const kvdByteOffset = headerReader._nextUint32()\n\n  const kvdByteLength = headerReader._nextUint32()\n\n  const sgdByteOffset = headerReader._nextUint64()\n\n  const sgdByteLength = headerReader._nextUint64() ///////////////////////////////////////////////////\n  // Level Index.\n  ///////////////////////////////////////////////////\n\n  const levelByteLength = levelCount * 3 * 8\n  const levelReader = new BufferReader(data, KTX2_ID.length + headerByteLength, levelByteLength, true)\n\n  for (let i = 0; i < levelCount; i++) {\n    container.levels.push({\n      levelData: new Uint8Array(data.buffer, data.byteOffset + levelReader._nextUint64(), levelReader._nextUint64()),\n      uncompressedByteLength: levelReader._nextUint64(),\n    })\n  } ///////////////////////////////////////////////////\n  // Data Format Descriptor (DFD).\n  ///////////////////////////////////////////////////\n\n  const dfdReader = new BufferReader(data, dfdByteOffset, dfdByteLength, true)\n  const dfd = {\n    vendorId: dfdReader\n      ._skip(\n        4,\n        /* totalSize */\n      )\n      ._nextUint16(),\n    descriptorType: dfdReader._nextUint16(),\n    versionNumber: dfdReader._nextUint16(),\n    descriptorBlockSize: dfdReader._nextUint16(),\n    colorModel: dfdReader._nextUint8(),\n    colorPrimaries: dfdReader._nextUint8(),\n    transferFunction: dfdReader._nextUint8(),\n    flags: dfdReader._nextUint8(),\n    texelBlockDimension: [\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n    ],\n    bytesPlane: [\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n      dfdReader._nextUint8(),\n    ],\n    samples: [],\n  }\n  const sampleStart = 6\n  const sampleWords = 4\n  const numSamples = (dfd.descriptorBlockSize / 4 - sampleStart) / sampleWords\n\n  for (let i = 0; i < numSamples; i++) {\n    const sample = {\n      bitOffset: dfdReader._nextUint16(),\n      bitLength: dfdReader._nextUint8(),\n      channelType: dfdReader._nextUint8(),\n      samplePosition: [dfdReader._nextUint8(), dfdReader._nextUint8(), dfdReader._nextUint8(), dfdReader._nextUint8()],\n      sampleLower: -Infinity,\n      sampleUpper: Infinity,\n    }\n\n    if (sample.channelType & KHR_DF_SAMPLE_DATATYPE_SIGNED) {\n      sample.sampleLower = dfdReader._nextInt32()\n      sample.sampleUpper = dfdReader._nextInt32()\n    } else {\n      sample.sampleLower = dfdReader._nextUint32()\n      sample.sampleUpper = dfdReader._nextUint32()\n    }\n\n    dfd.samples[i] = sample\n  }\n\n  container.dataFormatDescriptor.length = 0\n  container.dataFormatDescriptor.push(dfd) ///////////////////////////////////////////////////\n  // Key/Value Data (KVD).\n  ///////////////////////////////////////////////////\n\n  const kvdReader = new BufferReader(data, kvdByteOffset, kvdByteLength, true)\n\n  while (kvdReader._offset < kvdByteLength) {\n    const keyValueByteLength = kvdReader._nextUint32()\n\n    const keyData = kvdReader._scan(keyValueByteLength)\n\n    const key = decodeText(keyData)\n    container.keyValue[key] = kvdReader._nextUint8Array(keyValueByteLength - keyData.byteLength - 1)\n\n    if (key.match(/^ktx/i)) {\n      const text = decodeText(container.keyValue[key])\n      container.keyValue[key] = text.substring(0, text.lastIndexOf('\\x00'))\n    }\n\n    const kvPadding = keyValueByteLength % 4 ? 4 - (keyValueByteLength % 4) : 0 // align(4)\n    // 4-byte alignment.\n\n    kvdReader._skip(kvPadding)\n  } ///////////////////////////////////////////////////\n  // Supercompression Global Data (SGD).\n  ///////////////////////////////////////////////////\n\n  if (sgdByteLength <= 0) return container\n  const sgdReader = new BufferReader(data, sgdByteOffset, sgdByteLength, true)\n\n  const endpointCount = sgdReader._nextUint16()\n\n  const selectorCount = sgdReader._nextUint16()\n\n  const endpointsByteLength = sgdReader._nextUint32()\n\n  const selectorsByteLength = sgdReader._nextUint32()\n\n  const tablesByteLength = sgdReader._nextUint32()\n\n  const extendedByteLength = sgdReader._nextUint32()\n\n  const imageDescs = []\n\n  for (let i = 0; i < levelCount; i++) {\n    imageDescs.push({\n      imageFlags: sgdReader._nextUint32(),\n      rgbSliceByteOffset: sgdReader._nextUint32(),\n      rgbSliceByteLength: sgdReader._nextUint32(),\n      alphaSliceByteOffset: sgdReader._nextUint32(),\n      alphaSliceByteLength: sgdReader._nextUint32(),\n    })\n  }\n\n  const endpointsByteOffset = sgdByteOffset + sgdReader._offset\n  const selectorsByteOffset = endpointsByteOffset + endpointsByteLength\n  const tablesByteOffset = selectorsByteOffset + selectorsByteLength\n  const extendedByteOffset = tablesByteOffset + tablesByteLength\n  const endpointsData = new Uint8Array(data.buffer, data.byteOffset + endpointsByteOffset, endpointsByteLength)\n  const selectorsData = new Uint8Array(data.buffer, data.byteOffset + selectorsByteOffset, selectorsByteLength)\n  const tablesData = new Uint8Array(data.buffer, data.byteOffset + tablesByteOffset, tablesByteLength)\n  const extendedData = new Uint8Array(data.buffer, data.byteOffset + extendedByteOffset, extendedByteLength)\n  container.globalData = {\n    endpointCount,\n    selectorCount,\n    imageDescs,\n    endpointsData,\n    selectorsData,\n    tablesData,\n    extendedData,\n  }\n  return container\n}\n\nconst DEFAULT_OPTIONS = {\n  keepWriter: false,\n}\n/**\n * Serializes a {@link KTX2Container} instance to a KTX 2.0 file. Mip levels and other binary data\n * are copied into the resulting Uint8Array, so the original container can safely be edited or\n * destroyed after it is serialized.\n *\n * Options:\n * - keepWriter: If true, 'KTXWriter' key/value field is written as provided by the container.\n * \t\tOtherwise, a string for the current ktx-parse version is generated. Default: false.\n *\n * @param container\n * @param options\n */\n\nfunction write(container, options) {\n  if (options === void 0) {\n    options = {}\n  }\n\n  options = { ...DEFAULT_OPTIONS, ...options } ///////////////////////////////////////////////////\n  // Supercompression Global Data (SGD).\n  ///////////////////////////////////////////////////\n\n  let sgdBuffer = new ArrayBuffer(0)\n\n  if (container.globalData) {\n    const sgdHeaderBuffer = new ArrayBuffer(20 + container.globalData.imageDescs.length * 5 * 4)\n    const sgdHeaderView = new DataView(sgdHeaderBuffer)\n    sgdHeaderView.setUint16(0, container.globalData.endpointCount, true)\n    sgdHeaderView.setUint16(2, container.globalData.selectorCount, true)\n    sgdHeaderView.setUint32(4, container.globalData.endpointsData.byteLength, true)\n    sgdHeaderView.setUint32(8, container.globalData.selectorsData.byteLength, true)\n    sgdHeaderView.setUint32(12, container.globalData.tablesData.byteLength, true)\n    sgdHeaderView.setUint32(16, container.globalData.extendedData.byteLength, true)\n\n    for (let i = 0; i < container.globalData.imageDescs.length; i++) {\n      const imageDesc = container.globalData.imageDescs[i]\n      sgdHeaderView.setUint32(20 + i * 5 * 4 + 0, imageDesc.imageFlags, true)\n      sgdHeaderView.setUint32(20 + i * 5 * 4 + 4, imageDesc.rgbSliceByteOffset, true)\n      sgdHeaderView.setUint32(20 + i * 5 * 4 + 8, imageDesc.rgbSliceByteLength, true)\n      sgdHeaderView.setUint32(20 + i * 5 * 4 + 12, imageDesc.alphaSliceByteOffset, true)\n      sgdHeaderView.setUint32(20 + i * 5 * 4 + 16, imageDesc.alphaSliceByteLength, true)\n    }\n\n    sgdBuffer = concat([\n      sgdHeaderBuffer,\n      container.globalData.endpointsData,\n      container.globalData.selectorsData,\n      container.globalData.tablesData,\n      container.globalData.extendedData,\n    ])\n  } ///////////////////////////////////////////////////\n  // Key/Value Data (KVD).\n  ///////////////////////////////////////////////////\n\n  const keyValueData = []\n  let keyValue = container.keyValue\n\n  if (!options.keepWriter) {\n    keyValue = { ...container.keyValue, KTXwriter: KTX_WRITER }\n  }\n\n  for (const key in keyValue) {\n    const value = keyValue[key]\n    const keyData = encodeText(key)\n    const valueData = typeof value === 'string' ? concat([encodeText(value), NUL]) : value\n    const kvByteLength = keyData.byteLength + 1 + valueData.byteLength\n    const kvPadding = kvByteLength % 4 ? 4 - (kvByteLength % 4) : 0 // align(4)\n\n    keyValueData.push(\n      concat([\n        new Uint32Array([kvByteLength]),\n        keyData,\n        NUL,\n        valueData,\n        new Uint8Array(kvPadding).fill(0x00), // align(4)\n      ]),\n    )\n  }\n\n  const kvdBuffer = concat(keyValueData) ///////////////////////////////////////////////////\n  // Data Format Descriptor (DFD).\n  ///////////////////////////////////////////////////\n\n  if (\n    container.dataFormatDescriptor.length !== 1 ||\n    container.dataFormatDescriptor[0].descriptorType !== KHR_DF_KHR_DESCRIPTORTYPE_BASICFORMAT\n  ) {\n    throw new Error('Only BASICFORMAT Data Format Descriptor output supported.')\n  }\n\n  const dfd = container.dataFormatDescriptor[0]\n  const dfdBuffer = new ArrayBuffer(28 + dfd.samples.length * 16)\n  const dfdView = new DataView(dfdBuffer)\n  const descriptorBlockSize = 24 + dfd.samples.length * 16\n  dfdView.setUint32(0, dfdBuffer.byteLength, true)\n  dfdView.setUint16(4, dfd.vendorId, true)\n  dfdView.setUint16(6, dfd.descriptorType, true)\n  dfdView.setUint16(8, dfd.versionNumber, true)\n  dfdView.setUint16(10, descriptorBlockSize, true)\n  dfdView.setUint8(12, dfd.colorModel)\n  dfdView.setUint8(13, dfd.colorPrimaries)\n  dfdView.setUint8(14, dfd.transferFunction)\n  dfdView.setUint8(15, dfd.flags)\n\n  if (!Array.isArray(dfd.texelBlockDimension)) {\n    throw new Error('texelBlockDimension is now an array. For dimensionality `d`, set `d - 1`.')\n  }\n\n  dfdView.setUint8(16, dfd.texelBlockDimension[0])\n  dfdView.setUint8(17, dfd.texelBlockDimension[1])\n  dfdView.setUint8(18, dfd.texelBlockDimension[2])\n  dfdView.setUint8(19, dfd.texelBlockDimension[3])\n\n  for (let i = 0; i < 8; i++) dfdView.setUint8(20 + i, dfd.bytesPlane[i])\n\n  for (let i = 0; i < dfd.samples.length; i++) {\n    const sample = dfd.samples[i]\n    const sampleByteOffset = 28 + i * 16\n\n    if (sample.channelID) {\n      throw new Error('channelID has been renamed to channelType.')\n    }\n\n    dfdView.setUint16(sampleByteOffset + 0, sample.bitOffset, true)\n    dfdView.setUint8(sampleByteOffset + 2, sample.bitLength)\n    dfdView.setUint8(sampleByteOffset + 3, sample.channelType)\n    dfdView.setUint8(sampleByteOffset + 4, sample.samplePosition[0])\n    dfdView.setUint8(sampleByteOffset + 5, sample.samplePosition[1])\n    dfdView.setUint8(sampleByteOffset + 6, sample.samplePosition[2])\n    dfdView.setUint8(sampleByteOffset + 7, sample.samplePosition[3])\n\n    if (sample.channelType & KHR_DF_SAMPLE_DATATYPE_SIGNED) {\n      dfdView.setInt32(sampleByteOffset + 8, sample.sampleLower, true)\n      dfdView.setInt32(sampleByteOffset + 12, sample.sampleUpper, true)\n    } else {\n      dfdView.setUint32(sampleByteOffset + 8, sample.sampleLower, true)\n      dfdView.setUint32(sampleByteOffset + 12, sample.sampleUpper, true)\n    }\n  } ///////////////////////////////////////////////////\n  // Data alignment.\n  ///////////////////////////////////////////////////\n\n  const dfdByteOffset = KTX2_ID.length + HEADER_BYTE_LENGTH + container.levels.length * 3 * 8\n  const kvdByteOffset = dfdByteOffset + dfdBuffer.byteLength\n  let sgdByteOffset = sgdBuffer.byteLength > 0 ? kvdByteOffset + kvdBuffer.byteLength : 0\n  if (sgdByteOffset % 8) sgdByteOffset += 8 - (sgdByteOffset % 8) // align(8)\n  ///////////////////////////////////////////////////\n  // Level Index.\n  ///////////////////////////////////////////////////\n\n  const levelData = []\n  const levelIndex = new DataView(new ArrayBuffer(container.levels.length * 3 * 8))\n  let levelDataByteOffset = (sgdByteOffset || kvdByteOffset + kvdBuffer.byteLength) + sgdBuffer.byteLength\n\n  for (let i = 0; i < container.levels.length; i++) {\n    const level = container.levels[i]\n    levelData.push(level.levelData)\n    levelIndex.setBigUint64(i * 24 + 0, BigInt(levelDataByteOffset), true)\n    levelIndex.setBigUint64(i * 24 + 8, BigInt(level.levelData.byteLength), true)\n    levelIndex.setBigUint64(i * 24 + 16, BigInt(level.uncompressedByteLength), true)\n    levelDataByteOffset += level.levelData.byteLength\n  } ///////////////////////////////////////////////////\n  // Header.\n  ///////////////////////////////////////////////////\n\n  const headerBuffer = new ArrayBuffer(HEADER_BYTE_LENGTH)\n  const headerView = new DataView(headerBuffer)\n  headerView.setUint32(0, container.vkFormat, true)\n  headerView.setUint32(4, container.typeSize, true)\n  headerView.setUint32(8, container.pixelWidth, true)\n  headerView.setUint32(12, container.pixelHeight, true)\n  headerView.setUint32(16, container.pixelDepth, true)\n  headerView.setUint32(20, container.layerCount, true)\n  headerView.setUint32(24, container.faceCount, true)\n  headerView.setUint32(28, container.levels.length, true)\n  headerView.setUint32(32, container.supercompressionScheme, true)\n  headerView.setUint32(36, dfdByteOffset, true)\n  headerView.setUint32(40, dfdBuffer.byteLength, true)\n  headerView.setUint32(44, kvdByteOffset, true)\n  headerView.setUint32(48, kvdBuffer.byteLength, true)\n  headerView.setBigUint64(52, BigInt(sgdBuffer.byteLength > 0 ? sgdByteOffset : 0), true)\n  headerView.setBigUint64(60, BigInt(sgdBuffer.byteLength), true) ///////////////////////////////////////////////////\n  // Compose.\n  ///////////////////////////////////////////////////\n\n  return new Uint8Array(\n    concat([\n      new Uint8Array(KTX2_ID).buffer,\n      headerBuffer,\n      levelIndex.buffer,\n      dfdBuffer,\n      kvdBuffer,\n      sgdByteOffset > 0\n        ? new ArrayBuffer(sgdByteOffset - (kvdByteOffset + kvdBuffer.byteLength)) // align(8)\n        : new ArrayBuffer(0),\n      sgdBuffer,\n      ...levelData,\n    ]),\n  )\n}\n\nexport {\n  KHR_DF_CHANNEL_RGBSDA_ALPHA,\n  KHR_DF_CHANNEL_RGBSDA_BLUE,\n  KHR_DF_CHANNEL_RGBSDA_DEPTH,\n  KHR_DF_CHANNEL_RGBSDA_GREEN,\n  KHR_DF_CHANNEL_RGBSDA_RED,\n  KHR_DF_CHANNEL_RGBSDA_STENCIL,\n  KHR_DF_FLAG_ALPHA_PREMULTIPLIED,\n  KHR_DF_FLAG_ALPHA_STRAIGHT,\n  KHR_DF_KHR_DESCRIPTORTYPE_BASICFORMAT,\n  KHR_DF_MODEL_ASTC,\n  KHR_DF_MODEL_ETC1,\n  KHR_DF_MODEL_ETC1S,\n  KHR_DF_MODEL_ETC2,\n  KHR_DF_MODEL_RGBSDA,\n  KHR_DF_MODEL_UASTC,\n  KHR_DF_MODEL_UNSPECIFIED,\n  KHR_DF_PRIMARIES_ACES,\n  KHR_DF_PRIMARIES_ACESCC,\n  KHR_DF_PRIMARIES_ADOBERGB,\n  KHR_DF_PRIMARIES_BT2020,\n  KHR_DF_PRIMARIES_BT601_EBU,\n  KHR_DF_PRIMARIES_BT601_SMPTE,\n  KHR_DF_PRIMARIES_BT709,\n  KHR_DF_PRIMARIES_CIEXYZ,\n  KHR_DF_PRIMARIES_DISPLAYP3,\n  KHR_DF_PRIMARIES_NTSC1953,\n  KHR_DF_PRIMARIES_PAL525,\n  KHR_DF_PRIMARIES_UNSPECIFIED,\n  KHR_DF_SAMPLE_DATATYPE_EXPONENT,\n  KHR_DF_SAMPLE_DATATYPE_FLOAT,\n  KHR_DF_SAMPLE_DATATYPE_LINEAR,\n  KHR_DF_SAMPLE_DATATYPE_SIGNED,\n  KHR_DF_TRANSFER_ACESCC,\n  KHR_DF_TRANSFER_ACESCCT,\n  KHR_DF_TRANSFER_ADOBERGB,\n  KHR_DF_TRANSFER_BT1886,\n  KHR_DF_TRANSFER_DCIP3,\n  KHR_DF_TRANSFER_HLG_EOTF,\n  KHR_DF_TRANSFER_HLG_OETF,\n  KHR_DF_TRANSFER_ITU,\n  KHR_DF_TRANSFER_LINEAR,\n  KHR_DF_TRANSFER_NTSC,\n  KHR_DF_TRANSFER_PAL625_EOTF,\n  KHR_DF_TRANSFER_PAL_OETF,\n  KHR_DF_TRANSFER_PQ_EOTF,\n  KHR_DF_TRANSFER_PQ_OETF,\n  KHR_DF_TRANSFER_SLOG,\n  KHR_DF_TRANSFER_SLOG2,\n  KHR_DF_TRANSFER_SRGB,\n  KHR_DF_TRANSFER_ST240,\n  KHR_DF_TRANSFER_UNSPECIFIED,\n  KHR_DF_VENDORID_KHRONOS,\n  KHR_DF_VERSION,\n  KHR_SUPERCOMPRESSION_BASISLZ,\n  KHR_SUPERCOMPRESSION_NONE,\n  KHR_SUPERCOMPRESSION_ZLIB,\n  KHR_SUPERCOMPRESSION_ZSTD,\n  KTX2Container,\n  VK_FORMAT_A1R5G5B5_UNORM_PACK16,\n  VK_FORMAT_A2B10G10R10_SINT_PACK32,\n  VK_FORMAT_A2B10G10R10_SNORM_PACK32,\n  VK_FORMAT_A2B10G10R10_UINT_PACK32,\n  VK_FORMAT_A2B10G10R10_UNORM_PACK32,\n  VK_FORMAT_A2R10G10B10_SINT_PACK32,\n  VK_FORMAT_A2R10G10B10_SNORM_PACK32,\n  VK_FORMAT_A2R10G10B10_UINT_PACK32,\n  VK_FORMAT_A2R10G10B10_UNORM_PACK32,\n  VK_FORMAT_A4B4G4R4_UNORM_PACK16_EXT,\n  VK_FORMAT_A4R4G4B4_UNORM_PACK16_EXT,\n  VK_FORMAT_ASTC_10x10_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_10x10_SRGB_BLOCK,\n  VK_FORMAT_ASTC_10x10_UNORM_BLOCK,\n  VK_FORMAT_ASTC_10x5_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_10x5_SRGB_BLOCK,\n  VK_FORMAT_ASTC_10x5_UNORM_BLOCK,\n  VK_FORMAT_ASTC_10x6_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_10x6_SRGB_BLOCK,\n  VK_FORMAT_ASTC_10x6_UNORM_BLOCK,\n  VK_FORMAT_ASTC_10x8_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_10x8_SRGB_BLOCK,\n  VK_FORMAT_ASTC_10x8_UNORM_BLOCK,\n  VK_FORMAT_ASTC_12x10_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_12x10_SRGB_BLOCK,\n  VK_FORMAT_ASTC_12x10_UNORM_BLOCK,\n  VK_FORMAT_ASTC_12x12_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_12x12_SRGB_BLOCK,\n  VK_FORMAT_ASTC_12x12_UNORM_BLOCK,\n  VK_FORMAT_ASTC_4x4_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_4x4_SRGB_BLOCK,\n  VK_FORMAT_ASTC_4x4_UNORM_BLOCK,\n  VK_FORMAT_ASTC_5x4_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_5x4_SRGB_BLOCK,\n  VK_FORMAT_ASTC_5x4_UNORM_BLOCK,\n  VK_FORMAT_ASTC_5x5_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_5x5_SRGB_BLOCK,\n  VK_FORMAT_ASTC_5x5_UNORM_BLOCK,\n  VK_FORMAT_ASTC_6x5_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_6x5_SRGB_BLOCK,\n  VK_FORMAT_ASTC_6x5_UNORM_BLOCK,\n  VK_FORMAT_ASTC_6x6_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_6x6_SRGB_BLOCK,\n  VK_FORMAT_ASTC_6x6_UNORM_BLOCK,\n  VK_FORMAT_ASTC_8x5_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_8x5_SRGB_BLOCK,\n  VK_FORMAT_ASTC_8x5_UNORM_BLOCK,\n  VK_FORMAT_ASTC_8x6_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_8x6_SRGB_BLOCK,\n  VK_FORMAT_ASTC_8x6_UNORM_BLOCK,\n  VK_FORMAT_ASTC_8x8_SFLOAT_BLOCK_EXT,\n  VK_FORMAT_ASTC_8x8_SRGB_BLOCK,\n  VK_FORMAT_ASTC_8x8_UNORM_BLOCK,\n  VK_FORMAT_B10G11R11_UFLOAT_PACK32,\n  VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16,\n  VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16,\n  VK_FORMAT_B4G4R4A4_UNORM_PACK16,\n  VK_FORMAT_B5G5R5A1_UNORM_PACK16,\n  VK_FORMAT_B5G6R5_UNORM_PACK16,\n  VK_FORMAT_B8G8R8A8_SINT,\n  VK_FORMAT_B8G8R8A8_SNORM,\n  VK_FORMAT_B8G8R8A8_SRGB,\n  VK_FORMAT_B8G8R8A8_UINT,\n  VK_FORMAT_B8G8R8A8_UNORM,\n  VK_FORMAT_B8G8R8_SINT,\n  VK_FORMAT_B8G8R8_SNORM,\n  VK_FORMAT_B8G8R8_SRGB,\n  VK_FORMAT_B8G8R8_UINT,\n  VK_FORMAT_B8G8R8_UNORM,\n  VK_FORMAT_BC1_RGBA_SRGB_BLOCK,\n  VK_FORMAT_BC1_RGBA_UNORM_BLOCK,\n  VK_FORMAT_BC1_RGB_SRGB_BLOCK,\n  VK_FORMAT_BC1_RGB_UNORM_BLOCK,\n  VK_FORMAT_BC2_SRGB_BLOCK,\n  VK_FORMAT_BC2_UNORM_BLOCK,\n  VK_FORMAT_BC3_SRGB_BLOCK,\n  VK_FORMAT_BC3_UNORM_BLOCK,\n  VK_FORMAT_BC4_SNORM_BLOCK,\n  VK_FORMAT_BC4_UNORM_BLOCK,\n  VK_FORMAT_BC5_SNORM_BLOCK,\n  VK_FORMAT_BC5_UNORM_BLOCK,\n  VK_FORMAT_BC6H_SFLOAT_BLOCK,\n  VK_FORMAT_BC6H_UFLOAT_BLOCK,\n  VK_FORMAT_BC7_SRGB_BLOCK,\n  VK_FORMAT_BC7_UNORM_BLOCK,\n  VK_FORMAT_D16_UNORM,\n  VK_FORMAT_D16_UNORM_S8_UINT,\n  VK_FORMAT_D24_UNORM_S8_UINT,\n  VK_FORMAT_D32_SFLOAT,\n  VK_FORMAT_D32_SFLOAT_S8_UINT,\n  VK_FORMAT_E5B9G9R9_UFLOAT_PACK32,\n  VK_FORMAT_EAC_R11G11_SNORM_BLOCK,\n  VK_FORMAT_EAC_R11G11_UNORM_BLOCK,\n  VK_FORMAT_EAC_R11_SNORM_BLOCK,\n  VK_FORMAT_EAC_R11_UNORM_BLOCK,\n  VK_FORMAT_ETC2_R8G8B8A1_SRGB_BLOCK,\n  VK_FORMAT_ETC2_R8G8B8A1_UNORM_BLOCK,\n  VK_FORMAT_ETC2_R8G8B8A8_SRGB_BLOCK,\n  VK_FORMAT_ETC2_R8G8B8A8_UNORM_BLOCK,\n  VK_FORMAT_ETC2_R8G8B8_SRGB_BLOCK,\n  VK_FORMAT_ETC2_R8G8B8_UNORM_BLOCK,\n  VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16,\n  VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16,\n  VK_FORMAT_PVRTC1_2BPP_SRGB_BLOCK_IMG,\n  VK_FORMAT_PVRTC1_2BPP_UNORM_BLOCK_IMG,\n  VK_FORMAT_PVRTC1_4BPP_SRGB_BLOCK_IMG,\n  VK_FORMAT_PVRTC1_4BPP_UNORM_BLOCK_IMG,\n  VK_FORMAT_PVRTC2_2BPP_SRGB_BLOCK_IMG,\n  VK_FORMAT_PVRTC2_2BPP_UNORM_BLOCK_IMG,\n  VK_FORMAT_PVRTC2_4BPP_SRGB_BLOCK_IMG,\n  VK_FORMAT_PVRTC2_4BPP_UNORM_BLOCK_IMG,\n  VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16,\n  VK_FORMAT_R10X6G10X6_UNORM_2PACK16,\n  VK_FORMAT_R10X6_UNORM_PACK16,\n  VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16,\n  VK_FORMAT_R12X4G12X4_UNORM_2PACK16,\n  VK_FORMAT_R12X4_UNORM_PACK16,\n  VK_FORMAT_R16G16B16A16_SFLOAT,\n  VK_FORMAT_R16G16B16A16_SINT,\n  VK_FORMAT_R16G16B16A16_SNORM,\n  VK_FORMAT_R16G16B16A16_UINT,\n  VK_FORMAT_R16G16B16A16_UNORM,\n  VK_FORMAT_R16G16B16_SFLOAT,\n  VK_FORMAT_R16G16B16_SINT,\n  VK_FORMAT_R16G16B16_SNORM,\n  VK_FORMAT_R16G16B16_UINT,\n  VK_FORMAT_R16G16B16_UNORM,\n  VK_FORMAT_R16G16_SFLOAT,\n  VK_FORMAT_R16G16_SINT,\n  VK_FORMAT_R16G16_SNORM,\n  VK_FORMAT_R16G16_UINT,\n  VK_FORMAT_R16G16_UNORM,\n  VK_FORMAT_R16_SFLOAT,\n  VK_FORMAT_R16_SINT,\n  VK_FORMAT_R16_SNORM,\n  VK_FORMAT_R16_UINT,\n  VK_FORMAT_R16_UNORM,\n  VK_FORMAT_R32G32B32A32_SFLOAT,\n  VK_FORMAT_R32G32B32A32_SINT,\n  VK_FORMAT_R32G32B32A32_UINT,\n  VK_FORMAT_R32G32B32_SFLOAT,\n  VK_FORMAT_R32G32B32_SINT,\n  VK_FORMAT_R32G32B32_UINT,\n  VK_FORMAT_R32G32_SFLOAT,\n  VK_FORMAT_R32G32_SINT,\n  VK_FORMAT_R32G32_UINT,\n  VK_FORMAT_R32_SFLOAT,\n  VK_FORMAT_R32_SINT,\n  VK_FORMAT_R32_UINT,\n  VK_FORMAT_R4G4B4A4_UNORM_PACK16,\n  VK_FORMAT_R4G4_UNORM_PACK8,\n  VK_FORMAT_R5G5B5A1_UNORM_PACK16,\n  VK_FORMAT_R5G6B5_UNORM_PACK16,\n  VK_FORMAT_R64G64B64A64_SFLOAT,\n  VK_FORMAT_R64G64B64A64_SINT,\n  VK_FORMAT_R64G64B64A64_UINT,\n  VK_FORMAT_R64G64B64_SFLOAT,\n  VK_FORMAT_R64G64B64_SINT,\n  VK_FORMAT_R64G64B64_UINT,\n  VK_FORMAT_R64G64_SFLOAT,\n  VK_FORMAT_R64G64_SINT,\n  VK_FORMAT_R64G64_UINT,\n  VK_FORMAT_R64_SFLOAT,\n  VK_FORMAT_R64_SINT,\n  VK_FORMAT_R64_UINT,\n  VK_FORMAT_R8G8B8A8_SINT,\n  VK_FORMAT_R8G8B8A8_SNORM,\n  VK_FORMAT_R8G8B8A8_SRGB,\n  VK_FORMAT_R8G8B8A8_UINT,\n  VK_FORMAT_R8G8B8A8_UNORM,\n  VK_FORMAT_R8G8B8_SINT,\n  VK_FORMAT_R8G8B8_SNORM,\n  VK_FORMAT_R8G8B8_SRGB,\n  VK_FORMAT_R8G8B8_UINT,\n  VK_FORMAT_R8G8B8_UNORM,\n  VK_FORMAT_R8G8_SINT,\n  VK_FORMAT_R8G8_SNORM,\n  VK_FORMAT_R8G8_SRGB,\n  VK_FORMAT_R8G8_UINT,\n  VK_FORMAT_R8G8_UNORM,\n  VK_FORMAT_R8_SINT,\n  VK_FORMAT_R8_SNORM,\n  VK_FORMAT_R8_SRGB,\n  VK_FORMAT_R8_UINT,\n  VK_FORMAT_R8_UNORM,\n  VK_FORMAT_S8_UINT,\n  VK_FORMAT_UNDEFINED,\n  VK_FORMAT_X8_D24_UNORM_PACK32,\n  read,\n  write,\n}\n//# sourceMappingURL=ktx-parse.esm.js.map\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2ZS;AAxZJ,MAAC,4BAA4B;AAE7B,MAAC,4BAA4B;AAK7B,MAAC,wCAAwC;AACzC,MAAC,0BAA0B;AAC3B,MAAC,iBAAiB;AAClB,MAAC,2BAA2B;AAQ5B,MAAC,6BAA6B;AAC9B,MAAC,kCAAkC;AAGnC,MAAC,uBAAuB;AAiBxB,MAAC,+BAA+B;AAChC,MAAC,yBAAyB;AAS1B,MAAC,6BAA6B;AAS9B,MAAC,gCAAgC;AAMjC,MAAC,sBAAsB;AASvB,MAAC,qBAAqB;AAItB,MAAC,oBAAoB;AACrB,MAAC,uBAAuB;AAIxB,MAAC,sBAAsB;AAWvB,MAAC,2BAA2B;AAI5B,MAAC,0BAA0B;AAkB3B,MAAC,uBAAuB;AAKxB,MAAC,0BAA0B;AAU3B,MAAC,gCAAgC;AAGjC,MAAC,uBAAuB;AAGxB,MAAC,0BAA0B;AAM3B,MAAC,gCAAgC;AAwDjC,MAAC,iCAAiC;AAClC,MAAC,gCAAgC;AA4DtC,MAAM,cAAc;IAClB,aAAc;QACZ,IAAA,CAAK,QAAA,GAAW;QAChB,IAAA,CAAK,QAAA,GAAW;QAChB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,SAAA,GAAY;QACjB,IAAA,CAAK,sBAAA,GAAyB;QAC9B,IAAA,CAAK,MAAA,GAAS,CAAE,CAAA;QAChB,IAAA,CAAK,oBAAA,GAAuB;YAC1B;gBACE,UAAU;gBACV,gBAAgB;gBAChB,qBAAqB;gBACrB,eAAe;gBACf,YAAY;gBACZ,gBAAgB;gBAChB,kBAAkB;gBAClB,OAAO;gBACP,qBAAqB;oBAAC;oBAAG;oBAAG;oBAAG,CAAC;iBAAA;gBAChC,YAAY;oBAAC;oBAAG;oBAAG;oBAAG;oBAAG;oBAAG;oBAAG;oBAAG,CAAC;iBAAA;gBACnC,SAAS,CAAE,CAAA;YACZ;SACF;QACD,IAAA,CAAK,QAAA,GAAW,CAAE;QAClB,IAAA,CAAK,UAAA,GAAa;IACnB;AACH;AAEA,MAAM,aAAa;IACjB,YAAY,IAAA,EAAM,UAAA,EAAY,UAAA,EAAY,YAAA,CAAc;QACtD,IAAA,CAAK,SAAA,GAAY,KAAA;QACjB,IAAA,CAAK,aAAA,GAAgB,KAAA;QACrB,IAAA,CAAK,OAAA,GAAU,KAAA;QACf,IAAA,CAAK,SAAA,GAAY,IAAI,SAAS,KAAK,MAAA,EAAQ,KAAK,UAAA,GAAa,YAAY,UAAU;QACnF,IAAA,CAAK,aAAA,GAAgB;QACrB,IAAA,CAAK,OAAA,GAAU;IAChB;IAED,aAAa;QACX,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU,QAAA,CAAS,IAAA,CAAK,OAAO;QAElD,IAAA,CAAK,OAAA,IAAW;QAChB,OAAO;IACR;IAED,cAAc;QACZ,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU,SAAA,CAAU,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,aAAa;QAEvE,IAAA,CAAK,OAAA,IAAW;QAChB,OAAO;IACR;IAED,cAAc;QACZ,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU,SAAA,CAAU,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,aAAa;QAEvE,IAAA,CAAK,OAAA,IAAW;QAChB,OAAO;IACR;IAED,cAAc;QACZ,MAAM,OAAO,IAAA,CAAK,SAAA,CAAU,SAAA,CAAU,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,aAAa;QAEtE,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU,SAAA,CAAU,IAAA,CAAK,OAAA,GAAU,GAAG,IAAA,CAAK,aAAa;QAG3E,MAAM,QAAQ,OAAO,KAAK,KAAK;QAC/B,IAAA,CAAK,OAAA,IAAW;QAChB,OAAO;IACR;IAED,aAAa;QACX,MAAM,QAAQ,IAAA,CAAK,SAAA,CAAU,QAAA,CAAS,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,aAAa;QAEtE,IAAA,CAAK,OAAA,IAAW;QAChB,OAAO;IACR;IAED,gBAAgB,GAAA,EAAK;QACnB,MAAM,QAAQ,IAAI,WAAW,IAAA,CAAK,SAAA,CAAU,MAAA,EAAQ,IAAA,CAAK,SAAA,CAAU,UAAA,GAAa,IAAA,CAAK,OAAA,EAAS,GAAG;QACjG,IAAA,CAAK,OAAA,IAAW;QAChB,OAAO;IACR;IAED,MAAM,KAAA,EAAO;QACX,IAAA,CAAK,OAAA,IAAW;QAChB,OAAO,IAAA;IACR;IAED,MAAM,aAAA,EAAe,IAAA,EAAM;QACzB,IAAI,SAAS,KAAA,GAAQ;YACnB,OAAO;QACR;QAED,MAAM,aAAa,IAAA,CAAK,OAAA;QACxB,IAAI,aAAa;QAEjB,MAAO,IAAA,CAAK,SAAA,CAAU,QAAA,CAAS,IAAA,CAAK,OAAO,MAAM,QAAQ,aAAa,cAAe;YACnF;YACA,IAAA,CAAK,OAAA;QACN;QAED,IAAI,aAAa,eAAe,IAAA,CAAK,OAAA;QACrC,OAAO,IAAI,WAAW,IAAA,CAAK,SAAA,CAAU,MAAA,EAAQ,IAAA,CAAK,SAAA,CAAU,UAAA,GAAa,YAAY,UAAU;IAChG;AACH;AAUA,MAAM,UAAU;IAAA,8DAAA;IAEd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AAaA,SAAS,WAAW,MAAA,EAAQ;IAC1B,IAAI,OAAO,gBAAgB,aAAa;QACtC,OAAO,IAAI,YAAW,EAAG,MAAA,CAAO,MAAM;IACvC;IAED,8SAAO,CAAO,IAAA,CAAK,MAAM,EAAE,QAAA,CAAS,MAAM;AAC5C;AA6BA,SAAS,KAAK,IAAA,EAAM;IAIlB,MAAM,KAAK,IAAI,WAAW,KAAK,MAAA,EAAQ,KAAK,UAAA,EAAY,QAAQ,MAAM;IAEtE,IACE,EAAA,CAAG,CAAC,CAAA,KAAM,OAAA,CAAQ,CAAC,CAAA,IAAA,MAAA;IACnB,EAAA,CAAG,CAAC,CAAA,KAAM,OAAA,CAAQ,CAAC,CAAA,IAAA,MAAA;IACnB,EAAA,CAAG,CAAC,CAAA,KAAM,OAAA,CAAQ,CAAC,CAAA,IAAA,MAAA;IACnB,EAAA,CAAG,CAAC,CAAA,KAAM,OAAA,CAAQ,CAAC,CAAA,IAAA,MAAA;IACnB,EAAA,CAAG,CAAC,CAAA,KAAM,OAAA,CAAQ,CAAC,CAAA,IAAA,MAAA;IACnB,EAAA,CAAG,CAAC,CAAA,KAAM,OAAA,CAAQ,CAAC,CAAA,IAAA,MAAA;IACnB,EAAA,CAAG,CAAC,CAAA,KAAM,OAAA,CAAQ,CAAC,CAAA,IAAA,MAAA;IACnB,EAAA,CAAG,CAAC,CAAA,KAAM,OAAA,CAAQ,CAAC,CAAA,IAAA,MAAA;IACnB,EAAA,CAAG,CAAC,CAAA,KAAM,OAAA,CAAQ,CAAC,CAAA,IAAA,OAAA;IACnB,EAAA,CAAG,CAAC,CAAA,KAAM,OAAA,CAAQ,CAAC,CAAA,IAAA,OAAA;IACnB,EAAA,CAAG,EAAE,CAAA,KAAM,OAAA,CAAQ,EAAE,CAAA,IAAA,SAAA;IACrB,EAAA,CAAG,EAAE,CAAA,KAAM,OAAA,CAAQ,EAAE,CAAA,EACrB;QACA,MAAM,IAAI,MAAM,6BAA6B;IAC9C;IAED,MAAM,YAAY,IAAI,cAAe;IAIrC,MAAM,mBAAmB,KAAK,YAAY,iBAAA;IAC1C,MAAM,eAAe,IAAI,aAAa,MAAM,QAAQ,MAAA,EAAQ,kBAAkB,IAAI;IAClF,UAAU,QAAA,GAAW,aAAa,WAAA,CAAa;IAC/C,UAAU,QAAA,GAAW,aAAa,WAAA,CAAa;IAC/C,UAAU,UAAA,GAAa,aAAa,WAAA,CAAa;IACjD,UAAU,WAAA,GAAc,aAAa,WAAA,CAAa;IAClD,UAAU,UAAA,GAAa,aAAa,WAAA,CAAa;IACjD,UAAU,UAAA,GAAa,aAAa,WAAA,CAAa;IACjD,UAAU,SAAA,GAAY,aAAa,WAAA,CAAa;IAEhD,MAAM,aAAa,aAAa,WAAA,CAAa;IAE7C,UAAU,sBAAA,GAAyB,aAAa,WAAA,CAAa;IAE7D,MAAM,gBAAgB,aAAa,WAAA,CAAa;IAEhD,MAAM,gBAAgB,aAAa,WAAA,CAAa;IAEhD,MAAM,gBAAgB,aAAa,WAAA,CAAa;IAEhD,MAAM,gBAAgB,aAAa,WAAA,CAAa;IAEhD,MAAM,gBAAgB,aAAa,WAAA,CAAa;IAEhD,MAAM,gBAAgB,aAAa,WAAA,CAAa;IAIhD,MAAM,kBAAkB,aAAa,IAAI;IACzC,MAAM,cAAc,IAAI,aAAa,MAAM,QAAQ,MAAA,GAAS,kBAAkB,iBAAiB,IAAI;IAEnG,IAAA,IAAS,IAAI,GAAG,IAAI,YAAY,IAAK;QACnC,UAAU,MAAA,CAAO,IAAA,CAAK;YACpB,WAAW,IAAI,WAAW,KAAK,MAAA,EAAQ,KAAK,UAAA,GAAa,YAAY,WAAA,CAAW,GAAI,YAAY,WAAA,CAAW,CAAE;YAC7G,wBAAwB,YAAY,WAAA,CAAa;QACvD,CAAK;IACF;IAID,MAAM,YAAY,IAAI,aAAa,MAAM,eAAe,eAAe,IAAI;IAC3E,MAAM,MAAM;QACV,UAAU,UACP,KAAA,CACC,GAGD,WAAA,CAAa;QAChB,gBAAgB,UAAU,WAAA,CAAa;QACvC,eAAe,UAAU,WAAA,CAAa;QACtC,qBAAqB,UAAU,WAAA,CAAa;QAC5C,YAAY,UAAU,UAAA,CAAY;QAClC,gBAAgB,UAAU,UAAA,CAAY;QACtC,kBAAkB,UAAU,UAAA,CAAY;QACxC,OAAO,UAAU,UAAA,CAAY;QAC7B,qBAAqB;YACnB,UAAU,UAAA,CAAY;YACtB,UAAU,UAAA,CAAY;YACtB,UAAU,UAAA,CAAY;YACtB,UAAU,UAAA,CAAY;SACvB;QACD,YAAY;YACV,UAAU,UAAA,CAAY;YACtB,UAAU,UAAA,CAAY;YACtB,UAAU,UAAA,CAAY;YACtB,UAAU,UAAA,CAAY;YACtB,UAAU,UAAA,CAAY;YACtB,UAAU,UAAA,CAAY;YACtB,UAAU,UAAA,CAAY;YACtB,UAAU,UAAA,CAAY;SACvB;QACD,SAAS,CAAE,CAAA;IACZ;IACD,MAAM,cAAc;IACpB,MAAM,cAAc;IACpB,MAAM,aAAA,CAAc,IAAI,mBAAA,GAAsB,IAAI,WAAA,IAAe;IAEjE,IAAA,IAAS,IAAI,GAAG,IAAI,YAAY,IAAK;QACnC,MAAM,SAAS;YACb,WAAW,UAAU,WAAA,CAAa;YAClC,WAAW,UAAU,UAAA,CAAY;YACjC,aAAa,UAAU,UAAA,CAAY;YACnC,gBAAgB;gBAAC,UAAU,UAAA,CAAU;gBAAI,UAAU,UAAA;gBAAc,UAAU,UAAA,CAAU;gBAAI,UAAU,UAAA,CAAU,CAAE;aAAA;YAC/G,aAAa,CAAA;YACb,aAAa;QACd;QAED,IAAI,OAAO,WAAA,GAAc,+BAA+B;YACtD,OAAO,WAAA,GAAc,UAAU,UAAA,CAAY;YAC3C,OAAO,WAAA,GAAc,UAAU,UAAA,CAAY;QACjD,OAAW;YACL,OAAO,WAAA,GAAc,UAAU,WAAA,CAAa;YAC5C,OAAO,WAAA,GAAc,UAAU,WAAA,CAAa;QAC7C;QAED,IAAI,OAAA,CAAQ,CAAC,CAAA,GAAI;IAClB;IAED,UAAU,oBAAA,CAAqB,MAAA,GAAS;IACxC,UAAU,oBAAA,CAAqB,IAAA,CAAK,GAAG;IAIvC,MAAM,YAAY,IAAI,aAAa,MAAM,eAAe,eAAe,IAAI;IAE3E,MAAO,UAAU,OAAA,GAAU,cAAe;QACxC,MAAM,qBAAqB,UAAU,WAAA,CAAa;QAElD,MAAM,UAAU,UAAU,KAAA,CAAM,kBAAkB;QAElD,MAAM,MAAM,WAAW,OAAO;QAC9B,UAAU,QAAA,CAAS,GAAG,CAAA,GAAI,UAAU,eAAA,CAAgB,qBAAqB,QAAQ,UAAA,GAAa,CAAC;QAE/F,IAAI,IAAI,KAAA,CAAM,OAAO,GAAG;YACtB,MAAM,OAAO,WAAW,UAAU,QAAA,CAAS,GAAG,CAAC;YAC/C,UAAU,QAAA,CAAS,GAAG,CAAA,GAAI,KAAK,SAAA,CAAU,GAAG,KAAK,WAAA,CAAY,IAAM,CAAC;QACrE;QAED,MAAM,YAAY,qBAAqB,IAAI,IAAK,qBAAqB,IAAK;QAG1E,UAAU,KAAA,CAAM,SAAS;IAC1B;IAID,IAAI,iBAAiB,GAAG,OAAO;IAC/B,MAAM,YAAY,IAAI,aAAa,MAAM,eAAe,eAAe,IAAI;IAE3E,MAAM,gBAAgB,UAAU,WAAA,CAAa;IAE7C,MAAM,gBAAgB,UAAU,WAAA,CAAa;IAE7C,MAAM,sBAAsB,UAAU,WAAA,CAAa;IAEnD,MAAM,sBAAsB,UAAU,WAAA,CAAa;IAEnD,MAAM,mBAAmB,UAAU,WAAA,CAAa;IAEhD,MAAM,qBAAqB,UAAU,WAAA,CAAa;IAElD,MAAM,aAAa,CAAE,CAAA;IAErB,IAAA,IAAS,IAAI,GAAG,IAAI,YAAY,IAAK;QACnC,WAAW,IAAA,CAAK;YACd,YAAY,UAAU,WAAA,CAAa;YACnC,oBAAoB,UAAU,WAAA,CAAa;YAC3C,oBAAoB,UAAU,WAAA,CAAa;YAC3C,sBAAsB,UAAU,WAAA,CAAa;YAC7C,sBAAsB,UAAU,WAAA,CAAa;QACnD,CAAK;IACF;IAED,MAAM,sBAAsB,gBAAgB,UAAU,OAAA;IACtD,MAAM,sBAAsB,sBAAsB;IAClD,MAAM,mBAAmB,sBAAsB;IAC/C,MAAM,qBAAqB,mBAAmB;IAC9C,MAAM,gBAAgB,IAAI,WAAW,KAAK,MAAA,EAAQ,KAAK,UAAA,GAAa,qBAAqB,mBAAmB;IAC5G,MAAM,gBAAgB,IAAI,WAAW,KAAK,MAAA,EAAQ,KAAK,UAAA,GAAa,qBAAqB,mBAAmB;IAC5G,MAAM,aAAa,IAAI,WAAW,KAAK,MAAA,EAAQ,KAAK,UAAA,GAAa,kBAAkB,gBAAgB;IACnG,MAAM,eAAe,IAAI,WAAW,KAAK,MAAA,EAAQ,KAAK,UAAA,GAAa,oBAAoB,kBAAkB;IACzG,UAAU,UAAA,GAAa;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;IACD;IACD,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9900, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9906, "column": 0}, "map": {"version": 3, "file": "zstddec.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/libs/zstddec.ts"], "sourcesContent": ["interface DecoderExports {\n  memory: Uint8Array\n\n  ZSTD_findDecompressedSize: (compressedPtr: number, compressedSize: number) => BigInt\n  ZSTD_decompress: (\n    uncompressedPtr: number,\n    uncompressedSize: number,\n    compressedPtr: number,\n    compressedSize: number,\n  ) => number\n  malloc: (ptr: number) => number\n  free: (ptr: number) => void\n}\n\nlet init: Promise<void>\nlet instance: { exports: DecoderExports }\nlet heap: Uint8Array\n\nconst IMPORT_OBJECT = {\n  env: {\n    emscripten_notify_memory_growth: function (index: number): void {\n      heap = new Uint8Array(instance.exports.memory.buffer)\n    },\n  },\n}\n\n/**\n * ZSTD (Zstandard) decoder.\n */\nexport class ZSTDDecoder {\n  init(): Promise<void> {\n    if (init) return init\n\n    if (typeof fetch !== 'undefined') {\n      // Web.\n\n      init = fetch('data:application/wasm;base64,' + wasm)\n        .then((response) => response.arrayBuffer())\n        .then((arrayBuffer) => WebAssembly.instantiate(arrayBuffer, IMPORT_OBJECT))\n        .then(this._init)\n    } else {\n      // Node.js.\n\n      init = WebAssembly.instantiate(Buffer.from(wasm, 'base64'), IMPORT_OBJECT).then(this._init)\n    }\n\n    return init\n  }\n\n  _init(result: WebAssembly.WebAssemblyInstantiatedSource): void {\n    instance = (result.instance as unknown) as { exports: DecoderExports }\n\n    IMPORT_OBJECT.env.emscripten_notify_memory_growth(0) // initialize heap.\n  }\n\n  decode(array: Uint8Array, uncompressedSize = 0): Uint8Array {\n    if (!instance) throw new Error(`ZSTDDecoder: Await .init() before decoding.`)\n\n    // Write compressed data into WASM memory.\n    const compressedSize = array.byteLength\n    const compressedPtr = instance.exports.malloc(compressedSize)\n    heap.set(array, compressedPtr)\n\n    // Decompress into WASM memory.\n    uncompressedSize =\n      uncompressedSize || Number(instance.exports.ZSTD_findDecompressedSize(compressedPtr, compressedSize))\n    const uncompressedPtr = instance.exports.malloc(uncompressedSize)\n    const actualSize = instance.exports.ZSTD_decompress(\n      uncompressedPtr,\n      uncompressedSize,\n      compressedPtr,\n      compressedSize,\n    )\n\n    // Read decompressed data and free WASM memory.\n    const dec = heap.slice(uncompressedPtr, uncompressedPtr + actualSize)\n    instance.exports.free(compressedPtr)\n    instance.exports.free(uncompressedPtr)\n\n    return dec\n  }\n}\n\n/**\n * BSD License\n *\n * For Zstandard software\n *\n * Copyright (c) 2016-present, Yann Collet, Facebook, Inc. All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n *\n *  * Redistributions of source code must retain the above copyright notice, this\n *    list of conditions and the following disclaimer.\n *\n *  * Redistributions in binary form must reproduce the above copyright notice,\n *    this list of conditions and the following disclaimer in the documentation\n *    and/or other materials provided with the distribution.\n *\n *  * Neither the name Facebook nor the names of its contributors may be used to\n *    endorse or promote products derived from this software without specific\n *    prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR\n * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON\n * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n// wasm:begin\nconst wasm =\n  '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'\n// wasm:end\n"], "names": [], "mappings": ";;;AA2CqC;AA7BrC,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,MAAM,gBAAgB;IACpB,KAAK;QACH,iCAAiC,SAAU,KAAA,EAAqB;YAC9D,OAAO,IAAI,WAAW,SAAS,OAAA,CAAQ,MAAA,CAAO,MAAM;QACtD;IACF;AACF;AAKO,MAAM,YAAY;IACvB,OAAsB;QAChB,IAAA,MAAa,OAAA;QAEb,IAAA,OAAO,UAAU,aAAa;YAGzB,OAAA,MAAM,kCAAkC,IAAI,EAChD,IAAA,CAAK,CAAC,WAAa,SAAS,WAAA,EAAa,EACzC,IAAA,CAAK,CAAC,cAAgB,YAAY,WAAA,CAAY,aAAa,aAAa,CAAC,EACzE,IAAA,CAAK,IAAA,CAAK,KAAK;QAAA,OACb;YAGE,OAAA,YAAY,WAAA,wSAAY,CAAO,IAAA,CAAK,MAAM,QAAQ,GAAG,aAAa,EAAE,IAAA,CAAK,IAAA,CAAK,KAAK;QAC5F;QAEO,OAAA;IACT;IAEA,MAAM,MAAA,EAAyD;QAC7D,WAAY,OAAO,QAAA;QAEL,cAAA,GAAA,CAAI,+BAAA,CAAgC,CAAC;IACrD;IAEA,OAAO,KAAA,EAAmB,mBAAmB,CAAA,EAAe;QAC1D,IAAI,CAAC,UAAgB,MAAA,IAAI,MAAM,CAAA,2CAAA,CAA6C;QAG5E,MAAM,iBAAiB,MAAM,UAAA;QAC7B,MAAM,gBAAgB,SAAS,OAAA,CAAQ,MAAA,CAAO,cAAc;QACvD,KAAA,GAAA,CAAI,OAAO,aAAa;QAG7B,mBACE,oBAAoB,OAAO,SAAS,OAAA,CAAQ,yBAAA,CAA0B,eAAe,cAAc,CAAC;QACtG,MAAM,kBAAkB,SAAS,OAAA,CAAQ,MAAA,CAAO,gBAAgB;QAC1D,MAAA,aAAa,SAAS,OAAA,CAAQ,eAAA,CAClC,iBACA,kBACA,eACA;QAIF,MAAM,MAAM,KAAK,KAAA,CAAM,iBAAiB,kBAAkB,UAAU;QAC3D,SAAA,OAAA,CAAQ,IAAA,CAAK,aAAa;QAC1B,SAAA,OAAA,CAAQ,IAAA,CAAK,eAAe;QAE9B,OAAA;IACT;AACF;AAmCA,MAAM,OACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9951, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9957, "column": 0}, "map": {"version": 3, "file": "AsciiEffect.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/effects/AsciiEffect.js"], "sourcesContent": ["/**\n * Ascii generation is based on https://github.com/hassadee/jsascii/blob/master/jsascii.js\n *\n * 16 April 2012 - @blurspline\n */\n\nclass AsciiEffect {\n  constructor(renderer, charSet = ' .:-=+*#%@', options = {}) {\n    // ' .,:;=|iI+hHOE#`$';\n    // darker bolder character set from https://github.com/saw/Canvas-ASCII-Art/\n    // ' .\\'`^\",:;Il!i~+_-?][}{1)(|/tfjrxnuvczXYUJCLQ0OZmwqpdbkhao*#MW&8%B@$'.split('');\n\n    // Some ASCII settings\n\n    const fResolution = options['resolution'] || 0.15 // Higher for more details\n    const iScale = options['scale'] || 1\n    const bColor = options['color'] || false // nice but slows down rendering!\n    const bAlpha = options['alpha'] || false // Transparency\n    const bBlock = options['block'] || false // blocked characters. like good O dos\n    const bInvert = options['invert'] || false // black is white, white is black\n    const strResolution = options['strResolution'] || 'low'\n\n    let width, height\n\n    const domElement = document.createElement('div')\n    domElement.style.cursor = 'default'\n\n    const oAscii = document.createElement('table')\n    domElement.appendChild(oAscii)\n\n    let iWidth, iHeight\n    let oImg\n\n    this.setSize = function (w, h) {\n      width = w\n      height = h\n\n      renderer.setSize(w, h)\n\n      initAsciiSize()\n    }\n\n    this.render = function (scene, camera) {\n      renderer.render(scene, camera)\n      asciifyImage(oAscii)\n    }\n\n    this.domElement = domElement\n\n    // Throw in ascii library from https://github.com/hassadee/jsascii/blob/master/jsascii.js (MIT License)\n\n    function initAsciiSize() {\n      iWidth = Math.floor(width * fResolution)\n      iHeight = Math.floor(height * fResolution)\n\n      oCanvas.width = iWidth\n      oCanvas.height = iHeight\n      // oCanvas.style.display = \"none\";\n      // oCanvas.style.width = iWidth;\n      // oCanvas.style.height = iHeight;\n\n      oImg = renderer.domElement\n\n      if (oImg.style.backgroundColor) {\n        oAscii.rows[0].cells[0].style.backgroundColor = oImg.style.backgroundColor\n        oAscii.rows[0].cells[0].style.color = oImg.style.color\n      }\n\n      oAscii.cellSpacing = 0\n      oAscii.cellPadding = 0\n\n      const oStyle = oAscii.style\n      oStyle.whiteSpace = 'pre'\n      oStyle.margin = '0px'\n      oStyle.padding = '0px'\n      oStyle.letterSpacing = fLetterSpacing + 'px'\n      oStyle.fontFamily = strFont\n      oStyle.fontSize = fFontSize + 'px'\n      oStyle.lineHeight = fLineHeight + 'px'\n      oStyle.textAlign = 'left'\n      oStyle.textDecoration = 'none'\n    }\n\n    const aDefaultCharList = ' .,:;i1tfLCG08@'.split('')\n    const aDefaultColorCharList = ' CGO08@'.split('')\n    const strFont = 'courier new, monospace'\n\n    const oCanvasImg = renderer.domElement\n\n    const oCanvas = document.createElement('canvas')\n    if (!oCanvas.getContext) {\n      return\n    }\n\n    const oCtx = oCanvas.getContext('2d')\n    if (!oCtx.getImageData) {\n      return\n    }\n\n    let aCharList = bColor ? aDefaultColorCharList : aDefaultCharList\n\n    if (charSet) aCharList = charSet\n\n    // Setup dom\n\n    const fFontSize = (2 / fResolution) * iScale\n    const fLineHeight = (2 / fResolution) * iScale\n\n    // adjust letter-spacing for all combinations of scale and resolution to get it to fit the image width.\n\n    let fLetterSpacing = 0\n\n    if (strResolution == 'low') {\n      switch (iScale) {\n        case 1:\n          fLetterSpacing = -1\n          break\n        case 2:\n        case 3:\n          fLetterSpacing = -2.1\n          break\n        case 4:\n          fLetterSpacing = -3.1\n          break\n        case 5:\n          fLetterSpacing = -4.15\n          break\n      }\n    }\n\n    if (strResolution == 'medium') {\n      switch (iScale) {\n        case 1:\n          fLetterSpacing = 0\n          break\n        case 2:\n          fLetterSpacing = -1\n          break\n        case 3:\n          fLetterSpacing = -1.04\n          break\n        case 4:\n        case 5:\n          fLetterSpacing = -2.1\n          break\n      }\n    }\n\n    if (strResolution == 'high') {\n      switch (iScale) {\n        case 1:\n        case 2:\n          fLetterSpacing = 0\n          break\n        case 3:\n        case 4:\n        case 5:\n          fLetterSpacing = -1\n          break\n      }\n    }\n\n    // can't get a span or div to flow like an img element, but a table works?\n\n    // convert img element to ascii\n\n    function asciifyImage(oAscii) {\n      oCtx.clearRect(0, 0, iWidth, iHeight)\n      oCtx.drawImage(oCanvasImg, 0, 0, iWidth, iHeight)\n      const oImgData = oCtx.getImageData(0, 0, iWidth, iHeight).data\n\n      // Coloring loop starts now\n      let strChars = ''\n\n      // console.time('rendering');\n\n      for (let y = 0; y < iHeight; y += 2) {\n        for (let x = 0; x < iWidth; x++) {\n          const iOffset = (y * iWidth + x) * 4\n\n          const iRed = oImgData[iOffset]\n          const iGreen = oImgData[iOffset + 1]\n          const iBlue = oImgData[iOffset + 2]\n          const iAlpha = oImgData[iOffset + 3]\n          let iCharIdx\n\n          let fBrightness\n\n          fBrightness = (0.3 * iRed + 0.59 * iGreen + 0.11 * iBlue) / 255\n          // fBrightness = (0.3*iRed + 0.5*iGreen + 0.3*iBlue) / 255;\n\n          if (iAlpha == 0) {\n            // should calculate alpha instead, but quick hack :)\n            //fBrightness *= (iAlpha / 255);\n            fBrightness = 1\n          }\n\n          iCharIdx = Math.floor((1 - fBrightness) * (aCharList.length - 1))\n\n          if (bInvert) {\n            iCharIdx = aCharList.length - iCharIdx - 1\n          }\n\n          // good for debugging\n          //fBrightness = Math.floor(fBrightness * 10);\n          //strThisChar = fBrightness;\n\n          let strThisChar = aCharList[iCharIdx]\n\n          if (strThisChar === undefined || strThisChar == ' ') strThisChar = '&nbsp;'\n\n          if (bColor) {\n            strChars +=\n              \"<span style='\" +\n              'color:rgb(' +\n              iRed +\n              ',' +\n              iGreen +\n              ',' +\n              iBlue +\n              ');' +\n              (bBlock ? 'background-color:rgb(' + iRed + ',' + iGreen + ',' + iBlue + ');' : '') +\n              (bAlpha ? 'opacity:' + iAlpha / 255 + ';' : '') +\n              \"'>\" +\n              strThisChar +\n              '</span>'\n          } else {\n            strChars += strThisChar\n          }\n        }\n\n        strChars += '<br/>'\n      }\n\n      oAscii.innerHTML = `<tr><td style=\"display:block;width:${width}px;height:${height}px;overflow:hidden\">${strChars}</td></tr>`\n\n      // console.timeEnd('rendering');\n\n      // return oAscii;\n    }\n  }\n}\n\nexport { AsciiEffect }\n"], "names": ["oAscii"], "mappings": ";;;AAMA,MAAM,YAAY;IAChB,YAAY,QAAA,EAAU,UAAU,YAAA,EAAc,UAAU,CAAA,CAAA,CAAI;QAO1D,MAAM,cAAc,OAAA,CAAQ,YAAY,CAAA,IAAK;QAC7C,MAAM,SAAS,OAAA,CAAQ,OAAO,CAAA,IAAK;QACnC,MAAM,SAAS,OAAA,CAAQ,OAAO,CAAA,IAAK;QACnC,MAAM,SAAS,OAAA,CAAQ,OAAO,CAAA,IAAK;QACnC,MAAM,SAAS,OAAA,CAAQ,OAAO,CAAA,IAAK;QACnC,MAAM,UAAU,OAAA,CAAQ,QAAQ,CAAA,IAAK;QACrC,MAAM,gBAAgB,OAAA,CAAQ,eAAe,CAAA,IAAK;QAElD,IAAI,OAAO;QAEX,MAAM,aAAa,SAAS,aAAA,CAAc,KAAK;QAC/C,WAAW,KAAA,CAAM,MAAA,GAAS;QAE1B,MAAM,SAAS,SAAS,aAAA,CAAc,OAAO;QAC7C,WAAW,WAAA,CAAY,MAAM;QAE7B,IAAI,QAAQ;QACZ,IAAI;QAEJ,IAAA,CAAK,OAAA,GAAU,SAAU,CAAA,EAAG,CAAA,EAAG;YAC7B,QAAQ;YACR,SAAS;YAET,SAAS,OAAA,CAAQ,GAAG,CAAC;YAErB,cAAe;QAChB;QAED,IAAA,CAAK,MAAA,GAAS,SAAU,KAAA,EAAO,MAAA,EAAQ;YACrC,SAAS,MAAA,CAAO,OAAO,MAAM;YAC7B,aAAa,MAAM;QACpB;QAED,IAAA,CAAK,UAAA,GAAa;QAIlB,SAAS,gBAAgB;YACvB,SAAS,KAAK,KAAA,CAAM,QAAQ,WAAW;YACvC,UAAU,KAAK,KAAA,CAAM,SAAS,WAAW;YAEzC,QAAQ,KAAA,GAAQ;YAChB,QAAQ,MAAA,GAAS;YAKjB,OAAO,SAAS,UAAA;YAEhB,IAAI,KAAK,KAAA,CAAM,eAAA,EAAiB;gBAC9B,OAAO,IAAA,CAAK,CAAC,CAAA,CAAE,KAAA,CAAM,CAAC,CAAA,CAAE,KAAA,CAAM,eAAA,GAAkB,KAAK,KAAA,CAAM,eAAA;gBAC3D,OAAO,IAAA,CAAK,CAAC,CAAA,CAAE,KAAA,CAAM,CAAC,CAAA,CAAE,KAAA,CAAM,KAAA,GAAQ,KAAK,KAAA,CAAM,KAAA;YAClD;YAED,OAAO,WAAA,GAAc;YACrB,OAAO,WAAA,GAAc;YAErB,MAAM,SAAS,OAAO,KAAA;YACtB,OAAO,UAAA,GAAa;YACpB,OAAO,MAAA,GAAS;YAChB,OAAO,OAAA,GAAU;YACjB,OAAO,aAAA,GAAgB,iBAAiB;YACxC,OAAO,UAAA,GAAa;YACpB,OAAO,QAAA,GAAW,YAAY;YAC9B,OAAO,UAAA,GAAa,cAAc;YAClC,OAAO,SAAA,GAAY;YACnB,OAAO,cAAA,GAAiB;QACzB;QAED,MAAM,mBAAmB,kBAAkB,KAAA,CAAM,EAAE;QACnD,MAAM,wBAAwB,UAAU,KAAA,CAAM,EAAE;QAChD,MAAM,UAAU;QAEhB,MAAM,aAAa,SAAS,UAAA;QAE5B,MAAM,UAAU,SAAS,aAAA,CAAc,QAAQ;QAC/C,IAAI,CAAC,QAAQ,UAAA,EAAY;YACvB;QACD;QAED,MAAM,OAAO,QAAQ,UAAA,CAAW,IAAI;QACpC,IAAI,CAAC,KAAK,YAAA,EAAc;YACtB;QACD;QAED,IAAI,YAAY,SAAS,wBAAwB;QAEjD,IAAI,SAAS,YAAY;QAIzB,MAAM,YAAa,IAAI,cAAe;QACtC,MAAM,cAAe,IAAI,cAAe;QAIxC,IAAI,iBAAiB;QAErB,IAAI,iBAAiB,OAAO;YAC1B,OAAQ,QAAM;gBACZ,KAAK;oBACH,iBAAiB,CAAA;oBACjB;gBACF,KAAK;gBACL,KAAK;oBACH,iBAAiB,CAAA;oBACjB;gBACF,KAAK;oBACH,iBAAiB,CAAA;oBACjB;gBACF,KAAK;oBACH,iBAAiB,CAAA;oBACjB;YACH;QACF;QAED,IAAI,iBAAiB,UAAU;YAC7B,OAAQ,QAAM;gBACZ,KAAK;oBACH,iBAAiB;oBACjB;gBACF,KAAK;oBACH,iBAAiB,CAAA;oBACjB;gBACF,KAAK;oBACH,iBAAiB,CAAA;oBACjB;gBACF,KAAK;gBACL,KAAK;oBACH,iBAAiB,CAAA;oBACjB;YACH;QACF;QAED,IAAI,iBAAiB,QAAQ;YAC3B,OAAQ,QAAM;gBACZ,KAAK;gBACL,KAAK;oBACH,iBAAiB;oBACjB;gBACF,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,iBAAiB,CAAA;oBACjB;YACH;QACF;QAMD,SAAS,aAAaA,OAAAA,EAAQ;YAC5B,KAAK,SAAA,CAAU,GAAG,GAAG,QAAQ,OAAO;YACpC,KAAK,SAAA,CAAU,YAAY,GAAG,GAAG,QAAQ,OAAO;YAChD,MAAM,WAAW,KAAK,YAAA,CAAa,GAAG,GAAG,QAAQ,OAAO,EAAE,IAAA;YAG1D,IAAI,WAAW;YAIf,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,KAAK,EAAG;gBACnC,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,IAAK;oBAC/B,MAAM,UAAA,CAAW,IAAI,SAAS,CAAA,IAAK;oBAEnC,MAAM,OAAO,QAAA,CAAS,OAAO,CAAA;oBAC7B,MAAM,SAAS,QAAA,CAAS,UAAU,CAAC,CAAA;oBACnC,MAAM,QAAQ,QAAA,CAAS,UAAU,CAAC,CAAA;oBAClC,MAAM,SAAS,QAAA,CAAS,UAAU,CAAC,CAAA;oBACnC,IAAI;oBAEJ,IAAI;oBAEJ,cAAA,CAAe,MAAM,OAAO,OAAO,SAAS,OAAO,KAAA,IAAS;oBAG5D,IAAI,UAAU,GAAG;wBAGf,cAAc;oBACf;oBAED,WAAW,KAAK,KAAA,CAAA,CAAO,IAAI,WAAA,IAAA,CAAgB,UAAU,MAAA,GAAS,CAAA,CAAE;oBAEhE,IAAI,SAAS;wBACX,WAAW,UAAU,MAAA,GAAS,WAAW;oBAC1C;oBAMD,IAAI,cAAc,SAAA,CAAU,QAAQ,CAAA;oBAEpC,IAAI,gBAAgB,KAAA,KAAa,eAAe,KAAK,cAAc;oBAEnE,IAAI,QAAQ;wBACV,YACE,4BAEA,OACA,MACA,SACA,MACA,QACA,OAAA,CACC,SAAS,0BAA0B,OAAO,MAAM,SAAS,MAAM,QAAQ,OAAO,EAAA,IAAA,CAC9E,SAAS,aAAa,SAAS,MAAM,MAAM,EAAA,IAC5C,OACA,cACA;oBACd,OAAiB;wBACL,YAAY;oBACb;gBACF;gBAED,YAAY;YACb;YAEDA,QAAO,SAAA,GAAY,CAAA,mCAAA,EAAsC,MAAA,UAAA,EAAkB,OAAA,oBAAA,EAA6B,SAAA,UAAA,CAAA;QAKzG;IACF;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 10112, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 10118, "column": 0}, "map": {"version": 3, "file": "NURBSUtils.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/curves/NURBSUtils.js"], "sourcesContent": ["import { Vector3, Vector4 } from 'three'\n\n/**\n * NURBS utils\n *\n * See NURBSCurve and NURBSSurface.\n **/\n\n/**************************************************************\n *\tNURBS Utils\n **************************************************************/\n\n/*\nFinds knot vector span.\n\np : degree\nu : parametric value\nU : knot vector\n\nreturns the span\n*/\nfunction findSpan(p, u, U) {\n  const n = U.length - p - 1\n\n  if (u >= U[n]) {\n    return n - 1\n  }\n\n  if (u <= U[p]) {\n    return p\n  }\n\n  let low = p\n  let high = n\n  let mid = Math.floor((low + high) / 2)\n\n  while (u < U[mid] || u >= U[mid + 1]) {\n    if (u < U[mid]) {\n      high = mid\n    } else {\n      low = mid\n    }\n\n    mid = Math.floor((low + high) / 2)\n  }\n\n  return mid\n}\n\n/*\nCalculate basis functions. See The NURBS Book, page 70, algorithm A2.2\n\nspan : span in which u lies\nu    : parametric point\np    : degree\nU    : knot vector\n\nreturns array[p+1] with basis functions values.\n*/\nfunction calcBasisFunctions(span, u, p, U) {\n  const N = []\n  const left = []\n  const right = []\n  N[0] = 1.0\n\n  for (let j = 1; j <= p; ++j) {\n    left[j] = u - U[span + 1 - j]\n    right[j] = U[span + j] - u\n\n    let saved = 0.0\n\n    for (let r = 0; r < j; ++r) {\n      const rv = right[r + 1]\n      const lv = left[j - r]\n      const temp = N[r] / (rv + lv)\n      N[r] = saved + rv * temp\n      saved = lv * temp\n    }\n\n    N[j] = saved\n  }\n\n  return N\n}\n\n/*\nCalculate B-Spline curve points. See The NURBS Book, page 82, algorithm A3.1.\n\np : degree of B-Spline\nU : knot vector\nP : control points (x, y, z, w)\nu : parametric point\n\nreturns point for given u\n*/\nfunction calcBSplinePoint(p, U, P, u) {\n  const span = findSpan(p, u, U)\n  const N = calcBasisFunctions(span, u, p, U)\n  const C = new Vector4(0, 0, 0, 0)\n\n  for (let j = 0; j <= p; ++j) {\n    const point = P[span - p + j]\n    const Nj = N[j]\n    const wNj = point.w * Nj\n    C.x += point.x * wNj\n    C.y += point.y * wNj\n    C.z += point.z * wNj\n    C.w += point.w * Nj\n  }\n\n  return C\n}\n\n/*\nCalculate basis functions derivatives. See The NURBS Book, page 72, algorithm A2.3.\n\nspan : span in which u lies\nu    : parametric point\np    : degree\nn    : number of derivatives to calculate\nU    : knot vector\n\nreturns array[n+1][p+1] with basis functions derivatives\n*/\nfunction calcBasisFunctionDerivatives(span, u, p, n, U) {\n  const zeroArr = []\n  for (let i = 0; i <= p; ++i) zeroArr[i] = 0.0\n\n  const ders = []\n\n  for (let i = 0; i <= n; ++i) ders[i] = zeroArr.slice(0)\n\n  const ndu = []\n\n  for (let i = 0; i <= p; ++i) ndu[i] = zeroArr.slice(0)\n\n  ndu[0][0] = 1.0\n\n  const left = zeroArr.slice(0)\n  const right = zeroArr.slice(0)\n\n  for (let j = 1; j <= p; ++j) {\n    left[j] = u - U[span + 1 - j]\n    right[j] = U[span + j] - u\n\n    let saved = 0.0\n\n    for (let r = 0; r < j; ++r) {\n      const rv = right[r + 1]\n      const lv = left[j - r]\n      ndu[j][r] = rv + lv\n\n      const temp = ndu[r][j - 1] / ndu[j][r]\n      ndu[r][j] = saved + rv * temp\n      saved = lv * temp\n    }\n\n    ndu[j][j] = saved\n  }\n\n  for (let j = 0; j <= p; ++j) {\n    ders[0][j] = ndu[j][p]\n  }\n\n  for (let r = 0; r <= p; ++r) {\n    let s1 = 0\n    let s2 = 1\n\n    const a = []\n    for (let i = 0; i <= p; ++i) {\n      a[i] = zeroArr.slice(0)\n    }\n\n    a[0][0] = 1.0\n\n    for (let k = 1; k <= n; ++k) {\n      let d = 0.0\n      const rk = r - k\n      const pk = p - k\n\n      if (r >= k) {\n        a[s2][0] = a[s1][0] / ndu[pk + 1][rk]\n        d = a[s2][0] * ndu[rk][pk]\n      }\n\n      const j1 = rk >= -1 ? 1 : -rk\n      const j2 = r - 1 <= pk ? k - 1 : p - r\n\n      for (let j = j1; j <= j2; ++j) {\n        a[s2][j] = (a[s1][j] - a[s1][j - 1]) / ndu[pk + 1][rk + j]\n        d += a[s2][j] * ndu[rk + j][pk]\n      }\n\n      if (r <= pk) {\n        a[s2][k] = -a[s1][k - 1] / ndu[pk + 1][r]\n        d += a[s2][k] * ndu[r][pk]\n      }\n\n      ders[k][r] = d\n\n      const j = s1\n      s1 = s2\n      s2 = j\n    }\n  }\n\n  let r = p\n\n  for (let k = 1; k <= n; ++k) {\n    for (let j = 0; j <= p; ++j) {\n      ders[k][j] *= r\n    }\n\n    r *= p - k\n  }\n\n  return ders\n}\n\n/*\n\tCalculate derivatives of a B-Spline. See The NURBS Book, page 93, algorithm A3.2.\n\n\tp  : degree\n\tU  : knot vector\n\tP  : control points\n\tu  : Parametric points\n\tnd : number of derivatives\n\n\treturns array[d+1] with derivatives\n\t*/\nfunction calcBSplineDerivatives(p, U, P, u, nd) {\n  const du = nd < p ? nd : p\n  const CK = []\n  const span = findSpan(p, u, U)\n  const nders = calcBasisFunctionDerivatives(span, u, p, du, U)\n  const Pw = []\n\n  for (let i = 0; i < P.length; ++i) {\n    const point = P[i].clone()\n    const w = point.w\n\n    point.x *= w\n    point.y *= w\n    point.z *= w\n\n    Pw[i] = point\n  }\n\n  for (let k = 0; k <= du; ++k) {\n    const point = Pw[span - p].clone().multiplyScalar(nders[k][0])\n\n    for (let j = 1; j <= p; ++j) {\n      point.add(Pw[span - p + j].clone().multiplyScalar(nders[k][j]))\n    }\n\n    CK[k] = point\n  }\n\n  for (let k = du + 1; k <= nd + 1; ++k) {\n    CK[k] = new Vector4(0, 0, 0)\n  }\n\n  return CK\n}\n\n/*\nCalculate \"K over I\"\n\nreturns k!/(i!(k-i)!)\n*/\nfunction calcKoverI(k, i) {\n  let nom = 1\n\n  for (let j = 2; j <= k; ++j) {\n    nom *= j\n  }\n\n  let denom = 1\n\n  for (let j = 2; j <= i; ++j) {\n    denom *= j\n  }\n\n  for (let j = 2; j <= k - i; ++j) {\n    denom *= j\n  }\n\n  return nom / denom\n}\n\n/*\nCalculate derivatives (0-nd) of rational curve. See The NURBS Book, page 127, algorithm A4.2.\n\nPders : result of function calcBSplineDerivatives\n\nreturns array with derivatives for rational curve.\n*/\nfunction calcRationalCurveDerivatives(Pders) {\n  const nd = Pders.length\n  const Aders = []\n  const wders = []\n\n  for (let i = 0; i < nd; ++i) {\n    const point = Pders[i]\n    Aders[i] = new Vector3(point.x, point.y, point.z)\n    wders[i] = point.w\n  }\n\n  const CK = []\n\n  for (let k = 0; k < nd; ++k) {\n    const v = Aders[k].clone()\n\n    for (let i = 1; i <= k; ++i) {\n      v.sub(CK[k - i].clone().multiplyScalar(calcKoverI(k, i) * wders[i]))\n    }\n\n    CK[k] = v.divideScalar(wders[0])\n  }\n\n  return CK\n}\n\n/*\nCalculate NURBS curve derivatives. See The NURBS Book, page 127, algorithm A4.2.\n\np  : degree\nU  : knot vector\nP  : control points in homogeneous space\nu  : parametric points\nnd : number of derivatives\n\nreturns array with derivatives.\n*/\nfunction calcNURBSDerivatives(p, U, P, u, nd) {\n  const Pders = calcBSplineDerivatives(p, U, P, u, nd)\n  return calcRationalCurveDerivatives(Pders)\n}\n\n/*\nCalculate rational B-Spline surface point. See The NURBS Book, page 134, algorithm A4.3.\n\np1, p2 : degrees of B-Spline surface\nU1, U2 : knot vectors\nP      : control points (x, y, z, w)\nu, v   : parametric values\n\nreturns point for given (u, v)\n*/\nfunction calcSurfacePoint(p, q, U, V, P, u, v, target) {\n  const uspan = findSpan(p, u, U)\n  const vspan = findSpan(q, v, V)\n  const Nu = calcBasisFunctions(uspan, u, p, U)\n  const Nv = calcBasisFunctions(vspan, v, q, V)\n  const temp = []\n\n  for (let l = 0; l <= q; ++l) {\n    temp[l] = new Vector4(0, 0, 0, 0)\n    for (let k = 0; k <= p; ++k) {\n      const point = P[uspan - p + k][vspan - q + l].clone()\n      const w = point.w\n      point.x *= w\n      point.y *= w\n      point.z *= w\n      temp[l].add(point.multiplyScalar(Nu[k]))\n    }\n  }\n\n  const Sw = new Vector4(0, 0, 0, 0)\n  for (let l = 0; l <= q; ++l) {\n    Sw.add(temp[l].multiplyScalar(Nv[l]))\n  }\n\n  Sw.divideScalar(Sw.w)\n  target.set(Sw.x, Sw.y, Sw.z)\n}\n\nexport {\n  findSpan,\n  calcBasisFunctions,\n  calcBSplinePoint,\n  calcBasisFunctionDerivatives,\n  calcBSplineDerivatives,\n  calcKoverI,\n  calcRationalCurveDerivatives,\n  calcNURBSDerivatives,\n  calcSurfacePoint,\n}\n"], "names": ["r", "j"], "mappings": ";;;;;;;;;;;;;AAqBA,SAAS,SAAS,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IACzB,MAAM,IAAI,EAAE,MAAA,GAAS,IAAI;IAEzB,IAAI,KAAK,CAAA,CAAE,CAAC,CAAA,EAAG;QACb,OAAO,IAAI;IACZ;IAED,IAAI,KAAK,CAAA,CAAE,CAAC,CAAA,EAAG;QACb,OAAO;IACR;IAED,IAAI,MAAM;IACV,IAAI,OAAO;IACX,IAAI,MAAM,KAAK,KAAA,CAAA,CAAO,MAAM,IAAA,IAAQ,CAAC;IAErC,MAAO,IAAI,CAAA,CAAE,GAAG,CAAA,IAAK,KAAK,CAAA,CAAE,MAAM,CAAC,CAAA,CAAG;QACpC,IAAI,IAAI,CAAA,CAAE,GAAG,CAAA,EAAG;YACd,OAAO;QACb,OAAW;YACL,MAAM;QACP;QAED,MAAM,KAAK,KAAA,CAAA,CAAO,MAAM,IAAA,IAAQ,CAAC;IAClC;IAED,OAAO;AACT;AAYA,SAAS,mBAAmB,IAAA,EAAM,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IACzC,MAAM,IAAI,CAAE,CAAA;IACZ,MAAM,OAAO,CAAE,CAAA;IACf,MAAM,QAAQ,CAAE,CAAA;IAChB,CAAA,CAAE,CAAC,CAAA,GAAI;IAEP,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;QAC3B,IAAA,CAAK,CAAC,CAAA,GAAI,IAAI,CAAA,CAAE,OAAO,IAAI,CAAC,CAAA;QAC5B,KAAA,CAAM,CAAC,CAAA,GAAI,CAAA,CAAE,OAAO,CAAC,CAAA,GAAI;QAEzB,IAAI,QAAQ;QAEZ,IAAA,IAAS,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YAC1B,MAAM,KAAK,KAAA,CAAM,IAAI,CAAC,CAAA;YACtB,MAAM,KAAK,IAAA,CAAK,IAAI,CAAC,CAAA;YACrB,MAAM,OAAO,CAAA,CAAE,CAAC,CAAA,GAAA,CAAK,KAAK,EAAA;YAC1B,CAAA,CAAE,CAAC,CAAA,GAAI,QAAQ,KAAK;YACpB,QAAQ,KAAK;QACd;QAED,CAAA,CAAE,CAAC,CAAA,GAAI;IACR;IAED,OAAO;AACT;AAYA,SAAS,iBAAiB,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IACpC,MAAM,OAAO,SAAS,GAAG,GAAG,CAAC;IAC7B,MAAM,IAAI,mBAAmB,MAAM,GAAG,GAAG,CAAC;IAC1C,MAAM,IAAI,2MAAI,UAAA,CAAQ,GAAG,GAAG,GAAG,CAAC;IAEhC,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;QAC3B,MAAM,QAAQ,CAAA,CAAE,OAAO,IAAI,CAAC,CAAA;QAC5B,MAAM,KAAK,CAAA,CAAE,CAAC,CAAA;QACd,MAAM,MAAM,MAAM,CAAA,GAAI;QACtB,EAAE,CAAA,IAAK,MAAM,CAAA,GAAI;QACjB,EAAE,CAAA,IAAK,MAAM,CAAA,GAAI;QACjB,EAAE,CAAA,IAAK,MAAM,CAAA,GAAI;QACjB,EAAE,CAAA,IAAK,MAAM,CAAA,GAAI;IAClB;IAED,OAAO;AACT;AAaA,SAAS,6BAA6B,IAAA,EAAM,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG;IACtD,MAAM,UAAU,CAAE,CAAA;IAClB,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG,OAAA,CAAQ,CAAC,CAAA,GAAI;IAE1C,MAAM,OAAO,CAAE,CAAA;IAEf,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG,IAAA,CAAK,CAAC,CAAA,GAAI,QAAQ,KAAA,CAAM,CAAC;IAEtD,MAAM,MAAM,CAAE,CAAA;IAEd,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG,GAAA,CAAI,CAAC,CAAA,GAAI,QAAQ,KAAA,CAAM,CAAC;IAErD,GAAA,CAAI,CAAC,CAAA,CAAE,CAAC,CAAA,GAAI;IAEZ,MAAM,OAAO,QAAQ,KAAA,CAAM,CAAC;IAC5B,MAAM,QAAQ,QAAQ,KAAA,CAAM,CAAC;IAE7B,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;QAC3B,IAAA,CAAK,CAAC,CAAA,GAAI,IAAI,CAAA,CAAE,OAAO,IAAI,CAAC,CAAA;QAC5B,KAAA,CAAM,CAAC,CAAA,GAAI,CAAA,CAAE,OAAO,CAAC,CAAA,GAAI;QAEzB,IAAI,QAAQ;QAEZ,IAAA,IAASA,KAAI,GAAGA,KAAI,GAAG,EAAEA,GAAG;YAC1B,MAAM,KAAK,KAAA,CAAMA,KAAI,CAAC,CAAA;YACtB,MAAM,KAAK,IAAA,CAAK,IAAIA,EAAC,CAAA;YACrB,GAAA,CAAI,CAAC,CAAA,CAAEA,EAAC,CAAA,GAAI,KAAK;YAEjB,MAAM,OAAO,GAAA,CAAIA,EAAC,CAAA,CAAE,IAAI,CAAC,CAAA,GAAI,GAAA,CAAI,CAAC,CAAA,CAAEA,EAAC,CAAA;YACrC,GAAA,CAAIA,EAAC,CAAA,CAAE,CAAC,CAAA,GAAI,QAAQ,KAAK;YACzB,QAAQ,KAAK;QACd;QAED,GAAA,CAAI,CAAC,CAAA,CAAE,CAAC,CAAA,GAAI;IACb;IAED,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;QAC3B,IAAA,CAAK,CAAC,CAAA,CAAE,CAAC,CAAA,GAAI,GAAA,CAAI,CAAC,CAAA,CAAE,CAAC,CAAA;IACtB;IAED,IAAA,IAASA,KAAI,GAAGA,MAAK,GAAG,EAAEA,GAAG;QAC3B,IAAI,KAAK;QACT,IAAI,KAAK;QAET,MAAM,IAAI,CAAE,CAAA;QACZ,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;YAC3B,CAAA,CAAE,CAAC,CAAA,GAAI,QAAQ,KAAA,CAAM,CAAC;QACvB;QAED,CAAA,CAAE,CAAC,CAAA,CAAE,CAAC,CAAA,GAAI;QAEV,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;YAC3B,IAAI,IAAI;YACR,MAAM,KAAKA,KAAI;YACf,MAAM,KAAK,IAAI;YAEf,IAAIA,MAAK,GAAG;gBACV,CAAA,CAAE,EAAE,CAAA,CAAE,CAAC,CAAA,GAAI,CAAA,CAAE,EAAE,CAAA,CAAE,CAAC,CAAA,GAAI,GAAA,CAAI,KAAK,CAAC,CAAA,CAAE,EAAE,CAAA;gBACpC,IAAI,CAAA,CAAE,EAAE,CAAA,CAAE,CAAC,CAAA,GAAI,GAAA,CAAI,EAAE,CAAA,CAAE,EAAE,CAAA;YAC1B;YAED,MAAM,KAAK,MAAM,CAAA,IAAK,IAAI,CAAC;YAC3B,MAAM,KAAKA,KAAI,KAAK,KAAK,IAAI,IAAI,IAAIA;YAErC,IAAA,IAASC,KAAI,IAAIA,MAAK,IAAI,EAAEA,GAAG;gBAC7B,CAAA,CAAE,EAAE,CAAA,CAAEA,EAAC,CAAA,GAAA,CAAK,CAAA,CAAE,EAAE,CAAA,CAAEA,EAAC,CAAA,GAAI,CAAA,CAAE,EAAE,CAAA,CAAEA,KAAI,CAAC,CAAA,IAAK,GAAA,CAAI,KAAK,CAAC,CAAA,CAAE,KAAKA,EAAC,CAAA;gBACzD,KAAK,CAAA,CAAE,EAAE,CAAA,CAAEA,EAAC,CAAA,GAAI,GAAA,CAAI,KAAKA,EAAC,CAAA,CAAE,EAAE,CAAA;YAC/B;YAED,IAAID,MAAK,IAAI;gBACX,CAAA,CAAE,EAAE,CAAA,CAAE,CAAC,CAAA,GAAI,CAAC,CAAA,CAAE,EAAE,CAAA,CAAE,IAAI,CAAC,CAAA,GAAI,GAAA,CAAI,KAAK,CAAC,CAAA,CAAEA,EAAC,CAAA;gBACxC,KAAK,CAAA,CAAE,EAAE,CAAA,CAAE,CAAC,CAAA,GAAI,GAAA,CAAIA,EAAC,CAAA,CAAE,EAAE,CAAA;YAC1B;YAED,IAAA,CAAK,CAAC,CAAA,CAAEA,EAAC,CAAA,GAAI;YAEb,MAAM,IAAI;YACV,KAAK;YACL,KAAK;QACN;IACF;IAED,IAAI,IAAI;IAER,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;QAC3B,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;YAC3B,IAAA,CAAK,CAAC,CAAA,CAAE,CAAC,CAAA,IAAK;QACf;QAED,KAAK,IAAI;IACV;IAED,OAAO;AACT;AAaA,SAAS,uBAAuB,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG,EAAA,EAAI;IAC9C,MAAM,KAAK,KAAK,IAAI,KAAK;IACzB,MAAM,KAAK,CAAE,CAAA;IACb,MAAM,OAAO,SAAS,GAAG,GAAG,CAAC;IAC7B,MAAM,QAAQ,6BAA6B,MAAM,GAAG,GAAG,IAAI,CAAC;IAC5D,MAAM,KAAK,CAAE,CAAA;IAEb,IAAA,IAAS,IAAI,GAAG,IAAI,EAAE,MAAA,EAAQ,EAAE,EAAG;QACjC,MAAM,QAAQ,CAAA,CAAE,CAAC,CAAA,CAAE,KAAA,CAAO;QAC1B,MAAM,IAAI,MAAM,CAAA;QAEhB,MAAM,CAAA,IAAK;QACX,MAAM,CAAA,IAAK;QACX,MAAM,CAAA,IAAK;QAEX,EAAA,CAAG,CAAC,CAAA,GAAI;IACT;IAED,IAAA,IAAS,IAAI,GAAG,KAAK,IAAI,EAAE,EAAG;QAC5B,MAAM,QAAQ,EAAA,CAAG,OAAO,CAAC,CAAA,CAAE,KAAA,GAAQ,cAAA,CAAe,KAAA,CAAM,CAAC,CAAA,CAAE,CAAC,CAAC;QAE7D,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;YAC3B,MAAM,GAAA,CAAI,EAAA,CAAG,OAAO,IAAI,CAAC,CAAA,CAAE,KAAA,CAAO,EAAC,cAAA,CAAe,KAAA,CAAM,CAAC,CAAA,CAAE,CAAC,CAAC,CAAC;QAC/D;QAED,EAAA,CAAG,CAAC,CAAA,GAAI;IACT;IAED,IAAA,IAAS,IAAI,KAAK,GAAG,KAAK,KAAK,GAAG,EAAE,EAAG;QACrC,EAAA,CAAG,CAAC,CAAA,GAAI,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;IAC5B;IAED,OAAO;AACT;AAOA,SAAS,WAAW,CAAA,EAAG,CAAA,EAAG;IACxB,IAAI,MAAM;IAEV,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;QAC3B,OAAO;IACR;IAED,IAAI,QAAQ;IAEZ,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;QAC3B,SAAS;IACV;IAED,IAAA,IAAS,IAAI,GAAG,KAAK,IAAI,GAAG,EAAE,EAAG;QAC/B,SAAS;IACV;IAED,OAAO,MAAM;AACf;AASA,SAAS,6BAA6B,KAAA,EAAO;IAC3C,MAAM,KAAK,MAAM,MAAA;IACjB,MAAM,QAAQ,CAAE,CAAA;IAChB,MAAM,QAAQ,CAAE,CAAA;IAEhB,IAAA,IAAS,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;QAC3B,MAAM,QAAQ,KAAA,CAAM,CAAC,CAAA;QACrB,KAAA,CAAM,CAAC,CAAA,GAAI,2MAAI,UAAA,CAAQ,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,MAAM,CAAC;QAChD,KAAA,CAAM,CAAC,CAAA,GAAI,MAAM,CAAA;IAClB;IAED,MAAM,KAAK,CAAE,CAAA;IAEb,IAAA,IAAS,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;QAC3B,MAAM,IAAI,KAAA,CAAM,CAAC,CAAA,CAAE,KAAA,CAAO;QAE1B,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;YAC3B,EAAE,GAAA,CAAI,EAAA,CAAG,IAAI,CAAC,CAAA,CAAE,KAAA,CAAO,EAAC,cAAA,CAAe,WAAW,GAAG,CAAC,IAAI,KAAA,CAAM,CAAC,CAAC,CAAC;QACpE;QAED,EAAA,CAAG,CAAC,CAAA,GAAI,EAAE,YAAA,CAAa,KAAA,CAAM,CAAC,CAAC;IAChC;IAED,OAAO;AACT;AAaA,SAAS,qBAAqB,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG,EAAA,EAAI;IAC5C,MAAM,QAAQ,uBAAuB,GAAG,GAAG,GAAG,GAAG,EAAE;IACnD,OAAO,6BAA6B,KAAK;AAC3C;AAYA,SAAS,iBAAiB,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG,CAAA,EAAG,MAAA,EAAQ;IACrD,MAAM,QAAQ,SAAS,GAAG,GAAG,CAAC;IAC9B,MAAM,QAAQ,SAAS,GAAG,GAAG,CAAC;IAC9B,MAAM,KAAK,mBAAmB,OAAO,GAAG,GAAG,CAAC;IAC5C,MAAM,KAAK,mBAAmB,OAAO,GAAG,GAAG,CAAC;IAC5C,MAAM,OAAO,CAAE,CAAA;IAEf,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;QAC3B,IAAA,CAAK,CAAC,CAAA,GAAI,2MAAI,UAAA,CAAQ,GAAG,GAAG,GAAG,CAAC;QAChC,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;YAC3B,MAAM,QAAQ,CAAA,CAAE,QAAQ,IAAI,CAAC,CAAA,CAAE,QAAQ,IAAI,CAAC,CAAA,CAAE,KAAA,CAAO;YACrD,MAAM,IAAI,MAAM,CAAA;YAChB,MAAM,CAAA,IAAK;YACX,MAAM,CAAA,IAAK;YACX,MAAM,CAAA,IAAK;YACX,IAAA,CAAK,CAAC,CAAA,CAAE,GAAA,CAAI,MAAM,cAAA,CAAe,EAAA,CAAG,CAAC,CAAC,CAAC;QACxC;IACF;IAED,MAAM,KAAK,2MAAI,UAAA,CAAQ,GAAG,GAAG,GAAG,CAAC;IACjC,IAAA,IAAS,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;QAC3B,GAAG,GAAA,CAAI,IAAA,CAAK,CAAC,CAAA,CAAE,cAAA,CAAe,EAAA,CAAG,CAAC,CAAC,CAAC;IACrC;IAED,GAAG,YAAA,CAAa,GAAG,CAAC;IACpB,OAAO,GAAA,CAAI,GAAG,CAAA,EAAG,GAAG,CAAA,EAAG,GAAG,CAAC;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 10344, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 10350, "column": 0}, "map": {"version": 3, "file": "NURBSCurve.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/curves/NURBSCurve.js"], "sourcesContent": ["import { Curve, Vector3, Vector4 } from 'three'\nimport * as NURBSUtils from '../curves/NURBSUtils'\n\n/**\n * NURBS curve object\n *\n * Derives from Curve, overriding getPoint and getTangent.\n *\n * Implementation is based on (x, y [, z=0 [, w=1]]) control points with w=weight.\n *\n **/\n\nclass NURBSCurve extends Curve {\n  constructor(\n    degree,\n    knots /* array of reals */,\n    controlPoints /* array of Vector(2|3|4) */,\n    startKnot /* index in knots */,\n    endKnot /* index in knots */,\n  ) {\n    super()\n\n    this.degree = degree\n    this.knots = knots\n    this.controlPoints = []\n    // Used by periodic NURBS to remove hidden spans\n    this.startKnot = startKnot || 0\n    this.endKnot = endKnot || this.knots.length - 1\n    for (let i = 0; i < controlPoints.length; ++i) {\n      // ensure Vector4 for control points\n      const point = controlPoints[i]\n      this.controlPoints[i] = new Vector4(point.x, point.y, point.z, point.w)\n    }\n  }\n\n  getPoint(t, optionalTarget) {\n    const point = optionalTarget || new Vector3()\n\n    const u = this.knots[this.startKnot] + t * (this.knots[this.endKnot] - this.knots[this.startKnot]) // linear mapping t->u\n\n    // following results in (wx, wy, wz, w) homogeneous point\n    const hpoint = NURBSUtils.calcBSplinePoint(this.degree, this.knots, this.controlPoints, u)\n\n    if (hpoint.w != 1.0) {\n      // project to 3D space: (wx, wy, wz, w) -> (x, y, z, 1)\n      hpoint.divideScalar(hpoint.w)\n    }\n\n    return point.set(hpoint.x, hpoint.y, hpoint.z)\n  }\n\n  getTangent(t, optionalTarget) {\n    const tangent = optionalTarget || new Vector3()\n\n    const u = this.knots[0] + t * (this.knots[this.knots.length - 1] - this.knots[0])\n    const ders = NURBSUtils.calcNURBSDerivatives(this.degree, this.knots, this.controlPoints, u, 1)\n    tangent.copy(ders[1]).normalize()\n\n    return tangent\n  }\n}\n\nexport { NURBSCurve }\n"], "names": ["NURBSUtils.calcBSplinePoint", "NURBSUtils.calcNURBSDerivatives"], "mappings": ";;;;;;;AAYA,MAAM,0NAAmB,QAAA,CAAM;IAC7B,YACE,MAAA,EACA,KAAA,EACA,aAAA,EACA,SAAA,EACA,OAAA,CACA;QACA,KAAA,CAAO;QAEP,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,KAAA,GAAQ;QACb,IAAA,CAAK,aAAA,GAAgB,CAAE,CAAA;QAEvB,IAAA,CAAK,SAAA,GAAY,aAAa;QAC9B,IAAA,CAAK,OAAA,GAAU,WAAW,IAAA,CAAK,KAAA,CAAM,MAAA,GAAS;QAC9C,IAAA,IAAS,IAAI,GAAG,IAAI,cAAc,MAAA,EAAQ,EAAE,EAAG;YAE7C,MAAM,QAAQ,aAAA,CAAc,CAAC,CAAA;YAC7B,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,GAAI,2MAAI,UAAA,CAAQ,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,MAAM,CAAC;QACvE;IACF;IAED,SAAS,CAAA,EAAG,cAAA,EAAgB;QAC1B,MAAM,QAAQ,kBAAkB,2MAAI,UAAA,CAAS;QAE7C,MAAM,IAAI,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,SAAS,CAAA,GAAI,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,OAAO,CAAA,GAAI,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,SAAS,CAAA;QAGhG,MAAM,4PAASA,mBAAAA,EAA4B,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,aAAA,EAAe,CAAC;QAEzF,IAAI,OAAO,CAAA,IAAK,GAAK;YAEnB,OAAO,YAAA,CAAa,OAAO,CAAC;QAC7B;QAED,OAAO,MAAM,GAAA,CAAI,OAAO,CAAA,EAAG,OAAO,CAAA,EAAG,OAAO,CAAC;IAC9C;IAED,WAAW,CAAA,EAAG,cAAA,EAAgB;QAC5B,MAAM,UAAU,kBAAkB,2MAAI,UAAA,CAAS;QAE/C,MAAM,IAAI,IAAA,CAAK,KAAA,CAAM,CAAC,CAAA,GAAI,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,KAAA,CAAM,MAAA,GAAS,CAAC,CAAA,GAAI,IAAA,CAAK,KAAA,CAAM,CAAC,CAAA;QAC/E,MAAM,0PAAOC,uBAAAA,EAAgC,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,aAAA,EAAe,GAAG,CAAC;QAC9F,QAAQ,IAAA,CAAK,IAAA,CAAK,CAAC,CAAC,EAAE,SAAA,CAAW;QAEjC,OAAO;IACR;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 10389, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 10395, "column": 0}, "map": {"version": 3, "file": "CurveModifier.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/modifiers/CurveModifier.ts"], "sourcesContent": ["// Original src: https://github.com/zz85/threejs-path-flow\nconst CHANNELS = 4\nconst TEXTURE_WIDTH = 1024\nconst TEXTURE_HEIGHT = 4\n\nimport {\n  DataTexture,\n  RGBAFormat,\n  FloatType,\n  RepeatWrapping,\n  Mesh,\n  InstancedMesh,\n  NearestFilter,\n  DynamicDrawUsage,\n  Matrix4,\n  Material,\n  Curve,\n  BufferGeometry,\n} from 'three'\n\nimport type { IUniform } from 'three'\n\n/**\n * Make a new DataTexture to store the descriptions of the curves.\n *\n * @param { number } numberOfCurves the number of curves needed to be described by this texture.\n */\nexport const initSplineTexture = (numberOfCurves = 1): DataTexture => {\n  const dataArray = new Float32Array(TEXTURE_WIDTH * TEXTURE_HEIGHT * numberOfCurves * CHANNELS)\n  const dataTexture = new DataTexture(dataArray, TEXTURE_WIDTH, TEXTURE_HEIGHT * numberOfCurves, RGBAFormat, FloatType)\n\n  dataTexture.wrapS = RepeatWrapping\n  dataTexture.wrapT = RepeatWrapping\n  dataTexture.magFilter = NearestFilter\n  dataTexture.needsUpdate = true\n\n  return dataTexture\n}\n\n/**\n * Write the curve description to the data texture\n *\n * @param { DataTexture } texture The DataTexture to write to\n * @param { Curve } splineCurve The curve to describe\n * @param { number } offset Which curve slot to write to\n */\nexport const updateSplineTexture = <TCurve extends Curve<any>>(\n  texture: DataTexture,\n  splineCurve: TCurve,\n  offset = 0,\n): void => {\n  const numberOfPoints = Math.floor(TEXTURE_WIDTH * (TEXTURE_HEIGHT / 4))\n  splineCurve.arcLengthDivisions = numberOfPoints / 2\n  splineCurve.updateArcLengths()\n  const points = splineCurve.getSpacedPoints(numberOfPoints)\n  const frenetFrames = splineCurve.computeFrenetFrames(numberOfPoints, true)\n\n  for (let i = 0; i < numberOfPoints; i++) {\n    const rowOffset = Math.floor(i / TEXTURE_WIDTH)\n    const rowIndex = i % TEXTURE_WIDTH\n\n    let pt = points[i]\n    setTextureValue(texture, rowIndex, pt.x, pt.y, pt.z, 0 + rowOffset + TEXTURE_HEIGHT * offset)\n    pt = frenetFrames.tangents[i]\n    setTextureValue(texture, rowIndex, pt.x, pt.y, pt.z, 1 + rowOffset + TEXTURE_HEIGHT * offset)\n    pt = frenetFrames.normals[i]\n    setTextureValue(texture, rowIndex, pt.x, pt.y, pt.z, 2 + rowOffset + TEXTURE_HEIGHT * offset)\n    pt = frenetFrames.binormals[i]\n    setTextureValue(texture, rowIndex, pt.x, pt.y, pt.z, 3 + rowOffset + TEXTURE_HEIGHT * offset)\n  }\n\n  texture.needsUpdate = true\n}\n\nconst setTextureValue = (texture: DataTexture, index: number, x: number, y: number, z: number, o: number): void => {\n  const image = texture.image\n  const { data } = image\n  const i = CHANNELS * TEXTURE_WIDTH * o // Row Offset\n  data[index * CHANNELS + i + 0] = x\n  data[index * CHANNELS + i + 1] = y\n  data[index * CHANNELS + i + 2] = z\n  data[index * CHANNELS + i + 3] = 1\n}\n\nexport interface INumericUniform extends IUniform {\n  type: 'f' | 'i'\n  value: number\n}\n\nexport type CurveModifierUniforms = {\n  spineTexture: IUniform<DataTexture>\n  pathOffset: INumericUniform\n  pathSegment: INumericUniform\n  spineOffset: INumericUniform\n  spineLength: INumericUniform\n  flow: INumericUniform\n}\n\n/**\n * Create a new set of uniforms for describing the curve modifier\n *\n * @param { DataTexture } Texture which holds the curve description\n */\nexport const getUniforms = (splineTexture: DataTexture): CurveModifierUniforms => ({\n  spineTexture: { value: splineTexture },\n  pathOffset: { type: 'f', value: 0 }, // time of path curve\n  pathSegment: { type: 'f', value: 1 }, // fractional length of path\n  spineOffset: { type: 'f', value: 161 },\n  spineLength: { type: 'f', value: 400 },\n  flow: { type: 'i', value: 1 },\n})\n\nexport type ModifiedMaterial<TMaterial extends Material> = TMaterial & {\n  __ok: boolean\n}\n\nexport function modifyShader<TMaterial extends Material = Material>(\n  material: ModifiedMaterial<TMaterial>,\n  uniforms: CurveModifierUniforms,\n  numberOfCurves = 1,\n): void {\n  if (material.__ok) return\n  material.__ok = true\n\n  material.onBeforeCompile = (shader: { vertexShader: string; uniforms: { [uniform: string]: IUniform } }): void => {\n    if ((shader as any).__modified) return\n    ;(shader as any).__modified = true\n\n    Object.assign(shader.uniforms, uniforms)\n\n    const vertexShader = /* glsl */ `\n\t\tuniform sampler2D spineTexture;\n\t\tuniform float pathOffset;\n\t\tuniform float pathSegment;\n\t\tuniform float spineOffset;\n\t\tuniform float spineLength;\n\t\tuniform int flow;\n\n\t\tfloat textureLayers = ${TEXTURE_HEIGHT * numberOfCurves}.;\n\t\tfloat textureStacks = ${TEXTURE_HEIGHT / 4}.;\n\n\t\t${shader.vertexShader}\n\t\t`\n      // chunk import moved in front of modified shader below\n      .replace('#include <beginnormal_vertex>', '')\n\n      // vec3 transformedNormal declaration overriden below\n      .replace('#include <defaultnormal_vertex>', '')\n\n      // vec3 transformed declaration overriden below\n      .replace('#include <begin_vertex>', '')\n\n      // shader override\n      .replace(\n        /void\\s*main\\s*\\(\\)\\s*\\{/,\n        /* glsl */ `\n        void main() {\n        #include <beginnormal_vertex>\n\n        vec4 worldPos = modelMatrix * vec4(position, 1.);\n\n        bool bend = flow > 0;\n        float xWeight = bend ? 0. : 1.;\n\n        #ifdef USE_INSTANCING\n        float pathOffsetFromInstanceMatrix = instanceMatrix[3][2];\n        float spineLengthFromInstanceMatrix = instanceMatrix[3][0];\n        float spinePortion = bend ? (worldPos.x + spineOffset) / spineLengthFromInstanceMatrix : 0.;\n        float mt = (spinePortion * pathSegment + pathOffset + pathOffsetFromInstanceMatrix)*textureStacks;\n        #else\n        float spinePortion = bend ? (worldPos.x + spineOffset) / spineLength : 0.;\n        float mt = (spinePortion * pathSegment + pathOffset)*textureStacks;\n        #endif\n\n        mt = mod(mt, textureStacks);\n        float rowOffset = floor(mt);\n\n        #ifdef USE_INSTANCING\n        rowOffset += instanceMatrix[3][1] * ${TEXTURE_HEIGHT}.;\n        #endif\n\n        vec3 spinePos = texture2D(spineTexture, vec2(mt, (0. + rowOffset + 0.5) / textureLayers)).xyz;\n        vec3 a =        texture2D(spineTexture, vec2(mt, (1. + rowOffset + 0.5) / textureLayers)).xyz;\n        vec3 b =        texture2D(spineTexture, vec2(mt, (2. + rowOffset + 0.5) / textureLayers)).xyz;\n        vec3 c =        texture2D(spineTexture, vec2(mt, (3. + rowOffset + 0.5) / textureLayers)).xyz;\n        mat3 basis = mat3(a, b, c);\n\n        vec3 transformed = basis\n          * vec3(worldPos.x * xWeight, worldPos.y * 1., worldPos.z * 1.)\n          + spinePos;\n\n        vec3 transformedNormal = normalMatrix * (basis * objectNormal);\n\t\t\t`,\n      )\n      .replace(\n        '#include <project_vertex>',\n        /* glsl */ `vec4 mvPosition = modelViewMatrix * vec4( transformed, 1.0 );\n\t\t\t\tgl_Position = projectionMatrix * mvPosition;`,\n      )\n\n    shader.vertexShader = vertexShader\n  }\n}\n\n/**\n * A helper class for making meshes bend aroudn curves\n */\nexport class Flow<TMesh extends Mesh = Mesh> {\n  public curveArray: Curve<any>[]\n  public curveLengthArray: number[]\n\n  public object3D: TMesh\n  public splineTexure: DataTexture\n  public uniforms: CurveModifierUniforms\n\n  /**\n   * @param {Mesh} mesh The mesh to clone and modify to bend around the curve\n   * @param {number} numberOfCurves The amount of space that should preallocated for additional curves\n   */\n  constructor(mesh: TMesh, numberOfCurves = 1) {\n    const obj3D = mesh.clone() as TMesh\n    const splineTexure = initSplineTexture(numberOfCurves)\n    const uniforms = getUniforms(splineTexure)\n\n    obj3D.traverse((child) => {\n      if (child instanceof Mesh || child instanceof InstancedMesh) {\n        child.material = child.material.clone()\n        modifyShader(child.material, uniforms, numberOfCurves)\n      }\n    })\n\n    this.curveArray = new Array(numberOfCurves)\n    this.curveLengthArray = new Array(numberOfCurves)\n\n    this.object3D = obj3D\n    this.splineTexure = splineTexure\n    this.uniforms = uniforms\n  }\n\n  public updateCurve<TCurve extends Curve<any>>(index: number, curve: TCurve): void {\n    if (index >= this.curveArray.length) throw Error('Index out of range for Flow')\n    const curveLength = curve.getLength()\n    this.uniforms.spineLength.value = curveLength\n    this.curveLengthArray[index] = curveLength\n    this.curveArray[index] = curve\n    updateSplineTexture(this.splineTexure, curve, index)\n  }\n\n  public moveAlongCurve(amount: number): void {\n    this.uniforms.pathOffset.value += amount\n  }\n}\nconst matrix = /* @__PURE__ */ new Matrix4()\n\n/**\n * A helper class for creating instanced versions of flow, where the instances are placed on the curve.\n */\nexport class InstancedFlow<\n  TGeometry extends BufferGeometry = BufferGeometry,\n  TMaterial extends Material = Material\n> extends Flow<InstancedMesh<TGeometry, TMaterial>> {\n  public offsets: number[]\n  public whichCurve: number[]\n\n  /**\n   *\n   * @param {number} count The number of instanced elements\n   * @param {number} curveCount The number of curves to preallocate for\n   * @param {Geometry} geometry The geometry to use for the instanced mesh\n   * @param {Material} material The material to use for the instanced mesh\n   */\n  constructor(count: number, curveCount: number, geometry: TGeometry, material: TMaterial) {\n    const mesh = new InstancedMesh(geometry, material, count)\n    mesh.instanceMatrix.setUsage(DynamicDrawUsage)\n    mesh.frustumCulled = false\n    super(mesh, curveCount)\n\n    this.offsets = new Array(count).fill(0)\n    this.whichCurve = new Array(count).fill(0)\n  }\n\n  /**\n   * The extra information about which curve and curve position is stored in the translation components of the matrix for the instanced objects\n   * This writes that information to the matrix and marks it as needing update.\n   *\n   * @param {number} index of the instanced element to update\n   */\n  private writeChanges(index: number): void {\n    matrix.makeTranslation(this.curveLengthArray[this.whichCurve[index]], this.whichCurve[index], this.offsets[index])\n    this.object3D.setMatrixAt(index, matrix)\n    this.object3D.instanceMatrix.needsUpdate = true\n  }\n\n  /**\n   * Move an individual element along the curve by a specific amount\n   *\n   * @param {number} index Which element to update\n   * @param {number} offset Move by how much\n   */\n  public moveIndividualAlongCurve(index: number, offset: number): void {\n    this.offsets[index] += offset\n    this.writeChanges(index)\n  }\n\n  /**\n   * Select which curve to use for an element\n   *\n   * @param {number} index the index of the instanced element to update\n   * @param {number} curveNo the index of the curve it should use\n   */\n  public setCurve(index: number, curveNo: number): void {\n    if (isNaN(curveNo)) throw Error('curve index being set is Not a Number (NaN)')\n    this.whichCurve[index] = curveNo\n    this.writeChanges(index)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AACA,MAAM,WAAW;AACjB,MAAM,gBAAgB;AACtB,MAAM,iBAAiB;AAwBV,MAAA,oBAAoB,CAAC,iBAAiB,CAAA,KAAmB;IACpE,MAAM,YAAY,IAAI,aAAa,gBAAgB,iBAAiB,iBAAiB,QAAQ;IACvF,MAAA,cAAc,2MAAI,cAAA,CAAY,WAAW,eAAe,iBAAiB,uNAAgB,aAAA,EAAY,mNAAS;IAEpH,YAAY,KAAA,0MAAQ,iBAAA;IACpB,YAAY,KAAA,0MAAQ,iBAAA;IACpB,YAAY,SAAA,GAAY,uNAAA;IACxB,YAAY,WAAA,GAAc;IAEnB,OAAA;AACT;AASO,MAAM,sBAAsB,CACjC,SACA,aACA,SAAS,CAAA,KACA;IACT,MAAM,iBAAiB,KAAK,KAAA,CAAM,gBAAA,CAAiB,iBAAiB,CAAA,CAAE;IACtE,YAAY,kBAAA,GAAqB,iBAAiB;IAClD,YAAY,gBAAA,CAAiB;IACvB,MAAA,SAAS,YAAY,eAAA,CAAgB,cAAc;IACzD,MAAM,eAAe,YAAY,mBAAA,CAAoB,gBAAgB,IAAI;IAEzE,IAAA,IAAS,IAAI,GAAG,IAAI,gBAAgB,IAAK;QACvC,MAAM,YAAY,KAAK,KAAA,CAAM,IAAI,aAAa;QAC9C,MAAM,WAAW,IAAI;QAEjB,IAAA,KAAK,MAAA,CAAO,CAAC,CAAA;QACD,gBAAA,SAAS,UAAU,GAAG,CAAA,EAAG,GAAG,CAAA,EAAG,GAAG,CAAA,EAAG,IAAI,YAAY,iBAAiB,MAAM;QACvF,KAAA,aAAa,QAAA,CAAS,CAAC,CAAA;QACZ,gBAAA,SAAS,UAAU,GAAG,CAAA,EAAG,GAAG,CAAA,EAAG,GAAG,CAAA,EAAG,IAAI,YAAY,iBAAiB,MAAM;QACvF,KAAA,aAAa,OAAA,CAAQ,CAAC,CAAA;QACX,gBAAA,SAAS,UAAU,GAAG,CAAA,EAAG,GAAG,CAAA,EAAG,GAAG,CAAA,EAAG,IAAI,YAAY,iBAAiB,MAAM;QACvF,KAAA,aAAa,SAAA,CAAU,CAAC,CAAA;QACb,gBAAA,SAAS,UAAU,GAAG,CAAA,EAAG,GAAG,CAAA,EAAG,GAAG,CAAA,EAAG,IAAI,YAAY,iBAAiB,MAAM;IAC9F;IAEA,QAAQ,WAAA,GAAc;AACxB;AAEA,MAAM,kBAAkB,CAAC,SAAsB,OAAe,GAAW,GAAW,GAAW,MAAoB;IACjH,MAAM,QAAQ,QAAQ,KAAA;IAChB,MAAA,EAAE,IAAA,CAAS,CAAA,GAAA;IACX,MAAA,IAAI,WAAW,gBAAgB;IACrC,IAAA,CAAK,QAAQ,WAAW,IAAI,CAAC,CAAA,GAAI;IACjC,IAAA,CAAK,QAAQ,WAAW,IAAI,CAAC,CAAA,GAAI;IACjC,IAAA,CAAK,QAAQ,WAAW,IAAI,CAAC,CAAA,GAAI;IACjC,IAAA,CAAK,QAAQ,WAAW,IAAI,CAAC,CAAA,GAAI;AACnC;AAqBa,MAAA,cAAc,CAAC,gBAAA,CAAuD;QACjF,cAAc;YAAE,OAAO;QAAc;QACrC,YAAY;YAAE,MAAM;YAAK,OAAO;QAAE;QAAA,qBAAA;QAClC,aAAa;YAAE,MAAM;YAAK,OAAO;QAAE;QAAA,4BAAA;QACnC,aAAa;YAAE,MAAM;YAAK,OAAO;QAAI;QACrC,aAAa;YAAE,MAAM;YAAK,OAAO;QAAI;QACrC,MAAM;YAAE,MAAM;YAAK,OAAO;QAAE;IAC9B,CAAA;AAMO,SAAS,aACd,QAAA,EACA,QAAA,EACA,iBAAiB,CAAA,EACX;IACN,IAAI,SAAS,IAAA,EAAM;IACnB,SAAS,IAAA,GAAO;IAEP,SAAA,eAAA,GAAkB,CAAC,WAAsF;QAChH,IAAK,OAAe,UAAA,EAAY;QAC9B,OAAe,UAAA,GAAa;QAEvB,OAAA,MAAA,CAAO,OAAO,QAAA,EAAU,QAAQ;QAEjC,MAAA,eAAA,QAAA,GAA0B,CAAA;;;;;;;;wBAAA,EAQV,iBAAiB,eAAA;wBAAA,EACjB,iBAAiB,EAAA;;EAAA,EAEvC,OAAO,YAAA,CAAA;EAAA,CAAA,CAGJ,OAAA,CAAQ,iCAAiC,EAAE,EAG3C,OAAA,CAAQ,mCAAmC,EAAE,EAG7C,OAAA,CAAQ,2BAA2B,EAAE,EAGrC,OAAA,CACC,2BAAA,QAAA,GACW,CAAA;;;;;;;;;;;;;;;;;;;;;;;4CAAA,EAuB2B,eAAA;;;;;;;;;;;;;;GAAA,CAAA,EAgBvC,OAAA,CACC,6BAAA,QAAA,GACW,CAAA;gDAAA,CAAA;QAIf,OAAO,YAAA,GAAe;IAAA;AAE1B;AAKO,MAAM,KAAgC;IAAA;;;GAAA,GAY3C,YAAY,IAAA,EAAa,iBAAiB,CAAA,CAAG;QAXtC,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAOC,MAAA,QAAQ,KAAK,KAAA;QACb,MAAA,eAAe,kBAAkB,cAAc;QAC/C,MAAA,WAAW,YAAY,YAAY;QAEnC,MAAA,QAAA,CAAS,CAAC,UAAU;YACpB,IAAA,wNAAiB,OAAA,IAAQ,uNAAiB,iBAAA,EAAe;gBACrD,MAAA,QAAA,GAAW,MAAM,QAAA,CAAS,KAAA,CAAM;gBACzB,aAAA,MAAM,QAAA,EAAU,UAAU,cAAc;YACvD;QAAA,CACD;QAEI,IAAA,CAAA,UAAA,GAAa,IAAI,MAAM,cAAc;QACrC,IAAA,CAAA,gBAAA,GAAmB,IAAI,MAAM,cAAc;QAEhD,IAAA,CAAK,QAAA,GAAW;QAChB,IAAA,CAAK,YAAA,GAAe;QACpB,IAAA,CAAK,QAAA,GAAW;IAClB;IAEO,YAAuC,KAAA,EAAe,KAAA,EAAqB;QAC5E,IAAA,SAAS,IAAA,CAAK,UAAA,CAAW,MAAA,EAAQ,MAAM,MAAM,6BAA6B;QACxE,MAAA,cAAc,MAAM,SAAA;QACrB,IAAA,CAAA,QAAA,CAAS,WAAA,CAAY,KAAA,GAAQ;QAC7B,IAAA,CAAA,gBAAA,CAAiB,KAAK,CAAA,GAAI;QAC1B,IAAA,CAAA,UAAA,CAAW,KAAK,CAAA,GAAI;QACL,oBAAA,IAAA,CAAK,YAAA,EAAc,OAAO,KAAK;IACrD;IAEO,eAAe,MAAA,EAAsB;QACrC,IAAA,CAAA,QAAA,CAAS,UAAA,CAAW,KAAA,IAAS;IACpC;AACF;AACA,MAAM,SAAA,aAAA,GAAA,2MAA6B,UAAA;AAK5B,MAAM,sBAGH,KAA0C;IAAA;;;;;;GAAA,GAWlD,YAAY,KAAA,EAAe,UAAA,EAAoB,QAAA,EAAqB,QAAA,CAAqB;QACvF,MAAM,OAAO,2MAAI,gBAAA,CAAc,UAAU,UAAU,KAAK;QACnD,KAAA,cAAA,CAAe,QAAA,wMAAS,mBAAgB;QAC7C,KAAK,aAAA,GAAgB;QACrB,KAAA,CAAM,MAAM,UAAU;QAdjB,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAeL,IAAA,CAAK,OAAA,GAAU,IAAI,MAAM,KAAK,EAAE,IAAA,CAAK,CAAC;QACtC,IAAA,CAAK,UAAA,GAAa,IAAI,MAAM,KAAK,EAAE,IAAA,CAAK,CAAC;IAC3C;IAAA;;;;;GAAA,GAQQ,aAAa,KAAA,EAAqB;QACxC,OAAO,eAAA,CAAgB,IAAA,CAAK,gBAAA,CAAiB,IAAA,CAAK,UAAA,CAAW,KAAK,CAAC,CAAA,EAAG,IAAA,CAAK,UAAA,CAAW,KAAK,CAAA,EAAG,IAAA,CAAK,OAAA,CAAQ,KAAK,CAAC;QAC5G,IAAA,CAAA,QAAA,CAAS,WAAA,CAAY,OAAO,MAAM;QAClC,IAAA,CAAA,QAAA,CAAS,cAAA,CAAe,WAAA,GAAc;IAC7C;IAAA;;;;;GAAA,GAQO,yBAAyB,KAAA,EAAe,MAAA,EAAsB;QAC9D,IAAA,CAAA,OAAA,CAAQ,KAAK,CAAA,IAAK;QACvB,IAAA,CAAK,YAAA,CAAa,KAAK;IACzB;IAAA;;;;;GAAA,GAQO,SAAS,KAAA,EAAe,OAAA,EAAuB;QACpD,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,6CAA6C;QACxE,IAAA,CAAA,UAAA,CAAW,KAAK,CAAA,GAAI;QACzB,IAAA,CAAK,YAAA,CAAa,KAAK;IACzB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 10632, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}