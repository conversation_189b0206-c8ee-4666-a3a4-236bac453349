{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "EventDispatcher.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/controls/EventDispatcher.ts"], "sourcesContent": ["/*\nDue to @types/three r168 breaking change\nwe have to manually copy the EventDispatcher class from three.js.\nSo this files merges the declarations from https://github.com/DefinitelyTyped/DefinitelyTyped/blob/master/types/three/src/core/EventDispatcher.d.ts\nwith the implementation from https://github.com/mrdoob/three.js/blob/dev/src/core/EventDispatcher.js\nMore info in https://github.com/pmndrs/three-stdlib/issues/387\n*/\n\n/**\n * The minimal basic Event that can be dispatched by a {@link EventDispatcher<>}.\n */\nexport interface BaseEvent<TEventType extends string = string> {\n    readonly type: TEventType;\n    // not defined in @types/three\n    target: any;\n}\n\n/**\n * The minimal expected contract of a fired Event that was dispatched by a {@link EventDispatcher<>}.\n */\nexport interface Event<TEventType extends string = string, TTarget = unknown> {\n    readonly type: TEventType;\n    readonly target: TTarget;\n}\n\nexport type EventListener<TEventData, TEventType extends string, TTarget> = (\n    event: TEventData & Event<TEventType, TTarget>,\n) => void;\n\nexport class EventDispatcher<TEventMap extends {} = {}> {\n    // not defined in @types/three\n    private _listeners: any;\n\n    /**\n     * Adds a listener to an event type.\n     * @param type The type of event to listen to.\n     * @param listener The function that gets called when the event is fired.\n     */\n\taddEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): void {\n\n\t\tif ( this._listeners === undefined ) this._listeners = {};\n\n\t\tconst listeners = this._listeners;\n\n\t\tif ( listeners[ type ] === undefined ) {\n\n\t\t\tlisteners[ type ] = [];\n\n\t\t}\n\n\t\tif ( listeners[ type ].indexOf( listener ) === - 1 ) {\n\n\t\t\tlisteners[ type ].push( listener );\n\n\t\t}\n\n\t}\n\n\t/**\n     * Checks if listener is added to an event type.\n     * @param type The type of event to listen to.\n     * @param listener The function that gets called when the event is fired.\n     */\n    hasEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): boolean {\n\n\t\tif ( this._listeners === undefined ) return false;\n\n\t\tconst listeners = this._listeners;\n\n\t\treturn listeners[ type ] !== undefined && listeners[ type ].indexOf( listener ) !== - 1;\n\n\t}\n\n\t/**\n     * Removes a listener from an event type.\n     * @param type The type of the listener that gets removed.\n     * @param listener The listener function that gets removed.\n     */\n    removeEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): void {\n\n\t\tif ( this._listeners === undefined ) return;\n\n\t\tconst listeners = this._listeners;\n\t\tconst listenerArray = listeners[ type ];\n\n\t\tif ( listenerArray !== undefined ) {\n\n\t\t\tconst index = listenerArray.indexOf( listener );\n\n\t\t\tif ( index !== - 1 ) {\n\n\t\t\t\tlistenerArray.splice( index, 1 );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/**\n     * Fire an event type.\n     * @param event The event that gets fired.\n     */\n    dispatchEvent<T extends Extract<keyof TEventMap, string>>(event: BaseEvent<T> & TEventMap[T]): void {\n\n\t\tif ( this._listeners === undefined ) return;\n\n\t\tconst listeners = this._listeners;\n\t\tconst listenerArray = listeners[ event.type ];\n\n\t\tif ( listenerArray !== undefined ) {\n\n\t\t\tevent.target = this;\n\n\t\t\t// Make a copy, in case listeners are removed while iterating.\n\t\t\tconst array = listenerArray.slice( 0 );\n\n\t\t\tfor ( let i = 0, l = array.length; i < l; i ++ ) {\n\n\t\t\t\tarray[ i ].call( this, event );\n\n\t\t\t}\n\n\t\t\tevent.target = null;\n\n\t\t}\n\n\t}\n\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;AA6BO,MAAM,gBAA2C;IAAjD,aAAA;QAEK,8BAAA;QAAA,cAAA,IAAA,EAAA;IAAA;IAAA;;;;GAAA,GAOX,iBACO,IAAA,EACA,QAAA,EACI;QAEV,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAY,IAAA,CAAK,UAAA,GAAa,CAAA;QAEvD,MAAM,YAAY,IAAA,CAAK,UAAA;QAElB,IAAA,SAAA,CAAW,IAAK,CAAA,KAAM,KAAA,GAAY;YAE3B,SAAA,CAAA,IAAK,CAAA,GAAI,EAAA;QAErB;QAEA,IAAK,SAAA,CAAW,IAAK,CAAA,CAAE,OAAA,CAAS,QAAS,MAAM,CAAA,GAAM;YAEzC,SAAA,CAAA,IAAK,CAAA,CAAE,IAAA,CAAM,QAAS;QAElC;IAED;IAAA;;;;MAAA,GAOG,iBACI,IAAA,EACA,QAAA,EACO;QAEb,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAmB,OAAA;QAE5C,MAAM,YAAY,IAAA,CAAK,UAAA;QAEhB,OAAA,SAAA,CAAW,IAAK,CAAA,KAAM,KAAA,KAAa,SAAA,CAAW,IAAK,CAAA,CAAE,OAAA,CAAS,QAAS,MAAM,CAAA;IAErF;IAAA;;;;MAAA,GAOG,oBACI,IAAA,EACA,QAAA,EACI;QAEV,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAY;QAErC,MAAM,YAAY,IAAA,CAAK,UAAA;QACjB,MAAA,gBAAgB,SAAA,CAAW,IAAK,CAAA;QAEtC,IAAK,kBAAkB,KAAA,GAAY;YAE5B,MAAA,QAAQ,cAAc,OAAA,CAAS,QAAS;YAE9C,IAAK,UAAU,CAAA,GAAM;gBAEN,cAAA,MAAA,CAAQ,OAAO,CAAE;YAEhC;QAED;IAED;IAAA;;;MAAA,GAMG,cAA0D,KAAA,EAA0C;QAEtG,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAY;QAErC,MAAM,YAAY,IAAA,CAAK,UAAA;QACjB,MAAA,gBAAgB,SAAA,CAAW,MAAM,IAAK,CAAA;QAE5C,IAAK,kBAAkB,KAAA,GAAY;YAElC,MAAM,MAAA,GAAS,IAAA;YAGT,MAAA,QAAQ,cAAc,KAAA,CAAO,CAAE;YAErC,IAAA,IAAU,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAI,GAAG,IAAO;gBAEhD,KAAA,CAAO,CAAE,CAAA,CAAE,IAAA,CAAM,IAAA,EAAM,KAAM;YAE9B;YAEA,MAAM,MAAA,GAAS;QAEhB;IAED;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "file": "DeviceOrientationControls.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/controls/DeviceOrientationControls.ts"], "sourcesContent": ["import { <PERSON>, <PERSON>uler, <PERSON><PERSON><PERSON><PERSON>, Quaternion, Vector3 } from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\n/**\n * W3C Device Orientation control (http://w3c.github.io/deviceorientation/spec-source-orientation.html)\n */\n\nclass DeviceOrientationControls extends EventDispatcher<StandardControlsEventMap> {\n  public object: Camera\n\n  private changeEvent = { type: 'change' }\n  private EPS = 0.000001\n\n  public enabled = true\n  public deviceOrientation: Partial<DeviceOrientationEvent> = { alpha: 0, beta: 0, gamma: 0 }\n  public screenOrientation: string | number = 0\n  public alphaOffset = 0 // radians\n\n  constructor(object: Camera) {\n    super()\n\n    this.object = object\n    this.object.rotation.reorder('YXZ')\n\n    this.connect()\n  }\n\n  private onDeviceOrientationChangeEvent = (event: DeviceOrientationEvent): void => {\n    this.deviceOrientation = event\n  }\n\n  private onScreenOrientationChangeEvent = (): void => {\n    this.screenOrientation = window.orientation || 0\n  }\n\n  // The angles alpha, beta and gamma form a set of intrinsic Tait-Bryan angles of type Z-X'-Y''\n\n  private zee = new Vector3(0, 0, 1)\n  private euler = new Euler()\n  private q0 = new Quaternion()\n  private q1 = new Quaternion(-Math.sqrt(0.5), 0, 0, Math.sqrt(0.5)) // - PI/2 around the x-axis\n  private setObjectQuaternion = (\n    quaternion: Quaternion,\n    alpha: number,\n    beta: number,\n    gamma: number,\n    orient: number,\n  ): void => {\n    this.euler.set(beta, alpha, -gamma, 'YXZ') // 'ZXY' for the device, but 'YXZ' for us\n    quaternion.setFromEuler(this.euler) // orient the device\n    quaternion.multiply(this.q1) // camera looks out the back of the device, not the top\n    quaternion.multiply(this.q0.setFromAxisAngle(this.zee, -orient)) // adjust for screen orientation\n  }\n\n  public connect = (): void => {\n    this.onScreenOrientationChangeEvent() // run once on load\n\n    // iOS 13+\n\n    if (\n      window.DeviceOrientationEvent !== undefined &&\n      // @ts-ignore\n      typeof window.DeviceOrientationEvent.requestPermission === 'function'\n    ) {\n      // @ts-ignore\n      window.DeviceOrientationEvent.requestPermission()\n        .then((response: any) => {\n          if (response == 'granted') {\n            window.addEventListener('orientationchange', this.onScreenOrientationChangeEvent)\n            window.addEventListener('deviceorientation', this.onDeviceOrientationChangeEvent)\n          }\n        })\n        .catch((error: any) => {\n          console.error('THREE.DeviceOrientationControls: Unable to use DeviceOrientation API:', error)\n        })\n    } else {\n      window.addEventListener('orientationchange', this.onScreenOrientationChangeEvent)\n      window.addEventListener('deviceorientation', this.onDeviceOrientationChangeEvent)\n    }\n\n    this.enabled = true\n  }\n\n  public disconnect = (): void => {\n    window.removeEventListener('orientationchange', this.onScreenOrientationChangeEvent)\n    window.removeEventListener('deviceorientation', this.onDeviceOrientationChangeEvent)\n\n    this.enabled = false\n  }\n\n  private lastQuaternion = new Quaternion()\n  public update = (): void => {\n    if (this.enabled === false) return\n\n    const device = this.deviceOrientation\n\n    if (device) {\n      const alpha = device.alpha ? MathUtils.degToRad(device.alpha) + this.alphaOffset : 0 // Z\n      const beta = device.beta ? MathUtils.degToRad(device.beta) : 0 // X'\n      const gamma = device.gamma ? MathUtils.degToRad(device.gamma) : 0 // Y''\n      const orient = this.screenOrientation ? MathUtils.degToRad(this.screenOrientation as number) : 0 // O\n\n      this.setObjectQuaternion(this.object.quaternion, alpha, beta, gamma, orient)\n\n      if (8 * (1 - this.lastQuaternion.dot(this.object.quaternion)) > this.EPS) {\n        this.lastQuaternion.copy(this.object.quaternion)\n        // @ts-ignore\n        this.dispatchEvent(this.changeEvent)\n      }\n    }\n  }\n\n  public dispose = (): void => this.disconnect()\n}\n\nexport { DeviceOrientationControls }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAQA,MAAM,wRAAkC,kBAAA,CAA0C;IAAA,UAAA;IAWhF,YAAY,MAAA,CAAgB;QACpB,KAAA;QAXD,cAAA,IAAA,EAAA;QAEC,cAAA,IAAA,EAAA,eAAc;YAAE,MAAM;QAAA;QACtB,cAAA,IAAA,EAAA,OAAM;QAEP,cAAA,IAAA,EAAA,WAAU;QACV,cAAA,IAAA,EAAA,qBAAqD;YAAE,OAAO;YAAG,MAAM;YAAG,OAAO;QAAA;QACjF,cAAA,IAAA,EAAA,qBAAqC;QACrC,cAAA,IAAA,EAAA,eAAc;QAWb,cAAA,IAAA,EAAA,kCAAiC,CAAC,UAAwC;YAChF,IAAA,CAAK,iBAAA,GAAoB;QAAA;QAGnB,cAAA,IAAA,EAAA,kCAAiC,MAAY;YAC9C,IAAA,CAAA,iBAAA,GAAoB,OAAO,WAAA,IAAe;QAAA;QAKzC,8FAAA;QAAA,cAAA,IAAA,EAAA,OAAM,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;QACzB,cAAA,IAAA,EAAA,SAAQ,2MAAI,QAAA;QACZ,cAAA,IAAA,EAAA,MAAK,2MAAI,aAAA;QACT,cAAA,IAAA,EAAA,MAAK,IAAI,oNAAA,CAAW,CAAC,KAAK,IAAA,CAAK,GAAG,GAAG,GAAG,GAAG,KAAK,IAAA,CAAK,GAAG,CAAC;QACzD,2BAAA;QAAA,cAAA,IAAA,EAAA,uBAAsB,CAC5B,YACA,OACA,MACA,OACA,WACS;YACT,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,MAAM,OAAO,CAAC,OAAO,KAAK;YAC9B,WAAA,YAAA,CAAa,IAAA,CAAK,KAAK;YACvB,WAAA,QAAA,CAAS,IAAA,CAAK,EAAE;YAChB,WAAA,QAAA,CAAS,IAAA,CAAK,EAAA,CAAG,gBAAA,CAAiB,IAAA,CAAK,GAAA,EAAK,CAAC,MAAM,CAAC;QAAA;QAG1D,cAAA,IAAA,EAAA,WAAU,MAAY;YAC3B,IAAA,CAAK,8BAAA,CAA+B;YAIpC,IACE,OAAO,sBAAA,KAA2B,KAAA,KAAA,aAAA;YAElC,OAAO,OAAO,sBAAA,CAAuB,iBAAA,KAAsB,YAC3D;gBAEA,OAAO,sBAAA,CAAuB,iBAAA,CAC3B,EAAA,IAAA,CAAK,CAAC,aAAkB;oBACvB,IAAI,YAAY,WAAW;wBAClB,OAAA,gBAAA,CAAiB,qBAAqB,IAAA,CAAK,8BAA8B;wBACzE,OAAA,gBAAA,CAAiB,qBAAqB,IAAA,CAAK,8BAA8B;oBAClF;gBAAA,CACD,EACA,KAAA,CAAM,CAAC,UAAe;oBACb,QAAA,KAAA,CAAM,yEAAyE,KAAK;gBAAA,CAC7F;YAAA,OACE;gBACE,OAAA,gBAAA,CAAiB,qBAAqB,IAAA,CAAK,8BAA8B;gBACzE,OAAA,gBAAA,CAAiB,qBAAqB,IAAA,CAAK,8BAA8B;YAClF;YAEA,IAAA,CAAK,OAAA,GAAU;QAAA;QAGV,cAAA,IAAA,EAAA,cAAa,MAAY;YACvB,OAAA,mBAAA,CAAoB,qBAAqB,IAAA,CAAK,8BAA8B;YAC5E,OAAA,mBAAA,CAAoB,qBAAqB,IAAA,CAAK,8BAA8B;YAEnF,IAAA,CAAK,OAAA,GAAU;QAAA;QAGT,cAAA,IAAA,EAAA,kBAAiB,2MAAI,aAAA;QACtB,cAAA,IAAA,EAAA,UAAS,MAAY;YAC1B,IAAI,IAAA,CAAK,OAAA,KAAY,OAAO;YAE5B,MAAM,SAAS,IAAA,CAAK,iBAAA;YAEpB,IAAI,QAAQ;gBACJ,MAAA,QAAQ,OAAO,KAAA,0MAAQ,YAAA,CAAU,QAAA,CAAS,OAAO,KAAK,IAAI,IAAA,CAAK,WAAA,GAAc;gBACnF,MAAM,OAAO,OAAO,IAAA,0MAAO,YAAA,CAAU,QAAA,CAAS,OAAO,IAAI,IAAI;gBAC7D,MAAM,QAAQ,OAAO,KAAA,0MAAQ,YAAA,CAAU,QAAA,CAAS,OAAO,KAAK,IAAI;gBAChE,MAAM,SAAS,IAAA,CAAK,iBAAA,0MAAoB,YAAA,CAAU,QAAA,CAAS,IAAA,CAAK,iBAA2B,IAAI;gBAE/F,IAAA,CAAK,mBAAA,CAAoB,IAAA,CAAK,MAAA,CAAO,UAAA,EAAY,OAAO,MAAM,OAAO,MAAM;gBAEvE,IAAA,IAAA,CAAK,IAAI,IAAA,CAAK,cAAA,CAAe,GAAA,CAAI,IAAA,CAAK,MAAA,CAAO,UAAU,CAAA,IAAK,IAAA,CAAK,GAAA,EAAK;oBACxE,IAAA,CAAK,cAAA,CAAe,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,UAAU;oBAE1C,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,WAAW;gBACrC;YACF;QAAA;QAGK,cAAA,IAAA,EAAA,WAAU,IAAY,IAAA,CAAK,UAAA;QA3FhC,IAAA,CAAK,MAAA,GAAS;QACT,IAAA,CAAA,MAAA,CAAO,QAAA,CAAS,OAAA,CAAQ,KAAK;QAElC,IAAA,CAAK,OAAA,CAAQ;IACf;AAwFF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "file": "FlyControls.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/controls/FlyControls.ts"], "sourcesContent": ["import { Camera, Quaternion, Vector3 } from 'three'\nimport { EventDispatcher } from './EventDispatcher'\n\nfunction contextmenu(event: Event): void {\n  event.preventDefault()\n}\n\nexport interface FlyControlsEventMap {\n  /**\n   * Fires when the camera has been transformed by the controls.\n   */\n  change: {};\n}\n\nclass FlyControls extends EventDispatcher<FlyControlsEventMap> {\n  public object: Camera\n  public domElement: HTMLElement | Document = null!\n\n  public movementSpeed = 1.0\n  public rollSpeed = 0.005\n\n  public dragToLook = false\n  public autoForward = false\n\n  private changeEvent = { type: 'change' }\n  private EPS = 0.000001\n\n  private tmpQuaternion = new Quaternion()\n\n  private mouseStatus = 0\n\n  private movementSpeedMultiplier = 1\n\n  private moveState = {\n    up: 0,\n    down: 0,\n    left: 0,\n    right: 0,\n    forward: 0,\n    back: 0,\n    pitchUp: 0,\n    pitchDown: 0,\n    yawLeft: 0,\n    yawRight: 0,\n    rollLeft: 0,\n    rollRight: 0,\n  }\n  private moveVector = new Vector3(0, 0, 0)\n  private rotationVector = new Vector3(0, 0, 0)\n\n  constructor(object: Camera, domElement?: HTMLElement | Document) {\n    super()\n\n    this.object = object\n\n    // connect events\n    if (domElement !== undefined) this.connect(domElement)\n\n    this.updateMovementVector()\n    this.updateRotationVector()\n  }\n\n  private keydown = (event: KeyboardEvent): void => {\n    if (event.altKey) {\n      return\n    }\n\n    switch (event.code) {\n      case 'ShiftLeft':\n      case 'ShiftRight':\n        this.movementSpeedMultiplier = 0.1\n        break\n\n      case 'KeyW':\n        this.moveState.forward = 1\n        break\n      case 'KeyS':\n        this.moveState.back = 1\n        break\n\n      case 'KeyA':\n        this.moveState.left = 1\n        break\n      case 'KeyD':\n        this.moveState.right = 1\n        break\n\n      case 'KeyR':\n        this.moveState.up = 1\n        break\n      case 'KeyF':\n        this.moveState.down = 1\n        break\n\n      case 'ArrowUp':\n        this.moveState.pitchUp = 1\n        break\n      case 'ArrowDown':\n        this.moveState.pitchDown = 1\n        break\n\n      case 'ArrowLeft':\n        this.moveState.yawLeft = 1\n        break\n      case 'ArrowRight':\n        this.moveState.yawRight = 1\n        break\n\n      case 'KeyQ':\n        this.moveState.rollLeft = 1\n        break\n      case 'KeyE':\n        this.moveState.rollRight = 1\n        break\n    }\n\n    this.updateMovementVector()\n    this.updateRotationVector()\n  }\n\n  private keyup = (event: KeyboardEvent): void => {\n    switch (event.code) {\n      case 'ShiftLeft':\n      case 'ShiftRight':\n        this.movementSpeedMultiplier = 1\n        break\n\n      case 'KeyW':\n        this.moveState.forward = 0\n        break\n      case 'KeyS':\n        this.moveState.back = 0\n        break\n\n      case 'KeyA':\n        this.moveState.left = 0\n        break\n      case 'KeyD':\n        this.moveState.right = 0\n        break\n\n      case 'KeyR':\n        this.moveState.up = 0\n        break\n      case 'KeyF':\n        this.moveState.down = 0\n        break\n\n      case 'ArrowUp':\n        this.moveState.pitchUp = 0\n        break\n      case 'ArrowDown':\n        this.moveState.pitchDown = 0\n        break\n\n      case 'ArrowLeft':\n        this.moveState.yawLeft = 0\n        break\n      case 'ArrowRight':\n        this.moveState.yawRight = 0\n        break\n\n      case 'KeyQ':\n        this.moveState.rollLeft = 0\n        break\n      case 'KeyE':\n        this.moveState.rollRight = 0\n        break\n    }\n\n    this.updateMovementVector()\n    this.updateRotationVector()\n  }\n\n  private pointerdown = (event: MouseEvent): void => {\n    if (this.dragToLook) {\n      this.mouseStatus++\n    } else {\n      switch (event.button) {\n        case 0:\n          this.moveState.forward = 1\n          break\n        case 2:\n          this.moveState.back = 1\n          break\n      }\n\n      this.updateMovementVector()\n    }\n  }\n\n  private pointermove = (event: MouseEvent): void => {\n    if (!this.dragToLook || this.mouseStatus > 0) {\n      const container = this.getContainerDimensions()\n      const halfWidth = container.size[0] / 2\n      const halfHeight = container.size[1] / 2\n\n      this.moveState.yawLeft = -(event.pageX - container.offset[0] - halfWidth) / halfWidth\n      this.moveState.pitchDown = (event.pageY - container.offset[1] - halfHeight) / halfHeight\n\n      this.updateRotationVector()\n    }\n  }\n\n  private pointerup = (event: MouseEvent): void => {\n    if (this.dragToLook) {\n      this.mouseStatus--\n\n      this.moveState.yawLeft = this.moveState.pitchDown = 0\n    } else {\n      switch (event.button) {\n        case 0:\n          this.moveState.forward = 0\n          break\n        case 2:\n          this.moveState.back = 0\n          break\n      }\n\n      this.updateMovementVector()\n    }\n\n    this.updateRotationVector()\n  }\n\n  private lastQuaternion = new Quaternion()\n  private lastPosition = new Vector3()\n\n  public update = (delta: number): void => {\n    const moveMult = delta * this.movementSpeed\n    const rotMult = delta * this.rollSpeed\n\n    this.object.translateX(this.moveVector.x * moveMult)\n    this.object.translateY(this.moveVector.y * moveMult)\n    this.object.translateZ(this.moveVector.z * moveMult)\n\n    this.tmpQuaternion\n      .set(this.rotationVector.x * rotMult, this.rotationVector.y * rotMult, this.rotationVector.z * rotMult, 1)\n      .normalize()\n    this.object.quaternion.multiply(this.tmpQuaternion)\n\n    if (\n      this.lastPosition.distanceToSquared(this.object.position) > this.EPS ||\n      8 * (1 - this.lastQuaternion.dot(this.object.quaternion)) > this.EPS\n    ) {\n      // @ts-ignore\n      this.dispatchEvent(this.changeEvent)\n      this.lastQuaternion.copy(this.object.quaternion)\n      this.lastPosition.copy(this.object.position)\n    }\n  }\n\n  private updateMovementVector = (): void => {\n    const forward = this.moveState.forward || (this.autoForward && !this.moveState.back) ? 1 : 0\n\n    this.moveVector.x = -this.moveState.left + this.moveState.right\n    this.moveVector.y = -this.moveState.down + this.moveState.up\n    this.moveVector.z = -forward + this.moveState.back\n  }\n\n  private updateRotationVector = (): void => {\n    this.rotationVector.x = -this.moveState.pitchDown + this.moveState.pitchUp\n    this.rotationVector.y = -this.moveState.yawRight + this.moveState.yawLeft\n    this.rotationVector.z = -this.moveState.rollRight + this.moveState.rollLeft\n  }\n\n  private getContainerDimensions = (): {\n    size: number[]\n    offset: number[]\n  } => {\n    if (this.domElement != document && !(this.domElement instanceof Document)) {\n      return {\n        size: [this.domElement.offsetWidth, this.domElement.offsetHeight],\n        offset: [this.domElement.offsetLeft, this.domElement.offsetTop],\n      }\n    } else {\n      return {\n        size: [window.innerWidth, window.innerHeight],\n        offset: [0, 0],\n      }\n    }\n  }\n\n  // https://github.com/mrdoob/three.js/issues/20575\n  public connect = (domElement: HTMLElement | Document): void => {\n    this.domElement = domElement\n\n    if (!(domElement instanceof Document)) {\n      domElement.setAttribute('tabindex', -1 as any)\n    }\n\n    this.domElement.addEventListener('contextmenu', contextmenu)\n    ;(this.domElement as HTMLElement).addEventListener('pointermove', this.pointermove)\n    ;(this.domElement as HTMLElement).addEventListener('pointerdown', this.pointerdown)\n    ;(this.domElement as HTMLElement).addEventListener('pointerup', this.pointerup)\n\n    window.addEventListener('keydown', this.keydown)\n    window.addEventListener('keyup', this.keyup)\n  }\n\n  public dispose = (): void => {\n    this.domElement.removeEventListener('contextmenu', contextmenu)\n    ;(this.domElement as HTMLElement).removeEventListener('pointermove', this.pointermove)\n    ;(this.domElement as HTMLElement).removeEventListener('pointerdown', this.pointerdown)\n    ;(this.domElement as HTMLElement).removeEventListener('pointerup', this.pointerup)\n\n    window.removeEventListener('keydown', this.keydown)\n    window.removeEventListener('keyup', this.keyup)\n  }\n}\n\nexport { FlyControls }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA,SAAS,YAAY,KAAA,EAAoB;IACvC,MAAM,cAAA,CAAe;AACvB;AASA,MAAM,0QAAoB,kBAAA,CAAqC;IAoC7D,YAAY,MAAA,EAAgB,UAAA,CAAqC;QACzD,KAAA;QApCD,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA,cAAqC;QAErC,cAAA,IAAA,EAAA,iBAAgB;QAChB,cAAA,IAAA,EAAA,aAAY;QAEZ,cAAA,IAAA,EAAA,cAAa;QACb,cAAA,IAAA,EAAA,eAAc;QAEb,cAAA,IAAA,EAAA,eAAc;YAAE,MAAM;QAAA;QACtB,cAAA,IAAA,EAAA,OAAM;QAEN,cAAA,IAAA,EAAA,iBAAgB,2MAAI,aAAA;QAEpB,cAAA,IAAA,EAAA,eAAc;QAEd,cAAA,IAAA,EAAA,2BAA0B;QAE1B,cAAA,IAAA,EAAA,aAAY;YAClB,IAAI;YACJ,MAAM;YACN,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM;YACN,SAAS;YACT,WAAW;YACX,SAAS;YACT,UAAU;YACV,UAAU;YACV,WAAW;QAAA;QAEL,cAAA,IAAA,EAAA,cAAa,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;QAChC,cAAA,IAAA,EAAA,kBAAiB,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;QAcpC,cAAA,IAAA,EAAA,WAAU,CAAC,UAA+B;YAChD,IAAI,MAAM,MAAA,EAAQ;gBAChB;YACF;YAEA,OAAQ,MAAM,IAAA,EAAM;gBAClB,KAAK;gBACL,KAAK;oBACH,IAAA,CAAK,uBAAA,GAA0B;oBAC/B;gBAEF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,OAAA,GAAU;oBACzB;gBACF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,IAAA,GAAO;oBACtB;gBAEF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,IAAA,GAAO;oBACtB;gBACF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,KAAA,GAAQ;oBACvB;gBAEF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,EAAA,GAAK;oBACpB;gBACF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,IAAA,GAAO;oBACtB;gBAEF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,OAAA,GAAU;oBACzB;gBACF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,SAAA,GAAY;oBAC3B;gBAEF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,OAAA,GAAU;oBACzB;gBACF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,QAAA,GAAW;oBAC1B;gBAEF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,QAAA,GAAW;oBAC1B;gBACF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,SAAA,GAAY;oBAC3B;YACJ;YAEA,IAAA,CAAK,oBAAA,CAAqB;YAC1B,IAAA,CAAK,oBAAA,CAAqB;QAAA;QAGpB,cAAA,IAAA,EAAA,SAAQ,CAAC,UAA+B;YAC9C,OAAQ,MAAM,IAAA,EAAM;gBAClB,KAAK;gBACL,KAAK;oBACH,IAAA,CAAK,uBAAA,GAA0B;oBAC/B;gBAEF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,OAAA,GAAU;oBACzB;gBACF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,IAAA,GAAO;oBACtB;gBAEF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,IAAA,GAAO;oBACtB;gBACF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,KAAA,GAAQ;oBACvB;gBAEF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,EAAA,GAAK;oBACpB;gBACF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,IAAA,GAAO;oBACtB;gBAEF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,OAAA,GAAU;oBACzB;gBACF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,SAAA,GAAY;oBAC3B;gBAEF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,OAAA,GAAU;oBACzB;gBACF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,QAAA,GAAW;oBAC1B;gBAEF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,QAAA,GAAW;oBAC1B;gBACF,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU,SAAA,GAAY;oBAC3B;YACJ;YAEA,IAAA,CAAK,oBAAA,CAAqB;YAC1B,IAAA,CAAK,oBAAA,CAAqB;QAAA;QAGpB,cAAA,IAAA,EAAA,eAAc,CAAC,UAA4B;YACjD,IAAI,IAAA,CAAK,UAAA,EAAY;gBACd,IAAA,CAAA,WAAA;YAAA,OACA;gBACL,OAAQ,MAAM,MAAA,EAAQ;oBACpB,KAAK;wBACH,IAAA,CAAK,SAAA,CAAU,OAAA,GAAU;wBACzB;oBACF,KAAK;wBACH,IAAA,CAAK,SAAA,CAAU,IAAA,GAAO;wBACtB;gBACJ;gBAEA,IAAA,CAAK,oBAAA,CAAqB;YAC5B;QAAA;QAGM,cAAA,IAAA,EAAA,eAAc,CAAC,UAA4B;YACjD,IAAI,CAAC,IAAA,CAAK,UAAA,IAAc,IAAA,CAAK,WAAA,GAAc,GAAG;gBACtC,MAAA,YAAY,IAAA,CAAK,sBAAA;gBACvB,MAAM,YAAY,UAAU,IAAA,CAAK,CAAC,CAAA,GAAI;gBACtC,MAAM,aAAa,UAAU,IAAA,CAAK,CAAC,CAAA,GAAI;gBAElC,IAAA,CAAA,SAAA,CAAU,OAAA,GAAU,CAAA,CAAE,MAAM,KAAA,GAAQ,UAAU,MAAA,CAAO,CAAC,CAAA,GAAI,SAAA,IAAa;gBACvE,IAAA,CAAA,SAAA,CAAU,SAAA,GAAA,CAAa,MAAM,KAAA,GAAQ,UAAU,MAAA,CAAO,CAAC,CAAA,GAAI,UAAA,IAAc;gBAE9E,IAAA,CAAK,oBAAA,CAAqB;YAC5B;QAAA;QAGM,cAAA,IAAA,EAAA,aAAY,CAAC,UAA4B;YAC/C,IAAI,IAAA,CAAK,UAAA,EAAY;gBACd,IAAA,CAAA,WAAA;gBAEL,IAAA,CAAK,SAAA,CAAU,OAAA,GAAU,IAAA,CAAK,SAAA,CAAU,SAAA,GAAY;YAAA,OAC/C;gBACL,OAAQ,MAAM,MAAA,EAAQ;oBACpB,KAAK;wBACH,IAAA,CAAK,SAAA,CAAU,OAAA,GAAU;wBACzB;oBACF,KAAK;wBACH,IAAA,CAAK,SAAA,CAAU,IAAA,GAAO;wBACtB;gBACJ;gBAEA,IAAA,CAAK,oBAAA,CAAqB;YAC5B;YAEA,IAAA,CAAK,oBAAA,CAAqB;QAAA;QAGpB,cAAA,IAAA,EAAA,kBAAiB,2MAAI,aAAA;QACrB,cAAA,IAAA,EAAA,gBAAe,2MAAI,UAAA;QAEpB,cAAA,IAAA,EAAA,UAAS,CAAC,UAAwB;YACjC,MAAA,WAAW,QAAQ,IAAA,CAAK,aAAA;YACxB,MAAA,UAAU,QAAQ,IAAA,CAAK,SAAA;YAE7B,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,IAAA,CAAK,UAAA,CAAW,CAAA,GAAI,QAAQ;YACnD,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,IAAA,CAAK,UAAA,CAAW,CAAA,GAAI,QAAQ;YACnD,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,IAAA,CAAK,UAAA,CAAW,CAAA,GAAI,QAAQ;YAEnD,IAAA,CAAK,aAAA,CACF,GAAA,CAAI,IAAA,CAAK,cAAA,CAAe,CAAA,GAAI,SAAS,IAAA,CAAK,cAAA,CAAe,CAAA,GAAI,SAAS,IAAA,CAAK,cAAA,CAAe,CAAA,GAAI,SAAS,CAAC,EACxG,SAAA;YACH,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,QAAA,CAAS,IAAA,CAAK,aAAa;YAElD,IACE,IAAA,CAAK,YAAA,CAAa,iBAAA,CAAkB,IAAA,CAAK,MAAA,CAAO,QAAQ,IAAI,IAAA,CAAK,GAAA,IACjE,IAAA,CAAK,IAAI,IAAA,CAAK,cAAA,CAAe,GAAA,CAAI,IAAA,CAAK,MAAA,CAAO,UAAU,CAAA,IAAK,IAAA,CAAK,GAAA,EACjE;gBAEK,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,WAAW;gBACnC,IAAA,CAAK,cAAA,CAAe,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,UAAU;gBAC/C,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,QAAQ;YAC7C;QAAA;QAGM,cAAA,IAAA,EAAA,wBAAuB,MAAY;YACnC,MAAA,UAAU,IAAA,CAAK,SAAA,CAAU,OAAA,IAAY,IAAA,CAAK,WAAA,IAAe,CAAC,IAAA,CAAK,SAAA,CAAU,IAAA,GAAQ,IAAI;YAE3F,IAAA,CAAK,UAAA,CAAW,CAAA,GAAI,CAAC,IAAA,CAAK,SAAA,CAAU,IAAA,GAAO,IAAA,CAAK,SAAA,CAAU,KAAA;YAC1D,IAAA,CAAK,UAAA,CAAW,CAAA,GAAI,CAAC,IAAA,CAAK,SAAA,CAAU,IAAA,GAAO,IAAA,CAAK,SAAA,CAAU,EAAA;YAC1D,IAAA,CAAK,UAAA,CAAW,CAAA,GAAI,CAAC,UAAU,IAAA,CAAK,SAAA,CAAU,IAAA;QAAA;QAGxC,cAAA,IAAA,EAAA,wBAAuB,MAAY;YACzC,IAAA,CAAK,cAAA,CAAe,CAAA,GAAI,CAAC,IAAA,CAAK,SAAA,CAAU,SAAA,GAAY,IAAA,CAAK,SAAA,CAAU,OAAA;YACnE,IAAA,CAAK,cAAA,CAAe,CAAA,GAAI,CAAC,IAAA,CAAK,SAAA,CAAU,QAAA,GAAW,IAAA,CAAK,SAAA,CAAU,OAAA;YAClE,IAAA,CAAK,cAAA,CAAe,CAAA,GAAI,CAAC,IAAA,CAAK,SAAA,CAAU,SAAA,GAAY,IAAA,CAAK,SAAA,CAAU,QAAA;QAAA;QAG7D,cAAA,IAAA,EAAA,0BAAyB,MAG5B;YACH,IAAI,IAAA,CAAK,UAAA,IAAc,YAAY,CAAA,CAAE,IAAA,CAAK,UAAA,YAAsB,QAAA,GAAW;gBAClE,OAAA;oBACL,MAAM;wBAAC,IAAA,CAAK,UAAA,CAAW,WAAA;wBAAa,IAAA,CAAK,UAAA,CAAW,YAAY;qBAAA;oBAChE,QAAQ;wBAAC,IAAA,CAAK,UAAA,CAAW,UAAA;wBAAY,IAAA,CAAK,UAAA,CAAW,SAAS;qBAAA;gBAAA;YAChE,OACK;gBACE,OAAA;oBACL,MAAM;wBAAC,OAAO,UAAA;wBAAY,OAAO,WAAW;qBAAA;oBAC5C,QAAQ;wBAAC;wBAAG,CAAC;qBAAA;gBAAA;YAEjB;QAAA;QAIK,kDAAA;QAAA,cAAA,IAAA,EAAA,WAAU,CAAC,eAA6C;YAC7D,IAAA,CAAK,UAAA,GAAa;YAEd,IAAA,CAAA,CAAE,sBAAsB,QAAA,GAAW;gBAC1B,WAAA,YAAA,CAAa,YAAY,CAAA,CAAS;YAC/C;YAEK,IAAA,CAAA,UAAA,CAAW,gBAAA,CAAiB,eAAe,WAAW;YACzD,IAAA,CAAK,UAAA,CAA2B,gBAAA,CAAiB,eAAe,IAAA,CAAK,WAAW;YAChF,IAAA,CAAK,UAAA,CAA2B,gBAAA,CAAiB,eAAe,IAAA,CAAK,WAAW;YAChF,IAAA,CAAK,UAAA,CAA2B,gBAAA,CAAiB,aAAa,IAAA,CAAK,SAAS;YAEvE,OAAA,gBAAA,CAAiB,WAAW,IAAA,CAAK,OAAO;YACxC,OAAA,gBAAA,CAAiB,SAAS,IAAA,CAAK,KAAK;QAAA;QAGtC,cAAA,IAAA,EAAA,WAAU,MAAY;YACtB,IAAA,CAAA,UAAA,CAAW,mBAAA,CAAoB,eAAe,WAAW;YAC5D,IAAA,CAAK,UAAA,CAA2B,mBAAA,CAAoB,eAAe,IAAA,CAAK,WAAW;YACnF,IAAA,CAAK,UAAA,CAA2B,mBAAA,CAAoB,eAAe,IAAA,CAAK,WAAW;YACnF,IAAA,CAAK,UAAA,CAA2B,mBAAA,CAAoB,aAAa,IAAA,CAAK,SAAS;YAE1E,OAAA,mBAAA,CAAoB,WAAW,IAAA,CAAK,OAAO;YAC3C,OAAA,mBAAA,CAAoB,SAAS,IAAA,CAAK,KAAK;QAAA;QA9P9C,IAAA,CAAK,MAAA,GAAS;QAGd,IAAI,eAAe,KAAA,GAAW,IAAA,CAAK,OAAA,CAAQ,UAAU;QAErD,IAAA,CAAK,oBAAA,CAAqB;QAC1B,IAAA,CAAK,oBAAA,CAAqB;IAC5B;AAyPF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "file": "OrbitControls.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/controls/OrbitControls.ts"], "sourcesContent": ["import {\n  Matrix4,\n  MOUSE,\n  OrthographicCamera,\n  PerspectiveCamera,\n  Quaternion,\n  Spherical,\n  TOUCH,\n  Vector2,\n  Vector3,\n  Ray,\n  Plane,\n} from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\nconst _ray = /* @__PURE__ */ new Ray()\nconst _plane = /* @__PURE__ */ new Plane()\nconst TILT_LIMIT = Math.cos(70 * (Math.PI / 180))\n\n// This set of controls performs orbiting, dollying (zooming), and panning.\n// Unlike TrackballControls, it maintains the \"up\" direction object.up (+Y by default).\n//\n//    Orbit - left mouse / touch: one-finger move\n//    Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n//    Pan - right mouse, or left mouse + ctrl/meta/shiftKey, or arrow keys / touch: two-finger move\n\nconst moduloWrapAround = (offset: number, capacity: number) => ((offset % capacity) + capacity) % capacity\n\nclass OrbitControls extends EventDispatcher<StandardControlsEventMap> {\n  object: PerspectiveCamera | OrthographicCamera\n  domElement: HTMLElement | undefined\n  // Set to false to disable this control\n  enabled = true\n  // \"target\" sets the location of focus, where the object orbits around\n  target = new Vector3()\n  // How far you can dolly in and out ( PerspectiveCamera only )\n  minDistance = 0\n  maxDistance = Infinity\n  // How far you can zoom in and out ( OrthographicCamera only )\n  minZoom = 0\n  maxZoom = Infinity\n  // How far you can orbit vertically, upper and lower limits.\n  // Range is 0 to Math.PI radians.\n  minPolarAngle = 0 // radians\n  maxPolarAngle = Math.PI // radians\n  // How far you can orbit horizontally, upper and lower limits.\n  // If set, the interval [ min, max ] must be a sub-interval of [ - 2 PI, 2 PI ], with ( max - min < 2 PI )\n  minAzimuthAngle = -Infinity // radians\n  maxAzimuthAngle = Infinity // radians\n  // Set to true to enable damping (inertia)\n  // If damping is enabled, you must call controls.update() in your animation loop\n  enableDamping = false\n  dampingFactor = 0.05\n  // This option actually enables dollying in and out; left as \"zoom\" for backwards compatibility.\n  // Set to false to disable zooming\n  enableZoom = true\n  zoomSpeed = 1.0\n  // Set to false to disable rotating\n  enableRotate = true\n  rotateSpeed = 1.0\n  // Set to false to disable panning\n  enablePan = true\n  panSpeed = 1.0\n  screenSpacePanning = true // if false, pan orthogonal to world-space direction camera.up\n  keyPanSpeed = 7.0 // pixels moved per arrow key push\n  zoomToCursor = false\n  // Set to true to automatically rotate around the target\n  // If auto-rotate is enabled, you must call controls.update() in your animation loop\n  autoRotate = false\n  autoRotateSpeed = 2.0 // 30 seconds per orbit when fps is 60\n  reverseOrbit = false // true if you want to reverse the orbit to mouse drag from left to right = orbits left\n  reverseHorizontalOrbit = false // true if you want to reverse the horizontal orbit direction\n  reverseVerticalOrbit = false // true if you want to reverse the vertical orbit direction\n  // The four arrow keys\n  keys = { LEFT: 'ArrowLeft', UP: 'ArrowUp', RIGHT: 'ArrowRight', BOTTOM: 'ArrowDown' }\n  // Mouse buttons\n  mouseButtons: Partial<{\n    LEFT: MOUSE\n    MIDDLE: MOUSE\n    RIGHT: MOUSE\n  }> = {\n    LEFT: MOUSE.ROTATE,\n    MIDDLE: MOUSE.DOLLY,\n    RIGHT: MOUSE.PAN,\n  }\n  // Touch fingers\n  touches: Partial<{\n    ONE: TOUCH\n    TWO: TOUCH\n  }> = { ONE: TOUCH.ROTATE, TWO: TOUCH.DOLLY_PAN }\n  target0: Vector3\n  position0: Vector3\n  zoom0: number\n  // the target DOM element for key events\n  _domElementKeyEvents: any = null\n\n  getPolarAngle: () => number\n  getAzimuthalAngle: () => number\n  setPolarAngle: (x: number) => void\n  setAzimuthalAngle: (x: number) => void\n  getDistance: () => number\n  // Not used in most scenarios, however they can be useful for specific use cases\n  getZoomScale: () => number\n\n  listenToKeyEvents: (domElement: HTMLElement) => void\n  stopListenToKeyEvents: () => void\n  saveState: () => void\n  reset: () => void\n  update: () => void\n  connect: (domElement: HTMLElement) => void\n  dispose: () => void\n\n  // Dolly in programmatically\n  dollyIn: (dollyScale?: number) => void\n  // Dolly out programmatically\n  dollyOut: (dollyScale?: number) => void\n  // Get the current scale\n  getScale: () => number\n  // Set the current scale (these are not used in most scenarios, however they can be useful for specific use cases)\n  setScale: (newScale: number) => void\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement) {\n    super()\n\n    this.object = object\n    this.domElement = domElement\n\n    // for reset\n    this.target0 = this.target.clone()\n    this.position0 = this.object.position.clone()\n    this.zoom0 = this.object.zoom\n\n    //\n    // public methods\n    //\n\n    this.getPolarAngle = (): number => spherical.phi\n\n    this.getAzimuthalAngle = (): number => spherical.theta\n\n    this.setPolarAngle = (value: number): void => {\n      // use modulo wrapping to safeguard value\n      let phi = moduloWrapAround(value, 2 * Math.PI)\n      let currentPhi = spherical.phi\n\n      // convert to the equivalent shortest angle\n      if (currentPhi < 0) currentPhi += 2 * Math.PI\n      if (phi < 0) phi += 2 * Math.PI\n      let phiDist = Math.abs(phi - currentPhi)\n      if (2 * Math.PI - phiDist < phiDist) {\n        if (phi < currentPhi) {\n          phi += 2 * Math.PI\n        } else {\n          currentPhi += 2 * Math.PI\n        }\n      }\n      sphericalDelta.phi = phi - currentPhi\n      scope.update()\n    }\n\n    this.setAzimuthalAngle = (value: number): void => {\n      // use modulo wrapping to safeguard value\n      let theta = moduloWrapAround(value, 2 * Math.PI)\n      let currentTheta = spherical.theta\n\n      // convert to the equivalent shortest angle\n      if (currentTheta < 0) currentTheta += 2 * Math.PI\n      if (theta < 0) theta += 2 * Math.PI\n      let thetaDist = Math.abs(theta - currentTheta)\n      if (2 * Math.PI - thetaDist < thetaDist) {\n        if (theta < currentTheta) {\n          theta += 2 * Math.PI\n        } else {\n          currentTheta += 2 * Math.PI\n        }\n      }\n      sphericalDelta.theta = theta - currentTheta\n      scope.update()\n    }\n\n    this.getDistance = (): number => scope.object.position.distanceTo(scope.target)\n\n    this.listenToKeyEvents = (domElement: HTMLElement): void => {\n      domElement.addEventListener('keydown', onKeyDown)\n      this._domElementKeyEvents = domElement\n    }\n\n    this.stopListenToKeyEvents = (): void => {\n      this._domElementKeyEvents.removeEventListener('keydown', onKeyDown)\n      this._domElementKeyEvents = null\n    }\n\n    this.saveState = (): void => {\n      scope.target0.copy(scope.target)\n      scope.position0.copy(scope.object.position)\n      scope.zoom0 = scope.object.zoom\n    }\n\n    this.reset = (): void => {\n      scope.target.copy(scope.target0)\n      scope.object.position.copy(scope.position0)\n      scope.object.zoom = scope.zoom0\n      scope.object.updateProjectionMatrix()\n\n      // @ts-ignore\n      scope.dispatchEvent(changeEvent)\n\n      scope.update()\n\n      state = STATE.NONE\n    }\n\n    // this method is exposed, but perhaps it would be better if we can make it private...\n    this.update = ((): (() => void) => {\n      const offset = new Vector3()\n      const up = new Vector3(0, 1, 0)\n\n      // so camera.up is the orbit axis\n      const quat = new Quaternion().setFromUnitVectors(object.up, up)\n      const quatInverse = quat.clone().invert()\n\n      const lastPosition = new Vector3()\n      const lastQuaternion = new Quaternion()\n\n      const twoPI = 2 * Math.PI\n\n      return function update(): boolean {\n        const position = scope.object.position\n\n        // update new up direction\n        quat.setFromUnitVectors(object.up, up)\n        quatInverse.copy(quat).invert()\n\n        offset.copy(position).sub(scope.target)\n\n        // rotate offset to \"y-axis-is-up\" space\n        offset.applyQuaternion(quat)\n\n        // angle from z-axis around y-axis\n        spherical.setFromVector3(offset)\n\n        if (scope.autoRotate && state === STATE.NONE) {\n          rotateLeft(getAutoRotationAngle())\n        }\n\n        if (scope.enableDamping) {\n          spherical.theta += sphericalDelta.theta * scope.dampingFactor\n          spherical.phi += sphericalDelta.phi * scope.dampingFactor\n        } else {\n          spherical.theta += sphericalDelta.theta\n          spherical.phi += sphericalDelta.phi\n        }\n\n        // restrict theta to be between desired limits\n\n        let min = scope.minAzimuthAngle\n        let max = scope.maxAzimuthAngle\n\n        if (isFinite(min) && isFinite(max)) {\n          if (min < -Math.PI) min += twoPI\n          else if (min > Math.PI) min -= twoPI\n\n          if (max < -Math.PI) max += twoPI\n          else if (max > Math.PI) max -= twoPI\n\n          if (min <= max) {\n            spherical.theta = Math.max(min, Math.min(max, spherical.theta))\n          } else {\n            spherical.theta =\n              spherical.theta > (min + max) / 2 ? Math.max(min, spherical.theta) : Math.min(max, spherical.theta)\n          }\n        }\n\n        // restrict phi to be between desired limits\n        spherical.phi = Math.max(scope.minPolarAngle, Math.min(scope.maxPolarAngle, spherical.phi))\n        spherical.makeSafe()\n\n        // move target to panned location\n\n        if (scope.enableDamping === true) {\n          scope.target.addScaledVector(panOffset, scope.dampingFactor)\n        } else {\n          scope.target.add(panOffset)\n        }\n\n        // adjust the camera position based on zoom only if we're not zooming to the cursor or if it's an ortho camera\n        // we adjust zoom later in these cases\n        if ((scope.zoomToCursor && performCursorZoom) || (scope.object as OrthographicCamera).isOrthographicCamera) {\n          spherical.radius = clampDistance(spherical.radius)\n        } else {\n          spherical.radius = clampDistance(spherical.radius * scale)\n        }\n\n        offset.setFromSpherical(spherical)\n\n        // rotate offset back to \"camera-up-vector-is-up\" space\n        offset.applyQuaternion(quatInverse)\n\n        position.copy(scope.target).add(offset)\n\n        if (!scope.object.matrixAutoUpdate) scope.object.updateMatrix()\n        scope.object.lookAt(scope.target)\n\n        if (scope.enableDamping === true) {\n          sphericalDelta.theta *= 1 - scope.dampingFactor\n          sphericalDelta.phi *= 1 - scope.dampingFactor\n\n          panOffset.multiplyScalar(1 - scope.dampingFactor)\n        } else {\n          sphericalDelta.set(0, 0, 0)\n\n          panOffset.set(0, 0, 0)\n        }\n\n        // adjust camera position\n        let zoomChanged = false\n        if (scope.zoomToCursor && performCursorZoom) {\n          let newRadius = null\n          if (scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) {\n            // move the camera down the pointer ray\n            // this method avoids floating point error\n            const prevRadius = offset.length()\n            newRadius = clampDistance(prevRadius * scale)\n\n            const radiusDelta = prevRadius - newRadius\n            scope.object.position.addScaledVector(dollyDirection, radiusDelta)\n            scope.object.updateMatrixWorld()\n          } else if ((scope.object as OrthographicCamera).isOrthographicCamera) {\n            // adjust the ortho camera position based on zoom changes\n            const mouseBefore = new Vector3(mouse.x, mouse.y, 0)\n            mouseBefore.unproject(scope.object)\n\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale))\n            scope.object.updateProjectionMatrix()\n            zoomChanged = true\n\n            const mouseAfter = new Vector3(mouse.x, mouse.y, 0)\n            mouseAfter.unproject(scope.object)\n\n            scope.object.position.sub(mouseAfter).add(mouseBefore)\n            scope.object.updateMatrixWorld()\n\n            newRadius = offset.length()\n          } else {\n            console.warn('WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled.')\n            scope.zoomToCursor = false\n          }\n\n          // handle the placement of the target\n          if (newRadius !== null) {\n            if (scope.screenSpacePanning) {\n              // position the orbit target in front of the new camera position\n              scope.target\n                .set(0, 0, -1)\n                .transformDirection(scope.object.matrix)\n                .multiplyScalar(newRadius)\n                .add(scope.object.position)\n            } else {\n              // get the ray and translation plane to compute target\n              _ray.origin.copy(scope.object.position)\n              _ray.direction.set(0, 0, -1).transformDirection(scope.object.matrix)\n\n              // if the camera is 20 degrees above the horizon then don't adjust the focus target to avoid\n              // extremely large values\n              if (Math.abs(scope.object.up.dot(_ray.direction)) < TILT_LIMIT) {\n                object.lookAt(scope.target)\n              } else {\n                _plane.setFromNormalAndCoplanarPoint(scope.object.up, scope.target)\n                _ray.intersectPlane(_plane, scope.target)\n              }\n            }\n          }\n        } else if (scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera) {\n          zoomChanged = scale !== 1\n\n          if (zoomChanged) {\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale))\n            scope.object.updateProjectionMatrix()\n          }\n        }\n\n        scale = 1\n        performCursorZoom = false\n\n        // update condition is:\n        // min(camera displacement, camera rotation in radians)^2 > EPS\n        // using small-angle approximation cos(x/2) = 1 - x^2 / 8\n\n        if (\n          zoomChanged ||\n          lastPosition.distanceToSquared(scope.object.position) > EPS ||\n          8 * (1 - lastQuaternion.dot(scope.object.quaternion)) > EPS\n        ) {\n          // @ts-ignore\n          scope.dispatchEvent(changeEvent)\n\n          lastPosition.copy(scope.object.position)\n          lastQuaternion.copy(scope.object.quaternion)\n          zoomChanged = false\n\n          return true\n        }\n\n        return false\n      }\n    })()\n\n    // https://github.com/mrdoob/three.js/issues/20575\n    this.connect = (domElement: HTMLElement): void => {\n      scope.domElement = domElement\n      // disables touch scroll\n      // touch-action needs to be defined for pointer events to work on mobile\n      // https://stackoverflow.com/a/48254578\n      scope.domElement.style.touchAction = 'none'\n      scope.domElement.addEventListener('contextmenu', onContextMenu)\n      scope.domElement.addEventListener('pointerdown', onPointerDown)\n      scope.domElement.addEventListener('pointercancel', onPointerUp)\n      scope.domElement.addEventListener('wheel', onMouseWheel)\n    }\n\n    this.dispose = (): void => {\n      // Enabling touch scroll\n      if (scope.domElement) {\n        scope.domElement.style.touchAction = 'auto'\n      }\n      scope.domElement?.removeEventListener('contextmenu', onContextMenu)\n      scope.domElement?.removeEventListener('pointerdown', onPointerDown)\n      scope.domElement?.removeEventListener('pointercancel', onPointerUp)\n      scope.domElement?.removeEventListener('wheel', onMouseWheel)\n      scope.domElement?.ownerDocument.removeEventListener('pointermove', onPointerMove)\n      scope.domElement?.ownerDocument.removeEventListener('pointerup', onPointerUp)\n      if (scope._domElementKeyEvents !== null) {\n        scope._domElementKeyEvents.removeEventListener('keydown', onKeyDown)\n      }\n      //scope.dispatchEvent( { type: 'dispose' } ); // should this be added here?\n    }\n\n    //\n    // internals\n    //\n\n    const scope = this\n\n    const changeEvent = { type: 'change' }\n    const startEvent = { type: 'start' }\n    const endEvent = { type: 'end' }\n\n    const STATE = {\n      NONE: -1,\n      ROTATE: 0,\n      DOLLY: 1,\n      PAN: 2,\n      TOUCH_ROTATE: 3,\n      TOUCH_PAN: 4,\n      TOUCH_DOLLY_PAN: 5,\n      TOUCH_DOLLY_ROTATE: 6,\n    }\n\n    let state = STATE.NONE\n\n    const EPS = 0.000001\n\n    // current position in spherical coordinates\n    const spherical = new Spherical()\n    const sphericalDelta = new Spherical()\n\n    let scale = 1\n    const panOffset = new Vector3()\n\n    const rotateStart = new Vector2()\n    const rotateEnd = new Vector2()\n    const rotateDelta = new Vector2()\n\n    const panStart = new Vector2()\n    const panEnd = new Vector2()\n    const panDelta = new Vector2()\n\n    const dollyStart = new Vector2()\n    const dollyEnd = new Vector2()\n    const dollyDelta = new Vector2()\n\n    const dollyDirection = new Vector3()\n    const mouse = new Vector2()\n    let performCursorZoom = false\n\n    const pointers: PointerEvent[] = []\n    const pointerPositions: { [key: string]: Vector2 } = {}\n\n    function getAutoRotationAngle(): number {\n      return ((2 * Math.PI) / 60 / 60) * scope.autoRotateSpeed\n    }\n\n    function getZoomScale(): number {\n      return Math.pow(0.95, scope.zoomSpeed)\n    }\n\n    function rotateLeft(angle: number): void {\n      if (scope.reverseOrbit || scope.reverseHorizontalOrbit) {\n        sphericalDelta.theta += angle\n      } else {\n        sphericalDelta.theta -= angle\n      }\n    }\n\n    function rotateUp(angle: number): void {\n      if (scope.reverseOrbit || scope.reverseVerticalOrbit) {\n        sphericalDelta.phi += angle\n      } else {\n        sphericalDelta.phi -= angle\n      }\n    }\n\n    const panLeft = (() => {\n      const v = new Vector3()\n\n      return function panLeft(distance: number, objectMatrix: Matrix4) {\n        v.setFromMatrixColumn(objectMatrix, 0) // get X column of objectMatrix\n        v.multiplyScalar(-distance)\n\n        panOffset.add(v)\n      }\n    })()\n\n    const panUp = (() => {\n      const v = new Vector3()\n\n      return function panUp(distance: number, objectMatrix: Matrix4) {\n        if (scope.screenSpacePanning === true) {\n          v.setFromMatrixColumn(objectMatrix, 1)\n        } else {\n          v.setFromMatrixColumn(objectMatrix, 0)\n          v.crossVectors(scope.object.up, v)\n        }\n\n        v.multiplyScalar(distance)\n\n        panOffset.add(v)\n      }\n    })()\n\n    // deltaX and deltaY are in pixels; right and down are positive\n    const pan = (() => {\n      const offset = new Vector3()\n\n      return function pan(deltaX: number, deltaY: number) {\n        const element = scope.domElement\n\n        if (element && scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) {\n          // perspective\n          const position = scope.object.position\n          offset.copy(position).sub(scope.target)\n          let targetDistance = offset.length()\n\n          // half of the fov is center to top of screen\n          targetDistance *= Math.tan(((scope.object.fov / 2) * Math.PI) / 180.0)\n\n          // we use only clientHeight here so aspect ratio does not distort speed\n          panLeft((2 * deltaX * targetDistance) / element.clientHeight, scope.object.matrix)\n          panUp((2 * deltaY * targetDistance) / element.clientHeight, scope.object.matrix)\n        } else if (element && scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera) {\n          // orthographic\n          panLeft(\n            (deltaX * (scope.object.right - scope.object.left)) / scope.object.zoom / element.clientWidth,\n            scope.object.matrix,\n          )\n          panUp(\n            (deltaY * (scope.object.top - scope.object.bottom)) / scope.object.zoom / element.clientHeight,\n            scope.object.matrix,\n          )\n        } else {\n          // camera neither orthographic nor perspective\n          console.warn('WARNING: OrbitControls.js encountered an unknown camera type - pan disabled.')\n          scope.enablePan = false\n        }\n      }\n    })()\n\n    function setScale(newScale: number) {\n      if (\n        (scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) ||\n        (scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera)\n      ) {\n        scale = newScale\n      } else {\n        console.warn('WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.')\n        scope.enableZoom = false\n      }\n    }\n\n    function dollyOut(dollyScale: number) {\n      setScale(scale / dollyScale)\n    }\n\n    function dollyIn(dollyScale: number) {\n      setScale(scale * dollyScale)\n    }\n\n    function updateMouseParameters(event: MouseEvent): void {\n      if (!scope.zoomToCursor || !scope.domElement) {\n        return\n      }\n\n      performCursorZoom = true\n\n      const rect = scope.domElement.getBoundingClientRect()\n      const x = event.clientX - rect.left\n      const y = event.clientY - rect.top\n      const w = rect.width\n      const h = rect.height\n\n      mouse.x = (x / w) * 2 - 1\n      mouse.y = -(y / h) * 2 + 1\n\n      dollyDirection.set(mouse.x, mouse.y, 1).unproject(scope.object).sub(scope.object.position).normalize()\n    }\n\n    function clampDistance(dist: number): number {\n      return Math.max(scope.minDistance, Math.min(scope.maxDistance, dist))\n    }\n\n    //\n    // event callbacks - update the object state\n    //\n\n    function handleMouseDownRotate(event: MouseEvent) {\n      rotateStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseDownDolly(event: MouseEvent) {\n      updateMouseParameters(event)\n      dollyStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseDownPan(event: MouseEvent) {\n      panStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseMoveRotate(event: MouseEvent) {\n      rotateEnd.set(event.clientX, event.clientY)\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed)\n\n      const element = scope.domElement\n\n      if (element) {\n        rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight) // yes, height\n        rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight)\n      }\n      rotateStart.copy(rotateEnd)\n      scope.update()\n    }\n\n    function handleMouseMoveDolly(event: MouseEvent) {\n      dollyEnd.set(event.clientX, event.clientY)\n      dollyDelta.subVectors(dollyEnd, dollyStart)\n\n      if (dollyDelta.y > 0) {\n        dollyOut(getZoomScale())\n      } else if (dollyDelta.y < 0) {\n        dollyIn(getZoomScale())\n      }\n\n      dollyStart.copy(dollyEnd)\n      scope.update()\n    }\n\n    function handleMouseMovePan(event: MouseEvent) {\n      panEnd.set(event.clientX, event.clientY)\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed)\n      pan(panDelta.x, panDelta.y)\n      panStart.copy(panEnd)\n      scope.update()\n    }\n\n    function handleMouseWheel(event: WheelEvent) {\n      updateMouseParameters(event)\n\n      if (event.deltaY < 0) {\n        dollyIn(getZoomScale())\n      } else if (event.deltaY > 0) {\n        dollyOut(getZoomScale())\n      }\n\n      scope.update()\n    }\n\n    function handleKeyDown(event: KeyboardEvent) {\n      let needsUpdate = false\n\n      switch (event.code) {\n        case scope.keys.UP:\n          pan(0, scope.keyPanSpeed)\n          needsUpdate = true\n          break\n\n        case scope.keys.BOTTOM:\n          pan(0, -scope.keyPanSpeed)\n          needsUpdate = true\n          break\n\n        case scope.keys.LEFT:\n          pan(scope.keyPanSpeed, 0)\n          needsUpdate = true\n          break\n\n        case scope.keys.RIGHT:\n          pan(-scope.keyPanSpeed, 0)\n          needsUpdate = true\n          break\n      }\n\n      if (needsUpdate) {\n        // prevent the browser from scrolling on cursor keys\n        event.preventDefault()\n        scope.update()\n      }\n    }\n\n    function handleTouchStartRotate() {\n      if (pointers.length == 1) {\n        rotateStart.set(pointers[0].pageX, pointers[0].pageY)\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX)\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY)\n\n        rotateStart.set(x, y)\n      }\n    }\n\n    function handleTouchStartPan() {\n      if (pointers.length == 1) {\n        panStart.set(pointers[0].pageX, pointers[0].pageY)\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX)\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY)\n\n        panStart.set(x, y)\n      }\n    }\n\n    function handleTouchStartDolly() {\n      const dx = pointers[0].pageX - pointers[1].pageX\n      const dy = pointers[0].pageY - pointers[1].pageY\n      const distance = Math.sqrt(dx * dx + dy * dy)\n\n      dollyStart.set(0, distance)\n    }\n\n    function handleTouchStartDollyPan() {\n      if (scope.enableZoom) handleTouchStartDolly()\n      if (scope.enablePan) handleTouchStartPan()\n    }\n\n    function handleTouchStartDollyRotate() {\n      if (scope.enableZoom) handleTouchStartDolly()\n      if (scope.enableRotate) handleTouchStartRotate()\n    }\n\n    function handleTouchMoveRotate(event: PointerEvent) {\n      if (pointers.length == 1) {\n        rotateEnd.set(event.pageX, event.pageY)\n      } else {\n        const position = getSecondPointerPosition(event)\n        const x = 0.5 * (event.pageX + position.x)\n        const y = 0.5 * (event.pageY + position.y)\n        rotateEnd.set(x, y)\n      }\n\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed)\n\n      const element = scope.domElement\n\n      if (element) {\n        rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight) // yes, height\n        rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight)\n      }\n      rotateStart.copy(rotateEnd)\n    }\n\n    function handleTouchMovePan(event: PointerEvent) {\n      if (pointers.length == 1) {\n        panEnd.set(event.pageX, event.pageY)\n      } else {\n        const position = getSecondPointerPosition(event)\n        const x = 0.5 * (event.pageX + position.x)\n        const y = 0.5 * (event.pageY + position.y)\n        panEnd.set(x, y)\n      }\n\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed)\n      pan(panDelta.x, panDelta.y)\n      panStart.copy(panEnd)\n    }\n\n    function handleTouchMoveDolly(event: PointerEvent) {\n      const position = getSecondPointerPosition(event)\n      const dx = event.pageX - position.x\n      const dy = event.pageY - position.y\n      const distance = Math.sqrt(dx * dx + dy * dy)\n\n      dollyEnd.set(0, distance)\n      dollyDelta.set(0, Math.pow(dollyEnd.y / dollyStart.y, scope.zoomSpeed))\n      dollyOut(dollyDelta.y)\n      dollyStart.copy(dollyEnd)\n    }\n\n    function handleTouchMoveDollyPan(event: PointerEvent) {\n      if (scope.enableZoom) handleTouchMoveDolly(event)\n      if (scope.enablePan) handleTouchMovePan(event)\n    }\n\n    function handleTouchMoveDollyRotate(event: PointerEvent) {\n      if (scope.enableZoom) handleTouchMoveDolly(event)\n      if (scope.enableRotate) handleTouchMoveRotate(event)\n    }\n\n    //\n    // event handlers - FSM: listen for events and reset state\n    //\n\n    function onPointerDown(event: PointerEvent) {\n      if (scope.enabled === false) return\n\n      if (pointers.length === 0) {\n        scope.domElement?.ownerDocument.addEventListener('pointermove', onPointerMove)\n        scope.domElement?.ownerDocument.addEventListener('pointerup', onPointerUp)\n      }\n\n      addPointer(event)\n\n      if (event.pointerType === 'touch') {\n        onTouchStart(event)\n      } else {\n        onMouseDown(event)\n      }\n    }\n\n    function onPointerMove(event: PointerEvent) {\n      if (scope.enabled === false) return\n\n      if (event.pointerType === 'touch') {\n        onTouchMove(event)\n      } else {\n        onMouseMove(event)\n      }\n    }\n\n    function onPointerUp(event: PointerEvent) {\n      removePointer(event)\n\n      if (pointers.length === 0) {\n        scope.domElement?.releasePointerCapture(event.pointerId)\n\n        scope.domElement?.ownerDocument.removeEventListener('pointermove', onPointerMove)\n        scope.domElement?.ownerDocument.removeEventListener('pointerup', onPointerUp)\n      }\n\n      // @ts-ignore\n      scope.dispatchEvent(endEvent)\n\n      state = STATE.NONE\n    }\n\n    function onMouseDown(event: MouseEvent) {\n      let mouseAction\n\n      switch (event.button) {\n        case 0:\n          mouseAction = scope.mouseButtons.LEFT\n          break\n\n        case 1:\n          mouseAction = scope.mouseButtons.MIDDLE\n          break\n\n        case 2:\n          mouseAction = scope.mouseButtons.RIGHT\n          break\n\n        default:\n          mouseAction = -1\n      }\n\n      switch (mouseAction) {\n        case MOUSE.DOLLY:\n          if (scope.enableZoom === false) return\n          handleMouseDownDolly(event)\n          state = STATE.DOLLY\n          break\n\n        case MOUSE.ROTATE:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enablePan === false) return\n            handleMouseDownPan(event)\n            state = STATE.PAN\n          } else {\n            if (scope.enableRotate === false) return\n            handleMouseDownRotate(event)\n            state = STATE.ROTATE\n          }\n          break\n\n        case MOUSE.PAN:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enableRotate === false) return\n            handleMouseDownRotate(event)\n            state = STATE.ROTATE\n          } else {\n            if (scope.enablePan === false) return\n            handleMouseDownPan(event)\n            state = STATE.PAN\n          }\n          break\n\n        default:\n          state = STATE.NONE\n      }\n\n      if (state !== STATE.NONE) {\n        // @ts-ignore\n        scope.dispatchEvent(startEvent)\n      }\n    }\n\n    function onMouseMove(event: MouseEvent) {\n      if (scope.enabled === false) return\n\n      switch (state) {\n        case STATE.ROTATE:\n          if (scope.enableRotate === false) return\n          handleMouseMoveRotate(event)\n          break\n\n        case STATE.DOLLY:\n          if (scope.enableZoom === false) return\n          handleMouseMoveDolly(event)\n          break\n\n        case STATE.PAN:\n          if (scope.enablePan === false) return\n          handleMouseMovePan(event)\n          break\n      }\n    }\n\n    function onMouseWheel(event: WheelEvent) {\n      if (scope.enabled === false || scope.enableZoom === false || (state !== STATE.NONE && state !== STATE.ROTATE)) {\n        return\n      }\n\n      event.preventDefault()\n\n      // @ts-ignore\n      scope.dispatchEvent(startEvent)\n\n      handleMouseWheel(event)\n\n      // @ts-ignore\n      scope.dispatchEvent(endEvent)\n    }\n\n    function onKeyDown(event: KeyboardEvent) {\n      if (scope.enabled === false || scope.enablePan === false) return\n      handleKeyDown(event)\n    }\n\n    function onTouchStart(event: PointerEvent) {\n      trackPointer(event)\n\n      switch (pointers.length) {\n        case 1:\n          switch (scope.touches.ONE) {\n            case TOUCH.ROTATE:\n              if (scope.enableRotate === false) return\n              handleTouchStartRotate()\n              state = STATE.TOUCH_ROTATE\n              break\n\n            case TOUCH.PAN:\n              if (scope.enablePan === false) return\n              handleTouchStartPan()\n              state = STATE.TOUCH_PAN\n              break\n\n            default:\n              state = STATE.NONE\n          }\n\n          break\n\n        case 2:\n          switch (scope.touches.TWO) {\n            case TOUCH.DOLLY_PAN:\n              if (scope.enableZoom === false && scope.enablePan === false) return\n              handleTouchStartDollyPan()\n              state = STATE.TOUCH_DOLLY_PAN\n              break\n\n            case TOUCH.DOLLY_ROTATE:\n              if (scope.enableZoom === false && scope.enableRotate === false) return\n              handleTouchStartDollyRotate()\n              state = STATE.TOUCH_DOLLY_ROTATE\n              break\n\n            default:\n              state = STATE.NONE\n          }\n\n          break\n\n        default:\n          state = STATE.NONE\n      }\n\n      if (state !== STATE.NONE) {\n        // @ts-ignore\n        scope.dispatchEvent(startEvent)\n      }\n    }\n\n    function onTouchMove(event: PointerEvent) {\n      trackPointer(event)\n\n      switch (state) {\n        case STATE.TOUCH_ROTATE:\n          if (scope.enableRotate === false) return\n          handleTouchMoveRotate(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_PAN:\n          if (scope.enablePan === false) return\n          handleTouchMovePan(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_DOLLY_PAN:\n          if (scope.enableZoom === false && scope.enablePan === false) return\n          handleTouchMoveDollyPan(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_DOLLY_ROTATE:\n          if (scope.enableZoom === false && scope.enableRotate === false) return\n          handleTouchMoveDollyRotate(event)\n          scope.update()\n          break\n\n        default:\n          state = STATE.NONE\n      }\n    }\n\n    function onContextMenu(event: Event) {\n      if (scope.enabled === false) return\n      event.preventDefault()\n    }\n\n    function addPointer(event: PointerEvent) {\n      pointers.push(event)\n    }\n\n    function removePointer(event: PointerEvent) {\n      delete pointerPositions[event.pointerId]\n\n      for (let i = 0; i < pointers.length; i++) {\n        if (pointers[i].pointerId == event.pointerId) {\n          pointers.splice(i, 1)\n          return\n        }\n      }\n    }\n\n    function trackPointer(event: PointerEvent) {\n      let position = pointerPositions[event.pointerId]\n\n      if (position === undefined) {\n        position = new Vector2()\n        pointerPositions[event.pointerId] = position\n      }\n\n      position.set(event.pageX, event.pageY)\n    }\n\n    function getSecondPointerPosition(event: PointerEvent) {\n      const pointer = event.pointerId === pointers[0].pointerId ? pointers[1] : pointers[0]\n      return pointerPositions[pointer.pointerId]\n    }\n\n    // Add dolly in/out methods for public API\n\n    this.dollyIn = (dollyScale = getZoomScale()) => {\n      dollyIn(dollyScale)\n      scope.update()\n    }\n\n    this.dollyOut = (dollyScale = getZoomScale()) => {\n      dollyOut(dollyScale)\n      scope.update()\n    }\n\n    this.getScale = () => {\n      return scale\n    }\n\n    this.setScale = (newScale) => {\n      setScale(newScale)\n      scope.update()\n    }\n\n    this.getZoomScale = () => {\n      return getZoomScale()\n    }\n\n    // connect events\n    if (domElement !== undefined) this.connect(domElement)\n    // force an update at start\n    this.update()\n  }\n}\n\n// This set of controls performs orbiting, dollying (zooming), and panning.\n// Unlike TrackballControls, it maintains the \"up\" direction object.up (+Y by default).\n// This is very similar to OrbitControls, another set of touch behavior\n//\n//    Orbit - right mouse, or left mouse + ctrl/meta/shiftKey / touch: two-finger rotate\n//    Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n//    Pan - left mouse, or arrow keys / touch: one-finger move\n\nclass MapControls extends OrbitControls {\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement) {\n    super(object, domElement)\n\n    this.screenSpacePanning = false // pan orthogonal to world-space direction camera.up\n\n    this.mouseButtons.LEFT = MOUSE.PAN\n    this.mouseButtons.RIGHT = MOUSE.ROTATE\n\n    this.touches.ONE = TOUCH.PAN\n    this.touches.TWO = TOUCH.DOLLY_ROTATE\n  }\n}\n\nexport { OrbitControls, MapControls }\n"], "names": ["dom<PERSON>lement", "panLeft", "panUp", "pan"], "mappings": ";;;;;;;;;;;;;;;;;;;AAgBA,MAAM,OAAA,aAAA,GAAA,2MAA2B,MAAA;AACjC,MAAM,SAAA,aAAA,GAAA,2MAA6B,QAAA;AACnC,MAAM,aAAa,KAAK,GAAA,CAAI,KAAA,CAAM,KAAK,EAAA,GAAK,GAAA,CAAI;AAShD,MAAM,mBAAmB,CAAC,QAAgB,WAAA,CAAuB,SAAS,WAAY,QAAA,IAAY;AAElG,MAAM,4QAAsB,kBAAA,CAA0C;IA6FpE,YAAY,MAAA,EAAgD,UAAA,CAA0B;QAC9E,KAAA;QA7FR,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,uCAAA;QAAA,cAAA,IAAA,EAAA,WAAU;QAEV,sEAAA;QAAA,cAAA,IAAA,EAAA,UAAS,2MAAI,UAAA;QAEb,8DAAA;QAAA,cAAA,IAAA,EAAA,eAAc;QACd,cAAA,IAAA,EAAA,eAAc;QAEd,8DAAA;QAAA,cAAA,IAAA,EAAA,WAAU;QACV,cAAA,IAAA,EAAA,WAAU;QAGV,4DAAA;QAAA,iCAAA;QAAA,cAAA,IAAA,EAAA,iBAAgB;QAChB,UAAA;QAAA,cAAA,IAAA,EAAA,iBAAgB,KAAK,EAAA;QAGrB,UAAA;QAAA,8DAAA;QAAA,0GAAA;QAAA,cAAA,IAAA,EAAA,mBAAkB,CAAA;QAClB,UAAA;QAAA,cAAA,IAAA,EAAA,mBAAkB;QAGlB,UAAA;QAAA,0CAAA;QAAA,gFAAA;QAAA,cAAA,IAAA,EAAA,iBAAgB;QAChB,cAAA,IAAA,EAAA,iBAAgB;QAGhB,gGAAA;QAAA,kCAAA;QAAA,cAAA,IAAA,EAAA,cAAa;QACb,cAAA,IAAA,EAAA,aAAY;QAEZ,mCAAA;QAAA,cAAA,IAAA,EAAA,gBAAe;QACf,cAAA,IAAA,EAAA,eAAc;QAEd,kCAAA;QAAA,cAAA,IAAA,EAAA,aAAY;QACZ,cAAA,IAAA,EAAA,YAAW;QACX,cAAA,IAAA,EAAA,sBAAqB;QACrB,8DAAA;QAAA,cAAA,IAAA,EAAA,eAAc;QACd,kCAAA;QAAA,cAAA,IAAA,EAAA,gBAAe;QAGf,wDAAA;QAAA,oFAAA;QAAA,cAAA,IAAA,EAAA,cAAa;QACb,cAAA,IAAA,EAAA,mBAAkB;QAClB,sCAAA;QAAA,cAAA,IAAA,EAAA,gBAAe;QACf,uFAAA;QAAA,cAAA,IAAA,EAAA,0BAAyB;QACzB,6DAAA;QAAA,cAAA,IAAA,EAAA,wBAAuB;QAEvB,2DAAA;QAAA,sBAAA;QAAA,cAAA,IAAA,EAAA,QAAO;YAAE,MAAM;YAAa,IAAI;YAAW,OAAO;YAAc,QAAQ;QAAA;QAExE,gBAAA;QAAA,cAAA,IAAA,EAAA,gBAIK;YACH,MAAM,+MAAA,CAAM,MAAA;YACZ,+MAAQ,QAAA,CAAM,KAAA;YACd,8MAAO,QAAA,CAAM,GAAA;QAAA;QAGf,gBAAA;QAAA,cAAA,IAAA,EAAA,WAGK;YAAE,4MAAK,QAAA,CAAM,MAAA;YAAQ,4MAAK,QAAA,CAAM,SAAA;QAAA;QACrC,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,wCAAA;QAAA,cAAA,IAAA,EAAA,wBAA4B;QAE5B,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,gFAAA;QAAA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAGA,4BAAA;QAAA,cAAA,IAAA,EAAA;QAEA,6BAAA;QAAA,cAAA,IAAA,EAAA;QAEA,wBAAA;QAAA,cAAA,IAAA,EAAA;QAEA,kHAAA;QAAA,cAAA,IAAA,EAAA;QAKE,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,UAAA,GAAa;QAGb,IAAA,CAAA,OAAA,GAAU,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM;QACjC,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,KAAA,CAAM;QACvC,IAAA,CAAA,KAAA,GAAQ,IAAA,CAAK,MAAA,CAAO,IAAA;QAMpB,IAAA,CAAA,aAAA,GAAgB,IAAc,UAAU,GAAA;QAExC,IAAA,CAAA,iBAAA,GAAoB,IAAc,UAAU,KAAA;QAE5C,IAAA,CAAA,aAAA,GAAgB,CAAC,UAAwB;YAE5C,IAAI,MAAM,iBAAiB,OAAO,IAAI,KAAK,EAAE;YAC7C,IAAI,aAAa,UAAU,GAAA;YAG3B,IAAI,aAAa,GAAG,cAAc,IAAI,KAAK,EAAA;YAC3C,IAAI,MAAM,GAAG,OAAO,IAAI,KAAK,EAAA;YAC7B,IAAI,UAAU,KAAK,GAAA,CAAI,MAAM,UAAU;YACvC,IAAI,IAAI,KAAK,EAAA,GAAK,UAAU,SAAS;gBACnC,IAAI,MAAM,YAAY;oBACpB,OAAO,IAAI,KAAK,EAAA;gBAAA,OACX;oBACL,cAAc,IAAI,KAAK,EAAA;gBACzB;YACF;YACA,eAAe,GAAA,GAAM,MAAM;YAC3B,MAAM,MAAA,CAAO;QAAA;QAGV,IAAA,CAAA,iBAAA,GAAoB,CAAC,UAAwB;YAEhD,IAAI,QAAQ,iBAAiB,OAAO,IAAI,KAAK,EAAE;YAC/C,IAAI,eAAe,UAAU,KAAA;YAG7B,IAAI,eAAe,GAAG,gBAAgB,IAAI,KAAK,EAAA;YAC/C,IAAI,QAAQ,GAAG,SAAS,IAAI,KAAK,EAAA;YACjC,IAAI,YAAY,KAAK,GAAA,CAAI,QAAQ,YAAY;YAC7C,IAAI,IAAI,KAAK,EAAA,GAAK,YAAY,WAAW;gBACvC,IAAI,QAAQ,cAAc;oBACxB,SAAS,IAAI,KAAK,EAAA;gBAAA,OACb;oBACL,gBAAgB,IAAI,KAAK,EAAA;gBAC3B;YACF;YACA,eAAe,KAAA,GAAQ,QAAQ;YAC/B,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,WAAA,GAAc,IAAc,MAAM,MAAA,CAAO,QAAA,CAAS,UAAA,CAAW,MAAM,MAAM;QAEzE,IAAA,CAAA,iBAAA,GAAoB,CAACA,gBAAkC;YAC1DA,YAAW,gBAAA,CAAiB,WAAW,SAAS;YAChD,IAAA,CAAK,oBAAA,GAAuBA;QAAA;QAG9B,IAAA,CAAK,qBAAA,GAAwB,MAAY;YAClC,IAAA,CAAA,oBAAA,CAAqB,mBAAA,CAAoB,WAAW,SAAS;YAClE,IAAA,CAAK,oBAAA,GAAuB;QAAA;QAG9B,IAAA,CAAK,SAAA,GAAY,MAAY;YACrB,MAAA,OAAA,CAAQ,IAAA,CAAK,MAAM,MAAM;YAC/B,MAAM,SAAA,CAAU,IAAA,CAAK,MAAM,MAAA,CAAO,QAAQ;YACpC,MAAA,KAAA,GAAQ,MAAM,MAAA,CAAO,IAAA;QAAA;QAG7B,IAAA,CAAK,KAAA,GAAQ,MAAY;YACjB,MAAA,MAAA,CAAO,IAAA,CAAK,MAAM,OAAO;YAC/B,MAAM,MAAA,CAAO,QAAA,CAAS,IAAA,CAAK,MAAM,SAAS;YACpC,MAAA,MAAA,CAAO,IAAA,GAAO,MAAM,KAAA;YAC1B,MAAM,MAAA,CAAO,sBAAA;YAGb,MAAM,aAAA,CAAc,WAAW;YAE/B,MAAM,MAAA,CAAO;YAEb,QAAQ,MAAM,IAAA;QAAA;QAIhB,IAAA,CAAK,MAAA,GAAA,CAAU,MAAoB;YAC3B,MAAA,SAAS,2MAAI,UAAA;YACnB,MAAM,KAAK,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;YAG9B,MAAM,OAAO,2MAAI,aAAA,GAAa,kBAAA,CAAmB,OAAO,EAAA,EAAI,EAAE;YAC9D,MAAM,cAAc,KAAK,KAAA,CAAM,EAAE,MAAA,CAAO;YAElC,MAAA,eAAe,2MAAI,UAAA;YACnB,MAAA,iBAAiB,2MAAI,aAAA;YAErB,MAAA,QAAQ,IAAI,KAAK,EAAA;YAEvB,OAAO,SAAS,SAAkB;gBAC1B,MAAA,WAAW,MAAM,MAAA,CAAO,QAAA;gBAGzB,KAAA,kBAAA,CAAmB,OAAO,EAAA,EAAI,EAAE;gBACzB,YAAA,IAAA,CAAK,IAAI,EAAE,MAAA,CAAO;gBAE9B,OAAO,IAAA,CAAK,QAAQ,EAAE,GAAA,CAAI,MAAM,MAAM;gBAGtC,OAAO,eAAA,CAAgB,IAAI;gBAG3B,UAAU,cAAA,CAAe,MAAM;gBAE/B,IAAI,MAAM,UAAA,IAAc,UAAU,MAAM,IAAA,EAAM;oBAC5C,WAAW,sBAAsB;gBACnC;gBAEA,IAAI,MAAM,aAAA,EAAe;oBACb,UAAA,KAAA,IAAS,eAAe,KAAA,GAAQ,MAAM,aAAA;oBACtC,UAAA,GAAA,IAAO,eAAe,GAAA,GAAM,MAAM,aAAA;gBAAA,OACvC;oBACL,UAAU,KAAA,IAAS,eAAe,KAAA;oBAClC,UAAU,GAAA,IAAO,eAAe,GAAA;gBAClC;gBAIA,IAAI,MAAM,MAAM,eAAA;gBAChB,IAAI,MAAM,MAAM,eAAA;gBAEhB,IAAI,SAAS,GAAG,KAAK,SAAS,GAAG,GAAG;oBAC9B,IAAA,MAAM,CAAC,KAAK,EAAA,EAAW,OAAA;yBAAA,IAClB,MAAM,KAAK,EAAA,EAAW,OAAA;oBAE3B,IAAA,MAAM,CAAC,KAAK,EAAA,EAAW,OAAA;yBAAA,IAClB,MAAM,KAAK,EAAA,EAAW,OAAA;oBAE/B,IAAI,OAAO,KAAK;wBACJ,UAAA,KAAA,GAAQ,KAAK,GAAA,CAAI,KAAK,KAAK,GAAA,CAAI,KAAK,UAAU,KAAK,CAAC;oBAAA,OACzD;wBACL,UAAU,KAAA,GACR,UAAU,KAAA,GAAA,CAAS,MAAM,GAAA,IAAO,IAAI,KAAK,GAAA,CAAI,KAAK,UAAU,KAAK,IAAI,KAAK,GAAA,CAAI,KAAK,UAAU,KAAK;oBACtG;gBACF;gBAGU,UAAA,GAAA,GAAM,KAAK,GAAA,CAAI,MAAM,aAAA,EAAe,KAAK,GAAA,CAAI,MAAM,aAAA,EAAe,UAAU,GAAG,CAAC;gBAC1F,UAAU,QAAA,CAAS;gBAIf,IAAA,MAAM,aAAA,KAAkB,MAAM;oBAChC,MAAM,MAAA,CAAO,eAAA,CAAgB,WAAW,MAAM,aAAa;gBAAA,OACtD;oBACC,MAAA,MAAA,CAAO,GAAA,CAAI,SAAS;gBAC5B;gBAIA,IAAK,MAAM,YAAA,IAAgB,qBAAuB,MAAM,MAAA,CAA8B,oBAAA,EAAsB;oBAChG,UAAA,MAAA,GAAS,cAAc,UAAU,MAAM;gBAAA,OAC5C;oBACL,UAAU,MAAA,GAAS,cAAc,UAAU,MAAA,GAAS,KAAK;gBAC3D;gBAEA,OAAO,gBAAA,CAAiB,SAAS;gBAGjC,OAAO,eAAA,CAAgB,WAAW;gBAElC,SAAS,IAAA,CAAK,MAAM,MAAM,EAAE,GAAA,CAAI,MAAM;gBAElC,IAAA,CAAC,MAAM,MAAA,CAAO,gBAAA,EAAkB,MAAM,MAAA,CAAO,YAAA;gBAC3C,MAAA,MAAA,CAAO,MAAA,CAAO,MAAM,MAAM;gBAE5B,IAAA,MAAM,aAAA,KAAkB,MAAM;oBACjB,eAAA,KAAA,IAAS,IAAI,MAAM,aAAA;oBACnB,eAAA,GAAA,IAAO,IAAI,MAAM,aAAA;oBAEtB,UAAA,cAAA,CAAe,IAAI,MAAM,aAAa;gBAAA,OAC3C;oBACU,eAAA,GAAA,CAAI,GAAG,GAAG,CAAC;oBAEhB,UAAA,GAAA,CAAI,GAAG,GAAG,CAAC;gBACvB;gBAGA,IAAI,cAAc;gBACd,IAAA,MAAM,YAAA,IAAgB,mBAAmB;oBAC3C,IAAI,YAAY;oBAChB,IAAI,MAAM,MAAA,mNAAkB,oBAAA,IAAqB,MAAM,MAAA,CAAO,mBAAA,EAAqB;wBAG3E,MAAA,aAAa,OAAO,MAAA;wBACd,YAAA,cAAc,aAAa,KAAK;wBAE5C,MAAM,cAAc,aAAa;wBACjC,MAAM,MAAA,CAAO,QAAA,CAAS,eAAA,CAAgB,gBAAgB,WAAW;wBACjE,MAAM,MAAA,CAAO,iBAAA;oBAAkB,OAAA,IACrB,MAAM,MAAA,CAA8B,oBAAA,EAAsB;wBAEpE,MAAM,cAAc,2MAAI,UAAA,CAAQ,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,CAAC;wBACvC,YAAA,SAAA,CAAU,MAAM,MAAM;wBAElC,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,CAAC;wBAC9F,MAAM,MAAA,CAAO,sBAAA;wBACC,cAAA;wBAEd,MAAM,aAAa,2MAAI,UAAA,CAAQ,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,CAAC;wBACvC,WAAA,SAAA,CAAU,MAAM,MAAM;wBAEjC,MAAM,MAAA,CAAO,QAAA,CAAS,GAAA,CAAI,UAAU,EAAE,GAAA,CAAI,WAAW;wBACrD,MAAM,MAAA,CAAO,iBAAA;wBAEb,YAAY,OAAO,MAAA;oBAAO,OACrB;wBACL,QAAQ,IAAA,CAAK,yFAAyF;wBACtG,MAAM,YAAA,GAAe;oBACvB;oBAGA,IAAI,cAAc,MAAM;wBACtB,IAAI,MAAM,kBAAA,EAAoB;4BAE5B,MAAM,MAAA,CACH,GAAA,CAAI,GAAG,GAAG,CAAA,CAAE,EACZ,kBAAA,CAAmB,MAAM,MAAA,CAAO,MAAM,EACtC,cAAA,CAAe,SAAS,EACxB,GAAA,CAAI,MAAM,MAAA,CAAO,QAAQ;wBAAA,OACvB;4BAEL,KAAK,MAAA,CAAO,IAAA,CAAK,MAAM,MAAA,CAAO,QAAQ;4BACjC,KAAA,SAAA,CAAU,GAAA,CAAI,GAAG,GAAG,CAAA,CAAE,EAAE,kBAAA,CAAmB,MAAM,MAAA,CAAO,MAAM;4BAI/D,IAAA,KAAK,GAAA,CAAI,MAAM,MAAA,CAAO,EAAA,CAAG,GAAA,CAAI,KAAK,SAAS,CAAC,IAAI,YAAY;gCACvD,OAAA,MAAA,CAAO,MAAM,MAAM;4BAAA,OACrB;gCACL,OAAO,6BAAA,CAA8B,MAAM,MAAA,CAAO,EAAA,EAAI,MAAM,MAAM;gCAC7D,KAAA,cAAA,CAAe,QAAQ,MAAM,MAAM;4BAC1C;wBACF;oBACF;gBAAA,OAAA,IACS,MAAM,MAAA,mNAAkB,qBAAA,IAAsB,MAAM,MAAA,CAAO,oBAAA,EAAsB;oBAC1F,cAAc,UAAU;oBAExB,IAAI,aAAa;wBACf,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,CAAC;wBAC9F,MAAM,MAAA,CAAO,sBAAA;oBACf;gBACF;gBAEQ,QAAA;gBACY,oBAAA;gBAMpB,IACE,eACA,aAAa,iBAAA,CAAkB,MAAM,MAAA,CAAO,QAAQ,IAAI,OACxD,IAAA,CAAK,IAAI,eAAe,GAAA,CAAI,MAAM,MAAA,CAAO,UAAU,CAAA,IAAK,KACxD;oBAEA,MAAM,aAAA,CAAc,WAAW;oBAElB,aAAA,IAAA,CAAK,MAAM,MAAA,CAAO,QAAQ;oBACxB,eAAA,IAAA,CAAK,MAAM,MAAA,CAAO,UAAU;oBAC7B,cAAA;oBAEP,OAAA;gBACT;gBAEO,OAAA;YAAA;QACT,CAAA;QAIG,IAAA,CAAA,OAAA,GAAU,CAACA,gBAAkC;YAChD,MAAM,UAAA,GAAaA;YAIb,MAAA,UAAA,CAAW,KAAA,CAAM,WAAA,GAAc;YAC/B,MAAA,UAAA,CAAW,gBAAA,CAAiB,eAAe,aAAa;YACxD,MAAA,UAAA,CAAW,gBAAA,CAAiB,eAAe,aAAa;YACxD,MAAA,UAAA,CAAW,gBAAA,CAAiB,iBAAiB,WAAW;YACxD,MAAA,UAAA,CAAW,gBAAA,CAAiB,SAAS,YAAY;QAAA;QAGzD,IAAA,CAAK,OAAA,GAAU,MAAY;;YAEzB,IAAI,MAAM,UAAA,EAAY;gBACd,MAAA,UAAA,CAAW,KAAA,CAAM,WAAA,GAAc;YACvC;YACM,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,eAAe;YAC/C,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,eAAe;YAC/C,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,iBAAiB;YACjD,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,SAAS;YAC/C,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,eAAe;YACnE,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,aAAa;YAC7D,IAAA,MAAM,oBAAA,KAAyB,MAAM;gBACjC,MAAA,oBAAA,CAAqB,mBAAA,CAAoB,WAAW,SAAS;YACrE;QAAA;QAQF,MAAM,QAAQ,IAAA;QAER,MAAA,cAAc;YAAE,MAAM;QAAA;QACtB,MAAA,aAAa;YAAE,MAAM;QAAA;QACrB,MAAA,WAAW;YAAE,MAAM;QAAA;QAEzB,MAAM,QAAQ;YACZ,MAAM,CAAA;YACN,QAAQ;YACR,OAAO;YACP,KAAK;YACL,cAAc;YACd,WAAW;YACX,iBAAiB;YACjB,oBAAoB;QAAA;QAGtB,IAAI,QAAQ,MAAM,IAAA;QAElB,MAAM,MAAM;QAGN,MAAA,YAAY,2MAAI,YAAA;QAChB,MAAA,iBAAiB,2MAAI,YAAA;QAE3B,IAAI,QAAQ;QACN,MAAA,YAAY,2MAAI,UAAA;QAEhB,MAAA,cAAc,2MAAI,UAAA;QAClB,MAAA,YAAY,2MAAI,UAAA;QAChB,MAAA,cAAc,2MAAI,UAAA;QAElB,MAAA,WAAW,2MAAI,UAAA;QACf,MAAA,SAAS,IAAI,iNAAA;QACb,MAAA,WAAW,2MAAI,UAAA;QAEf,MAAA,aAAa,2MAAI,UAAA;QACjB,MAAA,WAAW,2MAAI,UAAA;QACf,MAAA,aAAa,2MAAI,UAAA;QAEjB,MAAA,iBAAiB,2MAAI,UAAA;QACrB,MAAA,QAAQ,2MAAI,UAAA;QAClB,IAAI,oBAAoB;QAExB,MAAM,WAA2B,CAAA,CAAA;QACjC,MAAM,mBAA+C,CAAA;QAErD,SAAS,uBAA+B;YACtC,OAAS,IAAI,KAAK,EAAA,GAAM,KAAK,KAAM,MAAM,eAAA;QAC3C;QAEA,SAAS,eAAuB;YAC9B,OAAO,KAAK,GAAA,CAAI,MAAM,MAAM,SAAS;QACvC;QAEA,SAAS,WAAW,KAAA,EAAqB;YACnC,IAAA,MAAM,YAAA,IAAgB,MAAM,sBAAA,EAAwB;gBACtD,eAAe,KAAA,IAAS;YAAA,OACnB;gBACL,eAAe,KAAA,IAAS;YAC1B;QACF;QAEA,SAAS,SAAS,KAAA,EAAqB;YACjC,IAAA,MAAM,YAAA,IAAgB,MAAM,oBAAA,EAAsB;gBACpD,eAAe,GAAA,IAAO;YAAA,OACjB;gBACL,eAAe,GAAA,IAAO;YACxB;QACF;QAEA,MAAM,UAAA,CAAW,MAAM;YACf,MAAA,IAAI,2MAAI,UAAA;YAEP,OAAA,SAASC,SAAQ,QAAA,EAAkB,YAAA,EAAuB;gBAC7D,EAAA,mBAAA,CAAoB,cAAc,CAAC;gBACnC,EAAA,cAAA,CAAe,CAAC,QAAQ;gBAE1B,UAAU,GAAA,CAAI,CAAC;YAAA;QACjB,CAAA;QAGF,MAAM,QAAA,CAAS,MAAM;YACb,MAAA,IAAI,2MAAI,UAAA;YAEP,OAAA,SAASC,OAAM,QAAA,EAAkB,YAAA,EAAuB;gBACzD,IAAA,MAAM,kBAAA,KAAuB,MAAM;oBACnC,EAAA,mBAAA,CAAoB,cAAc,CAAC;gBAAA,OAChC;oBACH,EAAA,mBAAA,CAAoB,cAAc,CAAC;oBACrC,EAAE,YAAA,CAAa,MAAM,MAAA,CAAO,EAAA,EAAI,CAAC;gBACnC;gBAEA,EAAE,cAAA,CAAe,QAAQ;gBAEzB,UAAU,GAAA,CAAI,CAAC;YAAA;QACjB,CAAA;QAIF,MAAM,MAAA,CAAO,MAAM;YACX,MAAA,SAAS,2MAAI,UAAA;YAEZ,OAAA,SAASC,KAAI,MAAA,EAAgB,MAAA,EAAgB;gBAClD,MAAM,UAAU,MAAM,UAAA;gBAEtB,IAAI,WAAW,MAAM,MAAA,mNAAkB,oBAAA,IAAqB,MAAM,MAAA,CAAO,mBAAA,EAAqB;oBAEtF,MAAA,WAAW,MAAM,MAAA,CAAO,QAAA;oBAC9B,OAAO,IAAA,CAAK,QAAQ,EAAE,GAAA,CAAI,MAAM,MAAM;oBAClC,IAAA,iBAAiB,OAAO,MAAA;oBAGV,kBAAA,KAAK,GAAA,CAAM,MAAM,MAAA,CAAO,GAAA,GAAM,IAAK,KAAK,EAAA,GAAM,GAAK;oBAGrE,QAAS,IAAI,SAAS,iBAAkB,QAAQ,YAAA,EAAc,MAAM,MAAA,CAAO,MAAM;oBACjF,MAAO,IAAI,SAAS,iBAAkB,QAAQ,YAAA,EAAc,MAAM,MAAA,CAAO,MAAM;gBAAA,OAAA,IACtE,WAAW,MAAM,MAAA,mNAAkB,qBAAA,IAAsB,MAAM,MAAA,CAAO,oBAAA,EAAsB;oBAErG,QACG,SAAA,CAAU,MAAM,MAAA,CAAO,KAAA,GAAQ,MAAM,MAAA,CAAO,IAAA,IAAS,MAAM,MAAA,CAAO,IAAA,GAAO,QAAQ,WAAA,EAClF,MAAM,MAAA,CAAO,MAAA;oBAEf,MACG,SAAA,CAAU,MAAM,MAAA,CAAO,GAAA,GAAM,MAAM,MAAA,CAAO,MAAA,IAAW,MAAM,MAAA,CAAO,IAAA,GAAO,QAAQ,YAAA,EAClF,MAAM,MAAA,CAAO,MAAA;gBACf,OACK;oBAEL,QAAQ,IAAA,CAAK,8EAA8E;oBAC3F,MAAM,SAAA,GAAY;gBACpB;YAAA;QACF,CAAA;QAGF,SAAS,SAAS,QAAA,EAAkB;YAE/B,IAAA,MAAM,MAAA,mNAAkB,oBAAA,IAAqB,MAAM,MAAA,CAAO,mBAAA,IAC1D,MAAM,MAAA,mNAAkB,qBAAA,IAAsB,MAAM,MAAA,CAAO,oBAAA,EAC5D;gBACQ,QAAA;YAAA,OACH;gBACL,QAAQ,IAAA,CAAK,qFAAqF;gBAClG,MAAM,UAAA,GAAa;YACrB;QACF;QAEA,SAAS,SAAS,UAAA,EAAoB;YACpC,SAAS,QAAQ,UAAU;QAC7B;QAEA,SAAS,QAAQ,UAAA,EAAoB;YACnC,SAAS,QAAQ,UAAU;QAC7B;QAEA,SAAS,sBAAsB,KAAA,EAAyB;YACtD,IAAI,CAAC,MAAM,YAAA,IAAgB,CAAC,MAAM,UAAA,EAAY;gBAC5C;YACF;YAEoB,oBAAA;YAEd,MAAA,OAAO,MAAM,UAAA,CAAW,qBAAA,CAAsB;YAC9C,MAAA,IAAI,MAAM,OAAA,GAAU,KAAK,IAAA;YACzB,MAAA,IAAI,MAAM,OAAA,GAAU,KAAK,GAAA;YAC/B,MAAM,IAAI,KAAK,KAAA;YACf,MAAM,IAAI,KAAK,MAAA;YAET,MAAA,CAAA,GAAK,IAAI,IAAK,IAAI;YACxB,MAAM,CAAA,GAAI,CAAA,CAAE,IAAI,CAAA,IAAK,IAAI;YAEzB,eAAe,GAAA,CAAI,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,CAAC,EAAE,SAAA,CAAU,MAAM,MAAM,EAAE,GAAA,CAAI,MAAM,MAAA,CAAO,QAAQ,EAAE,SAAA;QAC7F;QAEA,SAAS,cAAc,IAAA,EAAsB;YACpC,OAAA,KAAK,GAAA,CAAI,MAAM,WAAA,EAAa,KAAK,GAAA,CAAI,MAAM,WAAA,EAAa,IAAI,CAAC;QACtE;QAMA,SAAS,sBAAsB,KAAA,EAAmB;YAChD,YAAY,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;QAC9C;QAEA,SAAS,qBAAqB,KAAA,EAAmB;YAC/C,sBAAsB,KAAK;YAC3B,WAAW,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;QAC7C;QAEA,SAAS,mBAAmB,KAAA,EAAmB;YAC7C,SAAS,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;QAC3C;QAEA,SAAS,sBAAsB,KAAA,EAAmB;YAChD,UAAU,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;YAC1C,YAAY,UAAA,CAAW,WAAW,WAAW,EAAE,cAAA,CAAe,MAAM,WAAW;YAE/E,MAAM,UAAU,MAAM,UAAA;YAEtB,IAAI,SAAS;gBACX,WAAY,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;gBAC/D,SAAU,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;YAC/D;YACA,YAAY,IAAA,CAAK,SAAS;YAC1B,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,qBAAqB,KAAA,EAAmB;YAC/C,SAAS,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;YAC9B,WAAA,UAAA,CAAW,UAAU,UAAU;YAEtC,IAAA,WAAW,CAAA,GAAI,GAAG;gBACpB,SAAS,cAAc;YAAA,OAAA,IACd,WAAW,CAAA,GAAI,GAAG;gBAC3B,QAAQ,cAAc;YACxB;YAEA,WAAW,IAAA,CAAK,QAAQ;YACxB,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,mBAAmB,KAAA,EAAmB;YAC7C,OAAO,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;YACvC,SAAS,UAAA,CAAW,QAAQ,QAAQ,EAAE,cAAA,CAAe,MAAM,QAAQ;YAC/D,IAAA,SAAS,CAAA,EAAG,SAAS,CAAC;YAC1B,SAAS,IAAA,CAAK,MAAM;YACpB,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,iBAAiB,KAAA,EAAmB;YAC3C,sBAAsB,KAAK;YAEvB,IAAA,MAAM,MAAA,GAAS,GAAG;gBACpB,QAAQ,cAAc;YAAA,OAAA,IACb,MAAM,MAAA,GAAS,GAAG;gBAC3B,SAAS,cAAc;YACzB;YAEA,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,cAAc,KAAA,EAAsB;YAC3C,IAAI,cAAc;YAElB,OAAQ,MAAM,IAAA,EAAM;gBAClB,KAAK,MAAM,IAAA,CAAK,EAAA;oBACV,IAAA,GAAG,MAAM,WAAW;oBACV,cAAA;oBACd;gBAEF,KAAK,MAAM,IAAA,CAAK,MAAA;oBACV,IAAA,GAAG,CAAC,MAAM,WAAW;oBACX,cAAA;oBACd;gBAEF,KAAK,MAAM,IAAA,CAAK,IAAA;oBACV,IAAA,MAAM,WAAA,EAAa,CAAC;oBACV,cAAA;oBACd;gBAEF,KAAK,MAAM,IAAA,CAAK,KAAA;oBACV,IAAA,CAAC,MAAM,WAAA,EAAa,CAAC;oBACX,cAAA;oBACd;YACJ;YAEA,IAAI,aAAa;gBAEf,MAAM,cAAA,CAAe;gBACrB,MAAM,MAAA,CAAO;YACf;QACF;QAEA,SAAS,yBAAyB;YAC5B,IAAA,SAAS,MAAA,IAAU,GAAG;gBACZ,YAAA,GAAA,CAAI,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,EAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAK;YAAA,OAC/C;gBACC,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAC3C,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAErC,YAAA,GAAA,CAAI,GAAG,CAAC;YACtB;QACF;QAEA,SAAS,sBAAsB;YACzB,IAAA,SAAS,MAAA,IAAU,GAAG;gBACf,SAAA,GAAA,CAAI,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,EAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAK;YAAA,OAC5C;gBACC,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAC3C,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAExC,SAAA,GAAA,CAAI,GAAG,CAAC;YACnB;QACF;QAEA,SAAS,wBAAwB;YAC/B,MAAM,KAAK,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;YAC3C,MAAM,KAAK,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;YAC3C,MAAM,WAAW,KAAK,IAAA,CAAK,KAAK,KAAK,KAAK,EAAE;YAEjC,WAAA,GAAA,CAAI,GAAG,QAAQ;QAC5B;QAEA,SAAS,2BAA2B;YAClC,IAAI,MAAM,UAAA,EAAkC;YAC5C,IAAI,MAAM,SAAA,EAA+B;QAC3C;QAEA,SAAS,8BAA8B;YACrC,IAAI,MAAM,UAAA,EAAkC;YAC5C,IAAI,MAAM,YAAA,EAAqC;QACjD;QAEA,SAAS,sBAAsB,KAAA,EAAqB;YAC9C,IAAA,SAAS,MAAA,IAAU,GAAG;gBACxB,UAAU,GAAA,CAAI,MAAM,KAAA,EAAO,MAAM,KAAK;YAAA,OACjC;gBACC,MAAA,WAAW,yBAAyB,KAAK;gBAC/C,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBACxC,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBAC9B,UAAA,GAAA,CAAI,GAAG,CAAC;YACpB;YAEA,YAAY,UAAA,CAAW,WAAW,WAAW,EAAE,cAAA,CAAe,MAAM,WAAW;YAE/E,MAAM,UAAU,MAAM,UAAA;YAEtB,IAAI,SAAS;gBACX,WAAY,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;gBAC/D,SAAU,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;YAC/D;YACA,YAAY,IAAA,CAAK,SAAS;QAC5B;QAEA,SAAS,mBAAmB,KAAA,EAAqB;YAC3C,IAAA,SAAS,MAAA,IAAU,GAAG;gBACxB,OAAO,GAAA,CAAI,MAAM,KAAA,EAAO,MAAM,KAAK;YAAA,OAC9B;gBACC,MAAA,WAAW,yBAAyB,KAAK;gBAC/C,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBACxC,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBACjC,OAAA,GAAA,CAAI,GAAG,CAAC;YACjB;YAEA,SAAS,UAAA,CAAW,QAAQ,QAAQ,EAAE,cAAA,CAAe,MAAM,QAAQ;YAC/D,IAAA,SAAS,CAAA,EAAG,SAAS,CAAC;YAC1B,SAAS,IAAA,CAAK,MAAM;QACtB;QAEA,SAAS,qBAAqB,KAAA,EAAqB;YAC3C,MAAA,WAAW,yBAAyB,KAAK;YACzC,MAAA,KAAK,MAAM,KAAA,GAAQ,SAAS,CAAA;YAC5B,MAAA,KAAK,MAAM,KAAA,GAAQ,SAAS,CAAA;YAClC,MAAM,WAAW,KAAK,IAAA,CAAK,KAAK,KAAK,KAAK,EAAE;YAEnC,SAAA,GAAA,CAAI,GAAG,QAAQ;YACb,WAAA,GAAA,CAAI,GAAG,KAAK,GAAA,CAAI,SAAS,CAAA,GAAI,WAAW,CAAA,EAAG,MAAM,SAAS,CAAC;YACtE,SAAS,WAAW,CAAC;YACrB,WAAW,IAAA,CAAK,QAAQ;QAC1B;QAEA,SAAS,wBAAwB,KAAA,EAAqB;YACpD,IAAI,MAAM,UAAA,EAAY,qBAAqB,KAAK;YAChD,IAAI,MAAM,SAAA,EAAW,mBAAmB,KAAK;QAC/C;QAEA,SAAS,2BAA2B,KAAA,EAAqB;YACvD,IAAI,MAAM,UAAA,EAAY,qBAAqB,KAAK;YAChD,IAAI,MAAM,YAAA,EAAc,sBAAsB,KAAK;QACrD;QAMA,SAAS,cAAc,KAAA,EAAqB;;YAC1C,IAAI,MAAM,OAAA,KAAY,OAAO;YAEzB,IAAA,SAAS,MAAA,KAAW,GAAG;gBACzB,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,gBAAA,CAAiB,eAAe;gBAChE,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,gBAAA,CAAiB,aAAa;YAChE;YAEA,WAAW,KAAK;YAEZ,IAAA,MAAM,WAAA,KAAgB,SAAS;gBACjC,aAAa,KAAK;YAAA,OACb;gBACL,YAAY,KAAK;YACnB;QACF;QAEA,SAAS,cAAc,KAAA,EAAqB;YAC1C,IAAI,MAAM,OAAA,KAAY,OAAO;YAEzB,IAAA,MAAM,WAAA,KAAgB,SAAS;gBACjC,YAAY,KAAK;YAAA,OACZ;gBACL,YAAY,KAAK;YACnB;QACF;QAEA,SAAS,YAAY,KAAA,EAAqB;;YACxC,cAAc,KAAK;YAEf,IAAA,SAAS,MAAA,KAAW,GAAG;gBACnB,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,qBAAA,CAAsB,MAAM,SAAA;gBAE9C,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,eAAe;gBACnE,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,aAAa;YACnE;YAGA,MAAM,aAAA,CAAc,QAAQ;YAE5B,QAAQ,MAAM,IAAA;QAChB;QAEA,SAAS,YAAY,KAAA,EAAmB;YAClC,IAAA;YAEJ,OAAQ,MAAM,MAAA,EAAQ;gBACpB,KAAK;oBACH,cAAc,MAAM,YAAA,CAAa,IAAA;oBACjC;gBAEF,KAAK;oBACH,cAAc,MAAM,YAAA,CAAa,MAAA;oBACjC;gBAEF,KAAK;oBACH,cAAc,MAAM,YAAA,CAAa,KAAA;oBACjC;gBAEF;oBACgB,cAAA,CAAA;YAClB;YAEA,OAAQ,aAAa;gBACnB,KAAK,+MAAA,CAAM,KAAA;oBACT,IAAI,MAAM,UAAA,KAAe,OAAO;oBAChC,qBAAqB,KAAK;oBAC1B,QAAQ,MAAM,KAAA;oBACd;gBAEF,4MAAK,QAAA,CAAM,MAAA;oBACT,IAAI,MAAM,OAAA,IAAW,MAAM,OAAA,IAAW,MAAM,QAAA,EAAU;wBACpD,IAAI,MAAM,SAAA,KAAc,OAAO;wBAC/B,mBAAmB,KAAK;wBACxB,QAAQ,MAAM,GAAA;oBAAA,OACT;wBACL,IAAI,MAAM,YAAA,KAAiB,OAAO;wBAClC,sBAAsB,KAAK;wBAC3B,QAAQ,MAAM,MAAA;oBAChB;oBACA;gBAEF,4MAAK,QAAA,CAAM,GAAA;oBACT,IAAI,MAAM,OAAA,IAAW,MAAM,OAAA,IAAW,MAAM,QAAA,EAAU;wBACpD,IAAI,MAAM,YAAA,KAAiB,OAAO;wBAClC,sBAAsB,KAAK;wBAC3B,QAAQ,MAAM,MAAA;oBAAA,OACT;wBACL,IAAI,MAAM,SAAA,KAAc,OAAO;wBAC/B,mBAAmB,KAAK;wBACxB,QAAQ,MAAM,GAAA;oBAChB;oBACA;gBAEF;oBACE,QAAQ,MAAM,IAAA;YAClB;YAEI,IAAA,UAAU,MAAM,IAAA,EAAM;gBAExB,MAAM,aAAA,CAAc,UAAU;YAChC;QACF;QAEA,SAAS,YAAY,KAAA,EAAmB;YACtC,IAAI,MAAM,OAAA,KAAY,OAAO;YAE7B,OAAQ,OAAO;gBACb,KAAK,MAAM,MAAA;oBACT,IAAI,MAAM,YAAA,KAAiB,OAAO;oBAClC,sBAAsB,KAAK;oBAC3B;gBAEF,KAAK,MAAM,KAAA;oBACT,IAAI,MAAM,UAAA,KAAe,OAAO;oBAChC,qBAAqB,KAAK;oBAC1B;gBAEF,KAAK,MAAM,GAAA;oBACT,IAAI,MAAM,SAAA,KAAc,OAAO;oBAC/B,mBAAmB,KAAK;oBACxB;YACJ;QACF;QAEA,SAAS,aAAa,KAAA,EAAmB;YACnC,IAAA,MAAM,OAAA,KAAY,SAAS,MAAM,UAAA,KAAe,SAAU,UAAU,MAAM,IAAA,IAAQ,UAAU,MAAM,MAAA,EAAS;gBAC7G;YACF;YAEA,MAAM,cAAA,CAAe;YAGrB,MAAM,aAAA,CAAc,UAAU;YAE9B,iBAAiB,KAAK;YAGtB,MAAM,aAAA,CAAc,QAAQ;QAC9B;QAEA,SAAS,UAAU,KAAA,EAAsB;YACvC,IAAI,MAAM,OAAA,KAAY,SAAS,MAAM,SAAA,KAAc,OAAO;YAC1D,cAAc,KAAK;QACrB;QAEA,SAAS,aAAa,KAAA,EAAqB;YACzC,aAAa,KAAK;YAElB,OAAQ,SAAS,MAAA,EAAQ;gBACvB,KAAK;oBACK,OAAA,MAAM,OAAA,CAAQ,GAAA,EAAK;wBACzB,4MAAK,QAAA,CAAM,MAAA;4BACT,IAAI,MAAM,YAAA,KAAiB,OAAO;4BACX;4BACvB,QAAQ,MAAM,YAAA;4BACd;wBAEF,4MAAK,QAAA,CAAM,GAAA;4BACT,IAAI,MAAM,SAAA,KAAc,OAAO;4BACX;4BACpB,QAAQ,MAAM,SAAA;4BACd;wBAEF;4BACE,QAAQ,MAAM,IAAA;oBAClB;oBAEA;gBAEF,KAAK;oBACK,OAAA,MAAM,OAAA,CAAQ,GAAA,EAAK;wBACzB,4MAAK,QAAA,CAAM,SAAA;4BACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,SAAA,KAAc,OAAO;4BACpC;4BACzB,QAAQ,MAAM,eAAA;4BACd;wBAEF,4MAAK,QAAA,CAAM,YAAA;4BACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,YAAA,KAAiB,OAAO;4BACpC;4BAC5B,QAAQ,MAAM,kBAAA;4BACd;wBAEF;4BACE,QAAQ,MAAM,IAAA;oBAClB;oBAEA;gBAEF;oBACE,QAAQ,MAAM,IAAA;YAClB;YAEI,IAAA,UAAU,MAAM,IAAA,EAAM;gBAExB,MAAM,aAAA,CAAc,UAAU;YAChC;QACF;QAEA,SAAS,YAAY,KAAA,EAAqB;YACxC,aAAa,KAAK;YAElB,OAAQ,OAAO;gBACb,KAAK,MAAM,YAAA;oBACT,IAAI,MAAM,YAAA,KAAiB,OAAO;oBAClC,sBAAsB,KAAK;oBAC3B,MAAM,MAAA,CAAO;oBACb;gBAEF,KAAK,MAAM,SAAA;oBACT,IAAI,MAAM,SAAA,KAAc,OAAO;oBAC/B,mBAAmB,KAAK;oBACxB,MAAM,MAAA,CAAO;oBACb;gBAEF,KAAK,MAAM,eAAA;oBACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,SAAA,KAAc,OAAO;oBAC7D,wBAAwB,KAAK;oBAC7B,MAAM,MAAA,CAAO;oBACb;gBAEF,KAAK,MAAM,kBAAA;oBACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,YAAA,KAAiB,OAAO;oBAChE,2BAA2B,KAAK;oBAChC,MAAM,MAAA,CAAO;oBACb;gBAEF;oBACE,QAAQ,MAAM,IAAA;YAClB;QACF;QAEA,SAAS,cAAc,KAAA,EAAc;YACnC,IAAI,MAAM,OAAA,KAAY,OAAO;YAC7B,MAAM,cAAA,CAAe;QACvB;QAEA,SAAS,WAAW,KAAA,EAAqB;YACvC,SAAS,IAAA,CAAK,KAAK;QACrB;QAEA,SAAS,cAAc,KAAA,EAAqB;YACnC,OAAA,gBAAA,CAAiB,MAAM,SAAS,CAAA;YAEvC,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,MAAA,EAAQ,IAAK;gBACxC,IAAI,QAAA,CAAS,CAAC,CAAA,CAAE,SAAA,IAAa,MAAM,SAAA,EAAW;oBACnC,SAAA,MAAA,CAAO,GAAG,CAAC;oBACpB;gBACF;YACF;QACF;QAEA,SAAS,aAAa,KAAA,EAAqB;YACrC,IAAA,WAAW,gBAAA,CAAiB,MAAM,SAAS,CAAA;YAE/C,IAAI,aAAa,KAAA,GAAW;gBAC1B,WAAW,2MAAI,UAAA;gBACE,gBAAA,CAAA,MAAM,SAAS,CAAA,GAAI;YACtC;YAEA,SAAS,GAAA,CAAI,MAAM,KAAA,EAAO,MAAM,KAAK;QACvC;QAEA,SAAS,yBAAyB,KAAA,EAAqB;YAC/C,MAAA,UAAU,MAAM,SAAA,KAAc,QAAA,CAAS,CAAC,CAAA,CAAE,SAAA,GAAY,QAAA,CAAS,CAAC,CAAA,GAAI,QAAA,CAAS,CAAC,CAAA;YAC7E,OAAA,gBAAA,CAAiB,QAAQ,SAAS,CAAA;QAC3C;QAIA,IAAA,CAAK,OAAA,GAAU,CAAC,aAAa,aAAA,CAAA,KAAmB;YAC9C,QAAQ,UAAU;YAClB,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,QAAA,GAAW,CAAC,aAAa,aAAA,CAAA,KAAmB;YAC/C,SAAS,UAAU;YACnB,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,QAAA,GAAW,MAAM;YACb,OAAA;QAAA;QAGJ,IAAA,CAAA,QAAA,GAAW,CAAC,aAAa;YAC5B,SAAS,QAAQ;YACjB,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,YAAA,GAAe,MAAM;YACxB,OAAO,aAAa;QAAA;QAItB,IAAI,eAAe,KAAA,GAAW,IAAA,CAAK,OAAA,CAAQ,UAAU;QAErD,IAAA,CAAK,MAAA,CAAO;IACd;AACF;AAUA,MAAM,oBAAoB,cAAc;IACtC,YAAY,MAAA,EAAgD,UAAA,CAA0B;QACpF,KAAA,CAAM,QAAQ,UAAU;QAExB,IAAA,CAAK,kBAAA,GAAqB;QAErB,IAAA,CAAA,YAAA,CAAa,IAAA,0MAAO,QAAA,CAAM,GAAA;QAC1B,IAAA,CAAA,YAAA,CAAa,KAAA,0MAAQ,QAAA,CAAM,MAAA;QAE3B,IAAA,CAAA,OAAA,CAAQ,GAAA,0MAAM,QAAA,CAAM,GAAA;QACpB,IAAA,CAAA,OAAA,CAAQ,GAAA,0MAAM,QAAA,CAAM,YAAA;IAC3B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1331, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1337, "column": 0}, "map": {"version": 3, "file": "TrackballControls.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/controls/TrackballControls.ts"], "sourcesContent": ["import { MOUS<PERSON>, Quatern<PERSON>, Vector2, Vector3, PerspectiveCamera, OrthographicCamera } from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\nclass TrackballControls extends EventDispatcher<StandardControlsEventMap> {\n  public enabled = true\n\n  public screen = { left: 0, top: 0, width: 0, height: 0 }\n\n  public rotateSpeed = 1.0\n  public zoomSpeed = 1.2\n  public panSpeed = 0.3\n\n  public noRotate = false\n  public noZoom = false\n  public noPan = false\n\n  public staticMoving = false\n  public dynamicDampingFactor = 0.2\n\n  public minDistance = 0\n  public maxDistance = Infinity\n\n  public keys: [string, string, string] = ['KeyA' /*A*/, 'KeyS' /*S*/, 'KeyD' /*D*/]\n\n  public mouseButtons = {\n    LEFT: MOUSE.ROTATE,\n    MIDDLE: MOUSE.DOLLY,\n    RIGHT: MOUSE.PAN,\n  }\n\n  public object: PerspectiveCamera | OrthographicCamera\n  public domElement: HTMLElement | undefined\n  public cursorZoom: boolean = false\n\n  readonly target = new Vector3()\n  private mousePosition = new Vector2()\n\n  // internals\n  private STATE = {\n    NONE: -1,\n    ROTATE: 0,\n    ZOOM: 1,\n    PAN: 2,\n    TOUCH_ROTATE: 3,\n    TOUCH_ZOOM_PAN: 4,\n  }\n\n  private EPS = 0.000001\n  private lastZoom = 1\n\n  private lastPosition = new Vector3()\n  private cursorVector = new Vector3()\n  private targetVector = new Vector3()\n\n  private _state = this.STATE.NONE\n  private _keyState = this.STATE.NONE\n  private _eye = new Vector3()\n  private _movePrev = new Vector2()\n  private _moveCurr = new Vector2()\n  private _lastAxis = new Vector3()\n  private _lastAngle = 0\n  private _zoomStart = new Vector2()\n  private _zoomEnd = new Vector2()\n  private _touchZoomDistanceStart = 0\n  private _touchZoomDistanceEnd = 0\n  private _panStart = new Vector2()\n  private _panEnd = new Vector2()\n\n  private target0: Vector3\n  private position0: Vector3\n  private up0: Vector3\n  private zoom0: number\n\n  // events\n\n  private changeEvent = { type: 'change' }\n  private startEvent = { type: 'start' }\n  private endEvent = { type: 'end' }\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement) {\n    super()\n    this.object = object\n\n    // for reset\n\n    this.target0 = this.target.clone()\n    this.position0 = this.object.position.clone()\n    this.up0 = this.object.up.clone()\n    this.zoom0 = this.object.zoom\n\n    // connect events\n    if (domElement !== undefined) this.connect(domElement)\n\n    // force an update at start\n    this.update()\n  }\n\n  private onScreenVector = new Vector2()\n\n  private getMouseOnScreen = (pageX: number, pageY: number): Vector2 => {\n    this.onScreenVector.set(\n      (pageX - this.screen.left) / this.screen.width,\n      (pageY - this.screen.top) / this.screen.height,\n    )\n\n    return this.onScreenVector\n  }\n\n  private onCircleVector = new Vector2()\n\n  private getMouseOnCircle = (pageX: number, pageY: number): Vector2 => {\n    this.onCircleVector.set(\n      (pageX - this.screen.width * 0.5 - this.screen.left) / (this.screen.width * 0.5),\n      (this.screen.height + 2 * (this.screen.top - pageY)) / this.screen.width, // screen.width intentional\n    )\n\n    return this.onCircleVector\n  }\n\n  private axis = new Vector3()\n  private quaternion = new Quaternion()\n  private eyeDirection = new Vector3()\n  private objectUpDirection = new Vector3()\n  private objectSidewaysDirection = new Vector3()\n  private moveDirection = new Vector3()\n  private angle: number = 0\n\n  private rotateCamera = (): void => {\n    this.moveDirection.set(this._moveCurr.x - this._movePrev.x, this._moveCurr.y - this._movePrev.y, 0)\n    this.angle = this.moveDirection.length()\n\n    if (this.angle) {\n      this._eye.copy(this.object.position).sub(this.target)\n\n      this.eyeDirection.copy(this._eye).normalize()\n      this.objectUpDirection.copy(this.object.up).normalize()\n      this.objectSidewaysDirection.crossVectors(this.objectUpDirection, this.eyeDirection).normalize()\n\n      this.objectUpDirection.setLength(this._moveCurr.y - this._movePrev.y)\n      this.objectSidewaysDirection.setLength(this._moveCurr.x - this._movePrev.x)\n\n      this.moveDirection.copy(this.objectUpDirection.add(this.objectSidewaysDirection))\n\n      this.axis.crossVectors(this.moveDirection, this._eye).normalize()\n\n      this.angle *= this.rotateSpeed\n      this.quaternion.setFromAxisAngle(this.axis, this.angle)\n\n      this._eye.applyQuaternion(this.quaternion)\n      this.object.up.applyQuaternion(this.quaternion)\n\n      this._lastAxis.copy(this.axis)\n      this._lastAngle = this.angle\n    } else if (!this.staticMoving && this._lastAngle) {\n      this._lastAngle *= Math.sqrt(1.0 - this.dynamicDampingFactor)\n      this._eye.copy(this.object.position).sub(this.target)\n      this.quaternion.setFromAxisAngle(this._lastAxis, this._lastAngle)\n      this._eye.applyQuaternion(this.quaternion)\n      this.object.up.applyQuaternion(this.quaternion)\n    }\n\n    this._movePrev.copy(this._moveCurr)\n  }\n\n  private zoomCamera = (): void => {\n    let factor\n\n    if (this._state === this.STATE.TOUCH_ZOOM_PAN) {\n      factor = this._touchZoomDistanceStart / this._touchZoomDistanceEnd\n      this._touchZoomDistanceStart = this._touchZoomDistanceEnd\n\n      if ((this.object as PerspectiveCamera).isPerspectiveCamera) {\n        this._eye.multiplyScalar(factor)\n      } else if ((this.object as OrthographicCamera).isOrthographicCamera) {\n        this.object.zoom /= factor\n        this.object.updateProjectionMatrix()\n      } else {\n        console.warn('THREE.TrackballControls: Unsupported camera type')\n      }\n    } else {\n      factor = 1.0 + (this._zoomEnd.y - this._zoomStart.y) * this.zoomSpeed\n\n      if (Math.abs(factor - 1.0) > this.EPS && factor > 0.0) {\n        if ((this.object as PerspectiveCamera).isPerspectiveCamera) {\n          if (factor > 1.0 && this._eye.length() >= this.maxDistance - this.EPS) {\n            factor = 1.0\n          }\n          this._eye.multiplyScalar(factor)\n        } else if ((this.object as OrthographicCamera).isOrthographicCamera) {\n          if (factor > 1.0 && this.object.zoom < this.maxDistance * this.maxDistance) {\n            factor = 1.0\n          }\n          this.object.zoom /= factor\n        } else {\n          console.warn('THREE.TrackballControls: Unsupported camera type')\n        }\n      }\n\n      if (this.staticMoving) {\n        this._zoomStart.copy(this._zoomEnd)\n      } else {\n        this._zoomStart.y += (this._zoomEnd.y - this._zoomStart.y) * this.dynamicDampingFactor\n      }\n\n      if (this.cursorZoom) {\n        //determine 3D position of mouse cursor (on target plane)\n        this.targetVector.copy(this.target).project(this.object)\n        let worldPos = this.cursorVector\n          .set(this.mousePosition.x, this.mousePosition.y, this.targetVector.z)\n          .unproject(this.object)\n\n        //adjust target point so that \"point\" stays in place\n        this.target.lerpVectors(worldPos, this.target, factor)\n      }\n\n      // Update the projection matrix after all properties are changed\n      if ((this.object as OrthographicCamera).isOrthographicCamera) {\n        this.object.updateProjectionMatrix()\n      }\n    }\n  }\n\n  private mouseChange = new Vector2()\n  private objectUp = new Vector3()\n  private pan = new Vector3()\n\n  private panCamera = (): void => {\n    if (!this.domElement) return\n    this.mouseChange.copy(this._panEnd).sub(this._panStart)\n\n    if (this.mouseChange.lengthSq() > this.EPS) {\n      if ((this.object as OrthographicCamera).isOrthographicCamera) {\n        const orthoObject = this.object as OrthographicCamera\n        const scale_x = (orthoObject.right - orthoObject.left) / this.object.zoom\n        const scale_y = (orthoObject.top - orthoObject.bottom) / this.object.zoom\n\n        this.mouseChange.x *= scale_x\n        this.mouseChange.y *= scale_y\n      } else {\n        this.mouseChange.multiplyScalar(this._eye.length() * this.panSpeed)\n      }\n\n      this.pan.copy(this._eye).cross(this.object.up).setLength(this.mouseChange.x)\n      this.pan.add(this.objectUp.copy(this.object.up).setLength(this.mouseChange.y))\n\n      this.object.position.add(this.pan)\n      this.target.add(this.pan)\n\n      if (this.staticMoving) {\n        this._panStart.copy(this._panEnd)\n      } else {\n        this._panStart.add(\n          this.mouseChange.subVectors(this._panEnd, this._panStart).multiplyScalar(this.dynamicDampingFactor),\n        )\n      }\n    }\n  }\n\n  private checkDistances = (): void => {\n    if (!this.noZoom || !this.noPan) {\n      if (this._eye.lengthSq() > this.maxDistance * this.maxDistance) {\n        this.object.position.addVectors(this.target, this._eye.setLength(this.maxDistance))\n        this._zoomStart.copy(this._zoomEnd)\n      }\n\n      if (this._eye.lengthSq() < this.minDistance * this.minDistance) {\n        this.object.position.addVectors(this.target, this._eye.setLength(this.minDistance))\n        this._zoomStart.copy(this._zoomEnd)\n      }\n    }\n  }\n\n  public handleResize = (): void => {\n    if (!this.domElement) return\n    const box = this.domElement.getBoundingClientRect()\n    // adjustments come from similar code in the jquery offset() function\n    const d = this.domElement.ownerDocument.documentElement\n    this.screen.left = box.left + window.pageXOffset - d.clientLeft\n    this.screen.top = box.top + window.pageYOffset - d.clientTop\n    this.screen.width = box.width\n    this.screen.height = box.height\n  }\n\n  public update = (): void => {\n    this._eye.subVectors(this.object.position, this.target)\n\n    if (!this.noRotate) {\n      this.rotateCamera()\n    }\n\n    if (!this.noZoom) {\n      this.zoomCamera()\n    }\n\n    if (!this.noPan) {\n      this.panCamera()\n    }\n\n    this.object.position.addVectors(this.target, this._eye)\n\n    if ((this.object as PerspectiveCamera).isPerspectiveCamera) {\n      this.checkDistances()\n\n      this.object.lookAt(this.target)\n\n      if (this.lastPosition.distanceToSquared(this.object.position) > this.EPS) {\n        // @ts-ignore\n        this.dispatchEvent(this.changeEvent)\n\n        this.lastPosition.copy(this.object.position)\n      }\n    } else if ((this.object as OrthographicCamera).isOrthographicCamera) {\n      this.object.lookAt(this.target)\n\n      if (this.lastPosition.distanceToSquared(this.object.position) > this.EPS || this.lastZoom !== this.object.zoom) {\n        // @ts-ignore\n        this.dispatchEvent(this.changeEvent)\n\n        this.lastPosition.copy(this.object.position)\n        this.lastZoom = this.object.zoom\n      }\n    } else {\n      console.warn('THREE.TrackballControls: Unsupported camera type')\n    }\n  }\n\n  public reset = (): void => {\n    this._state = this.STATE.NONE\n    this._keyState = this.STATE.NONE\n\n    this.target.copy(this.target0)\n    this.object.position.copy(this.position0)\n    this.object.up.copy(this.up0)\n    this.object.zoom = this.zoom0\n\n    this.object.updateProjectionMatrix()\n\n    this._eye.subVectors(this.object.position, this.target)\n\n    this.object.lookAt(this.target)\n\n    // @ts-ignore\n    this.dispatchEvent(this.changeEvent)\n\n    this.lastPosition.copy(this.object.position)\n    this.lastZoom = this.object.zoom\n  }\n\n  private keydown = (event: KeyboardEvent): void => {\n    if (this.enabled === false) return\n\n    window.removeEventListener('keydown', this.keydown)\n\n    if (this._keyState !== this.STATE.NONE) {\n      return\n    } else if (event.code === this.keys[this.STATE.ROTATE] && !this.noRotate) {\n      this._keyState = this.STATE.ROTATE\n    } else if (event.code === this.keys[this.STATE.ZOOM] && !this.noZoom) {\n      this._keyState = this.STATE.ZOOM\n    } else if (event.code === this.keys[this.STATE.PAN] && !this.noPan) {\n      this._keyState = this.STATE.PAN\n    }\n  }\n\n  private onPointerDown = (event: PointerEvent): void => {\n    if (this.enabled === false) return\n\n    switch (event.pointerType) {\n      case 'mouse':\n      case 'pen':\n        this.onMouseDown(event)\n        break\n\n      // TODO touch\n    }\n  }\n\n  private onPointerMove = (event: PointerEvent): void => {\n    if (this.enabled === false) return\n\n    switch (event.pointerType) {\n      case 'mouse':\n      case 'pen':\n        this.onMouseMove(event)\n        break\n\n      // TODO touch\n    }\n  }\n\n  private onPointerUp = (event: PointerEvent): void => {\n    if (this.enabled === false) return\n\n    switch (event.pointerType) {\n      case 'mouse':\n      case 'pen':\n        this.onMouseUp()\n        break\n\n      // TODO touch\n    }\n  }\n\n  private keyup = (): void => {\n    if (this.enabled === false) return\n\n    this._keyState = this.STATE.NONE\n\n    window.addEventListener('keydown', this.keydown)\n  }\n\n  private onMouseDown = (event: MouseEvent): void => {\n    if (!this.domElement) return\n    if (this._state === this.STATE.NONE) {\n      switch (event.button) {\n        case this.mouseButtons.LEFT:\n          this._state = this.STATE.ROTATE\n          break\n\n        case this.mouseButtons.MIDDLE:\n          this._state = this.STATE.ZOOM\n          break\n\n        case this.mouseButtons.RIGHT:\n          this._state = this.STATE.PAN\n          break\n      }\n    }\n\n    const state = this._keyState !== this.STATE.NONE ? this._keyState : this._state\n\n    if (state === this.STATE.ROTATE && !this.noRotate) {\n      this._moveCurr.copy(this.getMouseOnCircle(event.pageX, event.pageY))\n      this._movePrev.copy(this._moveCurr)\n    } else if (state === this.STATE.ZOOM && !this.noZoom) {\n      this._zoomStart.copy(this.getMouseOnScreen(event.pageX, event.pageY))\n      this._zoomEnd.copy(this._zoomStart)\n    } else if (state === this.STATE.PAN && !this.noPan) {\n      this._panStart.copy(this.getMouseOnScreen(event.pageX, event.pageY))\n      this._panEnd.copy(this._panStart)\n    }\n\n    this.domElement.ownerDocument.addEventListener('pointermove', this.onPointerMove)\n    this.domElement.ownerDocument.addEventListener('pointerup', this.onPointerUp)\n\n    // @ts-ignore\n    this.dispatchEvent(this.startEvent)\n  }\n\n  private onMouseMove = (event: MouseEvent): void => {\n    if (this.enabled === false) return\n\n    const state = this._keyState !== this.STATE.NONE ? this._keyState : this._state\n\n    if (state === this.STATE.ROTATE && !this.noRotate) {\n      this._movePrev.copy(this._moveCurr)\n      this._moveCurr.copy(this.getMouseOnCircle(event.pageX, event.pageY))\n    } else if (state === this.STATE.ZOOM && !this.noZoom) {\n      this._zoomEnd.copy(this.getMouseOnScreen(event.pageX, event.pageY))\n    } else if (state === this.STATE.PAN && !this.noPan) {\n      this._panEnd.copy(this.getMouseOnScreen(event.pageX, event.pageY))\n    }\n  }\n\n  private onMouseUp = (): void => {\n    if (!this.domElement) return\n    if (this.enabled === false) return\n\n    this._state = this.STATE.NONE\n\n    this.domElement.ownerDocument.removeEventListener('pointermove', this.onPointerMove)\n    this.domElement.ownerDocument.removeEventListener('pointerup', this.onPointerUp)\n\n    // @ts-ignore\n    this.dispatchEvent(this.endEvent)\n  }\n\n  private mousewheel = (event: WheelEvent): void => {\n    if (this.enabled === false) return\n\n    if (this.noZoom === true) return\n\n    event.preventDefault()\n\n    switch (event.deltaMode) {\n      case 2:\n        // Zoom in pages\n        this._zoomStart.y -= event.deltaY * 0.025\n        break\n\n      case 1:\n        // Zoom in lines\n        this._zoomStart.y -= event.deltaY * 0.01\n        break\n\n      default:\n        // undefined, 0, assume pixels\n        this._zoomStart.y -= event.deltaY * 0.00025\n        break\n    }\n\n    this.mousePosition.x = (event.offsetX / this.screen.width) * 2 - 1\n    this.mousePosition.y = -(event.offsetY / this.screen.height) * 2 + 1\n\n    // @ts-ignore\n    this.dispatchEvent(this.startEvent)\n    // @ts-ignore\n    this.dispatchEvent(this.endEvent)\n  }\n\n  private touchstart = (event: TouchEvent): void => {\n    if (this.enabled === false) return\n\n    event.preventDefault()\n\n    switch (event.touches.length) {\n      case 1:\n        this._state = this.STATE.TOUCH_ROTATE\n        this._moveCurr.copy(this.getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY))\n        this._movePrev.copy(this._moveCurr)\n        break\n\n      default:\n        // 2 or more\n        this._state = this.STATE.TOUCH_ZOOM_PAN\n        const dx = event.touches[0].pageX - event.touches[1].pageX\n        const dy = event.touches[0].pageY - event.touches[1].pageY\n        this._touchZoomDistanceEnd = this._touchZoomDistanceStart = Math.sqrt(dx * dx + dy * dy)\n\n        const x = (event.touches[0].pageX + event.touches[1].pageX) / 2\n        const y = (event.touches[0].pageY + event.touches[1].pageY) / 2\n        this._panStart.copy(this.getMouseOnScreen(x, y))\n        this._panEnd.copy(this._panStart)\n        break\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(this.startEvent)\n  }\n\n  private touchmove = (event: TouchEvent): void => {\n    if (this.enabled === false) return\n\n    event.preventDefault()\n\n    switch (event.touches.length) {\n      case 1:\n        this._movePrev.copy(this._moveCurr)\n        this._moveCurr.copy(this.getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY))\n        break\n\n      default:\n        // 2 or more\n        const dx = event.touches[0].pageX - event.touches[1].pageX\n        const dy = event.touches[0].pageY - event.touches[1].pageY\n        this._touchZoomDistanceEnd = Math.sqrt(dx * dx + dy * dy)\n\n        const x = (event.touches[0].pageX + event.touches[1].pageX) / 2\n        const y = (event.touches[0].pageY + event.touches[1].pageY) / 2\n        this._panEnd.copy(this.getMouseOnScreen(x, y))\n        break\n    }\n  }\n\n  private touchend = (event: TouchEvent): void => {\n    if (this.enabled === false) return\n\n    switch (event.touches.length) {\n      case 0:\n        this._state = this.STATE.NONE\n        break\n\n      case 1:\n        this._state = this.STATE.TOUCH_ROTATE\n        this._moveCurr.copy(this.getMouseOnCircle(event.touches[0].pageX, event.touches[0].pageY))\n        this._movePrev.copy(this._moveCurr)\n        break\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(this.endEvent)\n  }\n\n  private contextmenu = (event: MouseEvent): void => {\n    if (this.enabled === false) return\n\n    event.preventDefault()\n  }\n\n  // https://github.com/mrdoob/three.js/issues/20575\n  public connect = (domElement: HTMLElement): void => {\n    if ((domElement as any) === document) {\n      console.error(\n        'THREE.OrbitControls: \"document\" should not be used as the target \"domElement\". Please use \"renderer.domElement\" instead.',\n      )\n    }\n    this.domElement = domElement\n    this.domElement.addEventListener('contextmenu', this.contextmenu)\n\n    this.domElement.addEventListener('pointerdown', this.onPointerDown)\n    this.domElement.addEventListener('wheel', this.mousewheel)\n\n    this.domElement.addEventListener('touchstart', this.touchstart)\n    this.domElement.addEventListener('touchend', this.touchend)\n    this.domElement.addEventListener('touchmove', this.touchmove)\n\n    this.domElement.ownerDocument.addEventListener('pointermove', this.onPointerMove)\n    this.domElement.ownerDocument.addEventListener('pointerup', this.onPointerUp)\n\n    window.addEventListener('keydown', this.keydown)\n    window.addEventListener('keyup', this.keyup)\n\n    this.handleResize()\n  }\n\n  public dispose = (): void => {\n    if (!this.domElement) return\n    this.domElement.removeEventListener('contextmenu', this.contextmenu)\n\n    this.domElement.removeEventListener('pointerdown', this.onPointerDown)\n    this.domElement.removeEventListener('wheel', this.mousewheel)\n\n    this.domElement.removeEventListener('touchstart', this.touchstart)\n    this.domElement.removeEventListener('touchend', this.touchend)\n    this.domElement.removeEventListener('touchmove', this.touchmove)\n\n    this.domElement.ownerDocument.removeEventListener('pointermove', this.onPointerMove)\n    this.domElement.ownerDocument.removeEventListener('pointerup', this.onPointerUp)\n\n    window.removeEventListener('keydown', this.keydown)\n    window.removeEventListener('keyup', this.keyup)\n  }\n}\n\nexport { TrackballControls }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAIA,MAAM,gRAA0B,kBAAA,CAA0C;IA4ExE,YAAY,MAAA,EAAgD,UAAA,CAA0B;QAC9E,KAAA;QA5ED,cAAA,IAAA,EAAA,WAAU;QAEV,cAAA,IAAA,EAAA,UAAS;YAAE,MAAM;YAAG,KAAK;YAAG,OAAO;YAAG,QAAQ;QAAA;QAE9C,cAAA,IAAA,EAAA,eAAc;QACd,cAAA,IAAA,EAAA,aAAY;QACZ,cAAA,IAAA,EAAA,YAAW;QAEX,cAAA,IAAA,EAAA,YAAW;QACX,cAAA,IAAA,EAAA,UAAS;QACT,cAAA,IAAA,EAAA,SAAQ;QAER,cAAA,IAAA,EAAA,gBAAe;QACf,cAAA,IAAA,EAAA,wBAAuB;QAEvB,cAAA,IAAA,EAAA,eAAc;QACd,cAAA,IAAA,EAAA,eAAc;QAEd,cAAA,IAAA,EAAA,QAAiC;YAAC;YAAc;YAAc;SAAA;QAE9D,cAAA,IAAA,EAAA,gBAAe;YACpB,6MAAM,QAAA,CAAM,MAAA;YACZ,+MAAQ,QAAA,CAAM,KAAA;YACd,8MAAO,QAAA,CAAM,GAAA;QAAA;QAGR,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA,cAAsB;QAEpB,cAAA,IAAA,EAAA,UAAS,2MAAI,UAAA;QACd,cAAA,IAAA,EAAA,iBAAgB,2MAAI,UAAA;QAGpB,YAAA;QAAA,cAAA,IAAA,EAAA,SAAQ;YACd,MAAM,CAAA;YACN,QAAQ;YACR,MAAM;YACN,KAAK;YACL,cAAc;YACd,gBAAgB;QAAA;QAGV,cAAA,IAAA,EAAA,OAAM;QACN,cAAA,IAAA,EAAA,YAAW;QAEX,cAAA,IAAA,EAAA,gBAAe,2MAAI,UAAA;QACnB,cAAA,IAAA,EAAA,gBAAe,IAAI,iNAAA;QACnB,cAAA,IAAA,EAAA,gBAAe,2MAAI,UAAA;QAEnB,cAAA,IAAA,EAAA,UAAS,IAAA,CAAK,KAAA,CAAM,IAAA;QACpB,cAAA,IAAA,EAAA,aAAY,IAAA,CAAK,KAAA,CAAM,IAAA;QACvB,cAAA,IAAA,EAAA,QAAO,2MAAI,UAAA;QACX,cAAA,IAAA,EAAA,aAAY,2MAAI,UAAA;QAChB,cAAA,IAAA,EAAA,aAAY,2MAAI,UAAA;QAChB,cAAA,IAAA,EAAA,aAAY,2MAAI,UAAA;QAChB,cAAA,IAAA,EAAA,cAAa;QACb,cAAA,IAAA,EAAA,cAAa,2MAAI,UAAA;QACjB,cAAA,IAAA,EAAA,YAAW,IAAI,iNAAA;QACf,cAAA,IAAA,EAAA,2BAA0B;QAC1B,cAAA,IAAA,EAAA,yBAAwB;QACxB,cAAA,IAAA,EAAA,aAAY,IAAI,iNAAA;QAChB,cAAA,IAAA,EAAA,WAAU,2MAAI,UAAA;QAEd,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAIA,SAAA;QAAA,cAAA,IAAA,EAAA,eAAc;YAAE,MAAM;QAAA;QACtB,cAAA,IAAA,EAAA,cAAa;YAAE,MAAM;QAAA;QACrB,cAAA,IAAA,EAAA,YAAW;YAAE,MAAM;QAAA;QAoBnB,cAAA,IAAA,EAAA,kBAAiB,IAAI,iNAAA;QAErB,cAAA,IAAA,EAAA,oBAAmB,CAAC,OAAe,UAA2B;YACpE,IAAA,CAAK,cAAA,CAAe,GAAA,CAAA,CACjB,QAAQ,IAAA,CAAK,MAAA,CAAO,IAAA,IAAQ,IAAA,CAAK,MAAA,CAAO,KAAA,EAAA,CACxC,QAAQ,IAAA,CAAK,MAAA,CAAO,GAAA,IAAO,IAAA,CAAK,MAAA,CAAO,MAAA;YAG1C,OAAO,IAAA,CAAK,cAAA;QAAA;QAGN,cAAA,IAAA,EAAA,kBAAiB,2MAAI,UAAA;QAErB,cAAA,IAAA,EAAA,oBAAmB,CAAC,OAAe,UAA2B;YACpE,IAAA,CAAK,cAAA,CAAe,GAAA,CAAA,CACjB,QAAQ,IAAA,CAAK,MAAA,CAAO,KAAA,GAAQ,MAAM,IAAA,CAAK,MAAA,CAAO,IAAA,IAAA,CAAS,IAAA,CAAK,MAAA,CAAO,KAAA,GAAQ,GAAA,GAAA,CAC3E,IAAA,CAAK,MAAA,CAAO,MAAA,GAAS,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,GAAA,GAAM,KAAA,CAAA,IAAU,IAAA,CAAK,MAAA,CAAO,KAAA;YAGrE,OAAO,IAAA,CAAK,cAAA;QAAA;QAGN,cAAA,IAAA,EAAA,QAAO,2MAAI,UAAA;QACX,cAAA,IAAA,EAAA,cAAa,2MAAI,aAAA;QACjB,cAAA,IAAA,EAAA,gBAAe,2MAAI,UAAA;QACnB,cAAA,IAAA,EAAA,qBAAoB,2MAAI,UAAA;QACxB,cAAA,IAAA,EAAA,2BAA0B,IAAI,iNAAA;QAC9B,cAAA,IAAA,EAAA,iBAAgB,2MAAI,UAAA;QACpB,cAAA,IAAA,EAAA,SAAgB;QAEhB,cAAA,IAAA,EAAA,gBAAe,MAAY;YACjC,IAAA,CAAK,aAAA,CAAc,GAAA,CAAI,IAAA,CAAK,SAAA,CAAU,CAAA,GAAI,IAAA,CAAK,SAAA,CAAU,CAAA,EAAG,IAAA,CAAK,SAAA,CAAU,CAAA,GAAI,IAAA,CAAK,SAAA,CAAU,CAAA,EAAG,CAAC;YAC7F,IAAA,CAAA,KAAA,GAAQ,IAAA,CAAK,aAAA,CAAc,MAAA,CAAO;YAEvC,IAAI,IAAA,CAAK,KAAA,EAAO;gBACT,IAAA,CAAA,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,QAAQ,EAAE,GAAA,CAAI,IAAA,CAAK,MAAM;gBAEpD,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,IAAA,CAAK,IAAI,EAAE,SAAA;gBAClC,IAAA,CAAK,iBAAA,CAAkB,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,EAAE,EAAE,SAAA;gBAC5C,IAAA,CAAK,uBAAA,CAAwB,YAAA,CAAa,IAAA,CAAK,iBAAA,EAAmB,IAAA,CAAK,YAAY,EAAE,SAAA;gBAErF,IAAA,CAAK,iBAAA,CAAkB,SAAA,CAAU,IAAA,CAAK,SAAA,CAAU,CAAA,GAAI,IAAA,CAAK,SAAA,CAAU,CAAC;gBACpE,IAAA,CAAK,uBAAA,CAAwB,SAAA,CAAU,IAAA,CAAK,SAAA,CAAU,CAAA,GAAI,IAAA,CAAK,SAAA,CAAU,CAAC;gBAE1E,IAAA,CAAK,aAAA,CAAc,IAAA,CAAK,IAAA,CAAK,iBAAA,CAAkB,GAAA,CAAI,IAAA,CAAK,uBAAuB,CAAC;gBAEhF,IAAA,CAAK,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,aAAA,EAAe,IAAA,CAAK,IAAI,EAAE,SAAA;gBAEtD,IAAA,CAAK,KAAA,IAAS,IAAA,CAAK,WAAA;gBACnB,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,IAAA,CAAK,IAAA,EAAM,IAAA,CAAK,KAAK;gBAEjD,IAAA,CAAA,IAAA,CAAK,eAAA,CAAgB,IAAA,CAAK,UAAU;gBACzC,IAAA,CAAK,MAAA,CAAO,EAAA,CAAG,eAAA,CAAgB,IAAA,CAAK,UAAU;gBAEzC,IAAA,CAAA,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,IAAI;gBAC7B,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,KAAA;YACd,OAAA,IAAA,CAAC,IAAA,CAAK,YAAA,IAAgB,IAAA,CAAK,UAAA,EAAY;gBAChD,IAAA,CAAK,UAAA,IAAc,KAAK,IAAA,CAAK,IAAM,IAAA,CAAK,oBAAoB;gBACvD,IAAA,CAAA,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,QAAQ,EAAE,GAAA,CAAI,IAAA,CAAK,MAAM;gBACpD,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,IAAA,CAAK,SAAA,EAAW,IAAA,CAAK,UAAU;gBAC3D,IAAA,CAAA,IAAA,CAAK,eAAA,CAAgB,IAAA,CAAK,UAAU;gBACzC,IAAA,CAAK,MAAA,CAAO,EAAA,CAAG,eAAA,CAAgB,IAAA,CAAK,UAAU;YAChD;YAEK,IAAA,CAAA,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,SAAS;QAAA;QAG5B,cAAA,IAAA,EAAA,cAAa,MAAY;YAC3B,IAAA;YAEJ,IAAI,IAAA,CAAK,MAAA,KAAW,IAAA,CAAK,KAAA,CAAM,cAAA,EAAgB;gBACpC,SAAA,IAAA,CAAK,uBAAA,GAA0B,IAAA,CAAK,qBAAA;gBAC7C,IAAA,CAAK,uBAAA,GAA0B,IAAA,CAAK,qBAAA;gBAE/B,IAAA,IAAA,CAAK,MAAA,CAA6B,mBAAA,EAAqB;oBACrD,IAAA,CAAA,IAAA,CAAK,cAAA,CAAe,MAAM;gBAAA,OAAA,IACrB,IAAA,CAAK,MAAA,CAA8B,oBAAA,EAAsB;oBACnE,IAAA,CAAK,MAAA,CAAO,IAAA,IAAQ;oBACpB,IAAA,CAAK,MAAA,CAAO,sBAAA;gBAAuB,OAC9B;oBACL,QAAQ,IAAA,CAAK,kDAAkD;gBACjE;YAAA,OACK;gBACL,SAAS,IAAA,CAAO,IAAA,CAAK,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,UAAA,CAAW,CAAA,IAAK,IAAA,CAAK,SAAA;gBAExD,IAAA,KAAK,GAAA,CAAI,SAAS,CAAG,IAAI,IAAA,CAAK,GAAA,IAAO,SAAS,GAAK;oBAChD,IAAA,IAAA,CAAK,MAAA,CAA6B,mBAAA,EAAqB;wBACtD,IAAA,SAAS,KAAO,IAAA,CAAK,IAAA,CAAK,MAAA,CAAY,KAAA,IAAA,CAAK,WAAA,GAAc,IAAA,CAAK,GAAA,EAAK;4BAC5D,SAAA;wBACX;wBACK,IAAA,CAAA,IAAA,CAAK,cAAA,CAAe,MAAM;oBAAA,OAAA,IACrB,IAAA,CAAK,MAAA,CAA8B,oBAAA,EAAsB;wBAC/D,IAAA,SAAS,KAAO,IAAA,CAAK,MAAA,CAAO,IAAA,GAAO,IAAA,CAAK,WAAA,GAAc,IAAA,CAAK,WAAA,EAAa;4BACjE,SAAA;wBACX;wBACA,IAAA,CAAK,MAAA,CAAO,IAAA,IAAQ;oBAAA,OACf;wBACL,QAAQ,IAAA,CAAK,kDAAkD;oBACjE;gBACF;gBAEA,IAAI,IAAA,CAAK,YAAA,EAAc;oBAChB,IAAA,CAAA,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,QAAQ;gBAAA,OAC7B;oBACA,IAAA,CAAA,UAAA,CAAW,CAAA,IAAA,CAAM,IAAA,CAAK,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,UAAA,CAAW,CAAA,IAAK,IAAA,CAAK,oBAAA;gBACpE;gBAEA,IAAI,IAAA,CAAK,UAAA,EAAY;oBAEnB,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,IAAA,CAAK,MAAM,EAAE,OAAA,CAAQ,IAAA,CAAK,MAAM;oBACvD,IAAI,WAAW,IAAA,CAAK,YAAA,CACjB,GAAA,CAAI,IAAA,CAAK,aAAA,CAAc,CAAA,EAAG,IAAA,CAAK,aAAA,CAAc,CAAA,EAAG,IAAA,CAAK,YAAA,CAAa,CAAC,EACnE,SAAA,CAAU,IAAA,CAAK,MAAM;oBAGxB,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY,UAAU,IAAA,CAAK,MAAA,EAAQ,MAAM;gBACvD;gBAGK,IAAA,IAAA,CAAK,MAAA,CAA8B,oBAAA,EAAsB;oBAC5D,IAAA,CAAK,MAAA,CAAO,sBAAA;gBACd;YACF;QAAA;QAGM,cAAA,IAAA,EAAA,eAAc,2MAAI,UAAA;QAClB,cAAA,IAAA,EAAA,YAAW,2MAAI,UAAA;QACf,cAAA,IAAA,EAAA,OAAM,2MAAI,UAAA;QAEV,cAAA,IAAA,EAAA,aAAY,MAAY;YAC9B,IAAI,CAAC,IAAA,CAAK,UAAA,EAAY;YACtB,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,OAAO,EAAE,GAAA,CAAI,IAAA,CAAK,SAAS;YAEtD,IAAI,IAAA,CAAK,WAAA,CAAY,QAAA,CAAS,IAAI,IAAA,CAAK,GAAA,EAAK;gBACrC,IAAA,IAAA,CAAK,MAAA,CAA8B,oBAAA,EAAsB;oBAC5D,MAAM,cAAc,IAAA,CAAK,MAAA;oBACzB,MAAM,UAAA,CAAW,YAAY,KAAA,GAAQ,YAAY,IAAA,IAAQ,IAAA,CAAK,MAAA,CAAO,IAAA;oBACrE,MAAM,UAAA,CAAW,YAAY,GAAA,GAAM,YAAY,MAAA,IAAU,IAAA,CAAK,MAAA,CAAO,IAAA;oBAErE,IAAA,CAAK,WAAA,CAAY,CAAA,IAAK;oBACtB,IAAA,CAAK,WAAA,CAAY,CAAA,IAAK;gBAAA,OACjB;oBACL,IAAA,CAAK,WAAA,CAAY,cAAA,CAAe,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,IAAI,IAAA,CAAK,QAAQ;gBACpE;gBAEA,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,IAAA,CAAK,IAAI,EAAE,KAAA,CAAM,IAAA,CAAK,MAAA,CAAO,EAAE,EAAE,SAAA,CAAU,IAAA,CAAK,WAAA,CAAY,CAAC;gBAC3E,IAAA,CAAK,GAAA,CAAI,GAAA,CAAI,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,EAAE,EAAE,SAAA,CAAU,IAAA,CAAK,WAAA,CAAY,CAAC,CAAC;gBAE7E,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,GAAA,CAAI,IAAA,CAAK,GAAG;gBAC5B,IAAA,CAAA,MAAA,CAAO,GAAA,CAAI,IAAA,CAAK,GAAG;gBAExB,IAAI,IAAA,CAAK,YAAA,EAAc;oBAChB,IAAA,CAAA,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,OAAO;gBAAA,OAC3B;oBACL,IAAA,CAAK,SAAA,CAAU,GAAA,CACb,IAAA,CAAK,WAAA,CAAY,UAAA,CAAW,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,SAAS,EAAE,cAAA,CAAe,IAAA,CAAK,oBAAoB;gBAEtG;YACF;QAAA;QAGM,cAAA,IAAA,EAAA,kBAAiB,MAAY;YACnC,IAAI,CAAC,IAAA,CAAK,MAAA,IAAU,CAAC,IAAA,CAAK,KAAA,EAAO;gBAC/B,IAAI,IAAA,CAAK,IAAA,CAAK,QAAA,CAAA,IAAa,IAAA,CAAK,WAAA,GAAc,IAAA,CAAK,WAAA,EAAa;oBACzD,IAAA,CAAA,MAAA,CAAO,QAAA,CAAS,UAAA,CAAW,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,WAAW,CAAC;oBAC7E,IAAA,CAAA,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,QAAQ;gBACpC;gBAEA,IAAI,IAAA,CAAK,IAAA,CAAK,QAAA,CAAA,IAAa,IAAA,CAAK,WAAA,GAAc,IAAA,CAAK,WAAA,EAAa;oBACzD,IAAA,CAAA,MAAA,CAAO,QAAA,CAAS,UAAA,CAAW,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,WAAW,CAAC;oBAC7E,IAAA,CAAA,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,QAAQ;gBACpC;YACF;QAAA;QAGK,cAAA,IAAA,EAAA,gBAAe,MAAY;YAChC,IAAI,CAAC,IAAA,CAAK,UAAA,EAAY;YAChB,MAAA,MAAM,IAAA,CAAK,UAAA,CAAW,qBAAA,CAAsB;YAE5C,MAAA,IAAI,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,eAAA;YACxC,IAAA,CAAK,MAAA,CAAO,IAAA,GAAO,IAAI,IAAA,GAAO,OAAO,WAAA,GAAc,EAAE,UAAA;YACrD,IAAA,CAAK,MAAA,CAAO,GAAA,GAAM,IAAI,GAAA,GAAM,OAAO,WAAA,GAAc,EAAE,SAAA;YAC9C,IAAA,CAAA,MAAA,CAAO,KAAA,GAAQ,IAAI,KAAA;YACnB,IAAA,CAAA,MAAA,CAAO,MAAA,GAAS,IAAI,MAAA;QAAA;QAGpB,cAAA,IAAA,EAAA,UAAS,MAAY;YAC1B,IAAA,CAAK,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,MAAA,CAAO,QAAA,EAAU,IAAA,CAAK,MAAM;YAElD,IAAA,CAAC,IAAA,CAAK,QAAA,EAAU;gBAClB,IAAA,CAAK,YAAA,CAAa;YACpB;YAEI,IAAA,CAAC,IAAA,CAAK,MAAA,EAAQ;gBAChB,IAAA,CAAK,UAAA,CAAW;YAClB;YAEI,IAAA,CAAC,IAAA,CAAK,KAAA,EAAO;gBACf,IAAA,CAAK,SAAA,CAAU;YACjB;YAEA,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,UAAA,CAAW,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,IAAI;YAEjD,IAAA,IAAA,CAAK,MAAA,CAA6B,mBAAA,EAAqB;gBAC1D,IAAA,CAAK,cAAA,CAAe;gBAEf,IAAA,CAAA,MAAA,CAAO,MAAA,CAAO,IAAA,CAAK,MAAM;gBAE1B,IAAA,IAAA,CAAK,YAAA,CAAa,iBAAA,CAAkB,IAAA,CAAK,MAAA,CAAO,QAAQ,IAAI,IAAA,CAAK,GAAA,EAAK;oBAEnE,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,WAAW;oBAEnC,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,QAAQ;gBAC7C;YAAA,OAAA,IACU,IAAA,CAAK,MAAA,CAA8B,oBAAA,EAAsB;gBAC9D,IAAA,CAAA,MAAA,CAAO,MAAA,CAAO,IAAA,CAAK,MAAM;gBAE9B,IAAI,IAAA,CAAK,YAAA,CAAa,iBAAA,CAAkB,IAAA,CAAK,MAAA,CAAO,QAAQ,IAAI,IAAA,CAAK,GAAA,IAAO,IAAA,CAAK,QAAA,KAAa,IAAA,CAAK,MAAA,CAAO,IAAA,EAAM;oBAEzG,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,WAAW;oBAEnC,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,QAAQ;oBACtC,IAAA,CAAA,QAAA,GAAW,IAAA,CAAK,MAAA,CAAO,IAAA;gBAC9B;YAAA,OACK;gBACL,QAAQ,IAAA,CAAK,kDAAkD;YACjE;QAAA;QAGK,cAAA,IAAA,EAAA,SAAQ,MAAY;YACpB,IAAA,CAAA,MAAA,GAAS,IAAA,CAAK,KAAA,CAAM,IAAA;YACpB,IAAA,CAAA,SAAA,GAAY,IAAA,CAAK,KAAA,CAAM,IAAA;YAEvB,IAAA,CAAA,MAAA,CAAO,IAAA,CAAK,IAAA,CAAK,OAAO;YAC7B,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,IAAA,CAAK,IAAA,CAAK,SAAS;YACxC,IAAA,CAAK,MAAA,CAAO,EAAA,CAAG,IAAA,CAAK,IAAA,CAAK,GAAG;YACvB,IAAA,CAAA,MAAA,CAAO,IAAA,GAAO,IAAA,CAAK,KAAA;YAExB,IAAA,CAAK,MAAA,CAAO,sBAAA;YAEZ,IAAA,CAAK,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,MAAA,CAAO,QAAA,EAAU,IAAA,CAAK,MAAM;YAEjD,IAAA,CAAA,MAAA,CAAO,MAAA,CAAO,IAAA,CAAK,MAAM;YAGzB,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,WAAW;YAEnC,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,QAAQ;YACtC,IAAA,CAAA,QAAA,GAAW,IAAA,CAAK,MAAA,CAAO,IAAA;QAAA;QAGtB,cAAA,IAAA,EAAA,WAAU,CAAC,UAA+B;YAChD,IAAI,IAAA,CAAK,OAAA,KAAY,OAAO;YAErB,OAAA,mBAAA,CAAoB,WAAW,IAAA,CAAK,OAAO;YAElD,IAAI,IAAA,CAAK,SAAA,KAAc,IAAA,CAAK,KAAA,CAAM,IAAA,EAAM;gBACtC;YACF,OAAA,IAAW,MAAM,IAAA,KAAS,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,MAAM,CAAA,IAAK,CAAC,IAAA,CAAK,QAAA,EAAU;gBACnE,IAAA,CAAA,SAAA,GAAY,IAAA,CAAK,KAAA,CAAM,MAAA;YAC9B,OAAA,IAAW,MAAM,IAAA,KAAS,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,IAAI,CAAA,IAAK,CAAC,IAAA,CAAK,MAAA,EAAQ;gBAC/D,IAAA,CAAA,SAAA,GAAY,IAAA,CAAK,KAAA,CAAM,IAAA;YAC9B,OAAA,IAAW,MAAM,IAAA,KAAS,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,GAAG,CAAA,IAAK,CAAC,IAAA,CAAK,KAAA,EAAO;gBAC7D,IAAA,CAAA,SAAA,GAAY,IAAA,CAAK,KAAA,CAAM,GAAA;YAC9B;QAAA;QAGM,cAAA,IAAA,EAAA,iBAAgB,CAAC,UAA8B;YACrD,IAAI,IAAA,CAAK,OAAA,KAAY,OAAO;YAE5B,OAAQ,MAAM,WAAA,EAAa;gBACzB,KAAK;gBACL,KAAK;oBACH,IAAA,CAAK,WAAA,CAAY,KAAK;oBACtB;YAGJ;QAAA;QAGM,cAAA,IAAA,EAAA,iBAAgB,CAAC,UAA8B;YACrD,IAAI,IAAA,CAAK,OAAA,KAAY,OAAO;YAE5B,OAAQ,MAAM,WAAA,EAAa;gBACzB,KAAK;gBACL,KAAK;oBACH,IAAA,CAAK,WAAA,CAAY,KAAK;oBACtB;YAGJ;QAAA;QAGM,cAAA,IAAA,EAAA,eAAc,CAAC,UAA8B;YACnD,IAAI,IAAA,CAAK,OAAA,KAAY,OAAO;YAE5B,OAAQ,MAAM,WAAA,EAAa;gBACzB,KAAK;gBACL,KAAK;oBACH,IAAA,CAAK,SAAA,CAAU;oBACf;YAGJ;QAAA;QAGM,cAAA,IAAA,EAAA,SAAQ,MAAY;YAC1B,IAAI,IAAA,CAAK,OAAA,KAAY,OAAO;YAEvB,IAAA,CAAA,SAAA,GAAY,IAAA,CAAK,KAAA,CAAM,IAAA;YAErB,OAAA,gBAAA,CAAiB,WAAW,IAAA,CAAK,OAAO;QAAA;QAGzC,cAAA,IAAA,EAAA,eAAc,CAAC,UAA4B;YACjD,IAAI,CAAC,IAAA,CAAK,UAAA,EAAY;YACtB,IAAI,IAAA,CAAK,MAAA,KAAW,IAAA,CAAK,KAAA,CAAM,IAAA,EAAM;gBACnC,OAAQ,MAAM,MAAA,EAAQ;oBACpB,KAAK,IAAA,CAAK,YAAA,CAAa,IAAA;wBAChB,IAAA,CAAA,MAAA,GAAS,IAAA,CAAK,KAAA,CAAM,MAAA;wBACzB;oBAEF,KAAK,IAAA,CAAK,YAAA,CAAa,MAAA;wBAChB,IAAA,CAAA,MAAA,GAAS,IAAA,CAAK,KAAA,CAAM,IAAA;wBACzB;oBAEF,KAAK,IAAA,CAAK,YAAA,CAAa,KAAA;wBAChB,IAAA,CAAA,MAAA,GAAS,IAAA,CAAK,KAAA,CAAM,GAAA;wBACzB;gBACJ;YACF;YAEM,MAAA,QAAQ,IAAA,CAAK,SAAA,KAAc,IAAA,CAAK,KAAA,CAAM,IAAA,GAAO,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,MAAA;YAEzE,IAAI,UAAU,IAAA,CAAK,KAAA,CAAM,MAAA,IAAU,CAAC,IAAA,CAAK,QAAA,EAAU;gBAC5C,IAAA,CAAA,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,gBAAA,CAAiB,MAAM,KAAA,EAAO,MAAM,KAAK,CAAC;gBAC9D,IAAA,CAAA,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,SAAS;YAAA,OAAA,IACzB,UAAU,IAAA,CAAK,KAAA,CAAM,IAAA,IAAQ,CAAC,IAAA,CAAK,MAAA,EAAQ;gBAC/C,IAAA,CAAA,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,gBAAA,CAAiB,MAAM,KAAA,EAAO,MAAM,KAAK,CAAC;gBAC/D,IAAA,CAAA,QAAA,CAAS,IAAA,CAAK,IAAA,CAAK,UAAU;YAAA,OAAA,IACzB,UAAU,IAAA,CAAK,KAAA,CAAM,GAAA,IAAO,CAAC,IAAA,CAAK,KAAA,EAAO;gBAC7C,IAAA,CAAA,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,gBAAA,CAAiB,MAAM,KAAA,EAAO,MAAM,KAAK,CAAC;gBAC9D,IAAA,CAAA,OAAA,CAAQ,IAAA,CAAK,IAAA,CAAK,SAAS;YAClC;YAEA,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,gBAAA,CAAiB,eAAe,IAAA,CAAK,aAAa;YAChF,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,gBAAA,CAAiB,aAAa,IAAA,CAAK,WAAW;YAGvE,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,UAAU;QAAA;QAG5B,cAAA,IAAA,EAAA,eAAc,CAAC,UAA4B;YACjD,IAAI,IAAA,CAAK,OAAA,KAAY,OAAO;YAEtB,MAAA,QAAQ,IAAA,CAAK,SAAA,KAAc,IAAA,CAAK,KAAA,CAAM,IAAA,GAAO,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,MAAA;YAEzE,IAAI,UAAU,IAAA,CAAK,KAAA,CAAM,MAAA,IAAU,CAAC,IAAA,CAAK,QAAA,EAAU;gBAC5C,IAAA,CAAA,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,SAAS;gBAC7B,IAAA,CAAA,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,gBAAA,CAAiB,MAAM,KAAA,EAAO,MAAM,KAAK,CAAC;YAAA,OAAA,IAC1D,UAAU,IAAA,CAAK,KAAA,CAAM,IAAA,IAAQ,CAAC,IAAA,CAAK,MAAA,EAAQ;gBAC/C,IAAA,CAAA,QAAA,CAAS,IAAA,CAAK,IAAA,CAAK,gBAAA,CAAiB,MAAM,KAAA,EAAO,MAAM,KAAK,CAAC;YAAA,OAAA,IACzD,UAAU,IAAA,CAAK,KAAA,CAAM,GAAA,IAAO,CAAC,IAAA,CAAK,KAAA,EAAO;gBAC7C,IAAA,CAAA,OAAA,CAAQ,IAAA,CAAK,IAAA,CAAK,gBAAA,CAAiB,MAAM,KAAA,EAAO,MAAM,KAAK,CAAC;YACnE;QAAA;QAGM,cAAA,IAAA,EAAA,aAAY,MAAY;YAC9B,IAAI,CAAC,IAAA,CAAK,UAAA,EAAY;YACtB,IAAI,IAAA,CAAK,OAAA,KAAY,OAAO;YAEvB,IAAA,CAAA,MAAA,GAAS,IAAA,CAAK,KAAA,CAAM,IAAA;YAEzB,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,mBAAA,CAAoB,eAAe,IAAA,CAAK,aAAa;YACnF,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,mBAAA,CAAoB,aAAa,IAAA,CAAK,WAAW;YAG1E,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,QAAQ;QAAA;QAG1B,cAAA,IAAA,EAAA,cAAa,CAAC,UAA4B;YAChD,IAAI,IAAA,CAAK,OAAA,KAAY,OAAO;YAE5B,IAAI,IAAA,CAAK,MAAA,KAAW,MAAM;YAE1B,MAAM,cAAA,CAAe;YAErB,OAAQ,MAAM,SAAA,EAAW;gBACvB,KAAK;oBAEE,IAAA,CAAA,UAAA,CAAW,CAAA,IAAK,MAAM,MAAA,GAAS;oBACpC;gBAEF,KAAK;oBAEE,IAAA,CAAA,UAAA,CAAW,CAAA,IAAK,MAAM,MAAA,GAAS;oBACpC;gBAEF;oBAEO,IAAA,CAAA,UAAA,CAAW,CAAA,IAAK,MAAM,MAAA,GAAS;oBACpC;YACJ;YAEA,IAAA,CAAK,aAAA,CAAc,CAAA,GAAK,MAAM,OAAA,GAAU,IAAA,CAAK,MAAA,CAAO,KAAA,GAAS,IAAI;YAC5D,IAAA,CAAA,aAAA,CAAc,CAAA,GAAI,CAAA,CAAE,MAAM,OAAA,GAAU,IAAA,CAAK,MAAA,CAAO,MAAA,IAAU,IAAI;YAG9D,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,UAAU;YAE7B,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,QAAQ;QAAA;QAG1B,cAAA,IAAA,EAAA,cAAa,CAAC,UAA4B;YAChD,IAAI,IAAA,CAAK,OAAA,KAAY,OAAO;YAE5B,MAAM,cAAA,CAAe;YAEb,OAAA,MAAM,OAAA,CAAQ,MAAA,EAAQ;gBAC5B,KAAK;oBACE,IAAA,CAAA,MAAA,GAAS,IAAA,CAAK,KAAA,CAAM,YAAA;oBACzB,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,gBAAA,CAAiB,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA,EAAO,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAK,CAAC;oBACpF,IAAA,CAAA,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,SAAS;oBAClC;gBAEF;oBAEO,IAAA,CAAA,MAAA,GAAS,IAAA,CAAK,KAAA,CAAM,cAAA;oBACnB,MAAA,KAAK,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA,GAAQ,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA;oBAC/C,MAAA,KAAK,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA,GAAQ,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA;oBAChD,IAAA,CAAA,qBAAA,GAAwB,IAAA,CAAK,uBAAA,GAA0B,KAAK,IAAA,CAAK,KAAK,KAAK,KAAK,EAAE;oBAEjF,MAAA,IAAA,CAAK,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA,GAAQ,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA,IAAS;oBACxD,MAAA,IAAA,CAAK,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA,GAAQ,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA,IAAS;oBAC9D,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,gBAAA,CAAiB,GAAG,CAAC,CAAC;oBAC1C,IAAA,CAAA,OAAA,CAAQ,IAAA,CAAK,IAAA,CAAK,SAAS;oBAChC;YACJ;YAGK,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,UAAU;QAAA;QAG5B,cAAA,IAAA,EAAA,aAAY,CAAC,UAA4B;YAC/C,IAAI,IAAA,CAAK,OAAA,KAAY,OAAO;YAE5B,MAAM,cAAA,CAAe;YAEb,OAAA,MAAM,OAAA,CAAQ,MAAA,EAAQ;gBAC5B,KAAK;oBACE,IAAA,CAAA,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,SAAS;oBAClC,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,gBAAA,CAAiB,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA,EAAO,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAK,CAAC;oBACzF;gBAEF;oBAEQ,MAAA,KAAK,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA,GAAQ,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA;oBAC/C,MAAA,KAAK,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA,GAAQ,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA;oBACrD,IAAA,CAAK,qBAAA,GAAwB,KAAK,IAAA,CAAK,KAAK,KAAK,KAAK,EAAE;oBAElD,MAAA,IAAA,CAAK,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA,GAAQ,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA,IAAS;oBACxD,MAAA,IAAA,CAAK,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA,GAAQ,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA,IAAS;oBAC9D,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,IAAA,CAAK,gBAAA,CAAiB,GAAG,CAAC,CAAC;oBAC7C;YACJ;QAAA;QAGM,cAAA,IAAA,EAAA,YAAW,CAAC,UAA4B;YAC9C,IAAI,IAAA,CAAK,OAAA,KAAY,OAAO;YAEpB,OAAA,MAAM,OAAA,CAAQ,MAAA,EAAQ;gBAC5B,KAAK;oBACE,IAAA,CAAA,MAAA,GAAS,IAAA,CAAK,KAAA,CAAM,IAAA;oBACzB;gBAEF,KAAK;oBACE,IAAA,CAAA,MAAA,GAAS,IAAA,CAAK,KAAA,CAAM,YAAA;oBACzB,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,gBAAA,CAAiB,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAA,EAAO,MAAM,OAAA,CAAQ,CAAC,CAAA,CAAE,KAAK,CAAC;oBACpF,IAAA,CAAA,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,SAAS;oBAClC;YACJ;YAGK,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,QAAQ;QAAA;QAG1B,cAAA,IAAA,EAAA,eAAc,CAAC,UAA4B;YACjD,IAAI,IAAA,CAAK,OAAA,KAAY,OAAO;YAE5B,MAAM,cAAA,CAAe;QAAA;QAIhB,kDAAA;QAAA,cAAA,IAAA,EAAA,WAAU,CAAC,eAAkC;YAClD,IAAK,eAAuB,UAAU;gBAC5B,QAAA,KAAA,CACN;YAEJ;YACA,IAAA,CAAK,UAAA,GAAa;YAClB,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,eAAe,IAAA,CAAK,WAAW;YAEhE,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,eAAe,IAAA,CAAK,aAAa;YAClE,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,SAAS,IAAA,CAAK,UAAU;YAEzD,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,cAAc,IAAA,CAAK,UAAU;YAC9D,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,YAAY,IAAA,CAAK,QAAQ;YAC1D,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,aAAa,IAAA,CAAK,SAAS;YAE5D,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,gBAAA,CAAiB,eAAe,IAAA,CAAK,aAAa;YAChF,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,gBAAA,CAAiB,aAAa,IAAA,CAAK,WAAW;YAErE,OAAA,gBAAA,CAAiB,WAAW,IAAA,CAAK,OAAO;YACxC,OAAA,gBAAA,CAAiB,SAAS,IAAA,CAAK,KAAK;YAE3C,IAAA,CAAK,YAAA,CAAa;QAAA;QAGb,cAAA,IAAA,EAAA,WAAU,MAAY;YAC3B,IAAI,CAAC,IAAA,CAAK,UAAA,EAAY;YACtB,IAAA,CAAK,UAAA,CAAW,mBAAA,CAAoB,eAAe,IAAA,CAAK,WAAW;YAEnE,IAAA,CAAK,UAAA,CAAW,mBAAA,CAAoB,eAAe,IAAA,CAAK,aAAa;YACrE,IAAA,CAAK,UAAA,CAAW,mBAAA,CAAoB,SAAS,IAAA,CAAK,UAAU;YAE5D,IAAA,CAAK,UAAA,CAAW,mBAAA,CAAoB,cAAc,IAAA,CAAK,UAAU;YACjE,IAAA,CAAK,UAAA,CAAW,mBAAA,CAAoB,YAAY,IAAA,CAAK,QAAQ;YAC7D,IAAA,CAAK,UAAA,CAAW,mBAAA,CAAoB,aAAa,IAAA,CAAK,SAAS;YAE/D,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,mBAAA,CAAoB,eAAe,IAAA,CAAK,aAAa;YACnF,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,mBAAA,CAAoB,aAAa,IAAA,CAAK,WAAW;YAExE,OAAA,mBAAA,CAAoB,WAAW,IAAA,CAAK,OAAO;YAC3C,OAAA,mBAAA,CAAoB,SAAS,IAAA,CAAK,KAAK;QAAA;QAriB9C,IAAA,CAAK,MAAA,GAAS;QAIT,IAAA,CAAA,OAAA,GAAU,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM;QACjC,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,KAAA,CAAM;QAC5C,IAAA,CAAK,GAAA,GAAM,IAAA,CAAK,MAAA,CAAO,EAAA,CAAG,KAAA,CAAM;QAC3B,IAAA,CAAA,KAAA,GAAQ,IAAA,CAAK,MAAA,CAAO,IAAA;QAGzB,IAAI,eAAe,KAAA,GAAW,IAAA,CAAK,OAAA,CAAQ,UAAU;QAGrD,IAAA,CAAK,MAAA,CAAO;IACd;AAyhBF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1827, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1833, "column": 0}, "map": {"version": 3, "file": "ArcballControls.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/controls/ArcballControls.ts"], "sourcesContent": ["import {\n  GridHelper,\n  EllipseCurve,\n  BufferGeometry,\n  Line,\n  LineBasicMaterial,\n  Raycaster,\n  Group,\n  Box3,\n  Sphere,\n  Quaternion,\n  Vector2,\n  Vector3,\n  Matrix4,\n  MathUtils,\n  Scene,\n  PerspectiveCamera,\n  OrthographicCamera,\n  Mesh,\n  Material,\n} from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\ntype Camera = OrthographicCamera | PerspectiveCamera\ntype Operation = 'PAN' | 'ROTATE' | 'ZOOM' | 'FOV'\ntype MouseButtonType = number | 'WHEEL'\ntype ModifierKey = 'CTRL' | 'SHIFT'\ntype MouseAction = {\n  operation: Operation\n  mouse: MouseButtonType\n  key: Modifier<PERSON>ey | null\n}\n\ntype Transformation = {\n  camera: Matrix4 | null\n  gizmos: Matrix4 | null\n}\n\n//trackball state\nconst STATE = {\n  IDLE: Symbol(),\n  ROTATE: Symbol(),\n  PAN: Symbol(),\n  SCALE: Symbol(),\n  FOV: Symbol(),\n  FOCUS: Symbol(),\n  ZROTATE: Symbol(),\n  TOUCH_MULTI: Symbol(),\n  ANIMATION_FOCUS: Symbol(),\n  ANIMATION_ROTATE: Symbol(),\n}\n\nconst INPUT = {\n  NONE: Symbol(),\n  ONE_FINGER: Symbol(),\n  ONE_FINGER_SWITCHED: Symbol(),\n  TWO_FINGER: Symbol(),\n  MULT_FINGER: Symbol(),\n  CURSOR: Symbol(),\n}\n\n//cursor center coordinates\nconst _center = {\n  x: 0,\n  y: 0,\n}\n\n//transformation matrices for gizmos and camera\nconst _transformation: Transformation = {\n  camera: /* @__PURE__ */ new Matrix4(),\n  gizmos: /* @__PURE__ */ new Matrix4(),\n}\n\n//events\nconst _changeEvent = { type: 'change' }\nconst _startEvent = { type: 'start' }\nconst _endEvent = { type: 'end' }\n\n/**\n *\n * @param {CamOrthographicCamera | PerspectiveCameraera} camera Virtual camera used in the scene\n * @param {HTMLElement=null} domElement Renderer's dom element\n * @param {Scene=null} scene The scene to be rendered\n */\nclass ArcballControls extends EventDispatcher<StandardControlsEventMap> {\n  private camera: OrthographicCamera | PerspectiveCamera | null\n  private domElement: HTMLElement | null | undefined\n  private scene: Scene | null | undefined\n\n  private mouseActions: (MouseAction & { state: Symbol })[]\n  private _mouseOp: Operation | null\n\n  private _v2_1: Vector2\n  private _v3_1: Vector3\n  private _v3_2: Vector3\n\n  private _m4_1: Matrix4\n  private _m4_2: Matrix4\n\n  private _quat: Quaternion\n\n  private _translationMatrix: Matrix4\n  private _rotationMatrix: Matrix4\n  private _scaleMatrix: Matrix4\n\n  private _rotationAxis: Vector3\n\n  private _cameraMatrixState: Matrix4\n  private _cameraProjectionState: Matrix4\n\n  private _fovState: number\n  private _upState: Vector3\n  private _zoomState: number\n  private _nearPos: number\n  private _farPos: number\n\n  private _gizmoMatrixState: Matrix4\n\n  private _up0: Vector3\n  private _zoom0: number\n  private _fov0: number\n  private _initialNear: number\n  private _nearPos0: number\n  private _initialFar: number\n  private _farPos0: number\n  private _cameraMatrixState0: Matrix4\n  private _gizmoMatrixState0: Matrix4\n\n  private _button: MouseButtonType\n  private _touchStart: PointerEvent[]\n  private _touchCurrent: PointerEvent[]\n  private _input: Symbol\n\n  private _switchSensibility: number\n  private _startFingerDistance: number\n  private _currentFingerDistance: number\n  private _startFingerRotation: number\n  private _currentFingerRotation: number\n\n  private _devPxRatio: number\n  private _downValid: boolean\n  private _nclicks: number\n  private _downEvents: PointerEvent[]\n  private _clickStart: number\n  private _maxDownTime: number\n  private _maxInterval: number\n  private _posThreshold: number\n  private _movementThreshold: number\n\n  private _currentCursorPosition: Vector3\n  private _startCursorPosition: Vector3\n\n  private _grid: GridHelper | null\n  private _gridPosition: Vector3\n\n  private _gizmos: Group\n  private _curvePts: number\n\n  private _timeStart: number\n  private _animationId: number\n\n  public focusAnimationTime: number\n\n  private _timePrev: number\n  private _timeCurrent: number\n  private _anglePrev: number\n  private _angleCurrent: number\n  private _cursorPosPrev: Vector3\n  private _cursorPosCurr: Vector3\n  private _wPrev: number\n  private _wCurr: number\n\n  public adjustNearFar: boolean\n  public scaleFactor: number\n  public dampingFactor: number\n  public wMax: number\n  public enableAnimations: boolean\n  public enableGrid: boolean\n  public cursorZoom: boolean\n  public minFov: number\n  public maxFov: number\n\n  public enabled: boolean\n  public enablePan: boolean\n  public enableRotate: boolean\n  public enableZoom: boolean\n\n  public minDistance: number\n  public maxDistance: number\n  public minZoom: number\n  public maxZoom: number\n\n  readonly target: Vector3\n  private _currentTarget: Vector3\n\n  private _tbRadius: number\n\n  private _state: Symbol\n\n  constructor(\n    camera: Camera | null,\n    domElement: HTMLElement | null | undefined = null,\n    scene: Scene | null | undefined = null,\n  ) {\n    super()\n    this.camera = null\n    this.domElement = domElement\n    this.scene = scene\n\n    this.mouseActions = []\n    this._mouseOp = null\n\n    //global vectors and matrices that are used in some operations to avoid creating new objects every time (e.g. every time cursor moves)\n    this._v2_1 = new Vector2()\n    this._v3_1 = new Vector3()\n    this._v3_2 = new Vector3()\n\n    this._m4_1 = new Matrix4()\n    this._m4_2 = new Matrix4()\n\n    this._quat = new Quaternion()\n\n    //transformation matrices\n    this._translationMatrix = new Matrix4() //matrix for translation operation\n    this._rotationMatrix = new Matrix4() //matrix for rotation operation\n    this._scaleMatrix = new Matrix4() //matrix for scaling operation\n\n    this._rotationAxis = new Vector3() //axis for rotate operation\n\n    //camera state\n    this._cameraMatrixState = new Matrix4()\n    this._cameraProjectionState = new Matrix4()\n\n    this._fovState = 1\n    this._upState = new Vector3()\n    this._zoomState = 1\n    this._nearPos = 0\n    this._farPos = 0\n\n    this._gizmoMatrixState = new Matrix4()\n\n    //initial values\n    this._up0 = new Vector3()\n    this._zoom0 = 1\n    this._fov0 = 0\n    this._initialNear = 0\n    this._nearPos0 = 0\n    this._initialFar = 0\n    this._farPos0 = 0\n    this._cameraMatrixState0 = new Matrix4()\n    this._gizmoMatrixState0 = new Matrix4()\n\n    //pointers array\n    this._button = -1\n    this._touchStart = []\n    this._touchCurrent = []\n    this._input = INPUT.NONE\n\n    //two fingers touch interaction\n    this._switchSensibility = 32 //minimum movement to be performed to fire single pan start after the second finger has been released\n    this._startFingerDistance = 0 //distance between two fingers\n    this._currentFingerDistance = 0\n    this._startFingerRotation = 0 //amount of rotation performed with two fingers\n    this._currentFingerRotation = 0\n\n    //double tap\n    this._devPxRatio = 0\n    this._downValid = true\n    this._nclicks = 0\n    this._downEvents = []\n    this._clickStart = 0 //first click time\n    this._maxDownTime = 250\n    this._maxInterval = 300\n    this._posThreshold = 24\n    this._movementThreshold = 24\n\n    //cursor positions\n    this._currentCursorPosition = new Vector3()\n    this._startCursorPosition = new Vector3()\n\n    //grid\n    this._grid = null //grid to be visualized during pan operation\n    this._gridPosition = new Vector3()\n\n    //gizmos\n    this._gizmos = new Group()\n    this._curvePts = 128\n\n    //animations\n    this._timeStart = -1 //initial time\n    this._animationId = -1\n\n    //focus animation\n    this.focusAnimationTime = 500 //duration of focus animation in ms\n\n    //rotate animation\n    this._timePrev = 0 //time at which previous rotate operation has been detected\n    this._timeCurrent = 0 //time at which current rotate operation has been detected\n    this._anglePrev = 0 //angle of previous rotation\n    this._angleCurrent = 0 //angle of current rotation\n    this._cursorPosPrev = new Vector3() //cursor position when previous rotate operation has been detected\n    this._cursorPosCurr = new Vector3() //cursor position when current rotate operation has been detected\n    this._wPrev = 0 //angular velocity of the previous rotate operation\n    this._wCurr = 0 //angular velocity of the current rotate operation\n\n    //parameters\n    this.adjustNearFar = false\n    this.scaleFactor = 1.1 //zoom/distance multiplier\n    this.dampingFactor = 25\n    this.wMax = 20 //maximum angular velocity allowed\n    this.enableAnimations = true //if animations should be performed\n    this.enableGrid = false //if grid should be showed during pan operation\n    this.cursorZoom = false //if wheel zoom should be cursor centered\n    this.minFov = 5\n    this.maxFov = 90\n\n    this.enabled = true\n    this.enablePan = true\n    this.enableRotate = true\n    this.enableZoom = true\n\n    this.minDistance = 0\n    this.maxDistance = Infinity\n    this.minZoom = 0\n    this.maxZoom = Infinity\n\n    //trackball parameters\n    this.target = new Vector3(0, 0, 0)\n    this._currentTarget = new Vector3(0, 0, 0)\n\n    this._tbRadius = 1\n\n    //FSA\n    this._state = STATE.IDLE\n\n    this.setCamera(camera)\n\n    if (this.scene) {\n      this.scene.add(this._gizmos)\n    }\n\n    this._devPxRatio = window.devicePixelRatio\n\n    this.initializeMouseActions()\n\n    if (this.domElement) this.connect(this.domElement)\n\n    window.addEventListener('resize', this.onWindowResize)\n  }\n\n  //listeners\n\n  private onWindowResize = (): void => {\n    const scale = (this._gizmos.scale.x + this._gizmos.scale.y + this._gizmos.scale.z) / 3\n    if (this.camera) {\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n    }\n\n    const newRadius = this._tbRadius / scale\n    // @ts-ignore\n    const curve = new EllipseCurve(0, 0, newRadius, newRadius)\n    const points = curve.getPoints(this._curvePts)\n    const curveGeometry = new BufferGeometry().setFromPoints(points)\n\n    for (const gizmo in this._gizmos.children) {\n      const child = this._gizmos.children[gizmo] as Mesh\n      child.geometry = curveGeometry\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(_changeEvent)\n  }\n\n  private onContextMenu = (event: MouseEvent): void => {\n    if (!this.enabled) {\n      return\n    }\n\n    for (let i = 0; i < this.mouseActions.length; i++) {\n      if (this.mouseActions[i].mouse == 2) {\n        //prevent only if button 2 is actually used\n        event.preventDefault()\n        break\n      }\n    }\n  }\n\n  private onPointerCancel = (): void => {\n    this._touchStart.splice(0, this._touchStart.length)\n    this._touchCurrent.splice(0, this._touchCurrent.length)\n    this._input = INPUT.NONE\n  }\n\n  private onPointerDown = (event: PointerEvent): void => {\n    if (event.button == 0 && event.isPrimary) {\n      this._downValid = true\n      this._downEvents.push(event)\n    } else {\n      this._downValid = false\n    }\n\n    if (event.pointerType == 'touch' && this._input != INPUT.CURSOR) {\n      this._touchStart.push(event)\n      this._touchCurrent.push(event)\n\n      switch (this._input) {\n        case INPUT.NONE:\n          //singleStart\n          this._input = INPUT.ONE_FINGER\n          this.onSinglePanStart(event, 'ROTATE')\n\n          window.addEventListener('pointermove', this.onPointerMove)\n          window.addEventListener('pointerup', this.onPointerUp)\n\n          break\n\n        case INPUT.ONE_FINGER:\n        case INPUT.ONE_FINGER_SWITCHED:\n          //doubleStart\n          this._input = INPUT.TWO_FINGER\n\n          this.onRotateStart()\n          this.onPinchStart()\n          this.onDoublePanStart()\n\n          break\n\n        case INPUT.TWO_FINGER:\n          //multipleStart\n          this._input = INPUT.MULT_FINGER\n          this.onTriplePanStart()\n          break\n      }\n    } else if (event.pointerType != 'touch' && this._input == INPUT.NONE) {\n      let modifier: ModifierKey | null = null\n\n      if (event.ctrlKey || event.metaKey) {\n        modifier = 'CTRL'\n      } else if (event.shiftKey) {\n        modifier = 'SHIFT'\n      }\n\n      this._mouseOp = this.getOpFromAction(event.button, modifier)\n      if (this._mouseOp) {\n        window.addEventListener('pointermove', this.onPointerMove)\n        window.addEventListener('pointerup', this.onPointerUp)\n\n        //singleStart\n        this._input = INPUT.CURSOR\n        this._button = event.button\n        this.onSinglePanStart(event, this._mouseOp)\n      }\n    }\n  }\n\n  private onPointerMove = (event: PointerEvent): void => {\n    if (event.pointerType == 'touch' && this._input != INPUT.CURSOR) {\n      switch (this._input) {\n        case INPUT.ONE_FINGER:\n          //singleMove\n          this.updateTouchEvent(event)\n\n          this.onSinglePanMove(event, STATE.ROTATE)\n          break\n\n        case INPUT.ONE_FINGER_SWITCHED:\n          const movement = this.calculatePointersDistance(this._touchCurrent[0], event) * this._devPxRatio\n\n          if (movement >= this._switchSensibility) {\n            //singleMove\n            this._input = INPUT.ONE_FINGER\n            this.updateTouchEvent(event)\n\n            this.onSinglePanStart(event, 'ROTATE')\n            break\n          }\n\n          break\n\n        case INPUT.TWO_FINGER:\n          //rotate/pan/pinchMove\n          this.updateTouchEvent(event)\n\n          this.onRotateMove()\n          this.onPinchMove()\n          this.onDoublePanMove()\n\n          break\n\n        case INPUT.MULT_FINGER:\n          //multMove\n          this.updateTouchEvent(event)\n\n          this.onTriplePanMove()\n          break\n      }\n    } else if (event.pointerType != 'touch' && this._input == INPUT.CURSOR) {\n      let modifier: ModifierKey | null = null\n\n      if (event.ctrlKey || event.metaKey) {\n        modifier = 'CTRL'\n      } else if (event.shiftKey) {\n        modifier = 'SHIFT'\n      }\n\n      const mouseOpState = this.getOpStateFromAction(this._button, modifier)\n\n      if (mouseOpState) {\n        this.onSinglePanMove(event, mouseOpState)\n      }\n    }\n\n    //checkDistance\n    if (this._downValid) {\n      const movement =\n        this.calculatePointersDistance(this._downEvents[this._downEvents.length - 1], event) * this._devPxRatio\n      if (movement > this._movementThreshold) {\n        this._downValid = false\n      }\n    }\n  }\n\n  private onPointerUp = (event: PointerEvent): void => {\n    if (event.pointerType == 'touch' && this._input != INPUT.CURSOR) {\n      const nTouch = this._touchCurrent.length\n\n      for (let i = 0; i < nTouch; i++) {\n        if (this._touchCurrent[i].pointerId == event.pointerId) {\n          this._touchCurrent.splice(i, 1)\n          this._touchStart.splice(i, 1)\n          break\n        }\n      }\n\n      switch (this._input) {\n        case INPUT.ONE_FINGER:\n        case INPUT.ONE_FINGER_SWITCHED:\n          //singleEnd\n          window.removeEventListener('pointermove', this.onPointerMove)\n          window.removeEventListener('pointerup', this.onPointerUp)\n\n          this._input = INPUT.NONE\n          this.onSinglePanEnd()\n\n          break\n\n        case INPUT.TWO_FINGER:\n          //doubleEnd\n          this.onDoublePanEnd()\n          this.onPinchEnd()\n          this.onRotateEnd()\n\n          //switching to singleStart\n          this._input = INPUT.ONE_FINGER_SWITCHED\n\n          break\n\n        case INPUT.MULT_FINGER:\n          if (this._touchCurrent.length == 0) {\n            window.removeEventListener('pointermove', this.onPointerMove)\n            window.removeEventListener('pointerup', this.onPointerUp)\n\n            //multCancel\n            this._input = INPUT.NONE\n            this.onTriplePanEnd()\n          }\n\n          break\n      }\n    } else if (event.pointerType != 'touch' && this._input == INPUT.CURSOR) {\n      window.removeEventListener('pointermove', this.onPointerMove)\n      window.removeEventListener('pointerup', this.onPointerUp)\n\n      this._input = INPUT.NONE\n      this.onSinglePanEnd()\n      this._button = -1\n    }\n\n    if (event.isPrimary) {\n      if (this._downValid) {\n        const downTime = event.timeStamp - this._downEvents[this._downEvents.length - 1].timeStamp\n\n        if (downTime <= this._maxDownTime) {\n          if (this._nclicks == 0) {\n            //first valid click detected\n            this._nclicks = 1\n            this._clickStart = performance.now()\n          } else {\n            const clickInterval = event.timeStamp - this._clickStart\n            const movement = this.calculatePointersDistance(this._downEvents[1], this._downEvents[0]) * this._devPxRatio\n\n            if (clickInterval <= this._maxInterval && movement <= this._posThreshold) {\n              //second valid click detected\n              //fire double tap and reset values\n              this._nclicks = 0\n              this._downEvents.splice(0, this._downEvents.length)\n              this.onDoubleTap(event)\n            } else {\n              //new 'first click'\n              this._nclicks = 1\n              this._downEvents.shift()\n              this._clickStart = performance.now()\n            }\n          }\n        } else {\n          this._downValid = false\n          this._nclicks = 0\n          this._downEvents.splice(0, this._downEvents.length)\n        }\n      } else {\n        this._nclicks = 0\n        this._downEvents.splice(0, this._downEvents.length)\n      }\n    }\n  }\n\n  private onWheel = (event: WheelEvent): void => {\n    if (this.enabled && this.enableZoom && this.domElement) {\n      let modifier: ModifierKey | null = null\n\n      if (event.ctrlKey || event.metaKey) {\n        modifier = 'CTRL'\n      } else if (event.shiftKey) {\n        modifier = 'SHIFT'\n      }\n\n      const mouseOp = this.getOpFromAction('WHEEL', modifier)\n\n      if (mouseOp) {\n        event.preventDefault()\n        // @ts-ignore\n        this.dispatchEvent(_startEvent)\n\n        const notchDeltaY = 125 //distance of one notch of mouse wheel\n        let sgn = event.deltaY / notchDeltaY\n\n        let size = 1\n\n        if (sgn > 0) {\n          size = 1 / this.scaleFactor\n        } else if (sgn < 0) {\n          size = this.scaleFactor\n        }\n\n        switch (mouseOp) {\n          case 'ZOOM':\n            this.updateTbState(STATE.SCALE, true)\n\n            if (sgn > 0) {\n              size = 1 / Math.pow(this.scaleFactor, sgn)\n            } else if (sgn < 0) {\n              size = Math.pow(this.scaleFactor, -sgn)\n            }\n\n            if (this.cursorZoom && this.enablePan) {\n              let scalePoint\n\n              if (this.camera instanceof OrthographicCamera) {\n                scalePoint = this.unprojectOnTbPlane(this.camera, event.clientX, event.clientY, this.domElement)\n                  ?.applyQuaternion(this.camera.quaternion)\n                  .multiplyScalar(1 / this.camera.zoom)\n                  .add(this._gizmos.position)\n              }\n\n              if (this.camera instanceof PerspectiveCamera) {\n                scalePoint = this.unprojectOnTbPlane(this.camera, event.clientX, event.clientY, this.domElement)\n                  ?.applyQuaternion(this.camera.quaternion)\n                  .add(this._gizmos.position)\n              }\n\n              if (scalePoint !== undefined) this.applyTransformMatrix(this.applyScale(size, scalePoint))\n            } else {\n              this.applyTransformMatrix(this.applyScale(size, this._gizmos.position))\n            }\n\n            if (this._grid) {\n              this.disposeGrid()\n              this.drawGrid()\n            }\n\n            this.updateTbState(STATE.IDLE, false)\n\n            // @ts-ignore\n            this.dispatchEvent(_changeEvent)\n            // @ts-ignore\n            this.dispatchEvent(_endEvent)\n\n            break\n\n          case 'FOV':\n            if (this.camera instanceof PerspectiveCamera) {\n              this.updateTbState(STATE.FOV, true)\n\n              //Vertigo effect\n\n              //\t  fov / 2\n              //\t\t|\\\n              //\t\t| \\\n              //\t\t|  \\\n              //\tx\t|\t\\\n              //\t\t| \t \\\n              //\t\t| \t  \\\n              //\t\t| _ _ _\\\n              //\t\t\ty\n\n              //check for iOs shift shortcut\n              if (event.deltaX != 0) {\n                sgn = event.deltaX / notchDeltaY\n\n                size = 1\n\n                if (sgn > 0) {\n                  size = 1 / Math.pow(this.scaleFactor, sgn)\n                } else if (sgn < 0) {\n                  size = Math.pow(this.scaleFactor, -sgn)\n                }\n              }\n\n              this._v3_1.setFromMatrixPosition(this._cameraMatrixState)\n              const x = this._v3_1.distanceTo(this._gizmos.position)\n              let xNew = x / size //distance between camera and gizmos if scale(size, scalepoint) would be performed\n\n              //check min and max distance\n              xNew = MathUtils.clamp(xNew, this.minDistance, this.maxDistance)\n\n              const y = x * Math.tan(MathUtils.DEG2RAD * this.camera.fov * 0.5)\n\n              //calculate new fov\n              let newFov = MathUtils.RAD2DEG * (Math.atan(y / xNew) * 2)\n\n              //check min and max fov\n              if (newFov > this.maxFov) {\n                newFov = this.maxFov\n              } else if (newFov < this.minFov) {\n                newFov = this.minFov\n              }\n\n              const newDistance = y / Math.tan(MathUtils.DEG2RAD * (newFov / 2))\n              size = x / newDistance\n\n              this.setFov(newFov)\n              this.applyTransformMatrix(this.applyScale(size, this._gizmos.position, false))\n            }\n\n            if (this._grid) {\n              this.disposeGrid()\n              this.drawGrid()\n            }\n\n            this.updateTbState(STATE.IDLE, false)\n\n            // @ts-ignore\n            this.dispatchEvent(_changeEvent)\n            // @ts-ignore\n            this.dispatchEvent(_endEvent)\n\n            break\n        }\n      }\n    }\n  }\n\n  private onSinglePanStart = (event: PointerEvent, operation: Operation): void => {\n    if (this.enabled && this.domElement) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n\n      this.setCenter(event.clientX, event.clientY)\n\n      switch (operation) {\n        case 'PAN':\n          if (!this.enablePan) return\n\n          if (this._animationId != -1) {\n            cancelAnimationFrame(this._animationId)\n            this._animationId = -1\n            this._timeStart = -1\n\n            this.activateGizmos(false)\n            // @ts-ignore\n            this.dispatchEvent(_changeEvent)\n          }\n\n          if (this.camera) {\n            this.updateTbState(STATE.PAN, true)\n            const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n            if (rayDir !== undefined) {\n              this._startCursorPosition.copy(rayDir)\n            }\n            if (this.enableGrid) {\n              this.drawGrid()\n              // @ts-ignore\n              this.dispatchEvent(_changeEvent)\n            }\n          }\n\n          break\n\n        case 'ROTATE':\n          if (!this.enableRotate) return\n\n          if (this._animationId != -1) {\n            cancelAnimationFrame(this._animationId)\n            this._animationId = -1\n            this._timeStart = -1\n          }\n\n          if (this.camera) {\n            this.updateTbState(STATE.ROTATE, true)\n            const rayDir = this.unprojectOnTbSurface(this.camera, _center.x, _center.y, this.domElement, this._tbRadius)\n            if (rayDir !== undefined) {\n              this._startCursorPosition.copy(rayDir)\n            }\n            this.activateGizmos(true)\n            if (this.enableAnimations) {\n              this._timePrev = this._timeCurrent = performance.now()\n              this._angleCurrent = this._anglePrev = 0\n              this._cursorPosPrev.copy(this._startCursorPosition)\n              this._cursorPosCurr.copy(this._cursorPosPrev)\n              this._wCurr = 0\n              this._wPrev = this._wCurr\n            }\n          }\n\n          // @ts-ignore\n          this.dispatchEvent(_changeEvent)\n          break\n\n        case 'FOV':\n          if (!this.enableZoom) return\n\n          if (this.camera instanceof PerspectiveCamera) {\n            if (this._animationId != -1) {\n              cancelAnimationFrame(this._animationId)\n              this._animationId = -1\n              this._timeStart = -1\n\n              this.activateGizmos(false)\n              // @ts-ignore\n              this.dispatchEvent(_changeEvent)\n            }\n\n            this.updateTbState(STATE.FOV, true)\n            this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n            this._currentCursorPosition.copy(this._startCursorPosition)\n          }\n          break\n\n        case 'ZOOM':\n          if (!this.enableZoom) return\n\n          if (this._animationId != -1) {\n            cancelAnimationFrame(this._animationId)\n            this._animationId = -1\n            this._timeStart = -1\n\n            this.activateGizmos(false)\n            // @ts-ignore\n            this.dispatchEvent(_changeEvent)\n          }\n\n          this.updateTbState(STATE.SCALE, true)\n          this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n          this._currentCursorPosition.copy(this._startCursorPosition)\n          break\n      }\n    }\n  }\n\n  private onSinglePanMove = (event: PointerEvent, opState: Symbol): void => {\n    if (this.enabled && this.domElement) {\n      const restart = opState != this._state\n      this.setCenter(event.clientX, event.clientY)\n\n      switch (opState) {\n        case STATE.PAN:\n          if (this.enablePan && this.camera) {\n            if (restart) {\n              //switch to pan operation\n\n              // @ts-ignore\n              this.dispatchEvent(_endEvent)\n              // @ts-ignore\n              this.dispatchEvent(_startEvent)\n\n              this.updateTbState(opState, true)\n              const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n              if (rayDir !== undefined) {\n                this._startCursorPosition.copy(rayDir)\n              }\n              if (this.enableGrid) {\n                this.drawGrid()\n              }\n\n              this.activateGizmos(false)\n            } else {\n              //continue with pan operation\n              const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n              if (rayDir !== undefined) {\n                this._currentCursorPosition.copy(rayDir)\n              }\n              this.applyTransformMatrix(this.pan(this._startCursorPosition, this._currentCursorPosition))\n            }\n          }\n\n          break\n\n        case STATE.ROTATE:\n          if (this.enableRotate && this.camera) {\n            if (restart) {\n              //switch to rotate operation\n\n              // @ts-ignore\n              this.dispatchEvent(_endEvent)\n              // @ts-ignore\n              this.dispatchEvent(_startEvent)\n\n              this.updateTbState(opState, true)\n              const rayDir = this.unprojectOnTbSurface(\n                this.camera,\n                _center.x,\n                _center.y,\n                this.domElement,\n                this._tbRadius,\n              )\n              if (rayDir !== undefined) {\n                this._startCursorPosition.copy(rayDir)\n              }\n\n              if (this.enableGrid) {\n                this.disposeGrid()\n              }\n\n              this.activateGizmos(true)\n            } else {\n              //continue with rotate operation\n              const rayDir = this.unprojectOnTbSurface(\n                this.camera,\n                _center.x,\n                _center.y,\n                this.domElement,\n                this._tbRadius,\n              )\n              if (rayDir !== undefined) {\n                this._currentCursorPosition.copy(rayDir)\n              }\n\n              const distance = this._startCursorPosition.distanceTo(this._currentCursorPosition)\n              const angle = this._startCursorPosition.angleTo(this._currentCursorPosition)\n              const amount = Math.max(distance / this._tbRadius, angle) //effective rotation angle\n\n              this.applyTransformMatrix(\n                this.rotate(this.calculateRotationAxis(this._startCursorPosition, this._currentCursorPosition), amount),\n              )\n\n              if (this.enableAnimations) {\n                this._timePrev = this._timeCurrent\n                this._timeCurrent = performance.now()\n                this._anglePrev = this._angleCurrent\n                this._angleCurrent = amount\n                this._cursorPosPrev.copy(this._cursorPosCurr)\n                this._cursorPosCurr.copy(this._currentCursorPosition)\n                this._wPrev = this._wCurr\n                this._wCurr = this.calculateAngularSpeed(\n                  this._anglePrev,\n                  this._angleCurrent,\n                  this._timePrev,\n                  this._timeCurrent,\n                )\n              }\n            }\n          }\n\n          break\n\n        case STATE.SCALE:\n          if (this.enableZoom) {\n            if (restart) {\n              //switch to zoom operation\n\n              // @ts-ignore\n              this.dispatchEvent(_endEvent)\n              // @ts-ignore\n              this.dispatchEvent(_startEvent)\n\n              this.updateTbState(opState, true)\n              this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n              this._currentCursorPosition.copy(this._startCursorPosition)\n\n              if (this.enableGrid) {\n                this.disposeGrid()\n              }\n\n              this.activateGizmos(false)\n            } else {\n              //continue with zoom operation\n              const screenNotches = 8 //how many wheel notches corresponds to a full screen pan\n              this._currentCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n\n              const movement = this._currentCursorPosition.y - this._startCursorPosition.y\n\n              let size = 1\n\n              if (movement < 0) {\n                size = 1 / Math.pow(this.scaleFactor, -movement * screenNotches)\n              } else if (movement > 0) {\n                size = Math.pow(this.scaleFactor, movement * screenNotches)\n              }\n\n              this.applyTransformMatrix(this.applyScale(size, this._gizmos.position))\n            }\n          }\n\n          break\n\n        case STATE.FOV:\n          if (this.enableZoom && this.camera instanceof PerspectiveCamera) {\n            if (restart) {\n              //switch to fov operation\n\n              // @ts-ignore\n              this.dispatchEvent(_endEvent)\n              // @ts-ignore\n              this.dispatchEvent(_startEvent)\n\n              this.updateTbState(opState, true)\n              this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n              this._currentCursorPosition.copy(this._startCursorPosition)\n\n              if (this.enableGrid) {\n                this.disposeGrid()\n              }\n\n              this.activateGizmos(false)\n            } else {\n              //continue with fov operation\n              const screenNotches = 8 //how many wheel notches corresponds to a full screen pan\n              this._currentCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n\n              const movement = this._currentCursorPosition.y - this._startCursorPosition.y\n\n              let size = 1\n\n              if (movement < 0) {\n                size = 1 / Math.pow(this.scaleFactor, -movement * screenNotches)\n              } else if (movement > 0) {\n                size = Math.pow(this.scaleFactor, movement * screenNotches)\n              }\n\n              this._v3_1.setFromMatrixPosition(this._cameraMatrixState)\n              const x = this._v3_1.distanceTo(this._gizmos.position)\n              let xNew = x / size //distance between camera and gizmos if scale(size, scalepoint) would be performed\n\n              //check min and max distance\n              xNew = MathUtils.clamp(xNew, this.minDistance, this.maxDistance)\n\n              const y = x * Math.tan(MathUtils.DEG2RAD * this._fovState * 0.5)\n\n              //calculate new fov\n              let newFov = MathUtils.RAD2DEG * (Math.atan(y / xNew) * 2)\n\n              //check min and max fov\n              newFov = MathUtils.clamp(newFov, this.minFov, this.maxFov)\n\n              const newDistance = y / Math.tan(MathUtils.DEG2RAD * (newFov / 2))\n              size = x / newDistance\n              this._v3_2.setFromMatrixPosition(this._gizmoMatrixState)\n\n              this.setFov(newFov)\n              this.applyTransformMatrix(this.applyScale(size, this._v3_2, false))\n\n              //adjusting distance\n              const direction = this._gizmos.position\n                .clone()\n                .sub(this.camera.position)\n                .normalize()\n                .multiplyScalar(newDistance / x)\n              this._m4_1.makeTranslation(direction.x, direction.y, direction.z)\n            }\n          }\n\n          break\n      }\n\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  private onSinglePanEnd = (): void => {\n    if (this._state == STATE.ROTATE) {\n      if (!this.enableRotate) {\n        return\n      }\n\n      if (this.enableAnimations) {\n        //perform rotation animation\n        const deltaTime = performance.now() - this._timeCurrent\n        if (deltaTime < 120) {\n          const w = Math.abs((this._wPrev + this._wCurr) / 2)\n\n          const self = this\n          this._animationId = window.requestAnimationFrame(function (t) {\n            self.updateTbState(STATE.ANIMATION_ROTATE, true)\n            const rotationAxis = self.calculateRotationAxis(self._cursorPosPrev, self._cursorPosCurr)\n\n            self.onRotationAnim(t, rotationAxis, Math.min(w, self.wMax))\n          })\n        } else {\n          //cursor has been standing still for over 120 ms since last movement\n          this.updateTbState(STATE.IDLE, false)\n          this.activateGizmos(false)\n          // @ts-ignore\n          this.dispatchEvent(_changeEvent)\n        }\n      } else {\n        this.updateTbState(STATE.IDLE, false)\n        this.activateGizmos(false)\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n      }\n    } else if (this._state == STATE.PAN || this._state == STATE.IDLE) {\n      this.updateTbState(STATE.IDLE, false)\n\n      if (this.enableGrid) {\n        this.disposeGrid()\n      }\n\n      this.activateGizmos(false)\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n  }\n\n  private onDoubleTap = (event: PointerEvent): void => {\n    if (this.enabled && this.enablePan && this.scene && this.camera && this.domElement) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n\n      this.setCenter(event.clientX, event.clientY)\n      const hitP = this.unprojectOnObj(this.getCursorNDC(_center.x, _center.y, this.domElement), this.camera)\n\n      if (hitP && this.enableAnimations) {\n        const self = this\n        if (this._animationId != -1) {\n          window.cancelAnimationFrame(this._animationId)\n        }\n\n        this._timeStart = -1\n        this._animationId = window.requestAnimationFrame(function (t) {\n          self.updateTbState(STATE.ANIMATION_FOCUS, true)\n          self.onFocusAnim(t, hitP, self._cameraMatrixState, self._gizmoMatrixState)\n        })\n      } else if (hitP && !this.enableAnimations) {\n        this.updateTbState(STATE.FOCUS, true)\n        this.focus(hitP, this.scaleFactor)\n        this.updateTbState(STATE.IDLE, false)\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n      }\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n  }\n\n  private onDoublePanStart = (): void => {\n    if (this.enabled && this.enablePan && this.camera && this.domElement) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n\n      this.updateTbState(STATE.PAN, true)\n\n      this.setCenter(\n        (this._touchCurrent[0].clientX + this._touchCurrent[1].clientX) / 2,\n        (this._touchCurrent[0].clientY + this._touchCurrent[1].clientY) / 2,\n      )\n\n      const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement, true)\n      if (rayDir !== undefined) {\n        this._startCursorPosition.copy(rayDir)\n      }\n      this._currentCursorPosition.copy(this._startCursorPosition)\n\n      this.activateGizmos(false)\n    }\n  }\n\n  private onDoublePanMove = (): void => {\n    if (this.enabled && this.enablePan && this.camera && this.domElement) {\n      this.setCenter(\n        (this._touchCurrent[0].clientX + this._touchCurrent[1].clientX) / 2,\n        (this._touchCurrent[0].clientY + this._touchCurrent[1].clientY) / 2,\n      )\n\n      if (this._state != STATE.PAN) {\n        this.updateTbState(STATE.PAN, true)\n        this._startCursorPosition.copy(this._currentCursorPosition)\n      }\n\n      const rayDir = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement, true)\n      if (rayDir !== undefined) this._currentCursorPosition.copy(rayDir)\n      this.applyTransformMatrix(this.pan(this._startCursorPosition, this._currentCursorPosition, true))\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  private onDoublePanEnd = (): void => {\n    this.updateTbState(STATE.IDLE, false)\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n  }\n\n  private onRotateStart = (): void => {\n    if (this.enabled && this.enableRotate) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n\n      this.updateTbState(STATE.ZROTATE, true)\n\n      //this._startFingerRotation = event.rotation;\n\n      this._startFingerRotation =\n        this.getAngle(this._touchCurrent[1], this._touchCurrent[0]) +\n        this.getAngle(this._touchStart[1], this._touchStart[0])\n      this._currentFingerRotation = this._startFingerRotation\n\n      this.camera?.getWorldDirection(this._rotationAxis) //rotation axis\n\n      if (!this.enablePan && !this.enableZoom) {\n        this.activateGizmos(true)\n      }\n    }\n  }\n\n  private onRotateMove = (): void => {\n    if (this.enabled && this.enableRotate && this.camera && this.domElement) {\n      this.setCenter(\n        (this._touchCurrent[0].clientX + this._touchCurrent[1].clientX) / 2,\n        (this._touchCurrent[0].clientY + this._touchCurrent[1].clientY) / 2,\n      )\n      let rotationPoint\n\n      if (this._state != STATE.ZROTATE) {\n        this.updateTbState(STATE.ZROTATE, true)\n        this._startFingerRotation = this._currentFingerRotation\n      }\n\n      //this._currentFingerRotation = event.rotation;\n      this._currentFingerRotation =\n        this.getAngle(this._touchCurrent[1], this._touchCurrent[0]) +\n        this.getAngle(this._touchStart[1], this._touchStart[0])\n\n      if (!this.enablePan) {\n        rotationPoint = new Vector3().setFromMatrixPosition(this._gizmoMatrixState)\n      } else if (this.camera) {\n        this._v3_2.setFromMatrixPosition(this._gizmoMatrixState)\n        rotationPoint = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n          ?.applyQuaternion(this.camera.quaternion)\n          .multiplyScalar(1 / this.camera.zoom)\n          .add(this._v3_2)\n      }\n\n      const amount = MathUtils.DEG2RAD * (this._startFingerRotation - this._currentFingerRotation)\n\n      if (rotationPoint !== undefined) {\n        this.applyTransformMatrix(this.zRotate(rotationPoint, amount))\n      }\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  private onRotateEnd = (): void => {\n    this.updateTbState(STATE.IDLE, false)\n    this.activateGizmos(false)\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n  }\n\n  private onPinchStart = (): void => {\n    if (this.enabled && this.enableZoom) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n      this.updateTbState(STATE.SCALE, true)\n\n      this._startFingerDistance = this.calculatePointersDistance(this._touchCurrent[0], this._touchCurrent[1])\n      this._currentFingerDistance = this._startFingerDistance\n\n      this.activateGizmos(false)\n    }\n  }\n\n  private onPinchMove = (): void => {\n    if (this.enabled && this.enableZoom && this.domElement) {\n      this.setCenter(\n        (this._touchCurrent[0].clientX + this._touchCurrent[1].clientX) / 2,\n        (this._touchCurrent[0].clientY + this._touchCurrent[1].clientY) / 2,\n      )\n      const minDistance = 12 //minimum distance between fingers (in css pixels)\n\n      if (this._state != STATE.SCALE) {\n        this._startFingerDistance = this._currentFingerDistance\n        this.updateTbState(STATE.SCALE, true)\n      }\n\n      this._currentFingerDistance = Math.max(\n        this.calculatePointersDistance(this._touchCurrent[0], this._touchCurrent[1]),\n        minDistance * this._devPxRatio,\n      )\n      const amount = this._currentFingerDistance / this._startFingerDistance\n\n      let scalePoint\n\n      if (!this.enablePan) {\n        scalePoint = this._gizmos.position\n      } else {\n        if (this.camera instanceof OrthographicCamera) {\n          scalePoint = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n            ?.applyQuaternion(this.camera.quaternion)\n            .multiplyScalar(1 / this.camera.zoom)\n            .add(this._gizmos.position)\n        } else if (this.camera instanceof PerspectiveCamera) {\n          scalePoint = this.unprojectOnTbPlane(this.camera, _center.x, _center.y, this.domElement)\n            ?.applyQuaternion(this.camera.quaternion)\n            .add(this._gizmos.position)\n        }\n      }\n\n      if (scalePoint !== undefined) {\n        this.applyTransformMatrix(this.applyScale(amount, scalePoint))\n      }\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  private onPinchEnd = (): void => {\n    this.updateTbState(STATE.IDLE, false)\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n  }\n\n  private onTriplePanStart = (): void => {\n    if (this.enabled && this.enableZoom && this.domElement) {\n      // @ts-ignore\n      this.dispatchEvent(_startEvent)\n\n      this.updateTbState(STATE.SCALE, true)\n\n      //const center = event.center;\n      let clientX = 0\n      let clientY = 0\n      const nFingers = this._touchCurrent.length\n\n      for (let i = 0; i < nFingers; i++) {\n        clientX += this._touchCurrent[i].clientX\n        clientY += this._touchCurrent[i].clientY\n      }\n\n      this.setCenter(clientX / nFingers, clientY / nFingers)\n\n      this._startCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n      this._currentCursorPosition.copy(this._startCursorPosition)\n    }\n  }\n\n  private onTriplePanMove = (): void => {\n    if (this.enabled && this.enableZoom && this.camera && this.domElement) {\n      //\t  fov / 2\n      //\t\t|\\\n      //\t\t| \\\n      //\t\t|  \\\n      //\tx\t|\t\\\n      //\t\t| \t \\\n      //\t\t| \t  \\\n      //\t\t| _ _ _\\\n      //\t\t\ty\n\n      //const center = event.center;\n      let clientX = 0\n      let clientY = 0\n      const nFingers = this._touchCurrent.length\n\n      for (let i = 0; i < nFingers; i++) {\n        clientX += this._touchCurrent[i].clientX\n        clientY += this._touchCurrent[i].clientY\n      }\n\n      this.setCenter(clientX / nFingers, clientY / nFingers)\n\n      const screenNotches = 8 //how many wheel notches corresponds to a full screen pan\n      this._currentCursorPosition.setY(this.getCursorNDC(_center.x, _center.y, this.domElement).y * 0.5)\n\n      const movement = this._currentCursorPosition.y - this._startCursorPosition.y\n\n      let size = 1\n\n      if (movement < 0) {\n        size = 1 / Math.pow(this.scaleFactor, -movement * screenNotches)\n      } else if (movement > 0) {\n        size = Math.pow(this.scaleFactor, movement * screenNotches)\n      }\n\n      this._v3_1.setFromMatrixPosition(this._cameraMatrixState)\n      const x = this._v3_1.distanceTo(this._gizmos.position)\n      let xNew = x / size //distance between camera and gizmos if scale(size, scalepoint) would be performed\n\n      //check min and max distance\n      xNew = MathUtils.clamp(xNew, this.minDistance, this.maxDistance)\n\n      const y = x * Math.tan(MathUtils.DEG2RAD * this._fovState * 0.5)\n\n      //calculate new fov\n      let newFov = MathUtils.RAD2DEG * (Math.atan(y / xNew) * 2)\n\n      //check min and max fov\n      newFov = MathUtils.clamp(newFov, this.minFov, this.maxFov)\n\n      const newDistance = y / Math.tan(MathUtils.DEG2RAD * (newFov / 2))\n      size = x / newDistance\n      this._v3_2.setFromMatrixPosition(this._gizmoMatrixState)\n\n      this.setFov(newFov)\n      this.applyTransformMatrix(this.applyScale(size, this._v3_2, false))\n\n      //adjusting distance\n      const direction = this._gizmos.position\n        .clone()\n        .sub(this.camera.position)\n        .normalize()\n        .multiplyScalar(newDistance / x)\n      this._m4_1.makeTranslation(direction.x, direction.y, direction.z)\n\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  private onTriplePanEnd = (): void => {\n    this.updateTbState(STATE.IDLE, false)\n    // @ts-ignore\n    this.dispatchEvent(_endEvent)\n    //this.dispatchEvent( _changeEvent );\n  }\n\n  /**\n   * Set _center's x/y coordinates\n   * @param {Number} clientX\n   * @param {Number} clientY\n   */\n  private setCenter = (clientX: number, clientY: number): void => {\n    _center.x = clientX\n    _center.y = clientY\n  }\n\n  /**\n   * Set default mouse actions\n   */\n  private initializeMouseActions = (): void => {\n    this.setMouseAction('PAN', 0, 'CTRL')\n    this.setMouseAction('PAN', 2)\n\n    this.setMouseAction('ROTATE', 0)\n\n    this.setMouseAction('ZOOM', 'WHEEL')\n    this.setMouseAction('ZOOM', 1)\n\n    this.setMouseAction('FOV', 'WHEEL', 'SHIFT')\n    this.setMouseAction('FOV', 1, 'SHIFT')\n  }\n\n  /**\n   * Set a new mouse action by specifying the operation to be performed and a mouse/key combination. In case of conflict, replaces the existing one\n   * @param {String} operation The operation to be performed ('PAN', 'ROTATE', 'ZOOM', 'FOV)\n   * @param {*} mouse A mouse button (0, 1, 2) or 'WHEEL' for wheel notches\n   * @param {*} key The keyboard modifier ('CTRL', 'SHIFT') or null if key is not needed\n   * @returns {Boolean} True if the mouse action has been successfully added, false otherwise\n   */\n  private setMouseAction = (operation: Operation, mouse: MouseButtonType, key: ModifierKey | null = null): boolean => {\n    const operationInput = ['PAN', 'ROTATE', 'ZOOM', 'FOV']\n    const mouseInput = [0, 1, 2, 'WHEEL']\n    const keyInput = ['CTRL', 'SHIFT', null]\n    let state\n\n    if (!operationInput.includes(operation) || !mouseInput.includes(mouse) || !keyInput.includes(key)) {\n      //invalid parameters\n      return false\n    }\n\n    if (mouse == 'WHEEL') {\n      if (operation != 'ZOOM' && operation != 'FOV') {\n        //cannot associate 2D operation to 1D input\n        return false\n      }\n    }\n\n    switch (operation) {\n      case 'PAN':\n        state = STATE.PAN\n        break\n\n      case 'ROTATE':\n        state = STATE.ROTATE\n        break\n\n      case 'ZOOM':\n        state = STATE.SCALE\n        break\n\n      case 'FOV':\n        state = STATE.FOV\n        break\n    }\n\n    const action = {\n      operation: operation,\n      mouse: mouse,\n      key: key,\n      state: state,\n    }\n\n    for (let i = 0; i < this.mouseActions.length; i++) {\n      if (this.mouseActions[i].mouse == action.mouse && this.mouseActions[i].key == action.key) {\n        this.mouseActions.splice(i, 1, action)\n        return true\n      }\n    }\n\n    this.mouseActions.push(action)\n    return true\n  }\n\n  /**\n   * Return the operation associated to a mouse/keyboard combination\n   * @param {*} mouse A mouse button (0, 1, 2) or 'WHEEL' for wheel notches\n   * @param {*} key The keyboard modifier ('CTRL', 'SHIFT') or null if key is not needed\n   * @returns The operation if it has been found, null otherwise\n   */\n  private getOpFromAction = (mouse: MouseButtonType, key: ModifierKey | null): Operation | null => {\n    let action\n\n    for (let i = 0; i < this.mouseActions.length; i++) {\n      action = this.mouseActions[i]\n      if (action.mouse == mouse && action.key == key) {\n        return action.operation\n      }\n    }\n\n    if (key) {\n      for (let i = 0; i < this.mouseActions.length; i++) {\n        action = this.mouseActions[i]\n        if (action.mouse == mouse && action.key == null) {\n          return action.operation\n        }\n      }\n    }\n\n    return null\n  }\n\n  /**\n   * Get the operation associated to mouse and key combination and returns the corresponding FSA state\n   * @param {Number} mouse Mouse button\n   * @param {String} key Keyboard modifier\n   * @returns The FSA state obtained from the operation associated to mouse/keyboard combination\n   */\n  private getOpStateFromAction = (mouse: MouseButtonType, key: ModifierKey | null): Symbol | null => {\n    let action\n\n    for (let i = 0; i < this.mouseActions.length; i++) {\n      action = this.mouseActions[i]\n      if (action.mouse == mouse && action.key == key) {\n        return action.state\n      }\n    }\n\n    if (key) {\n      for (let i = 0; i < this.mouseActions.length; i++) {\n        action = this.mouseActions[i]\n        if (action.mouse == mouse && action.key == null) {\n          return action.state\n        }\n      }\n    }\n\n    return null\n  }\n\n  /**\n   * Calculate the angle between two pointers\n   * @param {PointerEvent} p1\n   * @param {PointerEvent} p2\n   * @returns {Number} The angle between two pointers in degrees\n   */\n  private getAngle = (p1: PointerEvent, p2: PointerEvent): number => {\n    return (Math.atan2(p2.clientY - p1.clientY, p2.clientX - p1.clientX) * 180) / Math.PI\n  }\n\n  /**\n   * Update a PointerEvent inside current pointerevents array\n   * @param {PointerEvent} event\n   */\n  private updateTouchEvent = (event: PointerEvent): void => {\n    for (let i = 0; i < this._touchCurrent.length; i++) {\n      if (this._touchCurrent[i].pointerId == event.pointerId) {\n        this._touchCurrent.splice(i, 1, event)\n        break\n      }\n    }\n  }\n\n  /**\n   * Apply a transformation matrix, to the camera and gizmos\n   * @param {Object} transformation Object containing matrices to apply to camera and gizmos\n   */\n  private applyTransformMatrix(transformation: Transformation | undefined): void {\n    if (transformation?.camera && this.camera) {\n      this._m4_1.copy(this._cameraMatrixState).premultiply(transformation.camera)\n      this._m4_1.decompose(this.camera.position, this.camera.quaternion, this.camera.scale)\n      this.camera.updateMatrix()\n\n      //update camera up vector\n      if (this._state == STATE.ROTATE || this._state == STATE.ZROTATE || this._state == STATE.ANIMATION_ROTATE) {\n        this.camera.up.copy(this._upState).applyQuaternion(this.camera.quaternion)\n      }\n    }\n\n    if (transformation?.gizmos) {\n      this._m4_1.copy(this._gizmoMatrixState).premultiply(transformation.gizmos)\n      this._m4_1.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n      this._gizmos.updateMatrix()\n    }\n\n    if (\n      (this._state == STATE.SCALE || this._state == STATE.FOCUS || this._state == STATE.ANIMATION_FOCUS) &&\n      this.camera\n    ) {\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n\n      if (this.adjustNearFar) {\n        const cameraDistance = this.camera.position.distanceTo(this._gizmos.position)\n\n        const bb = new Box3()\n        bb.setFromObject(this._gizmos)\n        const sphere = new Sphere()\n        bb.getBoundingSphere(sphere)\n\n        const adjustedNearPosition = Math.max(this._nearPos0, sphere.radius + sphere.center.length())\n        const regularNearPosition = cameraDistance - this._initialNear\n\n        const minNearPos = Math.min(adjustedNearPosition, regularNearPosition)\n        this.camera.near = cameraDistance - minNearPos\n\n        const adjustedFarPosition = Math.min(this._farPos0, -sphere.radius + sphere.center.length())\n        const regularFarPosition = cameraDistance - this._initialFar\n\n        const minFarPos = Math.min(adjustedFarPosition, regularFarPosition)\n        this.camera.far = cameraDistance - minFarPos\n\n        this.camera.updateProjectionMatrix()\n      } else {\n        let update = false\n\n        if (this.camera.near != this._initialNear) {\n          this.camera.near = this._initialNear\n          update = true\n        }\n\n        if (this.camera.far != this._initialFar) {\n          this.camera.far = this._initialFar\n          update = true\n        }\n\n        if (update) {\n          this.camera.updateProjectionMatrix()\n        }\n      }\n    }\n  }\n\n  /**\n   * Calculate the angular speed\n   * @param {Number} p0 Position at t0\n   * @param {Number} p1 Position at t1\n   * @param {Number} t0 Initial time in milliseconds\n   * @param {Number} t1 Ending time in milliseconds\n   */\n  private calculateAngularSpeed = (p0: number, p1: number, t0: number, t1: number): number => {\n    const s = p1 - p0\n    const t = (t1 - t0) / 1000\n    if (t == 0) {\n      return 0\n    }\n\n    return s / t\n  }\n\n  /**\n   * Calculate the distance between two pointers\n   * @param {PointerEvent} p0 The first pointer\n   * @param {PointerEvent} p1 The second pointer\n   * @returns {number} The distance between the two pointers\n   */\n  private calculatePointersDistance = (p0: PointerEvent, p1: PointerEvent): number => {\n    return Math.sqrt(Math.pow(p1.clientX - p0.clientX, 2) + Math.pow(p1.clientY - p0.clientY, 2))\n  }\n\n  /**\n   * Calculate the rotation axis as the vector perpendicular between two vectors\n   * @param {Vector3} vec1 The first vector\n   * @param {Vector3} vec2 The second vector\n   * @returns {Vector3} The normalized rotation axis\n   */\n  private calculateRotationAxis = (vec1: Vector3, vec2: Vector3): Vector3 => {\n    this._rotationMatrix.extractRotation(this._cameraMatrixState)\n    this._quat.setFromRotationMatrix(this._rotationMatrix)\n\n    this._rotationAxis.crossVectors(vec1, vec2).applyQuaternion(this._quat)\n    return this._rotationAxis.normalize().clone()\n  }\n\n  /**\n   * Calculate the trackball radius so that gizmo's diamater will be 2/3 of the minimum side of the camera frustum\n   * @param {Camera} camera\n   * @returns {Number} The trackball radius\n   */\n  private calculateTbRadius = (camera: Camera): number | undefined => {\n    const factor = 0.67\n    const distance = camera.position.distanceTo(this._gizmos.position)\n\n    if (camera instanceof PerspectiveCamera) {\n      const halfFovV = MathUtils.DEG2RAD * camera.fov * 0.5 //vertical fov/2 in radians\n      const halfFovH = Math.atan(camera.aspect * Math.tan(halfFovV)) //horizontal fov/2 in radians\n      return Math.tan(Math.min(halfFovV, halfFovH)) * distance * factor\n    } else if (camera instanceof OrthographicCamera) {\n      return Math.min(camera.top, camera.right) * factor\n    }\n  }\n\n  /**\n   * Focus operation consist of positioning the point of interest in front of the camera and a slightly zoom in\n   * @param {Vector3} point The point of interest\n   * @param {Number} size Scale factor\n   * @param {Number} amount Amount of operation to be completed (used for focus animations, default is complete full operation)\n   */\n  private focus = (point: Vector3, size: number, amount = 1): void => {\n    if (this.camera) {\n      const focusPoint = point.clone()\n\n      //move center of camera (along with gizmos) towards point of interest\n      focusPoint.sub(this._gizmos.position).multiplyScalar(amount)\n      this._translationMatrix.makeTranslation(focusPoint.x, focusPoint.y, focusPoint.z)\n\n      const gizmoStateTemp = this._gizmoMatrixState.clone()\n      this._gizmoMatrixState.premultiply(this._translationMatrix)\n      this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n\n      const cameraStateTemp = this._cameraMatrixState.clone()\n      this._cameraMatrixState.premultiply(this._translationMatrix)\n      this._cameraMatrixState.decompose(this.camera.position, this.camera.quaternion, this.camera.scale)\n\n      //apply zoom\n      if (this.enableZoom) {\n        this.applyTransformMatrix(this.applyScale(size, this._gizmos.position))\n      }\n\n      this._gizmoMatrixState.copy(gizmoStateTemp)\n      this._cameraMatrixState.copy(cameraStateTemp)\n    }\n  }\n\n  /**\n   * Draw a grid and add it to the scene\n   */\n  private drawGrid = (): void => {\n    if (this.scene) {\n      const color = 0x888888\n      const multiplier = 3\n      let size, divisions, maxLength, tick\n\n      if (this.camera instanceof OrthographicCamera) {\n        const width = this.camera.right - this.camera.left\n        const height = this.camera.bottom - this.camera.top\n\n        maxLength = Math.max(width, height)\n        tick = maxLength / 20\n\n        size = (maxLength / this.camera.zoom) * multiplier\n        divisions = (size / tick) * this.camera.zoom\n      } else if (this.camera instanceof PerspectiveCamera) {\n        const distance = this.camera.position.distanceTo(this._gizmos.position)\n        const halfFovV = MathUtils.DEG2RAD * this.camera.fov * 0.5\n        const halfFovH = Math.atan(this.camera.aspect * Math.tan(halfFovV))\n\n        maxLength = Math.tan(Math.max(halfFovV, halfFovH)) * distance * 2\n        tick = maxLength / 20\n\n        size = maxLength * multiplier\n        divisions = size / tick\n      }\n\n      if (this._grid == null && this.camera) {\n        this._grid = new GridHelper(size, divisions, color, color)\n        this._grid.position.copy(this._gizmos.position)\n        this._gridPosition.copy(this._grid.position)\n        this._grid.quaternion.copy(this.camera.quaternion)\n        this._grid.rotateX(Math.PI * 0.5)\n\n        this.scene.add(this._grid)\n      }\n    }\n  }\n\n  public connect = (domElement: HTMLElement): void => {\n    // https://github.com/mrdoob/three.js/issues/20575\n\n    if ((domElement as any) === document) {\n      console.error(\n        'THREE.ArcballControls: \"document\" should not be used as the target \"domElement\". Please use \"renderer.domElement\" instead.',\n      )\n    }\n    this.domElement = domElement\n    // disables touch scroll\n    // touch-action needs to be defined for pointer events to work on mobile\n    // https://stackoverflow.com/a/48254578\n    this.domElement.style.touchAction = 'none'\n    this.domElement.addEventListener('contextmenu', this.onContextMenu)\n    this.domElement.addEventListener('pointerdown', this.onPointerDown)\n    this.domElement.addEventListener('pointercancel', this.onPointerCancel)\n    this.domElement.addEventListener('wheel', this.onWheel)\n  }\n\n  /**\n   * Remove all listeners, stop animations and clean scene\n   */\n  public dispose = (): void => {\n    if (this._animationId != -1) {\n      window.cancelAnimationFrame(this._animationId)\n    }\n\n    this.domElement?.removeEventListener('pointerdown', this.onPointerDown)\n    this.domElement?.removeEventListener('pointercancel', this.onPointerCancel)\n    this.domElement?.removeEventListener('wheel', this.onWheel)\n    this.domElement?.removeEventListener('contextmenu', this.onContextMenu)\n\n    window.removeEventListener('pointermove', this.onPointerMove)\n    window.removeEventListener('pointerup', this.onPointerUp)\n\n    window.removeEventListener('resize', this.onWindowResize)\n\n    this.scene?.remove(this._gizmos)\n    this.disposeGrid()\n  }\n\n  /**\n   * remove the grid from the scene\n   */\n  private disposeGrid = (): void => {\n    if (this._grid && this.scene) {\n      this.scene.remove(this._grid)\n      this._grid = null\n    }\n  }\n\n  /**\n   * Compute the easing out cubic function for ease out effect in animation\n   * @param {Number} t The absolute progress of the animation in the bound of 0 (beginning of the) and 1 (ending of animation)\n   * @returns {Number} Result of easing out cubic at time t\n   */\n  private easeOutCubic = (t: number): number => {\n    return 1 - Math.pow(1 - t, 3)\n  }\n\n  /**\n   * Make rotation gizmos more or less visible\n   * @param {Boolean} isActive If true, make gizmos more visible\n   */\n  private activateGizmos = (isActive: boolean): void => {\n    for (const gizmo of this._gizmos.children) {\n      ;(gizmo as Mesh<BufferGeometry, Material>).material.setValues({ opacity: isActive ? 1 : 0.6 })\n    }\n  }\n\n  /**\n   * Calculate the cursor position in NDC\n   * @param {number} x Cursor horizontal coordinate within the canvas\n   * @param {number} y Cursor vertical coordinate within the canvas\n   * @param {HTMLElement} canvas The canvas where the renderer draws its output\n   * @returns {Vector2} Cursor normalized position inside the canvas\n   */\n  private getCursorNDC = (cursorX: number, cursorY: number, canvas: HTMLElement): Vector2 => {\n    const canvasRect = canvas.getBoundingClientRect()\n    this._v2_1.setX(((cursorX - canvasRect.left) / canvasRect.width) * 2 - 1)\n    this._v2_1.setY(((canvasRect.bottom - cursorY) / canvasRect.height) * 2 - 1)\n    return this._v2_1.clone()\n  }\n\n  /**\n   * Calculate the cursor position inside the canvas x/y coordinates with the origin being in the center of the canvas\n   * @param {Number} x Cursor horizontal coordinate within the canvas\n   * @param {Number} y Cursor vertical coordinate within the canvas\n   * @param {HTMLElement} canvas The canvas where the renderer draws its output\n   * @returns {Vector2} Cursor position inside the canvas\n   */\n  private getCursorPosition = (cursorX: number, cursorY: number, canvas: HTMLElement): Vector2 => {\n    this._v2_1.copy(this.getCursorNDC(cursorX, cursorY, canvas))\n    if (this.camera instanceof OrthographicCamera) {\n      this._v2_1.x *= (this.camera.right - this.camera.left) * 0.5\n      this._v2_1.y *= (this.camera.top - this.camera.bottom) * 0.5\n    }\n    return this._v2_1.clone()\n  }\n\n  /**\n   * Set the camera to be controlled\n   * @param {Camera} camera The virtual camera to be controlled\n   */\n  private setCamera = (camera: Camera | null): void => {\n    if (camera) {\n      camera.lookAt(this.target)\n      camera.updateMatrix()\n\n      //setting state\n      if (camera instanceof PerspectiveCamera) {\n        this._fov0 = camera.fov\n        this._fovState = camera.fov\n      }\n\n      this._cameraMatrixState0.copy(camera.matrix)\n      this._cameraMatrixState.copy(this._cameraMatrixState0)\n      this._cameraProjectionState.copy(camera.projectionMatrix)\n      this._zoom0 = camera.zoom\n      this._zoomState = this._zoom0\n\n      this._initialNear = camera.near\n      this._nearPos0 = camera.position.distanceTo(this.target) - camera.near\n      this._nearPos = this._initialNear\n\n      this._initialFar = camera.far\n      this._farPos0 = camera.position.distanceTo(this.target) - camera.far\n      this._farPos = this._initialFar\n\n      this._up0.copy(camera.up)\n      this._upState.copy(camera.up)\n\n      this.camera = camera\n\n      this.camera.updateProjectionMatrix()\n\n      //making gizmos\n      const tbRadius = this.calculateTbRadius(camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n      this.makeGizmos(this.target, this._tbRadius)\n    }\n  }\n\n  /**\n   * Set gizmos visibility\n   * @param {Boolean} value Value of gizmos visibility\n   */\n  public setGizmosVisible(value: boolean): void {\n    this._gizmos.visible = value\n    // @ts-ignore\n    this.dispatchEvent(_changeEvent)\n  }\n\n  /**\n   * Creates the rotation gizmos matching trackball center and radius\n   * @param {Vector3} tbCenter The trackball center\n   * @param {number} tbRadius The trackball radius\n   */\n  private makeGizmos = (tbCenter: Vector3, tbRadius: number): void => {\n    // @ts-ignore\n    const curve = new EllipseCurve(0, 0, tbRadius, tbRadius)\n    const points = curve.getPoints(this._curvePts)\n\n    //geometry\n    const curveGeometry = new BufferGeometry().setFromPoints(points)\n\n    //material\n    const curveMaterialX = new LineBasicMaterial({ color: 0xff8080, fog: false, transparent: true, opacity: 0.6 })\n    const curveMaterialY = new LineBasicMaterial({ color: 0x80ff80, fog: false, transparent: true, opacity: 0.6 })\n    const curveMaterialZ = new LineBasicMaterial({ color: 0x8080ff, fog: false, transparent: true, opacity: 0.6 })\n\n    //line\n    const gizmoX = new Line(curveGeometry, curveMaterialX)\n    const gizmoY = new Line(curveGeometry, curveMaterialY)\n    const gizmoZ = new Line(curveGeometry, curveMaterialZ)\n\n    const rotation = Math.PI * 0.5\n    gizmoX.rotation.x = rotation\n    gizmoY.rotation.y = rotation\n\n    //setting state\n    this._gizmoMatrixState0.identity().setPosition(tbCenter)\n    this._gizmoMatrixState.copy(this._gizmoMatrixState0)\n\n    if (this.camera && this.camera.zoom != 1) {\n      //adapt gizmos size to camera zoom\n      const size = 1 / this.camera.zoom\n      this._scaleMatrix.makeScale(size, size, size)\n      this._translationMatrix.makeTranslation(-tbCenter.x, -tbCenter.y, -tbCenter.z)\n\n      this._gizmoMatrixState.premultiply(this._translationMatrix).premultiply(this._scaleMatrix)\n      this._translationMatrix.makeTranslation(tbCenter.x, tbCenter.y, tbCenter.z)\n      this._gizmoMatrixState.premultiply(this._translationMatrix)\n    }\n\n    this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n\n    this._gizmos.clear()\n\n    this._gizmos.add(gizmoX)\n    this._gizmos.add(gizmoY)\n    this._gizmos.add(gizmoZ)\n  }\n\n  /**\n   * Perform animation for focus operation\n   * @param {Number} time Instant in which this function is called as performance.now()\n   * @param {Vector3} point Point of interest for focus operation\n   * @param {Matrix4} cameraMatrix Camera matrix\n   * @param {Matrix4} gizmoMatrix Gizmos matrix\n   */\n  private onFocusAnim = (time: number, point: Vector3, cameraMatrix: Matrix4, gizmoMatrix: Matrix4): void => {\n    if (this._timeStart == -1) {\n      //animation start\n      this._timeStart = time\n    }\n\n    if (this._state == STATE.ANIMATION_FOCUS) {\n      const deltaTime = time - this._timeStart\n      const animTime = deltaTime / this.focusAnimationTime\n\n      this._gizmoMatrixState.copy(gizmoMatrix)\n\n      if (animTime >= 1) {\n        //animation end\n\n        this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n\n        this.focus(point, this.scaleFactor)\n\n        this._timeStart = -1\n        this.updateTbState(STATE.IDLE, false)\n        this.activateGizmos(false)\n\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n      } else {\n        const amount = this.easeOutCubic(animTime)\n        const size = 1 - amount + this.scaleFactor * amount\n\n        this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n        this.focus(point, size, amount)\n\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n        const self = this\n        this._animationId = window.requestAnimationFrame(function (t) {\n          self.onFocusAnim(t, point, cameraMatrix, gizmoMatrix.clone())\n        })\n      }\n    } else {\n      //interrupt animation\n\n      this._animationId = -1\n      this._timeStart = -1\n    }\n  }\n\n  /**\n   * Perform animation for rotation operation\n   * @param {Number} time Instant in which this function is called as performance.now()\n   * @param {Vector3} rotationAxis Rotation axis\n   * @param {number} w0 Initial angular velocity\n   */\n  private onRotationAnim = (time: number, rotationAxis: Vector3, w0: number): void => {\n    if (this._timeStart == -1) {\n      //animation start\n      this._anglePrev = 0\n      this._angleCurrent = 0\n      this._timeStart = time\n    }\n\n    if (this._state == STATE.ANIMATION_ROTATE) {\n      //w = w0 + alpha * t\n      const deltaTime = (time - this._timeStart) / 1000\n      const w = w0 + -this.dampingFactor * deltaTime\n\n      if (w > 0) {\n        //tetha = 0.5 * alpha * t^2 + w0 * t + tetha0\n        this._angleCurrent = 0.5 * -this.dampingFactor * Math.pow(deltaTime, 2) + w0 * deltaTime + 0\n        this.applyTransformMatrix(this.rotate(rotationAxis, this._angleCurrent))\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n        const self = this\n        this._animationId = window.requestAnimationFrame(function (t) {\n          self.onRotationAnim(t, rotationAxis, w0)\n        })\n      } else {\n        this._animationId = -1\n        this._timeStart = -1\n\n        this.updateTbState(STATE.IDLE, false)\n        this.activateGizmos(false)\n\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n      }\n    } else {\n      //interrupt animation\n\n      this._animationId = -1\n      this._timeStart = -1\n\n      if (this._state != STATE.ROTATE) {\n        this.activateGizmos(false)\n        // @ts-ignore\n        this.dispatchEvent(_changeEvent)\n      }\n    }\n  }\n\n  /**\n   * Perform pan operation moving camera between two points\n   * @param {Vector3} p0 Initial point\n   * @param {Vector3} p1 Ending point\n   * @param {Boolean} adjust If movement should be adjusted considering camera distance (Perspective only)\n   */\n  private pan = (p0: Vector3, p1: Vector3, adjust = false): Transformation => {\n    if (this.camera) {\n      const movement = p0.clone().sub(p1)\n\n      if (this.camera instanceof OrthographicCamera) {\n        //adjust movement amount\n        movement.multiplyScalar(1 / this.camera.zoom)\n      }\n\n      if (this.camera instanceof PerspectiveCamera && adjust) {\n        //adjust movement amount\n        this._v3_1.setFromMatrixPosition(this._cameraMatrixState0) //camera's initial position\n        this._v3_2.setFromMatrixPosition(this._gizmoMatrixState0) //gizmo's initial position\n        const distanceFactor =\n          this._v3_1.distanceTo(this._v3_2) / this.camera.position.distanceTo(this._gizmos.position)\n        movement.multiplyScalar(1 / distanceFactor)\n      }\n\n      this._v3_1.set(movement.x, movement.y, 0).applyQuaternion(this.camera.quaternion)\n\n      this._m4_1.makeTranslation(this._v3_1.x, this._v3_1.y, this._v3_1.z)\n\n      this.setTransformationMatrices(this._m4_1, this._m4_1)\n    }\n    return _transformation\n  }\n\n  /**\n   * Reset trackball\n   */\n  public reset = (): void => {\n    if (this.camera) {\n      this.camera.zoom = this._zoom0\n\n      if (this.camera instanceof PerspectiveCamera) {\n        this.camera.fov = this._fov0\n      }\n\n      this.camera.near = this._nearPos\n      this.camera.far = this._farPos\n      this._cameraMatrixState.copy(this._cameraMatrixState0)\n      this._cameraMatrixState.decompose(this.camera.position, this.camera.quaternion, this.camera.scale)\n      this.camera.up.copy(this._up0)\n\n      this.camera.updateMatrix()\n      this.camera.updateProjectionMatrix()\n\n      this._gizmoMatrixState.copy(this._gizmoMatrixState0)\n      this._gizmoMatrixState0.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n      this._gizmos.updateMatrix()\n\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n      this.makeGizmos(this._gizmos.position, this._tbRadius)\n\n      this.camera.lookAt(this._gizmos.position)\n\n      this.updateTbState(STATE.IDLE, false)\n\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n\n  /**\n   * Rotate the camera around an axis passing by trackball's center\n   * @param {Vector3} axis Rotation axis\n   * @param {number} angle Angle in radians\n   * @returns {Object} Object with 'camera' field containing transformation matrix resulting from the operation to be applied to the camera\n   */\n  private rotate = (axis: Vector3, angle: number): Transformation => {\n    const point = this._gizmos.position //rotation center\n    this._translationMatrix.makeTranslation(-point.x, -point.y, -point.z)\n    this._rotationMatrix.makeRotationAxis(axis, -angle)\n\n    //rotate camera\n    this._m4_1.makeTranslation(point.x, point.y, point.z)\n    this._m4_1.multiply(this._rotationMatrix)\n    this._m4_1.multiply(this._translationMatrix)\n\n    this.setTransformationMatrices(this._m4_1)\n\n    return _transformation\n  }\n\n  public copyState = (): void => {\n    if (this.camera) {\n      const state = JSON.stringify(\n        this.camera instanceof OrthographicCamera\n          ? {\n              arcballState: {\n                cameraFar: this.camera.far,\n                cameraMatrix: this.camera.matrix,\n                cameraNear: this.camera.near,\n                cameraUp: this.camera.up,\n                cameraZoom: this.camera.zoom,\n                gizmoMatrix: this._gizmos.matrix,\n              },\n            }\n          : {\n              arcballState: {\n                cameraFar: this.camera.far,\n                cameraFov: this.camera.fov,\n                cameraMatrix: this.camera.matrix,\n                cameraNear: this.camera.near,\n                cameraUp: this.camera.up,\n                cameraZoom: this.camera.zoom,\n                gizmoMatrix: this._gizmos.matrix,\n              },\n            },\n      )\n\n      navigator.clipboard.writeText(state)\n    }\n  }\n\n  public pasteState = (): void => {\n    const self = this\n    navigator.clipboard.readText().then(function resolved(value) {\n      self.setStateFromJSON(value)\n    })\n  }\n\n  /**\n   * Save the current state of the control. This can later be recovered with .reset\n   */\n  public saveState = (): void => {\n    if (!this.camera) return\n\n    this._cameraMatrixState0.copy(this.camera.matrix)\n    this._gizmoMatrixState0.copy(this._gizmos.matrix)\n    this._nearPos = this.camera.near\n    this._farPos = this.camera.far\n    this._zoom0 = this.camera.zoom\n    this._up0.copy(this.camera.up)\n\n    if (this.camera instanceof PerspectiveCamera) {\n      this._fov0 = this.camera.fov\n    }\n  }\n\n  /**\n   * Perform uniform scale operation around a given point\n   * @param {Number} size Scale factor\n   * @param {Vector3} point Point around which scale\n   * @param {Boolean} scaleGizmos If gizmos should be scaled (Perspective only)\n   * @returns {Object} Object with 'camera' and 'gizmo' fields containing transformation matrices resulting from the operation to be applied to the camera and gizmos\n   */\n  private applyScale = (size: number, point: Vector3, scaleGizmos = true): Transformation | undefined => {\n    if (!this.camera) return\n\n    const scalePoint = point.clone()\n    let sizeInverse = 1 / size\n\n    if (this.camera instanceof OrthographicCamera) {\n      //camera zoom\n      this.camera.zoom = this._zoomState\n      this.camera.zoom *= size\n\n      //check min and max zoom\n      if (this.camera.zoom > this.maxZoom) {\n        this.camera.zoom = this.maxZoom\n        sizeInverse = this._zoomState / this.maxZoom\n      } else if (this.camera.zoom < this.minZoom) {\n        this.camera.zoom = this.minZoom\n        sizeInverse = this._zoomState / this.minZoom\n      }\n\n      this.camera.updateProjectionMatrix()\n\n      this._v3_1.setFromMatrixPosition(this._gizmoMatrixState) //gizmos position\n\n      //scale gizmos so they appear in the same spot having the same dimension\n      this._scaleMatrix.makeScale(sizeInverse, sizeInverse, sizeInverse)\n      this._translationMatrix.makeTranslation(-this._v3_1.x, -this._v3_1.y, -this._v3_1.z)\n\n      this._m4_2.makeTranslation(this._v3_1.x, this._v3_1.y, this._v3_1.z).multiply(this._scaleMatrix)\n      this._m4_2.multiply(this._translationMatrix)\n\n      //move camera and gizmos to obtain pinch effect\n      scalePoint.sub(this._v3_1)\n\n      const amount = scalePoint.clone().multiplyScalar(sizeInverse)\n      scalePoint.sub(amount)\n\n      this._m4_1.makeTranslation(scalePoint.x, scalePoint.y, scalePoint.z)\n      this._m4_2.premultiply(this._m4_1)\n\n      this.setTransformationMatrices(this._m4_1, this._m4_2)\n      return _transformation\n    }\n\n    if (this.camera instanceof PerspectiveCamera) {\n      this._v3_1.setFromMatrixPosition(this._cameraMatrixState)\n      this._v3_2.setFromMatrixPosition(this._gizmoMatrixState)\n\n      //move camera\n      let distance = this._v3_1.distanceTo(scalePoint)\n      let amount = distance - distance * sizeInverse\n\n      //check min and max distance\n      const newDistance = distance - amount\n      if (newDistance < this.minDistance) {\n        sizeInverse = this.minDistance / distance\n        amount = distance - distance * sizeInverse\n      } else if (newDistance > this.maxDistance) {\n        sizeInverse = this.maxDistance / distance\n        amount = distance - distance * sizeInverse\n      }\n\n      let direction = scalePoint.clone().sub(this._v3_1).normalize().multiplyScalar(amount)\n\n      this._m4_1.makeTranslation(direction.x, direction.y, direction.z)\n\n      if (scaleGizmos) {\n        //scale gizmos so they appear in the same spot having the same dimension\n        const pos = this._v3_2\n\n        distance = pos.distanceTo(scalePoint)\n        amount = distance - distance * sizeInverse\n        direction = scalePoint.clone().sub(this._v3_2).normalize().multiplyScalar(amount)\n\n        this._translationMatrix.makeTranslation(pos.x, pos.y, pos.z)\n        this._scaleMatrix.makeScale(sizeInverse, sizeInverse, sizeInverse)\n\n        this._m4_2.makeTranslation(direction.x, direction.y, direction.z).multiply(this._translationMatrix)\n        this._m4_2.multiply(this._scaleMatrix)\n\n        this._translationMatrix.makeTranslation(-pos.x, -pos.y, -pos.z)\n\n        this._m4_2.multiply(this._translationMatrix)\n        this.setTransformationMatrices(this._m4_1, this._m4_2)\n      } else {\n        this.setTransformationMatrices(this._m4_1)\n      }\n\n      return _transformation\n    }\n  }\n\n  /**\n   * Set camera fov\n   * @param {Number} value fov to be setted\n   */\n  private setFov = (value: number): void => {\n    if (this.camera instanceof PerspectiveCamera) {\n      this.camera.fov = MathUtils.clamp(value, this.minFov, this.maxFov)\n      this.camera.updateProjectionMatrix()\n    }\n  }\n\n  /**\n   * Set the trackball's center point\n   * @param {Number} x X coordinate\n   * @param {Number} y Y coordinate\n   * @param {Number} z Z coordinate\n   */\n  public setTarget = (x: number, y: number, z: number): void => {\n    if (this.camera) {\n      this.target.set(x, y, z)\n      this._gizmos.position.set(x, y, z) //for correct radius calculation\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n      this.makeGizmos(this.target, this._tbRadius)\n      this.camera.lookAt(this.target)\n    }\n  }\n\n  /**\n   * Set values in transformation object\n   * @param {Matrix4} camera Transformation to be applied to the camera\n   * @param {Matrix4} gizmos Transformation to be applied to gizmos\n   */\n  private setTransformationMatrices(camera: Matrix4 | null = null, gizmos: Matrix4 | null = null): void {\n    if (camera) {\n      if (_transformation.camera) {\n        _transformation.camera.copy(camera)\n      } else {\n        _transformation.camera = camera.clone()\n      }\n    } else {\n      _transformation.camera = null\n    }\n\n    if (gizmos) {\n      if (_transformation.gizmos) {\n        _transformation.gizmos.copy(gizmos)\n      } else {\n        _transformation.gizmos = gizmos.clone()\n      }\n    } else {\n      _transformation.gizmos = null\n    }\n  }\n\n  /**\n   * Rotate camera around its direction axis passing by a given point by a given angle\n   * @param {Vector3} point The point where the rotation axis is passing trough\n   * @param {Number} angle Angle in radians\n   * @returns The computed transormation matix\n   */\n  private zRotate = (point: Vector3, angle: number): Transformation => {\n    this._rotationMatrix.makeRotationAxis(this._rotationAxis, angle)\n    this._translationMatrix.makeTranslation(-point.x, -point.y, -point.z)\n\n    this._m4_1.makeTranslation(point.x, point.y, point.z)\n    this._m4_1.multiply(this._rotationMatrix)\n    this._m4_1.multiply(this._translationMatrix)\n\n    this._v3_1.setFromMatrixPosition(this._gizmoMatrixState).sub(point) //vector from rotation center to gizmos position\n    this._v3_2.copy(this._v3_1).applyAxisAngle(this._rotationAxis, angle) //apply rotation\n    this._v3_2.sub(this._v3_1)\n\n    this._m4_2.makeTranslation(this._v3_2.x, this._v3_2.y, this._v3_2.z)\n\n    this.setTransformationMatrices(this._m4_1, this._m4_2)\n    return _transformation\n  }\n\n  /**\n   * Unproject the cursor on the 3D object surface\n   * @param {Vector2} cursor Cursor coordinates in NDC\n   * @param {Camera} camera Virtual camera\n   * @returns {Vector3} The point of intersection with the model, if exist, null otherwise\n   */\n  private unprojectOnObj = (cursor: Vector2, camera: Camera): Vector3 | null => {\n    if (!this.scene) return null\n\n    const raycaster = new Raycaster()\n    raycaster.near = camera.near\n    raycaster.far = camera.far\n    raycaster.setFromCamera(cursor, camera)\n\n    const intersect = raycaster.intersectObjects(this.scene.children, true)\n    for (let i = 0; i < intersect.length; i++) {\n      if (intersect[i].object.uuid != this._gizmos.uuid && intersect[i].face) {\n        return intersect[i].point.clone()\n      }\n    }\n\n    return null\n  }\n\n  /**\n   * Unproject the cursor on the trackball surface\n   * @param {Camera} camera The virtual camera\n   * @param {Number} cursorX Cursor horizontal coordinate on screen\n   * @param {Number} cursorY Cursor vertical coordinate on screen\n   * @param {HTMLElement} canvas The canvas where the renderer draws its output\n   * @param {number} tbRadius The trackball radius\n   * @returns {Vector3} The unprojected point on the trackball surface\n   */\n  private unprojectOnTbSurface = (\n    camera: Camera,\n    cursorX: number,\n    cursorY: number,\n    canvas: HTMLElement,\n    tbRadius: number,\n  ): Vector3 | undefined => {\n    if (camera instanceof OrthographicCamera) {\n      this._v2_1.copy(this.getCursorPosition(cursorX, cursorY, canvas))\n      this._v3_1.set(this._v2_1.x, this._v2_1.y, 0)\n\n      const x2 = Math.pow(this._v2_1.x, 2)\n      const y2 = Math.pow(this._v2_1.y, 2)\n      const r2 = Math.pow(this._tbRadius, 2)\n\n      if (x2 + y2 <= r2 * 0.5) {\n        //intersection with sphere\n        this._v3_1.setZ(Math.sqrt(r2 - (x2 + y2)))\n      } else {\n        //intersection with hyperboloid\n        this._v3_1.setZ((r2 * 0.5) / Math.sqrt(x2 + y2))\n      }\n\n      return this._v3_1\n    }\n\n    if (camera instanceof PerspectiveCamera) {\n      //unproject cursor on the near plane\n      this._v2_1.copy(this.getCursorNDC(cursorX, cursorY, canvas))\n\n      this._v3_1.set(this._v2_1.x, this._v2_1.y, -1)\n      this._v3_1.applyMatrix4(camera.projectionMatrixInverse)\n\n      const rayDir = this._v3_1.clone().normalize() //unprojected ray direction\n      const cameraGizmoDistance = camera.position.distanceTo(this._gizmos.position)\n      const radius2 = Math.pow(tbRadius, 2)\n\n      //\t  camera\n      //\t\t|\\\n      //\t\t| \\\n      //\t\t|  \\\n      //\th\t|\t\\\n      //\t\t| \t \\\n      //\t\t| \t  \\\n      //\t_ _ | _ _ _\\ _ _  near plane\n      //\t\t\tl\n\n      const h = this._v3_1.z\n      const l = Math.sqrt(Math.pow(this._v3_1.x, 2) + Math.pow(this._v3_1.y, 2))\n\n      if (l == 0) {\n        //ray aligned with camera\n        rayDir.set(this._v3_1.x, this._v3_1.y, tbRadius)\n        return rayDir\n      }\n\n      const m = h / l\n      const q = cameraGizmoDistance\n\n      /*\n       * calculate intersection point between unprojected ray and trackball surface\n       *|y = m * x + q\n       *|x^2 + y^2 = r^2\n       *\n       * (m^2 + 1) * x^2 + (2 * m * q) * x + q^2 - r^2 = 0\n       */\n      let a = Math.pow(m, 2) + 1\n      let b = 2 * m * q\n      let c = Math.pow(q, 2) - radius2\n      let delta = Math.pow(b, 2) - 4 * a * c\n\n      if (delta >= 0) {\n        //intersection with sphere\n        this._v2_1.setX((-b - Math.sqrt(delta)) / (2 * a))\n        this._v2_1.setY(m * this._v2_1.x + q)\n\n        const angle = MathUtils.RAD2DEG * this._v2_1.angle()\n\n        if (angle >= 45) {\n          //if angle between intersection point and X' axis is >= 45°, return that point\n          //otherwise, calculate intersection point with hyperboloid\n\n          const rayLength = Math.sqrt(Math.pow(this._v2_1.x, 2) + Math.pow(cameraGizmoDistance - this._v2_1.y, 2))\n          rayDir.multiplyScalar(rayLength)\n          rayDir.z += cameraGizmoDistance\n          return rayDir\n        }\n      }\n\n      //intersection with hyperboloid\n      /*\n       *|y = m * x + q\n       *|y = (1 / x) * (r^2 / 2)\n       *\n       * m * x^2 + q * x - r^2 / 2 = 0\n       */\n\n      a = m\n      b = q\n      c = -radius2 * 0.5\n      delta = Math.pow(b, 2) - 4 * a * c\n      this._v2_1.setX((-b - Math.sqrt(delta)) / (2 * a))\n      this._v2_1.setY(m * this._v2_1.x + q)\n\n      const rayLength = Math.sqrt(Math.pow(this._v2_1.x, 2) + Math.pow(cameraGizmoDistance - this._v2_1.y, 2))\n\n      rayDir.multiplyScalar(rayLength)\n      rayDir.z += cameraGizmoDistance\n      return rayDir\n    }\n  }\n\n  /**\n   * Unproject the cursor on the plane passing through the center of the trackball orthogonal to the camera\n   * @param {Camera} camera The virtual camera\n   * @param {Number} cursorX Cursor horizontal coordinate on screen\n   * @param {Number} cursorY Cursor vertical coordinate on screen\n   * @param {HTMLElement} canvas The canvas where the renderer draws its output\n   * @param {Boolean} initialDistance If initial distance between camera and gizmos should be used for calculations instead of current (Perspective only)\n   * @returns {Vector3} The unprojected point on the trackball plane\n   */\n  private unprojectOnTbPlane = (\n    camera: Camera,\n    cursorX: number,\n    cursorY: number,\n    canvas: HTMLElement,\n    initialDistance = false,\n  ): Vector3 | undefined => {\n    if (camera instanceof OrthographicCamera) {\n      this._v2_1.copy(this.getCursorPosition(cursorX, cursorY, canvas))\n      this._v3_1.set(this._v2_1.x, this._v2_1.y, 0)\n\n      return this._v3_1.clone()\n    }\n\n    if (camera instanceof PerspectiveCamera) {\n      this._v2_1.copy(this.getCursorNDC(cursorX, cursorY, canvas))\n\n      //unproject cursor on the near plane\n      this._v3_1.set(this._v2_1.x, this._v2_1.y, -1)\n      this._v3_1.applyMatrix4(camera.projectionMatrixInverse)\n\n      const rayDir = this._v3_1.clone().normalize() //unprojected ray direction\n\n      //\t  camera\n      //\t\t|\\\n      //\t\t| \\\n      //\t\t|  \\\n      //\th\t|\t\\\n      //\t\t| \t \\\n      //\t\t| \t  \\\n      //\t_ _ | _ _ _\\ _ _  near plane\n      //\t\t\tl\n\n      const h = this._v3_1.z\n      const l = Math.sqrt(Math.pow(this._v3_1.x, 2) + Math.pow(this._v3_1.y, 2))\n      let cameraGizmoDistance\n\n      if (initialDistance) {\n        cameraGizmoDistance = this._v3_1\n          .setFromMatrixPosition(this._cameraMatrixState0)\n          .distanceTo(this._v3_2.setFromMatrixPosition(this._gizmoMatrixState0))\n      } else {\n        cameraGizmoDistance = camera.position.distanceTo(this._gizmos.position)\n      }\n\n      /*\n       * calculate intersection point between unprojected ray and the plane\n       *|y = mx + q\n       *|y = 0\n       *\n       * x = -q/m\n       */\n      if (l == 0) {\n        //ray aligned with camera\n        rayDir.set(0, 0, 0)\n        return rayDir\n      }\n\n      const m = h / l\n      const q = cameraGizmoDistance\n      const x = -q / m\n\n      const rayLength = Math.sqrt(Math.pow(q, 2) + Math.pow(x, 2))\n      rayDir.multiplyScalar(rayLength)\n      rayDir.z = 0\n      return rayDir\n    }\n  }\n\n  /**\n   * Update camera and gizmos state\n   */\n  private updateMatrixState = (): void => {\n    if (!this.camera) return\n\n    //update camera and gizmos state\n    this._cameraMatrixState.copy(this.camera.matrix)\n    this._gizmoMatrixState.copy(this._gizmos.matrix)\n\n    if (this.camera instanceof OrthographicCamera) {\n      this._cameraProjectionState.copy(this.camera.projectionMatrix)\n      this.camera.updateProjectionMatrix()\n      this._zoomState = this.camera.zoom\n    }\n\n    if (this.camera instanceof PerspectiveCamera) {\n      this._fovState = this.camera.fov\n    }\n  }\n\n  /**\n   * Update the trackball FSA\n   * @param {STATE} newState New state of the FSA\n   * @param {Boolean} updateMatrices If matriices state should be updated\n   */\n  private updateTbState = (newState: Symbol, updateMatrices: boolean): void => {\n    this._state = newState\n    if (updateMatrices) {\n      this.updateMatrixState()\n    }\n  }\n\n  public update = (): void => {\n    const EPS = 0.000001\n\n    // Update target and gizmos state\n    if (!this.target.equals(this._currentTarget) && this.camera) {\n      this._gizmos.position.set(this.target.x, this.target.y, this.target.z) //for correct radius calculation\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n      this.makeGizmos(this.target, this._tbRadius)\n      this._currentTarget.copy(this.target)\n    }\n\n    if (!this.camera) return\n\n    //check min/max parameters\n    if (this.camera instanceof OrthographicCamera) {\n      //check zoom\n      if (this.camera.zoom > this.maxZoom || this.camera.zoom < this.minZoom) {\n        const newZoom = MathUtils.clamp(this.camera.zoom, this.minZoom, this.maxZoom)\n        this.applyTransformMatrix(this.applyScale(newZoom / this.camera.zoom, this._gizmos.position, true))\n      }\n    }\n\n    if (this.camera instanceof PerspectiveCamera) {\n      //check distance\n      const distance = this.camera.position.distanceTo(this._gizmos.position)\n\n      if (distance > this.maxDistance + EPS || distance < this.minDistance - EPS) {\n        const newDistance = MathUtils.clamp(distance, this.minDistance, this.maxDistance)\n        this.applyTransformMatrix(this.applyScale(newDistance / distance, this._gizmos.position))\n        this.updateMatrixState()\n      }\n\n      //check fov\n      if (this.camera.fov < this.minFov || this.camera.fov > this.maxFov) {\n        this.camera.fov = MathUtils.clamp(this.camera.fov, this.minFov, this.maxFov)\n        this.camera.updateProjectionMatrix()\n      }\n\n      const oldRadius = this._tbRadius\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n\n      if (oldRadius < this._tbRadius - EPS || oldRadius > this._tbRadius + EPS) {\n        const scale = (this._gizmos.scale.x + this._gizmos.scale.y + this._gizmos.scale.z) / 3\n        const newRadius = this._tbRadius / scale\n        // @ts-ignore\n        const curve = new EllipseCurve(0, 0, newRadius, newRadius)\n        const points = curve.getPoints(this._curvePts)\n        const curveGeometry = new BufferGeometry().setFromPoints(points)\n\n        for (const gizmo in this._gizmos.children) {\n          const child = this._gizmos.children[gizmo] as Mesh\n          child.geometry = curveGeometry\n        }\n      }\n    }\n\n    this.camera.lookAt(this._gizmos.position)\n  }\n\n  private setStateFromJSON = (json: string): void => {\n    const state = JSON.parse(json)\n\n    if (state.arcballState && this.camera) {\n      this._cameraMatrixState.fromArray(state.arcballState.cameraMatrix.elements)\n      this._cameraMatrixState.decompose(this.camera.position, this.camera.quaternion, this.camera.scale)\n\n      this.camera.up.copy(state.arcballState.cameraUp)\n      this.camera.near = state.arcballState.cameraNear\n      this.camera.far = state.arcballState.cameraFar\n\n      this.camera.zoom = state.arcballState.cameraZoom\n\n      if (this.camera instanceof PerspectiveCamera) {\n        this.camera.fov = state.arcballState.cameraFov\n      }\n\n      this._gizmoMatrixState.fromArray(state.arcballState.gizmoMatrix.elements)\n      this._gizmoMatrixState.decompose(this._gizmos.position, this._gizmos.quaternion, this._gizmos.scale)\n\n      this.camera.updateMatrix()\n      this.camera.updateProjectionMatrix()\n\n      this._gizmos.updateMatrix()\n\n      const tbRadius = this.calculateTbRadius(this.camera)\n      if (tbRadius !== undefined) {\n        this._tbRadius = tbRadius\n      }\n      const gizmoTmp = new Matrix4().copy(this._gizmoMatrixState0)\n      this.makeGizmos(this._gizmos.position, this._tbRadius)\n      this._gizmoMatrixState0.copy(gizmoTmp)\n\n      this.camera.lookAt(this._gizmos.position)\n      this.updateTbState(STATE.IDLE, false)\n\n      // @ts-ignore\n      this.dispatchEvent(_changeEvent)\n    }\n  }\n}\n\nexport { ArcballControls }\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;AAwCA,MAAM,QAAQ;IACZ,MAAM,OAAO;IACb,QAAQ,OAAO;IACf,KAAK,OAAO;IACZ,OAAO,OAAO;IACd,KAAK,OAAO;IACZ,OAAO,OAAO;IACd,SAAS,OAAO;IAChB,aAAa,OAAO;IACpB,iBAAiB,OAAO;IACxB,kBAAkB,OAAO;AAC3B;AAEA,MAAM,QAAQ;IACZ,MAAM,OAAO;IACb,YAAY,OAAO;IACnB,qBAAqB,OAAO;IAC5B,YAAY,OAAO;IACnB,aAAa,OAAO;IACpB,QAAQ,OAAO;AACjB;AAGA,MAAM,UAAU;IACd,GAAG;IACH,GAAG;AACL;AAGA,MAAM,kBAAkC;IACtC,QAAA,aAAA,GAAA,2MAA4B,UAAA,CAAQ;IACpC,QAAA,aAAA,GAAA,2MAA4B,UAAA,CAAQ;AACtC;AAGA,MAAM,eAAe;IAAE,MAAM;AAAA;AAC7B,MAAM,cAAc;IAAE,MAAM;AAAA;AAC5B,MAAM,YAAY;IAAE,MAAM;AAAA;AAQ1B,MAAM,8QAAwB,kBAAA,CAA0C;IAmHtE,YACE,MAAA,EACA,aAA6C,IAAA,EAC7C,QAAkC,IAAA,CAClC;QACM,KAAA;QAvHA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAED,cAAA,IAAA,EAAA;QAEC,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAED,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEE,cAAA,IAAA,EAAA;QACD,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QA2JA,WAAA;QAAA,cAAA,IAAA,EAAA,kBAAiB,MAAY;YACnC,MAAM,QAAA,CAAS,IAAA,CAAK,OAAA,CAAQ,KAAA,CAAM,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,KAAA,CAAM,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,KAAA,CAAM,CAAA,IAAK;YACrF,IAAI,IAAA,CAAK,MAAA,EAAQ;gBACf,MAAM,WAAW,IAAA,CAAK,iBAAA,CAAkB,IAAA,CAAK,MAAM;gBACnD,IAAI,aAAa,KAAA,GAAW;oBAC1B,IAAA,CAAK,SAAA,GAAY;gBACnB;YACF;YAEM,MAAA,YAAY,IAAA,CAAK,SAAA,GAAY;YAEnC,MAAM,QAAQ,2MAAI,eAAA,CAAa,GAAG,GAAG,WAAW,SAAS;YACzD,MAAM,SAAS,MAAM,SAAA,CAAU,IAAA,CAAK,SAAS;YAC7C,MAAM,gBAAgB,2MAAI,iBAAA,CAAe,EAAE,aAAA,CAAc,MAAM;YAEpD,IAAA,MAAA,SAAS,IAAA,CAAK,OAAA,CAAQ,QAAA,CAAU;gBACzC,MAAM,QAAQ,IAAA,CAAK,OAAA,CAAQ,QAAA,CAAS,KAAK,CAAA;gBACzC,MAAM,QAAA,GAAW;YACnB;YAGA,IAAA,CAAK,aAAA,CAAc,YAAY;QAAA;QAGzB,cAAA,IAAA,EAAA,iBAAgB,CAAC,UAA4B;YAC/C,IAAA,CAAC,IAAA,CAAK,OAAA,EAAS;gBACjB;YACF;YAEA,IAAA,IAAS,IAAI,GAAG,IAAI,IAAA,CAAK,YAAA,CAAa,MAAA,EAAQ,IAAK;gBACjD,IAAI,IAAA,CAAK,YAAA,CAAa,CAAC,CAAA,CAAE,KAAA,IAAS,GAAG;oBAEnC,MAAM,cAAA,CAAe;oBACrB;gBACF;YACF;QAAA;QAGM,cAAA,IAAA,EAAA,mBAAkB,MAAY;YACpC,IAAA,CAAK,WAAA,CAAY,MAAA,CAAO,GAAG,IAAA,CAAK,WAAA,CAAY,MAAM;YAClD,IAAA,CAAK,aAAA,CAAc,MAAA,CAAO,GAAG,IAAA,CAAK,aAAA,CAAc,MAAM;YACtD,IAAA,CAAK,MAAA,GAAS,MAAM,IAAA;QAAA;QAGd,cAAA,IAAA,EAAA,iBAAgB,CAAC,UAA8B;YACrD,IAAI,MAAM,MAAA,IAAU,KAAK,MAAM,SAAA,EAAW;gBACxC,IAAA,CAAK,UAAA,GAAa;gBACb,IAAA,CAAA,WAAA,CAAY,IAAA,CAAK,KAAK;YAAA,OACtB;gBACL,IAAA,CAAK,UAAA,GAAa;YACpB;YAEA,IAAI,MAAM,WAAA,IAAe,WAAW,IAAA,CAAK,MAAA,IAAU,MAAM,MAAA,EAAQ;gBAC1D,IAAA,CAAA,WAAA,CAAY,IAAA,CAAK,KAAK;gBACtB,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,KAAK;gBAE7B,OAAQ,IAAA,CAAK,MAAA,EAAQ;oBACnB,KAAK,MAAM,IAAA;wBAET,IAAA,CAAK,MAAA,GAAS,MAAM,UAAA;wBACf,IAAA,CAAA,gBAAA,CAAiB,OAAO,QAAQ;wBAE9B,OAAA,gBAAA,CAAiB,eAAe,IAAA,CAAK,aAAa;wBAClD,OAAA,gBAAA,CAAiB,aAAa,IAAA,CAAK,WAAW;wBAErD;oBAEF,KAAK,MAAM,UAAA;oBACX,KAAK,MAAM,mBAAA;wBAET,IAAA,CAAK,MAAA,GAAS,MAAM,UAAA;wBAEpB,IAAA,CAAK,aAAA,CAAc;wBACnB,IAAA,CAAK,YAAA,CAAa;wBAClB,IAAA,CAAK,gBAAA,CAAiB;wBAEtB;oBAEF,KAAK,MAAM,UAAA;wBAET,IAAA,CAAK,MAAA,GAAS,MAAM,WAAA;wBACpB,IAAA,CAAK,gBAAA,CAAiB;wBACtB;gBACJ;YAAA,OAAA,IACS,MAAM,WAAA,IAAe,WAAW,IAAA,CAAK,MAAA,IAAU,MAAM,IAAA,EAAM;gBACpE,IAAI,WAA+B;gBAE/B,IAAA,MAAM,OAAA,IAAW,MAAM,OAAA,EAAS;oBACvB,WAAA;gBAAA,OAAA,IACF,MAAM,QAAA,EAAU;oBACd,WAAA;gBACb;gBAEA,IAAA,CAAK,QAAA,GAAW,IAAA,CAAK,eAAA,CAAgB,MAAM,MAAA,EAAQ,QAAQ;gBAC3D,IAAI,IAAA,CAAK,QAAA,EAAU;oBACV,OAAA,gBAAA,CAAiB,eAAe,IAAA,CAAK,aAAa;oBAClD,OAAA,gBAAA,CAAiB,aAAa,IAAA,CAAK,WAAW;oBAGrD,IAAA,CAAK,MAAA,GAAS,MAAM,MAAA;oBACpB,IAAA,CAAK,OAAA,GAAU,MAAM,MAAA;oBAChB,IAAA,CAAA,gBAAA,CAAiB,OAAO,IAAA,CAAK,QAAQ;gBAC5C;YACF;QAAA;QAGM,cAAA,IAAA,EAAA,iBAAgB,CAAC,UAA8B;YACrD,IAAI,MAAM,WAAA,IAAe,WAAW,IAAA,CAAK,MAAA,IAAU,MAAM,MAAA,EAAQ;gBAC/D,OAAQ,IAAA,CAAK,MAAA,EAAQ;oBACnB,KAAK,MAAM,UAAA;wBAET,IAAA,CAAK,gBAAA,CAAiB,KAAK;wBAEtB,IAAA,CAAA,eAAA,CAAgB,OAAO,MAAM,MAAM;wBACxC;oBAEF,KAAK,MAAM,mBAAA;wBACH,MAAA,WAAW,IAAA,CAAK,yBAAA,CAA0B,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,EAAG,KAAK,IAAI,IAAA,CAAK,WAAA;wBAEjF,IAAA,YAAY,IAAA,CAAK,kBAAA,EAAoB;4BAEvC,IAAA,CAAK,MAAA,GAAS,MAAM,UAAA;4BACpB,IAAA,CAAK,gBAAA,CAAiB,KAAK;4BAEtB,IAAA,CAAA,gBAAA,CAAiB,OAAO,QAAQ;4BACrC;wBACF;wBAEA;oBAEF,KAAK,MAAM,UAAA;wBAET,IAAA,CAAK,gBAAA,CAAiB,KAAK;wBAE3B,IAAA,CAAK,YAAA,CAAa;wBAClB,IAAA,CAAK,WAAA,CAAY;wBACjB,IAAA,CAAK,eAAA,CAAgB;wBAErB;oBAEF,KAAK,MAAM,WAAA;wBAET,IAAA,CAAK,gBAAA,CAAiB,KAAK;wBAE3B,IAAA,CAAK,eAAA,CAAgB;wBACrB;gBACJ;YAAA,OAAA,IACS,MAAM,WAAA,IAAe,WAAW,IAAA,CAAK,MAAA,IAAU,MAAM,MAAA,EAAQ;gBACtE,IAAI,WAA+B;gBAE/B,IAAA,MAAM,OAAA,IAAW,MAAM,OAAA,EAAS;oBACvB,WAAA;gBAAA,OAAA,IACF,MAAM,QAAA,EAAU;oBACd,WAAA;gBACb;gBAEA,MAAM,eAAe,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,OAAA,EAAS,QAAQ;gBAErE,IAAI,cAAc;oBACX,IAAA,CAAA,eAAA,CAAgB,OAAO,YAAY;gBAC1C;YACF;YAGA,IAAI,IAAA,CAAK,UAAA,EAAY;gBACnB,MAAM,WACJ,IAAA,CAAK,yBAAA,CAA0B,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,WAAA,CAAY,MAAA,GAAS,CAAC,CAAA,EAAG,KAAK,IAAI,IAAA,CAAK,WAAA;gBAC1F,IAAA,WAAW,IAAA,CAAK,kBAAA,EAAoB;oBACtC,IAAA,CAAK,UAAA,GAAa;gBACpB;YACF;QAAA;QAGM,cAAA,IAAA,EAAA,eAAc,CAAC,UAA8B;YACnD,IAAI,MAAM,WAAA,IAAe,WAAW,IAAA,CAAK,MAAA,IAAU,MAAM,MAAA,EAAQ;gBACzD,MAAA,SAAS,IAAA,CAAK,aAAA,CAAc,MAAA;gBAElC,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,IAAK;oBAC/B,IAAI,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,SAAA,IAAa,MAAM,SAAA,EAAW;wBACjD,IAAA,CAAA,aAAA,CAAc,MAAA,CAAO,GAAG,CAAC;wBACzB,IAAA,CAAA,WAAA,CAAY,MAAA,CAAO,GAAG,CAAC;wBAC5B;oBACF;gBACF;gBAEA,OAAQ,IAAA,CAAK,MAAA,EAAQ;oBACnB,KAAK,MAAM,UAAA;oBACX,KAAK,MAAM,mBAAA;wBAEF,OAAA,mBAAA,CAAoB,eAAe,IAAA,CAAK,aAAa;wBACrD,OAAA,mBAAA,CAAoB,aAAa,IAAA,CAAK,WAAW;wBAExD,IAAA,CAAK,MAAA,GAAS,MAAM,IAAA;wBACpB,IAAA,CAAK,cAAA,CAAe;wBAEpB;oBAEF,KAAK,MAAM,UAAA;wBAET,IAAA,CAAK,cAAA,CAAe;wBACpB,IAAA,CAAK,UAAA,CAAW;wBAChB,IAAA,CAAK,WAAA,CAAY;wBAGjB,IAAA,CAAK,MAAA,GAAS,MAAM,mBAAA;wBAEpB;oBAEF,KAAK,MAAM,WAAA;wBACL,IAAA,IAAA,CAAK,aAAA,CAAc,MAAA,IAAU,GAAG;4BAC3B,OAAA,mBAAA,CAAoB,eAAe,IAAA,CAAK,aAAa;4BACrD,OAAA,mBAAA,CAAoB,aAAa,IAAA,CAAK,WAAW;4BAGxD,IAAA,CAAK,MAAA,GAAS,MAAM,IAAA;4BACpB,IAAA,CAAK,cAAA,CAAe;wBACtB;wBAEA;gBACJ;YAAA,OAAA,IACS,MAAM,WAAA,IAAe,WAAW,IAAA,CAAK,MAAA,IAAU,MAAM,MAAA,EAAQ;gBAC/D,OAAA,mBAAA,CAAoB,eAAe,IAAA,CAAK,aAAa;gBACrD,OAAA,mBAAA,CAAoB,aAAa,IAAA,CAAK,WAAW;gBAExD,IAAA,CAAK,MAAA,GAAS,MAAM,IAAA;gBACpB,IAAA,CAAK,cAAA,CAAe;gBACpB,IAAA,CAAK,OAAA,GAAU,CAAA;YACjB;YAEA,IAAI,MAAM,SAAA,EAAW;gBACnB,IAAI,IAAA,CAAK,UAAA,EAAY;oBACb,MAAA,WAAW,MAAM,SAAA,GAAY,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,WAAA,CAAY,MAAA,GAAS,CAAC,CAAA,CAAE,SAAA;oBAE7E,IAAA,YAAY,IAAA,CAAK,YAAA,EAAc;wBAC7B,IAAA,IAAA,CAAK,QAAA,IAAY,GAAG;4BAEtB,IAAA,CAAK,QAAA,GAAW;4BACX,IAAA,CAAA,WAAA,GAAc,YAAY,GAAA;wBAAI,OAC9B;4BACC,MAAA,gBAAgB,MAAM,SAAA,GAAY,IAAA,CAAK,WAAA;4BAC7C,MAAM,WAAW,IAAA,CAAK,yBAAA,CAA0B,IAAA,CAAK,WAAA,CAAY,CAAC,CAAA,EAAG,IAAA,CAAK,WAAA,CAAY,CAAC,CAAC,IAAI,IAAA,CAAK,WAAA;4BAEjG,IAAI,iBAAiB,IAAA,CAAK,YAAA,IAAgB,YAAY,IAAA,CAAK,aAAA,EAAe;gCAGxE,IAAA,CAAK,QAAA,GAAW;gCAChB,IAAA,CAAK,WAAA,CAAY,MAAA,CAAO,GAAG,IAAA,CAAK,WAAA,CAAY,MAAM;gCAClD,IAAA,CAAK,WAAA,CAAY,KAAK;4BAAA,OACjB;gCAEL,IAAA,CAAK,QAAA,GAAW;gCAChB,IAAA,CAAK,WAAA,CAAY,KAAA;gCACZ,IAAA,CAAA,WAAA,GAAc,YAAY,GAAA;4BACjC;wBACF;oBAAA,OACK;wBACL,IAAA,CAAK,UAAA,GAAa;wBAClB,IAAA,CAAK,QAAA,GAAW;wBAChB,IAAA,CAAK,WAAA,CAAY,MAAA,CAAO,GAAG,IAAA,CAAK,WAAA,CAAY,MAAM;oBACpD;gBAAA,OACK;oBACL,IAAA,CAAK,QAAA,GAAW;oBAChB,IAAA,CAAK,WAAA,CAAY,MAAA,CAAO,GAAG,IAAA,CAAK,WAAA,CAAY,MAAM;gBACpD;YACF;QAAA;QAGM,cAAA,IAAA,EAAA,WAAU,CAAC,UAA4B;;YAC7C,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,UAAA,IAAc,IAAA,CAAK,UAAA,EAAY;gBACtD,IAAI,WAA+B;gBAE/B,IAAA,MAAM,OAAA,IAAW,MAAM,OAAA,EAAS;oBACvB,WAAA;gBAAA,OAAA,IACF,MAAM,QAAA,EAAU;oBACd,WAAA;gBACb;gBAEA,MAAM,UAAU,IAAA,CAAK,eAAA,CAAgB,SAAS,QAAQ;gBAEtD,IAAI,SAAS;oBACX,MAAM,cAAA,CAAe;oBAErB,IAAA,CAAK,aAAA,CAAc,WAAW;oBAE9B,MAAM,cAAc;oBAChB,IAAA,MAAM,MAAM,MAAA,GAAS;oBAEzB,IAAI,OAAO;oBAEX,IAAI,MAAM,GAAG;wBACX,OAAO,IAAI,IAAA,CAAK,WAAA;oBAAA,OAAA,IACP,MAAM,GAAG;wBAClB,OAAO,IAAA,CAAK,WAAA;oBACd;oBAEA,OAAQ,SAAS;wBACf,KAAK;4BACE,IAAA,CAAA,aAAA,CAAc,MAAM,KAAA,EAAO,IAAI;4BAEpC,IAAI,MAAM,GAAG;gCACX,OAAO,IAAI,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,EAAa,GAAG;4BAAA,OAAA,IAChC,MAAM,GAAG;gCAClB,OAAO,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,EAAa,CAAC,GAAG;4BACxC;4BAEI,IAAA,IAAA,CAAK,UAAA,IAAc,IAAA,CAAK,SAAA,EAAW;gCACjC,IAAA;gCAEA,IAAA,IAAA,CAAK,MAAA,mNAAkB,qBAAA,EAAoB;oCAChC,aAAA,CAAA,KAAA,IAAA,CAAK,kBAAA,CAAmB,IAAA,CAAK,MAAA,EAAQ,MAAM,OAAA,EAAS,MAAM,OAAA,EAAS,IAAA,CAAK,UAAU,CAAA,KAAlF,OAAA,KAAA,IAAA,GACT,eAAA,CAAgB,IAAA,CAAK,MAAA,CAAO,UAAA,EAC7B,cAAA,CAAe,IAAI,IAAA,CAAK,MAAA,CAAO,IAAA,EAC/B,GAAA,CAAI,IAAA,CAAK,OAAA,CAAQ,QAAA;gCACtB;gCAEI,IAAA,IAAA,CAAK,MAAA,mNAAkB,oBAAA,EAAmB;oCAC5C,aAAA,CAAa,KAAA,IAAA,CAAK,kBAAA,CAAmB,IAAA,CAAK,MAAA,EAAQ,MAAM,OAAA,EAAS,MAAM,OAAA,EAAS,IAAA,CAAK,UAAU,CAAA,KAAlF,OAAA,KAAA,IAAA,GACT,eAAA,CAAgB,IAAA,CAAK,MAAA,CAAO,UAAA,EAC7B,GAAA,CAAI,IAAA,CAAK,OAAA,CAAQ,QAAA;gCACtB;gCAEA,IAAI,eAAe,KAAA,GAAW,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,UAAA,CAAW,MAAM,UAAU,CAAC;4BAAA,OACpF;gCACL,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,UAAA,CAAW,MAAM,IAAA,CAAK,OAAA,CAAQ,QAAQ,CAAC;4BACxE;4BAEA,IAAI,IAAA,CAAK,KAAA,EAAO;gCACd,IAAA,CAAK,WAAA,CAAY;gCACjB,IAAA,CAAK,QAAA,CAAS;4BAChB;4BAEK,IAAA,CAAA,aAAA,CAAc,MAAM,IAAA,EAAM,KAAK;4BAGpC,IAAA,CAAK,aAAA,CAAc,YAAY;4BAE/B,IAAA,CAAK,aAAA,CAAc,SAAS;4BAE5B;wBAEF,KAAK;4BACC,IAAA,IAAA,CAAK,MAAA,mNAAkB,oBAAA,EAAmB;gCACvC,IAAA,CAAA,aAAA,CAAc,MAAM,GAAA,EAAK,IAAI;gCAe9B,IAAA,MAAM,MAAA,IAAU,GAAG;oCACrB,MAAM,MAAM,MAAA,GAAS;oCAEd,OAAA;oCAEP,IAAI,MAAM,GAAG;wCACX,OAAO,IAAI,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,EAAa,GAAG;oCAAA,OAAA,IAChC,MAAM,GAAG;wCAClB,OAAO,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,EAAa,CAAC,GAAG;oCACxC;gCACF;gCAEK,IAAA,CAAA,KAAA,CAAM,qBAAA,CAAsB,IAAA,CAAK,kBAAkB;gCACxD,MAAM,IAAI,IAAA,CAAK,KAAA,CAAM,UAAA,CAAW,IAAA,CAAK,OAAA,CAAQ,QAAQ;gCACrD,IAAI,OAAO,IAAI;gCAGf,8MAAO,YAAA,CAAU,KAAA,CAAM,MAAM,IAAA,CAAK,WAAA,EAAa,IAAA,CAAK,WAAW;gCAEzD,MAAA,IAAI,IAAI,KAAK,GAAA,wMAAI,YAAA,CAAU,OAAA,GAAU,IAAA,CAAK,MAAA,CAAO,GAAA,GAAM,GAAG;gCAGhE,IAAI,gNAAS,YAAA,CAAU,OAAA,GAAA,CAAW,KAAK,IAAA,CAAK,IAAI,IAAI,IAAI,CAAA;gCAGpD,IAAA,SAAS,IAAA,CAAK,MAAA,EAAQ;oCACxB,SAAS,IAAA,CAAK,MAAA;gCAAA,OAAA,IACL,SAAS,IAAA,CAAK,MAAA,EAAQ;oCAC/B,SAAS,IAAA,CAAK,MAAA;gCAChB;gCAEA,MAAM,cAAc,IAAI,KAAK,GAAA,wMAAI,YAAA,CAAU,OAAA,GAAA,CAAW,SAAS,CAAA,CAAE;gCACjE,OAAO,IAAI;gCAEX,IAAA,CAAK,MAAA,CAAO,MAAM;gCACb,IAAA,CAAA,oBAAA,CAAqB,IAAA,CAAK,UAAA,CAAW,MAAM,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU,KAAK,CAAC;4BAC/E;4BAEA,IAAI,IAAA,CAAK,KAAA,EAAO;gCACd,IAAA,CAAK,WAAA,CAAY;gCACjB,IAAA,CAAK,QAAA,CAAS;4BAChB;4BAEK,IAAA,CAAA,aAAA,CAAc,MAAM,IAAA,EAAM,KAAK;4BAGpC,IAAA,CAAK,aAAA,CAAc,YAAY;4BAE/B,IAAA,CAAK,aAAA,CAAc,SAAS;4BAE5B;oBACJ;gBACF;YACF;QAAA;QAGM,cAAA,IAAA,EAAA,oBAAmB,CAAC,OAAqB,cAA+B;YAC1E,IAAA,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,UAAA,EAAY;gBAEnC,IAAA,CAAK,aAAA,CAAc,WAAW;gBAE9B,IAAA,CAAK,SAAA,CAAU,MAAM,OAAA,EAAS,MAAM,OAAO;gBAE3C,OAAQ,WAAW;oBACjB,KAAK;wBACH,IAAI,CAAC,IAAA,CAAK,SAAA,EAAW;wBAEjB,IAAA,IAAA,CAAK,YAAA,IAAgB,CAAA,GAAI;4BAC3B,qBAAqB,IAAA,CAAK,YAAY;4BACtC,IAAA,CAAK,YAAA,GAAe,CAAA;4BACpB,IAAA,CAAK,UAAA,GAAa,CAAA;4BAElB,IAAA,CAAK,cAAA,CAAe,KAAK;4BAEzB,IAAA,CAAK,aAAA,CAAc,YAAY;wBACjC;wBAEA,IAAI,IAAA,CAAK,MAAA,EAAQ;4BACV,IAAA,CAAA,aAAA,CAAc,MAAM,GAAA,EAAK,IAAI;4BAC5B,MAAA,SAAS,IAAA,CAAK,kBAAA,CAAmB,IAAA,CAAK,MAAA,EAAQ,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAU;4BACzF,IAAI,WAAW,KAAA,GAAW;gCACnB,IAAA,CAAA,oBAAA,CAAqB,IAAA,CAAK,MAAM;4BACvC;4BACA,IAAI,IAAA,CAAK,UAAA,EAAY;gCACnB,IAAA,CAAK,QAAA,CAAS;gCAEd,IAAA,CAAK,aAAA,CAAc,YAAY;4BACjC;wBACF;wBAEA;oBAEF,KAAK;wBACH,IAAI,CAAC,IAAA,CAAK,YAAA,EAAc;wBAEpB,IAAA,IAAA,CAAK,YAAA,IAAgB,CAAA,GAAI;4BAC3B,qBAAqB,IAAA,CAAK,YAAY;4BACtC,IAAA,CAAK,YAAA,GAAe,CAAA;4BACpB,IAAA,CAAK,UAAA,GAAa,CAAA;wBACpB;wBAEA,IAAI,IAAA,CAAK,MAAA,EAAQ;4BACV,IAAA,CAAA,aAAA,CAAc,MAAM,MAAA,EAAQ,IAAI;4BACrC,MAAM,SAAS,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,MAAA,EAAQ,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAA,EAAY,IAAA,CAAK,SAAS;4BAC3G,IAAI,WAAW,KAAA,GAAW;gCACnB,IAAA,CAAA,oBAAA,CAAqB,IAAA,CAAK,MAAM;4BACvC;4BACA,IAAA,CAAK,cAAA,CAAe,IAAI;4BACxB,IAAI,IAAA,CAAK,gBAAA,EAAkB;gCACzB,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,YAAA,GAAe,YAAY,GAAA,CAAI;gCAChD,IAAA,CAAA,aAAA,GAAgB,IAAA,CAAK,UAAA,GAAa;gCAClC,IAAA,CAAA,cAAA,CAAe,IAAA,CAAK,IAAA,CAAK,oBAAoB;gCAC7C,IAAA,CAAA,cAAA,CAAe,IAAA,CAAK,IAAA,CAAK,cAAc;gCAC5C,IAAA,CAAK,MAAA,GAAS;gCACd,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,MAAA;4BACrB;wBACF;wBAGA,IAAA,CAAK,aAAA,CAAc,YAAY;wBAC/B;oBAEF,KAAK;wBACH,IAAI,CAAC,IAAA,CAAK,UAAA,EAAY;wBAElB,IAAA,IAAA,CAAK,MAAA,mNAAkB,oBAAA,EAAmB;4BACxC,IAAA,IAAA,CAAK,YAAA,IAAgB,CAAA,GAAI;gCAC3B,qBAAqB,IAAA,CAAK,YAAY;gCACtC,IAAA,CAAK,YAAA,GAAe,CAAA;gCACpB,IAAA,CAAK,UAAA,GAAa,CAAA;gCAElB,IAAA,CAAK,cAAA,CAAe,KAAK;gCAEzB,IAAA,CAAK,aAAA,CAAc,YAAY;4BACjC;4BAEK,IAAA,CAAA,aAAA,CAAc,MAAM,GAAA,EAAK,IAAI;4BAClC,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,IAAA,CAAK,YAAA,CAAa,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAU,EAAE,CAAA,GAAI,GAAG;4BAC1F,IAAA,CAAA,sBAAA,CAAuB,IAAA,CAAK,IAAA,CAAK,oBAAoB;wBAC5D;wBACA;oBAEF,KAAK;wBACH,IAAI,CAAC,IAAA,CAAK,UAAA,EAAY;wBAElB,IAAA,IAAA,CAAK,YAAA,IAAgB,CAAA,GAAI;4BAC3B,qBAAqB,IAAA,CAAK,YAAY;4BACtC,IAAA,CAAK,YAAA,GAAe,CAAA;4BACpB,IAAA,CAAK,UAAA,GAAa,CAAA;4BAElB,IAAA,CAAK,cAAA,CAAe,KAAK;4BAEzB,IAAA,CAAK,aAAA,CAAc,YAAY;wBACjC;wBAEK,IAAA,CAAA,aAAA,CAAc,MAAM,KAAA,EAAO,IAAI;wBACpC,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,IAAA,CAAK,YAAA,CAAa,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAU,EAAE,CAAA,GAAI,GAAG;wBAC1F,IAAA,CAAA,sBAAA,CAAuB,IAAA,CAAK,IAAA,CAAK,oBAAoB;wBAC1D;gBACJ;YACF;QAAA;QAGM,cAAA,IAAA,EAAA,mBAAkB,CAAC,OAAqB,YAA0B;YACpE,IAAA,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,UAAA,EAAY;gBAC7B,MAAA,UAAU,WAAW,IAAA,CAAK,MAAA;gBAChC,IAAA,CAAK,SAAA,CAAU,MAAM,OAAA,EAAS,MAAM,OAAO;gBAE3C,OAAQ,SAAS;oBACf,KAAK,MAAM,GAAA;wBACL,IAAA,IAAA,CAAK,SAAA,IAAa,IAAA,CAAK,MAAA,EAAQ;4BACjC,IAAI,SAAS;gCAIX,IAAA,CAAK,aAAA,CAAc,SAAS;gCAE5B,IAAA,CAAK,aAAA,CAAc,WAAW;gCAEzB,IAAA,CAAA,aAAA,CAAc,SAAS,IAAI;gCAC1B,MAAA,SAAS,IAAA,CAAK,kBAAA,CAAmB,IAAA,CAAK,MAAA,EAAQ,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAU;gCACzF,IAAI,WAAW,KAAA,GAAW;oCACnB,IAAA,CAAA,oBAAA,CAAqB,IAAA,CAAK,MAAM;gCACvC;gCACA,IAAI,IAAA,CAAK,UAAA,EAAY;oCACnB,IAAA,CAAK,QAAA,CAAS;gCAChB;gCAEA,IAAA,CAAK,cAAA,CAAe,KAAK;4BAAA,OACpB;gCAEC,MAAA,SAAS,IAAA,CAAK,kBAAA,CAAmB,IAAA,CAAK,MAAA,EAAQ,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAU;gCACzF,IAAI,WAAW,KAAA,GAAW;oCACnB,IAAA,CAAA,sBAAA,CAAuB,IAAA,CAAK,MAAM;gCACzC;gCACA,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,oBAAA,EAAsB,IAAA,CAAK,sBAAsB,CAAC;4BAC5F;wBACF;wBAEA;oBAEF,KAAK,MAAM,MAAA;wBACL,IAAA,IAAA,CAAK,YAAA,IAAgB,IAAA,CAAK,MAAA,EAAQ;4BACpC,IAAI,SAAS;gCAIX,IAAA,CAAK,aAAA,CAAc,SAAS;gCAE5B,IAAA,CAAK,aAAA,CAAc,WAAW;gCAEzB,IAAA,CAAA,aAAA,CAAc,SAAS,IAAI;gCAChC,MAAM,SAAS,IAAA,CAAK,oBAAA,CAClB,IAAA,CAAK,MAAA,EACL,QAAQ,CAAA,EACR,QAAQ,CAAA,EACR,IAAA,CAAK,UAAA,EACL,IAAA,CAAK,SAAA;gCAEP,IAAI,WAAW,KAAA,GAAW;oCACnB,IAAA,CAAA,oBAAA,CAAqB,IAAA,CAAK,MAAM;gCACvC;gCAEA,IAAI,IAAA,CAAK,UAAA,EAAY;oCACnB,IAAA,CAAK,WAAA,CAAY;gCACnB;gCAEA,IAAA,CAAK,cAAA,CAAe,IAAI;4BAAA,OACnB;gCAEL,MAAM,SAAS,IAAA,CAAK,oBAAA,CAClB,IAAA,CAAK,MAAA,EACL,QAAQ,CAAA,EACR,QAAQ,CAAA,EACR,IAAA,CAAK,UAAA,EACL,IAAA,CAAK,SAAA;gCAEP,IAAI,WAAW,KAAA,GAAW;oCACnB,IAAA,CAAA,sBAAA,CAAuB,IAAA,CAAK,MAAM;gCACzC;gCAEA,MAAM,WAAW,IAAA,CAAK,oBAAA,CAAqB,UAAA,CAAW,IAAA,CAAK,sBAAsB;gCACjF,MAAM,QAAQ,IAAA,CAAK,oBAAA,CAAqB,OAAA,CAAQ,IAAA,CAAK,sBAAsB;gCAC3E,MAAM,SAAS,KAAK,GAAA,CAAI,WAAW,IAAA,CAAK,SAAA,EAAW,KAAK;gCAEnD,IAAA,CAAA,oBAAA,CACH,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,qBAAA,CAAsB,IAAA,CAAK,oBAAA,EAAsB,IAAA,CAAK,sBAAsB,GAAG,MAAM;gCAGxG,IAAI,IAAA,CAAK,gBAAA,EAAkB;oCACzB,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,YAAA;oCACjB,IAAA,CAAA,YAAA,GAAe,YAAY,GAAA;oCAChC,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,aAAA;oCACvB,IAAA,CAAK,aAAA,GAAgB;oCAChB,IAAA,CAAA,cAAA,CAAe,IAAA,CAAK,IAAA,CAAK,cAAc;oCACvC,IAAA,CAAA,cAAA,CAAe,IAAA,CAAK,IAAA,CAAK,sBAAsB;oCACpD,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,MAAA;oCACnB,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,qBAAA,CACjB,IAAA,CAAK,UAAA,EACL,IAAA,CAAK,aAAA,EACL,IAAA,CAAK,SAAA,EACL,IAAA,CAAK,YAAA;gCAET;4BACF;wBACF;wBAEA;oBAEF,KAAK,MAAM,KAAA;wBACT,IAAI,IAAA,CAAK,UAAA,EAAY;4BACnB,IAAI,SAAS;gCAIX,IAAA,CAAK,aAAA,CAAc,SAAS;gCAE5B,IAAA,CAAK,aAAA,CAAc,WAAW;gCAEzB,IAAA,CAAA,aAAA,CAAc,SAAS,IAAI;gCAChC,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,IAAA,CAAK,YAAA,CAAa,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAU,EAAE,CAAA,GAAI,GAAG;gCAC1F,IAAA,CAAA,sBAAA,CAAuB,IAAA,CAAK,IAAA,CAAK,oBAAoB;gCAE1D,IAAI,IAAA,CAAK,UAAA,EAAY;oCACnB,IAAA,CAAK,WAAA,CAAY;gCACnB;gCAEA,IAAA,CAAK,cAAA,CAAe,KAAK;4BAAA,OACpB;gCAEL,MAAM,gBAAgB;gCACtB,IAAA,CAAK,sBAAA,CAAuB,IAAA,CAAK,IAAA,CAAK,YAAA,CAAa,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAU,EAAE,CAAA,GAAI,GAAG;gCAEjG,MAAM,WAAW,IAAA,CAAK,sBAAA,CAAuB,CAAA,GAAI,IAAA,CAAK,oBAAA,CAAqB,CAAA;gCAE3E,IAAI,OAAO;gCAEX,IAAI,WAAW,GAAG;oCAChB,OAAO,IAAI,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,EAAa,CAAC,WAAW,aAAa;gCAAA,OAAA,IACtD,WAAW,GAAG;oCACvB,OAAO,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,EAAa,WAAW,aAAa;gCAC5D;gCAEA,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,UAAA,CAAW,MAAM,IAAA,CAAK,OAAA,CAAQ,QAAQ,CAAC;4BACxE;wBACF;wBAEA;oBAEF,KAAK,MAAM,GAAA;wBACT,IAAI,IAAA,CAAK,UAAA,IAAc,IAAA,CAAK,MAAA,kNAAkB,qBAAA,EAAmB;4BAC/D,IAAI,SAAS;gCAIX,IAAA,CAAK,aAAA,CAAc,SAAS;gCAE5B,IAAA,CAAK,aAAA,CAAc,WAAW;gCAEzB,IAAA,CAAA,aAAA,CAAc,SAAS,IAAI;gCAChC,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,IAAA,CAAK,YAAA,CAAa,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAU,EAAE,CAAA,GAAI,GAAG;gCAC1F,IAAA,CAAA,sBAAA,CAAuB,IAAA,CAAK,IAAA,CAAK,oBAAoB;gCAE1D,IAAI,IAAA,CAAK,UAAA,EAAY;oCACnB,IAAA,CAAK,WAAA,CAAY;gCACnB;gCAEA,IAAA,CAAK,cAAA,CAAe,KAAK;4BAAA,OACpB;gCAEL,MAAM,gBAAgB;gCACtB,IAAA,CAAK,sBAAA,CAAuB,IAAA,CAAK,IAAA,CAAK,YAAA,CAAa,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAU,EAAE,CAAA,GAAI,GAAG;gCAEjG,MAAM,WAAW,IAAA,CAAK,sBAAA,CAAuB,CAAA,GAAI,IAAA,CAAK,oBAAA,CAAqB,CAAA;gCAE3E,IAAI,OAAO;gCAEX,IAAI,WAAW,GAAG;oCAChB,OAAO,IAAI,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,EAAa,CAAC,WAAW,aAAa;gCAAA,OAAA,IACtD,WAAW,GAAG;oCACvB,OAAO,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,EAAa,WAAW,aAAa;gCAC5D;gCAEK,IAAA,CAAA,KAAA,CAAM,qBAAA,CAAsB,IAAA,CAAK,kBAAkB;gCACxD,MAAM,IAAI,IAAA,CAAK,KAAA,CAAM,UAAA,CAAW,IAAA,CAAK,OAAA,CAAQ,QAAQ;gCACrD,IAAI,OAAO,IAAI;gCAGf,OAAO,mNAAA,CAAU,KAAA,CAAM,MAAM,IAAA,CAAK,WAAA,EAAa,IAAA,CAAK,WAAW;gCAEzD,MAAA,IAAI,IAAI,KAAK,GAAA,CAAI,mNAAA,CAAU,OAAA,GAAU,IAAA,CAAK,SAAA,GAAY,GAAG;gCAG/D,IAAI,+MAAS,aAAA,CAAU,OAAA,GAAA,CAAW,KAAK,IAAA,CAAK,IAAI,IAAI,IAAI,CAAA;gCAGxD,gNAAS,YAAA,CAAU,KAAA,CAAM,QAAQ,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,MAAM;gCAEzD,MAAM,cAAc,IAAI,KAAK,GAAA,wMAAI,YAAA,CAAU,OAAA,GAAA,CAAW,SAAS,CAAA,CAAE;gCACjE,OAAO,IAAI;gCACN,IAAA,CAAA,KAAA,CAAM,qBAAA,CAAsB,IAAA,CAAK,iBAAiB;gCAEvD,IAAA,CAAK,MAAA,CAAO,MAAM;gCAClB,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,UAAA,CAAW,MAAM,IAAA,CAAK,KAAA,EAAO,KAAK,CAAC;gCAGlE,MAAM,YAAY,IAAA,CAAK,OAAA,CAAQ,QAAA,CAC5B,KAAA,CACA,EAAA,GAAA,CAAI,IAAA,CAAK,MAAA,CAAO,QAAQ,EACxB,SAAA,CAAA,EACA,cAAA,CAAe,cAAc,CAAC;gCACjC,IAAA,CAAK,KAAA,CAAM,eAAA,CAAgB,UAAU,CAAA,EAAG,UAAU,CAAA,EAAG,UAAU,CAAC;4BAClE;wBACF;wBAEA;gBACJ;gBAGA,IAAA,CAAK,aAAA,CAAc,YAAY;YACjC;QAAA;QAGM,cAAA,IAAA,EAAA,kBAAiB,MAAY;YAC/B,IAAA,IAAA,CAAK,MAAA,IAAU,MAAM,MAAA,EAAQ;gBAC3B,IAAA,CAAC,IAAA,CAAK,YAAA,EAAc;oBACtB;gBACF;gBAEA,IAAI,IAAA,CAAK,gBAAA,EAAkB;oBAEzB,MAAM,YAAY,YAAY,GAAA,CAAI,IAAI,IAAA,CAAK,YAAA;oBAC3C,IAAI,YAAY,KAAK;wBACnB,MAAM,IAAI,KAAK,GAAA,CAAA,CAAK,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,MAAA,IAAU,CAAC;wBAElD,MAAM,OAAO,IAAA;wBACb,IAAA,CAAK,YAAA,GAAe,OAAO,qBAAA,CAAsB,SAAU,CAAA,EAAG;4BACvD,KAAA,aAAA,CAAc,MAAM,gBAAA,EAAkB,IAAI;4BAC/C,MAAM,eAAe,KAAK,qBAAA,CAAsB,KAAK,cAAA,EAAgB,KAAK,cAAc;4BAEnF,KAAA,cAAA,CAAe,GAAG,cAAc,KAAK,GAAA,CAAI,GAAG,KAAK,IAAI,CAAC;wBAAA,CAC5D;oBAAA,OACI;wBAEA,IAAA,CAAA,aAAA,CAAc,MAAM,IAAA,EAAM,KAAK;wBACpC,IAAA,CAAK,cAAA,CAAe,KAAK;wBAEzB,IAAA,CAAK,aAAA,CAAc,YAAY;oBACjC;gBAAA,OACK;oBACA,IAAA,CAAA,aAAA,CAAc,MAAM,IAAA,EAAM,KAAK;oBACpC,IAAA,CAAK,cAAA,CAAe,KAAK;oBAEzB,IAAA,CAAK,aAAA,CAAc,YAAY;gBACjC;YAAA,OAAA,IACS,IAAA,CAAK,MAAA,IAAU,MAAM,GAAA,IAAO,IAAA,CAAK,MAAA,IAAU,MAAM,IAAA,EAAM;gBAC3D,IAAA,CAAA,aAAA,CAAc,MAAM,IAAA,EAAM,KAAK;gBAEpC,IAAI,IAAA,CAAK,UAAA,EAAY;oBACnB,IAAA,CAAK,WAAA,CAAY;gBACnB;gBAEA,IAAA,CAAK,cAAA,CAAe,KAAK;gBAEzB,IAAA,CAAK,aAAA,CAAc,YAAY;YACjC;YAGA,IAAA,CAAK,aAAA,CAAc,SAAS;QAAA;QAGtB,cAAA,IAAA,EAAA,eAAc,CAAC,UAA8B;YAC/C,IAAA,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,SAAA,IAAa,IAAA,CAAK,KAAA,IAAS,IAAA,CAAK,MAAA,IAAU,IAAA,CAAK,UAAA,EAAY;gBAElF,IAAA,CAAK,aAAA,CAAc,WAAW;gBAE9B,IAAA,CAAK,SAAA,CAAU,MAAM,OAAA,EAAS,MAAM,OAAO;gBAC3C,MAAM,OAAO,IAAA,CAAK,cAAA,CAAe,IAAA,CAAK,YAAA,CAAa,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAU,GAAG,IAAA,CAAK,MAAM;gBAElG,IAAA,QAAQ,IAAA,CAAK,gBAAA,EAAkB;oBACjC,MAAM,OAAO,IAAA;oBACT,IAAA,IAAA,CAAK,YAAA,IAAgB,CAAA,GAAI;wBACpB,OAAA,oBAAA,CAAqB,IAAA,CAAK,YAAY;oBAC/C;oBAEA,IAAA,CAAK,UAAA,GAAa,CAAA;oBAClB,IAAA,CAAK,YAAA,GAAe,OAAO,qBAAA,CAAsB,SAAU,CAAA,EAAG;wBACvD,KAAA,aAAA,CAAc,MAAM,eAAA,EAAiB,IAAI;wBAC9C,KAAK,WAAA,CAAY,GAAG,MAAM,KAAK,kBAAA,EAAoB,KAAK,iBAAiB;oBAAA,CAC1E;gBACQ,OAAA,IAAA,QAAQ,CAAC,IAAA,CAAK,gBAAA,EAAkB;oBACpC,IAAA,CAAA,aAAA,CAAc,MAAM,KAAA,EAAO,IAAI;oBAC/B,IAAA,CAAA,KAAA,CAAM,MAAM,IAAA,CAAK,WAAW;oBAC5B,IAAA,CAAA,aAAA,CAAc,MAAM,IAAA,EAAM,KAAK;oBAEpC,IAAA,CAAK,aAAA,CAAc,YAAY;gBACjC;YACF;YAGA,IAAA,CAAK,aAAA,CAAc,SAAS;QAAA;QAGtB,cAAA,IAAA,EAAA,oBAAmB,MAAY;YACrC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,SAAA,IAAa,IAAA,CAAK,MAAA,IAAU,IAAA,CAAK,UAAA,EAAY;gBAEpE,IAAA,CAAK,aAAA,CAAc,WAAW;gBAEzB,IAAA,CAAA,aAAA,CAAc,MAAM,GAAA,EAAK,IAAI;gBAE7B,IAAA,CAAA,SAAA,CAAA,CACF,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA,GAAU,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA,IAAW,GAAA,CACjE,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA,GAAU,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA,IAAW;gBAG9D,MAAA,SAAS,IAAA,CAAK,kBAAA,CAAmB,IAAA,CAAK,MAAA,EAAQ,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAA,EAAY,IAAI;gBAC/F,IAAI,WAAW,KAAA,GAAW;oBACnB,IAAA,CAAA,oBAAA,CAAqB,IAAA,CAAK,MAAM;gBACvC;gBACK,IAAA,CAAA,sBAAA,CAAuB,IAAA,CAAK,IAAA,CAAK,oBAAoB;gBAE1D,IAAA,CAAK,cAAA,CAAe,KAAK;YAC3B;QAAA;QAGM,cAAA,IAAA,EAAA,mBAAkB,MAAY;YACpC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,SAAA,IAAa,IAAA,CAAK,MAAA,IAAU,IAAA,CAAK,UAAA,EAAY;gBAC/D,IAAA,CAAA,SAAA,CAAA,CACF,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA,GAAU,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA,IAAW,GAAA,CACjE,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA,GAAU,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA,IAAW;gBAGhE,IAAA,IAAA,CAAK,MAAA,IAAU,MAAM,GAAA,EAAK;oBACvB,IAAA,CAAA,aAAA,CAAc,MAAM,GAAA,EAAK,IAAI;oBAC7B,IAAA,CAAA,oBAAA,CAAqB,IAAA,CAAK,IAAA,CAAK,sBAAsB;gBAC5D;gBAEM,MAAA,SAAS,IAAA,CAAK,kBAAA,CAAmB,IAAA,CAAK,MAAA,EAAQ,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAA,EAAY,IAAI;gBAC/F,IAAI,WAAW,KAAA,GAAgB,IAAA,CAAA,sBAAA,CAAuB,IAAA,CAAK,MAAM;gBAC5D,IAAA,CAAA,oBAAA,CAAqB,IAAA,CAAK,GAAA,CAAI,IAAA,CAAK,oBAAA,EAAsB,IAAA,CAAK,sBAAA,EAAwB,IAAI,CAAC;gBAEhG,IAAA,CAAK,aAAA,CAAc,YAAY;YACjC;QAAA;QAGM,cAAA,IAAA,EAAA,kBAAiB,MAAY;YAC9B,IAAA,CAAA,aAAA,CAAc,MAAM,IAAA,EAAM,KAAK;YAEpC,IAAA,CAAK,aAAA,CAAc,SAAS;QAAA;QAGtB,cAAA,IAAA,EAAA,iBAAgB,MAAY;;YAC9B,IAAA,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,YAAA,EAAc;gBAErC,IAAA,CAAK,aAAA,CAAc,WAAW;gBAEzB,IAAA,CAAA,aAAA,CAAc,MAAM,OAAA,EAAS,IAAI;gBAIjC,IAAA,CAAA,oBAAA,GACH,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,EAAG,IAAA,CAAK,aAAA,CAAc,CAAC,CAAC,IAC1D,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,WAAA,CAAY,CAAC,CAAA,EAAG,IAAA,CAAK,WAAA,CAAY,CAAC,CAAC;gBACxD,IAAA,CAAK,sBAAA,GAAyB,IAAA,CAAK,oBAAA;gBAE9B,CAAA,KAAA,IAAA,CAAA,MAAA,KAAA,OAAA,KAAA,IAAA,GAAQ,iBAAA,CAAkB,IAAA,CAAK,aAAA;gBAEpC,IAAI,CAAC,IAAA,CAAK,SAAA,IAAa,CAAC,IAAA,CAAK,UAAA,EAAY;oBACvC,IAAA,CAAK,cAAA,CAAe,IAAI;gBAC1B;YACF;QAAA;QAGM,cAAA,IAAA,EAAA,gBAAe,MAAY;;YACjC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,YAAA,IAAgB,IAAA,CAAK,MAAA,IAAU,IAAA,CAAK,UAAA,EAAY;gBAClE,IAAA,CAAA,SAAA,CAAA,CACF,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA,GAAU,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA,IAAW,GAAA,CACjE,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA,GAAU,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA,IAAW;gBAEhE,IAAA;gBAEA,IAAA,IAAA,CAAK,MAAA,IAAU,MAAM,OAAA,EAAS;oBAC3B,IAAA,CAAA,aAAA,CAAc,MAAM,OAAA,EAAS,IAAI;oBACtC,IAAA,CAAK,oBAAA,GAAuB,IAAA,CAAK,sBAAA;gBACnC;gBAGK,IAAA,CAAA,sBAAA,GACH,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,EAAG,IAAA,CAAK,aAAA,CAAc,CAAC,CAAC,IAC1D,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,WAAA,CAAY,CAAC,CAAA,EAAG,IAAA,CAAK,WAAA,CAAY,CAAC,CAAC;gBAEpD,IAAA,CAAC,IAAA,CAAK,SAAA,EAAW;oBACnB,gBAAgB,2MAAI,UAAA,CAAU,EAAA,qBAAA,CAAsB,IAAA,CAAK,iBAAiB;gBAAA,OAAA,IACjE,IAAA,CAAK,MAAA,EAAQ;oBACjB,IAAA,CAAA,KAAA,CAAM,qBAAA,CAAsB,IAAA,CAAK,iBAAiB;oBACvC,gBAAA,CAAA,KAAA,IAAA,CAAK,kBAAA,CAAmB,IAAA,CAAK,MAAA,EAAQ,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAU,CAAA,KAA1E,OAAA,KAAA,IAAA,GACZ,eAAA,CAAgB,IAAA,CAAK,MAAA,CAAO,UAAA,EAC7B,cAAA,CAAe,IAAI,IAAA,CAAK,MAAA,CAAO,IAAA,EAC/B,GAAA,CAAI,IAAA,CAAK,KAAA;gBACd;gBAEA,MAAM,gNAAS,YAAA,CAAU,OAAA,GAAA,CAAW,IAAA,CAAK,oBAAA,GAAuB,IAAA,CAAK,sBAAA;gBAErE,IAAI,kBAAkB,KAAA,GAAW;oBAC/B,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,OAAA,CAAQ,eAAe,MAAM,CAAC;gBAC/D;gBAEA,IAAA,CAAK,aAAA,CAAc,YAAY;YACjC;QAAA;QAGM,cAAA,IAAA,EAAA,eAAc,MAAY;YAC3B,IAAA,CAAA,aAAA,CAAc,MAAM,IAAA,EAAM,KAAK;YACpC,IAAA,CAAK,cAAA,CAAe,KAAK;YAEzB,IAAA,CAAK,aAAA,CAAc,SAAS;QAAA;QAGtB,cAAA,IAAA,EAAA,gBAAe,MAAY;YAC7B,IAAA,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,UAAA,EAAY;gBAEnC,IAAA,CAAK,aAAA,CAAc,WAAW;gBACzB,IAAA,CAAA,aAAA,CAAc,MAAM,KAAA,EAAO,IAAI;gBAE/B,IAAA,CAAA,oBAAA,GAAuB,IAAA,CAAK,yBAAA,CAA0B,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,EAAG,IAAA,CAAK,aAAA,CAAc,CAAC,CAAC;gBACvG,IAAA,CAAK,sBAAA,GAAyB,IAAA,CAAK,oBAAA;gBAEnC,IAAA,CAAK,cAAA,CAAe,KAAK;YAC3B;QAAA;QAGM,cAAA,IAAA,EAAA,eAAc,MAAY;;YAChC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,UAAA,IAAc,IAAA,CAAK,UAAA,EAAY;gBACjD,IAAA,CAAA,SAAA,CAAA,CACF,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA,GAAU,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA,IAAW,GAAA,CACjE,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA,GAAU,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA,IAAW;gBAEpE,MAAM,cAAc;gBAEhB,IAAA,IAAA,CAAK,MAAA,IAAU,MAAM,KAAA,EAAO;oBAC9B,IAAA,CAAK,oBAAA,GAAuB,IAAA,CAAK,sBAAA;oBAC5B,IAAA,CAAA,aAAA,CAAc,MAAM,KAAA,EAAO,IAAI;gBACtC;gBAEA,IAAA,CAAK,sBAAA,GAAyB,KAAK,GAAA,CACjC,IAAA,CAAK,yBAAA,CAA0B,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,EAAG,IAAA,CAAK,aAAA,CAAc,CAAC,CAAC,GAC3E,cAAc,IAAA,CAAK,WAAA;gBAEf,MAAA,SAAS,IAAA,CAAK,sBAAA,GAAyB,IAAA,CAAK,oBAAA;gBAE9C,IAAA;gBAEA,IAAA,CAAC,IAAA,CAAK,SAAA,EAAW;oBACnB,aAAa,IAAA,CAAK,OAAA,CAAQ,QAAA;gBAAA,OACrB;oBACD,IAAA,IAAA,CAAK,MAAA,mNAAkB,qBAAA,EAAoB;wBAChC,aAAA,CAAA,KAAA,IAAA,CAAK,kBAAA,CAAmB,IAAA,CAAK,MAAA,EAAQ,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAU,CAAA,KAA1E,OAAA,KAAA,IAAA,GACT,eAAA,CAAgB,IAAA,CAAK,MAAA,CAAO,UAAA,EAC7B,cAAA,CAAe,IAAI,IAAA,CAAK,MAAA,CAAO,IAAA,EAC/B,GAAA,CAAI,IAAA,CAAK,OAAA,CAAQ,QAAA;oBAAQ,OAAA,IACnB,IAAA,CAAK,MAAA,mNAAkB,oBAAA,EAAmB;wBACnD,aAAA,CAAa,KAAA,IAAA,CAAK,kBAAA,CAAmB,IAAA,CAAK,MAAA,EAAQ,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAU,CAAA,KAA1E,OAAA,KAAA,IAAA,GACT,eAAA,CAAgB,IAAA,CAAK,MAAA,CAAO,UAAA,EAC7B,GAAA,CAAI,IAAA,CAAK,OAAA,CAAQ,QAAA;oBACtB;gBACF;gBAEA,IAAI,eAAe,KAAA,GAAW;oBAC5B,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,UAAA,CAAW,QAAQ,UAAU,CAAC;gBAC/D;gBAEA,IAAA,CAAK,aAAA,CAAc,YAAY;YACjC;QAAA;QAGM,cAAA,IAAA,EAAA,cAAa,MAAY;YAC1B,IAAA,CAAA,aAAA,CAAc,MAAM,IAAA,EAAM,KAAK;YAEpC,IAAA,CAAK,aAAA,CAAc,SAAS;QAAA;QAGtB,cAAA,IAAA,EAAA,oBAAmB,MAAY;YACrC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,UAAA,IAAc,IAAA,CAAK,UAAA,EAAY;gBAEtD,IAAA,CAAK,aAAA,CAAc,WAAW;gBAEzB,IAAA,CAAA,aAAA,CAAc,MAAM,KAAA,EAAO,IAAI;gBAGpC,IAAI,UAAU;gBACd,IAAI,UAAU;gBACR,MAAA,WAAW,IAAA,CAAK,aAAA,CAAc,MAAA;gBAEpC,IAAA,IAAS,IAAI,GAAG,IAAI,UAAU,IAAK;oBACtB,WAAA,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA;oBACtB,WAAA,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA;gBACnC;gBAEA,IAAA,CAAK,SAAA,CAAU,UAAU,UAAU,UAAU,QAAQ;gBAErD,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,IAAA,CAAK,YAAA,CAAa,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAU,EAAE,CAAA,GAAI,GAAG;gBAC1F,IAAA,CAAA,sBAAA,CAAuB,IAAA,CAAK,IAAA,CAAK,oBAAoB;YAC5D;QAAA;QAGM,cAAA,IAAA,EAAA,mBAAkB,MAAY;YACpC,IAAI,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,UAAA,IAAc,IAAA,CAAK,MAAA,IAAU,IAAA,CAAK,UAAA,EAAY;gBAYrE,IAAI,UAAU;gBACd,IAAI,UAAU;gBACR,MAAA,WAAW,IAAA,CAAK,aAAA,CAAc,MAAA;gBAEpC,IAAA,IAAS,IAAI,GAAG,IAAI,UAAU,IAAK;oBACtB,WAAA,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA;oBACtB,WAAA,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,OAAA;gBACnC;gBAEA,IAAA,CAAK,SAAA,CAAU,UAAU,UAAU,UAAU,QAAQ;gBAErD,MAAM,gBAAgB;gBACtB,IAAA,CAAK,sBAAA,CAAuB,IAAA,CAAK,IAAA,CAAK,YAAA,CAAa,QAAQ,CAAA,EAAG,QAAQ,CAAA,EAAG,IAAA,CAAK,UAAU,EAAE,CAAA,GAAI,GAAG;gBAEjG,MAAM,WAAW,IAAA,CAAK,sBAAA,CAAuB,CAAA,GAAI,IAAA,CAAK,oBAAA,CAAqB,CAAA;gBAE3E,IAAI,OAAO;gBAEX,IAAI,WAAW,GAAG;oBAChB,OAAO,IAAI,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,EAAa,CAAC,WAAW,aAAa;gBAAA,OAAA,IACtD,WAAW,GAAG;oBACvB,OAAO,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,EAAa,WAAW,aAAa;gBAC5D;gBAEK,IAAA,CAAA,KAAA,CAAM,qBAAA,CAAsB,IAAA,CAAK,kBAAkB;gBACxD,MAAM,IAAI,IAAA,CAAK,KAAA,CAAM,UAAA,CAAW,IAAA,CAAK,OAAA,CAAQ,QAAQ;gBACrD,IAAI,OAAO,IAAI;gBAGf,OAAO,mNAAA,CAAU,KAAA,CAAM,MAAM,IAAA,CAAK,WAAA,EAAa,IAAA,CAAK,WAAW;gBAEzD,MAAA,IAAI,IAAI,KAAK,GAAA,wMAAI,YAAA,CAAU,OAAA,GAAU,IAAA,CAAK,SAAA,GAAY,GAAG;gBAG/D,IAAI,gNAAS,YAAA,CAAU,OAAA,GAAA,CAAW,KAAK,IAAA,CAAK,IAAI,IAAI,IAAI,CAAA;gBAGxD,gNAAS,YAAA,CAAU,KAAA,CAAM,QAAQ,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,MAAM;gBAEzD,MAAM,cAAc,IAAI,KAAK,GAAA,wMAAI,YAAA,CAAU,OAAA,GAAA,CAAW,SAAS,CAAA,CAAE;gBACjE,OAAO,IAAI;gBACN,IAAA,CAAA,KAAA,CAAM,qBAAA,CAAsB,IAAA,CAAK,iBAAiB;gBAEvD,IAAA,CAAK,MAAA,CAAO,MAAM;gBAClB,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,UAAA,CAAW,MAAM,IAAA,CAAK,KAAA,EAAO,KAAK,CAAC;gBAGlE,MAAM,YAAY,IAAA,CAAK,OAAA,CAAQ,QAAA,CAC5B,KAAA,CACA,EAAA,GAAA,CAAI,IAAA,CAAK,MAAA,CAAO,QAAQ,EACxB,SAAA,CAAA,EACA,cAAA,CAAe,cAAc,CAAC;gBACjC,IAAA,CAAK,KAAA,CAAM,eAAA,CAAgB,UAAU,CAAA,EAAG,UAAU,CAAA,EAAG,UAAU,CAAC;gBAGhE,IAAA,CAAK,aAAA,CAAc,YAAY;YACjC;QAAA;QAGM,cAAA,IAAA,EAAA,kBAAiB,MAAY;YAC9B,IAAA,CAAA,aAAA,CAAc,MAAM,IAAA,EAAM,KAAK;YAEpC,IAAA,CAAK,aAAA,CAAc,SAAS;QAAA;QAStB;;;;KAAA,GAAA,cAAA,IAAA,EAAA,aAAY,CAAC,SAAiB,YAA0B;YAC9D,QAAQ,CAAA,GAAI;YACZ,QAAQ,CAAA,GAAI;QAAA;QAMN;;KAAA,GAAA,cAAA,IAAA,EAAA,0BAAyB,MAAY;YACtC,IAAA,CAAA,cAAA,CAAe,OAAO,GAAG,MAAM;YAC/B,IAAA,CAAA,cAAA,CAAe,OAAO,CAAC;YAEvB,IAAA,CAAA,cAAA,CAAe,UAAU,CAAC;YAE1B,IAAA,CAAA,cAAA,CAAe,QAAQ,OAAO;YAC9B,IAAA,CAAA,cAAA,CAAe,QAAQ,CAAC;YAExB,IAAA,CAAA,cAAA,CAAe,OAAO,SAAS,OAAO;YACtC,IAAA,CAAA,cAAA,CAAe,OAAO,GAAG,OAAO;QAAA;QAU/B;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,kBAAiB,CAAC,WAAsB,OAAwB,MAA0B,IAAA,KAAkB;YAClH,MAAM,iBAAiB;gBAAC;gBAAO;gBAAU;gBAAQ,KAAK;aAAA;YACtD,MAAM,aAAa;gBAAC;gBAAG;gBAAG;gBAAG,OAAO;aAAA;YACpC,MAAM,WAAW;gBAAC;gBAAQ;gBAAS,IAAI;aAAA;YACnC,IAAA;YAEJ,IAAI,CAAC,eAAe,QAAA,CAAS,SAAS,KAAK,CAAC,WAAW,QAAA,CAAS,KAAK,KAAK,CAAC,SAAS,QAAA,CAAS,GAAG,GAAG;gBAE1F,OAAA;YACT;YAEA,IAAI,SAAS,SAAS;gBAChB,IAAA,aAAa,UAAU,aAAa,OAAO;oBAEtC,OAAA;gBACT;YACF;YAEA,OAAQ,WAAW;gBACjB,KAAK;oBACH,QAAQ,MAAM,GAAA;oBACd;gBAEF,KAAK;oBACH,QAAQ,MAAM,MAAA;oBACd;gBAEF,KAAK;oBACH,QAAQ,MAAM,KAAA;oBACd;gBAEF,KAAK;oBACH,QAAQ,MAAM,GAAA;oBACd;YACJ;YAEA,MAAM,SAAS;gBACb;gBACA;gBACA;gBACA;YAAA;YAGF,IAAA,IAAS,IAAI,GAAG,IAAI,IAAA,CAAK,YAAA,CAAa,MAAA,EAAQ,IAAK;gBACjD,IAAI,IAAA,CAAK,YAAA,CAAa,CAAC,CAAA,CAAE,KAAA,IAAS,OAAO,KAAA,IAAS,IAAA,CAAK,YAAA,CAAa,CAAC,CAAA,CAAE,GAAA,IAAO,OAAO,GAAA,EAAK;oBACxF,IAAA,CAAK,YAAA,CAAa,MAAA,CAAO,GAAG,GAAG,MAAM;oBAC9B,OAAA;gBACT;YACF;YAEK,IAAA,CAAA,YAAA,CAAa,IAAA,CAAK,MAAM;YACtB,OAAA;QAAA;QASD;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,mBAAkB,CAAC,OAAwB,QAA8C;YAC3F,IAAA;YAEJ,IAAA,IAAS,IAAI,GAAG,IAAI,IAAA,CAAK,YAAA,CAAa,MAAA,EAAQ,IAAK;gBACxC,SAAA,IAAA,CAAK,YAAA,CAAa,CAAC,CAAA;gBAC5B,IAAI,OAAO,KAAA,IAAS,SAAS,OAAO,GAAA,IAAO,KAAK;oBAC9C,OAAO,OAAO,SAAA;gBAChB;YACF;YAEA,IAAI,KAAK;gBACP,IAAA,IAAS,IAAI,GAAG,IAAI,IAAA,CAAK,YAAA,CAAa,MAAA,EAAQ,IAAK;oBACxC,SAAA,IAAA,CAAK,YAAA,CAAa,CAAC,CAAA;oBAC5B,IAAI,OAAO,KAAA,IAAS,SAAS,OAAO,GAAA,IAAO,MAAM;wBAC/C,OAAO,OAAO,SAAA;oBAChB;gBACF;YACF;YAEO,OAAA;QAAA;QASD;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,wBAAuB,CAAC,OAAwB,QAA2C;YAC7F,IAAA;YAEJ,IAAA,IAAS,IAAI,GAAG,IAAI,IAAA,CAAK,YAAA,CAAa,MAAA,EAAQ,IAAK;gBACxC,SAAA,IAAA,CAAK,YAAA,CAAa,CAAC,CAAA;gBAC5B,IAAI,OAAO,KAAA,IAAS,SAAS,OAAO,GAAA,IAAO,KAAK;oBAC9C,OAAO,OAAO,KAAA;gBAChB;YACF;YAEA,IAAI,KAAK;gBACP,IAAA,IAAS,IAAI,GAAG,IAAI,IAAA,CAAK,YAAA,CAAa,MAAA,EAAQ,IAAK;oBACxC,SAAA,IAAA,CAAK,YAAA,CAAa,CAAC,CAAA;oBAC5B,IAAI,OAAO,KAAA,IAAS,SAAS,OAAO,GAAA,IAAO,MAAM;wBAC/C,OAAO,OAAO,KAAA;oBAChB;gBACF;YACF;YAEO,OAAA;QAAA;QASD;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,YAAW,CAAC,IAAkB,OAA6B;YACjE,OAAQ,KAAK,KAAA,CAAM,GAAG,OAAA,GAAU,GAAG,OAAA,EAAS,GAAG,OAAA,GAAU,GAAG,OAAO,IAAI,MAAO,KAAK,EAAA;QAAA;QAO7E;;;KAAA,GAAA,cAAA,IAAA,EAAA,oBAAmB,CAAC,UAA8B;YACxD,IAAA,IAAS,IAAI,GAAG,IAAI,IAAA,CAAK,aAAA,CAAc,MAAA,EAAQ,IAAK;gBAClD,IAAI,IAAA,CAAK,aAAA,CAAc,CAAC,CAAA,CAAE,SAAA,IAAa,MAAM,SAAA,EAAW;oBACtD,IAAA,CAAK,aAAA,CAAc,MAAA,CAAO,GAAG,GAAG,KAAK;oBACrC;gBACF;YACF;QAAA;QAkFM;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,yBAAwB,CAAC,IAAY,IAAY,IAAY,OAAuB;YAC1F,MAAM,IAAI,KAAK;YACT,MAAA,IAAA,CAAK,KAAK,EAAA,IAAM;YACtB,IAAI,KAAK,GAAG;gBACH,OAAA;YACT;YAEA,OAAO,IAAI;QAAA;QASL;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,6BAA4B,CAAC,IAAkB,OAA6B;YAClF,OAAO,KAAK,IAAA,CAAK,KAAK,GAAA,CAAI,GAAG,OAAA,GAAU,GAAG,OAAA,EAAS,CAAC,IAAI,KAAK,GAAA,CAAI,GAAG,OAAA,GAAU,GAAG,OAAA,EAAS,CAAC,CAAC;QAAA;QAStF;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,yBAAwB,CAAC,MAAe,SAA2B;YACpE,IAAA,CAAA,eAAA,CAAgB,eAAA,CAAgB,IAAA,CAAK,kBAAkB;YACvD,IAAA,CAAA,KAAA,CAAM,qBAAA,CAAsB,IAAA,CAAK,eAAe;YAErD,IAAA,CAAK,aAAA,CAAc,YAAA,CAAa,MAAM,IAAI,EAAE,eAAA,CAAgB,IAAA,CAAK,KAAK;YACtE,OAAO,IAAA,CAAK,aAAA,CAAc,SAAA,CAAU,EAAE,KAAA,CAAM;QAAA;QAQtC;;;;KAAA,GAAA,cAAA,IAAA,EAAA,qBAAoB,CAAC,WAAuC;YAClE,MAAM,SAAS;YACf,MAAM,WAAW,OAAO,QAAA,CAAS,UAAA,CAAW,IAAA,CAAK,OAAA,CAAQ,QAAQ;YAEjE,IAAI,yNAAkB,oBAAA,EAAmB;gBACvC,MAAM,kNAAW,YAAA,CAAU,OAAA,GAAU,OAAO,GAAA,GAAM;gBAC5C,MAAA,WAAW,KAAK,IAAA,CAAK,OAAO,MAAA,GAAS,KAAK,GAAA,CAAI,QAAQ,CAAC;gBACtD,OAAA,KAAK,GAAA,CAAI,KAAK,GAAA,CAAI,UAAU,QAAQ,CAAC,IAAI,WAAW;YAAA,OAAA,IAClD,kBAAkB,4NAAA,EAAoB;gBAC/C,OAAO,KAAK,GAAA,CAAI,OAAO,GAAA,EAAK,OAAO,KAAK,IAAI;YAC9C;QAAA;QASM;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,SAAQ,CAAC,OAAgB,MAAc,SAAS,CAAA,KAAY;YAClE,IAAI,IAAA,CAAK,MAAA,EAAQ;gBACT,MAAA,aAAa,MAAM,KAAA;gBAGzB,WAAW,GAAA,CAAI,IAAA,CAAK,OAAA,CAAQ,QAAQ,EAAE,cAAA,CAAe,MAAM;gBAC3D,IAAA,CAAK,kBAAA,CAAmB,eAAA,CAAgB,WAAW,CAAA,EAAG,WAAW,CAAA,EAAG,WAAW,CAAC;gBAE1E,MAAA,iBAAiB,IAAA,CAAK,iBAAA,CAAkB,KAAA,CAAM;gBAC/C,IAAA,CAAA,iBAAA,CAAkB,WAAA,CAAY,IAAA,CAAK,kBAAkB;gBACrD,IAAA,CAAA,iBAAA,CAAkB,SAAA,CAAU,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY,IAAA,CAAK,OAAA,CAAQ,KAAK;gBAE7F,MAAA,kBAAkB,IAAA,CAAK,kBAAA,CAAmB,KAAA,CAAM;gBACjD,IAAA,CAAA,kBAAA,CAAmB,WAAA,CAAY,IAAA,CAAK,kBAAkB;gBACtD,IAAA,CAAA,kBAAA,CAAmB,SAAA,CAAU,IAAA,CAAK,MAAA,CAAO,QAAA,EAAU,IAAA,CAAK,MAAA,CAAO,UAAA,EAAY,IAAA,CAAK,MAAA,CAAO,KAAK;gBAGjG,IAAI,IAAA,CAAK,UAAA,EAAY;oBACnB,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,UAAA,CAAW,MAAM,IAAA,CAAK,OAAA,CAAQ,QAAQ,CAAC;gBACxE;gBAEK,IAAA,CAAA,iBAAA,CAAkB,IAAA,CAAK,cAAc;gBACrC,IAAA,CAAA,kBAAA,CAAmB,IAAA,CAAK,eAAe;YAC9C;QAAA;QAMM;;KAAA,GAAA,cAAA,IAAA,EAAA,YAAW,MAAY;YAC7B,IAAI,IAAA,CAAK,KAAA,EAAO;gBACd,MAAM,QAAQ;gBACd,MAAM,aAAa;gBACf,IAAA,MAAM,WAAW,WAAW;gBAE5B,IAAA,IAAA,CAAK,MAAA,mNAAkB,qBAAA,EAAoB;oBAC7C,MAAM,QAAQ,IAAA,CAAK,MAAA,CAAO,KAAA,GAAQ,IAAA,CAAK,MAAA,CAAO,IAAA;oBAC9C,MAAM,SAAS,IAAA,CAAK,MAAA,CAAO,MAAA,GAAS,IAAA,CAAK,MAAA,CAAO,GAAA;oBAEpC,YAAA,KAAK,GAAA,CAAI,OAAO,MAAM;oBAClC,OAAO,YAAY;oBAEX,OAAA,YAAY,IAAA,CAAK,MAAA,CAAO,IAAA,GAAQ;oBAC3B,YAAA,OAAO,OAAQ,IAAA,CAAK,MAAA,CAAO,IAAA;gBAAA,OAAA,IAC/B,IAAA,CAAK,MAAA,mNAAkB,oBAAA,EAAmB;oBACnD,MAAM,WAAW,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,UAAA,CAAW,IAAA,CAAK,OAAA,CAAQ,QAAQ;oBACtE,MAAM,kNAAW,YAAA,CAAU,OAAA,GAAU,IAAA,CAAK,MAAA,CAAO,GAAA,GAAM;oBACjD,MAAA,WAAW,KAAK,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,MAAA,GAAS,KAAK,GAAA,CAAI,QAAQ,CAAC;oBAEtD,YAAA,KAAK,GAAA,CAAI,KAAK,GAAA,CAAI,UAAU,QAAQ,CAAC,IAAI,WAAW;oBAChE,OAAO,YAAY;oBAEnB,OAAO,YAAY;oBACnB,YAAY,OAAO;gBACrB;gBAEA,IAAI,IAAA,CAAK,KAAA,IAAS,QAAQ,IAAA,CAAK,MAAA,EAAQ;oBACrC,IAAA,CAAK,KAAA,GAAQ,2MAAI,aAAA,CAAW,MAAM,WAAW,OAAO,KAAK;oBACzD,IAAA,CAAK,KAAA,CAAM,QAAA,CAAS,IAAA,CAAK,IAAA,CAAK,OAAA,CAAQ,QAAQ;oBAC9C,IAAA,CAAK,aAAA,CAAc,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,QAAQ;oBAC3C,IAAA,CAAK,KAAA,CAAM,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,UAAU;oBACjD,IAAA,CAAK,KAAA,CAAM,OAAA,CAAQ,KAAK,EAAA,GAAK,GAAG;oBAE3B,IAAA,CAAA,KAAA,CAAM,GAAA,CAAI,IAAA,CAAK,KAAK;gBAC3B;YACF;QAAA;QAGK,cAAA,IAAA,EAAA,WAAU,CAAC,eAAkC;YAGlD,IAAK,eAAuB,UAAU;gBAC5B,QAAA,KAAA,CACN;YAEJ;YACA,IAAA,CAAK,UAAA,GAAa;YAIb,IAAA,CAAA,UAAA,CAAW,KAAA,CAAM,WAAA,GAAc;YACpC,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,eAAe,IAAA,CAAK,aAAa;YAClE,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,eAAe,IAAA,CAAK,aAAa;YAClE,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,iBAAiB,IAAA,CAAK,eAAe;YACtE,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,SAAS,IAAA,CAAK,OAAO;QAAA;QAMjD;;KAAA,GAAA,cAAA,IAAA,EAAA,WAAU,MAAY;;YACvB,IAAA,IAAA,CAAK,YAAA,IAAgB,CAAA,GAAI;gBACpB,OAAA,oBAAA,CAAqB,IAAA,CAAK,YAAY;YAC/C;YAEA,CAAA,KAAA,IAAA,CAAK,UAAA,KAAL,OAAA,KAAA,IAAA,GAAiB,mBAAA,CAAoB,eAAe,IAAA,CAAK,aAAA;YACzD,CAAA,KAAA,IAAA,CAAK,UAAA,KAAL,OAAA,KAAA,IAAA,GAAiB,mBAAA,CAAoB,iBAAiB,IAAA,CAAK,eAAA;YAC3D,CAAA,KAAA,IAAA,CAAK,UAAA,KAAL,OAAA,KAAA,IAAA,GAAiB,mBAAA,CAAoB,SAAS,IAAA,CAAK,OAAA;YACnD,CAAA,KAAA,IAAA,CAAK,UAAA,KAAL,OAAA,KAAA,IAAA,GAAiB,mBAAA,CAAoB,eAAe,IAAA,CAAK,aAAA;YAElD,OAAA,mBAAA,CAAoB,eAAe,IAAA,CAAK,aAAa;YACrD,OAAA,mBAAA,CAAoB,aAAa,IAAA,CAAK,WAAW;YAEjD,OAAA,mBAAA,CAAoB,UAAU,IAAA,CAAK,cAAc;YAEnD,CAAA,KAAA,IAAA,CAAA,KAAA,KAAA,OAAA,KAAA,IAAA,GAAO,MAAA,CAAO,IAAA,CAAK,OAAA;YACxB,IAAA,CAAK,WAAA,CAAY;QAAA;QAMX;;KAAA,GAAA,cAAA,IAAA,EAAA,eAAc,MAAY;YAC5B,IAAA,IAAA,CAAK,KAAA,IAAS,IAAA,CAAK,KAAA,EAAO;gBACvB,IAAA,CAAA,KAAA,CAAM,MAAA,CAAO,IAAA,CAAK,KAAK;gBAC5B,IAAA,CAAK,KAAA,GAAQ;YACf;QAAA;QAQM;;;;KAAA,GAAA,cAAA,IAAA,EAAA,gBAAe,CAAC,MAAsB;YAC5C,OAAO,IAAI,KAAK,GAAA,CAAI,IAAI,GAAG,CAAC;QAAA;QAOtB;;;KAAA,GAAA,cAAA,IAAA,EAAA,kBAAiB,CAAC,aAA4B;YACzC,KAAA,MAAA,SAAS,IAAA,CAAK,OAAA,CAAQ,QAAA,CAAU;gBACvC,MAAyC,QAAA,CAAS,SAAA,CAAU;oBAAE,SAAS,WAAW,IAAI;gBAAA,CAAK;YAC/F;QAAA;QAUM;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,gBAAe,CAAC,SAAiB,SAAiB,WAAiC;YACnF,MAAA,aAAa,OAAO,qBAAA;YACrB,IAAA,CAAA,KAAA,CAAM,IAAA,CAAA,CAAO,UAAU,WAAW,IAAA,IAAQ,WAAW,KAAA,GAAS,IAAI,CAAC;YACnE,IAAA,CAAA,KAAA,CAAM,IAAA,CAAA,CAAO,WAAW,MAAA,GAAS,OAAA,IAAW,WAAW,MAAA,GAAU,IAAI,CAAC;YACpE,OAAA,IAAA,CAAK,KAAA,CAAM,KAAA;QAAM;QAUlB;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,qBAAoB,CAAC,SAAiB,SAAiB,WAAiC;YAC9F,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,YAAA,CAAa,SAAS,SAAS,MAAM,CAAC;YACvD,IAAA,IAAA,CAAK,MAAA,mNAAkB,qBAAA,EAAoB;gBAC7C,IAAA,CAAK,KAAA,CAAM,CAAA,IAAA,CAAM,IAAA,CAAK,MAAA,CAAO,KAAA,GAAQ,IAAA,CAAK,MAAA,CAAO,IAAA,IAAQ;gBACzD,IAAA,CAAK,KAAA,CAAM,CAAA,IAAA,CAAM,IAAA,CAAK,MAAA,CAAO,GAAA,GAAM,IAAA,CAAK,MAAA,CAAO,MAAA,IAAU;YAC3D;YACO,OAAA,IAAA,CAAK,KAAA,CAAM,KAAA;QAAM;QAOlB;;;KAAA,GAAA,cAAA,IAAA,EAAA,aAAY,CAAC,WAAgC;YACnD,IAAI,QAAQ;gBACH,OAAA,MAAA,CAAO,IAAA,CAAK,MAAM;gBACzB,OAAO,YAAA,CAAa;gBAGpB,IAAI,yNAAkB,oBAAA,EAAmB;oBACvC,IAAA,CAAK,KAAA,GAAQ,OAAO,GAAA;oBACpB,IAAA,CAAK,SAAA,GAAY,OAAO,GAAA;gBAC1B;gBAEK,IAAA,CAAA,mBAAA,CAAoB,IAAA,CAAK,OAAO,MAAM;gBACtC,IAAA,CAAA,kBAAA,CAAmB,IAAA,CAAK,IAAA,CAAK,mBAAmB;gBAChD,IAAA,CAAA,sBAAA,CAAuB,IAAA,CAAK,OAAO,gBAAgB;gBACxD,IAAA,CAAK,MAAA,GAAS,OAAO,IAAA;gBACrB,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,MAAA;gBAEvB,IAAA,CAAK,YAAA,GAAe,OAAO,IAAA;gBAC3B,IAAA,CAAK,SAAA,GAAY,OAAO,QAAA,CAAS,UAAA,CAAW,IAAA,CAAK,MAAM,IAAI,OAAO,IAAA;gBAClE,IAAA,CAAK,QAAA,GAAW,IAAA,CAAK,YAAA;gBAErB,IAAA,CAAK,WAAA,GAAc,OAAO,GAAA;gBAC1B,IAAA,CAAK,QAAA,GAAW,OAAO,QAAA,CAAS,UAAA,CAAW,IAAA,CAAK,MAAM,IAAI,OAAO,GAAA;gBACjE,IAAA,CAAK,OAAA,GAAU,IAAA,CAAK,WAAA;gBAEf,IAAA,CAAA,IAAA,CAAK,IAAA,CAAK,OAAO,EAAE;gBACnB,IAAA,CAAA,QAAA,CAAS,IAAA,CAAK,OAAO,EAAE;gBAE5B,IAAA,CAAK,MAAA,GAAS;gBAEd,IAAA,CAAK,MAAA,CAAO,sBAAA;gBAGN,MAAA,WAAW,IAAA,CAAK,iBAAA,CAAkB,MAAM;gBAC9C,IAAI,aAAa,KAAA,GAAW;oBAC1B,IAAA,CAAK,SAAA,GAAY;gBACnB;gBACA,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,SAAS;YAC7C;QAAA;QAkBM;;;;KAAA,GAAA,cAAA,IAAA,EAAA,cAAa,CAAC,UAAmB,aAA2B;YAElE,MAAM,QAAQ,2MAAI,eAAA,CAAa,GAAG,GAAG,UAAU,QAAQ;YACvD,MAAM,SAAS,MAAM,SAAA,CAAU,IAAA,CAAK,SAAS;YAG7C,MAAM,gBAAgB,2MAAI,iBAAA,CAAe,EAAE,aAAA,CAAc,MAAM;YAG/D,MAAM,iBAAiB,2MAAI,oBAAA,CAAkB;gBAAE,OAAO;gBAAU,KAAK;gBAAO,aAAa;gBAAM,SAAS;YAAK,CAAA;YAC7G,MAAM,iBAAiB,IAAI,2NAAA,CAAkB;gBAAE,OAAO;gBAAU,KAAK;gBAAO,aAAa;gBAAM,SAAS;YAAK,CAAA;YAC7G,MAAM,iBAAiB,2MAAI,oBAAA,CAAkB;gBAAE,OAAO;gBAAU,KAAK;gBAAO,aAAa;gBAAM,SAAS;YAAK,CAAA;YAG7G,MAAM,SAAS,2MAAI,OAAA,CAAK,eAAe,cAAc;YACrD,MAAM,SAAS,2MAAI,OAAA,CAAK,eAAe,cAAc;YACrD,MAAM,SAAS,2MAAI,OAAA,CAAK,eAAe,cAAc;YAE/C,MAAA,WAAW,KAAK,EAAA,GAAK;YAC3B,OAAO,QAAA,CAAS,CAAA,GAAI;YACpB,OAAO,QAAA,CAAS,CAAA,GAAI;YAGpB,IAAA,CAAK,kBAAA,CAAmB,QAAA,CAAW,EAAA,WAAA,CAAY,QAAQ;YAClD,IAAA,CAAA,iBAAA,CAAkB,IAAA,CAAK,IAAA,CAAK,kBAAkB;YAEnD,IAAI,IAAA,CAAK,MAAA,IAAU,IAAA,CAAK,MAAA,CAAO,IAAA,IAAQ,GAAG;gBAElC,MAAA,OAAO,IAAI,IAAA,CAAK,MAAA,CAAO,IAAA;gBAC7B,IAAA,CAAK,YAAA,CAAa,SAAA,CAAU,MAAM,MAAM,IAAI;gBACvC,IAAA,CAAA,kBAAA,CAAmB,eAAA,CAAgB,CAAC,SAAS,CAAA,EAAG,CAAC,SAAS,CAAA,EAAG,CAAC,SAAS,CAAC;gBAE7E,IAAA,CAAK,iBAAA,CAAkB,WAAA,CAAY,IAAA,CAAK,kBAAkB,EAAE,WAAA,CAAY,IAAA,CAAK,YAAY;gBACzF,IAAA,CAAK,kBAAA,CAAmB,eAAA,CAAgB,SAAS,CAAA,EAAG,SAAS,CAAA,EAAG,SAAS,CAAC;gBACrE,IAAA,CAAA,iBAAA,CAAkB,WAAA,CAAY,IAAA,CAAK,kBAAkB;YAC5D;YAEK,IAAA,CAAA,iBAAA,CAAkB,SAAA,CAAU,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY,IAAA,CAAK,OAAA,CAAQ,KAAK;YAEnG,IAAA,CAAK,OAAA,CAAQ,KAAA;YAER,IAAA,CAAA,OAAA,CAAQ,GAAA,CAAI,MAAM;YAClB,IAAA,CAAA,OAAA,CAAQ,GAAA,CAAI,MAAM;YAClB,IAAA,CAAA,OAAA,CAAQ,GAAA,CAAI,MAAM;QAAA;QAUjB;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,eAAc,CAAC,MAAc,OAAgB,cAAuB,gBAA+B;YACrG,IAAA,IAAA,CAAK,UAAA,IAAc,CAAA,GAAI;gBAEzB,IAAA,CAAK,UAAA,GAAa;YACpB;YAEI,IAAA,IAAA,CAAK,MAAA,IAAU,MAAM,eAAA,EAAiB;gBAClC,MAAA,YAAY,OAAO,IAAA,CAAK,UAAA;gBACxB,MAAA,WAAW,YAAY,IAAA,CAAK,kBAAA;gBAE7B,IAAA,CAAA,iBAAA,CAAkB,IAAA,CAAK,WAAW;gBAEvC,IAAI,YAAY,GAAG;oBAGZ,IAAA,CAAA,iBAAA,CAAkB,SAAA,CAAU,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY,IAAA,CAAK,OAAA,CAAQ,KAAK;oBAE9F,IAAA,CAAA,KAAA,CAAM,OAAO,IAAA,CAAK,WAAW;oBAElC,IAAA,CAAK,UAAA,GAAa,CAAA;oBACb,IAAA,CAAA,aAAA,CAAc,MAAM,IAAA,EAAM,KAAK;oBACpC,IAAA,CAAK,cAAA,CAAe,KAAK;oBAGzB,IAAA,CAAK,aAAA,CAAc,YAAY;gBAAA,OAC1B;oBACC,MAAA,SAAS,IAAA,CAAK,YAAA,CAAa,QAAQ;oBACzC,MAAM,OAAO,IAAI,SAAS,IAAA,CAAK,WAAA,GAAc;oBAExC,IAAA,CAAA,iBAAA,CAAkB,SAAA,CAAU,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY,IAAA,CAAK,OAAA,CAAQ,KAAK;oBAC9F,IAAA,CAAA,KAAA,CAAM,OAAO,MAAM,MAAM;oBAG9B,IAAA,CAAK,aAAA,CAAc,YAAY;oBAC/B,MAAM,OAAO,IAAA;oBACb,IAAA,CAAK,YAAA,GAAe,OAAO,qBAAA,CAAsB,SAAU,CAAA,EAAG;wBAC5D,KAAK,WAAA,CAAY,GAAG,OAAO,cAAc,YAAY,KAAA,EAAO;oBAAA,CAC7D;gBACH;YAAA,OACK;gBAGL,IAAA,CAAK,YAAA,GAAe,CAAA;gBACpB,IAAA,CAAK,UAAA,GAAa,CAAA;YACpB;QAAA;QASM;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,kBAAiB,CAAC,MAAc,cAAuB,OAAqB;YAC9E,IAAA,IAAA,CAAK,UAAA,IAAc,CAAA,GAAI;gBAEzB,IAAA,CAAK,UAAA,GAAa;gBAClB,IAAA,CAAK,aAAA,GAAgB;gBACrB,IAAA,CAAK,UAAA,GAAa;YACpB;YAEI,IAAA,IAAA,CAAK,MAAA,IAAU,MAAM,gBAAA,EAAkB;gBAEnC,MAAA,YAAA,CAAa,OAAO,IAAA,CAAK,UAAA,IAAc;gBAC7C,MAAM,IAAI,KAAK,CAAC,IAAA,CAAK,aAAA,GAAgB;gBAErC,IAAI,IAAI,GAAG;oBAEJ,IAAA,CAAA,aAAA,GAAgB,MAAM,CAAC,IAAA,CAAK,aAAA,GAAgB,KAAK,GAAA,CAAI,WAAW,CAAC,IAAI,KAAK,YAAY;oBAC3F,IAAA,CAAK,oBAAA,CAAqB,IAAA,CAAK,MAAA,CAAO,cAAc,IAAA,CAAK,aAAa,CAAC;oBAEvE,IAAA,CAAK,aAAA,CAAc,YAAY;oBAC/B,MAAM,OAAO,IAAA;oBACb,IAAA,CAAK,YAAA,GAAe,OAAO,qBAAA,CAAsB,SAAU,CAAA,EAAG;wBACvD,KAAA,cAAA,CAAe,GAAG,cAAc,EAAE;oBAAA,CACxC;gBAAA,OACI;oBACL,IAAA,CAAK,YAAA,GAAe,CAAA;oBACpB,IAAA,CAAK,UAAA,GAAa,CAAA;oBAEb,IAAA,CAAA,aAAA,CAAc,MAAM,IAAA,EAAM,KAAK;oBACpC,IAAA,CAAK,cAAA,CAAe,KAAK;oBAGzB,IAAA,CAAK,aAAA,CAAc,YAAY;gBACjC;YAAA,OACK;gBAGL,IAAA,CAAK,YAAA,GAAe,CAAA;gBACpB,IAAA,CAAK,UAAA,GAAa,CAAA;gBAEd,IAAA,IAAA,CAAK,MAAA,IAAU,MAAM,MAAA,EAAQ;oBAC/B,IAAA,CAAK,cAAA,CAAe,KAAK;oBAEzB,IAAA,CAAK,aAAA,CAAc,YAAY;gBACjC;YACF;QAAA;QASM;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,OAAM,CAAC,IAAa,IAAa,SAAS,KAAA,KAA0B;YAC1E,IAAI,IAAA,CAAK,MAAA,EAAQ;gBACf,MAAM,WAAW,GAAG,KAAA,CAAM,EAAE,GAAA,CAAI,EAAE;gBAE9B,IAAA,IAAA,CAAK,MAAA,mNAAkB,qBAAA,EAAoB;oBAE7C,SAAS,cAAA,CAAe,IAAI,IAAA,CAAK,MAAA,CAAO,IAAI;gBAC9C;gBAEI,IAAA,IAAA,CAAK,MAAA,mNAAkB,oBAAA,IAAqB,QAAQ;oBAEjD,IAAA,CAAA,KAAA,CAAM,qBAAA,CAAsB,IAAA,CAAK,mBAAmB;oBACpD,IAAA,CAAA,KAAA,CAAM,qBAAA,CAAsB,IAAA,CAAK,kBAAkB;oBACxD,MAAM,iBACJ,IAAA,CAAK,KAAA,CAAM,UAAA,CAAW,IAAA,CAAK,KAAK,IAAI,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,UAAA,CAAW,IAAA,CAAK,OAAA,CAAQ,QAAQ;oBAClF,SAAA,cAAA,CAAe,IAAI,cAAc;gBAC5C;gBAEK,IAAA,CAAA,KAAA,CAAM,GAAA,CAAI,SAAS,CAAA,EAAG,SAAS,CAAA,EAAG,CAAC,EAAE,eAAA,CAAgB,IAAA,CAAK,MAAA,CAAO,UAAU;gBAE3E,IAAA,CAAA,KAAA,CAAM,eAAA,CAAgB,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,IAAA,CAAK,KAAA,CAAM,CAAC;gBAEnE,IAAA,CAAK,yBAAA,CAA0B,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,KAAK;YACvD;YACO,OAAA;QAAA;QAMF;;KAAA,GAAA,cAAA,IAAA,EAAA,SAAQ,MAAY;YACzB,IAAI,IAAA,CAAK,MAAA,EAAQ;gBACV,IAAA,CAAA,MAAA,CAAO,IAAA,GAAO,IAAA,CAAK,MAAA;gBAEpB,IAAA,IAAA,CAAK,MAAA,mNAAkB,oBAAA,EAAmB;oBACvC,IAAA,CAAA,MAAA,CAAO,GAAA,GAAM,IAAA,CAAK,KAAA;gBACzB;gBAEK,IAAA,CAAA,MAAA,CAAO,IAAA,GAAO,IAAA,CAAK,QAAA;gBACnB,IAAA,CAAA,MAAA,CAAO,GAAA,GAAM,IAAA,CAAK,OAAA;gBAClB,IAAA,CAAA,kBAAA,CAAmB,IAAA,CAAK,IAAA,CAAK,mBAAmB;gBAChD,IAAA,CAAA,kBAAA,CAAmB,SAAA,CAAU,IAAA,CAAK,MAAA,CAAO,QAAA,EAAU,IAAA,CAAK,MAAA,CAAO,UAAA,EAAY,IAAA,CAAK,MAAA,CAAO,KAAK;gBACjG,IAAA,CAAK,MAAA,CAAO,EAAA,CAAG,IAAA,CAAK,IAAA,CAAK,IAAI;gBAE7B,IAAA,CAAK,MAAA,CAAO,YAAA;gBACZ,IAAA,CAAK,MAAA,CAAO,sBAAA;gBAEP,IAAA,CAAA,iBAAA,CAAkB,IAAA,CAAK,IAAA,CAAK,kBAAkB;gBAC9C,IAAA,CAAA,kBAAA,CAAmB,SAAA,CAAU,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY,IAAA,CAAK,OAAA,CAAQ,KAAK;gBACpG,IAAA,CAAK,OAAA,CAAQ,YAAA;gBAEb,MAAM,WAAW,IAAA,CAAK,iBAAA,CAAkB,IAAA,CAAK,MAAM;gBACnD,IAAI,aAAa,KAAA,GAAW;oBAC1B,IAAA,CAAK,SAAA,GAAY;gBACnB;gBACA,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU,IAAA,CAAK,SAAS;gBAErD,IAAA,CAAK,MAAA,CAAO,MAAA,CAAO,IAAA,CAAK,OAAA,CAAQ,QAAQ;gBAEnC,IAAA,CAAA,aAAA,CAAc,MAAM,IAAA,EAAM,KAAK;gBAGpC,IAAA,CAAK,aAAA,CAAc,YAAY;YACjC;QAAA;QASM;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,UAAS,CAAC,MAAe,UAAkC;YAC3D,MAAA,QAAQ,IAAA,CAAK,OAAA,CAAQ,QAAA;YACtB,IAAA,CAAA,kBAAA,CAAmB,eAAA,CAAgB,CAAC,MAAM,CAAA,EAAG,CAAC,MAAM,CAAA,EAAG,CAAC,MAAM,CAAC;YACpE,IAAA,CAAK,eAAA,CAAgB,gBAAA,CAAiB,MAAM,CAAC,KAAK;YAGlD,IAAA,CAAK,KAAA,CAAM,eAAA,CAAgB,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,MAAM,CAAC;YAC/C,IAAA,CAAA,KAAA,CAAM,QAAA,CAAS,IAAA,CAAK,eAAe;YACnC,IAAA,CAAA,KAAA,CAAM,QAAA,CAAS,IAAA,CAAK,kBAAkB;YAEtC,IAAA,CAAA,yBAAA,CAA0B,IAAA,CAAK,KAAK;YAElC,OAAA;QAAA;QAGF,cAAA,IAAA,EAAA,aAAY,MAAY;YAC7B,IAAI,IAAA,CAAK,MAAA,EAAQ;gBACf,MAAM,QAAQ,KAAK,SAAA,CACjB,IAAA,CAAK,MAAA,mNAAkB,qBAAA,GACnB;oBACE,cAAc;wBACZ,WAAW,IAAA,CAAK,MAAA,CAAO,GAAA;wBACvB,cAAc,IAAA,CAAK,MAAA,CAAO,MAAA;wBAC1B,YAAY,IAAA,CAAK,MAAA,CAAO,IAAA;wBACxB,UAAU,IAAA,CAAK,MAAA,CAAO,EAAA;wBACtB,YAAY,IAAA,CAAK,MAAA,CAAO,IAAA;wBACxB,aAAa,IAAA,CAAK,OAAA,CAAQ,MAAA;oBAC5B;gBAAA,IAEF;oBACE,cAAc;wBACZ,WAAW,IAAA,CAAK,MAAA,CAAO,GAAA;wBACvB,WAAW,IAAA,CAAK,MAAA,CAAO,GAAA;wBACvB,cAAc,IAAA,CAAK,MAAA,CAAO,MAAA;wBAC1B,YAAY,IAAA,CAAK,MAAA,CAAO,IAAA;wBACxB,UAAU,IAAA,CAAK,MAAA,CAAO,EAAA;wBACtB,YAAY,IAAA,CAAK,MAAA,CAAO,IAAA;wBACxB,aAAa,IAAA,CAAK,OAAA,CAAQ,MAAA;oBAC5B;gBACF;gBAGI,UAAA,SAAA,CAAU,SAAA,CAAU,KAAK;YACrC;QAAA;QAGK,cAAA,IAAA,EAAA,cAAa,MAAY;YAC9B,MAAM,OAAO,IAAA;YACb,UAAU,SAAA,CAAU,QAAA,CAAS,EAAE,IAAA,CAAK,SAAS,SAAS,KAAA,EAAO;gBAC3D,KAAK,gBAAA,CAAiB,KAAK;YAAA,CAC5B;QAAA;QAMI;;KAAA,GAAA,cAAA,IAAA,EAAA,aAAY,MAAY;YAC7B,IAAI,CAAC,IAAA,CAAK,MAAA,EAAQ;YAElB,IAAA,CAAK,mBAAA,CAAoB,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,MAAM;YAChD,IAAA,CAAK,kBAAA,CAAmB,IAAA,CAAK,IAAA,CAAK,OAAA,CAAQ,MAAM;YAC3C,IAAA,CAAA,QAAA,GAAW,IAAA,CAAK,MAAA,CAAO,IAAA;YACvB,IAAA,CAAA,OAAA,GAAU,IAAA,CAAK,MAAA,CAAO,GAAA;YACtB,IAAA,CAAA,MAAA,GAAS,IAAA,CAAK,MAAA,CAAO,IAAA;YAC1B,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,EAAE;YAEzB,IAAA,IAAA,CAAK,MAAA,kNAAkB,qBAAA,EAAmB;gBACvC,IAAA,CAAA,KAAA,GAAQ,IAAA,CAAK,MAAA,CAAO,GAAA;YAC3B;QAAA;QAUM;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,cAAa,CAAC,MAAc,OAAgB,cAAc,IAAA,KAAqC;YACrG,IAAI,CAAC,IAAA,CAAK,MAAA,EAAQ;YAEZ,MAAA,aAAa,MAAM,KAAA;YACzB,IAAI,cAAc,IAAI;YAElB,IAAA,IAAA,CAAK,MAAA,mNAAkB,qBAAA,EAAoB;gBAExC,IAAA,CAAA,MAAA,CAAO,IAAA,GAAO,IAAA,CAAK,UAAA;gBACxB,IAAA,CAAK,MAAA,CAAO,IAAA,IAAQ;gBAGpB,IAAI,IAAA,CAAK,MAAA,CAAO,IAAA,GAAO,IAAA,CAAK,OAAA,EAAS;oBAC9B,IAAA,CAAA,MAAA,CAAO,IAAA,GAAO,IAAA,CAAK,OAAA;oBACV,cAAA,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,OAAA;gBAC5B,OAAA,IAAA,IAAA,CAAK,MAAA,CAAO,IAAA,GAAO,IAAA,CAAK,OAAA,EAAS;oBACrC,IAAA,CAAA,MAAA,CAAO,IAAA,GAAO,IAAA,CAAK,OAAA;oBACV,cAAA,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,OAAA;gBACvC;gBAEA,IAAA,CAAK,MAAA,CAAO,sBAAA;gBAEP,IAAA,CAAA,KAAA,CAAM,qBAAA,CAAsB,IAAA,CAAK,iBAAiB;gBAGvD,IAAA,CAAK,YAAA,CAAa,SAAA,CAAU,aAAa,aAAa,WAAW;gBACjE,IAAA,CAAK,kBAAA,CAAmB,eAAA,CAAgB,CAAC,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,CAAC,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,CAAC,IAAA,CAAK,KAAA,CAAM,CAAC;gBAEnF,IAAA,CAAK,KAAA,CAAM,eAAA,CAAgB,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,IAAA,CAAK,KAAA,CAAM,CAAC,EAAE,QAAA,CAAS,IAAA,CAAK,YAAY;gBAC1F,IAAA,CAAA,KAAA,CAAM,QAAA,CAAS,IAAA,CAAK,kBAAkB;gBAGhC,WAAA,GAAA,CAAI,IAAA,CAAK,KAAK;gBAEzB,MAAM,SAAS,WAAW,KAAA,CAAM,EAAE,cAAA,CAAe,WAAW;gBAC5D,WAAW,GAAA,CAAI,MAAM;gBAErB,IAAA,CAAK,KAAA,CAAM,eAAA,CAAgB,WAAW,CAAA,EAAG,WAAW,CAAA,EAAG,WAAW,CAAC;gBAC9D,IAAA,CAAA,KAAA,CAAM,WAAA,CAAY,IAAA,CAAK,KAAK;gBAEjC,IAAA,CAAK,yBAAA,CAA0B,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,KAAK;gBAC9C,OAAA;YACT;YAEI,IAAA,IAAA,CAAK,MAAA,mNAAkB,oBAAA,EAAmB;gBACvC,IAAA,CAAA,KAAA,CAAM,qBAAA,CAAsB,IAAA,CAAK,kBAAkB;gBACnD,IAAA,CAAA,KAAA,CAAM,qBAAA,CAAsB,IAAA,CAAK,iBAAiB;gBAGvD,IAAI,WAAW,IAAA,CAAK,KAAA,CAAM,UAAA,CAAW,UAAU;gBAC3C,IAAA,SAAS,WAAW,WAAW;gBAGnC,MAAM,cAAc,WAAW;gBAC3B,IAAA,cAAc,IAAA,CAAK,WAAA,EAAa;oBAClC,cAAc,IAAA,CAAK,WAAA,GAAc;oBACjC,SAAS,WAAW,WAAW;gBAAA,OAAA,IACtB,cAAc,IAAA,CAAK,WAAA,EAAa;oBACzC,cAAc,IAAA,CAAK,WAAA,GAAc;oBACjC,SAAS,WAAW,WAAW;gBACjC;gBAEI,IAAA,YAAY,WAAW,KAAA,CAAA,EAAQ,GAAA,CAAI,IAAA,CAAK,KAAK,EAAE,SAAA,CAAA,EAAY,cAAA,CAAe,MAAM;gBAEpF,IAAA,CAAK,KAAA,CAAM,eAAA,CAAgB,UAAU,CAAA,EAAG,UAAU,CAAA,EAAG,UAAU,CAAC;gBAEhE,IAAI,aAAa;oBAEf,MAAM,MAAM,IAAA,CAAK,KAAA;oBAEN,WAAA,IAAI,UAAA,CAAW,UAAU;oBACpC,SAAS,WAAW,WAAW;oBACnB,YAAA,WAAW,KAAA,CAAM,EAAE,GAAA,CAAI,IAAA,CAAK,KAAK,EAAE,SAAA,CAAA,EAAY,cAAA,CAAe,MAAM;oBAEhF,IAAA,CAAK,kBAAA,CAAmB,eAAA,CAAgB,IAAI,CAAA,EAAG,IAAI,CAAA,EAAG,IAAI,CAAC;oBAC3D,IAAA,CAAK,YAAA,CAAa,SAAA,CAAU,aAAa,aAAa,WAAW;oBAE5D,IAAA,CAAA,KAAA,CAAM,eAAA,CAAgB,UAAU,CAAA,EAAG,UAAU,CAAA,EAAG,UAAU,CAAC,EAAE,QAAA,CAAS,IAAA,CAAK,kBAAkB;oBAC7F,IAAA,CAAA,KAAA,CAAM,QAAA,CAAS,IAAA,CAAK,YAAY;oBAEhC,IAAA,CAAA,kBAAA,CAAmB,eAAA,CAAgB,CAAC,IAAI,CAAA,EAAG,CAAC,IAAI,CAAA,EAAG,CAAC,IAAI,CAAC;oBAEzD,IAAA,CAAA,KAAA,CAAM,QAAA,CAAS,IAAA,CAAK,kBAAkB;oBAC3C,IAAA,CAAK,yBAAA,CAA0B,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,KAAK;gBAAA,OAChD;oBACA,IAAA,CAAA,yBAAA,CAA0B,IAAA,CAAK,KAAK;gBAC3C;gBAEO,OAAA;YACT;QAAA;QAOM;;;KAAA,GAAA,cAAA,IAAA,EAAA,UAAS,CAAC,UAAwB;YACpC,IAAA,IAAA,CAAK,MAAA,mNAAkB,oBAAA,EAAmB;gBACvC,IAAA,CAAA,MAAA,CAAO,GAAA,0MAAM,YAAA,CAAU,KAAA,CAAM,OAAO,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,MAAM;gBACjE,IAAA,CAAK,MAAA,CAAO,sBAAA;YACd;QAAA;QASK;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,aAAY,CAAC,GAAW,GAAW,MAAoB;YAC5D,IAAI,IAAA,CAAK,MAAA,EAAQ;gBACf,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI,GAAG,GAAG,CAAC;gBACvB,IAAA,CAAK,OAAA,CAAQ,QAAA,CAAS,GAAA,CAAI,GAAG,GAAG,CAAC;gBACjC,MAAM,WAAW,IAAA,CAAK,iBAAA,CAAkB,IAAA,CAAK,MAAM;gBACnD,IAAI,aAAa,KAAA,GAAW;oBAC1B,IAAA,CAAK,SAAA,GAAY;gBACnB;gBACA,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,SAAS;gBACtC,IAAA,CAAA,MAAA,CAAO,MAAA,CAAO,IAAA,CAAK,MAAM;YAChC;QAAA;QAoCM;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,WAAU,CAAC,OAAgB,UAAkC;YACnE,IAAA,CAAK,eAAA,CAAgB,gBAAA,CAAiB,IAAA,CAAK,aAAA,EAAe,KAAK;YAC1D,IAAA,CAAA,kBAAA,CAAmB,eAAA,CAAgB,CAAC,MAAM,CAAA,EAAG,CAAC,MAAM,CAAA,EAAG,CAAC,MAAM,CAAC;YAEpE,IAAA,CAAK,KAAA,CAAM,eAAA,CAAgB,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,MAAM,CAAC;YAC/C,IAAA,CAAA,KAAA,CAAM,QAAA,CAAS,IAAA,CAAK,eAAe;YACnC,IAAA,CAAA,KAAA,CAAM,QAAA,CAAS,IAAA,CAAK,kBAAkB;YAE3C,IAAA,CAAK,KAAA,CAAM,qBAAA,CAAsB,IAAA,CAAK,iBAAiB,EAAE,GAAA,CAAI,KAAK;YAC7D,IAAA,CAAA,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,cAAA,CAAe,IAAA,CAAK,aAAA,EAAe,KAAK;YAC/D,IAAA,CAAA,KAAA,CAAM,GAAA,CAAI,IAAA,CAAK,KAAK;YAEpB,IAAA,CAAA,KAAA,CAAM,eAAA,CAAgB,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,IAAA,CAAK,KAAA,CAAM,CAAC;YAEnE,IAAA,CAAK,yBAAA,CAA0B,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,KAAK;YAC9C,OAAA;QAAA;QASD;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,kBAAiB,CAAC,QAAiB,WAAmC;YAC5E,IAAI,CAAC,IAAA,CAAK,KAAA,EAAc,OAAA;YAElB,MAAA,YAAY,2MAAI,YAAA;YACtB,UAAU,IAAA,GAAO,OAAO,IAAA;YACxB,UAAU,GAAA,GAAM,OAAO,GAAA;YACb,UAAA,aAAA,CAAc,QAAQ,MAAM;YAEtC,MAAM,YAAY,UAAU,gBAAA,CAAiB,IAAA,CAAK,KAAA,CAAM,QAAA,EAAU,IAAI;YACtE,IAAA,IAAS,IAAI,GAAG,IAAI,UAAU,MAAA,EAAQ,IAAK;gBACrC,IAAA,SAAA,CAAU,CAAC,CAAA,CAAE,MAAA,CAAO,IAAA,IAAQ,IAAA,CAAK,OAAA,CAAQ,IAAA,IAAQ,SAAA,CAAU,CAAC,CAAA,CAAE,IAAA,EAAM;oBACtE,OAAO,SAAA,CAAU,CAAC,CAAA,CAAE,KAAA,CAAM,KAAA,CAAM;gBAClC;YACF;YAEO,OAAA;QAAA;QAYD;;;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,wBAAuB,CAC7B,QACA,SACA,SACA,QACA,aACwB;YACxB,IAAI,yNAAkB,qBAAA,EAAoB;gBACxC,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,iBAAA,CAAkB,SAAS,SAAS,MAAM,CAAC;gBAC3D,IAAA,CAAA,KAAA,CAAM,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,CAAC;gBAE5C,MAAM,KAAK,KAAK,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,CAAC;gBACnC,MAAM,KAAK,KAAK,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,CAAC;gBACnC,MAAM,KAAK,KAAK,GAAA,CAAI,IAAA,CAAK,SAAA,EAAW,CAAC;gBAEjC,IAAA,KAAK,MAAM,KAAK,KAAK;oBAEvB,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,KAAK,IAAA,CAAK,KAAA,CAAM,KAAK,EAAA,CAAG,CAAC;gBAAA,OACpC;oBAEA,IAAA,CAAA,KAAA,CAAM,IAAA,CAAM,KAAK,MAAO,KAAK,IAAA,CAAK,KAAK,EAAE,CAAC;gBACjD;gBAEA,OAAO,IAAA,CAAK,KAAA;YACd;YAEA,IAAI,yNAAkB,oBAAA,EAAmB;gBAEvC,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,YAAA,CAAa,SAAS,SAAS,MAAM,CAAC;gBAEtD,IAAA,CAAA,KAAA,CAAM,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,CAAA,CAAE;gBACxC,IAAA,CAAA,KAAA,CAAM,YAAA,CAAa,OAAO,uBAAuB;gBAEtD,MAAM,SAAS,IAAA,CAAK,KAAA,CAAM,KAAA,GAAQ,SAAA,CAAU;gBAC5C,MAAM,sBAAsB,OAAO,QAAA,CAAS,UAAA,CAAW,IAAA,CAAK,OAAA,CAAQ,QAAQ;gBAC5E,MAAM,UAAU,KAAK,GAAA,CAAI,UAAU,CAAC;gBAY9B,MAAA,IAAI,IAAA,CAAK,KAAA,CAAM,CAAA;gBACrB,MAAM,IAAI,KAAK,IAAA,CAAK,KAAK,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,CAAC,IAAI,KAAK,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,CAAC,CAAC;gBAEzE,IAAI,KAAK,GAAG;oBAEV,OAAO,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,QAAQ;oBACxC,OAAA;gBACT;gBAEA,MAAM,IAAI,IAAI;gBACd,MAAM,IAAI;gBASV,IAAI,IAAI,KAAK,GAAA,CAAI,GAAG,CAAC,IAAI;gBACrB,IAAA,IAAI,IAAI,IAAI;gBAChB,IAAI,IAAI,KAAK,GAAA,CAAI,GAAG,CAAC,IAAI;gBACzB,IAAI,QAAQ,KAAK,GAAA,CAAI,GAAG,CAAC,IAAI,IAAI,IAAI;gBAErC,IAAI,SAAS,GAAG;oBAET,IAAA,CAAA,KAAA,CAAM,IAAA,CAAA,CAAM,CAAC,IAAI,KAAK,IAAA,CAAK,KAAK,CAAA,IAAA,CAAM,IAAI,CAAA,CAAE;oBACjD,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAI,IAAA,CAAK,KAAA,CAAM,CAAA,GAAI,CAAC;oBAEpC,MAAM,+MAAQ,YAAA,CAAU,OAAA,GAAU,IAAA,CAAK,KAAA,CAAM,KAAA;oBAE7C,IAAI,SAAS,IAAI;wBAIf,MAAMA,aAAY,KAAK,IAAA,CAAK,KAAK,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,CAAC,IAAI,KAAK,GAAA,CAAI,sBAAsB,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,CAAC,CAAC;wBACvG,OAAO,cAAA,CAAeA,UAAS;wBAC/B,OAAO,CAAA,IAAK;wBACL,OAAA;oBACT;gBACF;gBAUI,IAAA;gBACA,IAAA;gBACJ,IAAI,CAAC,UAAU;gBACf,QAAQ,KAAK,GAAA,CAAI,GAAG,CAAC,IAAI,IAAI,IAAI;gBAC5B,IAAA,CAAA,KAAA,CAAM,IAAA,CAAA,CAAM,CAAC,IAAI,KAAK,IAAA,CAAK,KAAK,CAAA,IAAA,CAAM,IAAI,CAAA,CAAE;gBACjD,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAI,IAAA,CAAK,KAAA,CAAM,CAAA,GAAI,CAAC;gBAEpC,MAAM,YAAY,KAAK,IAAA,CAAK,KAAK,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,CAAC,IAAI,KAAK,GAAA,CAAI,sBAAsB,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,CAAC,CAAC;gBAEvG,OAAO,cAAA,CAAe,SAAS;gBAC/B,OAAO,CAAA,IAAK;gBACL,OAAA;YACT;QAAA;QAYM;;;;;;;;KAAA,GAAA,cAAA,IAAA,EAAA,sBAAqB,CAC3B,QACA,SACA,SACA,QACA,kBAAkB,KAAA,KACM;YACxB,IAAI,yNAAkB,qBAAA,EAAoB;gBACxC,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,iBAAA,CAAkB,SAAS,SAAS,MAAM,CAAC;gBAC3D,IAAA,CAAA,KAAA,CAAM,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,CAAC;gBAErC,OAAA,IAAA,CAAK,KAAA,CAAM,KAAA;YACpB;YAEA,IAAI,yNAAkB,oBAAA,EAAmB;gBACvC,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,YAAA,CAAa,SAAS,SAAS,MAAM,CAAC;gBAGtD,IAAA,CAAA,KAAA,CAAM,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,CAAA,CAAE;gBACxC,IAAA,CAAA,KAAA,CAAM,YAAA,CAAa,OAAO,uBAAuB;gBAEtD,MAAM,SAAS,IAAA,CAAK,KAAA,CAAM,KAAA,GAAQ,SAAA,CAAU;gBAYtC,MAAA,IAAI,IAAA,CAAK,KAAA,CAAM,CAAA;gBACrB,MAAM,IAAI,KAAK,IAAA,CAAK,KAAK,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,CAAC,IAAI,KAAK,GAAA,CAAI,IAAA,CAAK,KAAA,CAAM,CAAA,EAAG,CAAC,CAAC;gBACrE,IAAA;gBAEJ,IAAI,iBAAiB;oBACnB,sBAAsB,IAAA,CAAK,KAAA,CACxB,qBAAA,CAAsB,IAAA,CAAK,mBAAmB,EAC9C,UAAA,CAAW,IAAA,CAAK,KAAA,CAAM,qBAAA,CAAsB,IAAA,CAAK,kBAAkB,CAAC;gBAAA,OAClE;oBACL,sBAAsB,OAAO,QAAA,CAAS,UAAA,CAAW,IAAA,CAAK,OAAA,CAAQ,QAAQ;gBACxE;gBASA,IAAI,KAAK,GAAG;oBAEH,OAAA,GAAA,CAAI,GAAG,GAAG,CAAC;oBACX,OAAA;gBACT;gBAEA,MAAM,IAAI,IAAI;gBACd,MAAM,IAAI;gBACJ,MAAA,IAAI,CAAC,IAAI;gBAEf,MAAM,YAAY,KAAK,IAAA,CAAK,KAAK,GAAA,CAAI,GAAG,CAAC,IAAI,KAAK,GAAA,CAAI,GAAG,CAAC,CAAC;gBAC3D,OAAO,cAAA,CAAe,SAAS;gBAC/B,OAAO,CAAA,GAAI;gBACJ,OAAA;YACT;QAAA;QAMM;;KAAA,GAAA,cAAA,IAAA,EAAA,qBAAoB,MAAY;YACtC,IAAI,CAAC,IAAA,CAAK,MAAA,EAAQ;YAGlB,IAAA,CAAK,kBAAA,CAAmB,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,MAAM;YAC/C,IAAA,CAAK,iBAAA,CAAkB,IAAA,CAAK,IAAA,CAAK,OAAA,CAAQ,MAAM;YAE3C,IAAA,IAAA,CAAK,MAAA,mNAAkB,qBAAA,EAAoB;gBAC7C,IAAA,CAAK,sBAAA,CAAuB,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,gBAAgB;gBAC7D,IAAA,CAAK,MAAA,CAAO,sBAAA;gBACP,IAAA,CAAA,UAAA,GAAa,IAAA,CAAK,MAAA,CAAO,IAAA;YAChC;YAEI,IAAA,IAAA,CAAK,MAAA,mNAAkB,oBAAA,EAAmB;gBACvC,IAAA,CAAA,SAAA,GAAY,IAAA,CAAK,MAAA,CAAO,GAAA;YAC/B;QAAA;QAQM;;;;KAAA,GAAA,cAAA,IAAA,EAAA,iBAAgB,CAAC,UAAkB,mBAAkC;YAC3E,IAAA,CAAK,MAAA,GAAS;YACd,IAAI,gBAAgB;gBAClB,IAAA,CAAK,iBAAA,CAAkB;YACzB;QAAA;QAGK,cAAA,IAAA,EAAA,UAAS,MAAY;YAC1B,MAAM,MAAM;YAGR,IAAA,CAAC,IAAA,CAAK,MAAA,CAAO,MAAA,CAAO,IAAA,CAAK,cAAc,KAAK,IAAA,CAAK,MAAA,EAAQ;gBACtD,IAAA,CAAA,OAAA,CAAQ,QAAA,CAAS,GAAA,CAAI,IAAA,CAAK,MAAA,CAAO,CAAA,EAAG,IAAA,CAAK,MAAA,CAAO,CAAA,EAAG,IAAA,CAAK,MAAA,CAAO,CAAC;gBACrE,MAAM,WAAW,IAAA,CAAK,iBAAA,CAAkB,IAAA,CAAK,MAAM;gBACnD,IAAI,aAAa,KAAA,GAAW;oBAC1B,IAAA,CAAK,SAAA,GAAY;gBACnB;gBACA,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,SAAS;gBACtC,IAAA,CAAA,cAAA,CAAe,IAAA,CAAK,IAAA,CAAK,MAAM;YACtC;YAEA,IAAI,CAAC,IAAA,CAAK,MAAA,EAAQ;YAGd,IAAA,IAAA,CAAK,MAAA,mNAAkB,qBAAA,EAAoB;gBAEzC,IAAA,IAAA,CAAK,MAAA,CAAO,IAAA,GAAO,IAAA,CAAK,OAAA,IAAW,IAAA,CAAK,MAAA,CAAO,IAAA,GAAO,IAAA,CAAK,OAAA,EAAS;oBAChE,MAAA,iNAAU,YAAA,CAAU,KAAA,CAAM,IAAA,CAAK,MAAA,CAAO,IAAA,EAAM,IAAA,CAAK,OAAA,EAAS,IAAA,CAAK,OAAO;oBACvE,IAAA,CAAA,oBAAA,CAAqB,IAAA,CAAK,UAAA,CAAW,UAAU,IAAA,CAAK,MAAA,CAAO,IAAA,EAAM,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU,IAAI,CAAC;gBACpG;YACF;YAEI,IAAA,IAAA,CAAK,MAAA,mNAAkB,oBAAA,EAAmB;gBAE5C,MAAM,WAAW,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,UAAA,CAAW,IAAA,CAAK,OAAA,CAAQ,QAAQ;gBAEtE,IAAI,WAAW,IAAA,CAAK,WAAA,GAAc,OAAO,WAAW,IAAA,CAAK,WAAA,GAAc,KAAK;oBAC1E,MAAM,qNAAc,YAAA,CAAU,KAAA,CAAM,UAAU,IAAA,CAAK,WAAA,EAAa,IAAA,CAAK,WAAW;oBAC3E,IAAA,CAAA,oBAAA,CAAqB,IAAA,CAAK,UAAA,CAAW,cAAc,UAAU,IAAA,CAAK,OAAA,CAAQ,QAAQ,CAAC;oBACxF,IAAA,CAAK,iBAAA,CAAkB;gBACzB;gBAGI,IAAA,IAAA,CAAK,MAAA,CAAO,GAAA,GAAM,IAAA,CAAK,MAAA,IAAU,IAAA,CAAK,MAAA,CAAO,GAAA,GAAM,IAAA,CAAK,MAAA,EAAQ;oBAC7D,IAAA,CAAA,MAAA,CAAO,GAAA,0MAAM,YAAA,CAAU,KAAA,CAAM,IAAA,CAAK,MAAA,CAAO,GAAA,EAAK,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,MAAM;oBAC3E,IAAA,CAAK,MAAA,CAAO,sBAAA;gBACd;gBAEA,MAAM,YAAY,IAAA,CAAK,SAAA;gBACvB,MAAM,WAAW,IAAA,CAAK,iBAAA,CAAkB,IAAA,CAAK,MAAM;gBACnD,IAAI,aAAa,KAAA,GAAW;oBAC1B,IAAA,CAAK,SAAA,GAAY;gBACnB;gBAEA,IAAI,YAAY,IAAA,CAAK,SAAA,GAAY,OAAO,YAAY,IAAA,CAAK,SAAA,GAAY,KAAK;oBACxE,MAAM,QAAA,CAAS,IAAA,CAAK,OAAA,CAAQ,KAAA,CAAM,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,KAAA,CAAM,CAAA,GAAI,IAAA,CAAK,OAAA,CAAQ,KAAA,CAAM,CAAA,IAAK;oBAC/E,MAAA,YAAY,IAAA,CAAK,SAAA,GAAY;oBAEnC,MAAM,QAAQ,IAAI,sNAAA,CAAa,GAAG,GAAG,WAAW,SAAS;oBACzD,MAAM,SAAS,MAAM,SAAA,CAAU,IAAA,CAAK,SAAS;oBAC7C,MAAM,gBAAgB,2MAAI,iBAAA,CAAe,EAAE,aAAA,CAAc,MAAM;oBAEpD,IAAA,MAAA,SAAS,IAAA,CAAK,OAAA,CAAQ,QAAA,CAAU;wBACzC,MAAM,QAAQ,IAAA,CAAK,OAAA,CAAQ,QAAA,CAAS,KAAK,CAAA;wBACzC,MAAM,QAAA,GAAW;oBACnB;gBACF;YACF;YAEA,IAAA,CAAK,MAAA,CAAO,MAAA,CAAO,IAAA,CAAK,OAAA,CAAQ,QAAQ;QAAA;QAGlC,cAAA,IAAA,EAAA,oBAAmB,CAAC,SAAuB;YAC3C,MAAA,QAAQ,KAAK,KAAA,CAAM,IAAI;YAEzB,IAAA,MAAM,YAAA,IAAgB,IAAA,CAAK,MAAA,EAAQ;gBACrC,IAAA,CAAK,kBAAA,CAAmB,SAAA,CAAU,MAAM,YAAA,CAAa,YAAA,CAAa,QAAQ;gBACrE,IAAA,CAAA,kBAAA,CAAmB,SAAA,CAAU,IAAA,CAAK,MAAA,CAAO,QAAA,EAAU,IAAA,CAAK,MAAA,CAAO,UAAA,EAAY,IAAA,CAAK,MAAA,CAAO,KAAK;gBAEjG,IAAA,CAAK,MAAA,CAAO,EAAA,CAAG,IAAA,CAAK,MAAM,YAAA,CAAa,QAAQ;gBAC1C,IAAA,CAAA,MAAA,CAAO,IAAA,GAAO,MAAM,YAAA,CAAa,UAAA;gBACjC,IAAA,CAAA,MAAA,CAAO,GAAA,GAAM,MAAM,YAAA,CAAa,SAAA;gBAEhC,IAAA,CAAA,MAAA,CAAO,IAAA,GAAO,MAAM,YAAA,CAAa,UAAA;gBAElC,IAAA,IAAA,CAAK,MAAA,mNAAkB,oBAAA,EAAmB;oBACvC,IAAA,CAAA,MAAA,CAAO,GAAA,GAAM,MAAM,YAAA,CAAa,SAAA;gBACvC;gBAEA,IAAA,CAAK,iBAAA,CAAkB,SAAA,CAAU,MAAM,YAAA,CAAa,WAAA,CAAY,QAAQ;gBACnE,IAAA,CAAA,iBAAA,CAAkB,SAAA,CAAU,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY,IAAA,CAAK,OAAA,CAAQ,KAAK;gBAEnG,IAAA,CAAK,MAAA,CAAO,YAAA;gBACZ,IAAA,CAAK,MAAA,CAAO,sBAAA;gBAEZ,IAAA,CAAK,OAAA,CAAQ,YAAA;gBAEb,MAAM,WAAW,IAAA,CAAK,iBAAA,CAAkB,IAAA,CAAK,MAAM;gBACnD,IAAI,aAAa,KAAA,GAAW;oBAC1B,IAAA,CAAK,SAAA,GAAY;gBACnB;gBACA,MAAM,WAAW,2MAAI,UAAA,CAAA,EAAU,IAAA,CAAK,IAAA,CAAK,kBAAkB;gBAC3D,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU,IAAA,CAAK,SAAS;gBAChD,IAAA,CAAA,kBAAA,CAAmB,IAAA,CAAK,QAAQ;gBAErC,IAAA,CAAK,MAAA,CAAO,MAAA,CAAO,IAAA,CAAK,OAAA,CAAQ,QAAQ;gBACnC,IAAA,CAAA,aAAA,CAAc,MAAM,IAAA,EAAM,KAAK;gBAGpC,IAAA,CAAK,aAAA,CAAc,YAAY;YACjC;QAAA;QA5jFA,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,KAAA,GAAQ;QAEb,IAAA,CAAK,YAAA,GAAe,EAAA;QACpB,IAAA,CAAK,QAAA,GAAW;QAGX,IAAA,CAAA,KAAA,GAAQ,2MAAI,UAAA;QACZ,IAAA,CAAA,KAAA,GAAQ,2MAAI,UAAA;QACZ,IAAA,CAAA,KAAA,GAAQ,2MAAI,UAAA;QAEZ,IAAA,CAAA,KAAA,GAAQ,2MAAI,UAAA;QACZ,IAAA,CAAA,KAAA,GAAQ,2MAAI,UAAA;QAEZ,IAAA,CAAA,KAAA,GAAQ,2MAAI,aAAA;QAGZ,IAAA,CAAA,kBAAA,GAAqB,2MAAI,UAAA;QACzB,IAAA,CAAA,eAAA,GAAkB,2MAAI,UAAA;QACtB,IAAA,CAAA,YAAA,GAAe,2MAAI,UAAA;QAEnB,IAAA,CAAA,aAAA,GAAgB,2MAAI,UAAA;QAGpB,IAAA,CAAA,kBAAA,GAAqB,2MAAI,UAAA;QACzB,IAAA,CAAA,sBAAA,GAAyB,2MAAI,UAAA;QAElC,IAAA,CAAK,SAAA,GAAY;QACZ,IAAA,CAAA,QAAA,GAAW,2MAAI,UAAA;QACpB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,QAAA,GAAW;QAChB,IAAA,CAAK,OAAA,GAAU;QAEV,IAAA,CAAA,iBAAA,GAAoB,2MAAI,UAAA;QAGxB,IAAA,CAAA,IAAA,GAAO,2MAAI,UAAA;QAChB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,KAAA,GAAQ;QACb,IAAA,CAAK,YAAA,GAAe;QACpB,IAAA,CAAK,SAAA,GAAY;QACjB,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,QAAA,GAAW;QACX,IAAA,CAAA,mBAAA,GAAsB,0MAAI,WAAA;QAC1B,IAAA,CAAA,kBAAA,GAAqB,2MAAI,UAAA;QAG9B,IAAA,CAAK,OAAA,GAAU,CAAA;QACf,IAAA,CAAK,WAAA,GAAc,EAAA;QACnB,IAAA,CAAK,aAAA,GAAgB,EAAA;QACrB,IAAA,CAAK,MAAA,GAAS,MAAM,IAAA;QAGpB,IAAA,CAAK,kBAAA,GAAqB;QAC1B,IAAA,CAAK,oBAAA,GAAuB;QAC5B,IAAA,CAAK,sBAAA,GAAyB;QAC9B,IAAA,CAAK,oBAAA,GAAuB;QAC5B,IAAA,CAAK,sBAAA,GAAyB;QAG9B,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,QAAA,GAAW;QAChB,IAAA,CAAK,WAAA,GAAc,EAAA;QACnB,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,YAAA,GAAe;QACpB,IAAA,CAAK,YAAA,GAAe;QACpB,IAAA,CAAK,aAAA,GAAgB;QACrB,IAAA,CAAK,kBAAA,GAAqB;QAGrB,IAAA,CAAA,sBAAA,GAAyB,2MAAI,UAAA;QAC7B,IAAA,CAAA,oBAAA,GAAuB,IAAI,iNAAA;QAGhC,IAAA,CAAK,KAAA,GAAQ;QACR,IAAA,CAAA,aAAA,GAAgB,2MAAI,UAAA;QAGpB,IAAA,CAAA,OAAA,GAAU,2MAAI,QAAA;QACnB,IAAA,CAAK,SAAA,GAAY;QAGjB,IAAA,CAAK,UAAA,GAAa,CAAA;QAClB,IAAA,CAAK,YAAA,GAAe,CAAA;QAGpB,IAAA,CAAK,kBAAA,GAAqB;QAG1B,IAAA,CAAK,SAAA,GAAY;QACjB,IAAA,CAAK,YAAA,GAAe;QACpB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,aAAA,GAAgB;QAChB,IAAA,CAAA,cAAA,GAAiB,2MAAI,UAAA;QACrB,IAAA,CAAA,cAAA,GAAiB,IAAI,iNAAA;QAC1B,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,MAAA,GAAS;QAGd,IAAA,CAAK,aAAA,GAAgB;QACrB,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,aAAA,GAAgB;QACrB,IAAA,CAAK,IAAA,GAAO;QACZ,IAAA,CAAK,gBAAA,GAAmB;QACxB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,MAAA,GAAS;QAEd,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,SAAA,GAAY;QACjB,IAAA,CAAK,YAAA,GAAe;QACpB,IAAA,CAAK,UAAA,GAAa;QAElB,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,OAAA,GAAU;QAGf,IAAA,CAAK,MAAA,GAAS,IAAI,iNAAA,CAAQ,GAAG,GAAG,CAAC;QACjC,IAAA,CAAK,cAAA,GAAiB,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;QAEzC,IAAA,CAAK,SAAA,GAAY;QAGjB,IAAA,CAAK,MAAA,GAAS,MAAM,IAAA;QAEpB,IAAA,CAAK,SAAA,CAAU,MAAM;QAErB,IAAI,IAAA,CAAK,KAAA,EAAO;YACT,IAAA,CAAA,KAAA,CAAM,GAAA,CAAI,IAAA,CAAK,OAAO;QAC7B;QAEA,IAAA,CAAK,WAAA,GAAc,OAAO,gBAAA;QAE1B,IAAA,CAAK,sBAAA,CAAuB;QAE5B,IAAI,IAAA,CAAK,UAAA,EAAiB,IAAA,CAAA,OAAA,CAAQ,IAAA,CAAK,UAAU;QAE1C,OAAA,gBAAA,CAAiB,UAAU,IAAA,CAAK,cAAc;IACvD;IAAA;;;GAAA,GA2vCQ,qBAAqB,cAAA,EAAkD;QACzE,IAAA,CAAA,kBAAA,OAAA,KAAA,IAAA,eAAgB,MAAA,KAAU,IAAA,CAAK,MAAA,EAAQ;YACzC,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,kBAAkB,EAAE,WAAA,CAAY,eAAe,MAAM;YACrE,IAAA,CAAA,KAAA,CAAM,SAAA,CAAU,IAAA,CAAK,MAAA,CAAO,QAAA,EAAU,IAAA,CAAK,MAAA,CAAO,UAAA,EAAY,IAAA,CAAK,MAAA,CAAO,KAAK;YACpF,IAAA,CAAK,MAAA,CAAO,YAAA;YAGR,IAAA,IAAA,CAAK,MAAA,IAAU,MAAM,MAAA,IAAU,IAAA,CAAK,MAAA,IAAU,MAAM,OAAA,IAAW,IAAA,CAAK,MAAA,IAAU,MAAM,gBAAA,EAAkB;gBACnG,IAAA,CAAA,MAAA,CAAO,EAAA,CAAG,IAAA,CAAK,IAAA,CAAK,QAAQ,EAAE,eAAA,CAAgB,IAAA,CAAK,MAAA,CAAO,UAAU;YAC3E;QACF;QAEA,IAAI,kBAAA,OAAA,KAAA,IAAA,eAAgB,MAAA,EAAQ;YAC1B,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,iBAAiB,EAAE,WAAA,CAAY,eAAe,MAAM;YACpE,IAAA,CAAA,KAAA,CAAM,SAAA,CAAU,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY,IAAA,CAAK,OAAA,CAAQ,KAAK;YACvF,IAAA,CAAK,OAAA,CAAQ,YAAA;QACf;QAEA,IAAA,CACG,IAAA,CAAK,MAAA,IAAU,MAAM,KAAA,IAAS,IAAA,CAAK,MAAA,IAAU,MAAM,KAAA,IAAS,IAAA,CAAK,MAAA,IAAU,MAAM,eAAA,KAClF,IAAA,CAAK,MAAA,EACL;YACA,MAAM,WAAW,IAAA,CAAK,iBAAA,CAAkB,IAAA,CAAK,MAAM;YACnD,IAAI,aAAa,KAAA,GAAW;gBAC1B,IAAA,CAAK,SAAA,GAAY;YACnB;YAEA,IAAI,IAAA,CAAK,aAAA,EAAe;gBACtB,MAAM,iBAAiB,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,UAAA,CAAW,IAAA,CAAK,OAAA,CAAQ,QAAQ;gBAEtE,MAAA,KAAK,IAAI,8MAAA;gBACZ,GAAA,aAAA,CAAc,IAAA,CAAK,OAAO;gBACvB,MAAA,SAAS,2MAAI,SAAA;gBACnB,GAAG,iBAAA,CAAkB,MAAM;gBAErB,MAAA,uBAAuB,KAAK,GAAA,CAAI,IAAA,CAAK,SAAA,EAAW,OAAO,MAAA,GAAS,OAAO,MAAA,CAAO,MAAA,CAAQ,CAAA;gBACtF,MAAA,sBAAsB,iBAAiB,IAAA,CAAK,YAAA;gBAElD,MAAM,aAAa,KAAK,GAAA,CAAI,sBAAsB,mBAAmB;gBAChE,IAAA,CAAA,MAAA,CAAO,IAAA,GAAO,iBAAiB;gBAE9B,MAAA,sBAAsB,KAAK,GAAA,CAAI,IAAA,CAAK,QAAA,EAAU,CAAC,OAAO,MAAA,GAAS,OAAO,MAAA,CAAO,MAAA,CAAQ,CAAA;gBACrF,MAAA,qBAAqB,iBAAiB,IAAA,CAAK,WAAA;gBAEjD,MAAM,YAAY,KAAK,GAAA,CAAI,qBAAqB,kBAAkB;gBAC7D,IAAA,CAAA,MAAA,CAAO,GAAA,GAAM,iBAAiB;gBAEnC,IAAA,CAAK,MAAA,CAAO,sBAAA;YAAuB,OAC9B;gBACL,IAAI,SAAS;gBAEb,IAAI,IAAA,CAAK,MAAA,CAAO,IAAA,IAAQ,IAAA,CAAK,YAAA,EAAc;oBACpC,IAAA,CAAA,MAAA,CAAO,IAAA,GAAO,IAAA,CAAK,YAAA;oBACf,SAAA;gBACX;gBAEA,IAAI,IAAA,CAAK,MAAA,CAAO,GAAA,IAAO,IAAA,CAAK,WAAA,EAAa;oBAClC,IAAA,CAAA,MAAA,CAAO,GAAA,GAAM,IAAA,CAAK,WAAA;oBACd,SAAA;gBACX;gBAEA,IAAI,QAAQ;oBACV,IAAA,CAAK,MAAA,CAAO,sBAAA;gBACd;YACF;QACF;IACF;IAAA;;;GAAA,GA4RO,iBAAiB,KAAA,EAAsB;QAC5C,IAAA,CAAK,OAAA,CAAQ,OAAA,GAAU;QAEvB,IAAA,CAAK,aAAA,CAAc,YAAY;IACjC;IAAA;;;;GAAA,GA2bQ,0BAA0B,SAAyB,IAAA,EAAM,SAAyB,IAAA,EAAY;QACpG,IAAI,QAAQ;YACV,IAAI,gBAAgB,MAAA,EAAQ;gBACV,gBAAA,MAAA,CAAO,IAAA,CAAK,MAAM;YAAA,OAC7B;gBACW,gBAAA,MAAA,GAAS,OAAO,KAAA;YAClC;QAAA,OACK;YACL,gBAAgB,MAAA,GAAS;QAC3B;QAEA,IAAI,QAAQ;YACV,IAAI,gBAAgB,MAAA,EAAQ;gBACV,gBAAA,MAAA,CAAO,IAAA,CAAK,MAAM;YAAA,OAC7B;gBACW,gBAAA,MAAA,GAAS,OAAO,KAAA;YAClC;QAAA,OACK;YACL,gBAAgB,MAAA,GAAS;QAC3B;IACF;AAmYF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3823, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3829, "column": 0}, "map": {"version": 3, "file": "TransformControls.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/controls/TransformControls.ts"], "sourcesContent": ["import {\n  BoxGeometry,\n  BufferGeometry,\n  Color,\n  CylinderGeometry,\n  DoubleSide,\n  Euler,\n  Float32BufferAttribute,\n  Line,\n  LineBasicMaterial,\n  Material,\n  Matrix4,\n  Mesh,\n  MeshBasicMaterial,\n  Object3D,\n  OctahedronGeometry,\n  OrthographicCamera,\n  PerspectiveCamera,\n  PlaneGeometry,\n  Quaternion,\n  Raycaster,\n  SphereGeometry,\n  Intersection,\n  TorusGeometry,\n  Vector3,\n  Camera,\n  Vector2,\n} from 'three'\n\nexport interface TransformControlsPointerObject {\n  x: number\n  y: number\n  button: number\n}\n\nclass TransformControls<TCamera extends Camera = Camera> extends Object3D {\n  public readonly isTransformControls = true\n\n  public visible = false\n\n  private domElement: HTMLElement | undefined\n\n  private raycaster = new Raycaster()\n\n  private gizmo: TransformControlsGizmo\n  private plane: TransformControlsPlane\n\n  private tempVector = new Vector3()\n  private tempVector2 = new Vector3()\n  private tempQuaternion = new Quaternion()\n  private unit = {\n    X: new Vector3(1, 0, 0),\n    Y: new Vector3(0, 1, 0),\n    Z: new Vector3(0, 0, 1),\n  }\n\n  private pointStart = new Vector3()\n  private pointEnd = new Vector3()\n  private offset = new Vector3()\n  private rotationAxis = new Vector3()\n  private startNorm = new Vector3()\n  private endNorm = new Vector3()\n  private rotationAngle = 0\n\n  private cameraPosition = new Vector3()\n  private cameraQuaternion = new Quaternion()\n  private cameraScale = new Vector3()\n\n  private parentPosition = new Vector3()\n  private parentQuaternion = new Quaternion()\n  private parentQuaternionInv = new Quaternion()\n  private parentScale = new Vector3()\n\n  private worldPositionStart = new Vector3()\n  private worldQuaternionStart = new Quaternion()\n  private worldScaleStart = new Vector3()\n\n  private worldPosition = new Vector3()\n  private worldQuaternion = new Quaternion()\n  private worldQuaternionInv = new Quaternion()\n  private worldScale = new Vector3()\n\n  private eye = new Vector3()\n\n  private positionStart = new Vector3()\n  private quaternionStart = new Quaternion()\n  private scaleStart = new Vector3()\n\n  private camera: TCamera\n  private object: Object3D | undefined\n  private enabled = true\n  private axis: string | null = null\n  private mode: 'translate' | 'rotate' | 'scale' = 'translate'\n  private translationSnap: number | null = null\n  private rotationSnap: number | null = null\n  private scaleSnap: number | null = null\n  private space = 'world'\n  private size = 1\n  private dragging = false\n  private showX = true\n  private showY = true\n  private showZ = true\n\n  // events\n  private changeEvent = { type: 'change' }\n  private mouseDownEvent = { type: 'mouseDown', mode: this.mode }\n  private mouseUpEvent = { type: 'mouseUp', mode: this.mode }\n  private objectChangeEvent = { type: 'objectChange' }\n\n  constructor(camera: TCamera, domElement: HTMLElement | undefined) {\n    super()\n\n    this.domElement = domElement\n    this.camera = camera\n\n    this.gizmo = new TransformControlsGizmo()\n    this.add(this.gizmo)\n\n    this.plane = new TransformControlsPlane()\n    this.add(this.plane)\n\n    // Defined getter, setter and store for a property\n    const defineProperty = <TValue>(propName: string, defaultValue: TValue): void => {\n      let propValue = defaultValue\n\n      Object.defineProperty(this, propName, {\n        get: function () {\n          return propValue !== undefined ? propValue : defaultValue\n        },\n\n        set: function (value) {\n          if (propValue !== value) {\n            propValue = value\n            this.plane[propName] = value\n            this.gizmo[propName] = value\n\n            this.dispatchEvent({ type: propName + '-changed', value: value })\n            this.dispatchEvent(this.changeEvent)\n          }\n        },\n      })\n\n      //@ts-ignore\n      this[propName] = defaultValue\n      // @ts-ignore\n      this.plane[propName] = defaultValue\n      // @ts-ignore\n      this.gizmo[propName] = defaultValue\n    }\n\n    defineProperty('camera', this.camera)\n    defineProperty('object', this.object)\n    defineProperty('enabled', this.enabled)\n    defineProperty('axis', this.axis)\n    defineProperty('mode', this.mode)\n    defineProperty('translationSnap', this.translationSnap)\n    defineProperty('rotationSnap', this.rotationSnap)\n    defineProperty('scaleSnap', this.scaleSnap)\n    defineProperty('space', this.space)\n    defineProperty('size', this.size)\n    defineProperty('dragging', this.dragging)\n    defineProperty('showX', this.showX)\n    defineProperty('showY', this.showY)\n    defineProperty('showZ', this.showZ)\n    defineProperty('worldPosition', this.worldPosition)\n    defineProperty('worldPositionStart', this.worldPositionStart)\n    defineProperty('worldQuaternion', this.worldQuaternion)\n    defineProperty('worldQuaternionStart', this.worldQuaternionStart)\n    defineProperty('cameraPosition', this.cameraPosition)\n    defineProperty('cameraQuaternion', this.cameraQuaternion)\n    defineProperty('pointStart', this.pointStart)\n    defineProperty('pointEnd', this.pointEnd)\n    defineProperty('rotationAxis', this.rotationAxis)\n    defineProperty('rotationAngle', this.rotationAngle)\n    defineProperty('eye', this.eye)\n\n    // connect events\n    if (domElement !== undefined) this.connect(domElement)\n  }\n\n  private intersectObjectWithRay = (\n    object: Object3D,\n    raycaster: Raycaster,\n    includeInvisible?: boolean,\n  ): false | Intersection => {\n    const allIntersections = raycaster.intersectObject(object, true)\n\n    for (let i = 0; i < allIntersections.length; i++) {\n      if (allIntersections[i].object.visible || includeInvisible) {\n        return allIntersections[i]\n      }\n    }\n\n    return false\n  }\n\n  // Set current object\n  public attach = (object: Object3D): this => {\n    this.object = object\n    this.visible = true\n\n    return this\n  }\n\n  // Detatch from object\n  public detach = (): this => {\n    this.object = undefined\n    this.visible = false\n    this.axis = null\n\n    return this\n  }\n\n  // Reset\n  public reset = (): this => {\n    if (!this.enabled) return this\n\n    if (this.dragging) {\n      if (this.object !== undefined) {\n        this.object.position.copy(this.positionStart)\n        this.object.quaternion.copy(this.quaternionStart)\n        this.object.scale.copy(this.scaleStart)\n        // @ts-ignore\n        this.dispatchEvent(this.changeEvent)\n        // @ts-ignore\n        this.dispatchEvent(this.objectChangeEvent)\n        this.pointStart.copy(this.pointEnd)\n      }\n    }\n\n    return this\n  }\n\n  public updateMatrixWorld = (): void => {\n    if (this.object !== undefined) {\n      this.object.updateMatrixWorld()\n\n      if (this.object.parent === null) {\n        console.error('TransformControls: The attached 3D object must be a part of the scene graph.')\n      } else {\n        this.object.parent.matrixWorld.decompose(this.parentPosition, this.parentQuaternion, this.parentScale)\n      }\n\n      this.object.matrixWorld.decompose(this.worldPosition, this.worldQuaternion, this.worldScale)\n\n      this.parentQuaternionInv.copy(this.parentQuaternion).invert()\n      this.worldQuaternionInv.copy(this.worldQuaternion).invert()\n    }\n\n    this.camera.updateMatrixWorld()\n    this.camera.matrixWorld.decompose(this.cameraPosition, this.cameraQuaternion, this.cameraScale)\n\n    this.eye.copy(this.cameraPosition).sub(this.worldPosition).normalize()\n\n    super.updateMatrixWorld()\n  }\n\n  private pointerHover = (pointer: TransformControlsPointerObject): void => {\n    if (this.object === undefined || this.dragging === true) return\n\n    this.raycaster.setFromCamera((pointer as unknown) as Vector2, this.camera)\n\n    const intersect = this.intersectObjectWithRay(this.gizmo.picker[this.mode], this.raycaster)\n\n    if (intersect) {\n      this.axis = intersect.object.name\n    } else {\n      this.axis = null\n    }\n  }\n\n  private pointerDown = (pointer: TransformControlsPointerObject): void => {\n    if (this.object === undefined || this.dragging === true || pointer.button !== 0) return\n\n    if (this.axis !== null) {\n      this.raycaster.setFromCamera((pointer as unknown) as Vector2, this.camera)\n\n      const planeIntersect = this.intersectObjectWithRay(this.plane, this.raycaster, true)\n\n      if (planeIntersect) {\n        let space = this.space\n\n        if (this.mode === 'scale') {\n          space = 'local'\n        } else if (this.axis === 'E' || this.axis === 'XYZE' || this.axis === 'XYZ') {\n          space = 'world'\n        }\n\n        if (space === 'local' && this.mode === 'rotate') {\n          const snap = this.rotationSnap\n\n          if (this.axis === 'X' && snap) this.object.rotation.x = Math.round(this.object.rotation.x / snap) * snap\n          if (this.axis === 'Y' && snap) this.object.rotation.y = Math.round(this.object.rotation.y / snap) * snap\n          if (this.axis === 'Z' && snap) this.object.rotation.z = Math.round(this.object.rotation.z / snap) * snap\n        }\n\n        this.object.updateMatrixWorld()\n\n        if (this.object.parent) {\n          this.object.parent.updateMatrixWorld()\n        }\n\n        this.positionStart.copy(this.object.position)\n        this.quaternionStart.copy(this.object.quaternion)\n        this.scaleStart.copy(this.object.scale)\n\n        this.object.matrixWorld.decompose(this.worldPositionStart, this.worldQuaternionStart, this.worldScaleStart)\n\n        this.pointStart.copy(planeIntersect.point).sub(this.worldPositionStart)\n      }\n\n      this.dragging = true\n      this.mouseDownEvent.mode = this.mode\n      // @ts-ignore\n      this.dispatchEvent(this.mouseDownEvent)\n    }\n  }\n\n  private pointerMove = (pointer: TransformControlsPointerObject): void => {\n    const axis = this.axis\n    const mode = this.mode\n    const object = this.object\n    let space = this.space\n\n    if (mode === 'scale') {\n      space = 'local'\n    } else if (axis === 'E' || axis === 'XYZE' || axis === 'XYZ') {\n      space = 'world'\n    }\n\n    if (object === undefined || axis === null || this.dragging === false || pointer.button !== -1) return\n\n    this.raycaster.setFromCamera((pointer as unknown) as Vector2, this.camera)\n\n    const planeIntersect = this.intersectObjectWithRay(this.plane, this.raycaster, true)\n\n    if (!planeIntersect) return\n\n    this.pointEnd.copy(planeIntersect.point).sub(this.worldPositionStart)\n\n    if (mode === 'translate') {\n      // Apply translate\n\n      this.offset.copy(this.pointEnd).sub(this.pointStart)\n\n      if (space === 'local' && axis !== 'XYZ') {\n        this.offset.applyQuaternion(this.worldQuaternionInv)\n      }\n\n      if (axis.indexOf('X') === -1) this.offset.x = 0\n      if (axis.indexOf('Y') === -1) this.offset.y = 0\n      if (axis.indexOf('Z') === -1) this.offset.z = 0\n\n      if (space === 'local' && axis !== 'XYZ') {\n        this.offset.applyQuaternion(this.quaternionStart).divide(this.parentScale)\n      } else {\n        this.offset.applyQuaternion(this.parentQuaternionInv).divide(this.parentScale)\n      }\n\n      object.position.copy(this.offset).add(this.positionStart)\n\n      // Apply translation snap\n\n      if (this.translationSnap) {\n        if (space === 'local') {\n          object.position.applyQuaternion(this.tempQuaternion.copy(this.quaternionStart).invert())\n\n          if (axis.search('X') !== -1) {\n            object.position.x = Math.round(object.position.x / this.translationSnap) * this.translationSnap\n          }\n\n          if (axis.search('Y') !== -1) {\n            object.position.y = Math.round(object.position.y / this.translationSnap) * this.translationSnap\n          }\n\n          if (axis.search('Z') !== -1) {\n            object.position.z = Math.round(object.position.z / this.translationSnap) * this.translationSnap\n          }\n\n          object.position.applyQuaternion(this.quaternionStart)\n        }\n\n        if (space === 'world') {\n          if (object.parent) {\n            object.position.add(this.tempVector.setFromMatrixPosition(object.parent.matrixWorld))\n          }\n\n          if (axis.search('X') !== -1) {\n            object.position.x = Math.round(object.position.x / this.translationSnap) * this.translationSnap\n          }\n\n          if (axis.search('Y') !== -1) {\n            object.position.y = Math.round(object.position.y / this.translationSnap) * this.translationSnap\n          }\n\n          if (axis.search('Z') !== -1) {\n            object.position.z = Math.round(object.position.z / this.translationSnap) * this.translationSnap\n          }\n\n          if (object.parent) {\n            object.position.sub(this.tempVector.setFromMatrixPosition(object.parent.matrixWorld))\n          }\n        }\n      }\n    } else if (mode === 'scale') {\n      if (axis.search('XYZ') !== -1) {\n        let d = this.pointEnd.length() / this.pointStart.length()\n\n        if (this.pointEnd.dot(this.pointStart) < 0) d *= -1\n\n        this.tempVector2.set(d, d, d)\n      } else {\n        this.tempVector.copy(this.pointStart)\n        this.tempVector2.copy(this.pointEnd)\n\n        this.tempVector.applyQuaternion(this.worldQuaternionInv)\n        this.tempVector2.applyQuaternion(this.worldQuaternionInv)\n\n        this.tempVector2.divide(this.tempVector)\n\n        if (axis.search('X') === -1) {\n          this.tempVector2.x = 1\n        }\n\n        if (axis.search('Y') === -1) {\n          this.tempVector2.y = 1\n        }\n\n        if (axis.search('Z') === -1) {\n          this.tempVector2.z = 1\n        }\n      }\n\n      // Apply scale\n\n      object.scale.copy(this.scaleStart).multiply(this.tempVector2)\n\n      if (this.scaleSnap && this.object) {\n        if (axis.search('X') !== -1) {\n          this.object.scale.x = Math.round(object.scale.x / this.scaleSnap) * this.scaleSnap || this.scaleSnap\n        }\n\n        if (axis.search('Y') !== -1) {\n          object.scale.y = Math.round(object.scale.y / this.scaleSnap) * this.scaleSnap || this.scaleSnap\n        }\n\n        if (axis.search('Z') !== -1) {\n          object.scale.z = Math.round(object.scale.z / this.scaleSnap) * this.scaleSnap || this.scaleSnap\n        }\n      }\n    } else if (mode === 'rotate') {\n      this.offset.copy(this.pointEnd).sub(this.pointStart)\n\n      const ROTATION_SPEED =\n        20 / this.worldPosition.distanceTo(this.tempVector.setFromMatrixPosition(this.camera.matrixWorld))\n\n      if (axis === 'E') {\n        this.rotationAxis.copy(this.eye)\n        this.rotationAngle = this.pointEnd.angleTo(this.pointStart)\n\n        this.startNorm.copy(this.pointStart).normalize()\n        this.endNorm.copy(this.pointEnd).normalize()\n\n        this.rotationAngle *= this.endNorm.cross(this.startNorm).dot(this.eye) < 0 ? 1 : -1\n      } else if (axis === 'XYZE') {\n        this.rotationAxis.copy(this.offset).cross(this.eye).normalize()\n        this.rotationAngle = this.offset.dot(this.tempVector.copy(this.rotationAxis).cross(this.eye)) * ROTATION_SPEED\n      } else if (axis === 'X' || axis === 'Y' || axis === 'Z') {\n        this.rotationAxis.copy(this.unit[axis])\n\n        this.tempVector.copy(this.unit[axis])\n\n        if (space === 'local') {\n          this.tempVector.applyQuaternion(this.worldQuaternion)\n        }\n\n        this.rotationAngle = this.offset.dot(this.tempVector.cross(this.eye).normalize()) * ROTATION_SPEED\n      }\n\n      // Apply rotation snap\n\n      if (this.rotationSnap) {\n        this.rotationAngle = Math.round(this.rotationAngle / this.rotationSnap) * this.rotationSnap\n      }\n\n      // Apply rotate\n      if (space === 'local' && axis !== 'E' && axis !== 'XYZE') {\n        object.quaternion.copy(this.quaternionStart)\n        object.quaternion\n          .multiply(this.tempQuaternion.setFromAxisAngle(this.rotationAxis, this.rotationAngle))\n          .normalize()\n      } else {\n        this.rotationAxis.applyQuaternion(this.parentQuaternionInv)\n        object.quaternion.copy(this.tempQuaternion.setFromAxisAngle(this.rotationAxis, this.rotationAngle))\n        object.quaternion.multiply(this.quaternionStart).normalize()\n      }\n    }\n\n    // @ts-ignore\n    this.dispatchEvent(this.changeEvent)\n    // @ts-ignore\n    this.dispatchEvent(this.objectChangeEvent)\n  }\n\n  private pointerUp = (pointer: TransformControlsPointerObject): void => {\n    if (pointer.button !== 0) return\n\n    if (this.dragging && this.axis !== null) {\n      this.mouseUpEvent.mode = this.mode\n      // @ts-ignore\n      this.dispatchEvent(this.mouseUpEvent)\n    }\n\n    this.dragging = false\n    this.axis = null\n  }\n\n  private getPointer = (event: Event): TransformControlsPointerObject => {\n    if (this.domElement && this.domElement.ownerDocument?.pointerLockElement) {\n      return {\n        x: 0,\n        y: 0,\n        button: (event as MouseEvent).button,\n      }\n    } else {\n      const pointer = (event as TouchEvent).changedTouches\n        ? (event as TouchEvent).changedTouches[0]\n        : (event as MouseEvent)\n\n      const rect = this.domElement!.getBoundingClientRect()\n\n      return {\n        x: ((pointer.clientX - rect.left) / rect.width) * 2 - 1,\n        y: (-(pointer.clientY - rect.top) / rect.height) * 2 + 1,\n        button: (event as MouseEvent).button,\n      }\n    }\n  }\n\n  private onPointerHover = (event: Event): void => {\n    if (!this.enabled) return\n\n    switch ((event as PointerEvent).pointerType) {\n      case 'mouse':\n      case 'pen':\n        this.pointerHover(this.getPointer(event))\n        break\n    }\n  }\n\n  private onPointerDown = (event: Event): void => {\n    if (!this.enabled || !this.domElement) return\n\n    this.domElement.style.touchAction = 'none' // disable touch scroll\n    this.domElement.ownerDocument.addEventListener('pointermove', this.onPointerMove)\n    this.pointerHover(this.getPointer(event))\n    this.pointerDown(this.getPointer(event))\n  }\n\n  private onPointerMove = (event: Event): void => {\n    if (!this.enabled) return\n\n    this.pointerMove(this.getPointer(event))\n  }\n\n  private onPointerUp = (event: Event): void => {\n    if (!this.enabled || !this.domElement) return\n\n    this.domElement.style.touchAction! = ''\n    this.domElement.ownerDocument.removeEventListener('pointermove', this.onPointerMove)\n\n    this.pointerUp(this.getPointer(event))\n  }\n\n  public getMode = (): TransformControls['mode'] => this.mode\n\n  public setMode = (mode: TransformControls['mode']): void => {\n    this.mode = mode\n  }\n\n  public setTranslationSnap = (translationSnap: number): void => {\n    this.translationSnap = translationSnap\n  }\n\n  public setRotationSnap = (rotationSnap: number): void => {\n    this.rotationSnap = rotationSnap\n  }\n\n  public setScaleSnap = (scaleSnap: number): void => {\n    this.scaleSnap = scaleSnap\n  }\n\n  public setSize = (size: number): void => {\n    this.size = size\n  }\n\n  public setSpace = (space: string): void => {\n    this.space = space\n  }\n\n  public update = (): void => {\n    console.warn(\n      'THREE.TransformControls: update function has no more functionality and therefore has been deprecated.',\n    )\n  }\n\n  public connect = (domElement: HTMLElement): void => {\n    if ((domElement as any) === document) {\n      console.error(\n        'THREE.OrbitControls: \"document\" should not be used as the target \"domElement\". Please use \"renderer.domElement\" instead.',\n      )\n    }\n    this.domElement = domElement\n\n    this.domElement.addEventListener('pointerdown', this.onPointerDown)\n    this.domElement.addEventListener('pointermove', this.onPointerHover)\n    this.domElement.ownerDocument.addEventListener('pointerup', this.onPointerUp)\n  }\n\n  public dispose = (): void => {\n    this.domElement?.removeEventListener('pointerdown', this.onPointerDown)\n    this.domElement?.removeEventListener('pointermove', this.onPointerHover)\n    this.domElement?.ownerDocument?.removeEventListener('pointermove', this.onPointerMove)\n    this.domElement?.ownerDocument?.removeEventListener('pointerup', this.onPointerUp)\n\n    this.traverse((child) => {\n      const mesh = child as Mesh<BufferGeometry, Material>\n      if (mesh.geometry) {\n        mesh.geometry.dispose()\n      }\n      if (mesh.material) {\n        mesh.material.dispose()\n      }\n    })\n  }\n}\n\ntype TransformControlsGizmoPrivateGizmos = {\n  ['translate']: Object3D\n  ['scale']: Object3D\n  ['rotate']: Object3D\n  ['visible']: boolean\n}\n\nclass TransformControlsGizmo extends Object3D {\n  private isTransformControlsGizmo = true\n  public type = 'TransformControlsGizmo'\n\n  private tempVector = new Vector3(0, 0, 0)\n  private tempEuler = new Euler()\n  private alignVector = new Vector3(0, 1, 0)\n  private zeroVector = new Vector3(0, 0, 0)\n  private lookAtMatrix = new Matrix4()\n  private tempQuaternion = new Quaternion()\n  private tempQuaternion2 = new Quaternion()\n  private identityQuaternion = new Quaternion()\n\n  private unitX = new Vector3(1, 0, 0)\n  private unitY = new Vector3(0, 1, 0)\n  private unitZ = new Vector3(0, 0, 1)\n\n  private gizmo: TransformControlsGizmoPrivateGizmos\n  public picker: TransformControlsGizmoPrivateGizmos\n  private helper: TransformControlsGizmoPrivateGizmos\n\n  // these are set from parent class TransformControls\n  private rotationAxis = new Vector3()\n\n  private cameraPosition = new Vector3()\n\n  private worldPositionStart = new Vector3()\n  private worldQuaternionStart = new Quaternion()\n\n  private worldPosition = new Vector3()\n  private worldQuaternion = new Quaternion()\n\n  private eye = new Vector3()\n\n  private camera: PerspectiveCamera | OrthographicCamera = null!\n  private enabled = true\n  private axis: string | null = null\n  private mode: 'translate' | 'rotate' | 'scale' = 'translate'\n  private space = 'world'\n  private size = 1\n  private dragging = false\n  private showX = true\n  private showY = true\n  private showZ = true\n\n  constructor() {\n    super()\n\n    const gizmoMaterial = new MeshBasicMaterial({\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n      side: DoubleSide,\n      fog: false,\n      toneMapped: false,\n    })\n\n    const gizmoLineMaterial = new LineBasicMaterial({\n      depthTest: false,\n      depthWrite: false,\n      transparent: true,\n      linewidth: 1,\n      fog: false,\n      toneMapped: false,\n    })\n\n    // Make unique material for each axis/color\n\n    const matInvisible = gizmoMaterial.clone()\n    matInvisible.opacity = 0.15\n\n    const matHelper = gizmoMaterial.clone()\n    matHelper.opacity = 0.33\n\n    const matRed = gizmoMaterial.clone() as MeshBasicMaterial\n    matRed.color.set(0xff0000)\n\n    const matGreen = gizmoMaterial.clone() as MeshBasicMaterial\n    matGreen.color.set(0x00ff00)\n\n    const matBlue = gizmoMaterial.clone() as MeshBasicMaterial\n    matBlue.color.set(0x0000ff)\n\n    const matWhiteTransparent = gizmoMaterial.clone() as MeshBasicMaterial\n    matWhiteTransparent.opacity = 0.25\n\n    const matYellowTransparent = matWhiteTransparent.clone() as MeshBasicMaterial\n    matYellowTransparent.color.set(0xffff00)\n\n    const matCyanTransparent = matWhiteTransparent.clone() as MeshBasicMaterial\n    matCyanTransparent.color.set(0x00ffff)\n\n    const matMagentaTransparent = matWhiteTransparent.clone() as MeshBasicMaterial\n    matMagentaTransparent.color.set(0xff00ff)\n\n    const matYellow = gizmoMaterial.clone() as MeshBasicMaterial\n    matYellow.color.set(0xffff00)\n\n    const matLineRed = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineRed.color.set(0xff0000)\n\n    const matLineGreen = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineGreen.color.set(0x00ff00)\n\n    const matLineBlue = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineBlue.color.set(0x0000ff)\n\n    const matLineCyan = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineCyan.color.set(0x00ffff)\n\n    const matLineMagenta = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineMagenta.color.set(0xff00ff)\n\n    const matLineYellow = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineYellow.color.set(0xffff00)\n\n    const matLineGray = gizmoLineMaterial.clone() as LineBasicMaterial\n    matLineGray.color.set(0x787878)\n\n    const matLineYellowTransparent = matLineYellow.clone() as LineBasicMaterial\n    matLineYellowTransparent.opacity = 0.25\n\n    // reusable geometry\n\n    const arrowGeometry = new CylinderGeometry(0, 0.05, 0.2, 12, 1, false)\n\n    const scaleHandleGeometry = new BoxGeometry(0.125, 0.125, 0.125)\n\n    const lineGeometry = new BufferGeometry()\n    lineGeometry.setAttribute('position', new Float32BufferAttribute([0, 0, 0, 1, 0, 0], 3))\n\n    const CircleGeometry = (radius: number, arc: number): BufferGeometry => {\n      const geometry = new BufferGeometry()\n      const vertices = []\n\n      for (let i = 0; i <= 64 * arc; ++i) {\n        vertices.push(0, Math.cos((i / 32) * Math.PI) * radius, Math.sin((i / 32) * Math.PI) * radius)\n      }\n\n      geometry.setAttribute('position', new Float32BufferAttribute(vertices, 3))\n\n      return geometry\n    }\n\n    // Special geometry for transform helper. If scaled with position vector it spans from [0,0,0] to position\n\n    const TranslateHelperGeometry = (): BufferGeometry => {\n      const geometry = new BufferGeometry()\n\n      geometry.setAttribute('position', new Float32BufferAttribute([0, 0, 0, 1, 1, 1], 3))\n\n      return geometry\n    }\n\n    // Gizmo definitions - custom hierarchy definitions for setupGizmo() function\n\n    const gizmoTranslate = {\n      X: [\n        [new Mesh(arrowGeometry, matRed), [1, 0, 0], [0, 0, -Math.PI / 2], null, 'fwd'],\n        [new Mesh(arrowGeometry, matRed), [1, 0, 0], [0, 0, Math.PI / 2], null, 'bwd'],\n        [new Line(lineGeometry, matLineRed)],\n      ],\n      Y: [\n        [new Mesh(arrowGeometry, matGreen), [0, 1, 0], null, null, 'fwd'],\n        [new Mesh(arrowGeometry, matGreen), [0, 1, 0], [Math.PI, 0, 0], null, 'bwd'],\n        [new Line(lineGeometry, matLineGreen), null, [0, 0, Math.PI / 2]],\n      ],\n      Z: [\n        [new Mesh(arrowGeometry, matBlue), [0, 0, 1], [Math.PI / 2, 0, 0], null, 'fwd'],\n        [new Mesh(arrowGeometry, matBlue), [0, 0, 1], [-Math.PI / 2, 0, 0], null, 'bwd'],\n        [new Line(lineGeometry, matLineBlue), null, [0, -Math.PI / 2, 0]],\n      ],\n      XYZ: [[new Mesh(new OctahedronGeometry(0.1, 0), matWhiteTransparent.clone()), [0, 0, 0], [0, 0, 0]]],\n      XY: [\n        [new Mesh(new PlaneGeometry(0.295, 0.295), matYellowTransparent.clone()), [0.15, 0.15, 0]],\n        [new Line(lineGeometry, matLineYellow), [0.18, 0.3, 0], null, [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineYellow), [0.3, 0.18, 0], [0, 0, Math.PI / 2], [0.125, 1, 1]],\n      ],\n      YZ: [\n        [new Mesh(new PlaneGeometry(0.295, 0.295), matCyanTransparent.clone()), [0, 0.15, 0.15], [0, Math.PI / 2, 0]],\n        [new Line(lineGeometry, matLineCyan), [0, 0.18, 0.3], [0, 0, Math.PI / 2], [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineCyan), [0, 0.3, 0.18], [0, -Math.PI / 2, 0], [0.125, 1, 1]],\n      ],\n      XZ: [\n        [\n          new Mesh(new PlaneGeometry(0.295, 0.295), matMagentaTransparent.clone()),\n          [0.15, 0, 0.15],\n          [-Math.PI / 2, 0, 0],\n        ],\n        [new Line(lineGeometry, matLineMagenta), [0.18, 0, 0.3], null, [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineMagenta), [0.3, 0, 0.18], [0, -Math.PI / 2, 0], [0.125, 1, 1]],\n      ],\n    }\n\n    const pickerTranslate = {\n      X: [[new Mesh(new CylinderGeometry(0.2, 0, 1, 4, 1, false), matInvisible), [0.6, 0, 0], [0, 0, -Math.PI / 2]]],\n      Y: [[new Mesh(new CylinderGeometry(0.2, 0, 1, 4, 1, false), matInvisible), [0, 0.6, 0]]],\n      Z: [[new Mesh(new CylinderGeometry(0.2, 0, 1, 4, 1, false), matInvisible), [0, 0, 0.6], [Math.PI / 2, 0, 0]]],\n      XYZ: [[new Mesh(new OctahedronGeometry(0.2, 0), matInvisible)]],\n      XY: [[new Mesh(new PlaneGeometry(0.4, 0.4), matInvisible), [0.2, 0.2, 0]]],\n      YZ: [[new Mesh(new PlaneGeometry(0.4, 0.4), matInvisible), [0, 0.2, 0.2], [0, Math.PI / 2, 0]]],\n      XZ: [[new Mesh(new PlaneGeometry(0.4, 0.4), matInvisible), [0.2, 0, 0.2], [-Math.PI / 2, 0, 0]]],\n    }\n\n    const helperTranslate = {\n      START: [[new Mesh(new OctahedronGeometry(0.01, 2), matHelper), null, null, null, 'helper']],\n      END: [[new Mesh(new OctahedronGeometry(0.01, 2), matHelper), null, null, null, 'helper']],\n      DELTA: [[new Line(TranslateHelperGeometry(), matHelper), null, null, null, 'helper']],\n      X: [[new Line(lineGeometry, matHelper.clone()), [-1e3, 0, 0], null, [1e6, 1, 1], 'helper']],\n      Y: [[new Line(lineGeometry, matHelper.clone()), [0, -1e3, 0], [0, 0, Math.PI / 2], [1e6, 1, 1], 'helper']],\n      Z: [[new Line(lineGeometry, matHelper.clone()), [0, 0, -1e3], [0, -Math.PI / 2, 0], [1e6, 1, 1], 'helper']],\n    }\n\n    const gizmoRotate = {\n      X: [\n        [new Line(CircleGeometry(1, 0.5), matLineRed)],\n        [new Mesh(new OctahedronGeometry(0.04, 0), matRed), [0, 0, 0.99], null, [1, 3, 1]],\n      ],\n      Y: [\n        [new Line(CircleGeometry(1, 0.5), matLineGreen), null, [0, 0, -Math.PI / 2]],\n        [new Mesh(new OctahedronGeometry(0.04, 0), matGreen), [0, 0, 0.99], null, [3, 1, 1]],\n      ],\n      Z: [\n        [new Line(CircleGeometry(1, 0.5), matLineBlue), null, [0, Math.PI / 2, 0]],\n        [new Mesh(new OctahedronGeometry(0.04, 0), matBlue), [0.99, 0, 0], null, [1, 3, 1]],\n      ],\n      E: [\n        [new Line(CircleGeometry(1.25, 1), matLineYellowTransparent), null, [0, Math.PI / 2, 0]],\n        [\n          new Mesh(new CylinderGeometry(0.03, 0, 0.15, 4, 1, false), matLineYellowTransparent),\n          [1.17, 0, 0],\n          [0, 0, -Math.PI / 2],\n          [1, 1, 0.001],\n        ],\n        [\n          new Mesh(new CylinderGeometry(0.03, 0, 0.15, 4, 1, false), matLineYellowTransparent),\n          [-1.17, 0, 0],\n          [0, 0, Math.PI / 2],\n          [1, 1, 0.001],\n        ],\n        [\n          new Mesh(new CylinderGeometry(0.03, 0, 0.15, 4, 1, false), matLineYellowTransparent),\n          [0, -1.17, 0],\n          [Math.PI, 0, 0],\n          [1, 1, 0.001],\n        ],\n        [\n          new Mesh(new CylinderGeometry(0.03, 0, 0.15, 4, 1, false), matLineYellowTransparent),\n          [0, 1.17, 0],\n          [0, 0, 0],\n          [1, 1, 0.001],\n        ],\n      ],\n      XYZE: [[new Line(CircleGeometry(1, 1), matLineGray), null, [0, Math.PI / 2, 0]]],\n    }\n\n    const helperRotate = {\n      AXIS: [[new Line(lineGeometry, matHelper.clone()), [-1e3, 0, 0], null, [1e6, 1, 1], 'helper']],\n    }\n\n    const pickerRotate = {\n      X: [[new Mesh(new TorusGeometry(1, 0.1, 4, 24), matInvisible), [0, 0, 0], [0, -Math.PI / 2, -Math.PI / 2]]],\n      Y: [[new Mesh(new TorusGeometry(1, 0.1, 4, 24), matInvisible), [0, 0, 0], [Math.PI / 2, 0, 0]]],\n      Z: [[new Mesh(new TorusGeometry(1, 0.1, 4, 24), matInvisible), [0, 0, 0], [0, 0, -Math.PI / 2]]],\n      E: [[new Mesh(new TorusGeometry(1.25, 0.1, 2, 24), matInvisible)]],\n      XYZE: [[new Mesh(new SphereGeometry(0.7, 10, 8), matInvisible)]],\n    }\n\n    const gizmoScale = {\n      X: [\n        [new Mesh(scaleHandleGeometry, matRed), [0.8, 0, 0], [0, 0, -Math.PI / 2]],\n        [new Line(lineGeometry, matLineRed), null, null, [0.8, 1, 1]],\n      ],\n      Y: [\n        [new Mesh(scaleHandleGeometry, matGreen), [0, 0.8, 0]],\n        [new Line(lineGeometry, matLineGreen), null, [0, 0, Math.PI / 2], [0.8, 1, 1]],\n      ],\n      Z: [\n        [new Mesh(scaleHandleGeometry, matBlue), [0, 0, 0.8], [Math.PI / 2, 0, 0]],\n        [new Line(lineGeometry, matLineBlue), null, [0, -Math.PI / 2, 0], [0.8, 1, 1]],\n      ],\n      XY: [\n        [new Mesh(scaleHandleGeometry, matYellowTransparent), [0.85, 0.85, 0], null, [2, 2, 0.2]],\n        [new Line(lineGeometry, matLineYellow), [0.855, 0.98, 0], null, [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineYellow), [0.98, 0.855, 0], [0, 0, Math.PI / 2], [0.125, 1, 1]],\n      ],\n      YZ: [\n        [new Mesh(scaleHandleGeometry, matCyanTransparent), [0, 0.85, 0.85], null, [0.2, 2, 2]],\n        [new Line(lineGeometry, matLineCyan), [0, 0.855, 0.98], [0, 0, Math.PI / 2], [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineCyan), [0, 0.98, 0.855], [0, -Math.PI / 2, 0], [0.125, 1, 1]],\n      ],\n      XZ: [\n        [new Mesh(scaleHandleGeometry, matMagentaTransparent), [0.85, 0, 0.85], null, [2, 0.2, 2]],\n        [new Line(lineGeometry, matLineMagenta), [0.855, 0, 0.98], null, [0.125, 1, 1]],\n        [new Line(lineGeometry, matLineMagenta), [0.98, 0, 0.855], [0, -Math.PI / 2, 0], [0.125, 1, 1]],\n      ],\n      XYZX: [[new Mesh(new BoxGeometry(0.125, 0.125, 0.125), matWhiteTransparent.clone()), [1.1, 0, 0]]],\n      XYZY: [[new Mesh(new BoxGeometry(0.125, 0.125, 0.125), matWhiteTransparent.clone()), [0, 1.1, 0]]],\n      XYZZ: [[new Mesh(new BoxGeometry(0.125, 0.125, 0.125), matWhiteTransparent.clone()), [0, 0, 1.1]]],\n    }\n\n    const pickerScale = {\n      X: [[new Mesh(new CylinderGeometry(0.2, 0, 0.8, 4, 1, false), matInvisible), [0.5, 0, 0], [0, 0, -Math.PI / 2]]],\n      Y: [[new Mesh(new CylinderGeometry(0.2, 0, 0.8, 4, 1, false), matInvisible), [0, 0.5, 0]]],\n      Z: [[new Mesh(new CylinderGeometry(0.2, 0, 0.8, 4, 1, false), matInvisible), [0, 0, 0.5], [Math.PI / 2, 0, 0]]],\n      XY: [[new Mesh(scaleHandleGeometry, matInvisible), [0.85, 0.85, 0], null, [3, 3, 0.2]]],\n      YZ: [[new Mesh(scaleHandleGeometry, matInvisible), [0, 0.85, 0.85], null, [0.2, 3, 3]]],\n      XZ: [[new Mesh(scaleHandleGeometry, matInvisible), [0.85, 0, 0.85], null, [3, 0.2, 3]]],\n      XYZX: [[new Mesh(new BoxGeometry(0.2, 0.2, 0.2), matInvisible), [1.1, 0, 0]]],\n      XYZY: [[new Mesh(new BoxGeometry(0.2, 0.2, 0.2), matInvisible), [0, 1.1, 0]]],\n      XYZZ: [[new Mesh(new BoxGeometry(0.2, 0.2, 0.2), matInvisible), [0, 0, 1.1]]],\n    }\n\n    const helperScale = {\n      X: [[new Line(lineGeometry, matHelper.clone()), [-1e3, 0, 0], null, [1e6, 1, 1], 'helper']],\n      Y: [[new Line(lineGeometry, matHelper.clone()), [0, -1e3, 0], [0, 0, Math.PI / 2], [1e6, 1, 1], 'helper']],\n      Z: [[new Line(lineGeometry, matHelper.clone()), [0, 0, -1e3], [0, -Math.PI / 2, 0], [1e6, 1, 1], 'helper']],\n    }\n\n    // Creates an Object3D with gizmos described in custom hierarchy definition.\n    // this is nearly impossible to Type so i'm leaving it\n    const setupGizmo = (gizmoMap: any): Object3D => {\n      const gizmo = new Object3D()\n\n      for (let name in gizmoMap) {\n        for (let i = gizmoMap[name].length; i--; ) {\n          const object = gizmoMap[name][i][0].clone() as Mesh\n          const position = gizmoMap[name][i][1]\n          const rotation = gizmoMap[name][i][2]\n          const scale = gizmoMap[name][i][3]\n          const tag = gizmoMap[name][i][4]\n\n          // name and tag properties are essential for picking and updating logic.\n          object.name = name\n          // @ts-ignore\n          object.tag = tag\n\n          if (position) {\n            object.position.set(position[0], position[1], position[2])\n          }\n\n          if (rotation) {\n            object.rotation.set(rotation[0], rotation[1], rotation[2])\n          }\n\n          if (scale) {\n            object.scale.set(scale[0], scale[1], scale[2])\n          }\n\n          object.updateMatrix()\n\n          const tempGeometry = object.geometry.clone()\n          tempGeometry.applyMatrix4(object.matrix)\n          object.geometry = tempGeometry\n          object.renderOrder = Infinity\n\n          object.position.set(0, 0, 0)\n          object.rotation.set(0, 0, 0)\n          object.scale.set(1, 1, 1)\n\n          gizmo.add(object)\n        }\n      }\n\n      return gizmo\n    }\n\n    this.gizmo = {} as TransformControlsGizmoPrivateGizmos\n    this.picker = {} as TransformControlsGizmoPrivateGizmos\n    this.helper = {} as TransformControlsGizmoPrivateGizmos\n\n    this.add((this.gizmo['translate'] = setupGizmo(gizmoTranslate)))\n    this.add((this.gizmo['rotate'] = setupGizmo(gizmoRotate)))\n    this.add((this.gizmo['scale'] = setupGizmo(gizmoScale)))\n    this.add((this.picker['translate'] = setupGizmo(pickerTranslate)))\n    this.add((this.picker['rotate'] = setupGizmo(pickerRotate)))\n    this.add((this.picker['scale'] = setupGizmo(pickerScale)))\n    this.add((this.helper['translate'] = setupGizmo(helperTranslate)))\n    this.add((this.helper['rotate'] = setupGizmo(helperRotate)))\n    this.add((this.helper['scale'] = setupGizmo(helperScale)))\n\n    // Pickers should be hidden always\n\n    this.picker['translate'].visible = false\n    this.picker['rotate'].visible = false\n    this.picker['scale'].visible = false\n  }\n\n  // updateMatrixWorld will update transformations and appearance of individual handles\n  public updateMatrixWorld = (): void => {\n    let space = this.space\n\n    if (this.mode === 'scale') {\n      space = 'local' // scale always oriented to local rotation\n    }\n\n    const quaternion = space === 'local' ? this.worldQuaternion : this.identityQuaternion\n\n    // Show only gizmos for current transform mode\n\n    this.gizmo['translate'].visible = this.mode === 'translate'\n    this.gizmo['rotate'].visible = this.mode === 'rotate'\n    this.gizmo['scale'].visible = this.mode === 'scale'\n\n    this.helper['translate'].visible = this.mode === 'translate'\n    this.helper['rotate'].visible = this.mode === 'rotate'\n    this.helper['scale'].visible = this.mode === 'scale'\n\n    let handles: Array<Object3D & { tag?: string }> = []\n    handles = handles.concat(this.picker[this.mode].children)\n    handles = handles.concat(this.gizmo[this.mode].children)\n    handles = handles.concat(this.helper[this.mode].children)\n\n    for (let i = 0; i < handles.length; i++) {\n      const handle = handles[i]\n\n      // hide aligned to camera\n\n      handle.visible = true\n      handle.rotation.set(0, 0, 0)\n      handle.position.copy(this.worldPosition)\n\n      let factor\n\n      if ((this.camera as OrthographicCamera).isOrthographicCamera) {\n        factor =\n          ((this.camera as OrthographicCamera).top - (this.camera as OrthographicCamera).bottom) /\n          (this.camera as OrthographicCamera).zoom\n      } else {\n        factor =\n          this.worldPosition.distanceTo(this.cameraPosition) *\n          Math.min((1.9 * Math.tan((Math.PI * (this.camera as PerspectiveCamera).fov) / 360)) / this.camera.zoom, 7)\n      }\n\n      handle.scale.set(1, 1, 1).multiplyScalar((factor * this.size) / 7)\n\n      // TODO: simplify helpers and consider decoupling from gizmo\n\n      if (handle.tag === 'helper') {\n        handle.visible = false\n\n        if (handle.name === 'AXIS') {\n          handle.position.copy(this.worldPositionStart)\n          handle.visible = !!this.axis\n\n          if (this.axis === 'X') {\n            this.tempQuaternion.setFromEuler(this.tempEuler.set(0, 0, 0))\n            handle.quaternion.copy(quaternion).multiply(this.tempQuaternion)\n\n            if (Math.abs(this.alignVector.copy(this.unitX).applyQuaternion(quaternion).dot(this.eye)) > 0.9) {\n              handle.visible = false\n            }\n          }\n\n          if (this.axis === 'Y') {\n            this.tempQuaternion.setFromEuler(this.tempEuler.set(0, 0, Math.PI / 2))\n            handle.quaternion.copy(quaternion).multiply(this.tempQuaternion)\n\n            if (Math.abs(this.alignVector.copy(this.unitY).applyQuaternion(quaternion).dot(this.eye)) > 0.9) {\n              handle.visible = false\n            }\n          }\n\n          if (this.axis === 'Z') {\n            this.tempQuaternion.setFromEuler(this.tempEuler.set(0, Math.PI / 2, 0))\n            handle.quaternion.copy(quaternion).multiply(this.tempQuaternion)\n\n            if (Math.abs(this.alignVector.copy(this.unitZ).applyQuaternion(quaternion).dot(this.eye)) > 0.9) {\n              handle.visible = false\n            }\n          }\n\n          if (this.axis === 'XYZE') {\n            this.tempQuaternion.setFromEuler(this.tempEuler.set(0, Math.PI / 2, 0))\n            this.alignVector.copy(this.rotationAxis)\n            handle.quaternion.setFromRotationMatrix(\n              this.lookAtMatrix.lookAt(this.zeroVector, this.alignVector, this.unitY),\n            )\n            handle.quaternion.multiply(this.tempQuaternion)\n            handle.visible = this.dragging\n          }\n\n          if (this.axis === 'E') {\n            handle.visible = false\n          }\n        } else if (handle.name === 'START') {\n          handle.position.copy(this.worldPositionStart)\n          handle.visible = this.dragging\n        } else if (handle.name === 'END') {\n          handle.position.copy(this.worldPosition)\n          handle.visible = this.dragging\n        } else if (handle.name === 'DELTA') {\n          handle.position.copy(this.worldPositionStart)\n          handle.quaternion.copy(this.worldQuaternionStart)\n          this.tempVector\n            .set(1e-10, 1e-10, 1e-10)\n            .add(this.worldPositionStart)\n            .sub(this.worldPosition)\n            .multiplyScalar(-1)\n          this.tempVector.applyQuaternion(this.worldQuaternionStart.clone().invert())\n          handle.scale.copy(this.tempVector)\n          handle.visible = this.dragging\n        } else {\n          handle.quaternion.copy(quaternion)\n\n          if (this.dragging) {\n            handle.position.copy(this.worldPositionStart)\n          } else {\n            handle.position.copy(this.worldPosition)\n          }\n\n          if (this.axis) {\n            handle.visible = this.axis.search(handle.name) !== -1\n          }\n        }\n\n        // If updating helper, skip rest of the loop\n        continue\n      }\n\n      // Align handles to current local or world rotation\n\n      handle.quaternion.copy(quaternion)\n\n      if (this.mode === 'translate' || this.mode === 'scale') {\n        // Hide translate and scale axis facing the camera\n\n        const AXIS_HIDE_TRESHOLD = 0.99\n        const PLANE_HIDE_TRESHOLD = 0.2\n        const AXIS_FLIP_TRESHOLD = 0.0\n\n        if (handle.name === 'X' || handle.name === 'XYZX') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitX).applyQuaternion(quaternion).dot(this.eye)) > AXIS_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        if (handle.name === 'Y' || handle.name === 'XYZY') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitY).applyQuaternion(quaternion).dot(this.eye)) > AXIS_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        if (handle.name === 'Z' || handle.name === 'XYZZ') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitZ).applyQuaternion(quaternion).dot(this.eye)) > AXIS_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        if (handle.name === 'XY') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitZ).applyQuaternion(quaternion).dot(this.eye)) < PLANE_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        if (handle.name === 'YZ') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitX).applyQuaternion(quaternion).dot(this.eye)) < PLANE_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        if (handle.name === 'XZ') {\n          if (\n            Math.abs(this.alignVector.copy(this.unitY).applyQuaternion(quaternion).dot(this.eye)) < PLANE_HIDE_TRESHOLD\n          ) {\n            handle.scale.set(1e-10, 1e-10, 1e-10)\n            handle.visible = false\n          }\n        }\n\n        // Flip translate and scale axis ocluded behind another axis\n\n        if (handle.name.search('X') !== -1) {\n          if (this.alignVector.copy(this.unitX).applyQuaternion(quaternion).dot(this.eye) < AXIS_FLIP_TRESHOLD) {\n            if (handle.tag === 'fwd') {\n              handle.visible = false\n            } else {\n              handle.scale.x *= -1\n            }\n          } else if (handle.tag === 'bwd') {\n            handle.visible = false\n          }\n        }\n\n        if (handle.name.search('Y') !== -1) {\n          if (this.alignVector.copy(this.unitY).applyQuaternion(quaternion).dot(this.eye) < AXIS_FLIP_TRESHOLD) {\n            if (handle.tag === 'fwd') {\n              handle.visible = false\n            } else {\n              handle.scale.y *= -1\n            }\n          } else if (handle.tag === 'bwd') {\n            handle.visible = false\n          }\n        }\n\n        if (handle.name.search('Z') !== -1) {\n          if (this.alignVector.copy(this.unitZ).applyQuaternion(quaternion).dot(this.eye) < AXIS_FLIP_TRESHOLD) {\n            if (handle.tag === 'fwd') {\n              handle.visible = false\n            } else {\n              handle.scale.z *= -1\n            }\n          } else if (handle.tag === 'bwd') {\n            handle.visible = false\n          }\n        }\n      } else if (this.mode === 'rotate') {\n        // Align handles to current local or world rotation\n\n        this.tempQuaternion2.copy(quaternion)\n        this.alignVector.copy(this.eye).applyQuaternion(this.tempQuaternion.copy(quaternion).invert())\n\n        if (handle.name.search('E') !== -1) {\n          handle.quaternion.setFromRotationMatrix(this.lookAtMatrix.lookAt(this.eye, this.zeroVector, this.unitY))\n        }\n\n        if (handle.name === 'X') {\n          this.tempQuaternion.setFromAxisAngle(this.unitX, Math.atan2(-this.alignVector.y, this.alignVector.z))\n          this.tempQuaternion.multiplyQuaternions(this.tempQuaternion2, this.tempQuaternion)\n          handle.quaternion.copy(this.tempQuaternion)\n        }\n\n        if (handle.name === 'Y') {\n          this.tempQuaternion.setFromAxisAngle(this.unitY, Math.atan2(this.alignVector.x, this.alignVector.z))\n          this.tempQuaternion.multiplyQuaternions(this.tempQuaternion2, this.tempQuaternion)\n          handle.quaternion.copy(this.tempQuaternion)\n        }\n\n        if (handle.name === 'Z') {\n          this.tempQuaternion.setFromAxisAngle(this.unitZ, Math.atan2(this.alignVector.y, this.alignVector.x))\n          this.tempQuaternion.multiplyQuaternions(this.tempQuaternion2, this.tempQuaternion)\n          handle.quaternion.copy(this.tempQuaternion)\n        }\n      }\n\n      // Hide disabled axes\n      handle.visible = handle.visible && (handle.name.indexOf('X') === -1 || this.showX)\n      handle.visible = handle.visible && (handle.name.indexOf('Y') === -1 || this.showY)\n      handle.visible = handle.visible && (handle.name.indexOf('Z') === -1 || this.showZ)\n      handle.visible = handle.visible && (handle.name.indexOf('E') === -1 || (this.showX && this.showY && this.showZ))\n\n      // highlight selected axis\n\n      //@ts-ignore\n      handle.material.tempOpacity = handle.material.tempOpacity || handle.material.opacity\n      //@ts-ignore\n      handle.material.tempColor = handle.material.tempColor || handle.material.color.clone()\n      //@ts-ignore\n      handle.material.color.copy(handle.material.tempColor)\n      //@ts-ignore\n      handle.material.opacity = handle.material.tempOpacity\n\n      if (!this.enabled) {\n        //@ts-ignore\n        handle.material.opacity *= 0.5\n        //@ts-ignore\n        handle.material.color.lerp(new Color(1, 1, 1), 0.5)\n      } else if (this.axis) {\n        if (handle.name === this.axis) {\n          //@ts-ignore\n          handle.material.opacity = 1.0\n          //@ts-ignore\n          handle.material.color.lerp(new Color(1, 1, 1), 0.5)\n        } else if (\n          this.axis.split('').some(function (a) {\n            return handle.name === a\n          })\n        ) {\n          //@ts-ignore\n          handle.material.opacity = 1.0\n          //@ts-ignore\n          handle.material.color.lerp(new Color(1, 1, 1), 0.5)\n        } else {\n          //@ts-ignore\n          handle.material.opacity *= 0.25\n          //@ts-ignore\n          handle.material.color.lerp(new Color(1, 1, 1), 0.5)\n        }\n      }\n    }\n\n    super.updateMatrixWorld()\n  }\n}\n\nclass TransformControlsPlane extends Mesh<PlaneGeometry, MeshBasicMaterial> {\n  private isTransformControlsPlane = true\n  public type = 'TransformControlsPlane'\n\n  constructor() {\n    super(\n      new PlaneGeometry(100000, 100000, 2, 2),\n      new MeshBasicMaterial({\n        visible: false,\n        wireframe: true,\n        side: DoubleSide,\n        transparent: true,\n        opacity: 0.1,\n        toneMapped: false,\n      }),\n    )\n  }\n\n  private unitX = new Vector3(1, 0, 0)\n  private unitY = new Vector3(0, 1, 0)\n  private unitZ = new Vector3(0, 0, 1)\n\n  private tempVector = new Vector3()\n  private dirVector = new Vector3()\n  private alignVector = new Vector3()\n  private tempMatrix = new Matrix4()\n  private identityQuaternion = new Quaternion()\n\n  // these are set from parent class TransformControls\n  private cameraQuaternion = new Quaternion()\n\n  private worldPosition = new Vector3()\n  private worldQuaternion = new Quaternion()\n\n  private eye = new Vector3()\n\n  private axis: string | null = null\n  private mode: 'translate' | 'rotate' | 'scale' = 'translate'\n  private space = 'world'\n\n  public updateMatrixWorld = (): void => {\n    let space = this.space\n\n    this.position.copy(this.worldPosition)\n\n    if (this.mode === 'scale') space = 'local' // scale always oriented to local rotation\n\n    this.unitX.set(1, 0, 0).applyQuaternion(space === 'local' ? this.worldQuaternion : this.identityQuaternion)\n    this.unitY.set(0, 1, 0).applyQuaternion(space === 'local' ? this.worldQuaternion : this.identityQuaternion)\n    this.unitZ.set(0, 0, 1).applyQuaternion(space === 'local' ? this.worldQuaternion : this.identityQuaternion)\n\n    // Align the plane for current transform mode, axis and space.\n\n    this.alignVector.copy(this.unitY)\n\n    switch (this.mode) {\n      case 'translate':\n      case 'scale':\n        switch (this.axis) {\n          case 'X':\n            this.alignVector.copy(this.eye).cross(this.unitX)\n            this.dirVector.copy(this.unitX).cross(this.alignVector)\n            break\n          case 'Y':\n            this.alignVector.copy(this.eye).cross(this.unitY)\n            this.dirVector.copy(this.unitY).cross(this.alignVector)\n            break\n          case 'Z':\n            this.alignVector.copy(this.eye).cross(this.unitZ)\n            this.dirVector.copy(this.unitZ).cross(this.alignVector)\n            break\n          case 'XY':\n            this.dirVector.copy(this.unitZ)\n            break\n          case 'YZ':\n            this.dirVector.copy(this.unitX)\n            break\n          case 'XZ':\n            this.alignVector.copy(this.unitZ)\n            this.dirVector.copy(this.unitY)\n            break\n          case 'XYZ':\n          case 'E':\n            this.dirVector.set(0, 0, 0)\n            break\n        }\n\n        break\n      case 'rotate':\n      default:\n        // special case for rotate\n        this.dirVector.set(0, 0, 0)\n    }\n\n    if (this.dirVector.length() === 0) {\n      // If in rotate mode, make the plane parallel to camera\n      this.quaternion.copy(this.cameraQuaternion)\n    } else {\n      this.tempMatrix.lookAt(this.tempVector.set(0, 0, 0), this.dirVector, this.alignVector)\n\n      this.quaternion.setFromRotationMatrix(this.tempMatrix)\n    }\n\n    super.updateMatrixWorld()\n  }\n}\n\nexport { TransformControls, TransformControlsGizmo, TransformControlsPlane }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAmCA,MAAM,iOAA2D,WAAA,CAAS;IA0ExE,YAAY,MAAA,EAAiB,UAAA,CAAqC;QAC1D,KAAA;QA1EQ,cAAA,IAAA,EAAA,uBAAsB;QAE/B,cAAA,IAAA,EAAA,WAAU;QAET,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA,aAAY,2MAAI,YAAA;QAEhB,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA,cAAa,2MAAI,UAAA;QACjB,cAAA,IAAA,EAAA,eAAc,2MAAI,UAAA;QAClB,cAAA,IAAA,EAAA,kBAAiB,IAAI,oNAAA;QACrB,cAAA,IAAA,EAAA,QAAO;YACb,GAAG,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;YACtB,GAAG,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;YACtB,GAAG,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;QAAA;QAGhB,cAAA,IAAA,EAAA,cAAa,2MAAI,UAAA;QACjB,cAAA,IAAA,EAAA,YAAW,IAAI,iNAAA;QACf,cAAA,IAAA,EAAA,UAAS,IAAI,iNAAA;QACb,cAAA,IAAA,EAAA,gBAAe,2MAAI,UAAA;QACnB,cAAA,IAAA,EAAA,aAAY,IAAI,iNAAA;QAChB,cAAA,IAAA,EAAA,WAAU,2MAAI,UAAA;QACd,cAAA,IAAA,EAAA,iBAAgB;QAEhB,cAAA,IAAA,EAAA,kBAAiB,2MAAI,UAAA;QACrB,cAAA,IAAA,EAAA,oBAAmB,2MAAI,aAAA;QACvB,cAAA,IAAA,EAAA,eAAc,2MAAI,UAAA;QAElB,cAAA,IAAA,EAAA,kBAAiB,IAAI,iNAAA;QACrB,cAAA,IAAA,EAAA,oBAAmB,2MAAI,aAAA;QACvB,cAAA,IAAA,EAAA,uBAAsB,2MAAI,aAAA;QAC1B,cAAA,IAAA,EAAA,eAAc,2MAAI,UAAA;QAElB,cAAA,IAAA,EAAA,sBAAqB,0MAAI,WAAA;QACzB,cAAA,IAAA,EAAA,wBAAuB,2MAAI,aAAA;QAC3B,cAAA,IAAA,EAAA,mBAAkB,IAAI,iNAAA;QAEtB,cAAA,IAAA,EAAA,iBAAgB,IAAI,iNAAA;QACpB,cAAA,IAAA,EAAA,mBAAkB,2MAAI,aAAA;QACtB,cAAA,IAAA,EAAA,sBAAqB,IAAI,oNAAA;QACzB,cAAA,IAAA,EAAA,cAAa,2MAAI,UAAA;QAEjB,cAAA,IAAA,EAAA,OAAM,2MAAI,UAAA;QAEV,cAAA,IAAA,EAAA,iBAAgB,0MAAI,WAAA;QACpB,cAAA,IAAA,EAAA,mBAAkB,2MAAI,aAAA;QACtB,cAAA,IAAA,EAAA,cAAa,2MAAI,UAAA;QAEjB,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA,WAAU;QACV,cAAA,IAAA,EAAA,QAAsB;QACtB,cAAA,IAAA,EAAA,QAAyC;QACzC,cAAA,IAAA,EAAA,mBAAiC;QACjC,cAAA,IAAA,EAAA,gBAA8B;QAC9B,cAAA,IAAA,EAAA,aAA2B;QAC3B,cAAA,IAAA,EAAA,SAAQ;QACR,cAAA,IAAA,EAAA,QAAO;QACP,cAAA,IAAA,EAAA,YAAW;QACX,cAAA,IAAA,EAAA,SAAQ;QACR,cAAA,IAAA,EAAA,SAAQ;QACR,cAAA,IAAA,EAAA,SAAQ;QAGR,SAAA;QAAA,cAAA,IAAA,EAAA,eAAc;YAAE,MAAM;QAAA;QACtB,cAAA,IAAA,EAAA,kBAAiB;YAAE,MAAM;YAAa,MAAM,IAAA,CAAK,IAAA;QAAA;QACjD,cAAA,IAAA,EAAA,gBAAe;YAAE,MAAM;YAAW,MAAM,IAAA,CAAK,IAAA;QAAA;QAC7C,cAAA,IAAA,EAAA,qBAAoB;YAAE,MAAM;QAAA;QAyE5B,cAAA,IAAA,EAAA,0BAAyB,CAC/B,QACA,WACA,qBACyB;YACzB,MAAM,mBAAmB,UAAU,eAAA,CAAgB,QAAQ,IAAI;YAE/D,IAAA,IAAS,IAAI,GAAG,IAAI,iBAAiB,MAAA,EAAQ,IAAK;gBAChD,IAAI,gBAAA,CAAiB,CAAC,CAAA,CAAE,MAAA,CAAO,OAAA,IAAW,kBAAkB;oBAC1D,OAAO,gBAAA,CAAiB,CAAC,CAAA;gBAC3B;YACF;YAEO,OAAA;QAAA;QAIF,qBAAA;QAAA,cAAA,IAAA,EAAA,UAAS,CAAC,WAA2B;YAC1C,IAAA,CAAK,MAAA,GAAS;YACd,IAAA,CAAK,OAAA,GAAU;YAER,OAAA,IAAA;QAAA;QAIF,sBAAA;QAAA,cAAA,IAAA,EAAA,UAAS,MAAY;YAC1B,IAAA,CAAK,MAAA,GAAS,KAAA;YACd,IAAA,CAAK,OAAA,GAAU;YACf,IAAA,CAAK,IAAA,GAAO;YAEL,OAAA,IAAA;QAAA;QAIF,QAAA;QAAA,cAAA,IAAA,EAAA,SAAQ,MAAY;YACzB,IAAI,CAAC,IAAA,CAAK,OAAA,EAAgB,OAAA,IAAA;YAE1B,IAAI,IAAA,CAAK,QAAA,EAAU;gBACb,IAAA,IAAA,CAAK,MAAA,KAAW,KAAA,GAAW;oBAC7B,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,IAAA,CAAK,IAAA,CAAK,aAAa;oBAC5C,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,eAAe;oBAChD,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,UAAU;oBAEjC,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,WAAW;oBAE9B,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,iBAAiB;oBACpC,IAAA,CAAA,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,QAAQ;gBACpC;YACF;YAEO,OAAA,IAAA;QAAA;QAGF,cAAA,IAAA,EAAA,qBAAoB,MAAY;YACjC,IAAA,IAAA,CAAK,MAAA,KAAW,KAAA,GAAW;gBAC7B,IAAA,CAAK,MAAA,CAAO,iBAAA;gBAER,IAAA,IAAA,CAAK,MAAA,CAAO,MAAA,KAAW,MAAM;oBAC/B,QAAQ,KAAA,CAAM,8EAA8E;gBAAA,OACvF;oBACA,IAAA,CAAA,MAAA,CAAO,MAAA,CAAO,WAAA,CAAY,SAAA,CAAU,IAAA,CAAK,cAAA,EAAgB,IAAA,CAAK,gBAAA,EAAkB,IAAA,CAAK,WAAW;gBACvG;gBAEK,IAAA,CAAA,MAAA,CAAO,WAAA,CAAY,SAAA,CAAU,IAAA,CAAK,aAAA,EAAe,IAAA,CAAK,eAAA,EAAiB,IAAA,CAAK,UAAU;gBAE3F,IAAA,CAAK,mBAAA,CAAoB,IAAA,CAAK,IAAA,CAAK,gBAAgB,EAAE,MAAA;gBACrD,IAAA,CAAK,kBAAA,CAAmB,IAAA,CAAK,IAAA,CAAK,eAAe,EAAE,MAAA;YACrD;YAEA,IAAA,CAAK,MAAA,CAAO,iBAAA;YACP,IAAA,CAAA,MAAA,CAAO,WAAA,CAAY,SAAA,CAAU,IAAA,CAAK,cAAA,EAAgB,IAAA,CAAK,gBAAA,EAAkB,IAAA,CAAK,WAAW;YAEzF,IAAA,CAAA,GAAA,CAAI,IAAA,CAAK,IAAA,CAAK,cAAc,EAAE,GAAA,CAAI,IAAA,CAAK,aAAa,EAAE,SAAA;YAE3D,KAAA,CAAM,kBAAkB;QAAA;QAGlB,cAAA,IAAA,EAAA,gBAAe,CAAC,YAAkD;YACxE,IAAI,IAAA,CAAK,MAAA,KAAW,KAAA,KAAa,IAAA,CAAK,QAAA,KAAa,MAAM;YAEzD,IAAA,CAAK,SAAA,CAAU,aAAA,CAAe,SAAgC,IAAA,CAAK,MAAM;YAEnE,MAAA,YAAY,IAAA,CAAK,sBAAA,CAAuB,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,IAAA,CAAK,IAAI,CAAA,EAAG,IAAA,CAAK,SAAS;YAE1F,IAAI,WAAW;gBACR,IAAA,CAAA,IAAA,GAAO,UAAU,MAAA,CAAO,IAAA;YAAA,OACxB;gBACL,IAAA,CAAK,IAAA,GAAO;YACd;QAAA;QAGM,cAAA,IAAA,EAAA,eAAc,CAAC,YAAkD;YACvE,IAAI,IAAA,CAAK,MAAA,KAAW,KAAA,KAAa,IAAA,CAAK,QAAA,KAAa,QAAQ,QAAQ,MAAA,KAAW,GAAG;YAE7E,IAAA,IAAA,CAAK,IAAA,KAAS,MAAM;gBACtB,IAAA,CAAK,SAAA,CAAU,aAAA,CAAe,SAAgC,IAAA,CAAK,MAAM;gBAEzE,MAAM,iBAAiB,IAAA,CAAK,sBAAA,CAAuB,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,SAAA,EAAW,IAAI;gBAEnF,IAAI,gBAAgB;oBAClB,IAAI,QAAQ,IAAA,CAAK,KAAA;oBAEb,IAAA,IAAA,CAAK,IAAA,KAAS,SAAS;wBACjB,QAAA;oBAAA,OAAA,IACC,IAAA,CAAK,IAAA,KAAS,OAAO,IAAA,CAAK,IAAA,KAAS,UAAU,IAAA,CAAK,IAAA,KAAS,OAAO;wBACnE,QAAA;oBACV;oBAEA,IAAI,UAAU,WAAW,IAAA,CAAK,IAAA,KAAS,UAAU;wBAC/C,MAAM,OAAO,IAAA,CAAK,YAAA;wBAEd,IAAA,IAAA,CAAK,IAAA,KAAS,OAAO,MAAW,IAAA,CAAA,MAAA,CAAO,QAAA,CAAS,CAAA,GAAI,KAAK,KAAA,CAAM,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,CAAA,GAAI,IAAI,IAAI;wBAChG,IAAA,IAAA,CAAK,IAAA,KAAS,OAAO,MAAW,IAAA,CAAA,MAAA,CAAO,QAAA,CAAS,CAAA,GAAI,KAAK,KAAA,CAAM,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,CAAA,GAAI,IAAI,IAAI;wBAChG,IAAA,IAAA,CAAK,IAAA,KAAS,OAAO,MAAW,IAAA,CAAA,MAAA,CAAO,QAAA,CAAS,CAAA,GAAI,KAAK,KAAA,CAAM,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,CAAA,GAAI,IAAI,IAAI;oBACtG;oBAEA,IAAA,CAAK,MAAA,CAAO,iBAAA;oBAER,IAAA,IAAA,CAAK,MAAA,CAAO,MAAA,EAAQ;wBACjB,IAAA,CAAA,MAAA,CAAO,MAAA,CAAO,iBAAA;oBACrB;oBAEA,IAAA,CAAK,aAAA,CAAc,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,QAAQ;oBAC5C,IAAA,CAAK,eAAA,CAAgB,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,UAAU;oBAChD,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,KAAK;oBAEjC,IAAA,CAAA,MAAA,CAAO,WAAA,CAAY,SAAA,CAAU,IAAA,CAAK,kBAAA,EAAoB,IAAA,CAAK,oBAAA,EAAsB,IAAA,CAAK,eAAe;oBAE1G,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,eAAe,KAAK,EAAE,GAAA,CAAI,IAAA,CAAK,kBAAkB;gBACxE;gBAEA,IAAA,CAAK,QAAA,GAAW;gBACX,IAAA,CAAA,cAAA,CAAe,IAAA,GAAO,IAAA,CAAK,IAAA;gBAE3B,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,cAAc;YACxC;QAAA;QAGM,cAAA,IAAA,EAAA,eAAc,CAAC,YAAkD;YACvE,MAAM,OAAO,IAAA,CAAK,IAAA;YAClB,MAAM,OAAO,IAAA,CAAK,IAAA;YAClB,MAAM,SAAS,IAAA,CAAK,MAAA;YACpB,IAAI,QAAQ,IAAA,CAAK,KAAA;YAEjB,IAAI,SAAS,SAAS;gBACZ,QAAA;YAAA,OAAA,IACC,SAAS,OAAO,SAAS,UAAU,SAAS,OAAO;gBACpD,QAAA;YACV;YAEI,IAAA,WAAW,KAAA,KAAa,SAAS,QAAQ,IAAA,CAAK,QAAA,KAAa,SAAS,QAAQ,MAAA,KAAW,CAAA,GAAI;YAE/F,IAAA,CAAK,SAAA,CAAU,aAAA,CAAe,SAAgC,IAAA,CAAK,MAAM;YAEzE,MAAM,iBAAiB,IAAA,CAAK,sBAAA,CAAuB,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,SAAA,EAAW,IAAI;YAEnF,IAAI,CAAC,gBAAgB;YAErB,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,eAAe,KAAK,EAAE,GAAA,CAAI,IAAA,CAAK,kBAAkB;YAEpE,IAAI,SAAS,aAAa;gBAGxB,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,IAAA,CAAK,QAAQ,EAAE,GAAA,CAAI,IAAA,CAAK,UAAU;gBAE/C,IAAA,UAAU,WAAW,SAAS,OAAO;oBAClC,IAAA,CAAA,MAAA,CAAO,eAAA,CAAgB,IAAA,CAAK,kBAAkB;gBACrD;gBAEI,IAAA,KAAK,OAAA,CAAQ,GAAG,MAAM,CAAA,GAAI,IAAA,CAAK,MAAA,CAAO,CAAA,GAAI;gBAC1C,IAAA,KAAK,OAAA,CAAQ,GAAG,MAAM,CAAA,GAAI,IAAA,CAAK,MAAA,CAAO,CAAA,GAAI;gBAC1C,IAAA,KAAK,OAAA,CAAQ,GAAG,MAAM,CAAA,GAAI,IAAA,CAAK,MAAA,CAAO,CAAA,GAAI;gBAE1C,IAAA,UAAU,WAAW,SAAS,OAAO;oBACvC,IAAA,CAAK,MAAA,CAAO,eAAA,CAAgB,IAAA,CAAK,eAAe,EAAE,MAAA,CAAO,IAAA,CAAK,WAAW;gBAAA,OACpE;oBACL,IAAA,CAAK,MAAA,CAAO,eAAA,CAAgB,IAAA,CAAK,mBAAmB,EAAE,MAAA,CAAO,IAAA,CAAK,WAAW;gBAC/E;gBAEA,OAAO,QAAA,CAAS,IAAA,CAAK,IAAA,CAAK,MAAM,EAAE,GAAA,CAAI,IAAA,CAAK,aAAa;gBAIxD,IAAI,IAAA,CAAK,eAAA,EAAiB;oBACxB,IAAI,UAAU,SAAS;wBACd,OAAA,QAAA,CAAS,eAAA,CAAgB,IAAA,CAAK,cAAA,CAAe,IAAA,CAAK,IAAA,CAAK,eAAe,EAAE,MAAA,CAAA,CAAQ;wBAEvF,IAAI,KAAK,MAAA,CAAO,GAAG,MAAM,CAAA,GAAI;4BACpB,OAAA,QAAA,CAAS,CAAA,GAAI,KAAK,KAAA,CAAM,OAAO,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,eAAe,IAAI,IAAA,CAAK,eAAA;wBAClF;wBAEA,IAAI,KAAK,MAAA,CAAO,GAAG,MAAM,CAAA,GAAI;4BACpB,OAAA,QAAA,CAAS,CAAA,GAAI,KAAK,KAAA,CAAM,OAAO,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,eAAe,IAAI,IAAA,CAAK,eAAA;wBAClF;wBAEA,IAAI,KAAK,MAAA,CAAO,GAAG,MAAM,CAAA,GAAI;4BACpB,OAAA,QAAA,CAAS,CAAA,GAAI,KAAK,KAAA,CAAM,OAAO,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,eAAe,IAAI,IAAA,CAAK,eAAA;wBAClF;wBAEO,OAAA,QAAA,CAAS,eAAA,CAAgB,IAAA,CAAK,eAAe;oBACtD;oBAEA,IAAI,UAAU,SAAS;wBACrB,IAAI,OAAO,MAAA,EAAQ;4BACV,OAAA,QAAA,CAAS,GAAA,CAAI,IAAA,CAAK,UAAA,CAAW,qBAAA,CAAsB,OAAO,MAAA,CAAO,WAAW,CAAC;wBACtF;wBAEA,IAAI,KAAK,MAAA,CAAO,GAAG,MAAM,CAAA,GAAI;4BACpB,OAAA,QAAA,CAAS,CAAA,GAAI,KAAK,KAAA,CAAM,OAAO,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,eAAe,IAAI,IAAA,CAAK,eAAA;wBAClF;wBAEA,IAAI,KAAK,MAAA,CAAO,GAAG,MAAM,CAAA,GAAI;4BACpB,OAAA,QAAA,CAAS,CAAA,GAAI,KAAK,KAAA,CAAM,OAAO,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,eAAe,IAAI,IAAA,CAAK,eAAA;wBAClF;wBAEA,IAAI,KAAK,MAAA,CAAO,GAAG,MAAM,CAAA,GAAI;4BACpB,OAAA,QAAA,CAAS,CAAA,GAAI,KAAK,KAAA,CAAM,OAAO,QAAA,CAAS,CAAA,GAAI,IAAA,CAAK,eAAe,IAAI,IAAA,CAAK,eAAA;wBAClF;wBAEA,IAAI,OAAO,MAAA,EAAQ;4BACV,OAAA,QAAA,CAAS,GAAA,CAAI,IAAA,CAAK,UAAA,CAAW,qBAAA,CAAsB,OAAO,MAAA,CAAO,WAAW,CAAC;wBACtF;oBACF;gBACF;YAAA,OAAA,IACS,SAAS,SAAS;gBAC3B,IAAI,KAAK,MAAA,CAAO,KAAK,MAAM,CAAA,GAAI;oBAC7B,IAAI,IAAI,IAAA,CAAK,QAAA,CAAS,MAAA,CAAW,IAAA,IAAA,CAAK,UAAA,CAAW,MAAA;oBAEjD,IAAI,IAAA,CAAK,QAAA,CAAS,GAAA,CAAI,IAAA,CAAK,UAAU,IAAI,GAAQ,KAAA,CAAA;oBAEjD,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,GAAG,GAAG,CAAC;gBAAA,OACvB;oBACA,IAAA,CAAA,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,UAAU;oBAC/B,IAAA,CAAA,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,QAAQ;oBAE9B,IAAA,CAAA,UAAA,CAAW,eAAA,CAAgB,IAAA,CAAK,kBAAkB;oBAClD,IAAA,CAAA,WAAA,CAAY,eAAA,CAAgB,IAAA,CAAK,kBAAkB;oBAEnD,IAAA,CAAA,WAAA,CAAY,MAAA,CAAO,IAAA,CAAK,UAAU;oBAEvC,IAAI,KAAK,MAAA,CAAO,GAAG,MAAM,CAAA,GAAI;wBAC3B,IAAA,CAAK,WAAA,CAAY,CAAA,GAAI;oBACvB;oBAEA,IAAI,KAAK,MAAA,CAAO,GAAG,MAAM,CAAA,GAAI;wBAC3B,IAAA,CAAK,WAAA,CAAY,CAAA,GAAI;oBACvB;oBAEA,IAAI,KAAK,MAAA,CAAO,GAAG,MAAM,CAAA,GAAI;wBAC3B,IAAA,CAAK,WAAA,CAAY,CAAA,GAAI;oBACvB;gBACF;gBAIA,OAAO,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,UAAU,EAAE,QAAA,CAAS,IAAA,CAAK,WAAW;gBAExD,IAAA,IAAA,CAAK,SAAA,IAAa,IAAA,CAAK,MAAA,EAAQ;oBACjC,IAAI,KAAK,MAAA,CAAO,GAAG,MAAM,CAAA,GAAI;wBAC3B,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,CAAA,GAAI,KAAK,KAAA,CAAM,OAAO,KAAA,CAAM,CAAA,GAAI,IAAA,CAAK,SAAS,IAAI,IAAA,CAAK,SAAA,IAAa,IAAA,CAAK,SAAA;oBAC7F;oBAEA,IAAI,KAAK,MAAA,CAAO,GAAG,MAAM,CAAA,GAAI;wBAC3B,OAAO,KAAA,CAAM,CAAA,GAAI,KAAK,KAAA,CAAM,OAAO,KAAA,CAAM,CAAA,GAAI,IAAA,CAAK,SAAS,IAAI,IAAA,CAAK,SAAA,IAAa,IAAA,CAAK,SAAA;oBACxF;oBAEA,IAAI,KAAK,MAAA,CAAO,GAAG,MAAM,CAAA,GAAI;wBAC3B,OAAO,KAAA,CAAM,CAAA,GAAI,KAAK,KAAA,CAAM,OAAO,KAAA,CAAM,CAAA,GAAI,IAAA,CAAK,SAAS,IAAI,IAAA,CAAK,SAAA,IAAa,IAAA,CAAK,SAAA;oBACxF;gBACF;YAAA,OAAA,IACS,SAAS,UAAU;gBAC5B,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,IAAA,CAAK,QAAQ,EAAE,GAAA,CAAI,IAAA,CAAK,UAAU;gBAE7C,MAAA,iBACJ,KAAK,IAAA,CAAK,aAAA,CAAc,UAAA,CAAW,IAAA,CAAK,UAAA,CAAW,qBAAA,CAAsB,IAAA,CAAK,MAAA,CAAO,WAAW,CAAC;gBAEnG,IAAI,SAAS,KAAK;oBACX,IAAA,CAAA,YAAA,CAAa,IAAA,CAAK,IAAA,CAAK,GAAG;oBAC/B,IAAA,CAAK,aAAA,GAAgB,IAAA,CAAK,QAAA,CAAS,OAAA,CAAQ,IAAA,CAAK,UAAU;oBAE1D,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,UAAU,EAAE,SAAA;oBACrC,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,IAAA,CAAK,QAAQ,EAAE,SAAA;oBAEjC,IAAA,CAAK,aAAA,IAAiB,IAAA,CAAK,OAAA,CAAQ,KAAA,CAAM,IAAA,CAAK,SAAS,EAAE,GAAA,CAAI,IAAA,CAAK,GAAG,IAAI,IAAI,IAAI,CAAA;gBAAA,OAAA,IACxE,SAAS,QAAQ;oBACrB,IAAA,CAAA,YAAA,CAAa,IAAA,CAAK,IAAA,CAAK,MAAM,EAAE,KAAA,CAAM,IAAA,CAAK,GAAG,EAAE,SAAA;oBACpD,IAAA,CAAK,aAAA,GAAgB,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,YAAY,EAAE,KAAA,CAAM,IAAA,CAAK,GAAG,CAAC,IAAI;gBAAA,OAAA,IACvF,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK;oBACvD,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,IAAI,CAAC;oBAEtC,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,IAAI,CAAC;oBAEpC,IAAI,UAAU,SAAS;wBAChB,IAAA,CAAA,UAAA,CAAW,eAAA,CAAgB,IAAA,CAAK,eAAe;oBACtD;oBAEA,IAAA,CAAK,aAAA,GAAgB,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI,IAAA,CAAK,UAAA,CAAW,KAAA,CAAM,IAAA,CAAK,GAAG,EAAE,SAAA,CAAW,CAAA,IAAI;gBACtF;gBAIA,IAAI,IAAA,CAAK,YAAA,EAAc;oBAChB,IAAA,CAAA,aAAA,GAAgB,KAAK,KAAA,CAAM,IAAA,CAAK,aAAA,GAAgB,IAAA,CAAK,YAAY,IAAI,IAAA,CAAK,YAAA;gBACjF;gBAGA,IAAI,UAAU,WAAW,SAAS,OAAO,SAAS,QAAQ;oBACjD,OAAA,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,eAAe;oBACpC,OAAA,UAAA,CACJ,QAAA,CAAS,IAAA,CAAK,cAAA,CAAe,gBAAA,CAAiB,IAAA,CAAK,YAAA,EAAc,IAAA,CAAK,aAAa,CAAC,EACpF,SAAA,CAAU;gBAAA,OACR;oBACA,IAAA,CAAA,YAAA,CAAa,eAAA,CAAgB,IAAA,CAAK,mBAAmB;oBACnD,OAAA,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,cAAA,CAAe,gBAAA,CAAiB,IAAA,CAAK,YAAA,EAAc,IAAA,CAAK,aAAa,CAAC;oBAClG,OAAO,UAAA,CAAW,QAAA,CAAS,IAAA,CAAK,eAAe,EAAE,SAAA;gBACnD;YACF;YAGK,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,WAAW;YAE9B,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,iBAAiB;QAAA;QAGnC,cAAA,IAAA,EAAA,aAAY,CAAC,YAAkD;YACrE,IAAI,QAAQ,MAAA,KAAW,GAAG;YAE1B,IAAI,IAAA,CAAK,QAAA,IAAY,IAAA,CAAK,IAAA,KAAS,MAAM;gBAClC,IAAA,CAAA,YAAA,CAAa,IAAA,GAAO,IAAA,CAAK,IAAA;gBAEzB,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,YAAY;YACtC;YAEA,IAAA,CAAK,QAAA,GAAW;YAChB,IAAA,CAAK,IAAA,GAAO;QAAA;QAGN,cAAA,IAAA,EAAA,cAAa,CAAC,UAAiD;;YACrE,IAAI,IAAA,CAAK,UAAA,IAAA,CAAA,CAAc,KAAA,IAAA,CAAK,UAAA,CAAW,aAAA,KAAhB,OAAA,KAAA,IAAA,GAA+B,kBAAA,GAAoB;gBACjE,OAAA;oBACL,GAAG;oBACH,GAAG;oBACH,QAAS,MAAqB,MAAA;gBAAA;YAChC,OACK;gBACL,MAAM,UAAW,MAAqB,cAAA,GACjC,MAAqB,cAAA,CAAe,CAAC,CAAA,GACrC;gBAEC,MAAA,OAAO,IAAA,CAAK,UAAA,CAAY,qBAAA,CAAsB;gBAE7C,OAAA;oBACL,GAAA,CAAK,QAAQ,OAAA,GAAU,KAAK,IAAA,IAAQ,KAAK,KAAA,GAAS,IAAI;oBACtD,GAAI,CAAA,CAAE,QAAQ,OAAA,GAAU,KAAK,GAAA,IAAO,KAAK,MAAA,GAAU,IAAI;oBACvD,QAAS,MAAqB,MAAA;gBAAA;YAElC;QAAA;QAGM,cAAA,IAAA,EAAA,kBAAiB,CAAC,UAAuB;YAC/C,IAAI,CAAC,IAAA,CAAK,OAAA,EAAS;YAEnB,OAAS,MAAuB,WAAA,EAAa;gBAC3C,KAAK;gBACL,KAAK;oBACH,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,UAAA,CAAW,KAAK,CAAC;oBACxC;YACJ;QAAA;QAGM,cAAA,IAAA,EAAA,iBAAgB,CAAC,UAAuB;YAC9C,IAAI,CAAC,IAAA,CAAK,OAAA,IAAW,CAAC,IAAA,CAAK,UAAA,EAAY;YAElC,IAAA,CAAA,UAAA,CAAW,KAAA,CAAM,WAAA,GAAc;YACpC,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,gBAAA,CAAiB,eAAe,IAAA,CAAK,aAAa;YAChF,IAAA,CAAK,YAAA,CAAa,IAAA,CAAK,UAAA,CAAW,KAAK,CAAC;YACxC,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,UAAA,CAAW,KAAK,CAAC;QAAA;QAGjC,cAAA,IAAA,EAAA,iBAAgB,CAAC,UAAuB;YAC9C,IAAI,CAAC,IAAA,CAAK,OAAA,EAAS;YAEnB,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,UAAA,CAAW,KAAK,CAAC;QAAA;QAGjC,cAAA,IAAA,EAAA,eAAc,CAAC,UAAuB;YAC5C,IAAI,CAAC,IAAA,CAAK,OAAA,IAAW,CAAC,IAAA,CAAK,UAAA,EAAY;YAElC,IAAA,CAAA,UAAA,CAAW,KAAA,CAAM,WAAA,GAAe;YACrC,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,mBAAA,CAAoB,eAAe,IAAA,CAAK,aAAa;YAEnF,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,UAAA,CAAW,KAAK,CAAC;QAAA;QAGhC,cAAA,IAAA,EAAA,WAAU,IAAiC,IAAA,CAAK,IAAA;QAEhD,cAAA,IAAA,EAAA,WAAU,CAAC,SAA0C;YAC1D,IAAA,CAAK,IAAA,GAAO;QAAA;QAGP,cAAA,IAAA,EAAA,sBAAqB,CAAC,oBAAkC;YAC7D,IAAA,CAAK,eAAA,GAAkB;QAAA;QAGlB,cAAA,IAAA,EAAA,mBAAkB,CAAC,iBAA+B;YACvD,IAAA,CAAK,YAAA,GAAe;QAAA;QAGf,cAAA,IAAA,EAAA,gBAAe,CAAC,cAA4B;YACjD,IAAA,CAAK,SAAA,GAAY;QAAA;QAGZ,cAAA,IAAA,EAAA,WAAU,CAAC,SAAuB;YACvC,IAAA,CAAK,IAAA,GAAO;QAAA;QAGP,cAAA,IAAA,EAAA,YAAW,CAAC,UAAwB;YACzC,IAAA,CAAK,KAAA,GAAQ;QAAA;QAGR,cAAA,IAAA,EAAA,UAAS,MAAY;YAClB,QAAA,IAAA,CACN;QACF;QAGK,cAAA,IAAA,EAAA,WAAU,CAAC,eAAkC;YAClD,IAAK,eAAuB,UAAU;gBAC5B,QAAA,KAAA,CACN;YAEJ;YACA,IAAA,CAAK,UAAA,GAAa;YAElB,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,eAAe,IAAA,CAAK,aAAa;YAClE,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,eAAe,IAAA,CAAK,cAAc;YACnE,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,gBAAA,CAAiB,aAAa,IAAA,CAAK,WAAW;QAAA;QAGvE,cAAA,IAAA,EAAA,WAAU,MAAY;;YAC3B,CAAA,KAAA,IAAA,CAAK,UAAA,KAAL,OAAA,KAAA,IAAA,GAAiB,mBAAA,CAAoB,eAAe,IAAA,CAAK,aAAA;YACzD,CAAA,KAAA,IAAA,CAAK,UAAA,KAAL,OAAA,KAAA,IAAA,GAAiB,mBAAA,CAAoB,eAAe,IAAA,CAAK,cAAA;YACzD,CAAA,KAAA,CAAA,KAAA,IAAA,CAAK,UAAA,KAAL,OAAA,KAAA,IAAA,GAAiB,aAAA,KAAjB,OAAA,KAAA,IAAA,GAAgC,mBAAA,CAAoB,eAAe,IAAA,CAAK,aAAA;YACxE,CAAA,KAAA,CAAA,KAAA,IAAA,CAAK,UAAA,KAAL,OAAA,KAAA,IAAA,GAAiB,aAAA,KAAjB,OAAA,KAAA,IAAA,GAAgC,mBAAA,CAAoB,aAAa,IAAA,CAAK,WAAA;YAEjE,IAAA,CAAA,QAAA,CAAS,CAAC,UAAU;gBACvB,MAAM,OAAO;gBACb,IAAI,KAAK,QAAA,EAAU;oBACjB,KAAK,QAAA,CAAS,OAAA;gBAChB;gBACA,IAAI,KAAK,QAAA,EAAU;oBACjB,KAAK,QAAA,CAAS,OAAA;gBAChB;YAAA,CACD;QAAA;QAzgBD,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,MAAA,GAAS;QAET,IAAA,CAAA,KAAA,GAAQ,IAAI;QACZ,IAAA,CAAA,GAAA,CAAI,IAAA,CAAK,KAAK;QAEd,IAAA,CAAA,KAAA,GAAQ,IAAI;QACZ,IAAA,CAAA,GAAA,CAAI,IAAA,CAAK,KAAK;QAGb,MAAA,iBAAiB,CAAS,UAAkB,iBAA+B;YAC/E,IAAI,YAAY;YAET,OAAA,cAAA,CAAe,IAAA,EAAM,UAAU;gBACpC,KAAK,WAAY;oBACR,OAAA,cAAc,KAAA,IAAY,YAAY;gBAC/C;gBAEA,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAI,cAAc,OAAO;wBACX,YAAA;wBACP,IAAA,CAAA,KAAA,CAAM,QAAQ,CAAA,GAAI;wBAClB,IAAA,CAAA,KAAA,CAAM,QAAQ,CAAA,GAAI;wBAEvB,IAAA,CAAK,aAAA,CAAc;4BAAE,MAAM,WAAW;4BAAY;wBAAA,CAAc;wBAC3D,IAAA,CAAA,aAAA,CAAc,IAAA,CAAK,WAAW;oBACrC;gBACF;YAAA,CACD;YAGD,IAAA,CAAK,QAAQ,CAAA,GAAI;YAEZ,IAAA,CAAA,KAAA,CAAM,QAAQ,CAAA,GAAI;YAElB,IAAA,CAAA,KAAA,CAAM,QAAQ,CAAA,GAAI;QAAA;QAGV,eAAA,UAAU,IAAA,CAAK,MAAM;QACrB,eAAA,UAAU,IAAA,CAAK,MAAM;QACrB,eAAA,WAAW,IAAA,CAAK,OAAO;QACvB,eAAA,QAAQ,IAAA,CAAK,IAAI;QACjB,eAAA,QAAQ,IAAA,CAAK,IAAI;QACjB,eAAA,mBAAmB,IAAA,CAAK,eAAe;QACvC,eAAA,gBAAgB,IAAA,CAAK,YAAY;QACjC,eAAA,aAAa,IAAA,CAAK,SAAS;QAC3B,eAAA,SAAS,IAAA,CAAK,KAAK;QACnB,eAAA,QAAQ,IAAA,CAAK,IAAI;QACjB,eAAA,YAAY,IAAA,CAAK,QAAQ;QACzB,eAAA,SAAS,IAAA,CAAK,KAAK;QACnB,eAAA,SAAS,IAAA,CAAK,KAAK;QACnB,eAAA,SAAS,IAAA,CAAK,KAAK;QACnB,eAAA,iBAAiB,IAAA,CAAK,aAAa;QACnC,eAAA,sBAAsB,IAAA,CAAK,kBAAkB;QAC7C,eAAA,mBAAmB,IAAA,CAAK,eAAe;QACvC,eAAA,wBAAwB,IAAA,CAAK,oBAAoB;QACjD,eAAA,kBAAkB,IAAA,CAAK,cAAc;QACrC,eAAA,oBAAoB,IAAA,CAAK,gBAAgB;QACzC,eAAA,cAAc,IAAA,CAAK,UAAU;QAC7B,eAAA,YAAY,IAAA,CAAK,QAAQ;QACzB,eAAA,gBAAgB,IAAA,CAAK,YAAY;QACjC,eAAA,iBAAiB,IAAA,CAAK,aAAa;QACnC,eAAA,OAAO,IAAA,CAAK,GAAG;QAG9B,IAAI,eAAe,KAAA,GAAW,IAAA,CAAK,OAAA,CAAQ,UAAU;IACvD;AAycF;AASA,MAAM,sOAA+B,WAAA,CAAS;IA6C5C,aAAc;QACN,KAAA;QA7CA,cAAA,IAAA,EAAA,4BAA2B;QAC5B,cAAA,IAAA,EAAA,QAAO;QAEN,cAAA,IAAA,EAAA,cAAa,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;QAChC,cAAA,IAAA,EAAA,aAAY,IAAI,+MAAA;QAChB,cAAA,IAAA,EAAA,eAAc,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;QACjC,cAAA,IAAA,EAAA,cAAa,IAAI,iNAAA,CAAQ,GAAG,GAAG,CAAC;QAChC,cAAA,IAAA,EAAA,gBAAe,2MAAI,UAAA;QACnB,cAAA,IAAA,EAAA,kBAAiB,2MAAI,aAAA;QACrB,cAAA,IAAA,EAAA,mBAAkB,2MAAI,aAAA;QACtB,cAAA,IAAA,EAAA,sBAAqB,2MAAI,aAAA;QAEzB,cAAA,IAAA,EAAA,SAAQ,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;QAC3B,cAAA,IAAA,EAAA,SAAQ,0MAAI,WAAA,CAAQ,GAAG,GAAG,CAAC;QAC3B,cAAA,IAAA,EAAA,SAAQ,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;QAE3B,cAAA,IAAA,EAAA;QACD,cAAA,IAAA,EAAA;QACC,cAAA,IAAA,EAAA;QAGA,oDAAA;QAAA,cAAA,IAAA,EAAA,gBAAe,2MAAI,UAAA;QAEnB,cAAA,IAAA,EAAA,kBAAiB,2MAAI,UAAA;QAErB,cAAA,IAAA,EAAA,sBAAqB,2MAAI,UAAA;QACzB,cAAA,IAAA,EAAA,wBAAuB,2MAAI,aAAA;QAE3B,cAAA,IAAA,EAAA,iBAAgB,2MAAI,UAAA;QACpB,cAAA,IAAA,EAAA,mBAAkB,2MAAI,aAAA;QAEtB,cAAA,IAAA,EAAA,OAAM,2MAAI,UAAA;QAEV,cAAA,IAAA,EAAA,UAAiD;QACjD,cAAA,IAAA,EAAA,WAAU;QACV,cAAA,IAAA,EAAA,QAAsB;QACtB,cAAA,IAAA,EAAA,QAAyC;QACzC,cAAA,IAAA,EAAA,SAAQ;QACR,cAAA,IAAA,EAAA,QAAO;QACP,cAAA,IAAA,EAAA,YAAW;QACX,cAAA,IAAA,EAAA,SAAQ;QACR,cAAA,IAAA,EAAA,SAAQ;QACR,cAAA,IAAA,EAAA,SAAQ;QA0VT,qFAAA;QAAA,cAAA,IAAA,EAAA,qBAAoB,MAAY;YACrC,IAAI,QAAQ,IAAA,CAAK,KAAA;YAEb,IAAA,IAAA,CAAK,IAAA,KAAS,SAAS;gBACjB,QAAA;YACV;YAEA,MAAM,aAAa,UAAU,UAAU,IAAA,CAAK,eAAA,GAAkB,IAAA,CAAK,kBAAA;YAInE,IAAA,CAAK,KAAA,CAAM,WAAW,CAAA,CAAE,OAAA,GAAU,IAAA,CAAK,IAAA,KAAS;YAChD,IAAA,CAAK,KAAA,CAAM,QAAQ,CAAA,CAAE,OAAA,GAAU,IAAA,CAAK,IAAA,KAAS;YAC7C,IAAA,CAAK,KAAA,CAAM,OAAO,CAAA,CAAE,OAAA,GAAU,IAAA,CAAK,IAAA,KAAS;YAE5C,IAAA,CAAK,MAAA,CAAO,WAAW,CAAA,CAAE,OAAA,GAAU,IAAA,CAAK,IAAA,KAAS;YACjD,IAAA,CAAK,MAAA,CAAO,QAAQ,CAAA,CAAE,OAAA,GAAU,IAAA,CAAK,IAAA,KAAS;YAC9C,IAAA,CAAK,MAAA,CAAO,OAAO,CAAA,CAAE,OAAA,GAAU,IAAA,CAAK,IAAA,KAAS;YAE7C,IAAI,UAA8C,CAAA,CAAA;YAClD,UAAU,QAAQ,MAAA,CAAO,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,IAAI,CAAA,CAAE,QAAQ;YACxD,UAAU,QAAQ,MAAA,CAAO,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,IAAI,CAAA,CAAE,QAAQ;YACvD,UAAU,QAAQ,MAAA,CAAO,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,IAAI,CAAA,CAAE,QAAQ;YAExD,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,MAAA,EAAQ,IAAK;gBACjC,MAAA,SAAS,OAAA,CAAQ,CAAC,CAAA;gBAIxB,OAAO,OAAA,GAAU;gBACjB,OAAO,QAAA,CAAS,GAAA,CAAI,GAAG,GAAG,CAAC;gBACpB,OAAA,QAAA,CAAS,IAAA,CAAK,IAAA,CAAK,aAAa;gBAEnC,IAAA;gBAEC,IAAA,IAAA,CAAK,MAAA,CAA8B,oBAAA,EAAsB;oBAC5D,SAAA,CACI,IAAA,CAAK,MAAA,CAA8B,GAAA,GAAO,IAAA,CAAK,MAAA,CAA8B,MAAA,IAC9E,IAAA,CAAK,MAAA,CAA8B,IAAA;gBAAA,OACjC;oBAEH,SAAA,IAAA,CAAK,aAAA,CAAc,UAAA,CAAW,IAAA,CAAK,cAAc,IACjD,KAAK,GAAA,CAAK,MAAM,KAAK,GAAA,CAAK,KAAK,EAAA,GAAM,IAAA,CAAK,MAAA,CAA6B,GAAA,GAAO,GAAG,IAAK,IAAA,CAAK,MAAA,CAAO,IAAA,EAAM,CAAC;gBAC7G;gBAEO,OAAA,KAAA,CAAM,GAAA,CAAI,GAAG,GAAG,CAAC,EAAE,cAAA,CAAgB,SAAS,IAAA,CAAK,IAAA,GAAQ,CAAC;gBAI7D,IAAA,OAAO,GAAA,KAAQ,UAAU;oBAC3B,OAAO,OAAA,GAAU;oBAEb,IAAA,OAAO,IAAA,KAAS,QAAQ;wBACnB,OAAA,QAAA,CAAS,IAAA,CAAK,IAAA,CAAK,kBAAkB;wBACrC,OAAA,OAAA,GAAU,CAAC,CAAC,IAAA,CAAK,IAAA;wBAEpB,IAAA,IAAA,CAAK,IAAA,KAAS,KAAK;4BAChB,IAAA,CAAA,cAAA,CAAe,YAAA,CAAa,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,GAAG,GAAG,CAAC,CAAC;4BAC5D,OAAO,UAAA,CAAW,IAAA,CAAK,UAAU,EAAE,QAAA,CAAS,IAAA,CAAK,cAAc;4BAE/D,IAAI,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,eAAA,CAAgB,UAAU,EAAE,GAAA,CAAI,IAAA,CAAK,GAAG,CAAC,IAAI,KAAK;gCAC/F,OAAO,OAAA,GAAU;4BACnB;wBACF;wBAEI,IAAA,IAAA,CAAK,IAAA,KAAS,KAAK;4BAChB,IAAA,CAAA,cAAA,CAAe,YAAA,CAAa,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,GAAG,GAAG,KAAK,EAAA,GAAK,CAAC,CAAC;4BACtE,OAAO,UAAA,CAAW,IAAA,CAAK,UAAU,EAAE,QAAA,CAAS,IAAA,CAAK,cAAc;4BAE/D,IAAI,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,eAAA,CAAgB,UAAU,EAAE,GAAA,CAAI,IAAA,CAAK,GAAG,CAAC,IAAI,KAAK;gCAC/F,OAAO,OAAA,GAAU;4BACnB;wBACF;wBAEI,IAAA,IAAA,CAAK,IAAA,KAAS,KAAK;4BAChB,IAAA,CAAA,cAAA,CAAe,YAAA,CAAa,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,GAAG,KAAK,EAAA,GAAK,GAAG,CAAC,CAAC;4BACtE,OAAO,UAAA,CAAW,IAAA,CAAK,UAAU,EAAE,QAAA,CAAS,IAAA,CAAK,cAAc;4BAE/D,IAAI,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,eAAA,CAAgB,UAAU,EAAE,GAAA,CAAI,IAAA,CAAK,GAAG,CAAC,IAAI,KAAK;gCAC/F,OAAO,OAAA,GAAU;4BACnB;wBACF;wBAEI,IAAA,IAAA,CAAK,IAAA,KAAS,QAAQ;4BACnB,IAAA,CAAA,cAAA,CAAe,YAAA,CAAa,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,GAAG,KAAK,EAAA,GAAK,GAAG,CAAC,CAAC;4BACjE,IAAA,CAAA,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,YAAY;4BACvC,OAAO,UAAA,CAAW,qBAAA,CAChB,IAAA,CAAK,YAAA,CAAa,MAAA,CAAO,IAAA,CAAK,UAAA,EAAY,IAAA,CAAK,WAAA,EAAa,IAAA,CAAK,KAAK;4BAEjE,OAAA,UAAA,CAAW,QAAA,CAAS,IAAA,CAAK,cAAc;4BAC9C,OAAO,OAAA,GAAU,IAAA,CAAK,QAAA;wBACxB;wBAEI,IAAA,IAAA,CAAK,IAAA,KAAS,KAAK;4BACrB,OAAO,OAAA,GAAU;wBACnB;oBAAA,OAAA,IACS,OAAO,IAAA,KAAS,SAAS;wBAC3B,OAAA,QAAA,CAAS,IAAA,CAAK,IAAA,CAAK,kBAAkB;wBAC5C,OAAO,OAAA,GAAU,IAAA,CAAK,QAAA;oBAAA,OAAA,IACb,OAAO,IAAA,KAAS,OAAO;wBACzB,OAAA,QAAA,CAAS,IAAA,CAAK,IAAA,CAAK,aAAa;wBACvC,OAAO,OAAA,GAAU,IAAA,CAAK,QAAA;oBAAA,OAAA,IACb,OAAO,IAAA,KAAS,SAAS;wBAC3B,OAAA,QAAA,CAAS,IAAA,CAAK,IAAA,CAAK,kBAAkB;wBACrC,OAAA,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,oBAAoB;wBAChD,IAAA,CAAK,UAAA,CACF,GAAA,CAAI,OAAO,OAAO,KAAK,EACvB,GAAA,CAAI,IAAA,CAAK,kBAAkB,EAC3B,GAAA,CAAI,IAAA,CAAK,aAAa,EACtB,cAAA,CAAe,CAAA,CAAE;wBACpB,IAAA,CAAK,UAAA,CAAW,eAAA,CAAgB,IAAA,CAAK,oBAAA,CAAqB,KAAA,CAAM,EAAE,MAAA,EAAQ;wBACnE,OAAA,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,UAAU;wBACjC,OAAO,OAAA,GAAU,IAAA,CAAK,QAAA;oBAAA,OACjB;wBACE,OAAA,UAAA,CAAW,IAAA,CAAK,UAAU;wBAEjC,IAAI,IAAA,CAAK,QAAA,EAAU;4BACV,OAAA,QAAA,CAAS,IAAA,CAAK,IAAA,CAAK,kBAAkB;wBAAA,OACvC;4BACE,OAAA,QAAA,CAAS,IAAA,CAAK,IAAA,CAAK,aAAa;wBACzC;wBAEA,IAAI,IAAA,CAAK,IAAA,EAAM;4BACb,OAAO,OAAA,GAAU,IAAA,CAAK,IAAA,CAAK,MAAA,CAAO,OAAO,IAAI,MAAM,CAAA;wBACrD;oBACF;oBAGA;gBACF;gBAIO,OAAA,UAAA,CAAW,IAAA,CAAK,UAAU;gBAEjC,IAAI,IAAA,CAAK,IAAA,KAAS,eAAe,IAAA,CAAK,IAAA,KAAS,SAAS;oBAGtD,MAAM,qBAAqB;oBAC3B,MAAM,sBAAsB;oBAC5B,MAAM,qBAAqB;oBAE3B,IAAI,OAAO,IAAA,KAAS,OAAO,OAAO,IAAA,KAAS,QAAQ;wBACjD,IACE,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,eAAA,CAAgB,UAAU,EAAE,GAAA,CAAI,IAAA,CAAK,GAAG,CAAC,IAAI,oBACxF;4BACA,OAAO,KAAA,CAAM,GAAA,CAAI,OAAO,OAAO,KAAK;4BACpC,OAAO,OAAA,GAAU;wBACnB;oBACF;oBAEA,IAAI,OAAO,IAAA,KAAS,OAAO,OAAO,IAAA,KAAS,QAAQ;wBACjD,IACE,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,eAAA,CAAgB,UAAU,EAAE,GAAA,CAAI,IAAA,CAAK,GAAG,CAAC,IAAI,oBACxF;4BACA,OAAO,KAAA,CAAM,GAAA,CAAI,OAAO,OAAO,KAAK;4BACpC,OAAO,OAAA,GAAU;wBACnB;oBACF;oBAEA,IAAI,OAAO,IAAA,KAAS,OAAO,OAAO,IAAA,KAAS,QAAQ;wBACjD,IACE,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,eAAA,CAAgB,UAAU,EAAE,GAAA,CAAI,IAAA,CAAK,GAAG,CAAC,IAAI,oBACxF;4BACA,OAAO,KAAA,CAAM,GAAA,CAAI,OAAO,OAAO,KAAK;4BACpC,OAAO,OAAA,GAAU;wBACnB;oBACF;oBAEI,IAAA,OAAO,IAAA,KAAS,MAAM;wBACxB,IACE,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,eAAA,CAAgB,UAAU,EAAE,GAAA,CAAI,IAAA,CAAK,GAAG,CAAC,IAAI,qBACxF;4BACA,OAAO,KAAA,CAAM,GAAA,CAAI,OAAO,OAAO,KAAK;4BACpC,OAAO,OAAA,GAAU;wBACnB;oBACF;oBAEI,IAAA,OAAO,IAAA,KAAS,MAAM;wBACxB,IACE,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,eAAA,CAAgB,UAAU,EAAE,GAAA,CAAI,IAAA,CAAK,GAAG,CAAC,IAAI,qBACxF;4BACA,OAAO,KAAA,CAAM,GAAA,CAAI,OAAO,OAAO,KAAK;4BACpC,OAAO,OAAA,GAAU;wBACnB;oBACF;oBAEI,IAAA,OAAO,IAAA,KAAS,MAAM;wBACxB,IACE,KAAK,GAAA,CAAI,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,eAAA,CAAgB,UAAU,EAAE,GAAA,CAAI,IAAA,CAAK,GAAG,CAAC,IAAI,qBACxF;4BACA,OAAO,KAAA,CAAM,GAAA,CAAI,OAAO,OAAO,KAAK;4BACpC,OAAO,OAAA,GAAU;wBACnB;oBACF;oBAIA,IAAI,OAAO,IAAA,CAAK,MAAA,CAAO,GAAG,MAAM,CAAA,GAAI;wBAClC,IAAI,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,eAAA,CAAgB,UAAU,EAAE,GAAA,CAAI,IAAA,CAAK,GAAG,IAAI,oBAAoB;4BAChG,IAAA,OAAO,GAAA,KAAQ,OAAO;gCACxB,OAAO,OAAA,GAAU;4BAAA,OACZ;gCACL,OAAO,KAAA,CAAM,CAAA,IAAK,CAAA;4BACpB;wBAAA,OAAA,IACS,OAAO,GAAA,KAAQ,OAAO;4BAC/B,OAAO,OAAA,GAAU;wBACnB;oBACF;oBAEA,IAAI,OAAO,IAAA,CAAK,MAAA,CAAO,GAAG,MAAM,CAAA,GAAI;wBAClC,IAAI,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,eAAA,CAAgB,UAAU,EAAE,GAAA,CAAI,IAAA,CAAK,GAAG,IAAI,oBAAoB;4BAChG,IAAA,OAAO,GAAA,KAAQ,OAAO;gCACxB,OAAO,OAAA,GAAU;4BAAA,OACZ;gCACL,OAAO,KAAA,CAAM,CAAA,IAAK,CAAA;4BACpB;wBAAA,OAAA,IACS,OAAO,GAAA,KAAQ,OAAO;4BAC/B,OAAO,OAAA,GAAU;wBACnB;oBACF;oBAEA,IAAI,OAAO,IAAA,CAAK,MAAA,CAAO,GAAG,MAAM,CAAA,GAAI;wBAClC,IAAI,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,eAAA,CAAgB,UAAU,EAAE,GAAA,CAAI,IAAA,CAAK,GAAG,IAAI,oBAAoB;4BAChG,IAAA,OAAO,GAAA,KAAQ,OAAO;gCACxB,OAAO,OAAA,GAAU;4BAAA,OACZ;gCACL,OAAO,KAAA,CAAM,CAAA,IAAK,CAAA;4BACpB;wBAAA,OAAA,IACS,OAAO,GAAA,KAAQ,OAAO;4BAC/B,OAAO,OAAA,GAAU;wBACnB;oBACF;gBAAA,OAAA,IACS,IAAA,CAAK,IAAA,KAAS,UAAU;oBAG5B,IAAA,CAAA,eAAA,CAAgB,IAAA,CAAK,UAAU;oBACpC,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,GAAG,EAAE,eAAA,CAAgB,IAAA,CAAK,cAAA,CAAe,IAAA,CAAK,UAAU,EAAE,MAAA,CAAQ,CAAA;oBAE7F,IAAI,OAAO,IAAA,CAAK,MAAA,CAAO,GAAG,MAAM,CAAA,GAAI;wBAC3B,OAAA,UAAA,CAAW,qBAAA,CAAsB,IAAA,CAAK,YAAA,CAAa,MAAA,CAAO,IAAA,CAAK,GAAA,EAAK,IAAA,CAAK,UAAA,EAAY,IAAA,CAAK,KAAK,CAAC;oBACzG;oBAEI,IAAA,OAAO,IAAA,KAAS,KAAK;wBACvB,IAAA,CAAK,cAAA,CAAe,gBAAA,CAAiB,IAAA,CAAK,KAAA,EAAO,KAAK,KAAA,CAAM,CAAC,IAAA,CAAK,WAAA,CAAY,CAAA,EAAG,IAAA,CAAK,WAAA,CAAY,CAAC,CAAC;wBACpG,IAAA,CAAK,cAAA,CAAe,mBAAA,CAAoB,IAAA,CAAK,eAAA,EAAiB,IAAA,CAAK,cAAc;wBAC1E,OAAA,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,cAAc;oBAC5C;oBAEI,IAAA,OAAO,IAAA,KAAS,KAAK;wBACvB,IAAA,CAAK,cAAA,CAAe,gBAAA,CAAiB,IAAA,CAAK,KAAA,EAAO,KAAK,KAAA,CAAM,IAAA,CAAK,WAAA,CAAY,CAAA,EAAG,IAAA,CAAK,WAAA,CAAY,CAAC,CAAC;wBACnG,IAAA,CAAK,cAAA,CAAe,mBAAA,CAAoB,IAAA,CAAK,eAAA,EAAiB,IAAA,CAAK,cAAc;wBAC1E,OAAA,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,cAAc;oBAC5C;oBAEI,IAAA,OAAO,IAAA,KAAS,KAAK;wBACvB,IAAA,CAAK,cAAA,CAAe,gBAAA,CAAiB,IAAA,CAAK,KAAA,EAAO,KAAK,KAAA,CAAM,IAAA,CAAK,WAAA,CAAY,CAAA,EAAG,IAAA,CAAK,WAAA,CAAY,CAAC,CAAC;wBACnG,IAAA,CAAK,cAAA,CAAe,mBAAA,CAAoB,IAAA,CAAK,eAAA,EAAiB,IAAA,CAAK,cAAc;wBAC1E,OAAA,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,cAAc;oBAC5C;gBACF;gBAGO,OAAA,OAAA,GAAU,OAAO,OAAA,IAAA,CAAY,OAAO,IAAA,CAAK,OAAA,CAAQ,GAAG,MAAM,CAAA,KAAM,IAAA,CAAK,KAAA;gBACrE,OAAA,OAAA,GAAU,OAAO,OAAA,IAAA,CAAY,OAAO,IAAA,CAAK,OAAA,CAAQ,GAAG,MAAM,CAAA,KAAM,IAAA,CAAK,KAAA;gBACrE,OAAA,OAAA,GAAU,OAAO,OAAA,IAAA,CAAY,OAAO,IAAA,CAAK,OAAA,CAAQ,GAAG,MAAM,CAAA,KAAM,IAAA,CAAK,KAAA;gBAC5E,OAAO,OAAA,GAAU,OAAO,OAAA,IAAA,CAAY,OAAO,IAAA,CAAK,OAAA,CAAQ,GAAG,MAAM,CAAA,KAAO,IAAA,CAAK,KAAA,IAAS,IAAA,CAAK,KAAA,IAAS,IAAA,CAAK,KAAA;gBAKzG,OAAO,QAAA,CAAS,WAAA,GAAc,OAAO,QAAA,CAAS,WAAA,IAAe,OAAO,QAAA,CAAS,OAAA;gBAEtE,OAAA,QAAA,CAAS,SAAA,GAAY,OAAO,QAAA,CAAS,SAAA,IAAa,OAAO,QAAA,CAAS,KAAA,CAAM,KAAA;gBAE/E,OAAO,QAAA,CAAS,KAAA,CAAM,IAAA,CAAK,OAAO,QAAA,CAAS,SAAS;gBAE7C,OAAA,QAAA,CAAS,OAAA,GAAU,OAAO,QAAA,CAAS,WAAA;gBAEtC,IAAA,CAAC,IAAA,CAAK,OAAA,EAAS;oBAEjB,OAAO,QAAA,CAAS,OAAA,IAAW;oBAEpB,OAAA,QAAA,CAAS,KAAA,CAAM,IAAA,CAAK,2MAAI,QAAA,CAAM,GAAG,GAAG,CAAC,GAAG,GAAG;gBAAA,OAAA,IACzC,IAAA,CAAK,IAAA,EAAM;oBAChB,IAAA,OAAO,IAAA,KAAS,IAAA,CAAK,IAAA,EAAM;wBAE7B,OAAO,QAAA,CAAS,OAAA,GAAU;wBAEnB,OAAA,QAAA,CAAS,KAAA,CAAM,IAAA,CAAK,2MAAI,QAAA,CAAM,GAAG,GAAG,CAAC,GAAG,GAAG;oBAAA,OAAA,IAElD,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,EAAE,EAAE,IAAA,CAAK,SAAU,CAAA,EAAG;wBACpC,OAAO,OAAO,IAAA,KAAS;oBAAA,CACxB,GACD;wBAEA,OAAO,QAAA,CAAS,OAAA,GAAU;wBAEnB,OAAA,QAAA,CAAS,KAAA,CAAM,IAAA,CAAK,2MAAI,QAAA,CAAM,GAAG,GAAG,CAAC,GAAG,GAAG;oBAAA,OAC7C;wBAEL,OAAO,QAAA,CAAS,OAAA,IAAW;wBAEpB,OAAA,QAAA,CAAS,KAAA,CAAM,IAAA,CAAK,2MAAI,QAAA,CAAM,GAAG,GAAG,CAAC,GAAG,GAAG;oBACpD;gBACF;YACF;YAEA,KAAA,CAAM,kBAAkB;QAAA;QAzoBlB,MAAA,gBAAgB,2MAAI,oBAAA,CAAkB;YAC1C,WAAW;YACX,YAAY;YACZ,aAAa;YACb,6MAAM,aAAA;YACN,KAAK;YACL,YAAY;QAAA,CACb;QAEK,MAAA,oBAAoB,2MAAI,oBAAA,CAAkB;YAC9C,WAAW;YACX,YAAY;YACZ,aAAa;YACb,WAAW;YACX,KAAK;YACL,YAAY;QAAA,CACb;QAIK,MAAA,eAAe,cAAc,KAAA;QACnC,aAAa,OAAA,GAAU;QAEjB,MAAA,YAAY,cAAc,KAAA;QAChC,UAAU,OAAA,GAAU;QAEd,MAAA,SAAS,cAAc,KAAA;QACtB,OAAA,KAAA,CAAM,GAAA,CAAI,QAAQ;QAEnB,MAAA,WAAW,cAAc,KAAA;QACtB,SAAA,KAAA,CAAM,GAAA,CAAI,KAAQ;QAErB,MAAA,UAAU,cAAc,KAAA;QACtB,QAAA,KAAA,CAAM,GAAA,CAAI,GAAQ;QAEpB,MAAA,sBAAsB,cAAc,KAAA;QAC1C,oBAAoB,OAAA,GAAU;QAExB,MAAA,uBAAuB,oBAAoB,KAAA;QAC5B,qBAAA,KAAA,CAAM,GAAA,CAAI,QAAQ;QAEjC,MAAA,qBAAqB,oBAAoB,KAAA;QAC5B,mBAAA,KAAA,CAAM,GAAA,CAAI,KAAQ;QAE/B,MAAA,wBAAwB,oBAAoB,KAAA;QAC5B,sBAAA,KAAA,CAAM,GAAA,CAAI,QAAQ;QAElC,MAAA,YAAY,cAAc,KAAA;QACtB,UAAA,KAAA,CAAM,GAAA,CAAI,QAAQ;QAEtB,MAAA,aAAa,kBAAkB,KAAA;QAC1B,WAAA,KAAA,CAAM,GAAA,CAAI,QAAQ;QAEvB,MAAA,eAAe,kBAAkB,KAAA;QAC1B,aAAA,KAAA,CAAM,GAAA,CAAI,KAAQ;QAEzB,MAAA,cAAc,kBAAkB,KAAA;QAC1B,YAAA,KAAA,CAAM,GAAA,CAAI,GAAQ;QAExB,MAAA,cAAc,kBAAkB,KAAA;QAC1B,YAAA,KAAA,CAAM,GAAA,CAAI,KAAQ;QAExB,MAAA,iBAAiB,kBAAkB,KAAA;QAC1B,eAAA,KAAA,CAAM,GAAA,CAAI,QAAQ;QAE3B,MAAA,gBAAgB,kBAAkB,KAAA;QAC1B,cAAA,KAAA,CAAM,GAAA,CAAI,QAAQ;QAE1B,MAAA,cAAc,kBAAkB,KAAA;QAC1B,YAAA,KAAA,CAAM,GAAA,CAAI,OAAQ;QAExB,MAAA,2BAA2B,cAAc,KAAA;QAC/C,yBAAyB,OAAA,GAAU;QAI7B,MAAA,gBAAgB,2MAAI,mBAAA,CAAiB,GAAG,MAAM,KAAK,IAAI,GAAG,KAAK;QAErE,MAAM,sBAAsB,2MAAI,cAAA,CAAY,OAAO,OAAO,KAAK;QAEzD,MAAA,eAAe,IAAI,wNAAA;QACzB,aAAa,YAAA,CAAa,YAAY,2MAAI,yBAAA,CAAuB;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG,CAAC;SAAA,EAAG,CAAC,CAAC;QAEjF,MAAA,iBAAiB,CAAC,QAAgB,QAAgC;YAChE,MAAA,WAAW,2MAAI,iBAAA;YACrB,MAAM,WAAW,CAAA,CAAA;YAEjB,IAAA,IAAS,IAAI,GAAG,KAAK,KAAK,KAAK,EAAE,EAAG;gBAClC,SAAS,IAAA,CAAK,GAAG,KAAK,GAAA,CAAK,IAAI,KAAM,KAAK,EAAE,IAAI,QAAQ,KAAK,GAAA,CAAK,IAAI,KAAM,KAAK,EAAE,IAAI,MAAM;YAC/F;YAEA,SAAS,YAAA,CAAa,YAAY,2MAAI,yBAAA,CAAuB,UAAU,CAAC,CAAC;YAElE,OAAA;QAAA;QAKT,MAAM,0BAA0B,MAAsB;YAC9C,MAAA,WAAW,2MAAI,iBAAA;YAErB,SAAS,YAAA,CAAa,YAAY,2MAAI,yBAAA,CAAuB;gBAAC;gBAAG;gBAAG;gBAAG;gBAAG;gBAAG,CAAC;aAAA,EAAG,CAAC,CAAC;YAE5E,OAAA;QAAA;QAKT,MAAM,iBAAiB;YACrB,GAAG;gBACD;oBAAC,2MAAI,OAAA,CAAK,eAAe,MAAM;oBAAG;wBAAC;wBAAG;wBAAG,CAAC;qBAAA;oBAAG;wBAAC;wBAAG;wBAAG,CAAC,KAAK,EAAA,GAAK,CAAC;qBAAA;oBAAG;oBAAM,KAAK;iBAAA;gBAC9E;oBAAC,2MAAI,OAAA,CAAK,eAAe,MAAM;oBAAG;wBAAC;wBAAG;wBAAG,CAAC;qBAAA;oBAAG;wBAAC;wBAAG;wBAAG,KAAK,EAAA,GAAK,CAAC;qBAAA;oBAAG;oBAAM,KAAK;iBAAA;gBAC7E;oBAAC,IAAI,8MAAA,CAAK,cAAc,UAAU,CAAC;iBAAA;aACrC;YACA,GAAG;gBACD;oBAAC,2MAAI,OAAA,CAAK,eAAe,QAAQ;oBAAG;wBAAC;wBAAG;wBAAG,CAAC;qBAAA;oBAAG;oBAAM;oBAAM,KAAK;iBAAA;gBAChE;oBAAC,2MAAI,OAAA,CAAK,eAAe,QAAQ;oBAAG;wBAAC;wBAAG;wBAAG,CAAC;qBAAA;oBAAG;wBAAC,KAAK,EAAA;wBAAI;wBAAG,CAAC;qBAAA;oBAAG;oBAAM,KAAK;iBAAA;gBAC3E;oBAAC,2MAAI,OAAA,CAAK,cAAc,YAAY;oBAAG;oBAAM;wBAAC;wBAAG;wBAAG,KAAK,EAAA,GAAK,CAAC;qBAAC;iBAAA;aAClE;YACA,GAAG;gBACD;oBAAC,2MAAI,OAAA,CAAK,eAAe,OAAO;oBAAG;wBAAC;wBAAG;wBAAG,CAAC;qBAAA;oBAAG;wBAAC,KAAK,EAAA,GAAK;wBAAG;wBAAG,CAAC;qBAAA;oBAAG;oBAAM,KAAK;iBAAA;gBAC9E;oBAAC,2MAAI,OAAA,CAAK,eAAe,OAAO;oBAAG;wBAAC;wBAAG;wBAAG,CAAC;qBAAA;oBAAG;wBAAC,CAAC,KAAK,EAAA,GAAK;wBAAG;wBAAG,CAAC;qBAAA;oBAAG;oBAAM,KAAK;iBAAA;gBAC/E;oBAAC,2MAAI,OAAA,CAAK,cAAc,WAAW;oBAAG;oBAAM;wBAAC;wBAAG,CAAC,KAAK,EAAA,GAAK;wBAAG,CAAC;qBAAC;iBAAA;aAClE;YACA,KAAK;gBAAC;oBAAC,2MAAI,OAAA,CAAK,2MAAI,qBAAA,CAAmB,KAAK,CAAC,GAAG,oBAAoB,KAAA,CAAO,CAAA;oBAAG;wBAAC;wBAAG;wBAAG,CAAC;qBAAA;oBAAG;wBAAC;wBAAG;wBAAG,CAAC;qBAAC;iBAAC;aAAA;YACnG,IAAI;gBACF;oBAAC,2MAAI,OAAA,CAAK,2MAAI,gBAAA,CAAc,OAAO,KAAK,GAAG,qBAAqB,KAAA,CAAO,CAAA;oBAAG;wBAAC;wBAAM;wBAAM,CAAC;qBAAC;iBAAA;gBACzF;oBAAC,2MAAI,OAAA,CAAK,cAAc,aAAa;oBAAG;wBAAC;wBAAM;wBAAK,CAAC;qBAAA;oBAAG;oBAAM;wBAAC;wBAAO;wBAAG,CAAC;qBAAC;iBAAA;gBAC3E;oBAAC,0MAAI,QAAA,CAAK,cAAc,aAAa;oBAAG;wBAAC;wBAAK;wBAAM,CAAC;qBAAA;oBAAG;wBAAC;wBAAG;wBAAG,KAAK,EAAA,GAAK,CAAC;qBAAA;oBAAG;wBAAC;wBAAO;wBAAG,CAAC;qBAAC;iBAAA;aAC5F;YACA,IAAI;gBACF;oBAAC,2MAAI,OAAA,CAAK,2MAAI,gBAAA,CAAc,OAAO,KAAK,GAAG,mBAAmB,KAAA,EAAO;oBAAG;wBAAC;wBAAG;wBAAM,IAAI;qBAAA;oBAAG;wBAAC;wBAAG,KAAK,EAAA,GAAK;wBAAG,CAAC;qBAAC;iBAAA;gBAC5G;oBAAC,2MAAI,OAAA,CAAK,cAAc,WAAW;oBAAG;wBAAC;wBAAG;wBAAM,GAAG;qBAAA;oBAAG;wBAAC;wBAAG;wBAAG,KAAK,EAAA,GAAK,CAAC;qBAAA;oBAAG;wBAAC;wBAAO;wBAAG,CAAC;qBAAC;iBAAA;gBACxF;oBAAC,2MAAI,OAAA,CAAK,cAAc,WAAW;oBAAG;wBAAC;wBAAG;wBAAK,IAAI;qBAAA;oBAAG;wBAAC;wBAAG,CAAC,KAAK,EAAA,GAAK;wBAAG,CAAC;qBAAA;oBAAG;wBAAC;wBAAO;wBAAG,CAAC;qBAAC;iBAAA;aAC3F;YACA,IAAI;gBACF;oBACE,2MAAI,OAAA,CAAK,2MAAI,gBAAA,CAAc,OAAO,KAAK,GAAG,sBAAsB,KAAA,EAAO;oBACvE;wBAAC;wBAAM;wBAAG,IAAI;qBAAA;oBACd;wBAAC,CAAC,KAAK,EAAA,GAAK;wBAAG;wBAAG,CAAC;qBAAA;iBACrB;gBACA;oBAAC,2MAAI,OAAA,CAAK,cAAc,cAAc;oBAAG;wBAAC;wBAAM;wBAAG,GAAG;qBAAA;oBAAG;oBAAM;wBAAC;wBAAO;wBAAG,CAAC;qBAAC;iBAAA;gBAC5E;oBAAC,2MAAI,OAAA,CAAK,cAAc,cAAc;oBAAG;wBAAC;wBAAK;wBAAG,IAAI;qBAAA;oBAAG;wBAAC;wBAAG,CAAC,KAAK,EAAA,GAAK;wBAAG,CAAC;qBAAA;oBAAG;wBAAC;wBAAO;wBAAG,CAAC;qBAAC;iBAAA;aAC9F;QAAA;QAGF,MAAM,kBAAkB;YACtB,GAAG;gBAAC;oBAAC,2MAAI,OAAA,CAAK,2MAAI,mBAAA,CAAiB,KAAK,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,YAAY;oBAAG;wBAAC;wBAAK;wBAAG,CAAC;qBAAA;oBAAG;wBAAC;wBAAG;wBAAG,CAAC,KAAK,EAAA,GAAK,CAAC;qBAAC;iBAAC;aAAA;YAC7G,GAAG;gBAAC;oBAAC,2MAAI,OAAA,CAAK,2MAAI,mBAAA,CAAiB,KAAK,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,YAAY;oBAAG;wBAAC;wBAAG;wBAAK,CAAC;qBAAC;iBAAC;aAAA;YACvF,GAAG;gBAAC;oBAAC,2MAAI,OAAA,CAAK,2MAAI,mBAAA,CAAiB,KAAK,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,YAAY;oBAAG;wBAAC;wBAAG;wBAAG,GAAG;qBAAA;oBAAG;wBAAC,KAAK,EAAA,GAAK;wBAAG;wBAAG,CAAC;qBAAC;iBAAC;aAAA;YAC5G,KAAK;gBAAC;oBAAC,2MAAI,OAAA,CAAK,IAAI,4NAAA,CAAmB,KAAK,CAAC,GAAG,YAAY,CAAC;iBAAC;aAAA;YAC9D,IAAI;gBAAC;oBAAC,2MAAI,OAAA,CAAK,IAAI,uNAAA,CAAc,KAAK,GAAG,GAAG,YAAY;oBAAG;wBAAC;wBAAK;wBAAK,CAAC;qBAAC;iBAAC;aAAA;YACzE,IAAI;gBAAC;oBAAC,2MAAI,OAAA,CAAK,2MAAI,gBAAA,CAAc,KAAK,GAAG,GAAG,YAAY;oBAAG;wBAAC;wBAAG;wBAAK,GAAG;qBAAA;oBAAG;wBAAC;wBAAG,KAAK,EAAA,GAAK;wBAAG,CAAC;qBAAC;iBAAC;aAAA;YAC9F,IAAI;gBAAC;oBAAC,2MAAI,OAAA,CAAK,2MAAI,gBAAA,CAAc,KAAK,GAAG,GAAG,YAAY;oBAAG;wBAAC;wBAAK;wBAAG,GAAG;qBAAA;oBAAG;wBAAC,CAAC,KAAK,EAAA,GAAK;wBAAG;wBAAG,CAAC;qBAAC;iBAAC;aAAA;QAAA;QAGjG,MAAM,kBAAkB;YACtB,OAAO;gBAAC;oBAAC,2MAAI,OAAA,CAAK,IAAI,4NAAA,CAAmB,MAAM,CAAC,GAAG,SAAS;oBAAG;oBAAM;oBAAM;oBAAM,QAAQ;iBAAC;aAAA;YAC1F,KAAK;gBAAC;oBAAC,2MAAI,OAAA,CAAK,2MAAI,qBAAA,CAAmB,MAAM,CAAC,GAAG,SAAS;oBAAG;oBAAM;oBAAM;oBAAM,QAAQ;iBAAC;aAAA;YACxF,OAAO;gBAAC;oBAAC,2MAAI,OAAA,CAAK,wBAA2B,GAAA,SAAS;oBAAG;oBAAM;oBAAM;oBAAM,QAAQ;iBAAC;aAAA;YACpF,GAAG;gBAAC;oBAAC,IAAI,8MAAA,CAAK,cAAc,UAAU,KAAA,CAAM,CAAC;oBAAG;wBAAC,CAAA;wBAAM;wBAAG,CAAC;qBAAA;oBAAG;oBAAM;wBAAC;wBAAK;wBAAG,CAAC;qBAAA;oBAAG,QAAQ;iBAAC;aAAA;YAC1F,GAAG;gBAAC;oBAAC,0MAAI,QAAA,CAAK,cAAc,UAAU,KAAA,CAAA,CAAO;oBAAG;wBAAC;wBAAG,CAAA;wBAAM,CAAC;qBAAA;oBAAG;wBAAC;wBAAG;wBAAG,KAAK,EAAA,GAAK,CAAC;qBAAA;oBAAG;wBAAC;wBAAK;wBAAG,CAAC;qBAAA;oBAAG,QAAQ;iBAAC;aAAA;YACzG,GAAG;gBAAC;oBAAC,2MAAI,OAAA,CAAK,cAAc,UAAU,KAAA,CAAA,CAAO;oBAAG;wBAAC;wBAAG;wBAAG,CAAA,GAAI;qBAAA;oBAAG;wBAAC;wBAAG,CAAC,KAAK,EAAA,GAAK;wBAAG,CAAC;qBAAA;oBAAG;wBAAC;wBAAK;wBAAG,CAAC;qBAAA;oBAAG,QAAQ;iBAAC;aAAA;QAAA;QAG5G,MAAM,cAAc;YAClB,GAAG;gBACD;oBAAC,2MAAI,OAAA,CAAK,eAAe,GAAG,GAAG,GAAG,UAAU,CAAC;iBAAA;gBAC7C;oBAAC,2MAAI,OAAA,CAAK,2MAAI,qBAAA,CAAmB,MAAM,CAAC,GAAG,MAAM;oBAAG;wBAAC;wBAAG;wBAAG,IAAI;qBAAA;oBAAG;oBAAM;wBAAC;wBAAG;wBAAG,CAAC;qBAAC;iBAAA;aACnF;YACA,GAAG;gBACD;oBAAC,2MAAI,OAAA,CAAK,eAAe,GAAG,GAAG,GAAG,YAAY;oBAAG;oBAAM;wBAAC;wBAAG;wBAAG,CAAC,KAAK,EAAA,GAAK,CAAC;qBAAC;iBAAA;gBAC3E;oBAAC,2MAAI,OAAA,CAAK,2MAAI,qBAAA,CAAmB,MAAM,CAAC,GAAG,QAAQ;oBAAG;wBAAC;wBAAG;wBAAG,IAAI;qBAAA;oBAAG;oBAAM;wBAAC;wBAAG;wBAAG,CAAC;qBAAC;iBAAA;aACrF;YACA,GAAG;gBACD;oBAAC,2MAAI,OAAA,CAAK,eAAe,GAAG,GAAG,GAAG,WAAW;oBAAG;oBAAM;wBAAC;wBAAG,KAAK,EAAA,GAAK;wBAAG,CAAC;qBAAC;iBAAA;gBACzE;oBAAC,2MAAI,OAAA,CAAK,2MAAI,qBAAA,CAAmB,MAAM,CAAC,GAAG,OAAO;oBAAG;wBAAC;wBAAM;wBAAG,CAAC;qBAAA;oBAAG;oBAAM;wBAAC;wBAAG;wBAAG,CAAC;qBAAC;iBAAA;aACpF;YACA,GAAG;gBACD;oBAAC,2MAAI,OAAA,CAAK,eAAe,MAAM,CAAC,GAAG,wBAAwB;oBAAG;oBAAM;wBAAC;wBAAG,KAAK,EAAA,GAAK;wBAAG,CAAC;qBAAC;iBAAA;gBACvF;oBACE,2MAAI,OAAA,CAAK,2MAAI,mBAAA,CAAiB,MAAM,GAAG,MAAM,GAAG,GAAG,KAAK,GAAG,wBAAwB;oBACnF;wBAAC;wBAAM;wBAAG,CAAC;qBAAA;oBACX;wBAAC;wBAAG;wBAAG,CAAC,KAAK,EAAA,GAAK,CAAC;qBAAA;oBACnB;wBAAC;wBAAG;wBAAG,IAAK;qBAAA;iBACd;gBACA;oBACE,2MAAI,OAAA,CAAK,2MAAI,mBAAA,CAAiB,MAAM,GAAG,MAAM,GAAG,GAAG,KAAK,GAAG,wBAAwB;oBACnF;wBAAC,CAAA;wBAAO;wBAAG,CAAC;qBAAA;oBACZ;wBAAC;wBAAG;wBAAG,KAAK,EAAA,GAAK,CAAC;qBAAA;oBAClB;wBAAC;wBAAG;wBAAG,IAAK;qBAAA;iBACd;gBACA;oBACE,2MAAI,OAAA,CAAK,2MAAI,mBAAA,CAAiB,MAAM,GAAG,MAAM,GAAG,GAAG,KAAK,GAAG,wBAAwB;oBACnF;wBAAC;wBAAG,CAAA;wBAAO,CAAC;qBAAA;oBACZ;wBAAC,KAAK,EAAA;wBAAI;wBAAG,CAAC;qBAAA;oBACd;wBAAC;wBAAG;wBAAG,IAAK;qBAAA;iBACd;gBACA;oBACE,2MAAI,OAAA,CAAK,0MAAI,oBAAA,CAAiB,MAAM,GAAG,MAAM,GAAG,GAAG,KAAK,GAAG,wBAAwB;oBACnF;wBAAC;wBAAG;wBAAM,CAAC;qBAAA;oBACX;wBAAC;wBAAG;wBAAG,CAAC;qBAAA;oBACR;wBAAC;wBAAG;wBAAG,IAAK;qBAAA;iBACd;aACF;YACA,MAAM;gBAAC;oBAAC,2MAAI,OAAA,CAAK,eAAe,GAAG,CAAC,GAAG,WAAW;oBAAG;oBAAM;wBAAC;wBAAG,KAAK,EAAA,GAAK;wBAAG,CAAC;qBAAC;iBAAC;aAAA;QAAA;QAGjF,MAAM,eAAe;YACnB,MAAM;gBAAC;oBAAC,2MAAI,OAAA,CAAK,cAAc,UAAU,KAAA,CAAM,CAAC;oBAAG;wBAAC,CAAA;wBAAM;wBAAG,CAAC;qBAAA;oBAAG;oBAAM;wBAAC;wBAAK;wBAAG,CAAC;qBAAA;oBAAG,QAAQ;iBAAC;aAAA;QAAA;QAG/F,MAAM,eAAe;YACnB,GAAG;gBAAC;oBAAC,2MAAI,OAAA,CAAK,2MAAI,gBAAA,CAAc,GAAG,KAAK,GAAG,EAAE,GAAG,YAAY;oBAAG;wBAAC;wBAAG;wBAAG,CAAC;qBAAA;oBAAG;wBAAC;wBAAG,CAAC,KAAK,EAAA,GAAK;wBAAG,CAAC,KAAK,EAAA,GAAK,CAAC;qBAAC;iBAAC;aAAA;YAC1G,GAAG;gBAAC;oBAAC,IAAI,8MAAA,CAAK,2MAAI,gBAAA,CAAc,GAAG,KAAK,GAAG,EAAE,GAAG,YAAY;oBAAG;wBAAC;wBAAG;wBAAG,CAAC;qBAAA;oBAAG;wBAAC,KAAK,EAAA,GAAK;wBAAG;wBAAG,CAAC;qBAAC;iBAAC;aAAA;YAC9F,GAAG;gBAAC;oBAAC,2MAAI,OAAA,CAAK,2MAAI,gBAAA,CAAc,GAAG,KAAK,GAAG,EAAE,GAAG,YAAY;oBAAG;wBAAC;wBAAG;wBAAG,CAAC;qBAAA;oBAAG;wBAAC;wBAAG;wBAAG,CAAC,KAAK,EAAA,GAAK,CAAC;qBAAC;iBAAC;aAAA;YAC/F,GAAG;gBAAC;oBAAC,2MAAI,OAAA,CAAK,IAAI,uNAAA,CAAc,MAAM,KAAK,GAAG,EAAE,GAAG,YAAY,CAAC;iBAAC;aAAA;YACjE,MAAM;gBAAC;oBAAC,2MAAI,OAAA,CAAK,IAAI,wNAAA,CAAe,KAAK,IAAI,CAAC,GAAG,YAAY,CAAC;iBAAC;aAAA;QAAA;QAGjE,MAAM,aAAa;YACjB,GAAG;gBACD;oBAAC,2MAAI,OAAA,CAAK,qBAAqB,MAAM;oBAAG;wBAAC;wBAAK;wBAAG,CAAC;qBAAA;oBAAG;wBAAC;wBAAG;wBAAG,CAAC,KAAK,EAAA,GAAK,CAAC;qBAAC;iBAAA;gBACzE;oBAAC,2MAAI,OAAA,CAAK,cAAc,UAAU;oBAAG;oBAAM;oBAAM;wBAAC;wBAAK;wBAAG,CAAC;qBAAC;iBAAA;aAC9D;YACA,GAAG;gBACD;oBAAC,0MAAI,QAAA,CAAK,qBAAqB,QAAQ;oBAAG;wBAAC;wBAAG;wBAAK,CAAC;qBAAC;iBAAA;gBACrD;oBAAC,2MAAI,OAAA,CAAK,cAAc,YAAY;oBAAG;oBAAM;wBAAC;wBAAG;wBAAG,KAAK,EAAA,GAAK,CAAC;qBAAA;oBAAG;wBAAC;wBAAK;wBAAG,CAAC;qBAAC;iBAAA;aAC/E;YACA,GAAG;gBACD;oBAAC,2MAAI,OAAA,CAAK,qBAAqB,OAAO;oBAAG;wBAAC;wBAAG;wBAAG,GAAG;qBAAA;oBAAG;wBAAC,KAAK,EAAA,GAAK;wBAAG;wBAAG,CAAC;qBAAC;iBAAA;gBACzE;oBAAC,2MAAI,OAAA,CAAK,cAAc,WAAW;oBAAG;oBAAM;wBAAC;wBAAG,CAAC,KAAK,EAAA,GAAK;wBAAG,CAAC;qBAAA;oBAAG;wBAAC;wBAAK;wBAAG,CAAC;qBAAC;iBAAA;aAC/E;YACA,IAAI;gBACF;oBAAC,2MAAI,OAAA,CAAK,qBAAqB,oBAAoB;oBAAG;wBAAC;wBAAM;wBAAM,CAAC;qBAAA;oBAAG;oBAAM;wBAAC;wBAAG;wBAAG,GAAG;qBAAC;iBAAA;gBACxF;oBAAC,2MAAI,OAAA,CAAK,cAAc,aAAa;oBAAG;wBAAC;wBAAO;wBAAM,CAAC;qBAAA;oBAAG;oBAAM;wBAAC;wBAAO;wBAAG,CAAC;qBAAC;iBAAA;gBAC7E;oBAAC,2MAAI,OAAA,CAAK,cAAc,aAAa;oBAAG;wBAAC;wBAAM;wBAAO,CAAC;qBAAA;oBAAG;wBAAC;wBAAG;wBAAG,KAAK,EAAA,GAAK,CAAC;qBAAA;oBAAG;wBAAC;wBAAO;wBAAG,CAAC;qBAAC;iBAAA;aAC9F;YACA,IAAI;gBACF;oBAAC,2MAAI,OAAA,CAAK,qBAAqB,kBAAkB;oBAAG;wBAAC;wBAAG;wBAAM,IAAI;qBAAA;oBAAG;oBAAM;wBAAC;wBAAK;wBAAG,CAAC;qBAAC;iBAAA;gBACtF;oBAAC,2MAAI,OAAA,CAAK,cAAc,WAAW;oBAAG;wBAAC;wBAAG;wBAAO,IAAI;qBAAA;oBAAG;wBAAC;wBAAG;wBAAG,KAAK,EAAA,GAAK,CAAC;qBAAA;oBAAG;wBAAC;wBAAO;wBAAG,CAAC;qBAAC;iBAAA;gBAC1F;oBAAC,2MAAI,OAAA,CAAK,cAAc,WAAW;oBAAG;wBAAC;wBAAG;wBAAM,KAAK;qBAAA;oBAAG;wBAAC;wBAAG,CAAC,KAAK,EAAA,GAAK;wBAAG,CAAC;qBAAA;oBAAG;wBAAC;wBAAO;wBAAG,CAAC;qBAAC;iBAAA;aAC7F;YACA,IAAI;gBACF;oBAAC,2MAAI,OAAA,CAAK,qBAAqB,qBAAqB;oBAAG;wBAAC;wBAAM;wBAAG,IAAI;qBAAA;oBAAG;oBAAM;wBAAC;wBAAG;wBAAK,CAAC;qBAAC;iBAAA;gBACzF;oBAAC,IAAI,8MAAA,CAAK,cAAc,cAAc;oBAAG;wBAAC;wBAAO;wBAAG,IAAI;qBAAA;oBAAG;oBAAM;wBAAC;wBAAO;wBAAG,CAAC;qBAAC;iBAAA;gBAC9E;oBAAC,2MAAI,OAAA,CAAK,cAAc,cAAc;oBAAG;wBAAC;wBAAM;wBAAG,KAAK;qBAAA;oBAAG;wBAAC;wBAAG,CAAC,KAAK,EAAA,GAAK;wBAAG,CAAC;qBAAA;oBAAG;wBAAC;wBAAO;wBAAG,CAAC;qBAAC;iBAAA;aAChG;YACA,MAAM;gBAAC;oBAAC,2MAAI,OAAA,CAAK,IAAI,qNAAA,CAAY,OAAO,OAAO,KAAK,GAAG,oBAAoB,KAAA,CAAA,CAAO;oBAAG;wBAAC;wBAAK;wBAAG,CAAC;qBAAC;iBAAC;aAAA;YACjG,MAAM;gBAAC;oBAAC,2MAAI,OAAA,CAAK,IAAI,qNAAA,CAAY,OAAO,OAAO,KAAK,GAAG,oBAAoB,KAAA,CAAA,CAAO;oBAAG;wBAAC;wBAAG;wBAAK,CAAC;qBAAC;iBAAC;aAAA;YACjG,MAAM;gBAAC;oBAAC,2MAAI,OAAA,CAAK,2MAAI,cAAA,CAAY,OAAO,OAAO,KAAK,GAAG,oBAAoB,KAAA,CAAA,CAAO;oBAAG;wBAAC;wBAAG;wBAAG,GAAG;qBAAC;iBAAC;aAAA;QAAA;QAGnG,MAAM,cAAc;YAClB,GAAG;gBAAC;oBAAC,2MAAI,OAAA,CAAK,2MAAI,mBAAA,CAAiB,KAAK,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,YAAY;oBAAG;wBAAC;wBAAK;wBAAG,CAAC;qBAAA;oBAAG;wBAAC;wBAAG;wBAAG,CAAC,KAAK,EAAA,GAAK,CAAC;qBAAC;iBAAC;aAAA;YAC/G,GAAG;gBAAC;oBAAC,2MAAI,OAAA,CAAK,2MAAI,mBAAA,CAAiB,KAAK,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,YAAY;oBAAG;wBAAC;wBAAG;wBAAK,CAAC;qBAAC;iBAAC;aAAA;YACzF,GAAG;gBAAC;oBAAC,2MAAI,OAAA,CAAK,2MAAI,mBAAA,CAAiB,KAAK,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,YAAY;oBAAG;wBAAC;wBAAG;wBAAG,GAAG;qBAAA;oBAAG;wBAAC,KAAK,EAAA,GAAK;wBAAG;wBAAG,CAAC;qBAAC;iBAAC;aAAA;YAC9G,IAAI;gBAAC;oBAAC,2MAAI,OAAA,CAAK,qBAAqB,YAAY;oBAAG;wBAAC;wBAAM;wBAAM,CAAC;qBAAA;oBAAG;oBAAM;wBAAC;wBAAG;wBAAG,GAAG;qBAAC;iBAAC;aAAA;YACtF,IAAI;gBAAC;oBAAC,2MAAI,OAAA,CAAK,qBAAqB,YAAY;oBAAG;wBAAC;wBAAG;wBAAM,IAAI;qBAAA;oBAAG;oBAAM;wBAAC;wBAAK;wBAAG,CAAC;qBAAC;iBAAC;aAAA;YACtF,IAAI;gBAAC;oBAAC,2MAAI,OAAA,CAAK,qBAAqB,YAAY;oBAAG;wBAAC;wBAAM;wBAAG,IAAI;qBAAA;oBAAG;oBAAM;wBAAC;wBAAG;wBAAK,CAAC;qBAAC;iBAAC;aAAA;YACtF,MAAM;gBAAC;oBAAC,2MAAI,OAAA,CAAK,2MAAI,cAAA,CAAY,KAAK,KAAK,GAAG,GAAG,YAAY;oBAAG;wBAAC;wBAAK;wBAAG,CAAC;qBAAC;iBAAC;aAAA;YAC5E,MAAM;gBAAC;oBAAC,0MAAI,QAAA,CAAK,2MAAI,cAAA,CAAY,KAAK,KAAK,GAAG,GAAG,YAAY;oBAAG;wBAAC;wBAAG;wBAAK,CAAC;qBAAC;iBAAC;aAAA;YAC5E,MAAM;gBAAC;oBAAC,2MAAI,OAAA,CAAK,2MAAI,cAAA,CAAY,KAAK,KAAK,GAAG,GAAG,YAAY;oBAAG;wBAAC;wBAAG;wBAAG,GAAG;qBAAC;iBAAC;aAAA;QAAA;QAG9E,MAAM,cAAc;YAClB,GAAG;gBAAC;oBAAC,2MAAI,OAAA,CAAK,cAAc,UAAU,KAAA,CAAM,CAAC;oBAAG;wBAAC,CAAA;wBAAM;wBAAG,CAAC;qBAAA;oBAAG;oBAAM;wBAAC;wBAAK;wBAAG,CAAC;qBAAA;oBAAG,QAAQ;iBAAC;aAAA;YAC1F,GAAG;gBAAC;oBAAC,2MAAI,OAAA,CAAK,cAAc,UAAU,KAAA,CAAA,CAAO;oBAAG;wBAAC;wBAAG,CAAA;wBAAM,CAAC;qBAAA;oBAAG;wBAAC;wBAAG;wBAAG,KAAK,EAAA,GAAK,CAAC;qBAAA;oBAAG;wBAAC;wBAAK;wBAAG,CAAC;qBAAA;oBAAG,QAAQ;iBAAC;aAAA;YACzG,GAAG;gBAAC;oBAAC,2MAAI,OAAA,CAAK,cAAc,UAAU,KAAA,CAAA,CAAO;oBAAG;wBAAC;wBAAG;wBAAG,CAAA,GAAI;qBAAA;oBAAG;wBAAC;wBAAG,CAAC,KAAK,EAAA,GAAK;wBAAG,CAAC;qBAAA;oBAAG;wBAAC;wBAAK;wBAAG,CAAC;qBAAA;oBAAG,QAAQ;iBAAC;aAAA;QAAA;QAKtG,MAAA,aAAa,CAAC,aAA4B;YACxC,MAAA,QAAQ,2MAAI,WAAA;YAElB,IAAA,IAAS,QAAQ,SAAU;gBACzB,IAAA,IAAS,IAAI,QAAA,CAAS,IAAI,CAAA,CAAE,MAAA,EAAQ,KAAO;oBACnC,MAAA,SAAS,QAAA,CAAS,IAAI,CAAA,CAAE,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,KAAA;oBACpC,MAAM,WAAW,QAAA,CAAS,IAAI,CAAA,CAAE,CAAC,CAAA,CAAE,CAAC,CAAA;oBACpC,MAAM,WAAW,QAAA,CAAS,IAAI,CAAA,CAAE,CAAC,CAAA,CAAE,CAAC,CAAA;oBACpC,MAAM,QAAQ,QAAA,CAAS,IAAI,CAAA,CAAE,CAAC,CAAA,CAAE,CAAC,CAAA;oBACjC,MAAM,MAAM,QAAA,CAAS,IAAI,CAAA,CAAE,CAAC,CAAA,CAAE,CAAC,CAAA;oBAG/B,OAAO,IAAA,GAAO;oBAEd,OAAO,GAAA,GAAM;oBAEb,IAAI,UAAU;wBACL,OAAA,QAAA,CAAS,GAAA,CAAI,QAAA,CAAS,CAAC,CAAA,EAAG,QAAA,CAAS,CAAC,CAAA,EAAG,QAAA,CAAS,CAAC,CAAC;oBAC3D;oBAEA,IAAI,UAAU;wBACL,OAAA,QAAA,CAAS,GAAA,CAAI,QAAA,CAAS,CAAC,CAAA,EAAG,QAAA,CAAS,CAAC,CAAA,EAAG,QAAA,CAAS,CAAC,CAAC;oBAC3D;oBAEA,IAAI,OAAO;wBACF,OAAA,KAAA,CAAM,GAAA,CAAI,KAAA,CAAM,CAAC,CAAA,EAAG,KAAA,CAAM,CAAC,CAAA,EAAG,KAAA,CAAM,CAAC,CAAC;oBAC/C;oBAEA,OAAO,YAAA,CAAa;oBAEd,MAAA,eAAe,OAAO,QAAA,CAAS,KAAA,CAAM;oBAC9B,aAAA,YAAA,CAAa,OAAO,MAAM;oBACvC,OAAO,QAAA,GAAW;oBAClB,OAAO,WAAA,GAAc;oBAErB,OAAO,QAAA,CAAS,GAAA,CAAI,GAAG,GAAG,CAAC;oBAC3B,OAAO,QAAA,CAAS,GAAA,CAAI,GAAG,GAAG,CAAC;oBAC3B,OAAO,KAAA,CAAM,GAAA,CAAI,GAAG,GAAG,CAAC;oBAExB,MAAM,GAAA,CAAI,MAAM;gBAClB;YACF;YAEO,OAAA;QAAA;QAGT,IAAA,CAAK,KAAA,GAAQ,CAAA;QACb,IAAA,CAAK,MAAA,GAAS,CAAA;QACd,IAAA,CAAK,MAAA,GAAS,CAAA;QAEd,IAAA,CAAK,GAAA,CAAK,IAAA,CAAK,KAAA,CAAM,WAAW,CAAA,GAAI,WAAW,cAAc,CAAE;QAC/D,IAAA,CAAK,GAAA,CAAK,IAAA,CAAK,KAAA,CAAM,QAAQ,CAAA,GAAI,WAAW,WAAW,CAAE;QACzD,IAAA,CAAK,GAAA,CAAK,IAAA,CAAK,KAAA,CAAM,OAAO,CAAA,GAAI,WAAW,UAAU,CAAE;QACvD,IAAA,CAAK,GAAA,CAAK,IAAA,CAAK,MAAA,CAAO,WAAW,CAAA,GAAI,WAAW,eAAe,CAAE;QACjE,IAAA,CAAK,GAAA,CAAK,IAAA,CAAK,MAAA,CAAO,QAAQ,CAAA,GAAI,WAAW,YAAY,CAAE;QAC3D,IAAA,CAAK,GAAA,CAAK,IAAA,CAAK,MAAA,CAAO,OAAO,CAAA,GAAI,WAAW,WAAW,CAAE;QACzD,IAAA,CAAK,GAAA,CAAK,IAAA,CAAK,MAAA,CAAO,WAAW,CAAA,GAAI,WAAW,eAAe,CAAE;QACjE,IAAA,CAAK,GAAA,CAAK,IAAA,CAAK,MAAA,CAAO,QAAQ,CAAA,GAAI,WAAW,YAAY,CAAE;QAC3D,IAAA,CAAK,GAAA,CAAK,IAAA,CAAK,MAAA,CAAO,OAAO,CAAA,GAAI,WAAW,WAAW,CAAE;QAIpD,IAAA,CAAA,MAAA,CAAO,WAAW,CAAA,CAAE,OAAA,GAAU;QAC9B,IAAA,CAAA,MAAA,CAAO,QAAQ,CAAA,CAAE,OAAA,GAAU;QAC3B,IAAA,CAAA,MAAA,CAAO,OAAO,CAAA,CAAE,OAAA,GAAU;IACjC;AAyTF;AAEA,MAAM,+BAA+B,8MAAA,CAAuC;IAI1E,aAAc;QACZ,KAAA,CACE,2MAAI,gBAAA,CAAc,KAAQ,KAAQ,GAAG,CAAC,GACtC,2MAAI,oBAAA,CAAkB;YACpB,SAAS;YACT,WAAW;YACX,6MAAM,aAAA;YACN,aAAa;YACb,SAAS;YACT,YAAY;QAAA,CACb;QAbG,cAAA,IAAA,EAAA,4BAA2B;QAC5B,cAAA,IAAA,EAAA,QAAO;QAgBN,cAAA,IAAA,EAAA,SAAQ,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;QAC3B,cAAA,IAAA,EAAA,SAAQ,0MAAI,WAAA,CAAQ,GAAG,GAAG,CAAC;QAC3B,cAAA,IAAA,EAAA,SAAQ,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;QAE3B,cAAA,IAAA,EAAA,cAAa,2MAAI,UAAA;QACjB,cAAA,IAAA,EAAA,aAAY,2MAAI,UAAA;QAChB,cAAA,IAAA,EAAA,eAAc,2MAAI,UAAA;QAClB,cAAA,IAAA,EAAA,cAAa,2MAAI,UAAA;QACjB,cAAA,IAAA,EAAA,sBAAqB,2MAAI,aAAA;QAGzB,oDAAA;QAAA,cAAA,IAAA,EAAA,oBAAmB,2MAAI,aAAA;QAEvB,cAAA,IAAA,EAAA,iBAAgB,2MAAI,UAAA;QACpB,cAAA,IAAA,EAAA,mBAAkB,2MAAI,aAAA;QAEtB,cAAA,IAAA,EAAA,OAAM,2MAAI,UAAA;QAEV,cAAA,IAAA,EAAA,QAAsB;QACtB,cAAA,IAAA,EAAA,QAAyC;QACzC,cAAA,IAAA,EAAA,SAAQ;QAET,cAAA,IAAA,EAAA,qBAAoB,MAAY;YACrC,IAAI,QAAQ,IAAA,CAAK,KAAA;YAEZ,IAAA,CAAA,QAAA,CAAS,IAAA,CAAK,IAAA,CAAK,aAAa;YAErC,IAAI,IAAA,CAAK,IAAA,KAAS,SAAiB,QAAA;YAEnC,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,GAAG,GAAG,CAAC,EAAE,eAAA,CAAgB,UAAU,UAAU,IAAA,CAAK,eAAA,GAAkB,IAAA,CAAK,kBAAkB;YAC1G,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,GAAG,GAAG,CAAC,EAAE,eAAA,CAAgB,UAAU,UAAU,IAAA,CAAK,eAAA,GAAkB,IAAA,CAAK,kBAAkB;YAC1G,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,GAAG,GAAG,CAAC,EAAE,eAAA,CAAgB,UAAU,UAAU,IAAA,CAAK,eAAA,GAAkB,IAAA,CAAK,kBAAkB;YAIrG,IAAA,CAAA,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,KAAK;YAEhC,OAAQ,IAAA,CAAK,IAAA,EAAM;gBACjB,KAAK;gBACL,KAAK;oBACH,OAAQ,IAAA,CAAK,IAAA,EAAM;wBACjB,KAAK;4BACH,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,GAAG,EAAE,KAAA,CAAM,IAAA,CAAK,KAAK;4BAChD,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,KAAA,CAAM,IAAA,CAAK,WAAW;4BACtD;wBACF,KAAK;4BACH,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,GAAG,EAAE,KAAA,CAAM,IAAA,CAAK,KAAK;4BAChD,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,KAAA,CAAM,IAAA,CAAK,WAAW;4BACtD;wBACF,KAAK;4BACH,IAAA,CAAK,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,GAAG,EAAE,KAAA,CAAM,IAAA,CAAK,KAAK;4BAChD,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,KAAK,EAAE,KAAA,CAAM,IAAA,CAAK,WAAW;4BACtD;wBACF,KAAK;4BACE,IAAA,CAAA,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,KAAK;4BAC9B;wBACF,KAAK;4BACE,IAAA,CAAA,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,KAAK;4BAC9B;wBACF,KAAK;4BACE,IAAA,CAAA,WAAA,CAAY,IAAA,CAAK,IAAA,CAAK,KAAK;4BAC3B,IAAA,CAAA,SAAA,CAAU,IAAA,CAAK,IAAA,CAAK,KAAK;4BAC9B;wBACF,KAAK;wBACL,KAAK;4BACH,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,GAAG,GAAG,CAAC;4BAC1B;oBACJ;oBAEA;gBACF,KAAK;gBACL;oBAEE,IAAA,CAAK,SAAA,CAAU,GAAA,CAAI,GAAG,GAAG,CAAC;YAC9B;YAEA,IAAI,IAAA,CAAK,SAAA,CAAU,MAAA,CAAO,MAAM,GAAG;gBAE5B,IAAA,CAAA,UAAA,CAAW,IAAA,CAAK,IAAA,CAAK,gBAAgB;YAAA,OACrC;gBACL,IAAA,CAAK,UAAA,CAAW,MAAA,CAAO,IAAA,CAAK,UAAA,CAAW,GAAA,CAAI,GAAG,GAAG,CAAC,GAAG,IAAA,CAAK,SAAA,EAAW,IAAA,CAAK,WAAW;gBAEhF,IAAA,CAAA,UAAA,CAAW,qBAAA,CAAsB,IAAA,CAAK,UAAU;YACvD;YAEA,KAAA,CAAM,kBAAkB;QAAA;IAvF1B;AAyFF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5896, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5902, "column": 0}, "map": {"version": 3, "file": "PointerLockControls.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/controls/PointerLockControls.ts"], "sourcesContent": ["import { <PERSON>ule<PERSON>, <PERSON>, Vector3 } from 'three'\nimport { EventDispatcher } from './EventDispatcher'\n\nconst _euler = /* @__PURE__ */ new Euler(0, 0, 0, 'YXZ')\nconst _vector = /* @__PURE__ */ new Vector3()\nconst _changeEvent = { type: 'change' }\nconst _lockEvent = { type: 'lock' }\nconst _unlockEvent = { type: 'unlock' }\nconst _MOUSE_SENSITIVITY = 0.002\nconst _PI_2 = Math.PI / 2\n\nexport interface PointerLockControlsEventMap {\n  /**\n   * Fires when the user moves the mouse.\n   */\n  change: {}\n\n  /**\n   * Fires when the pointer lock status is \"locked\" (in other words: the mouse is captured).\n   */\n  lock: {}\n\n  /**\n   * Fires when the pointer lock status is \"unlocked\" (in other words: the mouse is not captured anymore).\n   */\n  unlock: {}\n}\n\nclass PointerLockControls extends EventDispatcher<PointerLockControlsEventMap> {\n  public camera: Camera\n  public domElement?: HTMLElement\n  public isLocked: boolean\n  public minPolarAngle: number\n  public maxPolarAngle: number\n  public pointerSpeed: number\n\n  constructor(camera: Camera, domElement?: HTMLElement) {\n    super()\n\n    this.camera = camera\n    this.domElement = domElement\n    this.isLocked = false\n\n    // Set to constrain the pitch of the camera\n    // Range is 0 to Math.PI radians\n    this.minPolarAngle = 0 // radians\n    this.maxPolarAngle = Math.PI // radians\n\n    this.pointerSpeed = 1.0\n    if (domElement) this.connect(domElement)\n  }\n\n  private onMouseMove = (event: MouseEvent): void => {\n    if (!this.domElement || this.isLocked === false) return\n    _euler.setFromQuaternion(this.camera.quaternion)\n    _euler.y -= event.movementX * _MOUSE_SENSITIVITY * this.pointerSpeed\n    _euler.x -= event.movementY * _MOUSE_SENSITIVITY * this.pointerSpeed\n    _euler.x = Math.max(_PI_2 - this.maxPolarAngle, Math.min(_PI_2 - this.minPolarAngle, _euler.x))\n    this.camera.quaternion.setFromEuler(_euler)\n    // @ts-ignore\n    this.dispatchEvent(_changeEvent)\n  }\n\n  private onPointerlockChange = (): void => {\n    if (!this.domElement) return\n    if (this.domElement.ownerDocument.pointerLockElement === this.domElement) {\n      // @ts-ignore\n      this.dispatchEvent(_lockEvent)\n      this.isLocked = true\n    } else {\n      // @ts-ignore\n      this.dispatchEvent(_unlockEvent)\n      this.isLocked = false\n    }\n  }\n\n  private onPointerlockError = (): void => {\n    console.error('THREE.PointerLockControls: Unable to use Pointer Lock API')\n  }\n\n  public connect = (domElement: HTMLElement): void => {\n    this.domElement = domElement || this.domElement\n    if (!this.domElement) return\n    this.domElement.ownerDocument.addEventListener('mousemove', this.onMouseMove)\n    this.domElement.ownerDocument.addEventListener('pointerlockchange', this.onPointerlockChange)\n    this.domElement.ownerDocument.addEventListener('pointerlockerror', this.onPointerlockError)\n  }\n\n  public disconnect = (): void => {\n    if (!this.domElement) return\n    this.domElement.ownerDocument.removeEventListener('mousemove', this.onMouseMove)\n    this.domElement.ownerDocument.removeEventListener('pointerlockchange', this.onPointerlockChange)\n    this.domElement.ownerDocument.removeEventListener('pointerlockerror', this.onPointerlockError)\n  }\n\n  public dispose = (): void => {\n    this.disconnect()\n  }\n\n  public getObject = (): Camera => {\n    // retaining this method for backward compatibility\n    return this.camera\n  }\n\n  private direction = new Vector3(0, 0, -1)\n  public getDirection = (v: Vector3): Vector3 => {\n    return v.copy(this.direction).applyQuaternion(this.camera.quaternion)\n  }\n\n  public moveForward = (distance: number): void => {\n    // move forward parallel to the xz-plane\n    // assumes camera.up is y-up\n    _vector.setFromMatrixColumn(this.camera.matrix, 0)\n    _vector.crossVectors(this.camera.up, _vector)\n    this.camera.position.addScaledVector(_vector, distance)\n  }\n\n  public moveRight = (distance: number): void => {\n    _vector.setFromMatrixColumn(this.camera.matrix, 0)\n    this.camera.position.addScaledVector(_vector, distance)\n  }\n\n  public lock = (): void => {\n    if (this.domElement) this.domElement.requestPointerLock()\n  }\n\n  public unlock = (): void => {\n    if (this.domElement) this.domElement.ownerDocument.exitPointerLock()\n  }\n}\n\nexport { PointerLockControls }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA,MAAM,SAA6B,aAAA,GAAA,2MAAA,QAAA,CAAM,GAAG,GAAG,GAAG,KAAK;AACvD,MAAM,UAAA,aAAA,GAAA,2MAA8B,UAAA;AACpC,MAAM,eAAe;IAAE,MAAM;AAAA;AAC7B,MAAM,aAAa;IAAE,MAAM;AAAA;AAC3B,MAAM,eAAe;IAAE,MAAM;AAAA;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,QAAQ,KAAK,EAAA,GAAK;AAmBxB,MAAM,kRAA4B,kBAAA,CAA6C;IAQ7E,YAAY,MAAA,EAAgB,UAAA,CAA0B;QAC9C,KAAA;QARD,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAkBC,cAAA,IAAA,EAAA,eAAc,CAAC,UAA4B;YACjD,IAAI,CAAC,IAAA,CAAK,UAAA,IAAc,IAAA,CAAK,QAAA,KAAa,OAAO;YAC1C,OAAA,iBAAA,CAAkB,IAAA,CAAK,MAAA,CAAO,UAAU;YAC/C,OAAO,CAAA,IAAK,MAAM,SAAA,GAAY,qBAAqB,IAAA,CAAK,YAAA;YACxD,OAAO,CAAA,IAAK,MAAM,SAAA,GAAY,qBAAqB,IAAA,CAAK,YAAA;YACxD,OAAO,CAAA,GAAI,KAAK,GAAA,CAAI,QAAQ,IAAA,CAAK,aAAA,EAAe,KAAK,GAAA,CAAI,QAAQ,IAAA,CAAK,aAAA,EAAe,OAAO,CAAC,CAAC;YACzF,IAAA,CAAA,MAAA,CAAO,UAAA,CAAW,YAAA,CAAa,MAAM;YAE1C,IAAA,CAAK,aAAA,CAAc,YAAY;QAAA;QAGzB,cAAA,IAAA,EAAA,uBAAsB,MAAY;YACxC,IAAI,CAAC,IAAA,CAAK,UAAA,EAAY;YACtB,IAAI,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,kBAAA,KAAuB,IAAA,CAAK,UAAA,EAAY;gBAExE,IAAA,CAAK,aAAA,CAAc,UAAU;gBAC7B,IAAA,CAAK,QAAA,GAAW;YAAA,OACX;gBAEL,IAAA,CAAK,aAAA,CAAc,YAAY;gBAC/B,IAAA,CAAK,QAAA,GAAW;YAClB;QAAA;QAGM,cAAA,IAAA,EAAA,sBAAqB,MAAY;YACvC,QAAQ,KAAA,CAAM,2DAA2D;QAAA;QAGpE,cAAA,IAAA,EAAA,WAAU,CAAC,eAAkC;YAC7C,IAAA,CAAA,UAAA,GAAa,cAAc,IAAA,CAAK,UAAA;YACrC,IAAI,CAAC,IAAA,CAAK,UAAA,EAAY;YACtB,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,gBAAA,CAAiB,aAAa,IAAA,CAAK,WAAW;YAC5E,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,gBAAA,CAAiB,qBAAqB,IAAA,CAAK,mBAAmB;YAC5F,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,gBAAA,CAAiB,oBAAoB,IAAA,CAAK,kBAAkB;QAAA;QAGrF,cAAA,IAAA,EAAA,cAAa,MAAY;YAC9B,IAAI,CAAC,IAAA,CAAK,UAAA,EAAY;YACtB,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,mBAAA,CAAoB,aAAa,IAAA,CAAK,WAAW;YAC/E,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,mBAAA,CAAoB,qBAAqB,IAAA,CAAK,mBAAmB;YAC/F,IAAA,CAAK,UAAA,CAAW,aAAA,CAAc,mBAAA,CAAoB,oBAAoB,IAAA,CAAK,kBAAkB;QAAA;QAGxF,cAAA,IAAA,EAAA,WAAU,MAAY;YAC3B,IAAA,CAAK,UAAA,CAAW;QAAA;QAGX,cAAA,IAAA,EAAA,aAAY,MAAc;YAE/B,OAAO,IAAA,CAAK,MAAA;QAAA;QAGN,cAAA,IAAA,EAAA,aAAY,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAA,CAAE;QACjC,cAAA,IAAA,EAAA,gBAAe,CAAC,MAAwB;YACtC,OAAA,EAAE,IAAA,CAAK,IAAA,CAAK,SAAS,EAAE,eAAA,CAAgB,IAAA,CAAK,MAAA,CAAO,UAAU;QAAA;QAG/D,cAAA,IAAA,EAAA,eAAc,CAAC,aAA2B;YAG/C,QAAQ,mBAAA,CAAoB,IAAA,CAAK,MAAA,CAAO,MAAA,EAAQ,CAAC;YACjD,QAAQ,YAAA,CAAa,IAAA,CAAK,MAAA,CAAO,EAAA,EAAI,OAAO;YAC5C,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,eAAA,CAAgB,SAAS,QAAQ;QAAA;QAGjD,cAAA,IAAA,EAAA,aAAY,CAAC,aAA2B;YAC7C,QAAQ,mBAAA,CAAoB,IAAA,CAAK,MAAA,CAAO,MAAA,EAAQ,CAAC;YACjD,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,eAAA,CAAgB,SAAS,QAAQ;QAAA;QAGjD,cAAA,IAAA,EAAA,QAAO,MAAY;YACxB,IAAI,IAAA,CAAK,UAAA,EAAY,IAAA,CAAK,UAAA,CAAW,kBAAA;QAAmB;QAGnD,cAAA,IAAA,EAAA,UAAS,MAAY;YAC1B,IAAI,IAAA,CAAK,UAAA,EAAiB,IAAA,CAAA,UAAA,CAAW,aAAA,CAAc,eAAA;QAAgB;QAxFnE,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,UAAA,GAAa;QAClB,IAAA,CAAK,QAAA,GAAW;QAIhB,IAAA,CAAK,aAAA,GAAgB;QACrB,IAAA,CAAK,aAAA,GAAgB,KAAK,EAAA;QAE1B,IAAA,CAAK,YAAA,GAAe;QAChB,IAAA,YAAY,IAAA,CAAK,OAAA,CAAQ,UAAU;IACzC;AA+EF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6013, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6019, "column": 0}, "map": {"version": 3, "file": "FirstPersonControls.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/controls/FirstPersonControls.ts"], "sourcesContent": ["import { MathUtils, Spherical, Vector3, Camera } from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\nconst targetPosition = /* @__PURE__ */ new Vector3()\n\nexport class FirstPersonControls extends EventDispatcher<{}> {\n  public object: Camera\n  public domElement?: HTMLElement | null\n\n  public enabled = true\n\n  public movementSpeed = 1.0\n  public lookSpeed = 0.005\n\n  public lookVertical = true\n  public autoForward = false\n\n  public activeLook = true\n\n  public heightSpeed = false\n  public heightCoef = 1.0\n  public heightMin = 0.0\n  public heightMax = 1.0\n\n  public constrainVertical = false\n  public verticalMin = 0\n  public verticalMax = Math.PI\n\n  public mouseDragOn = false\n\n  // internals\n\n  private autoSpeedFactor = 0.0\n\n  private mouseX = 0\n  private mouseY = 0\n\n  private moveForward = false\n  private moveBackward = false\n  private moveLeft = false\n  private moveRight = false\n  private moveUp = false\n  private moveDown = false\n\n  private viewHalfX = 0\n  private viewHalfY = 0\n\n  private lat = 0\n  private lon = 0\n\n  private lookDirection = new Vector3()\n  private spherical = new Spherical()\n  readonly target = new Vector3()\n\n  constructor(object: Camera, domElement?: HTMLElement | null) {\n    super()\n\n    this.object = object\n    this.domElement = domElement\n\n    this.setOrientation()\n\n    if (domElement) this.connect(domElement)\n  }\n\n  public connect = (domElement: HTMLElement): void => {\n    domElement.setAttribute('tabindex', '-1')\n\n    domElement.style.touchAction = 'none'\n\n    domElement.addEventListener('contextmenu', this.contextmenu)\n    domElement.addEventListener('mousemove', this.onMouseMove)\n    domElement.addEventListener('mousedown', this.onMouseDown)\n    domElement.addEventListener('mouseup', this.onMouseUp)\n\n    this.domElement = domElement\n\n    window.addEventListener('keydown', this.onKeyDown)\n    window.addEventListener('keyup', this.onKeyUp)\n\n    this.handleResize()\n  }\n\n  public dispose = (): void => {\n    this.domElement?.removeEventListener('contextmenu', this.contextmenu)\n    this.domElement?.removeEventListener('mousedown', this.onMouseDown)\n    this.domElement?.removeEventListener('mousemove', this.onMouseMove)\n    this.domElement?.removeEventListener('mouseup', this.onMouseUp)\n\n    window.removeEventListener('keydown', this.onKeyDown)\n    window.removeEventListener('keyup', this.onKeyUp)\n  }\n\n  public handleResize = (): void => {\n    if (this.domElement) {\n      this.viewHalfX = this.domElement.offsetWidth / 2\n      this.viewHalfY = this.domElement.offsetHeight / 2\n    }\n  }\n\n  private onMouseDown = (event: MouseEvent): void => {\n    this.domElement?.focus()\n\n    if (this.activeLook) {\n      switch (event.button) {\n        case 0:\n          this.moveForward = true\n          break\n        case 2:\n          this.moveBackward = true\n          break\n      }\n    }\n\n    this.mouseDragOn = true\n  }\n\n  private onMouseUp = (event: MouseEvent): void => {\n    if (this.activeLook) {\n      switch (event.button) {\n        case 0:\n          this.moveForward = false\n          break\n        case 2:\n          this.moveBackward = false\n          break\n      }\n    }\n\n    this.mouseDragOn = false\n  }\n\n  private onMouseMove = (event: MouseEvent): void => {\n    if (this.domElement) {\n      this.mouseX = event.pageX - this.domElement.offsetLeft - this.viewHalfX\n      this.mouseY = event.pageY - this.domElement.offsetTop - this.viewHalfY\n    }\n  }\n\n  private onKeyDown = (event: KeyboardEvent): void => {\n    switch (event.code) {\n      case 'ArrowUp':\n      case 'KeyW':\n        this.moveForward = true\n        break\n\n      case 'ArrowLeft':\n      case 'KeyA':\n        this.moveLeft = true\n        break\n\n      case 'ArrowDown':\n      case 'KeyS':\n        this.moveBackward = true\n        break\n\n      case 'ArrowRight':\n      case 'KeyD':\n        this.moveRight = true\n        break\n\n      case 'KeyR':\n        this.moveUp = true\n        break\n      case 'KeyF':\n        this.moveDown = true\n        break\n    }\n  }\n\n  private onKeyUp = (event: KeyboardEvent): void => {\n    switch (event.code) {\n      case 'ArrowUp':\n      case 'KeyW':\n        this.moveForward = false\n        break\n\n      case 'ArrowLeft':\n      case 'KeyA':\n        this.moveLeft = false\n        break\n\n      case 'ArrowDown':\n      case 'KeyS':\n        this.moveBackward = false\n        break\n\n      case 'ArrowRight':\n      case 'KeyD':\n        this.moveRight = false\n        break\n\n      case 'KeyR':\n        this.moveUp = false\n        break\n      case 'KeyF':\n        this.moveDown = false\n        break\n    }\n  }\n\n  public lookAt = (x: Vector3 | number, y?: number, z?: number): this => {\n    if (x instanceof Vector3) {\n      this.target.copy(x)\n    } else if (y && z) {\n      this.target.set(x, y, z)\n    }\n\n    this.object.lookAt(this.target)\n\n    this.setOrientation()\n\n    return this\n  }\n\n  public update = (delta: number): void => {\n    if (!this.enabled) return\n\n    if (this.heightSpeed) {\n      const y = MathUtils.clamp(this.object.position.y, this.heightMin, this.heightMax)\n      const heightDelta = y - this.heightMin\n\n      this.autoSpeedFactor = delta * (heightDelta * this.heightCoef)\n    } else {\n      this.autoSpeedFactor = 0.0\n    }\n\n    const actualMoveSpeed = delta * this.movementSpeed\n\n    if (this.moveForward || (this.autoForward && !this.moveBackward)) {\n      this.object.translateZ(-(actualMoveSpeed + this.autoSpeedFactor))\n    }\n    if (this.moveBackward) this.object.translateZ(actualMoveSpeed)\n\n    if (this.moveLeft) this.object.translateX(-actualMoveSpeed)\n    if (this.moveRight) this.object.translateX(actualMoveSpeed)\n\n    if (this.moveUp) this.object.translateY(actualMoveSpeed)\n    if (this.moveDown) this.object.translateY(-actualMoveSpeed)\n\n    let actualLookSpeed = delta * this.lookSpeed\n\n    if (!this.activeLook) {\n      actualLookSpeed = 0\n    }\n\n    let verticalLookRatio = 1\n\n    if (this.constrainVertical) {\n      verticalLookRatio = Math.PI / (this.verticalMax - this.verticalMin)\n    }\n\n    this.lon -= this.mouseX * actualLookSpeed\n    if (this.lookVertical) this.lat -= this.mouseY * actualLookSpeed * verticalLookRatio\n\n    this.lat = Math.max(-85, Math.min(85, this.lat))\n\n    let phi = MathUtils.degToRad(90 - this.lat)\n    const theta = MathUtils.degToRad(this.lon)\n\n    if (this.constrainVertical) {\n      phi = MathUtils.mapLinear(phi, 0, Math.PI, this.verticalMin, this.verticalMax)\n    }\n\n    const position = this.object.position\n\n    targetPosition.setFromSphericalCoords(1, phi, theta).add(position)\n\n    this.object.lookAt(targetPosition)\n  }\n\n  private contextmenu = (event: Event): void => event.preventDefault()\n\n  private setOrientation = (): void => {\n    this.lookDirection.set(0, 0, -1).applyQuaternion(this.object.quaternion)\n    this.spherical.setFromVector3(this.lookDirection)\n    this.lat = 90 - MathUtils.radToDeg(this.spherical.phi)\n    this.lon = MathUtils.radToDeg(this.spherical.theta)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAIA,MAAM,iBAAA,aAAA,GAAA,2MAAqC,UAAA;AAEpC,MAAM,kRAA4B,kBAAA,CAAoB;IAiD3D,YAAY,MAAA,EAAgB,UAAA,CAAiC;QACrD,KAAA;QAjDD,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA,WAAU;QAEV,cAAA,IAAA,EAAA,iBAAgB;QAChB,cAAA,IAAA,EAAA,aAAY;QAEZ,cAAA,IAAA,EAAA,gBAAe;QACf,cAAA,IAAA,EAAA,eAAc;QAEd,cAAA,IAAA,EAAA,cAAa;QAEb,cAAA,IAAA,EAAA,eAAc;QACd,cAAA,IAAA,EAAA,cAAa;QACb,cAAA,IAAA,EAAA,aAAY;QACZ,cAAA,IAAA,EAAA,aAAY;QAEZ,cAAA,IAAA,EAAA,qBAAoB;QACpB,cAAA,IAAA,EAAA,eAAc;QACd,cAAA,IAAA,EAAA,eAAc,KAAK,EAAA;QAEnB,cAAA,IAAA,EAAA,eAAc;QAIb,YAAA;QAAA,cAAA,IAAA,EAAA,mBAAkB;QAElB,cAAA,IAAA,EAAA,UAAS;QACT,cAAA,IAAA,EAAA,UAAS;QAET,cAAA,IAAA,EAAA,eAAc;QACd,cAAA,IAAA,EAAA,gBAAe;QACf,cAAA,IAAA,EAAA,YAAW;QACX,cAAA,IAAA,EAAA,aAAY;QACZ,cAAA,IAAA,EAAA,UAAS;QACT,cAAA,IAAA,EAAA,YAAW;QAEX,cAAA,IAAA,EAAA,aAAY;QACZ,cAAA,IAAA,EAAA,aAAY;QAEZ,cAAA,IAAA,EAAA,OAAM;QACN,cAAA,IAAA,EAAA,OAAM;QAEN,cAAA,IAAA,EAAA,iBAAgB,2MAAI,UAAA;QACpB,cAAA,IAAA,EAAA,aAAY,2MAAI,YAAA;QACf,cAAA,IAAA,EAAA,UAAS,2MAAI,UAAA;QAaf,cAAA,IAAA,EAAA,WAAU,CAAC,eAAkC;YACvC,WAAA,YAAA,CAAa,YAAY,IAAI;YAExC,WAAW,KAAA,CAAM,WAAA,GAAc;YAEpB,WAAA,gBAAA,CAAiB,eAAe,IAAA,CAAK,WAAW;YAChD,WAAA,gBAAA,CAAiB,aAAa,IAAA,CAAK,WAAW;YAC9C,WAAA,gBAAA,CAAiB,aAAa,IAAA,CAAK,WAAW;YAC9C,WAAA,gBAAA,CAAiB,WAAW,IAAA,CAAK,SAAS;YAErD,IAAA,CAAK,UAAA,GAAa;YAEX,OAAA,gBAAA,CAAiB,WAAW,IAAA,CAAK,SAAS;YAC1C,OAAA,gBAAA,CAAiB,SAAS,IAAA,CAAK,OAAO;YAE7C,IAAA,CAAK,YAAA,CAAa;QAAA;QAGb,cAAA,IAAA,EAAA,WAAU,MAAY;;YAC3B,CAAA,KAAA,IAAA,CAAK,UAAA,KAAL,OAAA,KAAA,IAAA,GAAiB,mBAAA,CAAoB,eAAe,IAAA,CAAK,WAAA;YACzD,CAAA,KAAA,IAAA,CAAK,UAAA,KAAL,OAAA,KAAA,IAAA,GAAiB,mBAAA,CAAoB,aAAa,IAAA,CAAK,WAAA;YACvD,CAAA,KAAA,IAAA,CAAK,UAAA,KAAL,OAAA,KAAA,IAAA,GAAiB,mBAAA,CAAoB,aAAa,IAAA,CAAK,WAAA;YACvD,CAAA,KAAA,IAAA,CAAK,UAAA,KAAL,OAAA,KAAA,IAAA,GAAiB,mBAAA,CAAoB,WAAW,IAAA,CAAK,SAAA;YAE9C,OAAA,mBAAA,CAAoB,WAAW,IAAA,CAAK,SAAS;YAC7C,OAAA,mBAAA,CAAoB,SAAS,IAAA,CAAK,OAAO;QAAA;QAG3C,cAAA,IAAA,EAAA,gBAAe,MAAY;YAChC,IAAI,IAAA,CAAK,UAAA,EAAY;gBACd,IAAA,CAAA,SAAA,GAAY,IAAA,CAAK,UAAA,CAAW,WAAA,GAAc;gBAC1C,IAAA,CAAA,SAAA,GAAY,IAAA,CAAK,UAAA,CAAW,YAAA,GAAe;YAClD;QAAA;QAGM,cAAA,IAAA,EAAA,eAAc,CAAC,UAA4B;;YACjD,CAAA,KAAA,IAAA,CAAK,UAAA,KAAL,OAAA,KAAA,IAAA,GAAiB,KAAA;YAEjB,IAAI,IAAA,CAAK,UAAA,EAAY;gBACnB,OAAQ,MAAM,MAAA,EAAQ;oBACpB,KAAK;wBACH,IAAA,CAAK,WAAA,GAAc;wBACnB;oBACF,KAAK;wBACH,IAAA,CAAK,YAAA,GAAe;wBACpB;gBACJ;YACF;YAEA,IAAA,CAAK,WAAA,GAAc;QAAA;QAGb,cAAA,IAAA,EAAA,aAAY,CAAC,UAA4B;YAC/C,IAAI,IAAA,CAAK,UAAA,EAAY;gBACnB,OAAQ,MAAM,MAAA,EAAQ;oBACpB,KAAK;wBACH,IAAA,CAAK,WAAA,GAAc;wBACnB;oBACF,KAAK;wBACH,IAAA,CAAK,YAAA,GAAe;wBACpB;gBACJ;YACF;YAEA,IAAA,CAAK,WAAA,GAAc;QAAA;QAGb,cAAA,IAAA,EAAA,eAAc,CAAC,UAA4B;YACjD,IAAI,IAAA,CAAK,UAAA,EAAY;gBACnB,IAAA,CAAK,MAAA,GAAS,MAAM,KAAA,GAAQ,IAAA,CAAK,UAAA,CAAW,UAAA,GAAa,IAAA,CAAK,SAAA;gBAC9D,IAAA,CAAK,MAAA,GAAS,MAAM,KAAA,GAAQ,IAAA,CAAK,UAAA,CAAW,SAAA,GAAY,IAAA,CAAK,SAAA;YAC/D;QAAA;QAGM,cAAA,IAAA,EAAA,aAAY,CAAC,UAA+B;YAClD,OAAQ,MAAM,IAAA,EAAM;gBAClB,KAAK;gBACL,KAAK;oBACH,IAAA,CAAK,WAAA,GAAc;oBACnB;gBAEF,KAAK;gBACL,KAAK;oBACH,IAAA,CAAK,QAAA,GAAW;oBAChB;gBAEF,KAAK;gBACL,KAAK;oBACH,IAAA,CAAK,YAAA,GAAe;oBACpB;gBAEF,KAAK;gBACL,KAAK;oBACH,IAAA,CAAK,SAAA,GAAY;oBACjB;gBAEF,KAAK;oBACH,IAAA,CAAK,MAAA,GAAS;oBACd;gBACF,KAAK;oBACH,IAAA,CAAK,QAAA,GAAW;oBAChB;YACJ;QAAA;QAGM,cAAA,IAAA,EAAA,WAAU,CAAC,UAA+B;YAChD,OAAQ,MAAM,IAAA,EAAM;gBAClB,KAAK;gBACL,KAAK;oBACH,IAAA,CAAK,WAAA,GAAc;oBACnB;gBAEF,KAAK;gBACL,KAAK;oBACH,IAAA,CAAK,QAAA,GAAW;oBAChB;gBAEF,KAAK;gBACL,KAAK;oBACH,IAAA,CAAK,YAAA,GAAe;oBACpB;gBAEF,KAAK;gBACL,KAAK;oBACH,IAAA,CAAK,SAAA,GAAY;oBACjB;gBAEF,KAAK;oBACH,IAAA,CAAK,MAAA,GAAS;oBACd;gBACF,KAAK;oBACH,IAAA,CAAK,QAAA,GAAW;oBAChB;YACJ;QAAA;QAGK,cAAA,IAAA,EAAA,UAAS,CAAC,GAAqB,GAAY,MAAqB;YACrE,IAAI,aAAa,iNAAA,EAAS;gBACnB,IAAA,CAAA,MAAA,CAAO,IAAA,CAAK,CAAC;YAAA,OAAA,IACT,KAAK,GAAG;gBACjB,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI,GAAG,GAAG,CAAC;YACzB;YAEK,IAAA,CAAA,MAAA,CAAO,MAAA,CAAO,IAAA,CAAK,MAAM;YAE9B,IAAA,CAAK,cAAA,CAAe;YAEb,OAAA,IAAA;QAAA;QAGF,cAAA,IAAA,EAAA,UAAS,CAAC,UAAwB;YACvC,IAAI,CAAC,IAAA,CAAK,OAAA,EAAS;YAEnB,IAAI,IAAA,CAAK,WAAA,EAAa;gBACd,MAAA,2MAAI,YAAA,CAAU,KAAA,CAAM,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,CAAA,EAAG,IAAA,CAAK,SAAA,EAAW,IAAA,CAAK,SAAS;gBAC1E,MAAA,cAAc,IAAI,IAAA,CAAK,SAAA;gBAExB,IAAA,CAAA,eAAA,GAAkB,QAAA,CAAS,cAAc,IAAA,CAAK,UAAA;YAAA,OAC9C;gBACL,IAAA,CAAK,eAAA,GAAkB;YACzB;YAEM,MAAA,kBAAkB,QAAQ,IAAA,CAAK,aAAA;YAErC,IAAI,IAAA,CAAK,WAAA,IAAgB,IAAA,CAAK,WAAA,IAAe,CAAC,IAAA,CAAK,YAAA,EAAe;gBAChE,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,CAAA,CAAE,kBAAkB,IAAA,CAAK,eAAA,CAAgB;YAClE;YACA,IAAI,IAAA,CAAK,YAAA,EAAmB,IAAA,CAAA,MAAA,CAAO,UAAA,CAAW,eAAe;YAE7D,IAAI,IAAA,CAAK,QAAA,EAAe,IAAA,CAAA,MAAA,CAAO,UAAA,CAAW,CAAC,eAAe;YAC1D,IAAI,IAAA,CAAK,SAAA,EAAgB,IAAA,CAAA,MAAA,CAAO,UAAA,CAAW,eAAe;YAE1D,IAAI,IAAA,CAAK,MAAA,EAAa,IAAA,CAAA,MAAA,CAAO,UAAA,CAAW,eAAe;YACvD,IAAI,IAAA,CAAK,QAAA,EAAe,IAAA,CAAA,MAAA,CAAO,UAAA,CAAW,CAAC,eAAe;YAEtD,IAAA,kBAAkB,QAAQ,IAAA,CAAK,SAAA;YAE/B,IAAA,CAAC,IAAA,CAAK,UAAA,EAAY;gBACF,kBAAA;YACpB;YAEA,IAAI,oBAAoB;YAExB,IAAI,IAAA,CAAK,iBAAA,EAAmB;gBAC1B,oBAAoB,KAAK,EAAA,GAAA,CAAM,IAAA,CAAK,WAAA,GAAc,IAAA,CAAK,WAAA;YACzD;YAEK,IAAA,CAAA,GAAA,IAAO,IAAA,CAAK,MAAA,GAAS;YAC1B,IAAI,IAAA,CAAK,YAAA,EAAmB,IAAA,CAAA,GAAA,IAAO,IAAA,CAAK,MAAA,GAAS,kBAAkB;YAE9D,IAAA,CAAA,GAAA,GAAM,KAAK,GAAA,CAAI,CAAA,IAAK,KAAK,GAAA,CAAI,IAAI,IAAA,CAAK,GAAG,CAAC;YAE/C,IAAI,6MAAM,YAAA,CAAU,QAAA,CAAS,KAAK,IAAA,CAAK,GAAG;YAC1C,MAAM,QAAQ,mNAAA,CAAU,QAAA,CAAS,IAAA,CAAK,GAAG;YAEzC,IAAI,IAAA,CAAK,iBAAA,EAAmB;gBACpB,6MAAA,YAAA,CAAU,SAAA,CAAU,KAAK,GAAG,KAAK,EAAA,EAAI,IAAA,CAAK,WAAA,EAAa,IAAA,CAAK,WAAW;YAC/E;YAEM,MAAA,WAAW,IAAA,CAAK,MAAA,CAAO,QAAA;YAE7B,eAAe,sBAAA,CAAuB,GAAG,KAAK,KAAK,EAAE,GAAA,CAAI,QAAQ;YAE5D,IAAA,CAAA,MAAA,CAAO,MAAA,CAAO,cAAc;QAAA;QAG3B,cAAA,IAAA,EAAA,eAAc,CAAC,QAAuB,MAAM,cAAA,CAAe;QAE3D,cAAA,IAAA,EAAA,kBAAiB,MAAY;YAC9B,IAAA,CAAA,aAAA,CAAc,GAAA,CAAI,GAAG,GAAG,CAAA,CAAE,EAAE,eAAA,CAAgB,IAAA,CAAK,MAAA,CAAO,UAAU;YAClE,IAAA,CAAA,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,aAAa;YAChD,IAAA,CAAK,GAAA,GAAM,4MAAK,YAAA,CAAU,QAAA,CAAS,IAAA,CAAK,SAAA,CAAU,GAAG;YACrD,IAAA,CAAK,GAAA,0MAAM,YAAA,CAAU,QAAA,CAAS,IAAA,CAAK,SAAA,CAAU,KAAK;QAAA;QA5NlD,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,UAAA,GAAa;QAElB,IAAA,CAAK,cAAA,CAAe;QAEhB,IAAA,YAAY,IAAA,CAAK,OAAA,CAAQ,UAAU;IACzC;AAwNF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6250, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}