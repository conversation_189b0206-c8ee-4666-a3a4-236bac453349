{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={`rounded-lg border bg-card text-card-foreground shadow-sm ${className || ''}`}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={`flex flex-col space-y-1.5 p-6 ${className || ''}`} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={`text-2xl font-semibold leading-none tracking-tight ${className || ''}`}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p ref={ref} className={`text-sm text-muted-foreground ${className || ''}`} {...props} />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={`p-6 pt-0 ${className || ''}`} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={`flex items-center p-6 pt-0 ${className || ''}`} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAEA,MAAM,qBAAO,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAC,yDAAyD,EAAE,aAAa,IAAI;QACvF,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAC,8BAA8B,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;;AAEzF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAC,mDAAmD,EAAE,aAAa,IAAI;QACjF,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAE,KAAK;QAAK,WAAW,CAAC,8BAA8B,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;;AAEvF,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAC,SAAS,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;;AAEpE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAC,2BAA2B,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;;AAEtF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/knowledge-graph-3d.tsx"], "sourcesContent": ["// 3D Knowledge Graph Visualization with React Three Fiber\n'use client';\n\nimport React, { useRef, useState, useEffect, Suspense } from 'react';\nimport { Canvas, useFrame } from '@react-three/fiber';\nimport { OrbitControls, Text, Line, Html } from '@react-three/drei';\nimport * as THREE from 'three';\nimport { useSpring, animated } from '@react-spring/three';\n\n// Types for the knowledge graph\nexport interface GraphNode {\n  id: string;\n  label: string;\n  type: 'concept' | 'entity' | 'document' | 'agent' | 'user';\n  position: [number, number, number];\n  importance?: number;\n  description?: string;\n  properties?: Record<string, any>;\n  connections?: string[];\n}\n\nexport interface GraphEdge {\n  id: string;\n  source: string;\n  target: string;\n  type: 'contains' | 'related' | 'created' | 'uses';\n  weight?: number;\n  label?: string;\n}\n\nexport interface GraphData {\n  nodes: GraphNode[];\n  edges: GraphEdge[];\n}\n\n// Node component representing entities in the knowledge graph\nfunction Node({ \n  node, \n  onSelect, \n  isSelected, \n  connectionCount \n}: { \n  node: GraphNode; \n  onSelect: () => void; \n  isSelected: boolean; \n  connectionCount: number;\n}) {\n  const meshRef = useRef<THREE.Mesh>(null);\n  const [hovered, setHovered] = useState(false);\n  \n  // Animation for hover and selection effects\n  const { scale, emissive } = useSpring({\n    scale: hovered || isSelected ? 1.2 : 1,\n    emissive: hovered ? '#666' : isSelected ? '#00f' : '#000',\n    config: { tension: 300, friction: 10 }\n  });\n\n  // Subtle floating animation\n  useFrame((state) => {\n    if (meshRef.current && !isSelected) {\n      meshRef.current.position.y += Math.sin(state.clock.elapsedTime * 0.5 + node.position[0]) * 0.001;\n    }\n  });\n\n  const color = getNodeColor(node.type);\n  const size = getNodeSize(node.importance || 1);\n\n  return (\n    <group position={node.position}>\n      <animated.mesh\n        ref={meshRef}\n        scale={scale}\n        onClick={() => onSelect()}\n        onPointerOver={() => setHovered(true)}\n        onPointerOut={() => setHovered(false)}\n      >\n        <sphereGeometry args={[size, 32, 32]} />\n        <animated.meshStandardMaterial color={color} emissive={emissive} />\n      </animated.mesh>\n      \n      <Html\n        position={[0, size + 0.3, 0]}\n        center\n        style={{\n          opacity: hovered || isSelected ? 1 : 0.7,\n          transition: 'opacity 0.2s',\n          background: 'rgba(0,0,0,0.5)',\n          padding: '2px 5px',\n          borderRadius: '3px',\n          color: 'white',\n          fontSize: '10px',\n          pointerEvents: 'none',\n          whiteSpace: 'nowrap'\n        }}\n      >\n        {node.label}\n      </Html>\n      \n      {/* Connection indicator */}\n      {connectionCount > 0 && (\n        <Html\n          position={[size + 0.2, 0, 0]}\n          center\n          style={{\n            opacity: 0.8,\n            background: 'rgba(255,255,255,0.8)',\n            padding: '1px 3px',\n            borderRadius: '50%',\n            color: 'black',\n            fontSize: '8px',\n            pointerEvents: 'none'\n          }}\n        >\n          {connectionCount}\n        </Html>\n      )}\n    </group>\n  );\n}\n\n// Edge component representing relationships between nodes\nfunction Edge({ \n  edge, \n  startPosition, \n  endPosition \n}: { \n  edge: GraphEdge; \n  startPosition: [number, number, number]; \n  endPosition: [number, number, number];\n}) {\n  const points = [\n    new THREE.Vector3(...startPosition),\n    new THREE.Vector3(...endPosition)\n  ];\n  \n  const color = getEdgeColor(edge.type);\n  const thickness = getEdgeThickness(edge.weight || 1);\n  \n  return (\n    <Line\n      points={points}\n      color={color}\n      lineWidth={thickness}\n      opacity={0.6}\n      transparent\n    />\n  );\n}\n\n// Detail panel that appears when a node is selected\nfunction DetailPanel({ \n  node, \n  onClose \n}: { \n  node: GraphNode | null; \n  onClose: () => void;\n}) {\n  if (!node) return null;\n  \n  return (\n    <div\n      style={{\n        position: 'absolute',\n        bottom: '20px',\n        right: '20px',\n        width: '300px',\n        padding: '15px',\n        backgroundColor: 'rgba(0,0,0,0.9)',\n        color: 'white',\n        borderRadius: '8px',\n        zIndex: 100,\n        border: '1px solid rgba(255,255,255,0.2)',\n        backdropFilter: 'blur(10px)'\n      }}\n    >\n      <h3 style={{ margin: '0 0 10px 0', color: getNodeColor(node.type) }}>\n        {node.label}\n      </h3>\n      <p><strong>Type:</strong> {node.type}</p>\n      <p><strong>Connections:</strong> {node.connections?.length || 0}</p>\n      {node.description && <p><strong>Description:</strong> {node.description}</p>}\n      \n      {node.properties && (\n        <div style={{ marginTop: '10px' }}>\n          <strong>Properties:</strong>\n          <div style={{ marginLeft: '10px', fontSize: '12px' }}>\n            {Object.entries(node.properties).map(([key, value]) => (\n              <div key={key} style={{ margin: '2px 0' }}>\n                <strong>{key}:</strong> {String(value)}\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n      \n      <button\n        onClick={onClose}\n        style={{\n          background: '#3498db',\n          border: 'none',\n          padding: '8px 15px',\n          marginTop: '15px',\n          color: 'white',\n          cursor: 'pointer',\n          borderRadius: '4px',\n          fontSize: '12px'\n        }}\n      >\n        Close\n      </button>\n    </div>\n  );\n}\n\n// Controls panel\nfunction ControlsPanel({ \n  nodes, \n  edges, \n  onResetView, \n  onLayoutChange,\n  currentLayout \n}: {\n  nodes: GraphNode[];\n  edges: GraphEdge[];\n  onResetView: () => void;\n  onLayoutChange: (layout: string) => void;\n  currentLayout: string;\n}) {\n  return (\n    <div\n      style={{\n        position: 'absolute',\n        top: '20px',\n        left: '20px',\n        padding: '15px',\n        backgroundColor: 'rgba(0,0,0,0.8)',\n        color: 'white',\n        borderRadius: '8px',\n        border: '1px solid rgba(255,255,255,0.2)',\n        backdropFilter: 'blur(10px)',\n        minWidth: '200px'\n      }}\n    >\n      <h3 style={{ margin: '0 0 15px 0' }}>3D Knowledge Graph</h3>\n      \n      <div style={{ marginBottom: '10px' }}>\n        <div>Nodes: {nodes.length}</div>\n        <div>Connections: {edges.length}</div>\n      </div>\n      \n      <div style={{ marginBottom: '15px' }}>\n        <label style={{ display: 'block', marginBottom: '5px', fontSize: '12px' }}>\n          Layout:\n        </label>\n        <select\n          value={currentLayout}\n          onChange={(e) => onLayoutChange(e.target.value)}\n          style={{\n            background: 'rgba(255,255,255,0.1)',\n            border: '1px solid rgba(255,255,255,0.3)',\n            color: 'white',\n            padding: '5px',\n            borderRadius: '4px',\n            width: '100%'\n          }}\n        >\n          <option value=\"sphere\">Sphere</option>\n          <option value=\"cube\">Cube</option>\n          <option value=\"random\">Random</option>\n          <option value=\"force\">Force-Directed</option>\n        </select>\n      </div>\n      \n      <button\n        onClick={onResetView}\n        style={{\n          background: '#3498db',\n          border: 'none',\n          padding: '8px 15px',\n          color: 'white',\n          cursor: 'pointer',\n          borderRadius: '4px',\n          width: '100%',\n          fontSize: '12px'\n        }}\n      >\n        Reset View\n      </button>\n    </div>\n  );\n}\n\n// Legend component\nfunction Legend() {\n  const nodeTypes = [\n    { type: 'concept', label: 'Concept', color: '#e74c3c' },\n    { type: 'entity', label: 'Entity', color: '#3498db' },\n    { type: 'document', label: 'Document', color: '#2ecc71' },\n    { type: 'agent', label: 'Agent', color: '#9b59b6' },\n    { type: 'user', label: 'User', color: '#f39c12' }\n  ];\n\n  const edgeTypes = [\n    { type: 'contains', label: 'Contains', color: '#3498db' },\n    { type: 'related', label: 'Related', color: '#95a5a6' },\n    { type: 'created', label: 'Created', color: '#2ecc71' },\n    { type: 'uses', label: 'Uses', color: '#e74c3c' }\n  ];\n\n  return (\n    <div\n      style={{\n        position: 'absolute',\n        top: '20px',\n        right: '20px',\n        padding: '15px',\n        backgroundColor: 'rgba(0,0,0,0.8)',\n        color: 'white',\n        borderRadius: '8px',\n        border: '1px solid rgba(255,255,255,0.2)',\n        backdropFilter: 'blur(10px)',\n        fontSize: '12px'\n      }}\n    >\n      <h4 style={{ margin: '0 0 10px 0' }}>Legend</h4>\n      \n      <div style={{ marginBottom: '15px' }}>\n        <strong>Node Types:</strong>\n        {nodeTypes.map(({ type, label, color }) => (\n          <div key={type} style={{ display: 'flex', alignItems: 'center', margin: '5px 0' }}>\n            <div\n              style={{\n                width: '12px',\n                height: '12px',\n                borderRadius: '50%',\n                backgroundColor: color,\n                marginRight: '8px'\n              }}\n            />\n            {label}\n          </div>\n        ))}\n      </div>\n      \n      <div>\n        <strong>Edge Types:</strong>\n        {edgeTypes.map(({ type, label, color }) => (\n          <div key={type} style={{ display: 'flex', alignItems: 'center', margin: '5px 0' }}>\n            <div\n              style={{\n                width: '20px',\n                height: '2px',\n                backgroundColor: color,\n                marginRight: '8px'\n              }}\n            />\n            {label}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n}\n\n// Helper functions for visual properties\nconst getNodeColor = (type: string) => {\n  const colorMap: Record<string, string> = {\n    'concept': '#e74c3c',\n    'entity': '#3498db',\n    'document': '#2ecc71',\n    'agent': '#9b59b6',\n    'user': '#f39c12'\n  };\n  return colorMap[type] || '#95a5a6';\n};\n\nconst getNodeSize = (importance: number) => {\n  return 0.3 + (importance * 0.2);\n};\n\nconst getEdgeColor = (type: string) => {\n  const colorMap: Record<string, string> = {\n    'contains': '#3498db',\n    'related': '#95a5a6',\n    'created': '#2ecc71',\n    'uses': '#e74c3c'\n  };\n  return colorMap[type] || '#bdc3c7';\n};\n\nconst getEdgeThickness = (weight: number) => {\n  return 1 + (weight * 2);\n};\n\n// Layout algorithms\nconst generateLayout = (nodes: GraphNode[], layout: string): GraphNode[] => {\n  const radius = 8;\n  \n  return nodes.map((node, index) => {\n    let position: [number, number, number];\n    \n    switch (layout) {\n      case 'sphere':\n        const phi = Math.acos(-1 + (2 * index) / nodes.length);\n        const theta = Math.sqrt(nodes.length * Math.PI) * phi;\n        position = [\n          radius * Math.cos(theta) * Math.sin(phi),\n          radius * Math.sin(theta) * Math.sin(phi),\n          radius * Math.cos(phi)\n        ];\n        break;\n        \n      case 'cube':\n        const size = Math.ceil(Math.cbrt(nodes.length));\n        const x = (index % size) - size / 2;\n        const y = Math.floor(index / size) % size - size / 2;\n        const z = Math.floor(index / (size * size)) - size / 2;\n        position = [x * 2, y * 2, z * 2];\n        break;\n        \n      case 'random':\n        position = [\n          (Math.random() - 0.5) * radius * 2,\n          (Math.random() - 0.5) * radius * 2,\n          (Math.random() - 0.5) * radius * 2\n        ];\n        break;\n        \n      case 'force':\n      default:\n        // Simple force-directed layout\n        const angle = (index / nodes.length) * Math.PI * 2;\n        const layer = Math.floor(index / 8);\n        const layerRadius = radius * (0.5 + layer * 0.3);\n        position = [\n          layerRadius * Math.cos(angle),\n          (Math.random() - 0.5) * 4,\n          layerRadius * Math.sin(angle)\n        ];\n        break;\n    }\n    \n    return { ...node, position };\n  });\n};\n\n// Main knowledge graph component\nexport default function KnowledgeGraph3D({ data }: { data: GraphData }) {\n  const [nodes, setNodes] = useState<GraphNode[]>([]);\n  const [edges, setEdges] = useState<GraphEdge[]>([]);\n  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);\n  const [cameraTarget, setCameraTarget] = useState<[number, number, number]>([0, 0, 0]);\n  const [layout, setLayout] = useState('sphere');\n  \n  // Process graph data when it changes\n  useEffect(() => {\n    if (!data) return;\n    \n    // Apply layout to nodes\n    const layoutNodes = generateLayout(data.nodes, layout);\n    setNodes(layoutNodes);\n    setEdges(data.edges);\n  }, [data, layout]);\n  \n  // Handle node selection\n  const handleNodeSelect = (node: GraphNode) => {\n    setSelectedNode(node);\n    setCameraTarget(node.position);\n  };\n  \n  // Handle layout change\n  const handleLayoutChange = (newLayout: string) => {\n    setLayout(newLayout);\n  };\n  \n  // Reset view\n  const handleResetView = () => {\n    setSelectedNode(null);\n    setCameraTarget([0, 0, 0]);\n  };\n  \n  // Calculate connection count for each node\n  const getConnectionCount = (nodeId: string) => {\n    return edges.filter(edge => edge.source === nodeId || edge.target === nodeId).length;\n  };\n  \n  return (\n    <div style={{ width: '100%', height: '100%', position: 'relative' }}>\n      <Canvas\n        camera={{ position: [0, 0, 15], fov: 60 }}\n        style={{ background: 'linear-gradient(to bottom, #1a1a2e, #16213e)' }}\n      >\n        <Suspense fallback={null}>\n          <ambientLight intensity={0.5} />\n          <pointLight position={[10, 10, 10]} intensity={1} />\n          <pointLight position={[-10, -10, -10]} intensity={0.5} />\n          \n          {/* Render edges */}\n          {edges.map((edge) => {\n            const startNode = nodes.find(node => node.id === edge.source);\n            const endNode = nodes.find(node => node.id === edge.target);\n            \n            if (!startNode || !endNode) return null;\n            \n            return (\n              <Edge\n                key={edge.id}\n                edge={edge}\n                startPosition={startNode.position}\n                endPosition={endNode.position}\n              />\n            );\n          })}\n          \n          {/* Render nodes */}\n          {nodes.map((node) => (\n            <Node\n              key={node.id}\n              node={node}\n              onSelect={() => handleNodeSelect(node)}\n              isSelected={selectedNode?.id === node.id}\n              connectionCount={getConnectionCount(node.id)}\n            />\n          ))}\n          \n          {/* Camera controls */}\n          <OrbitControls \n            enableDamping \n            dampingFactor={0.1} \n            rotateSpeed={0.5}\n            target={new THREE.Vector3(...cameraTarget)}\n            maxDistance={50}\n            minDistance={5}\n          />\n          \n          {/* Fog for depth effect */}\n          <fog attach=\"fog\" args={['#16213e', 10, 50]} />\n        </Suspense>\n      </Canvas>\n      \n      {/* UI Overlays */}\n      <ControlsPanel\n        nodes={nodes}\n        edges={edges}\n        onResetView={handleResetView}\n        onLayoutChange={handleLayoutChange}\n        currentLayout={layout}\n      />\n      \n      <Legend />\n      \n      <DetailPanel \n        node={selectedNode} \n        onClose={() => setSelectedNode(null)} \n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;AAG1D;;;;;;;;;;;;;;;;;;;;;;;AAFA;;;;;;AAkCA,8DAA8D;AAC9D,SAAS,KAAK,EACZ,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,eAAe,EAMhB;;IACC,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,SAAM,AAAD,EAAc;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,4CAA4C;IAC5C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,UAAU;QACpC,OAAO,WAAW,aAAa,MAAM;QACrC,UAAU,UAAU,SAAS,aAAa,SAAS;QACnD,QAAQ;YAAE,SAAS;YAAK,UAAU;QAAG;IACvC;IAEA,4BAA4B;IAC5B;yBAAS,CAAC;YACR,IAAI,QAAQ,OAAO,IAAI,CAAC,YAAY;gBAClC,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,MAAM,KAAK,QAAQ,CAAC,EAAE,IAAI;YAC7F;QACF;;IAEA,MAAM,QAAQ,aAAa,KAAK,IAAI;IACpC,MAAM,OAAO,YAAY,KAAK,UAAU,IAAI;IAE5C,qBACE,4TAAC;QAAM,UAAU,KAAK,QAAQ;;0BAC5B,4TAAC,SAAS,IAAI;gBACZ,KAAK;gBACL,OAAO;gBACP,SAAS,IAAM;gBACf,eAAe,IAAM,WAAW;gBAChC,cAAc,IAAM,WAAW;;kCAE/B,4TAAC;wBAAe,MAAM;4BAAC;4BAAM;4BAAI;yBAAG;;;;;;kCACpC,4TAAC,SAAS,oBAAoB;wBAAC,OAAO;wBAAO,UAAU;;;;;;;;;;;;0BAGzD,4TAAC;gBACC,UAAU;oBAAC;oBAAG,OAAO;oBAAK;iBAAE;gBAC5B,MAAM;gBACN,OAAO;oBACL,SAAS,WAAW,aAAa,IAAI;oBACrC,YAAY;oBACZ,YAAY;oBACZ,SAAS;oBACT,cAAc;oBACd,OAAO;oBACP,UAAU;oBACV,eAAe;oBACf,YAAY;gBACd;0BAEC,KAAK,KAAK;;;;;;YAIZ,kBAAkB,mBACjB,4TAAC;gBACC,UAAU;oBAAC,OAAO;oBAAK;oBAAG;iBAAE;gBAC5B,MAAM;gBACN,OAAO;oBACL,SAAS;oBACT,YAAY;oBACZ,SAAS;oBACT,cAAc;oBACd,OAAO;oBACP,UAAU;oBACV,eAAe;gBACjB;0BAEC;;;;;;;;;;;;AAKX;GAlFS;;QAeqB;QAO5B;;;KAtBO;AAoFT,0DAA0D;AAC1D,SAAS,KAAK,EACZ,IAAI,EACJ,aAAa,EACb,WAAW,EAKZ;IACC,MAAM,SAAS;QACb,IAAI,MAAM,OAAO,IAAI;QACrB,IAAI,MAAM,OAAO,IAAI;KACtB;IAED,MAAM,QAAQ,aAAa,KAAK,IAAI;IACpC,MAAM,YAAY,iBAAiB,KAAK,MAAM,IAAI;IAElD,qBACE,4TAAC;QACC,QAAQ;QACR,OAAO;QACP,WAAW;QACX,SAAS;QACT,WAAW;;;;;;AAGjB;MA1BS;AA4BT,oDAAoD;AACpD,SAAS,YAAY,EACnB,IAAI,EACJ,OAAO,EAIR;IACC,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,4TAAC;QACC,OAAO;YACL,UAAU;YACV,QAAQ;YACR,OAAO;YACP,OAAO;YACP,SAAS;YACT,iBAAiB;YACjB,OAAO;YACP,cAAc;YACd,QAAQ;YACR,QAAQ;YACR,gBAAgB;QAClB;;0BAEA,4TAAC;gBAAG,OAAO;oBAAE,QAAQ;oBAAc,OAAO,aAAa,KAAK,IAAI;gBAAE;0BAC/D,KAAK,KAAK;;;;;;0BAEb,4TAAC;;kCAAE,4TAAC;kCAAO;;;;;;oBAAc;oBAAE,KAAK,IAAI;;;;;;;0BACpC,4TAAC;;kCAAE,4TAAC;kCAAO;;;;;;oBAAqB;oBAAE,KAAK,WAAW,EAAE,UAAU;;;;;;;YAC7D,KAAK,WAAW,kBAAI,4TAAC;;kCAAE,4TAAC;kCAAO;;;;;;oBAAqB;oBAAE,KAAK,WAAW;;;;;;;YAEtE,KAAK,UAAU,kBACd,4TAAC;gBAAI,OAAO;oBAAE,WAAW;gBAAO;;kCAC9B,4TAAC;kCAAO;;;;;;kCACR,4TAAC;wBAAI,OAAO;4BAAE,YAAY;4BAAQ,UAAU;wBAAO;kCAChD,OAAO,OAAO,CAAC,KAAK,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAChD,4TAAC;gCAAc,OAAO;oCAAE,QAAQ;gCAAQ;;kDACtC,4TAAC;;4CAAQ;4CAAI;;;;;;;oCAAU;oCAAE,OAAO;;+BADxB;;;;;;;;;;;;;;;;0BAQlB,4TAAC;gBACC,SAAS;gBACT,OAAO;oBACL,YAAY;oBACZ,QAAQ;oBACR,SAAS;oBACT,WAAW;oBACX,OAAO;oBACP,QAAQ;oBACR,cAAc;oBACd,UAAU;gBACZ;0BACD;;;;;;;;;;;;AAKP;MA9DS;AAgET,iBAAiB;AACjB,SAAS,cAAc,EACrB,KAAK,EACL,KAAK,EACL,WAAW,EACX,cAAc,EACd,aAAa,EAOd;IACC,qBACE,4TAAC;QACC,OAAO;YACL,UAAU;YACV,KAAK;YACL,MAAM;YACN,SAAS;YACT,iBAAiB;YACjB,OAAO;YACP,cAAc;YACd,QAAQ;YACR,gBAAgB;YAChB,UAAU;QACZ;;0BAEA,4TAAC;gBAAG,OAAO;oBAAE,QAAQ;gBAAa;0BAAG;;;;;;0BAErC,4TAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;;kCACjC,4TAAC;;4BAAI;4BAAQ,MAAM,MAAM;;;;;;;kCACzB,4TAAC;;4BAAI;4BAAc,MAAM,MAAM;;;;;;;;;;;;;0BAGjC,4TAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;;kCACjC,4TAAC;wBAAM,OAAO;4BAAE,SAAS;4BAAS,cAAc;4BAAO,UAAU;wBAAO;kCAAG;;;;;;kCAG3E,4TAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wBAC9C,OAAO;4BACL,YAAY;4BACZ,QAAQ;4BACR,OAAO;4BACP,SAAS;4BACT,cAAc;4BACd,OAAO;wBACT;;0CAEA,4TAAC;gCAAO,OAAM;0CAAS;;;;;;0CACvB,4TAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,4TAAC;gCAAO,OAAM;0CAAS;;;;;;0CACvB,4TAAC;gCAAO,OAAM;0CAAQ;;;;;;;;;;;;;;;;;;0BAI1B,4TAAC;gBACC,SAAS;gBACT,OAAO;oBACL,YAAY;oBACZ,QAAQ;oBACR,SAAS;oBACT,OAAO;oBACP,QAAQ;oBACR,cAAc;oBACd,OAAO;oBACP,UAAU;gBACZ;0BACD;;;;;;;;;;;;AAKP;MA3ES;AA6ET,mBAAmB;AACnB,SAAS;IACP,MAAM,YAAY;QAChB;YAAE,MAAM;YAAW,OAAO;YAAW,OAAO;QAAU;QACtD;YAAE,MAAM;YAAU,OAAO;YAAU,OAAO;QAAU;QACpD;YAAE,MAAM;YAAY,OAAO;YAAY,OAAO;QAAU;QACxD;YAAE,MAAM;YAAS,OAAO;YAAS,OAAO;QAAU;QAClD;YAAE,MAAM;YAAQ,OAAO;YAAQ,OAAO;QAAU;KACjD;IAED,MAAM,YAAY;QAChB;YAAE,MAAM;YAAY,OAAO;YAAY,OAAO;QAAU;QACxD;YAAE,MAAM;YAAW,OAAO;YAAW,OAAO;QAAU;QACtD;YAAE,MAAM;YAAW,OAAO;YAAW,OAAO;QAAU;QACtD;YAAE,MAAM;YAAQ,OAAO;YAAQ,OAAO;QAAU;KACjD;IAED,qBACE,4TAAC;QACC,OAAO;YACL,UAAU;YACV,KAAK;YACL,OAAO;YACP,SAAS;YACT,iBAAiB;YACjB,OAAO;YACP,cAAc;YACd,QAAQ;YACR,gBAAgB;YAChB,UAAU;QACZ;;0BAEA,4TAAC;gBAAG,OAAO;oBAAE,QAAQ;gBAAa;0BAAG;;;;;;0BAErC,4TAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAO;;kCACjC,4TAAC;kCAAO;;;;;;oBACP,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,iBACpC,4TAAC;4BAAe,OAAO;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,QAAQ;4BAAQ;;8CAC9E,4TAAC;oCACC,OAAO;wCACL,OAAO;wCACP,QAAQ;wCACR,cAAc;wCACd,iBAAiB;wCACjB,aAAa;oCACf;;;;;;gCAED;;2BAVO;;;;;;;;;;;0BAed,4TAAC;;kCACC,4TAAC;kCAAO;;;;;;oBACP,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,iBACpC,4TAAC;4BAAe,OAAO;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,QAAQ;4BAAQ;;8CAC9E,4TAAC;oCACC,OAAO;wCACL,OAAO;wCACP,QAAQ;wCACR,iBAAiB;wCACjB,aAAa;oCACf;;;;;;gCAED;;2BATO;;;;;;;;;;;;;;;;;AAepB;MArES;AAuET,yCAAyC;AACzC,MAAM,eAAe,CAAC;IACpB,MAAM,WAAmC;QACvC,WAAW;QACX,UAAU;QACV,YAAY;QACZ,SAAS;QACT,QAAQ;IACV;IACA,OAAO,QAAQ,CAAC,KAAK,IAAI;AAC3B;AAEA,MAAM,cAAc,CAAC;IACnB,OAAO,MAAO,aAAa;AAC7B;AAEA,MAAM,eAAe,CAAC;IACpB,MAAM,WAAmC;QACvC,YAAY;QACZ,WAAW;QACX,WAAW;QACX,QAAQ;IACV;IACA,OAAO,QAAQ,CAAC,KAAK,IAAI;AAC3B;AAEA,MAAM,mBAAmB,CAAC;IACxB,OAAO,IAAK,SAAS;AACvB;AAEA,oBAAoB;AACpB,MAAM,iBAAiB,CAAC,OAAoB;IAC1C,MAAM,SAAS;IAEf,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM;QACtB,IAAI;QAEJ,OAAQ;YACN,KAAK;gBACH,MAAM,MAAM,KAAK,IAAI,CAAC,CAAC,IAAI,AAAC,IAAI,QAAS,MAAM,MAAM;gBACrD,MAAM,QAAQ,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG,KAAK,EAAE,IAAI;gBAClD,WAAW;oBACT,SAAS,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;oBACpC,SAAS,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;oBACpC,SAAS,KAAK,GAAG,CAAC;iBACnB;gBACD;YAEF,KAAK;gBACH,MAAM,OAAO,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,MAAM;gBAC7C,MAAM,IAAI,AAAC,QAAQ,OAAQ,OAAO;gBAClC,MAAM,IAAI,KAAK,KAAK,CAAC,QAAQ,QAAQ,OAAO,OAAO;gBACnD,MAAM,IAAI,KAAK,KAAK,CAAC,QAAQ,CAAC,OAAO,IAAI,KAAK,OAAO;gBACrD,WAAW;oBAAC,IAAI;oBAAG,IAAI;oBAAG,IAAI;iBAAE;gBAChC;YAEF,KAAK;gBACH,WAAW;oBACT,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,SAAS;oBACjC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,SAAS;oBACjC,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,SAAS;iBAClC;gBACD;YAEF,KAAK;YACL;gBACE,+BAA+B;gBAC/B,MAAM,QAAQ,AAAC,QAAQ,MAAM,MAAM,GAAI,KAAK,EAAE,GAAG;gBACjD,MAAM,QAAQ,KAAK,KAAK,CAAC,QAAQ;gBACjC,MAAM,cAAc,SAAS,CAAC,MAAM,QAAQ,GAAG;gBAC/C,WAAW;oBACT,cAAc,KAAK,GAAG,CAAC;oBACvB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBACxB,cAAc,KAAK,GAAG,CAAC;iBACxB;gBACD;QACJ;QAEA,OAAO;YAAE,GAAG,IAAI;YAAE;QAAS;IAC7B;AACF;AAGe,SAAS,iBAAiB,EAAE,IAAI,EAAuB;;IACpE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAClD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAoB;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAA4B;QAAC;QAAG;QAAG;KAAE;IACpF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qCAAqC;IACrC,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,MAAM;YAEX,wBAAwB;YACxB,MAAM,cAAc,eAAe,KAAK,KAAK,EAAE;YAC/C,SAAS;YACT,SAAS,KAAK,KAAK;QACrB;qCAAG;QAAC;QAAM;KAAO;IAEjB,wBAAwB;IACxB,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,gBAAgB,KAAK,QAAQ;IAC/B;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,CAAC;QAC1B,UAAU;IACZ;IAEA,aAAa;IACb,MAAM,kBAAkB;QACtB,gBAAgB;QAChB,gBAAgB;YAAC;YAAG;YAAG;SAAE;IAC3B;IAEA,2CAA2C;IAC3C,MAAM,qBAAqB,CAAC;QAC1B,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,KAAK,QAAQ,MAAM;IACtF;IAEA,qBACE,4TAAC;QAAI,OAAO;YAAE,OAAO;YAAQ,QAAQ;YAAQ,UAAU;QAAW;;0BAChE,4TAAC;gBACC,QAAQ;oBAAE,UAAU;wBAAC;wBAAG;wBAAG;qBAAG;oBAAE,KAAK;gBAAG;gBACxC,OAAO;oBAAE,YAAY;gBAA+C;0BAEpE,cAAA,4TAAC,4RAAA,CAAA,WAAQ;oBAAC,UAAU;;sCAClB,4TAAC;4BAAa,WAAW;;;;;;sCACzB,4TAAC;4BAAW,UAAU;gCAAC;gCAAI;gCAAI;6BAAG;4BAAE,WAAW;;;;;;sCAC/C,4TAAC;4BAAW,UAAU;gCAAC,CAAC;gCAAI,CAAC;gCAAI,CAAC;6BAAG;4BAAE,WAAW;;;;;;wBAGjD,MAAM,GAAG,CAAC,CAAC;4BACV,MAAM,YAAY,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KAAK,MAAM;4BAC5D,MAAM,UAAU,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,KAAK,MAAM;4BAE1D,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO;4BAEnC,qBACE,4TAAC;gCAEC,MAAM;gCACN,eAAe,UAAU,QAAQ;gCACjC,aAAa,QAAQ,QAAQ;+BAHxB,KAAK,EAAE;;;;;wBAMlB;wBAGC,MAAM,GAAG,CAAC,CAAC,qBACV,4TAAC;gCAEC,MAAM;gCACN,UAAU,IAAM,iBAAiB;gCACjC,YAAY,cAAc,OAAO,KAAK,EAAE;gCACxC,iBAAiB,mBAAmB,KAAK,EAAE;+BAJtC,KAAK,EAAE;;;;;sCAShB,4TAAC;4BACC,aAAa;4BACb,eAAe;4BACf,aAAa;4BACb,QAAQ,IAAI,MAAM,OAAO,IAAI;4BAC7B,aAAa;4BACb,aAAa;;;;;;sCAIf,4TAAC;4BAAI,QAAO;4BAAM,MAAM;gCAAC;gCAAW;gCAAI;6BAAG;;;;;;;;;;;;;;;;;0BAK/C,4TAAC;gBACC,OAAO;gBACP,OAAO;gBACP,aAAa;gBACb,gBAAgB;gBAChB,eAAe;;;;;;0BAGjB,4TAAC;;;;;0BAED,4TAAC;gBACC,MAAM;gBACN,SAAS,IAAM,gBAAgB;;;;;;;;;;;;AAIvC;IA9GwB;MAAA", "debugId": null}}, {"offset": {"line": 1043, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1049, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/lib/knowledge-graph-data.ts"], "sourcesContent": ["// Sample data generator for 3D Knowledge Graph\n\nimport type { GraphData, GraphNode, GraphEdge } from '@/components/knowledge-graph-3d';\n\n// Generate sample knowledge graph data\nexport function generateKnowledgeGraphData(): GraphData {\n  const nodes: GraphNode[] = [\n    // Core concepts\n    {\n      id: 'ai-concept',\n      label: 'Artificial Intelligence',\n      type: 'concept',\n      position: [0, 0, 0],\n      importance: 3,\n      description: 'The simulation of human intelligence in machines',\n      properties: {\n        field: 'Computer Science',\n        established: '1956',\n        applications: 'Machine Learning, NLP, Computer Vision'\n      }\n    },\n    {\n      id: 'ml-concept',\n      label: 'Machine Learning',\n      type: 'concept',\n      position: [2, 1, 0],\n      importance: 2.5,\n      description: 'A subset of AI that enables machines to learn from data',\n      properties: {\n        types: 'Supervised, Unsupervised, Reinforcement',\n        algorithms: 'Neural Networks, Decision Trees, SVM'\n      }\n    },\n    {\n      id: 'nlp-concept',\n      label: 'Natural Language Processing',\n      type: 'concept',\n      position: [-2, 1, 0],\n      importance: 2,\n      description: 'AI field focused on interaction between computers and human language',\n      properties: {\n        tasks: 'Translation, Sentiment Analysis, Text Generation',\n        models: 'Transformers, BERT, GPT'\n      }\n    },\n    \n    // Entities (specific implementations)\n    {\n      id: 'gpt-entity',\n      label: 'GPT-4',\n      type: 'entity',\n      position: [-3, 2, 1],\n      importance: 2.5,\n      description: 'Large language model developed by OpenAI',\n      properties: {\n        developer: 'OpenAI',\n        parameters: '~1.76 trillion',\n        release: '2023'\n      }\n    },\n    {\n      id: 'bert-entity',\n      label: 'BERT',\n      type: 'entity',\n      position: [-1, 2, 1],\n      importance: 2,\n      description: 'Bidirectional Encoder Representations from Transformers',\n      properties: {\n        developer: 'Google',\n        architecture: 'Transformer Encoder',\n        use_case: 'Understanding'\n      }\n    },\n    {\n      id: 'tensorflow-entity',\n      label: 'TensorFlow',\n      type: 'entity',\n      position: [3, 2, 1],\n      importance: 2,\n      description: 'Open-source machine learning framework',\n      properties: {\n        developer: 'Google',\n        language: 'Python, C++',\n        type: 'Framework'\n      }\n    },\n    \n    // Documents\n    {\n      id: 'attention-paper',\n      label: 'Attention Is All You Need',\n      type: 'document',\n      position: [0, 3, 2],\n      importance: 3,\n      description: 'Seminal paper introducing the Transformer architecture',\n      properties: {\n        authors: 'Vaswani et al.',\n        year: '2017',\n        venue: 'NIPS',\n        citations: '50000+'\n      }\n    },\n    {\n      id: 'bert-paper',\n      label: 'BERT Paper',\n      type: 'document',\n      position: [-2, 3, 2],\n      importance: 2.5,\n      description: 'Paper introducing BERT model',\n      properties: {\n        authors: 'Devlin et al.',\n        year: '2018',\n        venue: 'NAACL'\n      }\n    },\n    {\n      id: 'gpt-paper',\n      label: 'GPT Paper',\n      type: 'document',\n      position: [-4, 3, 2],\n      importance: 2,\n      description: 'Original GPT paper by OpenAI',\n      properties: {\n        authors: 'Radford et al.',\n        year: '2018',\n        focus: 'Unsupervised Language Understanding'\n      }\n    },\n    \n    // Agents\n    {\n      id: 'chatbot-agent',\n      label: 'ChatBot Agent',\n      type: 'agent',\n      position: [1, -1, 1],\n      importance: 1.5,\n      description: 'AI agent for conversational interactions',\n      properties: {\n        capabilities: 'Chat, Q&A, Task Assistance',\n        model: 'GPT-4',\n        status: 'Active'\n      }\n    },\n    {\n      id: 'research-agent',\n      label: 'Research Agent',\n      type: 'agent',\n      position: [3, -1, 1],\n      importance: 1.5,\n      description: 'AI agent specialized in research tasks',\n      properties: {\n        capabilities: 'Literature Review, Data Analysis',\n        model: 'Custom',\n        status: 'Active'\n      }\n    },\n    {\n      id: 'code-agent',\n      label: 'Code Agent',\n      type: 'agent',\n      position: [-1, -1, 1],\n      importance: 1.5,\n      description: 'AI agent for code generation and review',\n      properties: {\n        capabilities: 'Code Generation, Debugging, Review',\n        model: 'CodeT5',\n        status: 'Active'\n      }\n    },\n    \n    // Users\n    {\n      id: 'researcher-user',\n      label: 'AI Researcher',\n      type: 'user',\n      position: [0, -2, 0],\n      importance: 1,\n      description: 'Research scientist working on AI',\n      properties: {\n        role: 'Senior Researcher',\n        institution: 'Tech University',\n        focus: 'Deep Learning'\n      }\n    },\n    {\n      id: 'developer-user',\n      label: 'ML Engineer',\n      type: 'user',\n      position: [2, -2, 0],\n      importance: 1,\n      description: 'Software engineer specializing in ML',\n      properties: {\n        role: 'ML Engineer',\n        company: 'AI Startup',\n        experience: '5 years'\n      }\n    },\n    {\n      id: 'student-user',\n      label: 'CS Student',\n      type: 'user',\n      position: [-2, -2, 0],\n      importance: 0.8,\n      description: 'Computer science student learning AI',\n      properties: {\n        level: 'Graduate',\n        university: 'State University',\n        focus: 'Machine Learning'\n      }\n    }\n  ];\n\n  const edges: GraphEdge[] = [\n    // Concept relationships\n    { id: 'ai-ml', source: 'ai-concept', target: 'ml-concept', type: 'contains', weight: 3 },\n    { id: 'ai-nlp', source: 'ai-concept', target: 'nlp-concept', type: 'contains', weight: 2.5 },\n    { id: 'ml-nlp', source: 'ml-concept', target: 'nlp-concept', type: 'related', weight: 2 },\n    \n    // Entity to concept relationships\n    { id: 'gpt-nlp', source: 'gpt-entity', target: 'nlp-concept', type: 'uses', weight: 3 },\n    { id: 'bert-nlp', source: 'bert-entity', target: 'nlp-concept', type: 'uses', weight: 2.5 },\n    { id: 'tensorflow-ml', source: 'tensorflow-entity', target: 'ml-concept', type: 'uses', weight: 2.5 },\n    \n    // Document relationships\n    { id: 'attention-nlp', source: 'attention-paper', target: 'nlp-concept', type: 'related', weight: 3 },\n    { id: 'attention-ml', source: 'attention-paper', target: 'ml-concept', type: 'related', weight: 2.5 },\n    { id: 'bert-paper-bert', source: 'bert-paper', target: 'bert-entity', type: 'created', weight: 3 },\n    { id: 'gpt-paper-gpt', source: 'gpt-paper', target: 'gpt-entity', type: 'created', weight: 3 },\n    { id: 'attention-bert', source: 'attention-paper', target: 'bert-entity', type: 'related', weight: 2 },\n    { id: 'attention-gpt', source: 'attention-paper', target: 'gpt-entity', type: 'related', weight: 2 },\n    \n    // Agent relationships\n    { id: 'chatbot-gpt', source: 'chatbot-agent', target: 'gpt-entity', type: 'uses', weight: 2.5 },\n    { id: 'chatbot-nlp', source: 'chatbot-agent', target: 'nlp-concept', type: 'uses', weight: 2 },\n    { id: 'research-ml', source: 'research-agent', target: 'ml-concept', type: 'uses', weight: 2 },\n    { id: 'code-tensorflow', source: 'code-agent', target: 'tensorflow-entity', type: 'uses', weight: 2 },\n    \n    // User relationships\n    { id: 'researcher-ai', source: 'researcher-user', target: 'ai-concept', type: 'related', weight: 2.5 },\n    { id: 'researcher-papers', source: 'researcher-user', target: 'attention-paper', type: 'related', weight: 2 },\n    { id: 'researcher-bert-paper', source: 'researcher-user', target: 'bert-paper', type: 'related', weight: 1.5 },\n    { id: 'developer-tensorflow', source: 'developer-user', target: 'tensorflow-entity', type: 'uses', weight: 2.5 },\n    { id: 'developer-ml', source: 'developer-user', target: 'ml-concept', type: 'uses', weight: 2 },\n    { id: 'student-ai', source: 'student-user', target: 'ai-concept', type: 'related', weight: 1.5 },\n    { id: 'student-ml', source: 'student-user', target: 'ml-concept', type: 'related', weight: 2 },\n    \n    // User-agent interactions\n    { id: 'researcher-research-agent', source: 'researcher-user', target: 'research-agent', type: 'uses', weight: 2 },\n    { id: 'developer-code-agent', source: 'developer-user', target: 'code-agent', type: 'uses', weight: 2.5 },\n    { id: 'student-chatbot', source: 'student-user', target: 'chatbot-agent', type: 'uses', weight: 2 },\n    \n    // Cross-entity relationships\n    { id: 'gpt-bert', source: 'gpt-entity', target: 'bert-entity', type: 'related', weight: 1.5 },\n    { id: 'research-chatbot', source: 'research-agent', target: 'chatbot-agent', type: 'related', weight: 1 },\n    { id: 'code-research', source: 'code-agent', target: 'research-agent', type: 'related', weight: 1 }\n  ];\n\n  return { nodes, edges };\n}\n\n// Generate a simpler knowledge graph for testing\nexport function generateSimpleKnowledgeGraph(): GraphData {\n  const nodes: GraphNode[] = [\n    {\n      id: 'ai',\n      label: 'AI',\n      type: 'concept',\n      position: [0, 0, 0],\n      importance: 3,\n      description: 'Artificial Intelligence'\n    },\n    {\n      id: 'ml',\n      label: 'Machine Learning',\n      type: 'concept',\n      position: [2, 0, 0],\n      importance: 2,\n      description: 'Machine Learning subset of AI'\n    },\n    {\n      id: 'gpt',\n      label: 'GPT-4',\n      type: 'entity',\n      position: [1, 2, 0],\n      importance: 2,\n      description: 'Large Language Model'\n    },\n    {\n      id: 'user',\n      label: 'User',\n      type: 'user',\n      position: [0, -2, 0],\n      importance: 1,\n      description: 'System User'\n    },\n    {\n      id: 'agent',\n      label: 'AI Agent',\n      type: 'agent',\n      position: [-1, 1, 0],\n      importance: 1.5,\n      description: 'AI Assistant Agent'\n    }\n  ];\n\n  const edges: GraphEdge[] = [\n    { id: 'ai-ml', source: 'ai', target: 'ml', type: 'contains', weight: 2 },\n    { id: 'ml-gpt', source: 'ml', target: 'gpt', type: 'uses', weight: 2 },\n    { id: 'user-agent', source: 'user', target: 'agent', type: 'uses', weight: 2 },\n    { id: 'agent-gpt', source: 'agent', target: 'gpt', type: 'uses', weight: 2 },\n    { id: 'user-ai', source: 'user', target: 'ai', type: 'related', weight: 1 }\n  ];\n\n  return { nodes, edges };\n}\n\n// Generate knowledge graph from existing system data\nexport function generateKnowledgeGraphFromSystem(\n  users: any[] = [],\n  agents: any[] = [],\n  documents: any[] = [],\n  knowledgeBases: any[] = []\n): GraphData {\n  const nodes: GraphNode[] = [];\n  const edges: GraphEdge[] = [];\n\n  // Add knowledge bases as concept nodes\n  knowledgeBases.forEach((kb, index) => {\n    nodes.push({\n      id: `kb-${kb.id}`,\n      label: kb.name,\n      type: 'concept',\n      position: [index * 3 - knowledgeBases.length * 1.5, 0, 0],\n      importance: 2,\n      description: kb.description || 'Knowledge Base',\n      properties: {\n        owner: kb.ownerId,\n        created: kb.createdAt,\n        documents: documents.filter(d => d.knowledgeBaseId === kb.id).length\n      }\n    });\n  });\n\n  // Add documents as document nodes\n  documents.forEach((doc, index) => {\n    const angle = (index / documents.length) * Math.PI * 2;\n    const radius = 4;\n    nodes.push({\n      id: `doc-${doc.id}`,\n      label: doc.title,\n      type: 'document',\n      position: [\n        radius * Math.cos(angle),\n        2,\n        radius * Math.sin(angle)\n      ],\n      importance: 1.5,\n      description: doc.content?.substring(0, 100) + '...',\n      properties: {\n        type: doc.contentType,\n        created: doc.createdAt,\n        knowledgeBase: doc.knowledgeBaseId\n      }\n    });\n\n    // Connect documents to their knowledge bases\n    edges.push({\n      id: `kb-doc-${doc.id}`,\n      source: `kb-${doc.knowledgeBaseId}`,\n      target: `doc-${doc.id}`,\n      type: 'contains',\n      weight: 2\n    });\n  });\n\n  // Add agents as agent nodes\n  agents.forEach((agent, index) => {\n    const angle = (index / agents.length) * Math.PI * 2;\n    const radius = 3;\n    nodes.push({\n      id: `agent-${agent.id}`,\n      label: agent.name,\n      type: 'agent',\n      position: [\n        radius * Math.cos(angle),\n        -2,\n        radius * Math.sin(angle)\n      ],\n      importance: 2,\n      description: agent.description || 'AI Agent',\n      properties: {\n        type: agent.type,\n        capabilities: agent.capabilities?.join(', '),\n        owner: agent.ownerId\n      }\n    });\n\n    // Connect agents to their knowledge bases\n    if (agent.knowledgeBaseId) {\n      edges.push({\n        id: `agent-kb-${agent.id}`,\n        source: `agent-${agent.id}`,\n        target: `kb-${agent.knowledgeBaseId}`,\n        type: 'uses',\n        weight: 2\n      });\n    }\n  });\n\n  // Add users as user nodes\n  users.forEach((user, index) => {\n    nodes.push({\n      id: `user-${user.id}`,\n      label: user.name,\n      type: 'user',\n      position: [index * 2 - users.length, -4, 0],\n      importance: 1,\n      description: `User: ${user.email}`,\n      properties: {\n        email: user.email,\n        role: user.role,\n        created: user.createdAt\n      }\n    });\n\n    // Connect users to their knowledge bases\n    knowledgeBases\n      .filter(kb => kb.ownerId === user.id)\n      .forEach(kb => {\n        edges.push({\n          id: `user-kb-${user.id}-${kb.id}`,\n          source: `user-${user.id}`,\n          target: `kb-${kb.id}`,\n          type: 'created',\n          weight: 2\n        });\n      });\n\n    // Connect users to their agents\n    agents\n      .filter(agent => agent.ownerId === user.id)\n      .forEach(agent => {\n        edges.push({\n          id: `user-agent-${user.id}-${agent.id}`,\n          source: `user-${user.id}`,\n          target: `agent-${agent.id}`,\n          type: 'created',\n          weight: 2\n        });\n      });\n  });\n\n  return { nodes, edges };\n}\n"], "names": [], "mappings": "AAAA,+CAA+C;;;;;;AAKxC,SAAS;IACd,MAAM,QAAqB;QACzB,gBAAgB;QAChB;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG;gBAAG;aAAE;YACnB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG;gBAAG;aAAE;YACnB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,YAAY;YACd;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC,CAAC;gBAAG;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,sCAAsC;QACtC;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC,CAAC;gBAAG;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,WAAW;gBACX,YAAY;gBACZ,SAAS;YACX;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC,CAAC;gBAAG;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,WAAW;gBACX,cAAc;gBACd,UAAU;YACZ;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG;gBAAG;aAAE;YACnB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,WAAW;gBACX,UAAU;gBACV,MAAM;YACR;QACF;QAEA,YAAY;QACZ;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG;gBAAG;aAAE;YACnB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,SAAS;gBACT,MAAM;gBACN,OAAO;gBACP,WAAW;YACb;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC,CAAC;gBAAG;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,SAAS;gBACT,MAAM;gBACN,OAAO;YACT;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC,CAAC;gBAAG;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,SAAS;gBACT,MAAM;gBACN,OAAO;YACT;QACF;QAEA,SAAS;QACT;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG,CAAC;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,cAAc;gBACd,OAAO;gBACP,QAAQ;YACV;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG,CAAC;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,cAAc;gBACd,OAAO;gBACP,QAAQ;YACV;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC,CAAC;gBAAG,CAAC;gBAAG;aAAE;YACrB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,cAAc;gBACd,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,QAAQ;QACR;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG,CAAC;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,MAAM;gBACN,aAAa;gBACb,OAAO;YACT;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG,CAAC;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,MAAM;gBACN,SAAS;gBACT,YAAY;YACd;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC,CAAC;gBAAG,CAAC;gBAAG;aAAE;YACrB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,YAAY;gBACZ,OAAO;YACT;QACF;KACD;IAED,MAAM,QAAqB;QACzB,wBAAwB;QACxB;YAAE,IAAI;YAAS,QAAQ;YAAc,QAAQ;YAAc,MAAM;YAAY,QAAQ;QAAE;QACvF;YAAE,IAAI;YAAU,QAAQ;YAAc,QAAQ;YAAe,MAAM;YAAY,QAAQ;QAAI;QAC3F;YAAE,IAAI;YAAU,QAAQ;YAAc,QAAQ;YAAe,MAAM;YAAW,QAAQ;QAAE;QAExF,kCAAkC;QAClC;YAAE,IAAI;YAAW,QAAQ;YAAc,QAAQ;YAAe,MAAM;YAAQ,QAAQ;QAAE;QACtF;YAAE,IAAI;YAAY,QAAQ;YAAe,QAAQ;YAAe,MAAM;YAAQ,QAAQ;QAAI;QAC1F;YAAE,IAAI;YAAiB,QAAQ;YAAqB,QAAQ;YAAc,MAAM;YAAQ,QAAQ;QAAI;QAEpG,yBAAyB;QACzB;YAAE,IAAI;YAAiB,QAAQ;YAAmB,QAAQ;YAAe,MAAM;YAAW,QAAQ;QAAE;QACpG;YAAE,IAAI;YAAgB,QAAQ;YAAmB,QAAQ;YAAc,MAAM;YAAW,QAAQ;QAAI;QACpG;YAAE,IAAI;YAAmB,QAAQ;YAAc,QAAQ;YAAe,MAAM;YAAW,QAAQ;QAAE;QACjG;YAAE,IAAI;YAAiB,QAAQ;YAAa,QAAQ;YAAc,MAAM;YAAW,QAAQ;QAAE;QAC7F;YAAE,IAAI;YAAkB,QAAQ;YAAmB,QAAQ;YAAe,MAAM;YAAW,QAAQ;QAAE;QACrG;YAAE,IAAI;YAAiB,QAAQ;YAAmB,QAAQ;YAAc,MAAM;YAAW,QAAQ;QAAE;QAEnG,sBAAsB;QACtB;YAAE,IAAI;YAAe,QAAQ;YAAiB,QAAQ;YAAc,MAAM;YAAQ,QAAQ;QAAI;QAC9F;YAAE,IAAI;YAAe,QAAQ;YAAiB,QAAQ;YAAe,MAAM;YAAQ,QAAQ;QAAE;QAC7F;YAAE,IAAI;YAAe,QAAQ;YAAkB,QAAQ;YAAc,MAAM;YAAQ,QAAQ;QAAE;QAC7F;YAAE,IAAI;YAAmB,QAAQ;YAAc,QAAQ;YAAqB,MAAM;YAAQ,QAAQ;QAAE;QAEpG,qBAAqB;QACrB;YAAE,IAAI;YAAiB,QAAQ;YAAmB,QAAQ;YAAc,MAAM;YAAW,QAAQ;QAAI;QACrG;YAAE,IAAI;YAAqB,QAAQ;YAAmB,QAAQ;YAAmB,MAAM;YAAW,QAAQ;QAAE;QAC5G;YAAE,IAAI;YAAyB,QAAQ;YAAmB,QAAQ;YAAc,MAAM;YAAW,QAAQ;QAAI;QAC7G;YAAE,IAAI;YAAwB,QAAQ;YAAkB,QAAQ;YAAqB,MAAM;YAAQ,QAAQ;QAAI;QAC/G;YAAE,IAAI;YAAgB,QAAQ;YAAkB,QAAQ;YAAc,MAAM;YAAQ,QAAQ;QAAE;QAC9F;YAAE,IAAI;YAAc,QAAQ;YAAgB,QAAQ;YAAc,MAAM;YAAW,QAAQ;QAAI;QAC/F;YAAE,IAAI;YAAc,QAAQ;YAAgB,QAAQ;YAAc,MAAM;YAAW,QAAQ;QAAE;QAE7F,0BAA0B;QAC1B;YAAE,IAAI;YAA6B,QAAQ;YAAmB,QAAQ;YAAkB,MAAM;YAAQ,QAAQ;QAAE;QAChH;YAAE,IAAI;YAAwB,QAAQ;YAAkB,QAAQ;YAAc,MAAM;YAAQ,QAAQ;QAAI;QACxG;YAAE,IAAI;YAAmB,QAAQ;YAAgB,QAAQ;YAAiB,MAAM;YAAQ,QAAQ;QAAE;QAElG,6BAA6B;QAC7B;YAAE,IAAI;YAAY,QAAQ;YAAc,QAAQ;YAAe,MAAM;YAAW,QAAQ;QAAI;QAC5F;YAAE,IAAI;YAAoB,QAAQ;YAAkB,QAAQ;YAAiB,MAAM;YAAW,QAAQ;QAAE;QACxG;YAAE,IAAI;YAAiB,QAAQ;YAAc,QAAQ;YAAkB,MAAM;YAAW,QAAQ;QAAE;KACnG;IAED,OAAO;QAAE;QAAO;IAAM;AACxB;AAGO,SAAS;IACd,MAAM,QAAqB;QACzB;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG;gBAAG;aAAE;YACnB,YAAY;YACZ,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG;gBAAG;aAAE;YACnB,YAAY;YACZ,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG;gBAAG;aAAE;YACnB,YAAY;YACZ,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG,CAAC;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC,CAAC;gBAAG;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;QACf;KACD;IAED,MAAM,QAAqB;QACzB;YAAE,IAAI;YAAS,QAAQ;YAAM,QAAQ;YAAM,MAAM;YAAY,QAAQ;QAAE;QACvE;YAAE,IAAI;YAAU,QAAQ;YAAM,QAAQ;YAAO,MAAM;YAAQ,QAAQ;QAAE;QACrE;YAAE,IAAI;YAAc,QAAQ;YAAQ,QAAQ;YAAS,MAAM;YAAQ,QAAQ;QAAE;QAC7E;YAAE,IAAI;YAAa,QAAQ;YAAS,QAAQ;YAAO,MAAM;YAAQ,QAAQ;QAAE;QAC3E;YAAE,IAAI;YAAW,QAAQ;YAAQ,QAAQ;YAAM,MAAM;YAAW,QAAQ;QAAE;KAC3E;IAED,OAAO;QAAE;QAAO;IAAM;AACxB;AAGO,SAAS,iCACd,QAAe,EAAE,EACjB,SAAgB,EAAE,EAClB,YAAmB,EAAE,EACrB,iBAAwB,EAAE;IAE1B,MAAM,QAAqB,EAAE;IAC7B,MAAM,QAAqB,EAAE;IAE7B,uCAAuC;IACvC,eAAe,OAAO,CAAC,CAAC,IAAI;QAC1B,MAAM,IAAI,CAAC;YACT,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACjB,OAAO,GAAG,IAAI;YACd,MAAM;YACN,UAAU;gBAAC,QAAQ,IAAI,eAAe,MAAM,GAAG;gBAAK;gBAAG;aAAE;YACzD,YAAY;YACZ,aAAa,GAAG,WAAW,IAAI;YAC/B,YAAY;gBACV,OAAO,GAAG,OAAO;gBACjB,SAAS,GAAG,SAAS;gBACrB,WAAW,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,KAAK,GAAG,EAAE,EAAE,MAAM;YACtE;QACF;IACF;IAEA,kCAAkC;IAClC,UAAU,OAAO,CAAC,CAAC,KAAK;QACtB,MAAM,QAAQ,AAAC,QAAQ,UAAU,MAAM,GAAI,KAAK,EAAE,GAAG;QACrD,MAAM,SAAS;QACf,MAAM,IAAI,CAAC;YACT,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;YACnB,OAAO,IAAI,KAAK;YAChB,MAAM;YACN,UAAU;gBACR,SAAS,KAAK,GAAG,CAAC;gBAClB;gBACA,SAAS,KAAK,GAAG,CAAC;aACnB;YACD,YAAY;YACZ,aAAa,IAAI,OAAO,EAAE,UAAU,GAAG,OAAO;YAC9C,YAAY;gBACV,MAAM,IAAI,WAAW;gBACrB,SAAS,IAAI,SAAS;gBACtB,eAAe,IAAI,eAAe;YACpC;QACF;QAEA,6CAA6C;QAC7C,MAAM,IAAI,CAAC;YACT,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE;YACtB,QAAQ,CAAC,GAAG,EAAE,IAAI,eAAe,EAAE;YACnC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;YACvB,MAAM;YACN,QAAQ;QACV;IACF;IAEA,4BAA4B;IAC5B,OAAO,OAAO,CAAC,CAAC,OAAO;QACrB,MAAM,QAAQ,AAAC,QAAQ,OAAO,MAAM,GAAI,KAAK,EAAE,GAAG;QAClD,MAAM,SAAS;QACf,MAAM,IAAI,CAAC;YACT,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;YACvB,OAAO,MAAM,IAAI;YACjB,MAAM;YACN,UAAU;gBACR,SAAS,KAAK,GAAG,CAAC;gBAClB,CAAC;gBACD,SAAS,KAAK,GAAG,CAAC;aACnB;YACD,YAAY;YACZ,aAAa,MAAM,WAAW,IAAI;YAClC,YAAY;gBACV,MAAM,MAAM,IAAI;gBAChB,cAAc,MAAM,YAAY,EAAE,KAAK;gBACvC,OAAO,MAAM,OAAO;YACtB;QACF;QAEA,0CAA0C;QAC1C,IAAI,MAAM,eAAe,EAAE;YACzB,MAAM,IAAI,CAAC;gBACT,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE;gBAC1B,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;gBAC3B,QAAQ,CAAC,GAAG,EAAE,MAAM,eAAe,EAAE;gBACrC,MAAM;gBACN,QAAQ;YACV;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,MAAM,IAAI,CAAC;YACT,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YACrB,OAAO,KAAK,IAAI;YAChB,MAAM;YACN,UAAU;gBAAC,QAAQ,IAAI,MAAM,MAAM;gBAAE,CAAC;gBAAG;aAAE;YAC3C,YAAY;YACZ,aAAa,CAAC,MAAM,EAAE,KAAK,KAAK,EAAE;YAClC,YAAY;gBACV,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI;gBACf,SAAS,KAAK,SAAS;YACzB;QACF;QAEA,yCAAyC;QACzC,eACG,MAAM,CAAC,CAAA,KAAM,GAAG,OAAO,KAAK,KAAK,EAAE,EACnC,OAAO,CAAC,CAAA;YACP,MAAM,IAAI,CAAC;gBACT,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;gBACjC,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBACzB,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBACrB,MAAM;gBACN,QAAQ;YACV;QACF;QAEF,gCAAgC;QAChC,OACG,MAAM,CAAC,CAAA,QAAS,MAAM,OAAO,KAAK,KAAK,EAAE,EACzC,OAAO,CAAC,CAAA;YACP,MAAM,IAAI,CAAC;gBACT,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;gBACvC,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBACzB,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;gBAC3B,MAAM;gBACN,QAAQ;YACV;QACF;IACJ;IAEA,OAAO;QAAE;QAAO;IAAM;AACxB", "debugId": null}}, {"offset": {"line": 1772, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1778, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/lib/api-client.ts"], "sourcesContent": ["// API client for Knowledge OS backend\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\n\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  error?: string;\n}\n\nexport interface User {\n  id: string;\n  name: string;\n  email: string;\n  profileImage?: string;\n  role: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface KnowledgeBase {\n  id: string;\n  name: string;\n  description?: string;\n  createdAt: string;\n  updatedAt: string;\n  ownerId: string;\n  teamId?: string;\n}\n\nexport interface Document {\n  id: string;\n  title: string;\n  content: string;\n  contentType: string;\n  vectorId?: string;\n  createdAt: string;\n  updatedAt: string;\n  knowledgeBaseId: string;\n  metadata?: Record<string, any>;\n}\n\nexport interface Agent {\n  id: string;\n  name: string;\n  description?: string;\n  type: string;\n  capabilities: string[];\n  config: Record<string, any>;\n  createdAt: string;\n  updatedAt: string;\n  ownerId: string;\n  knowledgeBaseId?: string;\n}\n\nexport interface Query {\n  id: string;\n  content: string;\n  response: string;\n  metadata?: Record<string, any>;\n  createdAt: string;\n  sessionId: string;\n  userId: string;\n}\n\nexport interface SearchResult {\n  id: string;\n  content: string;\n  similarity: number;\n  document: {\n    id: string;\n    title: string;\n    knowledgeBaseId: string;\n  };\n}\n\nclass ApiClient {\n  private baseUrl: string;\n  private authToken?: string;\n\n  constructor(baseUrl: string = API_BASE_URL) {\n    this.baseUrl = baseUrl;\n  }\n\n  setAuthToken(token: string) {\n    this.authToken = token;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    const url = `${this.baseUrl}${endpoint}`;\n    const headers: HeadersInit = {\n      'Content-Type': 'application/json',\n      ...options.headers,\n    };\n\n    if (this.authToken) {\n      headers.Authorization = `Bearer ${this.authToken}`;\n    }\n\n    try {\n      const response = await fetch(url, {\n        ...options,\n        headers,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        return {\n          success: false,\n          error: errorData.detail || `HTTP ${response.status}: ${response.statusText}`,\n        };\n      }\n\n      const data = await response.json();\n      return {\n        success: true,\n        data,\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error occurred',\n      };\n    }\n  }\n\n  // Health check\n  async healthCheck(): Promise<ApiResponse<{ status: string; timestamp: string }>> {\n    return this.request('/health');\n  }\n\n  // User endpoints\n  async createUser(userData: {\n    name: string;\n    email: string;\n    profileImage?: string;\n    role?: string;\n  }): Promise<ApiResponse<User>> {\n    return this.request('/api/users', {\n      method: 'POST',\n      body: JSON.stringify(userData),\n    });\n  }\n\n  async getUser(userId: string): Promise<ApiResponse<User>> {\n    return this.request(`/api/users/${userId}`);\n  }\n\n  // Knowledge Base endpoints\n  async createKnowledgeBase(kbData: {\n    name: string;\n    description?: string;\n    teamId?: string;\n  }): Promise<ApiResponse<KnowledgeBase>> {\n    return this.request('/api/knowledge-bases', {\n      method: 'POST',\n      body: JSON.stringify(kbData),\n    });\n  }\n\n  async listKnowledgeBases(): Promise<ApiResponse<KnowledgeBase[]>> {\n    return this.request('/api/knowledge-bases');\n  }\n\n  // Document endpoints\n  async createDocument(docData: {\n    title: string;\n    content: string;\n    contentType?: string;\n    knowledgeBaseId: string;\n    metadata?: Record<string, any>;\n  }): Promise<ApiResponse<Document>> {\n    return this.request('/api/documents', {\n      method: 'POST',\n      body: JSON.stringify(docData),\n    });\n  }\n\n  async listDocuments(knowledgeBaseId?: string): Promise<ApiResponse<Document[]>> {\n    const params = knowledgeBaseId ? `?knowledge_base_id=${knowledgeBaseId}` : '';\n    return this.request(`/api/documents${params}`);\n  }\n\n  // Agent endpoints\n  async createAgent(agentData: {\n    name: string;\n    description?: string;\n    type?: string;\n    capabilities: string[];\n    config: Record<string, any>;\n    knowledgeBaseId?: string;\n  }): Promise<ApiResponse<Agent>> {\n    return this.request('/api/agents', {\n      method: 'POST',\n      body: JSON.stringify(agentData),\n    });\n  }\n\n  async listAgents(): Promise<ApiResponse<Agent[]>> {\n    return this.request('/api/agents');\n  }\n\n  // Search endpoints\n  async semanticSearch(searchData: {\n    query: string;\n    knowledgeBaseId?: string;\n    limit?: number;\n    threshold?: number;\n  }): Promise<ApiResponse<SearchResult[]>> {\n    return this.request('/api/search', {\n      method: 'POST',\n      body: JSON.stringify(searchData),\n    });\n  }\n\n  // Query/Chat endpoints\n  async processQuery(queryData: {\n    content: string;\n    agentId: string;\n    sessionId?: string;\n  }): Promise<ApiResponse<Query>> {\n    return this.request('/api/query', {\n      method: 'POST',\n      body: JSON.stringify(queryData),\n    });\n  }\n}\n\n// Create singleton instance\nexport const apiClient = new ApiClient();\n\n// React hooks for API calls\nexport function useApiClient() {\n  return apiClient;\n}\n\n// Utility functions for common operations\nexport async function withErrorHandling<T>(\n  apiCall: () => Promise<ApiResponse<T>>,\n  onError?: (error: string) => void\n): Promise<T | null> {\n  const response = await apiCall();\n  \n  if (response.success && response.data) {\n    return response.data;\n  } else {\n    const error = response.error || 'Unknown error occurred';\n    if (onError) {\n      onError(error);\n    } else {\n      console.error('API Error:', error);\n    }\n    return null;\n  }\n}\n\n// Mock data for development (when backend is not available)\nexport const mockData = {\n  users: [\n    {\n      id: 'user_1',\n      name: 'John Doe',\n      email: '<EMAIL>',\n      role: 'USER',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    },\n  ],\n  knowledgeBases: [\n    {\n      id: 'kb_1',\n      name: 'Personal Knowledge Base',\n      description: 'My personal collection of knowledge',\n      ownerId: 'user_1',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    },\n  ],\n  agents: [\n    {\n      id: 'agent_1',\n      name: 'AI Assistant',\n      description: 'A helpful AI assistant',\n      type: 'ASSISTANT',\n      capabilities: ['Q&A', 'Research', 'Writing'],\n      config: { model: 'gpt-4', temperature: 0.7 },\n      ownerId: 'user_1',\n      knowledgeBaseId: 'kb_1',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    },\n  ],\n};\n\n// Development mode flag\nexport const isDevelopmentMode = process.env.NODE_ENV === 'development';\n\n// Mock API client for development\nexport class MockApiClient extends ApiClient {\n  async healthCheck() {\n    return { success: true, data: { status: 'healthy', timestamp: new Date().toISOString() } };\n  }\n\n  async listKnowledgeBases() {\n    return { success: true, data: mockData.knowledgeBases };\n  }\n\n  async listAgents() {\n    return { success: true, data: mockData.agents };\n  }\n\n  async listDocuments() {\n    return { success: true, data: [] };\n  }\n\n  async semanticSearch() {\n    return { success: true, data: [] };\n  }\n}\n\n// Export the appropriate client based on environment\nexport const client = isDevelopmentMode ? new MockApiClient() : apiClient;\n"], "names": [], "mappings": "AAAA,sCAAsC;;;;;;;;;;AA0SL;AAxSjC,MAAM,eAAe,+RAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AA0ExD,MAAM;IACI,QAAgB;IAChB,UAAmB;IAE3B,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,aAAa,KAAa,EAAE;QAC1B,IAAI,CAAC,SAAS,GAAG;IACnB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QACxC,MAAM,UAAuB;YAC3B,gBAAgB;YAChB,GAAG,QAAQ,OAAO;QACpB;QAEA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,QAAQ,aAAa,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE;QACpD;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,GAAG,OAAO;gBACV;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,OAAO;oBACL,SAAS;oBACT,OAAO,UAAU,MAAM,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;gBAC9E;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBACL,SAAS;gBACT;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,eAAe;IACf,MAAM,cAA2E;QAC/E,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,iBAAiB;IACjB,MAAM,WAAW,QAKhB,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc;YAChC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,QAAQ,MAAc,EAA8B;QACxD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,QAAQ;IAC5C;IAEA,2BAA2B;IAC3B,MAAM,oBAAoB,MAIzB,EAAuC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAC,wBAAwB;YAC1C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,qBAA4D;QAChE,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,qBAAqB;IACrB,MAAM,eAAe,OAMpB,EAAkC;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB;YACpC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,eAAwB,EAAoC;QAC9E,MAAM,SAAS,kBAAkB,CAAC,mBAAmB,EAAE,iBAAiB,GAAG;QAC3E,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,QAAQ;IAC/C;IAEA,kBAAkB;IAClB,MAAM,YAAY,SAOjB,EAA+B;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe;YACjC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,aAA4C;QAChD,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,mBAAmB;IACnB,MAAM,eAAe,UAKpB,EAAwC;QACvC,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe;YACjC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,uBAAuB;IACvB,MAAM,aAAa,SAIlB,EAA+B;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc;YAChC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;AACF;AAGO,MAAM,YAAY,IAAI;AAGtB,SAAS;IACd,OAAO;AACT;AAGO,eAAe,kBACpB,OAAsC,EACtC,OAAiC;IAEjC,MAAM,WAAW,MAAM;IAEvB,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;QACrC,OAAO,SAAS,IAAI;IACtB,OAAO;QACL,MAAM,QAAQ,SAAS,KAAK,IAAI;QAChC,IAAI,SAAS;YACX,QAAQ;QACV,OAAO;YACL,QAAQ,KAAK,CAAC,cAAc;QAC9B;QACA,OAAO;IACT;AACF;AAGO,MAAM,WAAW;IACtB,OAAO;QACL;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;KACD;IACD,gBAAgB;QACd;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;KACD;IACD,QAAQ;QACN;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,cAAc;gBAAC;gBAAO;gBAAY;aAAU;YAC5C,QAAQ;gBAAE,OAAO;gBAAS,aAAa;YAAI;YAC3C,SAAS;YACT,iBAAiB;YACjB,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;KACD;AACH;AAGO,MAAM,oBAAoB,oDAAyB;AAGnD,MAAM,sBAAsB;IACjC,MAAM,cAAc;QAClB,OAAO;YAAE,SAAS;YAAM,MAAM;gBAAE,QAAQ;gBAAW,WAAW,IAAI,OAAO,WAAW;YAAG;QAAE;IAC3F;IAEA,MAAM,qBAAqB;QACzB,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,cAAc;QAAC;IACxD;IAEA,MAAM,aAAa;QACjB,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,MAAM;QAAC;IAChD;IAEA,MAAM,gBAAgB;QACpB,OAAO;YAAE,SAAS;YAAM,MAAM,EAAE;QAAC;IACnC;IAEA,MAAM,iBAAiB;QACrB,OAAO;YAAE,SAAS;YAAM,MAAM,EAAE;QAAC;IACnC;AACF;AAGO,MAAM,SAAS,uCAAoB,IAAI", "debugId": null}}, {"offset": {"line": 1993, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1999, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/knowledge-graph/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport KnowledgeGraph3D from '@/components/knowledge-graph-3d';\nimport type { GraphData } from '@/components/knowledge-graph-3d';\nimport { \n  generateKnowledgeGraphData, \n  generateSimpleKnowledgeGraph,\n  generateKnowledgeGraphFromSystem \n} from '@/lib/knowledge-graph-data';\nimport { mockData } from '@/lib/api-client';\n\nexport default function KnowledgeGraphPage() {\n  const [graphData, setGraphData] = useState<GraphData | null>(null);\n  const [dataSource, setDataSource] = useState<'sample' | 'simple' | 'system'>('sample');\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Load initial data\n  useEffect(() => {\n    loadGraphData(dataSource);\n  }, [dataSource]);\n\n  const loadGraphData = async (source: 'sample' | 'simple' | 'system') => {\n    setIsLoading(true);\n    \n    try {\n      let data: GraphData;\n      \n      switch (source) {\n        case 'simple':\n          data = generateSimpleKnowledgeGraph();\n          break;\n        case 'system':\n          // In a real app, you'd fetch this from your API\n          data = generateKnowledgeGraphFromSystem(\n            mockData.users,\n            mockData.agents,\n            [], // documents\n            mockData.knowledgeBases\n          );\n          break;\n        case 'sample':\n        default:\n          data = generateKnowledgeGraphData();\n          break;\n      }\n      \n      setGraphData(data);\n    } catch (error) {\n      console.error('Error loading graph data:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleDataSourceChange = (source: 'sample' | 'simple' | 'system') => {\n    setDataSource(source);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800\">\n      {/* Header */}\n      <div className=\"p-8 pb-4\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              3D Knowledge Graph Visualization\n            </h1>\n            <p className=\"text-lg text-gray-600 dark:text-gray-300 mb-6\">\n              Explore knowledge relationships in an interactive 3D space\n            </p>\n            \n            {/* Data Source Controls */}\n            <div className=\"flex justify-center gap-4 mb-6\">\n              <Button\n                variant={dataSource === 'simple' ? 'default' : 'outline'}\n                onClick={() => handleDataSourceChange('simple')}\n                disabled={isLoading}\n              >\n                Simple Graph\n              </Button>\n              <Button\n                variant={dataSource === 'sample' ? 'default' : 'outline'}\n                onClick={() => handleDataSourceChange('sample')}\n                disabled={isLoading}\n              >\n                AI Knowledge Graph\n              </Button>\n              <Button\n                variant={dataSource === 'system' ? 'default' : 'outline'}\n                onClick={() => handleDataSourceChange('system')}\n                disabled={isLoading}\n              >\n                System Data\n              </Button>\n            </div>\n          </div>\n\n          {/* Graph Statistics */}\n          {graphData && (\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n              <Card>\n                <CardHeader className=\"pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Total Nodes</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">{graphData.nodes.length}</div>\n                  <div className=\"text-xs text-gray-500 mt-1\">\n                    Concepts, Entities, Documents, Agents, Users\n                  </div>\n                </CardContent>\n              </Card>\n              \n              <Card>\n                <CardHeader className=\"pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Connections</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">{graphData.edges.length}</div>\n                  <div className=\"text-xs text-gray-500 mt-1\">\n                    Relationships between entities\n                  </div>\n                </CardContent>\n              </Card>\n              \n              <Card>\n                <CardHeader className=\"pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Node Types</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">\n                    {new Set(graphData.nodes.map(n => n.type)).size}\n                  </div>\n                  <div className=\"text-xs text-gray-500 mt-1\">\n                    Different entity categories\n                  </div>\n                </CardContent>\n              </Card>\n              \n              <Card>\n                <CardHeader className=\"pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Data Source</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-lg font-semibold capitalize\">{dataSource}</div>\n                  <div className=\"text-xs text-gray-500 mt-1\">\n                    Current visualization data\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* 3D Graph Visualization */}\n      <div className=\"px-8 pb-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <Card className=\"overflow-hidden\">\n            <CardContent className=\"p-0\">\n              <div style={{ height: '700px', width: '100%' }}>\n                {isLoading ? (\n                  <div className=\"flex items-center justify-center h-full\">\n                    <div className=\"text-center\">\n                      <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n                      <p className=\"text-gray-600 dark:text-gray-300\">Loading knowledge graph...</p>\n                    </div>\n                  </div>\n                ) : graphData ? (\n                  <KnowledgeGraph3D data={graphData} />\n                ) : (\n                  <div className=\"flex items-center justify-center h-full\">\n                    <p className=\"text-gray-600 dark:text-gray-300\">No data available</p>\n                  </div>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* Information Panel */}\n      <div className=\"px-8 pb-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Features */}\n            <Card>\n              <CardHeader>\n                <CardTitle>3D Visualization Features</CardTitle>\n                <CardDescription>\n                  Interactive features available in the knowledge graph\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-blue-500 rounded-full mt-2\"></div>\n                  <div>\n                    <p className=\"font-medium\">Interactive Navigation</p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                      Orbit, zoom, and pan around the 3D space\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full mt-2\"></div>\n                  <div>\n                    <p className=\"font-medium\">Node Selection</p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                      Click nodes to view detailed information\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-purple-500 rounded-full mt-2\"></div>\n                  <div>\n                    <p className=\"font-medium\">Dynamic Layouts</p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                      Switch between sphere, cube, random, and force-directed layouts\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-orange-500 rounded-full mt-2\"></div>\n                  <div>\n                    <p className=\"font-medium\">Hover Effects</p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                      Smooth animations and visual feedback on interaction\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-red-500 rounded-full mt-2\"></div>\n                  <div>\n                    <p className=\"font-medium\">Connection Visualization</p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                      Color-coded edges showing different relationship types\n                    </p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Controls Guide */}\n            <Card>\n              <CardHeader>\n                <CardTitle>How to Use</CardTitle>\n                <CardDescription>\n                  Guide to navigating the 3D knowledge graph\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div>\n                  <h4 className=\"font-medium mb-2\">Mouse Controls</h4>\n                  <ul className=\"space-y-1 text-sm text-gray-600 dark:text-gray-300\">\n                    <li>• <strong>Left Click + Drag:</strong> Rotate the view</li>\n                    <li>• <strong>Right Click + Drag:</strong> Pan the view</li>\n                    <li>• <strong>Scroll Wheel:</strong> Zoom in/out</li>\n                    <li>• <strong>Click Node:</strong> Select and view details</li>\n                    <li>• <strong>Hover Node:</strong> Highlight and preview</li>\n                  </ul>\n                </div>\n                \n                <div>\n                  <h4 className=\"font-medium mb-2\">Layout Options</h4>\n                  <ul className=\"space-y-1 text-sm text-gray-600 dark:text-gray-300\">\n                    <li>• <strong>Sphere:</strong> Nodes arranged on sphere surface</li>\n                    <li>• <strong>Cube:</strong> 3D grid arrangement</li>\n                    <li>• <strong>Random:</strong> Scattered positioning</li>\n                    <li>• <strong>Force:</strong> Physics-based layout</li>\n                  </ul>\n                </div>\n                \n                <div>\n                  <h4 className=\"font-medium mb-2\">Visual Legend</h4>\n                  <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n                      <span>Concepts</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-3 h-3 bg-blue-500 rounded-full\"></div>\n                      <span>Entities</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n                      <span>Documents</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-3 h-3 bg-purple-500 rounded-full\"></div>\n                      <span>Agents</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-3 h-3 bg-orange-500 rounded-full\"></div>\n                      <span>Users</span>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAEA;AAKA;;;AAbA;;;;;;;AAee,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAkC;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,oBAAoB;IACpB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;wCAAE;YACR,cAAc;QAChB;uCAAG;QAAC;KAAW;IAEf,MAAM,gBAAgB,OAAO;QAC3B,aAAa;QAEb,IAAI;YACF,IAAI;YAEJ,OAAQ;gBACN,KAAK;oBACH,OAAO,CAAA,GAAA,oIAAA,CAAA,+BAA4B,AAAD;oBAClC;gBACF,KAAK;oBACH,gDAAgD;oBAChD,OAAO,CAAA,GAAA,oIAAA,CAAA,mCAAgC,AAAD,EACpC,uHAAA,CAAA,WAAQ,CAAC,KAAK,EACd,uHAAA,CAAA,WAAQ,CAAC,MAAM,EACf,EAAE,EACF,uHAAA,CAAA,WAAQ,CAAC,cAAc;oBAEzB;gBACF,KAAK;gBACL;oBACE,OAAO,CAAA,GAAA,oIAAA,CAAA,6BAA0B,AAAD;oBAChC;YACJ;YAEA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,cAAc;IAChB;IAEA,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAGtE,4TAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAK7D,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,eAAe,WAAW,YAAY;4CAC/C,SAAS,IAAM,uBAAuB;4CACtC,UAAU;sDACX;;;;;;sDAGD,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,eAAe,WAAW,YAAY;4CAC/C,SAAS,IAAM,uBAAuB;4CACtC,UAAU;sDACX;;;;;;sDAGD,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,eAAe,WAAW,YAAY;4CAC/C,SAAS,IAAM,uBAAuB;4CACtC,UAAU;sDACX;;;;;;;;;;;;;;;;;;wBAOJ,2BACC,4TAAC;4BAAI,WAAU;;8CACb,4TAAC,4HAAA,CAAA,OAAI;;sDACH,4TAAC,4HAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,4TAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;;;;;;sDAE7C,4TAAC,4HAAA,CAAA,cAAW;;8DACV,4TAAC;oDAAI,WAAU;8DAAsB,UAAU,KAAK,CAAC,MAAM;;;;;;8DAC3D,4TAAC;oDAAI,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAMhD,4TAAC,4HAAA,CAAA,OAAI;;sDACH,4TAAC,4HAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,4TAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;;;;;;sDAE7C,4TAAC,4HAAA,CAAA,cAAW;;8DACV,4TAAC;oDAAI,WAAU;8DAAsB,UAAU,KAAK,CAAC,MAAM;;;;;;8DAC3D,4TAAC;oDAAI,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAMhD,4TAAC,4HAAA,CAAA,OAAI;;sDACH,4TAAC,4HAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,4TAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;;;;;;sDAE7C,4TAAC,4HAAA,CAAA,cAAW;;8DACV,4TAAC;oDAAI,WAAU;8DACZ,IAAI,IAAI,UAAU,KAAK,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,IAAI;;;;;;8DAEjD,4TAAC;oDAAI,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAMhD,4TAAC,4HAAA,CAAA,OAAI;;sDACH,4TAAC,4HAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,4TAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;;;;;;sDAE7C,4TAAC,4HAAA,CAAA,cAAW;;8DACV,4TAAC;oDAAI,WAAU;8DAAoC;;;;;;8DACnD,4TAAC;oDAAI,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWxD,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC,4HAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,4TAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,4TAAC;gCAAI,OAAO;oCAAE,QAAQ;oCAAS,OAAO;gCAAO;0CAC1C,0BACC,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;;;;;;0DACf,4TAAC;gDAAE,WAAU;0DAAmC;;;;;;;;;;;;;;;;2CAGlD,0BACF,4TAAC,0IAAA,CAAA,UAAgB;oCAAC,MAAM;;;;;yDAExB,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU9D,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAI,WAAU;;0CAEb,4TAAC,4HAAA,CAAA,OAAI;;kDACH,4TAAC,4HAAA,CAAA,aAAU;;0DACT,4TAAC,4HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,4TAAC,4HAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,4TAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;;;;;;kEACf,4TAAC;;0EACC,4TAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,4TAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAM5D,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;;;;;;kEACf,4TAAC;;0EACC,4TAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,4TAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAM5D,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;;;;;;kEACf,4TAAC;;0EACC,4TAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,4TAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAM5D,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;;;;;;kEACf,4TAAC;;0EACC,4TAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,4TAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAM5D,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;;;;;;kEACf,4TAAC;;0EACC,4TAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,4TAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAShE,4TAAC,4HAAA,CAAA,OAAI;;kDACH,4TAAC,4HAAA,CAAA,aAAU;;0DACT,4TAAC,4HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,4TAAC,4HAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,4TAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,4TAAC;;kEACC,4TAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,4TAAC;wDAAG,WAAU;;0EACZ,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAA2B;;;;;;;0EACzC,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAA4B;;;;;;;0EAC1C,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAAsB;;;;;;;0EACpC,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAAoB;;;;;;;0EAClC,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAAoB;;;;;;;;;;;;;;;;;;;0DAItC,4TAAC;;kEACC,4TAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,4TAAC;wDAAG,WAAU;;0EACZ,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAAgB;;;;;;;0EAC9B,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAAc;;;;;;;0EAC5B,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAAgB;;;;;;;0EAC9B,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAAe;;;;;;;;;;;;;;;;;;;0DAIjC,4TAAC;;kEACC,4TAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAU;;;;;;kFACf,4TAAC;kFAAK;;;;;;;;;;;;0EAER,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAU;;;;;;kFACf,4TAAC;kFAAK;;;;;;;;;;;;0EAER,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAU;;;;;;kFACf,4TAAC;kFAAK;;;;;;;;;;;;0EAER,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAU;;;;;;kFACf,4TAAC;kFAAK;;;;;;;;;;;;0EAER,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAU;;;;;;kFACf,4TAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5B;GAxSwB;KAAA", "debugId": null}}, {"offset": {"line": 3085, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}