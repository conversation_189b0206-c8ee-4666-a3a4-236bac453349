{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={`rounded-lg border bg-card text-card-foreground shadow-sm ${className || ''}`}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={`flex flex-col space-y-1.5 p-6 ${className || ''}`} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={`text-2xl font-semibold leading-none tracking-tight ${className || ''}`}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p ref={ref} className={`text-sm text-muted-foreground ${className || ''}`} {...props} />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={`p-6 pt-0 ${className || ''}`} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={`flex items-center p-6 pt-0 ${className || ''}`} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAEA,MAAM,qBAAO,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAC,yDAAyD,EAAE,aAAa,IAAI;QACvF,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAC,8BAA8B,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;;AAEzF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAC,mDAAmD,EAAE,aAAa,IAAI;QACjF,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAE,KAAK;QAAK,WAAW,CAAC,8BAA8B,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;;AAEvF,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAC,SAAS,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;;AAEpE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAC,2BAA2B,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;;AAEtF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'secondary' | 'destructive' | 'outline'\n}\n\nconst Badge = React.forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant = 'default', ...props }, ref) => {\n    const baseClasses = \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\"\n    \n    const variantClasses = {\n      default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n      secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n      destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n      outline: \"text-foreground\",\n    }\n    \n    return (\n      <div\n        ref={ref}\n        className={`${baseClasses} ${variantClasses[variant]} ${className || ''}`}\n        {...props}\n      />\n    )\n  }\n)\nBadge.displayName = \"Badge\"\n\nexport { Badge }\n"], "names": [], "mappings": ";;;;AAAA;;;AAOA,MAAM,sBAAQ,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAO,EAAE;IAC7C,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,aAAa;QACb,SAAS;IACX;IAEA,qBACE,4TAAC;QACC,KAAK;QACL,WAAW,GAAG,YAAY,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,aAAa,IAAI;QACxE,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/agent-chat.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\n\ninterface ChatMessage {\n  role: 'user' | 'assistant' | 'system';\n  content: string;\n  timestamp: Date;\n  model?: string;\n  provider?: string;\n}\n\ninterface AgentConfig {\n  id: string;\n  name: string;\n  type: string;\n  description: string;\n  capabilities: string[];\n  preferredProvider?: string;\n  preferredModel?: string;\n}\n\ninterface AgentChatProps {\n  agentId?: string;\n  userId?: string;\n  sessionId?: string;\n  onSessionCreate?: (sessionId: string) => void;\n}\n\nexport default function AgentChat({\n  agentId = 'assistant',\n  userId = 'user_123',\n  sessionId: initialSessionId,\n  onSessionCreate\n}: AgentChatProps) {\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [sessionId, setSessionId] = useState<string | null>(initialSessionId || null);\n  const [availableAgents, setAvailableAgents] = useState<AgentConfig[]>([]);\n  const [selectedAgent, setSelectedAgent] = useState<string>(agentId);\n  const [providers, setProviders] = useState<any[]>([]);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  // Load available agents and providers\n  useEffect(() => {\n    loadAgentsAndProviders();\n  }, []);\n\n  // Load session history if sessionId provided\n  useEffect(() => {\n    if (sessionId) {\n      loadSessionHistory();\n    }\n  }, [sessionId]);\n\n  // Auto-scroll to bottom\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const loadAgentsAndProviders = async () => {\n    try {\n      const [agentsRes, providersRes] = await Promise.all([\n        fetch('/api/agents/chat?action=agents'),\n        fetch('/api/agents/chat?action=providers')\n      ]);\n\n      if (agentsRes.ok) {\n        const { agents } = await agentsRes.json();\n        setAvailableAgents(agents);\n      }\n\n      if (providersRes.ok) {\n        const { providers } = await providersRes.json();\n        setProviders(providers);\n      }\n    } catch (error) {\n      console.error('Failed to load agents/providers:', error);\n    }\n  };\n\n  const loadSessionHistory = async () => {\n    if (!sessionId) return;\n\n    try {\n      const response = await fetch(`/api/agents/chat?action=session&sessionId=${sessionId}`);\n      if (response.ok) {\n        const { history } = await response.json();\n        const formattedMessages = history.map((msg: any) => ({\n          ...msg,\n          timestamp: new Date()\n        }));\n        setMessages(formattedMessages);\n      }\n    } catch (error) {\n      console.error('Failed to load session history:', error);\n    }\n  };\n\n  const sendMessage = async () => {\n    if (!inputMessage.trim() || isLoading) return;\n\n    const userMessage: ChatMessage = {\n      role: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsLoading(true);\n\n    try {\n      const response = await fetch('/api/agents/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          sessionId,\n          message: inputMessage,\n          agentId: selectedAgent,\n          userId,\n          options: {\n            temperature: 0.7,\n            maxTokens: 1000\n          }\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to send message');\n      }\n\n      const data = await response.json();\n\n      // Update session ID if new session was created\n      if (data.sessionId && !sessionId) {\n        setSessionId(data.sessionId);\n        onSessionCreate?.(data.sessionId);\n      }\n\n      const assistantMessage: ChatMessage = {\n        role: 'assistant',\n        content: data.response,\n        timestamp: new Date(),\n        model: data.model,\n        provider: data.provider\n      };\n\n      setMessages(prev => [...prev, assistantMessage]);\n\n    } catch (error) {\n      console.error('Error sending message:', error);\n\n      let errorContent = 'Sorry, I encountered an error processing your message.';\n\n      if (error instanceof Error) {\n        if (error.message.includes('Failed to send message')) {\n          errorContent = 'Failed to connect to AI service. Please check your internet connection and try again.';\n        } else if (error.message.includes('No AI providers')) {\n          errorContent = 'No AI providers are currently available. Please check the system configuration.';\n        } else {\n          errorContent = `Error: ${error.message}`;\n        }\n      }\n\n      const errorMessage: ChatMessage = {\n        role: 'assistant',\n        content: errorContent,\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const clearChat = () => {\n    setMessages([]);\n    setSessionId(null);\n  };\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n\n  const selectedAgentConfig = availableAgents.find(agent => agent.id === selectedAgent);\n\n  return (\n    <div className=\"flex flex-col h-full max-w-4xl mx-auto\">\n      {/* Header */}\n      <Card className=\"mb-4\">\n        <CardHeader className=\"pb-3\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <CardTitle className=\"flex items-center gap-2\">\n                🤖 AI Agent Chat\n                {selectedAgentConfig && (\n                  <Badge variant=\"secondary\">{selectedAgentConfig.name}</Badge>\n                )}\n              </CardTitle>\n              <CardDescription>\n                {selectedAgentConfig?.description || 'Chat with AI agents using your configured providers'}\n              </CardDescription>\n            </div>\n            <div className=\"flex gap-2\">\n              <Button variant=\"outline\" size=\"sm\" onClick={clearChat}>\n                Clear Chat\n              </Button>\n            </div>\n          </div>\n\n          {/* Agent Selection */}\n          <div className=\"flex flex-wrap gap-2 mt-3\">\n            {availableAgents.map(agent => (\n              <Button\n                key={agent.id}\n                variant={selectedAgent === agent.id ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setSelectedAgent(agent.id)}\n              >\n                {agent.name}\n              </Button>\n            ))}\n          </div>\n\n          {/* Provider Status */}\n          {providers.length > 0 && (\n            <div className=\"flex flex-wrap gap-2 mt-2\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Providers:</span>\n              {providers.map(provider => (\n                <Badge\n                  key={provider.id}\n                  variant={provider.available ? 'default' : 'secondary'}\n                  className=\"text-xs\"\n                >\n                  {provider.name} ({provider.models})\n                </Badge>\n              ))}\n            </div>\n          )}\n        </CardHeader>\n      </Card>\n\n      {/* Chat Messages */}\n      <Card className=\"flex-1 flex flex-col\">\n        <CardContent className=\"flex-1 flex flex-col p-4\">\n          <div className=\"flex-1 overflow-y-auto space-y-4 mb-4\">\n            {messages.length === 0 ? (\n              <div className=\"text-center text-gray-500 dark:text-gray-400 py-8\">\n                <div className=\"text-4xl mb-2\">💬</div>\n                <p>Start a conversation with the AI agent!</p>\n                {selectedAgentConfig && (\n                  <div className=\"mt-4 text-sm\">\n                    <p><strong>Capabilities:</strong></p>\n                    <div className=\"flex flex-wrap gap-1 justify-center mt-2\">\n                      {selectedAgentConfig.capabilities.map(cap => (\n                        <Badge key={cap} variant=\"outline\" className=\"text-xs\">\n                          {cap}\n                        </Badge>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            ) : (\n              messages.map((message, index) => (\n                <div\n                  key={index}\n                  className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\n                >\n                  <div\n                    className={`max-w-[80%] rounded-lg px-4 py-2 ${\n                      message.role === 'user'\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100'\n                    }`}\n                  >\n                    <div className=\"whitespace-pre-wrap\">{message.content}</div>\n                    <div className=\"text-xs opacity-70 mt-1 flex items-center gap-2\">\n                      <span>{message.timestamp.toLocaleTimeString()}</span>\n                      {message.model && (\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          {message.provider}/{message.model.split('/').pop()}\n                        </Badge>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))\n            )}\n\n            {isLoading && (\n              <div className=\"flex justify-start\">\n                <div className=\"bg-gray-100 dark:bg-gray-800 rounded-lg px-4 py-2\">\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"></div>\n                    <span className=\"text-gray-600 dark:text-gray-400\">AI is thinking...</span>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            <div ref={messagesEndRef} />\n          </div>\n\n          {/* Input Area */}\n          <div className=\"flex gap-2\">\n            <textarea\n              value={inputMessage}\n              onChange={(e) => setInputMessage(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Type your message... (Press Enter to send, Shift+Enter for new line)\"\n              className=\"flex-1 min-h-[60px] max-h-[120px] px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white\"\n              disabled={isLoading}\n            />\n            <Button\n              onClick={sendMessage}\n              disabled={!inputMessage.trim() || isLoading}\n              className=\"px-6\"\n            >\n              Send\n            </Button>\n          </div>\n\n          {/* Session Info */}\n          {sessionId && (\n            <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-2\">\n              Session: {sessionId}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAgCe,SAAS,UAAU,EAChC,UAAU,WAAW,EACrB,SAAS,UAAU,EACnB,WAAW,gBAAgB,EAC3B,eAAe,EACA;;IACf,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB,oBAAoB;IAC9E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,iBAAiB,CAAA,GAAA,4RAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,sCAAsC;IACtC,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,6CAA6C;IAC7C,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,WAAW;gBACb;YACF;QACF;8BAAG;QAAC;KAAU;IAEd,wBAAwB;IACxB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG;QAAC;KAAS;IAEb,MAAM,yBAAyB;QAC7B,IAAI;YACF,MAAM,CAAC,WAAW,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAClD,MAAM;gBACN,MAAM;aACP;YAED,IAAI,UAAU,EAAE,EAAE;gBAChB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,UAAU,IAAI;gBACvC,mBAAmB;YACrB;YAEA,IAAI,aAAa,EAAE,EAAE;gBACnB,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,aAAa,IAAI;gBAC7C,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,0CAA0C,EAAE,WAAW;YACrF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,SAAS,IAAI;gBACvC,MAAM,oBAAoB,QAAQ,GAAG,CAAC,CAAC,MAAa,CAAC;wBACnD,GAAG,GAAG;wBACN,WAAW,IAAI;oBACjB,CAAC;gBACD,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,aAAa,IAAI,MAAM,WAAW;QAEvC,MAAM,cAA2B;YAC/B,MAAM;YACN,SAAS;YACT,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,gBAAgB;QAChB,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,SAAS;oBACT,SAAS;oBACT;oBACA,SAAS;wBACP,aAAa;wBACb,WAAW;oBACb;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,+CAA+C;YAC/C,IAAI,KAAK,SAAS,IAAI,CAAC,WAAW;gBAChC,aAAa,KAAK,SAAS;gBAC3B,kBAAkB,KAAK,SAAS;YAClC;YAEA,MAAM,mBAAgC;gBACpC,MAAM;gBACN,SAAS,KAAK,QAAQ;gBACtB,WAAW,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;YACzB;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAiB;QAEjD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YAExC,IAAI,eAAe;YAEnB,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,2BAA2B;oBACpD,eAAe;gBACjB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,oBAAoB;oBACpD,eAAe;gBACjB,OAAO;oBACL,eAAe,CAAC,OAAO,EAAE,MAAM,OAAO,EAAE;gBAC1C;YACF;YAEA,MAAM,eAA4B;gBAChC,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,YAAY;QAChB,YAAY,EAAE;QACd,aAAa;IACf;IAEA,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,sBAAsB,gBAAgB,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAEvE,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,4TAAC,4HAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;;sDACC,4TAAC,4HAAA,CAAA,YAAS;4CAAC,WAAU;;gDAA0B;gDAE5C,qCACC,4TAAC,6HAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAa,oBAAoB,IAAI;;;;;;;;;;;;sDAGxD,4TAAC,4HAAA,CAAA,kBAAe;sDACb,qBAAqB,eAAe;;;;;;;;;;;;8CAGzC,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;kDAAW;;;;;;;;;;;;;;;;;sCAO5D,4TAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAA,sBACnB,4TAAC,8HAAA,CAAA,SAAM;oCAEL,SAAS,kBAAkB,MAAM,EAAE,GAAG,YAAY;oCAClD,MAAK;oCACL,SAAS,IAAM,iBAAiB,MAAM,EAAE;8CAEvC,MAAM,IAAI;mCALN,MAAM,EAAE;;;;;;;;;;wBAWlB,UAAU,MAAM,GAAG,mBAClB,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAK,WAAU;8CAA2C;;;;;;gCAC1D,UAAU,GAAG,CAAC,CAAA,yBACb,4TAAC,6HAAA,CAAA,QAAK;wCAEJ,SAAS,SAAS,SAAS,GAAG,YAAY;wCAC1C,WAAU;;4CAET,SAAS,IAAI;4CAAC;4CAAG,SAAS,MAAM;4CAAC;;uCAJ7B,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAa5B,4TAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,4TAAC,4HAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,4TAAC;4BAAI,WAAU;;gCACZ,SAAS,MAAM,KAAK,kBACnB,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,4TAAC;sDAAE;;;;;;wCACF,qCACC,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;8DAAE,cAAA,4TAAC;kEAAO;;;;;;;;;;;8DACX,4TAAC;oDAAI,WAAU;8DACZ,oBAAoB,YAAY,CAAC,GAAG,CAAC,CAAA,oBACpC,4TAAC,6HAAA,CAAA,QAAK;4DAAW,SAAQ;4DAAU,WAAU;sEAC1C;2DADS;;;;;;;;;;;;;;;;;;;;;2CAStB,SAAS,GAAG,CAAC,CAAC,SAAS,sBACrB,4TAAC;wCAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;kDAE9E,cAAA,4TAAC;4CACC,WAAW,CAAC,iCAAiC,EAC3C,QAAQ,IAAI,KAAK,SACb,2BACA,iEACJ;;8DAEF,4TAAC;oDAAI,WAAU;8DAAuB,QAAQ,OAAO;;;;;;8DACrD,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;sEAAM,QAAQ,SAAS,CAAC,kBAAkB;;;;;;wDAC1C,QAAQ,KAAK,kBACZ,4TAAC,6HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAChC,QAAQ,QAAQ;gEAAC;gEAAE,QAAQ,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG;;;;;;;;;;;;;;;;;;;uCAfnD;;;;;gCAwBV,2BACC,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAI,WAAU;kDACb,cAAA,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAI,WAAU;;;;;;8DACf,4TAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;;;;;8CAM3D,4TAAC;oCAAI,KAAK;;;;;;;;;;;;sCAIZ,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,YAAY;oCACZ,aAAY;oCACZ,WAAU;oCACV,UAAU;;;;;;8CAEZ,4TAAC,8HAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,CAAC,aAAa,IAAI,MAAM;oCAClC,WAAU;8CACX;;;;;;;;;;;;wBAMF,2BACC,4TAAC;4BAAI,WAAU;;gCAAgD;gCACnD;;;;;;;;;;;;;;;;;;;;;;;;AAOxB;GA3TwB;KAAA", "debugId": null}}, {"offset": {"line": 688, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 694, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/agents/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport {\n  <PERSON>,\n  CardContent,\n  CardDescription,\n  CardHeader,\n  CardTitle,\n} from \"@/components/ui/card\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport AgentChat from \"@/components/agent-chat\";\n\nexport default function AgentsPage() {\n  const [activeSession, setActiveSession] = useState<string | null>(null);\n  const [selectedAgent, setSelectedAgent] = useState<string>(\"assistant\");\n\n  const agentTypes = [\n    {\n      id: \"assistant\",\n      name: \"AI Assistant\",\n      icon: \"🤖\",\n      description: \"General-purpose AI assistant for questions and tasks\",\n      capabilities: [\"General Knowledge\", \"Problem Solving\", \"Task Assistance\"],\n      provider: \"OpenRouter (Claude 3.5 Sonnet)\",\n    },\n    {\n      id: \"researcher\",\n      name: \"Research Agent\",\n      icon: \"🔬\",\n      description:\n        \"Specialized in research, analysis, and information synthesis\",\n      capabilities: [\n        \"Literature Review\",\n        \"Data Analysis\",\n        \"Research Methodology\",\n      ],\n      provider: \"Google (Gemini 1.5 Pro)\",\n    },\n    {\n      id: \"coder\",\n      name: \"Code Agent\",\n      icon: \"💻\",\n      description:\n        \"Expert in programming, code review, and software development\",\n      capabilities: [\"Code Generation\", \"Debugging\", \"Architecture Design\"],\n      provider: \"Groq (Llama 3.1 70B)\",\n    },\n    {\n      id: \"analyst\",\n      name: \"Data Analyst\",\n      icon: \"📊\",\n      description: \"Specialized in data analysis, visualization, and insights\",\n      capabilities: [\n        \"Statistical Analysis\",\n        \"Data Visualization\",\n        \"Business Intelligence\",\n      ],\n      provider: \"Cerebras (Llama 3.1 70B)\",\n    },\n    {\n      id: \"creative\",\n      name: \"Creative Agent\",\n      icon: \"🎨\",\n      description: \"Creative writing, brainstorming, and content creation\",\n      capabilities: [\"Creative Writing\", \"Brainstorming\", \"Content Strategy\"],\n      provider: \"Chutes (Claude 3.5 Sonnet)\",\n    },\n    {\n      id: \"multilingual\",\n      name: \"Multilingual Agent\",\n      icon: \"🌍\",\n      description:\n        \"Translation, multilingual content, and cross-cultural communication\",\n      capabilities: [\"Translation\", \"Localization\", \"Cultural Adaptation\"],\n      provider: \"Mistral (Mistral Large)\",\n    },\n  ];\n\n  const handleSessionCreate = (sessionId: string) => {\n    setActiveSession(sessionId);\n  };\n\n  const startNewChat = (agentId: string) => {\n    setSelectedAgent(agentId);\n    setActiveSession(null);\n  };\n\n  return (\n    <div className='min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800'>\n      {/* Header */}\n      <div className='p-8 pb-4'>\n        <div className='max-w-7xl mx-auto'>\n          <div className='text-center mb-8'>\n            <h1 className='text-4xl font-bold text-gray-900 dark:text-white mb-4'>\n              🤖 AI Agent Network\n            </h1>\n            <p className='text-lg text-gray-600 dark:text-gray-300 mb-6'>\n              Chat with specialized AI agents powered by your configured\n              providers\n            </p>\n\n            {/* Provider Status */}\n            <div className='flex justify-center gap-2 mb-6'>\n              <Badge variant='default' className='bg-green-500'>\n                🔗 OpenRouter\n              </Badge>\n              <Badge variant='default' className='bg-blue-500'>\n                🔗 Google Gemini\n              </Badge>\n              <Badge variant='default' className='bg-orange-500'>\n                🔗 Groq\n              </Badge>\n              <Badge variant='default' className='bg-purple-500'>\n                🔗 Cerebras\n              </Badge>\n              <Badge variant='default' className='bg-pink-500'>\n                🔗 Chutes\n              </Badge>\n              <Badge variant='default' className='bg-indigo-500'>\n                🔗 Mistral\n              </Badge>\n            </div>\n          </div>\n\n          {/* Agent Selection Grid */}\n          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-8'>\n            {agentTypes.map((agent) => (\n              <Card\n                key={agent.id}\n                className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${\n                  selectedAgent === agent.id\n                    ? \"ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20\"\n                    : \"hover:shadow-md\"\n                }`}\n                onClick={() => startNewChat(agent.id)}\n              >\n                <CardHeader className='pb-3'>\n                  <div className='text-center'>\n                    <div className='text-3xl mb-2'>{agent.icon}</div>\n                    <CardTitle className='text-lg'>{agent.name}</CardTitle>\n                  </div>\n                </CardHeader>\n                <CardContent className='pt-0'>\n                  <CardDescription className='text-center text-sm mb-3'>\n                    {agent.description}\n                  </CardDescription>\n\n                  <div className='space-y-2'>\n                    <div className='text-xs font-medium text-gray-600 dark:text-gray-400'>\n                      Capabilities:\n                    </div>\n                    <div className='flex flex-wrap gap-1'>\n                      {agent.capabilities.map((cap) => (\n                        <Badge key={cap} variant='outline' className='text-xs'>\n                          {cap}\n                        </Badge>\n                      ))}\n                    </div>\n\n                    <div className='text-xs text-gray-500 dark:text-gray-400 mt-2'>\n                      <strong>Provider:</strong> {agent.provider}\n                    </div>\n                  </div>\n\n                  <Button\n                    className='w-full mt-3'\n                    size='sm'\n                    variant={selectedAgent === agent.id ? \"default\" : \"outline\"}\n                  >\n                    {selectedAgent === agent.id ? \"Selected\" : \"Select Agent\"}\n                  </Button>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Chat Interface */}\n      <div className='px-8 pb-8'>\n        <div className='max-w-7xl mx-auto'>\n          <Card>\n            <CardContent className='p-6' style={{ height: \"600px\" }}>\n              <AgentChat\n                agentId={selectedAgent}\n                userId='user_123'\n                sessionId={activeSession}\n                onSessionCreate={handleSessionCreate}\n              />\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* Features Info */}\n      <div className='px-8 pb-8'>\n        <div className='max-w-7xl mx-auto'>\n          <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>\n            {/* AI Providers */}\n            <Card>\n              <CardHeader>\n                <CardTitle>🔗 AI Provider Network</CardTitle>\n                <CardDescription>\n                  Multiple AI providers for diverse capabilities\n                </CardDescription>\n              </CardHeader>\n              <CardContent className='space-y-4'>\n                <div className='space-y-3'>\n                  <div className='flex items-center gap-3'>\n                    <Badge className='bg-green-500'>OpenRouter</Badge>\n                    <span className='text-sm'>\n                      Access to Claude, GPT-4, Llama models\n                    </span>\n                  </div>\n                  <div className='flex items-center gap-3'>\n                    <Badge className='bg-blue-500'>Google Gemini</Badge>\n                    <span className='text-sm'>\n                      Long-context understanding, multimodal\n                    </span>\n                  </div>\n                  <div className='flex items-center gap-3'>\n                    <Badge className='bg-orange-500'>Groq</Badge>\n                    <span className='text-sm'>\n                      Ultra-fast inference, Llama models\n                    </span>\n                  </div>\n                  <div className='flex items-center gap-3'>\n                    <Badge className='bg-purple-500'>Cerebras</Badge>\n                    <span className='text-sm'>\n                      Fastest AI inference available\n                    </span>\n                  </div>\n                  <div className='flex items-center gap-3'>\n                    <Badge className='bg-pink-500'>Chutes</Badge>\n                    <span className='text-sm'>\n                      Premium model access and routing\n                    </span>\n                  </div>\n                  <div className='flex items-center gap-3'>\n                    <Badge className='bg-indigo-500'>Mistral</Badge>\n                    <span className='text-sm'>\n                      European AI, multilingual, privacy-focused\n                    </span>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Features */}\n            <Card>\n              <CardHeader>\n                <CardTitle>✨ Advanced Features</CardTitle>\n                <CardDescription>\n                  Intelligent agent system with specialized capabilities\n                </CardDescription>\n              </CardHeader>\n              <CardContent className='space-y-4'>\n                <div className='space-y-3'>\n                  <div className='flex items-start gap-3'>\n                    <div className='w-2 h-2 bg-blue-500 rounded-full mt-2'></div>\n                    <div>\n                      <p className='font-medium'>Specialized Agents</p>\n                      <p className='text-sm text-gray-600 dark:text-gray-300'>\n                        Each agent optimized for specific tasks and use cases\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className='flex items-start gap-3'>\n                    <div className='w-2 h-2 bg-green-500 rounded-full mt-2'></div>\n                    <div>\n                      <p className='font-medium'>Provider Optimization</p>\n                      <p className='text-sm text-gray-600 dark:text-gray-300'>\n                        Automatic selection of best provider for each task\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className='flex items-start gap-3'>\n                    <div className='w-2 h-2 bg-purple-500 rounded-full mt-2'></div>\n                    <div>\n                      <p className='font-medium'>Session Management</p>\n                      <p className='text-sm text-gray-600 dark:text-gray-300'>\n                        Persistent conversations with context awareness\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className='flex items-start gap-3'>\n                    <div className='w-2 h-2 bg-orange-500 rounded-full mt-2'></div>\n                    <div>\n                      <p className='font-medium'>Real-time Responses</p>\n                      <p className='text-sm text-gray-600 dark:text-gray-300'>\n                        Fast, streaming responses with usage tracking\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;;;AAZA;;;;;;AAce,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,cAAc;gBAAC;gBAAqB;gBAAmB;aAAkB;YACzE,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aACE;YACF,cAAc;gBACZ;gBACA;gBACA;aACD;YACD,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YAC<PERSON>,MAAM;YAC<PERSON>,aACE;YACF,cAAc;gBAAC;gBAAmB;gBAAa;aAAsB;YACrE,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,cAAc;gBACZ;gBACA;gBACA;aACD;YACD,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,cAAc;gBAAC;gBAAoB;gBAAiB;aAAmB;YACvE,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aACE;YACF,cAAc;gBAAC;gBAAe;gBAAgB;aAAsB;YACpE,UAAU;QACZ;KACD;IAED,MAAM,sBAAsB,CAAC;QAC3B,iBAAiB;IACnB;IAEA,MAAM,eAAe,CAAC;QACpB,iBAAiB;QACjB,iBAAiB;IACnB;IAEA,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAGtE,4TAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAM7D,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAe;;;;;;sDAGlD,4TAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAc;;;;;;sDAGjD,4TAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAgB;;;;;;sDAGnD,4TAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAgB;;;;;;sDAGnD,4TAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAc;;;;;;sDAGjD,4TAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAOvD,4TAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,sBACf,4TAAC,4HAAA,CAAA,OAAI;oCAEH,WAAW,CAAC,2DAA2D,EACrE,kBAAkB,MAAM,EAAE,GACtB,wDACA,mBACJ;oCACF,SAAS,IAAM,aAAa,MAAM,EAAE;;sDAEpC,4TAAC,4HAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;kEAAiB,MAAM,IAAI;;;;;;kEAC1C,4TAAC,4HAAA,CAAA,YAAS;wDAAC,WAAU;kEAAW,MAAM,IAAI;;;;;;;;;;;;;;;;;sDAG9C,4TAAC,4HAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,4TAAC,4HAAA,CAAA,kBAAe;oDAAC,WAAU;8DACxB,MAAM,WAAW;;;;;;8DAGpB,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAI,WAAU;sEAAuD;;;;;;sEAGtE,4TAAC;4DAAI,WAAU;sEACZ,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC,oBACvB,4TAAC,6HAAA,CAAA,QAAK;oEAAW,SAAQ;oEAAU,WAAU;8EAC1C;mEADS;;;;;;;;;;sEAMhB,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;8EAAO;;;;;;gEAAkB;gEAAE,MAAM,QAAQ;;;;;;;;;;;;;8DAI9C,4TAAC,8HAAA,CAAA,SAAM;oDACL,WAAU;oDACV,MAAK;oDACL,SAAS,kBAAkB,MAAM,EAAE,GAAG,YAAY;8DAEjD,kBAAkB,MAAM,EAAE,GAAG,aAAa;;;;;;;;;;;;;mCAzC1C,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;0BAmDvB,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,4TAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;4BAAM,OAAO;gCAAE,QAAQ;4BAAQ;sCACpD,cAAA,4TAAC,+HAAA,CAAA,UAAS;gCACR,SAAS;gCACT,QAAO;gCACP,WAAW;gCACX,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3B,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAI,WAAU;;0CAEb,4TAAC,4HAAA,CAAA,OAAI;;kDACH,4TAAC,4HAAA,CAAA,aAAU;;0DACT,4TAAC,4HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,4TAAC,4HAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,4TAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,6HAAA,CAAA,QAAK;4DAAC,WAAU;sEAAe;;;;;;sEAChC,4TAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAI5B,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,6HAAA,CAAA,QAAK;4DAAC,WAAU;sEAAc;;;;;;sEAC/B,4TAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAI5B,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,6HAAA,CAAA,QAAK;4DAAC,WAAU;sEAAgB;;;;;;sEACjC,4TAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAI5B,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,6HAAA,CAAA,QAAK;4DAAC,WAAU;sEAAgB;;;;;;sEACjC,4TAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAI5B,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,6HAAA,CAAA,QAAK;4DAAC,WAAU;sEAAc;;;;;;sEAC/B,4TAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAI5B,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,6HAAA,CAAA,QAAK;4DAAC,WAAU;sEAAgB;;;;;;sEACjC,4TAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASlC,4TAAC,4HAAA,CAAA,OAAI;;kDACH,4TAAC,4HAAA,CAAA,aAAU;;0DACT,4TAAC,4HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,4TAAC,4HAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,4TAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAI,WAAU;;;;;;sEACf,4TAAC;;8EACC,4TAAC;oEAAE,WAAU;8EAAc;;;;;;8EAC3B,4TAAC;oEAAE,WAAU;8EAA2C;;;;;;;;;;;;;;;;;;8DAM5D,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAI,WAAU;;;;;;sEACf,4TAAC;;8EACC,4TAAC;oEAAE,WAAU;8EAAc;;;;;;8EAC3B,4TAAC;oEAAE,WAAU;8EAA2C;;;;;;;;;;;;;;;;;;8DAM5D,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAI,WAAU;;;;;;sEACf,4TAAC;;8EACC,4TAAC;oEAAE,WAAU;8EAAc;;;;;;8EAC3B,4TAAC;oEAAE,WAAU;8EAA2C;;;;;;;;;;;;;;;;;;8DAM5D,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAI,WAAU;;;;;;sEACf,4TAAC;;8EACC,4TAAC;oEAAE,WAAU;8EAAc;;;;;;8EAC3B,4TAAC;oEAAE,WAAU;8EAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa9E;GArSwB;KAAA", "debugId": null}}, {"offset": {"line": 1509, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}