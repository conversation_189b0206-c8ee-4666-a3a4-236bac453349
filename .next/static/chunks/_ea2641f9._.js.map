{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={`rounded-lg border bg-card text-card-foreground shadow-sm ${className || ''}`}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={`flex flex-col space-y-1.5 p-6 ${className || ''}`} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={`text-2xl font-semibold leading-none tracking-tight ${className || ''}`}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p ref={ref} className={`text-sm text-muted-foreground ${className || ''}`} {...props} />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={`p-6 pt-0 ${className || ''}`} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={`flex items-center p-6 pt-0 ${className || ''}`} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;;AAEA,MAAM,qBAAO,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAC,yDAAyD,EAAE,aAAa,IAAI;QACvF,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAC,8BAA8B,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;;AAEzF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAC,mDAAmD,EAAE,aAAa,IAAI;QACjF,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAE,KAAK;QAAK,WAAW,CAAC,8BAA8B,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;;AAEvF,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAC,SAAS,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;;AAEpE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAC,2BAA2B,EAAE,aAAa,IAAI;QAAG,GAAG,KAAK;;;;;;;AAEtF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/lib/knowledge-graph-data.ts"], "sourcesContent": ["// Sample data generator for 3D Knowledge Graph\n\nimport type { GraphData, GraphNode, GraphEdge } from '@/components/knowledge-graph-3d';\n\n// Generate sample knowledge graph data\nexport function generateKnowledgeGraphData(): GraphData {\n  const nodes: GraphNode[] = [\n    // Core concepts\n    {\n      id: 'ai-concept',\n      label: 'Artificial Intelligence',\n      type: 'concept',\n      position: [0, 0, 0],\n      importance: 3,\n      description: 'The simulation of human intelligence in machines',\n      properties: {\n        field: 'Computer Science',\n        established: '1956',\n        applications: 'Machine Learning, NLP, Computer Vision'\n      }\n    },\n    {\n      id: 'ml-concept',\n      label: 'Machine Learning',\n      type: 'concept',\n      position: [2, 1, 0],\n      importance: 2.5,\n      description: 'A subset of AI that enables machines to learn from data',\n      properties: {\n        types: 'Supervised, Unsupervised, Reinforcement',\n        algorithms: 'Neural Networks, Decision Trees, SVM'\n      }\n    },\n    {\n      id: 'nlp-concept',\n      label: 'Natural Language Processing',\n      type: 'concept',\n      position: [-2, 1, 0],\n      importance: 2,\n      description: 'AI field focused on interaction between computers and human language',\n      properties: {\n        tasks: 'Translation, Sentiment Analysis, Text Generation',\n        models: 'Transformers, BERT, GPT'\n      }\n    },\n    \n    // Entities (specific implementations)\n    {\n      id: 'gpt-entity',\n      label: 'GPT-4',\n      type: 'entity',\n      position: [-3, 2, 1],\n      importance: 2.5,\n      description: 'Large language model developed by OpenAI',\n      properties: {\n        developer: 'OpenAI',\n        parameters: '~1.76 trillion',\n        release: '2023'\n      }\n    },\n    {\n      id: 'bert-entity',\n      label: 'BERT',\n      type: 'entity',\n      position: [-1, 2, 1],\n      importance: 2,\n      description: 'Bidirectional Encoder Representations from Transformers',\n      properties: {\n        developer: 'Google',\n        architecture: 'Transformer Encoder',\n        use_case: 'Understanding'\n      }\n    },\n    {\n      id: 'tensorflow-entity',\n      label: 'TensorFlow',\n      type: 'entity',\n      position: [3, 2, 1],\n      importance: 2,\n      description: 'Open-source machine learning framework',\n      properties: {\n        developer: 'Google',\n        language: 'Python, C++',\n        type: 'Framework'\n      }\n    },\n    \n    // Documents\n    {\n      id: 'attention-paper',\n      label: 'Attention Is All You Need',\n      type: 'document',\n      position: [0, 3, 2],\n      importance: 3,\n      description: 'Seminal paper introducing the Transformer architecture',\n      properties: {\n        authors: 'Vaswani et al.',\n        year: '2017',\n        venue: 'NIPS',\n        citations: '50000+'\n      }\n    },\n    {\n      id: 'bert-paper',\n      label: 'BERT Paper',\n      type: 'document',\n      position: [-2, 3, 2],\n      importance: 2.5,\n      description: 'Paper introducing BERT model',\n      properties: {\n        authors: 'Devlin et al.',\n        year: '2018',\n        venue: 'NAACL'\n      }\n    },\n    {\n      id: 'gpt-paper',\n      label: 'GPT Paper',\n      type: 'document',\n      position: [-4, 3, 2],\n      importance: 2,\n      description: 'Original GPT paper by OpenAI',\n      properties: {\n        authors: 'Radford et al.',\n        year: '2018',\n        focus: 'Unsupervised Language Understanding'\n      }\n    },\n    \n    // Agents\n    {\n      id: 'chatbot-agent',\n      label: 'ChatBot Agent',\n      type: 'agent',\n      position: [1, -1, 1],\n      importance: 1.5,\n      description: 'AI agent for conversational interactions',\n      properties: {\n        capabilities: 'Chat, Q&A, Task Assistance',\n        model: 'GPT-4',\n        status: 'Active'\n      }\n    },\n    {\n      id: 'research-agent',\n      label: 'Research Agent',\n      type: 'agent',\n      position: [3, -1, 1],\n      importance: 1.5,\n      description: 'AI agent specialized in research tasks',\n      properties: {\n        capabilities: 'Literature Review, Data Analysis',\n        model: 'Custom',\n        status: 'Active'\n      }\n    },\n    {\n      id: 'code-agent',\n      label: 'Code Agent',\n      type: 'agent',\n      position: [-1, -1, 1],\n      importance: 1.5,\n      description: 'AI agent for code generation and review',\n      properties: {\n        capabilities: 'Code Generation, Debugging, Review',\n        model: 'CodeT5',\n        status: 'Active'\n      }\n    },\n    \n    // Users\n    {\n      id: 'researcher-user',\n      label: 'AI Researcher',\n      type: 'user',\n      position: [0, -2, 0],\n      importance: 1,\n      description: 'Research scientist working on AI',\n      properties: {\n        role: 'Senior Researcher',\n        institution: 'Tech University',\n        focus: 'Deep Learning'\n      }\n    },\n    {\n      id: 'developer-user',\n      label: 'ML Engineer',\n      type: 'user',\n      position: [2, -2, 0],\n      importance: 1,\n      description: 'Software engineer specializing in ML',\n      properties: {\n        role: 'ML Engineer',\n        company: 'AI Startup',\n        experience: '5 years'\n      }\n    },\n    {\n      id: 'student-user',\n      label: 'CS Student',\n      type: 'user',\n      position: [-2, -2, 0],\n      importance: 0.8,\n      description: 'Computer science student learning AI',\n      properties: {\n        level: 'Graduate',\n        university: 'State University',\n        focus: 'Machine Learning'\n      }\n    }\n  ];\n\n  const edges: GraphEdge[] = [\n    // Concept relationships\n    { id: 'ai-ml', source: 'ai-concept', target: 'ml-concept', type: 'contains', weight: 3 },\n    { id: 'ai-nlp', source: 'ai-concept', target: 'nlp-concept', type: 'contains', weight: 2.5 },\n    { id: 'ml-nlp', source: 'ml-concept', target: 'nlp-concept', type: 'related', weight: 2 },\n    \n    // Entity to concept relationships\n    { id: 'gpt-nlp', source: 'gpt-entity', target: 'nlp-concept', type: 'uses', weight: 3 },\n    { id: 'bert-nlp', source: 'bert-entity', target: 'nlp-concept', type: 'uses', weight: 2.5 },\n    { id: 'tensorflow-ml', source: 'tensorflow-entity', target: 'ml-concept', type: 'uses', weight: 2.5 },\n    \n    // Document relationships\n    { id: 'attention-nlp', source: 'attention-paper', target: 'nlp-concept', type: 'related', weight: 3 },\n    { id: 'attention-ml', source: 'attention-paper', target: 'ml-concept', type: 'related', weight: 2.5 },\n    { id: 'bert-paper-bert', source: 'bert-paper', target: 'bert-entity', type: 'created', weight: 3 },\n    { id: 'gpt-paper-gpt', source: 'gpt-paper', target: 'gpt-entity', type: 'created', weight: 3 },\n    { id: 'attention-bert', source: 'attention-paper', target: 'bert-entity', type: 'related', weight: 2 },\n    { id: 'attention-gpt', source: 'attention-paper', target: 'gpt-entity', type: 'related', weight: 2 },\n    \n    // Agent relationships\n    { id: 'chatbot-gpt', source: 'chatbot-agent', target: 'gpt-entity', type: 'uses', weight: 2.5 },\n    { id: 'chatbot-nlp', source: 'chatbot-agent', target: 'nlp-concept', type: 'uses', weight: 2 },\n    { id: 'research-ml', source: 'research-agent', target: 'ml-concept', type: 'uses', weight: 2 },\n    { id: 'code-tensorflow', source: 'code-agent', target: 'tensorflow-entity', type: 'uses', weight: 2 },\n    \n    // User relationships\n    { id: 'researcher-ai', source: 'researcher-user', target: 'ai-concept', type: 'related', weight: 2.5 },\n    { id: 'researcher-papers', source: 'researcher-user', target: 'attention-paper', type: 'related', weight: 2 },\n    { id: 'researcher-bert-paper', source: 'researcher-user', target: 'bert-paper', type: 'related', weight: 1.5 },\n    { id: 'developer-tensorflow', source: 'developer-user', target: 'tensorflow-entity', type: 'uses', weight: 2.5 },\n    { id: 'developer-ml', source: 'developer-user', target: 'ml-concept', type: 'uses', weight: 2 },\n    { id: 'student-ai', source: 'student-user', target: 'ai-concept', type: 'related', weight: 1.5 },\n    { id: 'student-ml', source: 'student-user', target: 'ml-concept', type: 'related', weight: 2 },\n    \n    // User-agent interactions\n    { id: 'researcher-research-agent', source: 'researcher-user', target: 'research-agent', type: 'uses', weight: 2 },\n    { id: 'developer-code-agent', source: 'developer-user', target: 'code-agent', type: 'uses', weight: 2.5 },\n    { id: 'student-chatbot', source: 'student-user', target: 'chatbot-agent', type: 'uses', weight: 2 },\n    \n    // Cross-entity relationships\n    { id: 'gpt-bert', source: 'gpt-entity', target: 'bert-entity', type: 'related', weight: 1.5 },\n    { id: 'research-chatbot', source: 'research-agent', target: 'chatbot-agent', type: 'related', weight: 1 },\n    { id: 'code-research', source: 'code-agent', target: 'research-agent', type: 'related', weight: 1 }\n  ];\n\n  return { nodes, edges };\n}\n\n// Generate a simpler knowledge graph for testing\nexport function generateSimpleKnowledgeGraph(): GraphData {\n  const nodes: GraphNode[] = [\n    {\n      id: 'ai',\n      label: 'AI',\n      type: 'concept',\n      position: [0, 0, 0],\n      importance: 3,\n      description: 'Artificial Intelligence'\n    },\n    {\n      id: 'ml',\n      label: 'Machine Learning',\n      type: 'concept',\n      position: [2, 0, 0],\n      importance: 2,\n      description: 'Machine Learning subset of AI'\n    },\n    {\n      id: 'gpt',\n      label: 'GPT-4',\n      type: 'entity',\n      position: [1, 2, 0],\n      importance: 2,\n      description: 'Large Language Model'\n    },\n    {\n      id: 'user',\n      label: 'User',\n      type: 'user',\n      position: [0, -2, 0],\n      importance: 1,\n      description: 'System User'\n    },\n    {\n      id: 'agent',\n      label: 'AI Agent',\n      type: 'agent',\n      position: [-1, 1, 0],\n      importance: 1.5,\n      description: 'AI Assistant Agent'\n    }\n  ];\n\n  const edges: GraphEdge[] = [\n    { id: 'ai-ml', source: 'ai', target: 'ml', type: 'contains', weight: 2 },\n    { id: 'ml-gpt', source: 'ml', target: 'gpt', type: 'uses', weight: 2 },\n    { id: 'user-agent', source: 'user', target: 'agent', type: 'uses', weight: 2 },\n    { id: 'agent-gpt', source: 'agent', target: 'gpt', type: 'uses', weight: 2 },\n    { id: 'user-ai', source: 'user', target: 'ai', type: 'related', weight: 1 }\n  ];\n\n  return { nodes, edges };\n}\n\n// Generate knowledge graph from existing system data\nexport function generateKnowledgeGraphFromSystem(\n  users: any[] = [],\n  agents: any[] = [],\n  documents: any[] = [],\n  knowledgeBases: any[] = []\n): GraphData {\n  const nodes: GraphNode[] = [];\n  const edges: GraphEdge[] = [];\n\n  // Add knowledge bases as concept nodes\n  knowledgeBases.forEach((kb, index) => {\n    nodes.push({\n      id: `kb-${kb.id}`,\n      label: kb.name,\n      type: 'concept',\n      position: [index * 3 - knowledgeBases.length * 1.5, 0, 0],\n      importance: 2,\n      description: kb.description || 'Knowledge Base',\n      properties: {\n        owner: kb.ownerId,\n        created: kb.createdAt,\n        documents: documents.filter(d => d.knowledgeBaseId === kb.id).length\n      }\n    });\n  });\n\n  // Add documents as document nodes\n  documents.forEach((doc, index) => {\n    const angle = (index / documents.length) * Math.PI * 2;\n    const radius = 4;\n    nodes.push({\n      id: `doc-${doc.id}`,\n      label: doc.title,\n      type: 'document',\n      position: [\n        radius * Math.cos(angle),\n        2,\n        radius * Math.sin(angle)\n      ],\n      importance: 1.5,\n      description: doc.content?.substring(0, 100) + '...',\n      properties: {\n        type: doc.contentType,\n        created: doc.createdAt,\n        knowledgeBase: doc.knowledgeBaseId\n      }\n    });\n\n    // Connect documents to their knowledge bases\n    edges.push({\n      id: `kb-doc-${doc.id}`,\n      source: `kb-${doc.knowledgeBaseId}`,\n      target: `doc-${doc.id}`,\n      type: 'contains',\n      weight: 2\n    });\n  });\n\n  // Add agents as agent nodes\n  agents.forEach((agent, index) => {\n    const angle = (index / agents.length) * Math.PI * 2;\n    const radius = 3;\n    nodes.push({\n      id: `agent-${agent.id}`,\n      label: agent.name,\n      type: 'agent',\n      position: [\n        radius * Math.cos(angle),\n        -2,\n        radius * Math.sin(angle)\n      ],\n      importance: 2,\n      description: agent.description || 'AI Agent',\n      properties: {\n        type: agent.type,\n        capabilities: agent.capabilities?.join(', '),\n        owner: agent.ownerId\n      }\n    });\n\n    // Connect agents to their knowledge bases\n    if (agent.knowledgeBaseId) {\n      edges.push({\n        id: `agent-kb-${agent.id}`,\n        source: `agent-${agent.id}`,\n        target: `kb-${agent.knowledgeBaseId}`,\n        type: 'uses',\n        weight: 2\n      });\n    }\n  });\n\n  // Add users as user nodes\n  users.forEach((user, index) => {\n    nodes.push({\n      id: `user-${user.id}`,\n      label: user.name,\n      type: 'user',\n      position: [index * 2 - users.length, -4, 0],\n      importance: 1,\n      description: `User: ${user.email}`,\n      properties: {\n        email: user.email,\n        role: user.role,\n        created: user.createdAt\n      }\n    });\n\n    // Connect users to their knowledge bases\n    knowledgeBases\n      .filter(kb => kb.ownerId === user.id)\n      .forEach(kb => {\n        edges.push({\n          id: `user-kb-${user.id}-${kb.id}`,\n          source: `user-${user.id}`,\n          target: `kb-${kb.id}`,\n          type: 'created',\n          weight: 2\n        });\n      });\n\n    // Connect users to their agents\n    agents\n      .filter(agent => agent.ownerId === user.id)\n      .forEach(agent => {\n        edges.push({\n          id: `user-agent-${user.id}-${agent.id}`,\n          source: `user-${user.id}`,\n          target: `agent-${agent.id}`,\n          type: 'created',\n          weight: 2\n        });\n      });\n  });\n\n  return { nodes, edges };\n}\n"], "names": [], "mappings": "AAAA,+CAA+C;;;;;;AAKxC,SAAS;IACd,MAAM,QAAqB;QACzB,gBAAgB;QAChB;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG;gBAAG;aAAE;YACnB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,aAAa;gBACb,cAAc;YAChB;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG;gBAAG;aAAE;YACnB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,YAAY;YACd;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC,CAAC;gBAAG;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,sCAAsC;QACtC;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC,CAAC;gBAAG;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,WAAW;gBACX,YAAY;gBACZ,SAAS;YACX;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC,CAAC;gBAAG;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,WAAW;gBACX,cAAc;gBACd,UAAU;YACZ;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG;gBAAG;aAAE;YACnB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,WAAW;gBACX,UAAU;gBACV,MAAM;YACR;QACF;QAEA,YAAY;QACZ;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG;gBAAG;aAAE;YACnB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,SAAS;gBACT,MAAM;gBACN,OAAO;gBACP,WAAW;YACb;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC,CAAC;gBAAG;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,SAAS;gBACT,MAAM;gBACN,OAAO;YACT;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC,CAAC;gBAAG;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,SAAS;gBACT,MAAM;gBACN,OAAO;YACT;QACF;QAEA,SAAS;QACT;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG,CAAC;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,cAAc;gBACd,OAAO;gBACP,QAAQ;YACV;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG,CAAC;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,cAAc;gBACd,OAAO;gBACP,QAAQ;YACV;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC,CAAC;gBAAG,CAAC;gBAAG;aAAE;YACrB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,cAAc;gBACd,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,QAAQ;QACR;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG,CAAC;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,MAAM;gBACN,aAAa;gBACb,OAAO;YACT;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG,CAAC;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,MAAM;gBACN,SAAS;gBACT,YAAY;YACd;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC,CAAC;gBAAG,CAAC;gBAAG;aAAE;YACrB,YAAY;YACZ,aAAa;YACb,YAAY;gBACV,OAAO;gBACP,YAAY;gBACZ,OAAO;YACT;QACF;KACD;IAED,MAAM,QAAqB;QACzB,wBAAwB;QACxB;YAAE,IAAI;YAAS,QAAQ;YAAc,QAAQ;YAAc,MAAM;YAAY,QAAQ;QAAE;QACvF;YAAE,IAAI;YAAU,QAAQ;YAAc,QAAQ;YAAe,MAAM;YAAY,QAAQ;QAAI;QAC3F;YAAE,IAAI;YAAU,QAAQ;YAAc,QAAQ;YAAe,MAAM;YAAW,QAAQ;QAAE;QAExF,kCAAkC;QAClC;YAAE,IAAI;YAAW,QAAQ;YAAc,QAAQ;YAAe,MAAM;YAAQ,QAAQ;QAAE;QACtF;YAAE,IAAI;YAAY,QAAQ;YAAe,QAAQ;YAAe,MAAM;YAAQ,QAAQ;QAAI;QAC1F;YAAE,IAAI;YAAiB,QAAQ;YAAqB,QAAQ;YAAc,MAAM;YAAQ,QAAQ;QAAI;QAEpG,yBAAyB;QACzB;YAAE,IAAI;YAAiB,QAAQ;YAAmB,QAAQ;YAAe,MAAM;YAAW,QAAQ;QAAE;QACpG;YAAE,IAAI;YAAgB,QAAQ;YAAmB,QAAQ;YAAc,MAAM;YAAW,QAAQ;QAAI;QACpG;YAAE,IAAI;YAAmB,QAAQ;YAAc,QAAQ;YAAe,MAAM;YAAW,QAAQ;QAAE;QACjG;YAAE,IAAI;YAAiB,QAAQ;YAAa,QAAQ;YAAc,MAAM;YAAW,QAAQ;QAAE;QAC7F;YAAE,IAAI;YAAkB,QAAQ;YAAmB,QAAQ;YAAe,MAAM;YAAW,QAAQ;QAAE;QACrG;YAAE,IAAI;YAAiB,QAAQ;YAAmB,QAAQ;YAAc,MAAM;YAAW,QAAQ;QAAE;QAEnG,sBAAsB;QACtB;YAAE,IAAI;YAAe,QAAQ;YAAiB,QAAQ;YAAc,MAAM;YAAQ,QAAQ;QAAI;QAC9F;YAAE,IAAI;YAAe,QAAQ;YAAiB,QAAQ;YAAe,MAAM;YAAQ,QAAQ;QAAE;QAC7F;YAAE,IAAI;YAAe,QAAQ;YAAkB,QAAQ;YAAc,MAAM;YAAQ,QAAQ;QAAE;QAC7F;YAAE,IAAI;YAAmB,QAAQ;YAAc,QAAQ;YAAqB,MAAM;YAAQ,QAAQ;QAAE;QAEpG,qBAAqB;QACrB;YAAE,IAAI;YAAiB,QAAQ;YAAmB,QAAQ;YAAc,MAAM;YAAW,QAAQ;QAAI;QACrG;YAAE,IAAI;YAAqB,QAAQ;YAAmB,QAAQ;YAAmB,MAAM;YAAW,QAAQ;QAAE;QAC5G;YAAE,IAAI;YAAyB,QAAQ;YAAmB,QAAQ;YAAc,MAAM;YAAW,QAAQ;QAAI;QAC7G;YAAE,IAAI;YAAwB,QAAQ;YAAkB,QAAQ;YAAqB,MAAM;YAAQ,QAAQ;QAAI;QAC/G;YAAE,IAAI;YAAgB,QAAQ;YAAkB,QAAQ;YAAc,MAAM;YAAQ,QAAQ;QAAE;QAC9F;YAAE,IAAI;YAAc,QAAQ;YAAgB,QAAQ;YAAc,MAAM;YAAW,QAAQ;QAAI;QAC/F;YAAE,IAAI;YAAc,QAAQ;YAAgB,QAAQ;YAAc,MAAM;YAAW,QAAQ;QAAE;QAE7F,0BAA0B;QAC1B;YAAE,IAAI;YAA6B,QAAQ;YAAmB,QAAQ;YAAkB,MAAM;YAAQ,QAAQ;QAAE;QAChH;YAAE,IAAI;YAAwB,QAAQ;YAAkB,QAAQ;YAAc,MAAM;YAAQ,QAAQ;QAAI;QACxG;YAAE,IAAI;YAAmB,QAAQ;YAAgB,QAAQ;YAAiB,MAAM;YAAQ,QAAQ;QAAE;QAElG,6BAA6B;QAC7B;YAAE,IAAI;YAAY,QAAQ;YAAc,QAAQ;YAAe,MAAM;YAAW,QAAQ;QAAI;QAC5F;YAAE,IAAI;YAAoB,QAAQ;YAAkB,QAAQ;YAAiB,MAAM;YAAW,QAAQ;QAAE;QACxG;YAAE,IAAI;YAAiB,QAAQ;YAAc,QAAQ;YAAkB,MAAM;YAAW,QAAQ;QAAE;KACnG;IAED,OAAO;QAAE;QAAO;IAAM;AACxB;AAGO,SAAS;IACd,MAAM,QAAqB;QACzB;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG;gBAAG;aAAE;YACnB,YAAY;YACZ,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG;gBAAG;aAAE;YACnB,YAAY;YACZ,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG;gBAAG;aAAE;YACnB,YAAY;YACZ,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC;gBAAG,CAAC;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;gBAAC,CAAC;gBAAG;gBAAG;aAAE;YACpB,YAAY;YACZ,aAAa;QACf;KACD;IAED,MAAM,QAAqB;QACzB;YAAE,IAAI;YAAS,QAAQ;YAAM,QAAQ;YAAM,MAAM;YAAY,QAAQ;QAAE;QACvE;YAAE,IAAI;YAAU,QAAQ;YAAM,QAAQ;YAAO,MAAM;YAAQ,QAAQ;QAAE;QACrE;YAAE,IAAI;YAAc,QAAQ;YAAQ,QAAQ;YAAS,MAAM;YAAQ,QAAQ;QAAE;QAC7E;YAAE,IAAI;YAAa,QAAQ;YAAS,QAAQ;YAAO,MAAM;YAAQ,QAAQ;QAAE;QAC3E;YAAE,IAAI;YAAW,QAAQ;YAAQ,QAAQ;YAAM,MAAM;YAAW,QAAQ;QAAE;KAC3E;IAED,OAAO;QAAE;QAAO;IAAM;AACxB;AAGO,SAAS,iCACd,QAAe,EAAE,EACjB,SAAgB,EAAE,EAClB,YAAmB,EAAE,EACrB,iBAAwB,EAAE;IAE1B,MAAM,QAAqB,EAAE;IAC7B,MAAM,QAAqB,EAAE;IAE7B,uCAAuC;IACvC,eAAe,OAAO,CAAC,CAAC,IAAI;QAC1B,MAAM,IAAI,CAAC;YACT,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACjB,OAAO,GAAG,IAAI;YACd,MAAM;YACN,UAAU;gBAAC,QAAQ,IAAI,eAAe,MAAM,GAAG;gBAAK;gBAAG;aAAE;YACzD,YAAY;YACZ,aAAa,GAAG,WAAW,IAAI;YAC/B,YAAY;gBACV,OAAO,GAAG,OAAO;gBACjB,SAAS,GAAG,SAAS;gBACrB,WAAW,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,KAAK,GAAG,EAAE,EAAE,MAAM;YACtE;QACF;IACF;IAEA,kCAAkC;IAClC,UAAU,OAAO,CAAC,CAAC,KAAK;QACtB,MAAM,QAAQ,AAAC,QAAQ,UAAU,MAAM,GAAI,KAAK,EAAE,GAAG;QACrD,MAAM,SAAS;QACf,MAAM,IAAI,CAAC;YACT,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;YACnB,OAAO,IAAI,KAAK;YAChB,MAAM;YACN,UAAU;gBACR,SAAS,KAAK,GAAG,CAAC;gBAClB;gBACA,SAAS,KAAK,GAAG,CAAC;aACnB;YACD,YAAY;YACZ,aAAa,IAAI,OAAO,EAAE,UAAU,GAAG,OAAO;YAC9C,YAAY;gBACV,MAAM,IAAI,WAAW;gBACrB,SAAS,IAAI,SAAS;gBACtB,eAAe,IAAI,eAAe;YACpC;QACF;QAEA,6CAA6C;QAC7C,MAAM,IAAI,CAAC;YACT,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE;YACtB,QAAQ,CAAC,GAAG,EAAE,IAAI,eAAe,EAAE;YACnC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;YACvB,MAAM;YACN,QAAQ;QACV;IACF;IAEA,4BAA4B;IAC5B,OAAO,OAAO,CAAC,CAAC,OAAO;QACrB,MAAM,QAAQ,AAAC,QAAQ,OAAO,MAAM,GAAI,KAAK,EAAE,GAAG;QAClD,MAAM,SAAS;QACf,MAAM,IAAI,CAAC;YACT,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;YACvB,OAAO,MAAM,IAAI;YACjB,MAAM;YACN,UAAU;gBACR,SAAS,KAAK,GAAG,CAAC;gBAClB,CAAC;gBACD,SAAS,KAAK,GAAG,CAAC;aACnB;YACD,YAAY;YACZ,aAAa,MAAM,WAAW,IAAI;YAClC,YAAY;gBACV,MAAM,MAAM,IAAI;gBAChB,cAAc,MAAM,YAAY,EAAE,KAAK;gBACvC,OAAO,MAAM,OAAO;YACtB;QACF;QAEA,0CAA0C;QAC1C,IAAI,MAAM,eAAe,EAAE;YACzB,MAAM,IAAI,CAAC;gBACT,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE;gBAC1B,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;gBAC3B,QAAQ,CAAC,GAAG,EAAE,MAAM,eAAe,EAAE;gBACrC,MAAM;gBACN,QAAQ;YACV;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,MAAM,IAAI,CAAC;YACT,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YACrB,OAAO,KAAK,IAAI;YAChB,MAAM;YACN,UAAU;gBAAC,QAAQ,IAAI,MAAM,MAAM;gBAAE,CAAC;gBAAG;aAAE;YAC3C,YAAY;YACZ,aAAa,CAAC,MAAM,EAAE,KAAK,KAAK,EAAE;YAClC,YAAY;gBACV,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI;gBACf,SAAS,KAAK,SAAS;YACzB;QACF;QAEA,yCAAyC;QACzC,eACG,MAAM,CAAC,CAAA,KAAM,GAAG,OAAO,KAAK,KAAK,EAAE,EACnC,OAAO,CAAC,CAAA;YACP,MAAM,IAAI,CAAC;gBACT,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;gBACjC,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBACzB,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBACrB,MAAM;gBACN,QAAQ;YACV;QACF;QAEF,gCAAgC;QAChC,OACG,MAAM,CAAC,CAAA,QAAS,MAAM,OAAO,KAAK,KAAK,EAAE,EACzC,OAAO,CAAC,CAAA;YACP,MAAM,IAAI,CAAC;gBACT,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;gBACvC,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBACzB,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;gBAC3B,MAAM;gBACN,QAAQ;YACV;QACF;IACJ;IAEA,OAAO;QAAE;QAAO;IAAM;AACxB", "debugId": null}}, {"offset": {"line": 831, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 837, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/lib/api-client.ts"], "sourcesContent": ["// API client for Knowledge OS backend\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\n\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  error?: string;\n}\n\nexport interface User {\n  id: string;\n  name: string;\n  email: string;\n  profileImage?: string;\n  role: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface KnowledgeBase {\n  id: string;\n  name: string;\n  description?: string;\n  createdAt: string;\n  updatedAt: string;\n  ownerId: string;\n  teamId?: string;\n}\n\nexport interface Document {\n  id: string;\n  title: string;\n  content: string;\n  contentType: string;\n  vectorId?: string;\n  createdAt: string;\n  updatedAt: string;\n  knowledgeBaseId: string;\n  metadata?: Record<string, any>;\n}\n\nexport interface Agent {\n  id: string;\n  name: string;\n  description?: string;\n  type: string;\n  capabilities: string[];\n  config: Record<string, any>;\n  createdAt: string;\n  updatedAt: string;\n  ownerId: string;\n  knowledgeBaseId?: string;\n}\n\nexport interface Query {\n  id: string;\n  content: string;\n  response: string;\n  metadata?: Record<string, any>;\n  createdAt: string;\n  sessionId: string;\n  userId: string;\n}\n\nexport interface SearchResult {\n  id: string;\n  content: string;\n  similarity: number;\n  document: {\n    id: string;\n    title: string;\n    knowledgeBaseId: string;\n  };\n}\n\nclass ApiClient {\n  private baseUrl: string;\n  private authToken?: string;\n\n  constructor(baseUrl: string = API_BASE_URL) {\n    this.baseUrl = baseUrl;\n  }\n\n  setAuthToken(token: string) {\n    this.authToken = token;\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    const url = `${this.baseUrl}${endpoint}`;\n    const headers: HeadersInit = {\n      'Content-Type': 'application/json',\n      ...options.headers,\n    };\n\n    if (this.authToken) {\n      headers.Authorization = `Bearer ${this.authToken}`;\n    }\n\n    try {\n      const response = await fetch(url, {\n        ...options,\n        headers,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        return {\n          success: false,\n          error: errorData.detail || `HTTP ${response.status}: ${response.statusText}`,\n        };\n      }\n\n      const data = await response.json();\n      return {\n        success: true,\n        data,\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error occurred',\n      };\n    }\n  }\n\n  // Health check\n  async healthCheck(): Promise<ApiResponse<{ status: string; timestamp: string }>> {\n    return this.request('/health');\n  }\n\n  // User endpoints\n  async createUser(userData: {\n    name: string;\n    email: string;\n    profileImage?: string;\n    role?: string;\n  }): Promise<ApiResponse<User>> {\n    return this.request('/api/users', {\n      method: 'POST',\n      body: JSON.stringify(userData),\n    });\n  }\n\n  async getUser(userId: string): Promise<ApiResponse<User>> {\n    return this.request(`/api/users/${userId}`);\n  }\n\n  // Knowledge Base endpoints\n  async createKnowledgeBase(kbData: {\n    name: string;\n    description?: string;\n    teamId?: string;\n  }): Promise<ApiResponse<KnowledgeBase>> {\n    return this.request('/api/knowledge-bases', {\n      method: 'POST',\n      body: JSON.stringify(kbData),\n    });\n  }\n\n  async listKnowledgeBases(): Promise<ApiResponse<KnowledgeBase[]>> {\n    return this.request('/api/knowledge-bases');\n  }\n\n  // Document endpoints\n  async createDocument(docData: {\n    title: string;\n    content: string;\n    contentType?: string;\n    knowledgeBaseId: string;\n    metadata?: Record<string, any>;\n  }): Promise<ApiResponse<Document>> {\n    return this.request('/api/documents', {\n      method: 'POST',\n      body: JSON.stringify(docData),\n    });\n  }\n\n  async listDocuments(knowledgeBaseId?: string): Promise<ApiResponse<Document[]>> {\n    const params = knowledgeBaseId ? `?knowledge_base_id=${knowledgeBaseId}` : '';\n    return this.request(`/api/documents${params}`);\n  }\n\n  // Agent endpoints\n  async createAgent(agentData: {\n    name: string;\n    description?: string;\n    type?: string;\n    capabilities: string[];\n    config: Record<string, any>;\n    knowledgeBaseId?: string;\n  }): Promise<ApiResponse<Agent>> {\n    return this.request('/api/agents', {\n      method: 'POST',\n      body: JSON.stringify(agentData),\n    });\n  }\n\n  async listAgents(): Promise<ApiResponse<Agent[]>> {\n    return this.request('/api/agents');\n  }\n\n  // Search endpoints\n  async semanticSearch(searchData: {\n    query: string;\n    knowledgeBaseId?: string;\n    limit?: number;\n    threshold?: number;\n  }): Promise<ApiResponse<SearchResult[]>> {\n    return this.request('/api/search', {\n      method: 'POST',\n      body: JSON.stringify(searchData),\n    });\n  }\n\n  // Query/Chat endpoints\n  async processQuery(queryData: {\n    content: string;\n    agentId: string;\n    sessionId?: string;\n  }): Promise<ApiResponse<Query>> {\n    return this.request('/api/query', {\n      method: 'POST',\n      body: JSON.stringify(queryData),\n    });\n  }\n}\n\n// Create singleton instance\nexport const apiClient = new ApiClient();\n\n// React hooks for API calls\nexport function useApiClient() {\n  return apiClient;\n}\n\n// Utility functions for common operations\nexport async function withErrorHandling<T>(\n  apiCall: () => Promise<ApiResponse<T>>,\n  onError?: (error: string) => void\n): Promise<T | null> {\n  const response = await apiCall();\n  \n  if (response.success && response.data) {\n    return response.data;\n  } else {\n    const error = response.error || 'Unknown error occurred';\n    if (onError) {\n      onError(error);\n    } else {\n      console.error('API Error:', error);\n    }\n    return null;\n  }\n}\n\n// Mock data for development (when backend is not available)\nexport const mockData = {\n  users: [\n    {\n      id: 'user_1',\n      name: 'John Doe',\n      email: '<EMAIL>',\n      role: 'USER',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    },\n  ],\n  knowledgeBases: [\n    {\n      id: 'kb_1',\n      name: 'Personal Knowledge Base',\n      description: 'My personal collection of knowledge',\n      ownerId: 'user_1',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    },\n  ],\n  agents: [\n    {\n      id: 'agent_1',\n      name: 'AI Assistant',\n      description: 'A helpful AI assistant',\n      type: 'ASSISTANT',\n      capabilities: ['Q&A', 'Research', 'Writing'],\n      config: { model: 'gpt-4', temperature: 0.7 },\n      ownerId: 'user_1',\n      knowledgeBaseId: 'kb_1',\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    },\n  ],\n};\n\n// Development mode flag\nexport const isDevelopmentMode = process.env.NODE_ENV === 'development';\n\n// Mock API client for development\nexport class MockApiClient extends ApiClient {\n  async healthCheck() {\n    return { success: true, data: { status: 'healthy', timestamp: new Date().toISOString() } };\n  }\n\n  async listKnowledgeBases() {\n    return { success: true, data: mockData.knowledgeBases };\n  }\n\n  async listAgents() {\n    return { success: true, data: mockData.agents };\n  }\n\n  async listDocuments() {\n    return { success: true, data: [] };\n  }\n\n  async semanticSearch() {\n    return { success: true, data: [] };\n  }\n}\n\n// Export the appropriate client based on environment\nexport const client = isDevelopmentMode ? new MockApiClient() : apiClient;\n"], "names": [], "mappings": "AAAA,sCAAsC;;;;;;;;;;AA0SL;AAxSjC,MAAM,eAAe,+RAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AA0ExD,MAAM;IACI,QAAgB;IAChB,UAAmB;IAE3B,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,aAAa,KAAa,EAAE;QAC1B,IAAI,CAAC,SAAS,GAAG;IACnB;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QACxC,MAAM,UAAuB;YAC3B,gBAAgB;YAChB,GAAG,QAAQ,OAAO;QACpB;QAEA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,QAAQ,aAAa,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE;QACpD;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,GAAG,OAAO;gBACV;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,OAAO;oBACL,SAAS;oBACT,OAAO,UAAU,MAAM,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;gBAC9E;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBACL,SAAS;gBACT;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,eAAe;IACf,MAAM,cAA2E;QAC/E,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,iBAAiB;IACjB,MAAM,WAAW,QAKhB,EAA8B;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc;YAChC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,QAAQ,MAAc,EAA8B;QACxD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,QAAQ;IAC5C;IAEA,2BAA2B;IAC3B,MAAM,oBAAoB,MAIzB,EAAuC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAC,wBAAwB;YAC1C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,qBAA4D;QAChE,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,qBAAqB;IACrB,MAAM,eAAe,OAMpB,EAAkC;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB;YACpC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,eAAwB,EAAoC;QAC9E,MAAM,SAAS,kBAAkB,CAAC,mBAAmB,EAAE,iBAAiB,GAAG;QAC3E,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,QAAQ;IAC/C;IAEA,kBAAkB;IAClB,MAAM,YAAY,SAOjB,EAA+B;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe;YACjC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,aAA4C;QAChD,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,mBAAmB;IACnB,MAAM,eAAe,UAKpB,EAAwC;QACvC,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe;YACjC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,uBAAuB;IACvB,MAAM,aAAa,SAIlB,EAA+B;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc;YAChC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;AACF;AAGO,MAAM,YAAY,IAAI;AAGtB,SAAS;IACd,OAAO;AACT;AAGO,eAAe,kBACpB,OAAsC,EACtC,OAAiC;IAEjC,MAAM,WAAW,MAAM;IAEvB,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;QACrC,OAAO,SAAS,IAAI;IACtB,OAAO;QACL,MAAM,QAAQ,SAAS,KAAK,IAAI;QAChC,IAAI,SAAS;YACX,QAAQ;QACV,OAAO;YACL,QAAQ,KAAK,CAAC,cAAc;QAC9B;QACA,OAAO;IACT;AACF;AAGO,MAAM,WAAW;IACtB,OAAO;QACL;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,MAAM;YACN,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;KACD;IACD,gBAAgB;QACd;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;KACD;IACD,QAAQ;QACN;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,cAAc;gBAAC;gBAAO;gBAAY;aAAU;YAC5C,QAAQ;gBAAE,OAAO;gBAAS,aAAa;YAAI;YAC3C,SAAS;YACT,iBAAiB;YACjB,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;KACD;AACH;AAGO,MAAM,oBAAoB,oDAAyB;AAGnD,MAAM,sBAAsB;IACjC,MAAM,cAAc;QAClB,OAAO;YAAE,SAAS;YAAM,MAAM;gBAAE,QAAQ;gBAAW,WAAW,IAAI,OAAO,WAAW;YAAG;QAAE;IAC3F;IAEA,MAAM,qBAAqB;QACzB,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,cAAc;QAAC;IACxD;IAEA,MAAM,aAAa;QACjB,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,MAAM;QAAC;IAChD;IAEA,MAAM,gBAAgB;QACpB,OAAO;YAAE,SAAS;YAAM,MAAM,EAAE;QAAC;IACnC;IAEA,MAAM,iBAAiB;QACrB,OAAO;YAAE,SAAS;YAAM,MAAM,EAAE;QAAC;IACnC;AACF;AAGO,MAAM,SAAS,uCAAoB,IAAI", "debugId": null}}, {"offset": {"line": 1052, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1058, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/app/knowledge-graph/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport dynamic from 'next/dynamic';\nimport KnowledgeGraphFallback from '@/components/knowledge-graph-fallback';\n\n// Dynamically import the 3D component to avoid SSR issues\nconst KnowledgeGraph3D = dynamic(() => import('@/components/knowledge-graph-3d'), {\n  ssr: false,\n  loading: () => (\n    <div className=\"flex items-center justify-center h-full\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n        <p className=\"text-gray-600 dark:text-gray-300\">Loading 3D Knowledge Graph...</p>\n      </div>\n    </div>\n  )\n});\nimport type { GraphData } from '@/components/knowledge-graph-3d';\nimport {\n  generateKnowledgeGraphData,\n  generateSimpleKnowledgeGraph,\n  generateKnowledgeGraphFromSystem\n} from '@/lib/knowledge-graph-data';\nimport { mockData } from '@/lib/api-client';\n\nexport default function KnowledgeGraphPage() {\n  const [graphData, setGraphData] = useState<GraphData | null>(null);\n  const [dataSource, setDataSource] = useState<'sample' | 'simple' | 'system'>('sample');\n  const [isLoading, setIsLoading] = useState(false);\n  const [use3D, setUse3D] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Load initial data\n  useEffect(() => {\n    loadGraphData(dataSource);\n  }, [dataSource]);\n\n  const loadGraphData = async (source: 'sample' | 'simple' | 'system') => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      let data: GraphData;\n\n      switch (source) {\n        case 'simple':\n          data = generateSimpleKnowledgeGraph();\n          break;\n        case 'system':\n          // In a real app, you'd fetch this from your API\n          data = generateKnowledgeGraphFromSystem(\n            mockData.users,\n            mockData.agents,\n            [], // documents\n            mockData.knowledgeBases\n          );\n          break;\n        case 'sample':\n        default:\n          data = generateKnowledgeGraphData();\n          break;\n      }\n\n      setGraphData(data);\n    } catch (error) {\n      console.error('Error loading graph data:', error);\n      setError(error instanceof Error ? error.message : 'Failed to load graph data');\n      setUse3D(false); // Fall back to 2D on error\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleDataSourceChange = (source: 'sample' | 'simple' | 'system') => {\n    setDataSource(source);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800\">\n      {/* Header */}\n      <div className=\"p-8 pb-4\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              3D Knowledge Graph Visualization\n            </h1>\n            <p className=\"text-lg text-gray-600 dark:text-gray-300 mb-6\">\n              Explore knowledge relationships in an interactive 3D space\n            </p>\n\n            {/* Data Source Controls */}\n            <div className=\"flex justify-center gap-4 mb-6\">\n              <Button\n                variant={dataSource === 'simple' ? 'default' : 'outline'}\n                onClick={() => handleDataSourceChange('simple')}\n                disabled={isLoading}\n              >\n                Simple Graph\n              </Button>\n              <Button\n                variant={dataSource === 'sample' ? 'default' : 'outline'}\n                onClick={() => handleDataSourceChange('sample')}\n                disabled={isLoading}\n              >\n                AI Knowledge Graph\n              </Button>\n              <Button\n                variant={dataSource === 'system' ? 'default' : 'outline'}\n                onClick={() => handleDataSourceChange('system')}\n                disabled={isLoading}\n              >\n                System Data\n              </Button>\n            </div>\n\n            {/* 3D/2D Toggle */}\n            <div className=\"flex justify-center gap-2 mt-4\">\n              <Button\n                variant={use3D ? 'default' : 'outline'}\n                onClick={() => setUse3D(true)}\n                disabled={isLoading}\n              >\n                3D View\n              </Button>\n              <Button\n                variant={!use3D ? 'default' : 'outline'}\n                onClick={() => setUse3D(false)}\n                disabled={isLoading}\n              >\n                2D View\n              </Button>\n            </div>\n          </div>\n\n          {/* Graph Statistics */}\n          {graphData && (\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n              <Card>\n                <CardHeader className=\"pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Total Nodes</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">{graphData.nodes.length}</div>\n                  <div className=\"text-xs text-gray-500 mt-1\">\n                    Concepts, Entities, Documents, Agents, Users\n                  </div>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader className=\"pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Connections</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">{graphData.edges.length}</div>\n                  <div className=\"text-xs text-gray-500 mt-1\">\n                    Relationships between entities\n                  </div>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader className=\"pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Node Types</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">\n                    {new Set(graphData.nodes.map(n => n.type)).size}\n                  </div>\n                  <div className=\"text-xs text-gray-500 mt-1\">\n                    Different entity categories\n                  </div>\n                </CardContent>\n              </Card>\n\n              <Card>\n                <CardHeader className=\"pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Data Source</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-lg font-semibold capitalize\">{dataSource}</div>\n                  <div className=\"text-xs text-gray-500 mt-1\">\n                    Current visualization data\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* 3D Graph Visualization */}\n      <div className=\"px-8 pb-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <Card className=\"overflow-hidden\">\n            <CardContent className=\"p-0\">\n              <div style={{ height: '700px', width: '100%' }}>\n                {isLoading ? (\n                  <div className=\"flex items-center justify-center h-full\">\n                    <div className=\"text-center\">\n                      <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n                      <p className=\"text-gray-600 dark:text-gray-300\">Loading knowledge graph...</p>\n                    </div>\n                  </div>\n                ) : graphData ? (\n                  <KnowledgeGraph3D data={graphData} />\n                ) : (\n                  <div className=\"flex items-center justify-center h-full\">\n                    <p className=\"text-gray-600 dark:text-gray-300\">No data available</p>\n                  </div>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* Information Panel */}\n      <div className=\"px-8 pb-8\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Features */}\n            <Card>\n              <CardHeader>\n                <CardTitle>3D Visualization Features</CardTitle>\n                <CardDescription>\n                  Interactive features available in the knowledge graph\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-blue-500 rounded-full mt-2\"></div>\n                  <div>\n                    <p className=\"font-medium\">Interactive Navigation</p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                      Orbit, zoom, and pan around the 3D space\n                    </p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full mt-2\"></div>\n                  <div>\n                    <p className=\"font-medium\">Node Selection</p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                      Click nodes to view detailed information\n                    </p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-purple-500 rounded-full mt-2\"></div>\n                  <div>\n                    <p className=\"font-medium\">Dynamic Layouts</p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                      Switch between sphere, cube, random, and force-directed layouts\n                    </p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-orange-500 rounded-full mt-2\"></div>\n                  <div>\n                    <p className=\"font-medium\">Hover Effects</p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                      Smooth animations and visual feedback on interaction\n                    </p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-red-500 rounded-full mt-2\"></div>\n                  <div>\n                    <p className=\"font-medium\">Connection Visualization</p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                      Color-coded edges showing different relationship types\n                    </p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Controls Guide */}\n            <Card>\n              <CardHeader>\n                <CardTitle>How to Use</CardTitle>\n                <CardDescription>\n                  Guide to navigating the 3D knowledge graph\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div>\n                  <h4 className=\"font-medium mb-2\">Mouse Controls</h4>\n                  <ul className=\"space-y-1 text-sm text-gray-600 dark:text-gray-300\">\n                    <li>• <strong>Left Click + Drag:</strong> Rotate the view</li>\n                    <li>• <strong>Right Click + Drag:</strong> Pan the view</li>\n                    <li>• <strong>Scroll Wheel:</strong> Zoom in/out</li>\n                    <li>• <strong>Click Node:</strong> Select and view details</li>\n                    <li>• <strong>Hover Node:</strong> Highlight and preview</li>\n                  </ul>\n                </div>\n\n                <div>\n                  <h4 className=\"font-medium mb-2\">Layout Options</h4>\n                  <ul className=\"space-y-1 text-sm text-gray-600 dark:text-gray-300\">\n                    <li>• <strong>Sphere:</strong> Nodes arranged on sphere surface</li>\n                    <li>• <strong>Cube:</strong> 3D grid arrangement</li>\n                    <li>• <strong>Random:</strong> Scattered positioning</li>\n                    <li>• <strong>Force:</strong> Physics-based layout</li>\n                  </ul>\n                </div>\n\n                <div>\n                  <h4 className=\"font-medium mb-2\">Visual Legend</h4>\n                  <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n                      <span>Concepts</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-3 h-3 bg-blue-500 rounded-full\"></div>\n                      <span>Entities</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n                      <span>Documents</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-3 h-3 bg-purple-500 rounded-full\"></div>\n                      <span>Agents</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-3 h-3 bg-orange-500 rounded-full\"></div>\n                      <span>Users</span>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAgBA;AAKA;;;;AA3BA;;;;;AASA,0DAA0D;AAC1D,MAAM,mBAAmB,CAAA,GAAA,iSAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAC/B,KAAK;IACL,SAAS,kBACP,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;;;;;;kCACf,4TAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;;KANlD;;;AAmBS,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAkC;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,oBAAoB;IACpB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;wCAAE;YACR,cAAc;QAChB;uCAAG;QAAC;KAAW;IAEf,MAAM,gBAAgB,OAAO;QAC3B,aAAa;QACb,SAAS;QAET,IAAI;YACF,IAAI;YAEJ,OAAQ;gBACN,KAAK;oBACH,OAAO,CAAA,GAAA,oIAAA,CAAA,+BAA4B,AAAD;oBAClC;gBACF,KAAK;oBACH,gDAAgD;oBAChD,OAAO,CAAA,GAAA,oIAAA,CAAA,mCAAgC,AAAD,EACpC,uHAAA,CAAA,WAAQ,CAAC,KAAK,EACd,uHAAA,CAAA,WAAQ,CAAC,MAAM,EACf,EAAE,EACF,uHAAA,CAAA,WAAQ,CAAC,cAAc;oBAEzB;gBACF,KAAK;gBACL;oBACE,OAAO,CAAA,GAAA,oIAAA,CAAA,6BAA0B,AAAD;oBAChC;YACJ;YAEA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,SAAS,QAAQ,2BAA2B;QAC9C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,cAAc;IAChB;IAEA,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAGtE,4TAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAK7D,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,eAAe,WAAW,YAAY;4CAC/C,SAAS,IAAM,uBAAuB;4CACtC,UAAU;sDACX;;;;;;sDAGD,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,eAAe,WAAW,YAAY;4CAC/C,SAAS,IAAM,uBAAuB;4CACtC,UAAU;sDACX;;;;;;sDAGD,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,eAAe,WAAW,YAAY;4CAC/C,SAAS,IAAM,uBAAuB;4CACtC,UAAU;sDACX;;;;;;;;;;;;8CAMH,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,QAAQ,YAAY;4CAC7B,SAAS,IAAM,SAAS;4CACxB,UAAU;sDACX;;;;;;sDAGD,4TAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,CAAC,QAAQ,YAAY;4CAC9B,SAAS,IAAM,SAAS;4CACxB,UAAU;sDACX;;;;;;;;;;;;;;;;;;wBAOJ,2BACC,4TAAC;4BAAI,WAAU;;8CACb,4TAAC,4HAAA,CAAA,OAAI;;sDACH,4TAAC,4HAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,4TAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;;;;;;sDAE7C,4TAAC,4HAAA,CAAA,cAAW;;8DACV,4TAAC;oDAAI,WAAU;8DAAsB,UAAU,KAAK,CAAC,MAAM;;;;;;8DAC3D,4TAAC;oDAAI,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAMhD,4TAAC,4HAAA,CAAA,OAAI;;sDACH,4TAAC,4HAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,4TAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;;;;;;sDAE7C,4TAAC,4HAAA,CAAA,cAAW;;8DACV,4TAAC;oDAAI,WAAU;8DAAsB,UAAU,KAAK,CAAC,MAAM;;;;;;8DAC3D,4TAAC;oDAAI,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAMhD,4TAAC,4HAAA,CAAA,OAAI;;sDACH,4TAAC,4HAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,4TAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;;;;;;sDAE7C,4TAAC,4HAAA,CAAA,cAAW;;8DACV,4TAAC;oDAAI,WAAU;8DACZ,IAAI,IAAI,UAAU,KAAK,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG,IAAI;;;;;;8DAEjD,4TAAC;oDAAI,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAMhD,4TAAC,4HAAA,CAAA,OAAI;;sDACH,4TAAC,4HAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,4TAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;;;;;;sDAE7C,4TAAC,4HAAA,CAAA,cAAW;;8DACV,4TAAC;oDAAI,WAAU;8DAAoC;;;;;;8DACnD,4TAAC;oDAAI,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWxD,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC,4HAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,4TAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,4TAAC;gCAAI,OAAO;oCAAE,QAAQ;oCAAS,OAAO;gCAAO;0CAC1C,0BACC,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;;;;;;0DACf,4TAAC;gDAAE,WAAU;0DAAmC;;;;;;;;;;;;;;;;2CAGlD,0BACF,4TAAC;oCAAiB,MAAM;;;;;yDAExB,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU9D,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAI,WAAU;;0CAEb,4TAAC,4HAAA,CAAA,OAAI;;kDACH,4TAAC,4HAAA,CAAA,aAAU;;0DACT,4TAAC,4HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,4TAAC,4HAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,4TAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;;;;;;kEACf,4TAAC;;0EACC,4TAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,4TAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAM5D,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;;;;;;kEACf,4TAAC;;0EACC,4TAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,4TAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAM5D,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;;;;;;kEACf,4TAAC;;0EACC,4TAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,4TAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAM5D,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;;;;;;kEACf,4TAAC;;0EACC,4TAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,4TAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAM5D,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAI,WAAU;;;;;;kEACf,4TAAC;;0EACC,4TAAC;gEAAE,WAAU;0EAAc;;;;;;0EAC3B,4TAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAShE,4TAAC,4HAAA,CAAA,OAAI;;kDACH,4TAAC,4HAAA,CAAA,aAAU;;0DACT,4TAAC,4HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,4TAAC,4HAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,4TAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,4TAAC;;kEACC,4TAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,4TAAC;wDAAG,WAAU;;0EACZ,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAA2B;;;;;;;0EACzC,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAA4B;;;;;;;0EAC1C,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAAsB;;;;;;;0EACpC,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAAoB;;;;;;;0EAClC,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAAoB;;;;;;;;;;;;;;;;;;;0DAItC,4TAAC;;kEACC,4TAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,4TAAC;wDAAG,WAAU;;0EACZ,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAAgB;;;;;;;0EAC9B,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAAc;;;;;;;0EAC5B,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAAgB;;;;;;;0EAC9B,4TAAC;;oEAAG;kFAAE,4TAAC;kFAAO;;;;;;oEAAe;;;;;;;;;;;;;;;;;;;0DAIjC,4TAAC;;kEACC,4TAAC;wDAAG,WAAU;kEAAmB;;;;;;kEACjC,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAU;;;;;;kFACf,4TAAC;kFAAK;;;;;;;;;;;;0EAER,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAU;;;;;;kFACf,4TAAC;kFAAK;;;;;;;;;;;;0EAER,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAU;;;;;;kFACf,4TAAC;kFAAK;;;;;;;;;;;;0EAER,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAU;;;;;;kFACf,4TAAC;kFAAK;;;;;;;;;;;;0EAER,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAU;;;;;;kFACf,4TAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5B;GA/TwB;MAAA", "debugId": null}}, {"offset": {"line": 2221, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}