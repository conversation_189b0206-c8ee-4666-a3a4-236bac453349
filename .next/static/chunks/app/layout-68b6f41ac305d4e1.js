(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{548:e=>{e.exports={style:{fontFamily:"'Geist', '<PERSON>eist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},2193:(e,t,r)=>{"use strict";r.d(t,{$:()=>s});var a=r(1500);let s=r(432).forwardRef((e,t)=>{let{className:r,variant:s="default",size:n="default",...o}=e;return(0,a.jsx)("button",{className:"".concat("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"," ").concat({default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"}[s]," ").concat({default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[n]," ").concat(r||""),ref:t,...o})});s.displayName="Button"},2956:(e,t,r)=>{Promise.resolve().then(r.bind(r,8650)),Promise.resolve().then(r.t.bind(r,548,23)),Promise.resolve().then(r.t.bind(r,5348,23)),Promise.resolve().then(r.t.bind(r,6414,23))},5348:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},6414:()=>{},8650:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var a=r(1500),s=r(4184),n=r.n(s),o=r(9385),i=r(2193);function l(){let e=(0,o.usePathname)();return(0,a.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-8 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,a.jsx)(n(),{href:"/",className:"text-xl font-bold text-gray-900 dark:text-white",children:"AI Agent Hub"}),(0,a.jsx)("div",{className:"flex space-x-4",children:[{href:"/",label:"Dashboard",icon:"\uD83C\uDFE0"},{href:"/network",label:"Network Graph",icon:"\uD83C\uDF10"}].map(t=>(0,a.jsx)(n(),{href:t.href,children:(0,a.jsxs)(i.$,{variant:e===t.href?"default":"ghost",className:"flex items-center gap-2",children:[(0,a.jsx)("span",{children:t.icon}),t.label]})},t.href))})]}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-300",children:"AI Agent Network Visualization"})]})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[455,815,702,358],()=>t(2956)),_N_E=e.O()}]);