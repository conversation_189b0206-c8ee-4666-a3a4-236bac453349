(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{6536:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var a=r(1500),s=r(432),i=r(2285);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:"rounded-lg border bg-card text-card-foreground shadow-sm ".concat(r||""),...s})});n.displayName="Card";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:"flex flex-col space-y-1.5 p-6 ".concat(r||""),...s})});o.displayName="CardHeader";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:"text-2xl font-semibold leading-none tracking-tight ".concat(r||""),...s})});d.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:"text-sm text-muted-foreground ".concat(r||""),...s})});c.displayName="CardDescription";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:"p-6 pt-0 ".concat(r||""),...s})});l.displayName="CardContent";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:"flex items-center p-6 pt-0 ".concat(r||""),...s})});u.displayName="CardFooter";let g=s.forwardRef((e,t)=>{let{className:r,variant:s="default",size:i="default",...n}=e;return(0,a.jsx)("button",{className:"".concat("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"," ").concat({default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"}[s]," ").concat({default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[i]," ").concat(r||""),ref:t,...n})});g.displayName="Button";let m=s.forwardRef((e,t)=>{let{className:r,variant:s="default",...i}=e;return(0,a.jsx)("div",{ref:t,className:"".concat("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"," ").concat({default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}[s]," ").concat(r||""),...i})});function f(e){let{name:t,description:r,capabilities:f,avatarUrl:p,isActive:x,onActivate:h}=e,[b,v]=(0,s.useState)(!1);return(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},whileHover:{scale:1.03},className:"w-full max-w-sm",onMouseEnter:()=>v(!0),onMouseLeave:()=>v(!1),children:(0,a.jsxs)(n,{className:"overflow-hidden border-2 ".concat(x?"border-blue-500":"border-gray-200"," transition-all duration-300"),children:[(0,a.jsxs)(o,{className:"relative p-0",children:[(0,a.jsx)(i.P.div,{className:"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600",initial:{opacity:.7},animate:{opacity:b?.9:.7}}),(0,a.jsxs)("div",{className:"relative p-6 flex items-center gap-4",children:[(0,a.jsx)(i.P.img,{src:p,alt:"".concat(t," avatar"),className:"w-16 h-16 rounded-full border-2 border-white",animate:{rotate:360*!!b},transition:{duration:2}}),(0,a.jsxs)("div",{children:[(0,a.jsx)(d,{className:"text-white",children:t}),(0,a.jsx)(c,{className:"text-white/80",children:"AI Agent"})]})]})]}),(0,a.jsxs)(l,{className:"p-6",children:[(0,a.jsx)("p",{className:"text-gray-700 dark:text-gray-300",children:r}),(0,a.jsx)("div",{className:"mt-4 flex flex-wrap gap-2",children:f.map((e,t)=>(0,a.jsx)(m,{variant:"outline",className:"bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300",children:e},t))})]}),(0,a.jsx)(u,{className:"border-t p-6",children:(0,a.jsx)(g,{onClick:h,className:"w-full ".concat(x?"bg-green-600 hover:bg-green-700":"bg-blue-600 hover:bg-blue-700"),children:x?"Active":"Activate Agent"})})]})})}function p(){var e;let[t,r]=(0,s.useState)(null),i=[{id:"assistant",name:"AI Assistant",description:"A helpful AI assistant that can answer questions, provide information, and help with various tasks.",capabilities:["Q&A","Research","Writing","Analysis"],avatarUrl:"https://images.unsplash.com/photo-1677442136019-21780ecad995?w=100&h=100&fit=crop&crop=face"},{id:"coder",name:"Code Expert",description:"Specialized in programming, code review, debugging, and software development best practices.",capabilities:["Coding","Debugging","Code Review","Architecture"],avatarUrl:"https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=100&h=100&fit=crop&crop=face"},{id:"creative",name:"Creative Writer",description:"Expert in creative writing, storytelling, content creation, and artistic expression.",capabilities:["Writing","Storytelling","Content","Creativity"],avatarUrl:"https://images.unsplash.com/photo-1552058544-f2b08422138a?w=100&h=100&fit=crop&crop=face"}],n=e=>{r(t===e?null:e)};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"AI Agent Dashboard"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"Choose an AI agent to assist you with your tasks"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-center",children:i.map(e=>(0,a.jsx)(f,{name:e.name,description:e.description,capabilities:e.capabilities,avatarUrl:e.avatarUrl,isActive:t===e.id,onActivate:()=>n(e.id)},e.id))}),t&&(0,a.jsx)("div",{className:"mt-12 text-center",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg max-w-md mx-auto",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Agent Activated!"}),(0,a.jsxs)("p",{className:"text-gray-600 dark:text-gray-300",children:[null===(e=i.find(e=>e.id===t))||void 0===e?void 0:e.name," is now ready to assist you."]})]})})]})})}m.displayName="Badge"},9865:(e,t,r)=>{Promise.resolve().then(r.bind(r,6536))}},e=>{var t=t=>e(e.s=t);e.O(0,[285,815,702,358],()=>t(9865)),_N_E=e.O()}]);