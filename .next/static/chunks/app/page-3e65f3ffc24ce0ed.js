(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2193:(e,t,r)=>{"use strict";r.d(t,{$:()=>s});var a=r(1500);let s=r(432).forwardRef((e,t)=>{let{className:r,variant:s="default",size:i="default",...n}=e;return(0,a.jsx)("button",{className:"".concat("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"," ").concat({default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"}[s]," ").concat({default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}[i]," ").concat(r||""),ref:t,...n})});s.displayName="Button"},2596:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(1500),s=r(432),i=r(2285),n=r(5131),o=r(2193),d=r(7978);function c(e){let{name:t,description:r,capabilities:c,avatarUrl:l,isActive:u,onActivate:g}=e,[m,p]=(0,s.useState)(!1);return(0,a.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},whileHover:{scale:1.03},className:"w-full max-w-sm",onMouseEnter:()=>p(!0),onMouseLeave:()=>p(!1),children:(0,a.jsxs)(n.Zp,{className:"overflow-hidden border-2 ".concat(u?"border-blue-500":"border-gray-200"," transition-all duration-300"),children:[(0,a.jsxs)(n.aR,{className:"relative p-0",children:[(0,a.jsx)(i.P.div,{className:"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600",initial:{opacity:.7},animate:{opacity:m?.9:.7}}),(0,a.jsxs)("div",{className:"relative p-6 flex items-center gap-4",children:[(0,a.jsx)(i.P.img,{src:l,alt:"".concat(t," avatar"),className:"w-16 h-16 rounded-full border-2 border-white",animate:{rotate:360*!!m},transition:{duration:2}}),(0,a.jsxs)("div",{children:[(0,a.jsx)(n.ZB,{className:"text-white",children:t}),(0,a.jsx)(n.BT,{className:"text-white/80",children:"AI Agent"})]})]})]}),(0,a.jsxs)(n.Wu,{className:"p-6",children:[(0,a.jsx)("p",{className:"text-gray-700 dark:text-gray-300",children:r}),(0,a.jsx)("div",{className:"mt-4 flex flex-wrap gap-2",children:c.map((e,t)=>(0,a.jsx)(d.E,{variant:"outline",className:"bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300",children:e},t))})]}),(0,a.jsx)(n.wL,{className:"border-t p-6",children:(0,a.jsx)(o.$,{onClick:g,className:"w-full ".concat(u?"bg-green-600 hover:bg-green-700":"bg-blue-600 hover:bg-blue-700"),children:u?"Active":"Activate Agent"})})]})})}function l(){var e;let[t,r]=(0,s.useState)(null),i=[{id:"assistant",name:"AI Assistant",description:"A helpful AI assistant that can answer questions, provide information, and help with various tasks.",capabilities:["Q&A","Research","Writing","Analysis"],avatarUrl:"https://images.unsplash.com/photo-1677442136019-21780ecad995?w=100&h=100&fit=crop&crop=face"},{id:"coder",name:"Code Expert",description:"Specialized in programming, code review, debugging, and software development best practices.",capabilities:["Coding","Debugging","Code Review","Architecture"],avatarUrl:"https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=100&h=100&fit=crop&crop=face"},{id:"creative",name:"Creative Writer",description:"Expert in creative writing, storytelling, content creation, and artistic expression.",capabilities:["Writing","Storytelling","Content","Creativity"],avatarUrl:"https://images.unsplash.com/photo-1552058544-f2b08422138a?w=100&h=100&fit=crop&crop=face"}],n=e=>{r(t===e?null:e)};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"AI Agent Dashboard"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"Choose an AI agent to assist you with your tasks"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-center",children:i.map(e=>(0,a.jsx)(c,{name:e.name,description:e.description,capabilities:e.capabilities,avatarUrl:e.avatarUrl,isActive:t===e.id,onActivate:()=>n(e.id)},e.id))}),t&&(0,a.jsx)("div",{className:"mt-12 text-center",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg max-w-md mx-auto",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Agent Activated!"}),(0,a.jsxs)("p",{className:"text-gray-600 dark:text-gray-300",children:[null===(e=i.find(e=>e.id===t))||void 0===e?void 0:e.name," is now ready to assist you."]})]})})]})})}},5131:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>n,wL:()=>l});var a=r(1500),s=r(432);let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:"rounded-lg border bg-card text-card-foreground shadow-sm ".concat(r||""),...s})});i.displayName="Card";let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:"flex flex-col space-y-1.5 p-6 ".concat(r||""),...s})});n.displayName="CardHeader";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:"text-2xl font-semibold leading-none tracking-tight ".concat(r||""),...s})});o.displayName="CardTitle";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:"text-sm text-muted-foreground ".concat(r||""),...s})});d.displayName="CardDescription";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:"p-6 pt-0 ".concat(r||""),...s})});c.displayName="CardContent";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:"flex items-center p-6 pt-0 ".concat(r||""),...s})});l.displayName="CardFooter"},7978:(e,t,r)=>{"use strict";r.d(t,{E:()=>s});var a=r(1500);let s=r(432).forwardRef((e,t)=>{let{className:r,variant:s="default",...i}=e;return(0,a.jsx)("div",{ref:t,className:"".concat("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"," ").concat({default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}[s]," ").concat(r||""),...i})});s.displayName="Badge"},9865:(e,t,r)=>{Promise.resolve().then(r.bind(r,2596))}},e=>{var t=t=>e(e.s=t);e.O(0,[285,815,702,358],()=>t(9865)),_N_E=e.O()}]);