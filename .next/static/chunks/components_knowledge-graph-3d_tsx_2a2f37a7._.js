(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/components_knowledge-graph-3d_tsx_2a2f37a7._.js", {

"[project]/components/knowledge-graph-3d.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/b8192_three_build_three_core_2ba35a5b.js",
  "static/chunks/b8192_three_build_three_module_57e10d21.js",
  "static/chunks/b8192_three_build_three_module_5e7378ed.js",
  "static/chunks/09190_react-reconciler_995c47f5._.js",
  "static/chunks/2b4b7_@react-three_fiber_dist_6e0dc785._.js",
  "static/chunks/bda96_@react-spring_core_dist_react-spring_core_modern_mjs_39016453._.js",
  "static/chunks/node_modules__pnpm_d1b8a558._.js",
  "static/chunks/components_knowledge-graph-3d_tsx_bc19b31c._.js",
  "static/chunks/components_knowledge-graph-3d_tsx_3596e079._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/knowledge-graph-3d.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);