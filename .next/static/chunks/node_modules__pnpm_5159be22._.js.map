{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-dispatch%403.0.1/node_modules/d3-dispatch/src/dispatch.js"], "sourcesContent": ["var noop = {value: () => {}};\n\nfunction dispatch() {\n  for (var i = 0, n = arguments.length, _ = {}, t; i < n; ++i) {\n    if (!(t = arguments[i] + \"\") || (t in _) || /[\\s.]/.test(t)) throw new Error(\"illegal type: \" + t);\n    _[t] = [];\n  }\n  return new Dispatch(_);\n}\n\nfunction Dispatch(_) {\n  this._ = _;\n}\n\nfunction parseTypenames(typenames, types) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    if (t && !types.hasOwnProperty(t)) throw new Error(\"unknown type: \" + t);\n    return {type: t, name: name};\n  });\n}\n\nDispatch.prototype = dispatch.prototype = {\n  constructor: Dispatch,\n  on: function(typename, callback) {\n    var _ = this._,\n        T = parseTypenames(typename + \"\", _),\n        t,\n        i = -1,\n        n = T.length;\n\n    // If no callback was specified, return the callback of the given type and name.\n    if (arguments.length < 2) {\n      while (++i < n) if ((t = (typename = T[i]).type) && (t = get(_[t], typename.name))) return t;\n      return;\n    }\n\n    // If a type was specified, set the callback for the given type and name.\n    // Otherwise, if a null callback was specified, remove callbacks of the given name.\n    if (callback != null && typeof callback !== \"function\") throw new Error(\"invalid callback: \" + callback);\n    while (++i < n) {\n      if (t = (typename = T[i]).type) _[t] = set(_[t], typename.name, callback);\n      else if (callback == null) for (t in _) _[t] = set(_[t], typename.name, null);\n    }\n\n    return this;\n  },\n  copy: function() {\n    var copy = {}, _ = this._;\n    for (var t in _) copy[t] = _[t].slice();\n    return new Dispatch(copy);\n  },\n  call: function(type, that) {\n    if ((n = arguments.length - 2) > 0) for (var args = new Array(n), i = 0, n, t; i < n; ++i) args[i] = arguments[i + 2];\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  },\n  apply: function(type, that, args) {\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (var t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  }\n};\n\nfunction get(type, name) {\n  for (var i = 0, n = type.length, c; i < n; ++i) {\n    if ((c = type[i]).name === name) {\n      return c.value;\n    }\n  }\n}\n\nfunction set(type, name, callback) {\n  for (var i = 0, n = type.length; i < n; ++i) {\n    if (type[i].name === name) {\n      type[i] = noop, type = type.slice(0, i).concat(type.slice(i + 1));\n      break;\n    }\n  }\n  if (callback != null) type.push({name: name, value: callback});\n  return type;\n}\n\nexport default dispatch;\n"], "names": [], "mappings": ";;;AAAA,IAAI,OAAO;IAAC,OAAO,KAAO;AAAC;AAE3B,SAAS;IACP,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,EAAG;QAC3D,IAAI,CAAC,CAAC,IAAI,SAAS,CAAC,EAAE,GAAG,EAAE,KAAM,KAAK,KAAM,QAAQ,IAAI,CAAC,IAAI,MAAM,IAAI,MAAM,mBAAmB;QAChG,CAAC,CAAC,EAAE,GAAG,EAAE;IACX;IACA,OAAO,IAAI,SAAS;AACtB;AAEA,SAAS,SAAS,CAAC;IACjB,IAAI,CAAC,CAAC,GAAG;AACX;AAEA,SAAS,eAAe,SAAS,EAAE,KAAK;IACtC,OAAO,UAAU,IAAI,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,SAAS,CAAC;QACnD,IAAI,OAAO,IAAI,IAAI,EAAE,OAAO,CAAC;QAC7B,IAAI,KAAK,GAAG,OAAO,EAAE,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE,KAAK,CAAC,GAAG;QAClD,IAAI,KAAK,CAAC,MAAM,cAAc,CAAC,IAAI,MAAM,IAAI,MAAM,mBAAmB;QACtE,OAAO;YAAC,MAAM;YAAG,MAAM;QAAI;IAC7B;AACF;AAEA,SAAS,SAAS,GAAG,SAAS,SAAS,GAAG;IACxC,aAAa;IACb,IAAI,SAAS,QAAQ,EAAE,QAAQ;QAC7B,IAAI,IAAI,IAAI,CAAC,CAAC,EACV,IAAI,eAAe,WAAW,IAAI,IAClC,GACA,IAAI,CAAC,GACL,IAAI,EAAE,MAAM;QAEhB,gFAAgF;QAChF,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,MAAO,EAAE,IAAI,EAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,EAAE,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,OAAO;YAC3F;QACF;QAEA,yEAAyE;QACzE,mFAAmF;QACnF,IAAI,YAAY,QAAQ,OAAO,aAAa,YAAY,MAAM,IAAI,MAAM,uBAAuB;QAC/F,MAAO,EAAE,IAAI,EAAG;YACd,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,SAAS,IAAI,EAAE;iBAC3D,IAAI,YAAY,MAAM,IAAK,KAAK,EAAG,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,EAAE,SAAS,IAAI,EAAE;QAC1E;QAEA,OAAO,IAAI;IACb;IACA,MAAM;QACJ,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;QACzB,IAAK,IAAI,KAAK,EAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK;QACrC,OAAO,IAAI,SAAS;IACtB;IACA,MAAM,SAAS,IAAI,EAAE,IAAI;QACvB,IAAI,CAAC,IAAI,UAAU,MAAM,GAAG,CAAC,IAAI,GAAG,IAAK,IAAI,OAAO,IAAI,MAAM,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,EAAE,EAAG,IAAI,CAAC,EAAE,GAAG,SAAS,CAAC,IAAI,EAAE;QACrH,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,MAAM,IAAI,MAAM,mBAAmB;QACrE,IAAK,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM;IACjF;IACA,OAAO,SAAS,IAAI,EAAE,IAAI,EAAE,IAAI;QAC9B,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,MAAM,IAAI,MAAM,mBAAmB;QACrE,IAAK,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM;IACrF;AACF;AAEA,SAAS,IAAI,IAAI,EAAE,IAAI;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,GAAG,IAAI,GAAG,EAAE,EAAG;QAC9C,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM;YAC/B,OAAO,EAAE,KAAK;QAChB;IACF;AACF;AAEA,SAAS,IAAI,IAAI,EAAE,IAAI,EAAE,QAAQ;IAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG;QAC3C,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,MAAM;YACzB,IAAI,CAAC,EAAE,GAAG,MAAM,OAAO,KAAK,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC,IAAI;YAC9D;QACF;IACF;IACA,IAAI,YAAY,MAAM,KAAK,IAAI,CAAC;QAAC,MAAM;QAAM,OAAO;IAAQ;IAC5D,OAAO;AACT;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/index.js"], "sourcesContent": ["import selection_select from \"./select.js\";\nimport selection_selectAll from \"./selectAll.js\";\nimport selection_selectChild from \"./selectChild.js\";\nimport selection_selectChildren from \"./selectChildren.js\";\nimport selection_filter from \"./filter.js\";\nimport selection_data from \"./data.js\";\nimport selection_enter from \"./enter.js\";\nimport selection_exit from \"./exit.js\";\nimport selection_join from \"./join.js\";\nimport selection_merge from \"./merge.js\";\nimport selection_order from \"./order.js\";\nimport selection_sort from \"./sort.js\";\nimport selection_call from \"./call.js\";\nimport selection_nodes from \"./nodes.js\";\nimport selection_node from \"./node.js\";\nimport selection_size from \"./size.js\";\nimport selection_empty from \"./empty.js\";\nimport selection_each from \"./each.js\";\nimport selection_attr from \"./attr.js\";\nimport selection_style from \"./style.js\";\nimport selection_property from \"./property.js\";\nimport selection_classed from \"./classed.js\";\nimport selection_text from \"./text.js\";\nimport selection_html from \"./html.js\";\nimport selection_raise from \"./raise.js\";\nimport selection_lower from \"./lower.js\";\nimport selection_append from \"./append.js\";\nimport selection_insert from \"./insert.js\";\nimport selection_remove from \"./remove.js\";\nimport selection_clone from \"./clone.js\";\nimport selection_datum from \"./datum.js\";\nimport selection_on from \"./on.js\";\nimport selection_dispatch from \"./dispatch.js\";\nimport selection_iterator from \"./iterator.js\";\n\nexport var root = [null];\n\nexport function Selection(groups, parents) {\n  this._groups = groups;\n  this._parents = parents;\n}\n\nfunction selection() {\n  return new Selection([[document.documentElement]], root);\n}\n\nfunction selection_selection() {\n  return this;\n}\n\nSelection.prototype = selection.prototype = {\n  constructor: Selection,\n  select: selection_select,\n  selectAll: selection_selectAll,\n  selectChild: selection_selectChild,\n  selectChildren: selection_selectChildren,\n  filter: selection_filter,\n  data: selection_data,\n  enter: selection_enter,\n  exit: selection_exit,\n  join: selection_join,\n  merge: selection_merge,\n  selection: selection_selection,\n  order: selection_order,\n  sort: selection_sort,\n  call: selection_call,\n  nodes: selection_nodes,\n  node: selection_node,\n  size: selection_size,\n  empty: selection_empty,\n  each: selection_each,\n  attr: selection_attr,\n  style: selection_style,\n  property: selection_property,\n  classed: selection_classed,\n  text: selection_text,\n  html: selection_html,\n  raise: selection_raise,\n  lower: selection_lower,\n  append: selection_append,\n  insert: selection_insert,\n  remove: selection_remove,\n  clone: selection_clone,\n  datum: selection_datum,\n  on: selection_on,\n  dispatch: selection_dispatch,\n  [Symbol.iterator]: selection_iterator\n};\n\nexport default selection;\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,IAAI,OAAO;IAAC;CAAK;AAEjB,SAAS,UAAU,MAAM,EAAE,OAAO;IACvC,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,QAAQ,GAAG;AAClB;AAEA,SAAS;IACP,OAAO,IAAI,UAAU;QAAC;YAAC,SAAS,eAAe;SAAC;KAAC,EAAE;AACrD;AAEA,SAAS;IACP,OAAO,IAAI;AACb;AAEA,UAAU,SAAS,GAAG,UAAU,SAAS,GAAG;IAC1C,aAAa;IACb,QAAQ,4NAAA,CAAA,UAAgB;IACxB,WAAW,+NAAA,CAAA,UAAmB;IAC9B,aAAa,iOAAA,CAAA,UAAqB;IAClC,gBAAgB,oOAAA,CAAA,UAAwB;IACxC,QAAQ,4NAAA,CAAA,UAAgB;IACxB,MAAM,0NAAA,CAAA,UAAc;IACpB,OAAO,2NAAA,CAAA,UAAe;IACtB,MAAM,0NAAA,CAAA,UAAc;IACpB,MAAM,0NAAA,CAAA,UAAc;IACpB,OAAO,2NAAA,CAAA,UAAe;IACtB,WAAW;IACX,OAAO,2NAAA,CAAA,UAAe;IACtB,MAAM,0NAAA,CAAA,UAAc;IACpB,MAAM,0NAAA,CAAA,UAAc;IACpB,OAAO,2NAAA,CAAA,UAAe;IACtB,MAAM,0NAAA,CAAA,UAAc;IACpB,MAAM,0NAAA,CAAA,UAAc;IACpB,OAAO,2NAAA,CAAA,UAAe;IACtB,MAAM,0NAAA,CAAA,UAAc;IACpB,MAAM,0NAAA,CAAA,UAAc;IACpB,OAAO,2NAAA,CAAA,UAAe;IACtB,UAAU,8NAAA,CAAA,UAAkB;IAC5B,SAAS,6NAAA,CAAA,UAAiB;IAC1B,MAAM,0NAAA,CAAA,UAAc;IACpB,MAAM,0NAAA,CAAA,UAAc;IACpB,OAAO,2NAAA,CAAA,UAAe;IACtB,OAAO,2NAAA,CAAA,UAAe;IACtB,QAAQ,4NAAA,CAAA,UAAgB;IACxB,QAAQ,4NAAA,CAAA,UAAgB;IACxB,QAAQ,4NAAA,CAAA,UAAgB;IACxB,OAAO,2NAAA,CAAA,UAAe;IACtB,OAAO,2NAAA,CAAA,UAAe;IACtB,IAAI,wNAAA,CAAA,UAAY;IAChB,UAAU,8NAAA,CAAA,UAAkB;IAC5B,CAAC,OAAO,QAAQ,CAAC,EAAE,8NAAA,CAAA,UAAkB;AACvC;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selector.js"], "sourcesContent": ["function none() {}\n\nexport default function(selector) {\n  return selector == null ? none : function() {\n    return this.querySelector(selector);\n  };\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,QAAQ;AAEF,wCAAS,QAAQ;IAC9B,OAAO,YAAY,OAAO,OAAO;QAC/B,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/select.js"], "sourcesContent": ["import {Selection} from \"./index.js\";\nimport selector from \"../selector.js\";\n\nexport default function(select) {\n  if (typeof select !== \"function\") select = selector(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n      }\n    }\n  }\n\n  return new Selection(subgroups, this._parents);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEe,wCAAS,MAAM;IAC5B,IAAI,OAAO,WAAW,YAAY,SAAS,CAAA,GAAA,iNAAA,CAAA,UAAQ,AAAD,EAAE;IAEpD,IAAK,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,OAAO,MAAM,EAAE,YAAY,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QAC9F,IAAK,IAAI,QAAQ,MAAM,CAAC,EAAE,EAAE,IAAI,MAAM,MAAM,EAAE,WAAW,SAAS,CAAC,EAAE,GAAG,IAAI,MAAM,IAAI,MAAM,SAAS,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACtH,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE,KAAK,CAAC,UAAU,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,GAAG,MAAM,GAAG;gBAC/E,IAAI,cAAc,MAAM,QAAQ,QAAQ,GAAG,KAAK,QAAQ;gBACxD,QAAQ,CAAC,EAAE,GAAG;YAChB;QACF;IACF;IAEA,OAAO,IAAI,2NAAA,CAAA,YAAS,CAAC,WAAW,IAAI,CAAC,QAAQ;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/array.js"], "sourcesContent": ["// Given something array like (or null), returns something that is strictly an\n// array. This is used to ensure that array-like objects passed to d3.selectAll\n// or selection.selectAll are converted into proper arrays when creating a\n// selection; we don’t ever want to create a selection backed by a live\n// HTMLCollection or NodeList. However, note that selection.selectAll will use a\n// static NodeList as a group, since it safely derived from querySelectorAll.\nexport default function array(x) {\n  return x == null ? [] : Array.isArray(x) ? x : Array.from(x);\n}\n"], "names": [], "mappings": "AAAA,8EAA8E;AAC9E,+EAA+E;AAC/E,0EAA0E;AAC1E,uEAAuE;AACvE,gFAAgF;AAChF,6EAA6E;;;;AAC9D,SAAS,MAAM,CAAC;IAC7B,OAAO,KAAK,OAAO,EAAE,GAAG,MAAM,OAAO,CAAC,KAAK,IAAI,MAAM,IAAI,CAAC;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selectorAll.js"], "sourcesContent": ["function empty() {\n  return [];\n}\n\nexport default function(selector) {\n  return selector == null ? empty : function() {\n    return this.querySelectorAll(selector);\n  };\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS;IACP,OAAO,EAAE;AACX;AAEe,wCAAS,QAAQ;IAC9B,OAAO,YAAY,OAAO,QAAQ;QAChC,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/selectAll.js"], "sourcesContent": ["import {Selection} from \"./index.js\";\nimport array from \"../array.js\";\nimport selectorAll from \"../selectorAll.js\";\n\nfunction arrayAll(select) {\n  return function() {\n    return array(select.apply(this, arguments));\n  };\n}\n\nexport default function(select) {\n  if (typeof select === \"function\") select = arrayAll(select);\n  else select = selectorAll(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        subgroups.push(select.call(node, node.__data__, i, group));\n        parents.push(node);\n      }\n    }\n  }\n\n  return new Selection(subgroups, parents);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,SAAS,MAAM;IACtB,OAAO;QACL,OAAO,CAAA,GAAA,8MAAA,CAAA,UAAK,AAAD,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE;IAClC;AACF;AAEe,wCAAS,MAAM;IAC5B,IAAI,OAAO,WAAW,YAAY,SAAS,SAAS;SAC/C,SAAS,CAAA,GAAA,oNAAA,CAAA,UAAW,AAAD,EAAE;IAE1B,IAAK,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,OAAO,MAAM,EAAE,YAAY,EAAE,EAAE,UAAU,EAAE,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QAClG,IAAK,IAAI,QAAQ,MAAM,CAAC,EAAE,EAAE,IAAI,MAAM,MAAM,EAAE,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACrE,IAAI,OAAO,KAAK,CAAC,EAAE,EAAE;gBACnB,UAAU,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,GAAG;gBACnD,QAAQ,IAAI,CAAC;YACf;QACF;IACF;IAEA,OAAO,IAAI,2NAAA,CAAA,YAAS,CAAC,WAAW;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/matcher.js"], "sourcesContent": ["export default function(selector) {\n  return function() {\n    return this.matches(selector);\n  };\n}\n\nexport function childMatcher(selector) {\n  return function(node) {\n    return node.matches(selector);\n  };\n}\n\n"], "names": [], "mappings": ";;;;AAAe,wCAAS,QAAQ;IAC9B,OAAO;QACL,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;AACF;AAEO,SAAS,aAAa,QAAQ;IACnC,OAAO,SAAS,IAAI;QAClB,OAAO,KAAK,OAAO,CAAC;IACtB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/selectChild.js"], "sourcesContent": ["import {childMatcher} from \"../matcher.js\";\n\nvar find = Array.prototype.find;\n\nfunction childFind(match) {\n  return function() {\n    return find.call(this.children, match);\n  };\n}\n\nfunction childFirst() {\n  return this.firstElementChild;\n}\n\nexport default function(match) {\n  return this.select(match == null ? childFirst\n      : childFind(typeof match === \"function\" ? match : childMatcher(match)));\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,IAAI,OAAO,MAAM,SAAS,CAAC,IAAI;AAE/B,SAAS,UAAU,KAAK;IACtB,OAAO;QACL,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;IAClC;AACF;AAEA,SAAS;IACP,OAAO,IAAI,CAAC,iBAAiB;AAC/B;AAEe,wCAAS,KAAK;IAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,OAAO,aAC7B,UAAU,OAAO,UAAU,aAAa,QAAQ,CAAA,GAAA,gNAAA,CAAA,eAAY,AAAD,EAAE;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 384, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/selectChildren.js"], "sourcesContent": ["import {childMatcher} from \"../matcher.js\";\n\nvar filter = Array.prototype.filter;\n\nfunction children() {\n  return Array.from(this.children);\n}\n\nfunction childrenFilter(match) {\n  return function() {\n    return filter.call(this.children, match);\n  };\n}\n\nexport default function(match) {\n  return this.selectAll(match == null ? children\n      : childrenFilter(typeof match === \"function\" ? match : childMatcher(match)));\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,IAAI,SAAS,MAAM,SAAS,CAAC,MAAM;AAEnC,SAAS;IACP,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ;AACjC;AAEA,SAAS,eAAe,KAAK;IAC3B,OAAO;QACL,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;IACpC;AACF;AAEe,wCAAS,KAAK;IAC3B,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,OAAO,WAChC,eAAe,OAAO,UAAU,aAAa,QAAQ,CAAA,GAAA,gNAAA,CAAA,eAAY,AAAD,EAAE;AAC1E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/filter.js"], "sourcesContent": ["import {Selection} from \"./index.js\";\nimport matcher from \"../matcher.js\";\n\nexport default function(match) {\n  if (typeof match !== \"function\") match = matcher(match);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n\n  return new Selection(subgroups, this._parents);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEe,wCAAS,KAAK;IAC3B,IAAI,OAAO,UAAU,YAAY,QAAQ,CAAA,GAAA,gNAAA,CAAA,UAAO,AAAD,EAAE;IAEjD,IAAK,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,OAAO,MAAM,EAAE,YAAY,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QAC9F,IAAK,IAAI,QAAQ,MAAM,CAAC,EAAE,EAAE,IAAI,MAAM,MAAM,EAAE,WAAW,SAAS,CAAC,EAAE,GAAG,EAAE,EAAE,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACnG,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,GAAG,QAAQ;gBAClE,SAAS,IAAI,CAAC;YAChB;QACF;IACF;IAEA,OAAO,IAAI,2NAAA,CAAA,YAAS,CAAC,WAAW,IAAI,CAAC,QAAQ;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/sparse.js"], "sourcesContent": ["export default function(update) {\n  return new Array(update.length);\n}\n"], "names": [], "mappings": ";;;AAAe,wCAAS,MAAM;IAC5B,OAAO,IAAI,MAAM,OAAO,MAAM;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/enter.js"], "sourcesContent": ["import sparse from \"./sparse.js\";\nimport {Selection} from \"./index.js\";\n\nexport default function() {\n  return new Selection(this._enter || this._groups.map(sparse), this._parents);\n}\n\nexport function EnterNode(parent, datum) {\n  this.ownerDocument = parent.ownerDocument;\n  this.namespaceURI = parent.namespaceURI;\n  this._next = null;\n  this._parent = parent;\n  this.__data__ = datum;\n}\n\nEnterNode.prototype = {\n  constructor: EnterNode,\n  appendChild: function(child) { return this._parent.insertBefore(child, this._next); },\n  insertBefore: function(child, next) { return this._parent.insertBefore(child, next); },\n  querySelector: function(selector) { return this._parent.querySelector(selector); },\n  querySelectorAll: function(selector) { return this._parent.querySelectorAll(selector); }\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEe;IACb,OAAO,IAAI,2NAAA,CAAA,YAAS,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,4NAAA,CAAA,UAAM,GAAG,IAAI,CAAC,QAAQ;AAC7E;AAEO,SAAS,UAAU,MAAM,EAAE,KAAK;IACrC,IAAI,CAAC,aAAa,GAAG,OAAO,aAAa;IACzC,IAAI,CAAC,YAAY,GAAG,OAAO,YAAY;IACvC,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,QAAQ,GAAG;AAClB;AAEA,UAAU,SAAS,GAAG;IACpB,aAAa;IACb,aAAa,SAAS,KAAK;QAAI,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,IAAI,CAAC,KAAK;IAAG;IACpF,cAAc,SAAS,KAAK,EAAE,IAAI;QAAI,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO;IAAO;IACrF,eAAe,SAAS,QAAQ;QAAI,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;IAAW;IACjF,kBAAkB,SAAS,QAAQ;QAAI,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC;IAAW;AACzF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/constant.js"], "sourcesContent": ["export default function(x) {\n  return function() {\n    return x;\n  };\n}\n"], "names": [], "mappings": ";;;AAAe,wCAAS,CAAC;IACvB,OAAO;QACL,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/data.js"], "sourcesContent": ["import {Selection} from \"./index.js\";\nimport {EnterNode} from \"./enter.js\";\nimport constant from \"../constant.js\";\n\nfunction bindIndex(parent, group, enter, update, exit, data) {\n  var i = 0,\n      node,\n      groupLength = group.length,\n      dataLength = data.length;\n\n  // Put any non-null nodes that fit into update.\n  // Put any null nodes into enter.\n  // Put any remaining data into enter.\n  for (; i < dataLength; ++i) {\n    if (node = group[i]) {\n      node.__data__ = data[i];\n      update[i] = node;\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Put any non-null nodes that don’t fit into exit.\n  for (; i < groupLength; ++i) {\n    if (node = group[i]) {\n      exit[i] = node;\n    }\n  }\n}\n\nfunction bindKey(parent, group, enter, update, exit, data, key) {\n  var i,\n      node,\n      nodeByKeyValue = new Map,\n      groupLength = group.length,\n      dataLength = data.length,\n      keyValues = new Array(groupLength),\n      keyValue;\n\n  // Compute the key for each node.\n  // If multiple nodes have the same key, the duplicates are added to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if (node = group[i]) {\n      keyValues[i] = keyValue = key.call(node, node.__data__, i, group) + \"\";\n      if (nodeByKeyValue.has(keyValue)) {\n        exit[i] = node;\n      } else {\n        nodeByKeyValue.set(keyValue, node);\n      }\n    }\n  }\n\n  // Compute the key for each datum.\n  // If there a node associated with this key, join and add it to update.\n  // If there is not (or the key is a duplicate), add it to enter.\n  for (i = 0; i < dataLength; ++i) {\n    keyValue = key.call(parent, data[i], i, data) + \"\";\n    if (node = nodeByKeyValue.get(keyValue)) {\n      update[i] = node;\n      node.__data__ = data[i];\n      nodeByKeyValue.delete(keyValue);\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Add any remaining nodes that were not bound to data to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if ((node = group[i]) && (nodeByKeyValue.get(keyValues[i]) === node)) {\n      exit[i] = node;\n    }\n  }\n}\n\nfunction datum(node) {\n  return node.__data__;\n}\n\nexport default function(value, key) {\n  if (!arguments.length) return Array.from(this, datum);\n\n  var bind = key ? bindKey : bindIndex,\n      parents = this._parents,\n      groups = this._groups;\n\n  if (typeof value !== \"function\") value = constant(value);\n\n  for (var m = groups.length, update = new Array(m), enter = new Array(m), exit = new Array(m), j = 0; j < m; ++j) {\n    var parent = parents[j],\n        group = groups[j],\n        groupLength = group.length,\n        data = arraylike(value.call(parent, parent && parent.__data__, j, parents)),\n        dataLength = data.length,\n        enterGroup = enter[j] = new Array(dataLength),\n        updateGroup = update[j] = new Array(dataLength),\n        exitGroup = exit[j] = new Array(groupLength);\n\n    bind(parent, group, enterGroup, updateGroup, exitGroup, data, key);\n\n    // Now connect the enter nodes to their following update node, such that\n    // appendChild can insert the materialized enter node before this node,\n    // rather than at the end of the parent node.\n    for (var i0 = 0, i1 = 0, previous, next; i0 < dataLength; ++i0) {\n      if (previous = enterGroup[i0]) {\n        if (i0 >= i1) i1 = i0 + 1;\n        while (!(next = updateGroup[i1]) && ++i1 < dataLength);\n        previous._next = next || null;\n      }\n    }\n  }\n\n  update = new Selection(update, parents);\n  update._enter = enter;\n  update._exit = exit;\n  return update;\n}\n\n// Given some data, this returns an array-like view of it: an object that\n// exposes a length property and allows numeric indexing. Note that unlike\n// selectAll, this isn’t worried about “live” collections because the resulting\n// array will only be used briefly while data is being bound. (It is possible to\n// cause the data to change while iterating by using a key function, but please\n// don’t; we’d rather avoid a gratuitous copy.)\nfunction arraylike(data) {\n  return typeof data === \"object\" && \"length\" in data\n    ? data // Array, TypedArray, NodeList, array-like\n    : Array.from(data); // Map, Set, iterable, string, or anything else\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,UAAU,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI;IACzD,IAAI,IAAI,GACJ,MACA,cAAc,MAAM,MAAM,EAC1B,aAAa,KAAK,MAAM;IAE5B,+CAA+C;IAC/C,iCAAiC;IACjC,qCAAqC;IACrC,MAAO,IAAI,YAAY,EAAE,EAAG;QAC1B,IAAI,OAAO,KAAK,CAAC,EAAE,EAAE;YACnB,KAAK,QAAQ,GAAG,IAAI,CAAC,EAAE;YACvB,MAAM,CAAC,EAAE,GAAG;QACd,OAAO;YACL,KAAK,CAAC,EAAE,GAAG,IAAI,2NAAA,CAAA,YAAS,CAAC,QAAQ,IAAI,CAAC,EAAE;QAC1C;IACF;IAEA,mDAAmD;IACnD,MAAO,IAAI,aAAa,EAAE,EAAG;QAC3B,IAAI,OAAO,KAAK,CAAC,EAAE,EAAE;YACnB,IAAI,CAAC,EAAE,GAAG;QACZ;IACF;AACF;AAEA,SAAS,QAAQ,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG;IAC5D,IAAI,GACA,MACA,iBAAiB,IAAI,KACrB,cAAc,MAAM,MAAM,EAC1B,aAAa,KAAK,MAAM,EACxB,YAAY,IAAI,MAAM,cACtB;IAEJ,iCAAiC;IACjC,yEAAyE;IACzE,IAAK,IAAI,GAAG,IAAI,aAAa,EAAE,EAAG;QAChC,IAAI,OAAO,KAAK,CAAC,EAAE,EAAE;YACnB,SAAS,CAAC,EAAE,GAAG,WAAW,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,GAAG,SAAS;YACpE,IAAI,eAAe,GAAG,CAAC,WAAW;gBAChC,IAAI,CAAC,EAAE,GAAG;YACZ,OAAO;gBACL,eAAe,GAAG,CAAC,UAAU;YAC/B;QACF;IACF;IAEA,kCAAkC;IAClC,uEAAuE;IACvE,gEAAgE;IAChE,IAAK,IAAI,GAAG,IAAI,YAAY,EAAE,EAAG;QAC/B,WAAW,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,EAAE,EAAE,GAAG,QAAQ;QAChD,IAAI,OAAO,eAAe,GAAG,CAAC,WAAW;YACvC,MAAM,CAAC,EAAE,GAAG;YACZ,KAAK,QAAQ,GAAG,IAAI,CAAC,EAAE;YACvB,eAAe,MAAM,CAAC;QACxB,OAAO;YACL,KAAK,CAAC,EAAE,GAAG,IAAI,2NAAA,CAAA,YAAS,CAAC,QAAQ,IAAI,CAAC,EAAE;QAC1C;IACF;IAEA,+DAA+D;IAC/D,IAAK,IAAI,GAAG,IAAI,aAAa,EAAE,EAAG;QAChC,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE,KAAM,eAAe,GAAG,CAAC,SAAS,CAAC,EAAE,MAAM,MAAO;YACpE,IAAI,CAAC,EAAE,GAAG;QACZ;IACF;AACF;AAEA,SAAS,MAAM,IAAI;IACjB,OAAO,KAAK,QAAQ;AACtB;AAEe,wCAAS,KAAK,EAAE,GAAG;IAChC,IAAI,CAAC,UAAU,MAAM,EAAE,OAAO,MAAM,IAAI,CAAC,IAAI,EAAE;IAE/C,IAAI,OAAO,MAAM,UAAU,WACvB,UAAU,IAAI,CAAC,QAAQ,EACvB,SAAS,IAAI,CAAC,OAAO;IAEzB,IAAI,OAAO,UAAU,YAAY,QAAQ,CAAA,GAAA,iNAAA,CAAA,UAAQ,AAAD,EAAE;IAElD,IAAK,IAAI,IAAI,OAAO,MAAM,EAAE,SAAS,IAAI,MAAM,IAAI,QAAQ,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QAC/G,IAAI,SAAS,OAAO,CAAC,EAAE,EACnB,QAAQ,MAAM,CAAC,EAAE,EACjB,cAAc,MAAM,MAAM,EAC1B,OAAO,UAAU,MAAM,IAAI,CAAC,QAAQ,UAAU,OAAO,QAAQ,EAAE,GAAG,WAClE,aAAa,KAAK,MAAM,EACxB,aAAa,KAAK,CAAC,EAAE,GAAG,IAAI,MAAM,aAClC,cAAc,MAAM,CAAC,EAAE,GAAG,IAAI,MAAM,aACpC,YAAY,IAAI,CAAC,EAAE,GAAG,IAAI,MAAM;QAEpC,KAAK,QAAQ,OAAO,YAAY,aAAa,WAAW,MAAM;QAE9D,wEAAwE;QACxE,uEAAuE;QACvE,6CAA6C;QAC7C,IAAK,IAAI,KAAK,GAAG,KAAK,GAAG,UAAU,MAAM,KAAK,YAAY,EAAE,GAAI;YAC9D,IAAI,WAAW,UAAU,CAAC,GAAG,EAAE;gBAC7B,IAAI,MAAM,IAAI,KAAK,KAAK;gBACxB,MAAO,CAAC,CAAC,OAAO,WAAW,CAAC,GAAG,KAAK,EAAE,KAAK;gBAC3C,SAAS,KAAK,GAAG,QAAQ;YAC3B;QACF;IACF;IAEA,SAAS,IAAI,2NAAA,CAAA,YAAS,CAAC,QAAQ;IAC/B,OAAO,MAAM,GAAG;IAChB,OAAO,KAAK,GAAG;IACf,OAAO;AACT;AAEA,yEAAyE;AACzE,0EAA0E;AAC1E,+EAA+E;AAC/E,gFAAgF;AAChF,+EAA+E;AAC/E,+CAA+C;AAC/C,SAAS,UAAU,IAAI;IACrB,OAAO,OAAO,SAAS,YAAY,YAAY,OAC3C,KAAK,0CAA0C;OAC/C,MAAM,IAAI,CAAC,OAAO,+CAA+C;AACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 601, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/exit.js"], "sourcesContent": ["import sparse from \"./sparse.js\";\nimport {Selection} from \"./index.js\";\n\nexport default function() {\n  return new Selection(this._exit || this._groups.map(sparse), this._parents);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEe;IACb,OAAO,IAAI,2NAAA,CAAA,YAAS,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,4NAAA,CAAA,UAAM,GAAG,IAAI,CAAC,QAAQ;AAC5E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 617, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 623, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/join.js"], "sourcesContent": ["export default function(onenter, onupdate, onexit) {\n  var enter = this.enter(), update = this, exit = this.exit();\n  if (typeof onenter === \"function\") {\n    enter = onenter(enter);\n    if (enter) enter = enter.selection();\n  } else {\n    enter = enter.append(onenter + \"\");\n  }\n  if (onupdate != null) {\n    update = onupdate(update);\n    if (update) update = update.selection();\n  }\n  if (onexit == null) exit.remove(); else onexit(exit);\n  return enter && update ? enter.merge(update).order() : update;\n}\n"], "names": [], "mappings": ";;;AAAe,wCAAS,OAAO,EAAE,QAAQ,EAAE,MAAM;IAC/C,IAAI,QAAQ,IAAI,CAAC,KAAK,IAAI,SAAS,IAAI,EAAE,OAAO,IAAI,CAAC,IAAI;IACzD,IAAI,OAAO,YAAY,YAAY;QACjC,QAAQ,QAAQ;QAChB,IAAI,OAAO,QAAQ,MAAM,SAAS;IACpC,OAAO;QACL,QAAQ,MAAM,MAAM,CAAC,UAAU;IACjC;IACA,IAAI,YAAY,MAAM;QACpB,SAAS,SAAS;QAClB,IAAI,QAAQ,SAAS,OAAO,SAAS;IACvC;IACA,IAAI,UAAU,MAAM,KAAK,MAAM;SAAS,OAAO;IAC/C,OAAO,SAAS,SAAS,MAAM,KAAK,CAAC,QAAQ,KAAK,KAAK;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 642, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/merge.js"], "sourcesContent": ["import {Selection} from \"./index.js\";\n\nexport default function(context) {\n  var selection = context.selection ? context.selection() : context;\n\n  for (var groups0 = this._groups, groups1 = selection._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n\n  return new Selection(merges, this._parents);\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,wCAAS,OAAO;IAC7B,IAAI,YAAY,QAAQ,SAAS,GAAG,QAAQ,SAAS,KAAK;IAE1D,IAAK,IAAI,UAAU,IAAI,CAAC,OAAO,EAAE,UAAU,UAAU,OAAO,EAAE,KAAK,QAAQ,MAAM,EAAE,KAAK,QAAQ,MAAM,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QACvK,IAAK,IAAI,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS,OAAO,CAAC,EAAE,EAAE,IAAI,OAAO,MAAM,EAAE,QAAQ,MAAM,CAAC,EAAE,GAAG,IAAI,MAAM,IAAI,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YAC/H,IAAI,OAAO,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE;gBACjC,KAAK,CAAC,EAAE,GAAG;YACb;QACF;IACF;IAEA,MAAO,IAAI,IAAI,EAAE,EAAG;QAClB,MAAM,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;IACxB;IAEA,OAAO,IAAI,2NAAA,CAAA,YAAS,CAAC,QAAQ,IAAI,CAAC,QAAQ;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 667, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/order.js"], "sourcesContent": ["export default function() {\n\n  for (var groups = this._groups, j = -1, m = groups.length; ++j < m;) {\n    for (var group = groups[j], i = group.length - 1, next = group[i], node; --i >= 0;) {\n      if (node = group[i]) {\n        if (next && node.compareDocumentPosition(next) ^ 4) next.parentNode.insertBefore(node, next);\n        next = node;\n      }\n    }\n  }\n\n  return this;\n}\n"], "names": [], "mappings": ";;;AAAe;IAEb,IAAK,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,IAAI,OAAO,MAAM,EAAE,EAAE,IAAI,GAAI;QACnE,IAAK,IAAI,QAAQ,MAAM,CAAC,EAAE,EAAE,IAAI,MAAM,MAAM,GAAG,GAAG,OAAO,KAAK,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,GAAI;YAClF,IAAI,OAAO,KAAK,CAAC,EAAE,EAAE;gBACnB,IAAI,QAAQ,KAAK,uBAAuB,CAAC,QAAQ,GAAG,KAAK,UAAU,CAAC,YAAY,CAAC,MAAM;gBACvF,OAAO;YACT;QACF;IACF;IAEA,OAAO,IAAI;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/sort.js"], "sourcesContent": ["import {Selection} from \"./index.js\";\n\nexport default function(compare) {\n  if (!compare) compare = ascending;\n\n  function compareNode(a, b) {\n    return a && b ? compare(a.__data__, b.__data__) : !a - !b;\n  }\n\n  for (var groups = this._groups, m = groups.length, sortgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, sortgroup = sortgroups[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        sortgroup[i] = node;\n      }\n    }\n    sortgroup.sort(compareNode);\n  }\n\n  return new Selection(sortgroups, this._parents).order();\n}\n\nfunction ascending(a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,wCAAS,OAAO;IAC7B,IAAI,CAAC,SAAS,UAAU;IAExB,SAAS,YAAY,CAAC,EAAE,CAAC;QACvB,OAAO,KAAK,IAAI,QAAQ,EAAE,QAAQ,EAAE,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC;IAC1D;IAEA,IAAK,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,OAAO,MAAM,EAAE,aAAa,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QAC/F,IAAK,IAAI,QAAQ,MAAM,CAAC,EAAE,EAAE,IAAI,MAAM,MAAM,EAAE,YAAY,UAAU,CAAC,EAAE,GAAG,IAAI,MAAM,IAAI,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YAC/G,IAAI,OAAO,KAAK,CAAC,EAAE,EAAE;gBACnB,SAAS,CAAC,EAAE,GAAG;YACjB;QACF;QACA,UAAU,IAAI,CAAC;IACjB;IAEA,OAAO,IAAI,2NAAA,CAAA,YAAS,CAAC,YAAY,IAAI,CAAC,QAAQ,EAAE,KAAK;AACvD;AAEA,SAAS,UAAU,CAAC,EAAE,CAAC;IACrB,OAAO,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 716, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 722, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/call.js"], "sourcesContent": ["export default function() {\n  var callback = arguments[0];\n  arguments[0] = this;\n  callback.apply(null, arguments);\n  return this;\n}\n"], "names": [], "mappings": ";;;AAAe;IACb,IAAI,WAAW,SAAS,CAAC,EAAE;IAC3B,SAAS,CAAC,EAAE,GAAG,IAAI;IACnB,SAAS,KAAK,CAAC,MAAM;IACrB,OAAO,IAAI;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 737, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/nodes.js"], "sourcesContent": ["export default function() {\n  return Array.from(this);\n}\n"], "names": [], "mappings": ";;;AAAe;IACb,OAAO,MAAM,IAAI,CAAC,IAAI;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 749, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/node.js"], "sourcesContent": ["export default function() {\n\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length; i < n; ++i) {\n      var node = group[i];\n      if (node) return node;\n    }\n  }\n\n  return null;\n}\n"], "names": [], "mappings": ";;;AAAe;IAEb,IAAK,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG;QACpE,IAAK,IAAI,QAAQ,MAAM,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG;YAC/D,IAAI,OAAO,KAAK,CAAC,EAAE;YACnB,IAAI,MAAM,OAAO;QACnB;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/size.js"], "sourcesContent": ["export default function() {\n  let size = 0;\n  for (const node of this) ++size; // eslint-disable-line no-unused-vars\n  return size;\n}\n"], "names": [], "mappings": ";;;AAAe;IACb,IAAI,OAAO;IACX,KAAK,MAAM,QAAQ,IAAI,CAAE,EAAE,MAAM,qCAAqC;IACtE,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 775, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/empty.js"], "sourcesContent": ["export default function() {\n  return !this.node();\n}\n"], "names": [], "mappings": ";;;AAAe;IACb,OAAO,CAAC,IAAI,CAAC,IAAI;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 787, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/each.js"], "sourcesContent": ["export default function(callback) {\n\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) callback.call(node, node.__data__, i, group);\n    }\n  }\n\n  return this;\n}\n"], "names": [], "mappings": ";;;AAAe,wCAAS,QAAQ;IAE9B,IAAK,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG;QACpE,IAAK,IAAI,QAAQ,MAAM,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,MAAM,IAAI,GAAG,EAAE,EAAG;YACrE,IAAI,OAAO,KAAK,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,GAAG;QAC7D;IACF;IAEA,OAAO,IAAI;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 804, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 810, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/namespaces.js"], "sourcesContent": ["export var xhtml = \"http://www.w3.org/1999/xhtml\";\n\nexport default {\n  svg: \"http://www.w3.org/2000/svg\",\n  xhtml: xhtml,\n  xlink: \"http://www.w3.org/1999/xlink\",\n  xml: \"http://www.w3.org/XML/1998/namespace\",\n  xmlns: \"http://www.w3.org/2000/xmlns/\"\n};\n"], "names": [], "mappings": ";;;;AAAO,IAAI,QAAQ;uCAEJ;IACb,KAAK;IACL,OAAO;IACP,OAAO;IACP,KAAK;IACL,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 822, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 828, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/namespace.js"], "sourcesContent": ["import namespaces from \"./namespaces.js\";\n\nexport default function(name) {\n  var prefix = name += \"\", i = prefix.indexOf(\":\");\n  if (i >= 0 && (prefix = name.slice(0, i)) !== \"xmlns\") name = name.slice(i + 1);\n  return namespaces.hasOwnProperty(prefix) ? {space: namespaces[prefix], local: name} : name; // eslint-disable-line no-prototype-builtins\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,wCAAS,IAAI;IAC1B,IAAI,SAAS,QAAQ,IAAI,IAAI,OAAO,OAAO,CAAC;IAC5C,IAAI,KAAK,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,GAAG,EAAE,MAAM,SAAS,OAAO,KAAK,KAAK,CAAC,IAAI;IAC7E,OAAO,mNAAA,CAAA,UAAU,CAAC,cAAc,CAAC,UAAU;QAAC,OAAO,mNAAA,CAAA,UAAU,CAAC,OAAO;QAAE,OAAO;IAAI,IAAI,MAAM,4CAA4C;AAC1I", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 841, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 847, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/attr.js"], "sourcesContent": ["import namespace from \"../namespace.js\";\n\nfunction attrRemove(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\n\nfunction attrRemoveNS(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\n\nfunction attrConstant(name, value) {\n  return function() {\n    this.setAttribute(name, value);\n  };\n}\n\nfunction attrConstantNS(fullname, value) {\n  return function() {\n    this.setAttributeNS(fullname.space, fullname.local, value);\n  };\n}\n\nfunction attrFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttribute(name);\n    else this.setAttribute(name, v);\n  };\n}\n\nfunction attrFunctionNS(fullname, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.removeAttributeNS(fullname.space, fullname.local);\n    else this.setAttributeNS(fullname.space, fullname.local, v);\n  };\n}\n\nexport default function(name, value) {\n  var fullname = namespace(name);\n\n  if (arguments.length < 2) {\n    var node = this.node();\n    return fullname.local\n        ? node.getAttributeNS(fullname.space, fullname.local)\n        : node.getAttribute(fullname);\n  }\n\n  return this.each((value == null\n      ? (fullname.local ? attrRemoveNS : attrRemove) : (typeof value === \"function\"\n      ? (fullname.local ? attrFunctionNS : attrFunction)\n      : (fullname.local ? attrConstantNS : attrConstant)))(fullname, value));\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,WAAW,IAAI;IACtB,OAAO;QACL,IAAI,CAAC,eAAe,CAAC;IACvB;AACF;AAEA,SAAS,aAAa,QAAQ;IAC5B,OAAO;QACL,IAAI,CAAC,iBAAiB,CAAC,SAAS,KAAK,EAAE,SAAS,KAAK;IACvD;AACF;AAEA,SAAS,aAAa,IAAI,EAAE,KAAK;IAC/B,OAAO;QACL,IAAI,CAAC,YAAY,CAAC,MAAM;IAC1B;AACF;AAEA,SAAS,eAAe,QAAQ,EAAE,KAAK;IACrC,OAAO;QACL,IAAI,CAAC,cAAc,CAAC,SAAS,KAAK,EAAE,SAAS,KAAK,EAAE;IACtD;AACF;AAEA,SAAS,aAAa,IAAI,EAAE,KAAK;IAC/B,OAAO;QACL,IAAI,IAAI,MAAM,KAAK,CAAC,IAAI,EAAE;QAC1B,IAAI,KAAK,MAAM,IAAI,CAAC,eAAe,CAAC;aAC/B,IAAI,CAAC,YAAY,CAAC,MAAM;IAC/B;AACF;AAEA,SAAS,eAAe,QAAQ,EAAE,KAAK;IACrC,OAAO;QACL,IAAI,IAAI,MAAM,KAAK,CAAC,IAAI,EAAE;QAC1B,IAAI,KAAK,MAAM,IAAI,CAAC,iBAAiB,CAAC,SAAS,KAAK,EAAE,SAAS,KAAK;aAC/D,IAAI,CAAC,cAAc,CAAC,SAAS,KAAK,EAAE,SAAS,KAAK,EAAE;IAC3D;AACF;AAEe,wCAAS,IAAI,EAAE,KAAK;IACjC,IAAI,WAAW,CAAA,GAAA,kNAAA,CAAA,UAAS,AAAD,EAAE;IAEzB,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,OAAO,SAAS,KAAK,GACf,KAAK,cAAc,CAAC,SAAS,KAAK,EAAE,SAAS,KAAK,IAClD,KAAK,YAAY,CAAC;IAC1B;IAEA,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,OACpB,SAAS,KAAK,GAAG,eAAe,aAAe,OAAO,UAAU,aAChE,SAAS,KAAK,GAAG,iBAAiB,eAClC,SAAS,KAAK,GAAG,iBAAiB,YAAc,EAAE,UAAU;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 894, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 900, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/window.js"], "sourcesContent": ["export default function(node) {\n  return (node.ownerDocument && node.ownerDocument.defaultView) // node is a Node\n      || (node.document && node) // node is a Window\n      || node.defaultView; // node is a Document\n}\n"], "names": [], "mappings": ";;;AAAe,wCAAS,IAAI;IAC1B,OAAO,AAAC,KAAK,aAAa,IAAI,KAAK,aAAa,CAAC,WAAW,IACpD,KAAK,QAAQ,IAAI,QAClB,KAAK,WAAW,EAAE,qBAAqB;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 912, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/style.js"], "sourcesContent": ["import defaultView from \"../window.js\";\n\nfunction styleRemove(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\n\nfunction styleConstant(name, value, priority) {\n  return function() {\n    this.style.setProperty(name, value, priority);\n  };\n}\n\nfunction styleFunction(name, value, priority) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.style.removeProperty(name);\n    else this.style.setProperty(name, v, priority);\n  };\n}\n\nexport default function(name, value, priority) {\n  return arguments.length > 1\n      ? this.each((value == null\n            ? styleRemove : typeof value === \"function\"\n            ? styleFunction\n            : styleConstant)(name, value, priority == null ? \"\" : priority))\n      : styleValue(this.node(), name);\n}\n\nexport function styleValue(node, name) {\n  return node.style.getPropertyValue(name)\n      || defaultView(node).getComputedStyle(node, null).getPropertyValue(name);\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,SAAS,YAAY,IAAI;IACvB,OAAO;QACL,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;IAC5B;AACF;AAEA,SAAS,cAAc,IAAI,EAAE,KAAK,EAAE,QAAQ;IAC1C,OAAO;QACL,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,OAAO;IACtC;AACF;AAEA,SAAS,cAAc,IAAI,EAAE,KAAK,EAAE,QAAQ;IAC1C,OAAO;QACL,IAAI,IAAI,MAAM,KAAK,CAAC,IAAI,EAAE;QAC1B,IAAI,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;aACpC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG;IACvC;AACF;AAEe,wCAAS,IAAI,EAAE,KAAK,EAAE,QAAQ;IAC3C,OAAO,UAAU,MAAM,GAAG,IACpB,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,OACd,cAAc,OAAO,UAAU,aAC/B,gBACA,aAAa,EAAE,MAAM,OAAO,YAAY,OAAO,KAAK,aAC1D,WAAW,IAAI,CAAC,IAAI,IAAI;AAChC;AAEO,SAAS,WAAW,IAAI,EAAE,IAAI;IACnC,OAAO,KAAK,KAAK,CAAC,gBAAgB,CAAC,SAC5B,CAAA,GAAA,+MAAA,CAAA,UAAW,AAAD,EAAE,MAAM,gBAAgB,CAAC,MAAM,MAAM,gBAAgB,CAAC;AACzE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/property.js"], "sourcesContent": ["function propertyRemove(name) {\n  return function() {\n    delete this[name];\n  };\n}\n\nfunction propertyConstant(name, value) {\n  return function() {\n    this[name] = value;\n  };\n}\n\nfunction propertyFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) delete this[name];\n    else this[name] = v;\n  };\n}\n\nexport default function(name, value) {\n  return arguments.length > 1\n      ? this.each((value == null\n          ? propertyRemove : typeof value === \"function\"\n          ? propertyFunction\n          : propertyConstant)(name, value))\n      : this.node()[name];\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,eAAe,IAAI;IAC1B,OAAO;QACL,OAAO,IAAI,CAAC,KAAK;IACnB;AACF;AAEA,SAAS,iBAAiB,IAAI,EAAE,KAAK;IACnC,OAAO;QACL,IAAI,CAAC,KAAK,GAAG;IACf;AACF;AAEA,SAAS,iBAAiB,IAAI,EAAE,KAAK;IACnC,OAAO;QACL,IAAI,IAAI,MAAM,KAAK,CAAC,IAAI,EAAE;QAC1B,IAAI,KAAK,MAAM,OAAO,IAAI,CAAC,KAAK;aAC3B,IAAI,CAAC,KAAK,GAAG;IACpB;AACF;AAEe,wCAAS,IAAI,EAAE,KAAK;IACjC,OAAO,UAAU,MAAM,GAAG,IACpB,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,OAChB,iBAAiB,OAAO,UAAU,aAClC,mBACA,gBAAgB,EAAE,MAAM,UAC5B,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 970, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 976, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/classed.js"], "sourcesContent": ["function classArray(string) {\n  return string.trim().split(/^|\\s+/);\n}\n\nfunction classList(node) {\n  return node.classList || new ClassList(node);\n}\n\nfunction ClassList(node) {\n  this._node = node;\n  this._names = classArray(node.getAttribute(\"class\") || \"\");\n}\n\nClassList.prototype = {\n  add: function(name) {\n    var i = this._names.indexOf(name);\n    if (i < 0) {\n      this._names.push(name);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  remove: function(name) {\n    var i = this._names.indexOf(name);\n    if (i >= 0) {\n      this._names.splice(i, 1);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  contains: function(name) {\n    return this._names.indexOf(name) >= 0;\n  }\n};\n\nfunction classedAdd(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.add(names[i]);\n}\n\nfunction classedRemove(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.remove(names[i]);\n}\n\nfunction classedTrue(names) {\n  return function() {\n    classedAdd(this, names);\n  };\n}\n\nfunction classedFalse(names) {\n  return function() {\n    classedRemove(this, names);\n  };\n}\n\nfunction classedFunction(names, value) {\n  return function() {\n    (value.apply(this, arguments) ? classedAdd : classedRemove)(this, names);\n  };\n}\n\nexport default function(name, value) {\n  var names = classArray(name + \"\");\n\n  if (arguments.length < 2) {\n    var list = classList(this.node()), i = -1, n = names.length;\n    while (++i < n) if (!list.contains(names[i])) return false;\n    return true;\n  }\n\n  return this.each((typeof value === \"function\"\n      ? classedFunction : value\n      ? classedTrue\n      : classedFalse)(names, value));\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,WAAW,MAAM;IACxB,OAAO,OAAO,IAAI,GAAG,KAAK,CAAC;AAC7B;AAEA,SAAS,UAAU,IAAI;IACrB,OAAO,KAAK,SAAS,IAAI,IAAI,UAAU;AACzC;AAEA,SAAS,UAAU,IAAI;IACrB,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,MAAM,GAAG,WAAW,KAAK,YAAY,CAAC,YAAY;AACzD;AAEA,UAAU,SAAS,GAAG;IACpB,KAAK,SAAS,IAAI;QAChB,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QAC5B,IAAI,IAAI,GAAG;YACT,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACjB,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACpD;IACF;IACA,QAAQ,SAAS,IAAI;QACnB,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QAC5B,IAAI,KAAK,GAAG;YACV,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG;YACtB,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACpD;IACF;IACA,UAAU,SAAS,IAAI;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS;IACtC;AACF;AAEA,SAAS,WAAW,IAAI,EAAE,KAAK;IAC7B,IAAI,OAAO,UAAU,OAAO,IAAI,CAAC,GAAG,IAAI,MAAM,MAAM;IACpD,MAAO,EAAE,IAAI,EAAG,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;AACnC;AAEA,SAAS,cAAc,IAAI,EAAE,KAAK;IAChC,IAAI,OAAO,UAAU,OAAO,IAAI,CAAC,GAAG,IAAI,MAAM,MAAM;IACpD,MAAO,EAAE,IAAI,EAAG,KAAK,MAAM,CAAC,KAAK,CAAC,EAAE;AACtC;AAEA,SAAS,YAAY,KAAK;IACxB,OAAO;QACL,WAAW,IAAI,EAAE;IACnB;AACF;AAEA,SAAS,aAAa,KAAK;IACzB,OAAO;QACL,cAAc,IAAI,EAAE;IACtB;AACF;AAEA,SAAS,gBAAgB,KAAK,EAAE,KAAK;IACnC,OAAO;QACL,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,aAAa,aAAa,aAAa,EAAE,IAAI,EAAE;IACpE;AACF;AAEe,wCAAS,IAAI,EAAE,KAAK;IACjC,IAAI,QAAQ,WAAW,OAAO;IAE9B,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,IAAI,OAAO,UAAU,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,MAAM,MAAM;QAC3D,MAAO,EAAE,IAAI,EAAG,IAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,CAAC,EAAE,GAAG,OAAO;QACrD,OAAO;IACT;IAEA,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,UAAU,aAC7B,kBAAkB,QAClB,cACA,YAAY,EAAE,OAAO;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1040, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1046, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/text.js"], "sourcesContent": ["function textRemove() {\n  this.textContent = \"\";\n}\n\nfunction textConstant(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\n\nfunction textFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.textContent = v == null ? \"\" : v;\n  };\n}\n\nexport default function(value) {\n  return arguments.length\n      ? this.each(value == null\n          ? textRemove : (typeof value === \"function\"\n          ? textFunction\n          : textConstant)(value))\n      : this.node().textContent;\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS;IACP,IAAI,CAAC,WAAW,GAAG;AACrB;AAEA,SAAS,aAAa,KAAK;IACzB,OAAO;QACL,IAAI,CAAC,WAAW,GAAG;IACrB;AACF;AAEA,SAAS,aAAa,KAAK;IACzB,OAAO;QACL,IAAI,IAAI,MAAM,KAAK,CAAC,IAAI,EAAE;QAC1B,IAAI,CAAC,WAAW,GAAG,KAAK,OAAO,KAAK;IACtC;AACF;AAEe,wCAAS,KAAK;IAC3B,OAAO,UAAU,MAAM,GACjB,IAAI,CAAC,IAAI,CAAC,SAAS,OACf,aAAa,CAAC,OAAO,UAAU,aAC/B,eACA,YAAY,EAAE,UAClB,IAAI,CAAC,IAAI,GAAG,WAAW;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1066, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1072, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/html.js"], "sourcesContent": ["function htmlRemove() {\n  this.innerHTML = \"\";\n}\n\nfunction htmlConstant(value) {\n  return function() {\n    this.innerHTML = value;\n  };\n}\n\nfunction htmlFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.innerHTML = v == null ? \"\" : v;\n  };\n}\n\nexport default function(value) {\n  return arguments.length\n      ? this.each(value == null\n          ? htmlRemove : (typeof value === \"function\"\n          ? htmlFunction\n          : htmlConstant)(value))\n      : this.node().innerHTML;\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS;IACP,IAAI,CAAC,SAAS,GAAG;AACnB;AAEA,SAAS,aAAa,KAAK;IACzB,OAAO;QACL,IAAI,CAAC,SAAS,GAAG;IACnB;AACF;AAEA,SAAS,aAAa,KAAK;IACzB,OAAO;QACL,IAAI,IAAI,MAAM,KAAK,CAAC,IAAI,EAAE;QAC1B,IAAI,CAAC,SAAS,GAAG,KAAK,OAAO,KAAK;IACpC;AACF;AAEe,wCAAS,KAAK;IAC3B,OAAO,UAAU,MAAM,GACjB,IAAI,CAAC,IAAI,CAAC,SAAS,OACf,aAAa,CAAC,OAAO,UAAU,aAC/B,eACA,YAAY,EAAE,UAClB,IAAI,CAAC,IAAI,GAAG,SAAS;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1092, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1098, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/raise.js"], "sourcesContent": ["function raise() {\n  if (this.nextSibling) this.parentNode.appendChild(this);\n}\n\nexport default function() {\n  return this.each(raise);\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS;IACP,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI;AACxD;AAEe;IACb,OAAO,IAAI,CAAC,IAAI,CAAC;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1107, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1113, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/lower.js"], "sourcesContent": ["function lower() {\n  if (this.previousSibling) this.parentNode.insertBefore(this, this.parentNode.firstChild);\n}\n\nexport default function() {\n  return this.each(lower);\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS;IACP,IAAI,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,UAAU;AACzF;AAEe;IACb,OAAO,IAAI,CAAC,IAAI,CAAC;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1122, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1128, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/creator.js"], "sourcesContent": ["import namespace from \"./namespace.js\";\nimport {xhtml} from \"./namespaces.js\";\n\nfunction creatorInherit(name) {\n  return function() {\n    var document = this.ownerDocument,\n        uri = this.namespaceURI;\n    return uri === xhtml && document.documentElement.namespaceURI === xhtml\n        ? document.createElement(name)\n        : document.createElementNS(uri, name);\n  };\n}\n\nfunction creatorFixed(fullname) {\n  return function() {\n    return this.ownerDocument.createElementNS(fullname.space, fullname.local);\n  };\n}\n\nexport default function(name) {\n  var fullname = namespace(name);\n  return (fullname.local\n      ? creatorFixed\n      : creatorInherit)(fullname);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,eAAe,IAAI;IAC1B,OAAO;QACL,IAAI,WAAW,IAAI,CAAC,aAAa,EAC7B,MAAM,IAAI,CAAC,YAAY;QAC3B,OAAO,QAAQ,mNAAA,CAAA,QAAK,IAAI,SAAS,eAAe,CAAC,YAAY,KAAK,mNAAA,CAAA,QAAK,GACjE,SAAS,aAAa,CAAC,QACvB,SAAS,eAAe,CAAC,KAAK;IACtC;AACF;AAEA,SAAS,aAAa,QAAQ;IAC5B,OAAO;QACL,OAAO,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,SAAS,KAAK,EAAE,SAAS,KAAK;IAC1E;AACF;AAEe,wCAAS,IAAI;IAC1B,IAAI,WAAW,CAAA,GAAA,kNAAA,CAAA,UAAS,AAAD,EAAE;IACzB,OAAO,CAAC,SAAS,KAAK,GAChB,eACA,cAAc,EAAE;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1150, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1156, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/append.js"], "sourcesContent": ["import creator from \"../creator.js\";\n\nexport default function(name) {\n  var create = typeof name === \"function\" ? name : creator(name);\n  return this.select(function() {\n    return this.appendChild(create.apply(this, arguments));\n  });\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,wCAAS,IAAI;IAC1B,IAAI,SAAS,OAAO,SAAS,aAAa,OAAO,CAAA,GAAA,gNAAA,CAAA,UAAO,AAAD,EAAE;IACzD,OAAO,IAAI,CAAC,MAAM,CAAC;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,CAAC,IAAI,EAAE;IAC7C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1167, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1173, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/insert.js"], "sourcesContent": ["import creator from \"../creator.js\";\nimport selector from \"../selector.js\";\n\nfunction constantNull() {\n  return null;\n}\n\nexport default function(name, before) {\n  var create = typeof name === \"function\" ? name : creator(name),\n      select = before == null ? constantNull : typeof before === \"function\" ? before : selector(before);\n  return this.select(function() {\n    return this.insertBefore(create.apply(this, arguments), select.apply(this, arguments) || null);\n  });\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS;IACP,OAAO;AACT;AAEe,wCAAS,IAAI,EAAE,MAAM;IAClC,IAAI,SAAS,OAAO,SAAS,aAAa,OAAO,CAAA,GAAA,gNAAA,CAAA,UAAO,AAAD,EAAE,OACrD,SAAS,UAAU,OAAO,eAAe,OAAO,WAAW,aAAa,SAAS,CAAA,GAAA,iNAAA,CAAA,UAAQ,AAAD,EAAE;IAC9F,OAAO,IAAI,CAAC,MAAM,CAAC;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,KAAK,CAAC,IAAI,EAAE,YAAY,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc;IAC3F;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1189, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1195, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/remove.js"], "sourcesContent": ["function remove() {\n  var parent = this.parentNode;\n  if (parent) parent.removeChild(this);\n}\n\nexport default function() {\n  return this.each(remove);\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS;IACP,IAAI,SAAS,IAAI,CAAC,UAAU;IAC5B,IAAI,QAAQ,OAAO,WAAW,CAAC,IAAI;AACrC;AAEe;IACb,OAAO,IAAI,CAAC,IAAI,CAAC;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1205, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1211, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/clone.js"], "sourcesContent": ["function selection_cloneShallow() {\n  var clone = this.cloneNode(false), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\n\nfunction selection_cloneDeep() {\n  var clone = this.cloneNode(true), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\n\nexport default function(deep) {\n  return this.select(deep ? selection_cloneDeep : selection_cloneShallow);\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS;IACP,IAAI,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ,SAAS,IAAI,CAAC,UAAU;IAC3D,OAAO,SAAS,OAAO,YAAY,CAAC,OAAO,IAAI,CAAC,WAAW,IAAI;AACjE;AAEA,SAAS;IACP,IAAI,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,SAAS,IAAI,CAAC,UAAU;IAC1D,OAAO,SAAS,OAAO,YAAY,CAAC,OAAO,IAAI,CAAC,WAAW,IAAI;AACjE;AAEe,wCAAS,IAAI;IAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,sBAAsB;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1225, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1231, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/datum.js"], "sourcesContent": ["export default function(value) {\n  return arguments.length\n      ? this.property(\"__data__\", value)\n      : this.node().__data__;\n}\n"], "names": [], "mappings": ";;;AAAe,wCAAS,KAAK;IAC3B,OAAO,UAAU,MAAM,GACjB,IAAI,CAAC,QAAQ,CAAC,YAAY,SAC1B,IAAI,CAAC,IAAI,GAAG,QAAQ;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1237, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1243, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/on.js"], "sourcesContent": ["function contextListener(listener) {\n  return function(event) {\n    listener.call(this, event, this.__data__);\n  };\n}\n\nfunction parseTypenames(typenames) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    return {type: t, name: name};\n  });\n}\n\nfunction onRemove(typename) {\n  return function() {\n    var on = this.__on;\n    if (!on) return;\n    for (var j = 0, i = -1, m = on.length, o; j < m; ++j) {\n      if (o = on[j], (!typename.type || o.type === typename.type) && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n      } else {\n        on[++i] = o;\n      }\n    }\n    if (++i) on.length = i;\n    else delete this.__on;\n  };\n}\n\nfunction onAdd(typename, value, options) {\n  return function() {\n    var on = this.__on, o, listener = contextListener(value);\n    if (on) for (var j = 0, m = on.length; j < m; ++j) {\n      if ((o = on[j]).type === typename.type && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n        this.addEventListener(o.type, o.listener = listener, o.options = options);\n        o.value = value;\n        return;\n      }\n    }\n    this.addEventListener(typename.type, listener, options);\n    o = {type: typename.type, name: typename.name, value: value, listener: listener, options: options};\n    if (!on) this.__on = [o];\n    else on.push(o);\n  };\n}\n\nexport default function(typename, value, options) {\n  var typenames = parseTypenames(typename + \"\"), i, n = typenames.length, t;\n\n  if (arguments.length < 2) {\n    var on = this.node().__on;\n    if (on) for (var j = 0, m = on.length, o; j < m; ++j) {\n      for (i = 0, o = on[j]; i < n; ++i) {\n        if ((t = typenames[i]).type === o.type && t.name === o.name) {\n          return o.value;\n        }\n      }\n    }\n    return;\n  }\n\n  on = value ? onAdd : onRemove;\n  for (i = 0; i < n; ++i) this.each(on(typenames[i], value, options));\n  return this;\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB,QAAQ;IAC/B,OAAO,SAAS,KAAK;QACnB,SAAS,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,QAAQ;IAC1C;AACF;AAEA,SAAS,eAAe,SAAS;IAC/B,OAAO,UAAU,IAAI,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,SAAS,CAAC;QACnD,IAAI,OAAO,IAAI,IAAI,EAAE,OAAO,CAAC;QAC7B,IAAI,KAAK,GAAG,OAAO,EAAE,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE,KAAK,CAAC,GAAG;QAClD,OAAO;YAAC,MAAM;YAAG,MAAM;QAAI;IAC7B;AACF;AAEA,SAAS,SAAS,QAAQ;IACxB,OAAO;QACL,IAAI,KAAK,IAAI,CAAC,IAAI;QAClB,IAAI,CAAC,IAAI;QACT,IAAK,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,MAAM,EAAE,GAAG,IAAI,GAAG,EAAE,EAAG;YACpD,IAAI,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,SAAS,IAAI,IAAI,EAAE,IAAI,KAAK,SAAS,IAAI,KAAK,EAAE,IAAI,KAAK,SAAS,IAAI,EAAE;gBACvF,IAAI,CAAC,mBAAmB,CAAC,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,EAAE,OAAO;YACxD,OAAO;gBACL,EAAE,CAAC,EAAE,EAAE,GAAG;YACZ;QACF;QACA,IAAI,EAAE,GAAG,GAAG,MAAM,GAAG;aAChB,OAAO,IAAI,CAAC,IAAI;IACvB;AACF;AAEA,SAAS,MAAM,QAAQ,EAAE,KAAK,EAAE,OAAO;IACrC,OAAO;QACL,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,GAAG,WAAW,gBAAgB;QAClD,IAAI,IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG;YACjD,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,KAAK,SAAS,IAAI,IAAI,EAAE,IAAI,KAAK,SAAS,IAAI,EAAE;gBAClE,IAAI,CAAC,mBAAmB,CAAC,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,EAAE,OAAO;gBACtD,IAAI,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,EAAE,QAAQ,GAAG,UAAU,EAAE,OAAO,GAAG;gBACjE,EAAE,KAAK,GAAG;gBACV;YACF;QACF;QACA,IAAI,CAAC,gBAAgB,CAAC,SAAS,IAAI,EAAE,UAAU;QAC/C,IAAI;YAAC,MAAM,SAAS,IAAI;YAAE,MAAM,SAAS,IAAI;YAAE,OAAO;YAAO,UAAU;YAAU,SAAS;QAAO;QACjG,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG;YAAC;SAAE;aACnB,GAAG,IAAI,CAAC;IACf;AACF;AAEe,wCAAS,QAAQ,EAAE,KAAK,EAAE,OAAO;IAC9C,IAAI,YAAY,eAAe,WAAW,KAAK,GAAG,IAAI,UAAU,MAAM,EAAE;IAExE,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI;QACzB,IAAI,IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,GAAG,IAAI,GAAG,EAAE,EAAG;YACpD,IAAK,IAAI,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,EAAG;gBACjC,IAAI,CAAC,IAAI,SAAS,CAAC,EAAE,EAAE,IAAI,KAAK,EAAE,IAAI,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,EAAE;oBAC3D,OAAO,EAAE,KAAK;gBAChB;YACF;QACF;QACA;IACF;IAEA,KAAK,QAAQ,QAAQ;IACrB,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,IAAI,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,EAAE,EAAE,OAAO;IAC1D,OAAO,IAAI;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1318, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1324, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/dispatch.js"], "sourcesContent": ["import defaultView from \"../window.js\";\n\nfunction dispatchEvent(node, type, params) {\n  var window = defaultView(node),\n      event = window.CustomEvent;\n\n  if (typeof event === \"function\") {\n    event = new event(type, params);\n  } else {\n    event = window.document.createEvent(\"Event\");\n    if (params) event.initEvent(type, params.bubbles, params.cancelable), event.detail = params.detail;\n    else event.initEvent(type, false, false);\n  }\n\n  node.dispatchEvent(event);\n}\n\nfunction dispatchConstant(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params);\n  };\n}\n\nfunction dispatchFunction(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params.apply(this, arguments));\n  };\n}\n\nexport default function(type, params) {\n  return this.each((typeof params === \"function\"\n      ? dispatchFunction\n      : dispatchConstant)(type, params));\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,cAAc,IAAI,EAAE,IAAI,EAAE,MAAM;IACvC,IAAI,SAAS,CAAA,GAAA,+MAAA,CAAA,UAAW,AAAD,EAAE,OACrB,QAAQ,OAAO,WAAW;IAE9B,IAAI,OAAO,UAAU,YAAY;QAC/B,QAAQ,IAAI,MAAM,MAAM;IAC1B,OAAO;QACL,QAAQ,OAAO,QAAQ,CAAC,WAAW,CAAC;QACpC,IAAI,QAAQ,MAAM,SAAS,CAAC,MAAM,OAAO,OAAO,EAAE,OAAO,UAAU,GAAG,MAAM,MAAM,GAAG,OAAO,MAAM;aAC7F,MAAM,SAAS,CAAC,MAAM,OAAO;IACpC;IAEA,KAAK,aAAa,CAAC;AACrB;AAEA,SAAS,iBAAiB,IAAI,EAAE,MAAM;IACpC,OAAO;QACL,OAAO,cAAc,IAAI,EAAE,MAAM;IACnC;AACF;AAEA,SAAS,iBAAiB,IAAI,EAAE,MAAM;IACpC,OAAO;QACL,OAAO,cAAc,IAAI,EAAE,MAAM,OAAO,KAAK,CAAC,IAAI,EAAE;IACtD;AACF;AAEe,wCAAS,IAAI,EAAE,MAAM;IAClC,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,WAAW,aAC9B,mBACA,gBAAgB,EAAE,MAAM;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1353, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1359, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/selection/iterator.js"], "sourcesContent": ["export default function*() {\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i]) yield node;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAe;IACb,IAAK,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG;QACpE,IAAK,IAAI,QAAQ,MAAM,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,MAAM,IAAI,GAAG,EAAE,EAAG;YACrE,IAAI,OAAO,KAAK,CAAC,EAAE,EAAE,MAAM;QAC7B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1369, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1375, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/select.js"], "sourcesContent": ["import {Selection, root} from \"./selection/index.js\";\n\nexport default function(selector) {\n  return typeof selector === \"string\"\n      ? new Selection([[document.querySelector(selector)]], [document.documentElement])\n      : new Selection([[selector]], root);\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,wCAAS,QAAQ;IAC9B,OAAO,OAAO,aAAa,WACrB,IAAI,2NAAA,CAAA,YAAS,CAAC;QAAC;YAAC,SAAS,aAAa,CAAC;SAAU;KAAC,EAAE;QAAC,SAAS,eAAe;KAAC,IAC9E,IAAI,2NAAA,CAAA,YAAS,CAAC;QAAC;YAAC;SAAS;KAAC,EAAE,2NAAA,CAAA,OAAI;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1393, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1409, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-drag%403.0.0/node_modules/d3-drag/src/noevent.js"], "sourcesContent": ["// These are typically used in conjunction with noevent to ensure that we can\n// preventDefault on the event.\nexport const nonpassive = {passive: false};\nexport const nonpassivecapture = {capture: true, passive: false};\n\nexport function nopropagation(event) {\n  event.stopImmediatePropagation();\n}\n\nexport default function(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n"], "names": [], "mappings": "AAAA,6EAA6E;AAC7E,+BAA+B;;;;;;;AACxB,MAAM,aAAa;IAAC,SAAS;AAAK;AAClC,MAAM,oBAAoB;IAAC,SAAS;IAAM,SAAS;AAAK;AAExD,SAAS,cAAc,KAAK;IACjC,MAAM,wBAAwB;AAChC;AAEe,wCAAS,KAAK;IAC3B,MAAM,cAAc;IACpB,MAAM,wBAAwB;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1431, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1437, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-drag%403.0.0/node_modules/d3-drag/src/nodrag.js"], "sourcesContent": ["import {select} from \"d3-selection\";\nimport noevent, {nonpassivecapture} from \"./noevent.js\";\n\nexport default function(view) {\n  var root = view.document.documentElement,\n      selection = select(view).on(\"dragstart.drag\", noevent, nonpassivecapture);\n  if (\"onselectstart\" in root) {\n    selection.on(\"selectstart.drag\", noevent, nonpassivecapture);\n  } else {\n    root.__noselect = root.style.MozUserSelect;\n    root.style.MozUserSelect = \"none\";\n  }\n}\n\nexport function yesdrag(view, noclick) {\n  var root = view.document.documentElement,\n      selection = select(view).on(\"dragstart.drag\", null);\n  if (noclick) {\n    selection.on(\"click.drag\", noevent, nonpassivecapture);\n    setTimeout(function() { selection.on(\"click.drag\", null); }, 0);\n  }\n  if (\"onselectstart\" in root) {\n    selection.on(\"selectstart.drag\", null);\n  } else {\n    root.style.MozUserSelect = root.__noselect;\n    delete root.__noselect;\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEe,wCAAS,IAAI;IAC1B,IAAI,OAAO,KAAK,QAAQ,CAAC,eAAe,EACpC,YAAY,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE,MAAM,EAAE,CAAC,kBAAkB,sMAAA,CAAA,UAAO,EAAE,sMAAA,CAAA,oBAAiB;IAC5E,IAAI,mBAAmB,MAAM;QAC3B,UAAU,EAAE,CAAC,oBAAoB,sMAAA,CAAA,UAAO,EAAE,sMAAA,CAAA,oBAAiB;IAC7D,OAAO;QACL,KAAK,UAAU,GAAG,KAAK,KAAK,CAAC,aAAa;QAC1C,KAAK,KAAK,CAAC,aAAa,GAAG;IAC7B;AACF;AAEO,SAAS,QAAQ,IAAI,EAAE,OAAO;IACnC,IAAI,OAAO,KAAK,QAAQ,CAAC,eAAe,EACpC,YAAY,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE,MAAM,EAAE,CAAC,kBAAkB;IAClD,IAAI,SAAS;QACX,UAAU,EAAE,CAAC,cAAc,sMAAA,CAAA,UAAO,EAAE,sMAAA,CAAA,oBAAiB;QACrD,WAAW;YAAa,UAAU,EAAE,CAAC,cAAc;QAAO,GAAG;IAC/D;IACA,IAAI,mBAAmB,MAAM;QAC3B,UAAU,EAAE,CAAC,oBAAoB;IACnC,OAAO;QACL,KAAK,KAAK,CAAC,aAAa,GAAG,KAAK,UAAU;QAC1C,OAAO,KAAK,UAAU;IACxB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1469, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1495, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-color%403.1.0/node_modules/d3-color/src/define.js"], "sourcesContent": ["export default function(constructor, factory, prototype) {\n  constructor.prototype = factory.prototype = prototype;\n  prototype.constructor = constructor;\n}\n\nexport function extend(parent, definition) {\n  var prototype = Object.create(parent.prototype);\n  for (var key in definition) prototype[key] = definition[key];\n  return prototype;\n}\n"], "names": [], "mappings": ";;;;AAAe,wCAAS,WAAW,EAAE,OAAO,EAAE,SAAS;IACrD,YAAY,SAAS,GAAG,QAAQ,SAAS,GAAG;IAC5C,UAAU,WAAW,GAAG;AAC1B;AAEO,SAAS,OAAO,MAAM,EAAE,UAAU;IACvC,IAAI,YAAY,OAAO,MAAM,CAAC,OAAO,SAAS;IAC9C,IAAK,IAAI,OAAO,WAAY,SAAS,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI;IAC5D,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1508, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1514, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-color%403.1.0/node_modules/d3-color/src/color.js"], "sourcesContent": ["import define, {extend} from \"./define.js\";\n\nexport function Color() {}\n\nexport var darker = 0.7;\nexport var brighter = 1 / darker;\n\nvar reI = \"\\\\s*([+-]?\\\\d+)\\\\s*\",\n    reN = \"\\\\s*([+-]?(?:\\\\d*\\\\.)?\\\\d+(?:[eE][+-]?\\\\d+)?)\\\\s*\",\n    reP = \"\\\\s*([+-]?(?:\\\\d*\\\\.)?\\\\d+(?:[eE][+-]?\\\\d+)?)%\\\\s*\",\n    reHex = /^#([0-9a-f]{3,8})$/,\n    reRgbInteger = new RegExp(`^rgb\\\\(${reI},${reI},${reI}\\\\)$`),\n    reRgbPercent = new RegExp(`^rgb\\\\(${reP},${reP},${reP}\\\\)$`),\n    reRgbaInteger = new RegExp(`^rgba\\\\(${reI},${reI},${reI},${reN}\\\\)$`),\n    reRgbaPercent = new RegExp(`^rgba\\\\(${reP},${reP},${reP},${reN}\\\\)$`),\n    reHslPercent = new RegExp(`^hsl\\\\(${reN},${reP},${reP}\\\\)$`),\n    reHslaPercent = new RegExp(`^hsla\\\\(${reN},${reP},${reP},${reN}\\\\)$`);\n\nvar named = {\n  aliceblue: 0xf0f8ff,\n  antiquewhite: 0xfaebd7,\n  aqua: 0x00ffff,\n  aquamarine: 0x7fffd4,\n  azure: 0xf0ffff,\n  beige: 0xf5f5dc,\n  bisque: 0xffe4c4,\n  black: 0x000000,\n  blanchedalmond: 0xffebcd,\n  blue: 0x0000ff,\n  blueviolet: 0x8a2be2,\n  brown: 0xa52a2a,\n  burlywood: 0xdeb887,\n  cadetblue: 0x5f9ea0,\n  chartreuse: 0x7fff00,\n  chocolate: 0xd2691e,\n  coral: 0xff7f50,\n  cornflowerblue: 0x6495ed,\n  cornsilk: 0xfff8dc,\n  crimson: 0xdc143c,\n  cyan: 0x00ffff,\n  darkblue: 0x00008b,\n  darkcyan: 0x008b8b,\n  darkgoldenrod: 0xb8860b,\n  darkgray: 0xa9a9a9,\n  darkgreen: 0x006400,\n  darkgrey: 0xa9a9a9,\n  darkkhaki: 0xbdb76b,\n  darkmagenta: 0x8b008b,\n  darkolivegreen: 0x556b2f,\n  darkorange: 0xff8c00,\n  darkorchid: 0x9932cc,\n  darkred: 0x8b0000,\n  darksalmon: 0xe9967a,\n  darkseagreen: 0x8fbc8f,\n  darkslateblue: 0x483d8b,\n  darkslategray: 0x2f4f4f,\n  darkslategrey: 0x2f4f4f,\n  darkturquoise: 0x00ced1,\n  darkviolet: 0x9400d3,\n  deeppink: 0xff1493,\n  deepskyblue: 0x00bfff,\n  dimgray: 0x696969,\n  dimgrey: 0x696969,\n  dodgerblue: 0x1e90ff,\n  firebrick: 0xb22222,\n  floralwhite: 0xfffaf0,\n  forestgreen: 0x228b22,\n  fuchsia: 0xff00ff,\n  gainsboro: 0xdcdcdc,\n  ghostwhite: 0xf8f8ff,\n  gold: 0xffd700,\n  goldenrod: 0xdaa520,\n  gray: 0x808080,\n  green: 0x008000,\n  greenyellow: 0xadff2f,\n  grey: 0x808080,\n  honeydew: 0xf0fff0,\n  hotpink: 0xff69b4,\n  indianred: 0xcd5c5c,\n  indigo: 0x4b0082,\n  ivory: 0xfffff0,\n  khaki: 0xf0e68c,\n  lavender: 0xe6e6fa,\n  lavenderblush: 0xfff0f5,\n  lawngreen: 0x7cfc00,\n  lemonchiffon: 0xfffacd,\n  lightblue: 0xadd8e6,\n  lightcoral: 0xf08080,\n  lightcyan: 0xe0ffff,\n  lightgoldenrodyellow: 0xfafad2,\n  lightgray: 0xd3d3d3,\n  lightgreen: 0x90ee90,\n  lightgrey: 0xd3d3d3,\n  lightpink: 0xffb6c1,\n  lightsalmon: 0xffa07a,\n  lightseagreen: 0x20b2aa,\n  lightskyblue: 0x87cefa,\n  lightslategray: 0x778899,\n  lightslategrey: 0x778899,\n  lightsteelblue: 0xb0c4de,\n  lightyellow: 0xffffe0,\n  lime: 0x00ff00,\n  limegreen: 0x32cd32,\n  linen: 0xfaf0e6,\n  magenta: 0xff00ff,\n  maroon: 0x800000,\n  mediumaquamarine: 0x66cdaa,\n  mediumblue: 0x0000cd,\n  mediumorchid: 0xba55d3,\n  mediumpurple: 0x9370db,\n  mediumseagreen: 0x3cb371,\n  mediumslateblue: 0x7b68ee,\n  mediumspringgreen: 0x00fa9a,\n  mediumturquoise: 0x48d1cc,\n  mediumvioletred: 0xc71585,\n  midnightblue: 0x191970,\n  mintcream: 0xf5fffa,\n  mistyrose: 0xffe4e1,\n  moccasin: 0xffe4b5,\n  navajowhite: 0xffdead,\n  navy: 0x000080,\n  oldlace: 0xfdf5e6,\n  olive: 0x808000,\n  olivedrab: 0x6b8e23,\n  orange: 0xffa500,\n  orangered: 0xff4500,\n  orchid: 0xda70d6,\n  palegoldenrod: 0xeee8aa,\n  palegreen: 0x98fb98,\n  paleturquoise: 0xafeeee,\n  palevioletred: 0xdb7093,\n  papayawhip: 0xffefd5,\n  peachpuff: 0xffdab9,\n  peru: 0xcd853f,\n  pink: 0xffc0cb,\n  plum: 0xdda0dd,\n  powderblue: 0xb0e0e6,\n  purple: 0x800080,\n  rebeccapurple: 0x663399,\n  red: 0xff0000,\n  rosybrown: 0xbc8f8f,\n  royalblue: 0x4169e1,\n  saddlebrown: 0x8b4513,\n  salmon: 0xfa8072,\n  sandybrown: 0xf4a460,\n  seagreen: 0x2e8b57,\n  seashell: 0xfff5ee,\n  sienna: 0xa0522d,\n  silver: 0xc0c0c0,\n  skyblue: 0x87ceeb,\n  slateblue: 0x6a5acd,\n  slategray: 0x708090,\n  slategrey: 0x708090,\n  snow: 0xfffafa,\n  springgreen: 0x00ff7f,\n  steelblue: 0x4682b4,\n  tan: 0xd2b48c,\n  teal: 0x008080,\n  thistle: 0xd8bfd8,\n  tomato: 0xff6347,\n  turquoise: 0x40e0d0,\n  violet: 0xee82ee,\n  wheat: 0xf5deb3,\n  white: 0xffffff,\n  whitesmoke: 0xf5f5f5,\n  yellow: 0xffff00,\n  yellowgreen: 0x9acd32\n};\n\ndefine(Color, color, {\n  copy(channels) {\n    return Object.assign(new this.constructor, this, channels);\n  },\n  displayable() {\n    return this.rgb().displayable();\n  },\n  hex: color_formatHex, // Deprecated! Use color.formatHex.\n  formatHex: color_formatHex,\n  formatHex8: color_formatHex8,\n  formatHsl: color_formatHsl,\n  formatRgb: color_formatRgb,\n  toString: color_formatRgb\n});\n\nfunction color_formatHex() {\n  return this.rgb().formatHex();\n}\n\nfunction color_formatHex8() {\n  return this.rgb().formatHex8();\n}\n\nfunction color_formatHsl() {\n  return hslConvert(this).formatHsl();\n}\n\nfunction color_formatRgb() {\n  return this.rgb().formatRgb();\n}\n\nexport default function color(format) {\n  var m, l;\n  format = (format + \"\").trim().toLowerCase();\n  return (m = reHex.exec(format)) ? (l = m[1].length, m = parseInt(m[1], 16), l === 6 ? rgbn(m) // #ff0000\n      : l === 3 ? new Rgb((m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), ((m & 0xf) << 4) | (m & 0xf), 1) // #f00\n      : l === 8 ? rgba(m >> 24 & 0xff, m >> 16 & 0xff, m >> 8 & 0xff, (m & 0xff) / 0xff) // #ff000000\n      : l === 4 ? rgba((m >> 12 & 0xf) | (m >> 8 & 0xf0), (m >> 8 & 0xf) | (m >> 4 & 0xf0), (m >> 4 & 0xf) | (m & 0xf0), (((m & 0xf) << 4) | (m & 0xf)) / 0xff) // #f000\n      : null) // invalid hex\n      : (m = reRgbInteger.exec(format)) ? new Rgb(m[1], m[2], m[3], 1) // rgb(255, 0, 0)\n      : (m = reRgbPercent.exec(format)) ? new Rgb(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, 1) // rgb(100%, 0%, 0%)\n      : (m = reRgbaInteger.exec(format)) ? rgba(m[1], m[2], m[3], m[4]) // rgba(255, 0, 0, 1)\n      : (m = reRgbaPercent.exec(format)) ? rgba(m[1] * 255 / 100, m[2] * 255 / 100, m[3] * 255 / 100, m[4]) // rgb(100%, 0%, 0%, 1)\n      : (m = reHslPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, 1) // hsl(120, 50%, 50%)\n      : (m = reHslaPercent.exec(format)) ? hsla(m[1], m[2] / 100, m[3] / 100, m[4]) // hsla(120, 50%, 50%, 1)\n      : named.hasOwnProperty(format) ? rgbn(named[format]) // eslint-disable-line no-prototype-builtins\n      : format === \"transparent\" ? new Rgb(NaN, NaN, NaN, 0)\n      : null;\n}\n\nfunction rgbn(n) {\n  return new Rgb(n >> 16 & 0xff, n >> 8 & 0xff, n & 0xff, 1);\n}\n\nfunction rgba(r, g, b, a) {\n  if (a <= 0) r = g = b = NaN;\n  return new Rgb(r, g, b, a);\n}\n\nexport function rgbConvert(o) {\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Rgb;\n  o = o.rgb();\n  return new Rgb(o.r, o.g, o.b, o.opacity);\n}\n\nexport function rgb(r, g, b, opacity) {\n  return arguments.length === 1 ? rgbConvert(r) : new Rgb(r, g, b, opacity == null ? 1 : opacity);\n}\n\nexport function Rgb(r, g, b, opacity) {\n  this.r = +r;\n  this.g = +g;\n  this.b = +b;\n  this.opacity = +opacity;\n}\n\ndefine(Rgb, rgb, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Rgb(this.r * k, this.g * k, this.b * k, this.opacity);\n  },\n  rgb() {\n    return this;\n  },\n  clamp() {\n    return new Rgb(clampi(this.r), clampi(this.g), clampi(this.b), clampa(this.opacity));\n  },\n  displayable() {\n    return (-0.5 <= this.r && this.r < 255.5)\n        && (-0.5 <= this.g && this.g < 255.5)\n        && (-0.5 <= this.b && this.b < 255.5)\n        && (0 <= this.opacity && this.opacity <= 1);\n  },\n  hex: rgb_formatHex, // Deprecated! Use color.formatHex.\n  formatHex: rgb_formatHex,\n  formatHex8: rgb_formatHex8,\n  formatRgb: rgb_formatRgb,\n  toString: rgb_formatRgb\n}));\n\nfunction rgb_formatHex() {\n  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}`;\n}\n\nfunction rgb_formatHex8() {\n  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}${hex((isNaN(this.opacity) ? 1 : this.opacity) * 255)}`;\n}\n\nfunction rgb_formatRgb() {\n  const a = clampa(this.opacity);\n  return `${a === 1 ? \"rgb(\" : \"rgba(\"}${clampi(this.r)}, ${clampi(this.g)}, ${clampi(this.b)}${a === 1 ? \")\" : `, ${a})`}`;\n}\n\nfunction clampa(opacity) {\n  return isNaN(opacity) ? 1 : Math.max(0, Math.min(1, opacity));\n}\n\nfunction clampi(value) {\n  return Math.max(0, Math.min(255, Math.round(value) || 0));\n}\n\nfunction hex(value) {\n  value = clampi(value);\n  return (value < 16 ? \"0\" : \"\") + value.toString(16);\n}\n\nfunction hsla(h, s, l, a) {\n  if (a <= 0) h = s = l = NaN;\n  else if (l <= 0 || l >= 1) h = s = NaN;\n  else if (s <= 0) h = NaN;\n  return new Hsl(h, s, l, a);\n}\n\nexport function hslConvert(o) {\n  if (o instanceof Hsl) return new Hsl(o.h, o.s, o.l, o.opacity);\n  if (!(o instanceof Color)) o = color(o);\n  if (!o) return new Hsl;\n  if (o instanceof Hsl) return o;\n  o = o.rgb();\n  var r = o.r / 255,\n      g = o.g / 255,\n      b = o.b / 255,\n      min = Math.min(r, g, b),\n      max = Math.max(r, g, b),\n      h = NaN,\n      s = max - min,\n      l = (max + min) / 2;\n  if (s) {\n    if (r === max) h = (g - b) / s + (g < b) * 6;\n    else if (g === max) h = (b - r) / s + 2;\n    else h = (r - g) / s + 4;\n    s /= l < 0.5 ? max + min : 2 - max - min;\n    h *= 60;\n  } else {\n    s = l > 0 && l < 1 ? 0 : h;\n  }\n  return new Hsl(h, s, l, o.opacity);\n}\n\nexport function hsl(h, s, l, opacity) {\n  return arguments.length === 1 ? hslConvert(h) : new Hsl(h, s, l, opacity == null ? 1 : opacity);\n}\n\nfunction Hsl(h, s, l, opacity) {\n  this.h = +h;\n  this.s = +s;\n  this.l = +l;\n  this.opacity = +opacity;\n}\n\ndefine(Hsl, hsl, extend(Color, {\n  brighter(k) {\n    k = k == null ? brighter : Math.pow(brighter, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  darker(k) {\n    k = k == null ? darker : Math.pow(darker, k);\n    return new Hsl(this.h, this.s, this.l * k, this.opacity);\n  },\n  rgb() {\n    var h = this.h % 360 + (this.h < 0) * 360,\n        s = isNaN(h) || isNaN(this.s) ? 0 : this.s,\n        l = this.l,\n        m2 = l + (l < 0.5 ? l : 1 - l) * s,\n        m1 = 2 * l - m2;\n    return new Rgb(\n      hsl2rgb(h >= 240 ? h - 240 : h + 120, m1, m2),\n      hsl2rgb(h, m1, m2),\n      hsl2rgb(h < 120 ? h + 240 : h - 120, m1, m2),\n      this.opacity\n    );\n  },\n  clamp() {\n    return new Hsl(clamph(this.h), clampt(this.s), clampt(this.l), clampa(this.opacity));\n  },\n  displayable() {\n    return (0 <= this.s && this.s <= 1 || isNaN(this.s))\n        && (0 <= this.l && this.l <= 1)\n        && (0 <= this.opacity && this.opacity <= 1);\n  },\n  formatHsl() {\n    const a = clampa(this.opacity);\n    return `${a === 1 ? \"hsl(\" : \"hsla(\"}${clamph(this.h)}, ${clampt(this.s) * 100}%, ${clampt(this.l) * 100}%${a === 1 ? \")\" : `, ${a})`}`;\n  }\n}));\n\nfunction clamph(value) {\n  value = (value || 0) % 360;\n  return value < 0 ? value + 360 : value;\n}\n\nfunction clampt(value) {\n  return Math.max(0, Math.min(1, value || 0));\n}\n\n/* From FvD 13.37, CSS Color Module Level 3 */\nfunction hsl2rgb(h, m1, m2) {\n  return (h < 60 ? m1 + (m2 - m1) * h / 60\n      : h < 180 ? m2\n      : h < 240 ? m1 + (m2 - m1) * (240 - h) / 60\n      : m1) * 255;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAEO,SAAS,SAAS;AAElB,IAAI,SAAS;AACb,IAAI,WAAW,IAAI;AAE1B,IAAI,MAAM,uBACN,MAAM,qDACN,MAAM,sDACN,QAAQ,sBACR,eAAe,IAAI,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,GAC3D,eAAe,IAAI,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,GAC3D,gBAAgB,IAAI,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,GACpE,gBAAgB,IAAI,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,GACpE,eAAe,IAAI,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,GAC3D,gBAAgB,IAAI,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC;AAExE,IAAI,QAAQ;IACV,WAAW;IACX,cAAc;IACd,MAAM;IACN,YAAY;IACZ,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,gBAAgB;IAChB,MAAM;IACN,YAAY;IACZ,OAAO;IACP,WAAW;IACX,WAAW;IACX,YAAY;IACZ,WAAW;IACX,OAAO;IACP,gBAAgB;IAChB,UAAU;IACV,SAAS;IACT,MAAM;IACN,UAAU;IACV,UAAU;IACV,eAAe;IACf,UAAU;IACV,WAAW;IACX,UAAU;IACV,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,YAAY;IACZ,YAAY;IACZ,SAAS;IACT,YAAY;IACZ,cAAc;IACd,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,YAAY;IACZ,UAAU;IACV,aAAa;IACb,SAAS;IACT,SAAS;IACT,YAAY;IACZ,WAAW;IACX,aAAa;IACb,aAAa;IACb,SAAS;IACT,WAAW;IACX,YAAY;IACZ,MAAM;IACN,WAAW;IACX,MAAM;IACN,OAAO;IACP,aAAa;IACb,MAAM;IACN,UAAU;IACV,SAAS;IACT,WAAW;IACX,QAAQ;IACR,OAAO;IACP,OAAO;IACP,UAAU;IACV,eAAe;IACf,WAAW;IACX,cAAc;IACd,WAAW;IACX,YAAY;IACZ,WAAW;IACX,sBAAsB;IACtB,WAAW;IACX,YAAY;IACZ,WAAW;IACX,WAAW;IACX,aAAa;IACb,eAAe;IACf,cAAc;IACd,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,aAAa;IACb,MAAM;IACN,WAAW;IACX,OAAO;IACP,SAAS;IACT,QAAQ;IACR,kBAAkB;IAClB,YAAY;IACZ,cAAc;IACd,cAAc;IACd,gBAAgB;IAChB,iBAAiB;IACjB,mBAAmB;IACnB,iBAAiB;IACjB,iBAAiB;IACjB,cAAc;IACd,WAAW;IACX,WAAW;IACX,UAAU;IACV,aAAa;IACb,MAAM;IACN,SAAS;IACT,OAAO;IACP,WAAW;IACX,QAAQ;IACR,WAAW;IACX,QAAQ;IACR,eAAe;IACf,WAAW;IACX,eAAe;IACf,eAAe;IACf,YAAY;IACZ,WAAW;IACX,MAAM;IACN,MAAM;IACN,MAAM;IACN,YAAY;IACZ,QAAQ;IACR,eAAe;IACf,KAAK;IACL,WAAW;IACX,WAAW;IACX,aAAa;IACb,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,WAAW;IACX,WAAW;IACX,WAAW;IACX,MAAM;IACN,aAAa;IACb,WAAW;IACX,KAAK;IACL,MAAM;IACN,SAAS;IACT,QAAQ;IACR,WAAW;IACX,QAAQ;IACR,OAAO;IACP,OAAO;IACP,YAAY;IACZ,QAAQ;IACR,aAAa;AACf;AAEA,CAAA,GAAA,uMAAA,CAAA,UAAM,AAAD,EAAE,OAAO,OAAO;IACnB,MAAK,QAAQ;QACX,OAAO,OAAO,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE;IACnD;IACA;QACE,OAAO,IAAI,CAAC,GAAG,GAAG,WAAW;IAC/B;IACA,KAAK;IACL,WAAW;IACX,YAAY;IACZ,WAAW;IACX,WAAW;IACX,UAAU;AACZ;AAEA,SAAS;IACP,OAAO,IAAI,CAAC,GAAG,GAAG,SAAS;AAC7B;AAEA,SAAS;IACP,OAAO,IAAI,CAAC,GAAG,GAAG,UAAU;AAC9B;AAEA,SAAS;IACP,OAAO,WAAW,IAAI,EAAE,SAAS;AACnC;AAEA,SAAS;IACP,OAAO,IAAI,CAAC,GAAG,GAAG,SAAS;AAC7B;AAEe,SAAS,MAAM,MAAM;IAClC,IAAI,GAAG;IACP,SAAS,CAAC,SAAS,EAAE,EAAE,IAAI,GAAG,WAAW;IACzC,OAAO,CAAC,IAAI,MAAM,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,SAAS,CAAC,CAAC,EAAE,EAAE,KAAK,MAAM,IAAI,KAAK,GAAG,UAAU;OAClG,MAAM,IAAI,IAAI,IAAI,AAAC,KAAK,IAAI,MAAQ,KAAK,IAAI,MAAO,AAAC,KAAK,IAAI,MAAQ,IAAI,MAAO,AAAC,CAAC,IAAI,GAAG,KAAK,IAAM,IAAI,KAAM,GAAG,OAAO;OACzH,MAAM,IAAI,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI,MAAM,YAAY;OAC7F,MAAM,IAAI,KAAK,AAAC,KAAK,KAAK,MAAQ,KAAK,IAAI,MAAO,AAAC,KAAK,IAAI,MAAQ,KAAK,IAAI,MAAO,AAAC,KAAK,IAAI,MAAQ,IAAI,MAAO,CAAC,AAAC,CAAC,IAAI,GAAG,KAAK,IAAM,IAAI,GAAI,IAAI,MAAM,QAAQ;OAChK,IAAI,EAAE,cAAc;OACpB,CAAC,IAAI,aAAa,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,iBAAiB;OAChF,CAAC,IAAI,aAAa,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,GAAG,MAAM,KAAK,GAAG,oBAAoB;OACvH,CAAC,IAAI,cAAc,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,qBAAqB;OACrF,CAAC,IAAI,cAAc,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC,EAAE,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,EAAE,uBAAuB;OAC3H,CAAC,IAAI,aAAa,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,KAAK,GAAG,qBAAqB;OAC7F,CAAC,IAAI,cAAc,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,EAAE,yBAAyB;OACrG,MAAM,cAAc,CAAC,UAAU,KAAK,KAAK,CAAC,OAAO,EAAE,4CAA4C;OAC/F,WAAW,gBAAgB,IAAI,IAAI,KAAK,KAAK,KAAK,KAClD;AACR;AAEA,SAAS,KAAK,CAAC;IACb,OAAO,IAAI,IAAI,KAAK,KAAK,MAAM,KAAK,IAAI,MAAM,IAAI,MAAM;AAC1D;AAEA,SAAS,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACtB,IAAI,KAAK,GAAG,IAAI,IAAI,IAAI;IACxB,OAAO,IAAI,IAAI,GAAG,GAAG,GAAG;AAC1B;AAEO,SAAS,WAAW,CAAC;IAC1B,IAAI,CAAC,CAAC,aAAa,KAAK,GAAG,IAAI,MAAM;IACrC,IAAI,CAAC,GAAG,OAAO,IAAI;IACnB,IAAI,EAAE,GAAG;IACT,OAAO,IAAI,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,OAAO;AACzC;AAEO,SAAS,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAClC,OAAO,UAAU,MAAM,KAAK,IAAI,WAAW,KAAK,IAAI,IAAI,GAAG,GAAG,GAAG,WAAW,OAAO,IAAI;AACzF;AAEO,SAAS,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAClC,IAAI,CAAC,CAAC,GAAG,CAAC;IACV,IAAI,CAAC,CAAC,GAAG,CAAC;IACV,IAAI,CAAC,CAAC,GAAG,CAAC;IACV,IAAI,CAAC,OAAO,GAAG,CAAC;AAClB;AAEA,CAAA,GAAA,uMAAA,CAAA,UAAM,AAAD,EAAE,KAAK,KAAK,CAAA,GAAA,uMAAA,CAAA,SAAM,AAAD,EAAE,OAAO;IAC7B,UAAS,CAAC;QACR,IAAI,KAAK,OAAO,WAAW,KAAK,GAAG,CAAC,UAAU;QAC9C,OAAO,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;IACjE;IACA,QAAO,CAAC;QACN,IAAI,KAAK,OAAO,SAAS,KAAK,GAAG,CAAC,QAAQ;QAC1C,OAAO,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;IACjE;IACA;QACE,OAAO,IAAI;IACb;IACA;QACE,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC,GAAG,OAAO,IAAI,CAAC,OAAO;IACpF;IACA;QACE,OAAO,AAAC,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,SAC3B,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,SAC3B,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,SAC3B,KAAK,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,IAAI;IAC/C;IACA,KAAK;IACL,WAAW;IACX,YAAY;IACZ,WAAW;IACX,UAAU;AACZ;AAEA,SAAS;IACP,OAAO,CAAC,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG;AACtD;AAEA,SAAS;IACP,OAAO,CAAC,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,MAAM;AAC5G;AAEA,SAAS;IACP,MAAM,IAAI,OAAO,IAAI,CAAC,OAAO;IAC7B,OAAO,GAAG,MAAM,IAAI,SAAS,UAAU,OAAO,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC,IAAI,MAAM,IAAI,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE;AAC3H;AAEA,SAAS,OAAO,OAAO;IACrB,OAAO,MAAM,WAAW,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AACtD;AAEA,SAAS,OAAO,KAAK;IACnB,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,UAAU;AACxD;AAEA,SAAS,IAAI,KAAK;IAChB,QAAQ,OAAO;IACf,OAAO,CAAC,QAAQ,KAAK,MAAM,EAAE,IAAI,MAAM,QAAQ,CAAC;AAClD;AAEA,SAAS,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACtB,IAAI,KAAK,GAAG,IAAI,IAAI,IAAI;SACnB,IAAI,KAAK,KAAK,KAAK,GAAG,IAAI,IAAI;SAC9B,IAAI,KAAK,GAAG,IAAI;IACrB,OAAO,IAAI,IAAI,GAAG,GAAG,GAAG;AAC1B;AAEO,SAAS,WAAW,CAAC;IAC1B,IAAI,aAAa,KAAK,OAAO,IAAI,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,OAAO;IAC7D,IAAI,CAAC,CAAC,aAAa,KAAK,GAAG,IAAI,MAAM;IACrC,IAAI,CAAC,GAAG,OAAO,IAAI;IACnB,IAAI,aAAa,KAAK,OAAO;IAC7B,IAAI,EAAE,GAAG;IACT,IAAI,IAAI,EAAE,CAAC,GAAG,KACV,IAAI,EAAE,CAAC,GAAG,KACV,IAAI,EAAE,CAAC,GAAG,KACV,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,IACrB,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG,IACrB,IAAI,KACJ,IAAI,MAAM,KACV,IAAI,CAAC,MAAM,GAAG,IAAI;IACtB,IAAI,GAAG;QACL,IAAI,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;aACtC,IAAI,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;aACjC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;QACvB,KAAK,IAAI,MAAM,MAAM,MAAM,IAAI,MAAM;QACrC,KAAK;IACP,OAAO;QACL,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;IAC3B;IACA,OAAO,IAAI,IAAI,GAAG,GAAG,GAAG,EAAE,OAAO;AACnC;AAEO,SAAS,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAClC,OAAO,UAAU,MAAM,KAAK,IAAI,WAAW,KAAK,IAAI,IAAI,GAAG,GAAG,GAAG,WAAW,OAAO,IAAI;AACzF;AAEA,SAAS,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO;IAC3B,IAAI,CAAC,CAAC,GAAG,CAAC;IACV,IAAI,CAAC,CAAC,GAAG,CAAC;IACV,IAAI,CAAC,CAAC,GAAG,CAAC;IACV,IAAI,CAAC,OAAO,GAAG,CAAC;AAClB;AAEA,CAAA,GAAA,uMAAA,CAAA,UAAM,AAAD,EAAE,KAAK,KAAK,CAAA,GAAA,uMAAA,CAAA,SAAM,AAAD,EAAE,OAAO;IAC7B,UAAS,CAAC;QACR,IAAI,KAAK,OAAO,WAAW,KAAK,GAAG,CAAC,UAAU;QAC9C,OAAO,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;IACzD;IACA,QAAO,CAAC;QACN,IAAI,KAAK,OAAO,SAAS,KAAK,GAAG,CAAC,QAAQ;QAC1C,OAAO,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;IACzD;IACA;QACE,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,KAClC,IAAI,MAAM,MAAM,MAAM,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,EAC1C,IAAI,IAAI,CAAC,CAAC,EACV,KAAK,IAAI,CAAC,IAAI,MAAM,IAAI,IAAI,CAAC,IAAI,GACjC,KAAK,IAAI,IAAI;QACjB,OAAO,IAAI,IACT,QAAQ,KAAK,MAAM,IAAI,MAAM,IAAI,KAAK,IAAI,KAC1C,QAAQ,GAAG,IAAI,KACf,QAAQ,IAAI,MAAM,IAAI,MAAM,IAAI,KAAK,IAAI,KACzC,IAAI,CAAC,OAAO;IAEhB;IACA;QACE,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC,GAAG,OAAO,IAAI,CAAC,OAAO;IACpF;IACA;QACE,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,CAAC,KAC3C,KAAK,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,KACzB,KAAK,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,IAAI;IAC/C;IACA;QACE,MAAM,IAAI,OAAO,IAAI,CAAC,OAAO;QAC7B,OAAO,GAAG,MAAM,IAAI,SAAS,UAAU,OAAO,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC,IAAI,IAAI,GAAG,EAAE,OAAO,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE,MAAM,IAAI,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE;IACzI;AACF;AAEA,SAAS,OAAO,KAAK;IACnB,QAAQ,CAAC,SAAS,CAAC,IAAI;IACvB,OAAO,QAAQ,IAAI,QAAQ,MAAM;AACnC;AAEA,SAAS,OAAO,KAAK;IACnB,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS;AAC1C;AAEA,4CAA4C,GAC5C,SAAS,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE;IACxB,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,KAAK,EAAE,IAAI,IAAI,KAChC,IAAI,MAAM,KACV,IAAI,MAAM,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,KACvC,EAAE,IAAI;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1857, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1873, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-interpolate%403.0.1/node_modules/d3-interpolate/src/basis.js"], "sourcesContent": ["export function basis(t1, v0, v1, v2, v3) {\n  var t2 = t1 * t1, t3 = t2 * t1;\n  return ((1 - 3 * t1 + 3 * t2 - t3) * v0\n      + (4 - 6 * t2 + 3 * t3) * v1\n      + (1 + 3 * t1 + 3 * t2 - 3 * t3) * v2\n      + t3 * v3) / 6;\n}\n\nexport default function(values) {\n  var n = values.length - 1;\n  return function(t) {\n    var i = t <= 0 ? (t = 0) : t >= 1 ? (t = 1, n - 1) : Math.floor(t * n),\n        v1 = values[i],\n        v2 = values[i + 1],\n        v0 = i > 0 ? values[i - 1] : 2 * v1 - v2,\n        v3 = i < n - 1 ? values[i + 2] : 2 * v2 - v1;\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IACtC,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK;IAC5B,OAAO,CAAC,CAAC,IAAI,IAAI,KAAK,IAAI,KAAK,EAAE,IAAI,KAC/B,CAAC,IAAI,IAAI,KAAK,IAAI,EAAE,IAAI,KACxB,CAAC,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,EAAE,IAAI,KACjC,KAAK,EAAE,IAAI;AACnB;AAEe,wCAAS,MAAM;IAC5B,IAAI,IAAI,OAAO,MAAM,GAAG;IACxB,OAAO,SAAS,CAAC;QACf,IAAI,IAAI,KAAK,IAAK,IAAI,IAAK,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,IAChE,KAAK,MAAM,CAAC,EAAE,EACd,KAAK,MAAM,CAAC,IAAI,EAAE,EAClB,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE,GAAG,IAAI,KAAK,IACtC,KAAK,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE,GAAG,IAAI,KAAK;QAC9C,OAAO,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI;IAC5C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1888, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1894, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-interpolate%403.0.1/node_modules/d3-interpolate/src/basisClosed.js"], "sourcesContent": ["import {basis} from \"./basis.js\";\n\nexport default function(values) {\n  var n = values.length;\n  return function(t) {\n    var i = Math.floor(((t %= 1) < 0 ? ++t : t) * n),\n        v0 = values[(i + n - 1) % n],\n        v1 = values[i % n],\n        v2 = values[(i + 1) % n],\n        v3 = values[(i + 2) % n];\n    return basis((t - i / n) * n, v0, v1, v2, v3);\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,wCAAS,MAAM;IAC5B,IAAI,IAAI,OAAO,MAAM;IACrB,OAAO,SAAS,CAAC;QACf,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,IAC1C,KAAK,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,EAC5B,KAAK,MAAM,CAAC,IAAI,EAAE,EAClB,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,EACxB,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE;QAC5B,OAAO,CAAA,GAAA,kNAAA,CAAA,QAAK,AAAD,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI;IAC5C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1906, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1912, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-interpolate%403.0.1/node_modules/d3-interpolate/src/constant.js"], "sourcesContent": ["export default x => () => x;\n"], "names": [], "mappings": ";;;uCAAe,CAAA,IAAK,IAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1916, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1922, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-interpolate%403.0.1/node_modules/d3-interpolate/src/color.js"], "sourcesContent": ["import constant from \"./constant.js\";\n\nfunction linear(a, d) {\n  return function(t) {\n    return a + t * d;\n  };\n}\n\nfunction exponential(a, b, y) {\n  return a = Math.pow(a, y), b = Math.pow(b, y) - a, y = 1 / y, function(t) {\n    return Math.pow(a + t * b, y);\n  };\n}\n\nexport function hue(a, b) {\n  var d = b - a;\n  return d ? linear(a, d > 180 || d < -180 ? d - 360 * Math.round(d / 360) : d) : constant(isNaN(a) ? b : a);\n}\n\nexport function gamma(y) {\n  return (y = +y) === 1 ? nogamma : function(a, b) {\n    return b - a ? exponential(a, b, y) : constant(isNaN(a) ? b : a);\n  };\n}\n\nexport default function nogamma(a, b) {\n  var d = b - a;\n  return d ? linear(a, d) : constant(isNaN(a) ? b : a);\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,SAAS,OAAO,CAAC,EAAE,CAAC;IAClB,OAAO,SAAS,CAAC;QACf,OAAO,IAAI,IAAI;IACjB;AACF;AAEA,SAAS,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC;IAC1B,OAAO,IAAI,KAAK,GAAG,CAAC,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI,IAAI,GAAG,SAAS,CAAC;QACtE,OAAO,KAAK,GAAG,CAAC,IAAI,IAAI,GAAG;IAC7B;AACF;AAEO,SAAS,IAAI,CAAC,EAAE,CAAC;IACtB,IAAI,IAAI,IAAI;IACZ,OAAO,IAAI,OAAO,GAAG,IAAI,OAAO,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC,IAAI,OAAO,KAAK,CAAA,GAAA,qNAAA,CAAA,UAAQ,AAAD,EAAE,MAAM,KAAK,IAAI;AAC1G;AAEO,SAAS,MAAM,CAAC;IACrB,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,UAAU,SAAS,CAAC,EAAE,CAAC;QAC7C,OAAO,IAAI,IAAI,YAAY,GAAG,GAAG,KAAK,CAAA,GAAA,qNAAA,CAAA,UAAQ,AAAD,EAAE,MAAM,KAAK,IAAI;IAChE;AACF;AAEe,SAAS,QAAQ,CAAC,EAAE,CAAC;IAClC,IAAI,IAAI,IAAI;IACZ,OAAO,IAAI,OAAO,GAAG,KAAK,CAAA,GAAA,qNAAA,CAAA,UAAQ,AAAD,EAAE,MAAM,KAAK,IAAI;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1952, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1958, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-interpolate%403.0.1/node_modules/d3-interpolate/src/rgb.js"], "sourcesContent": ["import {rgb as colorRgb} from \"d3-color\";\nimport basis from \"./basis.js\";\nimport basisClosed from \"./basisClosed.js\";\nimport nogamma, {gamma} from \"./color.js\";\n\nexport default (function rgbGamma(y) {\n  var color = gamma(y);\n\n  function rgb(start, end) {\n    var r = color((start = colorRgb(start)).r, (end = colorRgb(end)).r),\n        g = color(start.g, end.g),\n        b = color(start.b, end.b),\n        opacity = nogamma(start.opacity, end.opacity);\n    return function(t) {\n      start.r = r(t);\n      start.g = g(t);\n      start.b = b(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n\n  rgb.gamma = rgbGamma;\n\n  return rgb;\n})(1);\n\nfunction rgbSpline(spline) {\n  return function(colors) {\n    var n = colors.length,\n        r = new Array(n),\n        g = new Array(n),\n        b = new Array(n),\n        i, color;\n    for (i = 0; i < n; ++i) {\n      color = colorRgb(colors[i]);\n      r[i] = color.r || 0;\n      g[i] = color.g || 0;\n      b[i] = color.b || 0;\n    }\n    r = spline(r);\n    g = spline(g);\n    b = spline(b);\n    color.opacity = 1;\n    return function(t) {\n      color.r = r(t);\n      color.g = g(t);\n      color.b = b(t);\n      return color + \"\";\n    };\n  };\n}\n\nexport var rgbBasis = rgbSpline(basis);\nexport var rgbBasisClosed = rgbSpline(basisClosed);\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;uCAEe,AAAC,SAAS,SAAS,CAAC;IACjC,IAAI,QAAQ,CAAA,GAAA,kNAAA,CAAA,QAAK,AAAD,EAAE;IAElB,SAAS,IAAI,KAAK,EAAE,GAAG;QACrB,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAA,GAAA,sMAAA,CAAA,MAAQ,AAAD,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,MAAM,CAAA,GAAA,sMAAA,CAAA,MAAQ,AAAD,EAAE,IAAI,EAAE,CAAC,GAC9D,IAAI,MAAM,MAAM,CAAC,EAAE,IAAI,CAAC,GACxB,IAAI,MAAM,MAAM,CAAC,EAAE,IAAI,CAAC,GACxB,UAAU,CAAA,GAAA,kNAAA,CAAA,UAAO,AAAD,EAAE,MAAM,OAAO,EAAE,IAAI,OAAO;QAChD,OAAO,SAAS,CAAC;YACf,MAAM,CAAC,GAAG,EAAE;YACZ,MAAM,CAAC,GAAG,EAAE;YACZ,MAAM,CAAC,GAAG,EAAE;YACZ,MAAM,OAAO,GAAG,QAAQ;YACxB,OAAO,QAAQ;QACjB;IACF;IAEA,IAAI,KAAK,GAAG;IAEZ,OAAO;AACT,EAAG;AAEH,SAAS,UAAU,MAAM;IACvB,OAAO,SAAS,MAAM;QACpB,IAAI,IAAI,OAAO,MAAM,EACjB,IAAI,IAAI,MAAM,IACd,IAAI,IAAI,MAAM,IACd,IAAI,IAAI,MAAM,IACd,GAAG;QACP,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACtB,QAAQ,CAAA,GAAA,sMAAA,CAAA,MAAQ,AAAD,EAAE,MAAM,CAAC,EAAE;YAC1B,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI;YAClB,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI;YAClB,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI;QACpB;QACA,IAAI,OAAO;QACX,IAAI,OAAO;QACX,IAAI,OAAO;QACX,MAAM,OAAO,GAAG;QAChB,OAAO,SAAS,CAAC;YACf,MAAM,CAAC,GAAG,EAAE;YACZ,MAAM,CAAC,GAAG,EAAE;YACZ,MAAM,CAAC,GAAG,EAAE;YACZ,OAAO,QAAQ;QACjB;IACF;AACF;AAEO,IAAI,WAAW,UAAU,kNAAA,CAAA,UAAK;AAC9B,IAAI,iBAAiB,UAAU,wNAAA,CAAA,UAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2009, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2015, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-interpolate%403.0.1/node_modules/d3-interpolate/src/value.js"], "sourcesContent": ["import {color} from \"d3-color\";\nimport rgb from \"./rgb.js\";\nimport {genericArray} from \"./array.js\";\nimport date from \"./date.js\";\nimport number from \"./number.js\";\nimport object from \"./object.js\";\nimport string from \"./string.js\";\nimport constant from \"./constant.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  var t = typeof b, c;\n  return b == null || t === \"boolean\" ? constant(b)\n      : (t === \"number\" ? number\n      : t === \"string\" ? ((c = color(b)) ? (b = c, rgb) : string)\n      : b instanceof color ? rgb\n      : b instanceof Date ? date\n      : isNumberArray(b) ? numberArray\n      : Array.isArray(b) ? genericArray\n      : typeof b.valueOf !== \"function\" && typeof b.toString !== \"function\" || isNaN(b) ? object\n      : number)(a, b);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEe,wCAAS,CAAC,EAAE,CAAC;IAC1B,IAAI,IAAI,OAAO,GAAG;IAClB,OAAO,KAAK,QAAQ,MAAM,YAAY,CAAA,GAAA,qNAAA,CAAA,UAAQ,AAAD,EAAE,KACzC,CAAC,MAAM,WAAW,mNAAA,CAAA,UAAM,GACxB,MAAM,WAAY,CAAC,IAAI,CAAA,GAAA,0OAAA,CAAA,QAAK,AAAD,EAAE,EAAE,IAAI,CAAC,IAAI,GAAG,gNAAA,CAAA,UAAG,IAAI,mNAAA,CAAA,UAAM,GACxD,aAAa,0OAAA,CAAA,QAAK,GAAG,gNAAA,CAAA,UAAG,GACxB,aAAa,OAAO,iNAAA,CAAA,UAAI,GACxB,CAAA,GAAA,wNAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,wNAAA,CAAA,UAAW,GAC9B,MAAM,OAAO,CAAC,KAAK,kNAAA,CAAA,eAAY,GAC/B,OAAO,EAAE,OAAO,KAAK,cAAc,OAAO,EAAE,QAAQ,KAAK,cAAc,MAAM,KAAK,mNAAA,CAAA,UAAM,GACxF,mNAAA,CAAA,UAAM,EAAE,GAAG;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2040, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2046, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-interpolate%403.0.1/node_modules/d3-interpolate/src/numberArray.js"], "sourcesContent": ["export default function(a, b) {\n  if (!b) b = [];\n  var n = a ? Math.min(b.length, a.length) : 0,\n      c = b.slice(),\n      i;\n  return function(t) {\n    for (i = 0; i < n; ++i) c[i] = a[i] * (1 - t) + b[i] * t;\n    return c;\n  };\n}\n\nexport function isNumberArray(x) {\n  return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}\n"], "names": [], "mappings": ";;;;AAAe,wCAAS,CAAC,EAAE,CAAC;IAC1B,IAAI,CAAC,GAAG,IAAI,EAAE;IACd,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE,MAAM,EAAE,EAAE,MAAM,IAAI,GACvC,IAAI,EAAE,KAAK,IACX;IACJ,OAAO,SAAS,CAAC;QACf,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG;QACvD,OAAO;IACT;AACF;AAEO,SAAS,cAAc,CAAC;IAC7B,OAAO,YAAY,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,QAAQ;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2061, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2067, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-interpolate%403.0.1/node_modules/d3-interpolate/src/array.js"], "sourcesContent": ["import value from \"./value.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  return (isNumberArray(b) ? numberArray : genericArray)(a, b);\n}\n\nexport function genericArray(a, b) {\n  var nb = b ? b.length : 0,\n      na = a ? Math.min(nb, a.length) : 0,\n      x = new Array(na),\n      c = new Array(nb),\n      i;\n\n  for (i = 0; i < na; ++i) x[i] = value(a[i], b[i]);\n  for (; i < nb; ++i) c[i] = b[i];\n\n  return function(t) {\n    for (i = 0; i < na; ++i) c[i] = x[i](t);\n    return c;\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEe,wCAAS,CAAC,EAAE,CAAC;IAC1B,OAAO,CAAC,CAAA,GAAA,wNAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,wNAAA,CAAA,UAAW,GAAG,YAAY,EAAE,GAAG;AAC5D;AAEO,SAAS,aAAa,CAAC,EAAE,CAAC;IAC/B,IAAI,KAAK,IAAI,EAAE,MAAM,GAAG,GACpB,KAAK,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE,MAAM,IAAI,GAClC,IAAI,IAAI,MAAM,KACd,IAAI,IAAI,MAAM,KACd;IAEJ,IAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG,CAAC,CAAC,EAAE,GAAG,CAAA,GAAA,kNAAA,CAAA,UAAK,AAAD,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;IAChD,MAAO,IAAI,IAAI,EAAE,EAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAE/B,OAAO,SAAS,CAAC;QACf,IAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;QACrC,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2087, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2093, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-interpolate%403.0.1/node_modules/d3-interpolate/src/date.js"], "sourcesContent": ["export default function(a, b) {\n  var d = new Date;\n  return a = +a, b = +b, function(t) {\n    return d.setTime(a * (1 - t) + b * t), d;\n  };\n}\n"], "names": [], "mappings": ";;;AAAe,wCAAS,CAAC,EAAE,CAAC;IAC1B,IAAI,IAAI,IAAI;IACZ,OAAO,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,SAAS,CAAC;QAC/B,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI;IACzC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2102, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2108, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-interpolate%403.0.1/node_modules/d3-interpolate/src/number.js"], "sourcesContent": ["export default function(a, b) {\n  return a = +a, b = +b, function(t) {\n    return a * (1 - t) + b * t;\n  };\n}\n"], "names": [], "mappings": ";;;AAAe,wCAAS,CAAC,EAAE,CAAC;IAC1B,OAAO,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,SAAS,CAAC;QAC/B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IAC3B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2116, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2122, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-interpolate%403.0.1/node_modules/d3-interpolate/src/object.js"], "sourcesContent": ["import value from \"./value.js\";\n\nexport default function(a, b) {\n  var i = {},\n      c = {},\n      k;\n\n  if (a === null || typeof a !== \"object\") a = {};\n  if (b === null || typeof b !== \"object\") b = {};\n\n  for (k in b) {\n    if (k in a) {\n      i[k] = value(a[k], b[k]);\n    } else {\n      c[k] = b[k];\n    }\n  }\n\n  return function(t) {\n    for (k in i) c[k] = i[k](t);\n    return c;\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,wCAAS,CAAC,EAAE,CAAC;IAC1B,IAAI,IAAI,CAAC,GACL,IAAI,CAAC,GACL;IAEJ,IAAI,MAAM,QAAQ,OAAO,MAAM,UAAU,IAAI,CAAC;IAC9C,IAAI,MAAM,QAAQ,OAAO,MAAM,UAAU,IAAI,CAAC;IAE9C,IAAK,KAAK,EAAG;QACX,IAAI,KAAK,GAAG;YACV,CAAC,CAAC,EAAE,GAAG,CAAA,GAAA,kNAAA,CAAA,UAAK,AAAD,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;QACzB,OAAO;YACL,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACb;IACF;IAEA,OAAO,SAAS,CAAC;QACf,IAAK,KAAK,EAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;QACzB,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2143, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2149, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-interpolate%403.0.1/node_modules/d3-interpolate/src/string.js"], "sourcesContent": ["import number from \"./number.js\";\n\nvar reA = /[-+]?(?:\\d+\\.?\\d*|\\.?\\d+)(?:[eE][-+]?\\d+)?/g,\n    reB = new RegExp(reA.source, \"g\");\n\nfunction zero(b) {\n  return function() {\n    return b;\n  };\n}\n\nfunction one(b) {\n  return function(t) {\n    return b(t) + \"\";\n  };\n}\n\nexport default function(a, b) {\n  var bi = reA.lastIndex = reB.lastIndex = 0, // scan index for next number in b\n      am, // current match in a\n      bm, // current match in b\n      bs, // string preceding current number in b, if any\n      i = -1, // index in s\n      s = [], // string constants and placeholders\n      q = []; // number interpolators\n\n  // Coerce inputs to strings.\n  a = a + \"\", b = b + \"\";\n\n  // Interpolate pairs of numbers in a & b.\n  while ((am = reA.exec(a))\n      && (bm = reB.exec(b))) {\n    if ((bs = bm.index) > bi) { // a string precedes the next number in b\n      bs = b.slice(bi, bs);\n      if (s[i]) s[i] += bs; // coalesce with previous string\n      else s[++i] = bs;\n    }\n    if ((am = am[0]) === (bm = bm[0])) { // numbers in a & b match\n      if (s[i]) s[i] += bm; // coalesce with previous string\n      else s[++i] = bm;\n    } else { // interpolate non-matching numbers\n      s[++i] = null;\n      q.push({i: i, x: number(am, bm)});\n    }\n    bi = reB.lastIndex;\n  }\n\n  // Add remains of b.\n  if (bi < b.length) {\n    bs = b.slice(bi);\n    if (s[i]) s[i] += bs; // coalesce with previous string\n    else s[++i] = bs;\n  }\n\n  // Special optimization for only a single match.\n  // Otherwise, interpolate each of the numbers and rejoin the string.\n  return s.length < 2 ? (q[0]\n      ? one(q[0].x)\n      : zero(b))\n      : (b = q.length, function(t) {\n          for (var i = 0, o; i < b; ++i) s[(o = q[i]).i] = o.x(t);\n          return s.join(\"\");\n        });\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,IAAI,MAAM,+CACN,MAAM,IAAI,OAAO,IAAI,MAAM,EAAE;AAEjC,SAAS,KAAK,CAAC;IACb,OAAO;QACL,OAAO;IACT;AACF;AAEA,SAAS,IAAI,CAAC;IACZ,OAAO,SAAS,CAAC;QACf,OAAO,EAAE,KAAK;IAChB;AACF;AAEe,wCAAS,CAAC,EAAE,CAAC;IAC1B,IAAI,KAAK,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,GACrC,IACA,IACA,IACA,IAAI,CAAC,GACL,IAAI,EAAE,EACN,IAAI,EAAE,EAAE,uBAAuB;IAEnC,4BAA4B;IAC5B,IAAI,IAAI,IAAI,IAAI,IAAI;IAEpB,yCAAyC;IACzC,MAAO,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE,KACjB,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE,EAAG;QACzB,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,IAAI;YACxB,KAAK,EAAE,KAAK,CAAC,IAAI;YACjB,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,IAAI,gCAAgC;iBACjD,CAAC,CAAC,EAAE,EAAE,GAAG;QAChB;QACA,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG;YACjC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,IAAI,gCAAgC;iBACjD,CAAC,CAAC,EAAE,EAAE,GAAG;QAChB,OAAO;YACL,CAAC,CAAC,EAAE,EAAE,GAAG;YACT,EAAE,IAAI,CAAC;gBAAC,GAAG;gBAAG,GAAG,CAAA,GAAA,mNAAA,CAAA,UAAM,AAAD,EAAE,IAAI;YAAG;QACjC;QACA,KAAK,IAAI,SAAS;IACpB;IAEA,oBAAoB;IACpB,IAAI,KAAK,EAAE,MAAM,EAAE;QACjB,KAAK,EAAE,KAAK,CAAC;QACb,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,IAAI,gCAAgC;aACjD,CAAC,CAAC,EAAE,EAAE,GAAG;IAChB;IAEA,gDAAgD;IAChD,oEAAoE;IACpE,OAAO,EAAE,MAAM,GAAG,IAAK,CAAC,CAAC,EAAE,GACrB,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,IACV,KAAK,KACL,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,CAAC;QACvB,IAAK,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,EAAE,EAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;QACrD,OAAO,EAAE,IAAI,CAAC;IAChB,CAAC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2201, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2217, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/sourceEvent.js"], "sourcesContent": ["export default function(event) {\n  let sourceEvent;\n  while (sourceEvent = event.sourceEvent) event = sourceEvent;\n  return event;\n}\n"], "names": [], "mappings": ";;;AAAe,wCAAS,KAAK;IAC3B,IAAI;IACJ,MAAO,cAAc,MAAM,WAAW,CAAE,QAAQ;IAChD,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2225, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2231, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-selection%403.0.0/node_modules/d3-selection/src/pointer.js"], "sourcesContent": ["import sourceEvent from \"./sourceEvent.js\";\n\nexport default function(event, node) {\n  event = sourceEvent(event);\n  if (node === undefined) node = event.currentTarget;\n  if (node) {\n    var svg = node.ownerSVGElement || node;\n    if (svg.createSVGPoint) {\n      var point = svg.createSVGPoint();\n      point.x = event.clientX, point.y = event.clientY;\n      point = point.matrixTransform(node.getScreenCTM().inverse());\n      return [point.x, point.y];\n    }\n    if (node.getBoundingClientRect) {\n      var rect = node.getBoundingClientRect();\n      return [event.clientX - rect.left - node.clientLeft, event.clientY - rect.top - node.clientTop];\n    }\n  }\n  return [event.pageX, event.pageY];\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,wCAAS,KAAK,EAAE,IAAI;IACjC,QAAQ,CAAA,GAAA,oNAAA,CAAA,UAAW,AAAD,EAAE;IACpB,IAAI,SAAS,WAAW,OAAO,MAAM,aAAa;IAClD,IAAI,MAAM;QACR,IAAI,MAAM,KAAK,eAAe,IAAI;QAClC,IAAI,IAAI,cAAc,EAAE;YACtB,IAAI,QAAQ,IAAI,cAAc;YAC9B,MAAM,CAAC,GAAG,MAAM,OAAO,EAAE,MAAM,CAAC,GAAG,MAAM,OAAO;YAChD,QAAQ,MAAM,eAAe,CAAC,KAAK,YAAY,GAAG,OAAO;YACzD,OAAO;gBAAC,MAAM,CAAC;gBAAE,MAAM,CAAC;aAAC;QAC3B;QACA,IAAI,KAAK,qBAAqB,EAAE;YAC9B,IAAI,OAAO,KAAK,qBAAqB;YACrC,OAAO;gBAAC,MAAM,OAAO,GAAG,KAAK,IAAI,GAAG,KAAK,UAAU;gBAAE,MAAM,OAAO,GAAG,KAAK,GAAG,GAAG,KAAK,SAAS;aAAC;QACjG;IACF;IACA,OAAO;QAAC,MAAM,KAAK;QAAE,MAAM,KAAK;KAAC;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2263, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2289, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-timer%403.0.1/node_modules/d3-timer/src/timer.js"], "sourcesContent": ["var frame = 0, // is an animation frame pending?\n    timeout = 0, // is a timeout pending?\n    interval = 0, // are any timers active?\n    pokeDelay = 1000, // how frequently we check for clock skew\n    taskHead,\n    taskTail,\n    clockLast = 0,\n    clockNow = 0,\n    clockSkew = 0,\n    clock = typeof performance === \"object\" && performance.now ? performance : Date,\n    setFrame = typeof window === \"object\" && window.requestAnimationFrame ? window.requestAnimationFrame.bind(window) : function(f) { setTimeout(f, 17); };\n\nexport function now() {\n  return clockNow || (setFrame(clearNow), clockNow = clock.now() + clockSkew);\n}\n\nfunction clearNow() {\n  clockNow = 0;\n}\n\nexport function Timer() {\n  this._call =\n  this._time =\n  this._next = null;\n}\n\nTimer.prototype = timer.prototype = {\n  constructor: Timer,\n  restart: function(callback, delay, time) {\n    if (typeof callback !== \"function\") throw new TypeError(\"callback is not a function\");\n    time = (time == null ? now() : +time) + (delay == null ? 0 : +delay);\n    if (!this._next && taskTail !== this) {\n      if (taskTail) taskTail._next = this;\n      else taskHead = this;\n      taskTail = this;\n    }\n    this._call = callback;\n    this._time = time;\n    sleep();\n  },\n  stop: function() {\n    if (this._call) {\n      this._call = null;\n      this._time = Infinity;\n      sleep();\n    }\n  }\n};\n\nexport function timer(callback, delay, time) {\n  var t = new Timer;\n  t.restart(callback, delay, time);\n  return t;\n}\n\nexport function timerFlush() {\n  now(); // Get the current time, if not already set.\n  ++frame; // Pretend we’ve set an alarm, if we haven’t already.\n  var t = taskHead, e;\n  while (t) {\n    if ((e = clockNow - t._time) >= 0) t._call.call(undefined, e);\n    t = t._next;\n  }\n  --frame;\n}\n\nfunction wake() {\n  clockNow = (clockLast = clock.now()) + clockSkew;\n  frame = timeout = 0;\n  try {\n    timerFlush();\n  } finally {\n    frame = 0;\n    nap();\n    clockNow = 0;\n  }\n}\n\nfunction poke() {\n  var now = clock.now(), delay = now - clockLast;\n  if (delay > pokeDelay) clockSkew -= delay, clockLast = now;\n}\n\nfunction nap() {\n  var t0, t1 = taskHead, t2, time = Infinity;\n  while (t1) {\n    if (t1._call) {\n      if (time > t1._time) time = t1._time;\n      t0 = t1, t1 = t1._next;\n    } else {\n      t2 = t1._next, t1._next = null;\n      t1 = t0 ? t0._next = t2 : taskHead = t2;\n    }\n  }\n  taskTail = t0;\n  sleep(time);\n}\n\nfunction sleep(time) {\n  if (frame) return; // Soonest alarm already set, or will be.\n  if (timeout) timeout = clearTimeout(timeout);\n  var delay = time - clockNow; // Strictly less than if we recomputed clockNow.\n  if (delay > 24) {\n    if (time < Infinity) timeout = setTimeout(wake, time - clock.now() - clockSkew);\n    if (interval) interval = clearInterval(interval);\n  } else {\n    if (!interval) clockLast = clock.now(), interval = setInterval(poke, pokeDelay);\n    frame = 1, setFrame(wake);\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA,IAAI,QAAQ,GACR,UAAU,GACV,WAAW,GACX,YAAY,MACZ,UACA,UACA,YAAY,GACZ,WAAW,GACX,YAAY,GACZ,QAAQ,OAAO,gBAAgB,YAAY,YAAY,GAAG,GAAG,cAAc,MAC3E,WAAW,OAAO,WAAW,YAAY,OAAO,qBAAqB,GAAG,OAAO,qBAAqB,CAAC,IAAI,CAAC,UAAU,SAAS,CAAC;IAAI,WAAW,GAAG;AAAK;AAElJ,SAAS;IACd,OAAO,YAAY,CAAC,SAAS,WAAW,WAAW,MAAM,GAAG,KAAK,SAAS;AAC5E;AAEA,SAAS;IACP,WAAW;AACb;AAEO,SAAS;IACd,IAAI,CAAC,KAAK,GACV,IAAI,CAAC,KAAK,GACV,IAAI,CAAC,KAAK,GAAG;AACf;AAEA,MAAM,SAAS,GAAG,MAAM,SAAS,GAAG;IAClC,aAAa;IACb,SAAS,SAAS,QAAQ,EAAE,KAAK,EAAE,IAAI;QACrC,IAAI,OAAO,aAAa,YAAY,MAAM,IAAI,UAAU;QACxD,OAAO,CAAC,QAAQ,OAAO,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,KAAK;QACnE,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,aAAa,IAAI,EAAE;YACpC,IAAI,UAAU,SAAS,KAAK,GAAG,IAAI;iBAC9B,WAAW,IAAI;YACpB,WAAW,IAAI;QACjB;QACA,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,KAAK,GAAG;QACb;IACF;IACA,MAAM;QACJ,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,KAAK,GAAG;YACb;QACF;IACF;AACF;AAEO,SAAS,MAAM,QAAQ,EAAE,KAAK,EAAE,IAAI;IACzC,IAAI,IAAI,IAAI;IACZ,EAAE,OAAO,CAAC,UAAU,OAAO;IAC3B,OAAO;AACT;AAEO,SAAS;IACd,OAAO,4CAA4C;IACnD,EAAE,OAAO,qDAAqD;IAC9D,IAAI,IAAI,UAAU;IAClB,MAAO,EAAG;QACR,IAAI,CAAC,IAAI,WAAW,EAAE,KAAK,KAAK,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,WAAW;QAC3D,IAAI,EAAE,KAAK;IACb;IACA,EAAE;AACJ;AAEA,SAAS;IACP,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE,IAAI;IACvC,QAAQ,UAAU;IAClB,IAAI;QACF;IACF,SAAU;QACR,QAAQ;QACR;QACA,WAAW;IACb;AACF;AAEA,SAAS;IACP,IAAI,MAAM,MAAM,GAAG,IAAI,QAAQ,MAAM;IACrC,IAAI,QAAQ,WAAW,aAAa,OAAO,YAAY;AACzD;AAEA,SAAS;IACP,IAAI,IAAI,KAAK,UAAU,IAAI,OAAO;IAClC,MAAO,GAAI;QACT,IAAI,GAAG,KAAK,EAAE;YACZ,IAAI,OAAO,GAAG,KAAK,EAAE,OAAO,GAAG,KAAK;YACpC,KAAK,IAAI,KAAK,GAAG,KAAK;QACxB,OAAO;YACL,KAAK,GAAG,KAAK,EAAE,GAAG,KAAK,GAAG;YAC1B,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,WAAW;QACvC;IACF;IACA,WAAW;IACX,MAAM;AACR;AAEA,SAAS,MAAM,IAAI;IACjB,IAAI,OAAO,QAAQ,yCAAyC;IAC5D,IAAI,SAAS,UAAU,aAAa;IACpC,IAAI,QAAQ,OAAO,UAAU,gDAAgD;IAC7E,IAAI,QAAQ,IAAI;QACd,IAAI,OAAO,UAAU,UAAU,WAAW,MAAM,OAAO,MAAM,GAAG,KAAK;QACrE,IAAI,UAAU,WAAW,cAAc;IACzC,OAAO;QACL,IAAI,CAAC,UAAU,YAAY,MAAM,GAAG,IAAI,WAAW,YAAY,MAAM;QACrE,QAAQ,GAAG,SAAS;IACtB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2385, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2391, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-timer%403.0.1/node_modules/d3-timer/src/timeout.js"], "sourcesContent": ["import {Timer} from \"./timer.js\";\n\nexport default function(callback, delay, time) {\n  var t = new Timer;\n  delay = delay == null ? 0 : +delay;\n  t.restart(elapsed => {\n    t.stop();\n    callback(elapsed + delay);\n  }, delay, time);\n  return t;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,wCAAS,QAAQ,EAAE,KAAK,EAAE,IAAI;IAC3C,IAAI,IAAI,IAAI,sMAAA,CAAA,QAAK;IACjB,QAAQ,SAAS,OAAO,IAAI,CAAC;IAC7B,EAAE,OAAO,CAAC,CAAA;QACR,EAAE,IAAI;QACN,SAAS,UAAU;IACrB,GAAG,OAAO;IACV,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2405, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2421, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/schedule.js"], "sourcesContent": ["import {dispatch} from \"d3-dispatch\";\nimport {timer, timeout} from \"d3-timer\";\n\nvar emptyOn = dispatch(\"start\", \"end\", \"cancel\", \"interrupt\");\nvar emptyTween = [];\n\nexport var CREATED = 0;\nexport var SCHEDULED = 1;\nexport var STARTING = 2;\nexport var STARTED = 3;\nexport var RUNNING = 4;\nexport var ENDING = 5;\nexport var ENDED = 6;\n\nexport default function(node, name, id, index, group, timing) {\n  var schedules = node.__transition;\n  if (!schedules) node.__transition = {};\n  else if (id in schedules) return;\n  create(node, id, {\n    name: name,\n    index: index, // For context during callback.\n    group: group, // For context during callback.\n    on: emptyOn,\n    tween: emptyTween,\n    time: timing.time,\n    delay: timing.delay,\n    duration: timing.duration,\n    ease: timing.ease,\n    timer: null,\n    state: CREATED\n  });\n}\n\nexport function init(node, id) {\n  var schedule = get(node, id);\n  if (schedule.state > CREATED) throw new Error(\"too late; already scheduled\");\n  return schedule;\n}\n\nexport function set(node, id) {\n  var schedule = get(node, id);\n  if (schedule.state > STARTED) throw new Error(\"too late; already running\");\n  return schedule;\n}\n\nexport function get(node, id) {\n  var schedule = node.__transition;\n  if (!schedule || !(schedule = schedule[id])) throw new Error(\"transition not found\");\n  return schedule;\n}\n\nfunction create(node, id, self) {\n  var schedules = node.__transition,\n      tween;\n\n  // Initialize the self timer when the transition is created.\n  // Note the actual delay is not known until the first callback!\n  schedules[id] = self;\n  self.timer = timer(schedule, 0, self.time);\n\n  function schedule(elapsed) {\n    self.state = SCHEDULED;\n    self.timer.restart(start, self.delay, self.time);\n\n    // If the elapsed delay is less than our first sleep, start immediately.\n    if (self.delay <= elapsed) start(elapsed - self.delay);\n  }\n\n  function start(elapsed) {\n    var i, j, n, o;\n\n    // If the state is not SCHEDULED, then we previously errored on start.\n    if (self.state !== SCHEDULED) return stop();\n\n    for (i in schedules) {\n      o = schedules[i];\n      if (o.name !== self.name) continue;\n\n      // While this element already has a starting transition during this frame,\n      // defer starting an interrupting transition until that transition has a\n      // chance to tick (and possibly end); see d3/d3-transition#54!\n      if (o.state === STARTED) return timeout(start);\n\n      // Interrupt the active transition, if any.\n      if (o.state === RUNNING) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"interrupt\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      }\n\n      // Cancel any pre-empted transitions.\n      else if (+i < id) {\n        o.state = ENDED;\n        o.timer.stop();\n        o.on.call(\"cancel\", node, node.__data__, o.index, o.group);\n        delete schedules[i];\n      }\n    }\n\n    // Defer the first tick to end of the current frame; see d3/d3#1576.\n    // Note the transition may be canceled after start and before the first tick!\n    // Note this must be scheduled before the start event; see d3/d3-transition#16!\n    // Assuming this is successful, subsequent callbacks go straight to tick.\n    timeout(function() {\n      if (self.state === STARTED) {\n        self.state = RUNNING;\n        self.timer.restart(tick, self.delay, self.time);\n        tick(elapsed);\n      }\n    });\n\n    // Dispatch the start event.\n    // Note this must be done before the tween are initialized.\n    self.state = STARTING;\n    self.on.call(\"start\", node, node.__data__, self.index, self.group);\n    if (self.state !== STARTING) return; // interrupted\n    self.state = STARTED;\n\n    // Initialize the tween, deleting null tween.\n    tween = new Array(n = self.tween.length);\n    for (i = 0, j = -1; i < n; ++i) {\n      if (o = self.tween[i].value.call(node, node.__data__, self.index, self.group)) {\n        tween[++j] = o;\n      }\n    }\n    tween.length = j + 1;\n  }\n\n  function tick(elapsed) {\n    var t = elapsed < self.duration ? self.ease.call(null, elapsed / self.duration) : (self.timer.restart(stop), self.state = ENDING, 1),\n        i = -1,\n        n = tween.length;\n\n    while (++i < n) {\n      tween[i].call(node, t);\n    }\n\n    // Dispatch the end event.\n    if (self.state === ENDING) {\n      self.on.call(\"end\", node, node.__data__, self.index, self.group);\n      stop();\n    }\n  }\n\n  function stop() {\n    self.state = ENDED;\n    self.timer.stop();\n    delete schedules[id];\n    for (var i in schedules) return; // eslint-disable-line no-unused-vars\n    delete node.__transition;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AAAA;;;AAEA,IAAI,UAAU,CAAA,GAAA,sPAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,OAAO,UAAU;AACjD,IAAI,aAAa,EAAE;AAEZ,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,QAAQ;AAEJ,wCAAS,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM;IAC1D,IAAI,YAAY,KAAK,YAAY;IACjC,IAAI,CAAC,WAAW,KAAK,YAAY,GAAG,CAAC;SAChC,IAAI,MAAM,WAAW;IAC1B,OAAO,MAAM,IAAI;QACf,MAAM;QACN,OAAO;QACP,OAAO;QACP,IAAI;QACJ,OAAO;QACP,MAAM,OAAO,IAAI;QACjB,OAAO,OAAO,KAAK;QACnB,UAAU,OAAO,QAAQ;QACzB,MAAM,OAAO,IAAI;QACjB,OAAO;QACP,OAAO;IACT;AACF;AAEO,SAAS,KAAK,IAAI,EAAE,EAAE;IAC3B,IAAI,WAAW,IAAI,MAAM;IACzB,IAAI,SAAS,KAAK,GAAG,SAAS,MAAM,IAAI,MAAM;IAC9C,OAAO;AACT;AAEO,SAAS,IAAI,IAAI,EAAE,EAAE;IAC1B,IAAI,WAAW,IAAI,MAAM;IACzB,IAAI,SAAS,KAAK,GAAG,SAAS,MAAM,IAAI,MAAM;IAC9C,OAAO;AACT;AAEO,SAAS,IAAI,IAAI,EAAE,EAAE;IAC1B,IAAI,WAAW,KAAK,YAAY;IAChC,IAAI,CAAC,YAAY,CAAC,CAAC,WAAW,QAAQ,CAAC,GAAG,GAAG,MAAM,IAAI,MAAM;IAC7D,OAAO;AACT;AAEA,SAAS,OAAO,IAAI,EAAE,EAAE,EAAE,IAAI;IAC5B,IAAI,YAAY,KAAK,YAAY,EAC7B;IAEJ,4DAA4D;IAC5D,+DAA+D;IAC/D,SAAS,CAAC,GAAG,GAAG;IAChB,KAAK,KAAK,GAAG,CAAA,GAAA,sMAAA,CAAA,QAAK,AAAD,EAAE,UAAU,GAAG,KAAK,IAAI;IAEzC,SAAS,SAAS,OAAO;QACvB,KAAK,KAAK,GAAG;QACb,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE,KAAK,IAAI;QAE/C,wEAAwE;QACxE,IAAI,KAAK,KAAK,IAAI,SAAS,MAAM,UAAU,KAAK,KAAK;IACvD;IAEA,SAAS,MAAM,OAAO;QACpB,IAAI,GAAG,GAAG,GAAG;QAEb,sEAAsE;QACtE,IAAI,KAAK,KAAK,KAAK,WAAW,OAAO;QAErC,IAAK,KAAK,UAAW;YACnB,IAAI,SAAS,CAAC,EAAE;YAChB,IAAI,EAAE,IAAI,KAAK,KAAK,IAAI,EAAE;YAE1B,0EAA0E;YAC1E,wEAAwE;YACxE,8DAA8D;YAC9D,IAAI,EAAE,KAAK,KAAK,SAAS,OAAO,CAAA,GAAA,8OAAA,CAAA,UAAO,AAAD,EAAE;YAExC,2CAA2C;YAC3C,IAAI,EAAE,KAAK,KAAK,SAAS;gBACvB,EAAE,KAAK,GAAG;gBACV,EAAE,KAAK,CAAC,IAAI;gBACZ,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,MAAM,KAAK,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK;gBAC5D,OAAO,SAAS,CAAC,EAAE;YACrB,OAGK,IAAI,CAAC,IAAI,IAAI;gBAChB,EAAE,KAAK,GAAG;gBACV,EAAE,KAAK,CAAC,IAAI;gBACZ,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,MAAM,KAAK,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK;gBACzD,OAAO,SAAS,CAAC,EAAE;YACrB;QACF;QAEA,oEAAoE;QACpE,6EAA6E;QAC7E,+EAA+E;QAC/E,yEAAyE;QACzE,CAAA,GAAA,8OAAA,CAAA,UAAO,AAAD,EAAE;YACN,IAAI,KAAK,KAAK,KAAK,SAAS;gBAC1B,KAAK,KAAK,GAAG;gBACb,KAAK,KAAK,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE,KAAK,IAAI;gBAC9C,KAAK;YACP;QACF;QAEA,4BAA4B;QAC5B,2DAA2D;QAC3D,KAAK,KAAK,GAAG;QACb,KAAK,EAAE,CAAC,IAAI,CAAC,SAAS,MAAM,KAAK,QAAQ,EAAE,KAAK,KAAK,EAAE,KAAK,KAAK;QACjE,IAAI,KAAK,KAAK,KAAK,UAAU,QAAQ,cAAc;QACnD,KAAK,KAAK,GAAG;QAEb,6CAA6C;QAC7C,QAAQ,IAAI,MAAM,IAAI,KAAK,KAAK,CAAC,MAAM;QACvC,IAAK,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE,EAAG;YAC9B,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,KAAK,KAAK,EAAE,KAAK,KAAK,GAAG;gBAC7E,KAAK,CAAC,EAAE,EAAE,GAAG;YACf;QACF;QACA,MAAM,MAAM,GAAG,IAAI;IACrB;IAEA,SAAS,KAAK,OAAO;QACnB,IAAI,IAAI,UAAU,KAAK,QAAQ,GAAG,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,UAAU,KAAK,QAAQ,IAAI,CAAC,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,GAAG,QAAQ,CAAC,GAC/H,IAAI,CAAC,GACL,IAAI,MAAM,MAAM;QAEpB,MAAO,EAAE,IAAI,EAAG;YACd,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM;QACtB;QAEA,0BAA0B;QAC1B,IAAI,KAAK,KAAK,KAAK,QAAQ;YACzB,KAAK,EAAE,CAAC,IAAI,CAAC,OAAO,MAAM,KAAK,QAAQ,EAAE,KAAK,KAAK,EAAE,KAAK,KAAK;YAC/D;QACF;IACF;IAEA,SAAS;QACP,KAAK,KAAK,GAAG;QACb,KAAK,KAAK,CAAC,IAAI;QACf,OAAO,SAAS,CAAC,GAAG;QACpB,IAAK,IAAI,KAAK,UAAW,QAAQ,qCAAqC;QACtE,OAAO,KAAK,YAAY;IAC1B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2562, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2568, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/interrupt.js"], "sourcesContent": ["import {STARTING, ENDING, ENDED} from \"./transition/schedule.js\";\n\nexport default function(node, name) {\n  var schedules = node.__transition,\n      schedule,\n      active,\n      empty = true,\n      i;\n\n  if (!schedules) return;\n\n  name = name == null ? null : name + \"\";\n\n  for (i in schedules) {\n    if ((schedule = schedules[i]).name !== name) { empty = false; continue; }\n    active = schedule.state > STARTING && schedule.state < ENDING;\n    schedule.state = ENDED;\n    schedule.timer.stop();\n    schedule.on.call(active ? \"interrupt\" : \"cancel\", node, node.__data__, schedule.index, schedule.group);\n    delete schedules[i];\n  }\n\n  if (empty) delete node.__transition;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,wCAAS,IAAI,EAAE,IAAI;IAChC,IAAI,YAAY,KAAK,YAAY,EAC7B,UACA,QACA,QAAQ,MACR;IAEJ,IAAI,CAAC,WAAW;IAEhB,OAAO,QAAQ,OAAO,OAAO,OAAO;IAEpC,IAAK,KAAK,UAAW;QACnB,IAAI,CAAC,WAAW,SAAS,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM;YAAE,QAAQ;YAAO;QAAU;QACxE,SAAS,SAAS,KAAK,GAAG,gQAAA,CAAA,WAAQ,IAAI,SAAS,KAAK,GAAG,gQAAA,CAAA,SAAM;QAC7D,SAAS,KAAK,GAAG,gQAAA,CAAA,QAAK;QACtB,SAAS,KAAK,CAAC,IAAI;QACnB,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS,cAAc,UAAU,MAAM,KAAK,QAAQ,EAAE,SAAS,KAAK,EAAE,SAAS,KAAK;QACrG,OAAO,SAAS,CAAC,EAAE;IACrB;IAEA,IAAI,OAAO,OAAO,KAAK,YAAY;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2590, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2596, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/selection/interrupt.js"], "sourcesContent": ["import interrupt from \"../interrupt.js\";\n\nexport default function(name) {\n  return this.each(function() {\n    interrupt(this, name);\n  });\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,wCAAS,IAAI;IAC1B,OAAO,IAAI,CAAC,IAAI,CAAC;QACf,CAAA,GAAA,mPAAA,CAAA,UAAS,AAAD,EAAE,IAAI,EAAE;IAClB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2606, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2612, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-interpolate%403.0.1/node_modules/d3-interpolate/src/transform/decompose.js"], "sourcesContent": ["var degrees = 180 / Math.PI;\n\nexport var identity = {\n  translateX: 0,\n  translateY: 0,\n  rotate: 0,\n  skewX: 0,\n  scaleX: 1,\n  scaleY: 1\n};\n\nexport default function(a, b, c, d, e, f) {\n  var scaleX, scaleY, skewX;\n  if (scaleX = Math.sqrt(a * a + b * b)) a /= scaleX, b /= scaleX;\n  if (skewX = a * c + b * d) c -= a * skewX, d -= b * skewX;\n  if (scaleY = Math.sqrt(c * c + d * d)) c /= scaleY, d /= scaleY, skewX /= scaleY;\n  if (a * d < b * c) a = -a, b = -b, skewX = -skewX, scaleX = -scaleX;\n  return {\n    translateX: e,\n    translateY: f,\n    rotate: Math.atan2(b, a) * degrees,\n    skewX: Math.atan(skewX) * degrees,\n    scaleX: scaleX,\n    scaleY: scaleY\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA,IAAI,UAAU,MAAM,KAAK,EAAE;AAEpB,IAAI,WAAW;IACpB,YAAY;IACZ,YAAY;IACZ,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,QAAQ;AACV;AAEe,wCAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACtC,IAAI,QAAQ,QAAQ;IACpB,IAAI,SAAS,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,QAAQ,KAAK;IACzD,IAAI,QAAQ,IAAI,IAAI,IAAI,GAAG,KAAK,IAAI,OAAO,KAAK,IAAI;IACpD,IAAI,SAAS,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,QAAQ,KAAK,QAAQ,SAAS;IAC1E,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,QAAQ,CAAC,OAAO,SAAS,CAAC;IAC7D,OAAO;QACL,YAAY;QACZ,YAAY;QACZ,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK;QAC3B,OAAO,KAAK,IAAI,CAAC,SAAS;QAC1B,QAAQ;QACR,QAAQ;IACV;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2640, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2646, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-interpolate%403.0.1/node_modules/d3-interpolate/src/transform/parse.js"], "sourcesContent": ["import decompose, {identity} from \"./decompose.js\";\n\nvar svgNode;\n\n/* eslint-disable no-undef */\nexport function parseCss(value) {\n  const m = new (typeof DOMMatrix === \"function\" ? DOMMatrix : WebKitCSSMatrix)(value + \"\");\n  return m.isIdentity ? identity : decompose(m.a, m.b, m.c, m.d, m.e, m.f);\n}\n\nexport function parseSvg(value) {\n  if (value == null) return identity;\n  if (!svgNode) svgNode = document.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n  svgNode.setAttribute(\"transform\", value);\n  if (!(value = svgNode.transform.baseVal.consolidate())) return identity;\n  value = value.matrix;\n  return decompose(value.a, value.b, value.c, value.d, value.e, value.f);\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,IAAI;AAGG,SAAS,SAAS,KAAK;IAC5B,MAAM,IAAI,IAAI,CAAC,OAAO,cAAc,aAAa,YAAY,eAAe,EAAE,QAAQ;IACtF,OAAO,EAAE,UAAU,GAAG,mOAAA,CAAA,WAAQ,GAAG,CAAA,GAAA,mOAAA,CAAA,UAAS,AAAD,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;AACzE;AAEO,SAAS,SAAS,KAAK;IAC5B,IAAI,SAAS,MAAM,OAAO,mOAAA,CAAA,WAAQ;IAClC,IAAI,CAAC,SAAS,UAAU,SAAS,eAAe,CAAC,8BAA8B;IAC/E,QAAQ,YAAY,CAAC,aAAa;IAClC,IAAI,CAAC,CAAC,QAAQ,QAAQ,SAAS,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,OAAO,mOAAA,CAAA,WAAQ;IACvE,QAAQ,MAAM,MAAM;IACpB,OAAO,CAAA,GAAA,mOAAA,CAAA,UAAS,AAAD,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC;AACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2665, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2671, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-interpolate%403.0.1/node_modules/d3-interpolate/src/transform/index.js"], "sourcesContent": ["import number from \"../number.js\";\nimport {parseCss, parseSvg} from \"./parse.js\";\n\nfunction interpolateTransform(parse, pxComma, pxParen, degParen) {\n\n  function pop(s) {\n    return s.length ? s.pop() + \" \" : \"\";\n  }\n\n  function translate(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(\"translate(\", null, pxComma, null, pxParen);\n      q.push({i: i - 4, x: number(xa, xb)}, {i: i - 2, x: number(ya, yb)});\n    } else if (xb || yb) {\n      s.push(\"translate(\" + xb + pxComma + yb + pxParen);\n    }\n  }\n\n  function rotate(a, b, s, q) {\n    if (a !== b) {\n      if (a - b > 180) b += 360; else if (b - a > 180) a += 360; // shortest path\n      q.push({i: s.push(pop(s) + \"rotate(\", null, degParen) - 2, x: number(a, b)});\n    } else if (b) {\n      s.push(pop(s) + \"rotate(\" + b + degParen);\n    }\n  }\n\n  function skewX(a, b, s, q) {\n    if (a !== b) {\n      q.push({i: s.push(pop(s) + \"skewX(\", null, degParen) - 2, x: number(a, b)});\n    } else if (b) {\n      s.push(pop(s) + \"skewX(\" + b + degParen);\n    }\n  }\n\n  function scale(xa, ya, xb, yb, s, q) {\n    if (xa !== xb || ya !== yb) {\n      var i = s.push(pop(s) + \"scale(\", null, \",\", null, \")\");\n      q.push({i: i - 4, x: number(xa, xb)}, {i: i - 2, x: number(ya, yb)});\n    } else if (xb !== 1 || yb !== 1) {\n      s.push(pop(s) + \"scale(\" + xb + \",\" + yb + \")\");\n    }\n  }\n\n  return function(a, b) {\n    var s = [], // string constants and placeholders\n        q = []; // number interpolators\n    a = parse(a), b = parse(b);\n    translate(a.translateX, a.translateY, b.translateX, b.translateY, s, q);\n    rotate(a.rotate, b.rotate, s, q);\n    skewX(a.skewX, b.skewX, s, q);\n    scale(a.scaleX, a.scaleY, b.scaleX, b.scaleY, s, q);\n    a = b = null; // gc\n    return function(t) {\n      var i = -1, n = q.length, o;\n      while (++i < n) s[(o = q[i]).i] = o.x(t);\n      return s.join(\"\");\n    };\n  };\n}\n\nexport var interpolateTransformCss = interpolateTransform(parseCss, \"px, \", \"px)\", \"deg)\");\nexport var interpolateTransformSvg = interpolateTransform(parseSvg, \", \", \")\", \")\");\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,SAAS,qBAAqB,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ;IAE7D,SAAS,IAAI,CAAC;QACZ,OAAO,EAAE,MAAM,GAAG,EAAE,GAAG,KAAK,MAAM;IACpC;IAEA,SAAS,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;QACrC,IAAI,OAAO,MAAM,OAAO,IAAI;YAC1B,IAAI,IAAI,EAAE,IAAI,CAAC,cAAc,MAAM,SAAS,MAAM;YAClD,EAAE,IAAI,CAAC;gBAAC,GAAG,IAAI;gBAAG,GAAG,CAAA,GAAA,mNAAA,CAAA,UAAM,AAAD,EAAE,IAAI;YAAG,GAAG;gBAAC,GAAG,IAAI;gBAAG,GAAG,CAAA,GAAA,mNAAA,CAAA,UAAM,AAAD,EAAE,IAAI;YAAG;QACpE,OAAO,IAAI,MAAM,IAAI;YACnB,EAAE,IAAI,CAAC,eAAe,KAAK,UAAU,KAAK;QAC5C;IACF;IAEA,SAAS,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACxB,IAAI,MAAM,GAAG;YACX,IAAI,IAAI,IAAI,KAAK,KAAK;iBAAU,IAAI,IAAI,IAAI,KAAK,KAAK,KAAK,gBAAgB;YAC3E,EAAE,IAAI,CAAC;gBAAC,GAAG,EAAE,IAAI,CAAC,IAAI,KAAK,WAAW,MAAM,YAAY;gBAAG,GAAG,CAAA,GAAA,mNAAA,CAAA,UAAM,AAAD,EAAE,GAAG;YAAE;QAC5E,OAAO,IAAI,GAAG;YACZ,EAAE,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI;QAClC;IACF;IAEA,SAAS,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACvB,IAAI,MAAM,GAAG;YACX,EAAE,IAAI,CAAC;gBAAC,GAAG,EAAE,IAAI,CAAC,IAAI,KAAK,UAAU,MAAM,YAAY;gBAAG,GAAG,CAAA,GAAA,mNAAA,CAAA,UAAM,AAAD,EAAE,GAAG;YAAE;QAC3E,OAAO,IAAI,GAAG;YACZ,EAAE,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI;QACjC;IACF;IAEA,SAAS,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;QACjC,IAAI,OAAO,MAAM,OAAO,IAAI;YAC1B,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,KAAK,UAAU,MAAM,KAAK,MAAM;YACnD,EAAE,IAAI,CAAC;gBAAC,GAAG,IAAI;gBAAG,GAAG,CAAA,GAAA,mNAAA,CAAA,UAAM,AAAD,EAAE,IAAI;YAAG,GAAG;gBAAC,GAAG,IAAI;gBAAG,GAAG,CAAA,GAAA,mNAAA,CAAA,UAAM,AAAD,EAAE,IAAI;YAAG;QACpE,OAAO,IAAI,OAAO,KAAK,OAAO,GAAG;YAC/B,EAAE,IAAI,CAAC,IAAI,KAAK,WAAW,KAAK,MAAM,KAAK;QAC7C;IACF;IAEA,OAAO,SAAS,CAAC,EAAE,CAAC;QAClB,IAAI,IAAI,EAAE,EACN,IAAI,EAAE,EAAE,uBAAuB;QACnC,IAAI,MAAM,IAAI,IAAI,MAAM;QACxB,UAAU,EAAE,UAAU,EAAE,EAAE,UAAU,EAAE,EAAE,UAAU,EAAE,EAAE,UAAU,EAAE,GAAG;QACrE,OAAO,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,GAAG;QAC9B,MAAM,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG;QAC3B,MAAM,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,GAAG;QACjD,IAAI,IAAI,MAAM,KAAK;QACnB,OAAO,SAAS,CAAC;YACf,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE;YAC1B,MAAO,EAAE,IAAI,EAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;YACtC,OAAO,EAAE,IAAI,CAAC;QAChB;IACF;AACF;AAEO,IAAI,0BAA0B,qBAAqB,+NAAA,CAAA,WAAQ,EAAE,QAAQ,OAAO;AAC5E,IAAI,0BAA0B,qBAAqB,+NAAA,CAAA,WAAQ,EAAE,MAAM,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2750, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2766, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/tween.js"], "sourcesContent": ["import {get, set} from \"./schedule.js\";\n\nfunction tweenRemove(id, name) {\n  var tween0, tween1;\n  return function() {\n    var schedule = set(this, id),\n        tween = schedule.tween;\n\n    // If this node shared tween with the previous node,\n    // just assign the updated shared tween and we’re done!\n    // Otherwise, copy-on-write.\n    if (tween !== tween0) {\n      tween1 = tween0 = tween;\n      for (var i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1 = tween1.slice();\n          tween1.splice(i, 1);\n          break;\n        }\n      }\n    }\n\n    schedule.tween = tween1;\n  };\n}\n\nfunction tweenFunction(id, name, value) {\n  var tween0, tween1;\n  if (typeof value !== \"function\") throw new Error;\n  return function() {\n    var schedule = set(this, id),\n        tween = schedule.tween;\n\n    // If this node shared tween with the previous node,\n    // just assign the updated shared tween and we’re done!\n    // Otherwise, copy-on-write.\n    if (tween !== tween0) {\n      tween1 = (tween0 = tween).slice();\n      for (var t = {name: name, value: value}, i = 0, n = tween1.length; i < n; ++i) {\n        if (tween1[i].name === name) {\n          tween1[i] = t;\n          break;\n        }\n      }\n      if (i === n) tween1.push(t);\n    }\n\n    schedule.tween = tween1;\n  };\n}\n\nexport default function(name, value) {\n  var id = this._id;\n\n  name += \"\";\n\n  if (arguments.length < 2) {\n    var tween = get(this.node(), id).tween;\n    for (var i = 0, n = tween.length, t; i < n; ++i) {\n      if ((t = tween[i]).name === name) {\n        return t.value;\n      }\n    }\n    return null;\n  }\n\n  return this.each((value == null ? tweenRemove : tweenFunction)(id, name, value));\n}\n\nexport function tweenValue(transition, name, value) {\n  var id = transition._id;\n\n  transition.each(function() {\n    var schedule = set(this, id);\n    (schedule.value || (schedule.value = {}))[name] = value.apply(this, arguments);\n  });\n\n  return function(node) {\n    return get(node, id).value[name];\n  };\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,SAAS,YAAY,EAAE,EAAE,IAAI;IAC3B,IAAI,QAAQ;IACZ,OAAO;QACL,IAAI,WAAW,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,IAAI,EAAE,KACrB,QAAQ,SAAS,KAAK;QAE1B,oDAAoD;QACpD,uDAAuD;QACvD,4BAA4B;QAC5B,IAAI,UAAU,QAAQ;YACpB,SAAS,SAAS;YAClB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG;gBAC7C,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,MAAM;oBAC3B,SAAS,OAAO,KAAK;oBACrB,OAAO,MAAM,CAAC,GAAG;oBACjB;gBACF;YACF;QACF;QAEA,SAAS,KAAK,GAAG;IACnB;AACF;AAEA,SAAS,cAAc,EAAE,EAAE,IAAI,EAAE,KAAK;IACpC,IAAI,QAAQ;IACZ,IAAI,OAAO,UAAU,YAAY,MAAM,IAAI;IAC3C,OAAO;QACL,IAAI,WAAW,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,IAAI,EAAE,KACrB,QAAQ,SAAS,KAAK;QAE1B,oDAAoD;QACpD,uDAAuD;QACvD,4BAA4B;QAC5B,IAAI,UAAU,QAAQ;YACpB,SAAS,CAAC,SAAS,KAAK,EAAE,KAAK;YAC/B,IAAK,IAAI,IAAI;gBAAC,MAAM;gBAAM,OAAO;YAAK,GAAG,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG;gBAC7E,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,MAAM;oBAC3B,MAAM,CAAC,EAAE,GAAG;oBACZ;gBACF;YACF;YACA,IAAI,MAAM,GAAG,OAAO,IAAI,CAAC;QAC3B;QAEA,SAAS,KAAK,GAAG;IACnB;AACF;AAEe,wCAAS,IAAI,EAAE,KAAK;IACjC,IAAI,KAAK,IAAI,CAAC,GAAG;IAEjB,QAAQ;IAER,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,IAAI,QAAQ,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK;QACtC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,GAAG,IAAI,GAAG,EAAE,EAAG;YAC/C,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM;gBAChC,OAAO,EAAE,KAAK;YAChB;QACF;QACA,OAAO;IACT;IAEA,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,OAAO,cAAc,aAAa,EAAE,IAAI,MAAM;AAC3E;AAEO,SAAS,WAAW,UAAU,EAAE,IAAI,EAAE,KAAK;IAChD,IAAI,KAAK,WAAW,GAAG;IAEvB,WAAW,IAAI,CAAC;QACd,IAAI,WAAW,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,IAAI,EAAE;QACzB,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE;IACtE;IAEA,OAAO,SAAS,IAAI;QAClB,OAAO,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,MAAM,IAAI,KAAK,CAAC,KAAK;IAClC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2840, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2876, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/interpolate.js"], "sourcesContent": ["import {color} from \"d3-color\";\nimport {interpolateNumber, interpolateRgb, interpolateString} from \"d3-interpolate\";\n\nexport default function(a, b) {\n  var c;\n  return (typeof b === \"number\" ? interpolateNumber\n      : b instanceof color ? interpolateRgb\n      : (c = color(b)) ? (b = c, interpolateRgb)\n      : interpolateString)(a, b);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AAAA;;;AAEe,wCAAS,CAAC,EAAE,CAAC;IAC1B,IAAI;IACJ,OAAO,CAAC,OAAO,MAAM,WAAW,mQAAA,CAAA,oBAAiB,GAC3C,aAAa,0OAAA,CAAA,QAAK,GAAG,6PAAA,CAAA,iBAAc,GACnC,CAAC,IAAI,CAAA,GAAA,0OAAA,CAAA,QAAK,AAAD,EAAE,EAAE,IAAI,CAAC,IAAI,GAAG,6PAAA,CAAA,iBAAc,IACvC,mQAAA,CAAA,oBAAiB,EAAE,GAAG;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2889, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2895, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/attr.js"], "sourcesContent": ["import {interpolateTransformSvg as interpolateTransform} from \"d3-interpolate\";\nimport {namespace} from \"d3-selection\";\nimport {tweenValue} from \"./tween.js\";\nimport interpolate from \"./interpolate.js\";\n\nfunction attrRemove(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\n\nfunction attrRemoveNS(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\n\nfunction attrConstant(name, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = this.getAttribute(name);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction attrConstantNS(fullname, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = this.getAttributeNS(fullname.space, fullname.local);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction attrFunction(name, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0, value1 = value(this), string1;\n    if (value1 == null) return void this.removeAttribute(name);\n    string0 = this.getAttribute(name);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nfunction attrFunctionNS(fullname, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0, value1 = value(this), string1;\n    if (value1 == null) return void this.removeAttributeNS(fullname.space, fullname.local);\n    string0 = this.getAttributeNS(fullname.space, fullname.local);\n    string1 = value1 + \"\";\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nexport default function(name, value) {\n  var fullname = namespace(name), i = fullname === \"transform\" ? interpolateTransform : interpolate;\n  return this.attrTween(name, typeof value === \"function\"\n      ? (fullname.local ? attrFunctionNS : attrFunction)(fullname, i, tweenValue(this, \"attr.\" + name, value))\n      : value == null ? (fullname.local ? attrRemoveNS : attrRemove)(fullname)\n      : (fullname.local ? attrConstantNS : attrConstant)(fullname, i, value));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,SAAS,WAAW,IAAI;IACtB,OAAO;QACL,IAAI,CAAC,eAAe,CAAC;IACvB;AACF;AAEA,SAAS,aAAa,QAAQ;IAC5B,OAAO;QACL,IAAI,CAAC,iBAAiB,CAAC,SAAS,KAAK,EAAE,SAAS,KAAK;IACvD;AACF;AAEA,SAAS,aAAa,IAAI,EAAE,WAAW,EAAE,MAAM;IAC7C,IAAI,UACA,UAAU,SAAS,IACnB;IACJ,OAAO;QACL,IAAI,UAAU,IAAI,CAAC,YAAY,CAAC;QAChC,OAAO,YAAY,UAAU,OACvB,YAAY,WAAW,eACvB,eAAe,YAAY,WAAW,SAAS;IACvD;AACF;AAEA,SAAS,eAAe,QAAQ,EAAE,WAAW,EAAE,MAAM;IACnD,IAAI,UACA,UAAU,SAAS,IACnB;IACJ,OAAO;QACL,IAAI,UAAU,IAAI,CAAC,cAAc,CAAC,SAAS,KAAK,EAAE,SAAS,KAAK;QAChE,OAAO,YAAY,UAAU,OACvB,YAAY,WAAW,eACvB,eAAe,YAAY,WAAW,SAAS;IACvD;AACF;AAEA,SAAS,aAAa,IAAI,EAAE,WAAW,EAAE,KAAK;IAC5C,IAAI,UACA,UACA;IACJ,OAAO;QACL,IAAI,SAAS,SAAS,MAAM,IAAI,GAAG;QACnC,IAAI,UAAU,MAAM,OAAO,KAAK,IAAI,CAAC,eAAe,CAAC;QACrD,UAAU,IAAI,CAAC,YAAY,CAAC;QAC5B,UAAU,SAAS;QACnB,OAAO,YAAY,UAAU,OACvB,YAAY,YAAY,YAAY,WAAW,eAC/C,CAAC,WAAW,SAAS,eAAe,YAAY,WAAW,SAAS,OAAO;IACnF;AACF;AAEA,SAAS,eAAe,QAAQ,EAAE,WAAW,EAAE,KAAK;IAClD,IAAI,UACA,UACA;IACJ,OAAO;QACL,IAAI,SAAS,SAAS,MAAM,IAAI,GAAG;QACnC,IAAI,UAAU,MAAM,OAAO,KAAK,IAAI,CAAC,iBAAiB,CAAC,SAAS,KAAK,EAAE,SAAS,KAAK;QACrF,UAAU,IAAI,CAAC,cAAc,CAAC,SAAS,KAAK,EAAE,SAAS,KAAK;QAC5D,UAAU,SAAS;QACnB,OAAO,YAAY,UAAU,OACvB,YAAY,YAAY,YAAY,WAAW,eAC/C,CAAC,WAAW,SAAS,eAAe,YAAY,WAAW,SAAS,OAAO;IACnF;AACF;AAEe,wCAAS,IAAI,EAAE,KAAK;IACjC,IAAI,WAAW,CAAA,GAAA,0PAAA,CAAA,YAAS,AAAD,EAAE,OAAO,IAAI,aAAa,cAAc,+NAAA,CAAA,0BAAoB,GAAG,mQAAA,CAAA,UAAW;IACjG,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO,UAAU,aACvC,CAAC,SAAS,KAAK,GAAG,iBAAiB,YAAY,EAAE,UAAU,GAAG,CAAA,GAAA,6PAAA,CAAA,aAAU,AAAD,EAAE,IAAI,EAAE,UAAU,MAAM,UAC/F,SAAS,OAAO,CAAC,SAAS,KAAK,GAAG,eAAe,UAAU,EAAE,YAC7D,CAAC,SAAS,KAAK,GAAG,iBAAiB,YAAY,EAAE,UAAU,GAAG;AACtE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2954, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2960, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/attrTween.js"], "sourcesContent": ["import {namespace} from \"d3-selection\";\n\nfunction attrInterpolate(name, i) {\n  return function(t) {\n    this.setAttribute(name, i.call(this, t));\n  };\n}\n\nfunction attrInterpolateNS(fullname, i) {\n  return function(t) {\n    this.setAttributeNS(fullname.space, fullname.local, i.call(this, t));\n  };\n}\n\nfunction attrTweenNS(fullname, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolateNS(fullname, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nfunction attrTween(name, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolate(name, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(name, value) {\n  var key = \"attr.\" + name;\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  var fullname = namespace(name);\n  return this.tween(key, (fullname.local ? attrTweenNS : attrTween)(fullname, value));\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,gBAAgB,IAAI,EAAE,CAAC;IAC9B,OAAO,SAAS,CAAC;QACf,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE;IACvC;AACF;AAEA,SAAS,kBAAkB,QAAQ,EAAE,CAAC;IACpC,OAAO,SAAS,CAAC;QACf,IAAI,CAAC,cAAc,CAAC,SAAS,KAAK,EAAE,SAAS,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE;IACnE;AACF;AAEA,SAAS,YAAY,QAAQ,EAAE,KAAK;IAClC,IAAI,IAAI;IACR,SAAS;QACP,IAAI,IAAI,MAAM,KAAK,CAAC,IAAI,EAAE;QAC1B,IAAI,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,kBAAkB,UAAU;QAC3D,OAAO;IACT;IACA,MAAM,MAAM,GAAG;IACf,OAAO;AACT;AAEA,SAAS,UAAU,IAAI,EAAE,KAAK;IAC5B,IAAI,IAAI;IACR,SAAS;QACP,IAAI,IAAI,MAAM,KAAK,CAAC,IAAI,EAAE;QAC1B,IAAI,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,gBAAgB,MAAM;QACrD,OAAO;IACT;IACA,MAAM,MAAM,GAAG;IACf,OAAO;AACT;AAEe,wCAAS,IAAI,EAAE,KAAK;IACjC,IAAI,MAAM,UAAU;IACpB,IAAI,UAAU,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,MAAM;IACtE,IAAI,SAAS,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;IAC1C,IAAI,OAAO,UAAU,YAAY,MAAM,IAAI;IAC3C,IAAI,WAAW,CAAA,GAAA,0PAAA,CAAA,YAAS,AAAD,EAAE;IACzB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,KAAK,GAAG,cAAc,SAAS,EAAE,UAAU;AAC9E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3003, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3009, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/delay.js"], "sourcesContent": ["import {get, init} from \"./schedule.js\";\n\nfunction delayFunction(id, value) {\n  return function() {\n    init(this, id).delay = +value.apply(this, arguments);\n  };\n}\n\nfunction delayConstant(id, value) {\n  return value = +value, function() {\n    init(this, id).delay = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each((typeof value === \"function\"\n          ? delayFunction\n          : delayConstant)(id, value))\n      : get(this.node(), id).delay;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,cAAc,EAAE,EAAE,KAAK;IAC9B,OAAO;QACL,CAAA,GAAA,gQAAA,CAAA,OAAI,AAAD,EAAE,IAAI,EAAE,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE;IAC5C;AACF;AAEA,SAAS,cAAc,EAAE,EAAE,KAAK;IAC9B,OAAO,QAAQ,CAAC,OAAO;QACrB,CAAA,GAAA,gQAAA,CAAA,OAAI,AAAD,EAAE,IAAI,EAAE,IAAI,KAAK,GAAG;IACzB;AACF;AAEe,wCAAS,KAAK;IAC3B,IAAI,KAAK,IAAI,CAAC,GAAG;IAEjB,OAAO,UAAU,MAAM,GACjB,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,UAAU,aACxB,gBACA,aAAa,EAAE,IAAI,UACvB,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3028, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3034, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/duration.js"], "sourcesContent": ["import {get, set} from \"./schedule.js\";\n\nfunction durationFunction(id, value) {\n  return function() {\n    set(this, id).duration = +value.apply(this, arguments);\n  };\n}\n\nfunction durationConstant(id, value) {\n  return value = +value, function() {\n    set(this, id).duration = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each((typeof value === \"function\"\n          ? durationFunction\n          : durationConstant)(id, value))\n      : get(this.node(), id).duration;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,iBAAiB,EAAE,EAAE,KAAK;IACjC,OAAO;QACL,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,IAAI,EAAE,IAAI,QAAQ,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE;IAC9C;AACF;AAEA,SAAS,iBAAiB,EAAE,EAAE,KAAK;IACjC,OAAO,QAAQ,CAAC,OAAO;QACrB,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,IAAI,EAAE,IAAI,QAAQ,GAAG;IAC3B;AACF;AAEe,wCAAS,KAAK;IAC3B,IAAI,KAAK,IAAI,CAAC,GAAG;IAEjB,OAAO,UAAU,MAAM,GACjB,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,UAAU,aACxB,mBACA,gBAAgB,EAAE,IAAI,UAC1B,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,QAAQ;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3053, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3059, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/ease.js"], "sourcesContent": ["import {get, set} from \"./schedule.js\";\n\nfunction easeConstant(id, value) {\n  if (typeof value !== \"function\") throw new Error;\n  return function() {\n    set(this, id).ease = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each(easeConstant(id, value))\n      : get(this.node(), id).ease;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,aAAa,EAAE,EAAE,KAAK;IAC7B,IAAI,OAAO,UAAU,YAAY,MAAM,IAAI;IAC3C,OAAO;QACL,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,IAAI,EAAE,IAAI,IAAI,GAAG;IACvB;AACF;AAEe,wCAAS,KAAK;IAC3B,IAAI,KAAK,IAAI,CAAC,GAAG;IAEjB,OAAO,UAAU,MAAM,GACjB,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,UAC3B,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3074, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3080, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/easeVarying.js"], "sourcesContent": ["import {set} from \"./schedule.js\";\n\nfunction easeVarying(id, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (typeof v !== \"function\") throw new Error;\n    set(this, id).ease = v;\n  };\n}\n\nexport default function(value) {\n  if (typeof value !== \"function\") throw new Error;\n  return this.each(easeVarying(this._id, value));\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,YAAY,EAAE,EAAE,KAAK;IAC5B,OAAO;QACL,IAAI,IAAI,MAAM,KAAK,CAAC,IAAI,EAAE;QAC1B,IAAI,OAAO,MAAM,YAAY,MAAM,IAAI;QACvC,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,IAAI,EAAE,IAAI,IAAI,GAAG;IACvB;AACF;AAEe,wCAAS,KAAK;IAC3B,IAAI,OAAO,UAAU,YAAY,MAAM,IAAI;IAC3C,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,GAAG,EAAE;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3096, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3112, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/index.js"], "sourcesContent": ["import {selection} from \"d3-selection\";\nimport transition_attr from \"./attr.js\";\nimport transition_attrTween from \"./attrTween.js\";\nimport transition_delay from \"./delay.js\";\nimport transition_duration from \"./duration.js\";\nimport transition_ease from \"./ease.js\";\nimport transition_easeVarying from \"./easeVarying.js\";\nimport transition_filter from \"./filter.js\";\nimport transition_merge from \"./merge.js\";\nimport transition_on from \"./on.js\";\nimport transition_remove from \"./remove.js\";\nimport transition_select from \"./select.js\";\nimport transition_selectAll from \"./selectAll.js\";\nimport transition_selection from \"./selection.js\";\nimport transition_style from \"./style.js\";\nimport transition_styleTween from \"./styleTween.js\";\nimport transition_text from \"./text.js\";\nimport transition_textTween from \"./textTween.js\";\nimport transition_transition from \"./transition.js\";\nimport transition_tween from \"./tween.js\";\nimport transition_end from \"./end.js\";\n\nvar id = 0;\n\nexport function Transition(groups, parents, name, id) {\n  this._groups = groups;\n  this._parents = parents;\n  this._name = name;\n  this._id = id;\n}\n\nexport default function transition(name) {\n  return selection().transition(name);\n}\n\nexport function newId() {\n  return ++id;\n}\n\nvar selection_prototype = selection.prototype;\n\nTransition.prototype = transition.prototype = {\n  constructor: Transition,\n  select: transition_select,\n  selectAll: transition_selectAll,\n  selectChild: selection_prototype.selectChild,\n  selectChildren: selection_prototype.selectChildren,\n  filter: transition_filter,\n  merge: transition_merge,\n  selection: transition_selection,\n  transition: transition_transition,\n  call: selection_prototype.call,\n  nodes: selection_prototype.nodes,\n  node: selection_prototype.node,\n  size: selection_prototype.size,\n  empty: selection_prototype.empty,\n  each: selection_prototype.each,\n  on: transition_on,\n  attr: transition_attr,\n  attrTween: transition_attrTween,\n  style: transition_style,\n  styleTween: transition_styleTween,\n  text: transition_text,\n  textTween: transition_textTween,\n  remove: transition_remove,\n  tween: transition_tween,\n  delay: transition_delay,\n  duration: transition_duration,\n  ease: transition_ease,\n  easeVarying: transition_easeVarying,\n  end: transition_end,\n  [Symbol.iterator]: selection_prototype[Symbol.iterator]\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAI,KAAK;AAEF,SAAS,WAAW,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE;IAClD,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,GAAG,GAAG;AACb;AAEe,SAAS,WAAW,IAAI;IACrC,OAAO,CAAA,GAAA,mQAAA,CAAA,YAAS,AAAD,IAAI,UAAU,CAAC;AAChC;AAEO,SAAS;IACd,OAAO,EAAE;AACX;AAEA,IAAI,sBAAsB,mQAAA,CAAA,YAAS,CAAC,SAAS;AAE7C,WAAW,SAAS,GAAG,WAAW,SAAS,GAAG;IAC5C,aAAa;IACb,QAAQ,8PAAA,CAAA,UAAiB;IACzB,WAAW,iQAAA,CAAA,UAAoB;IAC/B,aAAa,oBAAoB,WAAW;IAC5C,gBAAgB,oBAAoB,cAAc;IAClD,QAAQ,8PAAA,CAAA,UAAiB;IACzB,OAAO,6PAAA,CAAA,UAAgB;IACvB,WAAW,iQAAA,CAAA,UAAoB;IAC/B,YAAY,kQAAA,CAAA,UAAqB;IACjC,MAAM,oBAAoB,IAAI;IAC9B,OAAO,oBAAoB,KAAK;IAChC,MAAM,oBAAoB,IAAI;IAC9B,MAAM,oBAAoB,IAAI;IAC9B,OAAO,oBAAoB,KAAK;IAChC,MAAM,oBAAoB,IAAI;IAC9B,IAAI,0PAAA,CAAA,UAAa;IACjB,MAAM,4PAAA,CAAA,UAAe;IACrB,WAAW,iQAAA,CAAA,UAAoB;IAC/B,OAAO,6PAAA,CAAA,UAAgB;IACvB,YAAY,kQAAA,CAAA,UAAqB;IACjC,MAAM,4PAAA,CAAA,UAAe;IACrB,WAAW,iQAAA,CAAA,UAAoB;IAC/B,QAAQ,8PAAA,CAAA,UAAiB;IACzB,OAAO,6PAAA,CAAA,UAAgB;IACvB,OAAO,6PAAA,CAAA,UAAgB;IACvB,UAAU,gQAAA,CAAA,UAAmB;IAC7B,MAAM,4PAAA,CAAA,UAAe;IACrB,aAAa,mQAAA,CAAA,UAAsB;IACnC,KAAK,2PAAA,CAAA,UAAc;IACnB,CAAC,OAAO,QAAQ,CAAC,EAAE,mBAAmB,CAAC,OAAO,QAAQ,CAAC;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3205, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3211, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/filter.js"], "sourcesContent": ["import {matcher} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\n\nexport default function(match) {\n  if (typeof match !== \"function\") match = matcher(match);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n\n  return new Transition(subgroups, this._parents, this._name, this._id);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEe,wCAAS,KAAK;IAC3B,IAAI,OAAO,UAAU,YAAY,QAAQ,CAAA,GAAA,sPAAA,CAAA,UAAO,AAAD,EAAE;IAEjD,IAAK,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,OAAO,MAAM,EAAE,YAAY,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QAC9F,IAAK,IAAI,QAAQ,MAAM,CAAC,EAAE,EAAE,IAAI,MAAM,MAAM,EAAE,WAAW,SAAS,CAAC,EAAE,GAAG,EAAE,EAAE,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACnG,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,GAAG,QAAQ;gBAClE,SAAS,IAAI,CAAC;YAChB;QACF;IACF;IAEA,OAAO,IAAI,6PAAA,CAAA,aAAU,CAAC,WAAW,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG;AACtE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3229, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3235, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/merge.js"], "sourcesContent": ["import {Transition} from \"./index.js\";\n\nexport default function(transition) {\n  if (transition._id !== this._id) throw new Error;\n\n  for (var groups0 = this._groups, groups1 = transition._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n\n  return new Transition(merges, this._parents, this._name, this._id);\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,wCAAS,UAAU;IAChC,IAAI,WAAW,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,MAAM,IAAI;IAE3C,IAAK,IAAI,UAAU,IAAI,CAAC,OAAO,EAAE,UAAU,WAAW,OAAO,EAAE,KAAK,QAAQ,MAAM,EAAE,KAAK,QAAQ,MAAM,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QACxK,IAAK,IAAI,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS,OAAO,CAAC,EAAE,EAAE,IAAI,OAAO,MAAM,EAAE,QAAQ,MAAM,CAAC,EAAE,GAAG,IAAI,MAAM,IAAI,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YAC/H,IAAI,OAAO,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE;gBACjC,KAAK,CAAC,EAAE,GAAG;YACb;QACF;IACF;IAEA,MAAO,IAAI,IAAI,EAAE,EAAG;QAClB,MAAM,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;IACxB;IAEA,OAAO,IAAI,6PAAA,CAAA,aAAU,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG;AACnE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3254, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3260, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/on.js"], "sourcesContent": ["import {get, set, init} from \"./schedule.js\";\n\nfunction start(name) {\n  return (name + \"\").trim().split(/^|\\s+/).every(function(t) {\n    var i = t.indexOf(\".\");\n    if (i >= 0) t = t.slice(0, i);\n    return !t || t === \"start\";\n  });\n}\n\nfunction onFunction(id, name, listener) {\n  var on0, on1, sit = start(name) ? init : set;\n  return function() {\n    var schedule = sit(this, id),\n        on = schedule.on;\n\n    // If this node shared a dispatch with the previous node,\n    // just assign the updated shared dispatch and we’re done!\n    // Otherwise, copy-on-write.\n    if (on !== on0) (on1 = (on0 = on).copy()).on(name, listener);\n\n    schedule.on = on1;\n  };\n}\n\nexport default function(name, listener) {\n  var id = this._id;\n\n  return arguments.length < 2\n      ? get(this.node(), id).on.on(name)\n      : this.each(onFunction(id, name, listener));\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,MAAM,IAAI;IACjB,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,GAAG,KAAK,CAAC,SAAS,KAAK,CAAC,SAAS,CAAC;QACvD,IAAI,IAAI,EAAE,OAAO,CAAC;QAClB,IAAI,KAAK,GAAG,IAAI,EAAE,KAAK,CAAC,GAAG;QAC3B,OAAO,CAAC,KAAK,MAAM;IACrB;AACF;AAEA,SAAS,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ;IACpC,IAAI,KAAK,KAAK,MAAM,MAAM,QAAQ,gQAAA,CAAA,OAAI,GAAG,gQAAA,CAAA,MAAG;IAC5C,OAAO;QACL,IAAI,WAAW,IAAI,IAAI,EAAE,KACrB,KAAK,SAAS,EAAE;QAEpB,yDAAyD;QACzD,0DAA0D;QAC1D,4BAA4B;QAC5B,IAAI,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,MAAM;QAEnD,SAAS,EAAE,GAAG;IAChB;AACF;AAEe,wCAAS,IAAI,EAAE,QAAQ;IACpC,IAAI,KAAK,IAAI,CAAC,GAAG;IAEjB,OAAO,UAAU,MAAM,GAAG,IACpB,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,EAAE,CAAC,QAC3B,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,MAAM;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3287, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3293, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/remove.js"], "sourcesContent": ["function removeFunction(id) {\n  return function() {\n    var parent = this.parentNode;\n    for (var i in this.__transition) if (+i !== id) return;\n    if (parent) parent.removeChild(this);\n  };\n}\n\nexport default function() {\n  return this.on(\"end.remove\", removeFunction(this._id));\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,eAAe,EAAE;IACxB,OAAO;QACL,IAAI,SAAS,IAAI,CAAC,UAAU;QAC5B,IAAK,IAAI,KAAK,IAAI,CAAC,YAAY,CAAE,IAAI,CAAC,MAAM,IAAI;QAChD,IAAI,QAAQ,OAAO,WAAW,CAAC,IAAI;IACrC;AACF;AAEe;IACb,OAAO,IAAI,CAAC,EAAE,CAAC,cAAc,eAAe,IAAI,CAAC,GAAG;AACtD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3306, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3322, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/select.js"], "sourcesContent": ["import {selector} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function(select) {\n  var name = this._name,\n      id = this._id;\n\n  if (typeof select !== \"function\") select = selector(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node) subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n        schedule(subgroup[i], name, id, i, subgroup, get(node, id));\n      }\n    }\n  }\n\n  return new Transition(subgroups, this._parents, name, id);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEe,wCAAS,MAAM;IAC5B,IAAI,OAAO,IAAI,CAAC,KAAK,EACjB,KAAK,IAAI,CAAC,GAAG;IAEjB,IAAI,OAAO,WAAW,YAAY,SAAS,CAAA,GAAA,wPAAA,CAAA,WAAQ,AAAD,EAAE;IAEpD,IAAK,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,OAAO,MAAM,EAAE,YAAY,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QAC9F,IAAK,IAAI,QAAQ,MAAM,CAAC,EAAE,EAAE,IAAI,MAAM,MAAM,EAAE,WAAW,SAAS,CAAC,EAAE,GAAG,IAAI,MAAM,IAAI,MAAM,SAAS,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACtH,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE,KAAK,CAAC,UAAU,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,GAAG,MAAM,GAAG;gBAC/E,IAAI,cAAc,MAAM,QAAQ,QAAQ,GAAG,KAAK,QAAQ;gBACxD,QAAQ,CAAC,EAAE,GAAG;gBACd,CAAA,GAAA,gQAAA,CAAA,UAAQ,AAAD,EAAE,QAAQ,CAAC,EAAE,EAAE,MAAM,IAAI,GAAG,UAAU,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,MAAM;YACzD;QACF;IACF;IAEA,OAAO,IAAI,6PAAA,CAAA,aAAU,CAAC,WAAW,IAAI,CAAC,QAAQ,EAAE,MAAM;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3345, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3361, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/selectAll.js"], "sourcesContent": ["import {selectorAll} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function(select) {\n  var name = this._name,\n      id = this._id;\n\n  if (typeof select !== \"function\") select = selectorAll(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        for (var children = select.call(node, node.__data__, i, group), child, inherit = get(node, id), k = 0, l = children.length; k < l; ++k) {\n          if (child = children[k]) {\n            schedule(child, name, id, k, children, inherit);\n          }\n        }\n        subgroups.push(children);\n        parents.push(node);\n      }\n    }\n  }\n\n  return new Transition(subgroups, parents, name, id);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEe,wCAAS,MAAM;IAC5B,IAAI,OAAO,IAAI,CAAC,KAAK,EACjB,KAAK,IAAI,CAAC,GAAG;IAEjB,IAAI,OAAO,WAAW,YAAY,SAAS,CAAA,GAAA,8PAAA,CAAA,cAAW,AAAD,EAAE;IAEvD,IAAK,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,OAAO,MAAM,EAAE,YAAY,EAAE,EAAE,UAAU,EAAE,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QAClG,IAAK,IAAI,QAAQ,MAAM,CAAC,EAAE,EAAE,IAAI,MAAM,MAAM,EAAE,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACrE,IAAI,OAAO,KAAK,CAAC,EAAE,EAAE;gBACnB,IAAK,IAAI,WAAW,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,GAAG,QAAQ,OAAO,UAAU,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,MAAM,KAAK,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG;oBACtI,IAAI,QAAQ,QAAQ,CAAC,EAAE,EAAE;wBACvB,CAAA,GAAA,gQAAA,CAAA,UAAQ,AAAD,EAAE,OAAO,MAAM,IAAI,GAAG,UAAU;oBACzC;gBACF;gBACA,UAAU,IAAI,CAAC;gBACf,QAAQ,IAAI,CAAC;YACf;QACF;IACF;IAEA,OAAO,IAAI,6PAAA,CAAA,aAAU,CAAC,WAAW,SAAS,MAAM;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3388, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3394, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/selection.js"], "sourcesContent": ["import {selection} from \"d3-selection\";\n\nvar Selection = selection.prototype.constructor;\n\nexport default function() {\n  return new Selection(this._groups, this._parents);\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,IAAI,YAAY,mQAAA,CAAA,YAAS,CAAC,SAAS,CAAC,WAAW;AAEhC;IACb,OAAO,IAAI,UAAU,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3403, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3419, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/style.js"], "sourcesContent": ["import {interpolateTransformCss as interpolateTransform} from \"d3-interpolate\";\nimport {style} from \"d3-selection\";\nimport {set} from \"./schedule.js\";\nimport {tweenValue} from \"./tween.js\";\nimport interpolate from \"./interpolate.js\";\n\nfunction styleNull(name, interpolate) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0 = style(this, name),\n        string1 = (this.style.removeProperty(name), style(this, name));\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, string10 = string1);\n  };\n}\n\nfunction styleRemove(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\n\nfunction styleConstant(name, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = style(this, name);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction styleFunction(name, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0 = style(this, name),\n        value1 = value(this),\n        string1 = value1 + \"\";\n    if (value1 == null) string1 = value1 = (this.style.removeProperty(name), style(this, name));\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nfunction styleMaybeRemove(id, name) {\n  var on0, on1, listener0, key = \"style.\" + name, event = \"end.\" + key, remove;\n  return function() {\n    var schedule = set(this, id),\n        on = schedule.on,\n        listener = schedule.value[key] == null ? remove || (remove = styleRemove(name)) : undefined;\n\n    // If this node shared a dispatch with the previous node,\n    // just assign the updated shared dispatch and we’re done!\n    // Otherwise, copy-on-write.\n    if (on !== on0 || listener0 !== listener) (on1 = (on0 = on).copy()).on(event, listener0 = listener);\n\n    schedule.on = on1;\n  };\n}\n\nexport default function(name, value, priority) {\n  var i = (name += \"\") === \"transform\" ? interpolateTransform : interpolate;\n  return value == null ? this\n      .styleTween(name, styleNull(name, i))\n      .on(\"end.style.\" + name, styleRemove(name))\n    : typeof value === \"function\" ? this\n      .styleTween(name, styleFunction(name, i, tweenValue(this, \"style.\" + name, value)))\n      .each(styleMaybeRemove(this._id, name))\n    : this\n      .styleTween(name, styleConstant(name, i, value), priority)\n      .on(\"end.style.\" + name, null);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,SAAS,UAAU,IAAI,EAAE,WAAW;IAClC,IAAI,UACA,UACA;IACJ,OAAO;QACL,IAAI,UAAU,CAAA,GAAA,kQAAA,CAAA,QAAK,AAAD,EAAE,IAAI,EAAE,OACtB,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAA,GAAA,kQAAA,CAAA,QAAK,AAAD,EAAE,IAAI,EAAE,KAAK;QACjE,OAAO,YAAY,UAAU,OACvB,YAAY,YAAY,YAAY,WAAW,eAC/C,eAAe,YAAY,WAAW,SAAS,WAAW;IAClE;AACF;AAEA,SAAS,YAAY,IAAI;IACvB,OAAO;QACL,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;IAC5B;AACF;AAEA,SAAS,cAAc,IAAI,EAAE,WAAW,EAAE,MAAM;IAC9C,IAAI,UACA,UAAU,SAAS,IACnB;IACJ,OAAO;QACL,IAAI,UAAU,CAAA,GAAA,kQAAA,CAAA,QAAK,AAAD,EAAE,IAAI,EAAE;QAC1B,OAAO,YAAY,UAAU,OACvB,YAAY,WAAW,eACvB,eAAe,YAAY,WAAW,SAAS;IACvD;AACF;AAEA,SAAS,cAAc,IAAI,EAAE,WAAW,EAAE,KAAK;IAC7C,IAAI,UACA,UACA;IACJ,OAAO;QACL,IAAI,UAAU,CAAA,GAAA,kQAAA,CAAA,QAAK,AAAD,EAAE,IAAI,EAAE,OACtB,SAAS,MAAM,IAAI,GACnB,UAAU,SAAS;QACvB,IAAI,UAAU,MAAM,UAAU,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAA,GAAA,kQAAA,CAAA,QAAK,AAAD,EAAE,IAAI,EAAE,KAAK;QAC1F,OAAO,YAAY,UAAU,OACvB,YAAY,YAAY,YAAY,WAAW,eAC/C,CAAC,WAAW,SAAS,eAAe,YAAY,WAAW,SAAS,OAAO;IACnF;AACF;AAEA,SAAS,iBAAiB,EAAE,EAAE,IAAI;IAChC,IAAI,KAAK,KAAK,WAAW,MAAM,WAAW,MAAM,QAAQ,SAAS,KAAK;IACtE,OAAO;QACL,IAAI,WAAW,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,IAAI,EAAE,KACrB,KAAK,SAAS,EAAE,EAChB,WAAW,SAAS,KAAK,CAAC,IAAI,IAAI,OAAO,UAAU,CAAC,SAAS,YAAY,KAAK,IAAI;QAEtF,yDAAyD;QACzD,0DAA0D;QAC1D,4BAA4B;QAC5B,IAAI,OAAO,OAAO,cAAc,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,OAAO,YAAY;QAE1F,SAAS,EAAE,GAAG;IAChB;AACF;AAEe,wCAAS,IAAI,EAAE,KAAK,EAAE,QAAQ;IAC3C,IAAI,IAAI,CAAC,QAAQ,EAAE,MAAM,cAAc,+NAAA,CAAA,0BAAoB,GAAG,mQAAA,CAAA,UAAW;IACzE,OAAO,SAAS,OAAO,IAAI,CACtB,UAAU,CAAC,MAAM,UAAU,MAAM,IACjC,EAAE,CAAC,eAAe,MAAM,YAAY,SACrC,OAAO,UAAU,aAAa,IAAI,CACjC,UAAU,CAAC,MAAM,cAAc,MAAM,GAAG,CAAA,GAAA,6PAAA,CAAA,aAAU,AAAD,EAAE,IAAI,EAAE,WAAW,MAAM,SAC1E,IAAI,CAAC,iBAAiB,IAAI,CAAC,GAAG,EAAE,SACjC,IAAI,CACH,UAAU,CAAC,MAAM,cAAc,MAAM,GAAG,QAAQ,UAChD,EAAE,CAAC,eAAe,MAAM;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3474, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3480, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/styleTween.js"], "sourcesContent": ["function styleInterpolate(name, i, priority) {\n  return function(t) {\n    this.style.setProperty(name, i.call(this, t), priority);\n  };\n}\n\nfunction styleTween(name, value, priority) {\n  var t, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t = (i0 = i) && styleInterpolate(name, i, priority);\n    return t;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(name, value, priority) {\n  var key = \"style.\" + (name += \"\");\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  return this.tween(key, styleTween(name, value, priority == null ? \"\" : priority));\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,iBAAiB,IAAI,EAAE,CAAC,EAAE,QAAQ;IACzC,OAAO,SAAS,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI;IAChD;AACF;AAEA,SAAS,WAAW,IAAI,EAAE,KAAK,EAAE,QAAQ;IACvC,IAAI,GAAG;IACP,SAAS;QACP,IAAI,IAAI,MAAM,KAAK,CAAC,IAAI,EAAE;QAC1B,IAAI,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,iBAAiB,MAAM,GAAG;QACxD,OAAO;IACT;IACA,MAAM,MAAM,GAAG;IACf,OAAO;AACT;AAEe,wCAAS,IAAI,EAAE,KAAK,EAAE,QAAQ;IAC3C,IAAI,MAAM,WAAW,CAAC,QAAQ,EAAE;IAChC,IAAI,UAAU,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,MAAM;IACtE,IAAI,SAAS,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;IAC1C,IAAI,OAAO,UAAU,YAAY,MAAM,IAAI;IAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,WAAW,MAAM,OAAO,YAAY,OAAO,KAAK;AACzE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3505, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3511, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/text.js"], "sourcesContent": ["import {tweenValue} from \"./tween.js\";\n\nfunction textConstant(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\n\nfunction textFunction(value) {\n  return function() {\n    var value1 = value(this);\n    this.textContent = value1 == null ? \"\" : value1;\n  };\n}\n\nexport default function(value) {\n  return this.tween(\"text\", typeof value === \"function\"\n      ? textFunction(tweenValue(this, \"text\", value))\n      : textConstant(value == null ? \"\" : value + \"\"));\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,aAAa,KAAK;IACzB,OAAO;QACL,IAAI,CAAC,WAAW,GAAG;IACrB;AACF;AAEA,SAAS,aAAa,KAAK;IACzB,OAAO;QACL,IAAI,SAAS,MAAM,IAAI;QACvB,IAAI,CAAC,WAAW,GAAG,UAAU,OAAO,KAAK;IAC3C;AACF;AAEe,wCAAS,KAAK;IAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,OAAO,UAAU,aACrC,aAAa,CAAA,GAAA,6PAAA,CAAA,aAAU,AAAD,EAAE,IAAI,EAAE,QAAQ,UACtC,aAAa,SAAS,OAAO,KAAK,QAAQ;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3530, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3536, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/textTween.js"], "sourcesContent": ["function textInterpolate(i) {\n  return function(t) {\n    this.textContent = i.call(this, t);\n  };\n}\n\nfunction textTween(value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && textInterpolate(i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(value) {\n  var key = \"text\";\n  if (arguments.length < 1) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  return this.tween(key, textTween(value));\n}\n"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB,CAAC;IACxB,OAAO,SAAS,CAAC;QACf,IAAI,CAAC,WAAW,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE;IAClC;AACF;AAEA,SAAS,UAAU,KAAK;IACtB,IAAI,IAAI;IACR,SAAS;QACP,IAAI,IAAI,MAAM,KAAK,CAAC,IAAI,EAAE;QAC1B,IAAI,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,gBAAgB;QAC/C,OAAO;IACT;IACA,MAAM,MAAM,GAAG;IACf,OAAO;AACT;AAEe,wCAAS,KAAK;IAC3B,IAAI,MAAM;IACV,IAAI,UAAU,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,MAAM;IACtE,IAAI,SAAS,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;IAC1C,IAAI,OAAO,UAAU,YAAY,MAAM,IAAI;IAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,UAAU;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3561, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3567, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/transition.js"], "sourcesContent": ["import {Transition, newId} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function() {\n  var name = this._name,\n      id0 = this._id,\n      id1 = newId();\n\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        var inherit = get(node, id0);\n        schedule(node, name, id1, i, group, {\n          time: inherit.time + inherit.delay + inherit.duration,\n          delay: 0,\n          duration: inherit.duration,\n          ease: inherit.ease\n        });\n      }\n    }\n  }\n\n  return new Transition(groups, this._parents, name, id1);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEe;IACb,IAAI,OAAO,IAAI,CAAC,KAAK,EACjB,MAAM,IAAI,CAAC,GAAG,EACd,MAAM,CAAA,GAAA,6PAAA,CAAA,QAAK,AAAD;IAEd,IAAK,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,OAAO,MAAM,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QACpE,IAAK,IAAI,QAAQ,MAAM,CAAC,EAAE,EAAE,IAAI,MAAM,MAAM,EAAE,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACrE,IAAI,OAAO,KAAK,CAAC,EAAE,EAAE;gBACnB,IAAI,UAAU,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,MAAM;gBACxB,CAAA,GAAA,gQAAA,CAAA,UAAQ,AAAD,EAAE,MAAM,MAAM,KAAK,GAAG,OAAO;oBAClC,MAAM,QAAQ,IAAI,GAAG,QAAQ,KAAK,GAAG,QAAQ,QAAQ;oBACrD,OAAO;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,MAAM,QAAQ,IAAI;gBACpB;YACF;QACF;IACF;IAEA,OAAO,IAAI,6PAAA,CAAA,aAAU,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE,MAAM;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3591, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3597, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/transition/end.js"], "sourcesContent": ["import {set} from \"./schedule.js\";\n\nexport default function() {\n  var on0, on1, that = this, id = that._id, size = that.size();\n  return new Promise(function(resolve, reject) {\n    var cancel = {value: reject},\n        end = {value: function() { if (--size === 0) resolve(); }};\n\n    that.each(function() {\n      var schedule = set(this, id),\n          on = schedule.on;\n\n      // If this node shared a dispatch with the previous node,\n      // just assign the updated shared dispatch and we’re done!\n      // Otherwise, copy-on-write.\n      if (on !== on0) {\n        on1 = (on0 = on).copy();\n        on1._.cancel.push(cancel);\n        on1._.interrupt.push(cancel);\n        on1._.end.push(end);\n      }\n\n      schedule.on = on1;\n    });\n\n    // The selection was empty, resolve end immediately\n    if (size === 0) resolve();\n  });\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe;IACb,IAAI,KAAK,KAAK,OAAO,IAAI,EAAE,KAAK,KAAK,GAAG,EAAE,OAAO,KAAK,IAAI;IAC1D,OAAO,IAAI,QAAQ,SAAS,OAAO,EAAE,MAAM;QACzC,IAAI,SAAS;YAAC,OAAO;QAAM,GACvB,MAAM;YAAC,OAAO;gBAAa,IAAI,EAAE,SAAS,GAAG;YAAW;QAAC;QAE7D,KAAK,IAAI,CAAC;YACR,IAAI,WAAW,CAAA,GAAA,gQAAA,CAAA,MAAG,AAAD,EAAE,IAAI,EAAE,KACrB,KAAK,SAAS,EAAE;YAEpB,yDAAyD;YACzD,0DAA0D;YAC1D,4BAA4B;YAC5B,IAAI,OAAO,KAAK;gBACd,MAAM,CAAC,MAAM,EAAE,EAAE,IAAI;gBACrB,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;gBAClB,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC;gBACrB,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;YACjB;YAEA,SAAS,EAAE,GAAG;QAChB;QAEA,mDAAmD;QACnD,IAAI,SAAS,GAAG;IAClB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3629, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3635, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-ease%403.0.1/node_modules/d3-ease/src/cubic.js"], "sourcesContent": ["export function cubicIn(t) {\n  return t * t * t;\n}\n\nexport function cubicOut(t) {\n  return --t * t * t + 1;\n}\n\nexport function cubicInOut(t) {\n  return ((t *= 2) <= 1 ? t * t * t : (t -= 2) * t * t + 2) / 2;\n}\n"], "names": [], "mappings": ";;;;;AAAO,SAAS,QAAQ,CAAC;IACvB,OAAO,IAAI,IAAI;AACjB;AAEO,SAAS,SAAS,CAAC;IACxB,OAAO,EAAE,IAAI,IAAI,IAAI;AACvB;AAEO,SAAS,WAAW,CAAC;IAC1B,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3649, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3665, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/selection/transition.js"], "sourcesContent": ["import {Transition, newId} from \"../transition/index.js\";\nimport schedule from \"../transition/schedule.js\";\nimport {easeCubicInOut} from \"d3-ease\";\nimport {now} from \"d3-timer\";\n\nvar defaultTiming = {\n  time: null, // Set on use.\n  delay: 0,\n  duration: 250,\n  ease: easeCubicInOut\n};\n\nfunction inherit(node, id) {\n  var timing;\n  while (!(timing = node.__transition) || !(timing = timing[id])) {\n    if (!(node = node.parentNode)) {\n      throw new Error(`transition ${id} not found`);\n    }\n  }\n  return timing;\n}\n\nexport default function(name) {\n  var id,\n      timing;\n\n  if (name instanceof Transition) {\n    id = name._id, name = name._name;\n  } else {\n    id = newId(), (timing = defaultTiming).time = now(), name = name == null ? null : name + \"\";\n  }\n\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        schedule(node, name, id, i, group, timing || inherit(node, id));\n      }\n    }\n  }\n\n  return new Transition(groups, this._parents, name, id);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,IAAI,gBAAgB;IAClB,MAAM;IACN,OAAO;IACP,UAAU;IACV,MAAM,oPAAA,CAAA,iBAAc;AACtB;AAEA,SAAS,QAAQ,IAAI,EAAE,EAAE;IACvB,IAAI;IACJ,MAAO,CAAC,CAAC,SAAS,KAAK,YAAY,KAAK,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,EAAG;QAC9D,IAAI,CAAC,CAAC,OAAO,KAAK,UAAU,GAAG;YAC7B,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,UAAU,CAAC;QAC9C;IACF;IACA,OAAO;AACT;AAEe,wCAAS,IAAI;IAC1B,IAAI,IACA;IAEJ,IAAI,gBAAgB,6PAAA,CAAA,aAAU,EAAE;QAC9B,KAAK,KAAK,GAAG,EAAE,OAAO,KAAK,KAAK;IAClC,OAAO;QACL,KAAK,CAAA,GAAA,6PAAA,CAAA,QAAK,AAAD,KAAK,CAAC,SAAS,aAAa,EAAE,IAAI,GAAG,CAAA,GAAA,sMAAA,CAAA,MAAG,AAAD,KAAK,OAAO,QAAQ,OAAO,OAAO,OAAO;IAC3F;IAEA,IAAK,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,OAAO,MAAM,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QACpE,IAAK,IAAI,QAAQ,MAAM,CAAC,EAAE,EAAE,IAAI,MAAM,MAAM,EAAE,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACrE,IAAI,OAAO,KAAK,CAAC,EAAE,EAAE;gBACnB,CAAA,GAAA,gQAAA,CAAA,UAAQ,AAAD,EAAE,MAAM,MAAM,IAAI,GAAG,OAAO,UAAU,QAAQ,MAAM;YAC7D;QACF;IACF;IAEA,OAAO,IAAI,6PAAA,CAAA,aAAU,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE,MAAM;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3707, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3713, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-transition%403.0.1_d3-selection%403.0.0/node_modules/d3-transition/src/selection/index.js"], "sourcesContent": ["import {selection} from \"d3-selection\";\nimport selection_interrupt from \"./interrupt.js\";\nimport selection_transition from \"./transition.js\";\n\nselection.prototype.interrupt = selection_interrupt;\nselection.prototype.transition = selection_transition;\n"], "names": [], "mappings": ";AAAA;AACA;AACA;;;;AAEA,mQAAA,CAAA,YAAS,CAAC,SAAS,CAAC,SAAS,GAAG,gQAAA,CAAA,UAAmB;AACnD,mQAAA,CAAA,YAAS,CAAC,SAAS,CAAC,UAAU,GAAG,iQAAA,CAAA,UAAoB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3722, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3728, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 3733, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3758, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-brush%403.0.0/node_modules/d3-brush/src/constant.js"], "sourcesContent": ["export default x => () => x;\n"], "names": [], "mappings": ";;;uCAAe,CAAA,IAAK,IAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3762, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3768, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-brush%403.0.0/node_modules/d3-brush/src/event.js"], "sourcesContent": ["export default function BrushEvent(type, {\n  sourceEvent,\n  target,\n  selection,\n  mode,\n  dispatch\n}) {\n  Object.defineProperties(this, {\n    type: {value: type, enumerable: true, configurable: true},\n    sourceEvent: {value: sourceEvent, enumerable: true, configurable: true},\n    target: {value: target, enumerable: true, configurable: true},\n    selection: {value: selection, enumerable: true, configurable: true},\n    mode: {value: mode, enumerable: true, configurable: true},\n    _: {value: dispatch}\n  });\n}\n"], "names": [], "mappings": ";;;AAAe,SAAS,WAAW,IAAI,EAAE,EACvC,WAAW,EACX,MAAM,EACN,SAAS,EACT,IAAI,EACJ,QAAQ,EACT;IACC,OAAO,gBAAgB,CAAC,IAAI,EAAE;QAC5B,MAAM;YAAC,OAAO;YAAM,YAAY;YAAM,cAAc;QAAI;QACxD,aAAa;YAAC,OAAO;YAAa,YAAY;YAAM,cAAc;QAAI;QACtE,QAAQ;YAAC,OAAO;YAAQ,YAAY;YAAM,cAAc;QAAI;QAC5D,WAAW;YAAC,OAAO;YAAW,YAAY;YAAM,cAAc;QAAI;QAClE,MAAM;YAAC,OAAO;YAAM,YAAY;YAAM,cAAc;QAAI;QACxD,GAAG;YAAC,OAAO;QAAQ;IACrB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3803, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3809, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-brush%403.0.0/node_modules/d3-brush/src/noevent.js"], "sourcesContent": ["export function nopropagation(event) {\n  event.stopImmediatePropagation();\n}\n\nexport default function(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS,cAAc,KAAK;IACjC,MAAM,wBAAwB;AAChC;AAEe,wCAAS,KAAK;IAC3B,MAAM,cAAc;IACpB,MAAM,wBAAwB;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3820, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3826, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-brush%403.0.0/node_modules/d3-brush/src/brush.js"], "sourcesContent": ["import {dispatch} from \"d3-dispatch\";\nimport {dragDisable, dragEnable} from \"d3-drag\";\nimport {interpolate} from \"d3-interpolate\";\nimport {pointer, select} from \"d3-selection\";\nimport {interrupt} from \"d3-transition\";\nimport constant from \"./constant.js\";\nimport BrushEvent from \"./event.js\";\nimport noevent, {nopropagation} from \"./noevent.js\";\n\nvar MODE_DRAG = {name: \"drag\"},\n    MODE_SPACE = {name: \"space\"},\n    MODE_HANDLE = {name: \"handle\"},\n    MODE_CENTER = {name: \"center\"};\n\nconst {abs, max, min} = Math;\n\nfunction number1(e) {\n  return [+e[0], +e[1]];\n}\n\nfunction number2(e) {\n  return [number1(e[0]), number1(e[1])];\n}\n\nvar X = {\n  name: \"x\",\n  handles: [\"w\", \"e\"].map(type),\n  input: function(x, e) { return x == null ? null : [[+x[0], e[0][1]], [+x[1], e[1][1]]]; },\n  output: function(xy) { return xy && [xy[0][0], xy[1][0]]; }\n};\n\nvar Y = {\n  name: \"y\",\n  handles: [\"n\", \"s\"].map(type),\n  input: function(y, e) { return y == null ? null : [[e[0][0], +y[0]], [e[1][0], +y[1]]]; },\n  output: function(xy) { return xy && [xy[0][1], xy[1][1]]; }\n};\n\nvar XY = {\n  name: \"xy\",\n  handles: [\"n\", \"w\", \"e\", \"s\", \"nw\", \"ne\", \"sw\", \"se\"].map(type),\n  input: function(xy) { return xy == null ? null : number2(xy); },\n  output: function(xy) { return xy; }\n};\n\nvar cursors = {\n  overlay: \"crosshair\",\n  selection: \"move\",\n  n: \"ns-resize\",\n  e: \"ew-resize\",\n  s: \"ns-resize\",\n  w: \"ew-resize\",\n  nw: \"nwse-resize\",\n  ne: \"nesw-resize\",\n  se: \"nwse-resize\",\n  sw: \"nesw-resize\"\n};\n\nvar flipX = {\n  e: \"w\",\n  w: \"e\",\n  nw: \"ne\",\n  ne: \"nw\",\n  se: \"sw\",\n  sw: \"se\"\n};\n\nvar flipY = {\n  n: \"s\",\n  s: \"n\",\n  nw: \"sw\",\n  ne: \"se\",\n  se: \"ne\",\n  sw: \"nw\"\n};\n\nvar signsX = {\n  overlay: +1,\n  selection: +1,\n  n: null,\n  e: +1,\n  s: null,\n  w: -1,\n  nw: -1,\n  ne: +1,\n  se: +1,\n  sw: -1\n};\n\nvar signsY = {\n  overlay: +1,\n  selection: +1,\n  n: -1,\n  e: null,\n  s: +1,\n  w: null,\n  nw: -1,\n  ne: -1,\n  se: +1,\n  sw: +1\n};\n\nfunction type(t) {\n  return {type: t};\n}\n\n// Ignore right-click, since that should open the context menu.\nfunction defaultFilter(event) {\n  return !event.ctrlKey && !event.button;\n}\n\nfunction defaultExtent() {\n  var svg = this.ownerSVGElement || this;\n  if (svg.hasAttribute(\"viewBox\")) {\n    svg = svg.viewBox.baseVal;\n    return [[svg.x, svg.y], [svg.x + svg.width, svg.y + svg.height]];\n  }\n  return [[0, 0], [svg.width.baseVal.value, svg.height.baseVal.value]];\n}\n\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || (\"ontouchstart\" in this);\n}\n\n// Like d3.local, but with the name “__brush” rather than auto-generated.\nfunction local(node) {\n  while (!node.__brush) if (!(node = node.parentNode)) return;\n  return node.__brush;\n}\n\nfunction empty(extent) {\n  return extent[0][0] === extent[1][0]\n      || extent[0][1] === extent[1][1];\n}\n\nexport function brushSelection(node) {\n  var state = node.__brush;\n  return state ? state.dim.output(state.selection) : null;\n}\n\nexport function brushX() {\n  return brush(X);\n}\n\nexport function brushY() {\n  return brush(Y);\n}\n\nexport default function() {\n  return brush(XY);\n}\n\nfunction brush(dim) {\n  var extent = defaultExtent,\n      filter = defaultFilter,\n      touchable = defaultTouchable,\n      keys = true,\n      listeners = dispatch(\"start\", \"brush\", \"end\"),\n      handleSize = 6,\n      touchending;\n\n  function brush(group) {\n    var overlay = group\n        .property(\"__brush\", initialize)\n      .selectAll(\".overlay\")\n      .data([type(\"overlay\")]);\n\n    overlay.enter().append(\"rect\")\n        .attr(\"class\", \"overlay\")\n        .attr(\"pointer-events\", \"all\")\n        .attr(\"cursor\", cursors.overlay)\n      .merge(overlay)\n        .each(function() {\n          var extent = local(this).extent;\n          select(this)\n              .attr(\"x\", extent[0][0])\n              .attr(\"y\", extent[0][1])\n              .attr(\"width\", extent[1][0] - extent[0][0])\n              .attr(\"height\", extent[1][1] - extent[0][1]);\n        });\n\n    group.selectAll(\".selection\")\n      .data([type(\"selection\")])\n      .enter().append(\"rect\")\n        .attr(\"class\", \"selection\")\n        .attr(\"cursor\", cursors.selection)\n        .attr(\"fill\", \"#777\")\n        .attr(\"fill-opacity\", 0.3)\n        .attr(\"stroke\", \"#fff\")\n        .attr(\"shape-rendering\", \"crispEdges\");\n\n    var handle = group.selectAll(\".handle\")\n      .data(dim.handles, function(d) { return d.type; });\n\n    handle.exit().remove();\n\n    handle.enter().append(\"rect\")\n        .attr(\"class\", function(d) { return \"handle handle--\" + d.type; })\n        .attr(\"cursor\", function(d) { return cursors[d.type]; });\n\n    group\n        .each(redraw)\n        .attr(\"fill\", \"none\")\n        .attr(\"pointer-events\", \"all\")\n        .on(\"mousedown.brush\", started)\n      .filter(touchable)\n        .on(\"touchstart.brush\", started)\n        .on(\"touchmove.brush\", touchmoved)\n        .on(\"touchend.brush touchcancel.brush\", touchended)\n        .style(\"touch-action\", \"none\")\n        .style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n\n  brush.move = function(group, selection, event) {\n    if (group.tween) {\n      group\n          .on(\"start.brush\", function(event) { emitter(this, arguments).beforestart().start(event); })\n          .on(\"interrupt.brush end.brush\", function(event) { emitter(this, arguments).end(event); })\n          .tween(\"brush\", function() {\n            var that = this,\n                state = that.__brush,\n                emit = emitter(that, arguments),\n                selection0 = state.selection,\n                selection1 = dim.input(typeof selection === \"function\" ? selection.apply(this, arguments) : selection, state.extent),\n                i = interpolate(selection0, selection1);\n\n            function tween(t) {\n              state.selection = t === 1 && selection1 === null ? null : i(t);\n              redraw.call(that);\n              emit.brush();\n            }\n\n            return selection0 !== null && selection1 !== null ? tween : tween(1);\n          });\n    } else {\n      group\n          .each(function() {\n            var that = this,\n                args = arguments,\n                state = that.__brush,\n                selection1 = dim.input(typeof selection === \"function\" ? selection.apply(that, args) : selection, state.extent),\n                emit = emitter(that, args).beforestart();\n\n            interrupt(that);\n            state.selection = selection1 === null ? null : selection1;\n            redraw.call(that);\n            emit.start(event).brush(event).end(event);\n          });\n    }\n  };\n\n  brush.clear = function(group, event) {\n    brush.move(group, null, event);\n  };\n\n  function redraw() {\n    var group = select(this),\n        selection = local(this).selection;\n\n    if (selection) {\n      group.selectAll(\".selection\")\n          .style(\"display\", null)\n          .attr(\"x\", selection[0][0])\n          .attr(\"y\", selection[0][1])\n          .attr(\"width\", selection[1][0] - selection[0][0])\n          .attr(\"height\", selection[1][1] - selection[0][1]);\n\n      group.selectAll(\".handle\")\n          .style(\"display\", null)\n          .attr(\"x\", function(d) { return d.type[d.type.length - 1] === \"e\" ? selection[1][0] - handleSize / 2 : selection[0][0] - handleSize / 2; })\n          .attr(\"y\", function(d) { return d.type[0] === \"s\" ? selection[1][1] - handleSize / 2 : selection[0][1] - handleSize / 2; })\n          .attr(\"width\", function(d) { return d.type === \"n\" || d.type === \"s\" ? selection[1][0] - selection[0][0] + handleSize : handleSize; })\n          .attr(\"height\", function(d) { return d.type === \"e\" || d.type === \"w\" ? selection[1][1] - selection[0][1] + handleSize : handleSize; });\n    }\n\n    else {\n      group.selectAll(\".selection,.handle\")\n          .style(\"display\", \"none\")\n          .attr(\"x\", null)\n          .attr(\"y\", null)\n          .attr(\"width\", null)\n          .attr(\"height\", null);\n    }\n  }\n\n  function emitter(that, args, clean) {\n    var emit = that.__brush.emitter;\n    return emit && (!clean || !emit.clean) ? emit : new Emitter(that, args, clean);\n  }\n\n  function Emitter(that, args, clean) {\n    this.that = that;\n    this.args = args;\n    this.state = that.__brush;\n    this.active = 0;\n    this.clean = clean;\n  }\n\n  Emitter.prototype = {\n    beforestart: function() {\n      if (++this.active === 1) this.state.emitter = this, this.starting = true;\n      return this;\n    },\n    start: function(event, mode) {\n      if (this.starting) this.starting = false, this.emit(\"start\", event, mode);\n      else this.emit(\"brush\", event);\n      return this;\n    },\n    brush: function(event, mode) {\n      this.emit(\"brush\", event, mode);\n      return this;\n    },\n    end: function(event, mode) {\n      if (--this.active === 0) delete this.state.emitter, this.emit(\"end\", event, mode);\n      return this;\n    },\n    emit: function(type, event, mode) {\n      var d = select(this.that).datum();\n      listeners.call(\n        type,\n        this.that,\n        new BrushEvent(type, {\n          sourceEvent: event,\n          target: brush,\n          selection: dim.output(this.state.selection),\n          mode,\n          dispatch: listeners\n        }),\n        d\n      );\n    }\n  };\n\n  function started(event) {\n    if (touchending && !event.touches) return;\n    if (!filter.apply(this, arguments)) return;\n\n    var that = this,\n        type = event.target.__data__.type,\n        mode = (keys && event.metaKey ? type = \"overlay\" : type) === \"selection\" ? MODE_DRAG : (keys && event.altKey ? MODE_CENTER : MODE_HANDLE),\n        signX = dim === Y ? null : signsX[type],\n        signY = dim === X ? null : signsY[type],\n        state = local(that),\n        extent = state.extent,\n        selection = state.selection,\n        W = extent[0][0], w0, w1,\n        N = extent[0][1], n0, n1,\n        E = extent[1][0], e0, e1,\n        S = extent[1][1], s0, s1,\n        dx = 0,\n        dy = 0,\n        moving,\n        shifting = signX && signY && keys && event.shiftKey,\n        lockX,\n        lockY,\n        points = Array.from(event.touches || [event], t => {\n          const i = t.identifier;\n          t = pointer(t, that);\n          t.point0 = t.slice();\n          t.identifier = i;\n          return t;\n        });\n\n    interrupt(that);\n    var emit = emitter(that, arguments, true).beforestart();\n\n    if (type === \"overlay\") {\n      if (selection) moving = true;\n      const pts = [points[0], points[1] || points[0]];\n      state.selection = selection = [[\n          w0 = dim === Y ? W : min(pts[0][0], pts[1][0]),\n          n0 = dim === X ? N : min(pts[0][1], pts[1][1])\n        ], [\n          e0 = dim === Y ? E : max(pts[0][0], pts[1][0]),\n          s0 = dim === X ? S : max(pts[0][1], pts[1][1])\n        ]];\n      if (points.length > 1) move(event);\n    } else {\n      w0 = selection[0][0];\n      n0 = selection[0][1];\n      e0 = selection[1][0];\n      s0 = selection[1][1];\n    }\n\n    w1 = w0;\n    n1 = n0;\n    e1 = e0;\n    s1 = s0;\n\n    var group = select(that)\n        .attr(\"pointer-events\", \"none\");\n\n    var overlay = group.selectAll(\".overlay\")\n        .attr(\"cursor\", cursors[type]);\n\n    if (event.touches) {\n      emit.moved = moved;\n      emit.ended = ended;\n    } else {\n      var view = select(event.view)\n          .on(\"mousemove.brush\", moved, true)\n          .on(\"mouseup.brush\", ended, true);\n      if (keys) view\n          .on(\"keydown.brush\", keydowned, true)\n          .on(\"keyup.brush\", keyupped, true)\n\n      dragDisable(event.view);\n    }\n\n    redraw.call(that);\n    emit.start(event, mode.name);\n\n    function moved(event) {\n      for (const p of event.changedTouches || [event]) {\n        for (const d of points)\n          if (d.identifier === p.identifier) d.cur = pointer(p, that);\n      }\n      if (shifting && !lockX && !lockY && points.length === 1) {\n        const point = points[0];\n        if (abs(point.cur[0] - point[0]) > abs(point.cur[1] - point[1]))\n          lockY = true;\n        else\n          lockX = true;\n      }\n      for (const point of points)\n        if (point.cur) point[0] = point.cur[0], point[1] = point.cur[1];\n      moving = true;\n      noevent(event);\n      move(event);\n    }\n\n    function move(event) {\n      const point = points[0], point0 = point.point0;\n      var t;\n\n      dx = point[0] - point0[0];\n      dy = point[1] - point0[1];\n\n      switch (mode) {\n        case MODE_SPACE:\n        case MODE_DRAG: {\n          if (signX) dx = max(W - w0, min(E - e0, dx)), w1 = w0 + dx, e1 = e0 + dx;\n          if (signY) dy = max(N - n0, min(S - s0, dy)), n1 = n0 + dy, s1 = s0 + dy;\n          break;\n        }\n        case MODE_HANDLE: {\n          if (points[1]) {\n            if (signX) w1 = max(W, min(E, points[0][0])), e1 = max(W, min(E, points[1][0])), signX = 1;\n            if (signY) n1 = max(N, min(S, points[0][1])), s1 = max(N, min(S, points[1][1])), signY = 1;\n          } else {\n            if (signX < 0) dx = max(W - w0, min(E - w0, dx)), w1 = w0 + dx, e1 = e0;\n            else if (signX > 0) dx = max(W - e0, min(E - e0, dx)), w1 = w0, e1 = e0 + dx;\n            if (signY < 0) dy = max(N - n0, min(S - n0, dy)), n1 = n0 + dy, s1 = s0;\n            else if (signY > 0) dy = max(N - s0, min(S - s0, dy)), n1 = n0, s1 = s0 + dy;\n          }\n          break;\n        }\n        case MODE_CENTER: {\n          if (signX) w1 = max(W, min(E, w0 - dx * signX)), e1 = max(W, min(E, e0 + dx * signX));\n          if (signY) n1 = max(N, min(S, n0 - dy * signY)), s1 = max(N, min(S, s0 + dy * signY));\n          break;\n        }\n      }\n\n      if (e1 < w1) {\n        signX *= -1;\n        t = w0, w0 = e0, e0 = t;\n        t = w1, w1 = e1, e1 = t;\n        if (type in flipX) overlay.attr(\"cursor\", cursors[type = flipX[type]]);\n      }\n\n      if (s1 < n1) {\n        signY *= -1;\n        t = n0, n0 = s0, s0 = t;\n        t = n1, n1 = s1, s1 = t;\n        if (type in flipY) overlay.attr(\"cursor\", cursors[type = flipY[type]]);\n      }\n\n      if (state.selection) selection = state.selection; // May be set by brush.move!\n      if (lockX) w1 = selection[0][0], e1 = selection[1][0];\n      if (lockY) n1 = selection[0][1], s1 = selection[1][1];\n\n      if (selection[0][0] !== w1\n          || selection[0][1] !== n1\n          || selection[1][0] !== e1\n          || selection[1][1] !== s1) {\n        state.selection = [[w1, n1], [e1, s1]];\n        redraw.call(that);\n        emit.brush(event, mode.name);\n      }\n    }\n\n    function ended(event) {\n      nopropagation(event);\n      if (event.touches) {\n        if (event.touches.length) return;\n        if (touchending) clearTimeout(touchending);\n        touchending = setTimeout(function() { touchending = null; }, 500); // Ghost clicks are delayed!\n      } else {\n        dragEnable(event.view, moving);\n        view.on(\"keydown.brush keyup.brush mousemove.brush mouseup.brush\", null);\n      }\n      group.attr(\"pointer-events\", \"all\");\n      overlay.attr(\"cursor\", cursors.overlay);\n      if (state.selection) selection = state.selection; // May be set by brush.move (on start)!\n      if (empty(selection)) state.selection = null, redraw.call(that);\n      emit.end(event, mode.name);\n    }\n\n    function keydowned(event) {\n      switch (event.keyCode) {\n        case 16: { // SHIFT\n          shifting = signX && signY;\n          break;\n        }\n        case 18: { // ALT\n          if (mode === MODE_HANDLE) {\n            if (signX) e0 = e1 - dx * signX, w0 = w1 + dx * signX;\n            if (signY) s0 = s1 - dy * signY, n0 = n1 + dy * signY;\n            mode = MODE_CENTER;\n            move(event);\n          }\n          break;\n        }\n        case 32: { // SPACE; takes priority over ALT\n          if (mode === MODE_HANDLE || mode === MODE_CENTER) {\n            if (signX < 0) e0 = e1 - dx; else if (signX > 0) w0 = w1 - dx;\n            if (signY < 0) s0 = s1 - dy; else if (signY > 0) n0 = n1 - dy;\n            mode = MODE_SPACE;\n            overlay.attr(\"cursor\", cursors.selection);\n            move(event);\n          }\n          break;\n        }\n        default: return;\n      }\n      noevent(event);\n    }\n\n    function keyupped(event) {\n      switch (event.keyCode) {\n        case 16: { // SHIFT\n          if (shifting) {\n            lockX = lockY = shifting = false;\n            move(event);\n          }\n          break;\n        }\n        case 18: { // ALT\n          if (mode === MODE_CENTER) {\n            if (signX < 0) e0 = e1; else if (signX > 0) w0 = w1;\n            if (signY < 0) s0 = s1; else if (signY > 0) n0 = n1;\n            mode = MODE_HANDLE;\n            move(event);\n          }\n          break;\n        }\n        case 32: { // SPACE\n          if (mode === MODE_SPACE) {\n            if (event.altKey) {\n              if (signX) e0 = e1 - dx * signX, w0 = w1 + dx * signX;\n              if (signY) s0 = s1 - dy * signY, n0 = n1 + dy * signY;\n              mode = MODE_CENTER;\n            } else {\n              if (signX < 0) e0 = e1; else if (signX > 0) w0 = w1;\n              if (signY < 0) s0 = s1; else if (signY > 0) n0 = n1;\n              mode = MODE_HANDLE;\n            }\n            overlay.attr(\"cursor\", cursors[type]);\n            move(event);\n          }\n          break;\n        }\n        default: return;\n      }\n      noevent(event);\n    }\n  }\n\n  function touchmoved(event) {\n    emitter(this, arguments).moved(event);\n  }\n\n  function touchended(event) {\n    emitter(this, arguments).ended(event);\n  }\n\n  function initialize() {\n    var state = this.__brush || {selection: null};\n    state.extent = number2(extent.apply(this, arguments));\n    state.dim = dim;\n    return state;\n  }\n\n  brush.extent = function(_) {\n    return arguments.length ? (extent = typeof _ === \"function\" ? _ : constant(number2(_)), brush) : extent;\n  };\n\n  brush.filter = function(_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), brush) : filter;\n  };\n\n  brush.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), brush) : touchable;\n  };\n\n  brush.handleSize = function(_) {\n    return arguments.length ? (handleSize = +_, brush) : handleSize;\n  };\n\n  brush.keyModifiers = function(_) {\n    return arguments.length ? (keys = !!_, brush) : keys;\n  };\n\n  brush.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? brush : value;\n  };\n\n  return brush;\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;;;;AAEA,IAAI,YAAY;IAAC,MAAM;AAAM,GACzB,aAAa;IAAC,MAAM;AAAO,GAC3B,cAAc;IAAC,MAAM;AAAQ,GAC7B,cAAc;IAAC,MAAM;AAAQ;AAEjC,MAAM,EAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAC,GAAG;AAExB,SAAS,QAAQ,CAAC;IAChB,OAAO;QAAC,CAAC,CAAC,CAAC,EAAE;QAAE,CAAC,CAAC,CAAC,EAAE;KAAC;AACvB;AAEA,SAAS,QAAQ,CAAC;IAChB,OAAO;QAAC,QAAQ,CAAC,CAAC,EAAE;QAAG,QAAQ,CAAC,CAAC,EAAE;KAAE;AACvC;AAEA,IAAI,IAAI;IACN,MAAM;IACN,SAAS;QAAC;QAAK;KAAI,CAAC,GAAG,CAAC;IACxB,OAAO,SAAS,CAAC,EAAE,CAAC;QAAI,OAAO,KAAK,OAAO,OAAO;YAAC;gBAAC,CAAC,CAAC,CAAC,EAAE;gBAAE,CAAC,CAAC,EAAE,CAAC,EAAE;aAAC;YAAE;gBAAC,CAAC,CAAC,CAAC,EAAE;gBAAE,CAAC,CAAC,EAAE,CAAC,EAAE;aAAC;SAAC;IAAE;IACxF,QAAQ,SAAS,EAAE;QAAI,OAAO,MAAM;YAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YAAE,EAAE,CAAC,EAAE,CAAC,EAAE;SAAC;IAAE;AAC5D;AAEA,IAAI,IAAI;IACN,MAAM;IACN,SAAS;QAAC;QAAK;KAAI,CAAC,GAAG,CAAC;IACxB,OAAO,SAAS,CAAC,EAAE,CAAC;QAAI,OAAO,KAAK,OAAO,OAAO;YAAC;gBAAC,CAAC,CAAC,EAAE,CAAC,EAAE;gBAAE,CAAC,CAAC,CAAC,EAAE;aAAC;YAAE;gBAAC,CAAC,CAAC,EAAE,CAAC,EAAE;gBAAE,CAAC,CAAC,CAAC,EAAE;aAAC;SAAC;IAAE;IACxF,QAAQ,SAAS,EAAE;QAAI,OAAO,MAAM;YAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YAAE,EAAE,CAAC,EAAE,CAAC,EAAE;SAAC;IAAE;AAC5D;AAEA,IAAI,KAAK;IACP,MAAM;IACN,SAAS;QAAC;QAAK;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;KAAK,CAAC,GAAG,CAAC;IAC1D,OAAO,SAAS,EAAE;QAAI,OAAO,MAAM,OAAO,OAAO,QAAQ;IAAK;IAC9D,QAAQ,SAAS,EAAE;QAAI,OAAO;IAAI;AACpC;AAEA,IAAI,UAAU;IACZ,SAAS;IACT,WAAW;IACX,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEA,IAAI,QAAQ;IACV,GAAG;IACH,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEA,IAAI,QAAQ;IACV,GAAG;IACH,GAAG;IACH,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEA,IAAI,SAAS;IACX,SAAS,CAAC;IACV,WAAW,CAAC;IACZ,GAAG;IACH,GAAG,CAAC;IACJ,GAAG;IACH,GAAG,CAAC;IACJ,IAAI,CAAC;IACL,IAAI,CAAC;IACL,IAAI,CAAC;IACL,IAAI,CAAC;AACP;AAEA,IAAI,SAAS;IACX,SAAS,CAAC;IACV,WAAW,CAAC;IACZ,GAAG,CAAC;IACJ,GAAG;IACH,GAAG,CAAC;IACJ,GAAG;IACH,IAAI,CAAC;IACL,IAAI,CAAC;IACL,IAAI,CAAC;IACL,IAAI,CAAC;AACP;AAEA,SAAS,KAAK,CAAC;IACb,OAAO;QAAC,MAAM;IAAC;AACjB;AAEA,+DAA+D;AAC/D,SAAS,cAAc,KAAK;IAC1B,OAAO,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,MAAM;AACxC;AAEA,SAAS;IACP,IAAI,MAAM,IAAI,CAAC,eAAe,IAAI,IAAI;IACtC,IAAI,IAAI,YAAY,CAAC,YAAY;QAC/B,MAAM,IAAI,OAAO,CAAC,OAAO;QACzB,OAAO;YAAC;gBAAC,IAAI,CAAC;gBAAE,IAAI,CAAC;aAAC;YAAE;gBAAC,IAAI,CAAC,GAAG,IAAI,KAAK;gBAAE,IAAI,CAAC,GAAG,IAAI,MAAM;aAAC;SAAC;IAClE;IACA,OAAO;QAAC;YAAC;YAAG;SAAE;QAAE;YAAC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK;YAAE,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK;SAAC;KAAC;AACtE;AAEA,SAAS;IACP,OAAO,UAAU,cAAc,IAAK,kBAAkB,IAAI;AAC5D;AAEA,yEAAyE;AACzE,SAAS,MAAM,IAAI;IACjB,MAAO,CAAC,KAAK,OAAO,CAAE,IAAI,CAAC,CAAC,OAAO,KAAK,UAAU,GAAG;IACrD,OAAO,KAAK,OAAO;AACrB;AAEA,SAAS,MAAM,MAAM;IACnB,OAAO,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE,IAC7B,MAAM,CAAC,EAAE,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE;AACtC;AAEO,SAAS,eAAe,IAAI;IACjC,IAAI,QAAQ,KAAK,OAAO;IACxB,OAAO,QAAQ,MAAM,GAAG,CAAC,MAAM,CAAC,MAAM,SAAS,IAAI;AACrD;AAEO,SAAS;IACd,OAAO,MAAM;AACf;AAEO,SAAS;IACd,OAAO,MAAM;AACf;AAEe;IACb,OAAO,MAAM;AACf;AAEA,SAAS,MAAM,GAAG;IAChB,IAAI,SAAS,eACT,SAAS,eACT,YAAY,kBACZ,OAAO,MACP,YAAY,CAAA,GAAA,sPAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,SAAS,QACvC,aAAa,GACb;IAEJ,SAAS,MAAM,KAAK;QAClB,IAAI,UAAU,MACT,QAAQ,CAAC,WAAW,YACtB,SAAS,CAAC,YACV,IAAI,CAAC;YAAC,KAAK;SAAW;QAEzB,QAAQ,KAAK,GAAG,MAAM,CAAC,QAClB,IAAI,CAAC,SAAS,WACd,IAAI,CAAC,kBAAkB,OACvB,IAAI,CAAC,UAAU,QAAQ,OAAO,EAChC,KAAK,CAAC,SACJ,IAAI,CAAC;YACJ,IAAI,SAAS,MAAM,IAAI,EAAE,MAAM;YAC/B,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE,IAAI,EACN,IAAI,CAAC,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE,EACtB,IAAI,CAAC,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE,EACtB,IAAI,CAAC,SAAS,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,EACzC,IAAI,CAAC,UAAU,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE;QACjD;QAEJ,MAAM,SAAS,CAAC,cACb,IAAI,CAAC;YAAC,KAAK;SAAa,EACxB,KAAK,GAAG,MAAM,CAAC,QACb,IAAI,CAAC,SAAS,aACd,IAAI,CAAC,UAAU,QAAQ,SAAS,EAChC,IAAI,CAAC,QAAQ,QACb,IAAI,CAAC,gBAAgB,KACrB,IAAI,CAAC,UAAU,QACf,IAAI,CAAC,mBAAmB;QAE7B,IAAI,SAAS,MAAM,SAAS,CAAC,WAC1B,IAAI,CAAC,IAAI,OAAO,EAAE,SAAS,CAAC;YAAI,OAAO,EAAE,IAAI;QAAE;QAElD,OAAO,IAAI,GAAG,MAAM;QAEpB,OAAO,KAAK,GAAG,MAAM,CAAC,QACjB,IAAI,CAAC,SAAS,SAAS,CAAC;YAAI,OAAO,oBAAoB,EAAE,IAAI;QAAE,GAC/D,IAAI,CAAC,UAAU,SAAS,CAAC;YAAI,OAAO,OAAO,CAAC,EAAE,IAAI,CAAC;QAAE;QAE1D,MACK,IAAI,CAAC,QACL,IAAI,CAAC,QAAQ,QACb,IAAI,CAAC,kBAAkB,OACvB,EAAE,CAAC,mBAAmB,SACxB,MAAM,CAAC,WACL,EAAE,CAAC,oBAAoB,SACvB,EAAE,CAAC,mBAAmB,YACtB,EAAE,CAAC,oCAAoC,YACvC,KAAK,CAAC,gBAAgB,QACtB,KAAK,CAAC,+BAA+B;IAC5C;IAEA,MAAM,IAAI,GAAG,SAAS,KAAK,EAAE,SAAS,EAAE,KAAK;QAC3C,IAAI,MAAM,KAAK,EAAE;YACf,MACK,EAAE,CAAC,eAAe,SAAS,KAAK;gBAAI,QAAQ,IAAI,EAAE,WAAW,WAAW,GAAG,KAAK,CAAC;YAAQ,GACzF,EAAE,CAAC,6BAA6B,SAAS,KAAK;gBAAI,QAAQ,IAAI,EAAE,WAAW,GAAG,CAAC;YAAQ,GACvF,KAAK,CAAC,SAAS;gBACd,IAAI,OAAO,IAAI,EACX,QAAQ,KAAK,OAAO,EACpB,OAAO,QAAQ,MAAM,YACrB,aAAa,MAAM,SAAS,EAC5B,aAAa,IAAI,KAAK,CAAC,OAAO,cAAc,aAAa,UAAU,KAAK,CAAC,IAAI,EAAE,aAAa,WAAW,MAAM,MAAM,GACnH,IAAI,CAAA,GAAA,4PAAA,CAAA,cAAW,AAAD,EAAE,YAAY;gBAEhC,SAAS,MAAM,CAAC;oBACd,MAAM,SAAS,GAAG,MAAM,KAAK,eAAe,OAAO,OAAO,EAAE;oBAC5D,OAAO,IAAI,CAAC;oBACZ,KAAK,KAAK;gBACZ;gBAEA,OAAO,eAAe,QAAQ,eAAe,OAAO,QAAQ,MAAM;YACpE;QACN,OAAO;YACL,MACK,IAAI,CAAC;gBACJ,IAAI,OAAO,IAAI,EACX,OAAO,WACP,QAAQ,KAAK,OAAO,EACpB,aAAa,IAAI,KAAK,CAAC,OAAO,cAAc,aAAa,UAAU,KAAK,CAAC,MAAM,QAAQ,WAAW,MAAM,MAAM,GAC9G,OAAO,QAAQ,MAAM,MAAM,WAAW;gBAE1C,CAAA,GAAA,2RAAA,CAAA,YAAS,AAAD,EAAE;gBACV,MAAM,SAAS,GAAG,eAAe,OAAO,OAAO;gBAC/C,OAAO,IAAI,CAAC;gBACZ,KAAK,KAAK,CAAC,OAAO,KAAK,CAAC,OAAO,GAAG,CAAC;YACrC;QACN;IACF;IAEA,MAAM,KAAK,GAAG,SAAS,KAAK,EAAE,KAAK;QACjC,MAAM,IAAI,CAAC,OAAO,MAAM;IAC1B;IAEA,SAAS;QACP,IAAI,QAAQ,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE,IAAI,GACnB,YAAY,MAAM,IAAI,EAAE,SAAS;QAErC,IAAI,WAAW;YACb,MAAM,SAAS,CAAC,cACX,KAAK,CAAC,WAAW,MACjB,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE,CAAC,EAAE,EACzB,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE,CAAC,EAAE,EACzB,IAAI,CAAC,SAAS,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,EAC/C,IAAI,CAAC,UAAU,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;YAErD,MAAM,SAAS,CAAC,WACX,KAAK,CAAC,WAAW,MACjB,IAAI,CAAC,KAAK,SAAS,CAAC;gBAAI,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,EAAE,KAAK,MAAM,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa,IAAI,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa;YAAG,GACxI,IAAI,CAAC,KAAK,SAAS,CAAC;gBAAI,OAAO,EAAE,IAAI,CAAC,EAAE,KAAK,MAAM,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa,IAAI,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa;YAAG,GACxH,IAAI,CAAC,SAAS,SAAS,CAAC;gBAAI,OAAO,EAAE,IAAI,KAAK,OAAO,EAAE,IAAI,KAAK,MAAM,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa;YAAY,GACnI,IAAI,CAAC,UAAU,SAAS,CAAC;gBAAI,OAAO,EAAE,IAAI,KAAK,OAAO,EAAE,IAAI,KAAK,MAAM,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG,aAAa;YAAY;QAC3I,OAEK;YACH,MAAM,SAAS,CAAC,sBACX,KAAK,CAAC,WAAW,QACjB,IAAI,CAAC,KAAK,MACV,IAAI,CAAC,KAAK,MACV,IAAI,CAAC,SAAS,MACd,IAAI,CAAC,UAAU;QACtB;IACF;IAEA,SAAS,QAAQ,IAAI,EAAE,IAAI,EAAE,KAAK;QAChC,IAAI,OAAO,KAAK,OAAO,CAAC,OAAO;QAC/B,OAAO,QAAQ,CAAC,CAAC,SAAS,CAAC,KAAK,KAAK,IAAI,OAAO,IAAI,QAAQ,MAAM,MAAM;IAC1E;IAEA,SAAS,QAAQ,IAAI,EAAE,IAAI,EAAE,KAAK;QAChC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG,KAAK,OAAO;QACzB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG;IACf;IAEA,QAAQ,SAAS,GAAG;QAClB,aAAa;YACX,IAAI,EAAE,IAAI,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,EAAE,IAAI,CAAC,QAAQ,GAAG;YACpE,OAAO,IAAI;QACb;QACA,OAAO,SAAS,KAAK,EAAE,IAAI;YACzB,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,OAAO;iBAC/D,IAAI,CAAC,IAAI,CAAC,SAAS;YACxB,OAAO,IAAI;QACb;QACA,OAAO,SAAS,KAAK,EAAE,IAAI;YACzB,IAAI,CAAC,IAAI,CAAC,SAAS,OAAO;YAC1B,OAAO,IAAI;QACb;QACA,KAAK,SAAS,KAAK,EAAE,IAAI;YACvB,IAAI,EAAE,IAAI,CAAC,MAAM,KAAK,GAAG,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,OAAO;YAC5E,OAAO,IAAI;QACb;QACA,MAAM,SAAS,IAAI,EAAE,KAAK,EAAE,IAAI;YAC9B,IAAI,IAAI,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK;YAC/B,UAAU,IAAI,CACZ,MACA,IAAI,CAAC,IAAI,EACT,IAAI,sMAAA,CAAA,UAAU,CAAC,MAAM;gBACnB,aAAa;gBACb,QAAQ;gBACR,WAAW,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS;gBAC1C;gBACA,UAAU;YACZ,IACA;QAEJ;IACF;IAEA,SAAS,QAAQ,KAAK;QACpB,IAAI,eAAe,CAAC,MAAM,OAAO,EAAE;QACnC,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,EAAE,YAAY;QAEpC,IAAI,OAAO,IAAI,EACX,OAAO,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,EACjC,OAAO,CAAC,QAAQ,MAAM,OAAO,GAAG,OAAO,YAAY,IAAI,MAAM,cAAc,YAAa,QAAQ,MAAM,MAAM,GAAG,cAAc,aAC7H,QAAQ,QAAQ,IAAI,OAAO,MAAM,CAAC,KAAK,EACvC,QAAQ,QAAQ,IAAI,OAAO,MAAM,CAAC,KAAK,EACvC,QAAQ,MAAM,OACd,SAAS,MAAM,MAAM,EACrB,YAAY,MAAM,SAAS,EAC3B,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,IACtB,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,IACtB,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,IACtB,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,IACtB,KAAK,GACL,KAAK,GACL,QACA,WAAW,SAAS,SAAS,QAAQ,MAAM,QAAQ,EACnD,OACA,OACA,SAAS,MAAM,IAAI,CAAC,MAAM,OAAO,IAAI;YAAC;SAAM,EAAE,CAAA;YAC5C,MAAM,IAAI,EAAE,UAAU;YACtB,IAAI,CAAA,GAAA,sPAAA,CAAA,UAAO,AAAD,EAAE,GAAG;YACf,EAAE,MAAM,GAAG,EAAE,KAAK;YAClB,EAAE,UAAU,GAAG;YACf,OAAO;QACT;QAEJ,CAAA,GAAA,2RAAA,CAAA,YAAS,AAAD,EAAE;QACV,IAAI,OAAO,QAAQ,MAAM,WAAW,MAAM,WAAW;QAErD,IAAI,SAAS,WAAW;YACtB,IAAI,WAAW,SAAS;YACxB,MAAM,MAAM;gBAAC,MAAM,CAAC,EAAE;gBAAE,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE;aAAC;YAC/C,MAAM,SAAS,GAAG,YAAY;gBAAC;oBAC3B,KAAK,QAAQ,IAAI,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE;oBAC7C,KAAK,QAAQ,IAAI,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE;iBAC9C;gBAAE;oBACD,KAAK,QAAQ,IAAI,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE;oBAC7C,KAAK,QAAQ,IAAI,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE;iBAC9C;aAAC;YACJ,IAAI,OAAO,MAAM,GAAG,GAAG,KAAK;QAC9B,OAAO;YACL,KAAK,SAAS,CAAC,EAAE,CAAC,EAAE;YACpB,KAAK,SAAS,CAAC,EAAE,CAAC,EAAE;YACpB,KAAK,SAAS,CAAC,EAAE,CAAC,EAAE;YACpB,KAAK,SAAS,CAAC,EAAE,CAAC,EAAE;QACtB;QAEA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QAEL,IAAI,QAAQ,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE,MACd,IAAI,CAAC,kBAAkB;QAE5B,IAAI,UAAU,MAAM,SAAS,CAAC,YACzB,IAAI,CAAC,UAAU,OAAO,CAAC,KAAK;QAEjC,IAAI,MAAM,OAAO,EAAE;YACjB,KAAK,KAAK,GAAG;YACb,KAAK,KAAK,GAAG;QACf,OAAO;YACL,IAAI,OAAO,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE,MAAM,IAAI,EACvB,EAAE,CAAC,mBAAmB,OAAO,MAC7B,EAAE,CAAC,iBAAiB,OAAO;YAChC,IAAI,MAAM,KACL,EAAE,CAAC,iBAAiB,WAAW,MAC/B,EAAE,CAAC,eAAe,UAAU;YAEjC,CAAA,GAAA,+OAAA,CAAA,cAAW,AAAD,EAAE,MAAM,IAAI;QACxB;QAEA,OAAO,IAAI,CAAC;QACZ,KAAK,KAAK,CAAC,OAAO,KAAK,IAAI;QAE3B,SAAS,MAAM,KAAK;YAClB,KAAK,MAAM,KAAK,MAAM,cAAc,IAAI;gBAAC;aAAM,CAAE;gBAC/C,KAAK,MAAM,KAAK,OACd,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,EAAE,EAAE,GAAG,GAAG,CAAA,GAAA,sPAAA,CAAA,UAAO,AAAD,EAAE,GAAG;YAC1D;YACA,IAAI,YAAY,CAAC,SAAS,CAAC,SAAS,OAAO,MAAM,KAAK,GAAG;gBACvD,MAAM,QAAQ,MAAM,CAAC,EAAE;gBACvB,IAAI,IAAI,MAAM,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,IAAI,IAAI,MAAM,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAC5D,QAAQ;qBAER,QAAQ;YACZ;YACA,KAAK,MAAM,SAAS,OAClB,IAAI,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE,GAAG,MAAM,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,GAAG,MAAM,GAAG,CAAC,EAAE;YACjE,SAAS;YACT,CAAA,GAAA,wMAAA,CAAA,UAAO,AAAD,EAAE;YACR,KAAK;QACP;QAEA,SAAS,KAAK,KAAK;YACjB,MAAM,QAAQ,MAAM,CAAC,EAAE,EAAE,SAAS,MAAM,MAAM;YAC9C,IAAI;YAEJ,KAAK,KAAK,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;YACzB,KAAK,KAAK,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;YAEzB,OAAQ;gBACN,KAAK;gBACL,KAAK;oBAAW;wBACd,IAAI,OAAO,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,KAAK,KAAK,IAAI,KAAK,KAAK;wBACtE,IAAI,OAAO,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,KAAK,KAAK,IAAI,KAAK,KAAK;wBACtE;oBACF;gBACA,KAAK;oBAAa;wBAChB,IAAI,MAAM,CAAC,EAAE,EAAE;4BACb,IAAI,OAAO,KAAK,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,QAAQ;4BACzF,IAAI,OAAO,KAAK,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,QAAQ;wBAC3F,OAAO;4BACL,IAAI,QAAQ,GAAG,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,KAAK,KAAK,IAAI,KAAK;iCAChE,IAAI,QAAQ,GAAG,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,KAAK,IAAI,KAAK,KAAK;4BAC1E,IAAI,QAAQ,GAAG,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,KAAK,KAAK,IAAI,KAAK;iCAChE,IAAI,QAAQ,GAAG,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,KAAK,IAAI,KAAK,KAAK;wBAC5E;wBACA;oBACF;gBACA,KAAK;oBAAa;wBAChB,IAAI,OAAO,KAAK,IAAI,GAAG,IAAI,GAAG,KAAK,KAAK,SAAS,KAAK,IAAI,GAAG,IAAI,GAAG,KAAK,KAAK;wBAC9E,IAAI,OAAO,KAAK,IAAI,GAAG,IAAI,GAAG,KAAK,KAAK,SAAS,KAAK,IAAI,GAAG,IAAI,GAAG,KAAK,KAAK;wBAC9E;oBACF;YACF;YAEA,IAAI,KAAK,IAAI;gBACX,SAAS,CAAC;gBACV,IAAI,IAAI,KAAK,IAAI,KAAK;gBACtB,IAAI,IAAI,KAAK,IAAI,KAAK;gBACtB,IAAI,QAAQ,OAAO,QAAQ,IAAI,CAAC,UAAU,OAAO,CAAC,OAAO,KAAK,CAAC,KAAK,CAAC;YACvE;YAEA,IAAI,KAAK,IAAI;gBACX,SAAS,CAAC;gBACV,IAAI,IAAI,KAAK,IAAI,KAAK;gBACtB,IAAI,IAAI,KAAK,IAAI,KAAK;gBACtB,IAAI,QAAQ,OAAO,QAAQ,IAAI,CAAC,UAAU,OAAO,CAAC,OAAO,KAAK,CAAC,KAAK,CAAC;YACvE;YAEA,IAAI,MAAM,SAAS,EAAE,YAAY,MAAM,SAAS,EAAE,4BAA4B;YAC9E,IAAI,OAAO,KAAK,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,SAAS,CAAC,EAAE,CAAC,EAAE;YACrD,IAAI,OAAO,KAAK,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,SAAS,CAAC,EAAE,CAAC,EAAE;YAErD,IAAI,SAAS,CAAC,EAAE,CAAC,EAAE,KAAK,MACjB,SAAS,CAAC,EAAE,CAAC,EAAE,KAAK,MACpB,SAAS,CAAC,EAAE,CAAC,EAAE,KAAK,MACpB,SAAS,CAAC,EAAE,CAAC,EAAE,KAAK,IAAI;gBAC7B,MAAM,SAAS,GAAG;oBAAC;wBAAC;wBAAI;qBAAG;oBAAE;wBAAC;wBAAI;qBAAG;iBAAC;gBACtC,OAAO,IAAI,CAAC;gBACZ,KAAK,KAAK,CAAC,OAAO,KAAK,IAAI;YAC7B;QACF;QAEA,SAAS,MAAM,KAAK;YAClB,CAAA,GAAA,wMAAA,CAAA,gBAAa,AAAD,EAAE;YACd,IAAI,MAAM,OAAO,EAAE;gBACjB,IAAI,MAAM,OAAO,CAAC,MAAM,EAAE;gBAC1B,IAAI,aAAa,aAAa;gBAC9B,cAAc,WAAW;oBAAa,cAAc;gBAAM,GAAG,MAAM,4BAA4B;YACjG,OAAO;gBACL,CAAA,GAAA,8OAAA,CAAA,aAAU,AAAD,EAAE,MAAM,IAAI,EAAE;gBACvB,KAAK,EAAE,CAAC,2DAA2D;YACrE;YACA,MAAM,IAAI,CAAC,kBAAkB;YAC7B,QAAQ,IAAI,CAAC,UAAU,QAAQ,OAAO;YACtC,IAAI,MAAM,SAAS,EAAE,YAAY,MAAM,SAAS,EAAE,uCAAuC;YACzF,IAAI,MAAM,YAAY,MAAM,SAAS,GAAG,MAAM,OAAO,IAAI,CAAC;YAC1D,KAAK,GAAG,CAAC,OAAO,KAAK,IAAI;QAC3B;QAEA,SAAS,UAAU,KAAK;YACtB,OAAQ,MAAM,OAAO;gBACnB,KAAK;oBAAI;wBACP,WAAW,SAAS;wBACpB;oBACF;gBACA,KAAK;oBAAI;wBACP,IAAI,SAAS,aAAa;4BACxB,IAAI,OAAO,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK;4BAChD,IAAI,OAAO,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK;4BAChD,OAAO;4BACP,KAAK;wBACP;wBACA;oBACF;gBACA,KAAK;oBAAI;wBACP,IAAI,SAAS,eAAe,SAAS,aAAa;4BAChD,IAAI,QAAQ,GAAG,KAAK,KAAK;iCAAS,IAAI,QAAQ,GAAG,KAAK,KAAK;4BAC3D,IAAI,QAAQ,GAAG,KAAK,KAAK;iCAAS,IAAI,QAAQ,GAAG,KAAK,KAAK;4BAC3D,OAAO;4BACP,QAAQ,IAAI,CAAC,UAAU,QAAQ,SAAS;4BACxC,KAAK;wBACP;wBACA;oBACF;gBACA;oBAAS;YACX;YACA,CAAA,GAAA,wMAAA,CAAA,UAAO,AAAD,EAAE;QACV;QAEA,SAAS,SAAS,KAAK;YACrB,OAAQ,MAAM,OAAO;gBACnB,KAAK;oBAAI;wBACP,IAAI,UAAU;4BACZ,QAAQ,QAAQ,WAAW;4BAC3B,KAAK;wBACP;wBACA;oBACF;gBACA,KAAK;oBAAI;wBACP,IAAI,SAAS,aAAa;4BACxB,IAAI,QAAQ,GAAG,KAAK;iCAAS,IAAI,QAAQ,GAAG,KAAK;4BACjD,IAAI,QAAQ,GAAG,KAAK;iCAAS,IAAI,QAAQ,GAAG,KAAK;4BACjD,OAAO;4BACP,KAAK;wBACP;wBACA;oBACF;gBACA,KAAK;oBAAI;wBACP,IAAI,SAAS,YAAY;4BACvB,IAAI,MAAM,MAAM,EAAE;gCAChB,IAAI,OAAO,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK;gCAChD,IAAI,OAAO,KAAK,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK;gCAChD,OAAO;4BACT,OAAO;gCACL,IAAI,QAAQ,GAAG,KAAK;qCAAS,IAAI,QAAQ,GAAG,KAAK;gCACjD,IAAI,QAAQ,GAAG,KAAK;qCAAS,IAAI,QAAQ,GAAG,KAAK;gCACjD,OAAO;4BACT;4BACA,QAAQ,IAAI,CAAC,UAAU,OAAO,CAAC,KAAK;4BACpC,KAAK;wBACP;wBACA;oBACF;gBACA;oBAAS;YACX;YACA,CAAA,GAAA,wMAAA,CAAA,UAAO,AAAD,EAAE;QACV;IACF;IAEA,SAAS,WAAW,KAAK;QACvB,QAAQ,IAAI,EAAE,WAAW,KAAK,CAAC;IACjC;IAEA,SAAS,WAAW,KAAK;QACvB,QAAQ,IAAI,EAAE,WAAW,KAAK,CAAC;IACjC;IAEA,SAAS;QACP,IAAI,QAAQ,IAAI,CAAC,OAAO,IAAI;YAAC,WAAW;QAAI;QAC5C,MAAM,MAAM,GAAG,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE;QAC1C,MAAM,GAAG,GAAG;QACZ,OAAO;IACT;IAEA,MAAM,MAAM,GAAG,SAAS,CAAC;QACvB,OAAO,UAAU,MAAM,GAAG,CAAC,SAAS,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,QAAQ,KAAK,KAAK,IAAI;IACnG;IAEA,MAAM,MAAM,GAAG,SAAS,CAAC;QACvB,OAAO,UAAU,MAAM,GAAG,CAAC,SAAS,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI;IAC5F;IAEA,MAAM,SAAS,GAAG,SAAS,CAAC;QAC1B,OAAO,UAAU,MAAM,GAAG,CAAC,YAAY,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,CAAC,IAAI,KAAK,IAAI;IAC/F;IAEA,MAAM,UAAU,GAAG,SAAS,CAAC;QAC3B,OAAO,UAAU,MAAM,GAAG,CAAC,aAAa,CAAC,GAAG,KAAK,IAAI;IACvD;IAEA,MAAM,YAAY,GAAG,SAAS,CAAC;QAC7B,OAAO,UAAU,MAAM,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,IAAI;IAClD;IAEA,MAAM,EAAE,GAAG;QACT,IAAI,QAAQ,UAAU,EAAE,CAAC,KAAK,CAAC,WAAW;QAC1C,OAAO,UAAU,YAAY,QAAQ;IACvC;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4429, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4435, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4437, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4452, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-interpolate%403.0.1/node_modules/d3-interpolate/src/zoom.js"], "sourcesContent": ["var epsilon2 = 1e-12;\n\nfunction cosh(x) {\n  return ((x = Math.exp(x)) + 1 / x) / 2;\n}\n\nfunction sinh(x) {\n  return ((x = Math.exp(x)) - 1 / x) / 2;\n}\n\nfunction tanh(x) {\n  return ((x = Math.exp(2 * x)) - 1) / (x + 1);\n}\n\nexport default (function zoomRho(rho, rho2, rho4) {\n\n  // p0 = [ux0, uy0, w0]\n  // p1 = [ux1, uy1, w1]\n  function zoom(p0, p1) {\n    var ux0 = p0[0], uy0 = p0[1], w0 = p0[2],\n        ux1 = p1[0], uy1 = p1[1], w1 = p1[2],\n        dx = ux1 - ux0,\n        dy = uy1 - uy0,\n        d2 = dx * dx + dy * dy,\n        i,\n        S;\n\n    // Special case for u0 ≅ u1.\n    if (d2 < epsilon2) {\n      S = Math.log(w1 / w0) / rho;\n      i = function(t) {\n        return [\n          ux0 + t * dx,\n          uy0 + t * dy,\n          w0 * Math.exp(rho * t * S)\n        ];\n      }\n    }\n\n    // General case.\n    else {\n      var d1 = Math.sqrt(d2),\n          b0 = (w1 * w1 - w0 * w0 + rho4 * d2) / (2 * w0 * rho2 * d1),\n          b1 = (w1 * w1 - w0 * w0 - rho4 * d2) / (2 * w1 * rho2 * d1),\n          r0 = Math.log(Math.sqrt(b0 * b0 + 1) - b0),\n          r1 = Math.log(Math.sqrt(b1 * b1 + 1) - b1);\n      S = (r1 - r0) / rho;\n      i = function(t) {\n        var s = t * S,\n            coshr0 = cosh(r0),\n            u = w0 / (rho2 * d1) * (coshr0 * tanh(rho * s + r0) - sinh(r0));\n        return [\n          ux0 + u * dx,\n          uy0 + u * dy,\n          w0 * coshr0 / cosh(rho * s + r0)\n        ];\n      }\n    }\n\n    i.duration = S * 1000 * rho / Math.SQRT2;\n\n    return i;\n  }\n\n  zoom.rho = function(_) {\n    var _1 = Math.max(1e-3, +_), _2 = _1 * _1, _4 = _2 * _2;\n    return zoomRho(_1, _2, _4);\n  };\n\n  return zoom;\n})(Math.SQRT2, 2, 4);\n"], "names": [], "mappings": ";;;AAAA,IAAI,WAAW;AAEf,SAAS,KAAK,CAAC;IACb,OAAO,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI;AACvC;AAEA,SAAS,KAAK,CAAC;IACb,OAAO,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI;AACvC;AAEA,SAAS,KAAK,CAAC;IACb,OAAO,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAC7C;uCAEe,AAAC,SAAS,QAAQ,GAAG,EAAE,IAAI,EAAE,IAAI;IAE9C,sBAAsB;IACtB,sBAAsB;IACtB,SAAS,KAAK,EAAE,EAAE,EAAE;QAClB,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EACpC,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EACpC,KAAK,MAAM,KACX,KAAK,MAAM,KACX,KAAK,KAAK,KAAK,KAAK,IACpB,GACA;QAEJ,4BAA4B;QAC5B,IAAI,KAAK,UAAU;YACjB,IAAI,KAAK,GAAG,CAAC,KAAK,MAAM;YACxB,IAAI,SAAS,CAAC;gBACZ,OAAO;oBACL,MAAM,IAAI;oBACV,MAAM,IAAI;oBACV,KAAK,KAAK,GAAG,CAAC,MAAM,IAAI;iBACzB;YACH;QACF,OAGK;YACH,IAAI,KAAK,KAAK,IAAI,CAAC,KACf,KAAK,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,GAC1D,KAAK,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,GAC1D,KAAK,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK,KACvC,KAAK,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;YAC3C,IAAI,CAAC,KAAK,EAAE,IAAI;YAChB,IAAI,SAAS,CAAC;gBACZ,IAAI,IAAI,IAAI,GACR,SAAS,KAAK,KACd,IAAI,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,KAAK,MAAM,IAAI,MAAM,KAAK,GAAG;gBAClE,OAAO;oBACL,MAAM,IAAI;oBACV,MAAM,IAAI;oBACV,KAAK,SAAS,KAAK,MAAM,IAAI;iBAC9B;YACH;QACF;QAEA,EAAE,QAAQ,GAAG,IAAI,OAAO,MAAM,KAAK,KAAK;QAExC,OAAO;IACT;IAEA,KAAK,GAAG,GAAG,SAAS,CAAC;QACnB,IAAI,KAAK,KAAK,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK;QACrD,OAAO,QAAQ,IAAI,IAAI;IACzB;IAEA,OAAO;AACT,EAAG,KAAK,KAAK,EAAE,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4501, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4517, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-zoom%403.0.0/node_modules/d3-zoom/src/constant.js"], "sourcesContent": ["export default x => () => x;\n"], "names": [], "mappings": ";;;uCAAe,CAAA,IAAK,IAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4521, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4527, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-zoom%403.0.0/node_modules/d3-zoom/src/event.js"], "sourcesContent": ["export default function ZoomEvent(type, {\n  sourceEvent,\n  target,\n  transform,\n  dispatch\n}) {\n  Object.defineProperties(this, {\n    type: {value: type, enumerable: true, configurable: true},\n    sourceEvent: {value: sourceEvent, enumerable: true, configurable: true},\n    target: {value: target, enumerable: true, configurable: true},\n    transform: {value: transform, enumerable: true, configurable: true},\n    _: {value: dispatch}\n  });\n}\n"], "names": [], "mappings": ";;;AAAe,SAAS,UAAU,IAAI,EAAE,EACtC,WAAW,EACX,MAAM,EACN,SAAS,EACT,QAAQ,EACT;IACC,OAAO,gBAAgB,CAAC,IAAI,EAAE;QAC5B,MAAM;YAAC,OAAO;YAAM,YAAY;YAAM,cAAc;QAAI;QACxD,aAAa;YAAC,OAAO;YAAa,YAAY;YAAM,cAAc;QAAI;QACtE,QAAQ;YAAC,OAAO;YAAQ,YAAY;YAAM,cAAc;QAAI;QAC5D,WAAW;YAAC,OAAO;YAAW,YAAY;YAAM,cAAc;QAAI;QAClE,GAAG;YAAC,OAAO;QAAQ;IACrB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4557, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4563, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-zoom%403.0.0/node_modules/d3-zoom/src/transform.js"], "sourcesContent": ["export function Transform(k, x, y) {\n  this.k = k;\n  this.x = x;\n  this.y = y;\n}\n\nTransform.prototype = {\n  constructor: Transform,\n  scale: function(k) {\n    return k === 1 ? this : new Transform(this.k * k, this.x, this.y);\n  },\n  translate: function(x, y) {\n    return x === 0 & y === 0 ? this : new Transform(this.k, this.x + this.k * x, this.y + this.k * y);\n  },\n  apply: function(point) {\n    return [point[0] * this.k + this.x, point[1] * this.k + this.y];\n  },\n  applyX: function(x) {\n    return x * this.k + this.x;\n  },\n  applyY: function(y) {\n    return y * this.k + this.y;\n  },\n  invert: function(location) {\n    return [(location[0] - this.x) / this.k, (location[1] - this.y) / this.k];\n  },\n  invertX: function(x) {\n    return (x - this.x) / this.k;\n  },\n  invertY: function(y) {\n    return (y - this.y) / this.k;\n  },\n  rescaleX: function(x) {\n    return x.copy().domain(x.range().map(this.invertX, this).map(x.invert, x));\n  },\n  rescaleY: function(y) {\n    return y.copy().domain(y.range().map(this.invertY, this).map(y.invert, y));\n  },\n  toString: function() {\n    return \"translate(\" + this.x + \",\" + this.y + \") scale(\" + this.k + \")\";\n  }\n};\n\nexport var identity = new Transform(1, 0, 0);\n\ntransform.prototype = Transform.prototype;\n\nexport default function transform(node) {\n  while (!node.__zoom) if (!(node = node.parentNode)) return identity;\n  return node.__zoom;\n}\n"], "names": [], "mappings": ";;;;;AAAO,SAAS,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;IAC/B,IAAI,CAAC,CAAC,GAAG;IACT,IAAI,CAAC,CAAC,GAAG;IACT,IAAI,CAAC,CAAC,GAAG;AACX;AAEA,UAAU,SAAS,GAAG;IACpB,aAAa;IACb,OAAO,SAAS,CAAC;QACf,OAAO,MAAM,IAAI,IAAI,GAAG,IAAI,UAAU,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IAClE;IACA,WAAW,SAAS,CAAC,EAAE,CAAC;QACtB,OAAO,MAAM,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI,UAAU,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG;IACjG;IACA,OAAO,SAAS,KAAK;QACnB,OAAO;YAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YAAE,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;SAAC;IACjE;IACA,QAAQ,SAAS,CAAC;QAChB,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IAC5B;IACA,QAAQ,SAAS,CAAC;QAChB,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;IAC5B;IACA,QAAQ,SAAS,QAAQ;QACvB,OAAO;YAAC,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;YAAE,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;SAAC;IAC3E;IACA,SAAS,SAAS,CAAC;QACjB,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;IAC9B;IACA,SAAS,SAAS,CAAC;QACjB,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;IAC9B;IACA,UAAU,SAAS,CAAC;QAClB,OAAO,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,MAAM,EAAE;IACzE;IACA,UAAU,SAAS,CAAC;QAClB,OAAO,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,MAAM,EAAE;IACzE;IACA,UAAU;QACR,OAAO,eAAe,IAAI,CAAC,CAAC,GAAG,MAAM,IAAI,CAAC,CAAC,GAAG,aAAa,IAAI,CAAC,CAAC,GAAG;IACtE;AACF;AAEO,IAAI,WAAW,IAAI,UAAU,GAAG,GAAG;AAE1C,UAAU,SAAS,GAAG,UAAU,SAAS;AAE1B,SAAS,UAAU,IAAI;IACpC,MAAO,CAAC,KAAK,MAAM,CAAE,IAAI,CAAC,CAAC,OAAO,KAAK,UAAU,GAAG,OAAO;IAC3D,OAAO,KAAK,MAAM;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4621, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4627, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-zoom%403.0.0/node_modules/d3-zoom/src/noevent.js"], "sourcesContent": ["export function nopropagation(event) {\n  event.stopImmediatePropagation();\n}\n\nexport default function(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS,cAAc,KAAK;IACjC,MAAM,wBAAwB;AAChC;AAEe,wCAAS,KAAK;IAC3B,MAAM,cAAc;IACpB,MAAM,wBAAwB;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4638, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4644, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-zoom%403.0.0/node_modules/d3-zoom/src/zoom.js"], "sourcesContent": ["import {dispatch} from \"d3-dispatch\";\nimport {dragDisable, dragEnable} from \"d3-drag\";\nimport {interpolateZoom} from \"d3-interpolate\";\nimport {select, pointer} from \"d3-selection\";\nimport {interrupt} from \"d3-transition\";\nimport constant from \"./constant.js\";\nimport ZoomEvent from \"./event.js\";\nimport {Transform, identity} from \"./transform.js\";\nimport noevent, {nopropagation} from \"./noevent.js\";\n\n// Ignore right-click, since that should open the context menu.\n// except for pinch-to-zoom, which is sent as a wheel+ctrlKey event\nfunction defaultFilter(event) {\n  return (!event.ctrlKey || event.type === 'wheel') && !event.button;\n}\n\nfunction defaultExtent() {\n  var e = this;\n  if (e instanceof SVGElement) {\n    e = e.ownerSVGElement || e;\n    if (e.hasAttribute(\"viewBox\")) {\n      e = e.viewBox.baseVal;\n      return [[e.x, e.y], [e.x + e.width, e.y + e.height]];\n    }\n    return [[0, 0], [e.width.baseVal.value, e.height.baseVal.value]];\n  }\n  return [[0, 0], [e.clientWidth, e.clientHeight]];\n}\n\nfunction defaultTransform() {\n  return this.__zoom || identity;\n}\n\nfunction defaultWheelDelta(event) {\n  return -event.deltaY * (event.deltaMode === 1 ? 0.05 : event.deltaMode ? 1 : 0.002) * (event.ctrlKey ? 10 : 1);\n}\n\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || (\"ontouchstart\" in this);\n}\n\nfunction defaultConstrain(transform, extent, translateExtent) {\n  var dx0 = transform.invertX(extent[0][0]) - translateExtent[0][0],\n      dx1 = transform.invertX(extent[1][0]) - translateExtent[1][0],\n      dy0 = transform.invertY(extent[0][1]) - translateExtent[0][1],\n      dy1 = transform.invertY(extent[1][1]) - translateExtent[1][1];\n  return transform.translate(\n    dx1 > dx0 ? (dx0 + dx1) / 2 : Math.min(0, dx0) || Math.max(0, dx1),\n    dy1 > dy0 ? (dy0 + dy1) / 2 : Math.min(0, dy0) || Math.max(0, dy1)\n  );\n}\n\nexport default function() {\n  var filter = defaultFilter,\n      extent = defaultExtent,\n      constrain = defaultConstrain,\n      wheelDelta = defaultWheelDelta,\n      touchable = defaultTouchable,\n      scaleExtent = [0, Infinity],\n      translateExtent = [[-Infinity, -Infinity], [Infinity, Infinity]],\n      duration = 250,\n      interpolate = interpolateZoom,\n      listeners = dispatch(\"start\", \"zoom\", \"end\"),\n      touchstarting,\n      touchfirst,\n      touchending,\n      touchDelay = 500,\n      wheelDelay = 150,\n      clickDistance2 = 0,\n      tapDistance = 10;\n\n  function zoom(selection) {\n    selection\n        .property(\"__zoom\", defaultTransform)\n        .on(\"wheel.zoom\", wheeled, {passive: false})\n        .on(\"mousedown.zoom\", mousedowned)\n        .on(\"dblclick.zoom\", dblclicked)\n      .filter(touchable)\n        .on(\"touchstart.zoom\", touchstarted)\n        .on(\"touchmove.zoom\", touchmoved)\n        .on(\"touchend.zoom touchcancel.zoom\", touchended)\n        .style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n\n  zoom.transform = function(collection, transform, point, event) {\n    var selection = collection.selection ? collection.selection() : collection;\n    selection.property(\"__zoom\", defaultTransform);\n    if (collection !== selection) {\n      schedule(collection, transform, point, event);\n    } else {\n      selection.interrupt().each(function() {\n        gesture(this, arguments)\n          .event(event)\n          .start()\n          .zoom(null, typeof transform === \"function\" ? transform.apply(this, arguments) : transform)\n          .end();\n      });\n    }\n  };\n\n  zoom.scaleBy = function(selection, k, p, event) {\n    zoom.scaleTo(selection, function() {\n      var k0 = this.__zoom.k,\n          k1 = typeof k === \"function\" ? k.apply(this, arguments) : k;\n      return k0 * k1;\n    }, p, event);\n  };\n\n  zoom.scaleTo = function(selection, k, p, event) {\n    zoom.transform(selection, function() {\n      var e = extent.apply(this, arguments),\n          t0 = this.__zoom,\n          p0 = p == null ? centroid(e) : typeof p === \"function\" ? p.apply(this, arguments) : p,\n          p1 = t0.invert(p0),\n          k1 = typeof k === \"function\" ? k.apply(this, arguments) : k;\n      return constrain(translate(scale(t0, k1), p0, p1), e, translateExtent);\n    }, p, event);\n  };\n\n  zoom.translateBy = function(selection, x, y, event) {\n    zoom.transform(selection, function() {\n      return constrain(this.__zoom.translate(\n        typeof x === \"function\" ? x.apply(this, arguments) : x,\n        typeof y === \"function\" ? y.apply(this, arguments) : y\n      ), extent.apply(this, arguments), translateExtent);\n    }, null, event);\n  };\n\n  zoom.translateTo = function(selection, x, y, p, event) {\n    zoom.transform(selection, function() {\n      var e = extent.apply(this, arguments),\n          t = this.__zoom,\n          p0 = p == null ? centroid(e) : typeof p === \"function\" ? p.apply(this, arguments) : p;\n      return constrain(identity.translate(p0[0], p0[1]).scale(t.k).translate(\n        typeof x === \"function\" ? -x.apply(this, arguments) : -x,\n        typeof y === \"function\" ? -y.apply(this, arguments) : -y\n      ), e, translateExtent);\n    }, p, event);\n  };\n\n  function scale(transform, k) {\n    k = Math.max(scaleExtent[0], Math.min(scaleExtent[1], k));\n    return k === transform.k ? transform : new Transform(k, transform.x, transform.y);\n  }\n\n  function translate(transform, p0, p1) {\n    var x = p0[0] - p1[0] * transform.k, y = p0[1] - p1[1] * transform.k;\n    return x === transform.x && y === transform.y ? transform : new Transform(transform.k, x, y);\n  }\n\n  function centroid(extent) {\n    return [(+extent[0][0] + +extent[1][0]) / 2, (+extent[0][1] + +extent[1][1]) / 2];\n  }\n\n  function schedule(transition, transform, point, event) {\n    transition\n        .on(\"start.zoom\", function() { gesture(this, arguments).event(event).start(); })\n        .on(\"interrupt.zoom end.zoom\", function() { gesture(this, arguments).event(event).end(); })\n        .tween(\"zoom\", function() {\n          var that = this,\n              args = arguments,\n              g = gesture(that, args).event(event),\n              e = extent.apply(that, args),\n              p = point == null ? centroid(e) : typeof point === \"function\" ? point.apply(that, args) : point,\n              w = Math.max(e[1][0] - e[0][0], e[1][1] - e[0][1]),\n              a = that.__zoom,\n              b = typeof transform === \"function\" ? transform.apply(that, args) : transform,\n              i = interpolate(a.invert(p).concat(w / a.k), b.invert(p).concat(w / b.k));\n          return function(t) {\n            if (t === 1) t = b; // Avoid rounding error on end.\n            else { var l = i(t), k = w / l[2]; t = new Transform(k, p[0] - l[0] * k, p[1] - l[1] * k); }\n            g.zoom(null, t);\n          };\n        });\n  }\n\n  function gesture(that, args, clean) {\n    return (!clean && that.__zooming) || new Gesture(that, args);\n  }\n\n  function Gesture(that, args) {\n    this.that = that;\n    this.args = args;\n    this.active = 0;\n    this.sourceEvent = null;\n    this.extent = extent.apply(that, args);\n    this.taps = 0;\n  }\n\n  Gesture.prototype = {\n    event: function(event) {\n      if (event) this.sourceEvent = event;\n      return this;\n    },\n    start: function() {\n      if (++this.active === 1) {\n        this.that.__zooming = this;\n        this.emit(\"start\");\n      }\n      return this;\n    },\n    zoom: function(key, transform) {\n      if (this.mouse && key !== \"mouse\") this.mouse[1] = transform.invert(this.mouse[0]);\n      if (this.touch0 && key !== \"touch\") this.touch0[1] = transform.invert(this.touch0[0]);\n      if (this.touch1 && key !== \"touch\") this.touch1[1] = transform.invert(this.touch1[0]);\n      this.that.__zoom = transform;\n      this.emit(\"zoom\");\n      return this;\n    },\n    end: function() {\n      if (--this.active === 0) {\n        delete this.that.__zooming;\n        this.emit(\"end\");\n      }\n      return this;\n    },\n    emit: function(type) {\n      var d = select(this.that).datum();\n      listeners.call(\n        type,\n        this.that,\n        new ZoomEvent(type, {\n          sourceEvent: this.sourceEvent,\n          target: zoom,\n          type,\n          transform: this.that.__zoom,\n          dispatch: listeners\n        }),\n        d\n      );\n    }\n  };\n\n  function wheeled(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var g = gesture(this, args).event(event),\n        t = this.__zoom,\n        k = Math.max(scaleExtent[0], Math.min(scaleExtent[1], t.k * Math.pow(2, wheelDelta.apply(this, arguments)))),\n        p = pointer(event);\n\n    // If the mouse is in the same location as before, reuse it.\n    // If there were recent wheel events, reset the wheel idle timeout.\n    if (g.wheel) {\n      if (g.mouse[0][0] !== p[0] || g.mouse[0][1] !== p[1]) {\n        g.mouse[1] = t.invert(g.mouse[0] = p);\n      }\n      clearTimeout(g.wheel);\n    }\n\n    // If this wheel event won’t trigger a transform change, ignore it.\n    else if (t.k === k) return;\n\n    // Otherwise, capture the mouse point and location at the start.\n    else {\n      g.mouse = [p, t.invert(p)];\n      interrupt(this);\n      g.start();\n    }\n\n    noevent(event);\n    g.wheel = setTimeout(wheelidled, wheelDelay);\n    g.zoom(\"mouse\", constrain(translate(scale(t, k), g.mouse[0], g.mouse[1]), g.extent, translateExtent));\n\n    function wheelidled() {\n      g.wheel = null;\n      g.end();\n    }\n  }\n\n  function mousedowned(event, ...args) {\n    if (touchending || !filter.apply(this, arguments)) return;\n    var currentTarget = event.currentTarget,\n        g = gesture(this, args, true).event(event),\n        v = select(event.view).on(\"mousemove.zoom\", mousemoved, true).on(\"mouseup.zoom\", mouseupped, true),\n        p = pointer(event, currentTarget),\n        x0 = event.clientX,\n        y0 = event.clientY;\n\n    dragDisable(event.view);\n    nopropagation(event);\n    g.mouse = [p, this.__zoom.invert(p)];\n    interrupt(this);\n    g.start();\n\n    function mousemoved(event) {\n      noevent(event);\n      if (!g.moved) {\n        var dx = event.clientX - x0, dy = event.clientY - y0;\n        g.moved = dx * dx + dy * dy > clickDistance2;\n      }\n      g.event(event)\n       .zoom(\"mouse\", constrain(translate(g.that.__zoom, g.mouse[0] = pointer(event, currentTarget), g.mouse[1]), g.extent, translateExtent));\n    }\n\n    function mouseupped(event) {\n      v.on(\"mousemove.zoom mouseup.zoom\", null);\n      dragEnable(event.view, g.moved);\n      noevent(event);\n      g.event(event).end();\n    }\n  }\n\n  function dblclicked(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var t0 = this.__zoom,\n        p0 = pointer(event.changedTouches ? event.changedTouches[0] : event, this),\n        p1 = t0.invert(p0),\n        k1 = t0.k * (event.shiftKey ? 0.5 : 2),\n        t1 = constrain(translate(scale(t0, k1), p0, p1), extent.apply(this, args), translateExtent);\n\n    noevent(event);\n    if (duration > 0) select(this).transition().duration(duration).call(schedule, t1, p0, event);\n    else select(this).call(zoom.transform, t1, p0, event);\n  }\n\n  function touchstarted(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var touches = event.touches,\n        n = touches.length,\n        g = gesture(this, args, event.changedTouches.length === n).event(event),\n        started, i, t, p;\n\n    nopropagation(event);\n    for (i = 0; i < n; ++i) {\n      t = touches[i], p = pointer(t, this);\n      p = [p, this.__zoom.invert(p), t.identifier];\n      if (!g.touch0) g.touch0 = p, started = true, g.taps = 1 + !!touchstarting;\n      else if (!g.touch1 && g.touch0[2] !== p[2]) g.touch1 = p, g.taps = 0;\n    }\n\n    if (touchstarting) touchstarting = clearTimeout(touchstarting);\n\n    if (started) {\n      if (g.taps < 2) touchfirst = p[0], touchstarting = setTimeout(function() { touchstarting = null; }, touchDelay);\n      interrupt(this);\n      g.start();\n    }\n  }\n\n  function touchmoved(event, ...args) {\n    if (!this.__zooming) return;\n    var g = gesture(this, args).event(event),\n        touches = event.changedTouches,\n        n = touches.length, i, t, p, l;\n\n    noevent(event);\n    for (i = 0; i < n; ++i) {\n      t = touches[i], p = pointer(t, this);\n      if (g.touch0 && g.touch0[2] === t.identifier) g.touch0[0] = p;\n      else if (g.touch1 && g.touch1[2] === t.identifier) g.touch1[0] = p;\n    }\n    t = g.that.__zoom;\n    if (g.touch1) {\n      var p0 = g.touch0[0], l0 = g.touch0[1],\n          p1 = g.touch1[0], l1 = g.touch1[1],\n          dp = (dp = p1[0] - p0[0]) * dp + (dp = p1[1] - p0[1]) * dp,\n          dl = (dl = l1[0] - l0[0]) * dl + (dl = l1[1] - l0[1]) * dl;\n      t = scale(t, Math.sqrt(dp / dl));\n      p = [(p0[0] + p1[0]) / 2, (p0[1] + p1[1]) / 2];\n      l = [(l0[0] + l1[0]) / 2, (l0[1] + l1[1]) / 2];\n    }\n    else if (g.touch0) p = g.touch0[0], l = g.touch0[1];\n    else return;\n\n    g.zoom(\"touch\", constrain(translate(t, p, l), g.extent, translateExtent));\n  }\n\n  function touchended(event, ...args) {\n    if (!this.__zooming) return;\n    var g = gesture(this, args).event(event),\n        touches = event.changedTouches,\n        n = touches.length, i, t;\n\n    nopropagation(event);\n    if (touchending) clearTimeout(touchending);\n    touchending = setTimeout(function() { touchending = null; }, touchDelay);\n    for (i = 0; i < n; ++i) {\n      t = touches[i];\n      if (g.touch0 && g.touch0[2] === t.identifier) delete g.touch0;\n      else if (g.touch1 && g.touch1[2] === t.identifier) delete g.touch1;\n    }\n    if (g.touch1 && !g.touch0) g.touch0 = g.touch1, delete g.touch1;\n    if (g.touch0) g.touch0[1] = this.__zoom.invert(g.touch0[0]);\n    else {\n      g.end();\n      // If this was a dbltap, reroute to the (optional) dblclick.zoom handler.\n      if (g.taps === 2) {\n        t = pointer(t, this);\n        if (Math.hypot(touchfirst[0] - t[0], touchfirst[1] - t[1]) < tapDistance) {\n          var p = select(this).on(\"dblclick.zoom\");\n          if (p) p.apply(this, arguments);\n        }\n      }\n    }\n  }\n\n  zoom.wheelDelta = function(_) {\n    return arguments.length ? (wheelDelta = typeof _ === \"function\" ? _ : constant(+_), zoom) : wheelDelta;\n  };\n\n  zoom.filter = function(_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), zoom) : filter;\n  };\n\n  zoom.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), zoom) : touchable;\n  };\n\n  zoom.extent = function(_) {\n    return arguments.length ? (extent = typeof _ === \"function\" ? _ : constant([[+_[0][0], +_[0][1]], [+_[1][0], +_[1][1]]]), zoom) : extent;\n  };\n\n  zoom.scaleExtent = function(_) {\n    return arguments.length ? (scaleExtent[0] = +_[0], scaleExtent[1] = +_[1], zoom) : [scaleExtent[0], scaleExtent[1]];\n  };\n\n  zoom.translateExtent = function(_) {\n    return arguments.length ? (translateExtent[0][0] = +_[0][0], translateExtent[1][0] = +_[1][0], translateExtent[0][1] = +_[0][1], translateExtent[1][1] = +_[1][1], zoom) : [[translateExtent[0][0], translateExtent[0][1]], [translateExtent[1][0], translateExtent[1][1]]];\n  };\n\n  zoom.constrain = function(_) {\n    return arguments.length ? (constrain = _, zoom) : constrain;\n  };\n\n  zoom.duration = function(_) {\n    return arguments.length ? (duration = +_, zoom) : duration;\n  };\n\n  zoom.interpolate = function(_) {\n    return arguments.length ? (interpolate = _, zoom) : interpolate;\n  };\n\n  zoom.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? zoom : value;\n  };\n\n  zoom.clickDistance = function(_) {\n    return arguments.length ? (clickDistance2 = (_ = +_) * _, zoom) : Math.sqrt(clickDistance2);\n  };\n\n  zoom.tapDistance = function(_) {\n    return arguments.length ? (tapDistance = +_, zoom) : tapDistance;\n  };\n\n  return zoom;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEA,+DAA+D;AAC/D,mEAAmE;AACnE,SAAS,cAAc,KAAK;IAC1B,OAAO,CAAC,CAAC,MAAM,OAAO,IAAI,MAAM,IAAI,KAAK,OAAO,KAAK,CAAC,MAAM,MAAM;AACpE;AAEA,SAAS;IACP,IAAI,IAAI,IAAI;IACZ,IAAI,aAAa,YAAY;QAC3B,IAAI,EAAE,eAAe,IAAI;QACzB,IAAI,EAAE,YAAY,CAAC,YAAY;YAC7B,IAAI,EAAE,OAAO,CAAC,OAAO;YACrB,OAAO;gBAAC;oBAAC,EAAE,CAAC;oBAAE,EAAE,CAAC;iBAAC;gBAAE;oBAAC,EAAE,CAAC,GAAG,EAAE,KAAK;oBAAE,EAAE,CAAC,GAAG,EAAE,MAAM;iBAAC;aAAC;QACtD;QACA,OAAO;YAAC;gBAAC;gBAAG;aAAE;YAAE;gBAAC,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK;gBAAE,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK;aAAC;SAAC;IAClE;IACA,OAAO;QAAC;YAAC;YAAG;SAAE;QAAE;YAAC,EAAE,WAAW;YAAE,EAAE,YAAY;SAAC;KAAC;AAClD;AAEA,SAAS;IACP,OAAO,IAAI,CAAC,MAAM,IAAI,wMAAA,CAAA,WAAQ;AAChC;AAEA,SAAS,kBAAkB,KAAK;IAC9B,OAAO,CAAC,MAAM,MAAM,GAAG,CAAC,MAAM,SAAS,KAAK,IAAI,OAAO,MAAM,SAAS,GAAG,IAAI,KAAK,IAAI,CAAC,MAAM,OAAO,GAAG,KAAK,CAAC;AAC/G;AAEA,SAAS;IACP,OAAO,UAAU,cAAc,IAAK,kBAAkB,IAAI;AAC5D;AAEA,SAAS,iBAAiB,SAAS,EAAE,MAAM,EAAE,eAAe;IAC1D,IAAI,MAAM,UAAU,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,eAAe,CAAC,EAAE,CAAC,EAAE,EAC7D,MAAM,UAAU,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,eAAe,CAAC,EAAE,CAAC,EAAE,EAC7D,MAAM,UAAU,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,eAAe,CAAC,EAAE,CAAC,EAAE,EAC7D,MAAM,UAAU,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,eAAe,CAAC,EAAE,CAAC,EAAE;IACjE,OAAO,UAAU,SAAS,CACxB,MAAM,MAAM,CAAC,MAAM,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,QAAQ,KAAK,GAAG,CAAC,GAAG,MAC9D,MAAM,MAAM,CAAC,MAAM,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,QAAQ,KAAK,GAAG,CAAC,GAAG;AAElE;AAEe;IACb,IAAI,SAAS,eACT,SAAS,eACT,YAAY,kBACZ,aAAa,mBACb,YAAY,kBACZ,cAAc;QAAC;QAAG;KAAS,EAC3B,kBAAkB;QAAC;YAAC,CAAC;YAAU,CAAC;SAAS;QAAE;YAAC;YAAU;SAAS;KAAC,EAChE,WAAW,KACX,cAAc,+PAAA,CAAA,kBAAe,EAC7B,YAAY,CAAA,GAAA,sPAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,QAAQ,QACtC,eACA,YACA,aACA,aAAa,KACb,aAAa,KACb,iBAAiB,GACjB,cAAc;IAElB,SAAS,KAAK,SAAS;QACrB,UACK,QAAQ,CAAC,UAAU,kBACnB,EAAE,CAAC,cAAc,SAAS;YAAC,SAAS;QAAK,GACzC,EAAE,CAAC,kBAAkB,aACrB,EAAE,CAAC,iBAAiB,YACtB,MAAM,CAAC,WACL,EAAE,CAAC,mBAAmB,cACtB,EAAE,CAAC,kBAAkB,YACrB,EAAE,CAAC,kCAAkC,YACrC,KAAK,CAAC,+BAA+B;IAC5C;IAEA,KAAK,SAAS,GAAG,SAAS,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK;QAC3D,IAAI,YAAY,WAAW,SAAS,GAAG,WAAW,SAAS,KAAK;QAChE,UAAU,QAAQ,CAAC,UAAU;QAC7B,IAAI,eAAe,WAAW;YAC5B,SAAS,YAAY,WAAW,OAAO;QACzC,OAAO;YACL,UAAU,SAAS,GAAG,IAAI,CAAC;gBACzB,QAAQ,IAAI,EAAE,WACX,KAAK,CAAC,OACN,KAAK,GACL,IAAI,CAAC,MAAM,OAAO,cAAc,aAAa,UAAU,KAAK,CAAC,IAAI,EAAE,aAAa,WAChF,GAAG;YACR;QACF;IACF;IAEA,KAAK,OAAO,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK;QAC5C,KAAK,OAAO,CAAC,WAAW;YACtB,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,EAClB,KAAK,OAAO,MAAM,aAAa,EAAE,KAAK,CAAC,IAAI,EAAE,aAAa;YAC9D,OAAO,KAAK;QACd,GAAG,GAAG;IACR;IAEA,KAAK,OAAO,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK;QAC5C,KAAK,SAAS,CAAC,WAAW;YACxB,IAAI,IAAI,OAAO,KAAK,CAAC,IAAI,EAAE,YACvB,KAAK,IAAI,CAAC,MAAM,EAChB,KAAK,KAAK,OAAO,SAAS,KAAK,OAAO,MAAM,aAAa,EAAE,KAAK,CAAC,IAAI,EAAE,aAAa,GACpF,KAAK,GAAG,MAAM,CAAC,KACf,KAAK,OAAO,MAAM,aAAa,EAAE,KAAK,CAAC,IAAI,EAAE,aAAa;YAC9D,OAAO,UAAU,UAAU,MAAM,IAAI,KAAK,IAAI,KAAK,GAAG;QACxD,GAAG,GAAG;IACR;IAEA,KAAK,WAAW,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK;QAChD,KAAK,SAAS,CAAC,WAAW;YACxB,OAAO,UAAU,IAAI,CAAC,MAAM,CAAC,SAAS,CACpC,OAAO,MAAM,aAAa,EAAE,KAAK,CAAC,IAAI,EAAE,aAAa,GACrD,OAAO,MAAM,aAAa,EAAE,KAAK,CAAC,IAAI,EAAE,aAAa,IACpD,OAAO,KAAK,CAAC,IAAI,EAAE,YAAY;QACpC,GAAG,MAAM;IACX;IAEA,KAAK,WAAW,GAAG,SAAS,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK;QACnD,KAAK,SAAS,CAAC,WAAW;YACxB,IAAI,IAAI,OAAO,KAAK,CAAC,IAAI,EAAE,YACvB,IAAI,IAAI,CAAC,MAAM,EACf,KAAK,KAAK,OAAO,SAAS,KAAK,OAAO,MAAM,aAAa,EAAE,KAAK,CAAC,IAAI,EAAE,aAAa;YACxF,OAAO,UAAU,wMAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,SAAS,CACpE,OAAO,MAAM,aAAa,CAAC,EAAE,KAAK,CAAC,IAAI,EAAE,aAAa,CAAC,GACvD,OAAO,MAAM,aAAa,CAAC,EAAE,KAAK,CAAC,IAAI,EAAE,aAAa,CAAC,IACtD,GAAG;QACR,GAAG,GAAG;IACR;IAEA,SAAS,MAAM,SAAS,EAAE,CAAC;QACzB,IAAI,KAAK,GAAG,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC,WAAW,CAAC,EAAE,EAAE;QACtD,OAAO,MAAM,UAAU,CAAC,GAAG,YAAY,IAAI,wMAAA,CAAA,YAAS,CAAC,GAAG,UAAU,CAAC,EAAE,UAAU,CAAC;IAClF;IAEA,SAAS,UAAU,SAAS,EAAE,EAAE,EAAE,EAAE;QAClC,IAAI,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC;QACpE,OAAO,MAAM,UAAU,CAAC,IAAI,MAAM,UAAU,CAAC,GAAG,YAAY,IAAI,wMAAA,CAAA,YAAS,CAAC,UAAU,CAAC,EAAE,GAAG;IAC5F;IAEA,SAAS,SAAS,MAAM;QACtB,OAAO;YAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI;YAAG,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI;SAAE;IACnF;IAEA,SAAS,SAAS,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK;QACnD,WACK,EAAE,CAAC,cAAc;YAAa,QAAQ,IAAI,EAAE,WAAW,KAAK,CAAC,OAAO,KAAK;QAAI,GAC7E,EAAE,CAAC,2BAA2B;YAAa,QAAQ,IAAI,EAAE,WAAW,KAAK,CAAC,OAAO,GAAG;QAAI,GACxF,KAAK,CAAC,QAAQ;YACb,IAAI,OAAO,IAAI,EACX,OAAO,WACP,IAAI,QAAQ,MAAM,MAAM,KAAK,CAAC,QAC9B,IAAI,OAAO,KAAK,CAAC,MAAM,OACvB,IAAI,SAAS,OAAO,SAAS,KAAK,OAAO,UAAU,aAAa,MAAM,KAAK,CAAC,MAAM,QAAQ,OAC1F,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GACjD,IAAI,KAAK,MAAM,EACf,IAAI,OAAO,cAAc,aAAa,UAAU,KAAK,CAAC,MAAM,QAAQ,WACpE,IAAI,YAAY,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;YAC3E,OAAO,SAAS,CAAC;gBACf,IAAI,MAAM,GAAG,IAAI,GAAG,+BAA+B;qBAC9C;oBAAE,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE;oBAAE,IAAI,IAAI,wMAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG;gBAAI;gBAC3F,EAAE,IAAI,CAAC,MAAM;YACf;QACF;IACN;IAEA,SAAS,QAAQ,IAAI,EAAE,IAAI,EAAE,KAAK;QAChC,OAAO,AAAC,CAAC,SAAS,KAAK,SAAS,IAAK,IAAI,QAAQ,MAAM;IACzD;IAEA,SAAS,QAAQ,IAAI,EAAE,IAAI;QACzB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK,CAAC,MAAM;QACjC,IAAI,CAAC,IAAI,GAAG;IACd;IAEA,QAAQ,SAAS,GAAG;QAClB,OAAO,SAAS,KAAK;YACnB,IAAI,OAAO,IAAI,CAAC,WAAW,GAAG;YAC9B,OAAO,IAAI;QACb;QACA,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,MAAM,KAAK,GAAG;gBACvB,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI;gBAC1B,IAAI,CAAC,IAAI,CAAC;YACZ;YACA,OAAO,IAAI;QACb;QACA,MAAM,SAAS,GAAG,EAAE,SAAS;YAC3B,IAAI,IAAI,CAAC,KAAK,IAAI,QAAQ,SAAS,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACjF,IAAI,IAAI,CAAC,MAAM,IAAI,QAAQ,SAAS,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,UAAU,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACpF,IAAI,IAAI,CAAC,MAAM,IAAI,QAAQ,SAAS,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,UAAU,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACpF,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;YACnB,IAAI,CAAC,IAAI,CAAC;YACV,OAAO,IAAI;QACb;QACA,KAAK;YACH,IAAI,EAAE,IAAI,CAAC,MAAM,KAAK,GAAG;gBACvB,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS;gBAC1B,IAAI,CAAC,IAAI,CAAC;YACZ;YACA,OAAO,IAAI;QACb;QACA,MAAM,SAAS,IAAI;YACjB,IAAI,IAAI,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK;YAC/B,UAAU,IAAI,CACZ,MACA,IAAI,CAAC,IAAI,EACT,IAAI,oMAAA,CAAA,UAAS,CAAC,MAAM;gBAClB,aAAa,IAAI,CAAC,WAAW;gBAC7B,QAAQ;gBACR;gBACA,WAAW,IAAI,CAAC,IAAI,CAAC,MAAM;gBAC3B,UAAU;YACZ,IACA;QAEJ;IACF;IAEA,SAAS,QAAQ,KAAK,EAAE,GAAG,IAAI;QAC7B,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,EAAE,YAAY;QACpC,IAAI,IAAI,QAAQ,IAAI,EAAE,MAAM,KAAK,CAAC,QAC9B,IAAI,IAAI,CAAC,MAAM,EACf,IAAI,KAAK,GAAG,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,KAAK,CAAC,IAAI,EAAE,eAC/F,IAAI,CAAA,GAAA,sPAAA,CAAA,UAAO,AAAD,EAAE;QAEhB,4DAA4D;QAC5D,mEAAmE;QACnE,IAAI,EAAE,KAAK,EAAE;YACX,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE;gBACpD,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG;YACrC;YACA,aAAa,EAAE,KAAK;QACtB,OAGK,IAAI,EAAE,CAAC,KAAK,GAAG;aAGf;YACH,EAAE,KAAK,GAAG;gBAAC;gBAAG,EAAE,MAAM,CAAC;aAAG;YAC1B,CAAA,GAAA,2RAAA,CAAA,YAAS,AAAD,EAAE,IAAI;YACd,EAAE,KAAK;QACT;QAEA,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE;QACR,EAAE,KAAK,GAAG,WAAW,YAAY;QACjC,EAAE,IAAI,CAAC,SAAS,UAAU,UAAU,MAAM,GAAG,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE;QAEpF,SAAS;YACP,EAAE,KAAK,GAAG;YACV,EAAE,GAAG;QACP;IACF;IAEA,SAAS,YAAY,KAAK,EAAE,GAAG,IAAI;QACjC,IAAI,eAAe,CAAC,OAAO,KAAK,CAAC,IAAI,EAAE,YAAY;QACnD,IAAI,gBAAgB,MAAM,aAAa,EACnC,IAAI,QAAQ,IAAI,EAAE,MAAM,MAAM,KAAK,CAAC,QACpC,IAAI,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE,MAAM,IAAI,EAAE,EAAE,CAAC,kBAAkB,YAAY,MAAM,EAAE,CAAC,gBAAgB,YAAY,OAC7F,IAAI,CAAA,GAAA,sPAAA,CAAA,UAAO,AAAD,EAAE,OAAO,gBACnB,KAAK,MAAM,OAAO,EAClB,KAAK,MAAM,OAAO;QAEtB,CAAA,GAAA,+OAAA,CAAA,cAAW,AAAD,EAAE,MAAM,IAAI;QACtB,CAAA,GAAA,sMAAA,CAAA,gBAAa,AAAD,EAAE;QACd,EAAE,KAAK,GAAG;YAAC;YAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;SAAG;QACpC,CAAA,GAAA,2RAAA,CAAA,YAAS,AAAD,EAAE,IAAI;QACd,EAAE,KAAK;QAEP,SAAS,WAAW,KAAK;YACvB,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE;YACR,IAAI,CAAC,EAAE,KAAK,EAAE;gBACZ,IAAI,KAAK,MAAM,OAAO,GAAG,IAAI,KAAK,MAAM,OAAO,GAAG;gBAClD,EAAE,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK;YAChC;YACA,EAAE,KAAK,CAAC,OACN,IAAI,CAAC,SAAS,UAAU,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,EAAE,GAAG,CAAA,GAAA,sPAAA,CAAA,UAAO,AAAD,EAAE,OAAO,gBAAgB,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE;QACxH;QAEA,SAAS,WAAW,KAAK;YACvB,EAAE,EAAE,CAAC,+BAA+B;YACpC,CAAA,GAAA,8OAAA,CAAA,aAAU,AAAD,EAAE,MAAM,IAAI,EAAE,EAAE,KAAK;YAC9B,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE;YACR,EAAE,KAAK,CAAC,OAAO,GAAG;QACpB;IACF;IAEA,SAAS,WAAW,KAAK,EAAE,GAAG,IAAI;QAChC,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,EAAE,YAAY;QACpC,IAAI,KAAK,IAAI,CAAC,MAAM,EAChB,KAAK,CAAA,GAAA,sPAAA,CAAA,UAAO,AAAD,EAAE,MAAM,cAAc,GAAG,MAAM,cAAc,CAAC,EAAE,GAAG,OAAO,IAAI,GACzE,KAAK,GAAG,MAAM,CAAC,KACf,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,QAAQ,GAAG,MAAM,CAAC,GACrC,KAAK,UAAU,UAAU,MAAM,IAAI,KAAK,IAAI,KAAK,OAAO,KAAK,CAAC,IAAI,EAAE,OAAO;QAE/E,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE;QACR,IAAI,WAAW,GAAG,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE,IAAI,EAAE,UAAU,GAAG,QAAQ,CAAC,UAAU,IAAI,CAAC,UAAU,IAAI,IAAI;aACjF,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,SAAS,EAAE,IAAI,IAAI;IACjD;IAEA,SAAS,aAAa,KAAK,EAAE,GAAG,IAAI;QAClC,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,EAAE,YAAY;QACpC,IAAI,UAAU,MAAM,OAAO,EACvB,IAAI,QAAQ,MAAM,EAClB,IAAI,QAAQ,IAAI,EAAE,MAAM,MAAM,cAAc,CAAC,MAAM,KAAK,GAAG,KAAK,CAAC,QACjE,SAAS,GAAG,GAAG;QAEnB,CAAA,GAAA,sMAAA,CAAA,gBAAa,AAAD,EAAE;QACd,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACtB,IAAI,OAAO,CAAC,EAAE,EAAE,IAAI,CAAA,GAAA,sPAAA,CAAA,UAAO,AAAD,EAAE,GAAG,IAAI;YACnC,IAAI;gBAAC;gBAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;gBAAI,EAAE,UAAU;aAAC;YAC5C,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,MAAM,GAAG,GAAG,UAAU,MAAM,EAAE,IAAI,GAAG,IAAI,CAAC,CAAC;iBACvD,IAAI,CAAC,EAAE,MAAM,IAAI,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE,MAAM,GAAG,GAAG,EAAE,IAAI,GAAG;QACrE;QAEA,IAAI,eAAe,gBAAgB,aAAa;QAEhD,IAAI,SAAS;YACX,IAAI,EAAE,IAAI,GAAG,GAAG,aAAa,CAAC,CAAC,EAAE,EAAE,gBAAgB,WAAW;gBAAa,gBAAgB;YAAM,GAAG;YACpG,CAAA,GAAA,2RAAA,CAAA,YAAS,AAAD,EAAE,IAAI;YACd,EAAE,KAAK;QACT;IACF;IAEA,SAAS,WAAW,KAAK,EAAE,GAAG,IAAI;QAChC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QACrB,IAAI,IAAI,QAAQ,IAAI,EAAE,MAAM,KAAK,CAAC,QAC9B,UAAU,MAAM,cAAc,EAC9B,IAAI,QAAQ,MAAM,EAAE,GAAG,GAAG,GAAG;QAEjC,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE;QACR,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACtB,IAAI,OAAO,CAAC,EAAE,EAAE,IAAI,CAAA,GAAA,sPAAA,CAAA,UAAO,AAAD,EAAE,GAAG,IAAI;YACnC,IAAI,EAAE,MAAM,IAAI,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,MAAM,CAAC,EAAE,GAAG;iBACvD,IAAI,EAAE,MAAM,IAAI,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,MAAM,CAAC,EAAE,GAAG;QACnE;QACA,IAAI,EAAE,IAAI,CAAC,MAAM;QACjB,IAAI,EAAE,MAAM,EAAE;YACZ,IAAI,KAAK,EAAE,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,EAClC,KAAK,EAAE,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,EAClC,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,IACxD,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI;YAC5D,IAAI,MAAM,GAAG,KAAK,IAAI,CAAC,KAAK;YAC5B,IAAI;gBAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI;gBAAG,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI;aAAE;YAC9C,IAAI;gBAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI;gBAAG,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI;aAAE;QAChD,OACK,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE;aAC9C;QAEL,EAAE,IAAI,CAAC,SAAS,UAAU,UAAU,GAAG,GAAG,IAAI,EAAE,MAAM,EAAE;IAC1D;IAEA,SAAS,WAAW,KAAK,EAAE,GAAG,IAAI;QAChC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QACrB,IAAI,IAAI,QAAQ,IAAI,EAAE,MAAM,KAAK,CAAC,QAC9B,UAAU,MAAM,cAAc,EAC9B,IAAI,QAAQ,MAAM,EAAE,GAAG;QAE3B,CAAA,GAAA,sMAAA,CAAA,gBAAa,AAAD,EAAE;QACd,IAAI,aAAa,aAAa;QAC9B,cAAc,WAAW;YAAa,cAAc;QAAM,GAAG;QAC7D,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACtB,IAAI,OAAO,CAAC,EAAE;YACd,IAAI,EAAE,MAAM,IAAI,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM;iBACxD,IAAI,EAAE,MAAM,IAAI,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM;QACpE;QACA,IAAI,EAAE,MAAM,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,MAAM,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;QAC/D,IAAI,EAAE,MAAM,EAAE,EAAE,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE;aACrD;YACH,EAAE,GAAG;YACL,yEAAyE;YACzE,IAAI,EAAE,IAAI,KAAK,GAAG;gBAChB,IAAI,CAAA,GAAA,sPAAA,CAAA,UAAO,AAAD,EAAE,GAAG,IAAI;gBACnB,IAAI,KAAK,KAAK,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,aAAa;oBACxE,IAAI,IAAI,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE,IAAI,EAAE,EAAE,CAAC;oBACxB,IAAI,GAAG,EAAE,KAAK,CAAC,IAAI,EAAE;gBACvB;YACF;QACF;IACF;IAEA,KAAK,UAAU,GAAG,SAAS,CAAC;QAC1B,OAAO,UAAU,MAAM,GAAG,CAAC,aAAa,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,uMAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,IAAI,IAAI;IAC9F;IAEA,KAAK,MAAM,GAAG,SAAS,CAAC;QACtB,OAAO,UAAU,MAAM,GAAG,CAAC,SAAS,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,uMAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,CAAC,IAAI,IAAI,IAAI;IAC3F;IAEA,KAAK,SAAS,GAAG,SAAS,CAAC;QACzB,OAAO,UAAU,MAAM,GAAG,CAAC,YAAY,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,uMAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,CAAC,IAAI,IAAI,IAAI;IAC9F;IAEA,KAAK,MAAM,GAAG,SAAS,CAAC;QACtB,OAAO,UAAU,MAAM,GAAG,CAAC,SAAS,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,uMAAA,CAAA,UAAQ,AAAD,EAAE;YAAC;gBAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;gBAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;aAAC;YAAE;gBAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;gBAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;aAAC;SAAC,GAAG,IAAI,IAAI;IACpI;IAEA,KAAK,WAAW,GAAG,SAAS,CAAC;QAC3B,OAAO,UAAU,MAAM,GAAG,CAAC,WAAW,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,IAAI;YAAC,WAAW,CAAC,EAAE;YAAE,WAAW,CAAC,EAAE;SAAC;IACrH;IAEA,KAAK,eAAe,GAAG,SAAS,CAAC;QAC/B,OAAO,UAAU,MAAM,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,eAAe,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,eAAe,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,eAAe,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,IAAI;YAAC;gBAAC,eAAe,CAAC,EAAE,CAAC,EAAE;gBAAE,eAAe,CAAC,EAAE,CAAC,EAAE;aAAC;YAAE;gBAAC,eAAe,CAAC,EAAE,CAAC,EAAE;gBAAE,eAAe,CAAC,EAAE,CAAC,EAAE;aAAC;SAAC;IAC7Q;IAEA,KAAK,SAAS,GAAG,SAAS,CAAC;QACzB,OAAO,UAAU,MAAM,GAAG,CAAC,YAAY,GAAG,IAAI,IAAI;IACpD;IAEA,KAAK,QAAQ,GAAG,SAAS,CAAC;QACxB,OAAO,UAAU,MAAM,GAAG,CAAC,WAAW,CAAC,GAAG,IAAI,IAAI;IACpD;IAEA,KAAK,WAAW,GAAG,SAAS,CAAC;QAC3B,OAAO,UAAU,MAAM,GAAG,CAAC,cAAc,GAAG,IAAI,IAAI;IACtD;IAEA,KAAK,EAAE,GAAG;QACR,IAAI,QAAQ,UAAU,EAAE,CAAC,KAAK,CAAC,WAAW;QAC1C,OAAO,UAAU,YAAY,OAAO;IACtC;IAEA,KAAK,aAAa,GAAG,SAAS,CAAC;QAC7B,OAAO,UAAU,MAAM,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI,CAAC;IAC9E;IAEA,KAAK,WAAW,GAAG,SAAS,CAAC;QAC3B,OAAO,UAAU,MAAM,GAAG,CAAC,cAAc,CAAC,GAAG,IAAI,IAAI;IACvD;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5053, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5059, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 5062, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5078, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 5109, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5136, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-force%403.0.0/node_modules/d3-force/src/lcg.js"], "sourcesContent": ["// https://en.wikipedia.org/wiki/Linear_congruential_generator#Parameters_in_common_use\nconst a = 1664525;\nconst c = 1013904223;\nconst m = 4294967296; // 2^32\n\nexport default function() {\n  let s = 1;\n  return () => (s = (a * s + c) % m) / m;\n}\n"], "names": [], "mappings": "AAAA,uFAAuF;;;;AACvF,MAAM,IAAI;AACV,MAAM,IAAI;AACV,MAAM,IAAI,YAAY,OAAO;AAEd;IACb,IAAI,IAAI;IACR,OAAO,IAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5147, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5153, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-force%403.0.0/node_modules/d3-force/src/simulation.js"], "sourcesContent": ["import {dispatch} from \"d3-dispatch\";\nimport {timer} from \"d3-timer\";\nimport lcg from \"./lcg.js\";\n\nexport function x(d) {\n  return d.x;\n}\n\nexport function y(d) {\n  return d.y;\n}\n\nvar initialRadius = 10,\n    initialAngle = Math.PI * (3 - Math.sqrt(5));\n\nexport default function(nodes) {\n  var simulation,\n      alpha = 1,\n      alphaMin = 0.001,\n      alphaDecay = 1 - Math.pow(alphaMin, 1 / 300),\n      alphaTarget = 0,\n      velocityDecay = 0.6,\n      forces = new Map(),\n      stepper = timer(step),\n      event = dispatch(\"tick\", \"end\"),\n      random = lcg();\n\n  if (nodes == null) nodes = [];\n\n  function step() {\n    tick();\n    event.call(\"tick\", simulation);\n    if (alpha < alphaMin) {\n      stepper.stop();\n      event.call(\"end\", simulation);\n    }\n  }\n\n  function tick(iterations) {\n    var i, n = nodes.length, node;\n\n    if (iterations === undefined) iterations = 1;\n\n    for (var k = 0; k < iterations; ++k) {\n      alpha += (alphaTarget - alpha) * alphaDecay;\n\n      forces.forEach(function(force) {\n        force(alpha);\n      });\n\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        if (node.fx == null) node.x += node.vx *= velocityDecay;\n        else node.x = node.fx, node.vx = 0;\n        if (node.fy == null) node.y += node.vy *= velocityDecay;\n        else node.y = node.fy, node.vy = 0;\n      }\n    }\n\n    return simulation;\n  }\n\n  function initializeNodes() {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.index = i;\n      if (node.fx != null) node.x = node.fx;\n      if (node.fy != null) node.y = node.fy;\n      if (isNaN(node.x) || isNaN(node.y)) {\n        var radius = initialRadius * Math.sqrt(0.5 + i), angle = i * initialAngle;\n        node.x = radius * Math.cos(angle);\n        node.y = radius * Math.sin(angle);\n      }\n      if (isNaN(node.vx) || isNaN(node.vy)) {\n        node.vx = node.vy = 0;\n      }\n    }\n  }\n\n  function initializeForce(force) {\n    if (force.initialize) force.initialize(nodes, random);\n    return force;\n  }\n\n  initializeNodes();\n\n  return simulation = {\n    tick: tick,\n\n    restart: function() {\n      return stepper.restart(step), simulation;\n    },\n\n    stop: function() {\n      return stepper.stop(), simulation;\n    },\n\n    nodes: function(_) {\n      return arguments.length ? (nodes = _, initializeNodes(), forces.forEach(initializeForce), simulation) : nodes;\n    },\n\n    alpha: function(_) {\n      return arguments.length ? (alpha = +_, simulation) : alpha;\n    },\n\n    alphaMin: function(_) {\n      return arguments.length ? (alphaMin = +_, simulation) : alphaMin;\n    },\n\n    alphaDecay: function(_) {\n      return arguments.length ? (alphaDecay = +_, simulation) : +alphaDecay;\n    },\n\n    alphaTarget: function(_) {\n      return arguments.length ? (alphaTarget = +_, simulation) : alphaTarget;\n    },\n\n    velocityDecay: function(_) {\n      return arguments.length ? (velocityDecay = 1 - _, simulation) : 1 - velocityDecay;\n    },\n\n    randomSource: function(_) {\n      return arguments.length ? (random = _, forces.forEach(initializeForce), simulation) : random;\n    },\n\n    force: function(name, _) {\n      return arguments.length > 1 ? ((_ == null ? forces.delete(name) : forces.set(name, initializeForce(_))), simulation) : forces.get(name);\n    },\n\n    find: function(x, y, radius) {\n      var i = 0,\n          n = nodes.length,\n          dx,\n          dy,\n          d2,\n          node,\n          closest;\n\n      if (radius == null) radius = Infinity;\n      else radius *= radius;\n\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        dx = x - node.x;\n        dy = y - node.y;\n        d2 = dx * dx + dy * dy;\n        if (d2 < radius) closest = node, radius = d2;\n      }\n\n      return closest;\n    },\n\n    on: function(name, _) {\n      return arguments.length > 1 ? (event.on(name, _), simulation) : event.on(name);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEO,SAAS,EAAE,CAAC;IACjB,OAAO,EAAE,CAAC;AACZ;AAEO,SAAS,EAAE,CAAC;IACjB,OAAO,EAAE,CAAC;AACZ;AAEA,IAAI,gBAAgB,IAChB,eAAe,KAAK,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE;AAE/B,wCAAS,KAAK;IAC3B,IAAI,YACA,QAAQ,GACR,WAAW,OACX,aAAa,IAAI,KAAK,GAAG,CAAC,UAAU,IAAI,MACxC,cAAc,GACd,gBAAgB,KAChB,SAAS,IAAI,OACb,UAAU,CAAA,GAAA,sMAAA,CAAA,QAAK,AAAD,EAAE,OAChB,QAAQ,CAAA,GAAA,sPAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,QACzB,SAAS,CAAA,GAAA,oMAAA,CAAA,UAAG,AAAD;IAEf,IAAI,SAAS,MAAM,QAAQ,EAAE;IAE7B,SAAS;QACP;QACA,MAAM,IAAI,CAAC,QAAQ;QACnB,IAAI,QAAQ,UAAU;YACpB,QAAQ,IAAI;YACZ,MAAM,IAAI,CAAC,OAAO;QACpB;IACF;IAEA,SAAS,KAAK,UAAU;QACtB,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE;QAEzB,IAAI,eAAe,WAAW,aAAa;QAE3C,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,EAAE,EAAG;YACnC,SAAS,CAAC,cAAc,KAAK,IAAI;YAEjC,OAAO,OAAO,CAAC,SAAS,KAAK;gBAC3B,MAAM;YACR;YAEA,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;gBACtB,OAAO,KAAK,CAAC,EAAE;gBACf,IAAI,KAAK,EAAE,IAAI,MAAM,KAAK,CAAC,IAAI,KAAK,EAAE,IAAI;qBACrC,KAAK,CAAC,GAAG,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG;gBACjC,IAAI,KAAK,EAAE,IAAI,MAAM,KAAK,CAAC,IAAI,KAAK,EAAE,IAAI;qBACrC,KAAK,CAAC,GAAG,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG;YACnC;QACF;QAEA,OAAO;IACT;IAEA,SAAS;QACP,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,MAAM,IAAI,GAAG,EAAE,EAAG;YAClD,OAAO,KAAK,CAAC,EAAE,EAAE,KAAK,KAAK,GAAG;YAC9B,IAAI,KAAK,EAAE,IAAI,MAAM,KAAK,CAAC,GAAG,KAAK,EAAE;YACrC,IAAI,KAAK,EAAE,IAAI,MAAM,KAAK,CAAC,GAAG,KAAK,EAAE;YACrC,IAAI,MAAM,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,GAAG;gBAClC,IAAI,SAAS,gBAAgB,KAAK,IAAI,CAAC,MAAM,IAAI,QAAQ,IAAI;gBAC7D,KAAK,CAAC,GAAG,SAAS,KAAK,GAAG,CAAC;gBAC3B,KAAK,CAAC,GAAG,SAAS,KAAK,GAAG,CAAC;YAC7B;YACA,IAAI,MAAM,KAAK,EAAE,KAAK,MAAM,KAAK,EAAE,GAAG;gBACpC,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG;YACtB;QACF;IACF;IAEA,SAAS,gBAAgB,KAAK;QAC5B,IAAI,MAAM,UAAU,EAAE,MAAM,UAAU,CAAC,OAAO;QAC9C,OAAO;IACT;IAEA;IAEA,OAAO,aAAa;QAClB,MAAM;QAEN,SAAS;YACP,OAAO,QAAQ,OAAO,CAAC,OAAO;QAChC;QAEA,MAAM;YACJ,OAAO,QAAQ,IAAI,IAAI;QACzB;QAEA,OAAO,SAAS,CAAC;YACf,OAAO,UAAU,MAAM,GAAG,CAAC,QAAQ,GAAG,mBAAmB,OAAO,OAAO,CAAC,kBAAkB,UAAU,IAAI;QAC1G;QAEA,OAAO,SAAS,CAAC;YACf,OAAO,UAAU,MAAM,GAAG,CAAC,QAAQ,CAAC,GAAG,UAAU,IAAI;QACvD;QAEA,UAAU,SAAS,CAAC;YAClB,OAAO,UAAU,MAAM,GAAG,CAAC,WAAW,CAAC,GAAG,UAAU,IAAI;QAC1D;QAEA,YAAY,SAAS,CAAC;YACpB,OAAO,UAAU,MAAM,GAAG,CAAC,aAAa,CAAC,GAAG,UAAU,IAAI,CAAC;QAC7D;QAEA,aAAa,SAAS,CAAC;YACrB,OAAO,UAAU,MAAM,GAAG,CAAC,cAAc,CAAC,GAAG,UAAU,IAAI;QAC7D;QAEA,eAAe,SAAS,CAAC;YACvB,OAAO,UAAU,MAAM,GAAG,CAAC,gBAAgB,IAAI,GAAG,UAAU,IAAI,IAAI;QACtE;QAEA,cAAc,SAAS,CAAC;YACtB,OAAO,UAAU,MAAM,GAAG,CAAC,SAAS,GAAG,OAAO,OAAO,CAAC,kBAAkB,UAAU,IAAI;QACxF;QAEA,OAAO,SAAS,IAAI,EAAE,CAAC;YACrB,OAAO,UAAU,MAAM,GAAG,IAAI,CAAC,AAAC,KAAK,OAAO,OAAO,MAAM,CAAC,QAAQ,OAAO,GAAG,CAAC,MAAM,gBAAgB,KAAM,UAAU,IAAI,OAAO,GAAG,CAAC;QACpI;QAEA,MAAM,SAAS,CAAC,EAAE,CAAC,EAAE,MAAM;YACzB,IAAI,IAAI,GACJ,IAAI,MAAM,MAAM,EAChB,IACA,IACA,IACA,MACA;YAEJ,IAAI,UAAU,MAAM,SAAS;iBACxB,UAAU;YAEf,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;gBACtB,OAAO,KAAK,CAAC,EAAE;gBACf,KAAK,IAAI,KAAK,CAAC;gBACf,KAAK,IAAI,KAAK,CAAC;gBACf,KAAK,KAAK,KAAK,KAAK;gBACpB,IAAI,KAAK,QAAQ,UAAU,MAAM,SAAS;YAC5C;YAEA,OAAO;QACT;QAEA,IAAI,SAAS,IAAI,EAAE,CAAC;YAClB,OAAO,UAAU,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,IAAI,UAAU,IAAI,MAAM,EAAE,CAAC;QAC3E;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5270, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5286, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-force%403.0.0/node_modules/d3-force/src/constant.js"], "sourcesContent": ["export default function(x) {\n  return function() {\n    return x;\n  };\n}\n"], "names": [], "mappings": ";;;AAAe,wCAAS,CAAC;IACvB,OAAO;QACL,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5294, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5300, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-force%403.0.0/node_modules/d3-force/src/jiggle.js"], "sourcesContent": ["export default function(random) {\n  return (random() - 0.5) * 1e-6;\n}\n"], "names": [], "mappings": ";;;AAAe,wCAAS,MAAM;IAC5B,OAAO,CAAC,WAAW,GAAG,IAAI;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5306, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5312, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-force%403.0.0/node_modules/d3-force/src/link.js"], "sourcesContent": ["import constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\n\nfunction index(d) {\n  return d.index;\n}\n\nfunction find(nodeById, nodeId) {\n  var node = nodeById.get(nodeId);\n  if (!node) throw new Error(\"node not found: \" + nodeId);\n  return node;\n}\n\nexport default function(links) {\n  var id = index,\n      strength = defaultStrength,\n      strengths,\n      distance = constant(30),\n      distances,\n      nodes,\n      count,\n      bias,\n      random,\n      iterations = 1;\n\n  if (links == null) links = [];\n\n  function defaultStrength(link) {\n    return 1 / Math.min(count[link.source.index], count[link.target.index]);\n  }\n\n  function force(alpha) {\n    for (var k = 0, n = links.length; k < iterations; ++k) {\n      for (var i = 0, link, source, target, x, y, l, b; i < n; ++i) {\n        link = links[i], source = link.source, target = link.target;\n        x = target.x + target.vx - source.x - source.vx || jiggle(random);\n        y = target.y + target.vy - source.y - source.vy || jiggle(random);\n        l = Math.sqrt(x * x + y * y);\n        l = (l - distances[i]) / l * alpha * strengths[i];\n        x *= l, y *= l;\n        target.vx -= x * (b = bias[i]);\n        target.vy -= y * b;\n        source.vx += x * (b = 1 - b);\n        source.vy += y * b;\n      }\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n\n    var i,\n        n = nodes.length,\n        m = links.length,\n        nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d])),\n        link;\n\n    for (i = 0, count = new Array(n); i < m; ++i) {\n      link = links[i], link.index = i;\n      if (typeof link.source !== \"object\") link.source = find(nodeById, link.source);\n      if (typeof link.target !== \"object\") link.target = find(nodeById, link.target);\n      count[link.source.index] = (count[link.source.index] || 0) + 1;\n      count[link.target.index] = (count[link.target.index] || 0) + 1;\n    }\n\n    for (i = 0, bias = new Array(m); i < m; ++i) {\n      link = links[i], bias[i] = count[link.source.index] / (count[link.source.index] + count[link.target.index]);\n    }\n\n    strengths = new Array(m), initializeStrength();\n    distances = new Array(m), initializeDistance();\n  }\n\n  function initializeStrength() {\n    if (!nodes) return;\n\n    for (var i = 0, n = links.length; i < n; ++i) {\n      strengths[i] = +strength(links[i], i, links);\n    }\n  }\n\n  function initializeDistance() {\n    if (!nodes) return;\n\n    for (var i = 0, n = links.length; i < n; ++i) {\n      distances[i] = +distance(links[i], i, links);\n    }\n  }\n\n  force.initialize = function(_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n\n  force.links = function(_) {\n    return arguments.length ? (links = _, initialize(), force) : links;\n  };\n\n  force.id = function(_) {\n    return arguments.length ? (id = _, force) : id;\n  };\n\n  force.iterations = function(_) {\n    return arguments.length ? (iterations = +_, force) : iterations;\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initializeStrength(), force) : strength;\n  };\n\n  force.distance = function(_) {\n    return arguments.length ? (distance = typeof _ === \"function\" ? _ : constant(+_), initializeDistance(), force) : distance;\n  };\n\n  return force;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,MAAM,CAAC;IACd,OAAO,EAAE,KAAK;AAChB;AAEA,SAAS,KAAK,QAAQ,EAAE,MAAM;IAC5B,IAAI,OAAO,SAAS,GAAG,CAAC;IACxB,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM,qBAAqB;IAChD,OAAO;AACT;AAEe,wCAAS,KAAK;IAC3B,IAAI,KAAK,OACL,WAAW,iBACX,WACA,WAAW,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,KACpB,WACA,OACA,OACA,MACA,QACA,aAAa;IAEjB,IAAI,SAAS,MAAM,QAAQ,EAAE;IAE7B,SAAS,gBAAgB,IAAI;QAC3B,OAAO,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC;IACxE;IAEA,SAAS,MAAM,KAAK;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,YAAY,EAAE,EAAG;YACrD,IAAK,IAAI,IAAI,GAAG,MAAM,QAAQ,QAAQ,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,EAAE,EAAG;gBAC5D,OAAO,KAAK,CAAC,EAAE,EAAE,SAAS,KAAK,MAAM,EAAE,SAAS,KAAK,MAAM;gBAC3D,IAAI,OAAO,CAAC,GAAG,OAAO,EAAE,GAAG,OAAO,CAAC,GAAG,OAAO,EAAE,IAAI,CAAA,GAAA,uMAAA,CAAA,UAAM,AAAD,EAAE;gBAC1D,IAAI,OAAO,CAAC,GAAG,OAAO,EAAE,GAAG,OAAO,CAAC,GAAG,OAAO,EAAE,IAAI,CAAA,GAAA,uMAAA,CAAA,UAAM,AAAD,EAAE;gBAC1D,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI;gBAC1B,IAAI,CAAC,IAAI,SAAS,CAAC,EAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE;gBACjD,KAAK,GAAG,KAAK;gBACb,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE;gBAC7B,OAAO,EAAE,IAAI,IAAI;gBACjB,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;gBAC3B,OAAO,EAAE,IAAI,IAAI;YACnB;QACF;IACF;IAEA,SAAS;QACP,IAAI,CAAC,OAAO;QAEZ,IAAI,GACA,IAAI,MAAM,MAAM,EAChB,IAAI,MAAM,MAAM,EAChB,WAAW,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,IAAM;gBAAC,GAAG,GAAG,GAAG;gBAAQ;aAAE,IAC3D;QAEJ,IAAK,IAAI,GAAG,QAAQ,IAAI,MAAM,IAAI,IAAI,GAAG,EAAE,EAAG;YAC5C,OAAO,KAAK,CAAC,EAAE,EAAE,KAAK,KAAK,GAAG;YAC9B,IAAI,OAAO,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,GAAG,KAAK,UAAU,KAAK,MAAM;YAC7E,IAAI,OAAO,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,GAAG,KAAK,UAAU,KAAK,MAAM;YAC7E,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI;YAC7D,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI;QAC/D;QAEA,IAAK,IAAI,GAAG,OAAO,IAAI,MAAM,IAAI,IAAI,GAAG,EAAE,EAAG;YAC3C,OAAO,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC;QAC5G;QAEA,YAAY,IAAI,MAAM,IAAI;QAC1B,YAAY,IAAI,MAAM,IAAI;IAC5B;IAEA,SAAS;QACP,IAAI,CAAC,OAAO;QAEZ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG;YAC5C,SAAS,CAAC,EAAE,GAAG,CAAC,SAAS,KAAK,CAAC,EAAE,EAAE,GAAG;QACxC;IACF;IAEA,SAAS;QACP,IAAI,CAAC,OAAO;QAEZ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG;YAC5C,SAAS,CAAC,EAAE,GAAG,CAAC,SAAS,KAAK,CAAC,EAAE,EAAE,GAAG;QACxC;IACF;IAEA,MAAM,UAAU,GAAG,SAAS,MAAM,EAAE,OAAO;QACzC,QAAQ;QACR,SAAS;QACT;IACF;IAEA,MAAM,KAAK,GAAG,SAAS,CAAC;QACtB,OAAO,UAAU,MAAM,GAAG,CAAC,QAAQ,GAAG,cAAc,KAAK,IAAI;IAC/D;IAEA,MAAM,EAAE,GAAG,SAAS,CAAC;QACnB,OAAO,UAAU,MAAM,GAAG,CAAC,KAAK,GAAG,KAAK,IAAI;IAC9C;IAEA,MAAM,UAAU,GAAG,SAAS,CAAC;QAC3B,OAAO,UAAU,MAAM,GAAG,CAAC,aAAa,CAAC,GAAG,KAAK,IAAI;IACvD;IAEA,MAAM,QAAQ,GAAG,SAAS,CAAC;QACzB,OAAO,UAAU,MAAM,GAAG,CAAC,WAAW,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,sBAAsB,KAAK,IAAI;IACnH;IAEA,MAAM,QAAQ,GAAG,SAAS,CAAC;QACzB,OAAO,UAAU,MAAM,GAAG,CAAC,WAAW,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,sBAAsB,KAAK,IAAI;IACnH;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5402, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5418, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-quadtree%403.0.1/node_modules/d3-quadtree/src/add.js"], "sourcesContent": ["export default function(d) {\n  const x = +this._x.call(null, d),\n      y = +this._y.call(null, d);\n  return add(this.cover(x, y), x, y, d);\n}\n\nfunction add(tree, x, y, d) {\n  if (isNaN(x) || isNaN(y)) return tree; // ignore invalid points\n\n  var parent,\n      node = tree._root,\n      leaf = {data: d},\n      x0 = tree._x0,\n      y0 = tree._y0,\n      x1 = tree._x1,\n      y1 = tree._y1,\n      xm,\n      ym,\n      xp,\n      yp,\n      right,\n      bottom,\n      i,\n      j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return tree._root = leaf, tree;\n\n  // Find the existing leaf for the new point, or add it.\n  while (node.length) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n    if (parent = node, !(node = node[i = bottom << 1 | right])) return parent[i] = leaf, tree;\n  }\n\n  // Is the new point is exactly coincident with the existing point?\n  xp = +tree._x.call(null, node.data);\n  yp = +tree._y.call(null, node.data);\n  if (x === xp && y === yp) return leaf.next = node, parent ? parent[i] = leaf : tree._root = leaf, tree;\n\n  // Otherwise, split the leaf node until the old and new point are separated.\n  do {\n    parent = parent ? parent[i] = new Array(4) : tree._root = new Array(4);\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n  } while ((i = bottom << 1 | right) === (j = (yp >= ym) << 1 | (xp >= xm)));\n  return parent[j] = node, parent[i] = leaf, tree;\n}\n\nexport function addAll(data) {\n  var d, i, n = data.length,\n      x,\n      y,\n      xz = new Array(n),\n      yz = new Array(n),\n      x0 = Infinity,\n      y0 = Infinity,\n      x1 = -Infinity,\n      y1 = -Infinity;\n\n  // Compute the points and their extent.\n  for (i = 0; i < n; ++i) {\n    if (isNaN(x = +this._x.call(null, d = data[i])) || isNaN(y = +this._y.call(null, d))) continue;\n    xz[i] = x;\n    yz[i] = y;\n    if (x < x0) x0 = x;\n    if (x > x1) x1 = x;\n    if (y < y0) y0 = y;\n    if (y > y1) y1 = y;\n  }\n\n  // If there were no (valid) points, abort.\n  if (x0 > x1 || y0 > y1) return this;\n\n  // Expand the tree to cover the new points.\n  this.cover(x0, y0).cover(x1, y1);\n\n  // Add the new points.\n  for (i = 0; i < n; ++i) {\n    add(this, xz[i], yz[i], data[i]);\n  }\n\n  return this;\n}\n"], "names": [], "mappings": ";;;;AAAe,wCAAS,CAAC;IACvB,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,IAC1B,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM;IAC5B,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG;AACrC;AAEA,SAAS,IAAI,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IACxB,IAAI,MAAM,MAAM,MAAM,IAAI,OAAO,MAAM,wBAAwB;IAE/D,IAAI,QACA,OAAO,KAAK,KAAK,EACjB,OAAO;QAAC,MAAM;IAAC,GACf,KAAK,KAAK,GAAG,EACb,KAAK,KAAK,GAAG,EACb,KAAK,KAAK,GAAG,EACb,KAAK,KAAK,GAAG,EACb,IACA,IACA,IACA,IACA,OACA,QACA,GACA;IAEJ,uDAAuD;IACvD,IAAI,CAAC,MAAM,OAAO,KAAK,KAAK,GAAG,MAAM;IAErC,uDAAuD;IACvD,MAAO,KAAK,MAAM,CAAE;QAClB,IAAI,QAAQ,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK;aAAS,KAAK;QAC1D,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK;aAAS,KAAK;QAC3D,IAAI,SAAS,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC,IAAI,UAAU,IAAI,MAAM,GAAG,OAAO,MAAM,CAAC,EAAE,GAAG,MAAM;IACvF;IAEA,kEAAkE;IAClE,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI;IAClC,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI;IAClC,IAAI,MAAM,MAAM,MAAM,IAAI,OAAO,KAAK,IAAI,GAAG,MAAM,SAAS,MAAM,CAAC,EAAE,GAAG,OAAO,KAAK,KAAK,GAAG,MAAM;IAElG,4EAA4E;IAC5E,GAAG;QACD,SAAS,SAAS,MAAM,CAAC,EAAE,GAAG,IAAI,MAAM,KAAK,KAAK,KAAK,GAAG,IAAI,MAAM;QACpE,IAAI,QAAQ,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK;aAAS,KAAK;QAC1D,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK;aAAS,KAAK;IAC7D,QAAS,CAAC,IAAI,UAAU,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,IAAK,MAAM,EAAG,EAAG;IAC3E,OAAO,MAAM,CAAC,EAAE,GAAG,MAAM,MAAM,CAAC,EAAE,GAAG,MAAM;AAC7C;AAEO,SAAS,OAAO,IAAI;IACzB,IAAI,GAAG,GAAG,IAAI,KAAK,MAAM,EACrB,GACA,GACA,KAAK,IAAI,MAAM,IACf,KAAK,IAAI,MAAM,IACf,KAAK,UACL,KAAK,UACL,KAAK,CAAC,UACN,KAAK,CAAC;IAEV,uCAAuC;IACvC,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QACtB,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,EAAE,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK;QACtF,EAAE,CAAC,EAAE,GAAG;QACR,EAAE,CAAC,EAAE,GAAG;QACR,IAAI,IAAI,IAAI,KAAK;QACjB,IAAI,IAAI,IAAI,KAAK;QACjB,IAAI,IAAI,IAAI,KAAK;QACjB,IAAI,IAAI,IAAI,KAAK;IACnB;IAEA,0CAA0C;IAC1C,IAAI,KAAK,MAAM,KAAK,IAAI,OAAO,IAAI;IAEnC,2CAA2C;IAC3C,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI;IAE7B,sBAAsB;IACtB,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QACtB,IAAI,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;IACjC;IAEA,OAAO,IAAI;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5477, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5483, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-quadtree%403.0.1/node_modules/d3-quadtree/src/cover.js"], "sourcesContent": ["export default function(x, y) {\n  if (isNaN(x = +x) || isNaN(y = +y)) return this; // ignore invalid points\n\n  var x0 = this._x0,\n      y0 = this._y0,\n      x1 = this._x1,\n      y1 = this._y1;\n\n  // If the quadtree has no extent, initialize them.\n  // Integer extent are necessary so that if we later double the extent,\n  // the existing quadrant boundaries don’t change due to floating point error!\n  if (isNaN(x0)) {\n    x1 = (x0 = Math.floor(x)) + 1;\n    y1 = (y0 = Math.floor(y)) + 1;\n  }\n\n  // Otherwise, double repeatedly to cover.\n  else {\n    var z = x1 - x0 || 1,\n        node = this._root,\n        parent,\n        i;\n\n    while (x0 > x || x >= x1 || y0 > y || y >= y1) {\n      i = (y < y0) << 1 | (x < x0);\n      parent = new Array(4), parent[i] = node, node = parent, z *= 2;\n      switch (i) {\n        case 0: x1 = x0 + z, y1 = y0 + z; break;\n        case 1: x0 = x1 - z, y1 = y0 + z; break;\n        case 2: x1 = x0 + z, y0 = y1 - z; break;\n        case 3: x0 = x1 - z, y0 = y1 - z; break;\n      }\n    }\n\n    if (this._root && this._root.length) this._root = node;\n  }\n\n  this._x0 = x0;\n  this._y0 = y0;\n  this._x1 = x1;\n  this._y1 = y1;\n  return this;\n}\n"], "names": [], "mappings": ";;;AAAe,wCAAS,CAAC,EAAE,CAAC;IAC1B,IAAI,MAAM,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI,OAAO,IAAI,EAAE,wBAAwB;IAEzE,IAAI,KAAK,IAAI,CAAC,GAAG,EACb,KAAK,IAAI,CAAC,GAAG,EACb,KAAK,IAAI,CAAC,GAAG,EACb,KAAK,IAAI,CAAC,GAAG;IAEjB,kDAAkD;IAClD,sEAAsE;IACtE,6EAA6E;IAC7E,IAAI,MAAM,KAAK;QACb,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,EAAE,IAAI;QAC5B,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,EAAE,IAAI;IAC9B,OAGK;QACH,IAAI,IAAI,KAAK,MAAM,GACf,OAAO,IAAI,CAAC,KAAK,EACjB,QACA;QAEJ,MAAO,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,GAAI;YAC7C,IAAI,CAAC,IAAI,EAAE,KAAK,IAAK,IAAI;YACzB,SAAS,IAAI,MAAM,IAAI,MAAM,CAAC,EAAE,GAAG,MAAM,OAAO,QAAQ,KAAK;YAC7D,OAAQ;gBACN,KAAK;oBAAG,KAAK,KAAK,GAAG,KAAK,KAAK;oBAAG;gBAClC,KAAK;oBAAG,KAAK,KAAK,GAAG,KAAK,KAAK;oBAAG;gBAClC,KAAK;oBAAG,KAAK,KAAK,GAAG,KAAK,KAAK;oBAAG;gBAClC,KAAK;oBAAG,KAAK,KAAK,GAAG,KAAK,KAAK;oBAAG;YACpC;QACF;QAEA,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,GAAG;IACpD;IAEA,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,GAAG,GAAG;IACX,OAAO,IAAI;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5523, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5529, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-quadtree%403.0.1/node_modules/d3-quadtree/src/data.js"], "sourcesContent": ["export default function() {\n  var data = [];\n  this.visit(function(node) {\n    if (!node.length) do data.push(node.data); while (node = node.next)\n  });\n  return data;\n}\n"], "names": [], "mappings": ";;;AAAe;IACb,IAAI,OAAO,EAAE;IACb,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI;QACtB,IAAI,CAAC,KAAK,MAAM,EAAE,GAAG,KAAK,IAAI,CAAC,KAAK,IAAI;eAAU,OAAO,KAAK,IAAI,CAAC;IACrE;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5540, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5546, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-quadtree%403.0.1/node_modules/d3-quadtree/src/extent.js"], "sourcesContent": ["export default function(_) {\n  return arguments.length\n      ? this.cover(+_[0][0], +_[0][1]).cover(+_[1][0], +_[1][1])\n      : isNaN(this._x0) ? undefined : [[this._x0, this._y0], [this._x1, this._y1]];\n}\n"], "names": [], "mappings": ";;;AAAe,wCAAS,CAAC;IACvB,OAAO,UAAU,MAAM,GACjB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IACvD,MAAM,IAAI,CAAC,GAAG,IAAI,YAAY;QAAC;YAAC,IAAI,CAAC,GAAG;YAAE,IAAI,CAAC,GAAG;SAAC;QAAE;YAAC,IAAI,CAAC,GAAG;YAAE,IAAI,CAAC,GAAG;SAAC;KAAC;AAClF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5561, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5567, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-quadtree%403.0.1/node_modules/d3-quadtree/src/quad.js"], "sourcesContent": ["export default function(node, x0, y0, x1, y1) {\n  this.node = node;\n  this.x0 = x0;\n  this.y0 = y0;\n  this.x1 = x1;\n  this.y1 = y1;\n}\n"], "names": [], "mappings": ";;;AAAe,wCAAS,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IAC1C,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,EAAE,GAAG;IACV,IAAI,CAAC,EAAE,GAAG;IACV,IAAI,CAAC,EAAE,GAAG;IACV,IAAI,CAAC,EAAE,GAAG;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5577, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5583, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-quadtree%403.0.1/node_modules/d3-quadtree/src/find.js"], "sourcesContent": ["import Quad from \"./quad.js\";\n\nexport default function(x, y, radius) {\n  var data,\n      x0 = this._x0,\n      y0 = this._y0,\n      x1,\n      y1,\n      x2,\n      y2,\n      x3 = this._x1,\n      y3 = this._y1,\n      quads = [],\n      node = this._root,\n      q,\n      i;\n\n  if (node) quads.push(new Quad(node, x0, y0, x3, y3));\n  if (radius == null) radius = Infinity;\n  else {\n    x0 = x - radius, y0 = y - radius;\n    x3 = x + radius, y3 = y + radius;\n    radius *= radius;\n  }\n\n  while (q = quads.pop()) {\n\n    // Stop searching if this quadrant can’t contain a closer node.\n    if (!(node = q.node)\n        || (x1 = q.x0) > x3\n        || (y1 = q.y0) > y3\n        || (x2 = q.x1) < x0\n        || (y2 = q.y1) < y0) continue;\n\n    // Bisect the current quadrant.\n    if (node.length) {\n      var xm = (x1 + x2) / 2,\n          ym = (y1 + y2) / 2;\n\n      quads.push(\n        new Quad(node[3], xm, ym, x2, y2),\n        new Quad(node[2], x1, ym, xm, y2),\n        new Quad(node[1], xm, y1, x2, ym),\n        new Quad(node[0], x1, y1, xm, ym)\n      );\n\n      // Visit the closest quadrant first.\n      if (i = (y >= ym) << 1 | (x >= xm)) {\n        q = quads[quads.length - 1];\n        quads[quads.length - 1] = quads[quads.length - 1 - i];\n        quads[quads.length - 1 - i] = q;\n      }\n    }\n\n    // Visit this point. (Visiting coincident points isn’t necessary!)\n    else {\n      var dx = x - +this._x.call(null, node.data),\n          dy = y - +this._y.call(null, node.data),\n          d2 = dx * dx + dy * dy;\n      if (d2 < radius) {\n        var d = Math.sqrt(radius = d2);\n        x0 = x - d, y0 = y - d;\n        x3 = x + d, y3 = y + d;\n        data = node.data;\n      }\n    }\n  }\n\n  return data;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,wCAAS,CAAC,EAAE,CAAC,EAAE,MAAM;IAClC,IAAI,MACA,KAAK,IAAI,CAAC,GAAG,EACb,KAAK,IAAI,CAAC,GAAG,EACb,IACA,IACA,IACA,IACA,KAAK,IAAI,CAAC,GAAG,EACb,KAAK,IAAI,CAAC,GAAG,EACb,QAAQ,EAAE,EACV,OAAO,IAAI,CAAC,KAAK,EACjB,GACA;IAEJ,IAAI,MAAM,MAAM,IAAI,CAAC,IAAI,2MAAA,CAAA,UAAI,CAAC,MAAM,IAAI,IAAI,IAAI;IAChD,IAAI,UAAU,MAAM,SAAS;SACxB;QACH,KAAK,IAAI,QAAQ,KAAK,IAAI;QAC1B,KAAK,IAAI,QAAQ,KAAK,IAAI;QAC1B,UAAU;IACZ;IAEA,MAAO,IAAI,MAAM,GAAG,GAAI;QAEtB,+DAA+D;QAC/D,IAAI,CAAC,CAAC,OAAO,EAAE,IAAI,KACZ,CAAC,KAAK,EAAE,EAAE,IAAI,MACd,CAAC,KAAK,EAAE,EAAE,IAAI,MACd,CAAC,KAAK,EAAE,EAAE,IAAI,MACd,CAAC,KAAK,EAAE,EAAE,IAAI,IAAI;QAEzB,+BAA+B;QAC/B,IAAI,KAAK,MAAM,EAAE;YACf,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,GACjB,KAAK,CAAC,KAAK,EAAE,IAAI;YAErB,MAAM,IAAI,CACR,IAAI,2MAAA,CAAA,UAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,IAAI,IAAI,KAC9B,IAAI,2MAAA,CAAA,UAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,IAAI,IAAI,KAC9B,IAAI,2MAAA,CAAA,UAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,IAAI,IAAI,KAC9B,IAAI,2MAAA,CAAA,UAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,IAAI,IAAI;YAGhC,oCAAoC;YACpC,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,IAAK,KAAK,IAAK;gBAClC,IAAI,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;gBAC3B,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,IAAI,EAAE;gBACrD,KAAK,CAAC,MAAM,MAAM,GAAG,IAAI,EAAE,GAAG;YAChC;QACF,OAGK;YACH,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,GACtC,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,GACtC,KAAK,KAAK,KAAK,KAAK;YACxB,IAAI,KAAK,QAAQ;gBACf,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS;gBAC3B,KAAK,IAAI,GAAG,KAAK,IAAI;gBACrB,KAAK,IAAI,GAAG,KAAK,IAAI;gBACrB,OAAO,KAAK,IAAI;YAClB;QACF;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5622, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5628, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-quadtree%403.0.1/node_modules/d3-quadtree/src/remove.js"], "sourcesContent": ["export default function(d) {\n  if (isNaN(x = +this._x.call(null, d)) || isNaN(y = +this._y.call(null, d))) return this; // ignore invalid points\n\n  var parent,\n      node = this._root,\n      retainer,\n      previous,\n      next,\n      x0 = this._x0,\n      y0 = this._y0,\n      x1 = this._x1,\n      y1 = this._y1,\n      x,\n      y,\n      xm,\n      ym,\n      right,\n      bottom,\n      i,\n      j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return this;\n\n  // Find the leaf node for the point.\n  // While descending, also retain the deepest parent with a non-removed sibling.\n  if (node.length) while (true) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n    if (!(parent = node, node = node[i = bottom << 1 | right])) return this;\n    if (!node.length) break;\n    if (parent[(i + 1) & 3] || parent[(i + 2) & 3] || parent[(i + 3) & 3]) retainer = parent, j = i;\n  }\n\n  // Find the point to remove.\n  while (node.data !== d) if (!(previous = node, node = node.next)) return this;\n  if (next = node.next) delete node.next;\n\n  // If there are multiple coincident points, remove just the point.\n  if (previous) return (next ? previous.next = next : delete previous.next), this;\n\n  // If this is the root point, remove it.\n  if (!parent) return this._root = next, this;\n\n  // Remove this leaf.\n  next ? parent[i] = next : delete parent[i];\n\n  // If the parent now contains exactly one leaf, collapse superfluous parents.\n  if ((node = parent[0] || parent[1] || parent[2] || parent[3])\n      && node === (parent[3] || parent[2] || parent[1] || parent[0])\n      && !node.length) {\n    if (retainer) retainer[j] = node;\n    else this._root = node;\n  }\n\n  return this;\n}\n\nexport function removeAll(data) {\n  for (var i = 0, n = data.length; i < n; ++i) this.remove(data[i]);\n  return this;\n}\n"], "names": [], "mappings": ";;;;AAAe,wCAAS,CAAC;IACvB,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,OAAO,IAAI,EAAE,wBAAwB;IAEjH,IAAI,QACA,OAAO,IAAI,CAAC,KAAK,EACjB,UACA,UACA,MACA,KAAK,IAAI,CAAC,GAAG,EACb,KAAK,IAAI,CAAC,GAAG,EACb,KAAK,IAAI,CAAC,GAAG,EACb,KAAK,IAAI,CAAC,GAAG,EACb,GACA,GACA,IACA,IACA,OACA,QACA,GACA;IAEJ,uDAAuD;IACvD,IAAI,CAAC,MAAM,OAAO,IAAI;IAEtB,oCAAoC;IACpC,+EAA+E;IAC/E,IAAI,KAAK,MAAM,EAAE,MAAO,KAAM;QAC5B,IAAI,QAAQ,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK;aAAS,KAAK;QAC1D,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK;aAAS,KAAK;QAC3D,IAAI,CAAC,CAAC,SAAS,MAAM,OAAO,IAAI,CAAC,IAAI,UAAU,IAAI,MAAM,GAAG,OAAO,IAAI;QACvE,IAAI,CAAC,KAAK,MAAM,EAAE;QAClB,IAAI,MAAM,CAAC,AAAC,IAAI,IAAK,EAAE,IAAI,MAAM,CAAC,AAAC,IAAI,IAAK,EAAE,IAAI,MAAM,CAAC,AAAC,IAAI,IAAK,EAAE,EAAE,WAAW,QAAQ,IAAI;IAChG;IAEA,4BAA4B;IAC5B,MAAO,KAAK,IAAI,KAAK,EAAG,IAAI,CAAC,CAAC,WAAW,MAAM,OAAO,KAAK,IAAI,GAAG,OAAO,IAAI;IAC7E,IAAI,OAAO,KAAK,IAAI,EAAE,OAAO,KAAK,IAAI;IAEtC,kEAAkE;IAClE,IAAI,UAAU,OAAO,AAAC,OAAO,SAAS,IAAI,GAAG,OAAO,OAAO,SAAS,IAAI,EAAG,IAAI;IAE/E,wCAAwC;IACxC,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI;IAE3C,oBAAoB;IACpB,OAAO,MAAM,CAAC,EAAE,GAAG,OAAO,OAAO,MAAM,CAAC,EAAE;IAE1C,6EAA6E;IAC7E,IAAI,CAAC,OAAO,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,KACrD,SAAS,CAAC,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,KAC1D,CAAC,KAAK,MAAM,EAAE;QACnB,IAAI,UAAU,QAAQ,CAAC,EAAE,GAAG;aACvB,IAAI,CAAC,KAAK,GAAG;IACpB;IAEA,OAAO,IAAI;AACb;AAEO,SAAS,UAAU,IAAI;IAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAI,GAAG,EAAE,EAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;IAChE,OAAO,IAAI;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5668, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5674, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-quadtree%403.0.1/node_modules/d3-quadtree/src/root.js"], "sourcesContent": ["export default function() {\n  return this._root;\n}\n"], "names": [], "mappings": ";;;AAAe;IACb,OAAO,IAAI,CAAC,KAAK;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5680, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5686, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-quadtree%403.0.1/node_modules/d3-quadtree/src/size.js"], "sourcesContent": ["export default function() {\n  var size = 0;\n  this.visit(function(node) {\n    if (!node.length) do ++size; while (node = node.next)\n  });\n  return size;\n}\n"], "names": [], "mappings": ";;;AAAe;IACb,IAAI,OAAO;IACX,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI;QACtB,IAAI,CAAC,KAAK,MAAM,EAAE,GAAG,EAAE;eAAa,OAAO,KAAK,IAAI,CAAC;IACvD;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5697, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5703, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-quadtree%403.0.1/node_modules/d3-quadtree/src/visit.js"], "sourcesContent": ["import Quad from \"./quad.js\";\n\nexport default function(callback) {\n  var quads = [], q, node = this._root, child, x0, y0, x1, y1;\n  if (node) quads.push(new Quad(node, this._x0, this._y0, this._x1, this._y1));\n  while (q = quads.pop()) {\n    if (!callback(node = q.node, x0 = q.x0, y0 = q.y0, x1 = q.x1, y1 = q.y1) && node.length) {\n      var xm = (x0 + x1) / 2, ym = (y0 + y1) / 2;\n      if (child = node[3]) quads.push(new Quad(child, xm, ym, x1, y1));\n      if (child = node[2]) quads.push(new Quad(child, x0, ym, xm, y1));\n      if (child = node[1]) quads.push(new Quad(child, xm, y0, x1, ym));\n      if (child = node[0]) quads.push(new Quad(child, x0, y0, xm, ym));\n    }\n  }\n  return this;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,wCAAS,QAAQ;IAC9B,IAAI,QAAQ,EAAE,EAAE,GAAG,OAAO,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,IAAI,IAAI;IACzD,IAAI,MAAM,MAAM,IAAI,CAAC,IAAI,2MAAA,CAAA,UAAI,CAAC,MAAM,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG;IAC1E,MAAO,IAAI,MAAM,GAAG,GAAI;QACtB,IAAI,CAAC,SAAS,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,KAAK,MAAM,EAAE;YACvF,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,GAAG,KAAK,CAAC,KAAK,EAAE,IAAI;YACzC,IAAI,QAAQ,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,IAAI,2MAAA,CAAA,UAAI,CAAC,OAAO,IAAI,IAAI,IAAI;YAC5D,IAAI,QAAQ,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,IAAI,2MAAA,CAAA,UAAI,CAAC,OAAO,IAAI,IAAI,IAAI;YAC5D,IAAI,QAAQ,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,IAAI,2MAAA,CAAA,UAAI,CAAC,OAAO,IAAI,IAAI,IAAI;YAC5D,IAAI,QAAQ,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,IAAI,2MAAA,CAAA,UAAI,CAAC,OAAO,IAAI,IAAI,IAAI;QAC9D;IACF;IACA,OAAO,IAAI;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5722, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5728, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-quadtree%403.0.1/node_modules/d3-quadtree/src/visitAfter.js"], "sourcesContent": ["import Quad from \"./quad.js\";\n\nexport default function(callback) {\n  var quads = [], next = [], q;\n  if (this._root) quads.push(new Quad(this._root, this._x0, this._y0, this._x1, this._y1));\n  while (q = quads.pop()) {\n    var node = q.node;\n    if (node.length) {\n      var child, x0 = q.x0, y0 = q.y0, x1 = q.x1, y1 = q.y1, xm = (x0 + x1) / 2, ym = (y0 + y1) / 2;\n      if (child = node[0]) quads.push(new Quad(child, x0, y0, xm, ym));\n      if (child = node[1]) quads.push(new Quad(child, xm, y0, x1, ym));\n      if (child = node[2]) quads.push(new Quad(child, x0, ym, xm, y1));\n      if (child = node[3]) quads.push(new Quad(child, xm, ym, x1, y1));\n    }\n    next.push(q);\n  }\n  while (q = next.pop()) {\n    callback(q.node, q.x0, q.y0, q.x1, q.y1);\n  }\n  return this;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,wCAAS,QAAQ;IAC9B,IAAI,QAAQ,EAAE,EAAE,OAAO,EAAE,EAAE;IAC3B,IAAI,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,IAAI,2MAAA,CAAA,UAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG;IACtF,MAAO,IAAI,MAAM,GAAG,GAAI;QACtB,IAAI,OAAO,EAAE,IAAI;QACjB,IAAI,KAAK,MAAM,EAAE;YACf,IAAI,OAAO,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,GAAG,KAAK,CAAC,KAAK,EAAE,IAAI;YAC5F,IAAI,QAAQ,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,IAAI,2MAAA,CAAA,UAAI,CAAC,OAAO,IAAI,IAAI,IAAI;YAC5D,IAAI,QAAQ,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,IAAI,2MAAA,CAAA,UAAI,CAAC,OAAO,IAAI,IAAI,IAAI;YAC5D,IAAI,QAAQ,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,IAAI,2MAAA,CAAA,UAAI,CAAC,OAAO,IAAI,IAAI,IAAI;YAC5D,IAAI,QAAQ,IAAI,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,IAAI,2MAAA,CAAA,UAAI,CAAC,OAAO,IAAI,IAAI,IAAI;QAC9D;QACA,KAAK,IAAI,CAAC;IACZ;IACA,MAAO,IAAI,KAAK,GAAG,GAAI;QACrB,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IACzC;IACA,OAAO,IAAI;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5752, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5758, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-quadtree%403.0.1/node_modules/d3-quadtree/src/x.js"], "sourcesContent": ["export function defaultX(d) {\n  return d[0];\n}\n\nexport default function(_) {\n  return arguments.length ? (this._x = _, this) : this._x;\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS,SAAS,CAAC;IACxB,OAAO,CAAC,CAAC,EAAE;AACb;AAEe,wCAAS,CAAC;IACvB,OAAO,UAAU,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI,IAAI,IAAI,CAAC,EAAE;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5768, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5774, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-quadtree%403.0.1/node_modules/d3-quadtree/src/y.js"], "sourcesContent": ["export function defaultY(d) {\n  return d[1];\n}\n\nexport default function(_) {\n  return arguments.length ? (this._y = _, this) : this._y;\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS,SAAS,CAAC;IACxB,OAAO,CAAC,CAAC,EAAE;AACb;AAEe,wCAAS,CAAC;IACvB,OAAO,UAAU,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,IAAI,IAAI,IAAI,CAAC,EAAE;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5784, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5790, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-quadtree%403.0.1/node_modules/d3-quadtree/src/quadtree.js"], "sourcesContent": ["import tree_add, {addAll as tree_addAll} from \"./add.js\";\nimport tree_cover from \"./cover.js\";\nimport tree_data from \"./data.js\";\nimport tree_extent from \"./extent.js\";\nimport tree_find from \"./find.js\";\nimport tree_remove, {removeAll as tree_removeAll} from \"./remove.js\";\nimport tree_root from \"./root.js\";\nimport tree_size from \"./size.js\";\nimport tree_visit from \"./visit.js\";\nimport tree_visitAfter from \"./visitAfter.js\";\nimport tree_x, {defaultX} from \"./x.js\";\nimport tree_y, {defaultY} from \"./y.js\";\n\nexport default function quadtree(nodes, x, y) {\n  var tree = new Quadtree(x == null ? defaultX : x, y == null ? defaultY : y, NaN, NaN, NaN, NaN);\n  return nodes == null ? tree : tree.addAll(nodes);\n}\n\nfunction Quadtree(x, y, x0, y0, x1, y1) {\n  this._x = x;\n  this._y = y;\n  this._x0 = x0;\n  this._y0 = y0;\n  this._x1 = x1;\n  this._y1 = y1;\n  this._root = undefined;\n}\n\nfunction leaf_copy(leaf) {\n  var copy = {data: leaf.data}, next = copy;\n  while (leaf = leaf.next) next = next.next = {data: leaf.data};\n  return copy;\n}\n\nvar treeProto = quadtree.prototype = Quadtree.prototype;\n\ntreeProto.copy = function() {\n  var copy = new Quadtree(this._x, this._y, this._x0, this._y0, this._x1, this._y1),\n      node = this._root,\n      nodes,\n      child;\n\n  if (!node) return copy;\n\n  if (!node.length) return copy._root = leaf_copy(node), copy;\n\n  nodes = [{source: node, target: copy._root = new Array(4)}];\n  while (node = nodes.pop()) {\n    for (var i = 0; i < 4; ++i) {\n      if (child = node.source[i]) {\n        if (child.length) nodes.push({source: child, target: node.target[i] = new Array(4)});\n        else node.target[i] = leaf_copy(child);\n      }\n    }\n  }\n\n  return copy;\n};\n\ntreeProto.add = tree_add;\ntreeProto.addAll = tree_addAll;\ntreeProto.cover = tree_cover;\ntreeProto.data = tree_data;\ntreeProto.extent = tree_extent;\ntreeProto.find = tree_find;\ntreeProto.remove = tree_remove;\ntreeProto.removeAll = tree_removeAll;\ntreeProto.root = tree_root;\ntreeProto.size = tree_size;\ntreeProto.visit = tree_visit;\ntreeProto.visitAfter = tree_visitAfter;\ntreeProto.x = tree_x;\ntreeProto.y = tree_y;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAEe,SAAS,SAAS,KAAK,EAAE,CAAC,EAAE,CAAC;IAC1C,IAAI,OAAO,IAAI,SAAS,KAAK,OAAO,wMAAA,CAAA,WAAQ,GAAG,GAAG,KAAK,OAAO,wMAAA,CAAA,WAAQ,GAAG,GAAG,KAAK,KAAK,KAAK;IAC3F,OAAO,SAAS,OAAO,OAAO,KAAK,MAAM,CAAC;AAC5C;AAEA,SAAS,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IACpC,IAAI,CAAC,EAAE,GAAG;IACV,IAAI,CAAC,EAAE,GAAG;IACV,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,KAAK,GAAG;AACf;AAEA,SAAS,UAAU,IAAI;IACrB,IAAI,OAAO;QAAC,MAAM,KAAK,IAAI;IAAA,GAAG,OAAO;IACrC,MAAO,OAAO,KAAK,IAAI,CAAE,OAAO,KAAK,IAAI,GAAG;QAAC,MAAM,KAAK,IAAI;IAAA;IAC5D,OAAO;AACT;AAEA,IAAI,YAAY,SAAS,SAAS,GAAG,SAAS,SAAS;AAEvD,UAAU,IAAI,GAAG;IACf,IAAI,OAAO,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,GAC5E,OAAO,IAAI,CAAC,KAAK,EACjB,OACA;IAEJ,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI,CAAC,KAAK,MAAM,EAAE,OAAO,KAAK,KAAK,GAAG,UAAU,OAAO;IAEvD,QAAQ;QAAC;YAAC,QAAQ;YAAM,QAAQ,KAAK,KAAK,GAAG,IAAI,MAAM;QAAE;KAAE;IAC3D,MAAO,OAAO,MAAM,GAAG,GAAI;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YAC1B,IAAI,QAAQ,KAAK,MAAM,CAAC,EAAE,EAAE;gBAC1B,IAAI,MAAM,MAAM,EAAE,MAAM,IAAI,CAAC;oBAAC,QAAQ;oBAAO,QAAQ,KAAK,MAAM,CAAC,EAAE,GAAG,IAAI,MAAM;gBAAE;qBAC7E,KAAK,MAAM,CAAC,EAAE,GAAG,UAAU;YAClC;QACF;IACF;IAEA,OAAO;AACT;AAEA,UAAU,GAAG,GAAG,0MAAA,CAAA,UAAQ;AACxB,UAAU,MAAM,GAAG,0MAAA,CAAA,SAAW;AAC9B,UAAU,KAAK,GAAG,4MAAA,CAAA,UAAU;AAC5B,UAAU,IAAI,GAAG,2MAAA,CAAA,UAAS;AAC1B,UAAU,MAAM,GAAG,6MAAA,CAAA,UAAW;AAC9B,UAAU,IAAI,GAAG,2MAAA,CAAA,UAAS;AAC1B,UAAU,MAAM,GAAG,6MAAA,CAAA,UAAW;AAC9B,UAAU,SAAS,GAAG,6MAAA,CAAA,YAAc;AACpC,UAAU,IAAI,GAAG,2MAAA,CAAA,UAAS;AAC1B,UAAU,IAAI,GAAG,2MAAA,CAAA,UAAS;AAC1B,UAAU,KAAK,GAAG,4MAAA,CAAA,UAAU;AAC5B,UAAU,UAAU,GAAG,iNAAA,CAAA,UAAe;AACtC,UAAU,CAAC,GAAG,wMAAA,CAAA,UAAM;AACpB,UAAU,CAAC,GAAG,wMAAA,CAAA,UAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5877, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5893, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-force%403.0.0/node_modules/d3-force/src/manyBody.js"], "sourcesContent": ["import {quadtree} from \"d3-quadtree\";\nimport constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\nimport {x, y} from \"./simulation.js\";\n\nexport default function() {\n  var nodes,\n      node,\n      random,\n      alpha,\n      strength = constant(-30),\n      strengths,\n      distanceMin2 = 1,\n      distanceMax2 = Infinity,\n      theta2 = 0.81;\n\n  function force(_) {\n    var i, n = nodes.length, tree = quadtree(nodes, x, y).visitAfter(accumulate);\n    for (alpha = _, i = 0; i < n; ++i) node = nodes[i], tree.visit(apply);\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length, node;\n    strengths = new Array(n);\n    for (i = 0; i < n; ++i) node = nodes[i], strengths[node.index] = +strength(node, i, nodes);\n  }\n\n  function accumulate(quad) {\n    var strength = 0, q, c, weight = 0, x, y, i;\n\n    // For internal nodes, accumulate forces from child quadrants.\n    if (quad.length) {\n      for (x = y = i = 0; i < 4; ++i) {\n        if ((q = quad[i]) && (c = Math.abs(q.value))) {\n          strength += q.value, weight += c, x += c * q.x, y += c * q.y;\n        }\n      }\n      quad.x = x / weight;\n      quad.y = y / weight;\n    }\n\n    // For leaf nodes, accumulate forces from coincident quadrants.\n    else {\n      q = quad;\n      q.x = q.data.x;\n      q.y = q.data.y;\n      do strength += strengths[q.data.index];\n      while (q = q.next);\n    }\n\n    quad.value = strength;\n  }\n\n  function apply(quad, x1, _, x2) {\n    if (!quad.value) return true;\n\n    var x = quad.x - node.x,\n        y = quad.y - node.y,\n        w = x2 - x1,\n        l = x * x + y * y;\n\n    // Apply the Barnes-Hut approximation if possible.\n    // Limit forces for very close nodes; randomize direction if coincident.\n    if (w * w / theta2 < l) {\n      if (l < distanceMax2) {\n        if (x === 0) x = jiggle(random), l += x * x;\n        if (y === 0) y = jiggle(random), l += y * y;\n        if (l < distanceMin2) l = Math.sqrt(distanceMin2 * l);\n        node.vx += x * quad.value * alpha / l;\n        node.vy += y * quad.value * alpha / l;\n      }\n      return true;\n    }\n\n    // Otherwise, process points directly.\n    else if (quad.length || l >= distanceMax2) return;\n\n    // Limit forces for very close nodes; randomize direction if coincident.\n    if (quad.data !== node || quad.next) {\n      if (x === 0) x = jiggle(random), l += x * x;\n      if (y === 0) y = jiggle(random), l += y * y;\n      if (l < distanceMin2) l = Math.sqrt(distanceMin2 * l);\n    }\n\n    do if (quad.data !== node) {\n      w = strengths[quad.data.index] * alpha / l;\n      node.vx += x * w;\n      node.vy += y * w;\n    } while (quad = quad.next);\n  }\n\n  force.initialize = function(_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.distanceMin = function(_) {\n    return arguments.length ? (distanceMin2 = _ * _, force) : Math.sqrt(distanceMin2);\n  };\n\n  force.distanceMax = function(_) {\n    return arguments.length ? (distanceMax2 = _ * _, force) : Math.sqrt(distanceMax2);\n  };\n\n  force.theta = function(_) {\n    return arguments.length ? (theta2 = _ * _, force) : Math.sqrt(theta2);\n  };\n\n  return force;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEe;IACb,IAAI,OACA,MACA,QACA,OACA,WAAW,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,KACrB,WACA,eAAe,GACf,eAAe,UACf,SAAS;IAEb,SAAS,MAAM,CAAC;QACd,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,OAAO,CAAA,GAAA,sPAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,2MAAA,CAAA,IAAC,EAAE,2MAAA,CAAA,IAAC,EAAE,UAAU,CAAC;QACjE,IAAK,QAAQ,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,OAAO,KAAK,CAAC,EAAE,EAAE,KAAK,KAAK,CAAC;IACjE;IAEA,SAAS;QACP,IAAI,CAAC,OAAO;QACZ,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE;QACzB,YAAY,IAAI,MAAM;QACtB,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,OAAO,KAAK,CAAC,EAAE,EAAE,SAAS,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,SAAS,MAAM,GAAG;IACtF;IAEA,SAAS,WAAW,IAAI;QACtB,IAAI,WAAW,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG;QAE1C,8DAA8D;QAC9D,IAAI,KAAK,MAAM,EAAE;YACf,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;gBAC9B,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,CAAC,GAAG;oBAC5C,YAAY,EAAE,KAAK,EAAE,UAAU,GAAG,KAAK,IAAI,EAAE,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC;gBAC9D;YACF;YACA,KAAK,CAAC,GAAG,IAAI;YACb,KAAK,CAAC,GAAG,IAAI;QACf,OAGK;YACH,IAAI;YACJ,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YACd,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YACd,GAAG,YAAY,SAAS,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC;mBAC/B,IAAI,EAAE,IAAI,CAAE;QACrB;QAEA,KAAK,KAAK,GAAG;IACf;IAEA,SAAS,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;QAC5B,IAAI,CAAC,KAAK,KAAK,EAAE,OAAO;QAExB,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,EACnB,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,EACnB,IAAI,KAAK,IACT,IAAI,IAAI,IAAI,IAAI;QAEpB,kDAAkD;QAClD,wEAAwE;QACxE,IAAI,IAAI,IAAI,SAAS,GAAG;YACtB,IAAI,IAAI,cAAc;gBACpB,IAAI,MAAM,GAAG,IAAI,CAAA,GAAA,uMAAA,CAAA,UAAM,AAAD,EAAE,SAAS,KAAK,IAAI;gBAC1C,IAAI,MAAM,GAAG,IAAI,CAAA,GAAA,uMAAA,CAAA,UAAM,AAAD,EAAE,SAAS,KAAK,IAAI;gBAC1C,IAAI,IAAI,cAAc,IAAI,KAAK,IAAI,CAAC,eAAe;gBACnD,KAAK,EAAE,IAAI,IAAI,KAAK,KAAK,GAAG,QAAQ;gBACpC,KAAK,EAAE,IAAI,IAAI,KAAK,KAAK,GAAG,QAAQ;YACtC;YACA,OAAO;QACT,OAGK,IAAI,KAAK,MAAM,IAAI,KAAK,cAAc;QAE3C,wEAAwE;QACxE,IAAI,KAAK,IAAI,KAAK,QAAQ,KAAK,IAAI,EAAE;YACnC,IAAI,MAAM,GAAG,IAAI,CAAA,GAAA,uMAAA,CAAA,UAAM,AAAD,EAAE,SAAS,KAAK,IAAI;YAC1C,IAAI,MAAM,GAAG,IAAI,CAAA,GAAA,uMAAA,CAAA,UAAM,AAAD,EAAE,SAAS,KAAK,IAAI;YAC1C,IAAI,IAAI,cAAc,IAAI,KAAK,IAAI,CAAC,eAAe;QACrD;QAEA,GAAG,IAAI,KAAK,IAAI,KAAK,MAAM;YACzB,IAAI,SAAS,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,QAAQ;YACzC,KAAK,EAAE,IAAI,IAAI;YACf,KAAK,EAAE,IAAI,IAAI;QACjB;eAAS,OAAO,KAAK,IAAI,CAAE;IAC7B;IAEA,MAAM,UAAU,GAAG,SAAS,MAAM,EAAE,OAAO;QACzC,QAAQ;QACR,SAAS;QACT;IACF;IAEA,MAAM,QAAQ,GAAG,SAAS,CAAC;QACzB,OAAO,UAAU,MAAM,GAAG,CAAC,WAAW,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,cAAc,KAAK,IAAI;IAC3G;IAEA,MAAM,WAAW,GAAG,SAAS,CAAC;QAC5B,OAAO,UAAU,MAAM,GAAG,CAAC,eAAe,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,CAAC;IACtE;IAEA,MAAM,WAAW,GAAG,SAAS,CAAC;QAC5B,OAAO,UAAU,MAAM,GAAG,CAAC,eAAe,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,CAAC;IACtE;IAEA,MAAM,KAAK,GAAG,SAAS,CAAC;QACtB,OAAO,UAAU,MAAM,GAAG,CAAC,SAAS,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,CAAC;IAChE;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5983, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5999, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-force%403.0.0/node_modules/d3-force/src/center.js"], "sourcesContent": ["export default function(x, y) {\n  var nodes, strength = 1;\n\n  if (x == null) x = 0;\n  if (y == null) y = 0;\n\n  function force() {\n    var i,\n        n = nodes.length,\n        node,\n        sx = 0,\n        sy = 0;\n\n    for (i = 0; i < n; ++i) {\n      node = nodes[i], sx += node.x, sy += node.y;\n    }\n\n    for (sx = (sx / n - x) * strength, sy = (sy / n - y) * strength, i = 0; i < n; ++i) {\n      node = nodes[i], node.x -= sx, node.y -= sy;\n    }\n  }\n\n  force.initialize = function(_) {\n    nodes = _;\n  };\n\n  force.x = function(_) {\n    return arguments.length ? (x = +_, force) : x;\n  };\n\n  force.y = function(_) {\n    return arguments.length ? (y = +_, force) : y;\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = +_, force) : strength;\n  };\n\n  return force;\n}\n"], "names": [], "mappings": ";;;AAAe,wCAAS,CAAC,EAAE,CAAC;IAC1B,IAAI,OAAO,WAAW;IAEtB,IAAI,KAAK,MAAM,IAAI;IACnB,IAAI,KAAK,MAAM,IAAI;IAEnB,SAAS;QACP,IAAI,GACA,IAAI,MAAM,MAAM,EAChB,MACA,KAAK,GACL,KAAK;QAET,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACtB,OAAO,KAAK,CAAC,EAAE,EAAE,MAAM,KAAK,CAAC,EAAE,MAAM,KAAK,CAAC;QAC7C;QAEA,IAAK,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,UAAU,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,UAAU,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YAClF,OAAO,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI;QAC3C;IACF;IAEA,MAAM,UAAU,GAAG,SAAS,CAAC;QAC3B,QAAQ;IACV;IAEA,MAAM,CAAC,GAAG,SAAS,CAAC;QAClB,OAAO,UAAU,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI;IAC9C;IAEA,MAAM,CAAC,GAAG,SAAS,CAAC;QAClB,OAAO,UAAU,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI;IAC9C;IAEA,MAAM,QAAQ,GAAG,SAAS,CAAC;QACzB,OAAO,UAAU,MAAM,GAAG,CAAC,WAAW,CAAC,GAAG,KAAK,IAAI;IACrD;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6029, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6045, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-force%403.0.0/node_modules/d3-force/src/collide.js"], "sourcesContent": ["import {quadtree} from \"d3-quadtree\";\nimport constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\n\nfunction x(d) {\n  return d.x + d.vx;\n}\n\nfunction y(d) {\n  return d.y + d.vy;\n}\n\nexport default function(radius) {\n  var nodes,\n      radii,\n      random,\n      strength = 1,\n      iterations = 1;\n\n  if (typeof radius !== \"function\") radius = constant(radius == null ? 1 : +radius);\n\n  function force() {\n    var i, n = nodes.length,\n        tree,\n        node,\n        xi,\n        yi,\n        ri,\n        ri2;\n\n    for (var k = 0; k < iterations; ++k) {\n      tree = quadtree(nodes, x, y).visitAfter(prepare);\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        ri = radii[node.index], ri2 = ri * ri;\n        xi = node.x + node.vx;\n        yi = node.y + node.vy;\n        tree.visit(apply);\n      }\n    }\n\n    function apply(quad, x0, y0, x1, y1) {\n      var data = quad.data, rj = quad.r, r = ri + rj;\n      if (data) {\n        if (data.index > node.index) {\n          var x = xi - data.x - data.vx,\n              y = yi - data.y - data.vy,\n              l = x * x + y * y;\n          if (l < r * r) {\n            if (x === 0) x = jiggle(random), l += x * x;\n            if (y === 0) y = jiggle(random), l += y * y;\n            l = (r - (l = Math.sqrt(l))) / l * strength;\n            node.vx += (x *= l) * (r = (rj *= rj) / (ri2 + rj));\n            node.vy += (y *= l) * r;\n            data.vx -= x * (r = 1 - r);\n            data.vy -= y * r;\n          }\n        }\n        return;\n      }\n      return x0 > xi + r || x1 < xi - r || y0 > yi + r || y1 < yi - r;\n    }\n  }\n\n  function prepare(quad) {\n    if (quad.data) return quad.r = radii[quad.data.index];\n    for (var i = quad.r = 0; i < 4; ++i) {\n      if (quad[i] && quad[i].r > quad.r) {\n        quad.r = quad[i].r;\n      }\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length, node;\n    radii = new Array(n);\n    for (i = 0; i < n; ++i) node = nodes[i], radii[node.index] = +radius(node, i, nodes);\n  }\n\n  force.initialize = function(_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n\n  force.iterations = function(_) {\n    return arguments.length ? (iterations = +_, force) : iterations;\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = +_, force) : strength;\n  };\n\n  force.radius = function(_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : radius;\n  };\n\n  return force;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,EAAE,CAAC;IACV,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;AACnB;AAEA,SAAS,EAAE,CAAC;IACV,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;AACnB;AAEe,wCAAS,MAAM;IAC5B,IAAI,OACA,OACA,QACA,WAAW,GACX,aAAa;IAEjB,IAAI,OAAO,WAAW,YAAY,SAAS,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,UAAU,OAAO,IAAI,CAAC;IAE1E,SAAS;QACP,IAAI,GAAG,IAAI,MAAM,MAAM,EACnB,MACA,MACA,IACA,IACA,IACA;QAEJ,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,EAAE,EAAG;YACnC,OAAO,CAAA,GAAA,sPAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,GAAG,GAAG,UAAU,CAAC;YACxC,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;gBACtB,OAAO,KAAK,CAAC,EAAE;gBACf,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,EAAE,MAAM,KAAK;gBACnC,KAAK,KAAK,CAAC,GAAG,KAAK,EAAE;gBACrB,KAAK,KAAK,CAAC,GAAG,KAAK,EAAE;gBACrB,KAAK,KAAK,CAAC;YACb;QACF;QAEA,SAAS,MAAM,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;YACjC,IAAI,OAAO,KAAK,IAAI,EAAE,KAAK,KAAK,CAAC,EAAE,IAAI,KAAK;YAC5C,IAAI,MAAM;gBACR,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,EAAE;oBAC3B,IAAI,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,EAAE,EACzB,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,EAAE,EACzB,IAAI,IAAI,IAAI,IAAI;oBACpB,IAAI,IAAI,IAAI,GAAG;wBACb,IAAI,MAAM,GAAG,IAAI,CAAA,GAAA,uMAAA,CAAA,UAAM,AAAD,EAAE,SAAS,KAAK,IAAI;wBAC1C,IAAI,MAAM,GAAG,IAAI,CAAA,GAAA,uMAAA,CAAA,UAAM,AAAD,EAAE,SAAS,KAAK,IAAI;wBAC1C,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI;wBACnC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;wBAClD,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;wBACtB,KAAK,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;wBACzB,KAAK,EAAE,IAAI,IAAI;oBACjB;gBACF;gBACA;YACF;YACA,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;QAChE;IACF;IAEA,SAAS,QAAQ,IAAI;QACnB,IAAI,KAAK,IAAI,EAAE,OAAO,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC;QACrD,IAAK,IAAI,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,EAAG;YACnC,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE;gBACjC,KAAK,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;YACpB;QACF;IACF;IAEA,SAAS;QACP,IAAI,CAAC,OAAO;QACZ,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE;QACzB,QAAQ,IAAI,MAAM;QAClB,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG,OAAO,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,MAAM,GAAG;IAChF;IAEA,MAAM,UAAU,GAAG,SAAS,MAAM,EAAE,OAAO;QACzC,QAAQ;QACR,SAAS;QACT;IACF;IAEA,MAAM,UAAU,GAAG,SAAS,CAAC;QAC3B,OAAO,UAAU,MAAM,GAAG,CAAC,aAAa,CAAC,GAAG,KAAK,IAAI;IACvD;IAEA,MAAM,QAAQ,GAAG,SAAS,CAAC;QACzB,OAAO,UAAU,MAAM,GAAG,CAAC,WAAW,CAAC,GAAG,KAAK,IAAI;IACrD;IAEA,MAAM,MAAM,GAAG,SAAS,CAAC;QACvB,OAAO,UAAU,MAAM,GAAG,CAAC,SAAS,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,cAAc,KAAK,IAAI;IACzG;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6125, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6141, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-force%403.0.0/node_modules/d3-force/src/x.js"], "sourcesContent": ["import constant from \"./constant.js\";\n\nexport default function(x) {\n  var strength = constant(0.1),\n      nodes,\n      strengths,\n      xz;\n\n  if (typeof x !== \"function\") x = constant(x == null ? 0 : +x);\n\n  function force(alpha) {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.vx += (xz[i] - node.x) * strengths[i] * alpha;\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length;\n    strengths = new Array(n);\n    xz = new Array(n);\n    for (i = 0; i < n; ++i) {\n      strengths[i] = isNaN(xz[i] = +x(nodes[i], i, nodes)) ? 0 : +strength(nodes[i], i, nodes);\n    }\n  }\n\n  force.initialize = function(_) {\n    nodes = _;\n    initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : x;\n  };\n\n  return force;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,wCAAS,CAAC;IACvB,IAAI,WAAW,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,MACpB,OACA,WACA;IAEJ,IAAI,OAAO,MAAM,YAAY,IAAI,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,KAAK,OAAO,IAAI,CAAC;IAE3D,SAAS,MAAM,KAAK;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,MAAM,IAAI,GAAG,EAAE,EAAG;YAClD,OAAO,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,EAAE,GAAG;QAChE;IACF;IAEA,SAAS;QACP,IAAI,CAAC,OAAO;QACZ,IAAI,GAAG,IAAI,MAAM,MAAM;QACvB,YAAY,IAAI,MAAM;QACtB,KAAK,IAAI,MAAM;QACf,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACtB,SAAS,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,GAAG,UAAU,IAAI,CAAC,SAAS,KAAK,CAAC,EAAE,EAAE,GAAG;QACpF;IACF;IAEA,MAAM,UAAU,GAAG,SAAS,CAAC;QAC3B,QAAQ;QACR;IACF;IAEA,MAAM,QAAQ,GAAG,SAAS,CAAC;QACzB,OAAO,UAAU,MAAM,GAAG,CAAC,WAAW,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,cAAc,KAAK,IAAI;IAC3G;IAEA,MAAM,CAAC,GAAG,SAAS,CAAC;QAClB,OAAO,UAAU,MAAM,GAAG,CAAC,IAAI,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,cAAc,KAAK,IAAI;IACpG;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6175, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6191, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-force%403.0.0/node_modules/d3-force/src/y.js"], "sourcesContent": ["import constant from \"./constant.js\";\n\nexport default function(y) {\n  var strength = constant(0.1),\n      nodes,\n      strengths,\n      yz;\n\n  if (typeof y !== \"function\") y = constant(y == null ? 0 : +y);\n\n  function force(alpha) {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.vy += (yz[i] - node.y) * strengths[i] * alpha;\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length;\n    strengths = new Array(n);\n    yz = new Array(n);\n    for (i = 0; i < n; ++i) {\n      strengths[i] = isNaN(yz[i] = +y(nodes[i], i, nodes)) ? 0 : +strength(nodes[i], i, nodes);\n    }\n  }\n\n  force.initialize = function(_) {\n    nodes = _;\n    initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : y;\n  };\n\n  return force;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,wCAAS,CAAC;IACvB,IAAI,WAAW,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,MACpB,OACA,WACA;IAEJ,IAAI,OAAO,MAAM,YAAY,IAAI,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,KAAK,OAAO,IAAI,CAAC;IAE3D,SAAS,MAAM,KAAK;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,MAAM,IAAI,GAAG,EAAE,EAAG;YAClD,OAAO,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,EAAE,GAAG;QAChE;IACF;IAEA,SAAS;QACP,IAAI,CAAC,OAAO;QACZ,IAAI,GAAG,IAAI,MAAM,MAAM;QACvB,YAAY,IAAI,MAAM;QACtB,KAAK,IAAI,MAAM;QACf,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACtB,SAAS,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,GAAG,UAAU,IAAI,CAAC,SAAS,KAAK,CAAC,EAAE,EAAE,GAAG;QACpF;IACF;IAEA,MAAM,UAAU,GAAG,SAAS,CAAC;QAC3B,QAAQ;QACR;IACF;IAEA,MAAM,QAAQ,GAAG,SAAS,CAAC;QACzB,OAAO,UAAU,MAAM,GAAG,CAAC,WAAW,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,cAAc,KAAK,IAAI;IAC3G;IAEA,MAAM,CAAC,GAAG,SAAS,CAAC;QAClB,OAAO,UAAU,MAAM,GAAG,CAAC,IAAI,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,yMAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,IAAI,cAAc,KAAK,IAAI;IACpG;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6225, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6241, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-drag%403.0.0/node_modules/d3-drag/src/constant.js"], "sourcesContent": ["export default x => () => x;\n"], "names": [], "mappings": ";;;uCAAe,CAAA,IAAK,IAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6245, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6251, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-drag%403.0.0/node_modules/d3-drag/src/event.js"], "sourcesContent": ["export default function DragEvent(type, {\n  sourceEvent,\n  subject,\n  target,\n  identifier,\n  active,\n  x, y, dx, dy,\n  dispatch\n}) {\n  Object.defineProperties(this, {\n    type: {value: type, enumerable: true, configurable: true},\n    sourceEvent: {value: sourceEvent, enumerable: true, configurable: true},\n    subject: {value: subject, enumerable: true, configurable: true},\n    target: {value: target, enumerable: true, configurable: true},\n    identifier: {value: identifier, enumerable: true, configurable: true},\n    active: {value: active, enumerable: true, configurable: true},\n    x: {value: x, enumerable: true, configurable: true},\n    y: {value: y, enumerable: true, configurable: true},\n    dx: {value: dx, enumerable: true, configurable: true},\n    dy: {value: dy, enumerable: true, configurable: true},\n    _: {value: dispatch}\n  });\n}\n\nDragEvent.prototype.on = function() {\n  var value = this._.on.apply(this._, arguments);\n  return value === this._ ? this : value;\n};\n"], "names": [], "mappings": ";;;AAAe,SAAS,UAAU,IAAI,EAAE,EACtC,WAAW,EACX,OAAO,EACP,MAAM,EACN,UAAU,EACV,MAAM,EACN,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EACZ,QAAQ,EACT;IACC,OAAO,gBAAgB,CAAC,IAAI,EAAE;QAC5B,MAAM;YAAC,OAAO;YAAM,YAAY;YAAM,cAAc;QAAI;QACxD,aAAa;YAAC,OAAO;YAAa,YAAY;YAAM,cAAc;QAAI;QACtE,SAAS;YAAC,OAAO;YAAS,YAAY;YAAM,cAAc;QAAI;QAC9D,QAAQ;YAAC,OAAO;YAAQ,YAAY;YAAM,cAAc;QAAI;QAC5D,YAAY;YAAC,OAAO;YAAY,YAAY;YAAM,cAAc;QAAI;QACpE,QAAQ;YAAC,OAAO;YAAQ,YAAY;YAAM,cAAc;QAAI;QAC5D,GAAG;YAAC,OAAO;YAAG,YAAY;YAAM,cAAc;QAAI;QAClD,GAAG;YAAC,OAAO;YAAG,YAAY;YAAM,cAAc;QAAI;QAClD,IAAI;YAAC,OAAO;YAAI,YAAY;YAAM,cAAc;QAAI;QACpD,IAAI;YAAC,OAAO;YAAI,YAAY;YAAM,cAAc;QAAI;QACpD,GAAG;YAAC,OAAO;QAAQ;IACrB;AACF;AAEA,UAAU,SAAS,CAAC,EAAE,GAAG;IACvB,IAAI,QAAQ,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE;IACpC,OAAO,UAAU,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6315, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6321, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/d3-drag%403.0.0/node_modules/d3-drag/src/drag.js"], "sourcesContent": ["import {dispatch} from \"d3-dispatch\";\nimport {select, pointer} from \"d3-selection\";\nimport nodrag, {yesdrag} from \"./nodrag.js\";\nimport noevent, {nonpassive, nonpassivecapture, nopropagation} from \"./noevent.js\";\nimport constant from \"./constant.js\";\nimport DragEvent from \"./event.js\";\n\n// Ignore right-click, since that should open the context menu.\nfunction defaultFilter(event) {\n  return !event.ctrlKey && !event.button;\n}\n\nfunction defaultContainer() {\n  return this.parentNode;\n}\n\nfunction defaultSubject(event, d) {\n  return d == null ? {x: event.x, y: event.y} : d;\n}\n\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || (\"ontouchstart\" in this);\n}\n\nexport default function() {\n  var filter = defaultFilter,\n      container = defaultContainer,\n      subject = defaultSubject,\n      touchable = defaultTouchable,\n      gestures = {},\n      listeners = dispatch(\"start\", \"drag\", \"end\"),\n      active = 0,\n      mousedownx,\n      mousedowny,\n      mousemoving,\n      touchending,\n      clickDistance2 = 0;\n\n  function drag(selection) {\n    selection\n        .on(\"mousedown.drag\", mousedowned)\n      .filter(touchable)\n        .on(\"touchstart.drag\", touchstarted)\n        .on(\"touchmove.drag\", touchmoved, nonpassive)\n        .on(\"touchend.drag touchcancel.drag\", touchended)\n        .style(\"touch-action\", \"none\")\n        .style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n\n  function mousedowned(event, d) {\n    if (touchending || !filter.call(this, event, d)) return;\n    var gesture = beforestart(this, container.call(this, event, d), event, d, \"mouse\");\n    if (!gesture) return;\n    select(event.view)\n      .on(\"mousemove.drag\", mousemoved, nonpassivecapture)\n      .on(\"mouseup.drag\", mouseupped, nonpassivecapture);\n    nodrag(event.view);\n    nopropagation(event);\n    mousemoving = false;\n    mousedownx = event.clientX;\n    mousedowny = event.clientY;\n    gesture(\"start\", event);\n  }\n\n  function mousemoved(event) {\n    noevent(event);\n    if (!mousemoving) {\n      var dx = event.clientX - mousedownx, dy = event.clientY - mousedowny;\n      mousemoving = dx * dx + dy * dy > clickDistance2;\n    }\n    gestures.mouse(\"drag\", event);\n  }\n\n  function mouseupped(event) {\n    select(event.view).on(\"mousemove.drag mouseup.drag\", null);\n    yesdrag(event.view, mousemoving);\n    noevent(event);\n    gestures.mouse(\"end\", event);\n  }\n\n  function touchstarted(event, d) {\n    if (!filter.call(this, event, d)) return;\n    var touches = event.changedTouches,\n        c = container.call(this, event, d),\n        n = touches.length, i, gesture;\n\n    for (i = 0; i < n; ++i) {\n      if (gesture = beforestart(this, c, event, d, touches[i].identifier, touches[i])) {\n        nopropagation(event);\n        gesture(\"start\", event, touches[i]);\n      }\n    }\n  }\n\n  function touchmoved(event) {\n    var touches = event.changedTouches,\n        n = touches.length, i, gesture;\n\n    for (i = 0; i < n; ++i) {\n      if (gesture = gestures[touches[i].identifier]) {\n        noevent(event);\n        gesture(\"drag\", event, touches[i]);\n      }\n    }\n  }\n\n  function touchended(event) {\n    var touches = event.changedTouches,\n        n = touches.length, i, gesture;\n\n    if (touchending) clearTimeout(touchending);\n    touchending = setTimeout(function() { touchending = null; }, 500); // Ghost clicks are delayed!\n    for (i = 0; i < n; ++i) {\n      if (gesture = gestures[touches[i].identifier]) {\n        nopropagation(event);\n        gesture(\"end\", event, touches[i]);\n      }\n    }\n  }\n\n  function beforestart(that, container, event, d, identifier, touch) {\n    var dispatch = listeners.copy(),\n        p = pointer(touch || event, container), dx, dy,\n        s;\n\n    if ((s = subject.call(that, new DragEvent(\"beforestart\", {\n        sourceEvent: event,\n        target: drag,\n        identifier,\n        active,\n        x: p[0],\n        y: p[1],\n        dx: 0,\n        dy: 0,\n        dispatch\n      }), d)) == null) return;\n\n    dx = s.x - p[0] || 0;\n    dy = s.y - p[1] || 0;\n\n    return function gesture(type, event, touch) {\n      var p0 = p, n;\n      switch (type) {\n        case \"start\": gestures[identifier] = gesture, n = active++; break;\n        case \"end\": delete gestures[identifier], --active; // falls through\n        case \"drag\": p = pointer(touch || event, container), n = active; break;\n      }\n      dispatch.call(\n        type,\n        that,\n        new DragEvent(type, {\n          sourceEvent: event,\n          subject: s,\n          target: drag,\n          identifier,\n          active: n,\n          x: p[0] + dx,\n          y: p[1] + dy,\n          dx: p[0] - p0[0],\n          dy: p[1] - p0[1],\n          dispatch\n        }),\n        d\n      );\n    };\n  }\n\n  drag.filter = function(_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), drag) : filter;\n  };\n\n  drag.container = function(_) {\n    return arguments.length ? (container = typeof _ === \"function\" ? _ : constant(_), drag) : container;\n  };\n\n  drag.subject = function(_) {\n    return arguments.length ? (subject = typeof _ === \"function\" ? _ : constant(_), drag) : subject;\n  };\n\n  drag.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), drag) : touchable;\n  };\n\n  drag.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? drag : value;\n  };\n\n  drag.clickDistance = function(_) {\n    return arguments.length ? (clickDistance2 = (_ = +_) * _, drag) : Math.sqrt(clickDistance2);\n  };\n\n  return drag;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEA,+DAA+D;AAC/D,SAAS,cAAc,KAAK;IAC1B,OAAO,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,MAAM;AACxC;AAEA,SAAS;IACP,OAAO,IAAI,CAAC,UAAU;AACxB;AAEA,SAAS,eAAe,KAAK,EAAE,CAAC;IAC9B,OAAO,KAAK,OAAO;QAAC,GAAG,MAAM,CAAC;QAAE,GAAG,MAAM,CAAC;IAAA,IAAI;AAChD;AAEA,SAAS;IACP,OAAO,UAAU,cAAc,IAAK,kBAAkB,IAAI;AAC5D;AAEe;IACb,IAAI,SAAS,eACT,YAAY,kBACZ,UAAU,gBACV,YAAY,kBACZ,WAAW,CAAC,GACZ,YAAY,CAAA,GAAA,sPAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,QAAQ,QACtC,SAAS,GACT,YACA,YACA,aACA,aACA,iBAAiB;IAErB,SAAS,KAAK,SAAS;QACrB,UACK,EAAE,CAAC,kBAAkB,aACvB,MAAM,CAAC,WACL,EAAE,CAAC,mBAAmB,cACtB,EAAE,CAAC,kBAAkB,YAAY,sMAAA,CAAA,aAAU,EAC3C,EAAE,CAAC,kCAAkC,YACrC,KAAK,CAAC,gBAAgB,QACtB,KAAK,CAAC,+BAA+B;IAC5C;IAEA,SAAS,YAAY,KAAK,EAAE,CAAC;QAC3B,IAAI,eAAe,CAAC,OAAO,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI;QACjD,IAAI,UAAU,YAAY,IAAI,EAAE,UAAU,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,OAAO,GAAG;QAC1E,IAAI,CAAC,SAAS;QACd,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE,MAAM,IAAI,EACd,EAAE,CAAC,kBAAkB,YAAY,sMAAA,CAAA,oBAAiB,EAClD,EAAE,CAAC,gBAAgB,YAAY,sMAAA,CAAA,oBAAiB;QACnD,CAAA,GAAA,qMAAA,CAAA,UAAM,AAAD,EAAE,MAAM,IAAI;QACjB,CAAA,GAAA,sMAAA,CAAA,gBAAa,AAAD,EAAE;QACd,cAAc;QACd,aAAa,MAAM,OAAO;QAC1B,aAAa,MAAM,OAAO;QAC1B,QAAQ,SAAS;IACnB;IAEA,SAAS,WAAW,KAAK;QACvB,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE;QACR,IAAI,CAAC,aAAa;YAChB,IAAI,KAAK,MAAM,OAAO,GAAG,YAAY,KAAK,MAAM,OAAO,GAAG;YAC1D,cAAc,KAAK,KAAK,KAAK,KAAK;QACpC;QACA,SAAS,KAAK,CAAC,QAAQ;IACzB;IAEA,SAAS,WAAW,KAAK;QACvB,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE,MAAM,IAAI,EAAE,EAAE,CAAC,+BAA+B;QACrD,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,MAAM,IAAI,EAAE;QACpB,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE;QACR,SAAS,KAAK,CAAC,OAAO;IACxB;IAEA,SAAS,aAAa,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI;QAClC,IAAI,UAAU,MAAM,cAAc,EAC9B,IAAI,UAAU,IAAI,CAAC,IAAI,EAAE,OAAO,IAChC,IAAI,QAAQ,MAAM,EAAE,GAAG;QAE3B,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACtB,IAAI,UAAU,YAAY,IAAI,EAAE,GAAG,OAAO,GAAG,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,GAAG;gBAC/E,CAAA,GAAA,sMAAA,CAAA,gBAAa,AAAD,EAAE;gBACd,QAAQ,SAAS,OAAO,OAAO,CAAC,EAAE;YACpC;QACF;IACF;IAEA,SAAS,WAAW,KAAK;QACvB,IAAI,UAAU,MAAM,cAAc,EAC9B,IAAI,QAAQ,MAAM,EAAE,GAAG;QAE3B,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACtB,IAAI,UAAU,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE;gBAC7C,CAAA,GAAA,sMAAA,CAAA,UAAO,AAAD,EAAE;gBACR,QAAQ,QAAQ,OAAO,OAAO,CAAC,EAAE;YACnC;QACF;IACF;IAEA,SAAS,WAAW,KAAK;QACvB,IAAI,UAAU,MAAM,cAAc,EAC9B,IAAI,QAAQ,MAAM,EAAE,GAAG;QAE3B,IAAI,aAAa,aAAa;QAC9B,cAAc,WAAW;YAAa,cAAc;QAAM,GAAG,MAAM,4BAA4B;QAC/F,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACtB,IAAI,UAAU,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE;gBAC7C,CAAA,GAAA,sMAAA,CAAA,gBAAa,AAAD,EAAE;gBACd,QAAQ,OAAO,OAAO,OAAO,CAAC,EAAE;YAClC;QACF;IACF;IAEA,SAAS,YAAY,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK;QAC/D,IAAI,WAAW,UAAU,IAAI,IACzB,IAAI,CAAA,GAAA,sPAAA,CAAA,UAAO,AAAD,EAAE,SAAS,OAAO,YAAY,IAAI,IAC5C;QAEJ,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,MAAM,IAAI,oMAAA,CAAA,UAAS,CAAC,eAAe;YACrD,aAAa;YACb,QAAQ;YACR;YACA;YACA,GAAG,CAAC,CAAC,EAAE;YACP,GAAG,CAAC,CAAC,EAAE;YACP,IAAI;YACJ,IAAI;YACJ;QACF,IAAI,EAAE,KAAK,MAAM;QAEnB,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI;QACnB,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI;QAEnB,OAAO,SAAS,QAAQ,IAAI,EAAE,KAAK,EAAE,KAAK;YACxC,IAAI,KAAK,GAAG;YACZ,OAAQ;gBACN,KAAK;oBAAS,QAAQ,CAAC,WAAW,GAAG,SAAS,IAAI;oBAAU;gBAC5D,KAAK;oBAAO,OAAO,QAAQ,CAAC,WAAW,EAAE,EAAE,QAAQ,gBAAgB;gBACnE,KAAK;oBAAQ,IAAI,CAAA,GAAA,sPAAA,CAAA,UAAO,AAAD,EAAE,SAAS,OAAO,YAAY,IAAI;oBAAQ;YACnE;YACA,SAAS,IAAI,CACX,MACA,MACA,IAAI,oMAAA,CAAA,UAAS,CAAC,MAAM;gBAClB,aAAa;gBACb,SAAS;gBACT,QAAQ;gBACR;gBACA,QAAQ;gBACR,GAAG,CAAC,CAAC,EAAE,GAAG;gBACV,GAAG,CAAC,CAAC,EAAE,GAAG;gBACV,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;gBAChB,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;gBAChB;YACF,IACA;QAEJ;IACF;IAEA,KAAK,MAAM,GAAG,SAAS,CAAC;QACtB,OAAO,UAAU,MAAM,GAAG,CAAC,SAAS,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,uMAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,CAAC,IAAI,IAAI,IAAI;IAC3F;IAEA,KAAK,SAAS,GAAG,SAAS,CAAC;QACzB,OAAO,UAAU,MAAM,GAAG,CAAC,YAAY,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,uMAAA,CAAA,UAAQ,AAAD,EAAE,IAAI,IAAI,IAAI;IAC5F;IAEA,KAAK,OAAO,GAAG,SAAS,CAAC;QACvB,OAAO,UAAU,MAAM,GAAG,CAAC,UAAU,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,uMAAA,CAAA,UAAQ,AAAD,EAAE,IAAI,IAAI,IAAI;IAC1F;IAEA,KAAK,SAAS,GAAG,SAAS,CAAC;QACzB,OAAO,UAAU,MAAM,GAAG,CAAC,YAAY,OAAO,MAAM,aAAa,IAAI,CAAA,GAAA,uMAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,CAAC,IAAI,IAAI,IAAI;IAC9F;IAEA,KAAK,EAAE,GAAG;QACR,IAAI,QAAQ,UAAU,EAAE,CAAC,KAAK,CAAC,WAAW;QAC1C,OAAO,UAAU,YAAY,OAAO;IACtC;IAEA,KAAK,aAAa,GAAG,SAAS,CAAC;QAC7B,OAAO,UAAU,MAAM,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI,CAAC;IAC9E;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6478, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}