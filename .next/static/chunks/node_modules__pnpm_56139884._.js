(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules__pnpm_56139884._.js", {

"[project]/node_modules/.pnpm/scheduler@0.25.0/node_modules/scheduler/cjs/scheduler.development.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * scheduler.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function performWorkUntilDeadline() {
        if (isMessageLoopRunning) {
            var currentTime = exports.unstable_now();
            startTime = currentTime;
            var hasMoreWork = !0;
            try {
                a: {
                    isHostCallbackScheduled = !1;
                    isHostTimeoutScheduled && (isHostTimeoutScheduled = !1, localClearTimeout(taskTimeoutID), taskTimeoutID = -1);
                    isPerformingWork = !0;
                    var previousPriorityLevel = currentPriorityLevel;
                    try {
                        b: {
                            advanceTimers(currentTime);
                            for(currentTask = peek(taskQueue); null !== currentTask && !(currentTask.expirationTime > currentTime && shouldYieldToHost());){
                                var callback = currentTask.callback;
                                if ("function" === typeof callback) {
                                    currentTask.callback = null;
                                    currentPriorityLevel = currentTask.priorityLevel;
                                    var continuationCallback = callback(currentTask.expirationTime <= currentTime);
                                    currentTime = exports.unstable_now();
                                    if ("function" === typeof continuationCallback) {
                                        currentTask.callback = continuationCallback;
                                        advanceTimers(currentTime);
                                        hasMoreWork = !0;
                                        break b;
                                    }
                                    currentTask === peek(taskQueue) && pop(taskQueue);
                                    advanceTimers(currentTime);
                                } else pop(taskQueue);
                                currentTask = peek(taskQueue);
                            }
                            if (null !== currentTask) hasMoreWork = !0;
                            else {
                                var firstTimer = peek(timerQueue);
                                null !== firstTimer && requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);
                                hasMoreWork = !1;
                            }
                        }
                        break a;
                    } finally{
                        currentTask = null, currentPriorityLevel = previousPriorityLevel, isPerformingWork = !1;
                    }
                    hasMoreWork = void 0;
                }
            } finally{
                hasMoreWork ? schedulePerformWorkUntilDeadline() : isMessageLoopRunning = !1;
            }
        }
    }
    function push(heap, node) {
        var index = heap.length;
        heap.push(node);
        a: for(; 0 < index;){
            var parentIndex = index - 1 >>> 1, parent = heap[parentIndex];
            if (0 < compare(parent, node)) heap[parentIndex] = node, heap[index] = parent, index = parentIndex;
            else break a;
        }
    }
    function peek(heap) {
        return 0 === heap.length ? null : heap[0];
    }
    function pop(heap) {
        if (0 === heap.length) return null;
        var first = heap[0], last = heap.pop();
        if (last !== first) {
            heap[0] = last;
            a: for(var index = 0, length = heap.length, halfLength = length >>> 1; index < halfLength;){
                var leftIndex = 2 * (index + 1) - 1, left = heap[leftIndex], rightIndex = leftIndex + 1, right = heap[rightIndex];
                if (0 > compare(left, last)) rightIndex < length && 0 > compare(right, left) ? (heap[index] = right, heap[rightIndex] = last, index = rightIndex) : (heap[index] = left, heap[leftIndex] = last, index = leftIndex);
                else if (rightIndex < length && 0 > compare(right, last)) heap[index] = right, heap[rightIndex] = last, index = rightIndex;
                else break a;
            }
        }
        return first;
    }
    function compare(a, b) {
        var diff = a.sortIndex - b.sortIndex;
        return 0 !== diff ? diff : a.id - b.id;
    }
    function advanceTimers(currentTime) {
        for(var timer = peek(timerQueue); null !== timer;){
            if (null === timer.callback) pop(timerQueue);
            else if (timer.startTime <= currentTime) pop(timerQueue), timer.sortIndex = timer.expirationTime, push(taskQueue, timer);
            else break;
            timer = peek(timerQueue);
        }
    }
    function handleTimeout(currentTime) {
        isHostTimeoutScheduled = !1;
        advanceTimers(currentTime);
        if (!isHostCallbackScheduled) if (null !== peek(taskQueue)) isHostCallbackScheduled = !0, requestHostCallback();
        else {
            var firstTimer = peek(timerQueue);
            null !== firstTimer && requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);
        }
    }
    function shouldYieldToHost() {
        return exports.unstable_now() - startTime < frameInterval ? !1 : !0;
    }
    function requestHostCallback() {
        isMessageLoopRunning || (isMessageLoopRunning = !0, schedulePerformWorkUntilDeadline());
    }
    function requestHostTimeout(callback, ms) {
        taskTimeoutID = localSetTimeout(function() {
            callback(exports.unstable_now());
        }, ms);
    }
    "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());
    exports.unstable_now = void 0;
    if ("object" === typeof performance && "function" === typeof performance.now) {
        var localPerformance = performance;
        exports.unstable_now = function() {
            return localPerformance.now();
        };
    } else {
        var localDate = Date, initialTime = localDate.now();
        exports.unstable_now = function() {
            return localDate.now() - initialTime;
        };
    }
    var taskQueue = [], timerQueue = [], taskIdCounter = 1, currentTask = null, currentPriorityLevel = 3, isPerformingWork = !1, isHostCallbackScheduled = !1, isHostTimeoutScheduled = !1, localSetTimeout = "function" === typeof setTimeout ? setTimeout : null, localClearTimeout = "function" === typeof clearTimeout ? clearTimeout : null, localSetImmediate = "undefined" !== typeof setImmediate ? setImmediate : null, isMessageLoopRunning = !1, taskTimeoutID = -1, frameInterval = 5, startTime = -1;
    if ("function" === typeof localSetImmediate) var schedulePerformWorkUntilDeadline = function() {
        localSetImmediate(performWorkUntilDeadline);
    };
    else if ("undefined" !== typeof MessageChannel) {
        var channel = new MessageChannel(), port = channel.port2;
        channel.port1.onmessage = performWorkUntilDeadline;
        schedulePerformWorkUntilDeadline = function() {
            port.postMessage(null);
        };
    } else schedulePerformWorkUntilDeadline = function() {
        localSetTimeout(performWorkUntilDeadline, 0);
    };
    exports.unstable_IdlePriority = 5;
    exports.unstable_ImmediatePriority = 1;
    exports.unstable_LowPriority = 4;
    exports.unstable_NormalPriority = 3;
    exports.unstable_Profiling = null;
    exports.unstable_UserBlockingPriority = 2;
    exports.unstable_cancelCallback = function(task) {
        task.callback = null;
    };
    exports.unstable_continueExecution = function() {
        isHostCallbackScheduled || isPerformingWork || (isHostCallbackScheduled = !0, requestHostCallback());
    };
    exports.unstable_forceFrameRate = function(fps) {
        0 > fps || 125 < fps ? console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported") : frameInterval = 0 < fps ? Math.floor(1e3 / fps) : 5;
    };
    exports.unstable_getCurrentPriorityLevel = function() {
        return currentPriorityLevel;
    };
    exports.unstable_getFirstCallbackNode = function() {
        return peek(taskQueue);
    };
    exports.unstable_next = function(eventHandler) {
        switch(currentPriorityLevel){
            case 1:
            case 2:
            case 3:
                var priorityLevel = 3;
                break;
            default:
                priorityLevel = currentPriorityLevel;
        }
        var previousPriorityLevel = currentPriorityLevel;
        currentPriorityLevel = priorityLevel;
        try {
            return eventHandler();
        } finally{
            currentPriorityLevel = previousPriorityLevel;
        }
    };
    exports.unstable_pauseExecution = function() {};
    exports.unstable_requestPaint = function() {};
    exports.unstable_runWithPriority = function(priorityLevel, eventHandler) {
        switch(priorityLevel){
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
                break;
            default:
                priorityLevel = 3;
        }
        var previousPriorityLevel = currentPriorityLevel;
        currentPriorityLevel = priorityLevel;
        try {
            return eventHandler();
        } finally{
            currentPriorityLevel = previousPriorityLevel;
        }
    };
    exports.unstable_scheduleCallback = function(priorityLevel, callback, options) {
        var currentTime = exports.unstable_now();
        "object" === typeof options && null !== options ? (options = options.delay, options = "number" === typeof options && 0 < options ? currentTime + options : currentTime) : options = currentTime;
        switch(priorityLevel){
            case 1:
                var timeout = -1;
                break;
            case 2:
                timeout = 250;
                break;
            case 5:
                timeout = 1073741823;
                break;
            case 4:
                timeout = 1e4;
                break;
            default:
                timeout = 5e3;
        }
        timeout = options + timeout;
        priorityLevel = {
            id: taskIdCounter++,
            callback: callback,
            priorityLevel: priorityLevel,
            startTime: options,
            expirationTime: timeout,
            sortIndex: -1
        };
        options > currentTime ? (priorityLevel.sortIndex = options, push(timerQueue, priorityLevel), null === peek(taskQueue) && priorityLevel === peek(timerQueue) && (isHostTimeoutScheduled ? (localClearTimeout(taskTimeoutID), taskTimeoutID = -1) : isHostTimeoutScheduled = !0, requestHostTimeout(handleTimeout, options - currentTime))) : (priorityLevel.sortIndex = timeout, push(taskQueue, priorityLevel), isHostCallbackScheduled || isPerformingWork || (isHostCallbackScheduled = !0, requestHostCallback()));
        return priorityLevel;
    };
    exports.unstable_shouldYield = shouldYieldToHost;
    exports.unstable_wrapCallback = function(callback) {
        var parentPriorityLevel = currentPriorityLevel;
        return function() {
            var previousPriorityLevel = currentPriorityLevel;
            currentPriorityLevel = parentPriorityLevel;
            try {
                return callback.apply(this, arguments);
            } finally{
                currentPriorityLevel = previousPriorityLevel;
            }
        };
    };
    "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());
}();
}}),
"[project]/node_modules/.pnpm/scheduler@0.25.0/node_modules/scheduler/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/.pnpm/scheduler@0.25.0/node_modules/scheduler/cjs/scheduler.development.js [app-client] (ecmascript)");
}
}}),
"[project]/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.0.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * use-sync-external-store-shim.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function is(x, y) {
        return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;
    }
    function useSyncExternalStore$2(subscribe, getSnapshot) {
        didWarnOld18Alpha || void 0 === React.startTransition || (didWarnOld18Alpha = !0, console.error("You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release."));
        var value = getSnapshot();
        if (!didWarnUncachedGetSnapshot) {
            var cachedValue = getSnapshot();
            objectIs(value, cachedValue) || (console.error("The result of getSnapshot should be cached to avoid an infinite loop"), didWarnUncachedGetSnapshot = !0);
        }
        cachedValue = useState({
            inst: {
                value: value,
                getSnapshot: getSnapshot
            }
        });
        var inst = cachedValue[0].inst, forceUpdate = cachedValue[1];
        useLayoutEffect({
            "useSyncExternalStore$2.useLayoutEffect": function() {
                inst.value = value;
                inst.getSnapshot = getSnapshot;
                checkIfSnapshotChanged(inst) && forceUpdate({
                    inst: inst
                });
            }
        }["useSyncExternalStore$2.useLayoutEffect"], [
            subscribe,
            value,
            getSnapshot
        ]);
        useEffect({
            "useSyncExternalStore$2.useEffect": function() {
                checkIfSnapshotChanged(inst) && forceUpdate({
                    inst: inst
                });
                return subscribe({
                    "useSyncExternalStore$2.useEffect": function() {
                        checkIfSnapshotChanged(inst) && forceUpdate({
                            inst: inst
                        });
                    }
                }["useSyncExternalStore$2.useEffect"]);
            }
        }["useSyncExternalStore$2.useEffect"], [
            subscribe
        ]);
        useDebugValue(value);
        return value;
    }
    function checkIfSnapshotChanged(inst) {
        var latestGetSnapshot = inst.getSnapshot;
        inst = inst.value;
        try {
            var nextValue = latestGetSnapshot();
            return !objectIs(inst, nextValue);
        } catch (error) {
            return !0;
        }
    }
    function useSyncExternalStore$1(subscribe, getSnapshot) {
        return getSnapshot();
    }
    "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());
    var React = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), objectIs = "function" === typeof Object.is ? Object.is : is, useState = React.useState, useEffect = React.useEffect, useLayoutEffect = React.useLayoutEffect, useDebugValue = React.useDebugValue, didWarnOld18Alpha = !1, didWarnUncachedGetSnapshot = !1, shim = "undefined" === typeof window || "undefined" === typeof window.document || "undefined" === typeof window.document.createElement ? useSyncExternalStore$1 : useSyncExternalStore$2;
    exports.useSyncExternalStore = void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;
    "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());
}();
}}),
"[project]/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.0.0/node_modules/use-sync-external-store/shim/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.0.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js [app-client] (ecmascript)");
}
}}),
"[project]/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.0.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * use-sync-external-store-shim/with-selector.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function is(x, y) {
        return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;
    }
    "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());
    var React = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), shim = __turbopack_context__.r("[project]/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.0.0/node_modules/use-sync-external-store/shim/index.js [app-client] (ecmascript)"), objectIs = "function" === typeof Object.is ? Object.is : is, useSyncExternalStore = shim.useSyncExternalStore, useRef = React.useRef, useEffect = React.useEffect, useMemo = React.useMemo, useDebugValue = React.useDebugValue;
    exports.useSyncExternalStoreWithSelector = function(subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {
        var instRef = useRef(null);
        if (null === instRef.current) {
            var inst = {
                hasValue: !1,
                value: null
            };
            instRef.current = inst;
        } else inst = instRef.current;
        instRef = useMemo(function() {
            function memoizedSelector(nextSnapshot) {
                if (!hasMemo) {
                    hasMemo = !0;
                    memoizedSnapshot = nextSnapshot;
                    nextSnapshot = selector(nextSnapshot);
                    if (void 0 !== isEqual && inst.hasValue) {
                        var currentSelection = inst.value;
                        if (isEqual(currentSelection, nextSnapshot)) return memoizedSelection = currentSelection;
                    }
                    return memoizedSelection = nextSnapshot;
                }
                currentSelection = memoizedSelection;
                if (objectIs(memoizedSnapshot, nextSnapshot)) return currentSelection;
                var nextSelection = selector(nextSnapshot);
                if (void 0 !== isEqual && isEqual(currentSelection, nextSelection)) return memoizedSnapshot = nextSnapshot, currentSelection;
                memoizedSnapshot = nextSnapshot;
                return memoizedSelection = nextSelection;
            }
            var hasMemo = !1, memoizedSnapshot, memoizedSelection, maybeGetServerSnapshot = void 0 === getServerSnapshot ? null : getServerSnapshot;
            return [
                function() {
                    return memoizedSelector(getSnapshot());
                },
                null === maybeGetServerSnapshot ? void 0 : function() {
                    return memoizedSelector(maybeGetServerSnapshot());
                }
            ];
        }, [
            getSnapshot,
            getServerSnapshot,
            selector,
            isEqual
        ]);
        var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);
        useEffect(function() {
            inst.hasValue = !0;
            inst.value = value;
        }, [
            value
        ]);
        useDebugValue(value);
        return value;
    };
    "undefined" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && "function" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());
}();
}}),
"[project]/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.0.0/node_modules/use-sync-external-store/shim/with-selector.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.0.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js [app-client] (ecmascript)");
}
}}),
"[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@19.0.10_react@19.0.0_use-sync-external-store@1.5.0_react@19.0.0_/node_modules/zustand/esm/vanilla.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createStore": (()=>createStore)
});
const createStoreImpl = (createState)=>{
    let state;
    const listeners = /* @__PURE__ */ new Set();
    const setState = (partial, replace)=>{
        const nextState = typeof partial === "function" ? partial(state) : partial;
        if (!Object.is(nextState, state)) {
            const previousState = state;
            state = (replace != null ? replace : typeof nextState !== "object" || nextState === null) ? nextState : Object.assign({}, state, nextState);
            listeners.forEach((listener)=>listener(state, previousState));
        }
    };
    const getState = ()=>state;
    const getInitialState = ()=>initialState;
    const subscribe = (listener)=>{
        listeners.add(listener);
        return ()=>listeners.delete(listener);
    };
    const api = {
        setState,
        getState,
        getInitialState,
        subscribe
    };
    const initialState = state = createState(setState, getState, api);
    return api;
};
const createStore = (createState)=>createState ? createStoreImpl(createState) : createStoreImpl;
;
}}),
"[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@19.0.10_react@19.0.0_use-sync-external-store@1.5.0_react@19.0.0_/node_modules/zustand/esm/traditional.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createWithEqualityFn": (()=>createWithEqualityFn),
    "useStoreWithEqualityFn": (()=>useStoreWithEqualityFn)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$use$2d$sync$2d$external$2d$store$40$1$2e$5$2e$0_react$40$19$2e$0$2e$0$2f$node_modules$2f$use$2d$sync$2d$external$2d$store$2f$shim$2f$with$2d$selector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/use-sync-external-store@1.5.0_react@19.0.0/node_modules/use-sync-external-store/shim/with-selector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$19$2e$0$2e$10_react$40$19$2e$0$2e$0_use$2d$sync$2d$external$2d$store$40$1$2e$5$2e$0_react$40$19$2e$0$2e$0_$2f$node_modules$2f$zustand$2f$esm$2f$vanilla$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/zustand@5.0.5_@types+react@19.0.10_react@19.0.0_use-sync-external-store@1.5.0_react@19.0.0_/node_modules/zustand/esm/vanilla.mjs [app-client] (ecmascript)");
;
;
;
const { useSyncExternalStoreWithSelector } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$use$2d$sync$2d$external$2d$store$40$1$2e$5$2e$0_react$40$19$2e$0$2e$0$2f$node_modules$2f$use$2d$sync$2d$external$2d$store$2f$shim$2f$with$2d$selector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
const identity = (arg)=>arg;
function useStoreWithEqualityFn(api, selector = identity, equalityFn) {
    const slice = useSyncExternalStoreWithSelector(api.subscribe, api.getState, api.getInitialState, selector, equalityFn);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useDebugValue(slice);
    return slice;
}
const createWithEqualityFnImpl = (createState, defaultEqualityFn)=>{
    const api = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$zustand$40$5$2e$0$2e$5_$40$types$2b$react$40$19$2e$0$2e$10_react$40$19$2e$0$2e$0_use$2d$sync$2d$external$2d$store$40$1$2e$5$2e$0_react$40$19$2e$0$2e$0_$2f$node_modules$2f$zustand$2f$esm$2f$vanilla$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createStore"])(createState);
    const useBoundStoreWithEqualityFn = (selector, equalityFn = defaultEqualityFn)=>useStoreWithEqualityFn(api, selector, equalityFn);
    Object.assign(useBoundStoreWithEqualityFn, api);
    return useBoundStoreWithEqualityFn;
};
const createWithEqualityFn = (createState, defaultEqualityFn)=>createState ? createWithEqualityFnImpl(createState, defaultEqualityFn) : createWithEqualityFnImpl;
;
}}),
"[project]/node_modules/.pnpm/suspend-react@0.1.3_react@19.0.0/node_modules/suspend-react/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "clear": (()=>clear),
    "peek": (()=>peek),
    "preload": (()=>preload),
    "suspend": (()=>suspend)
});
const isPromise = (promise)=>typeof promise === 'object' && typeof promise.then === 'function';
const globalCache = [];
function shallowEqualArrays(arrA, arrB, equal = (a, b)=>a === b) {
    if (arrA === arrB) return true;
    if (!arrA || !arrB) return false;
    const len = arrA.length;
    if (arrB.length !== len) return false;
    for(let i = 0; i < len; i++)if (!equal(arrA[i], arrB[i])) return false;
    return true;
}
function query(fn, keys = null, preload = false, config = {}) {
    // If no keys were given, the function is the key
    if (keys === null) keys = [
        fn
    ];
    for (const entry of globalCache){
        // Find a match
        if (shallowEqualArrays(keys, entry.keys, entry.equal)) {
            // If we're pre-loading and the element is present, just return
            if (preload) return undefined; // If an error occurred, throw
            if (Object.prototype.hasOwnProperty.call(entry, 'error')) throw entry.error; // If a response was successful, return
            if (Object.prototype.hasOwnProperty.call(entry, 'response')) {
                if (config.lifespan && config.lifespan > 0) {
                    if (entry.timeout) clearTimeout(entry.timeout);
                    entry.timeout = setTimeout(entry.remove, config.lifespan);
                }
                return entry.response;
            } // If the promise is still unresolved, throw
            if (!preload) throw entry.promise;
        }
    } // The request is new or has changed.
    const entry = {
        keys,
        equal: config.equal,
        remove: ()=>{
            const index = globalCache.indexOf(entry);
            if (index !== -1) globalCache.splice(index, 1);
        },
        promise: (isPromise(fn) ? fn : fn(...keys) // When it resolves, store its value
        ).then((response)=>{
            entry.response = response; // Remove the entry in time if a lifespan was given
            if (config.lifespan && config.lifespan > 0) {
                entry.timeout = setTimeout(entry.remove, config.lifespan);
            }
        }) // Store caught errors, they will be thrown in the render-phase to bubble into an error-bound
        .catch((error)=>entry.error = error)
    }; // Register the entry
    globalCache.push(entry); // And throw the promise, this yields control back to React
    if (!preload) throw entry.promise;
    return undefined;
}
const suspend = (fn, keys, config)=>query(fn, keys, false, config);
const preload = (fn, keys, config)=>void query(fn, keys, true, config);
const peek = (keys)=>{
    var _globalCache$find;
    return (_globalCache$find = globalCache.find((entry)=>shallowEqualArrays(keys, entry.keys, entry.equal))) == null ? void 0 : _globalCache$find.response;
};
const clear = (keys)=>{
    if (keys === undefined || keys.length === 0) globalCache.splice(0, globalCache.length);
    else {
        const entry = globalCache.find((entry)=>shallowEqualArrays(keys, entry.keys, entry.equal));
        if (entry) entry.remove();
    }
};
;
}}),
"[project]/node_modules/.pnpm/its-fine@2.0.0_@types+react@19.0.10_react@19.0.0/node_modules/its-fine/dist/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FiberProvider": (()=>m),
    "traverseFiber": (()=>i),
    "useContainer": (()=>w),
    "useContextBridge": (()=>x),
    "useContextMap": (()=>h),
    "useFiber": (()=>c),
    "useNearestChild": (()=>v),
    "useNearestParent": (()=>y)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
const f = /* @__PURE__ */ (()=>{
    var e, t;
    return typeof window != "undefined" && (((e = window.document) == null ? void 0 : e.createElement) || ((t = window.navigator) == null ? void 0 : t.product) === "ReactNative");
})() ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"];
function i(e, t, r) {
    if (!e) return;
    if (r(e) === !0) return e;
    let n = t ? e.return : e.child;
    for(; n;){
        const u = i(n, t, r);
        if (u) return u;
        n = t ? null : n.sibling;
    }
}
function l(e) {
    try {
        return Object.defineProperties(e, {
            _currentRenderer: {
                get () {
                    return null;
                },
                set () {}
            },
            _currentRenderer2: {
                get () {
                    return null;
                },
                set () {}
            }
        });
    } catch (t) {
        return e;
    }
}
const a = /* @__PURE__ */ l(/* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null));
class m extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Component"] {
    render() {
        return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(a.Provider, {
            value: this._reactInternals
        }, this.props.children);
    }
}
function c() {
    const e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(a);
    if (e === null) throw new Error("its-fine: useFiber must be called within a <FiberProvider />!");
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useId"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "c.useMemo": ()=>{
            for (const n of [
                e,
                e == null ? void 0 : e.alternate
            ]){
                if (!n) continue;
                const u = i(n, !1, {
                    "c.useMemo.u": (d)=>{
                        let s = d.memoizedState;
                        for(; s;){
                            if (s.memoizedState === t) return !0;
                            s = s.next;
                        }
                    }
                }["c.useMemo.u"]);
                if (u) return u;
            }
        }
    }["c.useMemo"], [
        e,
        t
    ]);
}
function w() {
    const e = c(), t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "w.useMemo[t]": ()=>i(e, !0, {
                "w.useMemo[t]": (r)=>{
                    var n;
                    return ((n = r.stateNode) == null ? void 0 : n.containerInfo) != null;
                }
            }["w.useMemo[t]"])
    }["w.useMemo[t]"], [
        e
    ]);
    return t == null ? void 0 : t.stateNode.containerInfo;
}
function v(e) {
    const t = c(), r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(void 0);
    return f(()=>{
        var n;
        r.current = (n = i(t, !1, (u)=>typeof u.type == "string" && (e === void 0 || u.type === e))) == null ? void 0 : n.stateNode;
    }, [
        t
    ]), r;
}
function y(e) {
    const t = c(), r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(void 0);
    return f(()=>{
        var n;
        r.current = (n = i(t, !0, (u)=>typeof u.type == "string" && (e === void 0 || u.type === e))) == null ? void 0 : n.stateNode;
    }, [
        t
    ]), r;
}
const p = Symbol.for("react.context"), b = (e)=>e !== null && typeof e == "object" && "$$typeof" in e && e.$$typeof === p;
function h() {
    const e = c(), [t] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "h.useState": ()=>/* @__PURE__ */ new Map()
    }["h.useState"]);
    t.clear();
    let r = e;
    for(; r;){
        const n = r.type;
        b(n) && n !== a && !t.has(n) && t.set(n, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["use"])(l(n))), r = r.return;
    }
    return t;
}
function x() {
    const e = h();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "x.useMemo": ()=>Array.from(e.keys()).reduce({
                "x.useMemo": (t, r)=>({
                        "x.useMemo": (n)=>/* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(t, null, /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(r.Provider, {
                                ...n,
                                value: e.get(r)
                            }))
                    })["x.useMemo"]
            }["x.useMemo"], {
                "x.useMemo": (t)=>/* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(m, {
                        ...t
                    })
            }["x.useMemo"])
    }["x.useMemo"], [
        e
    ]);
}
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/.pnpm/react-use-measure@2.1.7_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/react-use-measure/dist/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>j)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function g(n, t) {
    let o;
    return (...i)=>{
        window.clearTimeout(o), o = window.setTimeout(()=>n(...i), t);
    };
}
function j({ debounce: n, scroll: t, polyfill: o, offsetSize: i } = {
    debounce: 0,
    scroll: !1,
    offsetSize: !1
}) {
    const a = o || (typeof window == "undefined" ? class {
    } : window.ResizeObserver);
    if (!a) throw new Error("This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills");
    const [c, h] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        left: 0,
        top: 0,
        width: 0,
        height: 0,
        bottom: 0,
        right: 0,
        x: 0,
        y: 0
    }), e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({
        element: null,
        scrollContainers: null,
        resizeObserver: null,
        lastBounds: c,
        orientationHandler: null
    }), d = n ? typeof n == "number" ? n : n.scroll : null, f = n ? typeof n == "number" ? n : n.resize : null, w = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(!1);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>(w.current = !0, ()=>void (w.current = !1)));
    const [z, m, s] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        const r = ()=>{
            if (!e.current.element) return;
            const { left: y, top: C, width: H, height: O, bottom: S, right: x, x: B, y: R } = e.current.element.getBoundingClientRect(), l = {
                left: y,
                top: C,
                width: H,
                height: O,
                bottom: S,
                right: x,
                x: B,
                y: R
            };
            e.current.element instanceof HTMLElement && i && (l.height = e.current.element.offsetHeight, l.width = e.current.element.offsetWidth), Object.freeze(l), w.current && !D(e.current.lastBounds, l) && h(e.current.lastBounds = l);
        };
        return [
            r,
            f ? g(r, f) : r,
            d ? g(r, d) : r
        ];
    }, [
        h,
        i,
        d,
        f
    ]);
    function v() {
        e.current.scrollContainers && (e.current.scrollContainers.forEach((r)=>r.removeEventListener("scroll", s, !0)), e.current.scrollContainers = null), e.current.resizeObserver && (e.current.resizeObserver.disconnect(), e.current.resizeObserver = null), e.current.orientationHandler && ("orientation" in screen && "removeEventListener" in screen.orientation ? screen.orientation.removeEventListener("change", e.current.orientationHandler) : "onorientationchange" in window && window.removeEventListener("orientationchange", e.current.orientationHandler));
    }
    function b() {
        e.current.element && (e.current.resizeObserver = new a(s), e.current.resizeObserver.observe(e.current.element), t && e.current.scrollContainers && e.current.scrollContainers.forEach((r)=>r.addEventListener("scroll", s, {
                capture: !0,
                passive: !0
            })), e.current.orientationHandler = ()=>{
            s();
        }, "orientation" in screen && "addEventListener" in screen.orientation ? screen.orientation.addEventListener("change", e.current.orientationHandler) : "onorientationchange" in window && window.addEventListener("orientationchange", e.current.orientationHandler));
    }
    const L = (r)=>{
        !r || r === e.current.element || (v(), e.current.element = r, e.current.scrollContainers = E(r), b());
    };
    return X(s, !!t), W(m), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        v(), b();
    }, [
        t,
        s,
        m
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>v, []), [
        L,
        c,
        z
    ];
}
function W(n) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const t = n;
        return window.addEventListener("resize", t), ()=>void window.removeEventListener("resize", t);
    }, [
        n
    ]);
}
function X(n, t) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (t) {
            const o = n;
            return window.addEventListener("scroll", o, {
                capture: !0,
                passive: !0
            }), ()=>void window.removeEventListener("scroll", o, !0);
        }
    }, [
        n,
        t
    ]);
}
function E(n) {
    const t = [];
    if (!n || n === document.body) return t;
    const { overflow: o, overflowX: i, overflowY: a } = window.getComputedStyle(n);
    return [
        o,
        i,
        a
    ].some((c)=>c === "auto" || c === "scroll") && t.push(n), [
        ...t,
        ...E(n.parentElement)
    ];
}
const k = [
    "x",
    "y",
    "top",
    "bottom",
    "left",
    "right",
    "width",
    "height"
], D = (n, t)=>k.every((o)=>n[o] === t[o]);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/.pnpm/@react-spring+rafz@10.0.1/node_modules/@react-spring/rafz/dist/cjs/react-spring_rafz.development.cjs [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
// src/index.ts
var src_exports = {};
__export(src_exports, {
    __raf: ()=>__raf,
    raf: ()=>raf
});
module.exports = __toCommonJS(src_exports);
var updateQueue = makeQueue();
var raf = (fn)=>schedule(fn, updateQueue);
var writeQueue = makeQueue();
raf.write = (fn)=>schedule(fn, writeQueue);
var onStartQueue = makeQueue();
raf.onStart = (fn)=>schedule(fn, onStartQueue);
var onFrameQueue = makeQueue();
raf.onFrame = (fn)=>schedule(fn, onFrameQueue);
var onFinishQueue = makeQueue();
raf.onFinish = (fn)=>schedule(fn, onFinishQueue);
var timeouts = [];
raf.setTimeout = (handler, ms)=>{
    const time = raf.now() + ms;
    const cancel = ()=>{
        const i = timeouts.findIndex((t)=>t.cancel == cancel);
        if (~i) timeouts.splice(i, 1);
        pendingCount -= ~i ? 1 : 0;
    };
    const timeout = {
        time,
        handler,
        cancel
    };
    timeouts.splice(findTimeout(time), 0, timeout);
    pendingCount += 1;
    start();
    return timeout;
};
var findTimeout = (time)=>~(~timeouts.findIndex((t)=>t.time > time) || ~timeouts.length);
raf.cancel = (fn)=>{
    onStartQueue.delete(fn);
    onFrameQueue.delete(fn);
    onFinishQueue.delete(fn);
    updateQueue.delete(fn);
    writeQueue.delete(fn);
};
raf.sync = (fn)=>{
    sync = true;
    raf.batchedUpdates(fn);
    sync = false;
};
raf.throttle = (fn)=>{
    let lastArgs;
    function queuedFn() {
        try {
            fn(...lastArgs);
        } finally{
            lastArgs = null;
        }
    }
    function throttled(...args) {
        lastArgs = args;
        raf.onStart(queuedFn);
    }
    throttled.handler = fn;
    throttled.cancel = ()=>{
        onStartQueue.delete(queuedFn);
        lastArgs = null;
    };
    return throttled;
};
var nativeRaf = typeof window != "undefined" ? window.requestAnimationFrame : // eslint-disable-next-line @typescript-eslint/no-empty-function
()=>{};
raf.use = (impl)=>nativeRaf = impl;
raf.now = typeof performance != "undefined" ? ()=>performance.now() : Date.now;
raf.batchedUpdates = (fn)=>fn();
raf.catch = console.error;
raf.frameLoop = "always";
raf.advance = ()=>{
    if (raf.frameLoop !== "demand") {
        console.warn("Cannot call the manual advancement of rafz whilst frameLoop is not set as demand");
    } else {
        update();
    }
};
var ts = -1;
var pendingCount = 0;
var sync = false;
function schedule(fn, queue) {
    if (sync) {
        queue.delete(fn);
        fn(0);
    } else {
        queue.add(fn);
        start();
    }
}
function start() {
    if (ts < 0) {
        ts = 0;
        if (raf.frameLoop !== "demand") {
            nativeRaf(loop);
        }
    }
}
function stop() {
    ts = -1;
}
function loop() {
    if (~ts) {
        nativeRaf(loop);
        raf.batchedUpdates(update);
    }
}
function update() {
    const prevTs = ts;
    ts = raf.now();
    const count = findTimeout(ts);
    if (count) {
        eachSafely(timeouts.splice(0, count), (t)=>t.handler());
        pendingCount -= count;
    }
    if (!pendingCount) {
        stop();
        return;
    }
    onStartQueue.flush();
    updateQueue.flush(prevTs ? Math.min(64, ts - prevTs) : 16.667);
    onFrameQueue.flush();
    writeQueue.flush();
    onFinishQueue.flush();
}
function makeQueue() {
    let next = /* @__PURE__ */ new Set();
    let current = next;
    return {
        add (fn) {
            pendingCount += current == next && !next.has(fn) ? 1 : 0;
            next.add(fn);
        },
        delete (fn) {
            pendingCount -= current == next && next.has(fn) ? 1 : 0;
            return next.delete(fn);
        },
        flush (arg) {
            if (current.size) {
                next = /* @__PURE__ */ new Set();
                pendingCount -= current.size;
                eachSafely(current, (fn)=>fn(arg) && next.add(fn));
                pendingCount += next.size;
                current = next;
            }
        }
    };
}
function eachSafely(values, each) {
    values.forEach((value)=>{
        try {
            each(value);
        } catch (e) {
            raf.catch(e);
        }
    });
}
var __raf = {
    /** The number of pending tasks */ count () {
        return pendingCount;
    },
    /** Whether there's a raf update loop running */ isRunning () {
        return ts >= 0;
    },
    /** Clear internal state. Never call from update loop! */ clear () {
        ts = -1;
        timeouts = [];
        onStartQueue = makeQueue();
        updateQueue = makeQueue();
        onFrameQueue = makeQueue();
        writeQueue = makeQueue();
        onFinishQueue = makeQueue();
        pendingCount = 0;
    }
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    __raf,
    raf
});
}}),
"[project]/node_modules/.pnpm/@react-spring+rafz@10.0.1/node_modules/@react-spring/rafz/dist/cjs/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+rafz@10.0.1/node_modules/@react-spring/rafz/dist/cjs/react-spring_rafz.development.cjs [app-client] (ecmascript)");
}
}}),
"[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/react-spring_shared.development.cjs [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to2, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to2, key) && key !== except) __defProp(to2, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to2;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
// src/index.ts
var src_exports = {};
__export(src_exports, {
    FluidValue: ()=>FluidValue,
    Globals: ()=>globals_exports,
    addFluidObserver: ()=>addFluidObserver,
    callFluidObserver: ()=>callFluidObserver,
    callFluidObservers: ()=>callFluidObservers,
    clamp: ()=>clamp,
    colorToRgba: ()=>colorToRgba,
    colors: ()=>colors2,
    createInterpolator: ()=>createInterpolator,
    createStringInterpolator: ()=>createStringInterpolator2,
    defineHidden: ()=>defineHidden,
    deprecateDirectCall: ()=>deprecateDirectCall,
    deprecateInterpolate: ()=>deprecateInterpolate,
    each: ()=>each,
    eachProp: ()=>eachProp,
    easings: ()=>easings,
    flush: ()=>flush,
    flushCalls: ()=>flushCalls,
    frameLoop: ()=>frameLoop,
    getFluidObservers: ()=>getFluidObservers,
    getFluidValue: ()=>getFluidValue,
    hasFluidValue: ()=>hasFluidValue,
    hex3: ()=>hex3,
    hex4: ()=>hex4,
    hex6: ()=>hex6,
    hex8: ()=>hex8,
    hsl: ()=>hsl,
    hsla: ()=>hsla,
    is: ()=>is,
    isAnimatedString: ()=>isAnimatedString,
    isEqual: ()=>isEqual,
    isSSR: ()=>isSSR,
    noop: ()=>noop,
    onResize: ()=>onResize,
    onScroll: ()=>onScroll,
    once: ()=>once,
    prefix: ()=>prefix,
    raf: ()=>import_rafz4.raf,
    removeFluidObserver: ()=>removeFluidObserver,
    rgb: ()=>rgb,
    rgba: ()=>rgba,
    setFluidGetter: ()=>setFluidGetter,
    toArray: ()=>toArray,
    useConstant: ()=>useConstant,
    useForceUpdate: ()=>useForceUpdate,
    useIsomorphicLayoutEffect: ()=>useIsomorphicLayoutEffect,
    useMemoOne: ()=>useMemoOne,
    useOnce: ()=>useOnce,
    usePrev: ()=>usePrev,
    useReducedMotion: ()=>useReducedMotion
});
module.exports = __toCommonJS(src_exports);
// src/globals.ts
var globals_exports = {};
__export(globals_exports, {
    assign: ()=>assign,
    colors: ()=>colors,
    createStringInterpolator: ()=>createStringInterpolator,
    skipAnimation: ()=>skipAnimation,
    to: ()=>to,
    willAdvance: ()=>willAdvance
});
var import_rafz = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+rafz@10.0.1/node_modules/@react-spring/rafz/dist/cjs/index.js [app-client] (ecmascript)");
// src/helpers.ts
function noop() {}
var defineHidden = (obj, key, value)=>Object.defineProperty(obj, key, {
        value,
        writable: true,
        configurable: true
    });
var is = {
    arr: Array.isArray,
    obj: (a)=>!!a && a.constructor.name === "Object",
    fun: (a)=>typeof a === "function",
    str: (a)=>typeof a === "string",
    num: (a)=>typeof a === "number",
    und: (a)=>a === void 0
};
function isEqual(a, b) {
    if (is.arr(a)) {
        if (!is.arr(b) || a.length !== b.length) return false;
        for(let i = 0; i < a.length; i++){
            if (a[i] !== b[i]) return false;
        }
        return true;
    }
    return a === b;
}
var each = (obj, fn)=>obj.forEach(fn);
function eachProp(obj, fn, ctx) {
    if (is.arr(obj)) {
        for(let i = 0; i < obj.length; i++){
            fn.call(ctx, obj[i], `${i}`);
        }
        return;
    }
    for(const key in obj){
        if (obj.hasOwnProperty(key)) {
            fn.call(ctx, obj[key], key);
        }
    }
}
var toArray = (a)=>is.und(a) ? [] : is.arr(a) ? a : [
        a
    ];
function flush(queue, iterator) {
    if (queue.size) {
        const items = Array.from(queue);
        queue.clear();
        each(items, iterator);
    }
}
var flushCalls = (queue, ...args)=>flush(queue, (fn)=>fn(...args));
var isSSR = ()=>typeof window === "undefined" || !window.navigator || /ServerSideRendering|^Deno\//.test(window.navigator.userAgent);
// src/globals.ts
var createStringInterpolator;
var to;
var colors = null;
var skipAnimation = false;
var willAdvance = noop;
var assign = (globals)=>{
    if (globals.to) to = globals.to;
    if (globals.now) import_rafz.raf.now = globals.now;
    if (globals.colors !== void 0) colors = globals.colors;
    if (globals.skipAnimation != null) skipAnimation = globals.skipAnimation;
    if (globals.createStringInterpolator) createStringInterpolator = globals.createStringInterpolator;
    if (globals.requestAnimationFrame) import_rafz.raf.use(globals.requestAnimationFrame);
    if (globals.batchedUpdates) import_rafz.raf.batchedUpdates = globals.batchedUpdates;
    if (globals.willAdvance) willAdvance = globals.willAdvance;
    if (globals.frameLoop) import_rafz.raf.frameLoop = globals.frameLoop;
};
// src/FrameLoop.ts
var import_rafz2 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+rafz@10.0.1/node_modules/@react-spring/rafz/dist/cjs/index.js [app-client] (ecmascript)");
var startQueue = /* @__PURE__ */ new Set();
var currentFrame = [];
var prevFrame = [];
var priority = 0;
var frameLoop = {
    get idle () {
        return !startQueue.size && !currentFrame.length;
    },
    /** Advance the given animation on every frame until idle. */ start (animation) {
        if (priority > animation.priority) {
            startQueue.add(animation);
            import_rafz2.raf.onStart(flushStartQueue);
        } else {
            startSafely(animation);
            (0, import_rafz2.raf)(advance);
        }
    },
    /** Advance all animations by the given time. */ advance,
    /** Call this when an animation's priority changes. */ sort (animation) {
        if (priority) {
            import_rafz2.raf.onFrame(()=>frameLoop.sort(animation));
        } else {
            const prevIndex = currentFrame.indexOf(animation);
            if (~prevIndex) {
                currentFrame.splice(prevIndex, 1);
                startUnsafely(animation);
            }
        }
    },
    /**
   * Clear all animations. For testing purposes.
   *
   * ☠️ Never call this from within the frameloop.
   */ clear () {
        currentFrame = [];
        startQueue.clear();
    }
};
function flushStartQueue() {
    startQueue.forEach(startSafely);
    startQueue.clear();
    (0, import_rafz2.raf)(advance);
}
function startSafely(animation) {
    if (!currentFrame.includes(animation)) startUnsafely(animation);
}
function startUnsafely(animation) {
    currentFrame.splice(findIndex(currentFrame, (other)=>other.priority > animation.priority), 0, animation);
}
function advance(dt) {
    const nextFrame = prevFrame;
    for(let i = 0; i < currentFrame.length; i++){
        const animation = currentFrame[i];
        priority = animation.priority;
        if (!animation.idle) {
            willAdvance(animation);
            animation.advance(dt);
            if (!animation.idle) {
                nextFrame.push(animation);
            }
        }
    }
    priority = 0;
    prevFrame = currentFrame;
    prevFrame.length = 0;
    currentFrame = nextFrame;
    return currentFrame.length > 0;
}
function findIndex(arr, test) {
    const index = arr.findIndex(test);
    return index < 0 ? arr.length : index;
}
// src/clamp.ts
var clamp = (min, max, v)=>Math.min(Math.max(v, min), max);
// src/colors.ts
var colors2 = {
    transparent: 0,
    aliceblue: 4042850303,
    antiquewhite: 4209760255,
    aqua: 16777215,
    aquamarine: 2147472639,
    azure: 4043309055,
    beige: 4126530815,
    bisque: 4293182719,
    black: 255,
    blanchedalmond: 4293643775,
    blue: 65535,
    blueviolet: 2318131967,
    brown: 2771004159,
    burlywood: 3736635391,
    burntsienna: 3934150143,
    cadetblue: 1604231423,
    chartreuse: 2147418367,
    chocolate: 3530104575,
    coral: 4286533887,
    cornflowerblue: 1687547391,
    cornsilk: 4294499583,
    crimson: 3692313855,
    cyan: 16777215,
    darkblue: 35839,
    darkcyan: 9145343,
    darkgoldenrod: 3095792639,
    darkgray: 2846468607,
    darkgreen: 6553855,
    darkgrey: 2846468607,
    darkkhaki: 3182914559,
    darkmagenta: 2332068863,
    darkolivegreen: 1433087999,
    darkorange: 4287365375,
    darkorchid: 2570243327,
    darkred: 2332033279,
    darksalmon: 3918953215,
    darkseagreen: 2411499519,
    darkslateblue: 1211993087,
    darkslategray: 793726975,
    darkslategrey: 793726975,
    darkturquoise: 13554175,
    darkviolet: 2483082239,
    deeppink: 4279538687,
    deepskyblue: 12582911,
    dimgray: 1768516095,
    dimgrey: 1768516095,
    dodgerblue: 512819199,
    firebrick: 2988581631,
    floralwhite: 4294635775,
    forestgreen: 579543807,
    fuchsia: 4278255615,
    gainsboro: 3705462015,
    ghostwhite: 4177068031,
    gold: 4292280575,
    goldenrod: 3668254975,
    gray: 2155905279,
    green: 8388863,
    greenyellow: 2919182335,
    grey: 2155905279,
    honeydew: 4043305215,
    hotpink: 4285117695,
    indianred: 3445382399,
    indigo: 1258324735,
    ivory: 4294963455,
    khaki: 4041641215,
    lavender: 3873897215,
    lavenderblush: 4293981695,
    lawngreen: 2096890111,
    lemonchiffon: 4294626815,
    lightblue: 2916673279,
    lightcoral: 4034953471,
    lightcyan: 3774873599,
    lightgoldenrodyellow: 4210742015,
    lightgray: 3553874943,
    lightgreen: 2431553791,
    lightgrey: 3553874943,
    lightpink: 4290167295,
    lightsalmon: 4288707327,
    lightseagreen: 548580095,
    lightskyblue: 2278488831,
    lightslategray: 2005441023,
    lightslategrey: 2005441023,
    lightsteelblue: 2965692159,
    lightyellow: 4294959359,
    lime: 16711935,
    limegreen: 852308735,
    linen: 4210091775,
    magenta: 4278255615,
    maroon: 2147483903,
    mediumaquamarine: 1724754687,
    mediumblue: 52735,
    mediumorchid: 3126187007,
    mediumpurple: 2473647103,
    mediumseagreen: 1018393087,
    mediumslateblue: 2070474495,
    mediumspringgreen: 16423679,
    mediumturquoise: 1221709055,
    mediumvioletred: 3340076543,
    midnightblue: 421097727,
    mintcream: 4127193855,
    mistyrose: 4293190143,
    moccasin: 4293178879,
    navajowhite: 4292783615,
    navy: 33023,
    oldlace: 4260751103,
    olive: 2155872511,
    olivedrab: 1804477439,
    orange: 4289003775,
    orangered: 4282712319,
    orchid: 3664828159,
    palegoldenrod: 4008225535,
    palegreen: 2566625535,
    paleturquoise: 2951671551,
    palevioletred: 3681588223,
    papayawhip: 4293907967,
    peachpuff: 4292524543,
    peru: 3448061951,
    pink: 4290825215,
    plum: 3718307327,
    powderblue: 2967529215,
    purple: 2147516671,
    rebeccapurple: 1714657791,
    red: 4278190335,
    rosybrown: 3163525119,
    royalblue: 1097458175,
    saddlebrown: 2336560127,
    salmon: 4202722047,
    sandybrown: 4104413439,
    seagreen: 780883967,
    seashell: 4294307583,
    sienna: 2689740287,
    silver: 3233857791,
    skyblue: 2278484991,
    slateblue: 1784335871,
    slategray: 1887473919,
    slategrey: 1887473919,
    snow: 4294638335,
    springgreen: 16744447,
    steelblue: 1182971135,
    tan: 3535047935,
    teal: 8421631,
    thistle: 3636451583,
    tomato: 4284696575,
    turquoise: 1088475391,
    violet: 4001558271,
    wheat: 4125012991,
    white: 4294967295,
    whitesmoke: 4126537215,
    yellow: 4294902015,
    yellowgreen: 2597139199
};
// src/colorMatchers.ts
var NUMBER = "[-+]?\\d*\\.?\\d+";
var PERCENTAGE = NUMBER + "%";
function call(...parts) {
    return "\\(\\s*(" + parts.join(")\\s*,\\s*(") + ")\\s*\\)";
}
var rgb = new RegExp("rgb" + call(NUMBER, NUMBER, NUMBER));
var rgba = new RegExp("rgba" + call(NUMBER, NUMBER, NUMBER, NUMBER));
var hsl = new RegExp("hsl" + call(NUMBER, PERCENTAGE, PERCENTAGE));
var hsla = new RegExp("hsla" + call(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER));
var hex3 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;
var hex4 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;
var hex6 = /^#([0-9a-fA-F]{6})$/;
var hex8 = /^#([0-9a-fA-F]{8})$/;
// src/normalizeColor.ts
function normalizeColor(color) {
    let match;
    if (typeof color === "number") {
        return color >>> 0 === color && color >= 0 && color <= 4294967295 ? color : null;
    }
    if (match = hex6.exec(color)) return parseInt(match[1] + "ff", 16) >>> 0;
    if (colors && colors[color] !== void 0) {
        return colors[color];
    }
    if (match = rgb.exec(color)) {
        return (parse255(match[1]) << 24 | // r
        parse255(match[2]) << 16 | // g
        parse255(match[3]) << 8 | // b
        255) >>> // a
        0;
    }
    if (match = rgba.exec(color)) {
        return (parse255(match[1]) << 24 | // r
        parse255(match[2]) << 16 | // g
        parse255(match[3]) << 8 | // b
        parse1(match[4])) >>> // a
        0;
    }
    if (match = hex3.exec(color)) {
        return parseInt(match[1] + match[1] + // r
        match[2] + match[2] + // g
        match[3] + match[3] + // b
        "ff", // a
        16) >>> 0;
    }
    if (match = hex8.exec(color)) return parseInt(match[1], 16) >>> 0;
    if (match = hex4.exec(color)) {
        return parseInt(match[1] + match[1] + // r
        match[2] + match[2] + // g
        match[3] + match[3] + // b
        match[4] + match[4], // a
        16) >>> 0;
    }
    if (match = hsl.exec(color)) {
        return (hslToRgb(parse360(match[1]), // h
        parsePercentage(match[2]), // s
        parsePercentage(match[3])) | 255) >>> // a
        0;
    }
    if (match = hsla.exec(color)) {
        return (hslToRgb(parse360(match[1]), // h
        parsePercentage(match[2]), // s
        parsePercentage(match[3])) | parse1(match[4])) >>> // a
        0;
    }
    return null;
}
function hue2rgb(p, q, t) {
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1 / 6) return p + (q - p) * 6 * t;
    if (t < 1 / 2) return q;
    if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
    return p;
}
function hslToRgb(h, s, l) {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    const r = hue2rgb(p, q, h + 1 / 3);
    const g = hue2rgb(p, q, h);
    const b = hue2rgb(p, q, h - 1 / 3);
    return Math.round(r * 255) << 24 | Math.round(g * 255) << 16 | Math.round(b * 255) << 8;
}
function parse255(str) {
    const int = parseInt(str, 10);
    if (int < 0) return 0;
    if (int > 255) return 255;
    return int;
}
function parse360(str) {
    const int = parseFloat(str);
    return (int % 360 + 360) % 360 / 360;
}
function parse1(str) {
    const num = parseFloat(str);
    if (num < 0) return 0;
    if (num > 1) return 255;
    return Math.round(num * 255);
}
function parsePercentage(str) {
    const int = parseFloat(str);
    if (int < 0) return 0;
    if (int > 100) return 1;
    return int / 100;
}
// src/colorToRgba.ts
function colorToRgba(input) {
    let int32Color = normalizeColor(input);
    if (int32Color === null) return input;
    int32Color = int32Color || 0;
    const r = (int32Color & 4278190080) >>> 24;
    const g = (int32Color & 16711680) >>> 16;
    const b = (int32Color & 65280) >>> 8;
    const a = (int32Color & 255) / 255;
    return `rgba(${r}, ${g}, ${b}, ${a})`;
}
// src/createInterpolator.ts
var createInterpolator = (range, output, extrapolate)=>{
    if (is.fun(range)) {
        return range;
    }
    if (is.arr(range)) {
        return createInterpolator({
            range,
            output,
            extrapolate
        });
    }
    if (is.str(range.output[0])) {
        return createStringInterpolator(range);
    }
    const config = range;
    const outputRange = config.output;
    const inputRange = config.range || [
        0,
        1
    ];
    const extrapolateLeft = config.extrapolateLeft || config.extrapolate || "extend";
    const extrapolateRight = config.extrapolateRight || config.extrapolate || "extend";
    const easing = config.easing || ((t)=>t);
    return (input)=>{
        const range2 = findRange(input, inputRange);
        return interpolate(input, inputRange[range2], inputRange[range2 + 1], outputRange[range2], outputRange[range2 + 1], easing, extrapolateLeft, extrapolateRight, config.map);
    };
};
function interpolate(input, inputMin, inputMax, outputMin, outputMax, easing, extrapolateLeft, extrapolateRight, map) {
    let result = map ? map(input) : input;
    if (result < inputMin) {
        if (extrapolateLeft === "identity") return result;
        else if (extrapolateLeft === "clamp") result = inputMin;
    }
    if (result > inputMax) {
        if (extrapolateRight === "identity") return result;
        else if (extrapolateRight === "clamp") result = inputMax;
    }
    if (outputMin === outputMax) return outputMin;
    if (inputMin === inputMax) return input <= inputMin ? outputMin : outputMax;
    if (inputMin === -Infinity) result = -result;
    else if (inputMax === Infinity) result = result - inputMin;
    else result = (result - inputMin) / (inputMax - inputMin);
    result = easing(result);
    if (outputMin === -Infinity) result = -result;
    else if (outputMax === Infinity) result = result + outputMin;
    else result = result * (outputMax - outputMin) + outputMin;
    return result;
}
function findRange(input, inputRange) {
    for(var i = 1; i < inputRange.length - 1; ++i)if (inputRange[i] >= input) break;
    return i - 1;
}
// src/easings.ts
var steps = (steps2, direction = "end")=>(progress2)=>{
        progress2 = direction === "end" ? Math.min(progress2, 0.999) : Math.max(progress2, 1e-3);
        const expanded = progress2 * steps2;
        const rounded = direction === "end" ? Math.floor(expanded) : Math.ceil(expanded);
        return clamp(0, 1, rounded / steps2);
    };
var c1 = 1.70158;
var c2 = c1 * 1.525;
var c3 = c1 + 1;
var c4 = 2 * Math.PI / 3;
var c5 = 2 * Math.PI / 4.5;
var bounceOut = (x)=>{
    const n1 = 7.5625;
    const d1 = 2.75;
    if (x < 1 / d1) {
        return n1 * x * x;
    } else if (x < 2 / d1) {
        return n1 * (x -= 1.5 / d1) * x + 0.75;
    } else if (x < 2.5 / d1) {
        return n1 * (x -= 2.25 / d1) * x + 0.9375;
    } else {
        return n1 * (x -= 2.625 / d1) * x + 0.984375;
    }
};
var easings = {
    linear: (x)=>x,
    easeInQuad: (x)=>x * x,
    easeOutQuad: (x)=>1 - (1 - x) * (1 - x),
    easeInOutQuad: (x)=>x < 0.5 ? 2 * x * x : 1 - Math.pow(-2 * x + 2, 2) / 2,
    easeInCubic: (x)=>x * x * x,
    easeOutCubic: (x)=>1 - Math.pow(1 - x, 3),
    easeInOutCubic: (x)=>x < 0.5 ? 4 * x * x * x : 1 - Math.pow(-2 * x + 2, 3) / 2,
    easeInQuart: (x)=>x * x * x * x,
    easeOutQuart: (x)=>1 - Math.pow(1 - x, 4),
    easeInOutQuart: (x)=>x < 0.5 ? 8 * x * x * x * x : 1 - Math.pow(-2 * x + 2, 4) / 2,
    easeInQuint: (x)=>x * x * x * x * x,
    easeOutQuint: (x)=>1 - Math.pow(1 - x, 5),
    easeInOutQuint: (x)=>x < 0.5 ? 16 * x * x * x * x * x : 1 - Math.pow(-2 * x + 2, 5) / 2,
    easeInSine: (x)=>1 - Math.cos(x * Math.PI / 2),
    easeOutSine: (x)=>Math.sin(x * Math.PI / 2),
    easeInOutSine: (x)=>-(Math.cos(Math.PI * x) - 1) / 2,
    easeInExpo: (x)=>x === 0 ? 0 : Math.pow(2, 10 * x - 10),
    easeOutExpo: (x)=>x === 1 ? 1 : 1 - Math.pow(2, -10 * x),
    easeInOutExpo: (x)=>x === 0 ? 0 : x === 1 ? 1 : x < 0.5 ? Math.pow(2, 20 * x - 10) / 2 : (2 - Math.pow(2, -20 * x + 10)) / 2,
    easeInCirc: (x)=>1 - Math.sqrt(1 - Math.pow(x, 2)),
    easeOutCirc: (x)=>Math.sqrt(1 - Math.pow(x - 1, 2)),
    easeInOutCirc: (x)=>x < 0.5 ? (1 - Math.sqrt(1 - Math.pow(2 * x, 2))) / 2 : (Math.sqrt(1 - Math.pow(-2 * x + 2, 2)) + 1) / 2,
    easeInBack: (x)=>c3 * x * x * x - c1 * x * x,
    easeOutBack: (x)=>1 + c3 * Math.pow(x - 1, 3) + c1 * Math.pow(x - 1, 2),
    easeInOutBack: (x)=>x < 0.5 ? Math.pow(2 * x, 2) * ((c2 + 1) * 2 * x - c2) / 2 : (Math.pow(2 * x - 2, 2) * ((c2 + 1) * (x * 2 - 2) + c2) + 2) / 2,
    easeInElastic: (x)=>x === 0 ? 0 : x === 1 ? 1 : -Math.pow(2, 10 * x - 10) * Math.sin((x * 10 - 10.75) * c4),
    easeOutElastic: (x)=>x === 0 ? 0 : x === 1 ? 1 : Math.pow(2, -10 * x) * Math.sin((x * 10 - 0.75) * c4) + 1,
    easeInOutElastic: (x)=>x === 0 ? 0 : x === 1 ? 1 : x < 0.5 ? -(Math.pow(2, 20 * x - 10) * Math.sin((20 * x - 11.125) * c5)) / 2 : Math.pow(2, -20 * x + 10) * Math.sin((20 * x - 11.125) * c5) / 2 + 1,
    easeInBounce: (x)=>1 - bounceOut(1 - x),
    easeOutBounce: bounceOut,
    easeInOutBounce: (x)=>x < 0.5 ? (1 - bounceOut(1 - 2 * x)) / 2 : (1 + bounceOut(2 * x - 1)) / 2,
    steps
};
// src/fluids.ts
var $get = Symbol.for("FluidValue.get");
var $observers = Symbol.for("FluidValue.observers");
var hasFluidValue = (arg)=>Boolean(arg && arg[$get]);
var getFluidValue = (arg)=>arg && arg[$get] ? arg[$get]() : arg;
var getFluidObservers = (target)=>target[$observers] || null;
function callFluidObserver(observer2, event) {
    if (observer2.eventObserved) {
        observer2.eventObserved(event);
    } else {
        observer2(event);
    }
}
function callFluidObservers(target, event) {
    const observers = target[$observers];
    if (observers) {
        observers.forEach((observer2)=>{
            callFluidObserver(observer2, event);
        });
    }
}
$get, $observers;
var FluidValue = class {
    constructor(get){
        if (!get && !(get = this.get)) {
            throw Error("Unknown getter");
        }
        setFluidGetter(this, get);
    }
};
var setFluidGetter = (target, get)=>setHidden(target, $get, get);
function addFluidObserver(target, observer2) {
    if (target[$get]) {
        let observers = target[$observers];
        if (!observers) {
            setHidden(target, $observers, observers = /* @__PURE__ */ new Set());
        }
        if (!observers.has(observer2)) {
            observers.add(observer2);
            if (target.observerAdded) {
                target.observerAdded(observers.size, observer2);
            }
        }
    }
    return observer2;
}
function removeFluidObserver(target, observer2) {
    const observers = target[$observers];
    if (observers && observers.has(observer2)) {
        const count = observers.size - 1;
        if (count) {
            observers.delete(observer2);
        } else {
            target[$observers] = null;
        }
        if (target.observerRemoved) {
            target.observerRemoved(count, observer2);
        }
    }
}
var setHidden = (target, key, value)=>Object.defineProperty(target, key, {
        value,
        writable: true,
        configurable: true
    });
// src/regexs.ts
var numberRegex = /[+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?/g;
var colorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d\.]+%?\))/gi;
var unitRegex = new RegExp(`(${numberRegex.source})(%|[a-z]+)`, "i");
var rgbaRegex = /rgba\(([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+)\)/gi;
var cssVariableRegex = /var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;
// src/variableToRgba.ts
var variableToRgba = (input)=>{
    const [token, fallback] = parseCSSVariable(input);
    if (!token || isSSR()) {
        return input;
    }
    const value = window.getComputedStyle(document.documentElement).getPropertyValue(token);
    if (value) {
        return value.trim();
    } else if (fallback && fallback.startsWith("--")) {
        const value2 = window.getComputedStyle(document.documentElement).getPropertyValue(fallback);
        if (value2) {
            return value2;
        } else {
            return input;
        }
    } else if (fallback && cssVariableRegex.test(fallback)) {
        return variableToRgba(fallback);
    } else if (fallback) {
        return fallback;
    }
    return input;
};
var parseCSSVariable = (current)=>{
    const match = cssVariableRegex.exec(current);
    if (!match) return [
        , 
    ];
    const [, token, fallback] = match;
    return [
        token,
        fallback
    ];
};
// src/stringInterpolation.ts
var namedColorRegex;
var rgbaRound = (_, p1, p2, p3, p4)=>`rgba(${Math.round(p1)}, ${Math.round(p2)}, ${Math.round(p3)}, ${p4})`;
var createStringInterpolator2 = (config)=>{
    if (!namedColorRegex) namedColorRegex = colors ? // match color names, ignore partial matches
    new RegExp(`(${Object.keys(colors).join("|")})(?!\\w)`, "g") : // never match
    /^\b$/;
    const output = config.output.map((value)=>{
        return getFluidValue(value).replace(cssVariableRegex, variableToRgba).replace(colorRegex, colorToRgba).replace(namedColorRegex, colorToRgba);
    });
    const keyframes = output.map((value)=>value.match(numberRegex).map(Number));
    const outputRanges = keyframes[0].map((_, i)=>keyframes.map((values)=>{
            if (!(i in values)) {
                throw Error('The arity of each "output" value must be equal');
            }
            return values[i];
        }));
    const interpolators = outputRanges.map((output2)=>createInterpolator({
            ...config,
            output: output2
        }));
    return (input)=>{
        const missingUnit = !unitRegex.test(output[0]) && output.find((value)=>unitRegex.test(value))?.replace(numberRegex, "");
        let i = 0;
        return output[0].replace(numberRegex, ()=>`${interpolators[i++](input)}${missingUnit || ""}`).replace(rgbaRegex, rgbaRound);
    };
};
// src/deprecations.ts
var prefix = "react-spring: ";
var once = (fn)=>{
    const func = fn;
    let called = false;
    if (typeof func != "function") {
        throw new TypeError(`${prefix}once requires a function parameter`);
    }
    return (...args)=>{
        if (!called) {
            func(...args);
            called = true;
        }
    };
};
var warnInterpolate = once(console.warn);
function deprecateInterpolate() {
    warnInterpolate(`${prefix}The "interpolate" function is deprecated in v9 (use "to" instead)`);
}
var warnDirectCall = once(console.warn);
function deprecateDirectCall() {
    warnDirectCall(`${prefix}Directly calling start instead of using the api object is deprecated in v9 (use ".start" instead), this will be removed in later 0.X.0 versions`);
}
// src/isAnimatedString.ts
function isAnimatedString(value) {
    return is.str(value) && (value[0] == "#" || /\d/.test(value) || // Do not identify a CSS variable as an AnimatedString if its SSR
    !isSSR() && cssVariableRegex.test(value) || value in (colors || {}));
}
// src/dom-events/scroll/index.ts
var import_rafz3 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+rafz@10.0.1/node_modules/@react-spring/rafz/dist/cjs/index.js [app-client] (ecmascript)");
// src/dom-events/resize/resizeElement.ts
var observer;
var resizeHandlers = /* @__PURE__ */ new WeakMap();
var handleObservation = (entries)=>entries.forEach(({ target, contentRect })=>{
        return resizeHandlers.get(target)?.forEach((handler)=>handler(contentRect));
    });
function resizeElement(handler, target) {
    if (!observer) {
        if (typeof ResizeObserver !== "undefined") {
            observer = new ResizeObserver(handleObservation);
        }
    }
    let elementHandlers = resizeHandlers.get(target);
    if (!elementHandlers) {
        elementHandlers = /* @__PURE__ */ new Set();
        resizeHandlers.set(target, elementHandlers);
    }
    elementHandlers.add(handler);
    if (observer) {
        observer.observe(target);
    }
    return ()=>{
        const elementHandlers2 = resizeHandlers.get(target);
        if (!elementHandlers2) return;
        elementHandlers2.delete(handler);
        if (!elementHandlers2.size && observer) {
            observer.unobserve(target);
        }
    };
}
// src/dom-events/resize/resizeWindow.ts
var listeners = /* @__PURE__ */ new Set();
var cleanupWindowResizeHandler;
var createResizeHandler = ()=>{
    const handleResize = ()=>{
        listeners.forEach((callback)=>callback({
                width: window.innerWidth,
                height: window.innerHeight
            }));
    };
    window.addEventListener("resize", handleResize);
    return ()=>{
        window.removeEventListener("resize", handleResize);
    };
};
var resizeWindow = (callback)=>{
    listeners.add(callback);
    if (!cleanupWindowResizeHandler) {
        cleanupWindowResizeHandler = createResizeHandler();
    }
    return ()=>{
        listeners.delete(callback);
        if (!listeners.size && cleanupWindowResizeHandler) {
            cleanupWindowResizeHandler();
            cleanupWindowResizeHandler = void 0;
        }
    };
};
// src/dom-events/resize/index.ts
var onResize = (callback, { container = document.documentElement } = {})=>{
    if (container === document.documentElement) {
        return resizeWindow(callback);
    } else {
        return resizeElement(callback, container);
    }
};
// src/progress.ts
var progress = (min, max, value)=>max - min === 0 ? 1 : (value - min) / (max - min);
// src/dom-events/scroll/ScrollHandler.ts
var SCROLL_KEYS = {
    x: {
        length: "Width",
        position: "Left"
    },
    y: {
        length: "Height",
        position: "Top"
    }
};
var ScrollHandler = class {
    constructor(callback, container){
        this.createAxis = ()=>({
                current: 0,
                progress: 0,
                scrollLength: 0
            });
        this.updateAxis = (axisName)=>{
            const axis = this.info[axisName];
            const { length, position } = SCROLL_KEYS[axisName];
            axis.current = this.container[`scroll${position}`];
            axis.scrollLength = this.container[`scroll${length}`] - this.container[`client${length}`];
            axis.progress = progress(0, axis.scrollLength, axis.current);
        };
        this.update = ()=>{
            this.updateAxis("x");
            this.updateAxis("y");
        };
        this.sendEvent = ()=>{
            this.callback(this.info);
        };
        this.advance = ()=>{
            this.update();
            this.sendEvent();
        };
        this.callback = callback;
        this.container = container;
        this.info = {
            time: 0,
            x: this.createAxis(),
            y: this.createAxis()
        };
    }
};
// src/dom-events/scroll/index.ts
var scrollListeners = /* @__PURE__ */ new WeakMap();
var resizeListeners = /* @__PURE__ */ new WeakMap();
var onScrollHandlers = /* @__PURE__ */ new WeakMap();
var getTarget = (container)=>container === document.documentElement ? window : container;
var onScroll = (callback, { container = document.documentElement } = {})=>{
    let containerHandlers = onScrollHandlers.get(container);
    if (!containerHandlers) {
        containerHandlers = /* @__PURE__ */ new Set();
        onScrollHandlers.set(container, containerHandlers);
    }
    const containerHandler = new ScrollHandler(callback, container);
    containerHandlers.add(containerHandler);
    if (!scrollListeners.has(container)) {
        const listener = ()=>{
            containerHandlers?.forEach((handler)=>handler.advance());
            return true;
        };
        scrollListeners.set(container, listener);
        const target = getTarget(container);
        window.addEventListener("resize", listener, {
            passive: true
        });
        if (container !== document.documentElement) {
            resizeListeners.set(container, onResize(listener, {
                container
            }));
        }
        target.addEventListener("scroll", listener, {
            passive: true
        });
    }
    const animateScroll = scrollListeners.get(container);
    (0, import_rafz3.raf)(animateScroll);
    return ()=>{
        import_rafz3.raf.cancel(animateScroll);
        const containerHandlers2 = onScrollHandlers.get(container);
        if (!containerHandlers2) return;
        containerHandlers2.delete(containerHandler);
        if (containerHandlers2.size) return;
        const listener = scrollListeners.get(container);
        scrollListeners.delete(container);
        if (listener) {
            getTarget(container).removeEventListener("scroll", listener);
            window.removeEventListener("resize", listener);
            resizeListeners.get(container)?.();
        }
    };
};
// src/hooks/useConstant.ts
var import_react = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
function useConstant(init) {
    const ref = (0, import_react.useRef)(null);
    if (ref.current === null) {
        ref.current = init();
    }
    return ref.current;
}
// src/hooks/useForceUpdate.ts
var import_react4 = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
// src/hooks/useIsMounted.ts
var import_react3 = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
// src/hooks/useIsomorphicLayoutEffect.ts
var import_react2 = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var useIsomorphicLayoutEffect = isSSR() ? import_react2.useEffect : import_react2.useLayoutEffect;
// src/hooks/useIsMounted.ts
var useIsMounted = ()=>{
    const isMounted = (0, import_react3.useRef)(false);
    useIsomorphicLayoutEffect({
        "useIsMounted.useIsomorphicLayoutEffect": ()=>{
            isMounted.current = true;
            return ({
                "useIsMounted.useIsomorphicLayoutEffect": ()=>{
                    isMounted.current = false;
                }
            })["useIsMounted.useIsomorphicLayoutEffect"];
        }
    }["useIsMounted.useIsomorphicLayoutEffect"], []);
    return isMounted;
};
// src/hooks/useForceUpdate.ts
function useForceUpdate() {
    const update = (0, import_react4.useState)()[1];
    const isMounted = useIsMounted();
    return ()=>{
        if (isMounted.current) {
            update(Math.random());
        }
    };
}
// src/hooks/useMemoOne.ts
var import_react5 = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
function useMemoOne(getResult, inputs) {
    const [initial] = (0, import_react5.useState)(()=>({
            inputs,
            result: getResult()
        }));
    const committed = (0, import_react5.useRef)(void 0);
    const prevCache = committed.current;
    let cache = prevCache;
    if (cache) {
        const useCache = Boolean(inputs && cache.inputs && areInputsEqual(inputs, cache.inputs));
        if (!useCache) {
            cache = {
                inputs,
                result: getResult()
            };
        }
    } else {
        cache = initial;
    }
    (0, import_react5.useEffect)(()=>{
        committed.current = cache;
        if (prevCache == initial) {
            initial.inputs = initial.result = void 0;
        }
    }, [
        cache
    ]);
    return cache.result;
}
function areInputsEqual(next, prev) {
    if (next.length !== prev.length) {
        return false;
    }
    for(let i = 0; i < next.length; i++){
        if (next[i] !== prev[i]) {
            return false;
        }
    }
    return true;
}
// src/hooks/useOnce.ts
var import_react6 = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var useOnce = (effect)=>(0, import_react6.useEffect)(effect, emptyDeps);
var emptyDeps = [];
// src/hooks/usePrev.ts
var import_react7 = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
function usePrev(value) {
    const prevRef = (0, import_react7.useRef)(void 0);
    (0, import_react7.useEffect)(()=>{
        prevRef.current = value;
    });
    return prevRef.current;
}
// src/hooks/useReducedMotion.ts
var import_react8 = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var useReducedMotion = ()=>{
    const [reducedMotion, setReducedMotion] = (0, import_react8.useState)(null);
    useIsomorphicLayoutEffect({
        "useReducedMotion.useIsomorphicLayoutEffect": ()=>{
            const mql = window.matchMedia("(prefers-reduced-motion)");
            const handleMediaChange = {
                "useReducedMotion.useIsomorphicLayoutEffect.handleMediaChange": (e)=>{
                    setReducedMotion(e.matches);
                    assign({
                        skipAnimation: e.matches
                    });
                }
            }["useReducedMotion.useIsomorphicLayoutEffect.handleMediaChange"];
            handleMediaChange(mql);
            if (mql.addEventListener) {
                mql.addEventListener("change", handleMediaChange);
            } else {
                mql.addListener(handleMediaChange);
            }
            return ({
                "useReducedMotion.useIsomorphicLayoutEffect": ()=>{
                    if (mql.removeEventListener) {
                        mql.removeEventListener("change", handleMediaChange);
                    } else {
                        mql.removeListener(handleMediaChange);
                    }
                }
            })["useReducedMotion.useIsomorphicLayoutEffect"];
        }
    }["useReducedMotion.useIsomorphicLayoutEffect"], []);
    return reducedMotion;
};
// src/index.ts
var import_rafz4 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+rafz@10.0.1/node_modules/@react-spring/rafz/dist/cjs/index.js [app-client] (ecmascript)");
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    FluidValue,
    Globals,
    addFluidObserver,
    callFluidObserver,
    callFluidObservers,
    clamp,
    colorToRgba,
    colors,
    createInterpolator,
    createStringInterpolator,
    defineHidden,
    deprecateDirectCall,
    deprecateInterpolate,
    each,
    eachProp,
    easings,
    flush,
    flushCalls,
    frameLoop,
    getFluidObservers,
    getFluidValue,
    hasFluidValue,
    hex3,
    hex4,
    hex6,
    hex8,
    hsl,
    hsla,
    is,
    isAnimatedString,
    isEqual,
    isSSR,
    noop,
    onResize,
    onScroll,
    once,
    prefix,
    raf,
    removeFluidObserver,
    rgb,
    rgba,
    setFluidGetter,
    toArray,
    useConstant,
    useForceUpdate,
    useIsomorphicLayoutEffect,
    useMemoOne,
    useOnce,
    usePrev,
    useReducedMotion
});
}}),
"[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/react-spring_shared.development.cjs [app-client] (ecmascript)");
}
}}),
"[project]/node_modules/.pnpm/@react-spring+types@10.0.1/node_modules/@react-spring/types/dist/cjs/react-spring_types.development.cjs [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
// src/index.ts
var src_exports = {};
__export(src_exports, {
    Any: ()=>Any
});
module.exports = __toCommonJS(src_exports);
// src/utils.ts
var Any = class {
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    Any
});
}}),
"[project]/node_modules/.pnpm/@react-spring+types@10.0.1/node_modules/@react-spring/types/dist/cjs/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+types@10.0.1/node_modules/@react-spring/types/dist/cjs/react-spring_types.development.cjs [app-client] (ecmascript)");
}
}}),
"[project]/node_modules/.pnpm/@react-spring+animated@10.0.1_react@19.0.0/node_modules/@react-spring/animated/dist/cjs/react-spring_animated.development.cjs [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
// src/index.ts
var src_exports = {};
__export(src_exports, {
    Animated: ()=>Animated,
    AnimatedArray: ()=>AnimatedArray,
    AnimatedObject: ()=>AnimatedObject,
    AnimatedString: ()=>AnimatedString,
    AnimatedValue: ()=>AnimatedValue,
    createHost: ()=>createHost,
    getAnimated: ()=>getAnimated,
    getAnimatedType: ()=>getAnimatedType,
    getPayload: ()=>getPayload,
    isAnimated: ()=>isAnimated,
    setAnimated: ()=>setAnimated
});
module.exports = __toCommonJS(src_exports);
// src/Animated.ts
var import_shared = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
var $node = Symbol.for("Animated:node");
var isAnimated = (value)=>!!value && value[$node] === value;
var getAnimated = (owner)=>owner && owner[$node];
var setAnimated = (owner, node)=>(0, import_shared.defineHidden)(owner, $node, node);
var getPayload = (owner)=>owner && owner[$node] && owner[$node].getPayload();
var Animated = class {
    constructor(){
        setAnimated(this, this);
    }
    /** Get every `AnimatedValue` used by this node. */ getPayload() {
        return this.payload || [];
    }
};
// src/AnimatedValue.ts
var import_shared2 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
var AnimatedValue = class _AnimatedValue extends Animated {
    constructor(_value){
        super();
        this._value = _value;
        this.done = true;
        this.durationProgress = 0;
        if (import_shared2.is.num(this._value)) {
            this.lastPosition = this._value;
        }
    }
    /** @internal */ static create(value) {
        return new _AnimatedValue(value);
    }
    getPayload() {
        return [
            this
        ];
    }
    getValue() {
        return this._value;
    }
    setValue(value, step) {
        if (import_shared2.is.num(value)) {
            this.lastPosition = value;
            if (step) {
                value = Math.round(value / step) * step;
                if (this.done) {
                    this.lastPosition = value;
                }
            }
        }
        if (this._value === value) {
            return false;
        }
        this._value = value;
        return true;
    }
    reset() {
        const { done } = this;
        this.done = false;
        if (import_shared2.is.num(this._value)) {
            this.elapsedTime = 0;
            this.durationProgress = 0;
            this.lastPosition = this._value;
            if (done) this.lastVelocity = null;
            this.v0 = null;
        }
    }
};
// src/AnimatedString.ts
var import_shared3 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
var AnimatedString = class _AnimatedString extends AnimatedValue {
    constructor(value){
        super(0);
        this._string = null;
        this._toString = (0, import_shared3.createInterpolator)({
            output: [
                value,
                value
            ]
        });
    }
    /** @internal */ static create(value) {
        return new _AnimatedString(value);
    }
    getValue() {
        const value = this._string;
        return value == null ? this._string = this._toString(this._value) : value;
    }
    setValue(value) {
        if (import_shared3.is.str(value)) {
            if (value == this._string) {
                return false;
            }
            this._string = value;
            this._value = 1;
        } else if (super.setValue(value)) {
            this._string = null;
        } else {
            return false;
        }
        return true;
    }
    reset(goal) {
        if (goal) {
            this._toString = (0, import_shared3.createInterpolator)({
                output: [
                    this.getValue(),
                    goal
                ]
            });
        }
        this._value = 0;
        super.reset();
    }
};
// src/AnimatedArray.ts
var import_shared5 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
// src/AnimatedObject.ts
var import_shared4 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
// src/context.ts
var TreeContext = {
    dependencies: null
};
// src/AnimatedObject.ts
var AnimatedObject = class extends Animated {
    constructor(source){
        super();
        this.source = source;
        this.setValue(source);
    }
    getValue(animated) {
        const values = {};
        (0, import_shared4.eachProp)(this.source, (source, key)=>{
            if (isAnimated(source)) {
                values[key] = source.getValue(animated);
            } else if ((0, import_shared4.hasFluidValue)(source)) {
                values[key] = (0, import_shared4.getFluidValue)(source);
            } else if (!animated) {
                values[key] = source;
            }
        });
        return values;
    }
    /** Replace the raw object data */ setValue(source) {
        this.source = source;
        this.payload = this._makePayload(source);
    }
    reset() {
        if (this.payload) {
            (0, import_shared4.each)(this.payload, (node)=>node.reset());
        }
    }
    /** Create a payload set. */ _makePayload(source) {
        if (source) {
            const payload = /* @__PURE__ */ new Set();
            (0, import_shared4.eachProp)(source, this._addToPayload, payload);
            return Array.from(payload);
        }
    }
    /** Add to a payload set. */ _addToPayload(source) {
        if (TreeContext.dependencies && (0, import_shared4.hasFluidValue)(source)) {
            TreeContext.dependencies.add(source);
        }
        const payload = getPayload(source);
        if (payload) {
            (0, import_shared4.each)(payload, (node)=>this.add(node));
        }
    }
};
// src/AnimatedArray.ts
var AnimatedArray = class _AnimatedArray extends AnimatedObject {
    constructor(source){
        super(source);
    }
    /** @internal */ static create(source) {
        return new _AnimatedArray(source);
    }
    getValue() {
        return this.source.map((node)=>node.getValue());
    }
    setValue(source) {
        const payload = this.getPayload();
        if (source.length == payload.length) {
            return payload.map((node, i)=>node.setValue(source[i])).some(Boolean);
        }
        super.setValue(source.map(makeAnimated));
        return true;
    }
};
function makeAnimated(value) {
    const nodeType = (0, import_shared5.isAnimatedString)(value) ? AnimatedString : AnimatedValue;
    return nodeType.create(value);
}
// src/getAnimatedType.ts
var import_shared6 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
function getAnimatedType(value) {
    const parentNode = getAnimated(value);
    return parentNode ? parentNode.constructor : import_shared6.is.arr(value) ? AnimatedArray : (0, import_shared6.isAnimatedString)(value) ? AnimatedString : AnimatedValue;
}
// src/createHost.ts
var import_shared8 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
// src/withAnimated.tsx
var React = __toESM(__turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"));
var import_react = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var import_shared7 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
var withAnimated = (Component, host)=>{
    const hasInstance = // Function components must use "forwardRef" to avoid being
    // re-rendered on every animation frame.
    !import_shared7.is.fun(Component) || Component.prototype && Component.prototype.isReactComponent;
    return (0, import_react.forwardRef)((givenProps, givenRef)=>{
        const instanceRef = (0, import_react.useRef)(null);
        const ref = hasInstance && // eslint-disable-next-line react-hooks/rules-of-hooks
        (0, import_react.useCallback)((value)=>{
            instanceRef.current = updateRef(givenRef, value);
        }, [
            givenRef
        ]);
        const [props, deps] = getAnimatedState(givenProps, host);
        const forceUpdate = (0, import_shared7.useForceUpdate)();
        const callback = ()=>{
            const instance = instanceRef.current;
            if (hasInstance && !instance) {
                return;
            }
            const didUpdate = instance ? host.applyAnimatedValues(instance, props.getValue(true)) : false;
            if (didUpdate === false) {
                forceUpdate();
            }
        };
        const observer = new PropsObserver(callback, deps);
        const observerRef = (0, import_react.useRef)(void 0);
        (0, import_shared7.useIsomorphicLayoutEffect)(()=>{
            observerRef.current = observer;
            (0, import_shared7.each)(deps, (dep)=>(0, import_shared7.addFluidObserver)(dep, observer));
            return ()=>{
                if (observerRef.current) {
                    (0, import_shared7.each)(observerRef.current.deps, (dep)=>(0, import_shared7.removeFluidObserver)(dep, observerRef.current));
                    import_shared7.raf.cancel(observerRef.current.update);
                }
            };
        });
        (0, import_react.useEffect)(callback, []);
        (0, import_shared7.useOnce)(()=>()=>{
                const observer2 = observerRef.current;
                (0, import_shared7.each)(observer2.deps, (dep)=>(0, import_shared7.removeFluidObserver)(dep, observer2));
            });
        const usedProps = host.getComponentProps(props.getValue());
        return /* @__PURE__ */ React.createElement(Component, {
            ...usedProps,
            ref
        });
    });
};
var PropsObserver = class {
    constructor(update, deps){
        this.update = update;
        this.deps = deps;
    }
    eventObserved(event) {
        if (event.type == "change") {
            import_shared7.raf.write(this.update);
        }
    }
};
function getAnimatedState(props, host) {
    const dependencies = /* @__PURE__ */ new Set();
    TreeContext.dependencies = dependencies;
    if (props.style) props = {
        ...props,
        style: host.createAnimatedStyle(props.style)
    };
    props = new AnimatedObject(props);
    TreeContext.dependencies = null;
    return [
        props,
        dependencies
    ];
}
function updateRef(ref, value) {
    if (ref) {
        if (import_shared7.is.fun(ref)) ref(value);
        else ref.current = value;
    }
    return value;
}
// src/createHost.ts
var cacheKey = Symbol.for("AnimatedComponent");
var createHost = (components, { applyAnimatedValues = ()=>false, createAnimatedStyle = (style)=>new AnimatedObject(style), getComponentProps = (props)=>props } = {})=>{
    const hostConfig = {
        applyAnimatedValues,
        createAnimatedStyle,
        getComponentProps
    };
    const animated = (Component)=>{
        const displayName = getDisplayName(Component) || "Anonymous";
        if (import_shared8.is.str(Component)) {
            Component = animated[Component] || (animated[Component] = withAnimated(Component, hostConfig));
        } else {
            Component = Component[cacheKey] || (Component[cacheKey] = withAnimated(Component, hostConfig));
        }
        Component.displayName = `Animated(${displayName})`;
        return Component;
    };
    (0, import_shared8.eachProp)(components, (Component, key)=>{
        if (import_shared8.is.arr(components)) {
            key = getDisplayName(Component);
        }
        animated[key] = animated(Component);
    });
    return {
        animated
    };
};
var getDisplayName = (arg)=>import_shared8.is.str(arg) ? arg : arg && import_shared8.is.str(arg.displayName) ? arg.displayName : import_shared8.is.fun(arg) && arg.name || null;
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    Animated,
    AnimatedArray,
    AnimatedObject,
    AnimatedString,
    AnimatedValue,
    createHost,
    getAnimated,
    getAnimatedType,
    getPayload,
    isAnimated,
    setAnimated
});
}}),
"[project]/node_modules/.pnpm/@react-spring+animated@10.0.1_react@19.0.0/node_modules/@react-spring/animated/dist/cjs/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+animated@10.0.1_react@19.0.0/node_modules/@react-spring/animated/dist/cjs/react-spring_animated.development.cjs [app-client] (ecmascript)");
}
}}),
"[project]/node_modules/.pnpm/@react-spring+core@10.0.1_react@19.0.0/node_modules/@react-spring/core/dist/cjs/react-spring_core.development.cjs [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to2, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to2, key) && key !== except) __defProp(to2, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to2;
};
var __reExport = (target, mod, secondTarget)=>(__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
// src/index.ts
var src_exports = {};
__export(src_exports, {
    BailSignal: ()=>BailSignal,
    Controller: ()=>Controller,
    FrameValue: ()=>FrameValue,
    Globals: ()=>import_shared21.Globals,
    Interpolation: ()=>Interpolation,
    Spring: ()=>Spring,
    SpringContext: ()=>SpringContext,
    SpringRef: ()=>SpringRef,
    SpringValue: ()=>SpringValue,
    Trail: ()=>Trail,
    Transition: ()=>Transition,
    config: ()=>config,
    createInterpolator: ()=>import_shared22.createInterpolator,
    easings: ()=>import_shared22.easings,
    inferTo: ()=>inferTo,
    interpolate: ()=>interpolate,
    to: ()=>to,
    update: ()=>update,
    useChain: ()=>useChain,
    useInView: ()=>useInView,
    useIsomorphicLayoutEffect: ()=>import_shared22.useIsomorphicLayoutEffect,
    useReducedMotion: ()=>import_shared22.useReducedMotion,
    useResize: ()=>useResize,
    useScroll: ()=>useScroll,
    useSpring: ()=>useSpring,
    useSpringRef: ()=>useSpringRef,
    useSpringValue: ()=>useSpringValue,
    useSprings: ()=>useSprings,
    useTrail: ()=>useTrail,
    useTransition: ()=>useTransition
});
module.exports = __toCommonJS(src_exports);
// src/hooks/useChain.ts
var import_shared2 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
// src/helpers.ts
var import_shared = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
function callProp(value, ...args) {
    return import_shared.is.fun(value) ? value(...args) : value;
}
var matchProp = (value, key)=>value === true || !!(key && value && (import_shared.is.fun(value) ? value(key) : (0, import_shared.toArray)(value).includes(key)));
var resolveProp = (prop, key)=>import_shared.is.obj(prop) ? key && prop[key] : prop;
var getDefaultProp = (props, key)=>props.default === true ? props[key] : props.default ? props.default[key] : void 0;
var noopTransform = (value)=>value;
var getDefaultProps = (props, transform = noopTransform)=>{
    let keys = DEFAULT_PROPS;
    if (props.default && props.default !== true) {
        props = props.default;
        keys = Object.keys(props);
    }
    const defaults2 = {};
    for (const key of keys){
        const value = transform(props[key], key);
        if (!import_shared.is.und(value)) {
            defaults2[key] = value;
        }
    }
    return defaults2;
};
var DEFAULT_PROPS = [
    "config",
    "onProps",
    "onStart",
    "onChange",
    "onPause",
    "onResume",
    "onRest"
];
var RESERVED_PROPS = {
    config: 1,
    from: 1,
    to: 1,
    ref: 1,
    loop: 1,
    reset: 1,
    pause: 1,
    cancel: 1,
    reverse: 1,
    immediate: 1,
    default: 1,
    delay: 1,
    onProps: 1,
    onStart: 1,
    onChange: 1,
    onPause: 1,
    onResume: 1,
    onRest: 1,
    onResolve: 1,
    // Transition props
    items: 1,
    trail: 1,
    sort: 1,
    expires: 1,
    initial: 1,
    enter: 1,
    update: 1,
    leave: 1,
    children: 1,
    onDestroyed: 1,
    // Internal props
    keys: 1,
    callId: 1,
    parentId: 1
};
function getForwardProps(props) {
    const forward = {};
    let count = 0;
    (0, import_shared.eachProp)(props, (value, prop)=>{
        if (!RESERVED_PROPS[prop]) {
            forward[prop] = value;
            count++;
        }
    });
    if (count) {
        return forward;
    }
}
function inferTo(props) {
    const to2 = getForwardProps(props);
    if (to2) {
        const out = {
            to: to2
        };
        (0, import_shared.eachProp)(props, (val, key)=>key in to2 || (out[key] = val));
        return out;
    }
    return {
        ...props
    };
}
function computeGoal(value) {
    value = (0, import_shared.getFluidValue)(value);
    return import_shared.is.arr(value) ? value.map(computeGoal) : (0, import_shared.isAnimatedString)(value) ? import_shared.Globals.createStringInterpolator({
        range: [
            0,
            1
        ],
        output: [
            value,
            value
        ]
    })(1) : value;
}
function hasProps(props) {
    for(const _ in props)return true;
    return false;
}
function isAsyncTo(to2) {
    return import_shared.is.fun(to2) || import_shared.is.arr(to2) && import_shared.is.obj(to2[0]);
}
function detachRefs(ctrl, ref) {
    ctrl.ref?.delete(ctrl);
    ref?.delete(ctrl);
}
function replaceRef(ctrl, ref) {
    if (ref && ctrl.ref !== ref) {
        ctrl.ref?.delete(ctrl);
        ref.add(ctrl);
        ctrl.ref = ref;
    }
}
// src/hooks/useChain.ts
function useChain(refs, timeSteps, timeFrame = 1e3) {
    (0, import_shared2.useIsomorphicLayoutEffect)(()=>{
        if (timeSteps) {
            let prevDelay = 0;
            (0, import_shared2.each)(refs, (ref, i)=>{
                const controllers = ref.current;
                if (controllers.length) {
                    let delay = timeFrame * timeSteps[i];
                    if (isNaN(delay)) delay = prevDelay;
                    else prevDelay = delay;
                    (0, import_shared2.each)(controllers, (ctrl)=>{
                        (0, import_shared2.each)(ctrl.queue, (props)=>{
                            const memoizedDelayProp = props.delay;
                            props.delay = (key)=>delay + callProp(memoizedDelayProp || 0, key);
                        });
                    });
                    ref.start();
                }
            });
        } else {
            let p = Promise.resolve();
            (0, import_shared2.each)(refs, (ref)=>{
                const controllers = ref.current;
                if (controllers.length) {
                    const queues = controllers.map((ctrl)=>{
                        const q = ctrl.queue;
                        ctrl.queue = [];
                        return q;
                    });
                    p = p.then(()=>{
                        (0, import_shared2.each)(controllers, (ctrl, i)=>(0, import_shared2.each)(queues[i] || [], (update2)=>ctrl.queue.push(update2)));
                        return Promise.all(ref.start());
                    });
                }
            });
        }
    });
}
// src/hooks/useSpring.ts
var import_shared11 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
// src/hooks/useSprings.ts
var import_react2 = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var import_shared10 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
// src/SpringValue.ts
var import_shared7 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
var import_animated2 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+animated@10.0.1_react@19.0.0/node_modules/@react-spring/animated/dist/cjs/index.js [app-client] (ecmascript)");
// src/AnimationConfig.ts
var import_shared3 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
// src/constants.ts
var config = {
    default: {
        tension: 170,
        friction: 26
    },
    gentle: {
        tension: 120,
        friction: 14
    },
    wobbly: {
        tension: 180,
        friction: 12
    },
    stiff: {
        tension: 210,
        friction: 20
    },
    slow: {
        tension: 280,
        friction: 60
    },
    molasses: {
        tension: 280,
        friction: 120
    }
};
// src/AnimationConfig.ts
var defaults = {
    ...config.default,
    mass: 1,
    damping: 1,
    easing: import_shared3.easings.linear,
    clamp: false
};
var AnimationConfig = class {
    constructor(){
        /**
     * The initial velocity of one or more values.
     *
     * @default 0
     */ this.velocity = 0;
        Object.assign(this, defaults);
    }
};
function mergeConfig(config2, newConfig, defaultConfig) {
    if (defaultConfig) {
        defaultConfig = {
            ...defaultConfig
        };
        sanitizeConfig(defaultConfig, newConfig);
        newConfig = {
            ...defaultConfig,
            ...newConfig
        };
    }
    sanitizeConfig(config2, newConfig);
    Object.assign(config2, newConfig);
    for(const key in defaults){
        if (config2[key] == null) {
            config2[key] = defaults[key];
        }
    }
    let { frequency, damping } = config2;
    const { mass } = config2;
    if (!import_shared3.is.und(frequency)) {
        if (frequency < 0.01) frequency = 0.01;
        if (damping < 0) damping = 0;
        config2.tension = Math.pow(2 * Math.PI / frequency, 2) * mass;
        config2.friction = 4 * Math.PI * damping * mass / frequency;
    }
    return config2;
}
function sanitizeConfig(config2, props) {
    if (!import_shared3.is.und(props.decay)) {
        config2.duration = void 0;
    } else {
        const isTensionConfig = !import_shared3.is.und(props.tension) || !import_shared3.is.und(props.friction);
        if (isTensionConfig || !import_shared3.is.und(props.frequency) || !import_shared3.is.und(props.damping) || !import_shared3.is.und(props.mass)) {
            config2.duration = void 0;
            config2.decay = void 0;
        }
        if (isTensionConfig) {
            config2.frequency = void 0;
        }
    }
}
// src/Animation.ts
var emptyArray = [];
var Animation = class {
    constructor(){
        this.changed = false;
        this.values = emptyArray;
        this.toValues = null;
        this.fromValues = emptyArray;
        this.config = new AnimationConfig();
        this.immediate = false;
    }
};
// src/scheduleProps.ts
var import_shared4 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
function scheduleProps(callId, { key, props, defaultProps, state, actions }) {
    return new Promise((resolve, reject)=>{
        let delay;
        let timeout;
        let cancel = matchProp(props.cancel ?? defaultProps?.cancel, key);
        if (cancel) {
            onStart();
        } else {
            if (!import_shared4.is.und(props.pause)) {
                state.paused = matchProp(props.pause, key);
            }
            let pause = defaultProps?.pause;
            if (pause !== true) {
                pause = state.paused || matchProp(pause, key);
            }
            delay = callProp(props.delay || 0, key);
            if (pause) {
                state.resumeQueue.add(onResume);
                actions.pause();
            } else {
                actions.resume();
                onResume();
            }
        }
        function onPause() {
            state.resumeQueue.add(onResume);
            state.timeouts.delete(timeout);
            timeout.cancel();
            delay = timeout.time - import_shared4.raf.now();
        }
        function onResume() {
            if (delay > 0 && !import_shared4.Globals.skipAnimation) {
                state.delayed = true;
                timeout = import_shared4.raf.setTimeout(onStart, delay);
                state.pauseQueue.add(onPause);
                state.timeouts.add(timeout);
            } else {
                onStart();
            }
        }
        function onStart() {
            if (state.delayed) {
                state.delayed = false;
            }
            state.pauseQueue.delete(onPause);
            state.timeouts.delete(timeout);
            if (callId <= (state.cancelId || 0)) {
                cancel = true;
            }
            try {
                actions.start({
                    ...props,
                    callId,
                    cancel
                }, resolve);
            } catch (err) {
                reject(err);
            }
        }
    });
}
// src/runAsync.ts
var import_shared5 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
// src/AnimationResult.ts
var getCombinedResult = (target, results)=>results.length == 1 ? results[0] : results.some((result)=>result.cancelled) ? getCancelledResult(target.get()) : results.every((result)=>result.noop) ? getNoopResult(target.get()) : getFinishedResult(target.get(), results.every((result)=>result.finished));
var getNoopResult = (value)=>({
        value,
        noop: true,
        finished: true,
        cancelled: false
    });
var getFinishedResult = (value, finished, cancelled = false)=>({
        value,
        finished,
        cancelled
    });
var getCancelledResult = (value)=>({
        value,
        cancelled: true,
        finished: false
    });
// src/runAsync.ts
function runAsync(to2, props, state, target) {
    const { callId, parentId, onRest } = props;
    const { asyncTo: prevTo, promise: prevPromise } = state;
    if (!parentId && to2 === prevTo && !props.reset) {
        return prevPromise;
    }
    return state.promise = (async ()=>{
        state.asyncId = callId;
        state.asyncTo = to2;
        const defaultProps = getDefaultProps(props, (value, key)=>// The `onRest` prop is only called when the `runAsync` promise is resolved.
            key === "onRest" ? void 0 : value);
        let preventBail;
        let bail;
        const bailPromise = new Promise((resolve, reject)=>(preventBail = resolve, bail = reject));
        const bailIfEnded = (bailSignal)=>{
            const bailResult = // The `cancel` prop or `stop` method was used.
            callId <= (state.cancelId || 0) && getCancelledResult(target) || // The async `to` prop was replaced.
            callId !== state.asyncId && getFinishedResult(target, false);
            if (bailResult) {
                bailSignal.result = bailResult;
                bail(bailSignal);
                throw bailSignal;
            }
        };
        const animate = (arg1, arg2)=>{
            const bailSignal = new BailSignal();
            const skipAnimationSignal = new SkipAnimationSignal();
            return (async ()=>{
                if (import_shared5.Globals.skipAnimation) {
                    stopAsync(state);
                    skipAnimationSignal.result = getFinishedResult(target, false);
                    bail(skipAnimationSignal);
                    throw skipAnimationSignal;
                }
                bailIfEnded(bailSignal);
                const props2 = import_shared5.is.obj(arg1) ? {
                    ...arg1
                } : {
                    ...arg2,
                    to: arg1
                };
                props2.parentId = callId;
                (0, import_shared5.eachProp)(defaultProps, (value, key)=>{
                    if (import_shared5.is.und(props2[key])) {
                        props2[key] = value;
                    }
                });
                const result2 = await target.start(props2);
                bailIfEnded(bailSignal);
                if (state.paused) {
                    await new Promise((resume)=>{
                        state.resumeQueue.add(resume);
                    });
                }
                return result2;
            })();
        };
        let result;
        if (import_shared5.Globals.skipAnimation) {
            stopAsync(state);
            return getFinishedResult(target, false);
        }
        try {
            let animating;
            if (import_shared5.is.arr(to2)) {
                animating = (async (queue)=>{
                    for (const props2 of queue){
                        await animate(props2);
                    }
                })(to2);
            } else {
                animating = Promise.resolve(to2(animate, target.stop.bind(target)));
            }
            await Promise.all([
                animating.then(preventBail),
                bailPromise
            ]);
            result = getFinishedResult(target.get(), true, false);
        } catch (err) {
            if (err instanceof BailSignal) {
                result = err.result;
            } else if (err instanceof SkipAnimationSignal) {
                result = err.result;
            } else {
                throw err;
            }
        } finally{
            if (callId == state.asyncId) {
                state.asyncId = parentId;
                state.asyncTo = parentId ? prevTo : void 0;
                state.promise = parentId ? prevPromise : void 0;
            }
        }
        if (import_shared5.is.fun(onRest)) {
            import_shared5.raf.batchedUpdates(()=>{
                onRest(result, target, target.item);
            });
        }
        return result;
    })();
}
function stopAsync(state, cancelId) {
    (0, import_shared5.flush)(state.timeouts, (t)=>t.cancel());
    state.pauseQueue.clear();
    state.resumeQueue.clear();
    state.asyncId = state.asyncTo = state.promise = void 0;
    if (cancelId) state.cancelId = cancelId;
}
var BailSignal = class extends Error {
    constructor(){
        super("An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.");
    }
};
var SkipAnimationSignal = class extends Error {
    constructor(){
        super("SkipAnimationSignal");
    }
};
// src/FrameValue.ts
var import_shared6 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
var import_animated = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+animated@10.0.1_react@19.0.0/node_modules/@react-spring/animated/dist/cjs/index.js [app-client] (ecmascript)");
var isFrameValue = (value)=>value instanceof FrameValue;
var nextId = 1;
var FrameValue = class extends import_shared6.FluidValue {
    constructor(){
        super(...arguments);
        this.id = nextId++;
        this._priority = 0;
    }
    get priority() {
        return this._priority;
    }
    set priority(priority) {
        if (this._priority != priority) {
            this._priority = priority;
            this._onPriorityChange(priority);
        }
    }
    /** Get the current value */ get() {
        const node = (0, import_animated.getAnimated)(this);
        return node && node.getValue();
    }
    /** Create a spring that maps our value to another value */ to(...args) {
        return import_shared6.Globals.to(this, args);
    }
    /** @deprecated Use the `to` method instead. */ interpolate(...args) {
        (0, import_shared6.deprecateInterpolate)();
        return import_shared6.Globals.to(this, args);
    }
    toJSON() {
        return this.get();
    }
    observerAdded(count) {
        if (count == 1) this._attach();
    }
    observerRemoved(count) {
        if (count == 0) this._detach();
    }
    /** Called when the first child is added. */ _attach() {}
    /** Called when the last child is removed. */ _detach() {}
    /** Tell our children about our new value */ _onChange(value, idle = false) {
        (0, import_shared6.callFluidObservers)(this, {
            type: "change",
            parent: this,
            value,
            idle
        });
    }
    /** Tell our children about our new priority */ _onPriorityChange(priority) {
        if (!this.idle) {
            import_shared6.frameLoop.sort(this);
        }
        (0, import_shared6.callFluidObservers)(this, {
            type: "priority",
            parent: this,
            priority
        });
    }
};
// src/SpringPhase.ts
var $P = Symbol.for("SpringPhase");
var HAS_ANIMATED = 1;
var IS_ANIMATING = 2;
var IS_PAUSED = 4;
var hasAnimated = (target)=>(target[$P] & HAS_ANIMATED) > 0;
var isAnimating = (target)=>(target[$P] & IS_ANIMATING) > 0;
var isPaused = (target)=>(target[$P] & IS_PAUSED) > 0;
var setActiveBit = (target, active)=>active ? target[$P] |= IS_ANIMATING | HAS_ANIMATED : target[$P] &= ~IS_ANIMATING;
var setPausedBit = (target, paused)=>paused ? target[$P] |= IS_PAUSED : target[$P] &= ~IS_PAUSED;
// src/SpringValue.ts
var SpringValue = class extends FrameValue {
    constructor(arg1, arg2){
        super();
        /** The animation state */ this.animation = new Animation();
        /** Some props have customizable default values */ this.defaultProps = {};
        /** The state for `runAsync` calls */ this._state = {
            paused: false,
            delayed: false,
            pauseQueue: /* @__PURE__ */ new Set(),
            resumeQueue: /* @__PURE__ */ new Set(),
            timeouts: /* @__PURE__ */ new Set()
        };
        /** The promise resolvers of pending `start` calls */ this._pendingCalls = /* @__PURE__ */ new Set();
        /** The counter for tracking `scheduleProps` calls */ this._lastCallId = 0;
        /** The last `scheduleProps` call that changed the `to` prop */ this._lastToId = 0;
        this._memoizedDuration = 0;
        if (!import_shared7.is.und(arg1) || !import_shared7.is.und(arg2)) {
            const props = import_shared7.is.obj(arg1) ? {
                ...arg1
            } : {
                ...arg2,
                from: arg1
            };
            if (import_shared7.is.und(props.default)) {
                props.default = true;
            }
            this.start(props);
        }
    }
    /** Equals true when not advancing on each frame. */ get idle() {
        return !(isAnimating(this) || this._state.asyncTo) || isPaused(this);
    }
    get goal() {
        return (0, import_shared7.getFluidValue)(this.animation.to);
    }
    get velocity() {
        const node = (0, import_animated2.getAnimated)(this);
        return node instanceof import_animated2.AnimatedValue ? node.lastVelocity || 0 : node.getPayload().map((node2)=>node2.lastVelocity || 0);
    }
    /**
   * When true, this value has been animated at least once.
   */ get hasAnimated() {
        return hasAnimated(this);
    }
    /**
   * When true, this value has an unfinished animation,
   * which is either active or paused.
   */ get isAnimating() {
        return isAnimating(this);
    }
    /**
   * When true, all current and future animations are paused.
   */ get isPaused() {
        return isPaused(this);
    }
    /**
   *
   *
   */ get isDelayed() {
        return this._state.delayed;
    }
    /** Advance the current animation by a number of milliseconds */ advance(dt) {
        let idle = true;
        let changed = false;
        const anim = this.animation;
        let { toValues } = anim;
        const { config: config2 } = anim;
        const payload = (0, import_animated2.getPayload)(anim.to);
        if (!payload && (0, import_shared7.hasFluidValue)(anim.to)) {
            toValues = (0, import_shared7.toArray)((0, import_shared7.getFluidValue)(anim.to));
        }
        anim.values.forEach((node2, i)=>{
            if (node2.done) return;
            const to2 = // Animated strings always go from 0 to 1.
            node2.constructor == import_animated2.AnimatedString ? 1 : payload ? payload[i].lastPosition : toValues[i];
            let finished = anim.immediate;
            let position = to2;
            if (!finished) {
                position = node2.lastPosition;
                if (config2.tension <= 0) {
                    node2.done = true;
                    return;
                }
                let elapsed = node2.elapsedTime += dt;
                const from = anim.fromValues[i];
                const v0 = node2.v0 != null ? node2.v0 : node2.v0 = import_shared7.is.arr(config2.velocity) ? config2.velocity[i] : config2.velocity;
                let velocity;
                const precision = config2.precision || (from == to2 ? 5e-3 : Math.min(1, Math.abs(to2 - from) * 1e-3));
                if (!import_shared7.is.und(config2.duration)) {
                    let p = 1;
                    if (config2.duration > 0) {
                        if (this._memoizedDuration !== config2.duration) {
                            this._memoizedDuration = config2.duration;
                            if (node2.durationProgress > 0) {
                                node2.elapsedTime = config2.duration * node2.durationProgress;
                                elapsed = node2.elapsedTime += dt;
                            }
                        }
                        p = (config2.progress || 0) + elapsed / this._memoizedDuration;
                        p = p > 1 ? 1 : p < 0 ? 0 : p;
                        node2.durationProgress = p;
                    }
                    position = from + config2.easing(p) * (to2 - from);
                    velocity = (position - node2.lastPosition) / dt;
                    finished = p == 1;
                } else if (config2.decay) {
                    const decay = config2.decay === true ? 0.998 : config2.decay;
                    const e = Math.exp(-(1 - decay) * elapsed);
                    position = from + v0 / (1 - decay) * (1 - e);
                    finished = Math.abs(node2.lastPosition - position) <= precision;
                    velocity = v0 * e;
                } else {
                    velocity = node2.lastVelocity == null ? v0 : node2.lastVelocity;
                    const restVelocity = config2.restVelocity || precision / 10;
                    const bounceFactor = config2.clamp ? 0 : config2.bounce;
                    const canBounce = !import_shared7.is.und(bounceFactor);
                    const isGrowing = from == to2 ? node2.v0 > 0 : from < to2;
                    let isMoving;
                    let isBouncing = false;
                    const step = 1;
                    const numSteps = Math.ceil(dt / step);
                    for(let n = 0; n < numSteps; ++n){
                        isMoving = Math.abs(velocity) > restVelocity;
                        if (!isMoving) {
                            finished = Math.abs(to2 - position) <= precision;
                            if (finished) {
                                break;
                            }
                        }
                        if (canBounce) {
                            isBouncing = position == to2 || position > to2 == isGrowing;
                            if (isBouncing) {
                                velocity = -velocity * bounceFactor;
                                position = to2;
                            }
                        }
                        const springForce = -config2.tension * 1e-6 * (position - to2);
                        const dampingForce = -config2.friction * 1e-3 * velocity;
                        const acceleration = (springForce + dampingForce) / config2.mass;
                        velocity = velocity + acceleration * step;
                        position = position + velocity * step;
                    }
                }
                node2.lastVelocity = velocity;
                if (Number.isNaN(position)) {
                    console.warn(`Got NaN while animating:`, this);
                    finished = true;
                }
            }
            if (payload && !payload[i].done) {
                finished = false;
            }
            if (finished) {
                node2.done = true;
            } else {
                idle = false;
            }
            if (node2.setValue(position, config2.round)) {
                changed = true;
            }
        });
        const node = (0, import_animated2.getAnimated)(this);
        const currVal = node.getValue();
        if (idle) {
            const finalVal = (0, import_shared7.getFluidValue)(anim.to);
            if ((currVal !== finalVal || changed) && !config2.decay) {
                node.setValue(finalVal);
                this._onChange(finalVal);
            } else if (changed && config2.decay) {
                this._onChange(currVal);
            }
            this._stop();
        } else if (changed) {
            this._onChange(currVal);
        }
    }
    /** Set the current value, while stopping the current animation */ set(value) {
        import_shared7.raf.batchedUpdates(()=>{
            this._stop();
            this._focus(value);
            this._set(value);
        });
        return this;
    }
    /**
   * Freeze the active animation in time, as well as any updates merged
   * before `resume` is called.
   */ pause() {
        this._update({
            pause: true
        });
    }
    /** Resume the animation if paused. */ resume() {
        this._update({
            pause: false
        });
    }
    /** Skip to the end of the current animation. */ finish() {
        if (isAnimating(this)) {
            const { to: to2, config: config2 } = this.animation;
            import_shared7.raf.batchedUpdates(()=>{
                this._onStart();
                if (!config2.decay) {
                    this._set(to2, false);
                }
                this._stop();
            });
        }
        return this;
    }
    /** Push props into the pending queue. */ update(props) {
        const queue = this.queue || (this.queue = []);
        queue.push(props);
        return this;
    }
    start(to2, arg2) {
        let queue;
        if (!import_shared7.is.und(to2)) {
            queue = [
                import_shared7.is.obj(to2) ? to2 : {
                    ...arg2,
                    to: to2
                }
            ];
        } else {
            queue = this.queue || [];
            this.queue = [];
        }
        return Promise.all(queue.map((props)=>{
            const up = this._update(props);
            return up;
        })).then((results)=>getCombinedResult(this, results));
    }
    /**
   * Stop the current animation, and cancel any delayed updates.
   *
   * Pass `true` to call `onRest` with `cancelled: true`.
   */ stop(cancel) {
        const { to: to2 } = this.animation;
        this._focus(this.get());
        stopAsync(this._state, cancel && this._lastCallId);
        import_shared7.raf.batchedUpdates(()=>this._stop(to2, cancel));
        return this;
    }
    /** Restart the animation. */ reset() {
        this._update({
            reset: true
        });
    }
    /** @internal */ eventObserved(event) {
        if (event.type == "change") {
            this._start();
        } else if (event.type == "priority") {
            this.priority = event.priority + 1;
        }
    }
    /**
   * Parse the `to` and `from` range from the given `props` object.
   *
   * This also ensures the initial value is available to animated components
   * during the render phase.
   */ _prepareNode(props) {
        const key = this.key || "";
        let { to: to2, from } = props;
        to2 = import_shared7.is.obj(to2) ? to2[key] : to2;
        if (to2 == null || isAsyncTo(to2)) {
            to2 = void 0;
        }
        from = import_shared7.is.obj(from) ? from[key] : from;
        if (from == null) {
            from = void 0;
        }
        const range = {
            to: to2,
            from
        };
        if (!hasAnimated(this)) {
            if (props.reverse) [to2, from] = [
                from,
                to2
            ];
            from = (0, import_shared7.getFluidValue)(from);
            if (!import_shared7.is.und(from)) {
                this._set(from);
            } else if (!(0, import_animated2.getAnimated)(this)) {
                this._set(to2);
            }
        }
        return range;
    }
    /** Every update is processed by this method before merging. */ _update({ ...props }, isLoop) {
        const { key, defaultProps } = this;
        if (props.default) Object.assign(defaultProps, getDefaultProps(props, (value, prop)=>/^on/.test(prop) ? resolveProp(value, key) : value));
        mergeActiveFn(this, props, "onProps");
        sendEvent(this, "onProps", props, this);
        const range = this._prepareNode(props);
        if (Object.isFrozen(this)) {
            throw Error("Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?");
        }
        const state = this._state;
        return scheduleProps(++this._lastCallId, {
            key,
            props,
            defaultProps,
            state,
            actions: {
                pause: ()=>{
                    if (!isPaused(this)) {
                        setPausedBit(this, true);
                        (0, import_shared7.flushCalls)(state.pauseQueue);
                        sendEvent(this, "onPause", getFinishedResult(this, checkFinished(this, this.animation.to)), this);
                    }
                },
                resume: ()=>{
                    if (isPaused(this)) {
                        setPausedBit(this, false);
                        if (isAnimating(this)) {
                            this._resume();
                        }
                        (0, import_shared7.flushCalls)(state.resumeQueue);
                        sendEvent(this, "onResume", getFinishedResult(this, checkFinished(this, this.animation.to)), this);
                    }
                },
                start: this._merge.bind(this, range)
            }
        }).then((result)=>{
            if (props.loop && result.finished && !(isLoop && result.noop)) {
                const nextProps = createLoopUpdate(props);
                if (nextProps) {
                    return this._update(nextProps, true);
                }
            }
            return result;
        });
    }
    /** Merge props into the current animation */ _merge(range, props, resolve) {
        if (props.cancel) {
            this.stop(true);
            return resolve(getCancelledResult(this));
        }
        const hasToProp = !import_shared7.is.und(range.to);
        const hasFromProp = !import_shared7.is.und(range.from);
        if (hasToProp || hasFromProp) {
            if (props.callId > this._lastToId) {
                this._lastToId = props.callId;
            } else {
                return resolve(getCancelledResult(this));
            }
        }
        const { key, defaultProps, animation: anim } = this;
        const { to: prevTo, from: prevFrom } = anim;
        let { to: to2 = prevTo, from = prevFrom } = range;
        if (hasFromProp && !hasToProp && (!props.default || import_shared7.is.und(to2))) {
            to2 = from;
        }
        if (props.reverse) [to2, from] = [
            from,
            to2
        ];
        const hasFromChanged = !(0, import_shared7.isEqual)(from, prevFrom);
        if (hasFromChanged) {
            anim.from = from;
        }
        from = (0, import_shared7.getFluidValue)(from);
        const hasToChanged = !(0, import_shared7.isEqual)(to2, prevTo);
        if (hasToChanged) {
            this._focus(to2);
        }
        const hasAsyncTo = isAsyncTo(props.to);
        const { config: config2 } = anim;
        const { decay, velocity } = config2;
        if (hasToProp || hasFromProp) {
            config2.velocity = 0;
        }
        if (props.config && !hasAsyncTo) {
            mergeConfig(config2, callProp(props.config, key), // Avoid calling the same "config" prop twice.
            props.config !== defaultProps.config ? callProp(defaultProps.config, key) : void 0);
        }
        let node = (0, import_animated2.getAnimated)(this);
        if (!node || import_shared7.is.und(to2)) {
            return resolve(getFinishedResult(this, true));
        }
        const reset = // When `reset` is undefined, the `from` prop implies `reset: true`,
        // except for declarative updates. When `reset` is defined, there
        // must exist a value to animate from.
        import_shared7.is.und(props.reset) ? hasFromProp && !props.default : !import_shared7.is.und(from) && matchProp(props.reset, key);
        const value = reset ? from : this.get();
        const goal = computeGoal(to2);
        const isAnimatable = import_shared7.is.num(goal) || import_shared7.is.arr(goal) || (0, import_shared7.isAnimatedString)(goal);
        const immediate = !hasAsyncTo && (!isAnimatable || matchProp(defaultProps.immediate || props.immediate, key));
        if (hasToChanged) {
            const nodeType = (0, import_animated2.getAnimatedType)(to2);
            if (nodeType !== node.constructor) {
                if (immediate) {
                    node = this._set(goal);
                } else throw Error(`Cannot animate between ${node.constructor.name} and ${nodeType.name}, as the "to" prop suggests`);
            }
        }
        const goalType = node.constructor;
        let started = (0, import_shared7.hasFluidValue)(to2);
        let finished = false;
        if (!started) {
            const hasValueChanged = reset || !hasAnimated(this) && hasFromChanged;
            if (hasToChanged || hasValueChanged) {
                finished = (0, import_shared7.isEqual)(computeGoal(value), goal);
                started = !finished;
            }
            if (!(0, import_shared7.isEqual)(anim.immediate, immediate) && !immediate || !(0, import_shared7.isEqual)(config2.decay, decay) || !(0, import_shared7.isEqual)(config2.velocity, velocity)) {
                started = true;
            }
        }
        if (finished && isAnimating(this)) {
            if (anim.changed && !reset) {
                started = true;
            } else if (!started) {
                this._stop(prevTo);
            }
        }
        if (!hasAsyncTo) {
            if (started || (0, import_shared7.hasFluidValue)(prevTo)) {
                anim.values = node.getPayload();
                anim.toValues = (0, import_shared7.hasFluidValue)(to2) ? null : goalType == import_animated2.AnimatedString ? [
                    1
                ] : (0, import_shared7.toArray)(goal);
            }
            if (anim.immediate != immediate) {
                anim.immediate = immediate;
                if (!immediate && !reset) {
                    this._set(prevTo);
                }
            }
            if (started) {
                const { onRest } = anim;
                (0, import_shared7.each)(ACTIVE_EVENTS, (type)=>mergeActiveFn(this, props, type));
                const result = getFinishedResult(this, checkFinished(this, prevTo));
                (0, import_shared7.flushCalls)(this._pendingCalls, result);
                this._pendingCalls.add(resolve);
                if (anim.changed) import_shared7.raf.batchedUpdates(()=>{
                    anim.changed = !reset;
                    onRest?.(result, this);
                    if (reset) {
                        callProp(defaultProps.onRest, result);
                    } else {
                        anim.onStart?.(result, this);
                    }
                });
            }
        }
        if (reset) {
            this._set(value);
        }
        if (hasAsyncTo) {
            resolve(runAsync(props.to, props, this._state, this));
        } else if (started) {
            this._start();
        } else if (isAnimating(this) && !hasToChanged) {
            this._pendingCalls.add(resolve);
        } else {
            resolve(getNoopResult(value));
        }
    }
    /** Update the `animation.to` value, which might be a `FluidValue` */ _focus(value) {
        const anim = this.animation;
        if (value !== anim.to) {
            if ((0, import_shared7.getFluidObservers)(this)) {
                this._detach();
            }
            anim.to = value;
            if ((0, import_shared7.getFluidObservers)(this)) {
                this._attach();
            }
        }
    }
    _attach() {
        let priority = 0;
        const { to: to2 } = this.animation;
        if ((0, import_shared7.hasFluidValue)(to2)) {
            (0, import_shared7.addFluidObserver)(to2, this);
            if (isFrameValue(to2)) {
                priority = to2.priority + 1;
            }
        }
        this.priority = priority;
    }
    _detach() {
        const { to: to2 } = this.animation;
        if ((0, import_shared7.hasFluidValue)(to2)) {
            (0, import_shared7.removeFluidObserver)(to2, this);
        }
    }
    /**
   * Update the current value from outside the frameloop,
   * and return the `Animated` node.
   */ _set(arg, idle = true) {
        const value = (0, import_shared7.getFluidValue)(arg);
        if (!import_shared7.is.und(value)) {
            const oldNode = (0, import_animated2.getAnimated)(this);
            if (!oldNode || !(0, import_shared7.isEqual)(value, oldNode.getValue())) {
                const nodeType = (0, import_animated2.getAnimatedType)(value);
                if (!oldNode || oldNode.constructor != nodeType) {
                    (0, import_animated2.setAnimated)(this, nodeType.create(value));
                } else {
                    oldNode.setValue(value);
                }
                if (oldNode) {
                    import_shared7.raf.batchedUpdates(()=>{
                        this._onChange(value, idle);
                    });
                }
            }
        }
        return (0, import_animated2.getAnimated)(this);
    }
    _onStart() {
        const anim = this.animation;
        if (!anim.changed) {
            anim.changed = true;
            sendEvent(this, "onStart", getFinishedResult(this, checkFinished(this, anim.to)), this);
        }
    }
    _onChange(value, idle) {
        if (!idle) {
            this._onStart();
            callProp(this.animation.onChange, value, this);
        }
        callProp(this.defaultProps.onChange, value, this);
        super._onChange(value, idle);
    }
    // This method resets the animation state (even if already animating) to
    // ensure the latest from/to range is used, and it also ensures this spring
    // is added to the frameloop.
    _start() {
        const anim = this.animation;
        (0, import_animated2.getAnimated)(this).reset((0, import_shared7.getFluidValue)(anim.to));
        if (!anim.immediate) {
            anim.fromValues = anim.values.map((node)=>node.lastPosition);
        }
        if (!isAnimating(this)) {
            setActiveBit(this, true);
            if (!isPaused(this)) {
                this._resume();
            }
        }
    }
    _resume() {
        if (import_shared7.Globals.skipAnimation) {
            this.finish();
        } else {
            import_shared7.frameLoop.start(this);
        }
    }
    /**
   * Exit the frameloop and notify `onRest` listeners.
   *
   * Always wrap `_stop` calls with `batchedUpdates`.
   */ _stop(goal, cancel) {
        if (isAnimating(this)) {
            setActiveBit(this, false);
            const anim = this.animation;
            (0, import_shared7.each)(anim.values, (node)=>{
                node.done = true;
            });
            if (anim.toValues) {
                anim.onChange = anim.onPause = anim.onResume = void 0;
            }
            (0, import_shared7.callFluidObservers)(this, {
                type: "idle",
                parent: this
            });
            const result = cancel ? getCancelledResult(this.get()) : getFinishedResult(this.get(), checkFinished(this, goal ?? anim.to));
            (0, import_shared7.flushCalls)(this._pendingCalls, result);
            if (anim.changed) {
                anim.changed = false;
                sendEvent(this, "onRest", result, this);
            }
        }
    }
};
function checkFinished(target, to2) {
    const goal = computeGoal(to2);
    const value = computeGoal(target.get());
    return (0, import_shared7.isEqual)(value, goal);
}
function createLoopUpdate(props, loop = props.loop, to2 = props.to) {
    const loopRet = callProp(loop);
    if (loopRet) {
        const overrides = loopRet !== true && inferTo(loopRet);
        const reverse = (overrides || props).reverse;
        const reset = !overrides || overrides.reset;
        return createUpdate({
            ...props,
            loop,
            // Avoid updating default props when looping.
            default: false,
            // Never loop the `pause` prop.
            pause: void 0,
            // For the "reverse" prop to loop as expected, the "to" prop
            // must be undefined. The "reverse" prop is ignored when the
            // "to" prop is an array or function.
            to: !reverse || isAsyncTo(to2) ? to2 : void 0,
            // Ignore the "from" prop except on reset.
            from: reset ? props.from : void 0,
            reset,
            // The "loop" prop can return a "useSpring" props object to
            // override any of the original props.
            ...overrides
        });
    }
}
function createUpdate(props) {
    const { to: to2, from } = props = inferTo(props);
    const keys = /* @__PURE__ */ new Set();
    if (import_shared7.is.obj(to2)) findDefined(to2, keys);
    if (import_shared7.is.obj(from)) findDefined(from, keys);
    props.keys = keys.size ? Array.from(keys) : null;
    return props;
}
function declareUpdate(props) {
    const update2 = createUpdate(props);
    if (import_shared7.is.und(update2.default)) {
        update2.default = getDefaultProps(update2);
    }
    return update2;
}
function findDefined(values, keys) {
    (0, import_shared7.eachProp)(values, (value, key)=>value != null && keys.add(key));
}
var ACTIVE_EVENTS = [
    "onStart",
    "onRest",
    "onChange",
    "onPause",
    "onResume"
];
function mergeActiveFn(target, props, type) {
    target.animation[type] = props[type] !== getDefaultProp(props, type) ? resolveProp(props[type], target.key) : void 0;
}
function sendEvent(target, type, ...args) {
    target.animation[type]?.(...args);
    target.defaultProps[type]?.(...args);
}
// src/Controller.ts
var import_shared8 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
var BATCHED_EVENTS = [
    "onStart",
    "onChange",
    "onRest"
];
var nextId2 = 1;
var Controller = class {
    constructor(props, flush3){
        this.id = nextId2++;
        /** The animated values */ this.springs = {};
        /** The queue of props passed to the `update` method. */ this.queue = [];
        /** The counter for tracking `scheduleProps` calls */ this._lastAsyncId = 0;
        /** The values currently being animated */ this._active = /* @__PURE__ */ new Set();
        /** The values that changed recently */ this._changed = /* @__PURE__ */ new Set();
        /** Equals false when `onStart` listeners can be called */ this._started = false;
        /** State used by the `runAsync` function */ this._state = {
            paused: false,
            pauseQueue: /* @__PURE__ */ new Set(),
            resumeQueue: /* @__PURE__ */ new Set(),
            timeouts: /* @__PURE__ */ new Set()
        };
        /** The event queues that are flushed once per frame maximum */ this._events = {
            onStart: /* @__PURE__ */ new Map(),
            onChange: /* @__PURE__ */ new Map(),
            onRest: /* @__PURE__ */ new Map()
        };
        this._onFrame = this._onFrame.bind(this);
        if (flush3) {
            this._flush = flush3;
        }
        if (props) {
            this.start({
                default: true,
                ...props
            });
        }
    }
    /**
   * Equals `true` when no spring values are in the frameloop, and
   * no async animation is currently active.
   */ get idle() {
        return !this._state.asyncTo && Object.values(this.springs).every((spring)=>{
            return spring.idle && !spring.isDelayed && !spring.isPaused;
        });
    }
    get item() {
        return this._item;
    }
    set item(item) {
        this._item = item;
    }
    /** Get the current values of our springs */ get() {
        const values = {};
        this.each((spring, key)=>values[key] = spring.get());
        return values;
    }
    /** Set the current values without animating. */ set(values) {
        for(const key in values){
            const value = values[key];
            if (!import_shared8.is.und(value)) {
                this.springs[key].set(value);
            }
        }
    }
    /** Push an update onto the queue of each value. */ update(props) {
        if (props) {
            this.queue.push(createUpdate(props));
        }
        return this;
    }
    /**
   * Start the queued animations for every spring, and resolve the returned
   * promise once all queued animations have finished or been cancelled.
   *
   * When you pass a queue (instead of nothing), that queue is used instead of
   * the queued animations added with the `update` method, which are left alone.
   */ start(props) {
        let { queue } = this;
        if (props) {
            queue = (0, import_shared8.toArray)(props).map(createUpdate);
        } else {
            this.queue = [];
        }
        if (this._flush) {
            return this._flush(this, queue);
        }
        prepareKeys(this, queue);
        return flushUpdateQueue(this, queue);
    }
    /** @internal */ stop(arg, keys) {
        if (arg !== !!arg) {
            keys = arg;
        }
        if (keys) {
            const springs = this.springs;
            (0, import_shared8.each)((0, import_shared8.toArray)(keys), (key)=>springs[key].stop(!!arg));
        } else {
            stopAsync(this._state, this._lastAsyncId);
            this.each((spring)=>spring.stop(!!arg));
        }
        return this;
    }
    /** Freeze the active animation in time */ pause(keys) {
        if (import_shared8.is.und(keys)) {
            this.start({
                pause: true
            });
        } else {
            const springs = this.springs;
            (0, import_shared8.each)((0, import_shared8.toArray)(keys), (key)=>springs[key].pause());
        }
        return this;
    }
    /** Resume the animation if paused. */ resume(keys) {
        if (import_shared8.is.und(keys)) {
            this.start({
                pause: false
            });
        } else {
            const springs = this.springs;
            (0, import_shared8.each)((0, import_shared8.toArray)(keys), (key)=>springs[key].resume());
        }
        return this;
    }
    /** Call a function once per spring value */ each(iterator) {
        (0, import_shared8.eachProp)(this.springs, iterator);
    }
    /** @internal Called at the end of every animation frame */ _onFrame() {
        const { onStart, onChange, onRest } = this._events;
        const active = this._active.size > 0;
        const changed = this._changed.size > 0;
        if (active && !this._started || changed && !this._started) {
            this._started = true;
            (0, import_shared8.flush)(onStart, ([onStart2, result])=>{
                result.value = this.get();
                onStart2(result, this, this._item);
            });
        }
        const idle = !active && this._started;
        const values = changed || idle && onRest.size ? this.get() : null;
        if (changed && onChange.size) {
            (0, import_shared8.flush)(onChange, ([onChange2, result])=>{
                result.value = values;
                onChange2(result, this, this._item);
            });
        }
        if (idle) {
            this._started = false;
            (0, import_shared8.flush)(onRest, ([onRest2, result])=>{
                result.value = values;
                onRest2(result, this, this._item);
            });
        }
    }
    /** @internal */ eventObserved(event) {
        if (event.type == "change") {
            this._changed.add(event.parent);
            if (!event.idle) {
                this._active.add(event.parent);
            }
        } else if (event.type == "idle") {
            this._active.delete(event.parent);
        } else return;
        import_shared8.raf.onFrame(this._onFrame);
    }
};
function flushUpdateQueue(ctrl, queue) {
    return Promise.all(queue.map((props)=>flushUpdate(ctrl, props))).then((results)=>getCombinedResult(ctrl, results));
}
async function flushUpdate(ctrl, props, isLoop) {
    const { keys, to: to2, from, loop, onRest, onResolve } = props;
    const defaults2 = import_shared8.is.obj(props.default) && props.default;
    if (loop) {
        props.loop = false;
    }
    if (to2 === false) props.to = null;
    if (from === false) props.from = null;
    const asyncTo = import_shared8.is.arr(to2) || import_shared8.is.fun(to2) ? to2 : void 0;
    if (asyncTo) {
        props.to = void 0;
        props.onRest = void 0;
        if (defaults2) {
            defaults2.onRest = void 0;
        }
    } else {
        (0, import_shared8.each)(BATCHED_EVENTS, (key)=>{
            const handler = props[key];
            if (import_shared8.is.fun(handler)) {
                const queue = ctrl["_events"][key];
                props[key] = ({ finished, cancelled })=>{
                    const result2 = queue.get(handler);
                    if (result2) {
                        if (!finished) result2.finished = false;
                        if (cancelled) result2.cancelled = true;
                    } else {
                        queue.set(handler, {
                            value: null,
                            finished: finished || false,
                            cancelled: cancelled || false
                        });
                    }
                };
                if (defaults2) {
                    defaults2[key] = props[key];
                }
            }
        });
    }
    const state = ctrl["_state"];
    if (props.pause === !state.paused) {
        state.paused = props.pause;
        (0, import_shared8.flushCalls)(props.pause ? state.pauseQueue : state.resumeQueue);
    } else if (state.paused) {
        props.pause = true;
    }
    const promises = (keys || Object.keys(ctrl.springs)).map((key)=>ctrl.springs[key].start(props));
    const cancel = props.cancel === true || getDefaultProp(props, "cancel") === true;
    if (asyncTo || cancel && state.asyncId) {
        promises.push(scheduleProps(++ctrl["_lastAsyncId"], {
            props,
            state,
            actions: {
                pause: import_shared8.noop,
                resume: import_shared8.noop,
                start (props2, resolve) {
                    if (cancel) {
                        stopAsync(state, ctrl["_lastAsyncId"]);
                        resolve(getCancelledResult(ctrl));
                    } else {
                        props2.onRest = onRest;
                        resolve(runAsync(asyncTo, props2, state, ctrl));
                    }
                }
            }
        }));
    }
    if (state.paused) {
        await new Promise((resume)=>{
            state.resumeQueue.add(resume);
        });
    }
    const result = getCombinedResult(ctrl, await Promise.all(promises));
    if (loop && result.finished && !(isLoop && result.noop)) {
        const nextProps = createLoopUpdate(props, loop, to2);
        if (nextProps) {
            prepareKeys(ctrl, [
                nextProps
            ]);
            return flushUpdate(ctrl, nextProps, true);
        }
    }
    if (onResolve) {
        import_shared8.raf.batchedUpdates(()=>onResolve(result, ctrl, ctrl.item));
    }
    return result;
}
function getSprings(ctrl, props) {
    const springs = {
        ...ctrl.springs
    };
    if (props) {
        (0, import_shared8.each)((0, import_shared8.toArray)(props), (props2)=>{
            if (import_shared8.is.und(props2.keys)) {
                props2 = createUpdate(props2);
            }
            if (!import_shared8.is.obj(props2.to)) {
                props2 = {
                    ...props2,
                    to: void 0
                };
            }
            prepareSprings(springs, props2, (key)=>{
                return createSpring(key);
            });
        });
    }
    setSprings(ctrl, springs);
    return springs;
}
function setSprings(ctrl, springs) {
    (0, import_shared8.eachProp)(springs, (spring, key)=>{
        if (!ctrl.springs[key]) {
            ctrl.springs[key] = spring;
            (0, import_shared8.addFluidObserver)(spring, ctrl);
        }
    });
}
function createSpring(key, observer) {
    const spring = new SpringValue();
    spring.key = key;
    if (observer) {
        (0, import_shared8.addFluidObserver)(spring, observer);
    }
    return spring;
}
function prepareSprings(springs, props, create) {
    if (props.keys) {
        (0, import_shared8.each)(props.keys, (key)=>{
            const spring = springs[key] || (springs[key] = create(key));
            spring["_prepareNode"](props);
        });
    }
}
function prepareKeys(ctrl, queue) {
    (0, import_shared8.each)(queue, (props)=>{
        prepareSprings(ctrl.springs, props, (key)=>{
            return createSpring(key, ctrl);
        });
    });
}
// src/SpringContext.tsx
var React = __toESM(__turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"));
var import_react = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var SpringContext = React.createContext({
    pause: false,
    immediate: false
});
// src/SpringRef.ts
var import_shared9 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
var SpringRef = ()=>{
    const current = [];
    const SpringRef2 = function(props) {
        (0, import_shared9.deprecateDirectCall)();
        const results = [];
        (0, import_shared9.each)(current, (ctrl, i)=>{
            if (import_shared9.is.und(props)) {
                results.push(ctrl.start());
            } else {
                const update2 = _getProps(props, ctrl, i);
                if (update2) {
                    results.push(ctrl.start(update2));
                }
            }
        });
        return results;
    };
    SpringRef2.current = current;
    SpringRef2.add = function(ctrl) {
        if (!current.includes(ctrl)) {
            current.push(ctrl);
        }
    };
    SpringRef2.delete = function(ctrl) {
        const i = current.indexOf(ctrl);
        if (~i) current.splice(i, 1);
    };
    SpringRef2.pause = function() {
        (0, import_shared9.each)(current, (ctrl)=>ctrl.pause(...arguments));
        return this;
    };
    SpringRef2.resume = function() {
        (0, import_shared9.each)(current, (ctrl)=>ctrl.resume(...arguments));
        return this;
    };
    SpringRef2.set = function(values) {
        (0, import_shared9.each)(current, (ctrl, i)=>{
            const update2 = import_shared9.is.fun(values) ? values(i, ctrl) : values;
            if (update2) {
                ctrl.set(update2);
            }
        });
    };
    SpringRef2.start = function(props) {
        const results = [];
        (0, import_shared9.each)(current, (ctrl, i)=>{
            if (import_shared9.is.und(props)) {
                results.push(ctrl.start());
            } else {
                const update2 = this._getProps(props, ctrl, i);
                if (update2) {
                    results.push(ctrl.start(update2));
                }
            }
        });
        return results;
    };
    SpringRef2.stop = function() {
        (0, import_shared9.each)(current, (ctrl)=>ctrl.stop(...arguments));
        return this;
    };
    SpringRef2.update = function(props) {
        (0, import_shared9.each)(current, (ctrl, i)=>ctrl.update(this._getProps(props, ctrl, i)));
        return this;
    };
    const _getProps = function(arg, ctrl, index) {
        return import_shared9.is.fun(arg) ? arg(index, ctrl) : arg;
    };
    SpringRef2._getProps = _getProps;
    return SpringRef2;
};
// src/hooks/useSprings.ts
function useSprings(length, props, deps) {
    const propsFn = import_shared10.is.fun(props) && props;
    if (propsFn && !deps) deps = [];
    const ref = (0, import_react2.useMemo)(()=>propsFn || arguments.length == 3 ? SpringRef() : void 0, []);
    const layoutId = (0, import_react2.useRef)(0);
    const forceUpdate = (0, import_shared10.useForceUpdate)();
    const state = (0, import_react2.useMemo)(()=>({
            ctrls: [],
            queue: [],
            flush (ctrl, updates2) {
                const springs2 = getSprings(ctrl, updates2);
                const canFlushSync = layoutId.current > 0 && !state.queue.length && !Object.keys(springs2).some((key)=>!ctrl.springs[key]);
                return canFlushSync ? flushUpdateQueue(ctrl, updates2) : new Promise((resolve)=>{
                    setSprings(ctrl, springs2);
                    state.queue.push(()=>{
                        resolve(flushUpdateQueue(ctrl, updates2));
                    });
                    forceUpdate();
                });
            }
        }), []);
    const ctrls = (0, import_react2.useRef)([
        ...state.ctrls
    ]);
    const updates = (0, import_react2.useRef)([]);
    const prevLength = (0, import_shared10.usePrev)(length) || 0;
    (0, import_react2.useMemo)(()=>{
        (0, import_shared10.each)(ctrls.current.slice(length, prevLength), (ctrl)=>{
            detachRefs(ctrl, ref);
            ctrl.stop(true);
        });
        ctrls.current.length = length;
        declareUpdates(prevLength, length);
    }, [
        length
    ]);
    (0, import_react2.useMemo)(()=>{
        declareUpdates(0, Math.min(prevLength, length));
    }, deps);
    function declareUpdates(startIndex, endIndex) {
        for(let i = startIndex; i < endIndex; i++){
            const ctrl = ctrls.current[i] || (ctrls.current[i] = new Controller(null, state.flush));
            const update2 = propsFn ? propsFn(i, ctrl) : props[i];
            if (update2) {
                updates.current[i] = declareUpdate(update2);
            }
        }
    }
    const springs = ctrls.current.map((ctrl, i)=>getSprings(ctrl, updates.current[i]));
    const context = (0, import_react2.useContext)(SpringContext);
    const prevContext = (0, import_shared10.usePrev)(context);
    const hasContext = context !== prevContext && hasProps(context);
    (0, import_shared10.useIsomorphicLayoutEffect)(()=>{
        layoutId.current++;
        state.ctrls = ctrls.current;
        const { queue } = state;
        if (queue.length) {
            state.queue = [];
            (0, import_shared10.each)(queue, (cb)=>cb());
        }
        (0, import_shared10.each)(ctrls.current, (ctrl, i)=>{
            ref?.add(ctrl);
            if (hasContext) {
                ctrl.start({
                    default: context
                });
            }
            const update2 = updates.current[i];
            if (update2) {
                replaceRef(ctrl, update2.ref);
                if (ctrl.ref) {
                    ctrl.queue.push(update2);
                } else {
                    ctrl.start(update2);
                }
            }
        });
    });
    (0, import_shared10.useOnce)(()=>()=>{
            (0, import_shared10.each)(state.ctrls, (ctrl)=>ctrl.stop(true));
        });
    const values = springs.map((x)=>({
            ...x
        }));
    return ref ? [
        values,
        ref
    ] : values;
}
// src/hooks/useSpring.ts
function useSpring(props, deps) {
    const isFn = import_shared11.is.fun(props);
    const [[values], ref] = useSprings(1, isFn ? props : [
        props
    ], isFn ? deps || [] : deps);
    return isFn || arguments.length == 2 ? [
        values,
        ref
    ] : values;
}
// src/hooks/useSpringRef.ts
var import_react3 = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var initSpringRef = ()=>SpringRef();
var useSpringRef = ()=>(0, import_react3.useState)(initSpringRef)[0];
// src/hooks/useSpringValue.ts
var import_shared12 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
var useSpringValue = (initial, props)=>{
    const springValue = (0, import_shared12.useConstant)(()=>new SpringValue(initial, props));
    (0, import_shared12.useOnce)(()=>()=>{
            springValue.stop();
        });
    return springValue;
};
// src/hooks/useTrail.ts
var import_shared13 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
function useTrail(length, propsArg, deps) {
    const propsFn = import_shared13.is.fun(propsArg) && propsArg;
    if (propsFn && !deps) deps = [];
    let reverse = true;
    let passedRef = void 0;
    const result = useSprings(length, {
        "useTrail.useSprings[result]": (i, ctrl)=>{
            const props = propsFn ? propsFn(i, ctrl) : propsArg;
            passedRef = props.ref;
            reverse = reverse && props.reverse;
            return props;
        }
    }["useTrail.useSprings[result]"], // Ensure the props function is called when no deps exist.
    // This works around the 3 argument rule.
    deps || [
        {}
    ]);
    (0, import_shared13.useIsomorphicLayoutEffect)(()=>{
        (0, import_shared13.each)(result[1].current, (ctrl, i)=>{
            const parent = result[1].current[i + (reverse ? 1 : -1)];
            replaceRef(ctrl, passedRef);
            if (ctrl.ref) {
                if (parent) {
                    ctrl.update({
                        to: parent.springs
                    });
                }
                return;
            }
            if (parent) {
                ctrl.start({
                    to: parent.springs
                });
            } else {
                ctrl.start();
            }
        });
    }, deps);
    if (propsFn || arguments.length == 3) {
        const ref = passedRef ?? result[1];
        ref["_getProps"] = (propsArg2, ctrl, i)=>{
            const props = import_shared13.is.fun(propsArg2) ? propsArg2(i, ctrl) : propsArg2;
            if (props) {
                const parent = ref.current[i + (props.reverse ? 1 : -1)];
                if (parent) props.to = parent.springs;
                return props;
            }
        };
        return result;
    }
    return result[0];
}
// src/hooks/useTransition.tsx
var React2 = __toESM(__turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"));
var import_react4 = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var import_shared14 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
function useTransition(data, props, deps) {
    const propsFn = import_shared14.is.fun(props) && props;
    const { reset, sort, trail = 0, expires = true, exitBeforeEnter = false, onDestroyed, ref: propsRef, config: propsConfig } = propsFn ? propsFn() : props;
    const ref = (0, import_react4.useMemo)(()=>propsFn || arguments.length == 3 ? SpringRef() : void 0, []);
    const items = (0, import_shared14.toArray)(data);
    const transitions = [];
    const usedTransitions = (0, import_react4.useRef)(null);
    const prevTransitions = reset ? null : usedTransitions.current;
    (0, import_shared14.useIsomorphicLayoutEffect)(()=>{
        usedTransitions.current = transitions;
    });
    (0, import_shared14.useOnce)(()=>{
        (0, import_shared14.each)(transitions, (t)=>{
            ref?.add(t.ctrl);
            t.ctrl.ref = ref;
        });
        return ()=>{
            (0, import_shared14.each)(usedTransitions.current, (t)=>{
                if (t.expired) {
                    clearTimeout(t.expirationId);
                }
                detachRefs(t.ctrl, ref);
                t.ctrl.stop(true);
            });
        };
    });
    const keys = getKeys(items, propsFn ? propsFn() : props, prevTransitions);
    const expired = reset && usedTransitions.current || [];
    (0, import_shared14.useIsomorphicLayoutEffect)(()=>(0, import_shared14.each)(expired, ({ ctrl, item, key })=>{
            detachRefs(ctrl, ref);
            callProp(onDestroyed, item, key);
        }));
    const reused = [];
    if (prevTransitions) (0, import_shared14.each)(prevTransitions, (t, i)=>{
        if (t.expired) {
            clearTimeout(t.expirationId);
            expired.push(t);
        } else {
            i = reused[i] = keys.indexOf(t.key);
            if (~i) transitions[i] = t;
        }
    });
    (0, import_shared14.each)(items, (item, i)=>{
        if (!transitions[i]) {
            transitions[i] = {
                key: keys[i],
                item,
                phase: "mount" /* MOUNT */ ,
                ctrl: new Controller()
            };
            transitions[i].ctrl.item = item;
        }
    });
    if (reused.length) {
        let i = -1;
        const { leave } = propsFn ? propsFn() : props;
        (0, import_shared14.each)(reused, (keyIndex, prevIndex)=>{
            const t = prevTransitions[prevIndex];
            if (~keyIndex) {
                i = transitions.indexOf(t);
                transitions[i] = {
                    ...t,
                    item: items[keyIndex]
                };
            } else if (leave) {
                transitions.splice(++i, 0, t);
            }
        });
    }
    if (import_shared14.is.fun(sort)) {
        transitions.sort((a, b)=>sort(a.item, b.item));
    }
    let delay = -trail;
    const forceUpdate = (0, import_shared14.useForceUpdate)();
    const defaultProps = getDefaultProps(props);
    const changes = /* @__PURE__ */ new Map();
    const exitingTransitions = (0, import_react4.useRef)(/* @__PURE__ */ new Map());
    const forceChange = (0, import_react4.useRef)(false);
    (0, import_shared14.each)(transitions, (t, i)=>{
        const key = t.key;
        const prevPhase = t.phase;
        const p = propsFn ? propsFn() : props;
        let to2;
        let phase;
        const propsDelay = callProp(p.delay || 0, key);
        if (prevPhase == "mount" /* MOUNT */ ) {
            to2 = p.enter;
            phase = "enter" /* ENTER */ ;
        } else {
            const isLeave = keys.indexOf(key) < 0;
            if (prevPhase != "leave" /* LEAVE */ ) {
                if (isLeave) {
                    to2 = p.leave;
                    phase = "leave" /* LEAVE */ ;
                } else if (to2 = p.update) {
                    phase = "update" /* UPDATE */ ;
                } else return;
            } else if (!isLeave) {
                to2 = p.enter;
                phase = "enter" /* ENTER */ ;
            } else return;
        }
        to2 = callProp(to2, t.item, i);
        to2 = import_shared14.is.obj(to2) ? inferTo(to2) : {
            to: to2
        };
        if (!to2.config) {
            const config2 = propsConfig || defaultProps.config;
            to2.config = callProp(config2, t.item, i, phase);
        }
        delay += trail;
        const payload = {
            ...defaultProps,
            // we need to add our props.delay value you here.
            delay: propsDelay + delay,
            ref: propsRef,
            immediate: p.immediate,
            // This prevents implied resets.
            reset: false,
            // Merge any phase-specific props.
            ...to2
        };
        if (phase == "enter" /* ENTER */  && import_shared14.is.und(payload.from)) {
            const p2 = propsFn ? propsFn() : props;
            const from = import_shared14.is.und(p2.initial) || prevTransitions ? p2.from : p2.initial;
            payload.from = callProp(from, t.item, i);
        }
        const { onResolve } = payload;
        payload.onResolve = (result)=>{
            callProp(onResolve, result);
            const transitions2 = usedTransitions.current;
            const t2 = transitions2.find((t3)=>t3.key === key);
            if (!t2) return;
            if (result.cancelled && t2.phase != "update" /* UPDATE */ ) {
                return;
            }
            if (t2.ctrl.idle) {
                const idle = transitions2.every((t3)=>t3.ctrl.idle);
                if (t2.phase == "leave" /* LEAVE */ ) {
                    const expiry = callProp(expires, t2.item);
                    if (expiry !== false) {
                        const expiryMs = expiry === true ? 0 : expiry;
                        t2.expired = true;
                        if (!idle && expiryMs > 0) {
                            if (expiryMs <= 2147483647) t2.expirationId = setTimeout(forceUpdate, expiryMs);
                            return;
                        }
                    }
                }
                if (idle && transitions2.some((t3)=>t3.expired)) {
                    exitingTransitions.current.delete(t2);
                    if (exitBeforeEnter) {
                        forceChange.current = true;
                    }
                    forceUpdate();
                }
            }
        };
        const springs = getSprings(t.ctrl, payload);
        if (phase === "leave" /* LEAVE */  && exitBeforeEnter) {
            exitingTransitions.current.set(t, {
                phase,
                springs,
                payload
            });
        } else {
            changes.set(t, {
                phase,
                springs,
                payload
            });
        }
    });
    const context = (0, import_react4.useContext)(SpringContext);
    const prevContext = (0, import_shared14.usePrev)(context);
    const hasContext = context !== prevContext && hasProps(context);
    (0, import_shared14.useIsomorphicLayoutEffect)(()=>{
        if (hasContext) {
            (0, import_shared14.each)(transitions, (t)=>{
                t.ctrl.start({
                    default: context
                });
            });
        }
    }, [
        context
    ]);
    (0, import_shared14.each)(changes, (_, t)=>{
        if (exitingTransitions.current.size) {
            const ind = transitions.findIndex((state)=>state.key === t.key);
            transitions.splice(ind, 1);
        }
    });
    (0, import_shared14.useIsomorphicLayoutEffect)(()=>{
        (0, import_shared14.each)(exitingTransitions.current.size ? exitingTransitions.current : changes, ({ phase, payload }, t)=>{
            const { ctrl } = t;
            t.phase = phase;
            ref?.add(ctrl);
            if (hasContext && phase == "enter" /* ENTER */ ) {
                ctrl.start({
                    default: context
                });
            }
            if (payload) {
                replaceRef(ctrl, payload.ref);
                if ((ctrl.ref || ref) && !forceChange.current) {
                    ctrl.update(payload);
                } else {
                    ctrl.start(payload);
                    if (forceChange.current) {
                        forceChange.current = false;
                    }
                }
            }
        });
    }, reset ? void 0 : deps);
    const renderTransitions = (render)=>/* @__PURE__ */ React2.createElement(React2.Fragment, null, transitions.map((t, i)=>{
            const { springs } = changes.get(t) || t.ctrl;
            const elem = render({
                ...springs
            }, t.item, t, i);
            return elem && elem.type ? /* @__PURE__ */ React2.createElement(elem.type, {
                ...elem.props,
                key: import_shared14.is.str(t.key) || import_shared14.is.num(t.key) ? t.key : t.ctrl.id,
                ref: elem.ref
            }) : elem;
        }));
    return ref ? [
        renderTransitions,
        ref
    ] : renderTransitions;
}
var nextKey = 1;
function getKeys(items, { key, keys = key }, prevTransitions) {
    if (keys === null) {
        const reused = /* @__PURE__ */ new Set();
        return items.map((item)=>{
            const t = prevTransitions && prevTransitions.find((t2)=>t2.item === item && t2.phase !== "leave" /* LEAVE */  && !reused.has(t2));
            if (t) {
                reused.add(t);
                return t.key;
            }
            return nextKey++;
        });
    }
    return import_shared14.is.und(keys) ? items : import_shared14.is.fun(keys) ? items.map(keys) : (0, import_shared14.toArray)(keys);
}
// src/hooks/useScroll.ts
var import_shared15 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
var useScroll = ({ container, ...springOptions } = {})=>{
    const [scrollValues, api] = useSpring({
        "useScroll.useSpring": ()=>({
                scrollX: 0,
                scrollY: 0,
                scrollXProgress: 0,
                scrollYProgress: 0,
                ...springOptions
            })
    }["useScroll.useSpring"], []);
    (0, import_shared15.useIsomorphicLayoutEffect)(()=>{
        const cleanupScroll = (0, import_shared15.onScroll)(({ x, y })=>{
            api.start({
                scrollX: x.current,
                scrollXProgress: x.progress,
                scrollY: y.current,
                scrollYProgress: y.progress
            });
        }, {
            container: container?.current || void 0
        });
        return ()=>{
            (0, import_shared15.each)(Object.values(scrollValues), (value)=>value.stop());
            cleanupScroll();
        };
    }, []);
    return scrollValues;
};
// src/hooks/useResize.ts
var import_shared16 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
var useResize = ({ container, ...springOptions })=>{
    const [sizeValues, api] = useSpring({
        "useResize.useSpring": ()=>({
                width: 0,
                height: 0,
                ...springOptions
            })
    }["useResize.useSpring"], []);
    (0, import_shared16.useIsomorphicLayoutEffect)(()=>{
        const cleanupScroll = (0, import_shared16.onResize)(({ width, height })=>{
            api.start({
                width,
                height,
                immediate: sizeValues.width.get() === 0 || sizeValues.height.get() === 0
            });
        }, {
            container: container?.current || void 0
        });
        return ()=>{
            (0, import_shared16.each)(Object.values(sizeValues), (value)=>value.stop());
            cleanupScroll();
        };
    }, []);
    return sizeValues;
};
// src/hooks/useInView.ts
var import_react5 = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var import_shared17 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
var defaultThresholdOptions = {
    any: 0,
    all: 1
};
function useInView(props, args) {
    const [isInView, setIsInView] = (0, import_react5.useState)(false);
    const ref = (0, import_react5.useRef)(void 0);
    const propsFn = import_shared17.is.fun(props) && props;
    const springsProps = propsFn ? propsFn() : {};
    const { to: to2 = {}, from = {}, ...restSpringProps } = springsProps;
    const intersectionArguments = propsFn ? args : props;
    const [springs, api] = useSpring({
        "useInView.useSpring": ()=>({
                from,
                ...restSpringProps
            })
    }["useInView.useSpring"], []);
    (0, import_shared17.useIsomorphicLayoutEffect)(()=>{
        const element = ref.current;
        const { root, once, amount = "any", ...restArgs } = intersectionArguments ?? {};
        if (!element || once && isInView || typeof IntersectionObserver === "undefined") return;
        const activeIntersections = /* @__PURE__ */ new WeakMap();
        const onEnter = ()=>{
            if (to2) {
                api.start(to2);
            }
            setIsInView(true);
            const cleanup = ()=>{
                if (from) {
                    api.start(from);
                }
                setIsInView(false);
            };
            return once ? void 0 : cleanup;
        };
        const handleIntersection = (entries)=>{
            entries.forEach((entry)=>{
                const onLeave = activeIntersections.get(entry.target);
                if (entry.isIntersecting === Boolean(onLeave)) {
                    return;
                }
                if (entry.isIntersecting) {
                    const newOnLeave = onEnter();
                    if (import_shared17.is.fun(newOnLeave)) {
                        activeIntersections.set(entry.target, newOnLeave);
                    } else {
                        observer.unobserve(entry.target);
                    }
                } else if (onLeave) {
                    onLeave();
                    activeIntersections.delete(entry.target);
                }
            });
        };
        const observer = new IntersectionObserver(handleIntersection, {
            root: root && root.current || void 0,
            threshold: typeof amount === "number" || Array.isArray(amount) ? amount : defaultThresholdOptions[amount],
            ...restArgs
        });
        observer.observe(element);
        return ()=>observer.unobserve(element);
    }, [
        intersectionArguments
    ]);
    if (propsFn) {
        return [
            ref,
            springs
        ];
    }
    return [
        ref,
        isInView
    ];
}
// src/components/Spring.tsx
function Spring({ children, ...props }) {
    return children(useSpring(props));
}
// src/components/Trail.tsx
var import_shared18 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
function Trail({ items, children, ...props }) {
    const trails = useTrail(items.length, props);
    return items.map((item, index)=>{
        const result = children(item, index);
        return import_shared18.is.fun(result) ? result(trails[index]) : result;
    });
}
// src/components/Transition.tsx
function Transition({ items, children, ...props }) {
    return useTransition(items, props)(children);
}
// src/interpolate.ts
var import_shared20 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
// src/Interpolation.ts
var import_shared19 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
var import_animated3 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+animated@10.0.1_react@19.0.0/node_modules/@react-spring/animated/dist/cjs/index.js [app-client] (ecmascript)");
var Interpolation = class extends FrameValue {
    constructor(source, args){
        super();
        this.source = source;
        /** Equals false when in the frameloop */ this.idle = true;
        /** The inputs which are currently animating */ this._active = /* @__PURE__ */ new Set();
        this.calc = (0, import_shared19.createInterpolator)(...args);
        const value = this._get();
        const nodeType = (0, import_animated3.getAnimatedType)(value);
        (0, import_animated3.setAnimated)(this, nodeType.create(value));
    }
    advance(_dt) {
        const value = this._get();
        const oldValue = this.get();
        if (!(0, import_shared19.isEqual)(value, oldValue)) {
            (0, import_animated3.getAnimated)(this).setValue(value);
            this._onChange(value, this.idle);
        }
        if (!this.idle && checkIdle(this._active)) {
            becomeIdle(this);
        }
    }
    _get() {
        const inputs = import_shared19.is.arr(this.source) ? this.source.map(import_shared19.getFluidValue) : (0, import_shared19.toArray)((0, import_shared19.getFluidValue)(this.source));
        return this.calc(...inputs);
    }
    _start() {
        if (this.idle && !checkIdle(this._active)) {
            this.idle = false;
            (0, import_shared19.each)((0, import_animated3.getPayload)(this), (node)=>{
                node.done = false;
            });
            if (import_shared19.Globals.skipAnimation) {
                import_shared19.raf.batchedUpdates(()=>this.advance());
                becomeIdle(this);
            } else {
                import_shared19.frameLoop.start(this);
            }
        }
    }
    // Observe our sources only when we're observed.
    _attach() {
        let priority = 1;
        (0, import_shared19.each)((0, import_shared19.toArray)(this.source), (source)=>{
            if ((0, import_shared19.hasFluidValue)(source)) {
                (0, import_shared19.addFluidObserver)(source, this);
            }
            if (isFrameValue(source)) {
                if (!source.idle) {
                    this._active.add(source);
                }
                priority = Math.max(priority, source.priority + 1);
            }
        });
        this.priority = priority;
        this._start();
    }
    // Stop observing our sources once we have no observers.
    _detach() {
        (0, import_shared19.each)((0, import_shared19.toArray)(this.source), (source)=>{
            if ((0, import_shared19.hasFluidValue)(source)) {
                (0, import_shared19.removeFluidObserver)(source, this);
            }
        });
        this._active.clear();
        becomeIdle(this);
    }
    /** @internal */ eventObserved(event) {
        if (event.type == "change") {
            if (event.idle) {
                this.advance();
            } else {
                this._active.add(event.parent);
                this._start();
            }
        } else if (event.type == "idle") {
            this._active.delete(event.parent);
        } else if (event.type == "priority") {
            this.priority = (0, import_shared19.toArray)(this.source).reduce((highest, parent)=>Math.max(highest, (isFrameValue(parent) ? parent.priority : 0) + 1), 0);
        }
    }
};
function isIdle(source) {
    return source.idle !== false;
}
function checkIdle(active) {
    return !active.size || Array.from(active).every(isIdle);
}
function becomeIdle(self) {
    if (!self.idle) {
        self.idle = true;
        (0, import_shared19.each)((0, import_animated3.getPayload)(self), (node)=>{
            node.done = true;
        });
        (0, import_shared19.callFluidObservers)(self, {
            type: "idle",
            parent: self
        });
    }
}
// src/interpolate.ts
var to = (source, ...args)=>new Interpolation(source, args);
var interpolate = (source, ...args)=>((0, import_shared20.deprecateInterpolate)(), new Interpolation(source, args));
// src/globals.ts
var import_shared21 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
import_shared21.Globals.assign({
    createStringInterpolator: import_shared21.createStringInterpolator,
    to: (source, args)=>new Interpolation(source, args)
});
var update = import_shared21.frameLoop.advance;
// src/index.ts
var import_shared22 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
__reExport(src_exports, __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+types@10.0.1/node_modules/@react-spring/types/dist/cjs/index.js [app-client] (ecmascript)"), module.exports);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    BailSignal,
    Controller,
    FrameValue,
    Globals,
    Interpolation,
    Spring,
    SpringContext,
    SpringRef,
    SpringValue,
    Trail,
    Transition,
    config,
    createInterpolator,
    easings,
    inferTo,
    interpolate,
    to,
    update,
    useChain,
    useInView,
    useIsomorphicLayoutEffect,
    useReducedMotion,
    useResize,
    useScroll,
    useSpring,
    useSpringRef,
    useSpringValue,
    useSprings,
    useTrail,
    useTransition,
    ...__turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+types@10.0.1/node_modules/@react-spring/types/dist/cjs/index.js [app-client] (ecmascript)")
});
}}),
"[project]/node_modules/.pnpm/@react-spring+core@10.0.1_react@19.0.0/node_modules/@react-spring/core/dist/cjs/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+core@10.0.1_react@19.0.0/node_modules/@react-spring/core/dist/cjs/react-spring_core.development.cjs [app-client] (ecmascript)");
}
}}),
"[project]/node_modules/.pnpm/@react-spring+three@10.0.1_@react-three+fiber@9.1.2_@types+react@19.0.10_react-dom@19.0_3f41a382e4a72d11f8efba93b74546c7/node_modules/@react-spring/three/dist/cjs/react-spring_three.development.cjs [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __reExport = (target, mod, secondTarget)=>(__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
// src/index.ts
var src_exports = {};
__export(src_exports, {
    a: ()=>animated,
    animated: ()=>animated
});
module.exports = __toCommonJS(src_exports);
var import_fiber2 = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-three+fiber@9.1.2_@types+react@19.0.10_react-dom@19.0.0_react@19.0.0__react@19.0.0_three@0.177.0/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js [app-client] (ecmascript)");
var import_core = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+core@10.0.1_react@19.0.0/node_modules/@react-spring/core/dist/cjs/index.js [app-client] (ecmascript)");
var import_shared = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+shared@10.0.1_react@19.0.0/node_modules/@react-spring/shared/dist/cjs/index.js [app-client] (ecmascript)");
var import_animated = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+animated@10.0.1_react@19.0.0/node_modules/@react-spring/animated/dist/cjs/index.js [app-client] (ecmascript)");
// src/primitives.ts
var THREE = __toESM(__turbopack_context__.r("[project]/node_modules/.pnpm/three@0.177.0/node_modules/three/build/three.cjs [app-client] (ecmascript)"));
var import_fiber = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-three+fiber@9.1.2_@types+react@19.0.10_react-dom@19.0.0_react@19.0.0__react@19.0.0_three@0.177.0/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js [app-client] (ecmascript)");
var primitives = [
    "primitive"
].concat(Object.keys(THREE).filter((key)=>/^[A-Z]/.test(key)).map((key)=>key[0].toLowerCase() + key.slice(1)));
// src/index.ts
__reExport(src_exports, __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+core@10.0.1_react@19.0.0/node_modules/@react-spring/core/dist/cjs/index.js [app-client] (ecmascript)"), module.exports);
import_core.Globals.assign({
    createStringInterpolator: import_shared.createStringInterpolator,
    colors: import_shared.colors,
    frameLoop: "demand"
});
(0, import_fiber2.addEffect)(()=>{
    import_shared.raf.advance();
});
var host = (0, import_animated.createHost)(primitives, {
    applyAnimatedValues: import_fiber2.applyProps
});
var animated = host.animated;
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    a,
    animated,
    ...__turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+core@10.0.1_react@19.0.0/node_modules/@react-spring/core/dist/cjs/index.js [app-client] (ecmascript)")
});
}}),
"[project]/node_modules/.pnpm/@react-spring+three@10.0.1_@react-three+fiber@9.1.2_@types+react@19.0.10_react-dom@19.0_3f41a382e4a72d11f8efba93b74546c7/node_modules/@react-spring/three/dist/cjs/index.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, d: __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$2$2e$0_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.2.0_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/.pnpm/@react-spring+three@10.0.1_@react-three+fiber@9.1.2_@types+react@19.0.10_react-dom@19.0_3f41a382e4a72d11f8efba93b74546c7/node_modules/@react-spring/three/dist/cjs/react-spring_three.development.cjs [app-client] (ecmascript)");
}
}}),
}]);

//# sourceMappingURL=node_modules__pnpm_56139884._.js.map