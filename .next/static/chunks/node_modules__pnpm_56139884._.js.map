{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/scheduler%400.25.0/node_modules/scheduler/cjs/scheduler.development.js"], "sourcesContent": ["/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function performWorkUntilDeadline() {\n      if (isMessageLoopRunning) {\n        var currentTime = exports.unstable_now();\n        startTime = currentTime;\n        var hasMoreWork = !0;\n        try {\n          a: {\n            isHostCallbackScheduled = !1;\n            isHostTimeoutScheduled &&\n              ((isHostTimeoutScheduled = !1),\n              localClearTimeout(taskTimeoutID),\n              (taskTimeoutID = -1));\n            isPerformingWork = !0;\n            var previousPriorityLevel = currentPriorityLevel;\n            try {\n              b: {\n                advanceTimers(currentTime);\n                for (\n                  currentTask = peek(taskQueue);\n                  null !== currentTask &&\n                  !(\n                    currentTask.expirationTime > currentTime &&\n                    shouldYieldToHost()\n                  );\n\n                ) {\n                  var callback = currentTask.callback;\n                  if (\"function\" === typeof callback) {\n                    currentTask.callback = null;\n                    currentPriorityLevel = currentTask.priorityLevel;\n                    var continuationCallback = callback(\n                      currentTask.expirationTime <= currentTime\n                    );\n                    currentTime = exports.unstable_now();\n                    if (\"function\" === typeof continuationCallback) {\n                      currentTask.callback = continuationCallback;\n                      advanceTimers(currentTime);\n                      hasMoreWork = !0;\n                      break b;\n                    }\n                    currentTask === peek(taskQueue) && pop(taskQueue);\n                    advanceTimers(currentTime);\n                  } else pop(taskQueue);\n                  currentTask = peek(taskQueue);\n                }\n                if (null !== currentTask) hasMoreWork = !0;\n                else {\n                  var firstTimer = peek(timerQueue);\n                  null !== firstTimer &&\n                    requestHostTimeout(\n                      handleTimeout,\n                      firstTimer.startTime - currentTime\n                    );\n                  hasMoreWork = !1;\n                }\n              }\n              break a;\n            } finally {\n              (currentTask = null),\n                (currentPriorityLevel = previousPriorityLevel),\n                (isPerformingWork = !1);\n            }\n            hasMoreWork = void 0;\n          }\n        } finally {\n          hasMoreWork\n            ? schedulePerformWorkUntilDeadline()\n            : (isMessageLoopRunning = !1);\n        }\n      }\n    }\n    function push(heap, node) {\n      var index = heap.length;\n      heap.push(node);\n      a: for (; 0 < index; ) {\n        var parentIndex = (index - 1) >>> 1,\n          parent = heap[parentIndex];\n        if (0 < compare(parent, node))\n          (heap[parentIndex] = node),\n            (heap[index] = parent),\n            (index = parentIndex);\n        else break a;\n      }\n    }\n    function peek(heap) {\n      return 0 === heap.length ? null : heap[0];\n    }\n    function pop(heap) {\n      if (0 === heap.length) return null;\n      var first = heap[0],\n        last = heap.pop();\n      if (last !== first) {\n        heap[0] = last;\n        a: for (\n          var index = 0, length = heap.length, halfLength = length >>> 1;\n          index < halfLength;\n\n        ) {\n          var leftIndex = 2 * (index + 1) - 1,\n            left = heap[leftIndex],\n            rightIndex = leftIndex + 1,\n            right = heap[rightIndex];\n          if (0 > compare(left, last))\n            rightIndex < length && 0 > compare(right, left)\n              ? ((heap[index] = right),\n                (heap[rightIndex] = last),\n                (index = rightIndex))\n              : ((heap[index] = left),\n                (heap[leftIndex] = last),\n                (index = leftIndex));\n          else if (rightIndex < length && 0 > compare(right, last))\n            (heap[index] = right),\n              (heap[rightIndex] = last),\n              (index = rightIndex);\n          else break a;\n        }\n      }\n      return first;\n    }\n    function compare(a, b) {\n      var diff = a.sortIndex - b.sortIndex;\n      return 0 !== diff ? diff : a.id - b.id;\n    }\n    function advanceTimers(currentTime) {\n      for (var timer = peek(timerQueue); null !== timer; ) {\n        if (null === timer.callback) pop(timerQueue);\n        else if (timer.startTime <= currentTime)\n          pop(timerQueue),\n            (timer.sortIndex = timer.expirationTime),\n            push(taskQueue, timer);\n        else break;\n        timer = peek(timerQueue);\n      }\n    }\n    function handleTimeout(currentTime) {\n      isHostTimeoutScheduled = !1;\n      advanceTimers(currentTime);\n      if (!isHostCallbackScheduled)\n        if (null !== peek(taskQueue))\n          (isHostCallbackScheduled = !0), requestHostCallback();\n        else {\n          var firstTimer = peek(timerQueue);\n          null !== firstTimer &&\n            requestHostTimeout(\n              handleTimeout,\n              firstTimer.startTime - currentTime\n            );\n        }\n    }\n    function shouldYieldToHost() {\n      return exports.unstable_now() - startTime < frameInterval ? !1 : !0;\n    }\n    function requestHostCallback() {\n      isMessageLoopRunning ||\n        ((isMessageLoopRunning = !0), schedulePerformWorkUntilDeadline());\n    }\n    function requestHostTimeout(callback, ms) {\n      taskTimeoutID = localSetTimeout(function () {\n        callback(exports.unstable_now());\n      }, ms);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    exports.unstable_now = void 0;\n    if (\n      \"object\" === typeof performance &&\n      \"function\" === typeof performance.now\n    ) {\n      var localPerformance = performance;\n      exports.unstable_now = function () {\n        return localPerformance.now();\n      };\n    } else {\n      var localDate = Date,\n        initialTime = localDate.now();\n      exports.unstable_now = function () {\n        return localDate.now() - initialTime;\n      };\n    }\n    var taskQueue = [],\n      timerQueue = [],\n      taskIdCounter = 1,\n      currentTask = null,\n      currentPriorityLevel = 3,\n      isPerformingWork = !1,\n      isHostCallbackScheduled = !1,\n      isHostTimeoutScheduled = !1,\n      localSetTimeout = \"function\" === typeof setTimeout ? setTimeout : null,\n      localClearTimeout =\n        \"function\" === typeof clearTimeout ? clearTimeout : null,\n      localSetImmediate =\n        \"undefined\" !== typeof setImmediate ? setImmediate : null,\n      isMessageLoopRunning = !1,\n      taskTimeoutID = -1,\n      frameInterval = 5,\n      startTime = -1;\n    if (\"function\" === typeof localSetImmediate)\n      var schedulePerformWorkUntilDeadline = function () {\n        localSetImmediate(performWorkUntilDeadline);\n      };\n    else if (\"undefined\" !== typeof MessageChannel) {\n      var channel = new MessageChannel(),\n        port = channel.port2;\n      channel.port1.onmessage = performWorkUntilDeadline;\n      schedulePerformWorkUntilDeadline = function () {\n        port.postMessage(null);\n      };\n    } else\n      schedulePerformWorkUntilDeadline = function () {\n        localSetTimeout(performWorkUntilDeadline, 0);\n      };\n    exports.unstable_IdlePriority = 5;\n    exports.unstable_ImmediatePriority = 1;\n    exports.unstable_LowPriority = 4;\n    exports.unstable_NormalPriority = 3;\n    exports.unstable_Profiling = null;\n    exports.unstable_UserBlockingPriority = 2;\n    exports.unstable_cancelCallback = function (task) {\n      task.callback = null;\n    };\n    exports.unstable_continueExecution = function () {\n      isHostCallbackScheduled ||\n        isPerformingWork ||\n        ((isHostCallbackScheduled = !0), requestHostCallback());\n    };\n    exports.unstable_forceFrameRate = function (fps) {\n      0 > fps || 125 < fps\n        ? console.error(\n            \"forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported\"\n          )\n        : (frameInterval = 0 < fps ? Math.floor(1e3 / fps) : 5);\n    };\n    exports.unstable_getCurrentPriorityLevel = function () {\n      return currentPriorityLevel;\n    };\n    exports.unstable_getFirstCallbackNode = function () {\n      return peek(taskQueue);\n    };\n    exports.unstable_next = function (eventHandler) {\n      switch (currentPriorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n          var priorityLevel = 3;\n          break;\n        default:\n          priorityLevel = currentPriorityLevel;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_pauseExecution = function () {};\n    exports.unstable_requestPaint = function () {};\n    exports.unstable_runWithPriority = function (priorityLevel, eventHandler) {\n      switch (priorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n        case 4:\n        case 5:\n          break;\n        default:\n          priorityLevel = 3;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_scheduleCallback = function (\n      priorityLevel,\n      callback,\n      options\n    ) {\n      var currentTime = exports.unstable_now();\n      \"object\" === typeof options && null !== options\n        ? ((options = options.delay),\n          (options =\n            \"number\" === typeof options && 0 < options\n              ? currentTime + options\n              : currentTime))\n        : (options = currentTime);\n      switch (priorityLevel) {\n        case 1:\n          var timeout = -1;\n          break;\n        case 2:\n          timeout = 250;\n          break;\n        case 5:\n          timeout = 1073741823;\n          break;\n        case 4:\n          timeout = 1e4;\n          break;\n        default:\n          timeout = 5e3;\n      }\n      timeout = options + timeout;\n      priorityLevel = {\n        id: taskIdCounter++,\n        callback: callback,\n        priorityLevel: priorityLevel,\n        startTime: options,\n        expirationTime: timeout,\n        sortIndex: -1\n      };\n      options > currentTime\n        ? ((priorityLevel.sortIndex = options),\n          push(timerQueue, priorityLevel),\n          null === peek(taskQueue) &&\n            priorityLevel === peek(timerQueue) &&\n            (isHostTimeoutScheduled\n              ? (localClearTimeout(taskTimeoutID), (taskTimeoutID = -1))\n              : (isHostTimeoutScheduled = !0),\n            requestHostTimeout(handleTimeout, options - currentTime)))\n        : ((priorityLevel.sortIndex = timeout),\n          push(taskQueue, priorityLevel),\n          isHostCallbackScheduled ||\n            isPerformingWork ||\n            ((isHostCallbackScheduled = !0), requestHostCallback()));\n      return priorityLevel;\n    };\n    exports.unstable_shouldYield = shouldYieldToHost;\n    exports.unstable_wrapCallback = function (callback) {\n      var parentPriorityLevel = currentPriorityLevel;\n      return function () {\n        var previousPriorityLevel = currentPriorityLevel;\n        currentPriorityLevel = parentPriorityLevel;\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          currentPriorityLevel = previousPriorityLevel;\n        }\n      };\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS;QACP,IAAI,sBAAsB;YACxB,IAAI,cAAc,QAAQ,YAAY;YACtC,YAAY;YACZ,IAAI,cAAc,CAAC;YACnB,IAAI;gBACF,GAAG;oBACD,0BAA0B,CAAC;oBAC3B,0BACE,CAAC,AAAC,yBAAyB,CAAC,GAC5B,kBAAkB,gBACjB,gBAAgB,CAAC,CAAE;oBACtB,mBAAmB,CAAC;oBACpB,IAAI,wBAAwB;oBAC5B,IAAI;wBACF,GAAG;4BACD,cAAc;4BACd,IACE,cAAc,KAAK,YACnB,SAAS,eACT,CAAC,CACC,YAAY,cAAc,GAAG,eAC7B,mBACF,GAEA;gCACA,IAAI,WAAW,YAAY,QAAQ;gCACnC,IAAI,eAAe,OAAO,UAAU;oCAClC,YAAY,QAAQ,GAAG;oCACvB,uBAAuB,YAAY,aAAa;oCAChD,IAAI,uBAAuB,SACzB,YAAY,cAAc,IAAI;oCAEhC,cAAc,QAAQ,YAAY;oCAClC,IAAI,eAAe,OAAO,sBAAsB;wCAC9C,YAAY,QAAQ,GAAG;wCACvB,cAAc;wCACd,cAAc,CAAC;wCACf,MAAM;oCACR;oCACA,gBAAgB,KAAK,cAAc,IAAI;oCACvC,cAAc;gCAChB,OAAO,IAAI;gCACX,cAAc,KAAK;4BACrB;4BACA,IAAI,SAAS,aAAa,cAAc,CAAC;iCACpC;gCACH,IAAI,aAAa,KAAK;gCACtB,SAAS,cACP,mBACE,eACA,WAAW,SAAS,GAAG;gCAE3B,cAAc,CAAC;4BACjB;wBACF;wBACA,MAAM;oBACR,SAAU;wBACP,cAAc,MACZ,uBAAuB,uBACvB,mBAAmB,CAAC;oBACzB;oBACA,cAAc,KAAK;gBACrB;YACF,SAAU;gBACR,cACI,qCACC,uBAAuB,CAAC;YAC/B;QACF;IACF;IACA,SAAS,KAAK,IAAI,EAAE,IAAI;QACtB,IAAI,QAAQ,KAAK,MAAM;QACvB,KAAK,IAAI,CAAC;QACV,GAAG,MAAO,IAAI,OAAS;YACrB,IAAI,cAAc,AAAC,QAAQ,MAAO,GAChC,SAAS,IAAI,CAAC,YAAY;YAC5B,IAAI,IAAI,QAAQ,QAAQ,OACtB,AAAC,IAAI,CAAC,YAAY,GAAG,MAClB,IAAI,CAAC,MAAM,GAAG,QACd,QAAQ;iBACR,MAAM;QACb;IACF;IACA,SAAS,KAAK,IAAI;QAChB,OAAO,MAAM,KAAK,MAAM,GAAG,OAAO,IAAI,CAAC,EAAE;IAC3C;IACA,SAAS,IAAI,IAAI;QACf,IAAI,MAAM,KAAK,MAAM,EAAE,OAAO;QAC9B,IAAI,QAAQ,IAAI,CAAC,EAAE,EACjB,OAAO,KAAK,GAAG;QACjB,IAAI,SAAS,OAAO;YAClB,IAAI,CAAC,EAAE,GAAG;YACV,GAAG,IACD,IAAI,QAAQ,GAAG,SAAS,KAAK,MAAM,EAAE,aAAa,WAAW,GAC7D,QAAQ,YAER;gBACA,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,IAAI,GAChC,OAAO,IAAI,CAAC,UAAU,EACtB,aAAa,YAAY,GACzB,QAAQ,IAAI,CAAC,WAAW;gBAC1B,IAAI,IAAI,QAAQ,MAAM,OACpB,aAAa,UAAU,IAAI,QAAQ,OAAO,QACtC,CAAC,AAAC,IAAI,CAAC,MAAM,GAAG,OACf,IAAI,CAAC,WAAW,GAAG,MACnB,QAAQ,UAAW,IACpB,CAAC,AAAC,IAAI,CAAC,MAAM,GAAG,MACf,IAAI,CAAC,UAAU,GAAG,MAClB,QAAQ,SAAU;qBACpB,IAAI,aAAa,UAAU,IAAI,QAAQ,OAAO,OACjD,AAAC,IAAI,CAAC,MAAM,GAAG,OACZ,IAAI,CAAC,WAAW,GAAG,MACnB,QAAQ;qBACR,MAAM;YACb;QACF;QACA,OAAO;IACT;IACA,SAAS,QAAQ,CAAC,EAAE,CAAC;QACnB,IAAI,OAAO,EAAE,SAAS,GAAG,EAAE,SAAS;QACpC,OAAO,MAAM,OAAO,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE;IACxC;IACA,SAAS,cAAc,WAAW;QAChC,IAAK,IAAI,QAAQ,KAAK,aAAa,SAAS,OAAS;YACnD,IAAI,SAAS,MAAM,QAAQ,EAAE,IAAI;iBAC5B,IAAI,MAAM,SAAS,IAAI,aAC1B,IAAI,aACD,MAAM,SAAS,GAAG,MAAM,cAAc,EACvC,KAAK,WAAW;iBACf;YACL,QAAQ,KAAK;QACf;IACF;IACA,SAAS,cAAc,WAAW;QAChC,yBAAyB,CAAC;QAC1B,cAAc;QACd,IAAI,CAAC,yBACH,IAAI,SAAS,KAAK,YAChB,AAAC,0BAA0B,CAAC,GAAI;aAC7B;YACH,IAAI,aAAa,KAAK;YACtB,SAAS,cACP,mBACE,eACA,WAAW,SAAS,GAAG;QAE7B;IACJ;IACA,SAAS;QACP,OAAO,QAAQ,YAAY,KAAK,YAAY,gBAAgB,CAAC,IAAI,CAAC;IACpE;IACA,SAAS;QACP,wBACE,CAAC,AAAC,uBAAuB,CAAC,GAAI,kCAAkC;IACpE;IACA,SAAS,mBAAmB,QAAQ,EAAE,EAAE;QACtC,gBAAgB,gBAAgB;YAC9B,SAAS,QAAQ,YAAY;QAC/B,GAAG;IACL;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,QAAQ,YAAY,GAAG,KAAK;IAC5B,IACE,aAAa,OAAO,eACpB,eAAe,OAAO,YAAY,GAAG,EACrC;QACA,IAAI,mBAAmB;QACvB,QAAQ,YAAY,GAAG;YACrB,OAAO,iBAAiB,GAAG;QAC7B;IACF,OAAO;QACL,IAAI,YAAY,MACd,cAAc,UAAU,GAAG;QAC7B,QAAQ,YAAY,GAAG;YACrB,OAAO,UAAU,GAAG,KAAK;QAC3B;IACF;IACA,IAAI,YAAY,EAAE,EAChB,aAAa,EAAE,EACf,gBAAgB,GAChB,cAAc,MACd,uBAAuB,GACvB,mBAAmB,CAAC,GACpB,0BAA0B,CAAC,GAC3B,yBAAyB,CAAC,GAC1B,kBAAkB,eAAe,OAAO,aAAa,aAAa,MAClE,oBACE,eAAe,OAAO,eAAe,eAAe,MACtD,oBACE,gBAAgB,OAAO,eAAe,eAAe,MACvD,uBAAuB,CAAC,GACxB,gBAAgB,CAAC,GACjB,gBAAgB,GAChB,YAAY,CAAC;IACf,IAAI,eAAe,OAAO,mBACxB,IAAI,mCAAmC;QACrC,kBAAkB;IACpB;SACG,IAAI,gBAAgB,OAAO,gBAAgB;QAC9C,IAAI,UAAU,IAAI,kBAChB,OAAO,QAAQ,KAAK;QACtB,QAAQ,KAAK,CAAC,SAAS,GAAG;QAC1B,mCAAmC;YACjC,KAAK,WAAW,CAAC;QACnB;IACF,OACE,mCAAmC;QACjC,gBAAgB,0BAA0B;IAC5C;IACF,QAAQ,qBAAqB,GAAG;IAChC,QAAQ,0BAA0B,GAAG;IACrC,QAAQ,oBAAoB,GAAG;IAC/B,QAAQ,uBAAuB,GAAG;IAClC,QAAQ,kBAAkB,GAAG;IAC7B,QAAQ,6BAA6B,GAAG;IACxC,QAAQ,uBAAuB,GAAG,SAAU,IAAI;QAC9C,KAAK,QAAQ,GAAG;IAClB;IACA,QAAQ,0BAA0B,GAAG;QACnC,2BACE,oBACA,CAAC,AAAC,0BAA0B,CAAC,GAAI,qBAAqB;IAC1D;IACA,QAAQ,uBAAuB,GAAG,SAAU,GAAG;QAC7C,IAAI,OAAO,MAAM,MACb,QAAQ,KAAK,CACX,qHAED,gBAAgB,IAAI,MAAM,KAAK,KAAK,CAAC,MAAM,OAAO;IACzD;IACA,QAAQ,gCAAgC,GAAG;QACzC,OAAO;IACT;IACA,QAAQ,6BAA6B,GAAG;QACtC,OAAO,KAAK;IACd;IACA,QAAQ,aAAa,GAAG,SAAU,YAAY;QAC5C,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,IAAI,gBAAgB;gBACpB;YACF;gBACE,gBAAgB;QACpB;QACA,IAAI,wBAAwB;QAC5B,uBAAuB;QACvB,IAAI;YACF,OAAO;QACT,SAAU;YACR,uBAAuB;QACzB;IACF;IACA,QAAQ,uBAAuB,GAAG,YAAa;IAC/C,QAAQ,qBAAqB,GAAG,YAAa;IAC7C,QAAQ,wBAAwB,GAAG,SAAU,aAAa,EAAE,YAAY;QACtE,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACE,gBAAgB;QACpB;QACA,IAAI,wBAAwB;QAC5B,uBAAuB;QACvB,IAAI;YACF,OAAO;QACT,SAAU;YACR,uBAAuB;QACzB;IACF;IACA,QAAQ,yBAAyB,GAAG,SAClC,aAAa,EACb,QAAQ,EACR,OAAO;QAEP,IAAI,cAAc,QAAQ,YAAY;QACtC,aAAa,OAAO,WAAW,SAAS,UACpC,CAAC,AAAC,UAAU,QAAQ,KAAK,EACxB,UACC,aAAa,OAAO,WAAW,IAAI,UAC/B,cAAc,UACd,WAAY,IACjB,UAAU;QACf,OAAQ;YACN,KAAK;gBACH,IAAI,UAAU,CAAC;gBACf;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF;gBACE,UAAU;QACd;QACA,UAAU,UAAU;QACpB,gBAAgB;YACd,IAAI;YACJ,UAAU;YACV,eAAe;YACf,WAAW;YACX,gBAAgB;YAChB,WAAW,CAAC;QACd;QACA,UAAU,cACN,CAAC,AAAC,cAAc,SAAS,GAAG,SAC5B,KAAK,YAAY,gBACjB,SAAS,KAAK,cACZ,kBAAkB,KAAK,eACvB,CAAC,yBACG,CAAC,kBAAkB,gBAAiB,gBAAgB,CAAC,CAAE,IACtD,yBAAyB,CAAC,GAC/B,mBAAmB,eAAe,UAAU,YAAY,CAAC,IAC3D,CAAC,AAAC,cAAc,SAAS,GAAG,SAC5B,KAAK,WAAW,gBAChB,2BACE,oBACA,CAAC,AAAC,0BAA0B,CAAC,GAAI,qBAAqB,CAAC;QAC7D,OAAO;IACT;IACA,QAAQ,oBAAoB,GAAG;IAC/B,QAAQ,qBAAqB,GAAG,SAAU,QAAQ;QAChD,IAAI,sBAAsB;QAC1B,OAAO;YACL,IAAI,wBAAwB;YAC5B,uBAAuB;YACvB,IAAI;gBACF,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;YAC9B,SAAU;gBACR,uBAAuB;YACzB;QACF;IACF;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/scheduler%400.25.0/node_modules/scheduler/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/scheduler.production.js');\n} else {\n  module.exports = require('./cjs/scheduler.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/use-sync-external-store%401.5.0_react%4019.0.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,OAAO,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAO,MAAM,KAAK,MAAM;IACxE;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,qBACE,KAAK,MAAM,MAAM,eAAe,IAChC,CAAC,AAAC,oBAAoB,CAAC,GACvB,QAAQ,KAAK,CACX,iMACD;QACH,IAAI,QAAQ;QACZ,IAAI,CAAC,4BAA4B;YAC/B,IAAI,cAAc;YAClB,SAAS,OAAO,gBACd,CAAC,QAAQ,KAAK,CACZ,yEAED,6BAA6B,CAAC,CAAE;QACrC;QACA,cAAc,SAAS;YACrB,MAAM;gBAAE,OAAO;gBAAO,aAAa;YAAY;QACjD;QACA,IAAI,OAAO,WAAW,CAAC,EAAE,CAAC,IAAI,EAC5B,cAAc,WAAW,CAAC,EAAE;QAC9B;sDACE;gBACE,KAAK,KAAK,GAAG;gBACb,KAAK,WAAW,GAAG;gBACnB,uBAAuB,SAAS,YAAY;oBAAE,MAAM;gBAAK;YAC3D;qDACA;YAAC;YAAW;YAAO;SAAY;QAEjC;gDACE;gBACE,uBAAuB,SAAS,YAAY;oBAAE,MAAM;gBAAK;gBACzD,OAAO;wDAAU;wBACf,uBAAuB,SAAS,YAAY;4BAAE,MAAM;wBAAK;oBAC3D;;YACF;+CACA;YAAC;SAAU;QAEb,cAAc;QACd,OAAO;IACT;IACA,SAAS,uBAAuB,IAAI;QAClC,IAAI,oBAAoB,KAAK,WAAW;QACxC,OAAO,KAAK,KAAK;QACjB,IAAI;YACF,IAAI,YAAY;YAChB,OAAO,CAAC,SAAS,MAAM;QACzB,EAAE,OAAO,OAAO;YACd,OAAO,CAAC;QACV;IACF;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,OAAO;IACT;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,IAAI,kMACF,WAAW,eAAe,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,IACzD,WAAW,MAAM,QAAQ,EACzB,YAAY,MAAM,SAAS,EAC3B,kBAAkB,MAAM,eAAe,EACvC,gBAAgB,MAAM,aAAa,EACnC,oBAAoB,CAAC,GACrB,6BAA6B,CAAC,GAC9B,OACE,gBAAgB,OAAO,UACvB,gBAAgB,OAAO,OAAO,QAAQ,IACtC,gBAAgB,OAAO,OAAO,QAAQ,CAAC,aAAa,GAChD,yBACA;IACR,QAAQ,oBAAoB,GAC1B,KAAK,MAAM,MAAM,oBAAoB,GAAG,MAAM,oBAAoB,GAAG;IACvE,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/use-sync-external-store%401.5.0_react%4019.0.0/node_modules/use-sync-external-store/shim/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/use-sync-external-store%401.5.0_react%4019.0.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      shim = require(\"use-sync-external-store/shim\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = shim.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (\n      subscribe,\n      getSnapshot,\n      getServerSnapshot,\n      selector,\n      isEqual\n    ) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = { hasValue: !1, value: null };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(\n        function () {\n          function memoizedSelector(nextSnapshot) {\n            if (!hasMemo) {\n              hasMemo = !0;\n              memoizedSnapshot = nextSnapshot;\n              nextSnapshot = selector(nextSnapshot);\n              if (void 0 !== isEqual && inst.hasValue) {\n                var currentSelection = inst.value;\n                if (isEqual(currentSelection, nextSnapshot))\n                  return (memoizedSelection = currentSelection);\n              }\n              return (memoizedSelection = nextSnapshot);\n            }\n            currentSelection = memoizedSelection;\n            if (objectIs(memoizedSnapshot, nextSnapshot))\n              return currentSelection;\n            var nextSelection = selector(nextSnapshot);\n            if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n              return (memoizedSnapshot = nextSnapshot), currentSelection;\n            memoizedSnapshot = nextSnapshot;\n            return (memoizedSelection = nextSelection);\n          }\n          var hasMemo = !1,\n            memoizedSnapshot,\n            memoizedSelection,\n            maybeGetServerSnapshot =\n              void 0 === getServerSnapshot ? null : getServerSnapshot;\n          return [\n            function () {\n              return memoizedSelector(getSnapshot());\n            },\n            null === maybeGetServerSnapshot\n              ? void 0\n              : function () {\n                  return memoizedSelector(maybeGetServerSnapshot());\n                }\n          ];\n        },\n        [getSnapshot, getServerSnapshot, selector, isEqual]\n      );\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(\n        function () {\n          inst.hasValue = !0;\n          inst.value = value;\n        },\n        [value]\n      );\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,OAAO,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAO,MAAM,KAAK,MAAM;IACxE;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,IAAI,kMACF,wLACA,WAAW,eAAe,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,IACzD,uBAAuB,KAAK,oBAAoB,EAChD,SAAS,MAAM,MAAM,EACrB,YAAY,MAAM,SAAS,EAC3B,UAAU,MAAM,OAAO,EACvB,gBAAgB,MAAM,aAAa;IACrC,QAAQ,gCAAgC,GAAG,SACzC,SAAS,EACT,WAAW,EACX,iBAAiB,EACjB,QAAQ,EACR,OAAO;QAEP,IAAI,UAAU,OAAO;QACrB,IAAI,SAAS,QAAQ,OAAO,EAAE;YAC5B,IAAI,OAAO;gBAAE,UAAU,CAAC;gBAAG,OAAO;YAAK;YACvC,QAAQ,OAAO,GAAG;QACpB,OAAO,OAAO,QAAQ,OAAO;QAC7B,UAAU,QACR;YACE,SAAS,iBAAiB,YAAY;gBACpC,IAAI,CAAC,SAAS;oBACZ,UAAU,CAAC;oBACX,mBAAmB;oBACnB,eAAe,SAAS;oBACxB,IAAI,KAAK,MAAM,WAAW,KAAK,QAAQ,EAAE;wBACvC,IAAI,mBAAmB,KAAK,KAAK;wBACjC,IAAI,QAAQ,kBAAkB,eAC5B,OAAQ,oBAAoB;oBAChC;oBACA,OAAQ,oBAAoB;gBAC9B;gBACA,mBAAmB;gBACnB,IAAI,SAAS,kBAAkB,eAC7B,OAAO;gBACT,IAAI,gBAAgB,SAAS;gBAC7B,IAAI,KAAK,MAAM,WAAW,QAAQ,kBAAkB,gBAClD,OAAO,AAAC,mBAAmB,cAAe;gBAC5C,mBAAmB;gBACnB,OAAQ,oBAAoB;YAC9B;YACA,IAAI,UAAU,CAAC,GACb,kBACA,mBACA,yBACE,KAAK,MAAM,oBAAoB,OAAO;YAC1C,OAAO;gBACL;oBACE,OAAO,iBAAiB;gBAC1B;gBACA,SAAS,yBACL,KAAK,IACL;oBACE,OAAO,iBAAiB;gBAC1B;aACL;QACH,GACA;YAAC;YAAa;YAAmB;YAAU;SAAQ;QAErD,IAAI,QAAQ,qBAAqB,WAAW,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE;QAClE,UACE;YACE,KAAK,QAAQ,GAAG,CAAC;YACjB,KAAK,KAAK,GAAG;QACf,GACA;YAAC;SAAM;QAET,cAAc;QACd,OAAO;IACT;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 445, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/use-sync-external-store%401.5.0_react%4019.0.0/node_modules/use-sync-external-store/shim/with-selector.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/zustand%405.0.5_%40types%2Breact%4019.0.10_react%4019.0.0_use-sync-external-store%401.5.0_react%4019.0.0_/node_modules/zustand/esm/vanilla.mjs"], "sourcesContent": ["const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\nexport { createStore };\n"], "names": [], "mappings": ";;;AAAA,MAAM,kBAAkB,CAAC;IACvB,IAAI;IACJ,MAAM,YAAY,aAAa,GAAG,IAAI;IACtC,MAAM,WAAW,CAAC,SAAS;QACzB,MAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,SAAS;QACnE,IAAI,CAAC,OAAO,EAAE,CAAC,WAAW,QAAQ;YAChC,MAAM,gBAAgB;YACtB,QAAQ,CAAC,WAAW,OAAO,UAAU,OAAO,cAAc,YAAY,cAAc,IAAI,IAAI,YAAY,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;YACjI,UAAU,OAAO,CAAC,CAAC,WAAa,SAAS,OAAO;QAClD;IACF;IACA,MAAM,WAAW,IAAM;IACvB,MAAM,kBAAkB,IAAM;IAC9B,MAAM,YAAY,CAAC;QACjB,UAAU,GAAG,CAAC;QACd,OAAO,IAAM,UAAU,MAAM,CAAC;IAChC;IACA,MAAM,MAAM;QAAE;QAAU;QAAU;QAAiB;IAAU;IAC7D,MAAM,eAAe,QAAQ,YAAY,UAAU,UAAU;IAC7D,OAAO;AACT;AACA,MAAM,cAAc,CAAC,cAAgB,cAAc,gBAAgB,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/zustand%405.0.5_%40types%2Breact%4019.0.10_react%4019.0.0_use-sync-external-store%401.5.0_react%4019.0.0_/node_modules/zustand/esm/traditional.mjs"], "sourcesContent": ["import React from 'react';\nimport useSyncExternalStoreExports from 'use-sync-external-store/shim/with-selector.js';\nimport { createStore } from 'zustand/vanilla';\n\nconst { useSyncExternalStoreWithSelector } = useSyncExternalStoreExports;\nconst identity = (arg) => arg;\nfunction useStoreWithEqualityFn(api, selector = identity, equalityFn) {\n  const slice = useSyncExternalStoreWithSelector(\n    api.subscribe,\n    api.getState,\n    api.getInitialState,\n    selector,\n    equalityFn\n  );\n  React.useDebugValue(slice);\n  return slice;\n}\nconst createWithEqualityFnImpl = (createState, defaultEqualityFn) => {\n  const api = createStore(createState);\n  const useBoundStoreWithEqualityFn = (selector, equalityFn = defaultEqualityFn) => useStoreWithEqualityFn(api, selector, equalityFn);\n  Object.assign(useBoundStoreWithEqualityFn, api);\n  return useBoundStoreWithEqualityFn;\n};\nconst createWithEqualityFn = (createState, defaultEqualityFn) => createState ? createWithEqualityFnImpl(createState, defaultEqualityFn) : createWithEqualityFnImpl;\n\nexport { createWithEqualityFn, useStoreWithEqualityFn };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,MAAM,EAAE,gCAAgC,EAAE,GAAG,kRAAA,CAAA,UAA2B;AACxE,MAAM,WAAW,CAAC,MAAQ;AAC1B,SAAS,uBAAuB,GAAG,EAAE,WAAW,QAAQ,EAAE,UAAU;IAClE,MAAM,QAAQ,iCACZ,IAAI,SAAS,EACb,IAAI,QAAQ,EACZ,IAAI,eAAe,EACnB,UACA;IAEF,4RAAA,CAAA,UAAK,CAAC,aAAa,CAAC;IACpB,OAAO;AACT;AACA,MAAM,2BAA2B,CAAC,aAAa;IAC7C,MAAM,MAAM,CAAA,GAAA,kUAAA,CAAA,cAAW,AAAD,EAAE;IACxB,MAAM,8BAA8B,CAAC,UAAU,aAAa,iBAAiB,GAAK,uBAAuB,KAAK,UAAU;IACxH,OAAO,MAAM,CAAC,6BAA6B;IAC3C,OAAO;AACT;AACA,MAAM,uBAAuB,CAAC,aAAa,oBAAsB,cAAc,yBAAyB,aAAa,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/suspend-react%400.1.3_react%4019.0.0/node_modules/suspend-react/index.js"], "sourcesContent": ["const isPromise = promise => typeof promise === 'object' && typeof promise.then === 'function';\n\nconst globalCache = [];\n\nfunction shallowEqualArrays(arrA, arrB, equal = (a, b) => a === b) {\n  if (arrA === arrB) return true;\n  if (!arrA || !arrB) return false;\n  const len = arrA.length;\n  if (arrB.length !== len) return false;\n\n  for (let i = 0; i < len; i++) if (!equal(arrA[i], arrB[i])) return false;\n\n  return true;\n}\n\nfunction query(fn, keys = null, preload = false, config = {}) {\n  // If no keys were given, the function is the key\n  if (keys === null) keys = [fn];\n\n  for (const entry of globalCache) {\n    // Find a match\n    if (shallowEqualArrays(keys, entry.keys, entry.equal)) {\n      // If we're pre-loading and the element is present, just return\n      if (preload) return undefined; // If an error occurred, throw\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'error')) throw entry.error; // If a response was successful, return\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'response')) {\n        if (config.lifespan && config.lifespan > 0) {\n          if (entry.timeout) clearTimeout(entry.timeout);\n          entry.timeout = setTimeout(entry.remove, config.lifespan);\n        }\n\n        return entry.response;\n      } // If the promise is still unresolved, throw\n\n\n      if (!preload) throw entry.promise;\n    }\n  } // The request is new or has changed.\n\n\n  const entry = {\n    keys,\n    equal: config.equal,\n    remove: () => {\n      const index = globalCache.indexOf(entry);\n      if (index !== -1) globalCache.splice(index, 1);\n    },\n    promise: // Execute the promise\n    (isPromise(fn) ? fn : fn(...keys) // When it resolves, store its value\n    ).then(response => {\n      entry.response = response; // Remove the entry in time if a lifespan was given\n\n      if (config.lifespan && config.lifespan > 0) {\n        entry.timeout = setTimeout(entry.remove, config.lifespan);\n      }\n    }) // Store caught errors, they will be thrown in the render-phase to bubble into an error-bound\n    .catch(error => entry.error = error)\n  }; // Register the entry\n\n  globalCache.push(entry); // And throw the promise, this yields control back to React\n\n  if (!preload) throw entry.promise;\n  return undefined;\n}\n\nconst suspend = (fn, keys, config) => query(fn, keys, false, config);\n\nconst preload = (fn, keys, config) => void query(fn, keys, true, config);\n\nconst peek = keys => {\n  var _globalCache$find;\n\n  return (_globalCache$find = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal))) == null ? void 0 : _globalCache$find.response;\n};\n\nconst clear = keys => {\n  if (keys === undefined || keys.length === 0) globalCache.splice(0, globalCache.length);else {\n    const entry = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal));\n    if (entry) entry.remove();\n  }\n};\n\nexport { clear, peek, preload, suspend };\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,YAAY,CAAA,UAAW,OAAO,YAAY,YAAY,OAAO,QAAQ,IAAI,KAAK;AAEpF,MAAM,cAAc,EAAE;AAEtB,SAAS,mBAAmB,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,GAAG,IAAM,MAAM,CAAC;IAC/D,IAAI,SAAS,MAAM,OAAO;IAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO;IAC3B,MAAM,MAAM,KAAK,MAAM;IACvB,IAAI,KAAK,MAAM,KAAK,KAAK,OAAO;IAEhC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,OAAO;IAEnE,OAAO;AACT;AAEA,SAAS,MAAM,EAAE,EAAE,OAAO,IAAI,EAAE,UAAU,KAAK,EAAE,SAAS,CAAC,CAAC;IAC1D,iDAAiD;IACjD,IAAI,SAAS,MAAM,OAAO;QAAC;KAAG;IAE9B,KAAK,MAAM,SAAS,YAAa;QAC/B,eAAe;QACf,IAAI,mBAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK,GAAG;YACrD,+DAA+D;YAC/D,IAAI,SAAS,OAAO,WAAW,8BAA8B;YAE7D,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,UAAU,MAAM,MAAM,KAAK,EAAE,uCAAuC;YAEpH,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,aAAa;gBAC3D,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,GAAG,GAAG;oBAC1C,IAAI,MAAM,OAAO,EAAE,aAAa,MAAM,OAAO;oBAC7C,MAAM,OAAO,GAAG,WAAW,MAAM,MAAM,EAAE,OAAO,QAAQ;gBAC1D;gBAEA,OAAO,MAAM,QAAQ;YACvB,EAAE,4CAA4C;YAG9C,IAAI,CAAC,SAAS,MAAM,MAAM,OAAO;QACnC;IACF,EAAE,qCAAqC;IAGvC,MAAM,QAAQ;QACZ;QACA,OAAO,OAAO,KAAK;QACnB,QAAQ;YACN,MAAM,QAAQ,YAAY,OAAO,CAAC;YAClC,IAAI,UAAU,CAAC,GAAG,YAAY,MAAM,CAAC,OAAO;QAC9C;QACA,SACA,CAAC,UAAU,MAAM,KAAK,MAAM,MAAM,oCAAoC;QACtE,EAAE,IAAI,CAAC,CAAA;YACL,MAAM,QAAQ,GAAG,UAAU,mDAAmD;YAE9E,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,GAAG,GAAG;gBAC1C,MAAM,OAAO,GAAG,WAAW,MAAM,MAAM,EAAE,OAAO,QAAQ;YAC1D;QACF,GAAG,6FAA6F;SAC/F,KAAK,CAAC,CAAA,QAAS,MAAM,KAAK,GAAG;IAChC,GAAG,qBAAqB;IAExB,YAAY,IAAI,CAAC,QAAQ,2DAA2D;IAEpF,IAAI,CAAC,SAAS,MAAM,MAAM,OAAO;IACjC,OAAO;AACT;AAEA,MAAM,UAAU,CAAC,IAAI,MAAM,SAAW,MAAM,IAAI,MAAM,OAAO;AAE7D,MAAM,UAAU,CAAC,IAAI,MAAM,SAAW,KAAK,MAAM,IAAI,MAAM,MAAM;AAEjE,MAAM,OAAO,CAAA;IACX,IAAI;IAEJ,OAAO,CAAC,oBAAoB,YAAY,IAAI,CAAC,CAAA,QAAS,mBAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK,EAAE,KAAK,OAAO,KAAK,IAAI,kBAAkB,QAAQ;AACzJ;AAEA,MAAM,QAAQ,CAAA;IACZ,IAAI,SAAS,aAAa,KAAK,MAAM,KAAK,GAAG,YAAY,MAAM,CAAC,GAAG,YAAY,MAAM;SAAO;QAC1F,MAAM,QAAQ,YAAY,IAAI,CAAC,CAAA,QAAS,mBAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK;QACxF,IAAI,OAAO,MAAM,MAAM;IACzB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/its-fine%402.0.0_%40types%2Breact%4019.0.10_react%4019.0.0/node_modules/its-fine/src/index.tsx"], "sourcesContent": ["import * as React from 'react'\r\nimport type <PERSON>actR<PERSON>onciler from 'react-reconciler'\r\n\r\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */\r\nconst useIsomorphicLayoutEffect = /* @__PURE__ */ (() =>\r\n  typeof window !== 'undefined' && (window.document?.createElement || window.navigator?.product === 'ReactNative'))()\r\n  ? React.useLayoutEffect\r\n  : React.useEffect\r\n\r\n/**\r\n * Represents a react-internal Fiber node.\r\n */\r\nexport type Fiber<T = any> = Omit<ReactReconciler.Fiber, 'stateNode'> & { stateNode: T }\r\n\r\n/**\r\n * Represents a {@link Fiber} node selector for traversal.\r\n */\r\nexport type FiberSelector<T = any> = (\r\n  /** The current {@link Fiber} node. */\r\n  node: Fiber<T | null>,\r\n) => boolean | void\r\n\r\n/**\r\n * Traverses up or down a {@link Fiber}, return `true` to stop and select a node.\r\n */\r\nexport function traverseFiber<T = any>(\r\n  /** Input {@link Fiber} to traverse. */\r\n  fiber: Fiber | undefined,\r\n  /** Whether to ascend and walk up the tree. Will walk down if `false`. */\r\n  ascending: boolean,\r\n  /** A {@link Fiber} node selector, returns the first match when `true` is passed. */\r\n  selector: FiberSelector<T>,\r\n): Fiber<T> | undefined {\r\n  if (!fiber) return\r\n  if (selector(fiber) === true) return fiber\r\n\r\n  let child = ascending ? fiber.return : fiber.child\r\n  while (child) {\r\n    const match = traverseFiber(child, ascending, selector)\r\n    if (match) return match\r\n\r\n    child = ascending ? null : child.sibling\r\n  }\r\n}\r\n\r\n// In development, React will warn about using contexts between renderers.\r\n// Hide the warning because its-fine fixes this issue\r\n// https://github.com/facebook/react/pull/12779\r\nfunction wrapContext<T>(context: React.Context<T>): React.Context<T> {\r\n  try {\r\n    return Object.defineProperties(context, {\r\n      _currentRenderer: {\r\n        get() {\r\n          return null\r\n        },\r\n        set() {},\r\n      },\r\n      _currentRenderer2: {\r\n        get() {\r\n          return null\r\n        },\r\n        set() {},\r\n      },\r\n    })\r\n  } catch (_) {\r\n    return context\r\n  }\r\n}\r\n\r\nconst FiberContext = /* @__PURE__ */ wrapContext(/* @__PURE__ */ React.createContext<Fiber>(null!))\r\n\r\n/**\r\n * A react-internal {@link Fiber} provider. This component binds React children to the React Fiber tree. Call its-fine hooks within this.\r\n */\r\nexport class FiberProvider extends React.Component<{ children?: React.ReactNode }> {\r\n  private _reactInternals!: Fiber\r\n\r\n  render() {\r\n    return <FiberContext.Provider value={this._reactInternals}>{this.props.children}</FiberContext.Provider>\r\n  }\r\n}\r\n\r\n/**\r\n * Returns the current react-internal {@link Fiber}. This is an implementation detail of [react-reconciler](https://github.com/facebook/react/tree/main/packages/react-reconciler).\r\n */\r\nexport function useFiber(): Fiber<null> | undefined {\r\n  const root = React.useContext(FiberContext)\r\n  if (root === null) throw new Error('its-fine: useFiber must be called within a <FiberProvider />!')\r\n\r\n  const id = React.useId()\r\n  const fiber = React.useMemo(() => {\r\n    for (const maybeFiber of [root, root?.alternate]) {\r\n      if (!maybeFiber) continue\r\n      const fiber = traverseFiber<null>(maybeFiber, false, (node) => {\r\n        let state = node.memoizedState\r\n        while (state) {\r\n          if (state.memoizedState === id) return true\r\n          state = state.next\r\n        }\r\n      })\r\n      if (fiber) return fiber\r\n    }\r\n  }, [root, id])\r\n\r\n  return fiber\r\n}\r\n\r\n/**\r\n * Represents a react-reconciler container instance.\r\n */\r\nexport interface ContainerInstance<T = any> {\r\n  containerInfo: T\r\n}\r\n\r\n/**\r\n * Returns the current react-reconciler container info passed to {@link ReactReconciler.Reconciler.createContainer}.\r\n *\r\n * In react-dom, a container will point to the root DOM element; in react-three-fiber, it will point to the root Zustand store.\r\n */\r\nexport function useContainer<T = any>(): T | undefined {\r\n  const fiber = useFiber()\r\n  const root = React.useMemo(\r\n    () => traverseFiber<ContainerInstance<T>>(fiber, true, (node) => node.stateNode?.containerInfo != null),\r\n    [fiber],\r\n  )\r\n\r\n  return root?.stateNode.containerInfo\r\n}\r\n\r\n/**\r\n * Returns the nearest react-reconciler child instance or the node created from {@link ReactReconciler.HostConfig.createInstance}.\r\n *\r\n * In react-dom, this would be a DOM element; in react-three-fiber this would be an instance descriptor.\r\n */\r\nexport function useNearestChild<T = any>(\r\n  /** An optional element type to filter to. */\r\n  type?: keyof React.JSX.IntrinsicElements,\r\n): React.RefObject<T | undefined> {\r\n  const fiber = useFiber()\r\n  const childRef = React.useRef<T>(undefined)\r\n\r\n  useIsomorphicLayoutEffect(() => {\r\n    childRef.current = traverseFiber<T>(\r\n      fiber,\r\n      false,\r\n      (node) => typeof node.type === 'string' && (type === undefined || node.type === type),\r\n    )?.stateNode\r\n  }, [fiber])\r\n\r\n  return childRef\r\n}\r\n\r\n/**\r\n * Returns the nearest react-reconciler parent instance or the node created from {@link ReactReconciler.HostConfig.createInstance}.\r\n *\r\n * In react-dom, this would be a DOM element; in react-three-fiber this would be an instance descriptor.\r\n */\r\nexport function useNearestParent<T = any>(\r\n  /** An optional element type to filter to. */\r\n  type?: keyof React.JSX.IntrinsicElements,\r\n): React.RefObject<T | undefined> {\r\n  const fiber = useFiber()\r\n  const parentRef = React.useRef<T>(undefined)\r\n\r\n  useIsomorphicLayoutEffect(() => {\r\n    parentRef.current = traverseFiber<T>(\r\n      fiber,\r\n      true,\r\n      (node) => typeof node.type === 'string' && (type === undefined || node.type === type),\r\n    )?.stateNode\r\n  }, [fiber])\r\n\r\n  return parentRef\r\n}\r\n\r\nexport type ContextMap = Map<React.Context<any>, any> & {\r\n  get<T>(context: React.Context<T>): T | undefined\r\n}\r\n\r\nconst REACT_CONTEXT_TYPE = Symbol.for('react.context')\r\n\r\nconst isContext = <T,>(type: unknown): type is React.Context<T> =>\r\n  type !== null && typeof type === 'object' && '$$typeof' in type && type.$$typeof === REACT_CONTEXT_TYPE\r\n\r\n/**\r\n * Returns a map of all contexts and their values.\r\n */\r\nexport function useContextMap(): ContextMap {\r\n  const fiber = useFiber()\r\n  const [contextMap] = React.useState(() => new Map<React.Context<any>, any>())\r\n\r\n  // Collect live context\r\n  contextMap.clear()\r\n  let node = fiber\r\n  while (node) {\r\n    const context = node.type\r\n    if (isContext(context) && context !== FiberContext && !contextMap.has(context)) {\r\n      contextMap.set(context, React.use(wrapContext(context)))\r\n    }\r\n\r\n    node = node.return!\r\n  }\r\n\r\n  return contextMap\r\n}\r\n\r\n/**\r\n * Represents a react-context bridge provider component.\r\n */\r\nexport type ContextBridge = React.FC<React.PropsWithChildren<{}>>\r\n\r\n/**\r\n * React Context currently cannot be shared across [React renderers](https://reactjs.org/docs/codebase-overview.html#renderers) but explicitly forwarded between providers (see [react#17275](https://github.com/facebook/react/issues/17275)). This hook returns a {@link ContextBridge} of live context providers to pierce Context across renderers.\r\n *\r\n * Pass {@link ContextBridge} as a component to a secondary renderer to enable context-sharing within its children.\r\n */\r\nexport function useContextBridge(): ContextBridge {\r\n  const contextMap = useContextMap()\r\n\r\n  // Flatten context and their memoized values into a `ContextBridge` provider\r\n  return React.useMemo(\r\n    () =>\r\n      Array.from(contextMap.keys()).reduce(\r\n        (Prev, context) => (props) =>\r\n          (\r\n            <Prev>\r\n              <context.Provider {...props} value={contextMap.get(context)} />\r\n            </Prev>\r\n          ),\r\n        (props) => <FiberProvider {...props} />,\r\n      ),\r\n    [contextMap],\r\n  )\r\n}\r\n"], "names": ["useIsomorphicLayoutEffect", "_a", "_b", "React", "traverseFiber", "fiber", "ascending", "selector", "child", "match", "wrapContext", "context", "_", "FiberContext", "FiberProvider", "useFiber", "root", "id", "maybeFiber", "node", "state", "useContainer", "useNearestChild", "type", "childRef", "useNearestParent", "parentRef", "REACT_CONTEXT_TYPE", "isContext", "useContextMap", "contextMap", "useContextBridge", "Prev", "props"], "mappings": ";;;;;;;;;;;;AAYA,MAAMA,IAA6C,aAAA,GAAA,CAAA,MAAA;;IACjD,OAAA,OAAO,UAAW,eAAA,CAAA,CAAA,CAAgBC,IAAA,OAAO,QAAA,KAAP,OAAA,KAAA,IAAAA,EAAiB,aAAA,KAAA,CAAA,CAAiBC,IAAA,OAAO,SAAA,KAAP,OAAA,KAAA,IAAAA,EAAkB,OAAA,MAAY,aAAA;AAAA,CAAA,EAAA,iSAChGC,EAAM,gBAAA,gSACNA,EAAM,UAAA;AAkBM,SAAAC,EAEdC,CAAAA,EAEAC,CAAAA,EAEAC,CAAAA,EACsB;IACtB,IAAI,CAACF,EAAO,CAAA;IACZ,IAAIE,EAASF,CAAK,MAAM,CAAA,EAAa,CAAA,OAAAA;IAErC,IAAIG,IAAQF,IAAYD,EAAM,MAAA,GAASA,EAAM,KAAA;IAC7C,MAAOG,GAAO;QACZ,MAAMC,IAAQL,EAAcI,GAAOF,GAAWC,CAAQ;QACtD,IAAIE,EAAc,CAAA,OAAAA;QAEVD,IAAAF,IAAY,OAAOE,EAAM,OAAA;IAAA;AAErC;AAKA,SAASE,EAAeC,CAAAA,EAA6C;IAC/D,IAAA;QACK,OAAA,OAAO,gBAAA,CAAiBA,GAAS;YACtC,kBAAkB;gBAChB,MAAM;oBACG,OAAA;gBACT;gBACA,MAAM,EAAA;YACR;YACA,mBAAmB;gBACjB,MAAM;oBACG,OAAA;gBACT;gBACA,MAAM,EAAA;YAAC;QACT,CACD;IAAA,EAAA,OACMC,GAAG;QACH,OAAAD;IAAA;AAEX;AAEA,MAAME,IAA+B,aAAA,GAAAH,EAAkC,aAAA,oSAAAP,EAAA,cAAA,EAAqB,IAAK,CAAC;AAKrF,MAAAW,uSAAsBX,EAAM,UAAA,CAA0C;IAGjF,SAAS;QACA,OAAA,aAAA,oSAAAA,EAAA,cAAA,EAACU,EAAa,QAAA,EAAb;YAAsB,OAAO,IAAA,CAAK,eAAA;QAAA,GAAkB,IAAA,CAAK,KAAA,CAAM,QAAS;IAAA;AAEpF;AAKO,SAASE,IAAoC;IAC5C,MAAAC,KAAOb,EAAM,2SAAA,EAAWU,CAAY;IAC1C,IAAIG,MAAS,KAAY,CAAA,MAAA,IAAI,MAAM,+DAA+D;IAE5F,MAAAC,qSAAKd,EAAM,MAAA,CAAM;IAehB,WAdOA,EAAM,qSAAA;qBAAQ,MAAM;YAChC,KAAA,MAAWe,KAAc;gBAACF;gBAAMA,KAAA,OAAA,KAAA,IAAAA,EAAM,SAAS;aAAA,CAAG;gBAChD,IAAI,CAACE,EAAY,CAAA;gBACjB,MAAMb,IAAQD,EAAoBc,GAAY,CAAA;mCAAO,CAACC,MAAS;wBAC7D,IAAIC,IAAQD,EAAK,aAAA;wBACjB,MAAOC,GAAO;4BACR,IAAAA,EAAM,aAAA,KAAkBH,EAAW,CAAA,OAAA,CAAA;4BACvCG,IAAQA,EAAM,IAAA;wBAAA;oBAChB,CACD;;gBACD,IAAIf,EAAcA,CAAAA,OAAAA;YAAA;QACpB;oBACC;QAACW;QAAMC,CAAE;KAAC;AAGf;AAcO,SAASI,IAAuC;IACrD,MAAMhB,IAAQU,EAAS,GACjBC,qSAAOb,EAAM,QAAA;wBACjB,IAAMC,EAAoCC,GAAO,CAAA;gCAAM,CAACc,MAAS;;oBAAA,OAAA,CAAA,CAAAlB,IAAAkB,EAAK,SAAA,KAAL,OAAA,KAAA,IAAAlB,EAAgB,aAAA,KAAiB;gBAAA,CAAI;;uBACtG;QAACI,CAAK;KAAA;IAGR,OAAOW,KAAA,OAAA,KAAA,IAAAA,EAAM,SAAA,CAAU,aAAA;AACzB;AAOO,SAASM,EAEdC,CAAAA,EACgC;IAChC,MAAMlB,IAAQU,EAAS,GACjBS,qSAAWrB,EAAM,OAAA,EAAU,KAAA,CAAS;IAE1C,OAAAH,EAA0B,MAAM;;QAC9BwB,EAAS,OAAA,GAAA,CAAUvB,IAAAG,EACjBC,GACA,CAAA,GACA,CAACc,IAAS,OAAOA,EAAK,IAAA,IAAS,YAAA,CAAaI,MAAS,KAAA,KAAaJ,EAAK,IAAA,KAASI,CAAAA,EAAA,KAH/D,OAAA,KAAA,IAAAtB,EAIhB,SAAA;IAAA,GACF;QAACI,CAAK;KAAC,GAEHmB;AACT;AAOO,SAASC,EAEdF,CAAAA,EACgC;IAChC,MAAMlB,IAAQU,EAAS,GACjBW,qSAAYvB,EAAM,OAAA,EAAU,KAAA,CAAS;IAE3C,OAAAH,EAA0B,MAAM;;QAC9B0B,EAAU,OAAA,GAAA,CAAUzB,IAAAG,EAClBC,GACA,CAAA,GACA,CAACc,IAAS,OAAOA,EAAK,IAAA,IAAS,YAAA,CAAaI,MAAS,KAAA,KAAaJ,EAAK,IAAA,KAASI,CAAAA,EAAA,KAH9D,OAAA,KAAA,IAAAtB,EAIjB,SAAA;IAAA,GACF;QAACI,CAAK;KAAC,GAEHqB;AACT;AAMA,MAAMC,IAAqB,OAAO,GAAA,CAAI,eAAe,GAE/CC,IAAY,CAAKL,IACrBA,MAAS,QAAQ,OAAOA,KAAS,YAAY,cAAcA,KAAQA,EAAK,QAAA,KAAaI;AAKhF,SAASE,IAA4B;IAC1C,MAAMxB,IAAQU,EAAS,GACjB,CAACe,CAAU,CAAA,oSAAI3B,EAAM,SAAA;sBAAS,IAAM,aAAA,GAAA,IAAI,KAA8B;;IAG5E2B,EAAW,KAAA,CAAM;IACjB,IAAIX,IAAOd;IACX,MAAOc,GAAM;QACX,MAAMR,IAAUQ,EAAK,IAAA;QACjBS,EAAUjB,CAAO,KAAKA,MAAYE,KAAgB,CAACiB,EAAW,GAAA,CAAInB,CAAO,KAC3EmB,EAAW,GAAA,CAAInB,oSAASR,EAAM,IAAA,EAAIO,EAAYC,CAAO,CAAC,CAAC,GAGzDQ,IAAOA,EAAK,MAAA;IAAA;IAGP,OAAAW;AACT;AAYO,SAASC,IAAkC;IAChD,MAAMD,IAAaD,EAAc;IAGjC,wSAAO1B,EAAM,QAAA;qBACX,IACE,MAAM,IAAA,CAAK2B,EAAW,IAAA,CAAA,CAAM,EAAE,MAAA;6BAC5B,CAACE,GAAMrB;qCAAY,CAACsB,IAEhB,aAAA,oSAAA9B,EAAA,cAAA,EAAC6B,GAAAA,MACE,aAAA,oSAAA7B,EAAA,cAAA,EAAAQ,EAAQ,QAAA,EAAR;gCAAkB,GAAGsB,CAAAA;gCAAO,OAAOH,EAAW,GAAA,CAAInB,CAAO;4BAAA,CAAG,CAC/D;;;6BAEJ,CAACsB,IAAW,aAAA,oSAAA9B,EAAA,cAAA,EAAAW,GAAA;wBAAe,GAAGmB,CAAAA;oBAAO,CAAA;;oBAEzC;QAACH,CAAU;KAAA;AAEf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 756, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "names": ["createDebounce", "callback", "ms", "timeoutId", "args", "useMeasure", "debounce", "scroll", "polyfill", "offsetSize", "ResizeObserver", "bounds", "set", "useState", "state", "useRef", "scrollDebounce", "resizeDebounce", "mounted", "useEffect", "forceRefresh", "resizeChange", "scrollChange", "useMemo", "left", "top", "width", "height", "bottom", "right", "x", "y", "size", "areBoundsEqual", "removeListeners", "element", "addListeners", "scrollContainer", "ref", "node", "findScrollContainers", "useOnWindowScroll", "useOnWindowResize", "onWindowResize", "cb", "onScroll", "enabled", "result", "overflow", "overflowX", "overflowY", "prop", "keys", "a", "b", "key"], "mappings": ";;;;;AAEA,SAASA,EAAmDC,CAAAA,EAAaC,CAAAA,CAAY;IAC/EC,IAAAA;IAEJ,OAAO,CAAA,GAAIC,IAA8B;QAChC,OAAA,YAAA,CAAaD,CAAS,GAC7BA,IAAY,OAAO,UAAA,CAAW,IAAMF,EAAS,GAAGG,CAAI,GAAGF,CAAE;IAC3D;AACF;AA0CA,SAASG,EACP,EAAE,UAAAC,CAAAA,EAAU,QAAAC,CAAAA,EAAQ,UAAAC,CAAAA,EAAU,YAAAC,CAAW,EAAA,GAAa;IAAE,UAAU;IAAG,QAAQ,CAAA;IAAO,YAAY,CAAA;AAAA,CAAA,CACxF;IACR,MAAMC,IACJF,KAAAA,CAAa,OAAO,UAAW,cAAc,KAAqB;IAAA,IAAM,OAAe,cAAA;IAEzF,IAAI,CAACE,GACH,MAAM,IAAI,MACR,gJACF;IAGF,MAAM,CAACC,GAAQC,CAAG,CAAA,GAAIC,4SAAAA,EAAuB;QAC3C,MAAM;QACN,KAAK;QACL,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,GAAG;QACH,GAAG;IAAA,CACJ,GAGKC,QAAQC,sSAAAA,EAAc;QAC1B,SAAS;QACT,kBAAkB;QAClB,gBAAgB;QAChB,YAAYJ;QACZ,oBAAoB;IAAA,CACrB,GAGKK,IAAiBV,IAAY,OAAOA,KAAa,WAAWA,IAAWA,EAAS,MAAA,GAAU,MAC1FW,IAAiBX,IAAY,OAAOA,KAAa,WAAWA,IAAWA,EAAS,MAAA,GAAU,MAG1FY,8SAAUH,EAAO,CAAA,CAAK;iTAC5BI,EAAU,IAAA,CACRD,EAAQ,OAAA,GAAU,CAAA,GACX,IAAM,KAAA,CAAMA,EAAQ,OAAA,GAAU,CAAA,CAAA,CAAA,CACtC;IAGD,MAAM,CAACE,GAAcC,GAAcC,CAAY,CAAA,GAAIC,2SAAAA,EAAQ,IAAM;QAC/D,MAAMtB,IAAW,IAAM;YACjB,IAAA,CAACa,EAAM,OAAA,CAAQ,OAAA,EAAS;YACtB,MAAA,EAAE,MAAAU,CAAAA,EAAM,KAAAC,CAAAA,EAAK,OAAAC,CAAAA,EAAO,QAAAC,CAAAA,EAAQ,QAAAC,CAAAA,EAAQ,OAAAC,CAAAA,EAAO,GAAAC,CAAAA,EAAG,GAAAC,CAAE,EAAA,GACpDjB,EAAM,OAAA,CAAQ,OAAA,CAAQ,qBAAA,CAAsB,GAExCkB,IAAO;gBACX,MAAAR;gBACA,KAAAC;gBACA,OAAAC;gBACA,QAAAC;gBACA,QAAAC;gBACA,OAAAC;gBACA,GAAAC;gBACA,GAAAC;YACF;YAEIjB,EAAM,OAAA,CAAQ,OAAA,YAAmB,eAAeL,KAAAA,CAC7CuB,EAAA,MAAA,GAASlB,EAAM,OAAA,CAAQ,OAAA,CAAQ,YAAA,EAC/BkB,EAAA,KAAA,GAAQlB,EAAM,OAAA,CAAQ,OAAA,CAAQ,WAAA,GAGrC,OAAO,MAAA,CAAOkB,CAAI,GACdd,EAAQ,OAAA,IAAW,CAACe,EAAenB,EAAM,OAAA,CAAQ,UAAA,EAAYkB,CAAI,KAAGpB,EAAKE,EAAM,OAAA,CAAQ,UAAA,GAAakB,CAAK;QAC/G;QACO,OAAA;YACL/B;YACAgB,IAAiBjB,EAAeC,GAAUgB,CAAc,IAAIhB;YAC5De,IAAiBhB,EAAeC,GAAUe,CAAc,IAAIf,CAC9D;SAAA;IAAA,GACC;QAACW;QAAKH;QAAYO;QAAgBC,CAAc;KAAC;IAGpD,SAASiB,GAAkB;QACrBpB,EAAM,OAAA,CAAQ,gBAAA,IAAA,CACVA,EAAA,OAAA,CAAQ,gBAAA,CAAiB,OAAA,CAASqB,KAAYA,EAAQ,mBAAA,CAAoB,UAAUb,GAAc,CAAA,CAAI,CAAC,GAC7GR,EAAM,OAAA,CAAQ,gBAAA,GAAmB,IAAA,GAG/BA,EAAM,OAAA,CAAQ,cAAA,IAAA,CACVA,EAAA,OAAA,CAAQ,cAAA,CAAe,UAAA,CAAW,GACxCA,EAAM,OAAA,CAAQ,cAAA,GAAiB,IAAA,GAG7BA,EAAM,OAAA,CAAQ,kBAAA,IAAA,CACZ,iBAAiB,UAAU,yBAAyB,OAAO,WAAA,GAC7D,OAAO,WAAA,CAAY,mBAAA,CAAoB,UAAUA,EAAM,OAAA,CAAQ,kBAAkB,IACxE,yBAAyB,UAClC,OAAO,mBAAA,CAAoB,qBAAqBA,EAAM,OAAA,CAAQ,kBAAkB,CAAA;IAEpF;IAIF,SAASsB,GAAe;QACjBtB,EAAM,OAAA,CAAQ,OAAA,IAAA,CACnBA,EAAM,OAAA,CAAQ,cAAA,GAAiB,IAAIJ,EAAeY,CAAY,GAC9DR,EAAM,OAAA,CAAQ,cAAA,CAAgB,OAAA,CAAQA,EAAM,OAAA,CAAQ,OAAO,GACvDP,KAAUO,EAAM,OAAA,CAAQ,gBAAA,IAC1BA,EAAM,OAAA,CAAQ,gBAAA,CAAiB,OAAA,CAASuB,KACtCA,EAAgB,gBAAA,CAAiB,UAAUf,GAAc;gBAAE,SAAS,CAAA;gBAAM,SAAS,CAAA;YAAM,CAAA,CAC3F,GAIIR,EAAA,OAAA,CAAQ,kBAAA,GAAqB,IAAM;YAC1BQ,EAAA;QACf,GAGI,iBAAiB,UAAU,sBAAsB,OAAO,WAAA,GAC1D,OAAO,WAAA,CAAY,gBAAA,CAAiB,UAAUR,EAAM,OAAA,CAAQ,kBAAkB,IACrE,yBAAyB,UAElC,OAAO,gBAAA,CAAiB,qBAAqBA,EAAM,OAAA,CAAQ,kBAAkB,CAAA;IAC/E;IAIIwB,MAAAA,KAAOC,GAAkC;QACzC,CAACA,KAAQA,MAASzB,EAAM,OAAA,CAAQ,OAAA,IAAA,CACpBoB,EAAA,GAChBpB,EAAM,OAAA,CAAQ,OAAA,GAAUyB,GAClBzB,EAAA,OAAA,CAAQ,gBAAA,GAAmB0B,EAAqBD,CAAI,GAC7CH,EAAA,CAAA;IACf;IAGkBK,OAAAA,EAAAnB,GAAc,CAAQf,CAAAA,CAAO,GAC/CmC,EAAkBrB,CAAY,gTAG9BF,EAAU,IAAM;QACEe,EAAA,GACHE,EAAA;IACZ,GAAA;QAAC7B;QAAQe;QAAcD,CAAY;KAAC,IAG7BF,4SAAAA,EAAA,IAAMe,GAAiB,EAAE,GAC5B;QAACI;QAAK3B;QAAQS,CAAY;;AACnC;AAGA,SAASsB,EAAkBC,CAAAA,CAAwC;IACjExB,6SAAAA,EAAU,IAAM;QACd,MAAMyB,IAAKD;QACJ,OAAA,OAAA,gBAAA,CAAiB,UAAUC,CAAE,GAC7B,IAAM,KAAK,OAAO,mBAAA,CAAoB,UAAUA,CAAE;IAAA,GACxD;QAACD,CAAc;KAAC;AACrB;AACA,SAASF,EAAkBI,CAAAA,EAAsBC,CAAAA,CAAkB;iTACjE3B,EAAU,IAAM;QACd,IAAI2B,GAAS;YACX,MAAMF,IAAKC;YACJ,OAAA,OAAA,gBAAA,CAAiB,UAAUD,GAAI;gBAAE,SAAS,CAAA;gBAAM,SAAS,CAAA;YAAA,CAAM,GAC/D,IAAM,KAAK,OAAO,mBAAA,CAAoB,UAAUA,GAAI,CAAA,CAAI;QAAA;IACjE,GACC;QAACC;QAAUC,CAAO;KAAC;AACxB;AAGA,SAASN,EAAqBL,CAAAA,CAAsD;IAClF,MAAMY,IAA6B,CAAC,CAAA;IACpC,IAAI,CAACZ,KAAWA,MAAY,SAAS,IAAA,EAAaY,OAAAA;IAC5C,MAAA,EAAE,UAAAC,CAAAA,EAAU,WAAAC,CAAAA,EAAW,WAAAC,CAAc,EAAA,GAAA,OAAO,gBAAA,CAAiBf,CAAO;IACtE,OAAA;QAACa;QAAUC;QAAWC,CAAS;KAAA,CAAE,IAAA,CAAMC,KAASA,MAAS,UAAUA,MAAS,QAAQ,KAAGJ,EAAO,IAAA,CAAKZ,CAAO,GACvG,CAAC;WAAGY,EAAQ;WAAGP,EAAqBL,EAAQ,aAAa,CAAC;;AACnE;AAGA,MAAMiB,IAA+B;IAAC;IAAK;IAAK;IAAO;IAAU;IAAQ;IAAS;IAAS,QAAQ;CAAA,EAC7FnB,IAAiB,CAACoB,GAAiBC,IAA6BF,EAAK,KAAA,EAAOG,IAAQF,CAAAA,CAAEE,CAAG,CAAA,KAAMD,CAAAA,CAAEC,CAAG,CAAC", "debugId": null}}, {"offset": {"line": 891, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 896, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-spring%2Brafz%4010.0.1/node_modules/%40react-spring/rafz/dist/cjs/react-spring_rafz.development.cjs"], "sourcesContent": ["\"use strict\";\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  __raf: () => __raf,\n  raf: () => raf\n});\nmodule.exports = __toCommonJS(src_exports);\nvar updateQueue = makeQueue();\nvar raf = (fn) => schedule(fn, updateQueue);\nvar writeQueue = makeQueue();\nraf.write = (fn) => schedule(fn, writeQueue);\nvar onStartQueue = makeQueue();\nraf.onStart = (fn) => schedule(fn, onStartQueue);\nvar onFrameQueue = makeQueue();\nraf.onFrame = (fn) => schedule(fn, onFrameQueue);\nvar onFinishQueue = makeQueue();\nraf.onFinish = (fn) => schedule(fn, onFinishQueue);\nvar timeouts = [];\nraf.setTimeout = (handler, ms) => {\n  const time = raf.now() + ms;\n  const cancel = () => {\n    const i = timeouts.findIndex((t) => t.cancel == cancel);\n    if (~i) timeouts.splice(i, 1);\n    pendingCount -= ~i ? 1 : 0;\n  };\n  const timeout = { time, handler, cancel };\n  timeouts.splice(findTimeout(time), 0, timeout);\n  pendingCount += 1;\n  start();\n  return timeout;\n};\nvar findTimeout = (time) => ~(~timeouts.findIndex((t) => t.time > time) || ~timeouts.length);\nraf.cancel = (fn) => {\n  onStartQueue.delete(fn);\n  onFrameQueue.delete(fn);\n  onFinishQueue.delete(fn);\n  updateQueue.delete(fn);\n  writeQueue.delete(fn);\n};\nraf.sync = (fn) => {\n  sync = true;\n  raf.batchedUpdates(fn);\n  sync = false;\n};\nraf.throttle = (fn) => {\n  let lastArgs;\n  function queuedFn() {\n    try {\n      fn(...lastArgs);\n    } finally {\n      lastArgs = null;\n    }\n  }\n  function throttled(...args) {\n    lastArgs = args;\n    raf.onStart(queuedFn);\n  }\n  throttled.handler = fn;\n  throttled.cancel = () => {\n    onStartQueue.delete(queuedFn);\n    lastArgs = null;\n  };\n  return throttled;\n};\nvar nativeRaf = typeof window != \"undefined\" ? window.requestAnimationFrame : (\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  () => {\n  }\n);\nraf.use = (impl) => nativeRaf = impl;\nraf.now = typeof performance != \"undefined\" ? () => performance.now() : Date.now;\nraf.batchedUpdates = (fn) => fn();\nraf.catch = console.error;\nraf.frameLoop = \"always\";\nraf.advance = () => {\n  if (raf.frameLoop !== \"demand\") {\n    console.warn(\n      \"Cannot call the manual advancement of rafz whilst frameLoop is not set as demand\"\n    );\n  } else {\n    update();\n  }\n};\nvar ts = -1;\nvar pendingCount = 0;\nvar sync = false;\nfunction schedule(fn, queue) {\n  if (sync) {\n    queue.delete(fn);\n    fn(0);\n  } else {\n    queue.add(fn);\n    start();\n  }\n}\nfunction start() {\n  if (ts < 0) {\n    ts = 0;\n    if (raf.frameLoop !== \"demand\") {\n      nativeRaf(loop);\n    }\n  }\n}\nfunction stop() {\n  ts = -1;\n}\nfunction loop() {\n  if (~ts) {\n    nativeRaf(loop);\n    raf.batchedUpdates(update);\n  }\n}\nfunction update() {\n  const prevTs = ts;\n  ts = raf.now();\n  const count = findTimeout(ts);\n  if (count) {\n    eachSafely(timeouts.splice(0, count), (t) => t.handler());\n    pendingCount -= count;\n  }\n  if (!pendingCount) {\n    stop();\n    return;\n  }\n  onStartQueue.flush();\n  updateQueue.flush(prevTs ? Math.min(64, ts - prevTs) : 16.667);\n  onFrameQueue.flush();\n  writeQueue.flush();\n  onFinishQueue.flush();\n}\nfunction makeQueue() {\n  let next = /* @__PURE__ */ new Set();\n  let current = next;\n  return {\n    add(fn) {\n      pendingCount += current == next && !next.has(fn) ? 1 : 0;\n      next.add(fn);\n    },\n    delete(fn) {\n      pendingCount -= current == next && next.has(fn) ? 1 : 0;\n      return next.delete(fn);\n    },\n    flush(arg) {\n      if (current.size) {\n        next = /* @__PURE__ */ new Set();\n        pendingCount -= current.size;\n        eachSafely(current, (fn) => fn(arg) && next.add(fn));\n        pendingCount += next.size;\n        current = next;\n      }\n    }\n  };\n}\nfunction eachSafely(values, each) {\n  values.forEach((value) => {\n    try {\n      each(value);\n    } catch (e) {\n      raf.catch(e);\n    }\n  });\n}\nvar __raf = {\n  /** The number of pending tasks */\n  count() {\n    return pendingCount;\n  },\n  /** Whether there's a raf update loop running */\n  isRunning() {\n    return ts >= 0;\n  },\n  /** Clear internal state. Never call from update loop! */\n  clear() {\n    ts = -1;\n    timeouts = [];\n    onStartQueue = makeQueue();\n    updateQueue = makeQueue();\n    onFrameQueue = makeQueue();\n    writeQueue = makeQueue();\n    onFinishQueue = makeQueue();\n    pendingCount = 0;\n  }\n};\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  __raf,\n  raf\n});\n"], "names": [], "mappings": "AAAA;AACA,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,mBAAmB,OAAO,wBAAwB;AACtD,IAAI,oBAAoB,OAAO,mBAAmB;AAClD,IAAI,eAAe,OAAO,SAAS,CAAC,cAAc;AAClD,IAAI,WAAW,CAAC,QAAQ;IACtB,IAAK,IAAI,QAAQ,IACf,UAAU,QAAQ,MAAM;QAAE,KAAK,GAAG,CAAC,KAAK;QAAE,YAAY;IAAK;AAC/D;AACA,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ;IACnC,IAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;QAClE,KAAK,IAAI,OAAO,kBAAkB,MAChC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,QAAQ,QAAQ,QACzC,UAAU,IAAI,KAAK;YAAE,KAAK,IAAM,IAAI,CAAC,IAAI;YAAE,YAAY,CAAC,CAAC,OAAO,iBAAiB,MAAM,IAAI,KAAK,KAAK,UAAU;QAAC;IACtH;IACA,OAAO;AACT;AACA,IAAI,eAAe,CAAC,MAAQ,YAAY,UAAU,CAAC,GAAG,cAAc;QAAE,OAAO;IAAK,IAAI;AAEtF,eAAe;AACf,IAAI,cAAc,CAAC;AACnB,SAAS,aAAa;IACpB,OAAO,IAAM;IACb,KAAK,IAAM;AACb;AACA,OAAO,OAAO,GAAG,aAAa;AAC9B,IAAI,cAAc;AAClB,IAAI,MAAM,CAAC,KAAO,SAAS,IAAI;AAC/B,IAAI,aAAa;AACjB,IAAI,KAAK,GAAG,CAAC,KAAO,SAAS,IAAI;AACjC,IAAI,eAAe;AACnB,IAAI,OAAO,GAAG,CAAC,KAAO,SAAS,IAAI;AACnC,IAAI,eAAe;AACnB,IAAI,OAAO,GAAG,CAAC,KAAO,SAAS,IAAI;AACnC,IAAI,gBAAgB;AACpB,IAAI,QAAQ,GAAG,CAAC,KAAO,SAAS,IAAI;AACpC,IAAI,WAAW,EAAE;AACjB,IAAI,UAAU,GAAG,CAAC,SAAS;IACzB,MAAM,OAAO,IAAI,GAAG,KAAK;IACzB,MAAM,SAAS;QACb,MAAM,IAAI,SAAS,SAAS,CAAC,CAAC,IAAM,EAAE,MAAM,IAAI;QAChD,IAAI,CAAC,GAAG,SAAS,MAAM,CAAC,GAAG;QAC3B,gBAAgB,CAAC,IAAI,IAAI;IAC3B;IACA,MAAM,UAAU;QAAE;QAAM;QAAS;IAAO;IACxC,SAAS,MAAM,CAAC,YAAY,OAAO,GAAG;IACtC,gBAAgB;IAChB;IACA,OAAO;AACT;AACA,IAAI,cAAc,CAAC,OAAS,CAAC,CAAC,CAAC,SAAS,SAAS,CAAC,CAAC,IAAM,EAAE,IAAI,GAAG,SAAS,CAAC,SAAS,MAAM;AAC3F,IAAI,MAAM,GAAG,CAAC;IACZ,aAAa,MAAM,CAAC;IACpB,aAAa,MAAM,CAAC;IACpB,cAAc,MAAM,CAAC;IACrB,YAAY,MAAM,CAAC;IACnB,WAAW,MAAM,CAAC;AACpB;AACA,IAAI,IAAI,GAAG,CAAC;IACV,OAAO;IACP,IAAI,cAAc,CAAC;IACnB,OAAO;AACT;AACA,IAAI,QAAQ,GAAG,CAAC;IACd,IAAI;IACJ,SAAS;QACP,IAAI;YACF,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IACA,SAAS,UAAU,GAAG,IAAI;QACxB,WAAW;QACX,IAAI,OAAO,CAAC;IACd;IACA,UAAU,OAAO,GAAG;IACpB,UAAU,MAAM,GAAG;QACjB,aAAa,MAAM,CAAC;QACpB,WAAW;IACb;IACA,OAAO;AACT;AACA,IAAI,YAAY,OAAO,UAAU,cAAc,OAAO,qBAAqB,GACzE,gEAAgE;AAChE,KACA;AAEF,IAAI,GAAG,GAAG,CAAC,OAAS,YAAY;AAChC,IAAI,GAAG,GAAG,OAAO,eAAe,cAAc,IAAM,YAAY,GAAG,KAAK,KAAK,GAAG;AAChF,IAAI,cAAc,GAAG,CAAC,KAAO;AAC7B,IAAI,KAAK,GAAG,QAAQ,KAAK;AACzB,IAAI,SAAS,GAAG;AAChB,IAAI,OAAO,GAAG;IACZ,IAAI,IAAI,SAAS,KAAK,UAAU;QAC9B,QAAQ,IAAI,CACV;IAEJ,OAAO;QACL;IACF;AACF;AACA,IAAI,KAAK,CAAC;AACV,IAAI,eAAe;AACnB,IAAI,OAAO;AACX,SAAS,SAAS,EAAE,EAAE,KAAK;IACzB,IAAI,MAAM;QACR,MAAM,MAAM,CAAC;QACb,GAAG;IACL,OAAO;QACL,MAAM,GAAG,CAAC;QACV;IACF;AACF;AACA,SAAS;IACP,IAAI,KAAK,GAAG;QACV,KAAK;QACL,IAAI,IAAI,SAAS,KAAK,UAAU;YAC9B,UAAU;QACZ;IACF;AACF;AACA,SAAS;IACP,KAAK,CAAC;AACR;AACA,SAAS;IACP,IAAI,CAAC,IAAI;QACP,UAAU;QACV,IAAI,cAAc,CAAC;IACrB;AACF;AACA,SAAS;IACP,MAAM,SAAS;IACf,KAAK,IAAI,GAAG;IACZ,MAAM,QAAQ,YAAY;IAC1B,IAAI,OAAO;QACT,WAAW,SAAS,MAAM,CAAC,GAAG,QAAQ,CAAC,IAAM,EAAE,OAAO;QACtD,gBAAgB;IAClB;IACA,IAAI,CAAC,cAAc;QACjB;QACA;IACF;IACA,aAAa,KAAK;IAClB,YAAY,KAAK,CAAC,SAAS,KAAK,GAAG,CAAC,IAAI,KAAK,UAAU;IACvD,aAAa,KAAK;IAClB,WAAW,KAAK;IAChB,cAAc,KAAK;AACrB;AACA,SAAS;IACP,IAAI,OAAO,aAAa,GAAG,IAAI;IAC/B,IAAI,UAAU;IACd,OAAO;QACL,KAAI,EAAE;YACJ,gBAAgB,WAAW,QAAQ,CAAC,KAAK,GAAG,CAAC,MAAM,IAAI;YACvD,KAAK,GAAG,CAAC;QACX;QACA,QAAO,EAAE;YACP,gBAAgB,WAAW,QAAQ,KAAK,GAAG,CAAC,MAAM,IAAI;YACtD,OAAO,KAAK,MAAM,CAAC;QACrB;QACA,OAAM,GAAG;YACP,IAAI,QAAQ,IAAI,EAAE;gBAChB,OAAO,aAAa,GAAG,IAAI;gBAC3B,gBAAgB,QAAQ,IAAI;gBAC5B,WAAW,SAAS,CAAC,KAAO,GAAG,QAAQ,KAAK,GAAG,CAAC;gBAChD,gBAAgB,KAAK,IAAI;gBACzB,UAAU;YACZ;QACF;IACF;AACF;AACA,SAAS,WAAW,MAAM,EAAE,IAAI;IAC9B,OAAO,OAAO,CAAC,CAAC;QACd,IAAI;YACF,KAAK;QACP,EAAE,OAAO,GAAG;YACV,IAAI,KAAK,CAAC;QACZ;IACF;AACF;AACA,IAAI,QAAQ;IACV,gCAAgC,GAChC;QACE,OAAO;IACT;IACA,8CAA8C,GAC9C;QACE,OAAO,MAAM;IACf;IACA,uDAAuD,GACvD;QACE,KAAK,CAAC;QACN,WAAW,EAAE;QACb,eAAe;QACf,cAAc;QACd,eAAe;QACf,aAAa;QACb,gBAAgB;QAChB,eAAe;IACjB;AACF;AACA,6DAA6D;AAC7D,KAAK,CAAC,OAAO,OAAO,GAAG;IACrB;IACA;AACF,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1103, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1108, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-spring%2Brafz%4010.0.1/node_modules/%40react-spring/rafz/dist/cjs/index.js"], "sourcesContent": ["'use strict'\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./react-spring_rafz.production.min.cjs')\n} else {\n  module.exports = require('./react-spring_rafz.development.cjs')\n}"], "names": [], "mappings": "AACI;AADJ;AACA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1120, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-spring%2Bshared%4010.0.1_react%4019.0.0/node_modules/%40react-spring/shared/dist/cjs/react-spring_shared.development.cjs"], "sourcesContent": ["\"use strict\";\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to2, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to2, key) && key !== except)\n        __defProp(to2, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to2;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  FluidValue: () => FluidValue,\n  Globals: () => globals_exports,\n  addFluidObserver: () => addFluidObserver,\n  callFluidObserver: () => callFluidObserver,\n  callFluidObservers: () => callFluidObservers,\n  clamp: () => clamp,\n  colorToRgba: () => colorToRgba,\n  colors: () => colors2,\n  createInterpolator: () => createInterpolator,\n  createStringInterpolator: () => createStringInterpolator2,\n  defineHidden: () => defineHidden,\n  deprecateDirectCall: () => deprecateDirectCall,\n  deprecateInterpolate: () => deprecateInterpolate,\n  each: () => each,\n  eachProp: () => eachProp,\n  easings: () => easings,\n  flush: () => flush,\n  flushCalls: () => flushCalls,\n  frameLoop: () => frameLoop,\n  getFluidObservers: () => getFluidObservers,\n  getFluidValue: () => getFluidValue,\n  hasFluidValue: () => hasFluidValue,\n  hex3: () => hex3,\n  hex4: () => hex4,\n  hex6: () => hex6,\n  hex8: () => hex8,\n  hsl: () => hsl,\n  hsla: () => hsla,\n  is: () => is,\n  isAnimatedString: () => isAnimatedString,\n  isEqual: () => isEqual,\n  isSSR: () => isSSR,\n  noop: () => noop,\n  onResize: () => onResize,\n  onScroll: () => onScroll,\n  once: () => once,\n  prefix: () => prefix,\n  raf: () => import_rafz4.raf,\n  removeFluidObserver: () => removeFluidObserver,\n  rgb: () => rgb,\n  rgba: () => rgba,\n  setFluidGetter: () => setFluidGetter,\n  toArray: () => toArray,\n  useConstant: () => useConstant,\n  useForceUpdate: () => useForceUpdate,\n  useIsomorphicLayoutEffect: () => useIsomorphicLayoutEffect,\n  useMemoOne: () => useMemoOne,\n  useOnce: () => useOnce,\n  usePrev: () => usePrev,\n  useReducedMotion: () => useReducedMotion\n});\nmodule.exports = __toCommonJS(src_exports);\n\n// src/globals.ts\nvar globals_exports = {};\n__export(globals_exports, {\n  assign: () => assign,\n  colors: () => colors,\n  createStringInterpolator: () => createStringInterpolator,\n  skipAnimation: () => skipAnimation,\n  to: () => to,\n  willAdvance: () => willAdvance\n});\nvar import_rafz = require(\"@react-spring/rafz\");\n\n// src/helpers.ts\nfunction noop() {\n}\nvar defineHidden = (obj, key, value) => Object.defineProperty(obj, key, { value, writable: true, configurable: true });\nvar is = {\n  arr: Array.isArray,\n  obj: (a) => !!a && a.constructor.name === \"Object\",\n  fun: (a) => typeof a === \"function\",\n  str: (a) => typeof a === \"string\",\n  num: (a) => typeof a === \"number\",\n  und: (a) => a === void 0\n};\nfunction isEqual(a, b) {\n  if (is.arr(a)) {\n    if (!is.arr(b) || a.length !== b.length) return false;\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] !== b[i]) return false;\n    }\n    return true;\n  }\n  return a === b;\n}\nvar each = (obj, fn) => obj.forEach(fn);\nfunction eachProp(obj, fn, ctx) {\n  if (is.arr(obj)) {\n    for (let i = 0; i < obj.length; i++) {\n      fn.call(ctx, obj[i], `${i}`);\n    }\n    return;\n  }\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      fn.call(ctx, obj[key], key);\n    }\n  }\n}\nvar toArray = (a) => is.und(a) ? [] : is.arr(a) ? a : [a];\nfunction flush(queue, iterator) {\n  if (queue.size) {\n    const items = Array.from(queue);\n    queue.clear();\n    each(items, iterator);\n  }\n}\nvar flushCalls = (queue, ...args) => flush(queue, (fn) => fn(...args));\nvar isSSR = () => typeof window === \"undefined\" || !window.navigator || /ServerSideRendering|^Deno\\//.test(window.navigator.userAgent);\n\n// src/globals.ts\nvar createStringInterpolator;\nvar to;\nvar colors = null;\nvar skipAnimation = false;\nvar willAdvance = noop;\nvar assign = (globals) => {\n  if (globals.to) to = globals.to;\n  if (globals.now) import_rafz.raf.now = globals.now;\n  if (globals.colors !== void 0) colors = globals.colors;\n  if (globals.skipAnimation != null) skipAnimation = globals.skipAnimation;\n  if (globals.createStringInterpolator)\n    createStringInterpolator = globals.createStringInterpolator;\n  if (globals.requestAnimationFrame) import_rafz.raf.use(globals.requestAnimationFrame);\n  if (globals.batchedUpdates) import_rafz.raf.batchedUpdates = globals.batchedUpdates;\n  if (globals.willAdvance) willAdvance = globals.willAdvance;\n  if (globals.frameLoop) import_rafz.raf.frameLoop = globals.frameLoop;\n};\n\n// src/FrameLoop.ts\nvar import_rafz2 = require(\"@react-spring/rafz\");\nvar startQueue = /* @__PURE__ */ new Set();\nvar currentFrame = [];\nvar prevFrame = [];\nvar priority = 0;\nvar frameLoop = {\n  get idle() {\n    return !startQueue.size && !currentFrame.length;\n  },\n  /** Advance the given animation on every frame until idle. */\n  start(animation) {\n    if (priority > animation.priority) {\n      startQueue.add(animation);\n      import_rafz2.raf.onStart(flushStartQueue);\n    } else {\n      startSafely(animation);\n      (0, import_rafz2.raf)(advance);\n    }\n  },\n  /** Advance all animations by the given time. */\n  advance,\n  /** Call this when an animation's priority changes. */\n  sort(animation) {\n    if (priority) {\n      import_rafz2.raf.onFrame(() => frameLoop.sort(animation));\n    } else {\n      const prevIndex = currentFrame.indexOf(animation);\n      if (~prevIndex) {\n        currentFrame.splice(prevIndex, 1);\n        startUnsafely(animation);\n      }\n    }\n  },\n  /**\n   * Clear all animations. For testing purposes.\n   *\n   * ☠️ Never call this from within the frameloop.\n   */\n  clear() {\n    currentFrame = [];\n    startQueue.clear();\n  }\n};\nfunction flushStartQueue() {\n  startQueue.forEach(startSafely);\n  startQueue.clear();\n  (0, import_rafz2.raf)(advance);\n}\nfunction startSafely(animation) {\n  if (!currentFrame.includes(animation)) startUnsafely(animation);\n}\nfunction startUnsafely(animation) {\n  currentFrame.splice(\n    findIndex(currentFrame, (other) => other.priority > animation.priority),\n    0,\n    animation\n  );\n}\nfunction advance(dt) {\n  const nextFrame = prevFrame;\n  for (let i = 0; i < currentFrame.length; i++) {\n    const animation = currentFrame[i];\n    priority = animation.priority;\n    if (!animation.idle) {\n      willAdvance(animation);\n      animation.advance(dt);\n      if (!animation.idle) {\n        nextFrame.push(animation);\n      }\n    }\n  }\n  priority = 0;\n  prevFrame = currentFrame;\n  prevFrame.length = 0;\n  currentFrame = nextFrame;\n  return currentFrame.length > 0;\n}\nfunction findIndex(arr, test) {\n  const index = arr.findIndex(test);\n  return index < 0 ? arr.length : index;\n}\n\n// src/clamp.ts\nvar clamp = (min, max, v) => Math.min(Math.max(v, min), max);\n\n// src/colors.ts\nvar colors2 = {\n  transparent: 0,\n  aliceblue: 4042850303,\n  antiquewhite: 4209760255,\n  aqua: 16777215,\n  aquamarine: 2147472639,\n  azure: 4043309055,\n  beige: 4126530815,\n  bisque: 4293182719,\n  black: 255,\n  blanchedalmond: 4293643775,\n  blue: 65535,\n  blueviolet: 2318131967,\n  brown: 2771004159,\n  burlywood: 3736635391,\n  burntsienna: 3934150143,\n  cadetblue: 1604231423,\n  chartreuse: 2147418367,\n  chocolate: 3530104575,\n  coral: 4286533887,\n  cornflowerblue: 1687547391,\n  cornsilk: 4294499583,\n  crimson: 3692313855,\n  cyan: 16777215,\n  darkblue: 35839,\n  darkcyan: 9145343,\n  darkgoldenrod: 3095792639,\n  darkgray: 2846468607,\n  darkgreen: 6553855,\n  darkgrey: 2846468607,\n  darkkhaki: 3182914559,\n  darkmagenta: 2332068863,\n  darkolivegreen: 1433087999,\n  darkorange: 4287365375,\n  darkorchid: 2570243327,\n  darkred: 2332033279,\n  darksalmon: 3918953215,\n  darkseagreen: 2411499519,\n  darkslateblue: 1211993087,\n  darkslategray: 793726975,\n  darkslategrey: 793726975,\n  darkturquoise: 13554175,\n  darkviolet: 2483082239,\n  deeppink: 4279538687,\n  deepskyblue: 12582911,\n  dimgray: 1768516095,\n  dimgrey: 1768516095,\n  dodgerblue: 512819199,\n  firebrick: 2988581631,\n  floralwhite: 4294635775,\n  forestgreen: 579543807,\n  fuchsia: 4278255615,\n  gainsboro: 3705462015,\n  ghostwhite: 4177068031,\n  gold: 4292280575,\n  goldenrod: 3668254975,\n  gray: 2155905279,\n  green: 8388863,\n  greenyellow: 2919182335,\n  grey: 2155905279,\n  honeydew: 4043305215,\n  hotpink: 4285117695,\n  indianred: 3445382399,\n  indigo: 1258324735,\n  ivory: 4294963455,\n  khaki: 4041641215,\n  lavender: 3873897215,\n  lavenderblush: 4293981695,\n  lawngreen: 2096890111,\n  lemonchiffon: 4294626815,\n  lightblue: 2916673279,\n  lightcoral: 4034953471,\n  lightcyan: 3774873599,\n  lightgoldenrodyellow: 4210742015,\n  lightgray: 3553874943,\n  lightgreen: 2431553791,\n  lightgrey: 3553874943,\n  lightpink: 4290167295,\n  lightsalmon: 4288707327,\n  lightseagreen: 548580095,\n  lightskyblue: 2278488831,\n  lightslategray: 2005441023,\n  lightslategrey: 2005441023,\n  lightsteelblue: 2965692159,\n  lightyellow: 4294959359,\n  lime: 16711935,\n  limegreen: 852308735,\n  linen: 4210091775,\n  magenta: 4278255615,\n  maroon: 2147483903,\n  mediumaquamarine: 1724754687,\n  mediumblue: 52735,\n  mediumorchid: 3126187007,\n  mediumpurple: 2473647103,\n  mediumseagreen: 1018393087,\n  mediumslateblue: 2070474495,\n  mediumspringgreen: 16423679,\n  mediumturquoise: 1221709055,\n  mediumvioletred: 3340076543,\n  midnightblue: 421097727,\n  mintcream: 4127193855,\n  mistyrose: 4293190143,\n  moccasin: 4293178879,\n  navajowhite: 4292783615,\n  navy: 33023,\n  oldlace: 4260751103,\n  olive: 2155872511,\n  olivedrab: 1804477439,\n  orange: 4289003775,\n  orangered: 4282712319,\n  orchid: 3664828159,\n  palegoldenrod: 4008225535,\n  palegreen: 2566625535,\n  paleturquoise: 2951671551,\n  palevioletred: 3681588223,\n  papayawhip: 4293907967,\n  peachpuff: 4292524543,\n  peru: 3448061951,\n  pink: 4290825215,\n  plum: 3718307327,\n  powderblue: 2967529215,\n  purple: 2147516671,\n  rebeccapurple: 1714657791,\n  red: 4278190335,\n  rosybrown: 3163525119,\n  royalblue: 1097458175,\n  saddlebrown: 2336560127,\n  salmon: 4202722047,\n  sandybrown: 4104413439,\n  seagreen: 780883967,\n  seashell: 4294307583,\n  sienna: 2689740287,\n  silver: 3233857791,\n  skyblue: 2278484991,\n  slateblue: 1784335871,\n  slategray: 1887473919,\n  slategrey: 1887473919,\n  snow: 4294638335,\n  springgreen: 16744447,\n  steelblue: 1182971135,\n  tan: 3535047935,\n  teal: 8421631,\n  thistle: 3636451583,\n  tomato: 4284696575,\n  turquoise: 1088475391,\n  violet: 4001558271,\n  wheat: 4125012991,\n  white: 4294967295,\n  whitesmoke: 4126537215,\n  yellow: 4294902015,\n  yellowgreen: 2597139199\n};\n\n// src/colorMatchers.ts\nvar NUMBER = \"[-+]?\\\\d*\\\\.?\\\\d+\";\nvar PERCENTAGE = NUMBER + \"%\";\nfunction call(...parts) {\n  return \"\\\\(\\\\s*(\" + parts.join(\")\\\\s*,\\\\s*(\") + \")\\\\s*\\\\)\";\n}\nvar rgb = new RegExp(\"rgb\" + call(NUMBER, NUMBER, NUMBER));\nvar rgba = new RegExp(\"rgba\" + call(NUMBER, NUMBER, NUMBER, NUMBER));\nvar hsl = new RegExp(\"hsl\" + call(NUMBER, PERCENTAGE, PERCENTAGE));\nvar hsla = new RegExp(\n  \"hsla\" + call(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER)\n);\nvar hex3 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;\nvar hex4 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;\nvar hex6 = /^#([0-9a-fA-F]{6})$/;\nvar hex8 = /^#([0-9a-fA-F]{8})$/;\n\n// src/normalizeColor.ts\nfunction normalizeColor(color) {\n  let match;\n  if (typeof color === \"number\") {\n    return color >>> 0 === color && color >= 0 && color <= 4294967295 ? color : null;\n  }\n  if (match = hex6.exec(color))\n    return parseInt(match[1] + \"ff\", 16) >>> 0;\n  if (colors && colors[color] !== void 0) {\n    return colors[color];\n  }\n  if (match = rgb.exec(color)) {\n    return (parse255(match[1]) << 24 | // r\n    parse255(match[2]) << 16 | // g\n    parse255(match[3]) << 8 | // b\n    255) >>> // a\n    0;\n  }\n  if (match = rgba.exec(color)) {\n    return (parse255(match[1]) << 24 | // r\n    parse255(match[2]) << 16 | // g\n    parse255(match[3]) << 8 | // b\n    parse1(match[4])) >>> // a\n    0;\n  }\n  if (match = hex3.exec(color)) {\n    return parseInt(\n      match[1] + match[1] + // r\n      match[2] + match[2] + // g\n      match[3] + match[3] + // b\n      \"ff\",\n      // a\n      16\n    ) >>> 0;\n  }\n  if (match = hex8.exec(color)) return parseInt(match[1], 16) >>> 0;\n  if (match = hex4.exec(color)) {\n    return parseInt(\n      match[1] + match[1] + // r\n      match[2] + match[2] + // g\n      match[3] + match[3] + // b\n      match[4] + match[4],\n      // a\n      16\n    ) >>> 0;\n  }\n  if (match = hsl.exec(color)) {\n    return (hslToRgb(\n      parse360(match[1]),\n      // h\n      parsePercentage(match[2]),\n      // s\n      parsePercentage(match[3])\n      // l\n    ) | 255) >>> // a\n    0;\n  }\n  if (match = hsla.exec(color)) {\n    return (hslToRgb(\n      parse360(match[1]),\n      // h\n      parsePercentage(match[2]),\n      // s\n      parsePercentage(match[3])\n      // l\n    ) | parse1(match[4])) >>> // a\n    0;\n  }\n  return null;\n}\nfunction hue2rgb(p, q, t) {\n  if (t < 0) t += 1;\n  if (t > 1) t -= 1;\n  if (t < 1 / 6) return p + (q - p) * 6 * t;\n  if (t < 1 / 2) return q;\n  if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n  return p;\n}\nfunction hslToRgb(h, s, l) {\n  const q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n  const p = 2 * l - q;\n  const r = hue2rgb(p, q, h + 1 / 3);\n  const g = hue2rgb(p, q, h);\n  const b = hue2rgb(p, q, h - 1 / 3);\n  return Math.round(r * 255) << 24 | Math.round(g * 255) << 16 | Math.round(b * 255) << 8;\n}\nfunction parse255(str) {\n  const int = parseInt(str, 10);\n  if (int < 0) return 0;\n  if (int > 255) return 255;\n  return int;\n}\nfunction parse360(str) {\n  const int = parseFloat(str);\n  return (int % 360 + 360) % 360 / 360;\n}\nfunction parse1(str) {\n  const num = parseFloat(str);\n  if (num < 0) return 0;\n  if (num > 1) return 255;\n  return Math.round(num * 255);\n}\nfunction parsePercentage(str) {\n  const int = parseFloat(str);\n  if (int < 0) return 0;\n  if (int > 100) return 1;\n  return int / 100;\n}\n\n// src/colorToRgba.ts\nfunction colorToRgba(input) {\n  let int32Color = normalizeColor(input);\n  if (int32Color === null) return input;\n  int32Color = int32Color || 0;\n  const r = (int32Color & 4278190080) >>> 24;\n  const g = (int32Color & 16711680) >>> 16;\n  const b = (int32Color & 65280) >>> 8;\n  const a = (int32Color & 255) / 255;\n  return `rgba(${r}, ${g}, ${b}, ${a})`;\n}\n\n// src/createInterpolator.ts\nvar createInterpolator = (range, output, extrapolate) => {\n  if (is.fun(range)) {\n    return range;\n  }\n  if (is.arr(range)) {\n    return createInterpolator({\n      range,\n      output,\n      extrapolate\n    });\n  }\n  if (is.str(range.output[0])) {\n    return createStringInterpolator(range);\n  }\n  const config = range;\n  const outputRange = config.output;\n  const inputRange = config.range || [0, 1];\n  const extrapolateLeft = config.extrapolateLeft || config.extrapolate || \"extend\";\n  const extrapolateRight = config.extrapolateRight || config.extrapolate || \"extend\";\n  const easing = config.easing || ((t) => t);\n  return (input) => {\n    const range2 = findRange(input, inputRange);\n    return interpolate(\n      input,\n      inputRange[range2],\n      inputRange[range2 + 1],\n      outputRange[range2],\n      outputRange[range2 + 1],\n      easing,\n      extrapolateLeft,\n      extrapolateRight,\n      config.map\n    );\n  };\n};\nfunction interpolate(input, inputMin, inputMax, outputMin, outputMax, easing, extrapolateLeft, extrapolateRight, map) {\n  let result = map ? map(input) : input;\n  if (result < inputMin) {\n    if (extrapolateLeft === \"identity\") return result;\n    else if (extrapolateLeft === \"clamp\") result = inputMin;\n  }\n  if (result > inputMax) {\n    if (extrapolateRight === \"identity\") return result;\n    else if (extrapolateRight === \"clamp\") result = inputMax;\n  }\n  if (outputMin === outputMax) return outputMin;\n  if (inputMin === inputMax) return input <= inputMin ? outputMin : outputMax;\n  if (inputMin === -Infinity) result = -result;\n  else if (inputMax === Infinity) result = result - inputMin;\n  else result = (result - inputMin) / (inputMax - inputMin);\n  result = easing(result);\n  if (outputMin === -Infinity) result = -result;\n  else if (outputMax === Infinity) result = result + outputMin;\n  else result = result * (outputMax - outputMin) + outputMin;\n  return result;\n}\nfunction findRange(input, inputRange) {\n  for (var i = 1; i < inputRange.length - 1; ++i)\n    if (inputRange[i] >= input) break;\n  return i - 1;\n}\n\n// src/easings.ts\nvar steps = (steps2, direction = \"end\") => (progress2) => {\n  progress2 = direction === \"end\" ? Math.min(progress2, 0.999) : Math.max(progress2, 1e-3);\n  const expanded = progress2 * steps2;\n  const rounded = direction === \"end\" ? Math.floor(expanded) : Math.ceil(expanded);\n  return clamp(0, 1, rounded / steps2);\n};\nvar c1 = 1.70158;\nvar c2 = c1 * 1.525;\nvar c3 = c1 + 1;\nvar c4 = 2 * Math.PI / 3;\nvar c5 = 2 * Math.PI / 4.5;\nvar bounceOut = (x) => {\n  const n1 = 7.5625;\n  const d1 = 2.75;\n  if (x < 1 / d1) {\n    return n1 * x * x;\n  } else if (x < 2 / d1) {\n    return n1 * (x -= 1.5 / d1) * x + 0.75;\n  } else if (x < 2.5 / d1) {\n    return n1 * (x -= 2.25 / d1) * x + 0.9375;\n  } else {\n    return n1 * (x -= 2.625 / d1) * x + 0.984375;\n  }\n};\nvar easings = {\n  linear: (x) => x,\n  easeInQuad: (x) => x * x,\n  easeOutQuad: (x) => 1 - (1 - x) * (1 - x),\n  easeInOutQuad: (x) => x < 0.5 ? 2 * x * x : 1 - Math.pow(-2 * x + 2, 2) / 2,\n  easeInCubic: (x) => x * x * x,\n  easeOutCubic: (x) => 1 - Math.pow(1 - x, 3),\n  easeInOutCubic: (x) => x < 0.5 ? 4 * x * x * x : 1 - Math.pow(-2 * x + 2, 3) / 2,\n  easeInQuart: (x) => x * x * x * x,\n  easeOutQuart: (x) => 1 - Math.pow(1 - x, 4),\n  easeInOutQuart: (x) => x < 0.5 ? 8 * x * x * x * x : 1 - Math.pow(-2 * x + 2, 4) / 2,\n  easeInQuint: (x) => x * x * x * x * x,\n  easeOutQuint: (x) => 1 - Math.pow(1 - x, 5),\n  easeInOutQuint: (x) => x < 0.5 ? 16 * x * x * x * x * x : 1 - Math.pow(-2 * x + 2, 5) / 2,\n  easeInSine: (x) => 1 - Math.cos(x * Math.PI / 2),\n  easeOutSine: (x) => Math.sin(x * Math.PI / 2),\n  easeInOutSine: (x) => -(Math.cos(Math.PI * x) - 1) / 2,\n  easeInExpo: (x) => x === 0 ? 0 : Math.pow(2, 10 * x - 10),\n  easeOutExpo: (x) => x === 1 ? 1 : 1 - Math.pow(2, -10 * x),\n  easeInOutExpo: (x) => x === 0 ? 0 : x === 1 ? 1 : x < 0.5 ? Math.pow(2, 20 * x - 10) / 2 : (2 - Math.pow(2, -20 * x + 10)) / 2,\n  easeInCirc: (x) => 1 - Math.sqrt(1 - Math.pow(x, 2)),\n  easeOutCirc: (x) => Math.sqrt(1 - Math.pow(x - 1, 2)),\n  easeInOutCirc: (x) => x < 0.5 ? (1 - Math.sqrt(1 - Math.pow(2 * x, 2))) / 2 : (Math.sqrt(1 - Math.pow(-2 * x + 2, 2)) + 1) / 2,\n  easeInBack: (x) => c3 * x * x * x - c1 * x * x,\n  easeOutBack: (x) => 1 + c3 * Math.pow(x - 1, 3) + c1 * Math.pow(x - 1, 2),\n  easeInOutBack: (x) => x < 0.5 ? Math.pow(2 * x, 2) * ((c2 + 1) * 2 * x - c2) / 2 : (Math.pow(2 * x - 2, 2) * ((c2 + 1) * (x * 2 - 2) + c2) + 2) / 2,\n  easeInElastic: (x) => x === 0 ? 0 : x === 1 ? 1 : -Math.pow(2, 10 * x - 10) * Math.sin((x * 10 - 10.75) * c4),\n  easeOutElastic: (x) => x === 0 ? 0 : x === 1 ? 1 : Math.pow(2, -10 * x) * Math.sin((x * 10 - 0.75) * c4) + 1,\n  easeInOutElastic: (x) => x === 0 ? 0 : x === 1 ? 1 : x < 0.5 ? -(Math.pow(2, 20 * x - 10) * Math.sin((20 * x - 11.125) * c5)) / 2 : Math.pow(2, -20 * x + 10) * Math.sin((20 * x - 11.125) * c5) / 2 + 1,\n  easeInBounce: (x) => 1 - bounceOut(1 - x),\n  easeOutBounce: bounceOut,\n  easeInOutBounce: (x) => x < 0.5 ? (1 - bounceOut(1 - 2 * x)) / 2 : (1 + bounceOut(2 * x - 1)) / 2,\n  steps\n};\n\n// src/fluids.ts\nvar $get = Symbol.for(\"FluidValue.get\");\nvar $observers = Symbol.for(\"FluidValue.observers\");\nvar hasFluidValue = (arg) => Boolean(arg && arg[$get]);\nvar getFluidValue = (arg) => arg && arg[$get] ? arg[$get]() : arg;\nvar getFluidObservers = (target) => target[$observers] || null;\nfunction callFluidObserver(observer2, event) {\n  if (observer2.eventObserved) {\n    observer2.eventObserved(event);\n  } else {\n    observer2(event);\n  }\n}\nfunction callFluidObservers(target, event) {\n  const observers = target[$observers];\n  if (observers) {\n    observers.forEach((observer2) => {\n      callFluidObserver(observer2, event);\n    });\n  }\n}\n$get, $observers;\nvar FluidValue = class {\n  constructor(get) {\n    if (!get && !(get = this.get)) {\n      throw Error(\"Unknown getter\");\n    }\n    setFluidGetter(this, get);\n  }\n};\nvar setFluidGetter = (target, get) => setHidden(target, $get, get);\nfunction addFluidObserver(target, observer2) {\n  if (target[$get]) {\n    let observers = target[$observers];\n    if (!observers) {\n      setHidden(target, $observers, observers = /* @__PURE__ */ new Set());\n    }\n    if (!observers.has(observer2)) {\n      observers.add(observer2);\n      if (target.observerAdded) {\n        target.observerAdded(observers.size, observer2);\n      }\n    }\n  }\n  return observer2;\n}\nfunction removeFluidObserver(target, observer2) {\n  const observers = target[$observers];\n  if (observers && observers.has(observer2)) {\n    const count = observers.size - 1;\n    if (count) {\n      observers.delete(observer2);\n    } else {\n      target[$observers] = null;\n    }\n    if (target.observerRemoved) {\n      target.observerRemoved(count, observer2);\n    }\n  }\n}\nvar setHidden = (target, key, value) => Object.defineProperty(target, key, {\n  value,\n  writable: true,\n  configurable: true\n});\n\n// src/regexs.ts\nvar numberRegex = /[+\\-]?(?:0|[1-9]\\d*)(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?/g;\nvar colorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d\\.]+%?\\))/gi;\nvar unitRegex = new RegExp(`(${numberRegex.source})(%|[a-z]+)`, \"i\");\nvar rgbaRegex = /rgba\\(([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+)\\)/gi;\nvar cssVariableRegex = /var\\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\\)/;\n\n// src/variableToRgba.ts\nvar variableToRgba = (input) => {\n  const [token, fallback] = parseCSSVariable(input);\n  if (!token || isSSR()) {\n    return input;\n  }\n  const value = window.getComputedStyle(document.documentElement).getPropertyValue(token);\n  if (value) {\n    return value.trim();\n  } else if (fallback && fallback.startsWith(\"--\")) {\n    const value2 = window.getComputedStyle(document.documentElement).getPropertyValue(fallback);\n    if (value2) {\n      return value2;\n    } else {\n      return input;\n    }\n  } else if (fallback && cssVariableRegex.test(fallback)) {\n    return variableToRgba(fallback);\n  } else if (fallback) {\n    return fallback;\n  }\n  return input;\n};\nvar parseCSSVariable = (current) => {\n  const match = cssVariableRegex.exec(current);\n  if (!match) return [,];\n  const [, token, fallback] = match;\n  return [token, fallback];\n};\n\n// src/stringInterpolation.ts\nvar namedColorRegex;\nvar rgbaRound = (_, p1, p2, p3, p4) => `rgba(${Math.round(p1)}, ${Math.round(p2)}, ${Math.round(p3)}, ${p4})`;\nvar createStringInterpolator2 = (config) => {\n  if (!namedColorRegex)\n    namedColorRegex = colors ? (\n      // match color names, ignore partial matches\n      new RegExp(`(${Object.keys(colors).join(\"|\")})(?!\\\\w)`, \"g\")\n    ) : (\n      // never match\n      /^\\b$/\n    );\n  const output = config.output.map((value) => {\n    return getFluidValue(value).replace(cssVariableRegex, variableToRgba).replace(colorRegex, colorToRgba).replace(namedColorRegex, colorToRgba);\n  });\n  const keyframes = output.map((value) => value.match(numberRegex).map(Number));\n  const outputRanges = keyframes[0].map(\n    (_, i) => keyframes.map((values) => {\n      if (!(i in values)) {\n        throw Error('The arity of each \"output\" value must be equal');\n      }\n      return values[i];\n    })\n  );\n  const interpolators = outputRanges.map(\n    (output2) => createInterpolator({ ...config, output: output2 })\n  );\n  return (input) => {\n    const missingUnit = !unitRegex.test(output[0]) && output.find((value) => unitRegex.test(value))?.replace(numberRegex, \"\");\n    let i = 0;\n    return output[0].replace(\n      numberRegex,\n      () => `${interpolators[i++](input)}${missingUnit || \"\"}`\n    ).replace(rgbaRegex, rgbaRound);\n  };\n};\n\n// src/deprecations.ts\nvar prefix = \"react-spring: \";\nvar once = (fn) => {\n  const func = fn;\n  let called = false;\n  if (typeof func != \"function\") {\n    throw new TypeError(`${prefix}once requires a function parameter`);\n  }\n  return (...args) => {\n    if (!called) {\n      func(...args);\n      called = true;\n    }\n  };\n};\nvar warnInterpolate = once(console.warn);\nfunction deprecateInterpolate() {\n  warnInterpolate(\n    `${prefix}The \"interpolate\" function is deprecated in v9 (use \"to\" instead)`\n  );\n}\nvar warnDirectCall = once(console.warn);\nfunction deprecateDirectCall() {\n  warnDirectCall(\n    `${prefix}Directly calling start instead of using the api object is deprecated in v9 (use \".start\" instead), this will be removed in later 0.X.0 versions`\n  );\n}\n\n// src/isAnimatedString.ts\nfunction isAnimatedString(value) {\n  return is.str(value) && (value[0] == \"#\" || /\\d/.test(value) || // Do not identify a CSS variable as an AnimatedString if its SSR\n  !isSSR() && cssVariableRegex.test(value) || value in (colors || {}));\n}\n\n// src/dom-events/scroll/index.ts\nvar import_rafz3 = require(\"@react-spring/rafz\");\n\n// src/dom-events/resize/resizeElement.ts\nvar observer;\nvar resizeHandlers = /* @__PURE__ */ new WeakMap();\nvar handleObservation = (entries) => entries.forEach(({ target, contentRect }) => {\n  return resizeHandlers.get(target)?.forEach((handler) => handler(contentRect));\n});\nfunction resizeElement(handler, target) {\n  if (!observer) {\n    if (typeof ResizeObserver !== \"undefined\") {\n      observer = new ResizeObserver(handleObservation);\n    }\n  }\n  let elementHandlers = resizeHandlers.get(target);\n  if (!elementHandlers) {\n    elementHandlers = /* @__PURE__ */ new Set();\n    resizeHandlers.set(target, elementHandlers);\n  }\n  elementHandlers.add(handler);\n  if (observer) {\n    observer.observe(target);\n  }\n  return () => {\n    const elementHandlers2 = resizeHandlers.get(target);\n    if (!elementHandlers2) return;\n    elementHandlers2.delete(handler);\n    if (!elementHandlers2.size && observer) {\n      observer.unobserve(target);\n    }\n  };\n}\n\n// src/dom-events/resize/resizeWindow.ts\nvar listeners = /* @__PURE__ */ new Set();\nvar cleanupWindowResizeHandler;\nvar createResizeHandler = () => {\n  const handleResize = () => {\n    listeners.forEach(\n      (callback) => callback({\n        width: window.innerWidth,\n        height: window.innerHeight\n      })\n    );\n  };\n  window.addEventListener(\"resize\", handleResize);\n  return () => {\n    window.removeEventListener(\"resize\", handleResize);\n  };\n};\nvar resizeWindow = (callback) => {\n  listeners.add(callback);\n  if (!cleanupWindowResizeHandler) {\n    cleanupWindowResizeHandler = createResizeHandler();\n  }\n  return () => {\n    listeners.delete(callback);\n    if (!listeners.size && cleanupWindowResizeHandler) {\n      cleanupWindowResizeHandler();\n      cleanupWindowResizeHandler = void 0;\n    }\n  };\n};\n\n// src/dom-events/resize/index.ts\nvar onResize = (callback, { container = document.documentElement } = {}) => {\n  if (container === document.documentElement) {\n    return resizeWindow(callback);\n  } else {\n    return resizeElement(callback, container);\n  }\n};\n\n// src/progress.ts\nvar progress = (min, max, value) => max - min === 0 ? 1 : (value - min) / (max - min);\n\n// src/dom-events/scroll/ScrollHandler.ts\nvar SCROLL_KEYS = {\n  x: {\n    length: \"Width\",\n    position: \"Left\"\n  },\n  y: {\n    length: \"Height\",\n    position: \"Top\"\n  }\n};\nvar ScrollHandler = class {\n  constructor(callback, container) {\n    this.createAxis = () => ({\n      current: 0,\n      progress: 0,\n      scrollLength: 0\n    });\n    this.updateAxis = (axisName) => {\n      const axis = this.info[axisName];\n      const { length, position } = SCROLL_KEYS[axisName];\n      axis.current = this.container[`scroll${position}`];\n      axis.scrollLength = this.container[`scroll${length}`] - this.container[`client${length}`];\n      axis.progress = progress(0, axis.scrollLength, axis.current);\n    };\n    this.update = () => {\n      this.updateAxis(\"x\");\n      this.updateAxis(\"y\");\n    };\n    this.sendEvent = () => {\n      this.callback(this.info);\n    };\n    this.advance = () => {\n      this.update();\n      this.sendEvent();\n    };\n    this.callback = callback;\n    this.container = container;\n    this.info = {\n      time: 0,\n      x: this.createAxis(),\n      y: this.createAxis()\n    };\n  }\n};\n\n// src/dom-events/scroll/index.ts\nvar scrollListeners = /* @__PURE__ */ new WeakMap();\nvar resizeListeners = /* @__PURE__ */ new WeakMap();\nvar onScrollHandlers = /* @__PURE__ */ new WeakMap();\nvar getTarget = (container) => container === document.documentElement ? window : container;\nvar onScroll = (callback, { container = document.documentElement } = {}) => {\n  let containerHandlers = onScrollHandlers.get(container);\n  if (!containerHandlers) {\n    containerHandlers = /* @__PURE__ */ new Set();\n    onScrollHandlers.set(container, containerHandlers);\n  }\n  const containerHandler = new ScrollHandler(callback, container);\n  containerHandlers.add(containerHandler);\n  if (!scrollListeners.has(container)) {\n    const listener = () => {\n      containerHandlers?.forEach((handler) => handler.advance());\n      return true;\n    };\n    scrollListeners.set(container, listener);\n    const target = getTarget(container);\n    window.addEventListener(\"resize\", listener, { passive: true });\n    if (container !== document.documentElement) {\n      resizeListeners.set(container, onResize(listener, { container }));\n    }\n    target.addEventListener(\"scroll\", listener, { passive: true });\n  }\n  const animateScroll = scrollListeners.get(container);\n  (0, import_rafz3.raf)(animateScroll);\n  return () => {\n    import_rafz3.raf.cancel(animateScroll);\n    const containerHandlers2 = onScrollHandlers.get(container);\n    if (!containerHandlers2) return;\n    containerHandlers2.delete(containerHandler);\n    if (containerHandlers2.size) return;\n    const listener = scrollListeners.get(container);\n    scrollListeners.delete(container);\n    if (listener) {\n      getTarget(container).removeEventListener(\"scroll\", listener);\n      window.removeEventListener(\"resize\", listener);\n      resizeListeners.get(container)?.();\n    }\n  };\n};\n\n// src/hooks/useConstant.ts\nvar import_react = require(\"react\");\nfunction useConstant(init) {\n  const ref = (0, import_react.useRef)(null);\n  if (ref.current === null) {\n    ref.current = init();\n  }\n  return ref.current;\n}\n\n// src/hooks/useForceUpdate.ts\nvar import_react4 = require(\"react\");\n\n// src/hooks/useIsMounted.ts\nvar import_react3 = require(\"react\");\n\n// src/hooks/useIsomorphicLayoutEffect.ts\nvar import_react2 = require(\"react\");\nvar useIsomorphicLayoutEffect = isSSR() ? import_react2.useEffect : import_react2.useLayoutEffect;\n\n// src/hooks/useIsMounted.ts\nvar useIsMounted = () => {\n  const isMounted = (0, import_react3.useRef)(false);\n  useIsomorphicLayoutEffect(() => {\n    isMounted.current = true;\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n  return isMounted;\n};\n\n// src/hooks/useForceUpdate.ts\nfunction useForceUpdate() {\n  const update = (0, import_react4.useState)()[1];\n  const isMounted = useIsMounted();\n  return () => {\n    if (isMounted.current) {\n      update(Math.random());\n    }\n  };\n}\n\n// src/hooks/useMemoOne.ts\nvar import_react5 = require(\"react\");\nfunction useMemoOne(getResult, inputs) {\n  const [initial] = (0, import_react5.useState)(\n    () => ({\n      inputs,\n      result: getResult()\n    })\n  );\n  const committed = (0, import_react5.useRef)(void 0);\n  const prevCache = committed.current;\n  let cache = prevCache;\n  if (cache) {\n    const useCache = Boolean(\n      inputs && cache.inputs && areInputsEqual(inputs, cache.inputs)\n    );\n    if (!useCache) {\n      cache = {\n        inputs,\n        result: getResult()\n      };\n    }\n  } else {\n    cache = initial;\n  }\n  (0, import_react5.useEffect)(() => {\n    committed.current = cache;\n    if (prevCache == initial) {\n      initial.inputs = initial.result = void 0;\n    }\n  }, [cache]);\n  return cache.result;\n}\nfunction areInputsEqual(next, prev) {\n  if (next.length !== prev.length) {\n    return false;\n  }\n  for (let i = 0; i < next.length; i++) {\n    if (next[i] !== prev[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// src/hooks/useOnce.ts\nvar import_react6 = require(\"react\");\nvar useOnce = (effect) => (0, import_react6.useEffect)(effect, emptyDeps);\nvar emptyDeps = [];\n\n// src/hooks/usePrev.ts\nvar import_react7 = require(\"react\");\nfunction usePrev(value) {\n  const prevRef = (0, import_react7.useRef)(void 0);\n  (0, import_react7.useEffect)(() => {\n    prevRef.current = value;\n  });\n  return prevRef.current;\n}\n\n// src/hooks/useReducedMotion.ts\nvar import_react8 = require(\"react\");\nvar useReducedMotion = () => {\n  const [reducedMotion, setReducedMotion] = (0, import_react8.useState)(null);\n  useIsomorphicLayoutEffect(() => {\n    const mql = window.matchMedia(\"(prefers-reduced-motion)\");\n    const handleMediaChange = (e) => {\n      setReducedMotion(e.matches);\n      assign({\n        skipAnimation: e.matches\n      });\n    };\n    handleMediaChange(mql);\n    if (mql.addEventListener) {\n      mql.addEventListener(\"change\", handleMediaChange);\n    } else {\n      mql.addListener(handleMediaChange);\n    }\n    return () => {\n      if (mql.removeEventListener) {\n        mql.removeEventListener(\"change\", handleMediaChange);\n      } else {\n        mql.removeListener(handleMediaChange);\n      }\n    };\n  }, []);\n  return reducedMotion;\n};\n\n// src/index.ts\nvar import_rafz4 = require(\"@react-spring/rafz\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  FluidValue,\n  Globals,\n  addFluidObserver,\n  callFluidObserver,\n  callFluidObservers,\n  clamp,\n  colorToRgba,\n  colors,\n  createInterpolator,\n  createStringInterpolator,\n  defineHidden,\n  deprecateDirectCall,\n  deprecateInterpolate,\n  each,\n  eachProp,\n  easings,\n  flush,\n  flushCalls,\n  frameLoop,\n  getFluidObservers,\n  getFluidValue,\n  hasFluidValue,\n  hex3,\n  hex4,\n  hex6,\n  hex8,\n  hsl,\n  hsla,\n  is,\n  isAnimatedString,\n  isEqual,\n  isSSR,\n  noop,\n  onResize,\n  onScroll,\n  once,\n  prefix,\n  raf,\n  removeFluidObserver,\n  rgb,\n  rgba,\n  setFluidGetter,\n  toArray,\n  useConstant,\n  useForceUpdate,\n  useIsomorphicLayoutEffect,\n  useMemoOne,\n  useOnce,\n  usePrev,\n  useReducedMotion\n});\n"], "names": [], "mappings": "AAAA;AACA,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,mBAAmB,OAAO,wBAAwB;AACtD,IAAI,oBAAoB,OAAO,mBAAmB;AAClD,IAAI,eAAe,OAAO,SAAS,CAAC,cAAc;AAClD,IAAI,WAAW,CAAC,QAAQ;IACtB,IAAK,IAAI,QAAQ,IACf,UAAU,QAAQ,MAAM;QAAE,KAAK,GAAG,CAAC,KAAK;QAAE,YAAY;IAAK;AAC/D;AACA,IAAI,cAAc,CAAC,KAAK,MAAM,QAAQ;IACpC,IAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;QAClE,KAAK,IAAI,OAAO,kBAAkB,MAChC,IAAI,CAAC,aAAa,IAAI,CAAC,KAAK,QAAQ,QAAQ,QAC1C,UAAU,KAAK,KAAK;YAAE,KAAK,IAAM,IAAI,CAAC,IAAI;YAAE,YAAY,CAAC,CAAC,OAAO,iBAAiB,MAAM,IAAI,KAAK,KAAK,UAAU;QAAC;IACvH;IACA,OAAO;AACT;AACA,IAAI,eAAe,CAAC,MAAQ,YAAY,UAAU,CAAC,GAAG,cAAc;QAAE,OAAO;IAAK,IAAI;AAEtF,eAAe;AACf,IAAI,cAAc,CAAC;AACnB,SAAS,aAAa;IACpB,YAAY,IAAM;IAClB,SAAS,IAAM;IACf,kBAAkB,IAAM;IACxB,mBAAmB,IAAM;IACzB,oBAAoB,IAAM;IAC1B,OAAO,IAAM;IACb,aAAa,IAAM;IACnB,QAAQ,IAAM;IACd,oBAAoB,IAAM;IAC1B,0BAA0B,IAAM;IAChC,cAAc,IAAM;IACpB,qBAAqB,IAAM;IAC3B,sBAAsB,IAAM;IAC5B,MAAM,IAAM;IACZ,UAAU,IAAM;IAChB,SAAS,IAAM;IACf,OAAO,IAAM;IACb,YAAY,IAAM;IAClB,WAAW,IAAM;IACjB,mBAAmB,IAAM;IACzB,eAAe,IAAM;IACrB,eAAe,IAAM;IACrB,MAAM,IAAM;IACZ,MAAM,IAAM;IACZ,MAAM,IAAM;IACZ,MAAM,IAAM;IACZ,KAAK,IAAM;IACX,MAAM,IAAM;IACZ,IAAI,IAAM;IACV,kBAAkB,IAAM;IACxB,SAAS,IAAM;IACf,OAAO,IAAM;IACb,MAAM,IAAM;IACZ,UAAU,IAAM;IAChB,UAAU,IAAM;IAChB,MAAM,IAAM;IACZ,QAAQ,IAAM;IACd,KAAK,IAAM,aAAa,GAAG;IAC3B,qBAAqB,IAAM;IAC3B,KAAK,IAAM;IACX,MAAM,IAAM;IACZ,gBAAgB,IAAM;IACtB,SAAS,IAAM;IACf,aAAa,IAAM;IACnB,gBAAgB,IAAM;IACtB,2BAA2B,IAAM;IACjC,YAAY,IAAM;IAClB,SAAS,IAAM;IACf,SAAS,IAAM;IACf,kBAAkB,IAAM;AAC1B;AACA,OAAO,OAAO,GAAG,aAAa;AAE9B,iBAAiB;AACjB,IAAI,kBAAkB,CAAC;AACvB,SAAS,iBAAiB;IACxB,QAAQ,IAAM;IACd,QAAQ,IAAM;IACd,0BAA0B,IAAM;IAChC,eAAe,IAAM;IACrB,IAAI,IAAM;IACV,aAAa,IAAM;AACrB;AACA,IAAI;AAEJ,iBAAiB;AACjB,SAAS,QACT;AACA,IAAI,eAAe,CAAC,KAAK,KAAK,QAAU,OAAO,cAAc,CAAC,KAAK,KAAK;QAAE;QAAO,UAAU;QAAM,cAAc;IAAK;AACpH,IAAI,KAAK;IACP,KAAK,MAAM,OAAO;IAClB,KAAK,CAAC,IAAM,CAAC,CAAC,KAAK,EAAE,WAAW,CAAC,IAAI,KAAK;IAC1C,KAAK,CAAC,IAAM,OAAO,MAAM;IACzB,KAAK,CAAC,IAAM,OAAO,MAAM;IACzB,KAAK,CAAC,IAAM,OAAO,MAAM;IACzB,KAAK,CAAC,IAAM,MAAM,KAAK;AACzB;AACA,SAAS,QAAQ,CAAC,EAAE,CAAC;IACnB,IAAI,GAAG,GAAG,CAAC,IAAI;QACb,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE,OAAO;QAChD,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK;YACjC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,OAAO;QAC5B;QACA,OAAO;IACT;IACA,OAAO,MAAM;AACf;AACA,IAAI,OAAO,CAAC,KAAK,KAAO,IAAI,OAAO,CAAC;AACpC,SAAS,SAAS,GAAG,EAAE,EAAE,EAAE,GAAG;IAC5B,IAAI,GAAG,GAAG,CAAC,MAAM;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACnC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG;QAC7B;QACA;IACF;IACA,IAAK,MAAM,OAAO,IAAK;QACrB,IAAI,IAAI,cAAc,CAAC,MAAM;YAC3B,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE;QACzB;IACF;AACF;AACA,IAAI,UAAU,CAAC,IAAM,GAAG,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG,CAAC,KAAK,IAAI;QAAC;KAAE;AACzD,SAAS,MAAM,KAAK,EAAE,QAAQ;IAC5B,IAAI,MAAM,IAAI,EAAE;QACd,MAAM,QAAQ,MAAM,IAAI,CAAC;QACzB,MAAM,KAAK;QACX,KAAK,OAAO;IACd;AACF;AACA,IAAI,aAAa,CAAC,OAAO,GAAG,OAAS,MAAM,OAAO,CAAC,KAAO,MAAM;AAChE,IAAI,QAAQ,IAAM,OAAO,WAAW,eAAe,CAAC,OAAO,SAAS,IAAI,8BAA8B,IAAI,CAAC,OAAO,SAAS,CAAC,SAAS;AAErI,iBAAiB;AACjB,IAAI;AACJ,IAAI;AACJ,IAAI,SAAS;AACb,IAAI,gBAAgB;AACpB,IAAI,cAAc;AAClB,IAAI,SAAS,CAAC;IACZ,IAAI,QAAQ,EAAE,EAAE,KAAK,QAAQ,EAAE;IAC/B,IAAI,QAAQ,GAAG,EAAE,YAAY,GAAG,CAAC,GAAG,GAAG,QAAQ,GAAG;IAClD,IAAI,QAAQ,MAAM,KAAK,KAAK,GAAG,SAAS,QAAQ,MAAM;IACtD,IAAI,QAAQ,aAAa,IAAI,MAAM,gBAAgB,QAAQ,aAAa;IACxE,IAAI,QAAQ,wBAAwB,EAClC,2BAA2B,QAAQ,wBAAwB;IAC7D,IAAI,QAAQ,qBAAqB,EAAE,YAAY,GAAG,CAAC,GAAG,CAAC,QAAQ,qBAAqB;IACpF,IAAI,QAAQ,cAAc,EAAE,YAAY,GAAG,CAAC,cAAc,GAAG,QAAQ,cAAc;IACnF,IAAI,QAAQ,WAAW,EAAE,cAAc,QAAQ,WAAW;IAC1D,IAAI,QAAQ,SAAS,EAAE,YAAY,GAAG,CAAC,SAAS,GAAG,QAAQ,SAAS;AACtE;AAEA,mBAAmB;AACnB,IAAI;AACJ,IAAI,aAAa,aAAa,GAAG,IAAI;AACrC,IAAI,eAAe,EAAE;AACrB,IAAI,YAAY,EAAE;AAClB,IAAI,WAAW;AACf,IAAI,YAAY;IACd,IAAI,QAAO;QACT,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,MAAM;IACjD;IACA,2DAA2D,GAC3D,OAAM,SAAS;QACb,IAAI,WAAW,UAAU,QAAQ,EAAE;YACjC,WAAW,GAAG,CAAC;YACf,aAAa,GAAG,CAAC,OAAO,CAAC;QAC3B,OAAO;YACL,YAAY;YACZ,CAAC,GAAG,aAAa,GAAG,EAAE;QACxB;IACF;IACA,8CAA8C,GAC9C;IACA,oDAAoD,GACpD,MAAK,SAAS;QACZ,IAAI,UAAU;YACZ,aAAa,GAAG,CAAC,OAAO,CAAC,IAAM,UAAU,IAAI,CAAC;QAChD,OAAO;YACL,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,IAAI,CAAC,WAAW;gBACd,aAAa,MAAM,CAAC,WAAW;gBAC/B,cAAc;YAChB;QACF;IACF;IACA;;;;GAIC,GACD;QACE,eAAe,EAAE;QACjB,WAAW,KAAK;IAClB;AACF;AACA,SAAS;IACP,WAAW,OAAO,CAAC;IACnB,WAAW,KAAK;IAChB,CAAC,GAAG,aAAa,GAAG,EAAE;AACxB;AACA,SAAS,YAAY,SAAS;IAC5B,IAAI,CAAC,aAAa,QAAQ,CAAC,YAAY,cAAc;AACvD;AACA,SAAS,cAAc,SAAS;IAC9B,aAAa,MAAM,CACjB,UAAU,cAAc,CAAC,QAAU,MAAM,QAAQ,GAAG,UAAU,QAAQ,GACtE,GACA;AAEJ;AACA,SAAS,QAAQ,EAAE;IACjB,MAAM,YAAY;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,MAAM,YAAY,YAAY,CAAC,EAAE;QACjC,WAAW,UAAU,QAAQ;QAC7B,IAAI,CAAC,UAAU,IAAI,EAAE;YACnB,YAAY;YACZ,UAAU,OAAO,CAAC;YAClB,IAAI,CAAC,UAAU,IAAI,EAAE;gBACnB,UAAU,IAAI,CAAC;YACjB;QACF;IACF;IACA,WAAW;IACX,YAAY;IACZ,UAAU,MAAM,GAAG;IACnB,eAAe;IACf,OAAO,aAAa,MAAM,GAAG;AAC/B;AACA,SAAS,UAAU,GAAG,EAAE,IAAI;IAC1B,MAAM,QAAQ,IAAI,SAAS,CAAC;IAC5B,OAAO,QAAQ,IAAI,IAAI,MAAM,GAAG;AAClC;AAEA,eAAe;AACf,IAAI,QAAQ,CAAC,KAAK,KAAK,IAAM,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,MAAM;AAExD,gBAAgB;AAChB,IAAI,UAAU;IACZ,aAAa;IACb,WAAW;IACX,cAAc;IACd,MAAM;IACN,YAAY;IACZ,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,gBAAgB;IAChB,MAAM;IACN,YAAY;IACZ,OAAO;IACP,WAAW;IACX,aAAa;IACb,WAAW;IACX,YAAY;IACZ,WAAW;IACX,OAAO;IACP,gBAAgB;IAChB,UAAU;IACV,SAAS;IACT,MAAM;IACN,UAAU;IACV,UAAU;IACV,eAAe;IACf,UAAU;IACV,WAAW;IACX,UAAU;IACV,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,YAAY;IACZ,YAAY;IACZ,SAAS;IACT,YAAY;IACZ,cAAc;IACd,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,YAAY;IACZ,UAAU;IACV,aAAa;IACb,SAAS;IACT,SAAS;IACT,YAAY;IACZ,WAAW;IACX,aAAa;IACb,aAAa;IACb,SAAS;IACT,WAAW;IACX,YAAY;IACZ,MAAM;IACN,WAAW;IACX,MAAM;IACN,OAAO;IACP,aAAa;IACb,MAAM;IACN,UAAU;IACV,SAAS;IACT,WAAW;IACX,QAAQ;IACR,OAAO;IACP,OAAO;IACP,UAAU;IACV,eAAe;IACf,WAAW;IACX,cAAc;IACd,WAAW;IACX,YAAY;IACZ,WAAW;IACX,sBAAsB;IACtB,WAAW;IACX,YAAY;IACZ,WAAW;IACX,WAAW;IACX,aAAa;IACb,eAAe;IACf,cAAc;IACd,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,aAAa;IACb,MAAM;IACN,WAAW;IACX,OAAO;IACP,SAAS;IACT,QAAQ;IACR,kBAAkB;IAClB,YAAY;IACZ,cAAc;IACd,cAAc;IACd,gBAAgB;IAChB,iBAAiB;IACjB,mBAAmB;IACnB,iBAAiB;IACjB,iBAAiB;IACjB,cAAc;IACd,WAAW;IACX,WAAW;IACX,UAAU;IACV,aAAa;IACb,MAAM;IACN,SAAS;IACT,OAAO;IACP,WAAW;IACX,QAAQ;IACR,WAAW;IACX,QAAQ;IACR,eAAe;IACf,WAAW;IACX,eAAe;IACf,eAAe;IACf,YAAY;IACZ,WAAW;IACX,MAAM;IACN,MAAM;IACN,MAAM;IACN,YAAY;IACZ,QAAQ;IACR,eAAe;IACf,KAAK;IACL,WAAW;IACX,WAAW;IACX,aAAa;IACb,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,WAAW;IACX,WAAW;IACX,WAAW;IACX,MAAM;IACN,aAAa;IACb,WAAW;IACX,KAAK;IACL,MAAM;IACN,SAAS;IACT,QAAQ;IACR,WAAW;IACX,QAAQ;IACR,OAAO;IACP,OAAO;IACP,YAAY;IACZ,QAAQ;IACR,aAAa;AACf;AAEA,uBAAuB;AACvB,IAAI,SAAS;AACb,IAAI,aAAa,SAAS;AAC1B,SAAS,KAAK,GAAG,KAAK;IACpB,OAAO,aAAa,MAAM,IAAI,CAAC,iBAAiB;AAClD;AACA,IAAI,MAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,QAAQ;AAClD,IAAI,OAAO,IAAI,OAAO,SAAS,KAAK,QAAQ,QAAQ,QAAQ;AAC5D,IAAI,MAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,YAAY;AACtD,IAAI,OAAO,IAAI,OACb,SAAS,KAAK,QAAQ,YAAY,YAAY;AAEhD,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,OAAO;AAEX,wBAAwB;AACxB,SAAS,eAAe,KAAK;IAC3B,IAAI;IACJ,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO,UAAU,MAAM,SAAS,SAAS,KAAK,SAAS,aAAa,QAAQ;IAC9E;IACA,IAAI,QAAQ,KAAK,IAAI,CAAC,QACpB,OAAO,SAAS,KAAK,CAAC,EAAE,GAAG,MAAM,QAAQ;IAC3C,IAAI,UAAU,MAAM,CAAC,MAAM,KAAK,KAAK,GAAG;QACtC,OAAO,MAAM,CAAC,MAAM;IACtB;IACA,IAAI,QAAQ,IAAI,IAAI,CAAC,QAAQ;QAC3B,OAAO,CAAC,SAAS,KAAK,CAAC,EAAE,KAAK,KAAK,IAAI;QACvC,SAAS,KAAK,CAAC,EAAE,KAAK,KAAK,IAAI;QAC/B,SAAS,KAAK,CAAC,EAAE,KAAK,IAAI,IAAI;QAC9B,GAAG,MAAM,IAAI;QACb;IACF;IACA,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ;QAC5B,OAAO,CAAC,SAAS,KAAK,CAAC,EAAE,KAAK,KAAK,IAAI;QACvC,SAAS,KAAK,CAAC,EAAE,KAAK,KAAK,IAAI;QAC/B,SAAS,KAAK,CAAC,EAAE,KAAK,IAAI,IAAI;QAC9B,OAAO,KAAK,CAAC,EAAE,CAAC,MAAM,IAAI;QAC1B;IACF;IACA,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ;QAC5B,OAAO,SACL,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,IAAI;QAC1B,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,IAAI;QAC1B,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,IAAI;QAC1B,MACA,IAAI;QACJ,QACI;IACR;IACA,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,OAAO,SAAS,KAAK,CAAC,EAAE,EAAE,QAAQ;IAChE,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ;QAC5B,OAAO,SACL,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,IAAI;QAC1B,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,IAAI;QAC1B,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,IAAI;QAC1B,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,EACnB,IAAI;QACJ,QACI;IACR;IACA,IAAI,QAAQ,IAAI,IAAI,CAAC,QAAQ;QAC3B,OAAO,CAAC,SACN,SAAS,KAAK,CAAC,EAAE,GACjB,IAAI;QACJ,gBAAgB,KAAK,CAAC,EAAE,GACxB,IAAI;QACJ,gBAAgB,KAAK,CAAC,EAAE,KAEtB,GAAG,MAAM,IAAI;QACjB;IACF;IACA,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ;QAC5B,OAAO,CAAC,SACN,SAAS,KAAK,CAAC,EAAE,GACjB,IAAI;QACJ,gBAAgB,KAAK,CAAC,EAAE,GACxB,IAAI;QACJ,gBAAgB,KAAK,CAAC,EAAE,KAEtB,OAAO,KAAK,CAAC,EAAE,CAAC,MAAM,IAAI;QAC9B;IACF;IACA,OAAO;AACT;AACA,SAAS,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;IACtB,IAAI,IAAI,GAAG,KAAK;IAChB,IAAI,IAAI,GAAG,KAAK;IAChB,IAAI,IAAI,IAAI,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACxC,IAAI,IAAI,IAAI,GAAG,OAAO;IACtB,IAAI,IAAI,IAAI,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI;IAClD,OAAO;AACT;AACA,SAAS,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC;IACvB,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI;IAC9C,MAAM,IAAI,IAAI,IAAI;IAClB,MAAM,IAAI,QAAQ,GAAG,GAAG,IAAI,IAAI;IAChC,MAAM,IAAI,QAAQ,GAAG,GAAG;IACxB,MAAM,IAAI,QAAQ,GAAG,GAAG,IAAI,IAAI;IAChC,OAAO,KAAK,KAAK,CAAC,IAAI,QAAQ,KAAK,KAAK,KAAK,CAAC,IAAI,QAAQ,KAAK,KAAK,KAAK,CAAC,IAAI,QAAQ;AACxF;AACA,SAAS,SAAS,GAAG;IACnB,MAAM,MAAM,SAAS,KAAK;IAC1B,IAAI,MAAM,GAAG,OAAO;IACpB,IAAI,MAAM,KAAK,OAAO;IACtB,OAAO;AACT;AACA,SAAS,SAAS,GAAG;IACnB,MAAM,MAAM,WAAW;IACvB,OAAO,CAAC,MAAM,MAAM,GAAG,IAAI,MAAM;AACnC;AACA,SAAS,OAAO,GAAG;IACjB,MAAM,MAAM,WAAW;IACvB,IAAI,MAAM,GAAG,OAAO;IACpB,IAAI,MAAM,GAAG,OAAO;IACpB,OAAO,KAAK,KAAK,CAAC,MAAM;AAC1B;AACA,SAAS,gBAAgB,GAAG;IAC1B,MAAM,MAAM,WAAW;IACvB,IAAI,MAAM,GAAG,OAAO;IACpB,IAAI,MAAM,KAAK,OAAO;IACtB,OAAO,MAAM;AACf;AAEA,qBAAqB;AACrB,SAAS,YAAY,KAAK;IACxB,IAAI,aAAa,eAAe;IAChC,IAAI,eAAe,MAAM,OAAO;IAChC,aAAa,cAAc;IAC3B,MAAM,IAAI,CAAC,aAAa,UAAU,MAAM;IACxC,MAAM,IAAI,CAAC,aAAa,QAAQ,MAAM;IACtC,MAAM,IAAI,CAAC,aAAa,KAAK,MAAM;IACnC,MAAM,IAAI,CAAC,aAAa,GAAG,IAAI;IAC/B,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACvC;AAEA,4BAA4B;AAC5B,IAAI,qBAAqB,CAAC,OAAO,QAAQ;IACvC,IAAI,GAAG,GAAG,CAAC,QAAQ;QACjB,OAAO;IACT;IACA,IAAI,GAAG,GAAG,CAAC,QAAQ;QACjB,OAAO,mBAAmB;YACxB;YACA;YACA;QACF;IACF;IACA,IAAI,GAAG,GAAG,CAAC,MAAM,MAAM,CAAC,EAAE,GAAG;QAC3B,OAAO,yBAAyB;IAClC;IACA,MAAM,SAAS;IACf,MAAM,cAAc,OAAO,MAAM;IACjC,MAAM,aAAa,OAAO,KAAK,IAAI;QAAC;QAAG;KAAE;IACzC,MAAM,kBAAkB,OAAO,eAAe,IAAI,OAAO,WAAW,IAAI;IACxE,MAAM,mBAAmB,OAAO,gBAAgB,IAAI,OAAO,WAAW,IAAI;IAC1E,MAAM,SAAS,OAAO,MAAM,IAAI,CAAC,CAAC,IAAM,CAAC;IACzC,OAAO,CAAC;QACN,MAAM,SAAS,UAAU,OAAO;QAChC,OAAO,YACL,OACA,UAAU,CAAC,OAAO,EAClB,UAAU,CAAC,SAAS,EAAE,EACtB,WAAW,CAAC,OAAO,EACnB,WAAW,CAAC,SAAS,EAAE,EACvB,QACA,iBACA,kBACA,OAAO,GAAG;IAEd;AACF;AACA,SAAS,YAAY,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAAG;IAClH,IAAI,SAAS,MAAM,IAAI,SAAS;IAChC,IAAI,SAAS,UAAU;QACrB,IAAI,oBAAoB,YAAY,OAAO;aACtC,IAAI,oBAAoB,SAAS,SAAS;IACjD;IACA,IAAI,SAAS,UAAU;QACrB,IAAI,qBAAqB,YAAY,OAAO;aACvC,IAAI,qBAAqB,SAAS,SAAS;IAClD;IACA,IAAI,cAAc,WAAW,OAAO;IACpC,IAAI,aAAa,UAAU,OAAO,SAAS,WAAW,YAAY;IAClE,IAAI,aAAa,CAAC,UAAU,SAAS,CAAC;SACjC,IAAI,aAAa,UAAU,SAAS,SAAS;SAC7C,SAAS,CAAC,SAAS,QAAQ,IAAI,CAAC,WAAW,QAAQ;IACxD,SAAS,OAAO;IAChB,IAAI,cAAc,CAAC,UAAU,SAAS,CAAC;SAClC,IAAI,cAAc,UAAU,SAAS,SAAS;SAC9C,SAAS,SAAS,CAAC,YAAY,SAAS,IAAI;IACjD,OAAO;AACT;AACA,SAAS,UAAU,KAAK,EAAE,UAAU;IAClC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,GAAG,GAAG,EAAE,EAC3C,IAAI,UAAU,CAAC,EAAE,IAAI,OAAO;IAC9B,OAAO,IAAI;AACb;AAEA,iBAAiB;AACjB,IAAI,QAAQ,CAAC,QAAQ,YAAY,KAAK,GAAK,CAAC;QAC1C,YAAY,cAAc,QAAQ,KAAK,GAAG,CAAC,WAAW,SAAS,KAAK,GAAG,CAAC,WAAW;QACnF,MAAM,WAAW,YAAY;QAC7B,MAAM,UAAU,cAAc,QAAQ,KAAK,KAAK,CAAC,YAAY,KAAK,IAAI,CAAC;QACvE,OAAO,MAAM,GAAG,GAAG,UAAU;IAC/B;AACA,IAAI,KAAK;AACT,IAAI,KAAK,KAAK;AACd,IAAI,KAAK,KAAK;AACd,IAAI,KAAK,IAAI,KAAK,EAAE,GAAG;AACvB,IAAI,KAAK,IAAI,KAAK,EAAE,GAAG;AACvB,IAAI,YAAY,CAAC;IACf,MAAM,KAAK;IACX,MAAM,KAAK;IACX,IAAI,IAAI,IAAI,IAAI;QACd,OAAO,KAAK,IAAI;IAClB,OAAO,IAAI,IAAI,IAAI,IAAI;QACrB,OAAO,KAAK,CAAC,KAAK,MAAM,EAAE,IAAI,IAAI;IACpC,OAAO,IAAI,IAAI,MAAM,IAAI;QACvB,OAAO,KAAK,CAAC,KAAK,OAAO,EAAE,IAAI,IAAI;IACrC,OAAO;QACL,OAAO,KAAK,CAAC,KAAK,QAAQ,EAAE,IAAI,IAAI;IACtC;AACF;AACA,IAAI,UAAU;IACZ,QAAQ,CAAC,IAAM;IACf,YAAY,CAAC,IAAM,IAAI;IACvB,aAAa,CAAC,IAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IACxC,eAAe,CAAC,IAAM,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,KAAK;IAC1E,aAAa,CAAC,IAAM,IAAI,IAAI;IAC5B,cAAc,CAAC,IAAM,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG;IACzC,gBAAgB,CAAC,IAAM,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,KAAK;IAC/E,aAAa,CAAC,IAAM,IAAI,IAAI,IAAI;IAChC,cAAc,CAAC,IAAM,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG;IACzC,gBAAgB,CAAC,IAAM,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,KAAK;IACnF,aAAa,CAAC,IAAM,IAAI,IAAI,IAAI,IAAI;IACpC,cAAc,CAAC,IAAM,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG;IACzC,gBAAgB,CAAC,IAAM,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,KAAK;IACxF,YAAY,CAAC,IAAM,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,EAAE,GAAG;IAC9C,aAAa,CAAC,IAAM,KAAK,GAAG,CAAC,IAAI,KAAK,EAAE,GAAG;IAC3C,eAAe,CAAC,IAAM,CAAC,CAAC,KAAK,GAAG,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,IAAI;IACrD,YAAY,CAAC,IAAM,MAAM,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI;IACtD,aAAa,CAAC,IAAM,MAAM,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK;IACxD,eAAe,CAAC,IAAM,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,IAAI;IAC7H,YAAY,CAAC,IAAM,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG;IACjD,aAAa,CAAC,IAAM,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG;IAClD,eAAe,CAAC,IAAM,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI;IAC7H,YAAY,CAAC,IAAM,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI;IAC7C,aAAa,CAAC,IAAM,IAAI,KAAK,KAAK,GAAG,CAAC,IAAI,GAAG,KAAK,KAAK,KAAK,GAAG,CAAC,IAAI,GAAG;IACvE,eAAe,CAAC,IAAM,IAAI,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI;IAClJ,eAAe,CAAC,IAAM,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI;IAC1G,gBAAgB,CAAC,IAAM,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,MAAM;IAC3G,kBAAkB,CAAC,IAAM,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,MAAM,IAAI,MAAM,IAAI;IACvM,cAAc,CAAC,IAAM,IAAI,UAAU,IAAI;IACvC,eAAe;IACf,iBAAiB,CAAC,IAAM,IAAI,MAAM,CAAC,IAAI,UAAU,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,UAAU,IAAI,IAAI,EAAE,IAAI;IAChG;AACF;AAEA,gBAAgB;AAChB,IAAI,OAAO,OAAO,GAAG,CAAC;AACtB,IAAI,aAAa,OAAO,GAAG,CAAC;AAC5B,IAAI,gBAAgB,CAAC,MAAQ,QAAQ,OAAO,GAAG,CAAC,KAAK;AACrD,IAAI,gBAAgB,CAAC,MAAQ,OAAO,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,KAAK;AAC9D,IAAI,oBAAoB,CAAC,SAAW,MAAM,CAAC,WAAW,IAAI;AAC1D,SAAS,kBAAkB,SAAS,EAAE,KAAK;IACzC,IAAI,UAAU,aAAa,EAAE;QAC3B,UAAU,aAAa,CAAC;IAC1B,OAAO;QACL,UAAU;IACZ;AACF;AACA,SAAS,mBAAmB,MAAM,EAAE,KAAK;IACvC,MAAM,YAAY,MAAM,CAAC,WAAW;IACpC,IAAI,WAAW;QACb,UAAU,OAAO,CAAC,CAAC;YACjB,kBAAkB,WAAW;QAC/B;IACF;AACF;AACA,MAAM;AACN,IAAI,aAAa;IACf,YAAY,GAAG,CAAE;QACf,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,GAAG;YAC7B,MAAM,MAAM;QACd;QACA,eAAe,IAAI,EAAE;IACvB;AACF;AACA,IAAI,iBAAiB,CAAC,QAAQ,MAAQ,UAAU,QAAQ,MAAM;AAC9D,SAAS,iBAAiB,MAAM,EAAE,SAAS;IACzC,IAAI,MAAM,CAAC,KAAK,EAAE;QAChB,IAAI,YAAY,MAAM,CAAC,WAAW;QAClC,IAAI,CAAC,WAAW;YACd,UAAU,QAAQ,YAAY,YAAY,aAAa,GAAG,IAAI;QAChE;QACA,IAAI,CAAC,UAAU,GAAG,CAAC,YAAY;YAC7B,UAAU,GAAG,CAAC;YACd,IAAI,OAAO,aAAa,EAAE;gBACxB,OAAO,aAAa,CAAC,UAAU,IAAI,EAAE;YACvC;QACF;IACF;IACA,OAAO;AACT;AACA,SAAS,oBAAoB,MAAM,EAAE,SAAS;IAC5C,MAAM,YAAY,MAAM,CAAC,WAAW;IACpC,IAAI,aAAa,UAAU,GAAG,CAAC,YAAY;QACzC,MAAM,QAAQ,UAAU,IAAI,GAAG;QAC/B,IAAI,OAAO;YACT,UAAU,MAAM,CAAC;QACnB,OAAO;YACL,MAAM,CAAC,WAAW,GAAG;QACvB;QACA,IAAI,OAAO,eAAe,EAAE;YAC1B,OAAO,eAAe,CAAC,OAAO;QAChC;IACF;AACF;AACA,IAAI,YAAY,CAAC,QAAQ,KAAK,QAAU,OAAO,cAAc,CAAC,QAAQ,KAAK;QACzE;QACA,UAAU;QACV,cAAc;IAChB;AAEA,gBAAgB;AAChB,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,IAAI,YAAY,IAAI,OAAO,CAAC,CAAC,EAAE,YAAY,MAAM,CAAC,WAAW,CAAC,EAAE;AAChE,IAAI,YAAY;AAChB,IAAI,mBAAmB;AAEvB,wBAAwB;AACxB,IAAI,iBAAiB,CAAC;IACpB,MAAM,CAAC,OAAO,SAAS,GAAG,iBAAiB;IAC3C,IAAI,CAAC,SAAS,SAAS;QACrB,OAAO;IACT;IACA,MAAM,QAAQ,OAAO,gBAAgB,CAAC,SAAS,eAAe,EAAE,gBAAgB,CAAC;IACjF,IAAI,OAAO;QACT,OAAO,MAAM,IAAI;IACnB,OAAO,IAAI,YAAY,SAAS,UAAU,CAAC,OAAO;QAChD,MAAM,SAAS,OAAO,gBAAgB,CAAC,SAAS,eAAe,EAAE,gBAAgB,CAAC;QAClF,IAAI,QAAQ;YACV,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF,OAAO,IAAI,YAAY,iBAAiB,IAAI,CAAC,WAAW;QACtD,OAAO,eAAe;IACxB,OAAO,IAAI,UAAU;QACnB,OAAO;IACT;IACA,OAAO;AACT;AACA,IAAI,mBAAmB,CAAC;IACtB,MAAM,QAAQ,iBAAiB,IAAI,CAAC;IACpC,IAAI,CAAC,OAAO,OAAO;;KAAG;IACtB,MAAM,GAAG,OAAO,SAAS,GAAG;IAC5B,OAAO;QAAC;QAAO;KAAS;AAC1B;AAEA,6BAA6B;AAC7B,IAAI;AACJ,IAAI,YAAY,CAAC,GAAG,IAAI,IAAI,IAAI,KAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,IAAI,EAAE,EAAE,KAAK,KAAK,CAAC,IAAI,EAAE,EAAE,KAAK,KAAK,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC;AAC7G,IAAI,4BAA4B,CAAC;IAC/B,IAAI,CAAC,iBACH,kBAAkB,SAChB,4CAA4C;IAC5C,IAAI,OAAO,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,QAAQ,CAAC,EAAE,OAExD,cAAc;IACd;IAEJ,MAAM,SAAS,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;QAChC,OAAO,cAAc,OAAO,OAAO,CAAC,kBAAkB,gBAAgB,OAAO,CAAC,YAAY,aAAa,OAAO,CAAC,iBAAiB;IAClI;IACA,MAAM,YAAY,OAAO,GAAG,CAAC,CAAC,QAAU,MAAM,KAAK,CAAC,aAAa,GAAG,CAAC;IACrE,MAAM,eAAe,SAAS,CAAC,EAAE,CAAC,GAAG,CACnC,CAAC,GAAG,IAAM,UAAU,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,CAAC,KAAK,MAAM,GAAG;gBAClB,MAAM,MAAM;YACd;YACA,OAAO,MAAM,CAAC,EAAE;QAClB;IAEF,MAAM,gBAAgB,aAAa,GAAG,CACpC,CAAC,UAAY,mBAAmB;YAAE,GAAG,MAAM;YAAE,QAAQ;QAAQ;IAE/D,OAAO,CAAC;QACN,MAAM,cAAc,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,OAAO,IAAI,CAAC,CAAC,QAAU,UAAU,IAAI,CAAC,SAAS,QAAQ,aAAa;QACtH,IAAI,IAAI;QACR,OAAO,MAAM,CAAC,EAAE,CAAC,OAAO,CACtB,aACA,IAAM,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,eAAe,IAAI,EACxD,OAAO,CAAC,WAAW;IACvB;AACF;AAEA,sBAAsB;AACtB,IAAI,SAAS;AACb,IAAI,OAAO,CAAC;IACV,MAAM,OAAO;IACb,IAAI,SAAS;IACb,IAAI,OAAO,QAAQ,YAAY;QAC7B,MAAM,IAAI,UAAU,GAAG,OAAO,kCAAkC,CAAC;IACnE;IACA,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,QAAQ;YACX,QAAQ;YACR,SAAS;QACX;IACF;AACF;AACA,IAAI,kBAAkB,KAAK,QAAQ,IAAI;AACvC,SAAS;IACP,gBACE,GAAG,OAAO,iEAAiE,CAAC;AAEhF;AACA,IAAI,iBAAiB,KAAK,QAAQ,IAAI;AACtC,SAAS;IACP,eACE,GAAG,OAAO,+IAA+I,CAAC;AAE9J;AAEA,0BAA0B;AAC1B,SAAS,iBAAiB,KAAK;IAC7B,OAAO,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,IAAI,OAAO,KAAK,IAAI,CAAC,UAAU,iEAAiE;IACjI,CAAC,WAAW,iBAAiB,IAAI,CAAC,UAAU,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AACrE;AAEA,iCAAiC;AACjC,IAAI;AAEJ,yCAAyC;AACzC,IAAI;AACJ,IAAI,iBAAiB,aAAa,GAAG,IAAI;AACzC,IAAI,oBAAoB,CAAC,UAAY,QAAQ,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE;QAC3E,OAAO,eAAe,GAAG,CAAC,SAAS,QAAQ,CAAC,UAAY,QAAQ;IAClE;AACA,SAAS,cAAc,OAAO,EAAE,MAAM;IACpC,IAAI,CAAC,UAAU;QACb,IAAI,OAAO,mBAAmB,aAAa;YACzC,WAAW,IAAI,eAAe;QAChC;IACF;IACA,IAAI,kBAAkB,eAAe,GAAG,CAAC;IACzC,IAAI,CAAC,iBAAiB;QACpB,kBAAkB,aAAa,GAAG,IAAI;QACtC,eAAe,GAAG,CAAC,QAAQ;IAC7B;IACA,gBAAgB,GAAG,CAAC;IACpB,IAAI,UAAU;QACZ,SAAS,OAAO,CAAC;IACnB;IACA,OAAO;QACL,MAAM,mBAAmB,eAAe,GAAG,CAAC;QAC5C,IAAI,CAAC,kBAAkB;QACvB,iBAAiB,MAAM,CAAC;QACxB,IAAI,CAAC,iBAAiB,IAAI,IAAI,UAAU;YACtC,SAAS,SAAS,CAAC;QACrB;IACF;AACF;AAEA,wCAAwC;AACxC,IAAI,YAAY,aAAa,GAAG,IAAI;AACpC,IAAI;AACJ,IAAI,sBAAsB;IACxB,MAAM,eAAe;QACnB,UAAU,OAAO,CACf,CAAC,WAAa,SAAS;gBACrB,OAAO,OAAO,UAAU;gBACxB,QAAQ,OAAO,WAAW;YAC5B;IAEJ;IACA,OAAO,gBAAgB,CAAC,UAAU;IAClC,OAAO;QACL,OAAO,mBAAmB,CAAC,UAAU;IACvC;AACF;AACA,IAAI,eAAe,CAAC;IAClB,UAAU,GAAG,CAAC;IACd,IAAI,CAAC,4BAA4B;QAC/B,6BAA6B;IAC/B;IACA,OAAO;QACL,UAAU,MAAM,CAAC;QACjB,IAAI,CAAC,UAAU,IAAI,IAAI,4BAA4B;YACjD;YACA,6BAA6B,KAAK;QACpC;IACF;AACF;AAEA,iCAAiC;AACjC,IAAI,WAAW,CAAC,UAAU,EAAE,YAAY,SAAS,eAAe,EAAE,GAAG,CAAC,CAAC;IACrE,IAAI,cAAc,SAAS,eAAe,EAAE;QAC1C,OAAO,aAAa;IACtB,OAAO;QACL,OAAO,cAAc,UAAU;IACjC;AACF;AAEA,kBAAkB;AAClB,IAAI,WAAW,CAAC,KAAK,KAAK,QAAU,MAAM,QAAQ,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG;AAEpF,yCAAyC;AACzC,IAAI,cAAc;IAChB,GAAG;QACD,QAAQ;QACR,UAAU;IACZ;IACA,GAAG;QACD,QAAQ;QACR,UAAU;IACZ;AACF;AACA,IAAI,gBAAgB;IAClB,YAAY,QAAQ,EAAE,SAAS,CAAE;QAC/B,IAAI,CAAC,UAAU,GAAG,IAAM,CAAC;gBACvB,SAAS;gBACT,UAAU;gBACV,cAAc;YAChB,CAAC;QACD,IAAI,CAAC,UAAU,GAAG,CAAC;YACjB,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS;YAChC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAW,CAAC,SAAS;YAClD,KAAK,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,UAAU,CAAC;YAClD,KAAK,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC;YACzF,KAAK,QAAQ,GAAG,SAAS,GAAG,KAAK,YAAY,EAAE,KAAK,OAAO;QAC7D;QACA,IAAI,CAAC,MAAM,GAAG;YACZ,IAAI,CAAC,UAAU,CAAC;YAChB,IAAI,CAAC,UAAU,CAAC;QAClB;QACA,IAAI,CAAC,SAAS,GAAG;YACf,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;QACzB;QACA,IAAI,CAAC,OAAO,GAAG;YACb,IAAI,CAAC,MAAM;YACX,IAAI,CAAC,SAAS;QAChB;QACA,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,IAAI,GAAG;YACV,MAAM;YACN,GAAG,IAAI,CAAC,UAAU;YAClB,GAAG,IAAI,CAAC,UAAU;QACpB;IACF;AACF;AAEA,iCAAiC;AACjC,IAAI,kBAAkB,aAAa,GAAG,IAAI;AAC1C,IAAI,kBAAkB,aAAa,GAAG,IAAI;AAC1C,IAAI,mBAAmB,aAAa,GAAG,IAAI;AAC3C,IAAI,YAAY,CAAC,YAAc,cAAc,SAAS,eAAe,GAAG,SAAS;AACjF,IAAI,WAAW,CAAC,UAAU,EAAE,YAAY,SAAS,eAAe,EAAE,GAAG,CAAC,CAAC;IACrE,IAAI,oBAAoB,iBAAiB,GAAG,CAAC;IAC7C,IAAI,CAAC,mBAAmB;QACtB,oBAAoB,aAAa,GAAG,IAAI;QACxC,iBAAiB,GAAG,CAAC,WAAW;IAClC;IACA,MAAM,mBAAmB,IAAI,cAAc,UAAU;IACrD,kBAAkB,GAAG,CAAC;IACtB,IAAI,CAAC,gBAAgB,GAAG,CAAC,YAAY;QACnC,MAAM,WAAW;YACf,mBAAmB,QAAQ,CAAC,UAAY,QAAQ,OAAO;YACvD,OAAO;QACT;QACA,gBAAgB,GAAG,CAAC,WAAW;QAC/B,MAAM,SAAS,UAAU;QACzB,OAAO,gBAAgB,CAAC,UAAU,UAAU;YAAE,SAAS;QAAK;QAC5D,IAAI,cAAc,SAAS,eAAe,EAAE;YAC1C,gBAAgB,GAAG,CAAC,WAAW,SAAS,UAAU;gBAAE;YAAU;QAChE;QACA,OAAO,gBAAgB,CAAC,UAAU,UAAU;YAAE,SAAS;QAAK;IAC9D;IACA,MAAM,gBAAgB,gBAAgB,GAAG,CAAC;IAC1C,CAAC,GAAG,aAAa,GAAG,EAAE;IACtB,OAAO;QACL,aAAa,GAAG,CAAC,MAAM,CAAC;QACxB,MAAM,qBAAqB,iBAAiB,GAAG,CAAC;QAChD,IAAI,CAAC,oBAAoB;QACzB,mBAAmB,MAAM,CAAC;QAC1B,IAAI,mBAAmB,IAAI,EAAE;QAC7B,MAAM,WAAW,gBAAgB,GAAG,CAAC;QACrC,gBAAgB,MAAM,CAAC;QACvB,IAAI,UAAU;YACZ,UAAU,WAAW,mBAAmB,CAAC,UAAU;YACnD,OAAO,mBAAmB,CAAC,UAAU;YACrC,gBAAgB,GAAG,CAAC;QACtB;IACF;AACF;AAEA,2BAA2B;AAC3B,IAAI;AACJ,SAAS,YAAY,IAAI;IACvB,MAAM,MAAM,CAAC,GAAG,aAAa,MAAM,EAAE;IACrC,IAAI,IAAI,OAAO,KAAK,MAAM;QACxB,IAAI,OAAO,GAAG;IAChB;IACA,OAAO,IAAI,OAAO;AACpB;AAEA,8BAA8B;AAC9B,IAAI;AAEJ,4BAA4B;AAC5B,IAAI;AAEJ,yCAAyC;AACzC,IAAI;AACJ,IAAI,4BAA4B,UAAU,cAAc,SAAS,GAAG,cAAc,eAAe;AAEjG,4BAA4B;AAC5B,IAAI,eAAe;IACjB,MAAM,YAAY,CAAC,GAAG,cAAc,MAAM,EAAE;IAC5C;kDAA0B;YACxB,UAAU,OAAO,GAAG;YACpB;0DAAO;oBACL,UAAU,OAAO,GAAG;gBACtB;;QACF;iDAAG,EAAE;IACL,OAAO;AACT;AAEA,8BAA8B;AAC9B,SAAS;IACP,MAAM,SAAS,CAAC,GAAG,cAAc,QAAQ,GAAG,CAAC,EAAE;IAC/C,MAAM,YAAY;IAClB,OAAO;QACL,IAAI,UAAU,OAAO,EAAE;YACrB,OAAO,KAAK,MAAM;QACpB;IACF;AACF;AAEA,0BAA0B;AAC1B,IAAI;AACJ,SAAS,WAAW,SAAS,EAAE,MAAM;IACnC,MAAM,CAAC,QAAQ,GAAG,CAAC,GAAG,cAAc,QAAQ,EAC1C,IAAM,CAAC;YACL;YACA,QAAQ;QACV,CAAC;IAEH,MAAM,YAAY,CAAC,GAAG,cAAc,MAAM,EAAE,KAAK;IACjD,MAAM,YAAY,UAAU,OAAO;IACnC,IAAI,QAAQ;IACZ,IAAI,OAAO;QACT,MAAM,WAAW,QACf,UAAU,MAAM,MAAM,IAAI,eAAe,QAAQ,MAAM,MAAM;QAE/D,IAAI,CAAC,UAAU;YACb,QAAQ;gBACN;gBACA,QAAQ;YACV;QACF;IACF,OAAO;QACL,QAAQ;IACV;IACA,CAAC,GAAG,cAAc,SAAS,EAAE;QAC3B,UAAU,OAAO,GAAG;QACpB,IAAI,aAAa,SAAS;YACxB,QAAQ,MAAM,GAAG,QAAQ,MAAM,GAAG,KAAK;QACzC;IACF,GAAG;QAAC;KAAM;IACV,OAAO,MAAM,MAAM;AACrB;AACA,SAAS,eAAe,IAAI,EAAE,IAAI;IAChC,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM,EAAE;QAC/B,OAAO;IACT;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;YACvB,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA,uBAAuB;AACvB,IAAI;AACJ,IAAI,UAAU,CAAC,SAAW,CAAC,GAAG,cAAc,SAAS,EAAE,QAAQ;AAC/D,IAAI,YAAY,EAAE;AAElB,uBAAuB;AACvB,IAAI;AACJ,SAAS,QAAQ,KAAK;IACpB,MAAM,UAAU,CAAC,GAAG,cAAc,MAAM,EAAE,KAAK;IAC/C,CAAC,GAAG,cAAc,SAAS,EAAE;QAC3B,QAAQ,OAAO,GAAG;IACpB;IACA,OAAO,QAAQ,OAAO;AACxB;AAEA,gCAAgC;AAChC,IAAI;AACJ,IAAI,mBAAmB;IACrB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAC,GAAG,cAAc,QAAQ,EAAE;IACtE;sDAA0B;YACxB,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,MAAM;gFAAoB,CAAC;oBACzB,iBAAiB,EAAE,OAAO;oBAC1B,OAAO;wBACL,eAAe,EAAE,OAAO;oBAC1B;gBACF;;YACA,kBAAkB;YAClB,IAAI,IAAI,gBAAgB,EAAE;gBACxB,IAAI,gBAAgB,CAAC,UAAU;YACjC,OAAO;gBACL,IAAI,WAAW,CAAC;YAClB;YACA;8DAAO;oBACL,IAAI,IAAI,mBAAmB,EAAE;wBAC3B,IAAI,mBAAmB,CAAC,UAAU;oBACpC,OAAO;wBACL,IAAI,cAAc,CAAC;oBACrB;gBACF;;QACF;qDAAG,EAAE;IACL,OAAO;AACT;AAEA,eAAe;AACf,IAAI;AACJ,6DAA6D;AAC7D,KAAK,CAAC,OAAO,OAAO,GAAG;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2244, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2249, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-spring%2Bshared%4010.0.1_react%4019.0.0/node_modules/%40react-spring/shared/dist/cjs/index.js"], "sourcesContent": ["'use strict'\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./react-spring_shared.production.min.cjs')\n} else {\n  module.exports = require('./react-spring_shared.development.cjs')\n}"], "names": [], "mappings": "AACI;AADJ;AACA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2256, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2261, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-spring%2Btypes%4010.0.1/node_modules/%40react-spring/types/dist/cjs/react-spring_types.development.cjs"], "sourcesContent": ["\"use strict\";\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  Any: () => Any\n});\nmodule.exports = __toCommonJS(src_exports);\n\n// src/utils.ts\nvar Any = class {\n};\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  Any\n});\n"], "names": [], "mappings": "AAAA;AACA,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,mBAAmB,OAAO,wBAAwB;AACtD,IAAI,oBAAoB,OAAO,mBAAmB;AAClD,IAAI,eAAe,OAAO,SAAS,CAAC,cAAc;AAClD,IAAI,WAAW,CAAC,QAAQ;IACtB,IAAK,IAAI,QAAQ,IACf,UAAU,QAAQ,MAAM;QAAE,KAAK,GAAG,CAAC,KAAK;QAAE,YAAY;IAAK;AAC/D;AACA,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ;IACnC,IAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;QAClE,KAAK,IAAI,OAAO,kBAAkB,MAChC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,QAAQ,QAAQ,QACzC,UAAU,IAAI,KAAK;YAAE,KAAK,IAAM,IAAI,CAAC,IAAI;YAAE,YAAY,CAAC,CAAC,OAAO,iBAAiB,MAAM,IAAI,KAAK,KAAK,UAAU;QAAC;IACtH;IACA,OAAO;AACT;AACA,IAAI,eAAe,CAAC,MAAQ,YAAY,UAAU,CAAC,GAAG,cAAc;QAAE,OAAO;IAAK,IAAI;AAEtF,eAAe;AACf,IAAI,cAAc,CAAC;AACnB,SAAS,aAAa;IACpB,KAAK,IAAM;AACb;AACA,OAAO,OAAO,GAAG,aAAa;AAE9B,eAAe;AACf,IAAI,MAAM;AACV;AACA,6DAA6D;AAC7D,KAAK,CAAC,OAAO,OAAO,GAAG;IACrB;AACF,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2297, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2302, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-spring%2Btypes%4010.0.1/node_modules/%40react-spring/types/dist/cjs/index.js"], "sourcesContent": ["'use strict'\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./react-spring_types.production.min.cjs')\n} else {\n  module.exports = require('./react-spring_types.development.cjs')\n}"], "names": [], "mappings": "AACI;AADJ;AACA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2309, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2314, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-spring%2Banimated%4010.0.1_react%4019.0.0/node_modules/%40react-spring/animated/dist/cjs/react-spring_animated.development.cjs"], "sourcesContent": ["\"use strict\";\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  Animated: () => Animated,\n  AnimatedArray: () => AnimatedArray,\n  AnimatedObject: () => AnimatedObject,\n  AnimatedString: () => AnimatedString,\n  AnimatedValue: () => AnimatedValue,\n  createHost: () => createHost,\n  getAnimated: () => getAnimated,\n  getAnimatedType: () => getAnimatedType,\n  getPayload: () => getPayload,\n  isAnimated: () => isAnimated,\n  setAnimated: () => setAnimated\n});\nmodule.exports = __toCommonJS(src_exports);\n\n// src/Animated.ts\nvar import_shared = require(\"@react-spring/shared\");\nvar $node = Symbol.for(\"Animated:node\");\nvar isAnimated = (value) => !!value && value[$node] === value;\nvar getAnimated = (owner) => owner && owner[$node];\nvar setAnimated = (owner, node) => (0, import_shared.defineHidden)(owner, $node, node);\nvar getPayload = (owner) => owner && owner[$node] && owner[$node].getPayload();\nvar Animated = class {\n  constructor() {\n    setAnimated(this, this);\n  }\n  /** Get every `AnimatedValue` used by this node. */\n  getPayload() {\n    return this.payload || [];\n  }\n};\n\n// src/AnimatedValue.ts\nvar import_shared2 = require(\"@react-spring/shared\");\nvar AnimatedValue = class _AnimatedValue extends Animated {\n  constructor(_value) {\n    super();\n    this._value = _value;\n    this.done = true;\n    this.durationProgress = 0;\n    if (import_shared2.is.num(this._value)) {\n      this.lastPosition = this._value;\n    }\n  }\n  /** @internal */\n  static create(value) {\n    return new _AnimatedValue(value);\n  }\n  getPayload() {\n    return [this];\n  }\n  getValue() {\n    return this._value;\n  }\n  setValue(value, step) {\n    if (import_shared2.is.num(value)) {\n      this.lastPosition = value;\n      if (step) {\n        value = Math.round(value / step) * step;\n        if (this.done) {\n          this.lastPosition = value;\n        }\n      }\n    }\n    if (this._value === value) {\n      return false;\n    }\n    this._value = value;\n    return true;\n  }\n  reset() {\n    const { done } = this;\n    this.done = false;\n    if (import_shared2.is.num(this._value)) {\n      this.elapsedTime = 0;\n      this.durationProgress = 0;\n      this.lastPosition = this._value;\n      if (done) this.lastVelocity = null;\n      this.v0 = null;\n    }\n  }\n};\n\n// src/AnimatedString.ts\nvar import_shared3 = require(\"@react-spring/shared\");\nvar AnimatedString = class _AnimatedString extends AnimatedValue {\n  constructor(value) {\n    super(0);\n    this._string = null;\n    this._toString = (0, import_shared3.createInterpolator)({\n      output: [value, value]\n    });\n  }\n  /** @internal */\n  static create(value) {\n    return new _AnimatedString(value);\n  }\n  getValue() {\n    const value = this._string;\n    return value == null ? this._string = this._toString(this._value) : value;\n  }\n  setValue(value) {\n    if (import_shared3.is.str(value)) {\n      if (value == this._string) {\n        return false;\n      }\n      this._string = value;\n      this._value = 1;\n    } else if (super.setValue(value)) {\n      this._string = null;\n    } else {\n      return false;\n    }\n    return true;\n  }\n  reset(goal) {\n    if (goal) {\n      this._toString = (0, import_shared3.createInterpolator)({\n        output: [this.getValue(), goal]\n      });\n    }\n    this._value = 0;\n    super.reset();\n  }\n};\n\n// src/AnimatedArray.ts\nvar import_shared5 = require(\"@react-spring/shared\");\n\n// src/AnimatedObject.ts\nvar import_shared4 = require(\"@react-spring/shared\");\n\n// src/context.ts\nvar TreeContext = { dependencies: null };\n\n// src/AnimatedObject.ts\nvar AnimatedObject = class extends Animated {\n  constructor(source) {\n    super();\n    this.source = source;\n    this.setValue(source);\n  }\n  getValue(animated) {\n    const values = {};\n    (0, import_shared4.eachProp)(this.source, (source, key) => {\n      if (isAnimated(source)) {\n        values[key] = source.getValue(animated);\n      } else if ((0, import_shared4.hasFluidValue)(source)) {\n        values[key] = (0, import_shared4.getFluidValue)(source);\n      } else if (!animated) {\n        values[key] = source;\n      }\n    });\n    return values;\n  }\n  /** Replace the raw object data */\n  setValue(source) {\n    this.source = source;\n    this.payload = this._makePayload(source);\n  }\n  reset() {\n    if (this.payload) {\n      (0, import_shared4.each)(this.payload, (node) => node.reset());\n    }\n  }\n  /** Create a payload set. */\n  _makePayload(source) {\n    if (source) {\n      const payload = /* @__PURE__ */ new Set();\n      (0, import_shared4.eachProp)(source, this._addToPayload, payload);\n      return Array.from(payload);\n    }\n  }\n  /** Add to a payload set. */\n  _addToPayload(source) {\n    if (TreeContext.dependencies && (0, import_shared4.hasFluidValue)(source)) {\n      TreeContext.dependencies.add(source);\n    }\n    const payload = getPayload(source);\n    if (payload) {\n      (0, import_shared4.each)(payload, (node) => this.add(node));\n    }\n  }\n};\n\n// src/AnimatedArray.ts\nvar AnimatedArray = class _AnimatedArray extends AnimatedObject {\n  constructor(source) {\n    super(source);\n  }\n  /** @internal */\n  static create(source) {\n    return new _AnimatedArray(source);\n  }\n  getValue() {\n    return this.source.map((node) => node.getValue());\n  }\n  setValue(source) {\n    const payload = this.getPayload();\n    if (source.length == payload.length) {\n      return payload.map((node, i) => node.setValue(source[i])).some(Boolean);\n    }\n    super.setValue(source.map(makeAnimated));\n    return true;\n  }\n};\nfunction makeAnimated(value) {\n  const nodeType = (0, import_shared5.isAnimatedString)(value) ? AnimatedString : AnimatedValue;\n  return nodeType.create(value);\n}\n\n// src/getAnimatedType.ts\nvar import_shared6 = require(\"@react-spring/shared\");\nfunction getAnimatedType(value) {\n  const parentNode = getAnimated(value);\n  return parentNode ? parentNode.constructor : import_shared6.is.arr(value) ? AnimatedArray : (0, import_shared6.isAnimatedString)(value) ? AnimatedString : AnimatedValue;\n}\n\n// src/createHost.ts\nvar import_shared8 = require(\"@react-spring/shared\");\n\n// src/withAnimated.tsx\nvar React = __toESM(require(\"react\"));\nvar import_react = require(\"react\");\nvar import_shared7 = require(\"@react-spring/shared\");\nvar withAnimated = (Component, host) => {\n  const hasInstance = (\n    // Function components must use \"forwardRef\" to avoid being\n    // re-rendered on every animation frame.\n    !import_shared7.is.fun(Component) || Component.prototype && Component.prototype.isReactComponent\n  );\n  return (0, import_react.forwardRef)((givenProps, givenRef) => {\n    const instanceRef = (0, import_react.useRef)(null);\n    const ref = hasInstance && // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0, import_react.useCallback)(\n      (value) => {\n        instanceRef.current = updateRef(givenRef, value);\n      },\n      [givenRef]\n    );\n    const [props, deps] = getAnimatedState(givenProps, host);\n    const forceUpdate = (0, import_shared7.useForceUpdate)();\n    const callback = () => {\n      const instance = instanceRef.current;\n      if (hasInstance && !instance) {\n        return;\n      }\n      const didUpdate = instance ? host.applyAnimatedValues(instance, props.getValue(true)) : false;\n      if (didUpdate === false) {\n        forceUpdate();\n      }\n    };\n    const observer = new PropsObserver(callback, deps);\n    const observerRef = (0, import_react.useRef)(void 0);\n    (0, import_shared7.useIsomorphicLayoutEffect)(() => {\n      observerRef.current = observer;\n      (0, import_shared7.each)(deps, (dep) => (0, import_shared7.addFluidObserver)(dep, observer));\n      return () => {\n        if (observerRef.current) {\n          (0, import_shared7.each)(\n            observerRef.current.deps,\n            (dep) => (0, import_shared7.removeFluidObserver)(dep, observerRef.current)\n          );\n          import_shared7.raf.cancel(observerRef.current.update);\n        }\n      };\n    });\n    (0, import_react.useEffect)(callback, []);\n    (0, import_shared7.useOnce)(() => () => {\n      const observer2 = observerRef.current;\n      (0, import_shared7.each)(observer2.deps, (dep) => (0, import_shared7.removeFluidObserver)(dep, observer2));\n    });\n    const usedProps = host.getComponentProps(props.getValue());\n    return /* @__PURE__ */ React.createElement(Component, { ...usedProps, ref });\n  });\n};\nvar PropsObserver = class {\n  constructor(update, deps) {\n    this.update = update;\n    this.deps = deps;\n  }\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      import_shared7.raf.write(this.update);\n    }\n  }\n};\nfunction getAnimatedState(props, host) {\n  const dependencies = /* @__PURE__ */ new Set();\n  TreeContext.dependencies = dependencies;\n  if (props.style)\n    props = {\n      ...props,\n      style: host.createAnimatedStyle(props.style)\n    };\n  props = new AnimatedObject(props);\n  TreeContext.dependencies = null;\n  return [props, dependencies];\n}\nfunction updateRef(ref, value) {\n  if (ref) {\n    if (import_shared7.is.fun(ref)) ref(value);\n    else ref.current = value;\n  }\n  return value;\n}\n\n// src/createHost.ts\nvar cacheKey = Symbol.for(\"AnimatedComponent\");\nvar createHost = (components, {\n  applyAnimatedValues = () => false,\n  createAnimatedStyle = (style) => new AnimatedObject(style),\n  getComponentProps = (props) => props\n} = {}) => {\n  const hostConfig = {\n    applyAnimatedValues,\n    createAnimatedStyle,\n    getComponentProps\n  };\n  const animated = (Component) => {\n    const displayName = getDisplayName(Component) || \"Anonymous\";\n    if (import_shared8.is.str(Component)) {\n      Component = animated[Component] || (animated[Component] = withAnimated(Component, hostConfig));\n    } else {\n      Component = Component[cacheKey] || (Component[cacheKey] = withAnimated(Component, hostConfig));\n    }\n    Component.displayName = `Animated(${displayName})`;\n    return Component;\n  };\n  (0, import_shared8.eachProp)(components, (Component, key) => {\n    if (import_shared8.is.arr(components)) {\n      key = getDisplayName(Component);\n    }\n    animated[key] = animated(Component);\n  });\n  return {\n    animated\n  };\n};\nvar getDisplayName = (arg) => import_shared8.is.str(arg) ? arg : arg && import_shared8.is.str(arg.displayName) ? arg.displayName : import_shared8.is.fun(arg) && arg.name || null;\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  Animated,\n  AnimatedArray,\n  AnimatedObject,\n  AnimatedString,\n  AnimatedValue,\n  createHost,\n  getAnimated,\n  getAnimatedType,\n  getPayload,\n  isAnimated,\n  setAnimated\n});\n"], "names": [], "mappings": "AAAA;AACA,IAAI,WAAW,OAAO,MAAM;AAC5B,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,mBAAmB,OAAO,wBAAwB;AACtD,IAAI,oBAAoB,OAAO,mBAAmB;AAClD,IAAI,eAAe,OAAO,cAAc;AACxC,IAAI,eAAe,OAAO,SAAS,CAAC,cAAc;AAClD,IAAI,WAAW,CAAC,QAAQ;IACtB,IAAK,IAAI,QAAQ,IACf,UAAU,QAAQ,MAAM;QAAE,KAAK,GAAG,CAAC,KAAK;QAAE,YAAY;IAAK;AAC/D;AACA,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ;IACnC,IAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;QAClE,KAAK,IAAI,OAAO,kBAAkB,MAChC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,QAAQ,QAAQ,QACzC,UAAU,IAAI,KAAK;YAAE,KAAK,IAAM,IAAI,CAAC,IAAI;YAAE,YAAY,CAAC,CAAC,OAAO,iBAAiB,MAAM,IAAI,KAAK,KAAK,UAAU;QAAC;IACtH;IACA,OAAO;AACT;AACA,IAAI,UAAU,CAAC,KAAK,YAAY,SAAW,CAAC,SAAS,OAAO,OAAO,SAAS,aAAa,QAAQ,CAAC,GAAG,YACnG,sEAAsE;IACtE,iEAAiE;IACjE,sEAAsE;IACtE,qEAAqE;IACrE,cAAc,CAAC,OAAO,CAAC,IAAI,UAAU,GAAG,UAAU,QAAQ,WAAW;QAAE,OAAO;QAAK,YAAY;IAAK,KAAK,QACzG,IACD;AACD,IAAI,eAAe,CAAC,MAAQ,YAAY,UAAU,CAAC,GAAG,cAAc;QAAE,OAAO;IAAK,IAAI;AAEtF,eAAe;AACf,IAAI,cAAc,CAAC;AACnB,SAAS,aAAa;IACpB,UAAU,IAAM;IAChB,eAAe,IAAM;IACrB,gBAAgB,IAAM;IACtB,gBAAgB,IAAM;IACtB,eAAe,IAAM;IACrB,YAAY,IAAM;IAClB,aAAa,IAAM;IACnB,iBAAiB,IAAM;IACvB,YAAY,IAAM;IAClB,YAAY,IAAM;IAClB,aAAa,IAAM;AACrB;AACA,OAAO,OAAO,GAAG,aAAa;AAE9B,kBAAkB;AAClB,IAAI;AACJ,IAAI,QAAQ,OAAO,GAAG,CAAC;AACvB,IAAI,aAAa,CAAC,QAAU,CAAC,CAAC,SAAS,KAAK,CAAC,MAAM,KAAK;AACxD,IAAI,cAAc,CAAC,QAAU,SAAS,KAAK,CAAC,MAAM;AAClD,IAAI,cAAc,CAAC,OAAO,OAAS,CAAC,GAAG,cAAc,YAAY,EAAE,OAAO,OAAO;AACjF,IAAI,aAAa,CAAC,QAAU,SAAS,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,UAAU;AAC5E,IAAI,WAAW;IACb,aAAc;QACZ,YAAY,IAAI,EAAE,IAAI;IACxB;IACA,iDAAiD,GACjD,aAAa;QACX,OAAO,IAAI,CAAC,OAAO,IAAI,EAAE;IAC3B;AACF;AAEA,uBAAuB;AACvB,IAAI;AACJ,IAAI,gBAAgB,MAAM,uBAAuB;IAC/C,YAAY,MAAM,CAAE;QAClB,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG;YACtC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM;QACjC;IACF;IACA,cAAc,GACd,OAAO,OAAO,KAAK,EAAE;QACnB,OAAO,IAAI,eAAe;IAC5B;IACA,aAAa;QACX,OAAO;YAAC,IAAI;SAAC;IACf;IACA,WAAW;QACT,OAAO,IAAI,CAAC,MAAM;IACpB;IACA,SAAS,KAAK,EAAE,IAAI,EAAE;QACpB,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ;YAChC,IAAI,CAAC,YAAY,GAAG;YACpB,IAAI,MAAM;gBACR,QAAQ,KAAK,KAAK,CAAC,QAAQ,QAAQ;gBACnC,IAAI,IAAI,CAAC,IAAI,EAAE;oBACb,IAAI,CAAC,YAAY,GAAG;gBACtB;YACF;QACF;QACA,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO;YACzB,OAAO;QACT;QACA,IAAI,CAAC,MAAM,GAAG;QACd,OAAO;IACT;IACA,QAAQ;QACN,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI;QACrB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG;YACtC,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM;YAC/B,IAAI,MAAM,IAAI,CAAC,YAAY,GAAG;YAC9B,IAAI,CAAC,EAAE,GAAG;QACZ;IACF;AACF;AAEA,wBAAwB;AACxB,IAAI;AACJ,IAAI,iBAAiB,MAAM,wBAAwB;IACjD,YAAY,KAAK,CAAE;QACjB,KAAK,CAAC;QACN,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,eAAe,kBAAkB,EAAE;YACtD,QAAQ;gBAAC;gBAAO;aAAM;QACxB;IACF;IACA,cAAc,GACd,OAAO,OAAO,KAAK,EAAE;QACnB,OAAO,IAAI,gBAAgB;IAC7B;IACA,WAAW;QACT,MAAM,QAAQ,IAAI,CAAC,OAAO;QAC1B,OAAO,SAAS,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI;IACtE;IACA,SAAS,KAAK,EAAE;QACd,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ;YAChC,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,OAAO;YACT;YACA,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,MAAM,GAAG;QAChB,OAAO,IAAI,KAAK,CAAC,SAAS,QAAQ;YAChC,IAAI,CAAC,OAAO,GAAG;QACjB,OAAO;YACL,OAAO;QACT;QACA,OAAO;IACT;IACA,MAAM,IAAI,EAAE;QACV,IAAI,MAAM;YACR,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,eAAe,kBAAkB,EAAE;gBACtD,QAAQ;oBAAC,IAAI,CAAC,QAAQ;oBAAI;iBAAK;YACjC;QACF;QACA,IAAI,CAAC,MAAM,GAAG;QACd,KAAK,CAAC;IACR;AACF;AAEA,uBAAuB;AACvB,IAAI;AAEJ,wBAAwB;AACxB,IAAI;AAEJ,iBAAiB;AACjB,IAAI,cAAc;IAAE,cAAc;AAAK;AAEvC,wBAAwB;AACxB,IAAI,iBAAiB,cAAc;IACjC,YAAY,MAAM,CAAE;QAClB,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,CAAC;IAChB;IACA,SAAS,QAAQ,EAAE;QACjB,MAAM,SAAS,CAAC;QAChB,CAAC,GAAG,eAAe,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ;YACjD,IAAI,WAAW,SAAS;gBACtB,MAAM,CAAC,IAAI,GAAG,OAAO,QAAQ,CAAC;YAChC,OAAO,IAAI,CAAC,GAAG,eAAe,aAAa,EAAE,SAAS;gBACpD,MAAM,CAAC,IAAI,GAAG,CAAC,GAAG,eAAe,aAAa,EAAE;YAClD,OAAO,IAAI,CAAC,UAAU;gBACpB,MAAM,CAAC,IAAI,GAAG;YAChB;QACF;QACA,OAAO;IACT;IACA,gCAAgC,GAChC,SAAS,MAAM,EAAE;QACf,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC;IACnC;IACA,QAAQ;QACN,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,CAAC,GAAG,eAAe,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,OAAS,KAAK,KAAK;QAC7D;IACF;IACA,0BAA0B,GAC1B,aAAa,MAAM,EAAE;QACnB,IAAI,QAAQ;YACV,MAAM,UAAU,aAAa,GAAG,IAAI;YACpC,CAAC,GAAG,eAAe,QAAQ,EAAE,QAAQ,IAAI,CAAC,aAAa,EAAE;YACzD,OAAO,MAAM,IAAI,CAAC;QACpB;IACF;IACA,0BAA0B,GAC1B,cAAc,MAAM,EAAE;QACpB,IAAI,YAAY,YAAY,IAAI,CAAC,GAAG,eAAe,aAAa,EAAE,SAAS;YACzE,YAAY,YAAY,CAAC,GAAG,CAAC;QAC/B;QACA,MAAM,UAAU,WAAW;QAC3B,IAAI,SAAS;YACX,CAAC,GAAG,eAAe,IAAI,EAAE,SAAS,CAAC,OAAS,IAAI,CAAC,GAAG,CAAC;QACvD;IACF;AACF;AAEA,uBAAuB;AACvB,IAAI,gBAAgB,MAAM,uBAAuB;IAC/C,YAAY,MAAM,CAAE;QAClB,KAAK,CAAC;IACR;IACA,cAAc,GACd,OAAO,OAAO,MAAM,EAAE;QACpB,OAAO,IAAI,eAAe;IAC5B;IACA,WAAW;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,QAAQ;IAChD;IACA,SAAS,MAAM,EAAE;QACf,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,IAAI,OAAO,MAAM,IAAI,QAAQ,MAAM,EAAE;YACnC,OAAO,QAAQ,GAAG,CAAC,CAAC,MAAM,IAAM,KAAK,QAAQ,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC;QACjE;QACA,KAAK,CAAC,SAAS,OAAO,GAAG,CAAC;QAC1B,OAAO;IACT;AACF;AACA,SAAS,aAAa,KAAK;IACzB,MAAM,WAAW,CAAC,GAAG,eAAe,gBAAgB,EAAE,SAAS,iBAAiB;IAChF,OAAO,SAAS,MAAM,CAAC;AACzB;AAEA,yBAAyB;AACzB,IAAI;AACJ,SAAS,gBAAgB,KAAK;IAC5B,MAAM,aAAa,YAAY;IAC/B,OAAO,aAAa,WAAW,WAAW,GAAG,eAAe,EAAE,CAAC,GAAG,CAAC,SAAS,gBAAgB,CAAC,GAAG,eAAe,gBAAgB,EAAE,SAAS,iBAAiB;AAC7J;AAEA,oBAAoB;AACpB,IAAI;AAEJ,uBAAuB;AACvB,IAAI,QAAQ;AACZ,IAAI;AACJ,IAAI;AACJ,IAAI,eAAe,CAAC,WAAW;IAC7B,MAAM,cACJ,2DAA2D;IAC3D,wCAAwC;IACxC,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,cAAc,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,gBAAgB;IAElG,OAAO,CAAC,GAAG,aAAa,UAAU,EAAE,CAAC,YAAY;QAC/C,MAAM,cAAc,CAAC,GAAG,aAAa,MAAM,EAAE;QAC7C,MAAM,MAAM,eAAe,sDAAsD;QACjF,CAAC,GAAG,aAAa,WAAW,EAC1B,CAAC;YACC,YAAY,OAAO,GAAG,UAAU,UAAU;QAC5C,GACA;YAAC;SAAS;QAEZ,MAAM,CAAC,OAAO,KAAK,GAAG,iBAAiB,YAAY;QACnD,MAAM,cAAc,CAAC,GAAG,eAAe,cAAc;QACrD,MAAM,WAAW;YACf,MAAM,WAAW,YAAY,OAAO;YACpC,IAAI,eAAe,CAAC,UAAU;gBAC5B;YACF;YACA,MAAM,YAAY,WAAW,KAAK,mBAAmB,CAAC,UAAU,MAAM,QAAQ,CAAC,SAAS;YACxF,IAAI,cAAc,OAAO;gBACvB;YACF;QACF;QACA,MAAM,WAAW,IAAI,cAAc,UAAU;QAC7C,MAAM,cAAc,CAAC,GAAG,aAAa,MAAM,EAAE,KAAK;QAClD,CAAC,GAAG,eAAe,yBAAyB,EAAE;YAC5C,YAAY,OAAO,GAAG;YACtB,CAAC,GAAG,eAAe,IAAI,EAAE,MAAM,CAAC,MAAQ,CAAC,GAAG,eAAe,gBAAgB,EAAE,KAAK;YAClF,OAAO;gBACL,IAAI,YAAY,OAAO,EAAE;oBACvB,CAAC,GAAG,eAAe,IAAI,EACrB,YAAY,OAAO,CAAC,IAAI,EACxB,CAAC,MAAQ,CAAC,GAAG,eAAe,mBAAmB,EAAE,KAAK,YAAY,OAAO;oBAE3E,eAAe,GAAG,CAAC,MAAM,CAAC,YAAY,OAAO,CAAC,MAAM;gBACtD;YACF;QACF;QACA,CAAC,GAAG,aAAa,SAAS,EAAE,UAAU,EAAE;QACxC,CAAC,GAAG,eAAe,OAAO,EAAE,IAAM;gBAChC,MAAM,YAAY,YAAY,OAAO;gBACrC,CAAC,GAAG,eAAe,IAAI,EAAE,UAAU,IAAI,EAAE,CAAC,MAAQ,CAAC,GAAG,eAAe,mBAAmB,EAAE,KAAK;YACjG;QACA,MAAM,YAAY,KAAK,iBAAiB,CAAC,MAAM,QAAQ;QACvD,OAAO,aAAa,GAAG,MAAM,aAAa,CAAC,WAAW;YAAE,GAAG,SAAS;YAAE;QAAI;IAC5E;AACF;AACA,IAAI,gBAAgB;IAClB,YAAY,MAAM,EAAE,IAAI,CAAE;QACxB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG;IACd;IACA,cAAc,KAAK,EAAE;QACnB,IAAI,MAAM,IAAI,IAAI,UAAU;YAC1B,eAAe,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM;QACtC;IACF;AACF;AACA,SAAS,iBAAiB,KAAK,EAAE,IAAI;IACnC,MAAM,eAAe,aAAa,GAAG,IAAI;IACzC,YAAY,YAAY,GAAG;IAC3B,IAAI,MAAM,KAAK,EACb,QAAQ;QACN,GAAG,KAAK;QACR,OAAO,KAAK,mBAAmB,CAAC,MAAM,KAAK;IAC7C;IACF,QAAQ,IAAI,eAAe;IAC3B,YAAY,YAAY,GAAG;IAC3B,OAAO;QAAC;QAAO;KAAa;AAC9B;AACA,SAAS,UAAU,GAAG,EAAE,KAAK;IAC3B,IAAI,KAAK;QACP,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM,IAAI;aAC/B,IAAI,OAAO,GAAG;IACrB;IACA,OAAO;AACT;AAEA,oBAAoB;AACpB,IAAI,WAAW,OAAO,GAAG,CAAC;AAC1B,IAAI,aAAa,CAAC,YAAY,EAC5B,sBAAsB,IAAM,KAAK,EACjC,sBAAsB,CAAC,QAAU,IAAI,eAAe,MAAM,EAC1D,oBAAoB,CAAC,QAAU,KAAK,EACrC,GAAG,CAAC,CAAC;IACJ,MAAM,aAAa;QACjB;QACA;QACA;IACF;IACA,MAAM,WAAW,CAAC;QAChB,MAAM,cAAc,eAAe,cAAc;QACjD,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,YAAY;YACpC,YAAY,QAAQ,CAAC,UAAU,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,aAAa,WAAW,WAAW;QAC/F,OAAO;YACL,YAAY,SAAS,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,aAAa,WAAW,WAAW;QAC/F;QACA,UAAU,WAAW,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QAClD,OAAO;IACT;IACA,CAAC,GAAG,eAAe,QAAQ,EAAE,YAAY,CAAC,WAAW;QACnD,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,aAAa;YACrC,MAAM,eAAe;QACvB;QACA,QAAQ,CAAC,IAAI,GAAG,SAAS;IAC3B;IACA,OAAO;QACL;IACF;AACF;AACA,IAAI,iBAAiB,CAAC,MAAQ,eAAe,EAAE,CAAC,GAAG,CAAC,OAAO,MAAM,OAAO,eAAe,EAAE,CAAC,GAAG,CAAC,IAAI,WAAW,IAAI,IAAI,WAAW,GAAG,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ,IAAI,IAAI,IAAI;AAC7K,6DAA6D;AAC7D,KAAK,CAAC,OAAO,OAAO,GAAG;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2689, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2694, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-spring%2Banimated%4010.0.1_react%4019.0.0/node_modules/%40react-spring/animated/dist/cjs/index.js"], "sourcesContent": ["'use strict'\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./react-spring_animated.production.min.cjs')\n} else {\n  module.exports = require('./react-spring_animated.development.cjs')\n}"], "names": [], "mappings": "AACI;AADJ;AACA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2701, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2706, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-spring%2Bcore%4010.0.1_react%4019.0.0/node_modules/%40react-spring/core/dist/cjs/react-spring_core.development.cjs"], "sourcesContent": ["\"use strict\";\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to2, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to2, key) && key !== except)\n        __defProp(to2, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to2;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  BailSignal: () => BailSignal,\n  Controller: () => Controller,\n  FrameValue: () => FrameValue,\n  Globals: () => import_shared21.Globals,\n  Interpolation: () => Interpolation,\n  Spring: () => Spring,\n  SpringContext: () => SpringContext,\n  SpringRef: () => SpringRef,\n  SpringValue: () => SpringValue,\n  Trail: () => Trail,\n  Transition: () => Transition,\n  config: () => config,\n  createInterpolator: () => import_shared22.createInterpolator,\n  easings: () => import_shared22.easings,\n  inferTo: () => inferTo,\n  interpolate: () => interpolate,\n  to: () => to,\n  update: () => update,\n  useChain: () => useChain,\n  useInView: () => useInView,\n  useIsomorphicLayoutEffect: () => import_shared22.useIsomorphicLayoutEffect,\n  useReducedMotion: () => import_shared22.useReducedMotion,\n  useResize: () => useResize,\n  useScroll: () => useScroll,\n  useSpring: () => useSpring,\n  useSpringRef: () => useSpringRef,\n  useSpringValue: () => useSpringValue,\n  useSprings: () => useSprings,\n  useTrail: () => useTrail,\n  useTransition: () => useTransition\n});\nmodule.exports = __toCommonJS(src_exports);\n\n// src/hooks/useChain.ts\nvar import_shared2 = require(\"@react-spring/shared\");\n\n// src/helpers.ts\nvar import_shared = require(\"@react-spring/shared\");\nfunction callProp(value, ...args) {\n  return import_shared.is.fun(value) ? value(...args) : value;\n}\nvar matchProp = (value, key) => value === true || !!(key && value && (import_shared.is.fun(value) ? value(key) : (0, import_shared.toArray)(value).includes(key)));\nvar resolveProp = (prop, key) => import_shared.is.obj(prop) ? key && prop[key] : prop;\nvar getDefaultProp = (props, key) => props.default === true ? props[key] : props.default ? props.default[key] : void 0;\nvar noopTransform = (value) => value;\nvar getDefaultProps = (props, transform = noopTransform) => {\n  let keys = DEFAULT_PROPS;\n  if (props.default && props.default !== true) {\n    props = props.default;\n    keys = Object.keys(props);\n  }\n  const defaults2 = {};\n  for (const key of keys) {\n    const value = transform(props[key], key);\n    if (!import_shared.is.und(value)) {\n      defaults2[key] = value;\n    }\n  }\n  return defaults2;\n};\nvar DEFAULT_PROPS = [\n  \"config\",\n  \"onProps\",\n  \"onStart\",\n  \"onChange\",\n  \"onPause\",\n  \"onResume\",\n  \"onRest\"\n];\nvar RESERVED_PROPS = {\n  config: 1,\n  from: 1,\n  to: 1,\n  ref: 1,\n  loop: 1,\n  reset: 1,\n  pause: 1,\n  cancel: 1,\n  reverse: 1,\n  immediate: 1,\n  default: 1,\n  delay: 1,\n  onProps: 1,\n  onStart: 1,\n  onChange: 1,\n  onPause: 1,\n  onResume: 1,\n  onRest: 1,\n  onResolve: 1,\n  // Transition props\n  items: 1,\n  trail: 1,\n  sort: 1,\n  expires: 1,\n  initial: 1,\n  enter: 1,\n  update: 1,\n  leave: 1,\n  children: 1,\n  onDestroyed: 1,\n  // Internal props\n  keys: 1,\n  callId: 1,\n  parentId: 1\n};\nfunction getForwardProps(props) {\n  const forward = {};\n  let count = 0;\n  (0, import_shared.eachProp)(props, (value, prop) => {\n    if (!RESERVED_PROPS[prop]) {\n      forward[prop] = value;\n      count++;\n    }\n  });\n  if (count) {\n    return forward;\n  }\n}\nfunction inferTo(props) {\n  const to2 = getForwardProps(props);\n  if (to2) {\n    const out = { to: to2 };\n    (0, import_shared.eachProp)(props, (val, key) => key in to2 || (out[key] = val));\n    return out;\n  }\n  return { ...props };\n}\nfunction computeGoal(value) {\n  value = (0, import_shared.getFluidValue)(value);\n  return import_shared.is.arr(value) ? value.map(computeGoal) : (0, import_shared.isAnimatedString)(value) ? import_shared.Globals.createStringInterpolator({\n    range: [0, 1],\n    output: [value, value]\n  })(1) : value;\n}\nfunction hasProps(props) {\n  for (const _ in props) return true;\n  return false;\n}\nfunction isAsyncTo(to2) {\n  return import_shared.is.fun(to2) || import_shared.is.arr(to2) && import_shared.is.obj(to2[0]);\n}\nfunction detachRefs(ctrl, ref) {\n  ctrl.ref?.delete(ctrl);\n  ref?.delete(ctrl);\n}\nfunction replaceRef(ctrl, ref) {\n  if (ref && ctrl.ref !== ref) {\n    ctrl.ref?.delete(ctrl);\n    ref.add(ctrl);\n    ctrl.ref = ref;\n  }\n}\n\n// src/hooks/useChain.ts\nfunction useChain(refs, timeSteps, timeFrame = 1e3) {\n  (0, import_shared2.useIsomorphicLayoutEffect)(() => {\n    if (timeSteps) {\n      let prevDelay = 0;\n      (0, import_shared2.each)(refs, (ref, i) => {\n        const controllers = ref.current;\n        if (controllers.length) {\n          let delay = timeFrame * timeSteps[i];\n          if (isNaN(delay)) delay = prevDelay;\n          else prevDelay = delay;\n          (0, import_shared2.each)(controllers, (ctrl) => {\n            (0, import_shared2.each)(ctrl.queue, (props) => {\n              const memoizedDelayProp = props.delay;\n              props.delay = (key) => delay + callProp(memoizedDelayProp || 0, key);\n            });\n          });\n          ref.start();\n        }\n      });\n    } else {\n      let p = Promise.resolve();\n      (0, import_shared2.each)(refs, (ref) => {\n        const controllers = ref.current;\n        if (controllers.length) {\n          const queues = controllers.map((ctrl) => {\n            const q = ctrl.queue;\n            ctrl.queue = [];\n            return q;\n          });\n          p = p.then(() => {\n            (0, import_shared2.each)(\n              controllers,\n              (ctrl, i) => (0, import_shared2.each)(queues[i] || [], (update2) => ctrl.queue.push(update2))\n            );\n            return Promise.all(ref.start());\n          });\n        }\n      });\n    }\n  });\n}\n\n// src/hooks/useSpring.ts\nvar import_shared11 = require(\"@react-spring/shared\");\n\n// src/hooks/useSprings.ts\nvar import_react2 = require(\"react\");\nvar import_shared10 = require(\"@react-spring/shared\");\n\n// src/SpringValue.ts\nvar import_shared7 = require(\"@react-spring/shared\");\nvar import_animated2 = require(\"@react-spring/animated\");\n\n// src/AnimationConfig.ts\nvar import_shared3 = require(\"@react-spring/shared\");\n\n// src/constants.ts\nvar config = {\n  default: { tension: 170, friction: 26 },\n  gentle: { tension: 120, friction: 14 },\n  wobbly: { tension: 180, friction: 12 },\n  stiff: { tension: 210, friction: 20 },\n  slow: { tension: 280, friction: 60 },\n  molasses: { tension: 280, friction: 120 }\n};\n\n// src/AnimationConfig.ts\nvar defaults = {\n  ...config.default,\n  mass: 1,\n  damping: 1,\n  easing: import_shared3.easings.linear,\n  clamp: false\n};\nvar AnimationConfig = class {\n  constructor() {\n    /**\n     * The initial velocity of one or more values.\n     *\n     * @default 0\n     */\n    this.velocity = 0;\n    Object.assign(this, defaults);\n  }\n};\nfunction mergeConfig(config2, newConfig, defaultConfig) {\n  if (defaultConfig) {\n    defaultConfig = { ...defaultConfig };\n    sanitizeConfig(defaultConfig, newConfig);\n    newConfig = { ...defaultConfig, ...newConfig };\n  }\n  sanitizeConfig(config2, newConfig);\n  Object.assign(config2, newConfig);\n  for (const key in defaults) {\n    if (config2[key] == null) {\n      config2[key] = defaults[key];\n    }\n  }\n  let { frequency, damping } = config2;\n  const { mass } = config2;\n  if (!import_shared3.is.und(frequency)) {\n    if (frequency < 0.01) frequency = 0.01;\n    if (damping < 0) damping = 0;\n    config2.tension = Math.pow(2 * Math.PI / frequency, 2) * mass;\n    config2.friction = 4 * Math.PI * damping * mass / frequency;\n  }\n  return config2;\n}\nfunction sanitizeConfig(config2, props) {\n  if (!import_shared3.is.und(props.decay)) {\n    config2.duration = void 0;\n  } else {\n    const isTensionConfig = !import_shared3.is.und(props.tension) || !import_shared3.is.und(props.friction);\n    if (isTensionConfig || !import_shared3.is.und(props.frequency) || !import_shared3.is.und(props.damping) || !import_shared3.is.und(props.mass)) {\n      config2.duration = void 0;\n      config2.decay = void 0;\n    }\n    if (isTensionConfig) {\n      config2.frequency = void 0;\n    }\n  }\n}\n\n// src/Animation.ts\nvar emptyArray = [];\nvar Animation = class {\n  constructor() {\n    this.changed = false;\n    this.values = emptyArray;\n    this.toValues = null;\n    this.fromValues = emptyArray;\n    this.config = new AnimationConfig();\n    this.immediate = false;\n  }\n};\n\n// src/scheduleProps.ts\nvar import_shared4 = require(\"@react-spring/shared\");\nfunction scheduleProps(callId, { key, props, defaultProps, state, actions }) {\n  return new Promise((resolve, reject) => {\n    let delay;\n    let timeout;\n    let cancel = matchProp(props.cancel ?? defaultProps?.cancel, key);\n    if (cancel) {\n      onStart();\n    } else {\n      if (!import_shared4.is.und(props.pause)) {\n        state.paused = matchProp(props.pause, key);\n      }\n      let pause = defaultProps?.pause;\n      if (pause !== true) {\n        pause = state.paused || matchProp(pause, key);\n      }\n      delay = callProp(props.delay || 0, key);\n      if (pause) {\n        state.resumeQueue.add(onResume);\n        actions.pause();\n      } else {\n        actions.resume();\n        onResume();\n      }\n    }\n    function onPause() {\n      state.resumeQueue.add(onResume);\n      state.timeouts.delete(timeout);\n      timeout.cancel();\n      delay = timeout.time - import_shared4.raf.now();\n    }\n    function onResume() {\n      if (delay > 0 && !import_shared4.Globals.skipAnimation) {\n        state.delayed = true;\n        timeout = import_shared4.raf.setTimeout(onStart, delay);\n        state.pauseQueue.add(onPause);\n        state.timeouts.add(timeout);\n      } else {\n        onStart();\n      }\n    }\n    function onStart() {\n      if (state.delayed) {\n        state.delayed = false;\n      }\n      state.pauseQueue.delete(onPause);\n      state.timeouts.delete(timeout);\n      if (callId <= (state.cancelId || 0)) {\n        cancel = true;\n      }\n      try {\n        actions.start({ ...props, callId, cancel }, resolve);\n      } catch (err) {\n        reject(err);\n      }\n    }\n  });\n}\n\n// src/runAsync.ts\nvar import_shared5 = require(\"@react-spring/shared\");\n\n// src/AnimationResult.ts\nvar getCombinedResult = (target, results) => results.length == 1 ? results[0] : results.some((result) => result.cancelled) ? getCancelledResult(target.get()) : results.every((result) => result.noop) ? getNoopResult(target.get()) : getFinishedResult(\n  target.get(),\n  results.every((result) => result.finished)\n);\nvar getNoopResult = (value) => ({\n  value,\n  noop: true,\n  finished: true,\n  cancelled: false\n});\nvar getFinishedResult = (value, finished, cancelled = false) => ({\n  value,\n  finished,\n  cancelled\n});\nvar getCancelledResult = (value) => ({\n  value,\n  cancelled: true,\n  finished: false\n});\n\n// src/runAsync.ts\nfunction runAsync(to2, props, state, target) {\n  const { callId, parentId, onRest } = props;\n  const { asyncTo: prevTo, promise: prevPromise } = state;\n  if (!parentId && to2 === prevTo && !props.reset) {\n    return prevPromise;\n  }\n  return state.promise = (async () => {\n    state.asyncId = callId;\n    state.asyncTo = to2;\n    const defaultProps = getDefaultProps(\n      props,\n      (value, key) => (\n        // The `onRest` prop is only called when the `runAsync` promise is resolved.\n        key === \"onRest\" ? void 0 : value\n      )\n    );\n    let preventBail;\n    let bail;\n    const bailPromise = new Promise(\n      (resolve, reject) => (preventBail = resolve, bail = reject)\n    );\n    const bailIfEnded = (bailSignal) => {\n      const bailResult = (\n        // The `cancel` prop or `stop` method was used.\n        callId <= (state.cancelId || 0) && getCancelledResult(target) || // The async `to` prop was replaced.\n        callId !== state.asyncId && getFinishedResult(target, false)\n      );\n      if (bailResult) {\n        bailSignal.result = bailResult;\n        bail(bailSignal);\n        throw bailSignal;\n      }\n    };\n    const animate = (arg1, arg2) => {\n      const bailSignal = new BailSignal();\n      const skipAnimationSignal = new SkipAnimationSignal();\n      return (async () => {\n        if (import_shared5.Globals.skipAnimation) {\n          stopAsync(state);\n          skipAnimationSignal.result = getFinishedResult(target, false);\n          bail(skipAnimationSignal);\n          throw skipAnimationSignal;\n        }\n        bailIfEnded(bailSignal);\n        const props2 = import_shared5.is.obj(arg1) ? { ...arg1 } : { ...arg2, to: arg1 };\n        props2.parentId = callId;\n        (0, import_shared5.eachProp)(defaultProps, (value, key) => {\n          if (import_shared5.is.und(props2[key])) {\n            props2[key] = value;\n          }\n        });\n        const result2 = await target.start(props2);\n        bailIfEnded(bailSignal);\n        if (state.paused) {\n          await new Promise((resume) => {\n            state.resumeQueue.add(resume);\n          });\n        }\n        return result2;\n      })();\n    };\n    let result;\n    if (import_shared5.Globals.skipAnimation) {\n      stopAsync(state);\n      return getFinishedResult(target, false);\n    }\n    try {\n      let animating;\n      if (import_shared5.is.arr(to2)) {\n        animating = (async (queue) => {\n          for (const props2 of queue) {\n            await animate(props2);\n          }\n        })(to2);\n      } else {\n        animating = Promise.resolve(to2(animate, target.stop.bind(target)));\n      }\n      await Promise.all([animating.then(preventBail), bailPromise]);\n      result = getFinishedResult(target.get(), true, false);\n    } catch (err) {\n      if (err instanceof BailSignal) {\n        result = err.result;\n      } else if (err instanceof SkipAnimationSignal) {\n        result = err.result;\n      } else {\n        throw err;\n      }\n    } finally {\n      if (callId == state.asyncId) {\n        state.asyncId = parentId;\n        state.asyncTo = parentId ? prevTo : void 0;\n        state.promise = parentId ? prevPromise : void 0;\n      }\n    }\n    if (import_shared5.is.fun(onRest)) {\n      import_shared5.raf.batchedUpdates(() => {\n        onRest(result, target, target.item);\n      });\n    }\n    return result;\n  })();\n}\nfunction stopAsync(state, cancelId) {\n  (0, import_shared5.flush)(state.timeouts, (t) => t.cancel());\n  state.pauseQueue.clear();\n  state.resumeQueue.clear();\n  state.asyncId = state.asyncTo = state.promise = void 0;\n  if (cancelId) state.cancelId = cancelId;\n}\nvar BailSignal = class extends Error {\n  constructor() {\n    super(\n      \"An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.\"\n    );\n  }\n};\nvar SkipAnimationSignal = class extends Error {\n  constructor() {\n    super(\"SkipAnimationSignal\");\n  }\n};\n\n// src/FrameValue.ts\nvar import_shared6 = require(\"@react-spring/shared\");\nvar import_animated = require(\"@react-spring/animated\");\nvar isFrameValue = (value) => value instanceof FrameValue;\nvar nextId = 1;\nvar FrameValue = class extends import_shared6.FluidValue {\n  constructor() {\n    super(...arguments);\n    this.id = nextId++;\n    this._priority = 0;\n  }\n  get priority() {\n    return this._priority;\n  }\n  set priority(priority) {\n    if (this._priority != priority) {\n      this._priority = priority;\n      this._onPriorityChange(priority);\n    }\n  }\n  /** Get the current value */\n  get() {\n    const node = (0, import_animated.getAnimated)(this);\n    return node && node.getValue();\n  }\n  /** Create a spring that maps our value to another value */\n  to(...args) {\n    return import_shared6.Globals.to(this, args);\n  }\n  /** @deprecated Use the `to` method instead. */\n  interpolate(...args) {\n    (0, import_shared6.deprecateInterpolate)();\n    return import_shared6.Globals.to(this, args);\n  }\n  toJSON() {\n    return this.get();\n  }\n  observerAdded(count) {\n    if (count == 1) this._attach();\n  }\n  observerRemoved(count) {\n    if (count == 0) this._detach();\n  }\n  /** Called when the first child is added. */\n  _attach() {\n  }\n  /** Called when the last child is removed. */\n  _detach() {\n  }\n  /** Tell our children about our new value */\n  _onChange(value, idle = false) {\n    (0, import_shared6.callFluidObservers)(this, {\n      type: \"change\",\n      parent: this,\n      value,\n      idle\n    });\n  }\n  /** Tell our children about our new priority */\n  _onPriorityChange(priority) {\n    if (!this.idle) {\n      import_shared6.frameLoop.sort(this);\n    }\n    (0, import_shared6.callFluidObservers)(this, {\n      type: \"priority\",\n      parent: this,\n      priority\n    });\n  }\n};\n\n// src/SpringPhase.ts\nvar $P = Symbol.for(\"SpringPhase\");\nvar HAS_ANIMATED = 1;\nvar IS_ANIMATING = 2;\nvar IS_PAUSED = 4;\nvar hasAnimated = (target) => (target[$P] & HAS_ANIMATED) > 0;\nvar isAnimating = (target) => (target[$P] & IS_ANIMATING) > 0;\nvar isPaused = (target) => (target[$P] & IS_PAUSED) > 0;\nvar setActiveBit = (target, active) => active ? target[$P] |= IS_ANIMATING | HAS_ANIMATED : target[$P] &= ~IS_ANIMATING;\nvar setPausedBit = (target, paused) => paused ? target[$P] |= IS_PAUSED : target[$P] &= ~IS_PAUSED;\n\n// src/SpringValue.ts\nvar SpringValue = class extends FrameValue {\n  constructor(arg1, arg2) {\n    super();\n    /** The animation state */\n    this.animation = new Animation();\n    /** Some props have customizable default values */\n    this.defaultProps = {};\n    /** The state for `runAsync` calls */\n    this._state = {\n      paused: false,\n      delayed: false,\n      pauseQueue: /* @__PURE__ */ new Set(),\n      resumeQueue: /* @__PURE__ */ new Set(),\n      timeouts: /* @__PURE__ */ new Set()\n    };\n    /** The promise resolvers of pending `start` calls */\n    this._pendingCalls = /* @__PURE__ */ new Set();\n    /** The counter for tracking `scheduleProps` calls */\n    this._lastCallId = 0;\n    /** The last `scheduleProps` call that changed the `to` prop */\n    this._lastToId = 0;\n    this._memoizedDuration = 0;\n    if (!import_shared7.is.und(arg1) || !import_shared7.is.und(arg2)) {\n      const props = import_shared7.is.obj(arg1) ? { ...arg1 } : { ...arg2, from: arg1 };\n      if (import_shared7.is.und(props.default)) {\n        props.default = true;\n      }\n      this.start(props);\n    }\n  }\n  /** Equals true when not advancing on each frame. */\n  get idle() {\n    return !(isAnimating(this) || this._state.asyncTo) || isPaused(this);\n  }\n  get goal() {\n    return (0, import_shared7.getFluidValue)(this.animation.to);\n  }\n  get velocity() {\n    const node = (0, import_animated2.getAnimated)(this);\n    return node instanceof import_animated2.AnimatedValue ? node.lastVelocity || 0 : node.getPayload().map((node2) => node2.lastVelocity || 0);\n  }\n  /**\n   * When true, this value has been animated at least once.\n   */\n  get hasAnimated() {\n    return hasAnimated(this);\n  }\n  /**\n   * When true, this value has an unfinished animation,\n   * which is either active or paused.\n   */\n  get isAnimating() {\n    return isAnimating(this);\n  }\n  /**\n   * When true, all current and future animations are paused.\n   */\n  get isPaused() {\n    return isPaused(this);\n  }\n  /**\n   *\n   *\n   */\n  get isDelayed() {\n    return this._state.delayed;\n  }\n  /** Advance the current animation by a number of milliseconds */\n  advance(dt) {\n    let idle = true;\n    let changed = false;\n    const anim = this.animation;\n    let { toValues } = anim;\n    const { config: config2 } = anim;\n    const payload = (0, import_animated2.getPayload)(anim.to);\n    if (!payload && (0, import_shared7.hasFluidValue)(anim.to)) {\n      toValues = (0, import_shared7.toArray)((0, import_shared7.getFluidValue)(anim.to));\n    }\n    anim.values.forEach((node2, i) => {\n      if (node2.done) return;\n      const to2 = (\n        // Animated strings always go from 0 to 1.\n        node2.constructor == import_animated2.AnimatedString ? 1 : payload ? payload[i].lastPosition : toValues[i]\n      );\n      let finished = anim.immediate;\n      let position = to2;\n      if (!finished) {\n        position = node2.lastPosition;\n        if (config2.tension <= 0) {\n          node2.done = true;\n          return;\n        }\n        let elapsed = node2.elapsedTime += dt;\n        const from = anim.fromValues[i];\n        const v0 = node2.v0 != null ? node2.v0 : node2.v0 = import_shared7.is.arr(config2.velocity) ? config2.velocity[i] : config2.velocity;\n        let velocity;\n        const precision = config2.precision || (from == to2 ? 5e-3 : Math.min(1, Math.abs(to2 - from) * 1e-3));\n        if (!import_shared7.is.und(config2.duration)) {\n          let p = 1;\n          if (config2.duration > 0) {\n            if (this._memoizedDuration !== config2.duration) {\n              this._memoizedDuration = config2.duration;\n              if (node2.durationProgress > 0) {\n                node2.elapsedTime = config2.duration * node2.durationProgress;\n                elapsed = node2.elapsedTime += dt;\n              }\n            }\n            p = (config2.progress || 0) + elapsed / this._memoizedDuration;\n            p = p > 1 ? 1 : p < 0 ? 0 : p;\n            node2.durationProgress = p;\n          }\n          position = from + config2.easing(p) * (to2 - from);\n          velocity = (position - node2.lastPosition) / dt;\n          finished = p == 1;\n        } else if (config2.decay) {\n          const decay = config2.decay === true ? 0.998 : config2.decay;\n          const e = Math.exp(-(1 - decay) * elapsed);\n          position = from + v0 / (1 - decay) * (1 - e);\n          finished = Math.abs(node2.lastPosition - position) <= precision;\n          velocity = v0 * e;\n        } else {\n          velocity = node2.lastVelocity == null ? v0 : node2.lastVelocity;\n          const restVelocity = config2.restVelocity || precision / 10;\n          const bounceFactor = config2.clamp ? 0 : config2.bounce;\n          const canBounce = !import_shared7.is.und(bounceFactor);\n          const isGrowing = from == to2 ? node2.v0 > 0 : from < to2;\n          let isMoving;\n          let isBouncing = false;\n          const step = 1;\n          const numSteps = Math.ceil(dt / step);\n          for (let n = 0; n < numSteps; ++n) {\n            isMoving = Math.abs(velocity) > restVelocity;\n            if (!isMoving) {\n              finished = Math.abs(to2 - position) <= precision;\n              if (finished) {\n                break;\n              }\n            }\n            if (canBounce) {\n              isBouncing = position == to2 || position > to2 == isGrowing;\n              if (isBouncing) {\n                velocity = -velocity * bounceFactor;\n                position = to2;\n              }\n            }\n            const springForce = -config2.tension * 1e-6 * (position - to2);\n            const dampingForce = -config2.friction * 1e-3 * velocity;\n            const acceleration = (springForce + dampingForce) / config2.mass;\n            velocity = velocity + acceleration * step;\n            position = position + velocity * step;\n          }\n        }\n        node2.lastVelocity = velocity;\n        if (Number.isNaN(position)) {\n          console.warn(`Got NaN while animating:`, this);\n          finished = true;\n        }\n      }\n      if (payload && !payload[i].done) {\n        finished = false;\n      }\n      if (finished) {\n        node2.done = true;\n      } else {\n        idle = false;\n      }\n      if (node2.setValue(position, config2.round)) {\n        changed = true;\n      }\n    });\n    const node = (0, import_animated2.getAnimated)(this);\n    const currVal = node.getValue();\n    if (idle) {\n      const finalVal = (0, import_shared7.getFluidValue)(anim.to);\n      if ((currVal !== finalVal || changed) && !config2.decay) {\n        node.setValue(finalVal);\n        this._onChange(finalVal);\n      } else if (changed && config2.decay) {\n        this._onChange(currVal);\n      }\n      this._stop();\n    } else if (changed) {\n      this._onChange(currVal);\n    }\n  }\n  /** Set the current value, while stopping the current animation */\n  set(value) {\n    import_shared7.raf.batchedUpdates(() => {\n      this._stop();\n      this._focus(value);\n      this._set(value);\n    });\n    return this;\n  }\n  /**\n   * Freeze the active animation in time, as well as any updates merged\n   * before `resume` is called.\n   */\n  pause() {\n    this._update({ pause: true });\n  }\n  /** Resume the animation if paused. */\n  resume() {\n    this._update({ pause: false });\n  }\n  /** Skip to the end of the current animation. */\n  finish() {\n    if (isAnimating(this)) {\n      const { to: to2, config: config2 } = this.animation;\n      import_shared7.raf.batchedUpdates(() => {\n        this._onStart();\n        if (!config2.decay) {\n          this._set(to2, false);\n        }\n        this._stop();\n      });\n    }\n    return this;\n  }\n  /** Push props into the pending queue. */\n  update(props) {\n    const queue = this.queue || (this.queue = []);\n    queue.push(props);\n    return this;\n  }\n  start(to2, arg2) {\n    let queue;\n    if (!import_shared7.is.und(to2)) {\n      queue = [import_shared7.is.obj(to2) ? to2 : { ...arg2, to: to2 }];\n    } else {\n      queue = this.queue || [];\n      this.queue = [];\n    }\n    return Promise.all(\n      queue.map((props) => {\n        const up = this._update(props);\n        return up;\n      })\n    ).then((results) => getCombinedResult(this, results));\n  }\n  /**\n   * Stop the current animation, and cancel any delayed updates.\n   *\n   * Pass `true` to call `onRest` with `cancelled: true`.\n   */\n  stop(cancel) {\n    const { to: to2 } = this.animation;\n    this._focus(this.get());\n    stopAsync(this._state, cancel && this._lastCallId);\n    import_shared7.raf.batchedUpdates(() => this._stop(to2, cancel));\n    return this;\n  }\n  /** Restart the animation. */\n  reset() {\n    this._update({ reset: true });\n  }\n  /** @internal */\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      this._start();\n    } else if (event.type == \"priority\") {\n      this.priority = event.priority + 1;\n    }\n  }\n  /**\n   * Parse the `to` and `from` range from the given `props` object.\n   *\n   * This also ensures the initial value is available to animated components\n   * during the render phase.\n   */\n  _prepareNode(props) {\n    const key = this.key || \"\";\n    let { to: to2, from } = props;\n    to2 = import_shared7.is.obj(to2) ? to2[key] : to2;\n    if (to2 == null || isAsyncTo(to2)) {\n      to2 = void 0;\n    }\n    from = import_shared7.is.obj(from) ? from[key] : from;\n    if (from == null) {\n      from = void 0;\n    }\n    const range = { to: to2, from };\n    if (!hasAnimated(this)) {\n      if (props.reverse) [to2, from] = [from, to2];\n      from = (0, import_shared7.getFluidValue)(from);\n      if (!import_shared7.is.und(from)) {\n        this._set(from);\n      } else if (!(0, import_animated2.getAnimated)(this)) {\n        this._set(to2);\n      }\n    }\n    return range;\n  }\n  /** Every update is processed by this method before merging. */\n  _update({ ...props }, isLoop) {\n    const { key, defaultProps } = this;\n    if (props.default)\n      Object.assign(\n        defaultProps,\n        getDefaultProps(\n          props,\n          (value, prop) => /^on/.test(prop) ? resolveProp(value, key) : value\n        )\n      );\n    mergeActiveFn(this, props, \"onProps\");\n    sendEvent(this, \"onProps\", props, this);\n    const range = this._prepareNode(props);\n    if (Object.isFrozen(this)) {\n      throw Error(\n        \"Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?\"\n      );\n    }\n    const state = this._state;\n    return scheduleProps(++this._lastCallId, {\n      key,\n      props,\n      defaultProps,\n      state,\n      actions: {\n        pause: () => {\n          if (!isPaused(this)) {\n            setPausedBit(this, true);\n            (0, import_shared7.flushCalls)(state.pauseQueue);\n            sendEvent(\n              this,\n              \"onPause\",\n              getFinishedResult(this, checkFinished(this, this.animation.to)),\n              this\n            );\n          }\n        },\n        resume: () => {\n          if (isPaused(this)) {\n            setPausedBit(this, false);\n            if (isAnimating(this)) {\n              this._resume();\n            }\n            (0, import_shared7.flushCalls)(state.resumeQueue);\n            sendEvent(\n              this,\n              \"onResume\",\n              getFinishedResult(this, checkFinished(this, this.animation.to)),\n              this\n            );\n          }\n        },\n        start: this._merge.bind(this, range)\n      }\n    }).then((result) => {\n      if (props.loop && result.finished && !(isLoop && result.noop)) {\n        const nextProps = createLoopUpdate(props);\n        if (nextProps) {\n          return this._update(nextProps, true);\n        }\n      }\n      return result;\n    });\n  }\n  /** Merge props into the current animation */\n  _merge(range, props, resolve) {\n    if (props.cancel) {\n      this.stop(true);\n      return resolve(getCancelledResult(this));\n    }\n    const hasToProp = !import_shared7.is.und(range.to);\n    const hasFromProp = !import_shared7.is.und(range.from);\n    if (hasToProp || hasFromProp) {\n      if (props.callId > this._lastToId) {\n        this._lastToId = props.callId;\n      } else {\n        return resolve(getCancelledResult(this));\n      }\n    }\n    const { key, defaultProps, animation: anim } = this;\n    const { to: prevTo, from: prevFrom } = anim;\n    let { to: to2 = prevTo, from = prevFrom } = range;\n    if (hasFromProp && !hasToProp && (!props.default || import_shared7.is.und(to2))) {\n      to2 = from;\n    }\n    if (props.reverse) [to2, from] = [from, to2];\n    const hasFromChanged = !(0, import_shared7.isEqual)(from, prevFrom);\n    if (hasFromChanged) {\n      anim.from = from;\n    }\n    from = (0, import_shared7.getFluidValue)(from);\n    const hasToChanged = !(0, import_shared7.isEqual)(to2, prevTo);\n    if (hasToChanged) {\n      this._focus(to2);\n    }\n    const hasAsyncTo = isAsyncTo(props.to);\n    const { config: config2 } = anim;\n    const { decay, velocity } = config2;\n    if (hasToProp || hasFromProp) {\n      config2.velocity = 0;\n    }\n    if (props.config && !hasAsyncTo) {\n      mergeConfig(\n        config2,\n        callProp(props.config, key),\n        // Avoid calling the same \"config\" prop twice.\n        props.config !== defaultProps.config ? callProp(defaultProps.config, key) : void 0\n      );\n    }\n    let node = (0, import_animated2.getAnimated)(this);\n    if (!node || import_shared7.is.und(to2)) {\n      return resolve(getFinishedResult(this, true));\n    }\n    const reset = (\n      // When `reset` is undefined, the `from` prop implies `reset: true`,\n      // except for declarative updates. When `reset` is defined, there\n      // must exist a value to animate from.\n      import_shared7.is.und(props.reset) ? hasFromProp && !props.default : !import_shared7.is.und(from) && matchProp(props.reset, key)\n    );\n    const value = reset ? from : this.get();\n    const goal = computeGoal(to2);\n    const isAnimatable = import_shared7.is.num(goal) || import_shared7.is.arr(goal) || (0, import_shared7.isAnimatedString)(goal);\n    const immediate = !hasAsyncTo && (!isAnimatable || matchProp(defaultProps.immediate || props.immediate, key));\n    if (hasToChanged) {\n      const nodeType = (0, import_animated2.getAnimatedType)(to2);\n      if (nodeType !== node.constructor) {\n        if (immediate) {\n          node = this._set(goal);\n        } else\n          throw Error(\n            `Cannot animate between ${node.constructor.name} and ${nodeType.name}, as the \"to\" prop suggests`\n          );\n      }\n    }\n    const goalType = node.constructor;\n    let started = (0, import_shared7.hasFluidValue)(to2);\n    let finished = false;\n    if (!started) {\n      const hasValueChanged = reset || !hasAnimated(this) && hasFromChanged;\n      if (hasToChanged || hasValueChanged) {\n        finished = (0, import_shared7.isEqual)(computeGoal(value), goal);\n        started = !finished;\n      }\n      if (!(0, import_shared7.isEqual)(anim.immediate, immediate) && !immediate || !(0, import_shared7.isEqual)(config2.decay, decay) || !(0, import_shared7.isEqual)(config2.velocity, velocity)) {\n        started = true;\n      }\n    }\n    if (finished && isAnimating(this)) {\n      if (anim.changed && !reset) {\n        started = true;\n      } else if (!started) {\n        this._stop(prevTo);\n      }\n    }\n    if (!hasAsyncTo) {\n      if (started || (0, import_shared7.hasFluidValue)(prevTo)) {\n        anim.values = node.getPayload();\n        anim.toValues = (0, import_shared7.hasFluidValue)(to2) ? null : goalType == import_animated2.AnimatedString ? [1] : (0, import_shared7.toArray)(goal);\n      }\n      if (anim.immediate != immediate) {\n        anim.immediate = immediate;\n        if (!immediate && !reset) {\n          this._set(prevTo);\n        }\n      }\n      if (started) {\n        const { onRest } = anim;\n        (0, import_shared7.each)(ACTIVE_EVENTS, (type) => mergeActiveFn(this, props, type));\n        const result = getFinishedResult(this, checkFinished(this, prevTo));\n        (0, import_shared7.flushCalls)(this._pendingCalls, result);\n        this._pendingCalls.add(resolve);\n        if (anim.changed)\n          import_shared7.raf.batchedUpdates(() => {\n            anim.changed = !reset;\n            onRest?.(result, this);\n            if (reset) {\n              callProp(defaultProps.onRest, result);\n            } else {\n              anim.onStart?.(result, this);\n            }\n          });\n      }\n    }\n    if (reset) {\n      this._set(value);\n    }\n    if (hasAsyncTo) {\n      resolve(runAsync(props.to, props, this._state, this));\n    } else if (started) {\n      this._start();\n    } else if (isAnimating(this) && !hasToChanged) {\n      this._pendingCalls.add(resolve);\n    } else {\n      resolve(getNoopResult(value));\n    }\n  }\n  /** Update the `animation.to` value, which might be a `FluidValue` */\n  _focus(value) {\n    const anim = this.animation;\n    if (value !== anim.to) {\n      if ((0, import_shared7.getFluidObservers)(this)) {\n        this._detach();\n      }\n      anim.to = value;\n      if ((0, import_shared7.getFluidObservers)(this)) {\n        this._attach();\n      }\n    }\n  }\n  _attach() {\n    let priority = 0;\n    const { to: to2 } = this.animation;\n    if ((0, import_shared7.hasFluidValue)(to2)) {\n      (0, import_shared7.addFluidObserver)(to2, this);\n      if (isFrameValue(to2)) {\n        priority = to2.priority + 1;\n      }\n    }\n    this.priority = priority;\n  }\n  _detach() {\n    const { to: to2 } = this.animation;\n    if ((0, import_shared7.hasFluidValue)(to2)) {\n      (0, import_shared7.removeFluidObserver)(to2, this);\n    }\n  }\n  /**\n   * Update the current value from outside the frameloop,\n   * and return the `Animated` node.\n   */\n  _set(arg, idle = true) {\n    const value = (0, import_shared7.getFluidValue)(arg);\n    if (!import_shared7.is.und(value)) {\n      const oldNode = (0, import_animated2.getAnimated)(this);\n      if (!oldNode || !(0, import_shared7.isEqual)(value, oldNode.getValue())) {\n        const nodeType = (0, import_animated2.getAnimatedType)(value);\n        if (!oldNode || oldNode.constructor != nodeType) {\n          (0, import_animated2.setAnimated)(this, nodeType.create(value));\n        } else {\n          oldNode.setValue(value);\n        }\n        if (oldNode) {\n          import_shared7.raf.batchedUpdates(() => {\n            this._onChange(value, idle);\n          });\n        }\n      }\n    }\n    return (0, import_animated2.getAnimated)(this);\n  }\n  _onStart() {\n    const anim = this.animation;\n    if (!anim.changed) {\n      anim.changed = true;\n      sendEvent(\n        this,\n        \"onStart\",\n        getFinishedResult(this, checkFinished(this, anim.to)),\n        this\n      );\n    }\n  }\n  _onChange(value, idle) {\n    if (!idle) {\n      this._onStart();\n      callProp(this.animation.onChange, value, this);\n    }\n    callProp(this.defaultProps.onChange, value, this);\n    super._onChange(value, idle);\n  }\n  // This method resets the animation state (even if already animating) to\n  // ensure the latest from/to range is used, and it also ensures this spring\n  // is added to the frameloop.\n  _start() {\n    const anim = this.animation;\n    (0, import_animated2.getAnimated)(this).reset((0, import_shared7.getFluidValue)(anim.to));\n    if (!anim.immediate) {\n      anim.fromValues = anim.values.map((node) => node.lastPosition);\n    }\n    if (!isAnimating(this)) {\n      setActiveBit(this, true);\n      if (!isPaused(this)) {\n        this._resume();\n      }\n    }\n  }\n  _resume() {\n    if (import_shared7.Globals.skipAnimation) {\n      this.finish();\n    } else {\n      import_shared7.frameLoop.start(this);\n    }\n  }\n  /**\n   * Exit the frameloop and notify `onRest` listeners.\n   *\n   * Always wrap `_stop` calls with `batchedUpdates`.\n   */\n  _stop(goal, cancel) {\n    if (isAnimating(this)) {\n      setActiveBit(this, false);\n      const anim = this.animation;\n      (0, import_shared7.each)(anim.values, (node) => {\n        node.done = true;\n      });\n      if (anim.toValues) {\n        anim.onChange = anim.onPause = anim.onResume = void 0;\n      }\n      (0, import_shared7.callFluidObservers)(this, {\n        type: \"idle\",\n        parent: this\n      });\n      const result = cancel ? getCancelledResult(this.get()) : getFinishedResult(this.get(), checkFinished(this, goal ?? anim.to));\n      (0, import_shared7.flushCalls)(this._pendingCalls, result);\n      if (anim.changed) {\n        anim.changed = false;\n        sendEvent(this, \"onRest\", result, this);\n      }\n    }\n  }\n};\nfunction checkFinished(target, to2) {\n  const goal = computeGoal(to2);\n  const value = computeGoal(target.get());\n  return (0, import_shared7.isEqual)(value, goal);\n}\nfunction createLoopUpdate(props, loop = props.loop, to2 = props.to) {\n  const loopRet = callProp(loop);\n  if (loopRet) {\n    const overrides = loopRet !== true && inferTo(loopRet);\n    const reverse = (overrides || props).reverse;\n    const reset = !overrides || overrides.reset;\n    return createUpdate({\n      ...props,\n      loop,\n      // Avoid updating default props when looping.\n      default: false,\n      // Never loop the `pause` prop.\n      pause: void 0,\n      // For the \"reverse\" prop to loop as expected, the \"to\" prop\n      // must be undefined. The \"reverse\" prop is ignored when the\n      // \"to\" prop is an array or function.\n      to: !reverse || isAsyncTo(to2) ? to2 : void 0,\n      // Ignore the \"from\" prop except on reset.\n      from: reset ? props.from : void 0,\n      reset,\n      // The \"loop\" prop can return a \"useSpring\" props object to\n      // override any of the original props.\n      ...overrides\n    });\n  }\n}\nfunction createUpdate(props) {\n  const { to: to2, from } = props = inferTo(props);\n  const keys = /* @__PURE__ */ new Set();\n  if (import_shared7.is.obj(to2)) findDefined(to2, keys);\n  if (import_shared7.is.obj(from)) findDefined(from, keys);\n  props.keys = keys.size ? Array.from(keys) : null;\n  return props;\n}\nfunction declareUpdate(props) {\n  const update2 = createUpdate(props);\n  if (import_shared7.is.und(update2.default)) {\n    update2.default = getDefaultProps(update2);\n  }\n  return update2;\n}\nfunction findDefined(values, keys) {\n  (0, import_shared7.eachProp)(values, (value, key) => value != null && keys.add(key));\n}\nvar ACTIVE_EVENTS = [\n  \"onStart\",\n  \"onRest\",\n  \"onChange\",\n  \"onPause\",\n  \"onResume\"\n];\nfunction mergeActiveFn(target, props, type) {\n  target.animation[type] = props[type] !== getDefaultProp(props, type) ? resolveProp(props[type], target.key) : void 0;\n}\nfunction sendEvent(target, type, ...args) {\n  target.animation[type]?.(...args);\n  target.defaultProps[type]?.(...args);\n}\n\n// src/Controller.ts\nvar import_shared8 = require(\"@react-spring/shared\");\nvar BATCHED_EVENTS = [\"onStart\", \"onChange\", \"onRest\"];\nvar nextId2 = 1;\nvar Controller = class {\n  constructor(props, flush3) {\n    this.id = nextId2++;\n    /** The animated values */\n    this.springs = {};\n    /** The queue of props passed to the `update` method. */\n    this.queue = [];\n    /** The counter for tracking `scheduleProps` calls */\n    this._lastAsyncId = 0;\n    /** The values currently being animated */\n    this._active = /* @__PURE__ */ new Set();\n    /** The values that changed recently */\n    this._changed = /* @__PURE__ */ new Set();\n    /** Equals false when `onStart` listeners can be called */\n    this._started = false;\n    /** State used by the `runAsync` function */\n    this._state = {\n      paused: false,\n      pauseQueue: /* @__PURE__ */ new Set(),\n      resumeQueue: /* @__PURE__ */ new Set(),\n      timeouts: /* @__PURE__ */ new Set()\n    };\n    /** The event queues that are flushed once per frame maximum */\n    this._events = {\n      onStart: /* @__PURE__ */ new Map(),\n      onChange: /* @__PURE__ */ new Map(),\n      onRest: /* @__PURE__ */ new Map()\n    };\n    this._onFrame = this._onFrame.bind(this);\n    if (flush3) {\n      this._flush = flush3;\n    }\n    if (props) {\n      this.start({ default: true, ...props });\n    }\n  }\n  /**\n   * Equals `true` when no spring values are in the frameloop, and\n   * no async animation is currently active.\n   */\n  get idle() {\n    return !this._state.asyncTo && Object.values(this.springs).every((spring) => {\n      return spring.idle && !spring.isDelayed && !spring.isPaused;\n    });\n  }\n  get item() {\n    return this._item;\n  }\n  set item(item) {\n    this._item = item;\n  }\n  /** Get the current values of our springs */\n  get() {\n    const values = {};\n    this.each((spring, key) => values[key] = spring.get());\n    return values;\n  }\n  /** Set the current values without animating. */\n  set(values) {\n    for (const key in values) {\n      const value = values[key];\n      if (!import_shared8.is.und(value)) {\n        this.springs[key].set(value);\n      }\n    }\n  }\n  /** Push an update onto the queue of each value. */\n  update(props) {\n    if (props) {\n      this.queue.push(createUpdate(props));\n    }\n    return this;\n  }\n  /**\n   * Start the queued animations for every spring, and resolve the returned\n   * promise once all queued animations have finished or been cancelled.\n   *\n   * When you pass a queue (instead of nothing), that queue is used instead of\n   * the queued animations added with the `update` method, which are left alone.\n   */\n  start(props) {\n    let { queue } = this;\n    if (props) {\n      queue = (0, import_shared8.toArray)(props).map(createUpdate);\n    } else {\n      this.queue = [];\n    }\n    if (this._flush) {\n      return this._flush(this, queue);\n    }\n    prepareKeys(this, queue);\n    return flushUpdateQueue(this, queue);\n  }\n  /** @internal */\n  stop(arg, keys) {\n    if (arg !== !!arg) {\n      keys = arg;\n    }\n    if (keys) {\n      const springs = this.springs;\n      (0, import_shared8.each)((0, import_shared8.toArray)(keys), (key) => springs[key].stop(!!arg));\n    } else {\n      stopAsync(this._state, this._lastAsyncId);\n      this.each((spring) => spring.stop(!!arg));\n    }\n    return this;\n  }\n  /** Freeze the active animation in time */\n  pause(keys) {\n    if (import_shared8.is.und(keys)) {\n      this.start({ pause: true });\n    } else {\n      const springs = this.springs;\n      (0, import_shared8.each)((0, import_shared8.toArray)(keys), (key) => springs[key].pause());\n    }\n    return this;\n  }\n  /** Resume the animation if paused. */\n  resume(keys) {\n    if (import_shared8.is.und(keys)) {\n      this.start({ pause: false });\n    } else {\n      const springs = this.springs;\n      (0, import_shared8.each)((0, import_shared8.toArray)(keys), (key) => springs[key].resume());\n    }\n    return this;\n  }\n  /** Call a function once per spring value */\n  each(iterator) {\n    (0, import_shared8.eachProp)(this.springs, iterator);\n  }\n  /** @internal Called at the end of every animation frame */\n  _onFrame() {\n    const { onStart, onChange, onRest } = this._events;\n    const active = this._active.size > 0;\n    const changed = this._changed.size > 0;\n    if (active && !this._started || changed && !this._started) {\n      this._started = true;\n      (0, import_shared8.flush)(onStart, ([onStart2, result]) => {\n        result.value = this.get();\n        onStart2(result, this, this._item);\n      });\n    }\n    const idle = !active && this._started;\n    const values = changed || idle && onRest.size ? this.get() : null;\n    if (changed && onChange.size) {\n      (0, import_shared8.flush)(onChange, ([onChange2, result]) => {\n        result.value = values;\n        onChange2(result, this, this._item);\n      });\n    }\n    if (idle) {\n      this._started = false;\n      (0, import_shared8.flush)(onRest, ([onRest2, result]) => {\n        result.value = values;\n        onRest2(result, this, this._item);\n      });\n    }\n  }\n  /** @internal */\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      this._changed.add(event.parent);\n      if (!event.idle) {\n        this._active.add(event.parent);\n      }\n    } else if (event.type == \"idle\") {\n      this._active.delete(event.parent);\n    } else return;\n    import_shared8.raf.onFrame(this._onFrame);\n  }\n};\nfunction flushUpdateQueue(ctrl, queue) {\n  return Promise.all(queue.map((props) => flushUpdate(ctrl, props))).then(\n    (results) => getCombinedResult(ctrl, results)\n  );\n}\nasync function flushUpdate(ctrl, props, isLoop) {\n  const { keys, to: to2, from, loop, onRest, onResolve } = props;\n  const defaults2 = import_shared8.is.obj(props.default) && props.default;\n  if (loop) {\n    props.loop = false;\n  }\n  if (to2 === false) props.to = null;\n  if (from === false) props.from = null;\n  const asyncTo = import_shared8.is.arr(to2) || import_shared8.is.fun(to2) ? to2 : void 0;\n  if (asyncTo) {\n    props.to = void 0;\n    props.onRest = void 0;\n    if (defaults2) {\n      defaults2.onRest = void 0;\n    }\n  } else {\n    (0, import_shared8.each)(BATCHED_EVENTS, (key) => {\n      const handler = props[key];\n      if (import_shared8.is.fun(handler)) {\n        const queue = ctrl[\"_events\"][key];\n        props[key] = ({ finished, cancelled }) => {\n          const result2 = queue.get(handler);\n          if (result2) {\n            if (!finished) result2.finished = false;\n            if (cancelled) result2.cancelled = true;\n          } else {\n            queue.set(handler, {\n              value: null,\n              finished: finished || false,\n              cancelled: cancelled || false\n            });\n          }\n        };\n        if (defaults2) {\n          defaults2[key] = props[key];\n        }\n      }\n    });\n  }\n  const state = ctrl[\"_state\"];\n  if (props.pause === !state.paused) {\n    state.paused = props.pause;\n    (0, import_shared8.flushCalls)(props.pause ? state.pauseQueue : state.resumeQueue);\n  } else if (state.paused) {\n    props.pause = true;\n  }\n  const promises = (keys || Object.keys(ctrl.springs)).map(\n    (key) => ctrl.springs[key].start(props)\n  );\n  const cancel = props.cancel === true || getDefaultProp(props, \"cancel\") === true;\n  if (asyncTo || cancel && state.asyncId) {\n    promises.push(\n      scheduleProps(++ctrl[\"_lastAsyncId\"], {\n        props,\n        state,\n        actions: {\n          pause: import_shared8.noop,\n          resume: import_shared8.noop,\n          start(props2, resolve) {\n            if (cancel) {\n              stopAsync(state, ctrl[\"_lastAsyncId\"]);\n              resolve(getCancelledResult(ctrl));\n            } else {\n              props2.onRest = onRest;\n              resolve(\n                runAsync(\n                  asyncTo,\n                  props2,\n                  state,\n                  ctrl\n                )\n              );\n            }\n          }\n        }\n      })\n    );\n  }\n  if (state.paused) {\n    await new Promise((resume) => {\n      state.resumeQueue.add(resume);\n    });\n  }\n  const result = getCombinedResult(ctrl, await Promise.all(promises));\n  if (loop && result.finished && !(isLoop && result.noop)) {\n    const nextProps = createLoopUpdate(props, loop, to2);\n    if (nextProps) {\n      prepareKeys(ctrl, [nextProps]);\n      return flushUpdate(ctrl, nextProps, true);\n    }\n  }\n  if (onResolve) {\n    import_shared8.raf.batchedUpdates(() => onResolve(result, ctrl, ctrl.item));\n  }\n  return result;\n}\nfunction getSprings(ctrl, props) {\n  const springs = { ...ctrl.springs };\n  if (props) {\n    (0, import_shared8.each)((0, import_shared8.toArray)(props), (props2) => {\n      if (import_shared8.is.und(props2.keys)) {\n        props2 = createUpdate(props2);\n      }\n      if (!import_shared8.is.obj(props2.to)) {\n        props2 = { ...props2, to: void 0 };\n      }\n      prepareSprings(springs, props2, (key) => {\n        return createSpring(key);\n      });\n    });\n  }\n  setSprings(ctrl, springs);\n  return springs;\n}\nfunction setSprings(ctrl, springs) {\n  (0, import_shared8.eachProp)(springs, (spring, key) => {\n    if (!ctrl.springs[key]) {\n      ctrl.springs[key] = spring;\n      (0, import_shared8.addFluidObserver)(spring, ctrl);\n    }\n  });\n}\nfunction createSpring(key, observer) {\n  const spring = new SpringValue();\n  spring.key = key;\n  if (observer) {\n    (0, import_shared8.addFluidObserver)(spring, observer);\n  }\n  return spring;\n}\nfunction prepareSprings(springs, props, create) {\n  if (props.keys) {\n    (0, import_shared8.each)(props.keys, (key) => {\n      const spring = springs[key] || (springs[key] = create(key));\n      spring[\"_prepareNode\"](props);\n    });\n  }\n}\nfunction prepareKeys(ctrl, queue) {\n  (0, import_shared8.each)(queue, (props) => {\n    prepareSprings(ctrl.springs, props, (key) => {\n      return createSpring(key, ctrl);\n    });\n  });\n}\n\n// src/SpringContext.tsx\nvar React = __toESM(require(\"react\"));\nvar import_react = require(\"react\");\nvar SpringContext = React.createContext({\n  pause: false,\n  immediate: false\n});\n\n// src/SpringRef.ts\nvar import_shared9 = require(\"@react-spring/shared\");\nvar SpringRef = () => {\n  const current = [];\n  const SpringRef2 = function(props) {\n    (0, import_shared9.deprecateDirectCall)();\n    const results = [];\n    (0, import_shared9.each)(current, (ctrl, i) => {\n      if (import_shared9.is.und(props)) {\n        results.push(ctrl.start());\n      } else {\n        const update2 = _getProps(props, ctrl, i);\n        if (update2) {\n          results.push(ctrl.start(update2));\n        }\n      }\n    });\n    return results;\n  };\n  SpringRef2.current = current;\n  SpringRef2.add = function(ctrl) {\n    if (!current.includes(ctrl)) {\n      current.push(ctrl);\n    }\n  };\n  SpringRef2.delete = function(ctrl) {\n    const i = current.indexOf(ctrl);\n    if (~i) current.splice(i, 1);\n  };\n  SpringRef2.pause = function() {\n    (0, import_shared9.each)(current, (ctrl) => ctrl.pause(...arguments));\n    return this;\n  };\n  SpringRef2.resume = function() {\n    (0, import_shared9.each)(current, (ctrl) => ctrl.resume(...arguments));\n    return this;\n  };\n  SpringRef2.set = function(values) {\n    (0, import_shared9.each)(current, (ctrl, i) => {\n      const update2 = import_shared9.is.fun(values) ? values(i, ctrl) : values;\n      if (update2) {\n        ctrl.set(update2);\n      }\n    });\n  };\n  SpringRef2.start = function(props) {\n    const results = [];\n    (0, import_shared9.each)(current, (ctrl, i) => {\n      if (import_shared9.is.und(props)) {\n        results.push(ctrl.start());\n      } else {\n        const update2 = this._getProps(props, ctrl, i);\n        if (update2) {\n          results.push(ctrl.start(update2));\n        }\n      }\n    });\n    return results;\n  };\n  SpringRef2.stop = function() {\n    (0, import_shared9.each)(current, (ctrl) => ctrl.stop(...arguments));\n    return this;\n  };\n  SpringRef2.update = function(props) {\n    (0, import_shared9.each)(current, (ctrl, i) => ctrl.update(this._getProps(props, ctrl, i)));\n    return this;\n  };\n  const _getProps = function(arg, ctrl, index) {\n    return import_shared9.is.fun(arg) ? arg(index, ctrl) : arg;\n  };\n  SpringRef2._getProps = _getProps;\n  return SpringRef2;\n};\n\n// src/hooks/useSprings.ts\nfunction useSprings(length, props, deps) {\n  const propsFn = import_shared10.is.fun(props) && props;\n  if (propsFn && !deps) deps = [];\n  const ref = (0, import_react2.useMemo)(\n    () => propsFn || arguments.length == 3 ? SpringRef() : void 0,\n    []\n  );\n  const layoutId = (0, import_react2.useRef)(0);\n  const forceUpdate = (0, import_shared10.useForceUpdate)();\n  const state = (0, import_react2.useMemo)(\n    () => ({\n      ctrls: [],\n      queue: [],\n      flush(ctrl, updates2) {\n        const springs2 = getSprings(ctrl, updates2);\n        const canFlushSync = layoutId.current > 0 && !state.queue.length && !Object.keys(springs2).some((key) => !ctrl.springs[key]);\n        return canFlushSync ? flushUpdateQueue(ctrl, updates2) : new Promise((resolve) => {\n          setSprings(ctrl, springs2);\n          state.queue.push(() => {\n            resolve(flushUpdateQueue(ctrl, updates2));\n          });\n          forceUpdate();\n        });\n      }\n    }),\n    []\n  );\n  const ctrls = (0, import_react2.useRef)([...state.ctrls]);\n  const updates = (0, import_react2.useRef)([]);\n  const prevLength = (0, import_shared10.usePrev)(length) || 0;\n  (0, import_react2.useMemo)(() => {\n    (0, import_shared10.each)(ctrls.current.slice(length, prevLength), (ctrl) => {\n      detachRefs(ctrl, ref);\n      ctrl.stop(true);\n    });\n    ctrls.current.length = length;\n    declareUpdates(prevLength, length);\n  }, [length]);\n  (0, import_react2.useMemo)(() => {\n    declareUpdates(0, Math.min(prevLength, length));\n  }, deps);\n  function declareUpdates(startIndex, endIndex) {\n    for (let i = startIndex; i < endIndex; i++) {\n      const ctrl = ctrls.current[i] || (ctrls.current[i] = new Controller(null, state.flush));\n      const update2 = propsFn ? propsFn(i, ctrl) : props[i];\n      if (update2) {\n        updates.current[i] = declareUpdate(update2);\n      }\n    }\n  }\n  const springs = ctrls.current.map(\n    (ctrl, i) => getSprings(ctrl, updates.current[i])\n  );\n  const context = (0, import_react2.useContext)(SpringContext);\n  const prevContext = (0, import_shared10.usePrev)(context);\n  const hasContext = context !== prevContext && hasProps(context);\n  (0, import_shared10.useIsomorphicLayoutEffect)(() => {\n    layoutId.current++;\n    state.ctrls = ctrls.current;\n    const { queue } = state;\n    if (queue.length) {\n      state.queue = [];\n      (0, import_shared10.each)(queue, (cb) => cb());\n    }\n    (0, import_shared10.each)(ctrls.current, (ctrl, i) => {\n      ref?.add(ctrl);\n      if (hasContext) {\n        ctrl.start({ default: context });\n      }\n      const update2 = updates.current[i];\n      if (update2) {\n        replaceRef(ctrl, update2.ref);\n        if (ctrl.ref) {\n          ctrl.queue.push(update2);\n        } else {\n          ctrl.start(update2);\n        }\n      }\n    });\n  });\n  (0, import_shared10.useOnce)(() => () => {\n    (0, import_shared10.each)(state.ctrls, (ctrl) => ctrl.stop(true));\n  });\n  const values = springs.map((x) => ({ ...x }));\n  return ref ? [values, ref] : values;\n}\n\n// src/hooks/useSpring.ts\nfunction useSpring(props, deps) {\n  const isFn = import_shared11.is.fun(props);\n  const [[values], ref] = useSprings(\n    1,\n    isFn ? props : [props],\n    isFn ? deps || [] : deps\n  );\n  return isFn || arguments.length == 2 ? [values, ref] : values;\n}\n\n// src/hooks/useSpringRef.ts\nvar import_react3 = require(\"react\");\nvar initSpringRef = () => SpringRef();\nvar useSpringRef = () => (0, import_react3.useState)(initSpringRef)[0];\n\n// src/hooks/useSpringValue.ts\nvar import_shared12 = require(\"@react-spring/shared\");\nvar useSpringValue = (initial, props) => {\n  const springValue = (0, import_shared12.useConstant)(() => new SpringValue(initial, props));\n  (0, import_shared12.useOnce)(() => () => {\n    springValue.stop();\n  });\n  return springValue;\n};\n\n// src/hooks/useTrail.ts\nvar import_shared13 = require(\"@react-spring/shared\");\nfunction useTrail(length, propsArg, deps) {\n  const propsFn = import_shared13.is.fun(propsArg) && propsArg;\n  if (propsFn && !deps) deps = [];\n  let reverse = true;\n  let passedRef = void 0;\n  const result = useSprings(\n    length,\n    (i, ctrl) => {\n      const props = propsFn ? propsFn(i, ctrl) : propsArg;\n      passedRef = props.ref;\n      reverse = reverse && props.reverse;\n      return props;\n    },\n    // Ensure the props function is called when no deps exist.\n    // This works around the 3 argument rule.\n    deps || [{}]\n  );\n  (0, import_shared13.useIsomorphicLayoutEffect)(() => {\n    (0, import_shared13.each)(result[1].current, (ctrl, i) => {\n      const parent = result[1].current[i + (reverse ? 1 : -1)];\n      replaceRef(ctrl, passedRef);\n      if (ctrl.ref) {\n        if (parent) {\n          ctrl.update({ to: parent.springs });\n        }\n        return;\n      }\n      if (parent) {\n        ctrl.start({ to: parent.springs });\n      } else {\n        ctrl.start();\n      }\n    });\n  }, deps);\n  if (propsFn || arguments.length == 3) {\n    const ref = passedRef ?? result[1];\n    ref[\"_getProps\"] = (propsArg2, ctrl, i) => {\n      const props = import_shared13.is.fun(propsArg2) ? propsArg2(i, ctrl) : propsArg2;\n      if (props) {\n        const parent = ref.current[i + (props.reverse ? 1 : -1)];\n        if (parent) props.to = parent.springs;\n        return props;\n      }\n    };\n    return result;\n  }\n  return result[0];\n}\n\n// src/hooks/useTransition.tsx\nvar React2 = __toESM(require(\"react\"));\nvar import_react4 = require(\"react\");\nvar import_shared14 = require(\"@react-spring/shared\");\nfunction useTransition(data, props, deps) {\n  const propsFn = import_shared14.is.fun(props) && props;\n  const {\n    reset,\n    sort,\n    trail = 0,\n    expires = true,\n    exitBeforeEnter = false,\n    onDestroyed,\n    ref: propsRef,\n    config: propsConfig\n  } = propsFn ? propsFn() : props;\n  const ref = (0, import_react4.useMemo)(\n    () => propsFn || arguments.length == 3 ? SpringRef() : void 0,\n    []\n  );\n  const items = (0, import_shared14.toArray)(data);\n  const transitions = [];\n  const usedTransitions = (0, import_react4.useRef)(null);\n  const prevTransitions = reset ? null : usedTransitions.current;\n  (0, import_shared14.useIsomorphicLayoutEffect)(() => {\n    usedTransitions.current = transitions;\n  });\n  (0, import_shared14.useOnce)(() => {\n    (0, import_shared14.each)(transitions, (t) => {\n      ref?.add(t.ctrl);\n      t.ctrl.ref = ref;\n    });\n    return () => {\n      (0, import_shared14.each)(usedTransitions.current, (t) => {\n        if (t.expired) {\n          clearTimeout(t.expirationId);\n        }\n        detachRefs(t.ctrl, ref);\n        t.ctrl.stop(true);\n      });\n    };\n  });\n  const keys = getKeys(items, propsFn ? propsFn() : props, prevTransitions);\n  const expired = reset && usedTransitions.current || [];\n  (0, import_shared14.useIsomorphicLayoutEffect)(\n    () => (0, import_shared14.each)(expired, ({ ctrl, item, key }) => {\n      detachRefs(ctrl, ref);\n      callProp(onDestroyed, item, key);\n    })\n  );\n  const reused = [];\n  if (prevTransitions)\n    (0, import_shared14.each)(prevTransitions, (t, i) => {\n      if (t.expired) {\n        clearTimeout(t.expirationId);\n        expired.push(t);\n      } else {\n        i = reused[i] = keys.indexOf(t.key);\n        if (~i) transitions[i] = t;\n      }\n    });\n  (0, import_shared14.each)(items, (item, i) => {\n    if (!transitions[i]) {\n      transitions[i] = {\n        key: keys[i],\n        item,\n        phase: \"mount\" /* MOUNT */,\n        ctrl: new Controller()\n      };\n      transitions[i].ctrl.item = item;\n    }\n  });\n  if (reused.length) {\n    let i = -1;\n    const { leave } = propsFn ? propsFn() : props;\n    (0, import_shared14.each)(reused, (keyIndex, prevIndex) => {\n      const t = prevTransitions[prevIndex];\n      if (~keyIndex) {\n        i = transitions.indexOf(t);\n        transitions[i] = { ...t, item: items[keyIndex] };\n      } else if (leave) {\n        transitions.splice(++i, 0, t);\n      }\n    });\n  }\n  if (import_shared14.is.fun(sort)) {\n    transitions.sort((a, b) => sort(a.item, b.item));\n  }\n  let delay = -trail;\n  const forceUpdate = (0, import_shared14.useForceUpdate)();\n  const defaultProps = getDefaultProps(props);\n  const changes = /* @__PURE__ */ new Map();\n  const exitingTransitions = (0, import_react4.useRef)(/* @__PURE__ */ new Map());\n  const forceChange = (0, import_react4.useRef)(false);\n  (0, import_shared14.each)(transitions, (t, i) => {\n    const key = t.key;\n    const prevPhase = t.phase;\n    const p = propsFn ? propsFn() : props;\n    let to2;\n    let phase;\n    const propsDelay = callProp(p.delay || 0, key);\n    if (prevPhase == \"mount\" /* MOUNT */) {\n      to2 = p.enter;\n      phase = \"enter\" /* ENTER */;\n    } else {\n      const isLeave = keys.indexOf(key) < 0;\n      if (prevPhase != \"leave\" /* LEAVE */) {\n        if (isLeave) {\n          to2 = p.leave;\n          phase = \"leave\" /* LEAVE */;\n        } else if (to2 = p.update) {\n          phase = \"update\" /* UPDATE */;\n        } else return;\n      } else if (!isLeave) {\n        to2 = p.enter;\n        phase = \"enter\" /* ENTER */;\n      } else return;\n    }\n    to2 = callProp(to2, t.item, i);\n    to2 = import_shared14.is.obj(to2) ? inferTo(to2) : { to: to2 };\n    if (!to2.config) {\n      const config2 = propsConfig || defaultProps.config;\n      to2.config = callProp(config2, t.item, i, phase);\n    }\n    delay += trail;\n    const payload = {\n      ...defaultProps,\n      // we need to add our props.delay value you here.\n      delay: propsDelay + delay,\n      ref: propsRef,\n      immediate: p.immediate,\n      // This prevents implied resets.\n      reset: false,\n      // Merge any phase-specific props.\n      ...to2\n    };\n    if (phase == \"enter\" /* ENTER */ && import_shared14.is.und(payload.from)) {\n      const p2 = propsFn ? propsFn() : props;\n      const from = import_shared14.is.und(p2.initial) || prevTransitions ? p2.from : p2.initial;\n      payload.from = callProp(from, t.item, i);\n    }\n    const { onResolve } = payload;\n    payload.onResolve = (result) => {\n      callProp(onResolve, result);\n      const transitions2 = usedTransitions.current;\n      const t2 = transitions2.find((t3) => t3.key === key);\n      if (!t2) return;\n      if (result.cancelled && t2.phase != \"update\" /* UPDATE */) {\n        return;\n      }\n      if (t2.ctrl.idle) {\n        const idle = transitions2.every((t3) => t3.ctrl.idle);\n        if (t2.phase == \"leave\" /* LEAVE */) {\n          const expiry = callProp(expires, t2.item);\n          if (expiry !== false) {\n            const expiryMs = expiry === true ? 0 : expiry;\n            t2.expired = true;\n            if (!idle && expiryMs > 0) {\n              if (expiryMs <= 2147483647)\n                t2.expirationId = setTimeout(forceUpdate, expiryMs);\n              return;\n            }\n          }\n        }\n        if (idle && transitions2.some((t3) => t3.expired)) {\n          exitingTransitions.current.delete(t2);\n          if (exitBeforeEnter) {\n            forceChange.current = true;\n          }\n          forceUpdate();\n        }\n      }\n    };\n    const springs = getSprings(t.ctrl, payload);\n    if (phase === \"leave\" /* LEAVE */ && exitBeforeEnter) {\n      exitingTransitions.current.set(t, { phase, springs, payload });\n    } else {\n      changes.set(t, { phase, springs, payload });\n    }\n  });\n  const context = (0, import_react4.useContext)(SpringContext);\n  const prevContext = (0, import_shared14.usePrev)(context);\n  const hasContext = context !== prevContext && hasProps(context);\n  (0, import_shared14.useIsomorphicLayoutEffect)(() => {\n    if (hasContext) {\n      (0, import_shared14.each)(transitions, (t) => {\n        t.ctrl.start({ default: context });\n      });\n    }\n  }, [context]);\n  (0, import_shared14.each)(changes, (_, t) => {\n    if (exitingTransitions.current.size) {\n      const ind = transitions.findIndex((state) => state.key === t.key);\n      transitions.splice(ind, 1);\n    }\n  });\n  (0, import_shared14.useIsomorphicLayoutEffect)(\n    () => {\n      (0, import_shared14.each)(\n        exitingTransitions.current.size ? exitingTransitions.current : changes,\n        ({ phase, payload }, t) => {\n          const { ctrl } = t;\n          t.phase = phase;\n          ref?.add(ctrl);\n          if (hasContext && phase == \"enter\" /* ENTER */) {\n            ctrl.start({ default: context });\n          }\n          if (payload) {\n            replaceRef(ctrl, payload.ref);\n            if ((ctrl.ref || ref) && !forceChange.current) {\n              ctrl.update(payload);\n            } else {\n              ctrl.start(payload);\n              if (forceChange.current) {\n                forceChange.current = false;\n              }\n            }\n          }\n        }\n      );\n    },\n    reset ? void 0 : deps\n  );\n  const renderTransitions = (render) => /* @__PURE__ */ React2.createElement(React2.Fragment, null, transitions.map((t, i) => {\n    const { springs } = changes.get(t) || t.ctrl;\n    const elem = render({ ...springs }, t.item, t, i);\n    return elem && elem.type ? /* @__PURE__ */ React2.createElement(\n      elem.type,\n      {\n        ...elem.props,\n        key: import_shared14.is.str(t.key) || import_shared14.is.num(t.key) ? t.key : t.ctrl.id,\n        ref: elem.ref\n      }\n    ) : elem;\n  }));\n  return ref ? [renderTransitions, ref] : renderTransitions;\n}\nvar nextKey = 1;\nfunction getKeys(items, { key, keys = key }, prevTransitions) {\n  if (keys === null) {\n    const reused = /* @__PURE__ */ new Set();\n    return items.map((item) => {\n      const t = prevTransitions && prevTransitions.find(\n        (t2) => t2.item === item && t2.phase !== \"leave\" /* LEAVE */ && !reused.has(t2)\n      );\n      if (t) {\n        reused.add(t);\n        return t.key;\n      }\n      return nextKey++;\n    });\n  }\n  return import_shared14.is.und(keys) ? items : import_shared14.is.fun(keys) ? items.map(keys) : (0, import_shared14.toArray)(keys);\n}\n\n// src/hooks/useScroll.ts\nvar import_shared15 = require(\"@react-spring/shared\");\nvar useScroll = ({\n  container,\n  ...springOptions\n} = {}) => {\n  const [scrollValues, api] = useSpring(\n    () => ({\n      scrollX: 0,\n      scrollY: 0,\n      scrollXProgress: 0,\n      scrollYProgress: 0,\n      ...springOptions\n    }),\n    []\n  );\n  (0, import_shared15.useIsomorphicLayoutEffect)(() => {\n    const cleanupScroll = (0, import_shared15.onScroll)(\n      ({ x, y }) => {\n        api.start({\n          scrollX: x.current,\n          scrollXProgress: x.progress,\n          scrollY: y.current,\n          scrollYProgress: y.progress\n        });\n      },\n      { container: container?.current || void 0 }\n    );\n    return () => {\n      (0, import_shared15.each)(Object.values(scrollValues), (value) => value.stop());\n      cleanupScroll();\n    };\n  }, []);\n  return scrollValues;\n};\n\n// src/hooks/useResize.ts\nvar import_shared16 = require(\"@react-spring/shared\");\nvar useResize = ({\n  container,\n  ...springOptions\n}) => {\n  const [sizeValues, api] = useSpring(\n    () => ({\n      width: 0,\n      height: 0,\n      ...springOptions\n    }),\n    []\n  );\n  (0, import_shared16.useIsomorphicLayoutEffect)(() => {\n    const cleanupScroll = (0, import_shared16.onResize)(\n      ({ width, height }) => {\n        api.start({\n          width,\n          height,\n          immediate: sizeValues.width.get() === 0 || sizeValues.height.get() === 0\n        });\n      },\n      { container: container?.current || void 0 }\n    );\n    return () => {\n      (0, import_shared16.each)(Object.values(sizeValues), (value) => value.stop());\n      cleanupScroll();\n    };\n  }, []);\n  return sizeValues;\n};\n\n// src/hooks/useInView.ts\nvar import_react5 = require(\"react\");\nvar import_shared17 = require(\"@react-spring/shared\");\nvar defaultThresholdOptions = {\n  any: 0,\n  all: 1\n};\nfunction useInView(props, args) {\n  const [isInView, setIsInView] = (0, import_react5.useState)(false);\n  const ref = (0, import_react5.useRef)(void 0);\n  const propsFn = import_shared17.is.fun(props) && props;\n  const springsProps = propsFn ? propsFn() : {};\n  const { to: to2 = {}, from = {}, ...restSpringProps } = springsProps;\n  const intersectionArguments = propsFn ? args : props;\n  const [springs, api] = useSpring(() => ({ from, ...restSpringProps }), []);\n  (0, import_shared17.useIsomorphicLayoutEffect)(() => {\n    const element = ref.current;\n    const {\n      root,\n      once,\n      amount = \"any\",\n      ...restArgs\n    } = intersectionArguments ?? {};\n    if (!element || once && isInView || typeof IntersectionObserver === \"undefined\")\n      return;\n    const activeIntersections = /* @__PURE__ */ new WeakMap();\n    const onEnter = () => {\n      if (to2) {\n        api.start(to2);\n      }\n      setIsInView(true);\n      const cleanup = () => {\n        if (from) {\n          api.start(from);\n        }\n        setIsInView(false);\n      };\n      return once ? void 0 : cleanup;\n    };\n    const handleIntersection = (entries) => {\n      entries.forEach((entry) => {\n        const onLeave = activeIntersections.get(entry.target);\n        if (entry.isIntersecting === Boolean(onLeave)) {\n          return;\n        }\n        if (entry.isIntersecting) {\n          const newOnLeave = onEnter();\n          if (import_shared17.is.fun(newOnLeave)) {\n            activeIntersections.set(entry.target, newOnLeave);\n          } else {\n            observer.unobserve(entry.target);\n          }\n        } else if (onLeave) {\n          onLeave();\n          activeIntersections.delete(entry.target);\n        }\n      });\n    };\n    const observer = new IntersectionObserver(handleIntersection, {\n      root: root && root.current || void 0,\n      threshold: typeof amount === \"number\" || Array.isArray(amount) ? amount : defaultThresholdOptions[amount],\n      ...restArgs\n    });\n    observer.observe(element);\n    return () => observer.unobserve(element);\n  }, [intersectionArguments]);\n  if (propsFn) {\n    return [ref, springs];\n  }\n  return [ref, isInView];\n}\n\n// src/components/Spring.tsx\nfunction Spring({ children, ...props }) {\n  return children(useSpring(props));\n}\n\n// src/components/Trail.tsx\nvar import_shared18 = require(\"@react-spring/shared\");\nfunction Trail({\n  items,\n  children,\n  ...props\n}) {\n  const trails = useTrail(items.length, props);\n  return items.map((item, index) => {\n    const result = children(item, index);\n    return import_shared18.is.fun(result) ? result(trails[index]) : result;\n  });\n}\n\n// src/components/Transition.tsx\nfunction Transition({\n  items,\n  children,\n  ...props\n}) {\n  return useTransition(items, props)(children);\n}\n\n// src/interpolate.ts\nvar import_shared20 = require(\"@react-spring/shared\");\n\n// src/Interpolation.ts\nvar import_shared19 = require(\"@react-spring/shared\");\nvar import_animated3 = require(\"@react-spring/animated\");\nvar Interpolation = class extends FrameValue {\n  constructor(source, args) {\n    super();\n    this.source = source;\n    /** Equals false when in the frameloop */\n    this.idle = true;\n    /** The inputs which are currently animating */\n    this._active = /* @__PURE__ */ new Set();\n    this.calc = (0, import_shared19.createInterpolator)(...args);\n    const value = this._get();\n    const nodeType = (0, import_animated3.getAnimatedType)(value);\n    (0, import_animated3.setAnimated)(this, nodeType.create(value));\n  }\n  advance(_dt) {\n    const value = this._get();\n    const oldValue = this.get();\n    if (!(0, import_shared19.isEqual)(value, oldValue)) {\n      (0, import_animated3.getAnimated)(this).setValue(value);\n      this._onChange(value, this.idle);\n    }\n    if (!this.idle && checkIdle(this._active)) {\n      becomeIdle(this);\n    }\n  }\n  _get() {\n    const inputs = import_shared19.is.arr(this.source) ? this.source.map(import_shared19.getFluidValue) : (0, import_shared19.toArray)((0, import_shared19.getFluidValue)(this.source));\n    return this.calc(...inputs);\n  }\n  _start() {\n    if (this.idle && !checkIdle(this._active)) {\n      this.idle = false;\n      (0, import_shared19.each)((0, import_animated3.getPayload)(this), (node) => {\n        node.done = false;\n      });\n      if (import_shared19.Globals.skipAnimation) {\n        import_shared19.raf.batchedUpdates(() => this.advance());\n        becomeIdle(this);\n      } else {\n        import_shared19.frameLoop.start(this);\n      }\n    }\n  }\n  // Observe our sources only when we're observed.\n  _attach() {\n    let priority = 1;\n    (0, import_shared19.each)((0, import_shared19.toArray)(this.source), (source) => {\n      if ((0, import_shared19.hasFluidValue)(source)) {\n        (0, import_shared19.addFluidObserver)(source, this);\n      }\n      if (isFrameValue(source)) {\n        if (!source.idle) {\n          this._active.add(source);\n        }\n        priority = Math.max(priority, source.priority + 1);\n      }\n    });\n    this.priority = priority;\n    this._start();\n  }\n  // Stop observing our sources once we have no observers.\n  _detach() {\n    (0, import_shared19.each)((0, import_shared19.toArray)(this.source), (source) => {\n      if ((0, import_shared19.hasFluidValue)(source)) {\n        (0, import_shared19.removeFluidObserver)(source, this);\n      }\n    });\n    this._active.clear();\n    becomeIdle(this);\n  }\n  /** @internal */\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      if (event.idle) {\n        this.advance();\n      } else {\n        this._active.add(event.parent);\n        this._start();\n      }\n    } else if (event.type == \"idle\") {\n      this._active.delete(event.parent);\n    } else if (event.type == \"priority\") {\n      this.priority = (0, import_shared19.toArray)(this.source).reduce(\n        (highest, parent) => Math.max(highest, (isFrameValue(parent) ? parent.priority : 0) + 1),\n        0\n      );\n    }\n  }\n};\nfunction isIdle(source) {\n  return source.idle !== false;\n}\nfunction checkIdle(active) {\n  return !active.size || Array.from(active).every(isIdle);\n}\nfunction becomeIdle(self) {\n  if (!self.idle) {\n    self.idle = true;\n    (0, import_shared19.each)((0, import_animated3.getPayload)(self), (node) => {\n      node.done = true;\n    });\n    (0, import_shared19.callFluidObservers)(self, {\n      type: \"idle\",\n      parent: self\n    });\n  }\n}\n\n// src/interpolate.ts\nvar to = (source, ...args) => new Interpolation(source, args);\nvar interpolate = (source, ...args) => ((0, import_shared20.deprecateInterpolate)(), new Interpolation(source, args));\n\n// src/globals.ts\nvar import_shared21 = require(\"@react-spring/shared\");\nimport_shared21.Globals.assign({\n  createStringInterpolator: import_shared21.createStringInterpolator,\n  to: (source, args) => new Interpolation(source, args)\n});\nvar update = import_shared21.frameLoop.advance;\n\n// src/index.ts\nvar import_shared22 = require(\"@react-spring/shared\");\n__reExport(src_exports, require(\"@react-spring/types\"), module.exports);\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  BailSignal,\n  Controller,\n  FrameValue,\n  Globals,\n  Interpolation,\n  Spring,\n  SpringContext,\n  SpringRef,\n  SpringValue,\n  Trail,\n  Transition,\n  config,\n  createInterpolator,\n  easings,\n  inferTo,\n  interpolate,\n  to,\n  update,\n  useChain,\n  useInView,\n  useIsomorphicLayoutEffect,\n  useReducedMotion,\n  useResize,\n  useScroll,\n  useSpring,\n  useSpringRef,\n  useSpringValue,\n  useSprings,\n  useTrail,\n  useTransition,\n  ...require(\"@react-spring/types\")\n});\n"], "names": [], "mappings": "AAAA;AACA,IAAI,WAAW,OAAO,MAAM;AAC5B,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,mBAAmB,OAAO,wBAAwB;AACtD,IAAI,oBAAoB,OAAO,mBAAmB;AAClD,IAAI,eAAe,OAAO,cAAc;AACxC,IAAI,eAAe,OAAO,SAAS,CAAC,cAAc;AAClD,IAAI,WAAW,CAAC,QAAQ;IACtB,IAAK,IAAI,QAAQ,IACf,UAAU,QAAQ,MAAM;QAAE,KAAK,GAAG,CAAC,KAAK;QAAE,YAAY;IAAK;AAC/D;AACA,IAAI,cAAc,CAAC,KAAK,MAAM,QAAQ;IACpC,IAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;QAClE,KAAK,IAAI,OAAO,kBAAkB,MAChC,IAAI,CAAC,aAAa,IAAI,CAAC,KAAK,QAAQ,QAAQ,QAC1C,UAAU,KAAK,KAAK;YAAE,KAAK,IAAM,IAAI,CAAC,IAAI;YAAE,YAAY,CAAC,CAAC,OAAO,iBAAiB,MAAM,IAAI,KAAK,KAAK,UAAU;QAAC;IACvH;IACA,OAAO;AACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,eAAiB,CAAC,YAAY,QAAQ,KAAK,YAAY,gBAAgB,YAAY,cAAc,KAAK,UAAU;AAC/I,IAAI,UAAU,CAAC,KAAK,YAAY,SAAW,CAAC,SAAS,OAAO,OAAO,SAAS,aAAa,QAAQ,CAAC,GAAG,YACnG,sEAAsE;IACtE,iEAAiE;IACjE,sEAAsE;IACtE,qEAAqE;IACrE,cAAc,CAAC,OAAO,CAAC,IAAI,UAAU,GAAG,UAAU,QAAQ,WAAW;QAAE,OAAO;QAAK,YAAY;IAAK,KAAK,QACzG,IACD;AACD,IAAI,eAAe,CAAC,MAAQ,YAAY,UAAU,CAAC,GAAG,cAAc;QAAE,OAAO;IAAK,IAAI;AAEtF,eAAe;AACf,IAAI,cAAc,CAAC;AACnB,SAAS,aAAa;IACpB,YAAY,IAAM;IAClB,YAAY,IAAM;IAClB,YAAY,IAAM;IAClB,SAAS,IAAM,gBAAgB,OAAO;IACtC,eAAe,IAAM;IACrB,QAAQ,IAAM;IACd,eAAe,IAAM;IACrB,WAAW,IAAM;IACjB,aAAa,IAAM;IACnB,OAAO,IAAM;IACb,YAAY,IAAM;IAClB,QAAQ,IAAM;IACd,oBAAoB,IAAM,gBAAgB,kBAAkB;IAC5D,SAAS,IAAM,gBAAgB,OAAO;IACtC,SAAS,IAAM;IACf,aAAa,IAAM;IACnB,IAAI,IAAM;IACV,QAAQ,IAAM;IACd,UAAU,IAAM;IAChB,WAAW,IAAM;IACjB,2BAA2B,IAAM,gBAAgB,yBAAyB;IAC1E,kBAAkB,IAAM,gBAAgB,gBAAgB;IACxD,WAAW,IAAM;IACjB,WAAW,IAAM;IACjB,WAAW,IAAM;IACjB,cAAc,IAAM;IACpB,gBAAgB,IAAM;IACtB,YAAY,IAAM;IAClB,UAAU,IAAM;IAChB,eAAe,IAAM;AACvB;AACA,OAAO,OAAO,GAAG,aAAa;AAE9B,wBAAwB;AACxB,IAAI;AAEJ,iBAAiB;AACjB,IAAI;AACJ,SAAS,SAAS,KAAK,EAAE,GAAG,IAAI;IAC9B,OAAO,cAAc,EAAE,CAAC,GAAG,CAAC,SAAS,SAAS,QAAQ;AACxD;AACA,IAAI,YAAY,CAAC,OAAO,MAAQ,UAAU,QAAQ,CAAC,CAAC,CAAC,OAAO,SAAS,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,SAAS,MAAM,OAAO,CAAC,GAAG,cAAc,OAAO,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC;AACjK,IAAI,cAAc,CAAC,MAAM,MAAQ,cAAc,EAAE,CAAC,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,IAAI,GAAG;AACjF,IAAI,iBAAiB,CAAC,OAAO,MAAQ,MAAM,OAAO,KAAK,OAAO,KAAK,CAAC,IAAI,GAAG,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI,GAAG,KAAK;AACrH,IAAI,gBAAgB,CAAC,QAAU;AAC/B,IAAI,kBAAkB,CAAC,OAAO,YAAY,aAAa;IACrD,IAAI,OAAO;IACX,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,KAAK,MAAM;QAC3C,QAAQ,MAAM,OAAO;QACrB,OAAO,OAAO,IAAI,CAAC;IACrB;IACA,MAAM,YAAY,CAAC;IACnB,KAAK,MAAM,OAAO,KAAM;QACtB,MAAM,QAAQ,UAAU,KAAK,CAAC,IAAI,EAAE;QACpC,IAAI,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,QAAQ;YAChC,SAAS,CAAC,IAAI,GAAG;QACnB;IACF;IACA,OAAO;AACT;AACA,IAAI,gBAAgB;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,IAAI,iBAAiB;IACnB,QAAQ;IACR,MAAM;IACN,IAAI;IACJ,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,SAAS;IACT,WAAW;IACX,SAAS;IACT,OAAO;IACP,SAAS;IACT,SAAS;IACT,UAAU;IACV,SAAS;IACT,UAAU;IACV,QAAQ;IACR,WAAW;IACX,mBAAmB;IACnB,OAAO;IACP,OAAO;IACP,MAAM;IACN,SAAS;IACT,SAAS;IACT,OAAO;IACP,QAAQ;IACR,OAAO;IACP,UAAU;IACV,aAAa;IACb,iBAAiB;IACjB,MAAM;IACN,QAAQ;IACR,UAAU;AACZ;AACA,SAAS,gBAAgB,KAAK;IAC5B,MAAM,UAAU,CAAC;IACjB,IAAI,QAAQ;IACZ,CAAC,GAAG,cAAc,QAAQ,EAAE,OAAO,CAAC,OAAO;QACzC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;YACzB,OAAO,CAAC,KAAK,GAAG;YAChB;QACF;IACF;IACA,IAAI,OAAO;QACT,OAAO;IACT;AACF;AACA,SAAS,QAAQ,KAAK;IACpB,MAAM,MAAM,gBAAgB;IAC5B,IAAI,KAAK;QACP,MAAM,MAAM;YAAE,IAAI;QAAI;QACtB,CAAC,GAAG,cAAc,QAAQ,EAAE,OAAO,CAAC,KAAK,MAAQ,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG;QAC9E,OAAO;IACT;IACA,OAAO;QAAE,GAAG,KAAK;IAAC;AACpB;AACA,SAAS,YAAY,KAAK;IACxB,QAAQ,CAAC,GAAG,cAAc,aAAa,EAAE;IACzC,OAAO,cAAc,EAAE,CAAC,GAAG,CAAC,SAAS,MAAM,GAAG,CAAC,eAAe,CAAC,GAAG,cAAc,gBAAgB,EAAE,SAAS,cAAc,OAAO,CAAC,wBAAwB,CAAC;QACxJ,OAAO;YAAC;YAAG;SAAE;QACb,QAAQ;YAAC;YAAO;SAAM;IACxB,GAAG,KAAK;AACV;AACA,SAAS,SAAS,KAAK;IACrB,IAAK,MAAM,KAAK,MAAO,OAAO;IAC9B,OAAO;AACT;AACA,SAAS,UAAU,GAAG;IACpB,OAAO,cAAc,EAAE,CAAC,GAAG,CAAC,QAAQ,cAAc,EAAE,CAAC,GAAG,CAAC,QAAQ,cAAc,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC9F;AACA,SAAS,WAAW,IAAI,EAAE,GAAG;IAC3B,KAAK,GAAG,EAAE,OAAO;IACjB,KAAK,OAAO;AACd;AACA,SAAS,WAAW,IAAI,EAAE,GAAG;IAC3B,IAAI,OAAO,KAAK,GAAG,KAAK,KAAK;QAC3B,KAAK,GAAG,EAAE,OAAO;QACjB,IAAI,GAAG,CAAC;QACR,KAAK,GAAG,GAAG;IACb;AACF;AAEA,wBAAwB;AACxB,SAAS,SAAS,IAAI,EAAE,SAAS,EAAE,YAAY,GAAG;IAChD,CAAC,GAAG,eAAe,yBAAyB,EAAE;QAC5C,IAAI,WAAW;YACb,IAAI,YAAY;YAChB,CAAC,GAAG,eAAe,IAAI,EAAE,MAAM,CAAC,KAAK;gBACnC,MAAM,cAAc,IAAI,OAAO;gBAC/B,IAAI,YAAY,MAAM,EAAE;oBACtB,IAAI,QAAQ,YAAY,SAAS,CAAC,EAAE;oBACpC,IAAI,MAAM,QAAQ,QAAQ;yBACrB,YAAY;oBACjB,CAAC,GAAG,eAAe,IAAI,EAAE,aAAa,CAAC;wBACrC,CAAC,GAAG,eAAe,IAAI,EAAE,KAAK,KAAK,EAAE,CAAC;4BACpC,MAAM,oBAAoB,MAAM,KAAK;4BACrC,MAAM,KAAK,GAAG,CAAC,MAAQ,QAAQ,SAAS,qBAAqB,GAAG;wBAClE;oBACF;oBACA,IAAI,KAAK;gBACX;YACF;QACF,OAAO;YACL,IAAI,IAAI,QAAQ,OAAO;YACvB,CAAC,GAAG,eAAe,IAAI,EAAE,MAAM,CAAC;gBAC9B,MAAM,cAAc,IAAI,OAAO;gBAC/B,IAAI,YAAY,MAAM,EAAE;oBACtB,MAAM,SAAS,YAAY,GAAG,CAAC,CAAC;wBAC9B,MAAM,IAAI,KAAK,KAAK;wBACpB,KAAK,KAAK,GAAG,EAAE;wBACf,OAAO;oBACT;oBACA,IAAI,EAAE,IAAI,CAAC;wBACT,CAAC,GAAG,eAAe,IAAI,EACrB,aACA,CAAC,MAAM,IAAM,CAAC,GAAG,eAAe,IAAI,EAAE,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,UAAY,KAAK,KAAK,CAAC,IAAI,CAAC;wBAEtF,OAAO,QAAQ,GAAG,CAAC,IAAI,KAAK;oBAC9B;gBACF;YACF;QACF;IACF;AACF;AAEA,yBAAyB;AACzB,IAAI;AAEJ,0BAA0B;AAC1B,IAAI;AACJ,IAAI;AAEJ,qBAAqB;AACrB,IAAI;AACJ,IAAI;AAEJ,yBAAyB;AACzB,IAAI;AAEJ,mBAAmB;AACnB,IAAI,SAAS;IACX,SAAS;QAAE,SAAS;QAAK,UAAU;IAAG;IACtC,QAAQ;QAAE,SAAS;QAAK,UAAU;IAAG;IACrC,QAAQ;QAAE,SAAS;QAAK,UAAU;IAAG;IACrC,OAAO;QAAE,SAAS;QAAK,UAAU;IAAG;IACpC,MAAM;QAAE,SAAS;QAAK,UAAU;IAAG;IACnC,UAAU;QAAE,SAAS;QAAK,UAAU;IAAI;AAC1C;AAEA,yBAAyB;AACzB,IAAI,WAAW;IACb,GAAG,OAAO,OAAO;IACjB,MAAM;IACN,SAAS;IACT,QAAQ,eAAe,OAAO,CAAC,MAAM;IACrC,OAAO;AACT;AACA,IAAI,kBAAkB;IACpB,aAAc;QACZ;;;;KAIC,GACD,IAAI,CAAC,QAAQ,GAAG;QAChB,OAAO,MAAM,CAAC,IAAI,EAAE;IACtB;AACF;AACA,SAAS,YAAY,OAAO,EAAE,SAAS,EAAE,aAAa;IACpD,IAAI,eAAe;QACjB,gBAAgB;YAAE,GAAG,aAAa;QAAC;QACnC,eAAe,eAAe;QAC9B,YAAY;YAAE,GAAG,aAAa;YAAE,GAAG,SAAS;QAAC;IAC/C;IACA,eAAe,SAAS;IACxB,OAAO,MAAM,CAAC,SAAS;IACvB,IAAK,MAAM,OAAO,SAAU;QAC1B,IAAI,OAAO,CAAC,IAAI,IAAI,MAAM;YACxB,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI;QAC9B;IACF;IACA,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;IAC7B,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,YAAY;QACrC,IAAI,YAAY,MAAM,YAAY;QAClC,IAAI,UAAU,GAAG,UAAU;QAC3B,QAAQ,OAAO,GAAG,KAAK,GAAG,CAAC,IAAI,KAAK,EAAE,GAAG,WAAW,KAAK;QACzD,QAAQ,QAAQ,GAAG,IAAI,KAAK,EAAE,GAAG,UAAU,OAAO;IACpD;IACA,OAAO;AACT;AACA,SAAS,eAAe,OAAO,EAAE,KAAK;IACpC,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,GAAG;QACvC,QAAQ,QAAQ,GAAG,KAAK;IAC1B,OAAO;QACL,MAAM,kBAAkB,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM,OAAO,KAAK,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM,QAAQ;QACtG,IAAI,mBAAmB,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM,SAAS,KAAK,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM,OAAO,KAAK,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM,IAAI,GAAG;YAC7I,QAAQ,QAAQ,GAAG,KAAK;YACxB,QAAQ,KAAK,GAAG,KAAK;QACvB;QACA,IAAI,iBAAiB;YACnB,QAAQ,SAAS,GAAG,KAAK;QAC3B;IACF;AACF;AAEA,mBAAmB;AACnB,IAAI,aAAa,EAAE;AACnB,IAAI,YAAY;IACd,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,MAAM,GAAG,IAAI;QAClB,IAAI,CAAC,SAAS,GAAG;IACnB;AACF;AAEA,uBAAuB;AACvB,IAAI;AACJ,SAAS,cAAc,MAAM,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE;IACzE,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI;QACJ,IAAI;QACJ,IAAI,SAAS,UAAU,MAAM,MAAM,IAAI,cAAc,QAAQ;QAC7D,IAAI,QAAQ;YACV;QACF,OAAO;YACL,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,GAAG;gBACvC,MAAM,MAAM,GAAG,UAAU,MAAM,KAAK,EAAE;YACxC;YACA,IAAI,QAAQ,cAAc;YAC1B,IAAI,UAAU,MAAM;gBAClB,QAAQ,MAAM,MAAM,IAAI,UAAU,OAAO;YAC3C;YACA,QAAQ,SAAS,MAAM,KAAK,IAAI,GAAG;YACnC,IAAI,OAAO;gBACT,MAAM,WAAW,CAAC,GAAG,CAAC;gBACtB,QAAQ,KAAK;YACf,OAAO;gBACL,QAAQ,MAAM;gBACd;YACF;QACF;QACA,SAAS;YACP,MAAM,WAAW,CAAC,GAAG,CAAC;YACtB,MAAM,QAAQ,CAAC,MAAM,CAAC;YACtB,QAAQ,MAAM;YACd,QAAQ,QAAQ,IAAI,GAAG,eAAe,GAAG,CAAC,GAAG;QAC/C;QACA,SAAS;YACP,IAAI,QAAQ,KAAK,CAAC,eAAe,OAAO,CAAC,aAAa,EAAE;gBACtD,MAAM,OAAO,GAAG;gBAChB,UAAU,eAAe,GAAG,CAAC,UAAU,CAAC,SAAS;gBACjD,MAAM,UAAU,CAAC,GAAG,CAAC;gBACrB,MAAM,QAAQ,CAAC,GAAG,CAAC;YACrB,OAAO;gBACL;YACF;QACF;QACA,SAAS;YACP,IAAI,MAAM,OAAO,EAAE;gBACjB,MAAM,OAAO,GAAG;YAClB;YACA,MAAM,UAAU,CAAC,MAAM,CAAC;YACxB,MAAM,QAAQ,CAAC,MAAM,CAAC;YACtB,IAAI,UAAU,CAAC,MAAM,QAAQ,IAAI,CAAC,GAAG;gBACnC,SAAS;YACX;YACA,IAAI;gBACF,QAAQ,KAAK,CAAC;oBAAE,GAAG,KAAK;oBAAE;oBAAQ;gBAAO,GAAG;YAC9C,EAAE,OAAO,KAAK;gBACZ,OAAO;YACT;QACF;IACF;AACF;AAEA,kBAAkB;AAClB,IAAI;AAEJ,yBAAyB;AACzB,IAAI,oBAAoB,CAAC,QAAQ,UAAY,QAAQ,MAAM,IAAI,IAAI,OAAO,CAAC,EAAE,GAAG,QAAQ,IAAI,CAAC,CAAC,SAAW,OAAO,SAAS,IAAI,mBAAmB,OAAO,GAAG,MAAM,QAAQ,KAAK,CAAC,CAAC,SAAW,OAAO,IAAI,IAAI,cAAc,OAAO,GAAG,MAAM,kBACrO,OAAO,GAAG,IACV,QAAQ,KAAK,CAAC,CAAC,SAAW,OAAO,QAAQ;AAE3C,IAAI,gBAAgB,CAAC,QAAU,CAAC;QAC9B;QACA,MAAM;QACN,UAAU;QACV,WAAW;IACb,CAAC;AACD,IAAI,oBAAoB,CAAC,OAAO,UAAU,YAAY,KAAK,GAAK,CAAC;QAC/D;QACA;QACA;IACF,CAAC;AACD,IAAI,qBAAqB,CAAC,QAAU,CAAC;QACnC;QACA,WAAW;QACX,UAAU;IACZ,CAAC;AAED,kBAAkB;AAClB,SAAS,SAAS,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM;IACzC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IACrC,MAAM,EAAE,SAAS,MAAM,EAAE,SAAS,WAAW,EAAE,GAAG;IAClD,IAAI,CAAC,YAAY,QAAQ,UAAU,CAAC,MAAM,KAAK,EAAE;QAC/C,OAAO;IACT;IACA,OAAO,MAAM,OAAO,GAAG,CAAC;QACtB,MAAM,OAAO,GAAG;QAChB,MAAM,OAAO,GAAG;QAChB,MAAM,eAAe,gBACnB,OACA,CAAC,OAAO,MACN,4EAA4E;YAC5E,QAAQ,WAAW,KAAK,IAAI;QAGhC,IAAI;QACJ,IAAI;QACJ,MAAM,cAAc,IAAI,QACtB,CAAC,SAAS,SAAW,CAAC,cAAc,SAAS,OAAO,MAAM;QAE5D,MAAM,cAAc,CAAC;YACnB,MAAM,aACJ,+CAA+C;YAC/C,UAAU,CAAC,MAAM,QAAQ,IAAI,CAAC,KAAK,mBAAmB,WAAW,oCAAoC;YACrG,WAAW,MAAM,OAAO,IAAI,kBAAkB,QAAQ;YAExD,IAAI,YAAY;gBACd,WAAW,MAAM,GAAG;gBACpB,KAAK;gBACL,MAAM;YACR;QACF;QACA,MAAM,UAAU,CAAC,MAAM;YACrB,MAAM,aAAa,IAAI;YACvB,MAAM,sBAAsB,IAAI;YAChC,OAAO,CAAC;gBACN,IAAI,eAAe,OAAO,CAAC,aAAa,EAAE;oBACxC,UAAU;oBACV,oBAAoB,MAAM,GAAG,kBAAkB,QAAQ;oBACvD,KAAK;oBACL,MAAM;gBACR;gBACA,YAAY;gBACZ,MAAM,SAAS,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ;oBAAE,GAAG,IAAI;gBAAC,IAAI;oBAAE,GAAG,IAAI;oBAAE,IAAI;gBAAK;gBAC/E,OAAO,QAAQ,GAAG;gBAClB,CAAC,GAAG,eAAe,QAAQ,EAAE,cAAc,CAAC,OAAO;oBACjD,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;wBACtC,MAAM,CAAC,IAAI,GAAG;oBAChB;gBACF;gBACA,MAAM,UAAU,MAAM,OAAO,KAAK,CAAC;gBACnC,YAAY;gBACZ,IAAI,MAAM,MAAM,EAAE;oBAChB,MAAM,IAAI,QAAQ,CAAC;wBACjB,MAAM,WAAW,CAAC,GAAG,CAAC;oBACxB;gBACF;gBACA,OAAO;YACT,CAAC;QACH;QACA,IAAI;QACJ,IAAI,eAAe,OAAO,CAAC,aAAa,EAAE;YACxC,UAAU;YACV,OAAO,kBAAkB,QAAQ;QACnC;QACA,IAAI;YACF,IAAI;YACJ,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM;gBAC9B,YAAY,CAAC,OAAO;oBAClB,KAAK,MAAM,UAAU,MAAO;wBAC1B,MAAM,QAAQ;oBAChB;gBACF,CAAC,EAAE;YACL,OAAO;gBACL,YAAY,QAAQ,OAAO,CAAC,IAAI,SAAS,OAAO,IAAI,CAAC,IAAI,CAAC;YAC5D;YACA,MAAM,QAAQ,GAAG,CAAC;gBAAC,UAAU,IAAI,CAAC;gBAAc;aAAY;YAC5D,SAAS,kBAAkB,OAAO,GAAG,IAAI,MAAM;QACjD,EAAE,OAAO,KAAK;YACZ,IAAI,eAAe,YAAY;gBAC7B,SAAS,IAAI,MAAM;YACrB,OAAO,IAAI,eAAe,qBAAqB;gBAC7C,SAAS,IAAI,MAAM;YACrB,OAAO;gBACL,MAAM;YACR;QACF,SAAU;YACR,IAAI,UAAU,MAAM,OAAO,EAAE;gBAC3B,MAAM,OAAO,GAAG;gBAChB,MAAM,OAAO,GAAG,WAAW,SAAS,KAAK;gBACzC,MAAM,OAAO,GAAG,WAAW,cAAc,KAAK;YAChD;QACF;QACA,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,SAAS;YACjC,eAAe,GAAG,CAAC,cAAc,CAAC;gBAChC,OAAO,QAAQ,QAAQ,OAAO,IAAI;YACpC;QACF;QACA,OAAO;IACT,CAAC;AACH;AACA,SAAS,UAAU,KAAK,EAAE,QAAQ;IAChC,CAAC,GAAG,eAAe,KAAK,EAAE,MAAM,QAAQ,EAAE,CAAC,IAAM,EAAE,MAAM;IACzD,MAAM,UAAU,CAAC,KAAK;IACtB,MAAM,WAAW,CAAC,KAAK;IACvB,MAAM,OAAO,GAAG,MAAM,OAAO,GAAG,MAAM,OAAO,GAAG,KAAK;IACrD,IAAI,UAAU,MAAM,QAAQ,GAAG;AACjC;AACA,IAAI,aAAa,cAAc;IAC7B,aAAc;QACZ,KAAK,CACH;IAEJ;AACF;AACA,IAAI,sBAAsB,cAAc;IACtC,aAAc;QACZ,KAAK,CAAC;IACR;AACF;AAEA,oBAAoB;AACpB,IAAI;AACJ,IAAI;AACJ,IAAI,eAAe,CAAC,QAAU,iBAAiB;AAC/C,IAAI,SAAS;AACb,IAAI,aAAa,cAAc,eAAe,UAAU;IACtD,aAAc;QACZ,KAAK,IAAI;QACT,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,SAAS,GAAG;IACnB;IACA,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,SAAS;IACvB;IACA,IAAI,SAAS,QAAQ,EAAE;QACrB,IAAI,IAAI,CAAC,SAAS,IAAI,UAAU;YAC9B,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,iBAAiB,CAAC;QACzB;IACF;IACA,0BAA0B,GAC1B,MAAM;QACJ,MAAM,OAAO,CAAC,GAAG,gBAAgB,WAAW,EAAE,IAAI;QAClD,OAAO,QAAQ,KAAK,QAAQ;IAC9B;IACA,yDAAyD,GACzD,GAAG,GAAG,IAAI,EAAE;QACV,OAAO,eAAe,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE;IACzC;IACA,6CAA6C,GAC7C,YAAY,GAAG,IAAI,EAAE;QACnB,CAAC,GAAG,eAAe,oBAAoB;QACvC,OAAO,eAAe,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE;IACzC;IACA,SAAS;QACP,OAAO,IAAI,CAAC,GAAG;IACjB;IACA,cAAc,KAAK,EAAE;QACnB,IAAI,SAAS,GAAG,IAAI,CAAC,OAAO;IAC9B;IACA,gBAAgB,KAAK,EAAE;QACrB,IAAI,SAAS,GAAG,IAAI,CAAC,OAAO;IAC9B;IACA,0CAA0C,GAC1C,UAAU,CACV;IACA,2CAA2C,GAC3C,UAAU,CACV;IACA,0CAA0C,GAC1C,UAAU,KAAK,EAAE,OAAO,KAAK,EAAE;QAC7B,CAAC,GAAG,eAAe,kBAAkB,EAAE,IAAI,EAAE;YAC3C,MAAM;YACN,QAAQ,IAAI;YACZ;YACA;QACF;IACF;IACA,6CAA6C,GAC7C,kBAAkB,QAAQ,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACd,eAAe,SAAS,CAAC,IAAI,CAAC,IAAI;QACpC;QACA,CAAC,GAAG,eAAe,kBAAkB,EAAE,IAAI,EAAE;YAC3C,MAAM;YACN,QAAQ,IAAI;YACZ;QACF;IACF;AACF;AAEA,qBAAqB;AACrB,IAAI,KAAK,OAAO,GAAG,CAAC;AACpB,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,YAAY;AAChB,IAAI,cAAc,CAAC,SAAW,CAAC,MAAM,CAAC,GAAG,GAAG,YAAY,IAAI;AAC5D,IAAI,cAAc,CAAC,SAAW,CAAC,MAAM,CAAC,GAAG,GAAG,YAAY,IAAI;AAC5D,IAAI,WAAW,CAAC,SAAW,CAAC,MAAM,CAAC,GAAG,GAAG,SAAS,IAAI;AACtD,IAAI,eAAe,CAAC,QAAQ,SAAW,SAAS,MAAM,CAAC,GAAG,IAAI,eAAe,eAAe,MAAM,CAAC,GAAG,IAAI,CAAC;AAC3G,IAAI,eAAe,CAAC,QAAQ,SAAW,SAAS,MAAM,CAAC,GAAG,IAAI,YAAY,MAAM,CAAC,GAAG,IAAI,CAAC;AAEzF,qBAAqB;AACrB,IAAI,cAAc,cAAc;IAC9B,YAAY,IAAI,EAAE,IAAI,CAAE;QACtB,KAAK;QACL,wBAAwB,GACxB,IAAI,CAAC,SAAS,GAAG,IAAI;QACrB,gDAAgD,GAChD,IAAI,CAAC,YAAY,GAAG,CAAC;QACrB,mCAAmC,GACnC,IAAI,CAAC,MAAM,GAAG;YACZ,QAAQ;YACR,SAAS;YACT,YAAY,aAAa,GAAG,IAAI;YAChC,aAAa,aAAa,GAAG,IAAI;YACjC,UAAU,aAAa,GAAG,IAAI;QAChC;QACA,mDAAmD,GACnD,IAAI,CAAC,aAAa,GAAG,aAAa,GAAG,IAAI;QACzC,mDAAmD,GACnD,IAAI,CAAC,WAAW,GAAG;QACnB,6DAA6D,GAC7D,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,OAAO;YAChE,MAAM,QAAQ,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ;gBAAE,GAAG,IAAI;YAAC,IAAI;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAK;YAChF,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM,OAAO,GAAG;gBACxC,MAAM,OAAO,GAAG;YAClB;YACA,IAAI,CAAC,KAAK,CAAC;QACb;IACF;IACA,kDAAkD,GAClD,IAAI,OAAO;QACT,OAAO,CAAC,CAAC,YAAY,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,SAAS,IAAI;IACrE;IACA,IAAI,OAAO;QACT,OAAO,CAAC,GAAG,eAAe,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE;IAC5D;IACA,IAAI,WAAW;QACb,MAAM,OAAO,CAAC,GAAG,iBAAiB,WAAW,EAAE,IAAI;QACnD,OAAO,gBAAgB,iBAAiB,aAAa,GAAG,KAAK,YAAY,IAAI,IAAI,KAAK,UAAU,GAAG,GAAG,CAAC,CAAC,QAAU,MAAM,YAAY,IAAI;IAC1I;IACA;;GAEC,GACD,IAAI,cAAc;QAChB,OAAO,YAAY,IAAI;IACzB;IACA;;;GAGC,GACD,IAAI,cAAc;QAChB,OAAO,YAAY,IAAI;IACzB;IACA;;GAEC,GACD,IAAI,WAAW;QACb,OAAO,SAAS,IAAI;IACtB;IACA;;;GAGC,GACD,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO;IAC5B;IACA,8DAA8D,GAC9D,QAAQ,EAAE,EAAE;QACV,IAAI,OAAO;QACX,IAAI,UAAU;QACd,MAAM,OAAO,IAAI,CAAC,SAAS;QAC3B,IAAI,EAAE,QAAQ,EAAE,GAAG;QACnB,MAAM,EAAE,QAAQ,OAAO,EAAE,GAAG;QAC5B,MAAM,UAAU,CAAC,GAAG,iBAAiB,UAAU,EAAE,KAAK,EAAE;QACxD,IAAI,CAAC,WAAW,CAAC,GAAG,eAAe,aAAa,EAAE,KAAK,EAAE,GAAG;YAC1D,WAAW,CAAC,GAAG,eAAe,OAAO,EAAE,CAAC,GAAG,eAAe,aAAa,EAAE,KAAK,EAAE;QAClF;QACA,KAAK,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO;YAC1B,IAAI,MAAM,IAAI,EAAE;YAChB,MAAM,MACJ,0CAA0C;YAC1C,MAAM,WAAW,IAAI,iBAAiB,cAAc,GAAG,IAAI,UAAU,OAAO,CAAC,EAAE,CAAC,YAAY,GAAG,QAAQ,CAAC,EAAE;YAE5G,IAAI,WAAW,KAAK,SAAS;YAC7B,IAAI,WAAW;YACf,IAAI,CAAC,UAAU;gBACb,WAAW,MAAM,YAAY;gBAC7B,IAAI,QAAQ,OAAO,IAAI,GAAG;oBACxB,MAAM,IAAI,GAAG;oBACb;gBACF;gBACA,IAAI,UAAU,MAAM,WAAW,IAAI;gBACnC,MAAM,OAAO,KAAK,UAAU,CAAC,EAAE;gBAC/B,MAAM,KAAK,MAAM,EAAE,IAAI,OAAO,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,EAAE,GAAG,QAAQ,QAAQ;gBACpI,IAAI;gBACJ,MAAM,YAAY,QAAQ,SAAS,IAAI,CAAC,QAAQ,MAAM,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,QAAQ,KAAK;gBACrG,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ,QAAQ,GAAG;oBAC5C,IAAI,IAAI;oBACR,IAAI,QAAQ,QAAQ,GAAG,GAAG;wBACxB,IAAI,IAAI,CAAC,iBAAiB,KAAK,QAAQ,QAAQ,EAAE;4BAC/C,IAAI,CAAC,iBAAiB,GAAG,QAAQ,QAAQ;4BACzC,IAAI,MAAM,gBAAgB,GAAG,GAAG;gCAC9B,MAAM,WAAW,GAAG,QAAQ,QAAQ,GAAG,MAAM,gBAAgB;gCAC7D,UAAU,MAAM,WAAW,IAAI;4BACjC;wBACF;wBACA,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,IAAI,UAAU,IAAI,CAAC,iBAAiB;wBAC9D,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;wBAC5B,MAAM,gBAAgB,GAAG;oBAC3B;oBACA,WAAW,OAAO,QAAQ,MAAM,CAAC,KAAK,CAAC,MAAM,IAAI;oBACjD,WAAW,CAAC,WAAW,MAAM,YAAY,IAAI;oBAC7C,WAAW,KAAK;gBAClB,OAAO,IAAI,QAAQ,KAAK,EAAE;oBACxB,MAAM,QAAQ,QAAQ,KAAK,KAAK,OAAO,QAAQ,QAAQ,KAAK;oBAC5D,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI;oBAClC,WAAW,OAAO,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC;oBAC3C,WAAW,KAAK,GAAG,CAAC,MAAM,YAAY,GAAG,aAAa;oBACtD,WAAW,KAAK;gBAClB,OAAO;oBACL,WAAW,MAAM,YAAY,IAAI,OAAO,KAAK,MAAM,YAAY;oBAC/D,MAAM,eAAe,QAAQ,YAAY,IAAI,YAAY;oBACzD,MAAM,eAAe,QAAQ,KAAK,GAAG,IAAI,QAAQ,MAAM;oBACvD,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC;oBACzC,MAAM,YAAY,QAAQ,MAAM,MAAM,EAAE,GAAG,IAAI,OAAO;oBACtD,IAAI;oBACJ,IAAI,aAAa;oBACjB,MAAM,OAAO;oBACb,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK;oBAChC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,EAAE,EAAG;wBACjC,WAAW,KAAK,GAAG,CAAC,YAAY;wBAChC,IAAI,CAAC,UAAU;4BACb,WAAW,KAAK,GAAG,CAAC,MAAM,aAAa;4BACvC,IAAI,UAAU;gCACZ;4BACF;wBACF;wBACA,IAAI,WAAW;4BACb,aAAa,YAAY,OAAO,WAAW,OAAO;4BAClD,IAAI,YAAY;gCACd,WAAW,CAAC,WAAW;gCACvB,WAAW;4BACb;wBACF;wBACA,MAAM,cAAc,CAAC,QAAQ,OAAO,GAAG,OAAO,CAAC,WAAW,GAAG;wBAC7D,MAAM,eAAe,CAAC,QAAQ,QAAQ,GAAG,OAAO;wBAChD,MAAM,eAAe,CAAC,cAAc,YAAY,IAAI,QAAQ,IAAI;wBAChE,WAAW,WAAW,eAAe;wBACrC,WAAW,WAAW,WAAW;oBACnC;gBACF;gBACA,MAAM,YAAY,GAAG;gBACrB,IAAI,OAAO,KAAK,CAAC,WAAW;oBAC1B,QAAQ,IAAI,CAAC,CAAC,wBAAwB,CAAC,EAAE,IAAI;oBAC7C,WAAW;gBACb;YACF;YACA,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE;gBAC/B,WAAW;YACb;YACA,IAAI,UAAU;gBACZ,MAAM,IAAI,GAAG;YACf,OAAO;gBACL,OAAO;YACT;YACA,IAAI,MAAM,QAAQ,CAAC,UAAU,QAAQ,KAAK,GAAG;gBAC3C,UAAU;YACZ;QACF;QACA,MAAM,OAAO,CAAC,GAAG,iBAAiB,WAAW,EAAE,IAAI;QACnD,MAAM,UAAU,KAAK,QAAQ;QAC7B,IAAI,MAAM;YACR,MAAM,WAAW,CAAC,GAAG,eAAe,aAAa,EAAE,KAAK,EAAE;YAC1D,IAAI,CAAC,YAAY,YAAY,OAAO,KAAK,CAAC,QAAQ,KAAK,EAAE;gBACvD,KAAK,QAAQ,CAAC;gBACd,IAAI,CAAC,SAAS,CAAC;YACjB,OAAO,IAAI,WAAW,QAAQ,KAAK,EAAE;gBACnC,IAAI,CAAC,SAAS,CAAC;YACjB;YACA,IAAI,CAAC,KAAK;QACZ,OAAO,IAAI,SAAS;YAClB,IAAI,CAAC,SAAS,CAAC;QACjB;IACF;IACA,gEAAgE,GAChE,IAAI,KAAK,EAAE;QACT,eAAe,GAAG,CAAC,cAAc,CAAC;YAChC,IAAI,CAAC,KAAK;YACV,IAAI,CAAC,MAAM,CAAC;YACZ,IAAI,CAAC,IAAI,CAAC;QACZ;QACA,OAAO,IAAI;IACb;IACA;;;GAGC,GACD,QAAQ;QACN,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO;QAAK;IAC7B;IACA,oCAAoC,GACpC,SAAS;QACP,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO;QAAM;IAC9B;IACA,8CAA8C,GAC9C,SAAS;QACP,IAAI,YAAY,IAAI,GAAG;YACrB,MAAM,EAAE,IAAI,GAAG,EAAE,QAAQ,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS;YACnD,eAAe,GAAG,CAAC,cAAc,CAAC;gBAChC,IAAI,CAAC,QAAQ;gBACb,IAAI,CAAC,QAAQ,KAAK,EAAE;oBAClB,IAAI,CAAC,IAAI,CAAC,KAAK;gBACjB;gBACA,IAAI,CAAC,KAAK;YACZ;QACF;QACA,OAAO,IAAI;IACb;IACA,uCAAuC,GACvC,OAAO,KAAK,EAAE;QACZ,MAAM,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE;QAC5C,MAAM,IAAI,CAAC;QACX,OAAO,IAAI;IACb;IACA,MAAM,GAAG,EAAE,IAAI,EAAE;QACf,IAAI;QACJ,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM;YAC/B,QAAQ;gBAAC,eAAe,EAAE,CAAC,GAAG,CAAC,OAAO,MAAM;oBAAE,GAAG,IAAI;oBAAE,IAAI;gBAAI;aAAE;QACnE,OAAO;YACL,QAAQ,IAAI,CAAC,KAAK,IAAI,EAAE;YACxB,IAAI,CAAC,KAAK,GAAG,EAAE;QACjB;QACA,OAAO,QAAQ,GAAG,CAChB,MAAM,GAAG,CAAC,CAAC;YACT,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC;YACxB,OAAO;QACT,IACA,IAAI,CAAC,CAAC,UAAY,kBAAkB,IAAI,EAAE;IAC9C;IACA;;;;GAIC,GACD,KAAK,MAAM,EAAE;QACX,MAAM,EAAE,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS;QAClC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG;QACpB,UAAU,IAAI,CAAC,MAAM,EAAE,UAAU,IAAI,CAAC,WAAW;QACjD,eAAe,GAAG,CAAC,cAAc,CAAC,IAAM,IAAI,CAAC,KAAK,CAAC,KAAK;QACxD,OAAO,IAAI;IACb;IACA,2BAA2B,GAC3B,QAAQ;QACN,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO;QAAK;IAC7B;IACA,cAAc,GACd,cAAc,KAAK,EAAE;QACnB,IAAI,MAAM,IAAI,IAAI,UAAU;YAC1B,IAAI,CAAC,MAAM;QACb,OAAO,IAAI,MAAM,IAAI,IAAI,YAAY;YACnC,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ,GAAG;QACnC;IACF;IACA;;;;;GAKC,GACD,aAAa,KAAK,EAAE;QAClB,MAAM,MAAM,IAAI,CAAC,GAAG,IAAI;QACxB,IAAI,EAAE,IAAI,GAAG,EAAE,IAAI,EAAE,GAAG;QACxB,MAAM,eAAe,EAAE,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,IAAI,GAAG;QAC9C,IAAI,OAAO,QAAQ,UAAU,MAAM;YACjC,MAAM,KAAK;QACb;QACA,OAAO,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,GAAG;QACjD,IAAI,QAAQ,MAAM;YAChB,OAAO,KAAK;QACd;QACA,MAAM,QAAQ;YAAE,IAAI;YAAK;QAAK;QAC9B,IAAI,CAAC,YAAY,IAAI,GAAG;YACtB,IAAI,MAAM,OAAO,EAAE,CAAC,KAAK,KAAK,GAAG;gBAAC;gBAAM;aAAI;YAC5C,OAAO,CAAC,GAAG,eAAe,aAAa,EAAE;YACzC,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,OAAO;gBAChC,IAAI,CAAC,IAAI,CAAC;YACZ,OAAO,IAAI,CAAC,CAAC,GAAG,iBAAiB,WAAW,EAAE,IAAI,GAAG;gBACnD,IAAI,CAAC,IAAI,CAAC;YACZ;QACF;QACA,OAAO;IACT;IACA,6DAA6D,GAC7D,QAAQ,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE;QAC5B,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,IAAI;QAClC,IAAI,MAAM,OAAO,EACf,OAAO,MAAM,CACX,cACA,gBACE,OACA,CAAC,OAAO,OAAS,MAAM,IAAI,CAAC,QAAQ,YAAY,OAAO,OAAO;QAGpE,cAAc,IAAI,EAAE,OAAO;QAC3B,UAAU,IAAI,EAAE,WAAW,OAAO,IAAI;QACtC,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC;QAChC,IAAI,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,MAAM,MACJ;QAEJ;QACA,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,OAAO,cAAc,EAAE,IAAI,CAAC,WAAW,EAAE;YACvC;YACA;YACA;YACA;YACA,SAAS;gBACP,OAAO;oBACL,IAAI,CAAC,SAAS,IAAI,GAAG;wBACnB,aAAa,IAAI,EAAE;wBACnB,CAAC,GAAG,eAAe,UAAU,EAAE,MAAM,UAAU;wBAC/C,UACE,IAAI,EACJ,WACA,kBAAkB,IAAI,EAAE,cAAc,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,IAC7D,IAAI;oBAER;gBACF;gBACA,QAAQ;oBACN,IAAI,SAAS,IAAI,GAAG;wBAClB,aAAa,IAAI,EAAE;wBACnB,IAAI,YAAY,IAAI,GAAG;4BACrB,IAAI,CAAC,OAAO;wBACd;wBACA,CAAC,GAAG,eAAe,UAAU,EAAE,MAAM,WAAW;wBAChD,UACE,IAAI,EACJ,YACA,kBAAkB,IAAI,EAAE,cAAc,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,IAC7D,IAAI;oBAER;gBACF;gBACA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE;YAChC;QACF,GAAG,IAAI,CAAC,CAAC;YACP,IAAI,MAAM,IAAI,IAAI,OAAO,QAAQ,IAAI,CAAC,CAAC,UAAU,OAAO,IAAI,GAAG;gBAC7D,MAAM,YAAY,iBAAiB;gBACnC,IAAI,WAAW;oBACb,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW;gBACjC;YACF;YACA,OAAO;QACT;IACF;IACA,2CAA2C,GAC3C,OAAO,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE;QAC5B,IAAI,MAAM,MAAM,EAAE;YAChB,IAAI,CAAC,IAAI,CAAC;YACV,OAAO,QAAQ,mBAAmB,IAAI;QACxC;QACA,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE;QACjD,MAAM,cAAc,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM,IAAI;QACrD,IAAI,aAAa,aAAa;YAC5B,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE;gBACjC,IAAI,CAAC,SAAS,GAAG,MAAM,MAAM;YAC/B,OAAO;gBACL,OAAO,QAAQ,mBAAmB,IAAI;YACxC;QACF;QACA,MAAM,EAAE,GAAG,EAAE,YAAY,EAAE,WAAW,IAAI,EAAE,GAAG,IAAI;QACnD,MAAM,EAAE,IAAI,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG;QACvC,IAAI,EAAE,IAAI,MAAM,MAAM,EAAE,OAAO,QAAQ,EAAE,GAAG;QAC5C,IAAI,eAAe,CAAC,aAAa,CAAC,CAAC,MAAM,OAAO,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG;YAC/E,MAAM;QACR;QACA,IAAI,MAAM,OAAO,EAAE,CAAC,KAAK,KAAK,GAAG;YAAC;YAAM;SAAI;QAC5C,MAAM,iBAAiB,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,MAAM;QAC1D,IAAI,gBAAgB;YAClB,KAAK,IAAI,GAAG;QACd;QACA,OAAO,CAAC,GAAG,eAAe,aAAa,EAAE;QACzC,MAAM,eAAe,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,KAAK;QACvD,IAAI,cAAc;YAChB,IAAI,CAAC,MAAM,CAAC;QACd;QACA,MAAM,aAAa,UAAU,MAAM,EAAE;QACrC,MAAM,EAAE,QAAQ,OAAO,EAAE,GAAG;QAC5B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;QAC5B,IAAI,aAAa,aAAa;YAC5B,QAAQ,QAAQ,GAAG;QACrB;QACA,IAAI,MAAM,MAAM,IAAI,CAAC,YAAY;YAC/B,YACE,SACA,SAAS,MAAM,MAAM,EAAE,MACvB,8CAA8C;YAC9C,MAAM,MAAM,KAAK,aAAa,MAAM,GAAG,SAAS,aAAa,MAAM,EAAE,OAAO,KAAK;QAErF;QACA,IAAI,OAAO,CAAC,GAAG,iBAAiB,WAAW,EAAE,IAAI;QACjD,IAAI,CAAC,QAAQ,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM;YACvC,OAAO,QAAQ,kBAAkB,IAAI,EAAE;QACzC;QACA,MAAM,QACJ,oEAAoE;QACpE,iEAAiE;QACjE,sCAAsC;QACtC,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,IAAI,eAAe,CAAC,MAAM,OAAO,GAAG,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,SAAS,UAAU,MAAM,KAAK,EAAE;QAE9H,MAAM,QAAQ,QAAQ,OAAO,IAAI,CAAC,GAAG;QACrC,MAAM,OAAO,YAAY;QACzB,MAAM,eAAe,eAAe,EAAE,CAAC,GAAG,CAAC,SAAS,eAAe,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,eAAe,gBAAgB,EAAE;QACxH,MAAM,YAAY,CAAC,cAAc,CAAC,CAAC,gBAAgB,UAAU,aAAa,SAAS,IAAI,MAAM,SAAS,EAAE,IAAI;QAC5G,IAAI,cAAc;YAChB,MAAM,WAAW,CAAC,GAAG,iBAAiB,eAAe,EAAE;YACvD,IAAI,aAAa,KAAK,WAAW,EAAE;gBACjC,IAAI,WAAW;oBACb,OAAO,IAAI,CAAC,IAAI,CAAC;gBACnB,OACE,MAAM,MACJ,CAAC,uBAAuB,EAAE,KAAK,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,SAAS,IAAI,CAAC,2BAA2B,CAAC;YAEvG;QACF;QACA,MAAM,WAAW,KAAK,WAAW;QACjC,IAAI,UAAU,CAAC,GAAG,eAAe,aAAa,EAAE;QAChD,IAAI,WAAW;QACf,IAAI,CAAC,SAAS;YACZ,MAAM,kBAAkB,SAAS,CAAC,YAAY,IAAI,KAAK;YACvD,IAAI,gBAAgB,iBAAiB;gBACnC,WAAW,CAAC,GAAG,eAAe,OAAO,EAAE,YAAY,QAAQ;gBAC3D,UAAU,CAAC;YACb;YACA,IAAI,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,KAAK,SAAS,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,QAAQ,KAAK,EAAE,UAAU,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,QAAQ,QAAQ,EAAE,WAAW;gBAC3L,UAAU;YACZ;QACF;QACA,IAAI,YAAY,YAAY,IAAI,GAAG;YACjC,IAAI,KAAK,OAAO,IAAI,CAAC,OAAO;gBAC1B,UAAU;YACZ,OAAO,IAAI,CAAC,SAAS;gBACnB,IAAI,CAAC,KAAK,CAAC;YACb;QACF;QACA,IAAI,CAAC,YAAY;YACf,IAAI,WAAW,CAAC,GAAG,eAAe,aAAa,EAAE,SAAS;gBACxD,KAAK,MAAM,GAAG,KAAK,UAAU;gBAC7B,KAAK,QAAQ,GAAG,CAAC,GAAG,eAAe,aAAa,EAAE,OAAO,OAAO,YAAY,iBAAiB,cAAc,GAAG;oBAAC;iBAAE,GAAG,CAAC,GAAG,eAAe,OAAO,EAAE;YAClJ;YACA,IAAI,KAAK,SAAS,IAAI,WAAW;gBAC/B,KAAK,SAAS,GAAG;gBACjB,IAAI,CAAC,aAAa,CAAC,OAAO;oBACxB,IAAI,CAAC,IAAI,CAAC;gBACZ;YACF;YACA,IAAI,SAAS;gBACX,MAAM,EAAE,MAAM,EAAE,GAAG;gBACnB,CAAC,GAAG,eAAe,IAAI,EAAE,eAAe,CAAC,OAAS,cAAc,IAAI,EAAE,OAAO;gBAC7E,MAAM,SAAS,kBAAkB,IAAI,EAAE,cAAc,IAAI,EAAE;gBAC3D,CAAC,GAAG,eAAe,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;gBACnD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;gBACvB,IAAI,KAAK,OAAO,EACd,eAAe,GAAG,CAAC,cAAc,CAAC;oBAChC,KAAK,OAAO,GAAG,CAAC;oBAChB,SAAS,QAAQ,IAAI;oBACrB,IAAI,OAAO;wBACT,SAAS,aAAa,MAAM,EAAE;oBAChC,OAAO;wBACL,KAAK,OAAO,GAAG,QAAQ,IAAI;oBAC7B;gBACF;YACJ;QACF;QACA,IAAI,OAAO;YACT,IAAI,CAAC,IAAI,CAAC;QACZ;QACA,IAAI,YAAY;YACd,QAAQ,SAAS,MAAM,EAAE,EAAE,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI;QACrD,OAAO,IAAI,SAAS;YAClB,IAAI,CAAC,MAAM;QACb,OAAO,IAAI,YAAY,IAAI,KAAK,CAAC,cAAc;YAC7C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QACzB,OAAO;YACL,QAAQ,cAAc;QACxB;IACF;IACA,mEAAmE,GACnE,OAAO,KAAK,EAAE;QACZ,MAAM,OAAO,IAAI,CAAC,SAAS;QAC3B,IAAI,UAAU,KAAK,EAAE,EAAE;YACrB,IAAI,CAAC,GAAG,eAAe,iBAAiB,EAAE,IAAI,GAAG;gBAC/C,IAAI,CAAC,OAAO;YACd;YACA,KAAK,EAAE,GAAG;YACV,IAAI,CAAC,GAAG,eAAe,iBAAiB,EAAE,IAAI,GAAG;gBAC/C,IAAI,CAAC,OAAO;YACd;QACF;IACF;IACA,UAAU;QACR,IAAI,WAAW;QACf,MAAM,EAAE,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS;QAClC,IAAI,CAAC,GAAG,eAAe,aAAa,EAAE,MAAM;YAC1C,CAAC,GAAG,eAAe,gBAAgB,EAAE,KAAK,IAAI;YAC9C,IAAI,aAAa,MAAM;gBACrB,WAAW,IAAI,QAAQ,GAAG;YAC5B;QACF;QACA,IAAI,CAAC,QAAQ,GAAG;IAClB;IACA,UAAU;QACR,MAAM,EAAE,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS;QAClC,IAAI,CAAC,GAAG,eAAe,aAAa,EAAE,MAAM;YAC1C,CAAC,GAAG,eAAe,mBAAmB,EAAE,KAAK,IAAI;QACnD;IACF;IACA;;;GAGC,GACD,KAAK,GAAG,EAAE,OAAO,IAAI,EAAE;QACrB,MAAM,QAAQ,CAAC,GAAG,eAAe,aAAa,EAAE;QAChD,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ;YACjC,MAAM,UAAU,CAAC,GAAG,iBAAiB,WAAW,EAAE,IAAI;YACtD,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,eAAe,OAAO,EAAE,OAAO,QAAQ,QAAQ,KAAK;gBACvE,MAAM,WAAW,CAAC,GAAG,iBAAiB,eAAe,EAAE;gBACvD,IAAI,CAAC,WAAW,QAAQ,WAAW,IAAI,UAAU;oBAC/C,CAAC,GAAG,iBAAiB,WAAW,EAAE,IAAI,EAAE,SAAS,MAAM,CAAC;gBAC1D,OAAO;oBACL,QAAQ,QAAQ,CAAC;gBACnB;gBACA,IAAI,SAAS;oBACX,eAAe,GAAG,CAAC,cAAc,CAAC;wBAChC,IAAI,CAAC,SAAS,CAAC,OAAO;oBACxB;gBACF;YACF;QACF;QACA,OAAO,CAAC,GAAG,iBAAiB,WAAW,EAAE,IAAI;IAC/C;IACA,WAAW;QACT,MAAM,OAAO,IAAI,CAAC,SAAS;QAC3B,IAAI,CAAC,KAAK,OAAO,EAAE;YACjB,KAAK,OAAO,GAAG;YACf,UACE,IAAI,EACJ,WACA,kBAAkB,IAAI,EAAE,cAAc,IAAI,EAAE,KAAK,EAAE,IACnD,IAAI;QAER;IACF;IACA,UAAU,KAAK,EAAE,IAAI,EAAE;QACrB,IAAI,CAAC,MAAM;YACT,IAAI,CAAC,QAAQ;YACb,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,IAAI;QAC/C;QACA,SAAS,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,IAAI;QAChD,KAAK,CAAC,UAAU,OAAO;IACzB;IACA,wEAAwE;IACxE,2EAA2E;IAC3E,6BAA6B;IAC7B,SAAS;QACP,MAAM,OAAO,IAAI,CAAC,SAAS;QAC3B,CAAC,GAAG,iBAAiB,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,GAAG,eAAe,aAAa,EAAE,KAAK,EAAE;QACvF,IAAI,CAAC,KAAK,SAAS,EAAE;YACnB,KAAK,UAAU,GAAG,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,YAAY;QAC/D;QACA,IAAI,CAAC,YAAY,IAAI,GAAG;YACtB,aAAa,IAAI,EAAE;YACnB,IAAI,CAAC,SAAS,IAAI,GAAG;gBACnB,IAAI,CAAC,OAAO;YACd;QACF;IACF;IACA,UAAU;QACR,IAAI,eAAe,OAAO,CAAC,aAAa,EAAE;YACxC,IAAI,CAAC,MAAM;QACb,OAAO;YACL,eAAe,SAAS,CAAC,KAAK,CAAC,IAAI;QACrC;IACF;IACA;;;;GAIC,GACD,MAAM,IAAI,EAAE,MAAM,EAAE;QAClB,IAAI,YAAY,IAAI,GAAG;YACrB,aAAa,IAAI,EAAE;YACnB,MAAM,OAAO,IAAI,CAAC,SAAS;YAC3B,CAAC,GAAG,eAAe,IAAI,EAAE,KAAK,MAAM,EAAE,CAAC;gBACrC,KAAK,IAAI,GAAG;YACd;YACA,IAAI,KAAK,QAAQ,EAAE;gBACjB,KAAK,QAAQ,GAAG,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,KAAK;YACtD;YACA,CAAC,GAAG,eAAe,kBAAkB,EAAE,IAAI,EAAE;gBAC3C,MAAM;gBACN,QAAQ,IAAI;YACd;YACA,MAAM,SAAS,SAAS,mBAAmB,IAAI,CAAC,GAAG,MAAM,kBAAkB,IAAI,CAAC,GAAG,IAAI,cAAc,IAAI,EAAE,QAAQ,KAAK,EAAE;YAC1H,CAAC,GAAG,eAAe,UAAU,EAAE,IAAI,CAAC,aAAa,EAAE;YACnD,IAAI,KAAK,OAAO,EAAE;gBAChB,KAAK,OAAO,GAAG;gBACf,UAAU,IAAI,EAAE,UAAU,QAAQ,IAAI;YACxC;QACF;IACF;AACF;AACA,SAAS,cAAc,MAAM,EAAE,GAAG;IAChC,MAAM,OAAO,YAAY;IACzB,MAAM,QAAQ,YAAY,OAAO,GAAG;IACpC,OAAO,CAAC,GAAG,eAAe,OAAO,EAAE,OAAO;AAC5C;AACA,SAAS,iBAAiB,KAAK,EAAE,OAAO,MAAM,IAAI,EAAE,MAAM,MAAM,EAAE;IAChE,MAAM,UAAU,SAAS;IACzB,IAAI,SAAS;QACX,MAAM,YAAY,YAAY,QAAQ,QAAQ;QAC9C,MAAM,UAAU,CAAC,aAAa,KAAK,EAAE,OAAO;QAC5C,MAAM,QAAQ,CAAC,aAAa,UAAU,KAAK;QAC3C,OAAO,aAAa;YAClB,GAAG,KAAK;YACR;YACA,6CAA6C;YAC7C,SAAS;YACT,+BAA+B;YAC/B,OAAO,KAAK;YACZ,4DAA4D;YAC5D,4DAA4D;YAC5D,qCAAqC;YACrC,IAAI,CAAC,WAAW,UAAU,OAAO,MAAM,KAAK;YAC5C,0CAA0C;YAC1C,MAAM,QAAQ,MAAM,IAAI,GAAG,KAAK;YAChC;YACA,2DAA2D;YAC3D,sCAAsC;YACtC,GAAG,SAAS;QACd;IACF;AACF;AACA,SAAS,aAAa,KAAK;IACzB,MAAM,EAAE,IAAI,GAAG,EAAE,IAAI,EAAE,GAAG,QAAQ,QAAQ;IAC1C,MAAM,OAAO,aAAa,GAAG,IAAI;IACjC,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM,YAAY,KAAK;IACjD,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,OAAO,YAAY,MAAM;IACnD,MAAM,IAAI,GAAG,KAAK,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ;IAC5C,OAAO;AACT;AACA,SAAS,cAAc,KAAK;IAC1B,MAAM,UAAU,aAAa;IAC7B,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ,OAAO,GAAG;QAC1C,QAAQ,OAAO,GAAG,gBAAgB;IACpC;IACA,OAAO;AACT;AACA,SAAS,YAAY,MAAM,EAAE,IAAI;IAC/B,CAAC,GAAG,eAAe,QAAQ,EAAE,QAAQ,CAAC,OAAO,MAAQ,SAAS,QAAQ,KAAK,GAAG,CAAC;AACjF;AACA,IAAI,gBAAgB;IAClB;IACA;IACA;IACA;IACA;CACD;AACD,SAAS,cAAc,MAAM,EAAE,KAAK,EAAE,IAAI;IACxC,OAAO,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,KAAK,eAAe,OAAO,QAAQ,YAAY,KAAK,CAAC,KAAK,EAAE,OAAO,GAAG,IAAI,KAAK;AACrH;AACA,SAAS,UAAU,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI;IACtC,OAAO,SAAS,CAAC,KAAK,MAAM;IAC5B,OAAO,YAAY,CAAC,KAAK,MAAM;AACjC;AAEA,oBAAoB;AACpB,IAAI;AACJ,IAAI,iBAAiB;IAAC;IAAW;IAAY;CAAS;AACtD,IAAI,UAAU;AACd,IAAI,aAAa;IACf,YAAY,KAAK,EAAE,MAAM,CAAE;QACzB,IAAI,CAAC,EAAE,GAAG;QACV,wBAAwB,GACxB,IAAI,CAAC,OAAO,GAAG,CAAC;QAChB,sDAAsD,GACtD,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,mDAAmD,GACnD,IAAI,CAAC,YAAY,GAAG;QACpB,wCAAwC,GACxC,IAAI,CAAC,OAAO,GAAG,aAAa,GAAG,IAAI;QACnC,qCAAqC,GACrC,IAAI,CAAC,QAAQ,GAAG,aAAa,GAAG,IAAI;QACpC,wDAAwD,GACxD,IAAI,CAAC,QAAQ,GAAG;QAChB,0CAA0C,GAC1C,IAAI,CAAC,MAAM,GAAG;YACZ,QAAQ;YACR,YAAY,aAAa,GAAG,IAAI;YAChC,aAAa,aAAa,GAAG,IAAI;YACjC,UAAU,aAAa,GAAG,IAAI;QAChC;QACA,6DAA6D,GAC7D,IAAI,CAAC,OAAO,GAAG;YACb,SAAS,aAAa,GAAG,IAAI;YAC7B,UAAU,aAAa,GAAG,IAAI;YAC9B,QAAQ,aAAa,GAAG,IAAI;QAC9B;QACA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;QACvC,IAAI,QAAQ;YACV,IAAI,CAAC,MAAM,GAAG;QAChB;QACA,IAAI,OAAO;YACT,IAAI,CAAC,KAAK,CAAC;gBAAE,SAAS;gBAAM,GAAG,KAAK;YAAC;QACvC;IACF;IACA;;;GAGC,GACD,IAAI,OAAO;QACT,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAChE,OAAO,OAAO,IAAI,IAAI,CAAC,OAAO,SAAS,IAAI,CAAC,OAAO,QAAQ;QAC7D;IACF;IACA,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,KAAK;IACnB;IACA,IAAI,KAAK,IAAI,EAAE;QACb,IAAI,CAAC,KAAK,GAAG;IACf;IACA,0CAA0C,GAC1C,MAAM;QACJ,MAAM,SAAS,CAAC;QAChB,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,MAAQ,MAAM,CAAC,IAAI,GAAG,OAAO,GAAG;QACnD,OAAO;IACT;IACA,8CAA8C,GAC9C,IAAI,MAAM,EAAE;QACV,IAAK,MAAM,OAAO,OAAQ;YACxB,MAAM,QAAQ,MAAM,CAAC,IAAI;YACzB,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ;gBACjC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC;YACxB;QACF;IACF;IACA,iDAAiD,GACjD,OAAO,KAAK,EAAE;QACZ,IAAI,OAAO;YACT,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa;QAC/B;QACA,OAAO,IAAI;IACb;IACA;;;;;;GAMC,GACD,MAAM,KAAK,EAAE;QACX,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI;QACpB,IAAI,OAAO;YACT,QAAQ,CAAC,GAAG,eAAe,OAAO,EAAE,OAAO,GAAG,CAAC;QACjD,OAAO;YACL,IAAI,CAAC,KAAK,GAAG,EAAE;QACjB;QACA,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;QAC3B;QACA,YAAY,IAAI,EAAE;QAClB,OAAO,iBAAiB,IAAI,EAAE;IAChC;IACA,cAAc,GACd,KAAK,GAAG,EAAE,IAAI,EAAE;QACd,IAAI,QAAQ,CAAC,CAAC,KAAK;YACjB,OAAO;QACT;QACA,IAAI,MAAM;YACR,MAAM,UAAU,IAAI,CAAC,OAAO;YAC5B,CAAC,GAAG,eAAe,IAAI,EAAE,CAAC,GAAG,eAAe,OAAO,EAAE,OAAO,CAAC,MAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3F,OAAO;YACL,UAAU,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY;YACxC,IAAI,CAAC,IAAI,CAAC,CAAC,SAAW,OAAO,IAAI,CAAC,CAAC,CAAC;QACtC;QACA,OAAO,IAAI;IACb;IACA,wCAAwC,GACxC,MAAM,IAAI,EAAE;QACV,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,OAAO;YAC/B,IAAI,CAAC,KAAK,CAAC;gBAAE,OAAO;YAAK;QAC3B,OAAO;YACL,MAAM,UAAU,IAAI,CAAC,OAAO;YAC5B,CAAC,GAAG,eAAe,IAAI,EAAE,CAAC,GAAG,eAAe,OAAO,EAAE,OAAO,CAAC,MAAQ,OAAO,CAAC,IAAI,CAAC,KAAK;QACzF;QACA,OAAO,IAAI;IACb;IACA,oCAAoC,GACpC,OAAO,IAAI,EAAE;QACX,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,OAAO;YAC/B,IAAI,CAAC,KAAK,CAAC;gBAAE,OAAO;YAAM;QAC5B,OAAO;YACL,MAAM,UAAU,IAAI,CAAC,OAAO;YAC5B,CAAC,GAAG,eAAe,IAAI,EAAE,CAAC,GAAG,eAAe,OAAO,EAAE,OAAO,CAAC,MAAQ,OAAO,CAAC,IAAI,CAAC,MAAM;QAC1F;QACA,OAAO,IAAI;IACb;IACA,0CAA0C,GAC1C,KAAK,QAAQ,EAAE;QACb,CAAC,GAAG,eAAe,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE;IAC7C;IACA,yDAAyD,GACzD,WAAW;QACT,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO;QAClD,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG;QACnC,MAAM,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG;QACrC,IAAI,UAAU,CAAC,IAAI,CAAC,QAAQ,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE;YACzD,IAAI,CAAC,QAAQ,GAAG;YAChB,CAAC,GAAG,eAAe,KAAK,EAAE,SAAS,CAAC,CAAC,UAAU,OAAO;gBACpD,OAAO,KAAK,GAAG,IAAI,CAAC,GAAG;gBACvB,SAAS,QAAQ,IAAI,EAAE,IAAI,CAAC,KAAK;YACnC;QACF;QACA,MAAM,OAAO,CAAC,UAAU,IAAI,CAAC,QAAQ;QACrC,MAAM,SAAS,WAAW,QAAQ,OAAO,IAAI,GAAG,IAAI,CAAC,GAAG,KAAK;QAC7D,IAAI,WAAW,SAAS,IAAI,EAAE;YAC5B,CAAC,GAAG,eAAe,KAAK,EAAE,UAAU,CAAC,CAAC,WAAW,OAAO;gBACtD,OAAO,KAAK,GAAG;gBACf,UAAU,QAAQ,IAAI,EAAE,IAAI,CAAC,KAAK;YACpC;QACF;QACA,IAAI,MAAM;YACR,IAAI,CAAC,QAAQ,GAAG;YAChB,CAAC,GAAG,eAAe,KAAK,EAAE,QAAQ,CAAC,CAAC,SAAS,OAAO;gBAClD,OAAO,KAAK,GAAG;gBACf,QAAQ,QAAQ,IAAI,EAAE,IAAI,CAAC,KAAK;YAClC;QACF;IACF;IACA,cAAc,GACd,cAAc,KAAK,EAAE;QACnB,IAAI,MAAM,IAAI,IAAI,UAAU;YAC1B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,MAAM;YAC9B,IAAI,CAAC,MAAM,IAAI,EAAE;gBACf,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,MAAM;YAC/B;QACF,OAAO,IAAI,MAAM,IAAI,IAAI,QAAQ;YAC/B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,MAAM;QAClC,OAAO;QACP,eAAe,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ;IAC1C;AACF;AACA,SAAS,iBAAiB,IAAI,EAAE,KAAK;IACnC,OAAO,QAAQ,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,QAAU,YAAY,MAAM,SAAS,IAAI,CACrE,CAAC,UAAY,kBAAkB,MAAM;AAEzC;AACA,eAAe,YAAY,IAAI,EAAE,KAAK,EAAE,MAAM;IAC5C,MAAM,EAAE,IAAI,EAAE,IAAI,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG;IACzD,MAAM,YAAY,eAAe,EAAE,CAAC,GAAG,CAAC,MAAM,OAAO,KAAK,MAAM,OAAO;IACvE,IAAI,MAAM;QACR,MAAM,IAAI,GAAG;IACf;IACA,IAAI,QAAQ,OAAO,MAAM,EAAE,GAAG;IAC9B,IAAI,SAAS,OAAO,MAAM,IAAI,GAAG;IACjC,MAAM,UAAU,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ,eAAe,EAAE,CAAC,GAAG,CAAC,OAAO,MAAM,KAAK;IACtF,IAAI,SAAS;QACX,MAAM,EAAE,GAAG,KAAK;QAChB,MAAM,MAAM,GAAG,KAAK;QACpB,IAAI,WAAW;YACb,UAAU,MAAM,GAAG,KAAK;QAC1B;IACF,OAAO;QACL,CAAC,GAAG,eAAe,IAAI,EAAE,gBAAgB,CAAC;YACxC,MAAM,UAAU,KAAK,CAAC,IAAI;YAC1B,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,UAAU;gBAClC,MAAM,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI;gBAClC,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;oBACnC,MAAM,UAAU,MAAM,GAAG,CAAC;oBAC1B,IAAI,SAAS;wBACX,IAAI,CAAC,UAAU,QAAQ,QAAQ,GAAG;wBAClC,IAAI,WAAW,QAAQ,SAAS,GAAG;oBACrC,OAAO;wBACL,MAAM,GAAG,CAAC,SAAS;4BACjB,OAAO;4BACP,UAAU,YAAY;4BACtB,WAAW,aAAa;wBAC1B;oBACF;gBACF;gBACA,IAAI,WAAW;oBACb,SAAS,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;gBAC7B;YACF;QACF;IACF;IACA,MAAM,QAAQ,IAAI,CAAC,SAAS;IAC5B,IAAI,MAAM,KAAK,KAAK,CAAC,MAAM,MAAM,EAAE;QACjC,MAAM,MAAM,GAAG,MAAM,KAAK;QAC1B,CAAC,GAAG,eAAe,UAAU,EAAE,MAAM,KAAK,GAAG,MAAM,UAAU,GAAG,MAAM,WAAW;IACnF,OAAO,IAAI,MAAM,MAAM,EAAE;QACvB,MAAM,KAAK,GAAG;IAChB;IACA,MAAM,WAAW,CAAC,QAAQ,OAAO,IAAI,CAAC,KAAK,OAAO,CAAC,EAAE,GAAG,CACtD,CAAC,MAAQ,KAAK,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;IAEnC,MAAM,SAAS,MAAM,MAAM,KAAK,QAAQ,eAAe,OAAO,cAAc;IAC5E,IAAI,WAAW,UAAU,MAAM,OAAO,EAAE;QACtC,SAAS,IAAI,CACX,cAAc,EAAE,IAAI,CAAC,eAAe,EAAE;YACpC;YACA;YACA,SAAS;gBACP,OAAO,eAAe,IAAI;gBAC1B,QAAQ,eAAe,IAAI;gBAC3B,OAAM,MAAM,EAAE,OAAO;oBACnB,IAAI,QAAQ;wBACV,UAAU,OAAO,IAAI,CAAC,eAAe;wBACrC,QAAQ,mBAAmB;oBAC7B,OAAO;wBACL,OAAO,MAAM,GAAG;wBAChB,QACE,SACE,SACA,QACA,OACA;oBAGN;gBACF;YACF;QACF;IAEJ;IACA,IAAI,MAAM,MAAM,EAAE;QAChB,MAAM,IAAI,QAAQ,CAAC;YACjB,MAAM,WAAW,CAAC,GAAG,CAAC;QACxB;IACF;IACA,MAAM,SAAS,kBAAkB,MAAM,MAAM,QAAQ,GAAG,CAAC;IACzD,IAAI,QAAQ,OAAO,QAAQ,IAAI,CAAC,CAAC,UAAU,OAAO,IAAI,GAAG;QACvD,MAAM,YAAY,iBAAiB,OAAO,MAAM;QAChD,IAAI,WAAW;YACb,YAAY,MAAM;gBAAC;aAAU;YAC7B,OAAO,YAAY,MAAM,WAAW;QACtC;IACF;IACA,IAAI,WAAW;QACb,eAAe,GAAG,CAAC,cAAc,CAAC,IAAM,UAAU,QAAQ,MAAM,KAAK,IAAI;IAC3E;IACA,OAAO;AACT;AACA,SAAS,WAAW,IAAI,EAAE,KAAK;IAC7B,MAAM,UAAU;QAAE,GAAG,KAAK,OAAO;IAAC;IAClC,IAAI,OAAO;QACT,CAAC,GAAG,eAAe,IAAI,EAAE,CAAC,GAAG,eAAe,OAAO,EAAE,QAAQ,CAAC;YAC5D,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,OAAO,IAAI,GAAG;gBACtC,SAAS,aAAa;YACxB;YACA,IAAI,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG;gBACrC,SAAS;oBAAE,GAAG,MAAM;oBAAE,IAAI,KAAK;gBAAE;YACnC;YACA,eAAe,SAAS,QAAQ,CAAC;gBAC/B,OAAO,aAAa;YACtB;QACF;IACF;IACA,WAAW,MAAM;IACjB,OAAO;AACT;AACA,SAAS,WAAW,IAAI,EAAE,OAAO;IAC/B,CAAC,GAAG,eAAe,QAAQ,EAAE,SAAS,CAAC,QAAQ;QAC7C,IAAI,CAAC,KAAK,OAAO,CAAC,IAAI,EAAE;YACtB,KAAK,OAAO,CAAC,IAAI,GAAG;YACpB,CAAC,GAAG,eAAe,gBAAgB,EAAE,QAAQ;QAC/C;IACF;AACF;AACA,SAAS,aAAa,GAAG,EAAE,QAAQ;IACjC,MAAM,SAAS,IAAI;IACnB,OAAO,GAAG,GAAG;IACb,IAAI,UAAU;QACZ,CAAC,GAAG,eAAe,gBAAgB,EAAE,QAAQ;IAC/C;IACA,OAAO;AACT;AACA,SAAS,eAAe,OAAO,EAAE,KAAK,EAAE,MAAM;IAC5C,IAAI,MAAM,IAAI,EAAE;QACd,CAAC,GAAG,eAAe,IAAI,EAAE,MAAM,IAAI,EAAE,CAAC;YACpC,MAAM,SAAS,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,OAAO,IAAI;YAC1D,MAAM,CAAC,eAAe,CAAC;QACzB;IACF;AACF;AACA,SAAS,YAAY,IAAI,EAAE,KAAK;IAC9B,CAAC,GAAG,eAAe,IAAI,EAAE,OAAO,CAAC;QAC/B,eAAe,KAAK,OAAO,EAAE,OAAO,CAAC;YACnC,OAAO,aAAa,KAAK;QAC3B;IACF;AACF;AAEA,wBAAwB;AACxB,IAAI,QAAQ;AACZ,IAAI;AACJ,IAAI,gBAAgB,MAAM,aAAa,CAAC;IACtC,OAAO;IACP,WAAW;AACb;AAEA,mBAAmB;AACnB,IAAI;AACJ,IAAI,YAAY;IACd,MAAM,UAAU,EAAE;IAClB,MAAM,aAAa,SAAS,KAAK;QAC/B,CAAC,GAAG,eAAe,mBAAmB;QACtC,MAAM,UAAU,EAAE;QAClB,CAAC,GAAG,eAAe,IAAI,EAAE,SAAS,CAAC,MAAM;YACvC,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ;gBAChC,QAAQ,IAAI,CAAC,KAAK,KAAK;YACzB,OAAO;gBACL,MAAM,UAAU,UAAU,OAAO,MAAM;gBACvC,IAAI,SAAS;oBACX,QAAQ,IAAI,CAAC,KAAK,KAAK,CAAC;gBAC1B;YACF;QACF;QACA,OAAO;IACT;IACA,WAAW,OAAO,GAAG;IACrB,WAAW,GAAG,GAAG,SAAS,IAAI;QAC5B,IAAI,CAAC,QAAQ,QAAQ,CAAC,OAAO;YAC3B,QAAQ,IAAI,CAAC;QACf;IACF;IACA,WAAW,MAAM,GAAG,SAAS,IAAI;QAC/B,MAAM,IAAI,QAAQ,OAAO,CAAC;QAC1B,IAAI,CAAC,GAAG,QAAQ,MAAM,CAAC,GAAG;IAC5B;IACA,WAAW,KAAK,GAAG;QACjB,CAAC,GAAG,eAAe,IAAI,EAAE,SAAS,CAAC,OAAS,KAAK,KAAK,IAAI;QAC1D,OAAO,IAAI;IACb;IACA,WAAW,MAAM,GAAG;QAClB,CAAC,GAAG,eAAe,IAAI,EAAE,SAAS,CAAC,OAAS,KAAK,MAAM,IAAI;QAC3D,OAAO,IAAI;IACb;IACA,WAAW,GAAG,GAAG,SAAS,MAAM;QAC9B,CAAC,GAAG,eAAe,IAAI,EAAE,SAAS,CAAC,MAAM;YACvC,MAAM,UAAU,eAAe,EAAE,CAAC,GAAG,CAAC,UAAU,OAAO,GAAG,QAAQ;YAClE,IAAI,SAAS;gBACX,KAAK,GAAG,CAAC;YACX;QACF;IACF;IACA,WAAW,KAAK,GAAG,SAAS,KAAK;QAC/B,MAAM,UAAU,EAAE;QAClB,CAAC,GAAG,eAAe,IAAI,EAAE,SAAS,CAAC,MAAM;YACvC,IAAI,eAAe,EAAE,CAAC,GAAG,CAAC,QAAQ;gBAChC,QAAQ,IAAI,CAAC,KAAK,KAAK;YACzB,OAAO;gBACL,MAAM,UAAU,IAAI,CAAC,SAAS,CAAC,OAAO,MAAM;gBAC5C,IAAI,SAAS;oBACX,QAAQ,IAAI,CAAC,KAAK,KAAK,CAAC;gBAC1B;YACF;QACF;QACA,OAAO;IACT;IACA,WAAW,IAAI,GAAG;QAChB,CAAC,GAAG,eAAe,IAAI,EAAE,SAAS,CAAC,OAAS,KAAK,IAAI,IAAI;QACzD,OAAO,IAAI;IACb;IACA,WAAW,MAAM,GAAG,SAAS,KAAK;QAChC,CAAC,GAAG,eAAe,IAAI,EAAE,SAAS,CAAC,MAAM,IAAM,KAAK,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,MAAM;QACvF,OAAO,IAAI;IACb;IACA,MAAM,YAAY,SAAS,GAAG,EAAE,IAAI,EAAE,KAAK;QACzC,OAAO,eAAe,EAAE,CAAC,GAAG,CAAC,OAAO,IAAI,OAAO,QAAQ;IACzD;IACA,WAAW,SAAS,GAAG;IACvB,OAAO;AACT;AAEA,0BAA0B;AAC1B,SAAS,WAAW,MAAM,EAAE,KAAK,EAAE,IAAI;IACrC,MAAM,UAAU,gBAAgB,EAAE,CAAC,GAAG,CAAC,UAAU;IACjD,IAAI,WAAW,CAAC,MAAM,OAAO,EAAE;IAC/B,MAAM,MAAM,CAAC,GAAG,cAAc,OAAO,EACnC,IAAM,WAAW,UAAU,MAAM,IAAI,IAAI,cAAc,KAAK,GAC5D,EAAE;IAEJ,MAAM,WAAW,CAAC,GAAG,cAAc,MAAM,EAAE;IAC3C,MAAM,cAAc,CAAC,GAAG,gBAAgB,cAAc;IACtD,MAAM,QAAQ,CAAC,GAAG,cAAc,OAAO,EACrC,IAAM,CAAC;YACL,OAAO,EAAE;YACT,OAAO,EAAE;YACT,OAAM,IAAI,EAAE,QAAQ;gBAClB,MAAM,WAAW,WAAW,MAAM;gBAClC,MAAM,eAAe,SAAS,OAAO,GAAG,KAAK,CAAC,MAAM,KAAK,CAAC,MAAM,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,MAAQ,CAAC,KAAK,OAAO,CAAC,IAAI;gBAC3H,OAAO,eAAe,iBAAiB,MAAM,YAAY,IAAI,QAAQ,CAAC;oBACpE,WAAW,MAAM;oBACjB,MAAM,KAAK,CAAC,IAAI,CAAC;wBACf,QAAQ,iBAAiB,MAAM;oBACjC;oBACA;gBACF;YACF;QACF,CAAC,GACD,EAAE;IAEJ,MAAM,QAAQ,CAAC,GAAG,cAAc,MAAM,EAAE;WAAI,MAAM,KAAK;KAAC;IACxD,MAAM,UAAU,CAAC,GAAG,cAAc,MAAM,EAAE,EAAE;IAC5C,MAAM,aAAa,CAAC,GAAG,gBAAgB,OAAO,EAAE,WAAW;IAC3D,CAAC,GAAG,cAAc,OAAO,EAAE;QACzB,CAAC,GAAG,gBAAgB,IAAI,EAAE,MAAM,OAAO,CAAC,KAAK,CAAC,QAAQ,aAAa,CAAC;YAClE,WAAW,MAAM;YACjB,KAAK,IAAI,CAAC;QACZ;QACA,MAAM,OAAO,CAAC,MAAM,GAAG;QACvB,eAAe,YAAY;IAC7B,GAAG;QAAC;KAAO;IACX,CAAC,GAAG,cAAc,OAAO,EAAE;QACzB,eAAe,GAAG,KAAK,GAAG,CAAC,YAAY;IACzC,GAAG;IACH,SAAS,eAAe,UAAU,EAAE,QAAQ;QAC1C,IAAK,IAAI,IAAI,YAAY,IAAI,UAAU,IAAK;YAC1C,MAAM,OAAO,MAAM,OAAO,CAAC,EAAE,IAAI,CAAC,MAAM,OAAO,CAAC,EAAE,GAAG,IAAI,WAAW,MAAM,MAAM,KAAK,CAAC;YACtF,MAAM,UAAU,UAAU,QAAQ,GAAG,QAAQ,KAAK,CAAC,EAAE;YACrD,IAAI,SAAS;gBACX,QAAQ,OAAO,CAAC,EAAE,GAAG,cAAc;YACrC;QACF;IACF;IACA,MAAM,UAAU,MAAM,OAAO,CAAC,GAAG,CAC/B,CAAC,MAAM,IAAM,WAAW,MAAM,QAAQ,OAAO,CAAC,EAAE;IAElD,MAAM,UAAU,CAAC,GAAG,cAAc,UAAU,EAAE;IAC9C,MAAM,cAAc,CAAC,GAAG,gBAAgB,OAAO,EAAE;IACjD,MAAM,aAAa,YAAY,eAAe,SAAS;IACvD,CAAC,GAAG,gBAAgB,yBAAyB,EAAE;QAC7C,SAAS,OAAO;QAChB,MAAM,KAAK,GAAG,MAAM,OAAO;QAC3B,MAAM,EAAE,KAAK,EAAE,GAAG;QAClB,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,KAAK,GAAG,EAAE;YAChB,CAAC,GAAG,gBAAgB,IAAI,EAAE,OAAO,CAAC,KAAO;QAC3C;QACA,CAAC,GAAG,gBAAgB,IAAI,EAAE,MAAM,OAAO,EAAE,CAAC,MAAM;YAC9C,KAAK,IAAI;YACT,IAAI,YAAY;gBACd,KAAK,KAAK,CAAC;oBAAE,SAAS;gBAAQ;YAChC;YACA,MAAM,UAAU,QAAQ,OAAO,CAAC,EAAE;YAClC,IAAI,SAAS;gBACX,WAAW,MAAM,QAAQ,GAAG;gBAC5B,IAAI,KAAK,GAAG,EAAE;oBACZ,KAAK,KAAK,CAAC,IAAI,CAAC;gBAClB,OAAO;oBACL,KAAK,KAAK,CAAC;gBACb;YACF;QACF;IACF;IACA,CAAC,GAAG,gBAAgB,OAAO,EAAE,IAAM;YACjC,CAAC,GAAG,gBAAgB,IAAI,EAAE,MAAM,KAAK,EAAE,CAAC,OAAS,KAAK,IAAI,CAAC;QAC7D;IACA,MAAM,SAAS,QAAQ,GAAG,CAAC,CAAC,IAAM,CAAC;YAAE,GAAG,CAAC;QAAC,CAAC;IAC3C,OAAO,MAAM;QAAC;QAAQ;KAAI,GAAG;AAC/B;AAEA,yBAAyB;AACzB,SAAS,UAAU,KAAK,EAAE,IAAI;IAC5B,MAAM,OAAO,gBAAgB,EAAE,CAAC,GAAG,CAAC;IACpC,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI,GAAG,WACtB,GACA,OAAO,QAAQ;QAAC;KAAM,EACtB,OAAO,QAAQ,EAAE,GAAG;IAEtB,OAAO,QAAQ,UAAU,MAAM,IAAI,IAAI;QAAC;QAAQ;KAAI,GAAG;AACzD;AAEA,4BAA4B;AAC5B,IAAI;AACJ,IAAI,gBAAgB,IAAM;AAC1B,IAAI,eAAe,IAAM,CAAC,GAAG,cAAc,QAAQ,EAAE,cAAc,CAAC,EAAE;AAEtE,8BAA8B;AAC9B,IAAI;AACJ,IAAI,iBAAiB,CAAC,SAAS;IAC7B,MAAM,cAAc,CAAC,GAAG,gBAAgB,WAAW,EAAE,IAAM,IAAI,YAAY,SAAS;IACpF,CAAC,GAAG,gBAAgB,OAAO,EAAE,IAAM;YACjC,YAAY,IAAI;QAClB;IACA,OAAO;AACT;AAEA,wBAAwB;AACxB,IAAI;AACJ,SAAS,SAAS,MAAM,EAAE,QAAQ,EAAE,IAAI;IACtC,MAAM,UAAU,gBAAgB,EAAE,CAAC,GAAG,CAAC,aAAa;IACpD,IAAI,WAAW,CAAC,MAAM,OAAO,EAAE;IAC/B,IAAI,UAAU;IACd,IAAI,YAAY,KAAK;IACrB,MAAM,SAAS,WACb;uCACA,CAAC,GAAG;YACF,MAAM,QAAQ,UAAU,QAAQ,GAAG,QAAQ;YAC3C,YAAY,MAAM,GAAG;YACrB,UAAU,WAAW,MAAM,OAAO;YAClC,OAAO;QACT;sCACA,0DAA0D;IAC1D,yCAAyC;IACzC,QAAQ;QAAC,CAAC;KAAE;IAEd,CAAC,GAAG,gBAAgB,yBAAyB,EAAE;QAC7C,CAAC,GAAG,gBAAgB,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,MAAM;YAClD,MAAM,SAAS,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,EAAE;YACxD,WAAW,MAAM;YACjB,IAAI,KAAK,GAAG,EAAE;gBACZ,IAAI,QAAQ;oBACV,KAAK,MAAM,CAAC;wBAAE,IAAI,OAAO,OAAO;oBAAC;gBACnC;gBACA;YACF;YACA,IAAI,QAAQ;gBACV,KAAK,KAAK,CAAC;oBAAE,IAAI,OAAO,OAAO;gBAAC;YAClC,OAAO;gBACL,KAAK,KAAK;YACZ;QACF;IACF,GAAG;IACH,IAAI,WAAW,UAAU,MAAM,IAAI,GAAG;QACpC,MAAM,MAAM,aAAa,MAAM,CAAC,EAAE;QAClC,GAAG,CAAC,YAAY,GAAG,CAAC,WAAW,MAAM;YACnC,MAAM,QAAQ,gBAAgB,EAAE,CAAC,GAAG,CAAC,aAAa,UAAU,GAAG,QAAQ;YACvE,IAAI,OAAO;gBACT,MAAM,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,EAAE;gBACxD,IAAI,QAAQ,MAAM,EAAE,GAAG,OAAO,OAAO;gBACrC,OAAO;YACT;QACF;QACA,OAAO;IACT;IACA,OAAO,MAAM,CAAC,EAAE;AAClB;AAEA,8BAA8B;AAC9B,IAAI,SAAS;AACb,IAAI;AACJ,IAAI;AACJ,SAAS,cAAc,IAAI,EAAE,KAAK,EAAE,IAAI;IACtC,MAAM,UAAU,gBAAgB,EAAE,CAAC,GAAG,CAAC,UAAU;IACjD,MAAM,EACJ,KAAK,EACL,IAAI,EACJ,QAAQ,CAAC,EACT,UAAU,IAAI,EACd,kBAAkB,KAAK,EACvB,WAAW,EACX,KAAK,QAAQ,EACb,QAAQ,WAAW,EACpB,GAAG,UAAU,YAAY;IAC1B,MAAM,MAAM,CAAC,GAAG,cAAc,OAAO,EACnC,IAAM,WAAW,UAAU,MAAM,IAAI,IAAI,cAAc,KAAK,GAC5D,EAAE;IAEJ,MAAM,QAAQ,CAAC,GAAG,gBAAgB,OAAO,EAAE;IAC3C,MAAM,cAAc,EAAE;IACtB,MAAM,kBAAkB,CAAC,GAAG,cAAc,MAAM,EAAE;IAClD,MAAM,kBAAkB,QAAQ,OAAO,gBAAgB,OAAO;IAC9D,CAAC,GAAG,gBAAgB,yBAAyB,EAAE;QAC7C,gBAAgB,OAAO,GAAG;IAC5B;IACA,CAAC,GAAG,gBAAgB,OAAO,EAAE;QAC3B,CAAC,GAAG,gBAAgB,IAAI,EAAE,aAAa,CAAC;YACtC,KAAK,IAAI,EAAE,IAAI;YACf,EAAE,IAAI,CAAC,GAAG,GAAG;QACf;QACA,OAAO;YACL,CAAC,GAAG,gBAAgB,IAAI,EAAE,gBAAgB,OAAO,EAAE,CAAC;gBAClD,IAAI,EAAE,OAAO,EAAE;oBACb,aAAa,EAAE,YAAY;gBAC7B;gBACA,WAAW,EAAE,IAAI,EAAE;gBACnB,EAAE,IAAI,CAAC,IAAI,CAAC;YACd;QACF;IACF;IACA,MAAM,OAAO,QAAQ,OAAO,UAAU,YAAY,OAAO;IACzD,MAAM,UAAU,SAAS,gBAAgB,OAAO,IAAI,EAAE;IACtD,CAAC,GAAG,gBAAgB,yBAAyB,EAC3C,IAAM,CAAC,GAAG,gBAAgB,IAAI,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE;YAC3D,WAAW,MAAM;YACjB,SAAS,aAAa,MAAM;QAC9B;IAEF,MAAM,SAAS,EAAE;IACjB,IAAI,iBACF,CAAC,GAAG,gBAAgB,IAAI,EAAE,iBAAiB,CAAC,GAAG;QAC7C,IAAI,EAAE,OAAO,EAAE;YACb,aAAa,EAAE,YAAY;YAC3B,QAAQ,IAAI,CAAC;QACf,OAAO;YACL,IAAI,MAAM,CAAC,EAAE,GAAG,KAAK,OAAO,CAAC,EAAE,GAAG;YAClC,IAAI,CAAC,GAAG,WAAW,CAAC,EAAE,GAAG;QAC3B;IACF;IACF,CAAC,GAAG,gBAAgB,IAAI,EAAE,OAAO,CAAC,MAAM;QACtC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE;YACnB,WAAW,CAAC,EAAE,GAAG;gBACf,KAAK,IAAI,CAAC,EAAE;gBACZ;gBACA,OAAO,QAAQ,SAAS;gBACxB,MAAM,IAAI;YACZ;YACA,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG;QAC7B;IACF;IACA,IAAI,OAAO,MAAM,EAAE;QACjB,IAAI,IAAI,CAAC;QACT,MAAM,EAAE,KAAK,EAAE,GAAG,UAAU,YAAY;QACxC,CAAC,GAAG,gBAAgB,IAAI,EAAE,QAAQ,CAAC,UAAU;YAC3C,MAAM,IAAI,eAAe,CAAC,UAAU;YACpC,IAAI,CAAC,UAAU;gBACb,IAAI,YAAY,OAAO,CAAC;gBACxB,WAAW,CAAC,EAAE,GAAG;oBAAE,GAAG,CAAC;oBAAE,MAAM,KAAK,CAAC,SAAS;gBAAC;YACjD,OAAO,IAAI,OAAO;gBAChB,YAAY,MAAM,CAAC,EAAE,GAAG,GAAG;YAC7B;QACF;IACF;IACA,IAAI,gBAAgB,EAAE,CAAC,GAAG,CAAC,OAAO;QAChC,YAAY,IAAI,CAAC,CAAC,GAAG,IAAM,KAAK,EAAE,IAAI,EAAE,EAAE,IAAI;IAChD;IACA,IAAI,QAAQ,CAAC;IACb,MAAM,cAAc,CAAC,GAAG,gBAAgB,cAAc;IACtD,MAAM,eAAe,gBAAgB;IACrC,MAAM,UAAU,aAAa,GAAG,IAAI;IACpC,MAAM,qBAAqB,CAAC,GAAG,cAAc,MAAM,EAAE,aAAa,GAAG,IAAI;IACzE,MAAM,cAAc,CAAC,GAAG,cAAc,MAAM,EAAE;IAC9C,CAAC,GAAG,gBAAgB,IAAI,EAAE,aAAa,CAAC,GAAG;QACzC,MAAM,MAAM,EAAE,GAAG;QACjB,MAAM,YAAY,EAAE,KAAK;QACzB,MAAM,IAAI,UAAU,YAAY;QAChC,IAAI;QACJ,IAAI;QACJ,MAAM,aAAa,SAAS,EAAE,KAAK,IAAI,GAAG;QAC1C,IAAI,aAAa,QAAQ,SAAS,KAAI;YACpC,MAAM,EAAE,KAAK;YACb,QAAQ,QAAQ,SAAS;QAC3B,OAAO;YACL,MAAM,UAAU,KAAK,OAAO,CAAC,OAAO;YACpC,IAAI,aAAa,QAAQ,SAAS,KAAI;gBACpC,IAAI,SAAS;oBACX,MAAM,EAAE,KAAK;oBACb,QAAQ,QAAQ,SAAS;gBAC3B,OAAO,IAAI,MAAM,EAAE,MAAM,EAAE;oBACzB,QAAQ,SAAS,UAAU;gBAC7B,OAAO;YACT,OAAO,IAAI,CAAC,SAAS;gBACnB,MAAM,EAAE,KAAK;gBACb,QAAQ,QAAQ,SAAS;YAC3B,OAAO;QACT;QACA,MAAM,SAAS,KAAK,EAAE,IAAI,EAAE;QAC5B,MAAM,gBAAgB,EAAE,CAAC,GAAG,CAAC,OAAO,QAAQ,OAAO;YAAE,IAAI;QAAI;QAC7D,IAAI,CAAC,IAAI,MAAM,EAAE;YACf,MAAM,UAAU,eAAe,aAAa,MAAM;YAClD,IAAI,MAAM,GAAG,SAAS,SAAS,EAAE,IAAI,EAAE,GAAG;QAC5C;QACA,SAAS;QACT,MAAM,UAAU;YACd,GAAG,YAAY;YACf,iDAAiD;YACjD,OAAO,aAAa;YACpB,KAAK;YACL,WAAW,EAAE,SAAS;YACtB,gCAAgC;YAChC,OAAO;YACP,kCAAkC;YAClC,GAAG,GAAG;QACR;QACA,IAAI,SAAS,QAAQ,SAAS,OAAM,gBAAgB,EAAE,CAAC,GAAG,CAAC,QAAQ,IAAI,GAAG;YACxE,MAAM,KAAK,UAAU,YAAY;YACjC,MAAM,OAAO,gBAAgB,EAAE,CAAC,GAAG,CAAC,GAAG,OAAO,KAAK,kBAAkB,GAAG,IAAI,GAAG,GAAG,OAAO;YACzF,QAAQ,IAAI,GAAG,SAAS,MAAM,EAAE,IAAI,EAAE;QACxC;QACA,MAAM,EAAE,SAAS,EAAE,GAAG;QACtB,QAAQ,SAAS,GAAG,CAAC;YACnB,SAAS,WAAW;YACpB,MAAM,eAAe,gBAAgB,OAAO;YAC5C,MAAM,KAAK,aAAa,IAAI,CAAC,CAAC,KAAO,GAAG,GAAG,KAAK;YAChD,IAAI,CAAC,IAAI;YACT,IAAI,OAAO,SAAS,IAAI,GAAG,KAAK,IAAI,SAAS,UAAU,KAAI;gBACzD;YACF;YACA,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE;gBAChB,MAAM,OAAO,aAAa,KAAK,CAAC,CAAC,KAAO,GAAG,IAAI,CAAC,IAAI;gBACpD,IAAI,GAAG,KAAK,IAAI,QAAQ,SAAS,KAAI;oBACnC,MAAM,SAAS,SAAS,SAAS,GAAG,IAAI;oBACxC,IAAI,WAAW,OAAO;wBACpB,MAAM,WAAW,WAAW,OAAO,IAAI;wBACvC,GAAG,OAAO,GAAG;wBACb,IAAI,CAAC,QAAQ,WAAW,GAAG;4BACzB,IAAI,YAAY,YACd,GAAG,YAAY,GAAG,WAAW,aAAa;4BAC5C;wBACF;oBACF;gBACF;gBACA,IAAI,QAAQ,aAAa,IAAI,CAAC,CAAC,KAAO,GAAG,OAAO,GAAG;oBACjD,mBAAmB,OAAO,CAAC,MAAM,CAAC;oBAClC,IAAI,iBAAiB;wBACnB,YAAY,OAAO,GAAG;oBACxB;oBACA;gBACF;YACF;QACF;QACA,MAAM,UAAU,WAAW,EAAE,IAAI,EAAE;QACnC,IAAI,UAAU,QAAQ,SAAS,OAAM,iBAAiB;YACpD,mBAAmB,OAAO,CAAC,GAAG,CAAC,GAAG;gBAAE;gBAAO;gBAAS;YAAQ;QAC9D,OAAO;YACL,QAAQ,GAAG,CAAC,GAAG;gBAAE;gBAAO;gBAAS;YAAQ;QAC3C;IACF;IACA,MAAM,UAAU,CAAC,GAAG,cAAc,UAAU,EAAE;IAC9C,MAAM,cAAc,CAAC,GAAG,gBAAgB,OAAO,EAAE;IACjD,MAAM,aAAa,YAAY,eAAe,SAAS;IACvD,CAAC,GAAG,gBAAgB,yBAAyB,EAAE;QAC7C,IAAI,YAAY;YACd,CAAC,GAAG,gBAAgB,IAAI,EAAE,aAAa,CAAC;gBACtC,EAAE,IAAI,CAAC,KAAK,CAAC;oBAAE,SAAS;gBAAQ;YAClC;QACF;IACF,GAAG;QAAC;KAAQ;IACZ,CAAC,GAAG,gBAAgB,IAAI,EAAE,SAAS,CAAC,GAAG;QACrC,IAAI,mBAAmB,OAAO,CAAC,IAAI,EAAE;YACnC,MAAM,MAAM,YAAY,SAAS,CAAC,CAAC,QAAU,MAAM,GAAG,KAAK,EAAE,GAAG;YAChE,YAAY,MAAM,CAAC,KAAK;QAC1B;IACF;IACA,CAAC,GAAG,gBAAgB,yBAAyB,EAC3C;QACE,CAAC,GAAG,gBAAgB,IAAI,EACtB,mBAAmB,OAAO,CAAC,IAAI,GAAG,mBAAmB,OAAO,GAAG,SAC/D,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE;YACnB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,EAAE,KAAK,GAAG;YACV,KAAK,IAAI;YACT,IAAI,cAAc,SAAS,QAAQ,SAAS,KAAI;gBAC9C,KAAK,KAAK,CAAC;oBAAE,SAAS;gBAAQ;YAChC;YACA,IAAI,SAAS;gBACX,WAAW,MAAM,QAAQ,GAAG;gBAC5B,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC,YAAY,OAAO,EAAE;oBAC7C,KAAK,MAAM,CAAC;gBACd,OAAO;oBACL,KAAK,KAAK,CAAC;oBACX,IAAI,YAAY,OAAO,EAAE;wBACvB,YAAY,OAAO,GAAG;oBACxB;gBACF;YACF;QACF;IAEJ,GACA,QAAQ,KAAK,IAAI;IAEnB,MAAM,oBAAoB,CAAC,SAAW,aAAa,GAAG,OAAO,aAAa,CAAC,OAAO,QAAQ,EAAE,MAAM,YAAY,GAAG,CAAC,CAAC,GAAG;YACpH,MAAM,EAAE,OAAO,EAAE,GAAG,QAAQ,GAAG,CAAC,MAAM,EAAE,IAAI;YAC5C,MAAM,OAAO,OAAO;gBAAE,GAAG,OAAO;YAAC,GAAG,EAAE,IAAI,EAAE,GAAG;YAC/C,OAAO,QAAQ,KAAK,IAAI,GAAG,aAAa,GAAG,OAAO,aAAa,CAC7D,KAAK,IAAI,EACT;gBACE,GAAG,KAAK,KAAK;gBACb,KAAK,gBAAgB,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,KAAK,gBAAgB,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,GAAG,EAAE,IAAI,CAAC,EAAE;gBACvF,KAAK,KAAK,GAAG;YACf,KACE;QACN;IACA,OAAO,MAAM;QAAC;QAAmB;KAAI,GAAG;AAC1C;AACA,IAAI,UAAU;AACd,SAAS,QAAQ,KAAK,EAAE,EAAE,GAAG,EAAE,OAAO,GAAG,EAAE,EAAE,eAAe;IAC1D,IAAI,SAAS,MAAM;QACjB,MAAM,SAAS,aAAa,GAAG,IAAI;QACnC,OAAO,MAAM,GAAG,CAAC,CAAC;YAChB,MAAM,IAAI,mBAAmB,gBAAgB,IAAI,CAC/C,CAAC,KAAO,GAAG,IAAI,KAAK,QAAQ,GAAG,KAAK,KAAK,QAAQ,SAAS,OAAM,CAAC,OAAO,GAAG,CAAC;YAE9E,IAAI,GAAG;gBACL,OAAO,GAAG,CAAC;gBACX,OAAO,EAAE,GAAG;YACd;YACA,OAAO;QACT;IACF;IACA,OAAO,gBAAgB,EAAE,CAAC,GAAG,CAAC,QAAQ,QAAQ,gBAAgB,EAAE,CAAC,GAAG,CAAC,QAAQ,MAAM,GAAG,CAAC,QAAQ,CAAC,GAAG,gBAAgB,OAAO,EAAE;AAC9H;AAEA,yBAAyB;AACzB,IAAI;AACJ,IAAI,YAAY,CAAC,EACf,SAAS,EACT,GAAG,eACJ,GAAG,CAAC,CAAC;IACJ,MAAM,CAAC,cAAc,IAAI,GAAG;+BAC1B,IAAM,CAAC;gBACL,SAAS;gBACT,SAAS;gBACT,iBAAiB;gBACjB,iBAAiB;gBACjB,GAAG,aAAa;YAClB,CAAC;8BACD,EAAE;IAEJ,CAAC,GAAG,gBAAgB,yBAAyB,EAAE;QAC7C,MAAM,gBAAgB,CAAC,GAAG,gBAAgB,QAAQ,EAChD,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YACP,IAAI,KAAK,CAAC;gBACR,SAAS,EAAE,OAAO;gBAClB,iBAAiB,EAAE,QAAQ;gBAC3B,SAAS,EAAE,OAAO;gBAClB,iBAAiB,EAAE,QAAQ;YAC7B;QACF,GACA;YAAE,WAAW,WAAW,WAAW,KAAK;QAAE;QAE5C,OAAO;YACL,CAAC,GAAG,gBAAgB,IAAI,EAAE,OAAO,MAAM,CAAC,eAAe,CAAC,QAAU,MAAM,IAAI;YAC5E;QACF;IACF,GAAG,EAAE;IACL,OAAO;AACT;AAEA,yBAAyB;AACzB,IAAI;AACJ,IAAI,YAAY,CAAC,EACf,SAAS,EACT,GAAG,eACJ;IACC,MAAM,CAAC,YAAY,IAAI,GAAG;+BACxB,IAAM,CAAC;gBACL,OAAO;gBACP,QAAQ;gBACR,GAAG,aAAa;YAClB,CAAC;8BACD,EAAE;IAEJ,CAAC,GAAG,gBAAgB,yBAAyB,EAAE;QAC7C,MAAM,gBAAgB,CAAC,GAAG,gBAAgB,QAAQ,EAChD,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE;YAChB,IAAI,KAAK,CAAC;gBACR;gBACA;gBACA,WAAW,WAAW,KAAK,CAAC,GAAG,OAAO,KAAK,WAAW,MAAM,CAAC,GAAG,OAAO;YACzE;QACF,GACA;YAAE,WAAW,WAAW,WAAW,KAAK;QAAE;QAE5C,OAAO;YACL,CAAC,GAAG,gBAAgB,IAAI,EAAE,OAAO,MAAM,CAAC,aAAa,CAAC,QAAU,MAAM,IAAI;YAC1E;QACF;IACF,GAAG,EAAE;IACL,OAAO;AACT;AAEA,yBAAyB;AACzB,IAAI;AACJ,IAAI;AACJ,IAAI,0BAA0B;IAC5B,KAAK;IACL,KAAK;AACP;AACA,SAAS,UAAU,KAAK,EAAE,IAAI;IAC5B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAC,GAAG,cAAc,QAAQ,EAAE;IAC5D,MAAM,MAAM,CAAC,GAAG,cAAc,MAAM,EAAE,KAAK;IAC3C,MAAM,UAAU,gBAAgB,EAAE,CAAC,GAAG,CAAC,UAAU;IACjD,MAAM,eAAe,UAAU,YAAY,CAAC;IAC5C,MAAM,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,iBAAiB,GAAG;IACxD,MAAM,wBAAwB,UAAU,OAAO;IAC/C,MAAM,CAAC,SAAS,IAAI,GAAG;+BAAU,IAAM,CAAC;gBAAE;gBAAM,GAAG,eAAe;YAAC,CAAC;8BAAG,EAAE;IACzE,CAAC,GAAG,gBAAgB,yBAAyB,EAAE;QAC7C,MAAM,UAAU,IAAI,OAAO;QAC3B,MAAM,EACJ,IAAI,EACJ,IAAI,EACJ,SAAS,KAAK,EACd,GAAG,UACJ,GAAG,yBAAyB,CAAC;QAC9B,IAAI,CAAC,WAAW,QAAQ,YAAY,OAAO,yBAAyB,aAClE;QACF,MAAM,sBAAsB,aAAa,GAAG,IAAI;QAChD,MAAM,UAAU;YACd,IAAI,KAAK;gBACP,IAAI,KAAK,CAAC;YACZ;YACA,YAAY;YACZ,MAAM,UAAU;gBACd,IAAI,MAAM;oBACR,IAAI,KAAK,CAAC;gBACZ;gBACA,YAAY;YACd;YACA,OAAO,OAAO,KAAK,IAAI;QACzB;QACA,MAAM,qBAAqB,CAAC;YAC1B,QAAQ,OAAO,CAAC,CAAC;gBACf,MAAM,UAAU,oBAAoB,GAAG,CAAC,MAAM,MAAM;gBACpD,IAAI,MAAM,cAAc,KAAK,QAAQ,UAAU;oBAC7C;gBACF;gBACA,IAAI,MAAM,cAAc,EAAE;oBACxB,MAAM,aAAa;oBACnB,IAAI,gBAAgB,EAAE,CAAC,GAAG,CAAC,aAAa;wBACtC,oBAAoB,GAAG,CAAC,MAAM,MAAM,EAAE;oBACxC,OAAO;wBACL,SAAS,SAAS,CAAC,MAAM,MAAM;oBACjC;gBACF,OAAO,IAAI,SAAS;oBAClB;oBACA,oBAAoB,MAAM,CAAC,MAAM,MAAM;gBACzC;YACF;QACF;QACA,MAAM,WAAW,IAAI,qBAAqB,oBAAoB;YAC5D,MAAM,QAAQ,KAAK,OAAO,IAAI,KAAK;YACnC,WAAW,OAAO,WAAW,YAAY,MAAM,OAAO,CAAC,UAAU,SAAS,uBAAuB,CAAC,OAAO;YACzG,GAAG,QAAQ;QACb;QACA,SAAS,OAAO,CAAC;QACjB,OAAO,IAAM,SAAS,SAAS,CAAC;IAClC,GAAG;QAAC;KAAsB;IAC1B,IAAI,SAAS;QACX,OAAO;YAAC;YAAK;SAAQ;IACvB;IACA,OAAO;QAAC;QAAK;KAAS;AACxB;AAEA,4BAA4B;AAC5B,SAAS,OAAO,EAAE,QAAQ,EAAE,GAAG,OAAO;IACpC,OAAO,SAAS,UAAU;AAC5B;AAEA,2BAA2B;AAC3B,IAAI;AACJ,SAAS,MAAM,EACb,KAAK,EACL,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,SAAS,SAAS,MAAM,MAAM,EAAE;IACtC,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM;QACtB,MAAM,SAAS,SAAS,MAAM;QAC9B,OAAO,gBAAgB,EAAE,CAAC,GAAG,CAAC,UAAU,OAAO,MAAM,CAAC,MAAM,IAAI;IAClE;AACF;AAEA,gCAAgC;AAChC,SAAS,WAAW,EAClB,KAAK,EACL,QAAQ,EACR,GAAG,OACJ;IACC,OAAO,cAAc,OAAO,OAAO;AACrC;AAEA,qBAAqB;AACrB,IAAI;AAEJ,uBAAuB;AACvB,IAAI;AACJ,IAAI;AACJ,IAAI,gBAAgB,cAAc;IAChC,YAAY,MAAM,EAAE,IAAI,CAAE;QACxB,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;QACd,uCAAuC,GACvC,IAAI,CAAC,IAAI,GAAG;QACZ,6CAA6C,GAC7C,IAAI,CAAC,OAAO,GAAG,aAAa,GAAG,IAAI;QACnC,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,gBAAgB,kBAAkB,KAAK;QACvD,MAAM,QAAQ,IAAI,CAAC,IAAI;QACvB,MAAM,WAAW,CAAC,GAAG,iBAAiB,eAAe,EAAE;QACvD,CAAC,GAAG,iBAAiB,WAAW,EAAE,IAAI,EAAE,SAAS,MAAM,CAAC;IAC1D;IACA,QAAQ,GAAG,EAAE;QACX,MAAM,QAAQ,IAAI,CAAC,IAAI;QACvB,MAAM,WAAW,IAAI,CAAC,GAAG;QACzB,IAAI,CAAC,CAAC,GAAG,gBAAgB,OAAO,EAAE,OAAO,WAAW;YAClD,CAAC,GAAG,iBAAiB,WAAW,EAAE,IAAI,EAAE,QAAQ,CAAC;YACjD,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,IAAI;QACjC;QACA,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,UAAU,IAAI,CAAC,OAAO,GAAG;YACzC,WAAW,IAAI;QACjB;IACF;IACA,OAAO;QACL,MAAM,SAAS,gBAAgB,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,aAAa,IAAI,CAAC,GAAG,gBAAgB,OAAO,EAAE,CAAC,GAAG,gBAAgB,aAAa,EAAE,IAAI,CAAC,MAAM;QACjL,OAAO,IAAI,CAAC,IAAI,IAAI;IACtB;IACA,SAAS;QACP,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,GAAG;YACzC,IAAI,CAAC,IAAI,GAAG;YACZ,CAAC,GAAG,gBAAgB,IAAI,EAAE,CAAC,GAAG,iBAAiB,UAAU,EAAE,IAAI,GAAG,CAAC;gBACjE,KAAK,IAAI,GAAG;YACd;YACA,IAAI,gBAAgB,OAAO,CAAC,aAAa,EAAE;gBACzC,gBAAgB,GAAG,CAAC,cAAc,CAAC,IAAM,IAAI,CAAC,OAAO;gBACrD,WAAW,IAAI;YACjB,OAAO;gBACL,gBAAgB,SAAS,CAAC,KAAK,CAAC,IAAI;YACtC;QACF;IACF;IACA,gDAAgD;IAChD,UAAU;QACR,IAAI,WAAW;QACf,CAAC,GAAG,gBAAgB,IAAI,EAAE,CAAC,GAAG,gBAAgB,OAAO,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC;YACpE,IAAI,CAAC,GAAG,gBAAgB,aAAa,EAAE,SAAS;gBAC9C,CAAC,GAAG,gBAAgB,gBAAgB,EAAE,QAAQ,IAAI;YACpD;YACA,IAAI,aAAa,SAAS;gBACxB,IAAI,CAAC,OAAO,IAAI,EAAE;oBAChB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;gBACnB;gBACA,WAAW,KAAK,GAAG,CAAC,UAAU,OAAO,QAAQ,GAAG;YAClD;QACF;QACA,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM;IACb;IACA,wDAAwD;IACxD,UAAU;QACR,CAAC,GAAG,gBAAgB,IAAI,EAAE,CAAC,GAAG,gBAAgB,OAAO,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC;YACpE,IAAI,CAAC,GAAG,gBAAgB,aAAa,EAAE,SAAS;gBAC9C,CAAC,GAAG,gBAAgB,mBAAmB,EAAE,QAAQ,IAAI;YACvD;QACF;QACA,IAAI,CAAC,OAAO,CAAC,KAAK;QAClB,WAAW,IAAI;IACjB;IACA,cAAc,GACd,cAAc,KAAK,EAAE;QACnB,IAAI,MAAM,IAAI,IAAI,UAAU;YAC1B,IAAI,MAAM,IAAI,EAAE;gBACd,IAAI,CAAC,OAAO;YACd,OAAO;gBACL,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,MAAM;gBAC7B,IAAI,CAAC,MAAM;YACb;QACF,OAAO,IAAI,MAAM,IAAI,IAAI,QAAQ;YAC/B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,MAAM;QAClC,OAAO,IAAI,MAAM,IAAI,IAAI,YAAY;YACnC,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,gBAAgB,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAC9D,CAAC,SAAS,SAAW,KAAK,GAAG,CAAC,SAAS,CAAC,aAAa,UAAU,OAAO,QAAQ,GAAG,CAAC,IAAI,IACtF;QAEJ;IACF;AACF;AACA,SAAS,OAAO,MAAM;IACpB,OAAO,OAAO,IAAI,KAAK;AACzB;AACA,SAAS,UAAU,MAAM;IACvB,OAAO,CAAC,OAAO,IAAI,IAAI,MAAM,IAAI,CAAC,QAAQ,KAAK,CAAC;AAClD;AACA,SAAS,WAAW,IAAI;IACtB,IAAI,CAAC,KAAK,IAAI,EAAE;QACd,KAAK,IAAI,GAAG;QACZ,CAAC,GAAG,gBAAgB,IAAI,EAAE,CAAC,GAAG,iBAAiB,UAAU,EAAE,OAAO,CAAC;YACjE,KAAK,IAAI,GAAG;QACd;QACA,CAAC,GAAG,gBAAgB,kBAAkB,EAAE,MAAM;YAC5C,MAAM;YACN,QAAQ;QACV;IACF;AACF;AAEA,qBAAqB;AACrB,IAAI,KAAK,CAAC,QAAQ,GAAG,OAAS,IAAI,cAAc,QAAQ;AACxD,IAAI,cAAc,CAAC,QAAQ,GAAG,OAAS,CAAC,CAAC,GAAG,gBAAgB,oBAAoB,KAAK,IAAI,cAAc,QAAQ,KAAK;AAEpH,iBAAiB;AACjB,IAAI;AACJ,gBAAgB,OAAO,CAAC,MAAM,CAAC;IAC7B,0BAA0B,gBAAgB,wBAAwB;IAClE,IAAI,CAAC,QAAQ,OAAS,IAAI,cAAc,QAAQ;AAClD;AACA,IAAI,SAAS,gBAAgB,SAAS,CAAC,OAAO;AAE9C,eAAe;AACf,IAAI;AACJ,WAAW,8KAA6C,OAAO,OAAO;AACtE,6DAA6D;AAC7D,KAAK,CAAC,OAAO,OAAO,GAAG;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;sKACA;AACF,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5081, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5086, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-spring%2Bcore%4010.0.1_react%4019.0.0/node_modules/%40react-spring/core/dist/cjs/index.js"], "sourcesContent": ["'use strict'\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./react-spring_core.production.min.cjs')\n} else {\n  module.exports = require('./react-spring_core.development.cjs')\n}"], "names": [], "mappings": "AACI;AADJ;AACA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5093, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5098, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-spring%2Bthree%4010.0.1_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0_3f41a382e4a72d11f8efba93b74546c7/node_modules/%40react-spring/three/dist/cjs/react-spring_three.development.cjs"], "sourcesContent": ["\"use strict\";\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  a: () => animated,\n  animated: () => animated\n});\nmodule.exports = __toCommonJS(src_exports);\nvar import_fiber2 = require(\"@react-three/fiber\");\nvar import_core = require(\"@react-spring/core\");\nvar import_shared = require(\"@react-spring/shared\");\nvar import_animated = require(\"@react-spring/animated\");\n\n// src/primitives.ts\nvar THREE = __toESM(require(\"three\"));\nvar import_fiber = require(\"@react-three/fiber\");\nvar primitives = [\"primitive\"].concat(\n  Object.keys(THREE).filter((key) => /^[A-Z]/.test(key)).map((key) => key[0].toLowerCase() + key.slice(1))\n);\n\n// src/index.ts\n__reExport(src_exports, require(\"@react-spring/core\"), module.exports);\nimport_core.Globals.assign({\n  createStringInterpolator: import_shared.createStringInterpolator,\n  colors: import_shared.colors,\n  frameLoop: \"demand\"\n});\n(0, import_fiber2.addEffect)(() => {\n  import_shared.raf.advance();\n});\nvar host = (0, import_animated.createHost)(primitives, {\n  applyAnimatedValues: import_fiber2.applyProps\n});\nvar animated = host.animated;\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  a,\n  animated,\n  ...require(\"@react-spring/core\")\n});\n"], "names": [], "mappings": "AAAA;AACA,IAAI,WAAW,OAAO,MAAM;AAC5B,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,mBAAmB,OAAO,wBAAwB;AACtD,IAAI,oBAAoB,OAAO,mBAAmB;AAClD,IAAI,eAAe,OAAO,cAAc;AACxC,IAAI,eAAe,OAAO,SAAS,CAAC,cAAc;AAClD,IAAI,WAAW,CAAC,QAAQ;IACtB,IAAK,IAAI,QAAQ,IACf,UAAU,QAAQ,MAAM;QAAE,KAAK,GAAG,CAAC,KAAK;QAAE,YAAY;IAAK;AAC/D;AACA,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ;IACnC,IAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;QAClE,KAAK,IAAI,OAAO,kBAAkB,MAChC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,QAAQ,QAAQ,QACzC,UAAU,IAAI,KAAK;YAAE,KAAK,IAAM,IAAI,CAAC,IAAI;YAAE,YAAY,CAAC,CAAC,OAAO,iBAAiB,MAAM,IAAI,KAAK,KAAK,UAAU;QAAC;IACtH;IACA,OAAO;AACT;AACA,IAAI,aAAa,CAAC,QAAQ,KAAK,eAAiB,CAAC,YAAY,QAAQ,KAAK,YAAY,gBAAgB,YAAY,cAAc,KAAK,UAAU;AAC/I,IAAI,UAAU,CAAC,KAAK,YAAY,SAAW,CAAC,SAAS,OAAO,OAAO,SAAS,aAAa,QAAQ,CAAC,GAAG,YACnG,sEAAsE;IACtE,iEAAiE;IACjE,sEAAsE;IACtE,qEAAqE;IACrE,cAAc,CAAC,OAAO,CAAC,IAAI,UAAU,GAAG,UAAU,QAAQ,WAAW;QAAE,OAAO;QAAK,YAAY;IAAK,KAAK,QACzG,IACD;AACD,IAAI,eAAe,CAAC,MAAQ,YAAY,UAAU,CAAC,GAAG,cAAc;QAAE,OAAO;IAAK,IAAI;AAEtF,eAAe;AACf,IAAI,cAAc,CAAC;AACnB,SAAS,aAAa;IACpB,GAAG,IAAM;IACT,UAAU,IAAM;AAClB;AACA,OAAO,OAAO,GAAG,aAAa;AAC9B,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,oBAAoB;AACpB,IAAI,QAAQ;AACZ,IAAI;AACJ,IAAI,aAAa;IAAC;CAAY,CAAC,MAAM,CACnC,OAAO,IAAI,CAAC,OAAO,MAAM,CAAC,CAAC,MAAQ,SAAS,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,MAAQ,GAAG,CAAC,EAAE,CAAC,WAAW,KAAK,IAAI,KAAK,CAAC;AAGvG,eAAe;AACf,WAAW,yLAA4C,OAAO,OAAO;AACrE,YAAY,OAAO,CAAC,MAAM,CAAC;IACzB,0BAA0B,cAAc,wBAAwB;IAChE,QAAQ,cAAc,MAAM;IAC5B,WAAW;AACb;AACA,CAAC,GAAG,cAAc,SAAS,EAAE;IAC3B,cAAc,GAAG,CAAC,OAAO;AAC3B;AACA,IAAI,OAAO,CAAC,GAAG,gBAAgB,UAAU,EAAE,YAAY;IACrD,qBAAqB,cAAc,UAAU;AAC/C;AACA,IAAI,WAAW,KAAK,QAAQ;AAC5B,6DAA6D;AAC7D,KAAK,CAAC,OAAO,OAAO,GAAG;IACrB;IACA;iLACA;AACF,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5169, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5174, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-spring%2Bthree%4010.0.1_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0_3f41a382e4a72d11f8efba93b74546c7/node_modules/%40react-spring/three/dist/cjs/index.js"], "sourcesContent": ["'use strict'\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./react-spring_three.production.min.cjs')\n} else {\n  module.exports = require('./react-spring_three.development.cjs')\n}"], "names": [], "mappings": "AACI;AADJ;AACA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5181, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}