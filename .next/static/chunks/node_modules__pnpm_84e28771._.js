(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules__pnpm_84e28771._.js", {

"[project]/node_modules/.pnpm/hls.js@1.6.4/node_modules/hls.js/dist/hls.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/.pnpm/hls.js@1.6.4/node_modules/hls.js/dist/hls.mjs [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/@mediapipe+tasks-vision@0.10.17/node_modules/@mediapipe/tasks-vision/vision_bundle.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/2809b_@mediapipe_tasks-vision_vision_bundle_mjs_96ceb3d7._.js",
  "static/chunks/2809b_@mediapipe_tasks-vision_vision_bundle_mjs_2154205b._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@mediapipe+tasks-vision@0.10.17/node_modules/@mediapipe/tasks-vision/vision_bundle.mjs [app-client] (ecmascript)");
    });
});
}}),
}]);