{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/use-sync-external-store%401.5.0_react%4019.0.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,OAAO,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAO,MAAM,KAAK,MAAM;IACxE;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,qBACE,KAAK,MAAM,MAAM,eAAe,IAChC,CAAC,AAAC,oBAAoB,CAAC,GACvB,QAAQ,KAAK,CACX,iMACD;QACH,IAAI,QAAQ;QACZ,IAAI,CAAC,4BAA4B;YAC/B,IAAI,cAAc;YAClB,SAAS,OAAO,gBACd,CAAC,QAAQ,KAAK,CACZ,yEAED,6BAA6B,CAAC,CAAE;QACrC;QACA,cAAc,SAAS;YACrB,MAAM;gBAAE,OAAO;gBAAO,aAAa;YAAY;QACjD;QACA,IAAI,OAAO,WAAW,CAAC,EAAE,CAAC,IAAI,EAC5B,cAAc,WAAW,CAAC,EAAE;QAC9B;sDACE;gBACE,KAAK,KAAK,GAAG;gBACb,KAAK,WAAW,GAAG;gBACnB,uBAAuB,SAAS,YAAY;oBAAE,MAAM;gBAAK;YAC3D;qDACA;YAAC;YAAW;YAAO;SAAY;QAEjC;gDACE;gBACE,uBAAuB,SAAS,YAAY;oBAAE,MAAM;gBAAK;gBACzD,OAAO;wDAAU;wBACf,uBAAuB,SAAS,YAAY;4BAAE,MAAM;wBAAK;oBAC3D;;YACF;+CACA;YAAC;SAAU;QAEb,cAAc;QACd,OAAO;IACT;IACA,SAAS,uBAAuB,IAAI;QAClC,IAAI,oBAAoB,KAAK,WAAW;QACxC,OAAO,KAAK,KAAK;QACjB,IAAI;YACF,IAAI,YAAY;YAChB,OAAO,CAAC,SAAS,MAAM;QACzB,EAAE,OAAO,OAAO;YACd,OAAO,CAAC;QACV;IACF;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,OAAO;IACT;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,IAAI,kMACF,WAAW,eAAe,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,IACzD,WAAW,MAAM,QAAQ,EACzB,YAAY,MAAM,SAAS,EAC3B,kBAAkB,MAAM,eAAe,EACvC,gBAAgB,MAAM,aAAa,EACnC,oBAAoB,CAAC,GACrB,6BAA6B,CAAC,GAC9B,OACE,gBAAgB,OAAO,UACvB,gBAAgB,OAAO,OAAO,QAAQ,IACtC,gBAAgB,OAAO,OAAO,QAAQ,CAAC,aAAa,GAChD,yBACA;IACR,QAAQ,oBAAoB,GAC1B,KAAK,MAAM,MAAM,oBAAoB,GAAG,MAAM,oBAAoB,GAAG;IACvE,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/use-sync-external-store%401.5.0_react%4019.0.0/node_modules/use-sync-external-store/shim/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/use-sync-external-store%401.5.0_react%4019.0.0/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      shim = require(\"use-sync-external-store/shim\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = shim.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (\n      subscribe,\n      getSnapshot,\n      getServerSnapshot,\n      selector,\n      isEqual\n    ) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = { hasValue: !1, value: null };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(\n        function () {\n          function memoizedSelector(nextSnapshot) {\n            if (!hasMemo) {\n              hasMemo = !0;\n              memoizedSnapshot = nextSnapshot;\n              nextSnapshot = selector(nextSnapshot);\n              if (void 0 !== isEqual && inst.hasValue) {\n                var currentSelection = inst.value;\n                if (isEqual(currentSelection, nextSnapshot))\n                  return (memoizedSelection = currentSelection);\n              }\n              return (memoizedSelection = nextSnapshot);\n            }\n            currentSelection = memoizedSelection;\n            if (objectIs(memoizedSnapshot, nextSnapshot))\n              return currentSelection;\n            var nextSelection = selector(nextSnapshot);\n            if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n              return (memoizedSnapshot = nextSnapshot), currentSelection;\n            memoizedSnapshot = nextSnapshot;\n            return (memoizedSelection = nextSelection);\n          }\n          var hasMemo = !1,\n            memoizedSnapshot,\n            memoizedSelection,\n            maybeGetServerSnapshot =\n              void 0 === getServerSnapshot ? null : getServerSnapshot;\n          return [\n            function () {\n              return memoizedSelector(getSnapshot());\n            },\n            null === maybeGetServerSnapshot\n              ? void 0\n              : function () {\n                  return memoizedSelector(maybeGetServerSnapshot());\n                }\n          ];\n        },\n        [getSnapshot, getServerSnapshot, selector, isEqual]\n      );\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(\n        function () {\n          inst.hasValue = !0;\n          inst.value = value;\n        },\n        [value]\n      );\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,OAAO,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAO,MAAM,KAAK,MAAM;IACxE;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,IAAI,kMACF,wLACA,WAAW,eAAe,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,IACzD,uBAAuB,KAAK,oBAAoB,EAChD,SAAS,MAAM,MAAM,EACrB,YAAY,MAAM,SAAS,EAC3B,UAAU,MAAM,OAAO,EACvB,gBAAgB,MAAM,aAAa;IACrC,QAAQ,gCAAgC,GAAG,SACzC,SAAS,EACT,WAAW,EACX,iBAAiB,EACjB,QAAQ,EACR,OAAO;QAEP,IAAI,UAAU,OAAO;QACrB,IAAI,SAAS,QAAQ,OAAO,EAAE;YAC5B,IAAI,OAAO;gBAAE,UAAU,CAAC;gBAAG,OAAO;YAAK;YACvC,QAAQ,OAAO,GAAG;QACpB,OAAO,OAAO,QAAQ,OAAO;QAC7B,UAAU,QACR;YACE,SAAS,iBAAiB,YAAY;gBACpC,IAAI,CAAC,SAAS;oBACZ,UAAU,CAAC;oBACX,mBAAmB;oBACnB,eAAe,SAAS;oBACxB,IAAI,KAAK,MAAM,WAAW,KAAK,QAAQ,EAAE;wBACvC,IAAI,mBAAmB,KAAK,KAAK;wBACjC,IAAI,QAAQ,kBAAkB,eAC5B,OAAQ,oBAAoB;oBAChC;oBACA,OAAQ,oBAAoB;gBAC9B;gBACA,mBAAmB;gBACnB,IAAI,SAAS,kBAAkB,eAC7B,OAAO;gBACT,IAAI,gBAAgB,SAAS;gBAC7B,IAAI,KAAK,MAAM,WAAW,QAAQ,kBAAkB,gBAClD,OAAO,AAAC,mBAAmB,cAAe;gBAC5C,mBAAmB;gBACnB,OAAQ,oBAAoB;YAC9B;YACA,IAAI,UAAU,CAAC,GACb,kBACA,mBACA,yBACE,KAAK,MAAM,oBAAoB,OAAO;YAC1C,OAAO;gBACL;oBACE,OAAO,iBAAiB;gBAC1B;gBACA,SAAS,yBACL,KAAK,IACL;oBACE,OAAO,iBAAiB;gBAC1B;aACL;QACH,GACA;YAAC;YAAa;YAAmB;YAAU;SAAQ;QAErD,IAAI,QAAQ,qBAAqB,WAAW,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE;QAClE,UACE;YACE,KAAK,QAAQ,GAAG,CAAC;YACjB,KAAK,KAAK,GAAG;QACf,GACA;YAAC;SAAM;QAET,cAAc;QACd,OAAO;IACT;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/use-sync-external-store%401.5.0_react%4019.0.0/node_modules/use-sync-external-store/shim/with-selector.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/zustand%405.0.5_%40types%2Breact%4019.0.10_react%4019.0.0_use-sync-external-store%401.5.0_react%4019.0.0_/node_modules/zustand/esm/vanilla.mjs"], "sourcesContent": ["const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\nexport { createStore };\n"], "names": [], "mappings": ";;;AAAA,MAAM,kBAAkB,CAAC;IACvB,IAAI;IACJ,MAAM,YAAY,aAAa,GAAG,IAAI;IACtC,MAAM,WAAW,CAAC,SAAS;QACzB,MAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,SAAS;QACnE,IAAI,CAAC,OAAO,EAAE,CAAC,WAAW,QAAQ;YAChC,MAAM,gBAAgB;YACtB,QAAQ,CAAC,WAAW,OAAO,UAAU,OAAO,cAAc,YAAY,cAAc,IAAI,IAAI,YAAY,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;YACjI,UAAU,OAAO,CAAC,CAAC,WAAa,SAAS,OAAO;QAClD;IACF;IACA,MAAM,WAAW,IAAM;IACvB,MAAM,kBAAkB,IAAM;IAC9B,MAAM,YAAY,CAAC;QACjB,UAAU,GAAG,CAAC;QACd,OAAO,IAAM,UAAU,MAAM,CAAC;IAChC;IACA,MAAM,MAAM;QAAE;QAAU;QAAU;QAAiB;IAAU;IAC7D,MAAM,eAAe,QAAQ,YAAY,UAAU,UAAU;IAC7D,OAAO;AACT;AACA,MAAM,cAAc,CAAC,cAAgB,cAAc,gBAAgB,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/zustand%405.0.5_%40types%2Breact%4019.0.10_react%4019.0.0_use-sync-external-store%401.5.0_react%4019.0.0_/node_modules/zustand/esm/traditional.mjs"], "sourcesContent": ["import React from 'react';\nimport useSyncExternalStoreExports from 'use-sync-external-store/shim/with-selector.js';\nimport { createStore } from 'zustand/vanilla';\n\nconst { useSyncExternalStoreWithSelector } = useSyncExternalStoreExports;\nconst identity = (arg) => arg;\nfunction useStoreWithEqualityFn(api, selector = identity, equalityFn) {\n  const slice = useSyncExternalStoreWithSelector(\n    api.subscribe,\n    api.getState,\n    api.getInitialState,\n    selector,\n    equalityFn\n  );\n  React.useDebugValue(slice);\n  return slice;\n}\nconst createWithEqualityFnImpl = (createState, defaultEqualityFn) => {\n  const api = createStore(createState);\n  const useBoundStoreWithEqualityFn = (selector, equalityFn = defaultEqualityFn) => useStoreWithEqualityFn(api, selector, equalityFn);\n  Object.assign(useBoundStoreWithEqualityFn, api);\n  return useBoundStoreWithEqualityFn;\n};\nconst createWithEqualityFn = (createState, defaultEqualityFn) => createState ? createWithEqualityFnImpl(createState, defaultEqualityFn) : createWithEqualityFnImpl;\n\nexport { createWithEqualityFn, useStoreWithEqualityFn };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,MAAM,EAAE,gCAAgC,EAAE,GAAG,kRAAA,CAAA,UAA2B;AACxE,MAAM,WAAW,CAAC,MAAQ;AAC1B,SAAS,uBAAuB,GAAG,EAAE,WAAW,QAAQ,EAAE,UAAU;IAClE,MAAM,QAAQ,iCACZ,IAAI,SAAS,EACb,IAAI,QAAQ,EACZ,IAAI,eAAe,EACnB,UACA;IAEF,4RAAA,CAAA,UAAK,CAAC,aAAa,CAAC;IACpB,OAAO;AACT;AACA,MAAM,2BAA2B,CAAC,aAAa;IAC7C,MAAM,MAAM,CAAA,GAAA,kUAAA,CAAA,cAAW,AAAD,EAAE;IACxB,MAAM,8BAA8B,CAAC,UAAU,aAAa,iBAAiB,GAAK,uBAAuB,KAAK,UAAU;IACxH,OAAO,MAAM,CAAC,6BAA6B;IAC3C,OAAO;AACT;AACA,MAAM,uBAAuB,CAAC,aAAa,oBAAsB,cAAc,yBAAyB,aAAa,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/scheduler%400.25.0/node_modules/scheduler/cjs/scheduler.development.js"], "sourcesContent": ["/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function performWorkUntilDeadline() {\n      if (isMessageLoopRunning) {\n        var currentTime = exports.unstable_now();\n        startTime = currentTime;\n        var hasMoreWork = !0;\n        try {\n          a: {\n            isHostCallbackScheduled = !1;\n            isHostTimeoutScheduled &&\n              ((isHostTimeoutScheduled = !1),\n              localClearTimeout(taskTimeoutID),\n              (taskTimeoutID = -1));\n            isPerformingWork = !0;\n            var previousPriorityLevel = currentPriorityLevel;\n            try {\n              b: {\n                advanceTimers(currentTime);\n                for (\n                  currentTask = peek(taskQueue);\n                  null !== currentTask &&\n                  !(\n                    currentTask.expirationTime > currentTime &&\n                    shouldYieldToHost()\n                  );\n\n                ) {\n                  var callback = currentTask.callback;\n                  if (\"function\" === typeof callback) {\n                    currentTask.callback = null;\n                    currentPriorityLevel = currentTask.priorityLevel;\n                    var continuationCallback = callback(\n                      currentTask.expirationTime <= currentTime\n                    );\n                    currentTime = exports.unstable_now();\n                    if (\"function\" === typeof continuationCallback) {\n                      currentTask.callback = continuationCallback;\n                      advanceTimers(currentTime);\n                      hasMoreWork = !0;\n                      break b;\n                    }\n                    currentTask === peek(taskQueue) && pop(taskQueue);\n                    advanceTimers(currentTime);\n                  } else pop(taskQueue);\n                  currentTask = peek(taskQueue);\n                }\n                if (null !== currentTask) hasMoreWork = !0;\n                else {\n                  var firstTimer = peek(timerQueue);\n                  null !== firstTimer &&\n                    requestHostTimeout(\n                      handleTimeout,\n                      firstTimer.startTime - currentTime\n                    );\n                  hasMoreWork = !1;\n                }\n              }\n              break a;\n            } finally {\n              (currentTask = null),\n                (currentPriorityLevel = previousPriorityLevel),\n                (isPerformingWork = !1);\n            }\n            hasMoreWork = void 0;\n          }\n        } finally {\n          hasMoreWork\n            ? schedulePerformWorkUntilDeadline()\n            : (isMessageLoopRunning = !1);\n        }\n      }\n    }\n    function push(heap, node) {\n      var index = heap.length;\n      heap.push(node);\n      a: for (; 0 < index; ) {\n        var parentIndex = (index - 1) >>> 1,\n          parent = heap[parentIndex];\n        if (0 < compare(parent, node))\n          (heap[parentIndex] = node),\n            (heap[index] = parent),\n            (index = parentIndex);\n        else break a;\n      }\n    }\n    function peek(heap) {\n      return 0 === heap.length ? null : heap[0];\n    }\n    function pop(heap) {\n      if (0 === heap.length) return null;\n      var first = heap[0],\n        last = heap.pop();\n      if (last !== first) {\n        heap[0] = last;\n        a: for (\n          var index = 0, length = heap.length, halfLength = length >>> 1;\n          index < halfLength;\n\n        ) {\n          var leftIndex = 2 * (index + 1) - 1,\n            left = heap[leftIndex],\n            rightIndex = leftIndex + 1,\n            right = heap[rightIndex];\n          if (0 > compare(left, last))\n            rightIndex < length && 0 > compare(right, left)\n              ? ((heap[index] = right),\n                (heap[rightIndex] = last),\n                (index = rightIndex))\n              : ((heap[index] = left),\n                (heap[leftIndex] = last),\n                (index = leftIndex));\n          else if (rightIndex < length && 0 > compare(right, last))\n            (heap[index] = right),\n              (heap[rightIndex] = last),\n              (index = rightIndex);\n          else break a;\n        }\n      }\n      return first;\n    }\n    function compare(a, b) {\n      var diff = a.sortIndex - b.sortIndex;\n      return 0 !== diff ? diff : a.id - b.id;\n    }\n    function advanceTimers(currentTime) {\n      for (var timer = peek(timerQueue); null !== timer; ) {\n        if (null === timer.callback) pop(timerQueue);\n        else if (timer.startTime <= currentTime)\n          pop(timerQueue),\n            (timer.sortIndex = timer.expirationTime),\n            push(taskQueue, timer);\n        else break;\n        timer = peek(timerQueue);\n      }\n    }\n    function handleTimeout(currentTime) {\n      isHostTimeoutScheduled = !1;\n      advanceTimers(currentTime);\n      if (!isHostCallbackScheduled)\n        if (null !== peek(taskQueue))\n          (isHostCallbackScheduled = !0), requestHostCallback();\n        else {\n          var firstTimer = peek(timerQueue);\n          null !== firstTimer &&\n            requestHostTimeout(\n              handleTimeout,\n              firstTimer.startTime - currentTime\n            );\n        }\n    }\n    function shouldYieldToHost() {\n      return exports.unstable_now() - startTime < frameInterval ? !1 : !0;\n    }\n    function requestHostCallback() {\n      isMessageLoopRunning ||\n        ((isMessageLoopRunning = !0), schedulePerformWorkUntilDeadline());\n    }\n    function requestHostTimeout(callback, ms) {\n      taskTimeoutID = localSetTimeout(function () {\n        callback(exports.unstable_now());\n      }, ms);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    exports.unstable_now = void 0;\n    if (\n      \"object\" === typeof performance &&\n      \"function\" === typeof performance.now\n    ) {\n      var localPerformance = performance;\n      exports.unstable_now = function () {\n        return localPerformance.now();\n      };\n    } else {\n      var localDate = Date,\n        initialTime = localDate.now();\n      exports.unstable_now = function () {\n        return localDate.now() - initialTime;\n      };\n    }\n    var taskQueue = [],\n      timerQueue = [],\n      taskIdCounter = 1,\n      currentTask = null,\n      currentPriorityLevel = 3,\n      isPerformingWork = !1,\n      isHostCallbackScheduled = !1,\n      isHostTimeoutScheduled = !1,\n      localSetTimeout = \"function\" === typeof setTimeout ? setTimeout : null,\n      localClearTimeout =\n        \"function\" === typeof clearTimeout ? clearTimeout : null,\n      localSetImmediate =\n        \"undefined\" !== typeof setImmediate ? setImmediate : null,\n      isMessageLoopRunning = !1,\n      taskTimeoutID = -1,\n      frameInterval = 5,\n      startTime = -1;\n    if (\"function\" === typeof localSetImmediate)\n      var schedulePerformWorkUntilDeadline = function () {\n        localSetImmediate(performWorkUntilDeadline);\n      };\n    else if (\"undefined\" !== typeof MessageChannel) {\n      var channel = new MessageChannel(),\n        port = channel.port2;\n      channel.port1.onmessage = performWorkUntilDeadline;\n      schedulePerformWorkUntilDeadline = function () {\n        port.postMessage(null);\n      };\n    } else\n      schedulePerformWorkUntilDeadline = function () {\n        localSetTimeout(performWorkUntilDeadline, 0);\n      };\n    exports.unstable_IdlePriority = 5;\n    exports.unstable_ImmediatePriority = 1;\n    exports.unstable_LowPriority = 4;\n    exports.unstable_NormalPriority = 3;\n    exports.unstable_Profiling = null;\n    exports.unstable_UserBlockingPriority = 2;\n    exports.unstable_cancelCallback = function (task) {\n      task.callback = null;\n    };\n    exports.unstable_continueExecution = function () {\n      isHostCallbackScheduled ||\n        isPerformingWork ||\n        ((isHostCallbackScheduled = !0), requestHostCallback());\n    };\n    exports.unstable_forceFrameRate = function (fps) {\n      0 > fps || 125 < fps\n        ? console.error(\n            \"forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported\"\n          )\n        : (frameInterval = 0 < fps ? Math.floor(1e3 / fps) : 5);\n    };\n    exports.unstable_getCurrentPriorityLevel = function () {\n      return currentPriorityLevel;\n    };\n    exports.unstable_getFirstCallbackNode = function () {\n      return peek(taskQueue);\n    };\n    exports.unstable_next = function (eventHandler) {\n      switch (currentPriorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n          var priorityLevel = 3;\n          break;\n        default:\n          priorityLevel = currentPriorityLevel;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_pauseExecution = function () {};\n    exports.unstable_requestPaint = function () {};\n    exports.unstable_runWithPriority = function (priorityLevel, eventHandler) {\n      switch (priorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n        case 4:\n        case 5:\n          break;\n        default:\n          priorityLevel = 3;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_scheduleCallback = function (\n      priorityLevel,\n      callback,\n      options\n    ) {\n      var currentTime = exports.unstable_now();\n      \"object\" === typeof options && null !== options\n        ? ((options = options.delay),\n          (options =\n            \"number\" === typeof options && 0 < options\n              ? currentTime + options\n              : currentTime))\n        : (options = currentTime);\n      switch (priorityLevel) {\n        case 1:\n          var timeout = -1;\n          break;\n        case 2:\n          timeout = 250;\n          break;\n        case 5:\n          timeout = 1073741823;\n          break;\n        case 4:\n          timeout = 1e4;\n          break;\n        default:\n          timeout = 5e3;\n      }\n      timeout = options + timeout;\n      priorityLevel = {\n        id: taskIdCounter++,\n        callback: callback,\n        priorityLevel: priorityLevel,\n        startTime: options,\n        expirationTime: timeout,\n        sortIndex: -1\n      };\n      options > currentTime\n        ? ((priorityLevel.sortIndex = options),\n          push(timerQueue, priorityLevel),\n          null === peek(taskQueue) &&\n            priorityLevel === peek(timerQueue) &&\n            (isHostTimeoutScheduled\n              ? (localClearTimeout(taskTimeoutID), (taskTimeoutID = -1))\n              : (isHostTimeoutScheduled = !0),\n            requestHostTimeout(handleTimeout, options - currentTime)))\n        : ((priorityLevel.sortIndex = timeout),\n          push(taskQueue, priorityLevel),\n          isHostCallbackScheduled ||\n            isPerformingWork ||\n            ((isHostCallbackScheduled = !0), requestHostCallback()));\n      return priorityLevel;\n    };\n    exports.unstable_shouldYield = shouldYieldToHost;\n    exports.unstable_wrapCallback = function (callback) {\n      var parentPriorityLevel = currentPriorityLevel;\n      return function () {\n        var previousPriorityLevel = currentPriorityLevel;\n        currentPriorityLevel = parentPriorityLevel;\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          currentPriorityLevel = previousPriorityLevel;\n        }\n      };\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS;QACP,IAAI,sBAAsB;YACxB,IAAI,cAAc,QAAQ,YAAY;YACtC,YAAY;YACZ,IAAI,cAAc,CAAC;YACnB,IAAI;gBACF,GAAG;oBACD,0BAA0B,CAAC;oBAC3B,0BACE,CAAC,AAAC,yBAAyB,CAAC,GAC5B,kBAAkB,gBACjB,gBAAgB,CAAC,CAAE;oBACtB,mBAAmB,CAAC;oBACpB,IAAI,wBAAwB;oBAC5B,IAAI;wBACF,GAAG;4BACD,cAAc;4BACd,IACE,cAAc,KAAK,YACnB,SAAS,eACT,CAAC,CACC,YAAY,cAAc,GAAG,eAC7B,mBACF,GAEA;gCACA,IAAI,WAAW,YAAY,QAAQ;gCACnC,IAAI,eAAe,OAAO,UAAU;oCAClC,YAAY,QAAQ,GAAG;oCACvB,uBAAuB,YAAY,aAAa;oCAChD,IAAI,uBAAuB,SACzB,YAAY,cAAc,IAAI;oCAEhC,cAAc,QAAQ,YAAY;oCAClC,IAAI,eAAe,OAAO,sBAAsB;wCAC9C,YAAY,QAAQ,GAAG;wCACvB,cAAc;wCACd,cAAc,CAAC;wCACf,MAAM;oCACR;oCACA,gBAAgB,KAAK,cAAc,IAAI;oCACvC,cAAc;gCAChB,OAAO,IAAI;gCACX,cAAc,KAAK;4BACrB;4BACA,IAAI,SAAS,aAAa,cAAc,CAAC;iCACpC;gCACH,IAAI,aAAa,KAAK;gCACtB,SAAS,cACP,mBACE,eACA,WAAW,SAAS,GAAG;gCAE3B,cAAc,CAAC;4BACjB;wBACF;wBACA,MAAM;oBACR,SAAU;wBACP,cAAc,MACZ,uBAAuB,uBACvB,mBAAmB,CAAC;oBACzB;oBACA,cAAc,KAAK;gBACrB;YACF,SAAU;gBACR,cACI,qCACC,uBAAuB,CAAC;YAC/B;QACF;IACF;IACA,SAAS,KAAK,IAAI,EAAE,IAAI;QACtB,IAAI,QAAQ,KAAK,MAAM;QACvB,KAAK,IAAI,CAAC;QACV,GAAG,MAAO,IAAI,OAAS;YACrB,IAAI,cAAc,AAAC,QAAQ,MAAO,GAChC,SAAS,IAAI,CAAC,YAAY;YAC5B,IAAI,IAAI,QAAQ,QAAQ,OACtB,AAAC,IAAI,CAAC,YAAY,GAAG,MAClB,IAAI,CAAC,MAAM,GAAG,QACd,QAAQ;iBACR,MAAM;QACb;IACF;IACA,SAAS,KAAK,IAAI;QAChB,OAAO,MAAM,KAAK,MAAM,GAAG,OAAO,IAAI,CAAC,EAAE;IAC3C;IACA,SAAS,IAAI,IAAI;QACf,IAAI,MAAM,KAAK,MAAM,EAAE,OAAO;QAC9B,IAAI,QAAQ,IAAI,CAAC,EAAE,EACjB,OAAO,KAAK,GAAG;QACjB,IAAI,SAAS,OAAO;YAClB,IAAI,CAAC,EAAE,GAAG;YACV,GAAG,IACD,IAAI,QAAQ,GAAG,SAAS,KAAK,MAAM,EAAE,aAAa,WAAW,GAC7D,QAAQ,YAER;gBACA,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,IAAI,GAChC,OAAO,IAAI,CAAC,UAAU,EACtB,aAAa,YAAY,GACzB,QAAQ,IAAI,CAAC,WAAW;gBAC1B,IAAI,IAAI,QAAQ,MAAM,OACpB,aAAa,UAAU,IAAI,QAAQ,OAAO,QACtC,CAAC,AAAC,IAAI,CAAC,MAAM,GAAG,OACf,IAAI,CAAC,WAAW,GAAG,MACnB,QAAQ,UAAW,IACpB,CAAC,AAAC,IAAI,CAAC,MAAM,GAAG,MACf,IAAI,CAAC,UAAU,GAAG,MAClB,QAAQ,SAAU;qBACpB,IAAI,aAAa,UAAU,IAAI,QAAQ,OAAO,OACjD,AAAC,IAAI,CAAC,MAAM,GAAG,OACZ,IAAI,CAAC,WAAW,GAAG,MACnB,QAAQ;qBACR,MAAM;YACb;QACF;QACA,OAAO;IACT;IACA,SAAS,QAAQ,CAAC,EAAE,CAAC;QACnB,IAAI,OAAO,EAAE,SAAS,GAAG,EAAE,SAAS;QACpC,OAAO,MAAM,OAAO,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE;IACxC;IACA,SAAS,cAAc,WAAW;QAChC,IAAK,IAAI,QAAQ,KAAK,aAAa,SAAS,OAAS;YACnD,IAAI,SAAS,MAAM,QAAQ,EAAE,IAAI;iBAC5B,IAAI,MAAM,SAAS,IAAI,aAC1B,IAAI,aACD,MAAM,SAAS,GAAG,MAAM,cAAc,EACvC,KAAK,WAAW;iBACf;YACL,QAAQ,KAAK;QACf;IACF;IACA,SAAS,cAAc,WAAW;QAChC,yBAAyB,CAAC;QAC1B,cAAc;QACd,IAAI,CAAC,yBACH,IAAI,SAAS,KAAK,YAChB,AAAC,0BAA0B,CAAC,GAAI;aAC7B;YACH,IAAI,aAAa,KAAK;YACtB,SAAS,cACP,mBACE,eACA,WAAW,SAAS,GAAG;QAE7B;IACJ;IACA,SAAS;QACP,OAAO,QAAQ,YAAY,KAAK,YAAY,gBAAgB,CAAC,IAAI,CAAC;IACpE;IACA,SAAS;QACP,wBACE,CAAC,AAAC,uBAAuB,CAAC,GAAI,kCAAkC;IACpE;IACA,SAAS,mBAAmB,QAAQ,EAAE,EAAE;QACtC,gBAAgB,gBAAgB;YAC9B,SAAS,QAAQ,YAAY;QAC/B,GAAG;IACL;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,QAAQ,YAAY,GAAG,KAAK;IAC5B,IACE,aAAa,OAAO,eACpB,eAAe,OAAO,YAAY,GAAG,EACrC;QACA,IAAI,mBAAmB;QACvB,QAAQ,YAAY,GAAG;YACrB,OAAO,iBAAiB,GAAG;QAC7B;IACF,OAAO;QACL,IAAI,YAAY,MACd,cAAc,UAAU,GAAG;QAC7B,QAAQ,YAAY,GAAG;YACrB,OAAO,UAAU,GAAG,KAAK;QAC3B;IACF;IACA,IAAI,YAAY,EAAE,EAChB,aAAa,EAAE,EACf,gBAAgB,GAChB,cAAc,MACd,uBAAuB,GACvB,mBAAmB,CAAC,GACpB,0BAA0B,CAAC,GAC3B,yBAAyB,CAAC,GAC1B,kBAAkB,eAAe,OAAO,aAAa,aAAa,MAClE,oBACE,eAAe,OAAO,eAAe,eAAe,MACtD,oBACE,gBAAgB,OAAO,eAAe,eAAe,MACvD,uBAAuB,CAAC,GACxB,gBAAgB,CAAC,GACjB,gBAAgB,GAChB,YAAY,CAAC;IACf,IAAI,eAAe,OAAO,mBACxB,IAAI,mCAAmC;QACrC,kBAAkB;IACpB;SACG,IAAI,gBAAgB,OAAO,gBAAgB;QAC9C,IAAI,UAAU,IAAI,kBAChB,OAAO,QAAQ,KAAK;QACtB,QAAQ,KAAK,CAAC,SAAS,GAAG;QAC1B,mCAAmC;YACjC,KAAK,WAAW,CAAC;QACnB;IACF,OACE,mCAAmC;QACjC,gBAAgB,0BAA0B;IAC5C;IACF,QAAQ,qBAAqB,GAAG;IAChC,QAAQ,0BAA0B,GAAG;IACrC,QAAQ,oBAAoB,GAAG;IAC/B,QAAQ,uBAAuB,GAAG;IAClC,QAAQ,kBAAkB,GAAG;IAC7B,QAAQ,6BAA6B,GAAG;IACxC,QAAQ,uBAAuB,GAAG,SAAU,IAAI;QAC9C,KAAK,QAAQ,GAAG;IAClB;IACA,QAAQ,0BAA0B,GAAG;QACnC,2BACE,oBACA,CAAC,AAAC,0BAA0B,CAAC,GAAI,qBAAqB;IAC1D;IACA,QAAQ,uBAAuB,GAAG,SAAU,GAAG;QAC7C,IAAI,OAAO,MAAM,MACb,QAAQ,KAAK,CACX,qHAED,gBAAgB,IAAI,MAAM,KAAK,KAAK,CAAC,MAAM,OAAO;IACzD;IACA,QAAQ,gCAAgC,GAAG;QACzC,OAAO;IACT;IACA,QAAQ,6BAA6B,GAAG;QACtC,OAAO,KAAK;IACd;IACA,QAAQ,aAAa,GAAG,SAAU,YAAY;QAC5C,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,IAAI,gBAAgB;gBACpB;YACF;gBACE,gBAAgB;QACpB;QACA,IAAI,wBAAwB;QAC5B,uBAAuB;QACvB,IAAI;YACF,OAAO;QACT,SAAU;YACR,uBAAuB;QACzB;IACF;IACA,QAAQ,uBAAuB,GAAG,YAAa;IAC/C,QAAQ,qBAAqB,GAAG,YAAa;IAC7C,QAAQ,wBAAwB,GAAG,SAAU,aAAa,EAAE,YAAY;QACtE,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACE,gBAAgB;QACpB;QACA,IAAI,wBAAwB;QAC5B,uBAAuB;QACvB,IAAI;YACF,OAAO;QACT,SAAU;YACR,uBAAuB;QACzB;IACF;IACA,QAAQ,yBAAyB,GAAG,SAClC,aAAa,EACb,QAAQ,EACR,OAAO;QAEP,IAAI,cAAc,QAAQ,YAAY;QACtC,aAAa,OAAO,WAAW,SAAS,UACpC,CAAC,AAAC,UAAU,QAAQ,KAAK,EACxB,UACC,aAAa,OAAO,WAAW,IAAI,UAC/B,cAAc,UACd,WAAY,IACjB,UAAU;QACf,OAAQ;YACN,KAAK;gBACH,IAAI,UAAU,CAAC;gBACf;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF;gBACE,UAAU;QACd;QACA,UAAU,UAAU;QACpB,gBAAgB;YACd,IAAI;YACJ,UAAU;YACV,eAAe;YACf,WAAW;YACX,gBAAgB;YAChB,WAAW,CAAC;QACd;QACA,UAAU,cACN,CAAC,AAAC,cAAc,SAAS,GAAG,SAC5B,KAAK,YAAY,gBACjB,SAAS,KAAK,cACZ,kBAAkB,KAAK,eACvB,CAAC,yBACG,CAAC,kBAAkB,gBAAiB,gBAAgB,CAAC,CAAE,IACtD,yBAAyB,CAAC,GAC/B,mBAAmB,eAAe,UAAU,YAAY,CAAC,IAC3D,CAAC,AAAC,cAAc,SAAS,GAAG,SAC5B,KAAK,WAAW,gBAChB,2BACE,oBACA,CAAC,AAAC,0BAA0B,CAAC,GAAI,qBAAqB,CAAC;QAC7D,OAAO;IACT;IACA,QAAQ,oBAAoB,GAAG;IAC/B,QAAQ,qBAAqB,GAAG,SAAU,QAAQ;QAChD,IAAI,sBAAsB;QAC1B,OAAO;YACL,IAAI,wBAAwB;YAC5B,uBAAuB;YACvB,IAAI;gBACF,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;YAC9B,SAAU;gBACR,uBAAuB;YACzB;QACF;IACF;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/scheduler%400.25.0/node_modules/scheduler/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/scheduler.production.js');\n} else {\n  module.exports = require('./cjs/scheduler.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/suspend-react%400.1.3_react%4019.0.0/node_modules/suspend-react/index.js"], "sourcesContent": ["const isPromise = promise => typeof promise === 'object' && typeof promise.then === 'function';\n\nconst globalCache = [];\n\nfunction shallowEqualArrays(arrA, arrB, equal = (a, b) => a === b) {\n  if (arrA === arrB) return true;\n  if (!arrA || !arrB) return false;\n  const len = arrA.length;\n  if (arrB.length !== len) return false;\n\n  for (let i = 0; i < len; i++) if (!equal(arrA[i], arrB[i])) return false;\n\n  return true;\n}\n\nfunction query(fn, keys = null, preload = false, config = {}) {\n  // If no keys were given, the function is the key\n  if (keys === null) keys = [fn];\n\n  for (const entry of globalCache) {\n    // Find a match\n    if (shallowEqualArrays(keys, entry.keys, entry.equal)) {\n      // If we're pre-loading and the element is present, just return\n      if (preload) return undefined; // If an error occurred, throw\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'error')) throw entry.error; // If a response was successful, return\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'response')) {\n        if (config.lifespan && config.lifespan > 0) {\n          if (entry.timeout) clearTimeout(entry.timeout);\n          entry.timeout = setTimeout(entry.remove, config.lifespan);\n        }\n\n        return entry.response;\n      } // If the promise is still unresolved, throw\n\n\n      if (!preload) throw entry.promise;\n    }\n  } // The request is new or has changed.\n\n\n  const entry = {\n    keys,\n    equal: config.equal,\n    remove: () => {\n      const index = globalCache.indexOf(entry);\n      if (index !== -1) globalCache.splice(index, 1);\n    },\n    promise: // Execute the promise\n    (isPromise(fn) ? fn : fn(...keys) // When it resolves, store its value\n    ).then(response => {\n      entry.response = response; // Remove the entry in time if a lifespan was given\n\n      if (config.lifespan && config.lifespan > 0) {\n        entry.timeout = setTimeout(entry.remove, config.lifespan);\n      }\n    }) // Store caught errors, they will be thrown in the render-phase to bubble into an error-bound\n    .catch(error => entry.error = error)\n  }; // Register the entry\n\n  globalCache.push(entry); // And throw the promise, this yields control back to React\n\n  if (!preload) throw entry.promise;\n  return undefined;\n}\n\nconst suspend = (fn, keys, config) => query(fn, keys, false, config);\n\nconst preload = (fn, keys, config) => void query(fn, keys, true, config);\n\nconst peek = keys => {\n  var _globalCache$find;\n\n  return (_globalCache$find = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal))) == null ? void 0 : _globalCache$find.response;\n};\n\nconst clear = keys => {\n  if (keys === undefined || keys.length === 0) globalCache.splice(0, globalCache.length);else {\n    const entry = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal));\n    if (entry) entry.remove();\n  }\n};\n\nexport { clear, peek, preload, suspend };\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,YAAY,CAAA,UAAW,OAAO,YAAY,YAAY,OAAO,QAAQ,IAAI,KAAK;AAEpF,MAAM,cAAc,EAAE;AAEtB,SAAS,mBAAmB,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,GAAG,IAAM,MAAM,CAAC;IAC/D,IAAI,SAAS,MAAM,OAAO;IAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO;IAC3B,MAAM,MAAM,KAAK,MAAM;IACvB,IAAI,KAAK,MAAM,KAAK,KAAK,OAAO;IAEhC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,OAAO;IAEnE,OAAO;AACT;AAEA,SAAS,MAAM,EAAE,EAAE,OAAO,IAAI,EAAE,UAAU,KAAK,EAAE,SAAS,CAAC,CAAC;IAC1D,iDAAiD;IACjD,IAAI,SAAS,MAAM,OAAO;QAAC;KAAG;IAE9B,KAAK,MAAM,SAAS,YAAa;QAC/B,eAAe;QACf,IAAI,mBAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK,GAAG;YACrD,+DAA+D;YAC/D,IAAI,SAAS,OAAO,WAAW,8BAA8B;YAE7D,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,UAAU,MAAM,MAAM,KAAK,EAAE,uCAAuC;YAEpH,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,aAAa;gBAC3D,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,GAAG,GAAG;oBAC1C,IAAI,MAAM,OAAO,EAAE,aAAa,MAAM,OAAO;oBAC7C,MAAM,OAAO,GAAG,WAAW,MAAM,MAAM,EAAE,OAAO,QAAQ;gBAC1D;gBAEA,OAAO,MAAM,QAAQ;YACvB,EAAE,4CAA4C;YAG9C,IAAI,CAAC,SAAS,MAAM,MAAM,OAAO;QACnC;IACF,EAAE,qCAAqC;IAGvC,MAAM,QAAQ;QACZ;QACA,OAAO,OAAO,KAAK;QACnB,QAAQ;YACN,MAAM,QAAQ,YAAY,OAAO,CAAC;YAClC,IAAI,UAAU,CAAC,GAAG,YAAY,MAAM,CAAC,OAAO;QAC9C;QACA,SACA,CAAC,UAAU,MAAM,KAAK,MAAM,MAAM,oCAAoC;QACtE,EAAE,IAAI,CAAC,CAAA;YACL,MAAM,QAAQ,GAAG,UAAU,mDAAmD;YAE9E,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,GAAG,GAAG;gBAC1C,MAAM,OAAO,GAAG,WAAW,MAAM,MAAM,EAAE,OAAO,QAAQ;YAC1D;QACF,GAAG,6FAA6F;SAC/F,KAAK,CAAC,CAAA,QAAS,MAAM,KAAK,GAAG;IAChC,GAAG,qBAAqB;IAExB,YAAY,IAAI,CAAC,QAAQ,2DAA2D;IAEpF,IAAI,CAAC,SAAS,MAAM,MAAM,OAAO;IACjC,OAAO;AACT;AAEA,MAAM,UAAU,CAAC,IAAI,MAAM,SAAW,MAAM,IAAI,MAAM,OAAO;AAE7D,MAAM,UAAU,CAAC,IAAI,MAAM,SAAW,KAAK,MAAM,IAAI,MAAM,MAAM;AAEjE,MAAM,OAAO,CAAA;IACX,IAAI;IAEJ,OAAO,CAAC,oBAAoB,YAAY,IAAI,CAAC,CAAA,QAAS,mBAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK,EAAE,KAAK,OAAO,KAAK,IAAI,kBAAkB,QAAQ;AACzJ;AAEA,MAAM,QAAQ,CAAA;IACZ,IAAI,SAAS,aAAa,KAAK,MAAM,KAAK,GAAG,YAAY,MAAM,CAAC,GAAG,YAAY,MAAM;SAAO;QAC1F,MAAM,QAAQ,YAAY,IAAI,CAAC,CAAA,QAAS,mBAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK;QACxF,IAAI,OAAO,MAAM,MAAM;IACzB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/its-fine%402.0.0_%40types%2Breact%4019.0.10_react%4019.0.0/node_modules/its-fine/src/index.tsx"], "sourcesContent": ["import * as React from 'react'\r\nimport type <PERSON>actR<PERSON>onciler from 'react-reconciler'\r\n\r\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */\r\nconst useIsomorphicLayoutEffect = /* @__PURE__ */ (() =>\r\n  typeof window !== 'undefined' && (window.document?.createElement || window.navigator?.product === 'ReactNative'))()\r\n  ? React.useLayoutEffect\r\n  : React.useEffect\r\n\r\n/**\r\n * Represents a react-internal Fiber node.\r\n */\r\nexport type Fiber<T = any> = Omit<ReactReconciler.Fiber, 'stateNode'> & { stateNode: T }\r\n\r\n/**\r\n * Represents a {@link Fiber} node selector for traversal.\r\n */\r\nexport type FiberSelector<T = any> = (\r\n  /** The current {@link Fiber} node. */\r\n  node: Fiber<T | null>,\r\n) => boolean | void\r\n\r\n/**\r\n * Traverses up or down a {@link Fiber}, return `true` to stop and select a node.\r\n */\r\nexport function traverseFiber<T = any>(\r\n  /** Input {@link Fiber} to traverse. */\r\n  fiber: Fiber | undefined,\r\n  /** Whether to ascend and walk up the tree. Will walk down if `false`. */\r\n  ascending: boolean,\r\n  /** A {@link Fiber} node selector, returns the first match when `true` is passed. */\r\n  selector: FiberSelector<T>,\r\n): Fiber<T> | undefined {\r\n  if (!fiber) return\r\n  if (selector(fiber) === true) return fiber\r\n\r\n  let child = ascending ? fiber.return : fiber.child\r\n  while (child) {\r\n    const match = traverseFiber(child, ascending, selector)\r\n    if (match) return match\r\n\r\n    child = ascending ? null : child.sibling\r\n  }\r\n}\r\n\r\n// In development, React will warn about using contexts between renderers.\r\n// Hide the warning because its-fine fixes this issue\r\n// https://github.com/facebook/react/pull/12779\r\nfunction wrapContext<T>(context: React.Context<T>): React.Context<T> {\r\n  try {\r\n    return Object.defineProperties(context, {\r\n      _currentRenderer: {\r\n        get() {\r\n          return null\r\n        },\r\n        set() {},\r\n      },\r\n      _currentRenderer2: {\r\n        get() {\r\n          return null\r\n        },\r\n        set() {},\r\n      },\r\n    })\r\n  } catch (_) {\r\n    return context\r\n  }\r\n}\r\n\r\nconst FiberContext = /* @__PURE__ */ wrapContext(/* @__PURE__ */ React.createContext<Fiber>(null!))\r\n\r\n/**\r\n * A react-internal {@link Fiber} provider. This component binds React children to the React Fiber tree. Call its-fine hooks within this.\r\n */\r\nexport class FiberProvider extends React.Component<{ children?: React.ReactNode }> {\r\n  private _reactInternals!: Fiber\r\n\r\n  render() {\r\n    return <FiberContext.Provider value={this._reactInternals}>{this.props.children}</FiberContext.Provider>\r\n  }\r\n}\r\n\r\n/**\r\n * Returns the current react-internal {@link Fiber}. This is an implementation detail of [react-reconciler](https://github.com/facebook/react/tree/main/packages/react-reconciler).\r\n */\r\nexport function useFiber(): Fiber<null> | undefined {\r\n  const root = React.useContext(FiberContext)\r\n  if (root === null) throw new Error('its-fine: useFiber must be called within a <FiberProvider />!')\r\n\r\n  const id = React.useId()\r\n  const fiber = React.useMemo(() => {\r\n    for (const maybeFiber of [root, root?.alternate]) {\r\n      if (!maybeFiber) continue\r\n      const fiber = traverseFiber<null>(maybeFiber, false, (node) => {\r\n        let state = node.memoizedState\r\n        while (state) {\r\n          if (state.memoizedState === id) return true\r\n          state = state.next\r\n        }\r\n      })\r\n      if (fiber) return fiber\r\n    }\r\n  }, [root, id])\r\n\r\n  return fiber\r\n}\r\n\r\n/**\r\n * Represents a react-reconciler container instance.\r\n */\r\nexport interface ContainerInstance<T = any> {\r\n  containerInfo: T\r\n}\r\n\r\n/**\r\n * Returns the current react-reconciler container info passed to {@link ReactReconciler.Reconciler.createContainer}.\r\n *\r\n * In react-dom, a container will point to the root DOM element; in react-three-fiber, it will point to the root Zustand store.\r\n */\r\nexport function useContainer<T = any>(): T | undefined {\r\n  const fiber = useFiber()\r\n  const root = React.useMemo(\r\n    () => traverseFiber<ContainerInstance<T>>(fiber, true, (node) => node.stateNode?.containerInfo != null),\r\n    [fiber],\r\n  )\r\n\r\n  return root?.stateNode.containerInfo\r\n}\r\n\r\n/**\r\n * Returns the nearest react-reconciler child instance or the node created from {@link ReactReconciler.HostConfig.createInstance}.\r\n *\r\n * In react-dom, this would be a DOM element; in react-three-fiber this would be an instance descriptor.\r\n */\r\nexport function useNearestChild<T = any>(\r\n  /** An optional element type to filter to. */\r\n  type?: keyof React.JSX.IntrinsicElements,\r\n): React.RefObject<T | undefined> {\r\n  const fiber = useFiber()\r\n  const childRef = React.useRef<T>(undefined)\r\n\r\n  useIsomorphicLayoutEffect(() => {\r\n    childRef.current = traverseFiber<T>(\r\n      fiber,\r\n      false,\r\n      (node) => typeof node.type === 'string' && (type === undefined || node.type === type),\r\n    )?.stateNode\r\n  }, [fiber])\r\n\r\n  return childRef\r\n}\r\n\r\n/**\r\n * Returns the nearest react-reconciler parent instance or the node created from {@link ReactReconciler.HostConfig.createInstance}.\r\n *\r\n * In react-dom, this would be a DOM element; in react-three-fiber this would be an instance descriptor.\r\n */\r\nexport function useNearestParent<T = any>(\r\n  /** An optional element type to filter to. */\r\n  type?: keyof React.JSX.IntrinsicElements,\r\n): React.RefObject<T | undefined> {\r\n  const fiber = useFiber()\r\n  const parentRef = React.useRef<T>(undefined)\r\n\r\n  useIsomorphicLayoutEffect(() => {\r\n    parentRef.current = traverseFiber<T>(\r\n      fiber,\r\n      true,\r\n      (node) => typeof node.type === 'string' && (type === undefined || node.type === type),\r\n    )?.stateNode\r\n  }, [fiber])\r\n\r\n  return parentRef\r\n}\r\n\r\nexport type ContextMap = Map<React.Context<any>, any> & {\r\n  get<T>(context: React.Context<T>): T | undefined\r\n}\r\n\r\nconst REACT_CONTEXT_TYPE = Symbol.for('react.context')\r\n\r\nconst isContext = <T,>(type: unknown): type is React.Context<T> =>\r\n  type !== null && typeof type === 'object' && '$$typeof' in type && type.$$typeof === REACT_CONTEXT_TYPE\r\n\r\n/**\r\n * Returns a map of all contexts and their values.\r\n */\r\nexport function useContextMap(): ContextMap {\r\n  const fiber = useFiber()\r\n  const [contextMap] = React.useState(() => new Map<React.Context<any>, any>())\r\n\r\n  // Collect live context\r\n  contextMap.clear()\r\n  let node = fiber\r\n  while (node) {\r\n    const context = node.type\r\n    if (isContext(context) && context !== FiberContext && !contextMap.has(context)) {\r\n      contextMap.set(context, React.use(wrapContext(context)))\r\n    }\r\n\r\n    node = node.return!\r\n  }\r\n\r\n  return contextMap\r\n}\r\n\r\n/**\r\n * Represents a react-context bridge provider component.\r\n */\r\nexport type ContextBridge = React.FC<React.PropsWithChildren<{}>>\r\n\r\n/**\r\n * React Context currently cannot be shared across [React renderers](https://reactjs.org/docs/codebase-overview.html#renderers) but explicitly forwarded between providers (see [react#17275](https://github.com/facebook/react/issues/17275)). This hook returns a {@link ContextBridge} of live context providers to pierce Context across renderers.\r\n *\r\n * Pass {@link ContextBridge} as a component to a secondary renderer to enable context-sharing within its children.\r\n */\r\nexport function useContextBridge(): ContextBridge {\r\n  const contextMap = useContextMap()\r\n\r\n  // Flatten context and their memoized values into a `ContextBridge` provider\r\n  return React.useMemo(\r\n    () =>\r\n      Array.from(contextMap.keys()).reduce(\r\n        (Prev, context) => (props) =>\r\n          (\r\n            <Prev>\r\n              <context.Provider {...props} value={contextMap.get(context)} />\r\n            </Prev>\r\n          ),\r\n        (props) => <FiberProvider {...props} />,\r\n      ),\r\n    [contextMap],\r\n  )\r\n}\r\n"], "names": ["useIsomorphicLayoutEffect", "_a", "_b", "React", "traverseFiber", "fiber", "ascending", "selector", "child", "match", "wrapContext", "context", "_", "FiberContext", "FiberProvider", "useFiber", "root", "id", "maybeFiber", "node", "state", "useContainer", "useNearestChild", "type", "childRef", "useNearestParent", "parentRef", "REACT_CONTEXT_TYPE", "isContext", "useContextMap", "contextMap", "useContextBridge", "Prev", "props"], "mappings": ";;;;;;;;;;;;AAYA,MAAMA,IAA6C,aAAA,GAAA,CAAA,MAAA;;IACjD,OAAA,OAAO,UAAW,eAAA,CAAA,CAAA,CAAgBC,IAAA,OAAO,QAAA,KAAP,OAAA,KAAA,IAAAA,EAAiB,aAAA,KAAA,CAAA,CAAiBC,IAAA,OAAO,SAAA,KAAP,OAAA,KAAA,IAAAA,EAAkB,OAAA,MAAY,aAAA;AAAA,CAAA,EAAA,iSAChGC,EAAM,gBAAA,gSACNA,EAAM,UAAA;AAkBM,SAAAC,EAEdC,CAAAA,EAEAC,CAAAA,EAEAC,CAAAA,EACsB;IACtB,IAAI,CAACF,EAAO,CAAA;IACZ,IAAIE,EAASF,CAAK,MAAM,CAAA,EAAa,CAAA,OAAAA;IAErC,IAAIG,IAAQF,IAAYD,EAAM,MAAA,GAASA,EAAM,KAAA;IAC7C,MAAOG,GAAO;QACZ,MAAMC,IAAQL,EAAcI,GAAOF,GAAWC,CAAQ;QACtD,IAAIE,EAAc,CAAA,OAAAA;QAEVD,IAAAF,IAAY,OAAOE,EAAM,OAAA;IAAA;AAErC;AAKA,SAASE,EAAeC,CAAAA,EAA6C;IAC/D,IAAA;QACK,OAAA,OAAO,gBAAA,CAAiBA,GAAS;YACtC,kBAAkB;gBAChB,MAAM;oBACG,OAAA;gBACT;gBACA,MAAM,EAAA;YACR;YACA,mBAAmB;gBACjB,MAAM;oBACG,OAAA;gBACT;gBACA,MAAM,EAAA;YAAC;QACT,CACD;IAAA,EAAA,OACMC,GAAG;QACH,OAAAD;IAAA;AAEX;AAEA,MAAME,IAA+B,aAAA,GAAAH,EAAkC,aAAA,oSAAAP,EAAA,cAAA,EAAqB,IAAK,CAAC;AAKrF,MAAAW,uSAAsBX,EAAM,UAAA,CAA0C;IAGjF,SAAS;QACA,OAAA,aAAA,oSAAAA,EAAA,cAAA,EAACU,EAAa,QAAA,EAAb;YAAsB,OAAO,IAAA,CAAK,eAAA;QAAA,GAAkB,IAAA,CAAK,KAAA,CAAM,QAAS;IAAA;AAEpF;AAKO,SAASE,IAAoC;IAC5C,MAAAC,KAAOb,EAAM,2SAAA,EAAWU,CAAY;IAC1C,IAAIG,MAAS,KAAY,CAAA,MAAA,IAAI,MAAM,+DAA+D;IAE5F,MAAAC,qSAAKd,EAAM,MAAA,CAAM;IAehB,WAdOA,EAAM,qSAAA;qBAAQ,MAAM;YAChC,KAAA,MAAWe,KAAc;gBAACF;gBAAMA,KAAA,OAAA,KAAA,IAAAA,EAAM,SAAS;aAAA,CAAG;gBAChD,IAAI,CAACE,EAAY,CAAA;gBACjB,MAAMb,IAAQD,EAAoBc,GAAY,CAAA;mCAAO,CAACC,MAAS;wBAC7D,IAAIC,IAAQD,EAAK,aAAA;wBACjB,MAAOC,GAAO;4BACR,IAAAA,EAAM,aAAA,KAAkBH,EAAW,CAAA,OAAA,CAAA;4BACvCG,IAAQA,EAAM,IAAA;wBAAA;oBAChB,CACD;;gBACD,IAAIf,EAAcA,CAAAA,OAAAA;YAAA;QACpB;oBACC;QAACW;QAAMC,CAAE;KAAC;AAGf;AAcO,SAASI,IAAuC;IACrD,MAAMhB,IAAQU,EAAS,GACjBC,qSAAOb,EAAM,QAAA;wBACjB,IAAMC,EAAoCC,GAAO,CAAA;gCAAM,CAACc,MAAS;;oBAAA,OAAA,CAAA,CAAAlB,IAAAkB,EAAK,SAAA,KAAL,OAAA,KAAA,IAAAlB,EAAgB,aAAA,KAAiB;gBAAA,CAAI;;uBACtG;QAACI,CAAK;KAAA;IAGR,OAAOW,KAAA,OAAA,KAAA,IAAAA,EAAM,SAAA,CAAU,aAAA;AACzB;AAOO,SAASM,EAEdC,CAAAA,EACgC;IAChC,MAAMlB,IAAQU,EAAS,GACjBS,qSAAWrB,EAAM,OAAA,EAAU,KAAA,CAAS;IAE1C,OAAAH,EAA0B,MAAM;;QAC9BwB,EAAS,OAAA,GAAA,CAAUvB,IAAAG,EACjBC,GACA,CAAA,GACA,CAACc,IAAS,OAAOA,EAAK,IAAA,IAAS,YAAA,CAAaI,MAAS,KAAA,KAAaJ,EAAK,IAAA,KAASI,CAAAA,EAAA,KAH/D,OAAA,KAAA,IAAAtB,EAIhB,SAAA;IAAA,GACF;QAACI,CAAK;KAAC,GAEHmB;AACT;AAOO,SAASC,EAEdF,CAAAA,EACgC;IAChC,MAAMlB,IAAQU,EAAS,GACjBW,qSAAYvB,EAAM,OAAA,EAAU,KAAA,CAAS;IAE3C,OAAAH,EAA0B,MAAM;;QAC9B0B,EAAU,OAAA,GAAA,CAAUzB,IAAAG,EAClBC,GACA,CAAA,GACA,CAACc,IAAS,OAAOA,EAAK,IAAA,IAAS,YAAA,CAAaI,MAAS,KAAA,KAAaJ,EAAK,IAAA,KAASI,CAAAA,EAAA,KAH9D,OAAA,KAAA,IAAAtB,EAIjB,SAAA;IAAA,GACF;QAACI,CAAK;KAAC,GAEHqB;AACT;AAMA,MAAMC,IAAqB,OAAO,GAAA,CAAI,eAAe,GAE/CC,IAAY,CAAKL,IACrBA,MAAS,QAAQ,OAAOA,KAAS,YAAY,cAAcA,KAAQA,EAAK,QAAA,KAAaI;AAKhF,SAASE,IAA4B;IAC1C,MAAMxB,IAAQU,EAAS,GACjB,CAACe,CAAU,CAAA,oSAAI3B,EAAM,SAAA;sBAAS,IAAM,aAAA,GAAA,IAAI,KAA8B;;IAG5E2B,EAAW,KAAA,CAAM;IACjB,IAAIX,IAAOd;IACX,MAAOc,GAAM;QACX,MAAMR,IAAUQ,EAAK,IAAA;QACjBS,EAAUjB,CAAO,KAAKA,MAAYE,KAAgB,CAACiB,EAAW,GAAA,CAAInB,CAAO,KAC3EmB,EAAW,GAAA,CAAInB,oSAASR,EAAM,IAAA,EAAIO,EAAYC,CAAO,CAAC,CAAC,GAGzDQ,IAAOA,EAAK,MAAA;IAAA;IAGP,OAAAW;AACT;AAYO,SAASC,IAAkC;IAChD,MAAMD,IAAaD,EAAc;IAGjC,wSAAO1B,EAAM,QAAA;qBACX,IACE,MAAM,IAAA,CAAK2B,EAAW,IAAA,CAAA,CAAM,EAAE,MAAA;6BAC5B,CAACE,GAAMrB;qCAAY,CAACsB,IAEhB,aAAA,oSAAA9B,EAAA,cAAA,EAAC6B,GAAAA,MACE,aAAA,oSAAA7B,EAAA,cAAA,EAAAQ,EAAQ,QAAA,EAAR;gCAAkB,GAAGsB,CAAAA;gCAAO,OAAOH,EAAW,GAAA,CAAInB,CAAO;4BAAA,CAAG,CAC/D;;;6BAEJ,CAACsB,IAAW,aAAA,oSAAA9B,EAAA,cAAA,EAAAW,GAAA;wBAAe,GAAGmB,CAAAA;oBAAO,CAAA;;oBAEzC;QAACH,CAAU;KAAA;AAEf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 756, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "names": ["createDebounce", "callback", "ms", "timeoutId", "args", "useMeasure", "debounce", "scroll", "polyfill", "offsetSize", "ResizeObserver", "bounds", "set", "useState", "state", "useRef", "scrollDebounce", "resizeDebounce", "mounted", "useEffect", "forceRefresh", "resizeChange", "scrollChange", "useMemo", "left", "top", "width", "height", "bottom", "right", "x", "y", "size", "areBoundsEqual", "removeListeners", "element", "addListeners", "scrollContainer", "ref", "node", "findScrollContainers", "useOnWindowScroll", "useOnWindowResize", "onWindowResize", "cb", "onScroll", "enabled", "result", "overflow", "overflowX", "overflowY", "prop", "keys", "a", "b", "key"], "mappings": ";;;;;AAEA,SAASA,EAAmDC,CAAAA,EAAaC,CAAAA,CAAY;IAC/EC,IAAAA;IAEJ,OAAO,CAAA,GAAIC,IAA8B;QAChC,OAAA,YAAA,CAAaD,CAAS,GAC7BA,IAAY,OAAO,UAAA,CAAW,IAAMF,EAAS,GAAGG,CAAI,GAAGF,CAAE;IAC3D;AACF;AA0CA,SAASG,EACP,EAAE,UAAAC,CAAAA,EAAU,QAAAC,CAAAA,EAAQ,UAAAC,CAAAA,EAAU,YAAAC,CAAW,EAAA,GAAa;IAAE,UAAU;IAAG,QAAQ,CAAA;IAAO,YAAY,CAAA;AAAA,CAAA,CACxF;IACR,MAAMC,IACJF,KAAAA,CAAa,OAAO,UAAW,cAAc,KAAqB;IAAA,IAAM,OAAe,cAAA;IAEzF,IAAI,CAACE,GACH,MAAM,IAAI,MACR,gJACF;IAGF,MAAM,CAACC,GAAQC,CAAG,CAAA,GAAIC,4SAAAA,EAAuB;QAC3C,MAAM;QACN,KAAK;QACL,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,GAAG;QACH,GAAG;IAAA,CACJ,GAGKC,QAAQC,sSAAAA,EAAc;QAC1B,SAAS;QACT,kBAAkB;QAClB,gBAAgB;QAChB,YAAYJ;QACZ,oBAAoB;IAAA,CACrB,GAGKK,IAAiBV,IAAY,OAAOA,KAAa,WAAWA,IAAWA,EAAS,MAAA,GAAU,MAC1FW,IAAiBX,IAAY,OAAOA,KAAa,WAAWA,IAAWA,EAAS,MAAA,GAAU,MAG1FY,8SAAUH,EAAO,CAAA,CAAK;iTAC5BI,EAAU,IAAA,CACRD,EAAQ,OAAA,GAAU,CAAA,GACX,IAAM,KAAA,CAAMA,EAAQ,OAAA,GAAU,CAAA,CAAA,CAAA,CACtC;IAGD,MAAM,CAACE,GAAcC,GAAcC,CAAY,CAAA,GAAIC,2SAAAA,EAAQ,IAAM;QAC/D,MAAMtB,IAAW,IAAM;YACjB,IAAA,CAACa,EAAM,OAAA,CAAQ,OAAA,EAAS;YACtB,MAAA,EAAE,MAAAU,CAAAA,EAAM,KAAAC,CAAAA,EAAK,OAAAC,CAAAA,EAAO,QAAAC,CAAAA,EAAQ,QAAAC,CAAAA,EAAQ,OAAAC,CAAAA,EAAO,GAAAC,CAAAA,EAAG,GAAAC,CAAE,EAAA,GACpDjB,EAAM,OAAA,CAAQ,OAAA,CAAQ,qBAAA,CAAsB,GAExCkB,IAAO;gBACX,MAAAR;gBACA,KAAAC;gBACA,OAAAC;gBACA,QAAAC;gBACA,QAAAC;gBACA,OAAAC;gBACA,GAAAC;gBACA,GAAAC;YACF;YAEIjB,EAAM,OAAA,CAAQ,OAAA,YAAmB,eAAeL,KAAAA,CAC7CuB,EAAA,MAAA,GAASlB,EAAM,OAAA,CAAQ,OAAA,CAAQ,YAAA,EAC/BkB,EAAA,KAAA,GAAQlB,EAAM,OAAA,CAAQ,OAAA,CAAQ,WAAA,GAGrC,OAAO,MAAA,CAAOkB,CAAI,GACdd,EAAQ,OAAA,IAAW,CAACe,EAAenB,EAAM,OAAA,CAAQ,UAAA,EAAYkB,CAAI,KAAGpB,EAAKE,EAAM,OAAA,CAAQ,UAAA,GAAakB,CAAK;QAC/G;QACO,OAAA;YACL/B;YACAgB,IAAiBjB,EAAeC,GAAUgB,CAAc,IAAIhB;YAC5De,IAAiBhB,EAAeC,GAAUe,CAAc,IAAIf,CAC9D;SAAA;IAAA,GACC;QAACW;QAAKH;QAAYO;QAAgBC,CAAc;KAAC;IAGpD,SAASiB,GAAkB;QACrBpB,EAAM,OAAA,CAAQ,gBAAA,IAAA,CACVA,EAAA,OAAA,CAAQ,gBAAA,CAAiB,OAAA,CAASqB,KAAYA,EAAQ,mBAAA,CAAoB,UAAUb,GAAc,CAAA,CAAI,CAAC,GAC7GR,EAAM,OAAA,CAAQ,gBAAA,GAAmB,IAAA,GAG/BA,EAAM,OAAA,CAAQ,cAAA,IAAA,CACVA,EAAA,OAAA,CAAQ,cAAA,CAAe,UAAA,CAAW,GACxCA,EAAM,OAAA,CAAQ,cAAA,GAAiB,IAAA,GAG7BA,EAAM,OAAA,CAAQ,kBAAA,IAAA,CACZ,iBAAiB,UAAU,yBAAyB,OAAO,WAAA,GAC7D,OAAO,WAAA,CAAY,mBAAA,CAAoB,UAAUA,EAAM,OAAA,CAAQ,kBAAkB,IACxE,yBAAyB,UAClC,OAAO,mBAAA,CAAoB,qBAAqBA,EAAM,OAAA,CAAQ,kBAAkB,CAAA;IAEpF;IAIF,SAASsB,GAAe;QACjBtB,EAAM,OAAA,CAAQ,OAAA,IAAA,CACnBA,EAAM,OAAA,CAAQ,cAAA,GAAiB,IAAIJ,EAAeY,CAAY,GAC9DR,EAAM,OAAA,CAAQ,cAAA,CAAgB,OAAA,CAAQA,EAAM,OAAA,CAAQ,OAAO,GACvDP,KAAUO,EAAM,OAAA,CAAQ,gBAAA,IAC1BA,EAAM,OAAA,CAAQ,gBAAA,CAAiB,OAAA,CAASuB,KACtCA,EAAgB,gBAAA,CAAiB,UAAUf,GAAc;gBAAE,SAAS,CAAA;gBAAM,SAAS,CAAA;YAAM,CAAA,CAC3F,GAIIR,EAAA,OAAA,CAAQ,kBAAA,GAAqB,IAAM;YAC1BQ,EAAA;QACf,GAGI,iBAAiB,UAAU,sBAAsB,OAAO,WAAA,GAC1D,OAAO,WAAA,CAAY,gBAAA,CAAiB,UAAUR,EAAM,OAAA,CAAQ,kBAAkB,IACrE,yBAAyB,UAElC,OAAO,gBAAA,CAAiB,qBAAqBA,EAAM,OAAA,CAAQ,kBAAkB,CAAA;IAC/E;IAIIwB,MAAAA,KAAOC,GAAkC;QACzC,CAACA,KAAQA,MAASzB,EAAM,OAAA,CAAQ,OAAA,IAAA,CACpBoB,EAAA,GAChBpB,EAAM,OAAA,CAAQ,OAAA,GAAUyB,GAClBzB,EAAA,OAAA,CAAQ,gBAAA,GAAmB0B,EAAqBD,CAAI,GAC7CH,EAAA,CAAA;IACf;IAGkBK,OAAAA,EAAAnB,GAAc,CAAQf,CAAAA,CAAO,GAC/CmC,EAAkBrB,CAAY,gTAG9BF,EAAU,IAAM;QACEe,EAAA,GACHE,EAAA;IACZ,GAAA;QAAC7B;QAAQe;QAAcD,CAAY;KAAC,IAG7BF,4SAAAA,EAAA,IAAMe,GAAiB,EAAE,GAC5B;QAACI;QAAK3B;QAAQS,CAAY;;AACnC;AAGA,SAASsB,EAAkBC,CAAAA,CAAwC;IACjExB,6SAAAA,EAAU,IAAM;QACd,MAAMyB,IAAKD;QACJ,OAAA,OAAA,gBAAA,CAAiB,UAAUC,CAAE,GAC7B,IAAM,KAAK,OAAO,mBAAA,CAAoB,UAAUA,CAAE;IAAA,GACxD;QAACD,CAAc;KAAC;AACrB;AACA,SAASF,EAAkBI,CAAAA,EAAsBC,CAAAA,CAAkB;iTACjE3B,EAAU,IAAM;QACd,IAAI2B,GAAS;YACX,MAAMF,IAAKC;YACJ,OAAA,OAAA,gBAAA,CAAiB,UAAUD,GAAI;gBAAE,SAAS,CAAA;gBAAM,SAAS,CAAA;YAAA,CAAM,GAC/D,IAAM,KAAK,OAAO,mBAAA,CAAoB,UAAUA,GAAI,CAAA,CAAI;QAAA;IACjE,GACC;QAACC;QAAUC,CAAO;KAAC;AACxB;AAGA,SAASN,EAAqBL,CAAAA,CAAsD;IAClF,MAAMY,IAA6B,CAAC,CAAA;IACpC,IAAI,CAACZ,KAAWA,MAAY,SAAS,IAAA,EAAaY,OAAAA;IAC5C,MAAA,EAAE,UAAAC,CAAAA,EAAU,WAAAC,CAAAA,EAAW,WAAAC,CAAc,EAAA,GAAA,OAAO,gBAAA,CAAiBf,CAAO;IACtE,OAAA;QAACa;QAAUC;QAAWC,CAAS;KAAA,CAAE,IAAA,CAAMC,KAASA,MAAS,UAAUA,MAAS,QAAQ,KAAGJ,EAAO,IAAA,CAAKZ,CAAO,GACvG,CAAC;WAAGY,EAAQ;WAAGP,EAAqBL,EAAQ,aAAa,CAAC;;AACnE;AAGA,MAAMiB,IAA+B;IAAC;IAAK;IAAK;IAAO;IAAU;IAAQ;IAAS;IAAS,QAAQ;CAAA,EAC7FnB,IAAiB,CAACoB,GAAiBC,IAA6BF,EAAK,KAAA,EAAOG,IAAQF,CAAAA,CAAEE,CAAG,CAAA,KAAMD,CAAAA,CAAEC,CAAG,CAAC", "debugId": null}}, {"offset": {"line": 891, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40babel%2Bruntime%407.27.4/node_modules/%40babel/runtime/helpers/esm/extends.js"], "sourcesContent": ["function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };"], "names": [], "mappings": ";;;AAAA,SAAS;IACP,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,2CAMjD,SAAS,KAAK,CAAC,MAAM;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 904, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 910, "column": 0}, "map": {"version": 3, "file": "EventDispatcher.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/controls/EventDispatcher.ts"], "sourcesContent": ["/*\nDue to @types/three r168 breaking change\nwe have to manually copy the EventDispatcher class from three.js.\nSo this files merges the declarations from https://github.com/DefinitelyTyped/DefinitelyTyped/blob/master/types/three/src/core/EventDispatcher.d.ts\nwith the implementation from https://github.com/mrdoob/three.js/blob/dev/src/core/EventDispatcher.js\nMore info in https://github.com/pmndrs/three-stdlib/issues/387\n*/\n\n/**\n * The minimal basic Event that can be dispatched by a {@link EventDispatcher<>}.\n */\nexport interface BaseEvent<TEventType extends string = string> {\n    readonly type: TEventType;\n    // not defined in @types/three\n    target: any;\n}\n\n/**\n * The minimal expected contract of a fired Event that was dispatched by a {@link EventDispatcher<>}.\n */\nexport interface Event<TEventType extends string = string, TTarget = unknown> {\n    readonly type: TEventType;\n    readonly target: TTarget;\n}\n\nexport type EventListener<TEventData, TEventType extends string, TTarget> = (\n    event: TEventData & Event<TEventType, TTarget>,\n) => void;\n\nexport class EventDispatcher<TEventMap extends {} = {}> {\n    // not defined in @types/three\n    private _listeners: any;\n\n    /**\n     * Adds a listener to an event type.\n     * @param type The type of event to listen to.\n     * @param listener The function that gets called when the event is fired.\n     */\n\taddEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): void {\n\n\t\tif ( this._listeners === undefined ) this._listeners = {};\n\n\t\tconst listeners = this._listeners;\n\n\t\tif ( listeners[ type ] === undefined ) {\n\n\t\t\tlisteners[ type ] = [];\n\n\t\t}\n\n\t\tif ( listeners[ type ].indexOf( listener ) === - 1 ) {\n\n\t\t\tlisteners[ type ].push( listener );\n\n\t\t}\n\n\t}\n\n\t/**\n     * Checks if listener is added to an event type.\n     * @param type The type of event to listen to.\n     * @param listener The function that gets called when the event is fired.\n     */\n    hasEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): boolean {\n\n\t\tif ( this._listeners === undefined ) return false;\n\n\t\tconst listeners = this._listeners;\n\n\t\treturn listeners[ type ] !== undefined && listeners[ type ].indexOf( listener ) !== - 1;\n\n\t}\n\n\t/**\n     * Removes a listener from an event type.\n     * @param type The type of the listener that gets removed.\n     * @param listener The listener function that gets removed.\n     */\n    removeEventListener<T extends Extract<keyof TEventMap, string>>(\n        type: T,\n        listener: EventListener<TEventMap[T], T, this>,\n    ): void {\n\n\t\tif ( this._listeners === undefined ) return;\n\n\t\tconst listeners = this._listeners;\n\t\tconst listenerArray = listeners[ type ];\n\n\t\tif ( listenerArray !== undefined ) {\n\n\t\t\tconst index = listenerArray.indexOf( listener );\n\n\t\t\tif ( index !== - 1 ) {\n\n\t\t\t\tlistenerArray.splice( index, 1 );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/**\n     * Fire an event type.\n     * @param event The event that gets fired.\n     */\n    dispatchEvent<T extends Extract<keyof TEventMap, string>>(event: BaseEvent<T> & TEventMap[T]): void {\n\n\t\tif ( this._listeners === undefined ) return;\n\n\t\tconst listeners = this._listeners;\n\t\tconst listenerArray = listeners[ event.type ];\n\n\t\tif ( listenerArray !== undefined ) {\n\n\t\t\tevent.target = this;\n\n\t\t\t// Make a copy, in case listeners are removed while iterating.\n\t\t\tconst array = listenerArray.slice( 0 );\n\n\t\t\tfor ( let i = 0, l = array.length; i < l; i ++ ) {\n\n\t\t\t\tarray[ i ].call( this, event );\n\n\t\t\t}\n\n\t\t\tevent.target = null;\n\n\t\t}\n\n\t}\n\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;AA6BO,MAAM,gBAA2C;IAAjD,aAAA;QAEK,8BAAA;QAAA,cAAA,IAAA,EAAA;IAAA;IAAA;;;;GAAA,GAOX,iBACO,IAAA,EACA,QAAA,EACI;QAEV,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAY,IAAA,CAAK,UAAA,GAAa,CAAA;QAEvD,MAAM,YAAY,IAAA,CAAK,UAAA;QAElB,IAAA,SAAA,CAAW,IAAK,CAAA,KAAM,KAAA,GAAY;YAE3B,SAAA,CAAA,IAAK,CAAA,GAAI,EAAA;QAErB;QAEA,IAAK,SAAA,CAAW,IAAK,CAAA,CAAE,OAAA,CAAS,QAAS,MAAM,CAAA,GAAM;YAEzC,SAAA,CAAA,IAAK,CAAA,CAAE,IAAA,CAAM,QAAS;QAElC;IAED;IAAA;;;;MAAA,GAOG,iBACI,IAAA,EACA,QAAA,EACO;QAEb,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAmB,OAAA;QAE5C,MAAM,YAAY,IAAA,CAAK,UAAA;QAEhB,OAAA,SAAA,CAAW,IAAK,CAAA,KAAM,KAAA,KAAa,SAAA,CAAW,IAAK,CAAA,CAAE,OAAA,CAAS,QAAS,MAAM,CAAA;IAErF;IAAA;;;;MAAA,GAOG,oBACI,IAAA,EACA,QAAA,EACI;QAEV,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAY;QAErC,MAAM,YAAY,IAAA,CAAK,UAAA;QACjB,MAAA,gBAAgB,SAAA,CAAW,IAAK,CAAA;QAEtC,IAAK,kBAAkB,KAAA,GAAY;YAE5B,MAAA,QAAQ,cAAc,OAAA,CAAS,QAAS;YAE9C,IAAK,UAAU,CAAA,GAAM;gBAEN,cAAA,MAAA,CAAQ,OAAO,CAAE;YAEhC;QAED;IAED;IAAA;;;MAAA,GAMG,cAA0D,KAAA,EAA0C;QAEtG,IAAK,IAAA,CAAK,UAAA,KAAe,KAAA,GAAY;QAErC,MAAM,YAAY,IAAA,CAAK,UAAA;QACjB,MAAA,gBAAgB,SAAA,CAAW,MAAM,IAAK,CAAA;QAE5C,IAAK,kBAAkB,KAAA,GAAY;YAElC,MAAM,MAAA,GAAS,IAAA;YAGT,MAAA,QAAQ,cAAc,KAAA,CAAO,CAAE;YAErC,IAAA,IAAU,IAAI,GAAG,IAAI,MAAM,MAAA,EAAQ,IAAI,GAAG,IAAO;gBAEhD,KAAA,CAAO,CAAE,CAAA,CAAE,IAAA,CAAM,IAAA,EAAM,KAAM;YAE9B;YAEA,MAAM,MAAA,GAAS;QAEhB;IAED;AAED", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 986, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 992, "column": 0}, "map": {"version": 3, "file": "OrbitControls.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/controls/OrbitControls.ts"], "sourcesContent": ["import {\n  Matrix4,\n  MOUSE,\n  OrthographicCamera,\n  PerspectiveCamera,\n  Quaternion,\n  Spherical,\n  TOUCH,\n  Vector2,\n  Vector3,\n  Ray,\n  Plane,\n} from 'three'\nimport { EventDispatcher } from './EventDispatcher'\nimport { StandardControlsEventMap } from './StandardControlsEventMap'\n\nconst _ray = /* @__PURE__ */ new Ray()\nconst _plane = /* @__PURE__ */ new Plane()\nconst TILT_LIMIT = Math.cos(70 * (Math.PI / 180))\n\n// This set of controls performs orbiting, dollying (zooming), and panning.\n// Unlike TrackballControls, it maintains the \"up\" direction object.up (+Y by default).\n//\n//    Orbit - left mouse / touch: one-finger move\n//    Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n//    Pan - right mouse, or left mouse + ctrl/meta/shiftKey, or arrow keys / touch: two-finger move\n\nconst moduloWrapAround = (offset: number, capacity: number) => ((offset % capacity) + capacity) % capacity\n\nclass OrbitControls extends EventDispatcher<StandardControlsEventMap> {\n  object: PerspectiveCamera | OrthographicCamera\n  domElement: HTMLElement | undefined\n  // Set to false to disable this control\n  enabled = true\n  // \"target\" sets the location of focus, where the object orbits around\n  target = new Vector3()\n  // How far you can dolly in and out ( PerspectiveCamera only )\n  minDistance = 0\n  maxDistance = Infinity\n  // How far you can zoom in and out ( OrthographicCamera only )\n  minZoom = 0\n  maxZoom = Infinity\n  // How far you can orbit vertically, upper and lower limits.\n  // Range is 0 to Math.PI radians.\n  minPolarAngle = 0 // radians\n  maxPolarAngle = Math.PI // radians\n  // How far you can orbit horizontally, upper and lower limits.\n  // If set, the interval [ min, max ] must be a sub-interval of [ - 2 PI, 2 PI ], with ( max - min < 2 PI )\n  minAzimuthAngle = -Infinity // radians\n  maxAzimuthAngle = Infinity // radians\n  // Set to true to enable damping (inertia)\n  // If damping is enabled, you must call controls.update() in your animation loop\n  enableDamping = false\n  dampingFactor = 0.05\n  // This option actually enables dollying in and out; left as \"zoom\" for backwards compatibility.\n  // Set to false to disable zooming\n  enableZoom = true\n  zoomSpeed = 1.0\n  // Set to false to disable rotating\n  enableRotate = true\n  rotateSpeed = 1.0\n  // Set to false to disable panning\n  enablePan = true\n  panSpeed = 1.0\n  screenSpacePanning = true // if false, pan orthogonal to world-space direction camera.up\n  keyPanSpeed = 7.0 // pixels moved per arrow key push\n  zoomToCursor = false\n  // Set to true to automatically rotate around the target\n  // If auto-rotate is enabled, you must call controls.update() in your animation loop\n  autoRotate = false\n  autoRotateSpeed = 2.0 // 30 seconds per orbit when fps is 60\n  reverseOrbit = false // true if you want to reverse the orbit to mouse drag from left to right = orbits left\n  reverseHorizontalOrbit = false // true if you want to reverse the horizontal orbit direction\n  reverseVerticalOrbit = false // true if you want to reverse the vertical orbit direction\n  // The four arrow keys\n  keys = { LEFT: 'ArrowLeft', UP: 'ArrowUp', RIGHT: 'ArrowRight', BOTTOM: 'ArrowDown' }\n  // Mouse buttons\n  mouseButtons: Partial<{\n    LEFT: MOUSE\n    MIDDLE: MOUSE\n    RIGHT: MOUSE\n  }> = {\n    LEFT: MOUSE.ROTATE,\n    MIDDLE: MOUSE.DOLLY,\n    RIGHT: MOUSE.PAN,\n  }\n  // Touch fingers\n  touches: Partial<{\n    ONE: TOUCH\n    TWO: TOUCH\n  }> = { ONE: TOUCH.ROTATE, TWO: TOUCH.DOLLY_PAN }\n  target0: Vector3\n  position0: Vector3\n  zoom0: number\n  // the target DOM element for key events\n  _domElementKeyEvents: any = null\n\n  getPolarAngle: () => number\n  getAzimuthalAngle: () => number\n  setPolarAngle: (x: number) => void\n  setAzimuthalAngle: (x: number) => void\n  getDistance: () => number\n  // Not used in most scenarios, however they can be useful for specific use cases\n  getZoomScale: () => number\n\n  listenToKeyEvents: (domElement: HTMLElement) => void\n  stopListenToKeyEvents: () => void\n  saveState: () => void\n  reset: () => void\n  update: () => void\n  connect: (domElement: HTMLElement) => void\n  dispose: () => void\n\n  // Dolly in programmatically\n  dollyIn: (dollyScale?: number) => void\n  // Dolly out programmatically\n  dollyOut: (dollyScale?: number) => void\n  // Get the current scale\n  getScale: () => number\n  // Set the current scale (these are not used in most scenarios, however they can be useful for specific use cases)\n  setScale: (newScale: number) => void\n\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement) {\n    super()\n\n    this.object = object\n    this.domElement = domElement\n\n    // for reset\n    this.target0 = this.target.clone()\n    this.position0 = this.object.position.clone()\n    this.zoom0 = this.object.zoom\n\n    //\n    // public methods\n    //\n\n    this.getPolarAngle = (): number => spherical.phi\n\n    this.getAzimuthalAngle = (): number => spherical.theta\n\n    this.setPolarAngle = (value: number): void => {\n      // use modulo wrapping to safeguard value\n      let phi = moduloWrapAround(value, 2 * Math.PI)\n      let currentPhi = spherical.phi\n\n      // convert to the equivalent shortest angle\n      if (currentPhi < 0) currentPhi += 2 * Math.PI\n      if (phi < 0) phi += 2 * Math.PI\n      let phiDist = Math.abs(phi - currentPhi)\n      if (2 * Math.PI - phiDist < phiDist) {\n        if (phi < currentPhi) {\n          phi += 2 * Math.PI\n        } else {\n          currentPhi += 2 * Math.PI\n        }\n      }\n      sphericalDelta.phi = phi - currentPhi\n      scope.update()\n    }\n\n    this.setAzimuthalAngle = (value: number): void => {\n      // use modulo wrapping to safeguard value\n      let theta = moduloWrapAround(value, 2 * Math.PI)\n      let currentTheta = spherical.theta\n\n      // convert to the equivalent shortest angle\n      if (currentTheta < 0) currentTheta += 2 * Math.PI\n      if (theta < 0) theta += 2 * Math.PI\n      let thetaDist = Math.abs(theta - currentTheta)\n      if (2 * Math.PI - thetaDist < thetaDist) {\n        if (theta < currentTheta) {\n          theta += 2 * Math.PI\n        } else {\n          currentTheta += 2 * Math.PI\n        }\n      }\n      sphericalDelta.theta = theta - currentTheta\n      scope.update()\n    }\n\n    this.getDistance = (): number => scope.object.position.distanceTo(scope.target)\n\n    this.listenToKeyEvents = (domElement: HTMLElement): void => {\n      domElement.addEventListener('keydown', onKeyDown)\n      this._domElementKeyEvents = domElement\n    }\n\n    this.stopListenToKeyEvents = (): void => {\n      this._domElementKeyEvents.removeEventListener('keydown', onKeyDown)\n      this._domElementKeyEvents = null\n    }\n\n    this.saveState = (): void => {\n      scope.target0.copy(scope.target)\n      scope.position0.copy(scope.object.position)\n      scope.zoom0 = scope.object.zoom\n    }\n\n    this.reset = (): void => {\n      scope.target.copy(scope.target0)\n      scope.object.position.copy(scope.position0)\n      scope.object.zoom = scope.zoom0\n      scope.object.updateProjectionMatrix()\n\n      // @ts-ignore\n      scope.dispatchEvent(changeEvent)\n\n      scope.update()\n\n      state = STATE.NONE\n    }\n\n    // this method is exposed, but perhaps it would be better if we can make it private...\n    this.update = ((): (() => void) => {\n      const offset = new Vector3()\n      const up = new Vector3(0, 1, 0)\n\n      // so camera.up is the orbit axis\n      const quat = new Quaternion().setFromUnitVectors(object.up, up)\n      const quatInverse = quat.clone().invert()\n\n      const lastPosition = new Vector3()\n      const lastQuaternion = new Quaternion()\n\n      const twoPI = 2 * Math.PI\n\n      return function update(): boolean {\n        const position = scope.object.position\n\n        // update new up direction\n        quat.setFromUnitVectors(object.up, up)\n        quatInverse.copy(quat).invert()\n\n        offset.copy(position).sub(scope.target)\n\n        // rotate offset to \"y-axis-is-up\" space\n        offset.applyQuaternion(quat)\n\n        // angle from z-axis around y-axis\n        spherical.setFromVector3(offset)\n\n        if (scope.autoRotate && state === STATE.NONE) {\n          rotateLeft(getAutoRotationAngle())\n        }\n\n        if (scope.enableDamping) {\n          spherical.theta += sphericalDelta.theta * scope.dampingFactor\n          spherical.phi += sphericalDelta.phi * scope.dampingFactor\n        } else {\n          spherical.theta += sphericalDelta.theta\n          spherical.phi += sphericalDelta.phi\n        }\n\n        // restrict theta to be between desired limits\n\n        let min = scope.minAzimuthAngle\n        let max = scope.maxAzimuthAngle\n\n        if (isFinite(min) && isFinite(max)) {\n          if (min < -Math.PI) min += twoPI\n          else if (min > Math.PI) min -= twoPI\n\n          if (max < -Math.PI) max += twoPI\n          else if (max > Math.PI) max -= twoPI\n\n          if (min <= max) {\n            spherical.theta = Math.max(min, Math.min(max, spherical.theta))\n          } else {\n            spherical.theta =\n              spherical.theta > (min + max) / 2 ? Math.max(min, spherical.theta) : Math.min(max, spherical.theta)\n          }\n        }\n\n        // restrict phi to be between desired limits\n        spherical.phi = Math.max(scope.minPolarAngle, Math.min(scope.maxPolarAngle, spherical.phi))\n        spherical.makeSafe()\n\n        // move target to panned location\n\n        if (scope.enableDamping === true) {\n          scope.target.addScaledVector(panOffset, scope.dampingFactor)\n        } else {\n          scope.target.add(panOffset)\n        }\n\n        // adjust the camera position based on zoom only if we're not zooming to the cursor or if it's an ortho camera\n        // we adjust zoom later in these cases\n        if ((scope.zoomToCursor && performCursorZoom) || (scope.object as OrthographicCamera).isOrthographicCamera) {\n          spherical.radius = clampDistance(spherical.radius)\n        } else {\n          spherical.radius = clampDistance(spherical.radius * scale)\n        }\n\n        offset.setFromSpherical(spherical)\n\n        // rotate offset back to \"camera-up-vector-is-up\" space\n        offset.applyQuaternion(quatInverse)\n\n        position.copy(scope.target).add(offset)\n\n        if (!scope.object.matrixAutoUpdate) scope.object.updateMatrix()\n        scope.object.lookAt(scope.target)\n\n        if (scope.enableDamping === true) {\n          sphericalDelta.theta *= 1 - scope.dampingFactor\n          sphericalDelta.phi *= 1 - scope.dampingFactor\n\n          panOffset.multiplyScalar(1 - scope.dampingFactor)\n        } else {\n          sphericalDelta.set(0, 0, 0)\n\n          panOffset.set(0, 0, 0)\n        }\n\n        // adjust camera position\n        let zoomChanged = false\n        if (scope.zoomToCursor && performCursorZoom) {\n          let newRadius = null\n          if (scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) {\n            // move the camera down the pointer ray\n            // this method avoids floating point error\n            const prevRadius = offset.length()\n            newRadius = clampDistance(prevRadius * scale)\n\n            const radiusDelta = prevRadius - newRadius\n            scope.object.position.addScaledVector(dollyDirection, radiusDelta)\n            scope.object.updateMatrixWorld()\n          } else if ((scope.object as OrthographicCamera).isOrthographicCamera) {\n            // adjust the ortho camera position based on zoom changes\n            const mouseBefore = new Vector3(mouse.x, mouse.y, 0)\n            mouseBefore.unproject(scope.object)\n\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale))\n            scope.object.updateProjectionMatrix()\n            zoomChanged = true\n\n            const mouseAfter = new Vector3(mouse.x, mouse.y, 0)\n            mouseAfter.unproject(scope.object)\n\n            scope.object.position.sub(mouseAfter).add(mouseBefore)\n            scope.object.updateMatrixWorld()\n\n            newRadius = offset.length()\n          } else {\n            console.warn('WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled.')\n            scope.zoomToCursor = false\n          }\n\n          // handle the placement of the target\n          if (newRadius !== null) {\n            if (scope.screenSpacePanning) {\n              // position the orbit target in front of the new camera position\n              scope.target\n                .set(0, 0, -1)\n                .transformDirection(scope.object.matrix)\n                .multiplyScalar(newRadius)\n                .add(scope.object.position)\n            } else {\n              // get the ray and translation plane to compute target\n              _ray.origin.copy(scope.object.position)\n              _ray.direction.set(0, 0, -1).transformDirection(scope.object.matrix)\n\n              // if the camera is 20 degrees above the horizon then don't adjust the focus target to avoid\n              // extremely large values\n              if (Math.abs(scope.object.up.dot(_ray.direction)) < TILT_LIMIT) {\n                object.lookAt(scope.target)\n              } else {\n                _plane.setFromNormalAndCoplanarPoint(scope.object.up, scope.target)\n                _ray.intersectPlane(_plane, scope.target)\n              }\n            }\n          }\n        } else if (scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera) {\n          zoomChanged = scale !== 1\n\n          if (zoomChanged) {\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale))\n            scope.object.updateProjectionMatrix()\n          }\n        }\n\n        scale = 1\n        performCursorZoom = false\n\n        // update condition is:\n        // min(camera displacement, camera rotation in radians)^2 > EPS\n        // using small-angle approximation cos(x/2) = 1 - x^2 / 8\n\n        if (\n          zoomChanged ||\n          lastPosition.distanceToSquared(scope.object.position) > EPS ||\n          8 * (1 - lastQuaternion.dot(scope.object.quaternion)) > EPS\n        ) {\n          // @ts-ignore\n          scope.dispatchEvent(changeEvent)\n\n          lastPosition.copy(scope.object.position)\n          lastQuaternion.copy(scope.object.quaternion)\n          zoomChanged = false\n\n          return true\n        }\n\n        return false\n      }\n    })()\n\n    // https://github.com/mrdoob/three.js/issues/20575\n    this.connect = (domElement: HTMLElement): void => {\n      scope.domElement = domElement\n      // disables touch scroll\n      // touch-action needs to be defined for pointer events to work on mobile\n      // https://stackoverflow.com/a/48254578\n      scope.domElement.style.touchAction = 'none'\n      scope.domElement.addEventListener('contextmenu', onContextMenu)\n      scope.domElement.addEventListener('pointerdown', onPointerDown)\n      scope.domElement.addEventListener('pointercancel', onPointerUp)\n      scope.domElement.addEventListener('wheel', onMouseWheel)\n    }\n\n    this.dispose = (): void => {\n      // Enabling touch scroll\n      if (scope.domElement) {\n        scope.domElement.style.touchAction = 'auto'\n      }\n      scope.domElement?.removeEventListener('contextmenu', onContextMenu)\n      scope.domElement?.removeEventListener('pointerdown', onPointerDown)\n      scope.domElement?.removeEventListener('pointercancel', onPointerUp)\n      scope.domElement?.removeEventListener('wheel', onMouseWheel)\n      scope.domElement?.ownerDocument.removeEventListener('pointermove', onPointerMove)\n      scope.domElement?.ownerDocument.removeEventListener('pointerup', onPointerUp)\n      if (scope._domElementKeyEvents !== null) {\n        scope._domElementKeyEvents.removeEventListener('keydown', onKeyDown)\n      }\n      //scope.dispatchEvent( { type: 'dispose' } ); // should this be added here?\n    }\n\n    //\n    // internals\n    //\n\n    const scope = this\n\n    const changeEvent = { type: 'change' }\n    const startEvent = { type: 'start' }\n    const endEvent = { type: 'end' }\n\n    const STATE = {\n      NONE: -1,\n      ROTATE: 0,\n      DOLLY: 1,\n      PAN: 2,\n      TOUCH_ROTATE: 3,\n      TOUCH_PAN: 4,\n      TOUCH_DOLLY_PAN: 5,\n      TOUCH_DOLLY_ROTATE: 6,\n    }\n\n    let state = STATE.NONE\n\n    const EPS = 0.000001\n\n    // current position in spherical coordinates\n    const spherical = new Spherical()\n    const sphericalDelta = new Spherical()\n\n    let scale = 1\n    const panOffset = new Vector3()\n\n    const rotateStart = new Vector2()\n    const rotateEnd = new Vector2()\n    const rotateDelta = new Vector2()\n\n    const panStart = new Vector2()\n    const panEnd = new Vector2()\n    const panDelta = new Vector2()\n\n    const dollyStart = new Vector2()\n    const dollyEnd = new Vector2()\n    const dollyDelta = new Vector2()\n\n    const dollyDirection = new Vector3()\n    const mouse = new Vector2()\n    let performCursorZoom = false\n\n    const pointers: PointerEvent[] = []\n    const pointerPositions: { [key: string]: Vector2 } = {}\n\n    function getAutoRotationAngle(): number {\n      return ((2 * Math.PI) / 60 / 60) * scope.autoRotateSpeed\n    }\n\n    function getZoomScale(): number {\n      return Math.pow(0.95, scope.zoomSpeed)\n    }\n\n    function rotateLeft(angle: number): void {\n      if (scope.reverseOrbit || scope.reverseHorizontalOrbit) {\n        sphericalDelta.theta += angle\n      } else {\n        sphericalDelta.theta -= angle\n      }\n    }\n\n    function rotateUp(angle: number): void {\n      if (scope.reverseOrbit || scope.reverseVerticalOrbit) {\n        sphericalDelta.phi += angle\n      } else {\n        sphericalDelta.phi -= angle\n      }\n    }\n\n    const panLeft = (() => {\n      const v = new Vector3()\n\n      return function panLeft(distance: number, objectMatrix: Matrix4) {\n        v.setFromMatrixColumn(objectMatrix, 0) // get X column of objectMatrix\n        v.multiplyScalar(-distance)\n\n        panOffset.add(v)\n      }\n    })()\n\n    const panUp = (() => {\n      const v = new Vector3()\n\n      return function panUp(distance: number, objectMatrix: Matrix4) {\n        if (scope.screenSpacePanning === true) {\n          v.setFromMatrixColumn(objectMatrix, 1)\n        } else {\n          v.setFromMatrixColumn(objectMatrix, 0)\n          v.crossVectors(scope.object.up, v)\n        }\n\n        v.multiplyScalar(distance)\n\n        panOffset.add(v)\n      }\n    })()\n\n    // deltaX and deltaY are in pixels; right and down are positive\n    const pan = (() => {\n      const offset = new Vector3()\n\n      return function pan(deltaX: number, deltaY: number) {\n        const element = scope.domElement\n\n        if (element && scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) {\n          // perspective\n          const position = scope.object.position\n          offset.copy(position).sub(scope.target)\n          let targetDistance = offset.length()\n\n          // half of the fov is center to top of screen\n          targetDistance *= Math.tan(((scope.object.fov / 2) * Math.PI) / 180.0)\n\n          // we use only clientHeight here so aspect ratio does not distort speed\n          panLeft((2 * deltaX * targetDistance) / element.clientHeight, scope.object.matrix)\n          panUp((2 * deltaY * targetDistance) / element.clientHeight, scope.object.matrix)\n        } else if (element && scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera) {\n          // orthographic\n          panLeft(\n            (deltaX * (scope.object.right - scope.object.left)) / scope.object.zoom / element.clientWidth,\n            scope.object.matrix,\n          )\n          panUp(\n            (deltaY * (scope.object.top - scope.object.bottom)) / scope.object.zoom / element.clientHeight,\n            scope.object.matrix,\n          )\n        } else {\n          // camera neither orthographic nor perspective\n          console.warn('WARNING: OrbitControls.js encountered an unknown camera type - pan disabled.')\n          scope.enablePan = false\n        }\n      }\n    })()\n\n    function setScale(newScale: number) {\n      if (\n        (scope.object instanceof PerspectiveCamera && scope.object.isPerspectiveCamera) ||\n        (scope.object instanceof OrthographicCamera && scope.object.isOrthographicCamera)\n      ) {\n        scale = newScale\n      } else {\n        console.warn('WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.')\n        scope.enableZoom = false\n      }\n    }\n\n    function dollyOut(dollyScale: number) {\n      setScale(scale / dollyScale)\n    }\n\n    function dollyIn(dollyScale: number) {\n      setScale(scale * dollyScale)\n    }\n\n    function updateMouseParameters(event: MouseEvent): void {\n      if (!scope.zoomToCursor || !scope.domElement) {\n        return\n      }\n\n      performCursorZoom = true\n\n      const rect = scope.domElement.getBoundingClientRect()\n      const x = event.clientX - rect.left\n      const y = event.clientY - rect.top\n      const w = rect.width\n      const h = rect.height\n\n      mouse.x = (x / w) * 2 - 1\n      mouse.y = -(y / h) * 2 + 1\n\n      dollyDirection.set(mouse.x, mouse.y, 1).unproject(scope.object).sub(scope.object.position).normalize()\n    }\n\n    function clampDistance(dist: number): number {\n      return Math.max(scope.minDistance, Math.min(scope.maxDistance, dist))\n    }\n\n    //\n    // event callbacks - update the object state\n    //\n\n    function handleMouseDownRotate(event: MouseEvent) {\n      rotateStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseDownDolly(event: MouseEvent) {\n      updateMouseParameters(event)\n      dollyStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseDownPan(event: MouseEvent) {\n      panStart.set(event.clientX, event.clientY)\n    }\n\n    function handleMouseMoveRotate(event: MouseEvent) {\n      rotateEnd.set(event.clientX, event.clientY)\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed)\n\n      const element = scope.domElement\n\n      if (element) {\n        rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight) // yes, height\n        rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight)\n      }\n      rotateStart.copy(rotateEnd)\n      scope.update()\n    }\n\n    function handleMouseMoveDolly(event: MouseEvent) {\n      dollyEnd.set(event.clientX, event.clientY)\n      dollyDelta.subVectors(dollyEnd, dollyStart)\n\n      if (dollyDelta.y > 0) {\n        dollyOut(getZoomScale())\n      } else if (dollyDelta.y < 0) {\n        dollyIn(getZoomScale())\n      }\n\n      dollyStart.copy(dollyEnd)\n      scope.update()\n    }\n\n    function handleMouseMovePan(event: MouseEvent) {\n      panEnd.set(event.clientX, event.clientY)\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed)\n      pan(panDelta.x, panDelta.y)\n      panStart.copy(panEnd)\n      scope.update()\n    }\n\n    function handleMouseWheel(event: WheelEvent) {\n      updateMouseParameters(event)\n\n      if (event.deltaY < 0) {\n        dollyIn(getZoomScale())\n      } else if (event.deltaY > 0) {\n        dollyOut(getZoomScale())\n      }\n\n      scope.update()\n    }\n\n    function handleKeyDown(event: KeyboardEvent) {\n      let needsUpdate = false\n\n      switch (event.code) {\n        case scope.keys.UP:\n          pan(0, scope.keyPanSpeed)\n          needsUpdate = true\n          break\n\n        case scope.keys.BOTTOM:\n          pan(0, -scope.keyPanSpeed)\n          needsUpdate = true\n          break\n\n        case scope.keys.LEFT:\n          pan(scope.keyPanSpeed, 0)\n          needsUpdate = true\n          break\n\n        case scope.keys.RIGHT:\n          pan(-scope.keyPanSpeed, 0)\n          needsUpdate = true\n          break\n      }\n\n      if (needsUpdate) {\n        // prevent the browser from scrolling on cursor keys\n        event.preventDefault()\n        scope.update()\n      }\n    }\n\n    function handleTouchStartRotate() {\n      if (pointers.length == 1) {\n        rotateStart.set(pointers[0].pageX, pointers[0].pageY)\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX)\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY)\n\n        rotateStart.set(x, y)\n      }\n    }\n\n    function handleTouchStartPan() {\n      if (pointers.length == 1) {\n        panStart.set(pointers[0].pageX, pointers[0].pageY)\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX)\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY)\n\n        panStart.set(x, y)\n      }\n    }\n\n    function handleTouchStartDolly() {\n      const dx = pointers[0].pageX - pointers[1].pageX\n      const dy = pointers[0].pageY - pointers[1].pageY\n      const distance = Math.sqrt(dx * dx + dy * dy)\n\n      dollyStart.set(0, distance)\n    }\n\n    function handleTouchStartDollyPan() {\n      if (scope.enableZoom) handleTouchStartDolly()\n      if (scope.enablePan) handleTouchStartPan()\n    }\n\n    function handleTouchStartDollyRotate() {\n      if (scope.enableZoom) handleTouchStartDolly()\n      if (scope.enableRotate) handleTouchStartRotate()\n    }\n\n    function handleTouchMoveRotate(event: PointerEvent) {\n      if (pointers.length == 1) {\n        rotateEnd.set(event.pageX, event.pageY)\n      } else {\n        const position = getSecondPointerPosition(event)\n        const x = 0.5 * (event.pageX + position.x)\n        const y = 0.5 * (event.pageY + position.y)\n        rotateEnd.set(x, y)\n      }\n\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed)\n\n      const element = scope.domElement\n\n      if (element) {\n        rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight) // yes, height\n        rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight)\n      }\n      rotateStart.copy(rotateEnd)\n    }\n\n    function handleTouchMovePan(event: PointerEvent) {\n      if (pointers.length == 1) {\n        panEnd.set(event.pageX, event.pageY)\n      } else {\n        const position = getSecondPointerPosition(event)\n        const x = 0.5 * (event.pageX + position.x)\n        const y = 0.5 * (event.pageY + position.y)\n        panEnd.set(x, y)\n      }\n\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed)\n      pan(panDelta.x, panDelta.y)\n      panStart.copy(panEnd)\n    }\n\n    function handleTouchMoveDolly(event: PointerEvent) {\n      const position = getSecondPointerPosition(event)\n      const dx = event.pageX - position.x\n      const dy = event.pageY - position.y\n      const distance = Math.sqrt(dx * dx + dy * dy)\n\n      dollyEnd.set(0, distance)\n      dollyDelta.set(0, Math.pow(dollyEnd.y / dollyStart.y, scope.zoomSpeed))\n      dollyOut(dollyDelta.y)\n      dollyStart.copy(dollyEnd)\n    }\n\n    function handleTouchMoveDollyPan(event: PointerEvent) {\n      if (scope.enableZoom) handleTouchMoveDolly(event)\n      if (scope.enablePan) handleTouchMovePan(event)\n    }\n\n    function handleTouchMoveDollyRotate(event: PointerEvent) {\n      if (scope.enableZoom) handleTouchMoveDolly(event)\n      if (scope.enableRotate) handleTouchMoveRotate(event)\n    }\n\n    //\n    // event handlers - FSM: listen for events and reset state\n    //\n\n    function onPointerDown(event: PointerEvent) {\n      if (scope.enabled === false) return\n\n      if (pointers.length === 0) {\n        scope.domElement?.ownerDocument.addEventListener('pointermove', onPointerMove)\n        scope.domElement?.ownerDocument.addEventListener('pointerup', onPointerUp)\n      }\n\n      addPointer(event)\n\n      if (event.pointerType === 'touch') {\n        onTouchStart(event)\n      } else {\n        onMouseDown(event)\n      }\n    }\n\n    function onPointerMove(event: PointerEvent) {\n      if (scope.enabled === false) return\n\n      if (event.pointerType === 'touch') {\n        onTouchMove(event)\n      } else {\n        onMouseMove(event)\n      }\n    }\n\n    function onPointerUp(event: PointerEvent) {\n      removePointer(event)\n\n      if (pointers.length === 0) {\n        scope.domElement?.releasePointerCapture(event.pointerId)\n\n        scope.domElement?.ownerDocument.removeEventListener('pointermove', onPointerMove)\n        scope.domElement?.ownerDocument.removeEventListener('pointerup', onPointerUp)\n      }\n\n      // @ts-ignore\n      scope.dispatchEvent(endEvent)\n\n      state = STATE.NONE\n    }\n\n    function onMouseDown(event: MouseEvent) {\n      let mouseAction\n\n      switch (event.button) {\n        case 0:\n          mouseAction = scope.mouseButtons.LEFT\n          break\n\n        case 1:\n          mouseAction = scope.mouseButtons.MIDDLE\n          break\n\n        case 2:\n          mouseAction = scope.mouseButtons.RIGHT\n          break\n\n        default:\n          mouseAction = -1\n      }\n\n      switch (mouseAction) {\n        case MOUSE.DOLLY:\n          if (scope.enableZoom === false) return\n          handleMouseDownDolly(event)\n          state = STATE.DOLLY\n          break\n\n        case MOUSE.ROTATE:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enablePan === false) return\n            handleMouseDownPan(event)\n            state = STATE.PAN\n          } else {\n            if (scope.enableRotate === false) return\n            handleMouseDownRotate(event)\n            state = STATE.ROTATE\n          }\n          break\n\n        case MOUSE.PAN:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enableRotate === false) return\n            handleMouseDownRotate(event)\n            state = STATE.ROTATE\n          } else {\n            if (scope.enablePan === false) return\n            handleMouseDownPan(event)\n            state = STATE.PAN\n          }\n          break\n\n        default:\n          state = STATE.NONE\n      }\n\n      if (state !== STATE.NONE) {\n        // @ts-ignore\n        scope.dispatchEvent(startEvent)\n      }\n    }\n\n    function onMouseMove(event: MouseEvent) {\n      if (scope.enabled === false) return\n\n      switch (state) {\n        case STATE.ROTATE:\n          if (scope.enableRotate === false) return\n          handleMouseMoveRotate(event)\n          break\n\n        case STATE.DOLLY:\n          if (scope.enableZoom === false) return\n          handleMouseMoveDolly(event)\n          break\n\n        case STATE.PAN:\n          if (scope.enablePan === false) return\n          handleMouseMovePan(event)\n          break\n      }\n    }\n\n    function onMouseWheel(event: WheelEvent) {\n      if (scope.enabled === false || scope.enableZoom === false || (state !== STATE.NONE && state !== STATE.ROTATE)) {\n        return\n      }\n\n      event.preventDefault()\n\n      // @ts-ignore\n      scope.dispatchEvent(startEvent)\n\n      handleMouseWheel(event)\n\n      // @ts-ignore\n      scope.dispatchEvent(endEvent)\n    }\n\n    function onKeyDown(event: KeyboardEvent) {\n      if (scope.enabled === false || scope.enablePan === false) return\n      handleKeyDown(event)\n    }\n\n    function onTouchStart(event: PointerEvent) {\n      trackPointer(event)\n\n      switch (pointers.length) {\n        case 1:\n          switch (scope.touches.ONE) {\n            case TOUCH.ROTATE:\n              if (scope.enableRotate === false) return\n              handleTouchStartRotate()\n              state = STATE.TOUCH_ROTATE\n              break\n\n            case TOUCH.PAN:\n              if (scope.enablePan === false) return\n              handleTouchStartPan()\n              state = STATE.TOUCH_PAN\n              break\n\n            default:\n              state = STATE.NONE\n          }\n\n          break\n\n        case 2:\n          switch (scope.touches.TWO) {\n            case TOUCH.DOLLY_PAN:\n              if (scope.enableZoom === false && scope.enablePan === false) return\n              handleTouchStartDollyPan()\n              state = STATE.TOUCH_DOLLY_PAN\n              break\n\n            case TOUCH.DOLLY_ROTATE:\n              if (scope.enableZoom === false && scope.enableRotate === false) return\n              handleTouchStartDollyRotate()\n              state = STATE.TOUCH_DOLLY_ROTATE\n              break\n\n            default:\n              state = STATE.NONE\n          }\n\n          break\n\n        default:\n          state = STATE.NONE\n      }\n\n      if (state !== STATE.NONE) {\n        // @ts-ignore\n        scope.dispatchEvent(startEvent)\n      }\n    }\n\n    function onTouchMove(event: PointerEvent) {\n      trackPointer(event)\n\n      switch (state) {\n        case STATE.TOUCH_ROTATE:\n          if (scope.enableRotate === false) return\n          handleTouchMoveRotate(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_PAN:\n          if (scope.enablePan === false) return\n          handleTouchMovePan(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_DOLLY_PAN:\n          if (scope.enableZoom === false && scope.enablePan === false) return\n          handleTouchMoveDollyPan(event)\n          scope.update()\n          break\n\n        case STATE.TOUCH_DOLLY_ROTATE:\n          if (scope.enableZoom === false && scope.enableRotate === false) return\n          handleTouchMoveDollyRotate(event)\n          scope.update()\n          break\n\n        default:\n          state = STATE.NONE\n      }\n    }\n\n    function onContextMenu(event: Event) {\n      if (scope.enabled === false) return\n      event.preventDefault()\n    }\n\n    function addPointer(event: PointerEvent) {\n      pointers.push(event)\n    }\n\n    function removePointer(event: PointerEvent) {\n      delete pointerPositions[event.pointerId]\n\n      for (let i = 0; i < pointers.length; i++) {\n        if (pointers[i].pointerId == event.pointerId) {\n          pointers.splice(i, 1)\n          return\n        }\n      }\n    }\n\n    function trackPointer(event: PointerEvent) {\n      let position = pointerPositions[event.pointerId]\n\n      if (position === undefined) {\n        position = new Vector2()\n        pointerPositions[event.pointerId] = position\n      }\n\n      position.set(event.pageX, event.pageY)\n    }\n\n    function getSecondPointerPosition(event: PointerEvent) {\n      const pointer = event.pointerId === pointers[0].pointerId ? pointers[1] : pointers[0]\n      return pointerPositions[pointer.pointerId]\n    }\n\n    // Add dolly in/out methods for public API\n\n    this.dollyIn = (dollyScale = getZoomScale()) => {\n      dollyIn(dollyScale)\n      scope.update()\n    }\n\n    this.dollyOut = (dollyScale = getZoomScale()) => {\n      dollyOut(dollyScale)\n      scope.update()\n    }\n\n    this.getScale = () => {\n      return scale\n    }\n\n    this.setScale = (newScale) => {\n      setScale(newScale)\n      scope.update()\n    }\n\n    this.getZoomScale = () => {\n      return getZoomScale()\n    }\n\n    // connect events\n    if (domElement !== undefined) this.connect(domElement)\n    // force an update at start\n    this.update()\n  }\n}\n\n// This set of controls performs orbiting, dollying (zooming), and panning.\n// Unlike TrackballControls, it maintains the \"up\" direction object.up (+Y by default).\n// This is very similar to OrbitControls, another set of touch behavior\n//\n//    Orbit - right mouse, or left mouse + ctrl/meta/shiftKey / touch: two-finger rotate\n//    Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish\n//    Pan - left mouse, or arrow keys / touch: one-finger move\n\nclass MapControls extends OrbitControls {\n  constructor(object: PerspectiveCamera | OrthographicCamera, domElement?: HTMLElement) {\n    super(object, domElement)\n\n    this.screenSpacePanning = false // pan orthogonal to world-space direction camera.up\n\n    this.mouseButtons.LEFT = MOUSE.PAN\n    this.mouseButtons.RIGHT = MOUSE.ROTATE\n\n    this.touches.ONE = TOUCH.PAN\n    this.touches.TWO = TOUCH.DOLLY_ROTATE\n  }\n}\n\nexport { OrbitControls, MapControls }\n"], "names": ["dom<PERSON>lement", "panLeft", "panUp", "pan"], "mappings": ";;;;;;;;;;;;;;;;;;;AAgBA,MAAM,OAAA,aAAA,GAAA,2MAA2B,MAAA;AACjC,MAAM,SAAA,aAAA,GAAA,2MAA6B,QAAA;AACnC,MAAM,aAAa,KAAK,GAAA,CAAI,KAAA,CAAM,KAAK,EAAA,GAAK,GAAA,CAAI;AAShD,MAAM,mBAAmB,CAAC,QAAgB,WAAA,CAAuB,SAAS,WAAY,QAAA,IAAY;AAElG,MAAM,4QAAsB,kBAAA,CAA0C;IA6FpE,YAAY,MAAA,EAAgD,UAAA,CAA0B;QAC9E,KAAA;QA7FR,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,uCAAA;QAAA,cAAA,IAAA,EAAA,WAAU;QAEV,sEAAA;QAAA,cAAA,IAAA,EAAA,UAAS,2MAAI,UAAA;QAEb,8DAAA;QAAA,cAAA,IAAA,EAAA,eAAc;QACd,cAAA,IAAA,EAAA,eAAc;QAEd,8DAAA;QAAA,cAAA,IAAA,EAAA,WAAU;QACV,cAAA,IAAA,EAAA,WAAU;QAGV,4DAAA;QAAA,iCAAA;QAAA,cAAA,IAAA,EAAA,iBAAgB;QAChB,UAAA;QAAA,cAAA,IAAA,EAAA,iBAAgB,KAAK,EAAA;QAGrB,UAAA;QAAA,8DAAA;QAAA,0GAAA;QAAA,cAAA,IAAA,EAAA,mBAAkB,CAAA;QAClB,UAAA;QAAA,cAAA,IAAA,EAAA,mBAAkB;QAGlB,UAAA;QAAA,0CAAA;QAAA,gFAAA;QAAA,cAAA,IAAA,EAAA,iBAAgB;QAChB,cAAA,IAAA,EAAA,iBAAgB;QAGhB,gGAAA;QAAA,kCAAA;QAAA,cAAA,IAAA,EAAA,cAAa;QACb,cAAA,IAAA,EAAA,aAAY;QAEZ,mCAAA;QAAA,cAAA,IAAA,EAAA,gBAAe;QACf,cAAA,IAAA,EAAA,eAAc;QAEd,kCAAA;QAAA,cAAA,IAAA,EAAA,aAAY;QACZ,cAAA,IAAA,EAAA,YAAW;QACX,cAAA,IAAA,EAAA,sBAAqB;QACrB,8DAAA;QAAA,cAAA,IAAA,EAAA,eAAc;QACd,kCAAA;QAAA,cAAA,IAAA,EAAA,gBAAe;QAGf,wDAAA;QAAA,oFAAA;QAAA,cAAA,IAAA,EAAA,cAAa;QACb,cAAA,IAAA,EAAA,mBAAkB;QAClB,sCAAA;QAAA,cAAA,IAAA,EAAA,gBAAe;QACf,uFAAA;QAAA,cAAA,IAAA,EAAA,0BAAyB;QACzB,6DAAA;QAAA,cAAA,IAAA,EAAA,wBAAuB;QAEvB,2DAAA;QAAA,sBAAA;QAAA,cAAA,IAAA,EAAA,QAAO;YAAE,MAAM;YAAa,IAAI;YAAW,OAAO;YAAc,QAAQ;QAAA;QAExE,gBAAA;QAAA,cAAA,IAAA,EAAA,gBAIK;YACH,MAAM,+MAAA,CAAM,MAAA;YACZ,+MAAQ,QAAA,CAAM,KAAA;YACd,8MAAO,QAAA,CAAM,GAAA;QAAA;QAGf,gBAAA;QAAA,cAAA,IAAA,EAAA,WAGK;YAAE,4MAAK,QAAA,CAAM,MAAA;YAAQ,4MAAK,QAAA,CAAM,SAAA;QAAA;QACrC,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,wCAAA;QAAA,cAAA,IAAA,EAAA,wBAA4B;QAE5B,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAEA,gFAAA;QAAA,cAAA,IAAA,EAAA;QAEA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QACA,cAAA,IAAA,EAAA;QAGA,4BAAA;QAAA,cAAA,IAAA,EAAA;QAEA,6BAAA;QAAA,cAAA,IAAA,EAAA;QAEA,wBAAA;QAAA,cAAA,IAAA,EAAA;QAEA,kHAAA;QAAA,cAAA,IAAA,EAAA;QAKE,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,UAAA,GAAa;QAGb,IAAA,CAAA,OAAA,GAAU,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM;QACjC,IAAA,CAAK,SAAA,GAAY,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,KAAA,CAAM;QACvC,IAAA,CAAA,KAAA,GAAQ,IAAA,CAAK,MAAA,CAAO,IAAA;QAMpB,IAAA,CAAA,aAAA,GAAgB,IAAc,UAAU,GAAA;QAExC,IAAA,CAAA,iBAAA,GAAoB,IAAc,UAAU,KAAA;QAE5C,IAAA,CAAA,aAAA,GAAgB,CAAC,UAAwB;YAE5C,IAAI,MAAM,iBAAiB,OAAO,IAAI,KAAK,EAAE;YAC7C,IAAI,aAAa,UAAU,GAAA;YAG3B,IAAI,aAAa,GAAG,cAAc,IAAI,KAAK,EAAA;YAC3C,IAAI,MAAM,GAAG,OAAO,IAAI,KAAK,EAAA;YAC7B,IAAI,UAAU,KAAK,GAAA,CAAI,MAAM,UAAU;YACvC,IAAI,IAAI,KAAK,EAAA,GAAK,UAAU,SAAS;gBACnC,IAAI,MAAM,YAAY;oBACpB,OAAO,IAAI,KAAK,EAAA;gBAAA,OACX;oBACL,cAAc,IAAI,KAAK,EAAA;gBACzB;YACF;YACA,eAAe,GAAA,GAAM,MAAM;YAC3B,MAAM,MAAA,CAAO;QAAA;QAGV,IAAA,CAAA,iBAAA,GAAoB,CAAC,UAAwB;YAEhD,IAAI,QAAQ,iBAAiB,OAAO,IAAI,KAAK,EAAE;YAC/C,IAAI,eAAe,UAAU,KAAA;YAG7B,IAAI,eAAe,GAAG,gBAAgB,IAAI,KAAK,EAAA;YAC/C,IAAI,QAAQ,GAAG,SAAS,IAAI,KAAK,EAAA;YACjC,IAAI,YAAY,KAAK,GAAA,CAAI,QAAQ,YAAY;YAC7C,IAAI,IAAI,KAAK,EAAA,GAAK,YAAY,WAAW;gBACvC,IAAI,QAAQ,cAAc;oBACxB,SAAS,IAAI,KAAK,EAAA;gBAAA,OACb;oBACL,gBAAgB,IAAI,KAAK,EAAA;gBAC3B;YACF;YACA,eAAe,KAAA,GAAQ,QAAQ;YAC/B,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,WAAA,GAAc,IAAc,MAAM,MAAA,CAAO,QAAA,CAAS,UAAA,CAAW,MAAM,MAAM;QAEzE,IAAA,CAAA,iBAAA,GAAoB,CAACA,gBAAkC;YAC1DA,YAAW,gBAAA,CAAiB,WAAW,SAAS;YAChD,IAAA,CAAK,oBAAA,GAAuBA;QAAA;QAG9B,IAAA,CAAK,qBAAA,GAAwB,MAAY;YAClC,IAAA,CAAA,oBAAA,CAAqB,mBAAA,CAAoB,WAAW,SAAS;YAClE,IAAA,CAAK,oBAAA,GAAuB;QAAA;QAG9B,IAAA,CAAK,SAAA,GAAY,MAAY;YACrB,MAAA,OAAA,CAAQ,IAAA,CAAK,MAAM,MAAM;YAC/B,MAAM,SAAA,CAAU,IAAA,CAAK,MAAM,MAAA,CAAO,QAAQ;YACpC,MAAA,KAAA,GAAQ,MAAM,MAAA,CAAO,IAAA;QAAA;QAG7B,IAAA,CAAK,KAAA,GAAQ,MAAY;YACjB,MAAA,MAAA,CAAO,IAAA,CAAK,MAAM,OAAO;YAC/B,MAAM,MAAA,CAAO,QAAA,CAAS,IAAA,CAAK,MAAM,SAAS;YACpC,MAAA,MAAA,CAAO,IAAA,GAAO,MAAM,KAAA;YAC1B,MAAM,MAAA,CAAO,sBAAA;YAGb,MAAM,aAAA,CAAc,WAAW;YAE/B,MAAM,MAAA,CAAO;YAEb,QAAQ,MAAM,IAAA;QAAA;QAIhB,IAAA,CAAK,MAAA,GAAA,CAAU,MAAoB;YAC3B,MAAA,SAAS,2MAAI,UAAA;YACnB,MAAM,KAAK,2MAAI,UAAA,CAAQ,GAAG,GAAG,CAAC;YAG9B,MAAM,OAAO,2MAAI,aAAA,GAAa,kBAAA,CAAmB,OAAO,EAAA,EAAI,EAAE;YAC9D,MAAM,cAAc,KAAK,KAAA,CAAM,EAAE,MAAA,CAAO;YAElC,MAAA,eAAe,2MAAI,UAAA;YACnB,MAAA,iBAAiB,2MAAI,aAAA;YAErB,MAAA,QAAQ,IAAI,KAAK,EAAA;YAEvB,OAAO,SAAS,SAAkB;gBAC1B,MAAA,WAAW,MAAM,MAAA,CAAO,QAAA;gBAGzB,KAAA,kBAAA,CAAmB,OAAO,EAAA,EAAI,EAAE;gBACzB,YAAA,IAAA,CAAK,IAAI,EAAE,MAAA,CAAO;gBAE9B,OAAO,IAAA,CAAK,QAAQ,EAAE,GAAA,CAAI,MAAM,MAAM;gBAGtC,OAAO,eAAA,CAAgB,IAAI;gBAG3B,UAAU,cAAA,CAAe,MAAM;gBAE/B,IAAI,MAAM,UAAA,IAAc,UAAU,MAAM,IAAA,EAAM;oBAC5C,WAAW,sBAAsB;gBACnC;gBAEA,IAAI,MAAM,aAAA,EAAe;oBACb,UAAA,KAAA,IAAS,eAAe,KAAA,GAAQ,MAAM,aAAA;oBACtC,UAAA,GAAA,IAAO,eAAe,GAAA,GAAM,MAAM,aAAA;gBAAA,OACvC;oBACL,UAAU,KAAA,IAAS,eAAe,KAAA;oBAClC,UAAU,GAAA,IAAO,eAAe,GAAA;gBAClC;gBAIA,IAAI,MAAM,MAAM,eAAA;gBAChB,IAAI,MAAM,MAAM,eAAA;gBAEhB,IAAI,SAAS,GAAG,KAAK,SAAS,GAAG,GAAG;oBAC9B,IAAA,MAAM,CAAC,KAAK,EAAA,EAAW,OAAA;yBAAA,IAClB,MAAM,KAAK,EAAA,EAAW,OAAA;oBAE3B,IAAA,MAAM,CAAC,KAAK,EAAA,EAAW,OAAA;yBAAA,IAClB,MAAM,KAAK,EAAA,EAAW,OAAA;oBAE/B,IAAI,OAAO,KAAK;wBACJ,UAAA,KAAA,GAAQ,KAAK,GAAA,CAAI,KAAK,KAAK,GAAA,CAAI,KAAK,UAAU,KAAK,CAAC;oBAAA,OACzD;wBACL,UAAU,KAAA,GACR,UAAU,KAAA,GAAA,CAAS,MAAM,GAAA,IAAO,IAAI,KAAK,GAAA,CAAI,KAAK,UAAU,KAAK,IAAI,KAAK,GAAA,CAAI,KAAK,UAAU,KAAK;oBACtG;gBACF;gBAGU,UAAA,GAAA,GAAM,KAAK,GAAA,CAAI,MAAM,aAAA,EAAe,KAAK,GAAA,CAAI,MAAM,aAAA,EAAe,UAAU,GAAG,CAAC;gBAC1F,UAAU,QAAA,CAAS;gBAIf,IAAA,MAAM,aAAA,KAAkB,MAAM;oBAChC,MAAM,MAAA,CAAO,eAAA,CAAgB,WAAW,MAAM,aAAa;gBAAA,OACtD;oBACC,MAAA,MAAA,CAAO,GAAA,CAAI,SAAS;gBAC5B;gBAIA,IAAK,MAAM,YAAA,IAAgB,qBAAuB,MAAM,MAAA,CAA8B,oBAAA,EAAsB;oBAChG,UAAA,MAAA,GAAS,cAAc,UAAU,MAAM;gBAAA,OAC5C;oBACL,UAAU,MAAA,GAAS,cAAc,UAAU,MAAA,GAAS,KAAK;gBAC3D;gBAEA,OAAO,gBAAA,CAAiB,SAAS;gBAGjC,OAAO,eAAA,CAAgB,WAAW;gBAElC,SAAS,IAAA,CAAK,MAAM,MAAM,EAAE,GAAA,CAAI,MAAM;gBAElC,IAAA,CAAC,MAAM,MAAA,CAAO,gBAAA,EAAkB,MAAM,MAAA,CAAO,YAAA;gBAC3C,MAAA,MAAA,CAAO,MAAA,CAAO,MAAM,MAAM;gBAE5B,IAAA,MAAM,aAAA,KAAkB,MAAM;oBACjB,eAAA,KAAA,IAAS,IAAI,MAAM,aAAA;oBACnB,eAAA,GAAA,IAAO,IAAI,MAAM,aAAA;oBAEtB,UAAA,cAAA,CAAe,IAAI,MAAM,aAAa;gBAAA,OAC3C;oBACU,eAAA,GAAA,CAAI,GAAG,GAAG,CAAC;oBAEhB,UAAA,GAAA,CAAI,GAAG,GAAG,CAAC;gBACvB;gBAGA,IAAI,cAAc;gBACd,IAAA,MAAM,YAAA,IAAgB,mBAAmB;oBAC3C,IAAI,YAAY;oBAChB,IAAI,MAAM,MAAA,mNAAkB,oBAAA,IAAqB,MAAM,MAAA,CAAO,mBAAA,EAAqB;wBAG3E,MAAA,aAAa,OAAO,MAAA;wBACd,YAAA,cAAc,aAAa,KAAK;wBAE5C,MAAM,cAAc,aAAa;wBACjC,MAAM,MAAA,CAAO,QAAA,CAAS,eAAA,CAAgB,gBAAgB,WAAW;wBACjE,MAAM,MAAA,CAAO,iBAAA;oBAAkB,OAAA,IACrB,MAAM,MAAA,CAA8B,oBAAA,EAAsB;wBAEpE,MAAM,cAAc,2MAAI,UAAA,CAAQ,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,CAAC;wBACvC,YAAA,SAAA,CAAU,MAAM,MAAM;wBAElC,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,CAAC;wBAC9F,MAAM,MAAA,CAAO,sBAAA;wBACC,cAAA;wBAEd,MAAM,aAAa,2MAAI,UAAA,CAAQ,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,CAAC;wBACvC,WAAA,SAAA,CAAU,MAAM,MAAM;wBAEjC,MAAM,MAAA,CAAO,QAAA,CAAS,GAAA,CAAI,UAAU,EAAE,GAAA,CAAI,WAAW;wBACrD,MAAM,MAAA,CAAO,iBAAA;wBAEb,YAAY,OAAO,MAAA;oBAAO,OACrB;wBACL,QAAQ,IAAA,CAAK,yFAAyF;wBACtG,MAAM,YAAA,GAAe;oBACvB;oBAGA,IAAI,cAAc,MAAM;wBACtB,IAAI,MAAM,kBAAA,EAAoB;4BAE5B,MAAM,MAAA,CACH,GAAA,CAAI,GAAG,GAAG,CAAA,CAAE,EACZ,kBAAA,CAAmB,MAAM,MAAA,CAAO,MAAM,EACtC,cAAA,CAAe,SAAS,EACxB,GAAA,CAAI,MAAM,MAAA,CAAO,QAAQ;wBAAA,OACvB;4BAEL,KAAK,MAAA,CAAO,IAAA,CAAK,MAAM,MAAA,CAAO,QAAQ;4BACjC,KAAA,SAAA,CAAU,GAAA,CAAI,GAAG,GAAG,CAAA,CAAE,EAAE,kBAAA,CAAmB,MAAM,MAAA,CAAO,MAAM;4BAI/D,IAAA,KAAK,GAAA,CAAI,MAAM,MAAA,CAAO,EAAA,CAAG,GAAA,CAAI,KAAK,SAAS,CAAC,IAAI,YAAY;gCACvD,OAAA,MAAA,CAAO,MAAM,MAAM;4BAAA,OACrB;gCACL,OAAO,6BAAA,CAA8B,MAAM,MAAA,CAAO,EAAA,EAAI,MAAM,MAAM;gCAC7D,KAAA,cAAA,CAAe,QAAQ,MAAM,MAAM;4BAC1C;wBACF;oBACF;gBAAA,OAAA,IACS,MAAM,MAAA,mNAAkB,qBAAA,IAAsB,MAAM,MAAA,CAAO,oBAAA,EAAsB;oBAC1F,cAAc,UAAU;oBAExB,IAAI,aAAa;wBACf,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,KAAK,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,MAAA,CAAO,IAAA,GAAO,KAAK,CAAC;wBAC9F,MAAM,MAAA,CAAO,sBAAA;oBACf;gBACF;gBAEQ,QAAA;gBACY,oBAAA;gBAMpB,IACE,eACA,aAAa,iBAAA,CAAkB,MAAM,MAAA,CAAO,QAAQ,IAAI,OACxD,IAAA,CAAK,IAAI,eAAe,GAAA,CAAI,MAAM,MAAA,CAAO,UAAU,CAAA,IAAK,KACxD;oBAEA,MAAM,aAAA,CAAc,WAAW;oBAElB,aAAA,IAAA,CAAK,MAAM,MAAA,CAAO,QAAQ;oBACxB,eAAA,IAAA,CAAK,MAAM,MAAA,CAAO,UAAU;oBAC7B,cAAA;oBAEP,OAAA;gBACT;gBAEO,OAAA;YAAA;QACT,CAAA;QAIG,IAAA,CAAA,OAAA,GAAU,CAACA,gBAAkC;YAChD,MAAM,UAAA,GAAaA;YAIb,MAAA,UAAA,CAAW,KAAA,CAAM,WAAA,GAAc;YAC/B,MAAA,UAAA,CAAW,gBAAA,CAAiB,eAAe,aAAa;YACxD,MAAA,UAAA,CAAW,gBAAA,CAAiB,eAAe,aAAa;YACxD,MAAA,UAAA,CAAW,gBAAA,CAAiB,iBAAiB,WAAW;YACxD,MAAA,UAAA,CAAW,gBAAA,CAAiB,SAAS,YAAY;QAAA;QAGzD,IAAA,CAAK,OAAA,GAAU,MAAY;;YAEzB,IAAI,MAAM,UAAA,EAAY;gBACd,MAAA,UAAA,CAAW,KAAA,CAAM,WAAA,GAAc;YACvC;YACM,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,eAAe;YAC/C,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,eAAe;YAC/C,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,iBAAiB;YACjD,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,mBAAA,CAAoB,SAAS;YAC/C,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,eAAe;YACnE,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,aAAa;YAC7D,IAAA,MAAM,oBAAA,KAAyB,MAAM;gBACjC,MAAA,oBAAA,CAAqB,mBAAA,CAAoB,WAAW,SAAS;YACrE;QAAA;QAQF,MAAM,QAAQ,IAAA;QAER,MAAA,cAAc;YAAE,MAAM;QAAA;QACtB,MAAA,aAAa;YAAE,MAAM;QAAA;QACrB,MAAA,WAAW;YAAE,MAAM;QAAA;QAEzB,MAAM,QAAQ;YACZ,MAAM,CAAA;YACN,QAAQ;YACR,OAAO;YACP,KAAK;YACL,cAAc;YACd,WAAW;YACX,iBAAiB;YACjB,oBAAoB;QAAA;QAGtB,IAAI,QAAQ,MAAM,IAAA;QAElB,MAAM,MAAM;QAGN,MAAA,YAAY,2MAAI,YAAA;QAChB,MAAA,iBAAiB,2MAAI,YAAA;QAE3B,IAAI,QAAQ;QACN,MAAA,YAAY,2MAAI,UAAA;QAEhB,MAAA,cAAc,2MAAI,UAAA;QAClB,MAAA,YAAY,2MAAI,UAAA;QAChB,MAAA,cAAc,2MAAI,UAAA;QAElB,MAAA,WAAW,2MAAI,UAAA;QACf,MAAA,SAAS,IAAI,iNAAA;QACb,MAAA,WAAW,2MAAI,UAAA;QAEf,MAAA,aAAa,2MAAI,UAAA;QACjB,MAAA,WAAW,2MAAI,UAAA;QACf,MAAA,aAAa,2MAAI,UAAA;QAEjB,MAAA,iBAAiB,2MAAI,UAAA;QACrB,MAAA,QAAQ,2MAAI,UAAA;QAClB,IAAI,oBAAoB;QAExB,MAAM,WAA2B,CAAA,CAAA;QACjC,MAAM,mBAA+C,CAAA;QAErD,SAAS,uBAA+B;YACtC,OAAS,IAAI,KAAK,EAAA,GAAM,KAAK,KAAM,MAAM,eAAA;QAC3C;QAEA,SAAS,eAAuB;YAC9B,OAAO,KAAK,GAAA,CAAI,MAAM,MAAM,SAAS;QACvC;QAEA,SAAS,WAAW,KAAA,EAAqB;YACnC,IAAA,MAAM,YAAA,IAAgB,MAAM,sBAAA,EAAwB;gBACtD,eAAe,KAAA,IAAS;YAAA,OACnB;gBACL,eAAe,KAAA,IAAS;YAC1B;QACF;QAEA,SAAS,SAAS,KAAA,EAAqB;YACjC,IAAA,MAAM,YAAA,IAAgB,MAAM,oBAAA,EAAsB;gBACpD,eAAe,GAAA,IAAO;YAAA,OACjB;gBACL,eAAe,GAAA,IAAO;YACxB;QACF;QAEA,MAAM,UAAA,CAAW,MAAM;YACf,MAAA,IAAI,2MAAI,UAAA;YAEP,OAAA,SAASC,SAAQ,QAAA,EAAkB,YAAA,EAAuB;gBAC7D,EAAA,mBAAA,CAAoB,cAAc,CAAC;gBACnC,EAAA,cAAA,CAAe,CAAC,QAAQ;gBAE1B,UAAU,GAAA,CAAI,CAAC;YAAA;QACjB,CAAA;QAGF,MAAM,QAAA,CAAS,MAAM;YACb,MAAA,IAAI,2MAAI,UAAA;YAEP,OAAA,SAASC,OAAM,QAAA,EAAkB,YAAA,EAAuB;gBACzD,IAAA,MAAM,kBAAA,KAAuB,MAAM;oBACnC,EAAA,mBAAA,CAAoB,cAAc,CAAC;gBAAA,OAChC;oBACH,EAAA,mBAAA,CAAoB,cAAc,CAAC;oBACrC,EAAE,YAAA,CAAa,MAAM,MAAA,CAAO,EAAA,EAAI,CAAC;gBACnC;gBAEA,EAAE,cAAA,CAAe,QAAQ;gBAEzB,UAAU,GAAA,CAAI,CAAC;YAAA;QACjB,CAAA;QAIF,MAAM,MAAA,CAAO,MAAM;YACX,MAAA,SAAS,2MAAI,UAAA;YAEZ,OAAA,SAASC,KAAI,MAAA,EAAgB,MAAA,EAAgB;gBAClD,MAAM,UAAU,MAAM,UAAA;gBAEtB,IAAI,WAAW,MAAM,MAAA,mNAAkB,oBAAA,IAAqB,MAAM,MAAA,CAAO,mBAAA,EAAqB;oBAEtF,MAAA,WAAW,MAAM,MAAA,CAAO,QAAA;oBAC9B,OAAO,IAAA,CAAK,QAAQ,EAAE,GAAA,CAAI,MAAM,MAAM;oBAClC,IAAA,iBAAiB,OAAO,MAAA;oBAGV,kBAAA,KAAK,GAAA,CAAM,MAAM,MAAA,CAAO,GAAA,GAAM,IAAK,KAAK,EAAA,GAAM,GAAK;oBAGrE,QAAS,IAAI,SAAS,iBAAkB,QAAQ,YAAA,EAAc,MAAM,MAAA,CAAO,MAAM;oBACjF,MAAO,IAAI,SAAS,iBAAkB,QAAQ,YAAA,EAAc,MAAM,MAAA,CAAO,MAAM;gBAAA,OAAA,IACtE,WAAW,MAAM,MAAA,mNAAkB,qBAAA,IAAsB,MAAM,MAAA,CAAO,oBAAA,EAAsB;oBAErG,QACG,SAAA,CAAU,MAAM,MAAA,CAAO,KAAA,GAAQ,MAAM,MAAA,CAAO,IAAA,IAAS,MAAM,MAAA,CAAO,IAAA,GAAO,QAAQ,WAAA,EAClF,MAAM,MAAA,CAAO,MAAA;oBAEf,MACG,SAAA,CAAU,MAAM,MAAA,CAAO,GAAA,GAAM,MAAM,MAAA,CAAO,MAAA,IAAW,MAAM,MAAA,CAAO,IAAA,GAAO,QAAQ,YAAA,EAClF,MAAM,MAAA,CAAO,MAAA;gBACf,OACK;oBAEL,QAAQ,IAAA,CAAK,8EAA8E;oBAC3F,MAAM,SAAA,GAAY;gBACpB;YAAA;QACF,CAAA;QAGF,SAAS,SAAS,QAAA,EAAkB;YAE/B,IAAA,MAAM,MAAA,mNAAkB,oBAAA,IAAqB,MAAM,MAAA,CAAO,mBAAA,IAC1D,MAAM,MAAA,mNAAkB,qBAAA,IAAsB,MAAM,MAAA,CAAO,oBAAA,EAC5D;gBACQ,QAAA;YAAA,OACH;gBACL,QAAQ,IAAA,CAAK,qFAAqF;gBAClG,MAAM,UAAA,GAAa;YACrB;QACF;QAEA,SAAS,SAAS,UAAA,EAAoB;YACpC,SAAS,QAAQ,UAAU;QAC7B;QAEA,SAAS,QAAQ,UAAA,EAAoB;YACnC,SAAS,QAAQ,UAAU;QAC7B;QAEA,SAAS,sBAAsB,KAAA,EAAyB;YACtD,IAAI,CAAC,MAAM,YAAA,IAAgB,CAAC,MAAM,UAAA,EAAY;gBAC5C;YACF;YAEoB,oBAAA;YAEd,MAAA,OAAO,MAAM,UAAA,CAAW,qBAAA,CAAsB;YAC9C,MAAA,IAAI,MAAM,OAAA,GAAU,KAAK,IAAA;YACzB,MAAA,IAAI,MAAM,OAAA,GAAU,KAAK,GAAA;YAC/B,MAAM,IAAI,KAAK,KAAA;YACf,MAAM,IAAI,KAAK,MAAA;YAET,MAAA,CAAA,GAAK,IAAI,IAAK,IAAI;YACxB,MAAM,CAAA,GAAI,CAAA,CAAE,IAAI,CAAA,IAAK,IAAI;YAEzB,eAAe,GAAA,CAAI,MAAM,CAAA,EAAG,MAAM,CAAA,EAAG,CAAC,EAAE,SAAA,CAAU,MAAM,MAAM,EAAE,GAAA,CAAI,MAAM,MAAA,CAAO,QAAQ,EAAE,SAAA;QAC7F;QAEA,SAAS,cAAc,IAAA,EAAsB;YACpC,OAAA,KAAK,GAAA,CAAI,MAAM,WAAA,EAAa,KAAK,GAAA,CAAI,MAAM,WAAA,EAAa,IAAI,CAAC;QACtE;QAMA,SAAS,sBAAsB,KAAA,EAAmB;YAChD,YAAY,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;QAC9C;QAEA,SAAS,qBAAqB,KAAA,EAAmB;YAC/C,sBAAsB,KAAK;YAC3B,WAAW,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;QAC7C;QAEA,SAAS,mBAAmB,KAAA,EAAmB;YAC7C,SAAS,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;QAC3C;QAEA,SAAS,sBAAsB,KAAA,EAAmB;YAChD,UAAU,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;YAC1C,YAAY,UAAA,CAAW,WAAW,WAAW,EAAE,cAAA,CAAe,MAAM,WAAW;YAE/E,MAAM,UAAU,MAAM,UAAA;YAEtB,IAAI,SAAS;gBACX,WAAY,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;gBAC/D,SAAU,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;YAC/D;YACA,YAAY,IAAA,CAAK,SAAS;YAC1B,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,qBAAqB,KAAA,EAAmB;YAC/C,SAAS,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;YAC9B,WAAA,UAAA,CAAW,UAAU,UAAU;YAEtC,IAAA,WAAW,CAAA,GAAI,GAAG;gBACpB,SAAS,cAAc;YAAA,OAAA,IACd,WAAW,CAAA,GAAI,GAAG;gBAC3B,QAAQ,cAAc;YACxB;YAEA,WAAW,IAAA,CAAK,QAAQ;YACxB,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,mBAAmB,KAAA,EAAmB;YAC7C,OAAO,GAAA,CAAI,MAAM,OAAA,EAAS,MAAM,OAAO;YACvC,SAAS,UAAA,CAAW,QAAQ,QAAQ,EAAE,cAAA,CAAe,MAAM,QAAQ;YAC/D,IAAA,SAAS,CAAA,EAAG,SAAS,CAAC;YAC1B,SAAS,IAAA,CAAK,MAAM;YACpB,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,iBAAiB,KAAA,EAAmB;YAC3C,sBAAsB,KAAK;YAEvB,IAAA,MAAM,MAAA,GAAS,GAAG;gBACpB,QAAQ,cAAc;YAAA,OAAA,IACb,MAAM,MAAA,GAAS,GAAG;gBAC3B,SAAS,cAAc;YACzB;YAEA,MAAM,MAAA,CAAO;QACf;QAEA,SAAS,cAAc,KAAA,EAAsB;YAC3C,IAAI,cAAc;YAElB,OAAQ,MAAM,IAAA,EAAM;gBAClB,KAAK,MAAM,IAAA,CAAK,EAAA;oBACV,IAAA,GAAG,MAAM,WAAW;oBACV,cAAA;oBACd;gBAEF,KAAK,MAAM,IAAA,CAAK,MAAA;oBACV,IAAA,GAAG,CAAC,MAAM,WAAW;oBACX,cAAA;oBACd;gBAEF,KAAK,MAAM,IAAA,CAAK,IAAA;oBACV,IAAA,MAAM,WAAA,EAAa,CAAC;oBACV,cAAA;oBACd;gBAEF,KAAK,MAAM,IAAA,CAAK,KAAA;oBACV,IAAA,CAAC,MAAM,WAAA,EAAa,CAAC;oBACX,cAAA;oBACd;YACJ;YAEA,IAAI,aAAa;gBAEf,MAAM,cAAA,CAAe;gBACrB,MAAM,MAAA,CAAO;YACf;QACF;QAEA,SAAS,yBAAyB;YAC5B,IAAA,SAAS,MAAA,IAAU,GAAG;gBACZ,YAAA,GAAA,CAAI,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,EAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAK;YAAA,OAC/C;gBACC,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAC3C,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAErC,YAAA,GAAA,CAAI,GAAG,CAAC;YACtB;QACF;QAEA,SAAS,sBAAsB;YACzB,IAAA,SAAS,MAAA,IAAU,GAAG;gBACf,SAAA,GAAA,CAAI,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,EAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAK;YAAA,OAC5C;gBACC,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAC3C,MAAA,IAAI,MAAA,CAAO,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;gBAExC,SAAA,GAAA,CAAI,GAAG,CAAC;YACnB;QACF;QAEA,SAAS,wBAAwB;YAC/B,MAAM,KAAK,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;YAC3C,MAAM,KAAK,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,GAAQ,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA;YAC3C,MAAM,WAAW,KAAK,IAAA,CAAK,KAAK,KAAK,KAAK,EAAE;YAEjC,WAAA,GAAA,CAAI,GAAG,QAAQ;QAC5B;QAEA,SAAS,2BAA2B;YAClC,IAAI,MAAM,UAAA,EAAkC;YAC5C,IAAI,MAAM,SAAA,EAA+B;QAC3C;QAEA,SAAS,8BAA8B;YACrC,IAAI,MAAM,UAAA,EAAkC;YAC5C,IAAI,MAAM,YAAA,EAAqC;QACjD;QAEA,SAAS,sBAAsB,KAAA,EAAqB;YAC9C,IAAA,SAAS,MAAA,IAAU,GAAG;gBACxB,UAAU,GAAA,CAAI,MAAM,KAAA,EAAO,MAAM,KAAK;YAAA,OACjC;gBACC,MAAA,WAAW,yBAAyB,KAAK;gBAC/C,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBACxC,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBAC9B,UAAA,GAAA,CAAI,GAAG,CAAC;YACpB;YAEA,YAAY,UAAA,CAAW,WAAW,WAAW,EAAE,cAAA,CAAe,MAAM,WAAW;YAE/E,MAAM,UAAU,MAAM,UAAA;YAEtB,IAAI,SAAS;gBACX,WAAY,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;gBAC/D,SAAU,IAAI,KAAK,EAAA,GAAK,YAAY,CAAA,GAAK,QAAQ,YAAY;YAC/D;YACA,YAAY,IAAA,CAAK,SAAS;QAC5B;QAEA,SAAS,mBAAmB,KAAA,EAAqB;YAC3C,IAAA,SAAS,MAAA,IAAU,GAAG;gBACxB,OAAO,GAAA,CAAI,MAAM,KAAA,EAAO,MAAM,KAAK;YAAA,OAC9B;gBACC,MAAA,WAAW,yBAAyB,KAAK;gBAC/C,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBACxC,MAAM,IAAI,MAAA,CAAO,MAAM,KAAA,GAAQ,SAAS,CAAA;gBACjC,OAAA,GAAA,CAAI,GAAG,CAAC;YACjB;YAEA,SAAS,UAAA,CAAW,QAAQ,QAAQ,EAAE,cAAA,CAAe,MAAM,QAAQ;YAC/D,IAAA,SAAS,CAAA,EAAG,SAAS,CAAC;YAC1B,SAAS,IAAA,CAAK,MAAM;QACtB;QAEA,SAAS,qBAAqB,KAAA,EAAqB;YAC3C,MAAA,WAAW,yBAAyB,KAAK;YACzC,MAAA,KAAK,MAAM,KAAA,GAAQ,SAAS,CAAA;YAC5B,MAAA,KAAK,MAAM,KAAA,GAAQ,SAAS,CAAA;YAClC,MAAM,WAAW,KAAK,IAAA,CAAK,KAAK,KAAK,KAAK,EAAE;YAEnC,SAAA,GAAA,CAAI,GAAG,QAAQ;YACb,WAAA,GAAA,CAAI,GAAG,KAAK,GAAA,CAAI,SAAS,CAAA,GAAI,WAAW,CAAA,EAAG,MAAM,SAAS,CAAC;YACtE,SAAS,WAAW,CAAC;YACrB,WAAW,IAAA,CAAK,QAAQ;QAC1B;QAEA,SAAS,wBAAwB,KAAA,EAAqB;YACpD,IAAI,MAAM,UAAA,EAAY,qBAAqB,KAAK;YAChD,IAAI,MAAM,SAAA,EAAW,mBAAmB,KAAK;QAC/C;QAEA,SAAS,2BAA2B,KAAA,EAAqB;YACvD,IAAI,MAAM,UAAA,EAAY,qBAAqB,KAAK;YAChD,IAAI,MAAM,YAAA,EAAc,sBAAsB,KAAK;QACrD;QAMA,SAAS,cAAc,KAAA,EAAqB;;YAC1C,IAAI,MAAM,OAAA,KAAY,OAAO;YAEzB,IAAA,SAAS,MAAA,KAAW,GAAG;gBACzB,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,gBAAA,CAAiB,eAAe;gBAChE,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,gBAAA,CAAiB,aAAa;YAChE;YAEA,WAAW,KAAK;YAEZ,IAAA,MAAM,WAAA,KAAgB,SAAS;gBACjC,aAAa,KAAK;YAAA,OACb;gBACL,YAAY,KAAK;YACnB;QACF;QAEA,SAAS,cAAc,KAAA,EAAqB;YAC1C,IAAI,MAAM,OAAA,KAAY,OAAO;YAEzB,IAAA,MAAM,WAAA,KAAgB,SAAS;gBACjC,YAAY,KAAK;YAAA,OACZ;gBACL,YAAY,KAAK;YACnB;QACF;QAEA,SAAS,YAAY,KAAA,EAAqB;;YACxC,cAAc,KAAK;YAEf,IAAA,SAAS,MAAA,KAAW,GAAG;gBACnB,CAAA,KAAA,MAAA,UAAA,KAAA,OAAA,KAAA,IAAA,GAAY,qBAAA,CAAsB,MAAM,SAAA;gBAE9C,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,eAAe;gBACnE,CAAA,KAAA,MAAM,UAAA,KAAN,OAAA,KAAA,IAAA,GAAkB,aAAA,CAAc,mBAAA,CAAoB,aAAa;YACnE;YAGA,MAAM,aAAA,CAAc,QAAQ;YAE5B,QAAQ,MAAM,IAAA;QAChB;QAEA,SAAS,YAAY,KAAA,EAAmB;YAClC,IAAA;YAEJ,OAAQ,MAAM,MAAA,EAAQ;gBACpB,KAAK;oBACH,cAAc,MAAM,YAAA,CAAa,IAAA;oBACjC;gBAEF,KAAK;oBACH,cAAc,MAAM,YAAA,CAAa,MAAA;oBACjC;gBAEF,KAAK;oBACH,cAAc,MAAM,YAAA,CAAa,KAAA;oBACjC;gBAEF;oBACgB,cAAA,CAAA;YAClB;YAEA,OAAQ,aAAa;gBACnB,KAAK,+MAAA,CAAM,KAAA;oBACT,IAAI,MAAM,UAAA,KAAe,OAAO;oBAChC,qBAAqB,KAAK;oBAC1B,QAAQ,MAAM,KAAA;oBACd;gBAEF,4MAAK,QAAA,CAAM,MAAA;oBACT,IAAI,MAAM,OAAA,IAAW,MAAM,OAAA,IAAW,MAAM,QAAA,EAAU;wBACpD,IAAI,MAAM,SAAA,KAAc,OAAO;wBAC/B,mBAAmB,KAAK;wBACxB,QAAQ,MAAM,GAAA;oBAAA,OACT;wBACL,IAAI,MAAM,YAAA,KAAiB,OAAO;wBAClC,sBAAsB,KAAK;wBAC3B,QAAQ,MAAM,MAAA;oBAChB;oBACA;gBAEF,4MAAK,QAAA,CAAM,GAAA;oBACT,IAAI,MAAM,OAAA,IAAW,MAAM,OAAA,IAAW,MAAM,QAAA,EAAU;wBACpD,IAAI,MAAM,YAAA,KAAiB,OAAO;wBAClC,sBAAsB,KAAK;wBAC3B,QAAQ,MAAM,MAAA;oBAAA,OACT;wBACL,IAAI,MAAM,SAAA,KAAc,OAAO;wBAC/B,mBAAmB,KAAK;wBACxB,QAAQ,MAAM,GAAA;oBAChB;oBACA;gBAEF;oBACE,QAAQ,MAAM,IAAA;YAClB;YAEI,IAAA,UAAU,MAAM,IAAA,EAAM;gBAExB,MAAM,aAAA,CAAc,UAAU;YAChC;QACF;QAEA,SAAS,YAAY,KAAA,EAAmB;YACtC,IAAI,MAAM,OAAA,KAAY,OAAO;YAE7B,OAAQ,OAAO;gBACb,KAAK,MAAM,MAAA;oBACT,IAAI,MAAM,YAAA,KAAiB,OAAO;oBAClC,sBAAsB,KAAK;oBAC3B;gBAEF,KAAK,MAAM,KAAA;oBACT,IAAI,MAAM,UAAA,KAAe,OAAO;oBAChC,qBAAqB,KAAK;oBAC1B;gBAEF,KAAK,MAAM,GAAA;oBACT,IAAI,MAAM,SAAA,KAAc,OAAO;oBAC/B,mBAAmB,KAAK;oBACxB;YACJ;QACF;QAEA,SAAS,aAAa,KAAA,EAAmB;YACnC,IAAA,MAAM,OAAA,KAAY,SAAS,MAAM,UAAA,KAAe,SAAU,UAAU,MAAM,IAAA,IAAQ,UAAU,MAAM,MAAA,EAAS;gBAC7G;YACF;YAEA,MAAM,cAAA,CAAe;YAGrB,MAAM,aAAA,CAAc,UAAU;YAE9B,iBAAiB,KAAK;YAGtB,MAAM,aAAA,CAAc,QAAQ;QAC9B;QAEA,SAAS,UAAU,KAAA,EAAsB;YACvC,IAAI,MAAM,OAAA,KAAY,SAAS,MAAM,SAAA,KAAc,OAAO;YAC1D,cAAc,KAAK;QACrB;QAEA,SAAS,aAAa,KAAA,EAAqB;YACzC,aAAa,KAAK;YAElB,OAAQ,SAAS,MAAA,EAAQ;gBACvB,KAAK;oBACK,OAAA,MAAM,OAAA,CAAQ,GAAA,EAAK;wBACzB,4MAAK,QAAA,CAAM,MAAA;4BACT,IAAI,MAAM,YAAA,KAAiB,OAAO;4BACX;4BACvB,QAAQ,MAAM,YAAA;4BACd;wBAEF,4MAAK,QAAA,CAAM,GAAA;4BACT,IAAI,MAAM,SAAA,KAAc,OAAO;4BACX;4BACpB,QAAQ,MAAM,SAAA;4BACd;wBAEF;4BACE,QAAQ,MAAM,IAAA;oBAClB;oBAEA;gBAEF,KAAK;oBACK,OAAA,MAAM,OAAA,CAAQ,GAAA,EAAK;wBACzB,4MAAK,QAAA,CAAM,SAAA;4BACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,SAAA,KAAc,OAAO;4BACpC;4BACzB,QAAQ,MAAM,eAAA;4BACd;wBAEF,4MAAK,QAAA,CAAM,YAAA;4BACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,YAAA,KAAiB,OAAO;4BACpC;4BAC5B,QAAQ,MAAM,kBAAA;4BACd;wBAEF;4BACE,QAAQ,MAAM,IAAA;oBAClB;oBAEA;gBAEF;oBACE,QAAQ,MAAM,IAAA;YAClB;YAEI,IAAA,UAAU,MAAM,IAAA,EAAM;gBAExB,MAAM,aAAA,CAAc,UAAU;YAChC;QACF;QAEA,SAAS,YAAY,KAAA,EAAqB;YACxC,aAAa,KAAK;YAElB,OAAQ,OAAO;gBACb,KAAK,MAAM,YAAA;oBACT,IAAI,MAAM,YAAA,KAAiB,OAAO;oBAClC,sBAAsB,KAAK;oBAC3B,MAAM,MAAA,CAAO;oBACb;gBAEF,KAAK,MAAM,SAAA;oBACT,IAAI,MAAM,SAAA,KAAc,OAAO;oBAC/B,mBAAmB,KAAK;oBACxB,MAAM,MAAA,CAAO;oBACb;gBAEF,KAAK,MAAM,eAAA;oBACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,SAAA,KAAc,OAAO;oBAC7D,wBAAwB,KAAK;oBAC7B,MAAM,MAAA,CAAO;oBACb;gBAEF,KAAK,MAAM,kBAAA;oBACT,IAAI,MAAM,UAAA,KAAe,SAAS,MAAM,YAAA,KAAiB,OAAO;oBAChE,2BAA2B,KAAK;oBAChC,MAAM,MAAA,CAAO;oBACb;gBAEF;oBACE,QAAQ,MAAM,IAAA;YAClB;QACF;QAEA,SAAS,cAAc,KAAA,EAAc;YACnC,IAAI,MAAM,OAAA,KAAY,OAAO;YAC7B,MAAM,cAAA,CAAe;QACvB;QAEA,SAAS,WAAW,KAAA,EAAqB;YACvC,SAAS,IAAA,CAAK,KAAK;QACrB;QAEA,SAAS,cAAc,KAAA,EAAqB;YACnC,OAAA,gBAAA,CAAiB,MAAM,SAAS,CAAA;YAEvC,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,MAAA,EAAQ,IAAK;gBACxC,IAAI,QAAA,CAAS,CAAC,CAAA,CAAE,SAAA,IAAa,MAAM,SAAA,EAAW;oBACnC,SAAA,MAAA,CAAO,GAAG,CAAC;oBACpB;gBACF;YACF;QACF;QAEA,SAAS,aAAa,KAAA,EAAqB;YACrC,IAAA,WAAW,gBAAA,CAAiB,MAAM,SAAS,CAAA;YAE/C,IAAI,aAAa,KAAA,GAAW;gBAC1B,WAAW,2MAAI,UAAA;gBACE,gBAAA,CAAA,MAAM,SAAS,CAAA,GAAI;YACtC;YAEA,SAAS,GAAA,CAAI,MAAM,KAAA,EAAO,MAAM,KAAK;QACvC;QAEA,SAAS,yBAAyB,KAAA,EAAqB;YAC/C,MAAA,UAAU,MAAM,SAAA,KAAc,QAAA,CAAS,CAAC,CAAA,CAAE,SAAA,GAAY,QAAA,CAAS,CAAC,CAAA,GAAI,QAAA,CAAS,CAAC,CAAA;YAC7E,OAAA,gBAAA,CAAiB,QAAQ,SAAS,CAAA;QAC3C;QAIA,IAAA,CAAK,OAAA,GAAU,CAAC,aAAa,aAAA,CAAA,KAAmB;YAC9C,QAAQ,UAAU;YAClB,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,QAAA,GAAW,CAAC,aAAa,aAAA,CAAA,KAAmB;YAC/C,SAAS,UAAU;YACnB,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,QAAA,GAAW,MAAM;YACb,OAAA;QAAA;QAGJ,IAAA,CAAA,QAAA,GAAW,CAAC,aAAa;YAC5B,SAAS,QAAQ;YACjB,MAAM,MAAA,CAAO;QAAA;QAGf,IAAA,CAAK,YAAA,GAAe,MAAM;YACxB,OAAO,aAAa;QAAA;QAItB,IAAI,eAAe,KAAA,GAAW,IAAA,CAAK,OAAA,CAAQ,UAAU;QAErD,IAAA,CAAK,MAAA,CAAO;IACd;AACF;AAUA,MAAM,oBAAoB,cAAc;IACtC,YAAY,MAAA,EAAgD,UAAA,CAA0B;QACpF,KAAA,CAAM,QAAQ,UAAU;QAExB,IAAA,CAAK,kBAAA,GAAqB;QAErB,IAAA,CAAA,YAAA,CAAa,IAAA,0MAAO,QAAA,CAAM,GAAA;QAC1B,IAAA,CAAA,YAAA,CAAa,KAAA,0MAAQ,QAAA,CAAM,MAAA;QAE3B,IAAA,CAAA,OAAA,CAAQ,GAAA,0MAAM,QAAA,CAAM,GAAA;QACpB,IAAA,CAAA,OAAA,CAAQ,GAAA,0MAAM,QAAA,CAAM,YAAA;IAC3B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1851, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1857, "column": 0}, "map": {"version": 3, "file": "LineSegmentsGeometry.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/lines/LineSegmentsGeometry.js"], "sourcesContent": ["import {\n  Box3,\n  Float32<PERSON><PERSON>er<PERSON>ttribute,\n  InstancedBufferGeometry,\n  InstancedInterleavedBuffer,\n  InterleavedBufferAttribute,\n  Sphere,\n  Vector3,\n  WireframeGeometry,\n} from 'three'\n\nconst _box = /* @__PURE__ */ new Box3()\nconst _vector = /* @__PURE__ */ new Vector3()\n\nclass LineSegmentsGeometry extends InstancedBufferGeometry {\n  constructor() {\n    super()\n\n    this.isLineSegmentsGeometry = true\n\n    this.type = 'LineSegmentsGeometry'\n\n    const positions = [-1, 2, 0, 1, 2, 0, -1, 1, 0, 1, 1, 0, -1, 0, 0, 1, 0, 0, -1, -1, 0, 1, -1, 0]\n    const uvs = [-1, 2, 1, 2, -1, 1, 1, 1, -1, -1, 1, -1, -1, -2, 1, -2]\n    const index = [0, 2, 1, 2, 3, 1, 2, 4, 3, 4, 5, 3, 4, 6, 5, 6, 7, 5]\n\n    this.setIndex(index)\n    this.setAttribute('position', new Float32BufferAttribute(positions, 3))\n    this.setAttribute('uv', new Float32BufferAttribute(uvs, 2))\n  }\n\n  applyMatrix4(matrix) {\n    const start = this.attributes.instanceStart\n    const end = this.attributes.instanceEnd\n\n    if (start !== undefined) {\n      start.applyMatrix4(matrix)\n\n      end.applyMatrix4(matrix)\n\n      start.needsUpdate = true\n    }\n\n    if (this.boundingBox !== null) {\n      this.computeBoundingBox()\n    }\n\n    if (this.boundingSphere !== null) {\n      this.computeBoundingSphere()\n    }\n\n    return this\n  }\n\n  setPositions(array) {\n    let lineSegments\n\n    if (array instanceof Float32Array) {\n      lineSegments = array\n    } else if (Array.isArray(array)) {\n      lineSegments = new Float32Array(array)\n    }\n\n    const instanceBuffer = new InstancedInterleavedBuffer(lineSegments, 6, 1) // xyz, xyz\n\n    this.setAttribute('instanceStart', new InterleavedBufferAttribute(instanceBuffer, 3, 0)) // xyz\n    this.setAttribute('instanceEnd', new InterleavedBufferAttribute(instanceBuffer, 3, 3)) // xyz\n\n    //\n\n    this.computeBoundingBox()\n    this.computeBoundingSphere()\n\n    return this\n  }\n\n  setColors(array, itemSize = 3) {\n    let colors\n\n    if (array instanceof Float32Array) {\n      colors = array\n    } else if (Array.isArray(array)) {\n      colors = new Float32Array(array)\n    }\n\n    const instanceColorBuffer = new InstancedInterleavedBuffer(colors, itemSize * 2, 1) // rgb(a), rgb(a)\n\n    this.setAttribute('instanceColorStart', new InterleavedBufferAttribute(instanceColorBuffer, itemSize, 0)) // rgb(a)\n    this.setAttribute('instanceColorEnd', new InterleavedBufferAttribute(instanceColorBuffer, itemSize, itemSize)) // rgb(a)\n\n    return this\n  }\n\n  fromWireframeGeometry(geometry) {\n    this.setPositions(geometry.attributes.position.array)\n\n    return this\n  }\n\n  fromEdgesGeometry(geometry) {\n    this.setPositions(geometry.attributes.position.array)\n\n    return this\n  }\n\n  fromMesh(mesh) {\n    this.fromWireframeGeometry(new WireframeGeometry(mesh.geometry))\n\n    // set colors, maybe\n\n    return this\n  }\n\n  fromLineSegments(lineSegments) {\n    const geometry = lineSegments.geometry\n\n    this.setPositions(geometry.attributes.position.array) // assumes non-indexed\n\n    // set colors, maybe\n\n    return this\n  }\n\n  computeBoundingBox() {\n    if (this.boundingBox === null) {\n      this.boundingBox = new Box3()\n    }\n\n    const start = this.attributes.instanceStart\n    const end = this.attributes.instanceEnd\n\n    if (start !== undefined && end !== undefined) {\n      this.boundingBox.setFromBufferAttribute(start)\n\n      _box.setFromBufferAttribute(end)\n\n      this.boundingBox.union(_box)\n    }\n  }\n\n  computeBoundingSphere() {\n    if (this.boundingSphere === null) {\n      this.boundingSphere = new Sphere()\n    }\n\n    if (this.boundingBox === null) {\n      this.computeBoundingBox()\n    }\n\n    const start = this.attributes.instanceStart\n    const end = this.attributes.instanceEnd\n\n    if (start !== undefined && end !== undefined) {\n      const center = this.boundingSphere.center\n\n      this.boundingBox.getCenter(center)\n\n      let maxRadiusSq = 0\n\n      for (let i = 0, il = start.count; i < il; i++) {\n        _vector.fromBufferAttribute(start, i)\n        maxRadiusSq = Math.max(maxRadiusSq, center.distanceToSquared(_vector))\n\n        _vector.fromBufferAttribute(end, i)\n        maxRadiusSq = Math.max(maxRadiusSq, center.distanceToSquared(_vector))\n      }\n\n      this.boundingSphere.radius = Math.sqrt(maxRadiusSq)\n\n      if (isNaN(this.boundingSphere.radius)) {\n        console.error(\n          'THREE.LineSegmentsGeometry.computeBoundingSphere(): Computed radius is NaN. The instanced position data is likely to have NaN values.',\n          this,\n        )\n      }\n    }\n  }\n\n  toJSON() {\n    // todo\n  }\n\n  applyMatrix(matrix) {\n    console.warn('THREE.LineSegmentsGeometry: applyMatrix() has been renamed to applyMatrix4().')\n\n    return this.applyMatrix4(matrix)\n  }\n}\n\nexport { LineSegmentsGeometry }\n"], "names": [], "mappings": ";;;;;AAWA,MAAM,OAAuB,aAAA,GAAA,2MAAI,OAAA,CAAM;AACvC,MAAM,UAA0B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAE7C,MAAM,oOAA6B,0BAAA,CAAwB;IACzD,aAAc;QACZ,KAAA,CAAO;QAEP,IAAA,CAAK,sBAAA,GAAyB;QAE9B,IAAA,CAAK,IAAA,GAAO;QAEZ,MAAM,YAAY;YAAC,CAAA;YAAI;YAAG;YAAG;YAAG;YAAG;YAAG,CAAA;YAAI;YAAG;YAAG;YAAG;YAAG;YAAG,CAAA;YAAI;YAAG;YAAG;YAAG;YAAG;YAAG,CAAA;YAAI,CAAA;YAAI;YAAG;YAAG,CAAA;YAAI,CAAC;SAAA;QAC/F,MAAM,MAAM;YAAC,CAAA;YAAI;YAAG;YAAG;YAAG,CAAA;YAAI;YAAG;YAAG;YAAG,CAAA;YAAI,CAAA;YAAI;YAAG,CAAA;YAAI,CAAA;YAAI,CAAA;YAAI;YAAG,CAAA,CAAE;SAAA;QACnE,MAAM,QAAQ;YAAC;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG;YAAG,CAAC;SAAA;QAEnE,IAAA,CAAK,QAAA,CAAS,KAAK;QACnB,IAAA,CAAK,YAAA,CAAa,YAAY,2MAAI,yBAAA,CAAuB,WAAW,CAAC,CAAC;QACtE,IAAA,CAAK,YAAA,CAAa,MAAM,2MAAI,yBAAA,CAAuB,KAAK,CAAC,CAAC;IAC3D;IAED,aAAa,MAAA,EAAQ;QACnB,MAAM,QAAQ,IAAA,CAAK,UAAA,CAAW,aAAA;QAC9B,MAAM,MAAM,IAAA,CAAK,UAAA,CAAW,WAAA;QAE5B,IAAI,UAAU,KAAA,GAAW;YACvB,MAAM,YAAA,CAAa,MAAM;YAEzB,IAAI,YAAA,CAAa,MAAM;YAEvB,MAAM,WAAA,GAAc;QACrB;QAED,IAAI,IAAA,CAAK,WAAA,KAAgB,MAAM;YAC7B,IAAA,CAAK,kBAAA,CAAoB;QAC1B;QAED,IAAI,IAAA,CAAK,cAAA,KAAmB,MAAM;YAChC,IAAA,CAAK,qBAAA,CAAuB;QAC7B;QAED,OAAO,IAAA;IACR;IAED,aAAa,KAAA,EAAO;QAClB,IAAI;QAEJ,IAAI,iBAAiB,cAAc;YACjC,eAAe;QAChB,OAAA,IAAU,MAAM,OAAA,CAAQ,KAAK,GAAG;YAC/B,eAAe,IAAI,aAAa,KAAK;QACtC;QAED,MAAM,iBAAiB,2MAAI,6BAAA,CAA2B,cAAc,GAAG,CAAC;QAExE,IAAA,CAAK,YAAA,CAAa,iBAAiB,2MAAI,6BAAA,CAA2B,gBAAgB,GAAG,CAAC,CAAC;QACvF,IAAA,CAAK,YAAA,CAAa,eAAe,2MAAI,6BAAA,CAA2B,gBAAgB,GAAG,CAAC,CAAC;QAIrF,IAAA,CAAK,kBAAA,CAAoB;QACzB,IAAA,CAAK,qBAAA,CAAuB;QAE5B,OAAO,IAAA;IACR;IAED,UAAU,KAAA,EAAO,WAAW,CAAA,EAAG;QAC7B,IAAI;QAEJ,IAAI,iBAAiB,cAAc;YACjC,SAAS;QACV,OAAA,IAAU,MAAM,OAAA,CAAQ,KAAK,GAAG;YAC/B,SAAS,IAAI,aAAa,KAAK;QAChC;QAED,MAAM,sBAAsB,2MAAI,6BAAA,CAA2B,QAAQ,WAAW,GAAG,CAAC;QAElF,IAAA,CAAK,YAAA,CAAa,sBAAsB,2MAAI,6BAAA,CAA2B,qBAAqB,UAAU,CAAC,CAAC;QACxG,IAAA,CAAK,YAAA,CAAa,oBAAoB,2MAAI,6BAAA,CAA2B,qBAAqB,UAAU,QAAQ,CAAC;QAE7G,OAAO,IAAA;IACR;IAED,sBAAsB,QAAA,EAAU;QAC9B,IAAA,CAAK,YAAA,CAAa,SAAS,UAAA,CAAW,QAAA,CAAS,KAAK;QAEpD,OAAO,IAAA;IACR;IAED,kBAAkB,QAAA,EAAU;QAC1B,IAAA,CAAK,YAAA,CAAa,SAAS,UAAA,CAAW,QAAA,CAAS,KAAK;QAEpD,OAAO,IAAA;IACR;IAED,SAAS,IAAA,EAAM;QACb,IAAA,CAAK,qBAAA,CAAsB,2MAAI,oBAAA,CAAkB,KAAK,QAAQ,CAAC;QAI/D,OAAO,IAAA;IACR;IAED,iBAAiB,YAAA,EAAc;QAC7B,MAAM,WAAW,aAAa,QAAA;QAE9B,IAAA,CAAK,YAAA,CAAa,SAAS,UAAA,CAAW,QAAA,CAAS,KAAK;QAIpD,OAAO,IAAA;IACR;IAED,qBAAqB;QACnB,IAAI,IAAA,CAAK,WAAA,KAAgB,MAAM;YAC7B,IAAA,CAAK,WAAA,GAAc,2MAAI,OAAA,CAAM;QAC9B;QAED,MAAM,QAAQ,IAAA,CAAK,UAAA,CAAW,aAAA;QAC9B,MAAM,MAAM,IAAA,CAAK,UAAA,CAAW,WAAA;QAE5B,IAAI,UAAU,KAAA,KAAa,QAAQ,KAAA,GAAW;YAC5C,IAAA,CAAK,WAAA,CAAY,sBAAA,CAAuB,KAAK;YAE7C,KAAK,sBAAA,CAAuB,GAAG;YAE/B,IAAA,CAAK,WAAA,CAAY,KAAA,CAAM,IAAI;QAC5B;IACF;IAED,wBAAwB;QACtB,IAAI,IAAA,CAAK,cAAA,KAAmB,MAAM;YAChC,IAAA,CAAK,cAAA,GAAiB,2MAAI,SAAA,CAAQ;QACnC;QAED,IAAI,IAAA,CAAK,WAAA,KAAgB,MAAM;YAC7B,IAAA,CAAK,kBAAA,CAAoB;QAC1B;QAED,MAAM,QAAQ,IAAA,CAAK,UAAA,CAAW,aAAA;QAC9B,MAAM,MAAM,IAAA,CAAK,UAAA,CAAW,WAAA;QAE5B,IAAI,UAAU,KAAA,KAAa,QAAQ,KAAA,GAAW;YAC5C,MAAM,SAAS,IAAA,CAAK,cAAA,CAAe,MAAA;YAEnC,IAAA,CAAK,WAAA,CAAY,SAAA,CAAU,MAAM;YAEjC,IAAI,cAAc;YAElB,IAAA,IAAS,IAAI,GAAG,KAAK,MAAM,KAAA,EAAO,IAAI,IAAI,IAAK;gBAC7C,QAAQ,mBAAA,CAAoB,OAAO,CAAC;gBACpC,cAAc,KAAK,GAAA,CAAI,aAAa,OAAO,iBAAA,CAAkB,OAAO,CAAC;gBAErE,QAAQ,mBAAA,CAAoB,KAAK,CAAC;gBAClC,cAAc,KAAK,GAAA,CAAI,aAAa,OAAO,iBAAA,CAAkB,OAAO,CAAC;YACtE;YAED,IAAA,CAAK,cAAA,CAAe,MAAA,GAAS,KAAK,IAAA,CAAK,WAAW;YAElD,IAAI,MAAM,IAAA,CAAK,cAAA,CAAe,MAAM,GAAG;gBACrC,QAAQ,KAAA,CACN,yIACA,IAAA;YAEH;QACF;IACF;IAED,SAAS,CAER;IAED,YAAY,MAAA,EAAQ;QAClB,QAAQ,IAAA,CAAK,+EAA+E;QAE5F,OAAO,IAAA,CAAK,YAAA,CAAa,MAAM;IAChC;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2041, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2047, "column": 0}, "map": {"version": 3, "file": "constants.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/_polyfill/constants.ts"], "sourcesContent": ["import { REVISION } from 'three'\n\nexport const version = /* @__PURE__ */ (() => parseInt(REVISION.replace(/\\D+/g, '')))()\n"], "names": [], "mappings": ";;;;;AAEa,MAAA,UAAA,aAAA,GAAA,CAAA,IAAiC,gNAAS,WAAA,CAAS,OAAA,CAAQ,QAAQ,EAAE,CAAC,CAAA,EAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2055, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2061, "column": 0}, "map": {"version": 3, "file": "LineMaterial.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/lines/LineMaterial.js"], "sourcesContent": ["/**\n * parameters = {\n *  color: <hex>,\n *  linewidth: <float>,\n *  dashed: <boolean>,\n *  dashScale: <float>,\n *  dashSize: <float>,\n *  dashOffset: <float>,\n *  gapSize: <float>,\n * }\n */\n\nimport { ShaderMaterial, UniformsLib, UniformsUtils, Vector2 } from 'three'\nimport { version } from '../_polyfill/constants'\n\nclass LineMaterial extends ShaderMaterial {\n  constructor(parameters) {\n    super({\n      type: 'LineMaterial',\n\n      uniforms: UniformsUtils.clone(\n        UniformsUtils.merge([\n          UniformsLib.common,\n          UniformsLib.fog,\n          {\n            worldUnits: { value: 1 },\n            linewidth: { value: 1 },\n            resolution: { value: new Vector2(1, 1) },\n            dashOffset: { value: 0 },\n            dashScale: { value: 1 },\n            dashSize: { value: 1 },\n            gapSize: { value: 1 }, // todo FIX - maybe change to totalSize\n          },\n        ]),\n      ),\n\n      vertexShader: /* glsl */ `\n\t\t\t\t#include <common>\n\t\t\t\t#include <fog_pars_vertex>\n\t\t\t\t#include <logdepthbuf_pars_vertex>\n\t\t\t\t#include <clipping_planes_pars_vertex>\n\n\t\t\t\tuniform float linewidth;\n\t\t\t\tuniform vec2 resolution;\n\n\t\t\t\tattribute vec3 instanceStart;\n\t\t\t\tattribute vec3 instanceEnd;\n\n\t\t\t\t#ifdef USE_COLOR\n\t\t\t\t\t#ifdef USE_LINE_COLOR_ALPHA\n\t\t\t\t\t\tvarying vec4 vLineColor;\n\t\t\t\t\t\tattribute vec4 instanceColorStart;\n\t\t\t\t\t\tattribute vec4 instanceColorEnd;\n\t\t\t\t\t#else\n\t\t\t\t\t\tvarying vec3 vLineColor;\n\t\t\t\t\t\tattribute vec3 instanceColorStart;\n\t\t\t\t\t\tattribute vec3 instanceColorEnd;\n\t\t\t\t\t#endif\n\t\t\t\t#endif\n\n\t\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\t\tvarying vec4 worldPos;\n\t\t\t\t\tvarying vec3 worldStart;\n\t\t\t\t\tvarying vec3 worldEnd;\n\n\t\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\t\tvarying vec2 vUv;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t#else\n\n\t\t\t\t\tvarying vec2 vUv;\n\n\t\t\t\t#endif\n\n\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\tuniform float dashScale;\n\t\t\t\t\tattribute float instanceDistanceStart;\n\t\t\t\t\tattribute float instanceDistanceEnd;\n\t\t\t\t\tvarying float vLineDistance;\n\n\t\t\t\t#endif\n\n\t\t\t\tvoid trimSegment( const in vec4 start, inout vec4 end ) {\n\n\t\t\t\t\t// trim end segment so it terminates between the camera plane and the near plane\n\n\t\t\t\t\t// conservative estimate of the near plane\n\t\t\t\t\tfloat a = projectionMatrix[ 2 ][ 2 ]; // 3nd entry in 3th column\n\t\t\t\t\tfloat b = projectionMatrix[ 3 ][ 2 ]; // 3nd entry in 4th column\n\t\t\t\t\tfloat nearEstimate = - 0.5 * b / a;\n\n\t\t\t\t\tfloat alpha = ( nearEstimate - start.z ) / ( end.z - start.z );\n\n\t\t\t\t\tend.xyz = mix( start.xyz, end.xyz, alpha );\n\n\t\t\t\t}\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\t#ifdef USE_COLOR\n\n\t\t\t\t\t\tvLineColor = ( position.y < 0.5 ) ? instanceColorStart : instanceColorEnd;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\t\tvLineDistance = ( position.y < 0.5 ) ? dashScale * instanceDistanceStart : dashScale * instanceDistanceEnd;\n\t\t\t\t\t\tvUv = uv;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\tfloat aspect = resolution.x / resolution.y;\n\n\t\t\t\t\t// camera space\n\t\t\t\t\tvec4 start = modelViewMatrix * vec4( instanceStart, 1.0 );\n\t\t\t\t\tvec4 end = modelViewMatrix * vec4( instanceEnd, 1.0 );\n\n\t\t\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\t\t\tworldStart = start.xyz;\n\t\t\t\t\t\tworldEnd = end.xyz;\n\n\t\t\t\t\t#else\n\n\t\t\t\t\t\tvUv = uv;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\t// special case for perspective projection, and segments that terminate either in, or behind, the camera plane\n\t\t\t\t\t// clearly the gpu firmware has a way of addressing this issue when projecting into ndc space\n\t\t\t\t\t// but we need to perform ndc-space calculations in the shader, so we must address this issue directly\n\t\t\t\t\t// perhaps there is a more elegant solution -- WestLangley\n\n\t\t\t\t\tbool perspective = ( projectionMatrix[ 2 ][ 3 ] == - 1.0 ); // 4th entry in the 3rd column\n\n\t\t\t\t\tif ( perspective ) {\n\n\t\t\t\t\t\tif ( start.z < 0.0 && end.z >= 0.0 ) {\n\n\t\t\t\t\t\t\ttrimSegment( start, end );\n\n\t\t\t\t\t\t} else if ( end.z < 0.0 && start.z >= 0.0 ) {\n\n\t\t\t\t\t\t\ttrimSegment( end, start );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\t// clip space\n\t\t\t\t\tvec4 clipStart = projectionMatrix * start;\n\t\t\t\t\tvec4 clipEnd = projectionMatrix * end;\n\n\t\t\t\t\t// ndc space\n\t\t\t\t\tvec3 ndcStart = clipStart.xyz / clipStart.w;\n\t\t\t\t\tvec3 ndcEnd = clipEnd.xyz / clipEnd.w;\n\n\t\t\t\t\t// direction\n\t\t\t\t\tvec2 dir = ndcEnd.xy - ndcStart.xy;\n\n\t\t\t\t\t// account for clip-space aspect ratio\n\t\t\t\t\tdir.x *= aspect;\n\t\t\t\t\tdir = normalize( dir );\n\n\t\t\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\t\t\t// get the offset direction as perpendicular to the view vector\n\t\t\t\t\t\tvec3 worldDir = normalize( end.xyz - start.xyz );\n\t\t\t\t\t\tvec3 offset;\n\t\t\t\t\t\tif ( position.y < 0.5 ) {\n\n\t\t\t\t\t\t\toffset = normalize( cross( start.xyz, worldDir ) );\n\n\t\t\t\t\t\t} else {\n\n\t\t\t\t\t\t\toffset = normalize( cross( end.xyz, worldDir ) );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// sign flip\n\t\t\t\t\t\tif ( position.x < 0.0 ) offset *= - 1.0;\n\n\t\t\t\t\t\tfloat forwardOffset = dot( worldDir, vec3( 0.0, 0.0, 1.0 ) );\n\n\t\t\t\t\t\t// don't extend the line if we're rendering dashes because we\n\t\t\t\t\t\t// won't be rendering the endcaps\n\t\t\t\t\t\t#ifndef USE_DASH\n\n\t\t\t\t\t\t\t// extend the line bounds to encompass  endcaps\n\t\t\t\t\t\t\tstart.xyz += - worldDir * linewidth * 0.5;\n\t\t\t\t\t\t\tend.xyz += worldDir * linewidth * 0.5;\n\n\t\t\t\t\t\t\t// shift the position of the quad so it hugs the forward edge of the line\n\t\t\t\t\t\t\toffset.xy -= dir * forwardOffset;\n\t\t\t\t\t\t\toffset.z += 0.5;\n\n\t\t\t\t\t\t#endif\n\n\t\t\t\t\t\t// endcaps\n\t\t\t\t\t\tif ( position.y > 1.0 || position.y < 0.0 ) {\n\n\t\t\t\t\t\t\toffset.xy += dir * 2.0 * forwardOffset;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// adjust for linewidth\n\t\t\t\t\t\toffset *= linewidth * 0.5;\n\n\t\t\t\t\t\t// set the world position\n\t\t\t\t\t\tworldPos = ( position.y < 0.5 ) ? start : end;\n\t\t\t\t\t\tworldPos.xyz += offset;\n\n\t\t\t\t\t\t// project the worldpos\n\t\t\t\t\t\tvec4 clip = projectionMatrix * worldPos;\n\n\t\t\t\t\t\t// shift the depth of the projected points so the line\n\t\t\t\t\t\t// segments overlap neatly\n\t\t\t\t\t\tvec3 clipPose = ( position.y < 0.5 ) ? ndcStart : ndcEnd;\n\t\t\t\t\t\tclip.z = clipPose.z * clip.w;\n\n\t\t\t\t\t#else\n\n\t\t\t\t\t\tvec2 offset = vec2( dir.y, - dir.x );\n\t\t\t\t\t\t// undo aspect ratio adjustment\n\t\t\t\t\t\tdir.x /= aspect;\n\t\t\t\t\t\toffset.x /= aspect;\n\n\t\t\t\t\t\t// sign flip\n\t\t\t\t\t\tif ( position.x < 0.0 ) offset *= - 1.0;\n\n\t\t\t\t\t\t// endcaps\n\t\t\t\t\t\tif ( position.y < 0.0 ) {\n\n\t\t\t\t\t\t\toffset += - dir;\n\n\t\t\t\t\t\t} else if ( position.y > 1.0 ) {\n\n\t\t\t\t\t\t\toffset += dir;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// adjust for linewidth\n\t\t\t\t\t\toffset *= linewidth;\n\n\t\t\t\t\t\t// adjust for clip-space to screen-space conversion // maybe resolution should be based on viewport ...\n\t\t\t\t\t\toffset /= resolution.y;\n\n\t\t\t\t\t\t// select end\n\t\t\t\t\t\tvec4 clip = ( position.y < 0.5 ) ? clipStart : clipEnd;\n\n\t\t\t\t\t\t// back to clip space\n\t\t\t\t\t\toffset *= clip.w;\n\n\t\t\t\t\t\tclip.xy += offset;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\tgl_Position = clip;\n\n\t\t\t\t\tvec4 mvPosition = ( position.y < 0.5 ) ? start : end; // this is an approximation\n\n\t\t\t\t\t#include <logdepthbuf_vertex>\n\t\t\t\t\t#include <clipping_planes_vertex>\n\t\t\t\t\t#include <fog_vertex>\n\n\t\t\t\t}\n\t\t\t`,\n      fragmentShader: /* glsl */ `\n\t\t\t\tuniform vec3 diffuse;\n\t\t\t\tuniform float opacity;\n\t\t\t\tuniform float linewidth;\n\n\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\tuniform float dashOffset;\n\t\t\t\t\tuniform float dashSize;\n\t\t\t\t\tuniform float gapSize;\n\n\t\t\t\t#endif\n\n\t\t\t\tvarying float vLineDistance;\n\n\t\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\t\tvarying vec4 worldPos;\n\t\t\t\t\tvarying vec3 worldStart;\n\t\t\t\t\tvarying vec3 worldEnd;\n\n\t\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\t\tvarying vec2 vUv;\n\n\t\t\t\t\t#endif\n\n\t\t\t\t#else\n\n\t\t\t\t\tvarying vec2 vUv;\n\n\t\t\t\t#endif\n\n\t\t\t\t#include <common>\n\t\t\t\t#include <fog_pars_fragment>\n\t\t\t\t#include <logdepthbuf_pars_fragment>\n\t\t\t\t#include <clipping_planes_pars_fragment>\n\n\t\t\t\t#ifdef USE_COLOR\n\t\t\t\t\t#ifdef USE_LINE_COLOR_ALPHA\n\t\t\t\t\t\tvarying vec4 vLineColor;\n\t\t\t\t\t#else\n\t\t\t\t\t\tvarying vec3 vLineColor;\n\t\t\t\t\t#endif\n\t\t\t\t#endif\n\n\t\t\t\tvec2 closestLineToLine(vec3 p1, vec3 p2, vec3 p3, vec3 p4) {\n\n\t\t\t\t\tfloat mua;\n\t\t\t\t\tfloat mub;\n\n\t\t\t\t\tvec3 p13 = p1 - p3;\n\t\t\t\t\tvec3 p43 = p4 - p3;\n\n\t\t\t\t\tvec3 p21 = p2 - p1;\n\n\t\t\t\t\tfloat d1343 = dot( p13, p43 );\n\t\t\t\t\tfloat d4321 = dot( p43, p21 );\n\t\t\t\t\tfloat d1321 = dot( p13, p21 );\n\t\t\t\t\tfloat d4343 = dot( p43, p43 );\n\t\t\t\t\tfloat d2121 = dot( p21, p21 );\n\n\t\t\t\t\tfloat denom = d2121 * d4343 - d4321 * d4321;\n\n\t\t\t\t\tfloat numer = d1343 * d4321 - d1321 * d4343;\n\n\t\t\t\t\tmua = numer / denom;\n\t\t\t\t\tmua = clamp( mua, 0.0, 1.0 );\n\t\t\t\t\tmub = ( d1343 + d4321 * ( mua ) ) / d4343;\n\t\t\t\t\tmub = clamp( mub, 0.0, 1.0 );\n\n\t\t\t\t\treturn vec2( mua, mub );\n\n\t\t\t\t}\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\t#include <clipping_planes_fragment>\n\n\t\t\t\t\t#ifdef USE_DASH\n\n\t\t\t\t\t\tif ( vUv.y < - 1.0 || vUv.y > 1.0 ) discard; // discard endcaps\n\n\t\t\t\t\t\tif ( mod( vLineDistance + dashOffset, dashSize + gapSize ) > dashSize ) discard; // todo - FIX\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\tfloat alpha = opacity;\n\n\t\t\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\t\t\t// Find the closest points on the view ray and the line segment\n\t\t\t\t\t\tvec3 rayEnd = normalize( worldPos.xyz ) * 1e5;\n\t\t\t\t\t\tvec3 lineDir = worldEnd - worldStart;\n\t\t\t\t\t\tvec2 params = closestLineToLine( worldStart, worldEnd, vec3( 0.0, 0.0, 0.0 ), rayEnd );\n\n\t\t\t\t\t\tvec3 p1 = worldStart + lineDir * params.x;\n\t\t\t\t\t\tvec3 p2 = rayEnd * params.y;\n\t\t\t\t\t\tvec3 delta = p1 - p2;\n\t\t\t\t\t\tfloat len = length( delta );\n\t\t\t\t\t\tfloat norm = len / linewidth;\n\n\t\t\t\t\t\t#ifndef USE_DASH\n\n\t\t\t\t\t\t\t#ifdef USE_ALPHA_TO_COVERAGE\n\n\t\t\t\t\t\t\t\tfloat dnorm = fwidth( norm );\n\t\t\t\t\t\t\t\talpha = 1.0 - smoothstep( 0.5 - dnorm, 0.5 + dnorm, norm );\n\n\t\t\t\t\t\t\t#else\n\n\t\t\t\t\t\t\t\tif ( norm > 0.5 ) {\n\n\t\t\t\t\t\t\t\t\tdiscard;\n\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t#endif\n\n\t\t\t\t\t\t#endif\n\n\t\t\t\t\t#else\n\n\t\t\t\t\t\t#ifdef USE_ALPHA_TO_COVERAGE\n\n\t\t\t\t\t\t\t// artifacts appear on some hardware if a derivative is taken within a conditional\n\t\t\t\t\t\t\tfloat a = vUv.x;\n\t\t\t\t\t\t\tfloat b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;\n\t\t\t\t\t\t\tfloat len2 = a * a + b * b;\n\t\t\t\t\t\t\tfloat dlen = fwidth( len2 );\n\n\t\t\t\t\t\t\tif ( abs( vUv.y ) > 1.0 ) {\n\n\t\t\t\t\t\t\t\talpha = 1.0 - smoothstep( 1.0 - dlen, 1.0 + dlen, len2 );\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t#else\n\n\t\t\t\t\t\t\tif ( abs( vUv.y ) > 1.0 ) {\n\n\t\t\t\t\t\t\t\tfloat a = vUv.x;\n\t\t\t\t\t\t\t\tfloat b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;\n\t\t\t\t\t\t\t\tfloat len2 = a * a + b * b;\n\n\t\t\t\t\t\t\t\tif ( len2 > 1.0 ) discard;\n\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t#endif\n\n\t\t\t\t\t#endif\n\n\t\t\t\t\tvec4 diffuseColor = vec4( diffuse, alpha );\n\t\t\t\t\t#ifdef USE_COLOR\n\t\t\t\t\t\t#ifdef USE_LINE_COLOR_ALPHA\n\t\t\t\t\t\t\tdiffuseColor *= vLineColor;\n\t\t\t\t\t\t#else\n\t\t\t\t\t\t\tdiffuseColor.rgb *= vLineColor;\n\t\t\t\t\t\t#endif\n\t\t\t\t\t#endif\n\n\t\t\t\t\t#include <logdepthbuf_fragment>\n\n\t\t\t\t\tgl_FragColor = diffuseColor;\n\n\t\t\t\t\t#include <tonemapping_fragment>\n\t\t\t\t\t#include <${version >= 154 ? 'colorspace_fragment' : 'encodings_fragment'}>\n\t\t\t\t\t#include <fog_fragment>\n\t\t\t\t\t#include <premultiplied_alpha_fragment>\n\n\t\t\t\t}\n\t\t\t`,\n      clipping: true, // required for clipping support\n    })\n\n    this.isLineMaterial = true\n\n    this.onBeforeCompile = function () {\n      if (this.transparent) {\n        this.defines.USE_LINE_COLOR_ALPHA = '1'\n      } else {\n        delete this.defines.USE_LINE_COLOR_ALPHA\n      }\n    }\n\n    Object.defineProperties(this, {\n      color: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.diffuse.value\n        },\n\n        set: function (value) {\n          this.uniforms.diffuse.value = value\n        },\n      },\n\n      worldUnits: {\n        enumerable: true,\n\n        get: function () {\n          return 'WORLD_UNITS' in this.defines\n        },\n\n        set: function (value) {\n          if (value === true) {\n            this.defines.WORLD_UNITS = ''\n          } else {\n            delete this.defines.WORLD_UNITS\n          }\n        },\n      },\n\n      linewidth: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.linewidth.value\n        },\n\n        set: function (value) {\n          this.uniforms.linewidth.value = value\n        },\n      },\n\n      dashed: {\n        enumerable: true,\n\n        get: function () {\n          return Boolean('USE_DASH' in this.defines)\n        },\n\n        set(value) {\n          if (Boolean(value) !== Boolean('USE_DASH' in this.defines)) {\n            this.needsUpdate = true\n          }\n\n          if (value === true) {\n            this.defines.USE_DASH = ''\n          } else {\n            delete this.defines.USE_DASH\n          }\n        },\n      },\n\n      dashScale: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.dashScale.value\n        },\n\n        set: function (value) {\n          this.uniforms.dashScale.value = value\n        },\n      },\n\n      dashSize: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.dashSize.value\n        },\n\n        set: function (value) {\n          this.uniforms.dashSize.value = value\n        },\n      },\n\n      dashOffset: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.dashOffset.value\n        },\n\n        set: function (value) {\n          this.uniforms.dashOffset.value = value\n        },\n      },\n\n      gapSize: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.gapSize.value\n        },\n\n        set: function (value) {\n          this.uniforms.gapSize.value = value\n        },\n      },\n\n      opacity: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.opacity.value\n        },\n\n        set: function (value) {\n          this.uniforms.opacity.value = value\n        },\n      },\n\n      resolution: {\n        enumerable: true,\n\n        get: function () {\n          return this.uniforms.resolution.value\n        },\n\n        set: function (value) {\n          this.uniforms.resolution.value.copy(value)\n        },\n      },\n\n      alphaToCoverage: {\n        enumerable: true,\n\n        get: function () {\n          return Boolean('USE_ALPHA_TO_COVERAGE' in this.defines)\n        },\n\n        set: function (value) {\n          if (Boolean(value) !== Boolean('USE_ALPHA_TO_COVERAGE' in this.defines)) {\n            this.needsUpdate = true\n          }\n\n          if (value === true) {\n            this.defines.USE_ALPHA_TO_COVERAGE = ''\n            this.extensions.derivatives = true\n          } else {\n            delete this.defines.USE_ALPHA_TO_COVERAGE\n            this.extensions.derivatives = false\n          }\n        },\n      },\n    })\n\n    this.setValues(parameters)\n  }\n}\n\nexport { LineMaterial }\n"], "names": [], "mappings": ";;;;;;;;AAeA,MAAM,4NAAqB,iBAAA,CAAe;IACxC,YAAY,UAAA,CAAY;QACtB,KAAA,CAAM;YACJ,MAAM;YAEN,iNAAU,gBAAA,CAAc,KAAA,wMACtB,gBAAA,CAAc,KAAA,CAAM;yOAClB,cAAA,CAAY,MAAA;yOACZ,cAAA,CAAY,GAAA;gBACZ;oBACE,YAAY;wBAAE,OAAO;oBAAG;oBACxB,WAAW;wBAAE,OAAO;oBAAG;oBACvB,YAAY;wBAAE,OAAO,2MAAI,UAAA,CAAQ,GAAG,CAAC;oBAAG;oBACxC,YAAY;wBAAE,OAAO;oBAAG;oBACxB,WAAW;wBAAE,OAAO;oBAAG;oBACvB,UAAU;wBAAE,OAAO;oBAAG;oBACtB,SAAS;wBAAE,OAAO;oBAAG;gBACtB;aACF;YAGH,cAAA,QAAA,GAAyB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,CAAA;YA6OzB,gBAAA,QAAA,GAA2B,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAAA,mPAuKhB,UAAA,IAAW,MAAM,wBAAwB,qBAAA;;;;;GAAA,CAAA;YAMpD,UAAU;QAChB,CAAK;QAED,IAAA,CAAK,cAAA,GAAiB;QAEtB,IAAA,CAAK,eAAA,GAAkB,WAAY;YACjC,IAAI,IAAA,CAAK,WAAA,EAAa;gBACpB,IAAA,CAAK,OAAA,CAAQ,oBAAA,GAAuB;YAC5C,OAAa;gBACL,OAAO,IAAA,CAAK,OAAA,CAAQ,oBAAA;YACrB;QACF;QAED,OAAO,gBAAA,CAAiB,IAAA,EAAM;YAC5B,OAAO;gBACL,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,IAAA,CAAK,QAAA,CAAS,OAAA,CAAQ,KAAA;gBAC9B;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAA,CAAK,QAAA,CAAS,OAAA,CAAQ,KAAA,GAAQ;gBAC/B;YACF;YAED,YAAY;gBACV,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,iBAAiB,IAAA,CAAK,OAAA;gBAC9B;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAI,UAAU,MAAM;wBAClB,IAAA,CAAK,OAAA,CAAQ,WAAA,GAAc;oBACvC,OAAiB;wBACL,OAAO,IAAA,CAAK,OAAA,CAAQ,WAAA;oBACrB;gBACF;YACF;YAED,WAAW;gBACT,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,IAAA,CAAK,QAAA,CAAS,SAAA,CAAU,KAAA;gBAChC;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAA,CAAK,QAAA,CAAS,SAAA,CAAU,KAAA,GAAQ;gBACjC;YACF;YAED,QAAQ;gBACN,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,QAAQ,cAAc,IAAA,CAAK,OAAO;gBAC1C;gBAED,KAAI,KAAA,EAAO;oBACT,IAAI,QAAQ,KAAK,MAAM,QAAQ,cAAc,IAAA,CAAK,OAAO,GAAG;wBAC1D,IAAA,CAAK,WAAA,GAAc;oBACpB;oBAED,IAAI,UAAU,MAAM;wBAClB,IAAA,CAAK,OAAA,CAAQ,QAAA,GAAW;oBACpC,OAAiB;wBACL,OAAO,IAAA,CAAK,OAAA,CAAQ,QAAA;oBACrB;gBACF;YACF;YAED,WAAW;gBACT,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,IAAA,CAAK,QAAA,CAAS,SAAA,CAAU,KAAA;gBAChC;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAA,CAAK,QAAA,CAAS,SAAA,CAAU,KAAA,GAAQ;gBACjC;YACF;YAED,UAAU;gBACR,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,IAAA,CAAK,QAAA,CAAS,QAAA,CAAS,KAAA;gBAC/B;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAA,CAAK,QAAA,CAAS,QAAA,CAAS,KAAA,GAAQ;gBAChC;YACF;YAED,YAAY;gBACV,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,IAAA,CAAK,QAAA,CAAS,UAAA,CAAW,KAAA;gBACjC;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAA,CAAK,QAAA,CAAS,UAAA,CAAW,KAAA,GAAQ;gBAClC;YACF;YAED,SAAS;gBACP,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,IAAA,CAAK,QAAA,CAAS,OAAA,CAAQ,KAAA;gBAC9B;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAA,CAAK,QAAA,CAAS,OAAA,CAAQ,KAAA,GAAQ;gBAC/B;YACF;YAED,SAAS;gBACP,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,IAAA,CAAK,QAAA,CAAS,OAAA,CAAQ,KAAA;gBAC9B;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAA,CAAK,QAAA,CAAS,OAAA,CAAQ,KAAA,GAAQ;gBAC/B;YACF;YAED,YAAY;gBACV,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,IAAA,CAAK,QAAA,CAAS,UAAA,CAAW,KAAA;gBACjC;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAA,CAAK,QAAA,CAAS,UAAA,CAAW,KAAA,CAAM,IAAA,CAAK,KAAK;gBAC1C;YACF;YAED,iBAAiB;gBACf,YAAY;gBAEZ,KAAK,WAAY;oBACf,OAAO,QAAQ,2BAA2B,IAAA,CAAK,OAAO;gBACvD;gBAED,KAAK,SAAU,KAAA,EAAO;oBACpB,IAAI,QAAQ,KAAK,MAAM,QAAQ,2BAA2B,IAAA,CAAK,OAAO,GAAG;wBACvE,IAAA,CAAK,WAAA,GAAc;oBACpB;oBAED,IAAI,UAAU,MAAM;wBAClB,IAAA,CAAK,OAAA,CAAQ,qBAAA,GAAwB;wBACrC,IAAA,CAAK,UAAA,CAAW,WAAA,GAAc;oBAC1C,OAAiB;wBACL,OAAO,IAAA,CAAK,OAAA,CAAQ,qBAAA;wBACpB,IAAA,CAAK,UAAA,CAAW,WAAA,GAAc;oBAC/B;gBACF;YACF;QACP,CAAK;QAED,IAAA,CAAK,SAAA,CAAU,UAAU;IAC1B;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2646, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2652, "column": 0}, "map": {"version": 3, "file": "uv1.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/_polyfill/uv1.ts"], "sourcesContent": ["import { version } from \"./constants\";\n\n/** uv2 renamed to uv1 in r125\n * \n * https://github.com/mrdoob/three.js/pull/25943\n*/\nexport const UV1 = version >= 125 ? 'uv1' : 'uv2'"], "names": [], "mappings": ";;;;;AAMa,MAAA,uPAAM,UAAA,IAAW,MAAM,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2660, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2666, "column": 0}, "map": {"version": 3, "file": "LineSegments2.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/lines/LineSegments2.js"], "sourcesContent": ["import {\n  Box3,\n  InstancedInterleavedBuffer,\n  InterleavedBuffer<PERSON><PERSON><PERSON><PERSON><PERSON>,\n  Line3,\n  MathUtils,\n  Matrix4,\n  Mesh,\n  Sphere,\n  Vector3,\n  Vector4,\n} from 'three'\nimport { LineSegmentsGeometry } from '../lines/LineSegmentsGeometry'\nimport { LineMaterial } from '../lines/LineMaterial'\nimport { UV1 } from '../_polyfill/uv1'\n\nconst _viewport = /* @__PURE__ */ new Vector4()\n\nconst _start = /* @__PURE__ */ new Vector3()\nconst _end = /* @__PURE__ */ new Vector3()\n\nconst _start4 = /* @__PURE__ */ new Vector4()\nconst _end4 = /* @__PURE__ */ new Vector4()\n\nconst _ssOrigin = /* @__PURE__ */ new Vector4()\nconst _ssOrigin3 = /* @__PURE__ */ new Vector3()\nconst _mvMatrix = /* @__PURE__ */ new Matrix4()\nconst _line = /* @__PURE__ */ new Line3()\nconst _closestPoint = /* @__PURE__ */ new Vector3()\n\nconst _box = /* @__PURE__ */ new Box3()\nconst _sphere = /* @__PURE__ */ new Sphere()\nconst _clipToWorldVector = /* @__PURE__ */ new Vector4()\n\nlet _ray, _lineWidth\n\n// Returns the margin required to expand by in world space given the distance from the camera,\n// line width, resolution, and camera projection\nfunction getWorldSpaceHalfWidth(camera, distance, resolution) {\n  // transform into clip space, adjust the x and y values by the pixel width offset, then\n  // transform back into world space to get world offset. Note clip space is [-1, 1] so full\n  // width does not need to be halved.\n  _clipToWorldVector.set(0, 0, -distance, 1.0).applyMatrix4(camera.projectionMatrix)\n  _clipToWorldVector.multiplyScalar(1.0 / _clipToWorldVector.w)\n  _clipToWorldVector.x = _lineWidth / resolution.width\n  _clipToWorldVector.y = _lineWidth / resolution.height\n  _clipToWorldVector.applyMatrix4(camera.projectionMatrixInverse)\n  _clipToWorldVector.multiplyScalar(1.0 / _clipToWorldVector.w)\n\n  return Math.abs(Math.max(_clipToWorldVector.x, _clipToWorldVector.y))\n}\n\nfunction raycastWorldUnits(lineSegments, intersects) {\n  const matrixWorld = lineSegments.matrixWorld\n  const geometry = lineSegments.geometry\n  const instanceStart = geometry.attributes.instanceStart\n  const instanceEnd = geometry.attributes.instanceEnd\n  const segmentCount = Math.min(geometry.instanceCount, instanceStart.count)\n\n  for (let i = 0, l = segmentCount; i < l; i++) {\n    _line.start.fromBufferAttribute(instanceStart, i)\n    _line.end.fromBufferAttribute(instanceEnd, i)\n\n    _line.applyMatrix4(matrixWorld)\n\n    const pointOnLine = new Vector3()\n    const point = new Vector3()\n\n    _ray.distanceSqToSegment(_line.start, _line.end, point, pointOnLine)\n    const isInside = point.distanceTo(pointOnLine) < _lineWidth * 0.5\n\n    if (isInside) {\n      intersects.push({\n        point,\n        pointOnLine,\n        distance: _ray.origin.distanceTo(point),\n        object: lineSegments,\n        face: null,\n        faceIndex: i,\n        uv: null,\n        [UV1]: null,\n      })\n    }\n  }\n}\n\nfunction raycastScreenSpace(lineSegments, camera, intersects) {\n  const projectionMatrix = camera.projectionMatrix\n  const material = lineSegments.material\n  const resolution = material.resolution\n  const matrixWorld = lineSegments.matrixWorld\n\n  const geometry = lineSegments.geometry\n  const instanceStart = geometry.attributes.instanceStart\n  const instanceEnd = geometry.attributes.instanceEnd\n  const segmentCount = Math.min(geometry.instanceCount, instanceStart.count)\n\n  const near = -camera.near\n\n  //\n\n  // pick a point 1 unit out along the ray to avoid the ray origin\n  // sitting at the camera origin which will cause \"w\" to be 0 when\n  // applying the projection matrix.\n  _ray.at(1, _ssOrigin)\n\n  // ndc space [ - 1.0, 1.0 ]\n  _ssOrigin.w = 1\n  _ssOrigin.applyMatrix4(camera.matrixWorldInverse)\n  _ssOrigin.applyMatrix4(projectionMatrix)\n  _ssOrigin.multiplyScalar(1 / _ssOrigin.w)\n\n  // screen space\n  _ssOrigin.x *= resolution.x / 2\n  _ssOrigin.y *= resolution.y / 2\n  _ssOrigin.z = 0\n\n  _ssOrigin3.copy(_ssOrigin)\n\n  _mvMatrix.multiplyMatrices(camera.matrixWorldInverse, matrixWorld)\n\n  for (let i = 0, l = segmentCount; i < l; i++) {\n    _start4.fromBufferAttribute(instanceStart, i)\n    _end4.fromBufferAttribute(instanceEnd, i)\n\n    _start4.w = 1\n    _end4.w = 1\n\n    // camera space\n    _start4.applyMatrix4(_mvMatrix)\n    _end4.applyMatrix4(_mvMatrix)\n\n    // skip the segment if it's entirely behind the camera\n    const isBehindCameraNear = _start4.z > near && _end4.z > near\n    if (isBehindCameraNear) {\n      continue\n    }\n\n    // trim the segment if it extends behind camera near\n    if (_start4.z > near) {\n      const deltaDist = _start4.z - _end4.z\n      const t = (_start4.z - near) / deltaDist\n      _start4.lerp(_end4, t)\n    } else if (_end4.z > near) {\n      const deltaDist = _end4.z - _start4.z\n      const t = (_end4.z - near) / deltaDist\n      _end4.lerp(_start4, t)\n    }\n\n    // clip space\n    _start4.applyMatrix4(projectionMatrix)\n    _end4.applyMatrix4(projectionMatrix)\n\n    // ndc space [ - 1.0, 1.0 ]\n    _start4.multiplyScalar(1 / _start4.w)\n    _end4.multiplyScalar(1 / _end4.w)\n\n    // screen space\n    _start4.x *= resolution.x / 2\n    _start4.y *= resolution.y / 2\n\n    _end4.x *= resolution.x / 2\n    _end4.y *= resolution.y / 2\n\n    // create 2d segment\n    _line.start.copy(_start4)\n    _line.start.z = 0\n\n    _line.end.copy(_end4)\n    _line.end.z = 0\n\n    // get closest point on ray to segment\n    const param = _line.closestPointToPointParameter(_ssOrigin3, true)\n    _line.at(param, _closestPoint)\n\n    // check if the intersection point is within clip space\n    const zPos = MathUtils.lerp(_start4.z, _end4.z, param)\n    const isInClipSpace = zPos >= -1 && zPos <= 1\n\n    const isInside = _ssOrigin3.distanceTo(_closestPoint) < _lineWidth * 0.5\n\n    if (isInClipSpace && isInside) {\n      _line.start.fromBufferAttribute(instanceStart, i)\n      _line.end.fromBufferAttribute(instanceEnd, i)\n\n      _line.start.applyMatrix4(matrixWorld)\n      _line.end.applyMatrix4(matrixWorld)\n\n      const pointOnLine = new Vector3()\n      const point = new Vector3()\n\n      _ray.distanceSqToSegment(_line.start, _line.end, point, pointOnLine)\n\n      intersects.push({\n        point: point,\n        pointOnLine: pointOnLine,\n        distance: _ray.origin.distanceTo(point),\n        object: lineSegments,\n        face: null,\n        faceIndex: i,\n        uv: null,\n        [UV1]: null,\n      })\n    }\n  }\n}\n\nclass LineSegments2 extends Mesh {\n  constructor(geometry = new LineSegmentsGeometry(), material = new LineMaterial({ color: Math.random() * 0xffffff })) {\n    super(geometry, material)\n\n    this.isLineSegments2 = true\n\n    this.type = 'LineSegments2'\n  }\n\n  // for backwards-compatibility, but could be a method of LineSegmentsGeometry...\n\n  computeLineDistances() {\n    const geometry = this.geometry\n\n    const instanceStart = geometry.attributes.instanceStart\n    const instanceEnd = geometry.attributes.instanceEnd\n    const lineDistances = new Float32Array(2 * instanceStart.count)\n\n    for (let i = 0, j = 0, l = instanceStart.count; i < l; i++, j += 2) {\n      _start.fromBufferAttribute(instanceStart, i)\n      _end.fromBufferAttribute(instanceEnd, i)\n\n      lineDistances[j] = j === 0 ? 0 : lineDistances[j - 1]\n      lineDistances[j + 1] = lineDistances[j] + _start.distanceTo(_end)\n    }\n\n    const instanceDistanceBuffer = new InstancedInterleavedBuffer(lineDistances, 2, 1) // d0, d1\n\n    geometry.setAttribute('instanceDistanceStart', new InterleavedBufferAttribute(instanceDistanceBuffer, 1, 0)) // d0\n    geometry.setAttribute('instanceDistanceEnd', new InterleavedBufferAttribute(instanceDistanceBuffer, 1, 1)) // d1\n\n    return this\n  }\n\n  raycast(raycaster, intersects) {\n    const worldUnits = this.material.worldUnits\n    const camera = raycaster.camera\n\n    if (camera === null && !worldUnits) {\n      console.error(\n        'LineSegments2: \"Raycaster.camera\" needs to be set in order to raycast against LineSegments2 while worldUnits is set to false.',\n      )\n    }\n\n    const threshold = raycaster.params.Line2 !== undefined ? raycaster.params.Line2.threshold || 0 : 0\n\n    _ray = raycaster.ray\n\n    const matrixWorld = this.matrixWorld\n    const geometry = this.geometry\n    const material = this.material\n\n    _lineWidth = material.linewidth + threshold\n\n    // check if we intersect the sphere bounds\n    if (geometry.boundingSphere === null) {\n      geometry.computeBoundingSphere()\n    }\n\n    _sphere.copy(geometry.boundingSphere).applyMatrix4(matrixWorld)\n\n    // increase the sphere bounds by the worst case line screen space width\n    let sphereMargin\n    if (worldUnits) {\n      sphereMargin = _lineWidth * 0.5\n    } else {\n      const distanceToSphere = Math.max(camera.near, _sphere.distanceToPoint(_ray.origin))\n      sphereMargin = getWorldSpaceHalfWidth(camera, distanceToSphere, material.resolution)\n    }\n\n    _sphere.radius += sphereMargin\n\n    if (_ray.intersectsSphere(_sphere) === false) {\n      return\n    }\n\n    // check if we intersect the box bounds\n    if (geometry.boundingBox === null) {\n      geometry.computeBoundingBox()\n    }\n\n    _box.copy(geometry.boundingBox).applyMatrix4(matrixWorld)\n\n    // increase the box bounds by the worst case line width\n    let boxMargin\n    if (worldUnits) {\n      boxMargin = _lineWidth * 0.5\n    } else {\n      const distanceToBox = Math.max(camera.near, _box.distanceToPoint(_ray.origin))\n      boxMargin = getWorldSpaceHalfWidth(camera, distanceToBox, material.resolution)\n    }\n\n    _box.expandByScalar(boxMargin)\n\n    if (_ray.intersectsBox(_box) === false) {\n      return\n    }\n\n    if (worldUnits) {\n      raycastWorldUnits(this, intersects)\n    } else {\n      raycastScreenSpace(this, camera, intersects)\n    }\n  }\n\n  onBeforeRender(renderer) {\n    const uniforms = this.material.uniforms\n\n    if (uniforms && uniforms.resolution) {\n      renderer.getViewport(_viewport)\n      this.material.uniforms.resolution.value.set(_viewport.z, _viewport.w)\n    }\n  }\n}\n\nexport { LineSegments2 }\n"], "names": [], "mappings": ";;;;;;;;;;;AAgBA,MAAM,YAA4B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAE/C,MAAM,SAAyB,aAAA,GAAA,2MAAI,UAAA,CAAS;AAC5C,MAAM,OAAuB,aAAA,GAAA,2MAAI,UAAA,CAAS;AAE1C,MAAM,UAA0B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAC7C,MAAM,QAAwB,aAAA,GAAA,2MAAI,UAAA,CAAS;AAE3C,MAAM,YAA4B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAC/C,MAAM,aAA6B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAChD,MAAM,YAA4B,aAAA,GAAA,2MAAI,UAAA,CAAS;AAC/C,MAAM,QAAwB,aAAA,GAAA,2MAAI,QAAA,CAAO;AACzC,MAAM,gBAAgC,aAAA,GAAA,2MAAI,UAAA,CAAS;AAEnD,MAAM,OAAuB,aAAA,GAAA,2MAAI,OAAA,CAAM;AACvC,MAAM,UAA0B,aAAA,GAAA,2MAAI,SAAA,CAAQ;AAC5C,MAAM,qBAAqC,aAAA,GAAA,2MAAI,UAAA,CAAS;AAExD,IAAI,MAAM;AAIV,SAAS,uBAAuB,MAAA,EAAQ,QAAA,EAAU,UAAA,EAAY;IAI5D,mBAAmB,GAAA,CAAI,GAAG,GAAG,CAAC,UAAU,CAAG,EAAE,YAAA,CAAa,OAAO,gBAAgB;IACjF,mBAAmB,cAAA,CAAe,IAAM,mBAAmB,CAAC;IAC5D,mBAAmB,CAAA,GAAI,aAAa,WAAW,KAAA;IAC/C,mBAAmB,CAAA,GAAI,aAAa,WAAW,MAAA;IAC/C,mBAAmB,YAAA,CAAa,OAAO,uBAAuB;IAC9D,mBAAmB,cAAA,CAAe,IAAM,mBAAmB,CAAC;IAE5D,OAAO,KAAK,GAAA,CAAI,KAAK,GAAA,CAAI,mBAAmB,CAAA,EAAG,mBAAmB,CAAC,CAAC;AACtE;AAEA,SAAS,kBAAkB,YAAA,EAAc,UAAA,EAAY;IACnD,MAAM,cAAc,aAAa,WAAA;IACjC,MAAM,WAAW,aAAa,QAAA;IAC9B,MAAM,gBAAgB,SAAS,UAAA,CAAW,aAAA;IAC1C,MAAM,cAAc,SAAS,UAAA,CAAW,WAAA;IACxC,MAAM,eAAe,KAAK,GAAA,CAAI,SAAS,aAAA,EAAe,cAAc,KAAK;IAEzE,IAAA,IAAS,IAAI,GAAG,IAAI,cAAc,IAAI,GAAG,IAAK;QAC5C,MAAM,KAAA,CAAM,mBAAA,CAAoB,eAAe,CAAC;QAChD,MAAM,GAAA,CAAI,mBAAA,CAAoB,aAAa,CAAC;QAE5C,MAAM,YAAA,CAAa,WAAW;QAE9B,MAAM,cAAc,2MAAI,UAAA,CAAS;QACjC,MAAM,QAAQ,2MAAI,UAAA,CAAS;QAE3B,KAAK,mBAAA,CAAoB,MAAM,KAAA,EAAO,MAAM,GAAA,EAAK,OAAO,WAAW;QACnE,MAAM,WAAW,MAAM,UAAA,CAAW,WAAW,IAAI,aAAa;QAE9D,IAAI,UAAU;YACZ,WAAW,IAAA,CAAK;gBACd;gBACA;gBACA,UAAU,KAAK,MAAA,CAAO,UAAA,CAAW,KAAK;gBACtC,QAAQ;gBACR,MAAM;gBACN,WAAW;gBACX,IAAI;gBACJ,4OAAC,MAAG,CAAA,EAAG;YACf,CAAO;QACF;IACF;AACH;AAEA,SAAS,mBAAmB,YAAA,EAAc,MAAA,EAAQ,UAAA,EAAY;IAC5D,MAAM,mBAAmB,OAAO,gBAAA;IAChC,MAAM,WAAW,aAAa,QAAA;IAC9B,MAAM,aAAa,SAAS,UAAA;IAC5B,MAAM,cAAc,aAAa,WAAA;IAEjC,MAAM,WAAW,aAAa,QAAA;IAC9B,MAAM,gBAAgB,SAAS,UAAA,CAAW,aAAA;IAC1C,MAAM,cAAc,SAAS,UAAA,CAAW,WAAA;IACxC,MAAM,eAAe,KAAK,GAAA,CAAI,SAAS,aAAA,EAAe,cAAc,KAAK;IAEzE,MAAM,OAAO,CAAC,OAAO,IAAA;IAOrB,KAAK,EAAA,CAAG,GAAG,SAAS;IAGpB,UAAU,CAAA,GAAI;IACd,UAAU,YAAA,CAAa,OAAO,kBAAkB;IAChD,UAAU,YAAA,CAAa,gBAAgB;IACvC,UAAU,cAAA,CAAe,IAAI,UAAU,CAAC;IAGxC,UAAU,CAAA,IAAK,WAAW,CAAA,GAAI;IAC9B,UAAU,CAAA,IAAK,WAAW,CAAA,GAAI;IAC9B,UAAU,CAAA,GAAI;IAEd,WAAW,IAAA,CAAK,SAAS;IAEzB,UAAU,gBAAA,CAAiB,OAAO,kBAAA,EAAoB,WAAW;IAEjE,IAAA,IAAS,IAAI,GAAG,IAAI,cAAc,IAAI,GAAG,IAAK;QAC5C,QAAQ,mBAAA,CAAoB,eAAe,CAAC;QAC5C,MAAM,mBAAA,CAAoB,aAAa,CAAC;QAExC,QAAQ,CAAA,GAAI;QACZ,MAAM,CAAA,GAAI;QAGV,QAAQ,YAAA,CAAa,SAAS;QAC9B,MAAM,YAAA,CAAa,SAAS;QAG5B,MAAM,qBAAqB,QAAQ,CAAA,GAAI,QAAQ,MAAM,CAAA,GAAI;QACzD,IAAI,oBAAoB;YACtB;QACD;QAGD,IAAI,QAAQ,CAAA,GAAI,MAAM;YACpB,MAAM,YAAY,QAAQ,CAAA,GAAI,MAAM,CAAA;YACpC,MAAM,IAAA,CAAK,QAAQ,CAAA,GAAI,IAAA,IAAQ;YAC/B,QAAQ,IAAA,CAAK,OAAO,CAAC;QAC3B,OAAA,IAAe,MAAM,CAAA,GAAI,MAAM;YACzB,MAAM,YAAY,MAAM,CAAA,GAAI,QAAQ,CAAA;YACpC,MAAM,IAAA,CAAK,MAAM,CAAA,GAAI,IAAA,IAAQ;YAC7B,MAAM,IAAA,CAAK,SAAS,CAAC;QACtB;QAGD,QAAQ,YAAA,CAAa,gBAAgB;QACrC,MAAM,YAAA,CAAa,gBAAgB;QAGnC,QAAQ,cAAA,CAAe,IAAI,QAAQ,CAAC;QACpC,MAAM,cAAA,CAAe,IAAI,MAAM,CAAC;QAGhC,QAAQ,CAAA,IAAK,WAAW,CAAA,GAAI;QAC5B,QAAQ,CAAA,IAAK,WAAW,CAAA,GAAI;QAE5B,MAAM,CAAA,IAAK,WAAW,CAAA,GAAI;QAC1B,MAAM,CAAA,IAAK,WAAW,CAAA,GAAI;QAG1B,MAAM,KAAA,CAAM,IAAA,CAAK,OAAO;QACxB,MAAM,KAAA,CAAM,CAAA,GAAI;QAEhB,MAAM,GAAA,CAAI,IAAA,CAAK,KAAK;QACpB,MAAM,GAAA,CAAI,CAAA,GAAI;QAGd,MAAM,QAAQ,MAAM,4BAAA,CAA6B,YAAY,IAAI;QACjE,MAAM,EAAA,CAAG,OAAO,aAAa;QAG7B,MAAM,8MAAO,YAAA,CAAU,IAAA,CAAK,QAAQ,CAAA,EAAG,MAAM,CAAA,EAAG,KAAK;QACrD,MAAM,gBAAgB,QAAQ,CAAA,KAAM,QAAQ;QAE5C,MAAM,WAAW,WAAW,UAAA,CAAW,aAAa,IAAI,aAAa;QAErE,IAAI,iBAAiB,UAAU;YAC7B,MAAM,KAAA,CAAM,mBAAA,CAAoB,eAAe,CAAC;YAChD,MAAM,GAAA,CAAI,mBAAA,CAAoB,aAAa,CAAC;YAE5C,MAAM,KAAA,CAAM,YAAA,CAAa,WAAW;YACpC,MAAM,GAAA,CAAI,YAAA,CAAa,WAAW;YAElC,MAAM,cAAc,2MAAI,UAAA,CAAS;YACjC,MAAM,QAAQ,2MAAI,UAAA,CAAS;YAE3B,KAAK,mBAAA,CAAoB,MAAM,KAAA,EAAO,MAAM,GAAA,EAAK,OAAO,WAAW;YAEnE,WAAW,IAAA,CAAK;gBACd;gBACA;gBACA,UAAU,KAAK,MAAA,CAAO,UAAA,CAAW,KAAK;gBACtC,QAAQ;gBACR,MAAM;gBACN,WAAW;gBACX,IAAI;gBACJ,4OAAC,MAAG,CAAA,EAAG;YACf,CAAO;QACF;IACF;AACH;AAEA,MAAM,6NAAsB,OAAA,CAAK;IAC/B,YAAY,WAAW,4PAAI,uBAAA,CAAsB,CAAA,EAAE,WAAW,oPAAI,eAAA,CAAa;QAAE,OAAO,KAAK,MAAA,KAAW;IAAU,CAAA,CAAA,CAAG;QACnH,KAAA,CAAM,UAAU,QAAQ;QAExB,IAAA,CAAK,eAAA,GAAkB;QAEvB,IAAA,CAAK,IAAA,GAAO;IACb;IAAA,gFAAA;IAID,uBAAuB;QACrB,MAAM,WAAW,IAAA,CAAK,QAAA;QAEtB,MAAM,gBAAgB,SAAS,UAAA,CAAW,aAAA;QAC1C,MAAM,cAAc,SAAS,UAAA,CAAW,WAAA;QACxC,MAAM,gBAAgB,IAAI,aAAa,IAAI,cAAc,KAAK;QAE9D,IAAA,IAAS,IAAI,GAAG,IAAI,GAAG,IAAI,cAAc,KAAA,EAAO,IAAI,GAAG,KAAK,KAAK,EAAG;YAClE,OAAO,mBAAA,CAAoB,eAAe,CAAC;YAC3C,KAAK,mBAAA,CAAoB,aAAa,CAAC;YAEvC,aAAA,CAAc,CAAC,CAAA,GAAI,MAAM,IAAI,IAAI,aAAA,CAAc,IAAI,CAAC,CAAA;YACpD,aAAA,CAAc,IAAI,CAAC,CAAA,GAAI,aAAA,CAAc,CAAC,CAAA,GAAI,OAAO,UAAA,CAAW,IAAI;QACjE;QAED,MAAM,yBAAyB,2MAAI,6BAAA,CAA2B,eAAe,GAAG,CAAC;QAEjF,SAAS,YAAA,CAAa,yBAAyB,2MAAI,6BAAA,CAA2B,wBAAwB,GAAG,CAAC,CAAC;QAC3G,SAAS,YAAA,CAAa,uBAAuB,2MAAI,6BAAA,CAA2B,wBAAwB,GAAG,CAAC,CAAC;QAEzG,OAAO,IAAA;IACR;IAED,QAAQ,SAAA,EAAW,UAAA,EAAY;QAC7B,MAAM,aAAa,IAAA,CAAK,QAAA,CAAS,UAAA;QACjC,MAAM,SAAS,UAAU,MAAA;QAEzB,IAAI,WAAW,QAAQ,CAAC,YAAY;YAClC,QAAQ,KAAA,CACN;QAEH;QAED,MAAM,YAAY,UAAU,MAAA,CAAO,KAAA,KAAU,KAAA,IAAY,UAAU,MAAA,CAAO,KAAA,CAAM,SAAA,IAAa,IAAI;QAEjG,OAAO,UAAU,GAAA;QAEjB,MAAM,cAAc,IAAA,CAAK,WAAA;QACzB,MAAM,WAAW,IAAA,CAAK,QAAA;QACtB,MAAM,WAAW,IAAA,CAAK,QAAA;QAEtB,aAAa,SAAS,SAAA,GAAY;QAGlC,IAAI,SAAS,cAAA,KAAmB,MAAM;YACpC,SAAS,qBAAA,CAAuB;QACjC;QAED,QAAQ,IAAA,CAAK,SAAS,cAAc,EAAE,YAAA,CAAa,WAAW;QAG9D,IAAI;QACJ,IAAI,YAAY;YACd,eAAe,aAAa;QAClC,OAAW;YACL,MAAM,mBAAmB,KAAK,GAAA,CAAI,OAAO,IAAA,EAAM,QAAQ,eAAA,CAAgB,KAAK,MAAM,CAAC;YACnF,eAAe,uBAAuB,QAAQ,kBAAkB,SAAS,UAAU;QACpF;QAED,QAAQ,MAAA,IAAU;QAElB,IAAI,KAAK,gBAAA,CAAiB,OAAO,MAAM,OAAO;YAC5C;QACD;QAGD,IAAI,SAAS,WAAA,KAAgB,MAAM;YACjC,SAAS,kBAAA,CAAoB;QAC9B;QAED,KAAK,IAAA,CAAK,SAAS,WAAW,EAAE,YAAA,CAAa,WAAW;QAGxD,IAAI;QACJ,IAAI,YAAY;YACd,YAAY,aAAa;QAC/B,OAAW;YACL,MAAM,gBAAgB,KAAK,GAAA,CAAI,OAAO,IAAA,EAAM,KAAK,eAAA,CAAgB,KAAK,MAAM,CAAC;YAC7E,YAAY,uBAAuB,QAAQ,eAAe,SAAS,UAAU;QAC9E;QAED,KAAK,cAAA,CAAe,SAAS;QAE7B,IAAI,KAAK,aAAA,CAAc,IAAI,MAAM,OAAO;YACtC;QACD;QAED,IAAI,YAAY;YACd,kBAAkB,IAAA,EAAM,UAAU;QACxC,OAAW;YACL,mBAAmB,IAAA,EAAM,QAAQ,UAAU;QAC5C;IACF;IAED,eAAe,QAAA,EAAU;QACvB,MAAM,WAAW,IAAA,CAAK,QAAA,CAAS,QAAA;QAE/B,IAAI,YAAY,SAAS,UAAA,EAAY;YACnC,SAAS,WAAA,CAAY,SAAS;YAC9B,IAAA,CAAK,QAAA,CAAS,QAAA,CAAS,UAAA,CAAW,KAAA,CAAM,GAAA,CAAI,UAAU,CAAA,EAAG,UAAU,CAAC;QACrE;IACF;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2889, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2895, "column": 0}, "map": {"version": 3, "file": "LineGeometry.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/lines/LineGeometry.js"], "sourcesContent": ["import { LineSegmentsGeometry } from '../lines/LineSegmentsGeometry'\n\nclass LineGeometry extends LineSegmentsGeometry {\n  constructor() {\n    super()\n\n    this.isLineGeometry = true\n\n    this.type = 'LineGeometry'\n  }\n\n  setPositions(array) {\n    // converts [ x1, y1, z1,  x2, y2, z2, ... ] to pairs format\n\n    const length = array.length - 3\n    const points = new Float32Array(2 * length)\n\n    for (let i = 0; i < length; i += 3) {\n      points[2 * i] = array[i]\n      points[2 * i + 1] = array[i + 1]\n      points[2 * i + 2] = array[i + 2]\n\n      points[2 * i + 3] = array[i + 3]\n      points[2 * i + 4] = array[i + 4]\n      points[2 * i + 5] = array[i + 5]\n    }\n\n    super.setPositions(points)\n\n    return this\n  }\n\n  setColors(array, itemSize = 3) {\n    // converts [ r1, g1, b1, (a1),  r2, g2, b2, (a2), ... ] to pairs format\n\n    const length = array.length - itemSize\n    const colors = new Float32Array(2 * length)\n\n    if (itemSize === 3) {\n      for (let i = 0; i < length; i += itemSize) {\n        colors[2 * i] = array[i]\n        colors[2 * i + 1] = array[i + 1]\n        colors[2 * i + 2] = array[i + 2]\n\n        colors[2 * i + 3] = array[i + 3]\n        colors[2 * i + 4] = array[i + 4]\n        colors[2 * i + 5] = array[i + 5]\n      }\n    } else {\n      for (let i = 0; i < length; i += itemSize) {\n        colors[2 * i] = array[i]\n        colors[2 * i + 1] = array[i + 1]\n        colors[2 * i + 2] = array[i + 2]\n        colors[2 * i + 3] = array[i + 3]\n\n        colors[2 * i + 4] = array[i + 4]\n        colors[2 * i + 5] = array[i + 5]\n        colors[2 * i + 6] = array[i + 6]\n        colors[2 * i + 7] = array[i + 7]\n      }\n    }\n\n    super.setColors(colors, itemSize)\n\n    return this\n  }\n\n  fromLine(line) {\n    const geometry = line.geometry\n\n    this.setPositions(geometry.attributes.position.array) // assumes non-indexed\n\n    // set colors, maybe\n\n    return this\n  }\n}\n\nexport { LineGeometry }\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,6QAAqB,uBAAA,CAAqB;IAC9C,aAAc;QACZ,KAAA,CAAO;QAEP,IAAA,CAAK,cAAA,GAAiB;QAEtB,IAAA,CAAK,IAAA,GAAO;IACb;IAED,aAAa,KAAA,EAAO;QAGlB,MAAM,SAAS,MAAM,MAAA,GAAS;QAC9B,MAAM,SAAS,IAAI,aAAa,IAAI,MAAM;QAE1C,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,EAAG;YAClC,MAAA,CAAO,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;YACvB,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;YAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;YAE/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;YAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;YAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;QAChC;QAED,KAAA,CAAM,aAAa,MAAM;QAEzB,OAAO,IAAA;IACR;IAED,UAAU,KAAA,EAAO,WAAW,CAAA,EAAG;QAG7B,MAAM,SAAS,MAAM,MAAA,GAAS;QAC9B,MAAM,SAAS,IAAI,aAAa,IAAI,MAAM;QAE1C,IAAI,aAAa,GAAG;YAClB,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,SAAU;gBACzC,MAAA,CAAO,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;gBACvB,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAE/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;YAChC;QACP,OAAW;YACL,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,SAAU;gBACzC,MAAA,CAAO,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;gBACvB,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAE/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;gBAC/B,MAAA,CAAO,IAAI,IAAI,CAAC,CAAA,GAAI,KAAA,CAAM,IAAI,CAAC,CAAA;YAChC;QACF;QAED,KAAA,CAAM,UAAU,QAAQ,QAAQ;QAEhC,OAAO,IAAA;IACR;IAED,SAAS,IAAA,EAAM;QACb,MAAM,WAAW,KAAK,QAAA;QAEtB,IAAA,CAAK,YAAA,CAAa,SAAS,UAAA,CAAW,QAAA,CAAS,KAAK;QAIpD,OAAO,IAAA;IACR;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2955, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2961, "column": 0}, "map": {"version": 3, "file": "Line2.js", "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/three-stdlib%402.36.0_three%400.177.0/node_modules/src/lines/Line2.js"], "sourcesContent": ["import { LineSegments2 } from '../lines/LineSegments2'\nimport { LineGeometry } from '../lines/LineGeometry'\nimport { LineMaterial } from '../lines/LineMaterial'\n\nclass Line2 extends LineSegments2 {\n  constructor(geometry = new LineGeometry(), material = new LineMaterial({ color: Math.random() * 0xffffff })) {\n    super(geometry, material)\n\n    this.isLine2 = true\n\n    this.type = 'Line2'\n  }\n}\n\nexport { Line2 }\n"], "names": [], "mappings": ";;;;;;;;;AAIA,MAAM,+PAAc,gBAAA,CAAc;IAChC,YAAY,WAAW,oPAAI,eAAA,CAAc,CAAA,EAAE,WAAW,oPAAI,eAAA,CAAa;QAAE,OAAO,KAAK,MAAA,KAAW;IAAU,CAAA,CAAA,CAAG;QAC3G,KAAA,CAAM,UAAU,QAAQ;QAExB,IAAA,CAAK,OAAA,GAAU;QAEf,IAAA,CAAK,IAAA,GAAO;IACb;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2981, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2987, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/core/OrbitControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { OrbitControls as OrbitControls$1 } from 'three-stdlib';\n\nconst OrbitControls = /* @__PURE__ */React.forwardRef(({\n  makeDefault,\n  camera,\n  regress,\n  domElement,\n  enableDamping = true,\n  keyEvents = false,\n  onChange,\n  onStart,\n  onEnd,\n  ...restProps\n}, ref) => {\n  const invalidate = useThree(state => state.invalidate);\n  const defaultCamera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const setEvents = useThree(state => state.setEvents);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const performance = useThree(state => state.performance);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = React.useMemo(() => new OrbitControls$1(explCamera), [explCamera]);\n  useFrame(() => {\n    if (controls.enabled) controls.update();\n  }, -1);\n  React.useEffect(() => {\n    if (keyEvents) {\n      controls.connect(keyEvents === true ? explDomElement : keyEvents);\n    }\n    controls.connect(explDomElement);\n    return () => void controls.dispose();\n  }, [keyEvents, explDomElement, regress, controls, invalidate]);\n  React.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (regress) performance.regress();\n      if (onChange) onChange(e);\n    };\n    const onStartCb = e => {\n      if (onStart) onStart(e);\n    };\n    const onEndCb = e => {\n      if (onEnd) onEnd(e);\n    };\n    controls.addEventListener('change', callback);\n    controls.addEventListener('start', onStartCb);\n    controls.addEventListener('end', onEndCb);\n    return () => {\n      controls.removeEventListener('start', onStartCb);\n      controls.removeEventListener('end', onEndCb);\n      controls.removeEventListener('change', callback);\n    };\n  }, [onChange, onStart, onEnd, controls, invalidate, setEvents]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      // @ts-ignore https://github.com/three-types/three-ts-types/pull/1398\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls,\n    enableDamping: enableDamping\n  }, restProps));\n});\n\nexport { OrbitControls };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EACrD,WAAW,EACX,MAAM,EACN,OAAO,EACP,UAAU,EACV,gBAAgB,IAAI,EACpB,YAAY,KAAK,EACjB,QAAQ,EACR,OAAO,EACP,KAAK,EACL,GAAG,WACJ,EAAE;IACD,MAAM,aAAa,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;8CAAE,CAAA,QAAS,MAAM,UAAU;;IACrD,MAAM,gBAAgB,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;iDAAE,CAAA,QAAS,MAAM,MAAM;;IACpD,MAAM,KAAK,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;sCAAE,CAAA,QAAS,MAAM,EAAE;;IACrC,MAAM,SAAS,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;0CAAE,CAAA,QAAS,MAAM,MAAM;;IAC7C,MAAM,YAAY,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;6CAAE,CAAA,QAAS,MAAM,SAAS;;IACnD,MAAM,MAAM,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;uCAAE,CAAA,QAAS,MAAM,GAAG;;IACvC,MAAM,MAAM,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;uCAAE,CAAA,QAAS,MAAM,GAAG;;IACvC,MAAM,cAAc,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;+CAAE,CAAA,QAAS,MAAM,WAAW;;IACvD,MAAM,aAAa,UAAU;IAC7B,MAAM,iBAAiB,cAAc,OAAO,SAAS,IAAI,GAAG,UAAU;IACtE,MAAM,WAAW,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;2CAAE,IAAM,IAAI,mPAAA,CAAA,gBAAe,CAAC;0CAAa;QAAC;KAAW;IAClF,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;kCAAE;YACP,IAAI,SAAS,OAAO,EAAE,SAAS,MAAM;QACvC;iCAAG,CAAC;IACJ,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;mCAAE;YACd,IAAI,WAAW;gBACb,SAAS,OAAO,CAAC,cAAc,OAAO,iBAAiB;YACzD;YACA,SAAS,OAAO,CAAC;YACjB;2CAAO,IAAM,KAAK,SAAS,OAAO;;QACpC;kCAAG;QAAC;QAAW;QAAgB;QAAS;QAAU;KAAW;IAC7D,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;mCAAE;YACd,MAAM;oDAAW,CAAA;oBACf;oBACA,IAAI,SAAS,YAAY,OAAO;oBAChC,IAAI,UAAU,SAAS;gBACzB;;YACA,MAAM;qDAAY,CAAA;oBAChB,IAAI,SAAS,QAAQ;gBACvB;;YACA,MAAM;mDAAU,CAAA;oBACd,IAAI,OAAO,MAAM;gBACnB;;YACA,SAAS,gBAAgB,CAAC,UAAU;YACpC,SAAS,gBAAgB,CAAC,SAAS;YACnC,SAAS,gBAAgB,CAAC,OAAO;YACjC;2CAAO;oBACL,SAAS,mBAAmB,CAAC,SAAS;oBACtC,SAAS,mBAAmB,CAAC,OAAO;oBACpC,SAAS,mBAAmB,CAAC,UAAU;gBACzC;;QACF;kCAAG;QAAC;QAAU;QAAS;QAAO;QAAU;QAAY;KAAU;IAC9D,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;mCAAE;YACd,IAAI,aAAa;gBACf,MAAM,MAAM,MAAM,QAAQ;gBAC1B,qEAAqE;gBACrE,IAAI;oBACF;gBACF;gBACA;+CAAO,IAAM,IAAI;4BACf,UAAU;wBACZ;;YACF;QACF;kCAAG;QAAC;QAAa;KAAS;IAC1B,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,aAAa,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;QAC5D,KAAK;QACL,QAAQ;QACR,eAAe;IACjB,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3117, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3123, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/web/Html.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom/client';\nimport { Vector3, DoubleSide, OrthographicCamera, PerspectiveCamera, Vector2 } from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\n\nconst v1 = /* @__PURE__ */new Vector3();\nconst v2 = /* @__PURE__ */new Vector3();\nconst v3 = /* @__PURE__ */new Vector3();\nconst v4 = /* @__PURE__ */new Vector2();\nfunction defaultCalculatePosition(el, camera, size) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  objectPos.project(camera);\n  const widthHalf = size.width / 2;\n  const heightHalf = size.height / 2;\n  return [objectPos.x * widthHalf + widthHalf, -(objectPos.y * heightHalf) + heightHalf];\n}\nfunction isObjectBehindCamera(el, camera) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n  const deltaCamObj = objectPos.sub(cameraPos);\n  const camDir = camera.getWorldDirection(v3);\n  return deltaCamObj.angleTo(camDir) > Math.PI / 2;\n}\nfunction isObjectVisible(el, camera, raycaster, occlude) {\n  const elPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const screenPos = elPos.clone();\n  screenPos.project(camera);\n  v4.set(screenPos.x, screenPos.y);\n  raycaster.setFromCamera(v4, camera);\n  const intersects = raycaster.intersectObjects(occlude, true);\n  if (intersects.length) {\n    const intersectionDistance = intersects[0].distance;\n    const pointDistance = elPos.distanceTo(raycaster.ray.origin);\n    return pointDistance < intersectionDistance;\n  }\n  return true;\n}\nfunction objectScale(el, camera) {\n  if (camera instanceof OrthographicCamera) {\n    return camera.zoom;\n  } else if (camera instanceof PerspectiveCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const vFOV = camera.fov * Math.PI / 180;\n    const dist = objectPos.distanceTo(cameraPos);\n    const scaleFOV = 2 * Math.tan(vFOV / 2) * dist;\n    return 1 / scaleFOV;\n  } else {\n    return 1;\n  }\n}\nfunction objectZIndex(el, camera, zIndexRange) {\n  if (camera instanceof PerspectiveCamera || camera instanceof OrthographicCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const dist = objectPos.distanceTo(cameraPos);\n    const A = (zIndexRange[1] - zIndexRange[0]) / (camera.far - camera.near);\n    const B = zIndexRange[1] - A * camera.far;\n    return Math.round(A * dist + B);\n  }\n  return undefined;\n}\nconst epsilon = value => Math.abs(value) < 1e-10 ? 0 : value;\nfunction getCSSMatrix(matrix, multipliers, prepend = '') {\n  let matrix3d = 'matrix3d(';\n  for (let i = 0; i !== 16; i++) {\n    matrix3d += epsilon(multipliers[i] * matrix.elements[i]) + (i !== 15 ? ',' : ')');\n  }\n  return prepend + matrix3d;\n}\nconst getCameraCSSMatrix = (multipliers => {\n  return matrix => getCSSMatrix(matrix, multipliers);\n})([1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1]);\nconst getObjectCSSMatrix = (scaleMultipliers => {\n  return (matrix, factor) => getCSSMatrix(matrix, scaleMultipliers(factor), 'translate(-50%,-50%)');\n})(f => [1 / f, 1 / f, 1 / f, 1, -1 / f, -1 / f, -1 / f, -1, 1 / f, 1 / f, 1 / f, 1, 1, 1, 1, 1]);\nfunction isRefObject(ref) {\n  return ref && typeof ref === 'object' && 'current' in ref;\n}\nconst Html = /* @__PURE__ */React.forwardRef(({\n  children,\n  eps = 0.001,\n  style,\n  className,\n  prepend,\n  center,\n  fullscreen,\n  portal,\n  distanceFactor,\n  sprite = false,\n  transform = false,\n  occlude,\n  onOcclude,\n  castShadow,\n  receiveShadow,\n  material,\n  geometry,\n  zIndexRange = [16777271, 0],\n  calculatePosition = defaultCalculatePosition,\n  as = 'div',\n  wrapperClass,\n  pointerEvents = 'auto',\n  ...props\n}, ref) => {\n  const {\n    gl,\n    camera,\n    scene,\n    size,\n    raycaster,\n    events,\n    viewport\n  } = useThree();\n  const [el] = React.useState(() => document.createElement(as));\n  const root = React.useRef(null);\n  const group = React.useRef(null);\n  const oldZoom = React.useRef(0);\n  const oldPosition = React.useRef([0, 0]);\n  const transformOuterRef = React.useRef(null);\n  const transformInnerRef = React.useRef(null);\n  // Append to the connected element, which makes HTML work with views\n  const target = (portal == null ? void 0 : portal.current) || events.connected || gl.domElement.parentNode;\n  const occlusionMeshRef = React.useRef(null);\n  const isMeshSizeSet = React.useRef(false);\n  const isRayCastOcclusion = React.useMemo(() => {\n    return occlude && occlude !== 'blending' || Array.isArray(occlude) && occlude.length && isRefObject(occlude[0]);\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    const el = gl.domElement;\n    if (occlude && occlude === 'blending') {\n      el.style.zIndex = `${Math.floor(zIndexRange[0] / 2)}`;\n      el.style.position = 'absolute';\n      el.style.pointerEvents = 'none';\n    } else {\n      el.style.zIndex = null;\n      el.style.position = null;\n      el.style.pointerEvents = null;\n    }\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    if (group.current) {\n      const currentRoot = root.current = ReactDOM.createRoot(el);\n      scene.updateMatrixWorld();\n      if (transform) {\n        el.style.cssText = `position:absolute;top:0;left:0;pointer-events:none;overflow:hidden;`;\n      } else {\n        const vec = calculatePosition(group.current, camera, size);\n        el.style.cssText = `position:absolute;top:0;left:0;transform:translate3d(${vec[0]}px,${vec[1]}px,0);transform-origin:0 0;`;\n      }\n      if (target) {\n        if (prepend) target.prepend(el);else target.appendChild(el);\n      }\n      return () => {\n        if (target) target.removeChild(el);\n        currentRoot.unmount();\n      };\n    }\n  }, [target, transform]);\n  React.useLayoutEffect(() => {\n    if (wrapperClass) el.className = wrapperClass;\n  }, [wrapperClass]);\n  const styles = React.useMemo(() => {\n    if (transform) {\n      return {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: size.width,\n        height: size.height,\n        transformStyle: 'preserve-3d',\n        pointerEvents: 'none'\n      };\n    } else {\n      return {\n        position: 'absolute',\n        transform: center ? 'translate3d(-50%,-50%,0)' : 'none',\n        ...(fullscreen && {\n          top: -size.height / 2,\n          left: -size.width / 2,\n          width: size.width,\n          height: size.height\n        }),\n        ...style\n      };\n    }\n  }, [style, center, fullscreen, size, transform]);\n  const transformInnerStyles = React.useMemo(() => ({\n    position: 'absolute',\n    pointerEvents\n  }), [pointerEvents]);\n  React.useLayoutEffect(() => {\n    isMeshSizeSet.current = false;\n    if (transform) {\n      var _root$current;\n      (_root$current = root.current) == null || _root$current.render(/*#__PURE__*/React.createElement(\"div\", {\n        ref: transformOuterRef,\n        style: styles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: transformInnerRef,\n        style: transformInnerStyles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        className: className,\n        style: style,\n        children: children\n      }))));\n    } else {\n      var _root$current2;\n      (_root$current2 = root.current) == null || _root$current2.render(/*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        style: styles,\n        className: className,\n        children: children\n      }));\n    }\n  });\n  const visible = React.useRef(true);\n  useFrame(gl => {\n    if (group.current) {\n      camera.updateMatrixWorld();\n      group.current.updateWorldMatrix(true, false);\n      const vec = transform ? oldPosition.current : calculatePosition(group.current, camera, size);\n      if (transform || Math.abs(oldZoom.current - camera.zoom) > eps || Math.abs(oldPosition.current[0] - vec[0]) > eps || Math.abs(oldPosition.current[1] - vec[1]) > eps) {\n        const isBehindCamera = isObjectBehindCamera(group.current, camera);\n        let raytraceTarget = false;\n        if (isRayCastOcclusion) {\n          if (Array.isArray(occlude)) {\n            raytraceTarget = occlude.map(item => item.current);\n          } else if (occlude !== 'blending') {\n            raytraceTarget = [scene];\n          }\n        }\n        const previouslyVisible = visible.current;\n        if (raytraceTarget) {\n          const isvisible = isObjectVisible(group.current, camera, raycaster, raytraceTarget);\n          visible.current = isvisible && !isBehindCamera;\n        } else {\n          visible.current = !isBehindCamera;\n        }\n        if (previouslyVisible !== visible.current) {\n          if (onOcclude) onOcclude(!visible.current);else el.style.display = visible.current ? 'block' : 'none';\n        }\n        const halfRange = Math.floor(zIndexRange[0] / 2);\n        const zRange = occlude ? isRayCastOcclusion //\n        ? [zIndexRange[0], halfRange] : [halfRange - 1, 0] : zIndexRange;\n        el.style.zIndex = `${objectZIndex(group.current, camera, zRange)}`;\n        if (transform) {\n          const [widthHalf, heightHalf] = [size.width / 2, size.height / 2];\n          const fov = camera.projectionMatrix.elements[5] * heightHalf;\n          const {\n            isOrthographicCamera,\n            top,\n            left,\n            bottom,\n            right\n          } = camera;\n          const cameraMatrix = getCameraCSSMatrix(camera.matrixWorldInverse);\n          const cameraTransform = isOrthographicCamera ? `scale(${fov})translate(${epsilon(-(right + left) / 2)}px,${epsilon((top + bottom) / 2)}px)` : `translateZ(${fov}px)`;\n          let matrix = group.current.matrixWorld;\n          if (sprite) {\n            matrix = camera.matrixWorldInverse.clone().transpose().copyPosition(matrix).scale(group.current.scale);\n            matrix.elements[3] = matrix.elements[7] = matrix.elements[11] = 0;\n            matrix.elements[15] = 1;\n          }\n          el.style.width = size.width + 'px';\n          el.style.height = size.height + 'px';\n          el.style.perspective = isOrthographicCamera ? '' : `${fov}px`;\n          if (transformOuterRef.current && transformInnerRef.current) {\n            transformOuterRef.current.style.transform = `${cameraTransform}${cameraMatrix}translate(${widthHalf}px,${heightHalf}px)`;\n            transformInnerRef.current.style.transform = getObjectCSSMatrix(matrix, 1 / ((distanceFactor || 10) / 400));\n          }\n        } else {\n          const scale = distanceFactor === undefined ? 1 : objectScale(group.current, camera) * distanceFactor;\n          el.style.transform = `translate3d(${vec[0]}px,${vec[1]}px,0) scale(${scale})`;\n        }\n        oldPosition.current = vec;\n        oldZoom.current = camera.zoom;\n      }\n    }\n    if (!isRayCastOcclusion && occlusionMeshRef.current && !isMeshSizeSet.current) {\n      if (transform) {\n        if (transformOuterRef.current) {\n          const el = transformOuterRef.current.children[0];\n          if (el != null && el.clientWidth && el != null && el.clientHeight) {\n            const {\n              isOrthographicCamera\n            } = camera;\n            if (isOrthographicCamera || geometry) {\n              if (props.scale) {\n                if (!Array.isArray(props.scale)) {\n                  occlusionMeshRef.current.scale.setScalar(1 / props.scale);\n                } else if (props.scale instanceof Vector3) {\n                  occlusionMeshRef.current.scale.copy(props.scale.clone().divideScalar(1));\n                } else {\n                  occlusionMeshRef.current.scale.set(1 / props.scale[0], 1 / props.scale[1], 1 / props.scale[2]);\n                }\n              }\n            } else {\n              const ratio = (distanceFactor || 10) / 400;\n              const w = el.clientWidth * ratio;\n              const h = el.clientHeight * ratio;\n              occlusionMeshRef.current.scale.set(w, h, 1);\n            }\n            isMeshSizeSet.current = true;\n          }\n        }\n      } else {\n        const ele = el.children[0];\n        if (ele != null && ele.clientWidth && ele != null && ele.clientHeight) {\n          const ratio = 1 / viewport.factor;\n          const w = ele.clientWidth * ratio;\n          const h = ele.clientHeight * ratio;\n          occlusionMeshRef.current.scale.set(w, h, 1);\n          isMeshSizeSet.current = true;\n        }\n        occlusionMeshRef.current.lookAt(gl.camera.position);\n      }\n    }\n  });\n  const shaders = React.useMemo(() => ({\n    vertexShader: !transform ? /* glsl */`\n          /*\n            This shader is from the THREE's SpriteMaterial.\n            We need to turn the backing plane into a Sprite\n            (make it always face the camera) if \"transfrom\"\n            is false.\n          */\n          #include <common>\n\n          void main() {\n            vec2 center = vec2(0., 1.);\n            float rotation = 0.0;\n\n            // This is somewhat arbitrary, but it seems to work well\n            // Need to figure out how to derive this dynamically if it even matters\n            float size = 0.03;\n\n            vec4 mvPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );\n            vec2 scale;\n            scale.x = length( vec3( modelMatrix[ 0 ].x, modelMatrix[ 0 ].y, modelMatrix[ 0 ].z ) );\n            scale.y = length( vec3( modelMatrix[ 1 ].x, modelMatrix[ 1 ].y, modelMatrix[ 1 ].z ) );\n\n            bool isPerspective = isPerspectiveMatrix( projectionMatrix );\n            if ( isPerspective ) scale *= - mvPosition.z;\n\n            vec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale * size;\n            vec2 rotatedPosition;\n            rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;\n            rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;\n            mvPosition.xy += rotatedPosition;\n\n            gl_Position = projectionMatrix * mvPosition;\n          }\n      ` : undefined,\n    fragmentShader: /* glsl */`\n        void main() {\n          gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n        }\n      `\n  }), [transform]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({}, props, {\n    ref: group\n  }), occlude && !isRayCastOcclusion && /*#__PURE__*/React.createElement(\"mesh\", {\n    castShadow: castShadow,\n    receiveShadow: receiveShadow,\n    ref: occlusionMeshRef\n  }, geometry || /*#__PURE__*/React.createElement(\"planeGeometry\", null), material || /*#__PURE__*/React.createElement(\"shaderMaterial\", {\n    side: DoubleSide,\n    vertexShader: shaders.vertexShader,\n    fragmentShader: shaders.fragmentShader\n  })));\n});\n\nexport { Html };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;AAEA,MAAM,KAAK,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAO;AACrC,MAAM,KAAK,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAO;AACrC,MAAM,KAAK,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAO;AACrC,MAAM,KAAK,aAAa,GAAE,IAAI,sMAAA,CAAA,UAAO;AACrC,SAAS,yBAAyB,EAAE,EAAE,MAAM,EAAE,IAAI;IAChD,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;IACzD,UAAU,OAAO,CAAC;IAClB,MAAM,YAAY,KAAK,KAAK,GAAG;IAC/B,MAAM,aAAa,KAAK,MAAM,GAAG;IACjC,OAAO;QAAC,UAAU,CAAC,GAAG,YAAY;QAAW,CAAC,CAAC,UAAU,CAAC,GAAG,UAAU,IAAI;KAAW;AACxF;AACA,SAAS,qBAAqB,EAAE,EAAE,MAAM;IACtC,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;IACzD,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,WAAW;IAC7D,MAAM,cAAc,UAAU,GAAG,CAAC;IAClC,MAAM,SAAS,OAAO,iBAAiB,CAAC;IACxC,OAAO,YAAY,OAAO,CAAC,UAAU,KAAK,EAAE,GAAG;AACjD;AACA,SAAS,gBAAgB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO;IACrD,MAAM,QAAQ,GAAG,qBAAqB,CAAC,GAAG,WAAW;IACrD,MAAM,YAAY,MAAM,KAAK;IAC7B,UAAU,OAAO,CAAC;IAClB,GAAG,GAAG,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC;IAC/B,UAAU,aAAa,CAAC,IAAI;IAC5B,MAAM,aAAa,UAAU,gBAAgB,CAAC,SAAS;IACvD,IAAI,WAAW,MAAM,EAAE;QACrB,MAAM,uBAAuB,UAAU,CAAC,EAAE,CAAC,QAAQ;QACnD,MAAM,gBAAgB,MAAM,UAAU,CAAC,UAAU,GAAG,CAAC,MAAM;QAC3D,OAAO,gBAAgB;IACzB;IACA,OAAO;AACT;AACA,SAAS,YAAY,EAAE,EAAE,MAAM;IAC7B,IAAI,kBAAkB,sMAAA,CAAA,qBAAkB,EAAE;QACxC,OAAO,OAAO,IAAI;IACpB,OAAO,IAAI,kBAAkB,sMAAA,CAAA,oBAAiB,EAAE;QAC9C,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;QACzD,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,WAAW;QAC7D,MAAM,OAAO,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG;QACpC,MAAM,OAAO,UAAU,UAAU,CAAC;QAClC,MAAM,WAAW,IAAI,KAAK,GAAG,CAAC,OAAO,KAAK;QAC1C,OAAO,IAAI;IACb,OAAO;QACL,OAAO;IACT;AACF;AACA,SAAS,aAAa,EAAE,EAAE,MAAM,EAAE,WAAW;IAC3C,IAAI,kBAAkB,sMAAA,CAAA,oBAAiB,IAAI,kBAAkB,sMAAA,CAAA,qBAAkB,EAAE;QAC/E,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;QACzD,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,WAAW;QAC7D,MAAM,OAAO,UAAU,UAAU,CAAC;QAClC,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,GAAG,OAAO,IAAI;QACvE,MAAM,IAAI,WAAW,CAAC,EAAE,GAAG,IAAI,OAAO,GAAG;QACzC,OAAO,KAAK,KAAK,CAAC,IAAI,OAAO;IAC/B;IACA,OAAO;AACT;AACA,MAAM,UAAU,CAAA,QAAS,KAAK,GAAG,CAAC,SAAS,QAAQ,IAAI;AACvD,SAAS,aAAa,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE;IACrD,IAAI,WAAW;IACf,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,IAAK;QAC7B,YAAY,QAAQ,WAAW,CAAC,EAAE,GAAG,OAAO,QAAQ,CAAC,EAAE,IAAI,CAAC,MAAM,KAAK,MAAM,GAAG;IAClF;IACA,OAAO,UAAU;AACnB;AACA,MAAM,qBAAqB,CAAC,CAAA;IAC1B,OAAO,CAAA,SAAU,aAAa,QAAQ;AACxC,CAAC,EAAE;IAAC;IAAG,CAAC;IAAG;IAAG;IAAG;IAAG,CAAC;IAAG;IAAG;IAAG;IAAG,CAAC;IAAG;IAAG;IAAG;IAAG,CAAC;IAAG;IAAG;CAAE;AACvD,MAAM,qBAAqB,CAAC,CAAA;IAC1B,OAAO,CAAC,QAAQ,SAAW,aAAa,QAAQ,iBAAiB,SAAS;AAC5E,CAAC,EAAE,CAAA,IAAK;QAAC,IAAI;QAAG,IAAI;QAAG,IAAI;QAAG;QAAG,CAAC,IAAI;QAAG,CAAC,IAAI;QAAG,CAAC,IAAI;QAAG,CAAC;QAAG,IAAI;QAAG,IAAI;QAAG,IAAI;QAAG;QAAG;QAAG;QAAG;QAAG;KAAE;AAChG,SAAS,YAAY,GAAG;IACtB,OAAO,OAAO,OAAO,QAAQ,YAAY,aAAa;AACxD;AACA,MAAM,OAAO,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAC5C,QAAQ,EACR,MAAM,KAAK,EACX,KAAK,EACL,SAAS,EACT,OAAO,EACP,MAAM,EACN,UAAU,EACV,MAAM,EACN,cAAc,EACd,SAAS,KAAK,EACd,YAAY,KAAK,EACjB,OAAO,EACP,SAAS,EACT,UAAU,EACV,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,cAAc;IAAC;IAAU;CAAE,EAC3B,oBAAoB,wBAAwB,EAC5C,KAAK,KAAK,EACV,YAAY,EACZ,gBAAgB,MAAM,EACtB,GAAG,OACJ,EAAE;IACD,MAAM,EACJ,EAAE,EACF,MAAM,EACN,KAAK,EACL,IAAI,EACJ,SAAS,EACT,MAAM,EACN,QAAQ,EACT,GAAG,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;IACX,MAAM,CAAC,GAAG,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD;yBAAE,IAAM,SAAS,aAAa,CAAC;;IACzD,MAAM,OAAO,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC1B,MAAM,QAAQ,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC3B,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC7B,MAAM,cAAc,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;QAAC;QAAG;KAAE;IACvC,MAAM,oBAAoB,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IACvC,MAAM,oBAAoB,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IACvC,oEAAoE;IACpE,MAAM,SAAS,CAAC,UAAU,OAAO,KAAK,IAAI,OAAO,OAAO,KAAK,OAAO,SAAS,IAAI,GAAG,UAAU,CAAC,UAAU;IACzG,MAAM,mBAAmB,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IACtC,MAAM,gBAAgB,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IACnC,MAAM,qBAAqB,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;4CAAE;YACvC,OAAO,WAAW,YAAY,cAAc,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,IAAI,YAAY,OAAO,CAAC,EAAE;QAChH;2CAAG;QAAC;KAAQ;IACZ,CAAA,GAAA,4RAAA,CAAA,kBAAqB,AAAD;gCAAE;YACpB,MAAM,KAAK,GAAG,UAAU;YACxB,IAAI,WAAW,YAAY,YAAY;gBACrC,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG,KAAK,KAAK,CAAC,WAAW,CAAC,EAAE,GAAG,IAAI;gBACrD,GAAG,KAAK,CAAC,QAAQ,GAAG;gBACpB,GAAG,KAAK,CAAC,aAAa,GAAG;YAC3B,OAAO;gBACL,GAAG,KAAK,CAAC,MAAM,GAAG;gBAClB,GAAG,KAAK,CAAC,QAAQ,GAAG;gBACpB,GAAG,KAAK,CAAC,aAAa,GAAG;YAC3B;QACF;+BAAG;QAAC;KAAQ;IACZ,CAAA,GAAA,4RAAA,CAAA,kBAAqB,AAAD;gCAAE;YACpB,IAAI,MAAM,OAAO,EAAE;gBACjB,MAAM,cAAc,KAAK,OAAO,GAAG,CAAA,GAAA,oSAAA,CAAA,aAAmB,AAAD,EAAE;gBACvD,MAAM,iBAAiB;gBACvB,IAAI,WAAW;oBACb,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,mEAAmE,CAAC;gBAC1F,OAAO;oBACL,MAAM,MAAM,kBAAkB,MAAM,OAAO,EAAE,QAAQ;oBACrD,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,qDAAqD,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,2BAA2B,CAAC;gBAC5H;gBACA,IAAI,QAAQ;oBACV,IAAI,SAAS,OAAO,OAAO,CAAC;yBAAS,OAAO,WAAW,CAAC;gBAC1D;gBACA;4CAAO;wBACL,IAAI,QAAQ,OAAO,WAAW,CAAC;wBAC/B,YAAY,OAAO;oBACrB;;YACF;QACF;+BAAG;QAAC;QAAQ;KAAU;IACtB,CAAA,GAAA,4RAAA,CAAA,kBAAqB,AAAD;gCAAE;YACpB,IAAI,cAAc,GAAG,SAAS,GAAG;QACnC;+BAAG;QAAC;KAAa;IACjB,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;gCAAE;YAC3B,IAAI,WAAW;gBACb,OAAO;oBACL,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO,KAAK,KAAK;oBACjB,QAAQ,KAAK,MAAM;oBACnB,gBAAgB;oBAChB,eAAe;gBACjB;YACF,OAAO;gBACL,OAAO;oBACL,UAAU;oBACV,WAAW,SAAS,6BAA6B;oBACjD,GAAI,cAAc;wBAChB,KAAK,CAAC,KAAK,MAAM,GAAG;wBACpB,MAAM,CAAC,KAAK,KAAK,GAAG;wBACpB,OAAO,KAAK,KAAK;wBACjB,QAAQ,KAAK,MAAM;oBACrB,CAAC;oBACD,GAAG,KAAK;gBACV;YACF;QACF;+BAAG;QAAC;QAAO;QAAQ;QAAY;QAAM;KAAU;IAC/C,MAAM,uBAAuB,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;8CAAE,IAAM,CAAC;gBAChD,UAAU;gBACV;YACF,CAAC;6CAAG;QAAC;KAAc;IACnB,CAAA,GAAA,4RAAA,CAAA,kBAAqB,AAAD;gCAAE;YACpB,cAAc,OAAO,GAAG;YACxB,IAAI,WAAW;gBACb,IAAI;gBACJ,CAAC,gBAAgB,KAAK,OAAO,KAAK,QAAQ,cAAc,MAAM,CAAC,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;oBACrG,KAAK;oBACL,OAAO;gBACT,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;oBACzC,KAAK;oBACL,OAAO;gBACT,GAAG,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;oBACzC,KAAK;oBACL,WAAW;oBACX,OAAO;oBACP,UAAU;gBACZ;YACF,OAAO;gBACL,IAAI;gBACJ,CAAC,iBAAiB,KAAK,OAAO,KAAK,QAAQ,eAAe,MAAM,CAAC,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;oBACvG,KAAK;oBACL,OAAO;oBACP,WAAW;oBACX,UAAU;gBACZ;YACF;QACF;;IACA,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,SAAY,AAAD,EAAE;IAC7B,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;yBAAE,CAAA;YACP,IAAI,MAAM,OAAO,EAAE;gBACjB,OAAO,iBAAiB;gBACxB,MAAM,OAAO,CAAC,iBAAiB,CAAC,MAAM;gBACtC,MAAM,MAAM,YAAY,YAAY,OAAO,GAAG,kBAAkB,MAAM,OAAO,EAAE,QAAQ;gBACvF,IAAI,aAAa,KAAK,GAAG,CAAC,QAAQ,OAAO,GAAG,OAAO,IAAI,IAAI,OAAO,KAAK,GAAG,CAAC,YAAY,OAAO,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,OAAO,KAAK,GAAG,CAAC,YAAY,OAAO,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,KAAK;oBACpK,MAAM,iBAAiB,qBAAqB,MAAM,OAAO,EAAE;oBAC3D,IAAI,iBAAiB;oBACrB,IAAI,oBAAoB;wBACtB,IAAI,MAAM,OAAO,CAAC,UAAU;4BAC1B,iBAAiB,QAAQ,GAAG;iDAAC,CAAA,OAAQ,KAAK,OAAO;;wBACnD,OAAO,IAAI,YAAY,YAAY;4BACjC,iBAAiB;gCAAC;6BAAM;wBAC1B;oBACF;oBACA,MAAM,oBAAoB,QAAQ,OAAO;oBACzC,IAAI,gBAAgB;wBAClB,MAAM,YAAY,gBAAgB,MAAM,OAAO,EAAE,QAAQ,WAAW;wBACpE,QAAQ,OAAO,GAAG,aAAa,CAAC;oBAClC,OAAO;wBACL,QAAQ,OAAO,GAAG,CAAC;oBACrB;oBACA,IAAI,sBAAsB,QAAQ,OAAO,EAAE;wBACzC,IAAI,WAAW,UAAU,CAAC,QAAQ,OAAO;6BAAO,GAAG,KAAK,CAAC,OAAO,GAAG,QAAQ,OAAO,GAAG,UAAU;oBACjG;oBACA,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW,CAAC,EAAE,GAAG;oBAC9C,MAAM,SAAS,UAAU,mBAAmB,EAAE;uBAC5C;wBAAC,WAAW,CAAC,EAAE;wBAAE;qBAAU,GAAG;wBAAC,YAAY;wBAAG;qBAAE,GAAG;oBACrD,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG,aAAa,MAAM,OAAO,EAAE,QAAQ,SAAS;oBAClE,IAAI,WAAW;wBACb,MAAM,CAAC,WAAW,WAAW,GAAG;4BAAC,KAAK,KAAK,GAAG;4BAAG,KAAK,MAAM,GAAG;yBAAE;wBACjE,MAAM,MAAM,OAAO,gBAAgB,CAAC,QAAQ,CAAC,EAAE,GAAG;wBAClD,MAAM,EACJ,oBAAoB,EACpB,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACN,GAAG;wBACJ,MAAM,eAAe,mBAAmB,OAAO,kBAAkB;wBACjE,MAAM,kBAAkB,uBAAuB,CAAC,MAAM,EAAE,IAAI,WAAW,EAAE,QAAQ,CAAC,CAAC,QAAQ,IAAI,IAAI,GAAG,GAAG,EAAE,QAAQ,CAAC,MAAM,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC;wBACpK,IAAI,SAAS,MAAM,OAAO,CAAC,WAAW;wBACtC,IAAI,QAAQ;4BACV,SAAS,OAAO,kBAAkB,CAAC,KAAK,GAAG,SAAS,GAAG,YAAY,CAAC,QAAQ,KAAK,CAAC,MAAM,OAAO,CAAC,KAAK;4BACrG,OAAO,QAAQ,CAAC,EAAE,GAAG,OAAO,QAAQ,CAAC,EAAE,GAAG,OAAO,QAAQ,CAAC,GAAG,GAAG;4BAChE,OAAO,QAAQ,CAAC,GAAG,GAAG;wBACxB;wBACA,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,KAAK,GAAG;wBAC9B,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,MAAM,GAAG;wBAChC,GAAG,KAAK,CAAC,WAAW,GAAG,uBAAuB,KAAK,GAAG,IAAI,EAAE,CAAC;wBAC7D,IAAI,kBAAkB,OAAO,IAAI,kBAAkB,OAAO,EAAE;4BAC1D,kBAAkB,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,kBAAkB,aAAa,UAAU,EAAE,UAAU,GAAG,EAAE,WAAW,GAAG,CAAC;4BACxH,kBAAkB,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,mBAAmB,QAAQ,IAAI,CAAC,CAAC,kBAAkB,EAAE,IAAI,GAAG;wBAC1G;oBACF,OAAO;wBACL,MAAM,QAAQ,mBAAmB,YAAY,IAAI,YAAY,MAAM,OAAO,EAAE,UAAU;wBACtF,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;oBAC/E;oBACA,YAAY,OAAO,GAAG;oBACtB,QAAQ,OAAO,GAAG,OAAO,IAAI;gBAC/B;YACF;YACA,IAAI,CAAC,sBAAsB,iBAAiB,OAAO,IAAI,CAAC,cAAc,OAAO,EAAE;gBAC7E,IAAI,WAAW;oBACb,IAAI,kBAAkB,OAAO,EAAE;wBAC7B,MAAM,KAAK,kBAAkB,OAAO,CAAC,QAAQ,CAAC,EAAE;wBAChD,IAAI,MAAM,QAAQ,GAAG,WAAW,IAAI,MAAM,QAAQ,GAAG,YAAY,EAAE;4BACjE,MAAM,EACJ,oBAAoB,EACrB,GAAG;4BACJ,IAAI,wBAAwB,UAAU;gCACpC,IAAI,MAAM,KAAK,EAAE;oCACf,IAAI,CAAC,MAAM,OAAO,CAAC,MAAM,KAAK,GAAG;wCAC/B,iBAAiB,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,MAAM,KAAK;oCAC1D,OAAO,IAAI,MAAM,KAAK,YAAY,sMAAA,CAAA,UAAO,EAAE;wCACzC,iBAAiB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,KAAK,GAAG,YAAY,CAAC;oCACvE,OAAO;wCACL,iBAAiB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE;oCAC/F;gCACF;4BACF,OAAO;gCACL,MAAM,QAAQ,CAAC,kBAAkB,EAAE,IAAI;gCACvC,MAAM,IAAI,GAAG,WAAW,GAAG;gCAC3B,MAAM,IAAI,GAAG,YAAY,GAAG;gCAC5B,iBAAiB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG;4BAC3C;4BACA,cAAc,OAAO,GAAG;wBAC1B;oBACF;gBACF,OAAO;oBACL,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE;oBAC1B,IAAI,OAAO,QAAQ,IAAI,WAAW,IAAI,OAAO,QAAQ,IAAI,YAAY,EAAE;wBACrE,MAAM,QAAQ,IAAI,SAAS,MAAM;wBACjC,MAAM,IAAI,IAAI,WAAW,GAAG;wBAC5B,MAAM,IAAI,IAAI,YAAY,GAAG;wBAC7B,iBAAiB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG;wBACzC,cAAc,OAAO,GAAG;oBAC1B;oBACA,iBAAiB,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,QAAQ;gBACpD;YACF;QACF;;IACA,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;iCAAE,IAAM,CAAC;gBACnC,cAAc,CAAC,YAAY,QAAQ,GAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAiCpC,CAAC,GAAG;gBACN,gBAAgB,QAAQ,GAAE,CAAC;;;;MAIzB,CAAC;YACL,CAAC;gCAAG;QAAC;KAAU;IACf,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QACnE,KAAK;IACP,IAAI,WAAW,CAAC,sBAAsB,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC7E,YAAY;QACZ,eAAe;QACf,KAAK;IACP,GAAG,YAAY,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,iBAAiB,OAAO,YAAY,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,kBAAkB;QACrI,MAAM,sMAAA,CAAA,aAAU;QAChB,cAAc,QAAQ,YAAY;QAClC,gBAAgB,QAAQ,cAAc;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3564, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3570, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-three%2Bdrei%4010.1.2_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0.0_25016c57484519cf3a785dd0022bda46/node_modules/%40react-three/drei/core/Line.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { Vector3, Vector4, Vector2, Color } from 'three';\nimport { useThree } from '@react-three/fiber';\nimport { LineSegments2, Line2, LineMaterial, LineSegmentsGeometry, LineGeometry } from 'three-stdlib';\n\nconst Line = /* @__PURE__ */React.forwardRef(function Line({\n  points,\n  color = 0xffffff,\n  vertexColors,\n  linewidth,\n  lineWidth,\n  segments,\n  dashed,\n  ...rest\n}, ref) {\n  var _vertexColors$, _ref;\n  const size = useThree(state => state.size);\n  const line2 = React.useMemo(() => segments ? new LineSegments2() : new Line2(), [segments]);\n  const [lineMaterial] = React.useState(() => new LineMaterial());\n  const itemSize = (vertexColors == null || (_vertexColors$ = vertexColors[0]) == null ? void 0 : _vertexColors$.length) === 4 ? 4 : 3;\n  const lineGeom = React.useMemo(() => {\n    const geom = segments ? new LineSegmentsGeometry() : new LineGeometry();\n    const pValues = points.map(p => {\n      const isArray = Array.isArray(p);\n      return p instanceof Vector3 || p instanceof Vector4 ? [p.x, p.y, p.z] : p instanceof Vector2 ? [p.x, p.y, 0] : isArray && p.length === 3 ? [p[0], p[1], p[2]] : isArray && p.length === 2 ? [p[0], p[1], 0] : p;\n    });\n    geom.setPositions(pValues.flat());\n    if (vertexColors) {\n      // using vertexColors requires the color value to be white see #1813\n      color = 0xffffff;\n      const cValues = vertexColors.map(c => c instanceof Color ? c.toArray() : c);\n      geom.setColors(cValues.flat(), itemSize);\n    }\n    return geom;\n  }, [points, segments, vertexColors, itemSize]);\n  React.useLayoutEffect(() => {\n    line2.computeLineDistances();\n  }, [points, line2]);\n  React.useLayoutEffect(() => {\n    if (dashed) {\n      lineMaterial.defines.USE_DASH = '';\n    } else {\n      // Setting lineMaterial.defines.USE_DASH to undefined is apparently not sufficient.\n      delete lineMaterial.defines.USE_DASH;\n    }\n    lineMaterial.needsUpdate = true;\n  }, [dashed, lineMaterial]);\n  React.useEffect(() => {\n    return () => {\n      lineGeom.dispose();\n      lineMaterial.dispose();\n    };\n  }, [lineGeom]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: line2,\n    ref: ref\n  }, rest), /*#__PURE__*/React.createElement(\"primitive\", {\n    object: lineGeom,\n    attach: \"geometry\"\n  }), /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    object: lineMaterial,\n    attach: \"material\",\n    color: color,\n    vertexColors: Boolean(vertexColors),\n    resolution: [size.width, size.height],\n    linewidth: (_ref = linewidth !== null && linewidth !== void 0 ? linewidth : lineWidth) !== null && _ref !== void 0 ? _ref : 1,\n    dashed: dashed,\n    transparent: itemSize === 4\n  }, rest)));\n});\n\nexport { Line };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;AAEA,MAAM,OAAO,aAAa,GAAE,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAAE,SAAS,KAAK,EACzD,MAAM,EACN,QAAQ,QAAQ,EAChB,YAAY,EACZ,SAAS,EACT,SAAS,EACT,QAAQ,EACR,MAAM,EACN,GAAG,MACJ,EAAE,GAAG;IACJ,IAAI,gBAAgB;IACpB,MAAM,OAAO,CAAA,GAAA,gaAAA,CAAA,WAAQ,AAAD;oCAAE,CAAA,QAAS,MAAM,IAAI;;IACzC,MAAM,QAAQ,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;oCAAE,IAAM,WAAW,IAAI,gPAAA,CAAA,gBAAa,KAAK,IAAI,wOAAA,CAAA,QAAK;mCAAI;QAAC;KAAS;IAC1F,MAAM,CAAC,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAc,AAAD;8BAAE,IAAM,IAAI,+OAAA,CAAA,eAAY;;IAC5D,MAAM,WAAW,CAAC,gBAAgB,QAAQ,CAAC,iBAAiB,YAAY,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,eAAe,MAAM,MAAM,IAAI,IAAI;IACnI,MAAM,WAAW,CAAA,GAAA,4RAAA,CAAA,UAAa,AAAD;uCAAE;YAC7B,MAAM,OAAO,WAAW,IAAI,uPAAA,CAAA,uBAAoB,KAAK,IAAI,+OAAA,CAAA,eAAY;YACrE,MAAM,UAAU,OAAO,GAAG;uDAAC,CAAA;oBACzB,MAAM,UAAU,MAAM,OAAO,CAAC;oBAC9B,OAAO,aAAa,sMAAA,CAAA,UAAO,IAAI,aAAa,sMAAA,CAAA,UAAO,GAAG;wBAAC,EAAE,CAAC;wBAAE,EAAE,CAAC;wBAAE,EAAE,CAAC;qBAAC,GAAG,aAAa,sMAAA,CAAA,UAAO,GAAG;wBAAC,EAAE,CAAC;wBAAE,EAAE,CAAC;wBAAE;qBAAE,GAAG,WAAW,EAAE,MAAM,KAAK,IAAI;wBAAC,CAAC,CAAC,EAAE;wBAAE,CAAC,CAAC,EAAE;wBAAE,CAAC,CAAC,EAAE;qBAAC,GAAG,WAAW,EAAE,MAAM,KAAK,IAAI;wBAAC,CAAC,CAAC,EAAE;wBAAE,CAAC,CAAC,EAAE;wBAAE;qBAAE,GAAG;gBAChN;;YACA,KAAK,YAAY,CAAC,QAAQ,IAAI;YAC9B,IAAI,cAAc;gBAChB,oEAAoE;gBACpE,QAAQ;gBACR,MAAM,UAAU,aAAa,GAAG;2DAAC,CAAA,IAAK,aAAa,sMAAA,CAAA,QAAK,GAAG,EAAE,OAAO,KAAK;;gBACzE,KAAK,SAAS,CAAC,QAAQ,IAAI,IAAI;YACjC;YACA,OAAO;QACT;sCAAG;QAAC;QAAQ;QAAU;QAAc;KAAS;IAC7C,CAAA,GAAA,4RAAA,CAAA,kBAAqB,AAAD;qCAAE;YACpB,MAAM,oBAAoB;QAC5B;oCAAG;QAAC;QAAQ;KAAM;IAClB,CAAA,GAAA,4RAAA,CAAA,kBAAqB,AAAD;qCAAE;YACpB,IAAI,QAAQ;gBACV,aAAa,OAAO,CAAC,QAAQ,GAAG;YAClC,OAAO;gBACL,mFAAmF;gBACnF,OAAO,aAAa,OAAO,CAAC,QAAQ;YACtC;YACA,aAAa,WAAW,GAAG;QAC7B;oCAAG;QAAC;QAAQ;KAAa;IACzB,CAAA,GAAA,4RAAA,CAAA,YAAe,AAAD;+BAAE;YACd;uCAAO;oBACL,SAAS,OAAO;oBAChB,aAAa,OAAO;gBACtB;;QACF;8BAAG;QAAC;KAAS;IACb,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,aAAa,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;QAC5D,QAAQ;QACR,KAAK;IACP,GAAG,OAAO,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,aAAa;QACtD,QAAQ;QACR,QAAQ;IACV,IAAI,WAAW,GAAE,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,aAAa,CAAA,GAAA,kOAAA,CAAA,UAAQ,AAAD,EAAE;QACzD,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,cAAc,QAAQ;QACtB,YAAY;YAAC,KAAK,KAAK;YAAE,KAAK,MAAM;SAAC;QACrC,WAAW,CAAC,OAAO,cAAc,QAAQ,cAAc,KAAK,IAAI,YAAY,SAAS,MAAM,QAAQ,SAAS,KAAK,IAAI,OAAO;QAC5H,QAAQ;QACR,aAAa,aAAa;IAC5B,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3698, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3704, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-spring%2Brafz%4010.0.1/node_modules/%40react-spring/rafz/dist/react-spring_rafz.modern.mjs"], "sourcesContent": ["// src/index.ts\nvar updateQueue = makeQueue();\nvar raf = (fn) => schedule(fn, updateQueue);\nvar writeQueue = makeQueue();\nraf.write = (fn) => schedule(fn, writeQueue);\nvar onStartQueue = makeQueue();\nraf.onStart = (fn) => schedule(fn, onStartQueue);\nvar onFrameQueue = makeQueue();\nraf.onFrame = (fn) => schedule(fn, onFrameQueue);\nvar onFinishQueue = makeQueue();\nraf.onFinish = (fn) => schedule(fn, onFinishQueue);\nvar timeouts = [];\nraf.setTimeout = (handler, ms) => {\n  const time = raf.now() + ms;\n  const cancel = () => {\n    const i = timeouts.findIndex((t) => t.cancel == cancel);\n    if (~i) timeouts.splice(i, 1);\n    pendingCount -= ~i ? 1 : 0;\n  };\n  const timeout = { time, handler, cancel };\n  timeouts.splice(findTimeout(time), 0, timeout);\n  pendingCount += 1;\n  start();\n  return timeout;\n};\nvar findTimeout = (time) => ~(~timeouts.findIndex((t) => t.time > time) || ~timeouts.length);\nraf.cancel = (fn) => {\n  onStartQueue.delete(fn);\n  onFrameQueue.delete(fn);\n  onFinishQueue.delete(fn);\n  updateQueue.delete(fn);\n  writeQueue.delete(fn);\n};\nraf.sync = (fn) => {\n  sync = true;\n  raf.batchedUpdates(fn);\n  sync = false;\n};\nraf.throttle = (fn) => {\n  let lastArgs;\n  function queuedFn() {\n    try {\n      fn(...lastArgs);\n    } finally {\n      lastArgs = null;\n    }\n  }\n  function throttled(...args) {\n    lastArgs = args;\n    raf.onStart(queuedFn);\n  }\n  throttled.handler = fn;\n  throttled.cancel = () => {\n    onStartQueue.delete(queuedFn);\n    lastArgs = null;\n  };\n  return throttled;\n};\nvar nativeRaf = typeof window != \"undefined\" ? window.requestAnimationFrame : (\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  () => {\n  }\n);\nraf.use = (impl) => nativeRaf = impl;\nraf.now = typeof performance != \"undefined\" ? () => performance.now() : Date.now;\nraf.batchedUpdates = (fn) => fn();\nraf.catch = console.error;\nraf.frameLoop = \"always\";\nraf.advance = () => {\n  if (raf.frameLoop !== \"demand\") {\n    console.warn(\n      \"Cannot call the manual advancement of rafz whilst frameLoop is not set as demand\"\n    );\n  } else {\n    update();\n  }\n};\nvar ts = -1;\nvar pendingCount = 0;\nvar sync = false;\nfunction schedule(fn, queue) {\n  if (sync) {\n    queue.delete(fn);\n    fn(0);\n  } else {\n    queue.add(fn);\n    start();\n  }\n}\nfunction start() {\n  if (ts < 0) {\n    ts = 0;\n    if (raf.frameLoop !== \"demand\") {\n      nativeRaf(loop);\n    }\n  }\n}\nfunction stop() {\n  ts = -1;\n}\nfunction loop() {\n  if (~ts) {\n    nativeRaf(loop);\n    raf.batchedUpdates(update);\n  }\n}\nfunction update() {\n  const prevTs = ts;\n  ts = raf.now();\n  const count = findTimeout(ts);\n  if (count) {\n    eachSafely(timeouts.splice(0, count), (t) => t.handler());\n    pendingCount -= count;\n  }\n  if (!pendingCount) {\n    stop();\n    return;\n  }\n  onStartQueue.flush();\n  updateQueue.flush(prevTs ? Math.min(64, ts - prevTs) : 16.667);\n  onFrameQueue.flush();\n  writeQueue.flush();\n  onFinishQueue.flush();\n}\nfunction makeQueue() {\n  let next = /* @__PURE__ */ new Set();\n  let current = next;\n  return {\n    add(fn) {\n      pendingCount += current == next && !next.has(fn) ? 1 : 0;\n      next.add(fn);\n    },\n    delete(fn) {\n      pendingCount -= current == next && next.has(fn) ? 1 : 0;\n      return next.delete(fn);\n    },\n    flush(arg) {\n      if (current.size) {\n        next = /* @__PURE__ */ new Set();\n        pendingCount -= current.size;\n        eachSafely(current, (fn) => fn(arg) && next.add(fn));\n        pendingCount += next.size;\n        current = next;\n      }\n    }\n  };\n}\nfunction eachSafely(values, each) {\n  values.forEach((value) => {\n    try {\n      each(value);\n    } catch (e) {\n      raf.catch(e);\n    }\n  });\n}\nvar __raf = {\n  /** The number of pending tasks */\n  count() {\n    return pendingCount;\n  },\n  /** Whether there's a raf update loop running */\n  isRunning() {\n    return ts >= 0;\n  },\n  /** Clear internal state. Never call from update loop! */\n  clear() {\n    ts = -1;\n    timeouts = [];\n    onStartQueue = makeQueue();\n    updateQueue = makeQueue();\n    onFrameQueue = makeQueue();\n    writeQueue = makeQueue();\n    onFinishQueue = makeQueue();\n    pendingCount = 0;\n  }\n};\nexport {\n  __raf,\n  raf\n};\n"], "names": [], "mappings": "AAAA,eAAe;;;;;AACf,IAAI,cAAc;AAClB,IAAI,MAAM,CAAC,KAAO,SAAS,IAAI;AAC/B,IAAI,aAAa;AACjB,IAAI,KAAK,GAAG,CAAC,KAAO,SAAS,IAAI;AACjC,IAAI,eAAe;AACnB,IAAI,OAAO,GAAG,CAAC,KAAO,SAAS,IAAI;AACnC,IAAI,eAAe;AACnB,IAAI,OAAO,GAAG,CAAC,KAAO,SAAS,IAAI;AACnC,IAAI,gBAAgB;AACpB,IAAI,QAAQ,GAAG,CAAC,KAAO,SAAS,IAAI;AACpC,IAAI,WAAW,EAAE;AACjB,IAAI,UAAU,GAAG,CAAC,SAAS;IACzB,MAAM,OAAO,IAAI,GAAG,KAAK;IACzB,MAAM,SAAS;QACb,MAAM,IAAI,SAAS,SAAS,CAAC,CAAC,IAAM,EAAE,MAAM,IAAI;QAChD,IAAI,CAAC,GAAG,SAAS,MAAM,CAAC,GAAG;QAC3B,gBAAgB,CAAC,IAAI,IAAI;IAC3B;IACA,MAAM,UAAU;QAAE;QAAM;QAAS;IAAO;IACxC,SAAS,MAAM,CAAC,YAAY,OAAO,GAAG;IACtC,gBAAgB;IAChB;IACA,OAAO;AACT;AACA,IAAI,cAAc,CAAC,OAAS,CAAC,CAAC,CAAC,SAAS,SAAS,CAAC,CAAC,IAAM,EAAE,IAAI,GAAG,SAAS,CAAC,SAAS,MAAM;AAC3F,IAAI,MAAM,GAAG,CAAC;IACZ,aAAa,MAAM,CAAC;IACpB,aAAa,MAAM,CAAC;IACpB,cAAc,MAAM,CAAC;IACrB,YAAY,MAAM,CAAC;IACnB,WAAW,MAAM,CAAC;AACpB;AACA,IAAI,IAAI,GAAG,CAAC;IACV,OAAO;IACP,IAAI,cAAc,CAAC;IACnB,OAAO;AACT;AACA,IAAI,QAAQ,GAAG,CAAC;IACd,IAAI;IACJ,SAAS;QACP,IAAI;YACF,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IACA,SAAS,UAAU,GAAG,IAAI;QACxB,WAAW;QACX,IAAI,OAAO,CAAC;IACd;IACA,UAAU,OAAO,GAAG;IACpB,UAAU,MAAM,GAAG;QACjB,aAAa,MAAM,CAAC;QACpB,WAAW;IACb;IACA,OAAO;AACT;AACA,IAAI,YAAY,OAAO,UAAU,cAAc,OAAO,qBAAqB,GACzE,gEAAgE;AAChE,KACA;AAEF,IAAI,GAAG,GAAG,CAAC,OAAS,YAAY;AAChC,IAAI,GAAG,GAAG,OAAO,eAAe,cAAc,IAAM,YAAY,GAAG,KAAK,KAAK,GAAG;AAChF,IAAI,cAAc,GAAG,CAAC,KAAO;AAC7B,IAAI,KAAK,GAAG,QAAQ,KAAK;AACzB,IAAI,SAAS,GAAG;AAChB,IAAI,OAAO,GAAG;IACZ,IAAI,IAAI,SAAS,KAAK,UAAU;QAC9B,QAAQ,IAAI,CACV;IAEJ,OAAO;QACL;IACF;AACF;AACA,IAAI,KAAK,CAAC;AACV,IAAI,eAAe;AACnB,IAAI,OAAO;AACX,SAAS,SAAS,EAAE,EAAE,KAAK;IACzB,IAAI,MAAM;QACR,MAAM,MAAM,CAAC;QACb,GAAG;IACL,OAAO;QACL,MAAM,GAAG,CAAC;QACV;IACF;AACF;AACA,SAAS;IACP,IAAI,KAAK,GAAG;QACV,KAAK;QACL,IAAI,IAAI,SAAS,KAAK,UAAU;YAC9B,UAAU;QACZ;IACF;AACF;AACA,SAAS;IACP,KAAK,CAAC;AACR;AACA,SAAS;IACP,IAAI,CAAC,IAAI;QACP,UAAU;QACV,IAAI,cAAc,CAAC;IACrB;AACF;AACA,SAAS;IACP,MAAM,SAAS;IACf,KAAK,IAAI,GAAG;IACZ,MAAM,QAAQ,YAAY;IAC1B,IAAI,OAAO;QACT,WAAW,SAAS,MAAM,CAAC,GAAG,QAAQ,CAAC,IAAM,EAAE,OAAO;QACtD,gBAAgB;IAClB;IACA,IAAI,CAAC,cAAc;QACjB;QACA;IACF;IACA,aAAa,KAAK;IAClB,YAAY,KAAK,CAAC,SAAS,KAAK,GAAG,CAAC,IAAI,KAAK,UAAU;IACvD,aAAa,KAAK;IAClB,WAAW,KAAK;IAChB,cAAc,KAAK;AACrB;AACA,SAAS;IACP,IAAI,OAAO,aAAa,GAAG,IAAI;IAC/B,IAAI,UAAU;IACd,OAAO;QACL,KAAI,EAAE;YACJ,gBAAgB,WAAW,QAAQ,CAAC,KAAK,GAAG,CAAC,MAAM,IAAI;YACvD,KAAK,GAAG,CAAC;QACX;QACA,QAAO,EAAE;YACP,gBAAgB,WAAW,QAAQ,KAAK,GAAG,CAAC,MAAM,IAAI;YACtD,OAAO,KAAK,MAAM,CAAC;QACrB;QACA,OAAM,GAAG;YACP,IAAI,QAAQ,IAAI,EAAE;gBAChB,OAAO,aAAa,GAAG,IAAI;gBAC3B,gBAAgB,QAAQ,IAAI;gBAC5B,WAAW,SAAS,CAAC,KAAO,GAAG,QAAQ,KAAK,GAAG,CAAC;gBAChD,gBAAgB,KAAK,IAAI;gBACzB,UAAU;YACZ;QACF;IACF;AACF;AACA,SAAS,WAAW,MAAM,EAAE,IAAI;IAC9B,OAAO,OAAO,CAAC,CAAC;QACd,IAAI;YACF,KAAK;QACP,EAAE,OAAO,GAAG;YACV,IAAI,KAAK,CAAC;QACZ;IACF;AACF;AACA,IAAI,QAAQ;IACV,gCAAgC,GAChC;QACE,OAAO;IACT;IACA,8CAA8C,GAC9C;QACE,OAAO,MAAM;IACf;IACA,uDAAuD,GACvD;QACE,KAAK,CAAC;QACN,WAAW,EAAE;QACb,eAAe;QACf,cAAc;QACd,eAAe;QACf,aAAa;QACb,gBAAgB;QAChB,eAAe;IACjB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3882, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3888, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-spring%2Bshared%4010.0.1_react%4019.0.0/node_modules/%40react-spring/shared/dist/react-spring_shared.modern.mjs"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// src/globals.ts\nvar globals_exports = {};\n__export(globals_exports, {\n  assign: () => assign,\n  colors: () => colors,\n  createStringInterpolator: () => createStringInterpolator,\n  skipAnimation: () => skipAnimation,\n  to: () => to,\n  willAdvance: () => willAdvance\n});\nimport { raf } from \"@react-spring/rafz\";\n\n// src/helpers.ts\nfunction noop() {\n}\nvar defineHidden = (obj, key, value) => Object.defineProperty(obj, key, { value, writable: true, configurable: true });\nvar is = {\n  arr: Array.isArray,\n  obj: (a) => !!a && a.constructor.name === \"Object\",\n  fun: (a) => typeof a === \"function\",\n  str: (a) => typeof a === \"string\",\n  num: (a) => typeof a === \"number\",\n  und: (a) => a === void 0\n};\nfunction isEqual(a, b) {\n  if (is.arr(a)) {\n    if (!is.arr(b) || a.length !== b.length) return false;\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] !== b[i]) return false;\n    }\n    return true;\n  }\n  return a === b;\n}\nvar each = (obj, fn) => obj.forEach(fn);\nfunction eachProp(obj, fn, ctx) {\n  if (is.arr(obj)) {\n    for (let i = 0; i < obj.length; i++) {\n      fn.call(ctx, obj[i], `${i}`);\n    }\n    return;\n  }\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      fn.call(ctx, obj[key], key);\n    }\n  }\n}\nvar toArray = (a) => is.und(a) ? [] : is.arr(a) ? a : [a];\nfunction flush(queue, iterator) {\n  if (queue.size) {\n    const items = Array.from(queue);\n    queue.clear();\n    each(items, iterator);\n  }\n}\nvar flushCalls = (queue, ...args) => flush(queue, (fn) => fn(...args));\nvar isSSR = () => typeof window === \"undefined\" || !window.navigator || /ServerSideRendering|^Deno\\//.test(window.navigator.userAgent);\n\n// src/globals.ts\nvar createStringInterpolator;\nvar to;\nvar colors = null;\nvar skipAnimation = false;\nvar willAdvance = noop;\nvar assign = (globals) => {\n  if (globals.to) to = globals.to;\n  if (globals.now) raf.now = globals.now;\n  if (globals.colors !== void 0) colors = globals.colors;\n  if (globals.skipAnimation != null) skipAnimation = globals.skipAnimation;\n  if (globals.createStringInterpolator)\n    createStringInterpolator = globals.createStringInterpolator;\n  if (globals.requestAnimationFrame) raf.use(globals.requestAnimationFrame);\n  if (globals.batchedUpdates) raf.batchedUpdates = globals.batchedUpdates;\n  if (globals.willAdvance) willAdvance = globals.willAdvance;\n  if (globals.frameLoop) raf.frameLoop = globals.frameLoop;\n};\n\n// src/FrameLoop.ts\nimport { raf as raf2 } from \"@react-spring/rafz\";\nvar startQueue = /* @__PURE__ */ new Set();\nvar currentFrame = [];\nvar prevFrame = [];\nvar priority = 0;\nvar frameLoop = {\n  get idle() {\n    return !startQueue.size && !currentFrame.length;\n  },\n  /** Advance the given animation on every frame until idle. */\n  start(animation) {\n    if (priority > animation.priority) {\n      startQueue.add(animation);\n      raf2.onStart(flushStartQueue);\n    } else {\n      startSafely(animation);\n      raf2(advance);\n    }\n  },\n  /** Advance all animations by the given time. */\n  advance,\n  /** Call this when an animation's priority changes. */\n  sort(animation) {\n    if (priority) {\n      raf2.onFrame(() => frameLoop.sort(animation));\n    } else {\n      const prevIndex = currentFrame.indexOf(animation);\n      if (~prevIndex) {\n        currentFrame.splice(prevIndex, 1);\n        startUnsafely(animation);\n      }\n    }\n  },\n  /**\n   * Clear all animations. For testing purposes.\n   *\n   * ☠️ Never call this from within the frameloop.\n   */\n  clear() {\n    currentFrame = [];\n    startQueue.clear();\n  }\n};\nfunction flushStartQueue() {\n  startQueue.forEach(startSafely);\n  startQueue.clear();\n  raf2(advance);\n}\nfunction startSafely(animation) {\n  if (!currentFrame.includes(animation)) startUnsafely(animation);\n}\nfunction startUnsafely(animation) {\n  currentFrame.splice(\n    findIndex(currentFrame, (other) => other.priority > animation.priority),\n    0,\n    animation\n  );\n}\nfunction advance(dt) {\n  const nextFrame = prevFrame;\n  for (let i = 0; i < currentFrame.length; i++) {\n    const animation = currentFrame[i];\n    priority = animation.priority;\n    if (!animation.idle) {\n      willAdvance(animation);\n      animation.advance(dt);\n      if (!animation.idle) {\n        nextFrame.push(animation);\n      }\n    }\n  }\n  priority = 0;\n  prevFrame = currentFrame;\n  prevFrame.length = 0;\n  currentFrame = nextFrame;\n  return currentFrame.length > 0;\n}\nfunction findIndex(arr, test) {\n  const index = arr.findIndex(test);\n  return index < 0 ? arr.length : index;\n}\n\n// src/clamp.ts\nvar clamp = (min, max, v) => Math.min(Math.max(v, min), max);\n\n// src/colors.ts\nvar colors2 = {\n  transparent: 0,\n  aliceblue: 4042850303,\n  antiquewhite: 4209760255,\n  aqua: 16777215,\n  aquamarine: 2147472639,\n  azure: 4043309055,\n  beige: 4126530815,\n  bisque: 4293182719,\n  black: 255,\n  blanchedalmond: 4293643775,\n  blue: 65535,\n  blueviolet: 2318131967,\n  brown: 2771004159,\n  burlywood: 3736635391,\n  burntsienna: 3934150143,\n  cadetblue: 1604231423,\n  chartreuse: 2147418367,\n  chocolate: 3530104575,\n  coral: 4286533887,\n  cornflowerblue: 1687547391,\n  cornsilk: 4294499583,\n  crimson: 3692313855,\n  cyan: 16777215,\n  darkblue: 35839,\n  darkcyan: 9145343,\n  darkgoldenrod: 3095792639,\n  darkgray: 2846468607,\n  darkgreen: 6553855,\n  darkgrey: 2846468607,\n  darkkhaki: 3182914559,\n  darkmagenta: 2332068863,\n  darkolivegreen: 1433087999,\n  darkorange: 4287365375,\n  darkorchid: 2570243327,\n  darkred: 2332033279,\n  darksalmon: 3918953215,\n  darkseagreen: 2411499519,\n  darkslateblue: 1211993087,\n  darkslategray: 793726975,\n  darkslategrey: 793726975,\n  darkturquoise: 13554175,\n  darkviolet: 2483082239,\n  deeppink: 4279538687,\n  deepskyblue: 12582911,\n  dimgray: 1768516095,\n  dimgrey: 1768516095,\n  dodgerblue: 512819199,\n  firebrick: 2988581631,\n  floralwhite: 4294635775,\n  forestgreen: 579543807,\n  fuchsia: 4278255615,\n  gainsboro: 3705462015,\n  ghostwhite: 4177068031,\n  gold: 4292280575,\n  goldenrod: 3668254975,\n  gray: 2155905279,\n  green: 8388863,\n  greenyellow: 2919182335,\n  grey: 2155905279,\n  honeydew: 4043305215,\n  hotpink: 4285117695,\n  indianred: 3445382399,\n  indigo: 1258324735,\n  ivory: 4294963455,\n  khaki: 4041641215,\n  lavender: 3873897215,\n  lavenderblush: 4293981695,\n  lawngreen: 2096890111,\n  lemonchiffon: 4294626815,\n  lightblue: 2916673279,\n  lightcoral: 4034953471,\n  lightcyan: 3774873599,\n  lightgoldenrodyellow: 4210742015,\n  lightgray: 3553874943,\n  lightgreen: 2431553791,\n  lightgrey: 3553874943,\n  lightpink: 4290167295,\n  lightsalmon: 4288707327,\n  lightseagreen: 548580095,\n  lightskyblue: 2278488831,\n  lightslategray: 2005441023,\n  lightslategrey: 2005441023,\n  lightsteelblue: 2965692159,\n  lightyellow: 4294959359,\n  lime: 16711935,\n  limegreen: 852308735,\n  linen: 4210091775,\n  magenta: 4278255615,\n  maroon: 2147483903,\n  mediumaquamarine: 1724754687,\n  mediumblue: 52735,\n  mediumorchid: 3126187007,\n  mediumpurple: 2473647103,\n  mediumseagreen: 1018393087,\n  mediumslateblue: 2070474495,\n  mediumspringgreen: 16423679,\n  mediumturquoise: 1221709055,\n  mediumvioletred: 3340076543,\n  midnightblue: 421097727,\n  mintcream: 4127193855,\n  mistyrose: 4293190143,\n  moccasin: 4293178879,\n  navajowhite: 4292783615,\n  navy: 33023,\n  oldlace: 4260751103,\n  olive: 2155872511,\n  olivedrab: 1804477439,\n  orange: 4289003775,\n  orangered: 4282712319,\n  orchid: 3664828159,\n  palegoldenrod: 4008225535,\n  palegreen: 2566625535,\n  paleturquoise: 2951671551,\n  palevioletred: 3681588223,\n  papayawhip: 4293907967,\n  peachpuff: 4292524543,\n  peru: 3448061951,\n  pink: 4290825215,\n  plum: 3718307327,\n  powderblue: 2967529215,\n  purple: 2147516671,\n  rebeccapurple: 1714657791,\n  red: 4278190335,\n  rosybrown: 3163525119,\n  royalblue: 1097458175,\n  saddlebrown: 2336560127,\n  salmon: 4202722047,\n  sandybrown: 4104413439,\n  seagreen: 780883967,\n  seashell: 4294307583,\n  sienna: 2689740287,\n  silver: 3233857791,\n  skyblue: 2278484991,\n  slateblue: 1784335871,\n  slategray: 1887473919,\n  slategrey: 1887473919,\n  snow: 4294638335,\n  springgreen: 16744447,\n  steelblue: 1182971135,\n  tan: 3535047935,\n  teal: 8421631,\n  thistle: 3636451583,\n  tomato: 4284696575,\n  turquoise: 1088475391,\n  violet: 4001558271,\n  wheat: 4125012991,\n  white: 4294967295,\n  whitesmoke: 4126537215,\n  yellow: 4294902015,\n  yellowgreen: 2597139199\n};\n\n// src/colorMatchers.ts\nvar NUMBER = \"[-+]?\\\\d*\\\\.?\\\\d+\";\nvar PERCENTAGE = NUMBER + \"%\";\nfunction call(...parts) {\n  return \"\\\\(\\\\s*(\" + parts.join(\")\\\\s*,\\\\s*(\") + \")\\\\s*\\\\)\";\n}\nvar rgb = new RegExp(\"rgb\" + call(NUMBER, NUMBER, NUMBER));\nvar rgba = new RegExp(\"rgba\" + call(NUMBER, NUMBER, NUMBER, NUMBER));\nvar hsl = new RegExp(\"hsl\" + call(NUMBER, PERCENTAGE, PERCENTAGE));\nvar hsla = new RegExp(\n  \"hsla\" + call(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER)\n);\nvar hex3 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;\nvar hex4 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;\nvar hex6 = /^#([0-9a-fA-F]{6})$/;\nvar hex8 = /^#([0-9a-fA-F]{8})$/;\n\n// src/normalizeColor.ts\nfunction normalizeColor(color) {\n  let match;\n  if (typeof color === \"number\") {\n    return color >>> 0 === color && color >= 0 && color <= 4294967295 ? color : null;\n  }\n  if (match = hex6.exec(color))\n    return parseInt(match[1] + \"ff\", 16) >>> 0;\n  if (colors && colors[color] !== void 0) {\n    return colors[color];\n  }\n  if (match = rgb.exec(color)) {\n    return (parse255(match[1]) << 24 | // r\n    parse255(match[2]) << 16 | // g\n    parse255(match[3]) << 8 | // b\n    255) >>> // a\n    0;\n  }\n  if (match = rgba.exec(color)) {\n    return (parse255(match[1]) << 24 | // r\n    parse255(match[2]) << 16 | // g\n    parse255(match[3]) << 8 | // b\n    parse1(match[4])) >>> // a\n    0;\n  }\n  if (match = hex3.exec(color)) {\n    return parseInt(\n      match[1] + match[1] + // r\n      match[2] + match[2] + // g\n      match[3] + match[3] + // b\n      \"ff\",\n      // a\n      16\n    ) >>> 0;\n  }\n  if (match = hex8.exec(color)) return parseInt(match[1], 16) >>> 0;\n  if (match = hex4.exec(color)) {\n    return parseInt(\n      match[1] + match[1] + // r\n      match[2] + match[2] + // g\n      match[3] + match[3] + // b\n      match[4] + match[4],\n      // a\n      16\n    ) >>> 0;\n  }\n  if (match = hsl.exec(color)) {\n    return (hslToRgb(\n      parse360(match[1]),\n      // h\n      parsePercentage(match[2]),\n      // s\n      parsePercentage(match[3])\n      // l\n    ) | 255) >>> // a\n    0;\n  }\n  if (match = hsla.exec(color)) {\n    return (hslToRgb(\n      parse360(match[1]),\n      // h\n      parsePercentage(match[2]),\n      // s\n      parsePercentage(match[3])\n      // l\n    ) | parse1(match[4])) >>> // a\n    0;\n  }\n  return null;\n}\nfunction hue2rgb(p, q, t) {\n  if (t < 0) t += 1;\n  if (t > 1) t -= 1;\n  if (t < 1 / 6) return p + (q - p) * 6 * t;\n  if (t < 1 / 2) return q;\n  if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n  return p;\n}\nfunction hslToRgb(h, s, l) {\n  const q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n  const p = 2 * l - q;\n  const r = hue2rgb(p, q, h + 1 / 3);\n  const g = hue2rgb(p, q, h);\n  const b = hue2rgb(p, q, h - 1 / 3);\n  return Math.round(r * 255) << 24 | Math.round(g * 255) << 16 | Math.round(b * 255) << 8;\n}\nfunction parse255(str) {\n  const int = parseInt(str, 10);\n  if (int < 0) return 0;\n  if (int > 255) return 255;\n  return int;\n}\nfunction parse360(str) {\n  const int = parseFloat(str);\n  return (int % 360 + 360) % 360 / 360;\n}\nfunction parse1(str) {\n  const num = parseFloat(str);\n  if (num < 0) return 0;\n  if (num > 1) return 255;\n  return Math.round(num * 255);\n}\nfunction parsePercentage(str) {\n  const int = parseFloat(str);\n  if (int < 0) return 0;\n  if (int > 100) return 1;\n  return int / 100;\n}\n\n// src/colorToRgba.ts\nfunction colorToRgba(input) {\n  let int32Color = normalizeColor(input);\n  if (int32Color === null) return input;\n  int32Color = int32Color || 0;\n  const r = (int32Color & 4278190080) >>> 24;\n  const g = (int32Color & 16711680) >>> 16;\n  const b = (int32Color & 65280) >>> 8;\n  const a = (int32Color & 255) / 255;\n  return `rgba(${r}, ${g}, ${b}, ${a})`;\n}\n\n// src/createInterpolator.ts\nvar createInterpolator = (range, output, extrapolate) => {\n  if (is.fun(range)) {\n    return range;\n  }\n  if (is.arr(range)) {\n    return createInterpolator({\n      range,\n      output,\n      extrapolate\n    });\n  }\n  if (is.str(range.output[0])) {\n    return createStringInterpolator(range);\n  }\n  const config = range;\n  const outputRange = config.output;\n  const inputRange = config.range || [0, 1];\n  const extrapolateLeft = config.extrapolateLeft || config.extrapolate || \"extend\";\n  const extrapolateRight = config.extrapolateRight || config.extrapolate || \"extend\";\n  const easing = config.easing || ((t) => t);\n  return (input) => {\n    const range2 = findRange(input, inputRange);\n    return interpolate(\n      input,\n      inputRange[range2],\n      inputRange[range2 + 1],\n      outputRange[range2],\n      outputRange[range2 + 1],\n      easing,\n      extrapolateLeft,\n      extrapolateRight,\n      config.map\n    );\n  };\n};\nfunction interpolate(input, inputMin, inputMax, outputMin, outputMax, easing, extrapolateLeft, extrapolateRight, map) {\n  let result = map ? map(input) : input;\n  if (result < inputMin) {\n    if (extrapolateLeft === \"identity\") return result;\n    else if (extrapolateLeft === \"clamp\") result = inputMin;\n  }\n  if (result > inputMax) {\n    if (extrapolateRight === \"identity\") return result;\n    else if (extrapolateRight === \"clamp\") result = inputMax;\n  }\n  if (outputMin === outputMax) return outputMin;\n  if (inputMin === inputMax) return input <= inputMin ? outputMin : outputMax;\n  if (inputMin === -Infinity) result = -result;\n  else if (inputMax === Infinity) result = result - inputMin;\n  else result = (result - inputMin) / (inputMax - inputMin);\n  result = easing(result);\n  if (outputMin === -Infinity) result = -result;\n  else if (outputMax === Infinity) result = result + outputMin;\n  else result = result * (outputMax - outputMin) + outputMin;\n  return result;\n}\nfunction findRange(input, inputRange) {\n  for (var i = 1; i < inputRange.length - 1; ++i)\n    if (inputRange[i] >= input) break;\n  return i - 1;\n}\n\n// src/easings.ts\nvar steps = (steps2, direction = \"end\") => (progress2) => {\n  progress2 = direction === \"end\" ? Math.min(progress2, 0.999) : Math.max(progress2, 1e-3);\n  const expanded = progress2 * steps2;\n  const rounded = direction === \"end\" ? Math.floor(expanded) : Math.ceil(expanded);\n  return clamp(0, 1, rounded / steps2);\n};\nvar c1 = 1.70158;\nvar c2 = c1 * 1.525;\nvar c3 = c1 + 1;\nvar c4 = 2 * Math.PI / 3;\nvar c5 = 2 * Math.PI / 4.5;\nvar bounceOut = (x) => {\n  const n1 = 7.5625;\n  const d1 = 2.75;\n  if (x < 1 / d1) {\n    return n1 * x * x;\n  } else if (x < 2 / d1) {\n    return n1 * (x -= 1.5 / d1) * x + 0.75;\n  } else if (x < 2.5 / d1) {\n    return n1 * (x -= 2.25 / d1) * x + 0.9375;\n  } else {\n    return n1 * (x -= 2.625 / d1) * x + 0.984375;\n  }\n};\nvar easings = {\n  linear: (x) => x,\n  easeInQuad: (x) => x * x,\n  easeOutQuad: (x) => 1 - (1 - x) * (1 - x),\n  easeInOutQuad: (x) => x < 0.5 ? 2 * x * x : 1 - Math.pow(-2 * x + 2, 2) / 2,\n  easeInCubic: (x) => x * x * x,\n  easeOutCubic: (x) => 1 - Math.pow(1 - x, 3),\n  easeInOutCubic: (x) => x < 0.5 ? 4 * x * x * x : 1 - Math.pow(-2 * x + 2, 3) / 2,\n  easeInQuart: (x) => x * x * x * x,\n  easeOutQuart: (x) => 1 - Math.pow(1 - x, 4),\n  easeInOutQuart: (x) => x < 0.5 ? 8 * x * x * x * x : 1 - Math.pow(-2 * x + 2, 4) / 2,\n  easeInQuint: (x) => x * x * x * x * x,\n  easeOutQuint: (x) => 1 - Math.pow(1 - x, 5),\n  easeInOutQuint: (x) => x < 0.5 ? 16 * x * x * x * x * x : 1 - Math.pow(-2 * x + 2, 5) / 2,\n  easeInSine: (x) => 1 - Math.cos(x * Math.PI / 2),\n  easeOutSine: (x) => Math.sin(x * Math.PI / 2),\n  easeInOutSine: (x) => -(Math.cos(Math.PI * x) - 1) / 2,\n  easeInExpo: (x) => x === 0 ? 0 : Math.pow(2, 10 * x - 10),\n  easeOutExpo: (x) => x === 1 ? 1 : 1 - Math.pow(2, -10 * x),\n  easeInOutExpo: (x) => x === 0 ? 0 : x === 1 ? 1 : x < 0.5 ? Math.pow(2, 20 * x - 10) / 2 : (2 - Math.pow(2, -20 * x + 10)) / 2,\n  easeInCirc: (x) => 1 - Math.sqrt(1 - Math.pow(x, 2)),\n  easeOutCirc: (x) => Math.sqrt(1 - Math.pow(x - 1, 2)),\n  easeInOutCirc: (x) => x < 0.5 ? (1 - Math.sqrt(1 - Math.pow(2 * x, 2))) / 2 : (Math.sqrt(1 - Math.pow(-2 * x + 2, 2)) + 1) / 2,\n  easeInBack: (x) => c3 * x * x * x - c1 * x * x,\n  easeOutBack: (x) => 1 + c3 * Math.pow(x - 1, 3) + c1 * Math.pow(x - 1, 2),\n  easeInOutBack: (x) => x < 0.5 ? Math.pow(2 * x, 2) * ((c2 + 1) * 2 * x - c2) / 2 : (Math.pow(2 * x - 2, 2) * ((c2 + 1) * (x * 2 - 2) + c2) + 2) / 2,\n  easeInElastic: (x) => x === 0 ? 0 : x === 1 ? 1 : -Math.pow(2, 10 * x - 10) * Math.sin((x * 10 - 10.75) * c4),\n  easeOutElastic: (x) => x === 0 ? 0 : x === 1 ? 1 : Math.pow(2, -10 * x) * Math.sin((x * 10 - 0.75) * c4) + 1,\n  easeInOutElastic: (x) => x === 0 ? 0 : x === 1 ? 1 : x < 0.5 ? -(Math.pow(2, 20 * x - 10) * Math.sin((20 * x - 11.125) * c5)) / 2 : Math.pow(2, -20 * x + 10) * Math.sin((20 * x - 11.125) * c5) / 2 + 1,\n  easeInBounce: (x) => 1 - bounceOut(1 - x),\n  easeOutBounce: bounceOut,\n  easeInOutBounce: (x) => x < 0.5 ? (1 - bounceOut(1 - 2 * x)) / 2 : (1 + bounceOut(2 * x - 1)) / 2,\n  steps\n};\n\n// src/fluids.ts\nvar $get = Symbol.for(\"FluidValue.get\");\nvar $observers = Symbol.for(\"FluidValue.observers\");\nvar hasFluidValue = (arg) => Boolean(arg && arg[$get]);\nvar getFluidValue = (arg) => arg && arg[$get] ? arg[$get]() : arg;\nvar getFluidObservers = (target) => target[$observers] || null;\nfunction callFluidObserver(observer2, event) {\n  if (observer2.eventObserved) {\n    observer2.eventObserved(event);\n  } else {\n    observer2(event);\n  }\n}\nfunction callFluidObservers(target, event) {\n  const observers = target[$observers];\n  if (observers) {\n    observers.forEach((observer2) => {\n      callFluidObserver(observer2, event);\n    });\n  }\n}\n$get, $observers;\nvar FluidValue = class {\n  constructor(get) {\n    if (!get && !(get = this.get)) {\n      throw Error(\"Unknown getter\");\n    }\n    setFluidGetter(this, get);\n  }\n};\nvar setFluidGetter = (target, get) => setHidden(target, $get, get);\nfunction addFluidObserver(target, observer2) {\n  if (target[$get]) {\n    let observers = target[$observers];\n    if (!observers) {\n      setHidden(target, $observers, observers = /* @__PURE__ */ new Set());\n    }\n    if (!observers.has(observer2)) {\n      observers.add(observer2);\n      if (target.observerAdded) {\n        target.observerAdded(observers.size, observer2);\n      }\n    }\n  }\n  return observer2;\n}\nfunction removeFluidObserver(target, observer2) {\n  const observers = target[$observers];\n  if (observers && observers.has(observer2)) {\n    const count = observers.size - 1;\n    if (count) {\n      observers.delete(observer2);\n    } else {\n      target[$observers] = null;\n    }\n    if (target.observerRemoved) {\n      target.observerRemoved(count, observer2);\n    }\n  }\n}\nvar setHidden = (target, key, value) => Object.defineProperty(target, key, {\n  value,\n  writable: true,\n  configurable: true\n});\n\n// src/regexs.ts\nvar numberRegex = /[+\\-]?(?:0|[1-9]\\d*)(?:\\.\\d*)?(?:[eE][+\\-]?\\d+)?/g;\nvar colorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d\\.]+%?\\))/gi;\nvar unitRegex = new RegExp(`(${numberRegex.source})(%|[a-z]+)`, \"i\");\nvar rgbaRegex = /rgba\\(([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+), ([0-9\\.-]+)\\)/gi;\nvar cssVariableRegex = /var\\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\\)/;\n\n// src/variableToRgba.ts\nvar variableToRgba = (input) => {\n  const [token, fallback] = parseCSSVariable(input);\n  if (!token || isSSR()) {\n    return input;\n  }\n  const value = window.getComputedStyle(document.documentElement).getPropertyValue(token);\n  if (value) {\n    return value.trim();\n  } else if (fallback && fallback.startsWith(\"--\")) {\n    const value2 = window.getComputedStyle(document.documentElement).getPropertyValue(fallback);\n    if (value2) {\n      return value2;\n    } else {\n      return input;\n    }\n  } else if (fallback && cssVariableRegex.test(fallback)) {\n    return variableToRgba(fallback);\n  } else if (fallback) {\n    return fallback;\n  }\n  return input;\n};\nvar parseCSSVariable = (current) => {\n  const match = cssVariableRegex.exec(current);\n  if (!match) return [,];\n  const [, token, fallback] = match;\n  return [token, fallback];\n};\n\n// src/stringInterpolation.ts\nvar namedColorRegex;\nvar rgbaRound = (_, p1, p2, p3, p4) => `rgba(${Math.round(p1)}, ${Math.round(p2)}, ${Math.round(p3)}, ${p4})`;\nvar createStringInterpolator2 = (config) => {\n  if (!namedColorRegex)\n    namedColorRegex = colors ? (\n      // match color names, ignore partial matches\n      new RegExp(`(${Object.keys(colors).join(\"|\")})(?!\\\\w)`, \"g\")\n    ) : (\n      // never match\n      /^\\b$/\n    );\n  const output = config.output.map((value) => {\n    return getFluidValue(value).replace(cssVariableRegex, variableToRgba).replace(colorRegex, colorToRgba).replace(namedColorRegex, colorToRgba);\n  });\n  const keyframes = output.map((value) => value.match(numberRegex).map(Number));\n  const outputRanges = keyframes[0].map(\n    (_, i) => keyframes.map((values) => {\n      if (!(i in values)) {\n        throw Error('The arity of each \"output\" value must be equal');\n      }\n      return values[i];\n    })\n  );\n  const interpolators = outputRanges.map(\n    (output2) => createInterpolator({ ...config, output: output2 })\n  );\n  return (input) => {\n    const missingUnit = !unitRegex.test(output[0]) && output.find((value) => unitRegex.test(value))?.replace(numberRegex, \"\");\n    let i = 0;\n    return output[0].replace(\n      numberRegex,\n      () => `${interpolators[i++](input)}${missingUnit || \"\"}`\n    ).replace(rgbaRegex, rgbaRound);\n  };\n};\n\n// src/deprecations.ts\nvar prefix = \"react-spring: \";\nvar once = (fn) => {\n  const func = fn;\n  let called = false;\n  if (typeof func != \"function\") {\n    throw new TypeError(`${prefix}once requires a function parameter`);\n  }\n  return (...args) => {\n    if (!called) {\n      func(...args);\n      called = true;\n    }\n  };\n};\nvar warnInterpolate = once(console.warn);\nfunction deprecateInterpolate() {\n  warnInterpolate(\n    `${prefix}The \"interpolate\" function is deprecated in v9 (use \"to\" instead)`\n  );\n}\nvar warnDirectCall = once(console.warn);\nfunction deprecateDirectCall() {\n  warnDirectCall(\n    `${prefix}Directly calling start instead of using the api object is deprecated in v9 (use \".start\" instead), this will be removed in later 0.X.0 versions`\n  );\n}\n\n// src/isAnimatedString.ts\nfunction isAnimatedString(value) {\n  return is.str(value) && (value[0] == \"#\" || /\\d/.test(value) || // Do not identify a CSS variable as an AnimatedString if its SSR\n  !isSSR() && cssVariableRegex.test(value) || value in (colors || {}));\n}\n\n// src/dom-events/scroll/index.ts\nimport { raf as raf3 } from \"@react-spring/rafz\";\n\n// src/dom-events/resize/resizeElement.ts\nvar observer;\nvar resizeHandlers = /* @__PURE__ */ new WeakMap();\nvar handleObservation = (entries) => entries.forEach(({ target, contentRect }) => {\n  return resizeHandlers.get(target)?.forEach((handler) => handler(contentRect));\n});\nfunction resizeElement(handler, target) {\n  if (!observer) {\n    if (typeof ResizeObserver !== \"undefined\") {\n      observer = new ResizeObserver(handleObservation);\n    }\n  }\n  let elementHandlers = resizeHandlers.get(target);\n  if (!elementHandlers) {\n    elementHandlers = /* @__PURE__ */ new Set();\n    resizeHandlers.set(target, elementHandlers);\n  }\n  elementHandlers.add(handler);\n  if (observer) {\n    observer.observe(target);\n  }\n  return () => {\n    const elementHandlers2 = resizeHandlers.get(target);\n    if (!elementHandlers2) return;\n    elementHandlers2.delete(handler);\n    if (!elementHandlers2.size && observer) {\n      observer.unobserve(target);\n    }\n  };\n}\n\n// src/dom-events/resize/resizeWindow.ts\nvar listeners = /* @__PURE__ */ new Set();\nvar cleanupWindowResizeHandler;\nvar createResizeHandler = () => {\n  const handleResize = () => {\n    listeners.forEach(\n      (callback) => callback({\n        width: window.innerWidth,\n        height: window.innerHeight\n      })\n    );\n  };\n  window.addEventListener(\"resize\", handleResize);\n  return () => {\n    window.removeEventListener(\"resize\", handleResize);\n  };\n};\nvar resizeWindow = (callback) => {\n  listeners.add(callback);\n  if (!cleanupWindowResizeHandler) {\n    cleanupWindowResizeHandler = createResizeHandler();\n  }\n  return () => {\n    listeners.delete(callback);\n    if (!listeners.size && cleanupWindowResizeHandler) {\n      cleanupWindowResizeHandler();\n      cleanupWindowResizeHandler = void 0;\n    }\n  };\n};\n\n// src/dom-events/resize/index.ts\nvar onResize = (callback, { container = document.documentElement } = {}) => {\n  if (container === document.documentElement) {\n    return resizeWindow(callback);\n  } else {\n    return resizeElement(callback, container);\n  }\n};\n\n// src/progress.ts\nvar progress = (min, max, value) => max - min === 0 ? 1 : (value - min) / (max - min);\n\n// src/dom-events/scroll/ScrollHandler.ts\nvar SCROLL_KEYS = {\n  x: {\n    length: \"Width\",\n    position: \"Left\"\n  },\n  y: {\n    length: \"Height\",\n    position: \"Top\"\n  }\n};\nvar ScrollHandler = class {\n  constructor(callback, container) {\n    this.createAxis = () => ({\n      current: 0,\n      progress: 0,\n      scrollLength: 0\n    });\n    this.updateAxis = (axisName) => {\n      const axis = this.info[axisName];\n      const { length, position } = SCROLL_KEYS[axisName];\n      axis.current = this.container[`scroll${position}`];\n      axis.scrollLength = this.container[`scroll${length}`] - this.container[`client${length}`];\n      axis.progress = progress(0, axis.scrollLength, axis.current);\n    };\n    this.update = () => {\n      this.updateAxis(\"x\");\n      this.updateAxis(\"y\");\n    };\n    this.sendEvent = () => {\n      this.callback(this.info);\n    };\n    this.advance = () => {\n      this.update();\n      this.sendEvent();\n    };\n    this.callback = callback;\n    this.container = container;\n    this.info = {\n      time: 0,\n      x: this.createAxis(),\n      y: this.createAxis()\n    };\n  }\n};\n\n// src/dom-events/scroll/index.ts\nvar scrollListeners = /* @__PURE__ */ new WeakMap();\nvar resizeListeners = /* @__PURE__ */ new WeakMap();\nvar onScrollHandlers = /* @__PURE__ */ new WeakMap();\nvar getTarget = (container) => container === document.documentElement ? window : container;\nvar onScroll = (callback, { container = document.documentElement } = {}) => {\n  let containerHandlers = onScrollHandlers.get(container);\n  if (!containerHandlers) {\n    containerHandlers = /* @__PURE__ */ new Set();\n    onScrollHandlers.set(container, containerHandlers);\n  }\n  const containerHandler = new ScrollHandler(callback, container);\n  containerHandlers.add(containerHandler);\n  if (!scrollListeners.has(container)) {\n    const listener = () => {\n      containerHandlers?.forEach((handler) => handler.advance());\n      return true;\n    };\n    scrollListeners.set(container, listener);\n    const target = getTarget(container);\n    window.addEventListener(\"resize\", listener, { passive: true });\n    if (container !== document.documentElement) {\n      resizeListeners.set(container, onResize(listener, { container }));\n    }\n    target.addEventListener(\"scroll\", listener, { passive: true });\n  }\n  const animateScroll = scrollListeners.get(container);\n  raf3(animateScroll);\n  return () => {\n    raf3.cancel(animateScroll);\n    const containerHandlers2 = onScrollHandlers.get(container);\n    if (!containerHandlers2) return;\n    containerHandlers2.delete(containerHandler);\n    if (containerHandlers2.size) return;\n    const listener = scrollListeners.get(container);\n    scrollListeners.delete(container);\n    if (listener) {\n      getTarget(container).removeEventListener(\"scroll\", listener);\n      window.removeEventListener(\"resize\", listener);\n      resizeListeners.get(container)?.();\n    }\n  };\n};\n\n// src/hooks/useConstant.ts\nimport { useRef } from \"react\";\nfunction useConstant(init) {\n  const ref = useRef(null);\n  if (ref.current === null) {\n    ref.current = init();\n  }\n  return ref.current;\n}\n\n// src/hooks/useForceUpdate.ts\nimport { useState } from \"react\";\n\n// src/hooks/useIsMounted.ts\nimport { useRef as useRef2 } from \"react\";\n\n// src/hooks/useIsomorphicLayoutEffect.ts\nimport { useEffect, useLayoutEffect } from \"react\";\nvar useIsomorphicLayoutEffect = isSSR() ? useEffect : useLayoutEffect;\n\n// src/hooks/useIsMounted.ts\nvar useIsMounted = () => {\n  const isMounted = useRef2(false);\n  useIsomorphicLayoutEffect(() => {\n    isMounted.current = true;\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n  return isMounted;\n};\n\n// src/hooks/useForceUpdate.ts\nfunction useForceUpdate() {\n  const update = useState()[1];\n  const isMounted = useIsMounted();\n  return () => {\n    if (isMounted.current) {\n      update(Math.random());\n    }\n  };\n}\n\n// src/hooks/useMemoOne.ts\nimport { useEffect as useEffect2, useRef as useRef3, useState as useState2 } from \"react\";\nfunction useMemoOne(getResult, inputs) {\n  const [initial] = useState2(\n    () => ({\n      inputs,\n      result: getResult()\n    })\n  );\n  const committed = useRef3(void 0);\n  const prevCache = committed.current;\n  let cache = prevCache;\n  if (cache) {\n    const useCache = Boolean(\n      inputs && cache.inputs && areInputsEqual(inputs, cache.inputs)\n    );\n    if (!useCache) {\n      cache = {\n        inputs,\n        result: getResult()\n      };\n    }\n  } else {\n    cache = initial;\n  }\n  useEffect2(() => {\n    committed.current = cache;\n    if (prevCache == initial) {\n      initial.inputs = initial.result = void 0;\n    }\n  }, [cache]);\n  return cache.result;\n}\nfunction areInputsEqual(next, prev) {\n  if (next.length !== prev.length) {\n    return false;\n  }\n  for (let i = 0; i < next.length; i++) {\n    if (next[i] !== prev[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// src/hooks/useOnce.ts\nimport { useEffect as useEffect3 } from \"react\";\nvar useOnce = (effect) => useEffect3(effect, emptyDeps);\nvar emptyDeps = [];\n\n// src/hooks/usePrev.ts\nimport { useEffect as useEffect4, useRef as useRef4 } from \"react\";\nfunction usePrev(value) {\n  const prevRef = useRef4(void 0);\n  useEffect4(() => {\n    prevRef.current = value;\n  });\n  return prevRef.current;\n}\n\n// src/hooks/useReducedMotion.ts\nimport { useState as useState3 } from \"react\";\nvar useReducedMotion = () => {\n  const [reducedMotion, setReducedMotion] = useState3(null);\n  useIsomorphicLayoutEffect(() => {\n    const mql = window.matchMedia(\"(prefers-reduced-motion)\");\n    const handleMediaChange = (e) => {\n      setReducedMotion(e.matches);\n      assign({\n        skipAnimation: e.matches\n      });\n    };\n    handleMediaChange(mql);\n    if (mql.addEventListener) {\n      mql.addEventListener(\"change\", handleMediaChange);\n    } else {\n      mql.addListener(handleMediaChange);\n    }\n    return () => {\n      if (mql.removeEventListener) {\n        mql.removeEventListener(\"change\", handleMediaChange);\n      } else {\n        mql.removeListener(handleMediaChange);\n      }\n    };\n  }, []);\n  return reducedMotion;\n};\n\n// src/index.ts\nimport { raf as raf4 } from \"@react-spring/rafz\";\nexport {\n  FluidValue,\n  globals_exports as Globals,\n  addFluidObserver,\n  callFluidObserver,\n  callFluidObservers,\n  clamp,\n  colorToRgba,\n  colors2 as colors,\n  createInterpolator,\n  createStringInterpolator2 as createStringInterpolator,\n  defineHidden,\n  deprecateDirectCall,\n  deprecateInterpolate,\n  each,\n  eachProp,\n  easings,\n  flush,\n  flushCalls,\n  frameLoop,\n  getFluidObservers,\n  getFluidValue,\n  hasFluidValue,\n  hex3,\n  hex4,\n  hex6,\n  hex8,\n  hsl,\n  hsla,\n  is,\n  isAnimatedString,\n  isEqual,\n  isSSR,\n  noop,\n  onResize,\n  onScroll,\n  once,\n  prefix,\n  raf4 as raf,\n  removeFluidObserver,\n  rgb,\n  rgba,\n  setFluidGetter,\n  toArray,\n  useConstant,\n  useForceUpdate,\n  useIsomorphicLayoutEffect,\n  useMemoOne,\n  useOnce,\n  usePrev,\n  useReducedMotion\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA;AA84BA,2BAA2B;AAC3B;AA/5BA,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,WAAW,CAAC,QAAQ;IACtB,IAAK,IAAI,QAAQ,IACf,UAAU,QAAQ,MAAM;QAAE,KAAK,GAAG,CAAC,KAAK;QAAE,YAAY;IAAK;AAC/D;AAEA,iBAAiB;AACjB,IAAI,kBAAkB,CAAC;AACvB,SAAS,iBAAiB;IACxB,QAAQ,IAAM;IACd,QAAQ,IAAM;IACd,0BAA0B,IAAM;IAChC,eAAe,IAAM;IACrB,IAAI,IAAM;IACV,aAAa,IAAM;AACrB;;AAGA,iBAAiB;AACjB,SAAS,QACT;AACA,IAAI,eAAe,CAAC,KAAK,KAAK,QAAU,OAAO,cAAc,CAAC,KAAK,KAAK;QAAE;QAAO,UAAU;QAAM,cAAc;IAAK;AACpH,IAAI,KAAK;IACP,KAAK,MAAM,OAAO;IAClB,KAAK,CAAC,IAAM,CAAC,CAAC,KAAK,EAAE,WAAW,CAAC,IAAI,KAAK;IAC1C,KAAK,CAAC,IAAM,OAAO,MAAM;IACzB,KAAK,CAAC,IAAM,OAAO,MAAM;IACzB,KAAK,CAAC,IAAM,OAAO,MAAM;IACzB,KAAK,CAAC,IAAM,MAAM,KAAK;AACzB;AACA,SAAS,QAAQ,CAAC,EAAE,CAAC;IACnB,IAAI,GAAG,GAAG,CAAC,IAAI;QACb,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE,OAAO;QAChD,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK;YACjC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,OAAO;QAC5B;QACA,OAAO;IACT;IACA,OAAO,MAAM;AACf;AACA,IAAI,OAAO,CAAC,KAAK,KAAO,IAAI,OAAO,CAAC;AACpC,SAAS,SAAS,GAAG,EAAE,EAAE,EAAE,GAAG;IAC5B,IAAI,GAAG,GAAG,CAAC,MAAM;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACnC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG;QAC7B;QACA;IACF;IACA,IAAK,MAAM,OAAO,IAAK;QACrB,IAAI,IAAI,cAAc,CAAC,MAAM;YAC3B,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE;QACzB;IACF;AACF;AACA,IAAI,UAAU,CAAC,IAAM,GAAG,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG,CAAC,KAAK,IAAI;QAAC;KAAE;AACzD,SAAS,MAAM,KAAK,EAAE,QAAQ;IAC5B,IAAI,MAAM,IAAI,EAAE;QACd,MAAM,QAAQ,MAAM,IAAI,CAAC;QACzB,MAAM,KAAK;QACX,KAAK,OAAO;IACd;AACF;AACA,IAAI,aAAa,CAAC,OAAO,GAAG,OAAS,MAAM,OAAO,CAAC,KAAO,MAAM;AAChE,IAAI,QAAQ,IAAM,OAAO,WAAW,eAAe,CAAC,OAAO,SAAS,IAAI,8BAA8B,IAAI,CAAC,OAAO,SAAS,CAAC,SAAS;AAErI,iBAAiB;AACjB,IAAI;AACJ,IAAI;AACJ,IAAI,SAAS;AACb,IAAI,gBAAgB;AACpB,IAAI,cAAc;AAClB,IAAI,SAAS,CAAC;IACZ,IAAI,QAAQ,EAAE,EAAE,KAAK,QAAQ,EAAE;IAC/B,IAAI,QAAQ,GAAG,EAAE,8PAAA,CAAA,MAAG,CAAC,GAAG,GAAG,QAAQ,GAAG;IACtC,IAAI,QAAQ,MAAM,KAAK,KAAK,GAAG,SAAS,QAAQ,MAAM;IACtD,IAAI,QAAQ,aAAa,IAAI,MAAM,gBAAgB,QAAQ,aAAa;IACxE,IAAI,QAAQ,wBAAwB,EAClC,2BAA2B,QAAQ,wBAAwB;IAC7D,IAAI,QAAQ,qBAAqB,EAAE,8PAAA,CAAA,MAAG,CAAC,GAAG,CAAC,QAAQ,qBAAqB;IACxE,IAAI,QAAQ,cAAc,EAAE,8PAAA,CAAA,MAAG,CAAC,cAAc,GAAG,QAAQ,cAAc;IACvE,IAAI,QAAQ,WAAW,EAAE,cAAc,QAAQ,WAAW;IAC1D,IAAI,QAAQ,SAAS,EAAE,8PAAA,CAAA,MAAG,CAAC,SAAS,GAAG,QAAQ,SAAS;AAC1D;;AAIA,IAAI,aAAa,aAAa,GAAG,IAAI;AACrC,IAAI,eAAe,EAAE;AACrB,IAAI,YAAY,EAAE;AAClB,IAAI,WAAW;AACf,IAAI,YAAY;IACd,IAAI,QAAO;QACT,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,aAAa,MAAM;IACjD;IACA,2DAA2D,GAC3D,OAAM,SAAS;QACb,IAAI,WAAW,UAAU,QAAQ,EAAE;YACjC,WAAW,GAAG,CAAC;YACf,8PAAA,CAAA,MAAI,CAAC,OAAO,CAAC;QACf,OAAO;YACL,YAAY;YACZ,CAAA,GAAA,8PAAA,CAAA,MAAI,AAAD,EAAE;QACP;IACF;IACA,8CAA8C,GAC9C;IACA,oDAAoD,GACpD,MAAK,SAAS;QACZ,IAAI,UAAU;YACZ,8PAAA,CAAA,MAAI,CAAC,OAAO,CAAC,IAAM,UAAU,IAAI,CAAC;QACpC,OAAO;YACL,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,IAAI,CAAC,WAAW;gBACd,aAAa,MAAM,CAAC,WAAW;gBAC/B,cAAc;YAChB;QACF;IACF;IACA;;;;GAIC,GACD;QACE,eAAe,EAAE;QACjB,WAAW,KAAK;IAClB;AACF;AACA,SAAS;IACP,WAAW,OAAO,CAAC;IACnB,WAAW,KAAK;IAChB,CAAA,GAAA,8PAAA,CAAA,MAAI,AAAD,EAAE;AACP;AACA,SAAS,YAAY,SAAS;IAC5B,IAAI,CAAC,aAAa,QAAQ,CAAC,YAAY,cAAc;AACvD;AACA,SAAS,cAAc,SAAS;IAC9B,aAAa,MAAM,CACjB,UAAU,cAAc,CAAC,QAAU,MAAM,QAAQ,GAAG,UAAU,QAAQ,GACtE,GACA;AAEJ;AACA,SAAS,QAAQ,EAAE;IACjB,MAAM,YAAY;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,MAAM,YAAY,YAAY,CAAC,EAAE;QACjC,WAAW,UAAU,QAAQ;QAC7B,IAAI,CAAC,UAAU,IAAI,EAAE;YACnB,YAAY;YACZ,UAAU,OAAO,CAAC;YAClB,IAAI,CAAC,UAAU,IAAI,EAAE;gBACnB,UAAU,IAAI,CAAC;YACjB;QACF;IACF;IACA,WAAW;IACX,YAAY;IACZ,UAAU,MAAM,GAAG;IACnB,eAAe;IACf,OAAO,aAAa,MAAM,GAAG;AAC/B;AACA,SAAS,UAAU,GAAG,EAAE,IAAI;IAC1B,MAAM,QAAQ,IAAI,SAAS,CAAC;IAC5B,OAAO,QAAQ,IAAI,IAAI,MAAM,GAAG;AAClC;AAEA,eAAe;AACf,IAAI,QAAQ,CAAC,KAAK,KAAK,IAAM,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,MAAM;AAExD,gBAAgB;AAChB,IAAI,UAAU;IACZ,aAAa;IACb,WAAW;IACX,cAAc;IACd,MAAM;IACN,YAAY;IACZ,OAAO;IACP,OAAO;IACP,QAAQ;IACR,OAAO;IACP,gBAAgB;IAChB,MAAM;IACN,YAAY;IACZ,OAAO;IACP,WAAW;IACX,aAAa;IACb,WAAW;IACX,YAAY;IACZ,WAAW;IACX,OAAO;IACP,gBAAgB;IAChB,UAAU;IACV,SAAS;IACT,MAAM;IACN,UAAU;IACV,UAAU;IACV,eAAe;IACf,UAAU;IACV,WAAW;IACX,UAAU;IACV,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,YAAY;IACZ,YAAY;IACZ,SAAS;IACT,YAAY;IACZ,cAAc;IACd,eAAe;IACf,eAAe;IACf,eAAe;IACf,eAAe;IACf,YAAY;IACZ,UAAU;IACV,aAAa;IACb,SAAS;IACT,SAAS;IACT,YAAY;IACZ,WAAW;IACX,aAAa;IACb,aAAa;IACb,SAAS;IACT,WAAW;IACX,YAAY;IACZ,MAAM;IACN,WAAW;IACX,MAAM;IACN,OAAO;IACP,aAAa;IACb,MAAM;IACN,UAAU;IACV,SAAS;IACT,WAAW;IACX,QAAQ;IACR,OAAO;IACP,OAAO;IACP,UAAU;IACV,eAAe;IACf,WAAW;IACX,cAAc;IACd,WAAW;IACX,YAAY;IACZ,WAAW;IACX,sBAAsB;IACtB,WAAW;IACX,YAAY;IACZ,WAAW;IACX,WAAW;IACX,aAAa;IACb,eAAe;IACf,cAAc;IACd,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,aAAa;IACb,MAAM;IACN,WAAW;IACX,OAAO;IACP,SAAS;IACT,QAAQ;IACR,kBAAkB;IAClB,YAAY;IACZ,cAAc;IACd,cAAc;IACd,gBAAgB;IAChB,iBAAiB;IACjB,mBAAmB;IACnB,iBAAiB;IACjB,iBAAiB;IACjB,cAAc;IACd,WAAW;IACX,WAAW;IACX,UAAU;IACV,aAAa;IACb,MAAM;IACN,SAAS;IACT,OAAO;IACP,WAAW;IACX,QAAQ;IACR,WAAW;IACX,QAAQ;IACR,eAAe;IACf,WAAW;IACX,eAAe;IACf,eAAe;IACf,YAAY;IACZ,WAAW;IACX,MAAM;IACN,MAAM;IACN,MAAM;IACN,YAAY;IACZ,QAAQ;IACR,eAAe;IACf,KAAK;IACL,WAAW;IACX,WAAW;IACX,aAAa;IACb,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,WAAW;IACX,WAAW;IACX,WAAW;IACX,MAAM;IACN,aAAa;IACb,WAAW;IACX,KAAK;IACL,MAAM;IACN,SAAS;IACT,QAAQ;IACR,WAAW;IACX,QAAQ;IACR,OAAO;IACP,OAAO;IACP,YAAY;IACZ,QAAQ;IACR,aAAa;AACf;AAEA,uBAAuB;AACvB,IAAI,SAAS;AACb,IAAI,aAAa,SAAS;AAC1B,SAAS,KAAK,GAAG,KAAK;IACpB,OAAO,aAAa,MAAM,IAAI,CAAC,iBAAiB;AAClD;AACA,IAAI,MAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,QAAQ;AAClD,IAAI,OAAO,IAAI,OAAO,SAAS,KAAK,QAAQ,QAAQ,QAAQ;AAC5D,IAAI,MAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,YAAY;AACtD,IAAI,OAAO,IAAI,OACb,SAAS,KAAK,QAAQ,YAAY,YAAY;AAEhD,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,OAAO;AAEX,wBAAwB;AACxB,SAAS,eAAe,KAAK;IAC3B,IAAI;IACJ,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO,UAAU,MAAM,SAAS,SAAS,KAAK,SAAS,aAAa,QAAQ;IAC9E;IACA,IAAI,QAAQ,KAAK,IAAI,CAAC,QACpB,OAAO,SAAS,KAAK,CAAC,EAAE,GAAG,MAAM,QAAQ;IAC3C,IAAI,UAAU,MAAM,CAAC,MAAM,KAAK,KAAK,GAAG;QACtC,OAAO,MAAM,CAAC,MAAM;IACtB;IACA,IAAI,QAAQ,IAAI,IAAI,CAAC,QAAQ;QAC3B,OAAO,CAAC,SAAS,KAAK,CAAC,EAAE,KAAK,KAAK,IAAI;QACvC,SAAS,KAAK,CAAC,EAAE,KAAK,KAAK,IAAI;QAC/B,SAAS,KAAK,CAAC,EAAE,KAAK,IAAI,IAAI;QAC9B,GAAG,MAAM,IAAI;QACb;IACF;IACA,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ;QAC5B,OAAO,CAAC,SAAS,KAAK,CAAC,EAAE,KAAK,KAAK,IAAI;QACvC,SAAS,KAAK,CAAC,EAAE,KAAK,KAAK,IAAI;QAC/B,SAAS,KAAK,CAAC,EAAE,KAAK,IAAI,IAAI;QAC9B,OAAO,KAAK,CAAC,EAAE,CAAC,MAAM,IAAI;QAC1B;IACF;IACA,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ;QAC5B,OAAO,SACL,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,IAAI;QAC1B,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,IAAI;QAC1B,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,IAAI;QAC1B,MACA,IAAI;QACJ,QACI;IACR;IACA,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ,OAAO,SAAS,KAAK,CAAC,EAAE,EAAE,QAAQ;IAChE,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ;QAC5B,OAAO,SACL,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,IAAI;QAC1B,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,IAAI;QAC1B,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG,IAAI;QAC1B,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,EACnB,IAAI;QACJ,QACI;IACR;IACA,IAAI,QAAQ,IAAI,IAAI,CAAC,QAAQ;QAC3B,OAAO,CAAC,SACN,SAAS,KAAK,CAAC,EAAE,GACjB,IAAI;QACJ,gBAAgB,KAAK,CAAC,EAAE,GACxB,IAAI;QACJ,gBAAgB,KAAK,CAAC,EAAE,KAEtB,GAAG,MAAM,IAAI;QACjB;IACF;IACA,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAQ;QAC5B,OAAO,CAAC,SACN,SAAS,KAAK,CAAC,EAAE,GACjB,IAAI;QACJ,gBAAgB,KAAK,CAAC,EAAE,GACxB,IAAI;QACJ,gBAAgB,KAAK,CAAC,EAAE,KAEtB,OAAO,KAAK,CAAC,EAAE,CAAC,MAAM,IAAI;QAC9B;IACF;IACA,OAAO;AACT;AACA,SAAS,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;IACtB,IAAI,IAAI,GAAG,KAAK;IAChB,IAAI,IAAI,GAAG,KAAK;IAChB,IAAI,IAAI,IAAI,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACxC,IAAI,IAAI,IAAI,GAAG,OAAO;IACtB,IAAI,IAAI,IAAI,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI;IAClD,OAAO;AACT;AACA,SAAS,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC;IACvB,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI;IAC9C,MAAM,IAAI,IAAI,IAAI;IAClB,MAAM,IAAI,QAAQ,GAAG,GAAG,IAAI,IAAI;IAChC,MAAM,IAAI,QAAQ,GAAG,GAAG;IACxB,MAAM,IAAI,QAAQ,GAAG,GAAG,IAAI,IAAI;IAChC,OAAO,KAAK,KAAK,CAAC,IAAI,QAAQ,KAAK,KAAK,KAAK,CAAC,IAAI,QAAQ,KAAK,KAAK,KAAK,CAAC,IAAI,QAAQ;AACxF;AACA,SAAS,SAAS,GAAG;IACnB,MAAM,MAAM,SAAS,KAAK;IAC1B,IAAI,MAAM,GAAG,OAAO;IACpB,IAAI,MAAM,KAAK,OAAO;IACtB,OAAO;AACT;AACA,SAAS,SAAS,GAAG;IACnB,MAAM,MAAM,WAAW;IACvB,OAAO,CAAC,MAAM,MAAM,GAAG,IAAI,MAAM;AACnC;AACA,SAAS,OAAO,GAAG;IACjB,MAAM,MAAM,WAAW;IACvB,IAAI,MAAM,GAAG,OAAO;IACpB,IAAI,MAAM,GAAG,OAAO;IACpB,OAAO,KAAK,KAAK,CAAC,MAAM;AAC1B;AACA,SAAS,gBAAgB,GAAG;IAC1B,MAAM,MAAM,WAAW;IACvB,IAAI,MAAM,GAAG,OAAO;IACpB,IAAI,MAAM,KAAK,OAAO;IACtB,OAAO,MAAM;AACf;AAEA,qBAAqB;AACrB,SAAS,YAAY,KAAK;IACxB,IAAI,aAAa,eAAe;IAChC,IAAI,eAAe,MAAM,OAAO;IAChC,aAAa,cAAc;IAC3B,MAAM,IAAI,CAAC,aAAa,UAAU,MAAM;IACxC,MAAM,IAAI,CAAC,aAAa,QAAQ,MAAM;IACtC,MAAM,IAAI,CAAC,aAAa,KAAK,MAAM;IACnC,MAAM,IAAI,CAAC,aAAa,GAAG,IAAI;IAC/B,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACvC;AAEA,4BAA4B;AAC5B,IAAI,qBAAqB,CAAC,OAAO,QAAQ;IACvC,IAAI,GAAG,GAAG,CAAC,QAAQ;QACjB,OAAO;IACT;IACA,IAAI,GAAG,GAAG,CAAC,QAAQ;QACjB,OAAO,mBAAmB;YACxB;YACA;YACA;QACF;IACF;IACA,IAAI,GAAG,GAAG,CAAC,MAAM,MAAM,CAAC,EAAE,GAAG;QAC3B,OAAO,yBAAyB;IAClC;IACA,MAAM,SAAS;IACf,MAAM,cAAc,OAAO,MAAM;IACjC,MAAM,aAAa,OAAO,KAAK,IAAI;QAAC;QAAG;KAAE;IACzC,MAAM,kBAAkB,OAAO,eAAe,IAAI,OAAO,WAAW,IAAI;IACxE,MAAM,mBAAmB,OAAO,gBAAgB,IAAI,OAAO,WAAW,IAAI;IAC1E,MAAM,SAAS,OAAO,MAAM,IAAI,CAAC,CAAC,IAAM,CAAC;IACzC,OAAO,CAAC;QACN,MAAM,SAAS,UAAU,OAAO;QAChC,OAAO,YACL,OACA,UAAU,CAAC,OAAO,EAClB,UAAU,CAAC,SAAS,EAAE,EACtB,WAAW,CAAC,OAAO,EACnB,WAAW,CAAC,SAAS,EAAE,EACvB,QACA,iBACA,kBACA,OAAO,GAAG;IAEd;AACF;AACA,SAAS,YAAY,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAAG;IAClH,IAAI,SAAS,MAAM,IAAI,SAAS;IAChC,IAAI,SAAS,UAAU;QACrB,IAAI,oBAAoB,YAAY,OAAO;aACtC,IAAI,oBAAoB,SAAS,SAAS;IACjD;IACA,IAAI,SAAS,UAAU;QACrB,IAAI,qBAAqB,YAAY,OAAO;aACvC,IAAI,qBAAqB,SAAS,SAAS;IAClD;IACA,IAAI,cAAc,WAAW,OAAO;IACpC,IAAI,aAAa,UAAU,OAAO,SAAS,WAAW,YAAY;IAClE,IAAI,aAAa,CAAC,UAAU,SAAS,CAAC;SACjC,IAAI,aAAa,UAAU,SAAS,SAAS;SAC7C,SAAS,CAAC,SAAS,QAAQ,IAAI,CAAC,WAAW,QAAQ;IACxD,SAAS,OAAO;IAChB,IAAI,cAAc,CAAC,UAAU,SAAS,CAAC;SAClC,IAAI,cAAc,UAAU,SAAS,SAAS;SAC9C,SAAS,SAAS,CAAC,YAAY,SAAS,IAAI;IACjD,OAAO;AACT;AACA,SAAS,UAAU,KAAK,EAAE,UAAU;IAClC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,GAAG,GAAG,EAAE,EAC3C,IAAI,UAAU,CAAC,EAAE,IAAI,OAAO;IAC9B,OAAO,IAAI;AACb;AAEA,iBAAiB;AACjB,IAAI,QAAQ,CAAC,QAAQ,YAAY,KAAK,GAAK,CAAC;QAC1C,YAAY,cAAc,QAAQ,KAAK,GAAG,CAAC,WAAW,SAAS,KAAK,GAAG,CAAC,WAAW;QACnF,MAAM,WAAW,YAAY;QAC7B,MAAM,UAAU,cAAc,QAAQ,KAAK,KAAK,CAAC,YAAY,KAAK,IAAI,CAAC;QACvE,OAAO,MAAM,GAAG,GAAG,UAAU;IAC/B;AACA,IAAI,KAAK;AACT,IAAI,KAAK,KAAK;AACd,IAAI,KAAK,KAAK;AACd,IAAI,KAAK,IAAI,KAAK,EAAE,GAAG;AACvB,IAAI,KAAK,IAAI,KAAK,EAAE,GAAG;AACvB,IAAI,YAAY,CAAC;IACf,MAAM,KAAK;IACX,MAAM,KAAK;IACX,IAAI,IAAI,IAAI,IAAI;QACd,OAAO,KAAK,IAAI;IAClB,OAAO,IAAI,IAAI,IAAI,IAAI;QACrB,OAAO,KAAK,CAAC,KAAK,MAAM,EAAE,IAAI,IAAI;IACpC,OAAO,IAAI,IAAI,MAAM,IAAI;QACvB,OAAO,KAAK,CAAC,KAAK,OAAO,EAAE,IAAI,IAAI;IACrC,OAAO;QACL,OAAO,KAAK,CAAC,KAAK,QAAQ,EAAE,IAAI,IAAI;IACtC;AACF;AACA,IAAI,UAAU;IACZ,QAAQ,CAAC,IAAM;IACf,YAAY,CAAC,IAAM,IAAI;IACvB,aAAa,CAAC,IAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IACxC,eAAe,CAAC,IAAM,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,KAAK;IAC1E,aAAa,CAAC,IAAM,IAAI,IAAI;IAC5B,cAAc,CAAC,IAAM,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG;IACzC,gBAAgB,CAAC,IAAM,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,KAAK;IAC/E,aAAa,CAAC,IAAM,IAAI,IAAI,IAAI;IAChC,cAAc,CAAC,IAAM,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG;IACzC,gBAAgB,CAAC,IAAM,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,KAAK;IACnF,aAAa,CAAC,IAAM,IAAI,IAAI,IAAI,IAAI;IACpC,cAAc,CAAC,IAAM,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG;IACzC,gBAAgB,CAAC,IAAM,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,KAAK;IACxF,YAAY,CAAC,IAAM,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,EAAE,GAAG;IAC9C,aAAa,CAAC,IAAM,KAAK,GAAG,CAAC,IAAI,KAAK,EAAE,GAAG;IAC3C,eAAe,CAAC,IAAM,CAAC,CAAC,KAAK,GAAG,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,IAAI;IACrD,YAAY,CAAC,IAAM,MAAM,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI;IACtD,aAAa,CAAC,IAAM,MAAM,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK;IACxD,eAAe,CAAC,IAAM,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,IAAI;IAC7H,YAAY,CAAC,IAAM,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG;IACjD,aAAa,CAAC,IAAM,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG;IAClD,eAAe,CAAC,IAAM,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI;IAC7H,YAAY,CAAC,IAAM,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI;IAC7C,aAAa,CAAC,IAAM,IAAI,KAAK,KAAK,GAAG,CAAC,IAAI,GAAG,KAAK,KAAK,KAAK,GAAG,CAAC,IAAI,GAAG;IACvE,eAAe,CAAC,IAAM,IAAI,MAAM,KAAK,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI;IAClJ,eAAe,CAAC,IAAM,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,KAAK,IAAI;IAC1G,gBAAgB,CAAC,IAAM,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,IAAI,MAAM;IAC3G,kBAAkB,CAAC,IAAM,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,MAAM,IAAI,MAAM,IAAI;IACvM,cAAc,CAAC,IAAM,IAAI,UAAU,IAAI;IACvC,eAAe;IACf,iBAAiB,CAAC,IAAM,IAAI,MAAM,CAAC,IAAI,UAAU,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,UAAU,IAAI,IAAI,EAAE,IAAI;IAChG;AACF;AAEA,gBAAgB;AAChB,IAAI,OAAO,OAAO,GAAG,CAAC;AACtB,IAAI,aAAa,OAAO,GAAG,CAAC;AAC5B,IAAI,gBAAgB,CAAC,MAAQ,QAAQ,OAAO,GAAG,CAAC,KAAK;AACrD,IAAI,gBAAgB,CAAC,MAAQ,OAAO,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,KAAK;AAC9D,IAAI,oBAAoB,CAAC,SAAW,MAAM,CAAC,WAAW,IAAI;AAC1D,SAAS,kBAAkB,SAAS,EAAE,KAAK;IACzC,IAAI,UAAU,aAAa,EAAE;QAC3B,UAAU,aAAa,CAAC;IAC1B,OAAO;QACL,UAAU;IACZ;AACF;AACA,SAAS,mBAAmB,MAAM,EAAE,KAAK;IACvC,MAAM,YAAY,MAAM,CAAC,WAAW;IACpC,IAAI,WAAW;QACb,UAAU,OAAO,CAAC,CAAC;YACjB,kBAAkB,WAAW;QAC/B;IACF;AACF;AACA,MAAM;AACN,IAAI,aAAa;IACf,YAAY,GAAG,CAAE;QACf,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,GAAG;YAC7B,MAAM,MAAM;QACd;QACA,eAAe,IAAI,EAAE;IACvB;AACF;AACA,IAAI,iBAAiB,CAAC,QAAQ,MAAQ,UAAU,QAAQ,MAAM;AAC9D,SAAS,iBAAiB,MAAM,EAAE,SAAS;IACzC,IAAI,MAAM,CAAC,KAAK,EAAE;QAChB,IAAI,YAAY,MAAM,CAAC,WAAW;QAClC,IAAI,CAAC,WAAW;YACd,UAAU,QAAQ,YAAY,YAAY,aAAa,GAAG,IAAI;QAChE;QACA,IAAI,CAAC,UAAU,GAAG,CAAC,YAAY;YAC7B,UAAU,GAAG,CAAC;YACd,IAAI,OAAO,aAAa,EAAE;gBACxB,OAAO,aAAa,CAAC,UAAU,IAAI,EAAE;YACvC;QACF;IACF;IACA,OAAO;AACT;AACA,SAAS,oBAAoB,MAAM,EAAE,SAAS;IAC5C,MAAM,YAAY,MAAM,CAAC,WAAW;IACpC,IAAI,aAAa,UAAU,GAAG,CAAC,YAAY;QACzC,MAAM,QAAQ,UAAU,IAAI,GAAG;QAC/B,IAAI,OAAO;YACT,UAAU,MAAM,CAAC;QACnB,OAAO;YACL,MAAM,CAAC,WAAW,GAAG;QACvB;QACA,IAAI,OAAO,eAAe,EAAE;YAC1B,OAAO,eAAe,CAAC,OAAO;QAChC;IACF;AACF;AACA,IAAI,YAAY,CAAC,QAAQ,KAAK,QAAU,OAAO,cAAc,CAAC,QAAQ,KAAK;QACzE;QACA,UAAU;QACV,cAAc;IAChB;AAEA,gBAAgB;AAChB,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,IAAI,YAAY,IAAI,OAAO,CAAC,CAAC,EAAE,YAAY,MAAM,CAAC,WAAW,CAAC,EAAE;AAChE,IAAI,YAAY;AAChB,IAAI,mBAAmB;AAEvB,wBAAwB;AACxB,IAAI,iBAAiB,CAAC;IACpB,MAAM,CAAC,OAAO,SAAS,GAAG,iBAAiB;IAC3C,IAAI,CAAC,SAAS,SAAS;QACrB,OAAO;IACT;IACA,MAAM,QAAQ,OAAO,gBAAgB,CAAC,SAAS,eAAe,EAAE,gBAAgB,CAAC;IACjF,IAAI,OAAO;QACT,OAAO,MAAM,IAAI;IACnB,OAAO,IAAI,YAAY,SAAS,UAAU,CAAC,OAAO;QAChD,MAAM,SAAS,OAAO,gBAAgB,CAAC,SAAS,eAAe,EAAE,gBAAgB,CAAC;QAClF,IAAI,QAAQ;YACV,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF,OAAO,IAAI,YAAY,iBAAiB,IAAI,CAAC,WAAW;QACtD,OAAO,eAAe;IACxB,OAAO,IAAI,UAAU;QACnB,OAAO;IACT;IACA,OAAO;AACT;AACA,IAAI,mBAAmB,CAAC;IACtB,MAAM,QAAQ,iBAAiB,IAAI,CAAC;IACpC,IAAI,CAAC,OAAO,OAAO;;KAAG;IACtB,MAAM,GAAG,OAAO,SAAS,GAAG;IAC5B,OAAO;QAAC;QAAO;KAAS;AAC1B;AAEA,6BAA6B;AAC7B,IAAI;AACJ,IAAI,YAAY,CAAC,GAAG,IAAI,IAAI,IAAI,KAAO,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,IAAI,EAAE,EAAE,KAAK,KAAK,CAAC,IAAI,EAAE,EAAE,KAAK,KAAK,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC;AAC7G,IAAI,4BAA4B,CAAC;IAC/B,IAAI,CAAC,iBACH,kBAAkB,SAChB,4CAA4C;IAC5C,IAAI,OAAO,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,QAAQ,CAAC,EAAE,OAExD,cAAc;IACd;IAEJ,MAAM,SAAS,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;QAChC,OAAO,cAAc,OAAO,OAAO,CAAC,kBAAkB,gBAAgB,OAAO,CAAC,YAAY,aAAa,OAAO,CAAC,iBAAiB;IAClI;IACA,MAAM,YAAY,OAAO,GAAG,CAAC,CAAC,QAAU,MAAM,KAAK,CAAC,aAAa,GAAG,CAAC;IACrE,MAAM,eAAe,SAAS,CAAC,EAAE,CAAC,GAAG,CACnC,CAAC,GAAG,IAAM,UAAU,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,CAAC,KAAK,MAAM,GAAG;gBAClB,MAAM,MAAM;YACd;YACA,OAAO,MAAM,CAAC,EAAE;QAClB;IAEF,MAAM,gBAAgB,aAAa,GAAG,CACpC,CAAC,UAAY,mBAAmB;YAAE,GAAG,MAAM;YAAE,QAAQ;QAAQ;IAE/D,OAAO,CAAC;QACN,MAAM,cAAc,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,OAAO,IAAI,CAAC,CAAC,QAAU,UAAU,IAAI,CAAC,SAAS,QAAQ,aAAa;QACtH,IAAI,IAAI;QACR,OAAO,MAAM,CAAC,EAAE,CAAC,OAAO,CACtB,aACA,IAAM,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,eAAe,IAAI,EACxD,OAAO,CAAC,WAAW;IACvB;AACF;AAEA,sBAAsB;AACtB,IAAI,SAAS;AACb,IAAI,OAAO,CAAC;IACV,MAAM,OAAO;IACb,IAAI,SAAS;IACb,IAAI,OAAO,QAAQ,YAAY;QAC7B,MAAM,IAAI,UAAU,GAAG,OAAO,kCAAkC,CAAC;IACnE;IACA,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,QAAQ;YACX,QAAQ;YACR,SAAS;QACX;IACF;AACF;AACA,IAAI,kBAAkB,KAAK,QAAQ,IAAI;AACvC,SAAS;IACP,gBACE,GAAG,OAAO,iEAAiE,CAAC;AAEhF;AACA,IAAI,iBAAiB,KAAK,QAAQ,IAAI;AACtC,SAAS;IACP,eACE,GAAG,OAAO,+IAA+I,CAAC;AAE9J;AAEA,0BAA0B;AAC1B,SAAS,iBAAiB,KAAK;IAC7B,OAAO,GAAG,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,IAAI,OAAO,KAAK,IAAI,CAAC,UAAU,iEAAiE;IACjI,CAAC,WAAW,iBAAiB,IAAI,CAAC,UAAU,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AACrE;;AAKA,yCAAyC;AACzC,IAAI;AACJ,IAAI,iBAAiB,aAAa,GAAG,IAAI;AACzC,IAAI,oBAAoB,CAAC,UAAY,QAAQ,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE;QAC3E,OAAO,eAAe,GAAG,CAAC,SAAS,QAAQ,CAAC,UAAY,QAAQ;IAClE;AACA,SAAS,cAAc,OAAO,EAAE,MAAM;IACpC,IAAI,CAAC,UAAU;QACb,IAAI,OAAO,mBAAmB,aAAa;YACzC,WAAW,IAAI,eAAe;QAChC;IACF;IACA,IAAI,kBAAkB,eAAe,GAAG,CAAC;IACzC,IAAI,CAAC,iBAAiB;QACpB,kBAAkB,aAAa,GAAG,IAAI;QACtC,eAAe,GAAG,CAAC,QAAQ;IAC7B;IACA,gBAAgB,GAAG,CAAC;IACpB,IAAI,UAAU;QACZ,SAAS,OAAO,CAAC;IACnB;IACA,OAAO;QACL,MAAM,mBAAmB,eAAe,GAAG,CAAC;QAC5C,IAAI,CAAC,kBAAkB;QACvB,iBAAiB,MAAM,CAAC;QACxB,IAAI,CAAC,iBAAiB,IAAI,IAAI,UAAU;YACtC,SAAS,SAAS,CAAC;QACrB;IACF;AACF;AAEA,wCAAwC;AACxC,IAAI,YAAY,aAAa,GAAG,IAAI;AACpC,IAAI;AACJ,IAAI,sBAAsB;IACxB,MAAM,eAAe;QACnB,UAAU,OAAO,CACf,CAAC,WAAa,SAAS;gBACrB,OAAO,OAAO,UAAU;gBACxB,QAAQ,OAAO,WAAW;YAC5B;IAEJ;IACA,OAAO,gBAAgB,CAAC,UAAU;IAClC,OAAO;QACL,OAAO,mBAAmB,CAAC,UAAU;IACvC;AACF;AACA,IAAI,eAAe,CAAC;IAClB,UAAU,GAAG,CAAC;IACd,IAAI,CAAC,4BAA4B;QAC/B,6BAA6B;IAC/B;IACA,OAAO;QACL,UAAU,MAAM,CAAC;QACjB,IAAI,CAAC,UAAU,IAAI,IAAI,4BAA4B;YACjD;YACA,6BAA6B,KAAK;QACpC;IACF;AACF;AAEA,iCAAiC;AACjC,IAAI,WAAW,CAAC,UAAU,EAAE,YAAY,SAAS,eAAe,EAAE,GAAG,CAAC,CAAC;IACrE,IAAI,cAAc,SAAS,eAAe,EAAE;QAC1C,OAAO,aAAa;IACtB,OAAO;QACL,OAAO,cAAc,UAAU;IACjC;AACF;AAEA,kBAAkB;AAClB,IAAI,WAAW,CAAC,KAAK,KAAK,QAAU,MAAM,QAAQ,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG;AAEpF,yCAAyC;AACzC,IAAI,cAAc;IAChB,GAAG;QACD,QAAQ;QACR,UAAU;IACZ;IACA,GAAG;QACD,QAAQ;QACR,UAAU;IACZ;AACF;AACA,IAAI,gBAAgB;IAClB,YAAY,QAAQ,EAAE,SAAS,CAAE;QAC/B,IAAI,CAAC,UAAU,GAAG,IAAM,CAAC;gBACvB,SAAS;gBACT,UAAU;gBACV,cAAc;YAChB,CAAC;QACD,IAAI,CAAC,UAAU,GAAG,CAAC;YACjB,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS;YAChC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAW,CAAC,SAAS;YAClD,KAAK,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,UAAU,CAAC;YAClD,KAAK,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC;YACzF,KAAK,QAAQ,GAAG,SAAS,GAAG,KAAK,YAAY,EAAE,KAAK,OAAO;QAC7D;QACA,IAAI,CAAC,MAAM,GAAG;YACZ,IAAI,CAAC,UAAU,CAAC;YAChB,IAAI,CAAC,UAAU,CAAC;QAClB;QACA,IAAI,CAAC,SAAS,GAAG;YACf,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;QACzB;QACA,IAAI,CAAC,OAAO,GAAG;YACb,IAAI,CAAC,MAAM;YACX,IAAI,CAAC,SAAS;QAChB;QACA,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,IAAI,GAAG;YACV,MAAM;YACN,GAAG,IAAI,CAAC,UAAU;YAClB,GAAG,IAAI,CAAC,UAAU;QACpB;IACF;AACF;AAEA,iCAAiC;AACjC,IAAI,kBAAkB,aAAa,GAAG,IAAI;AAC1C,IAAI,kBAAkB,aAAa,GAAG,IAAI;AAC1C,IAAI,mBAAmB,aAAa,GAAG,IAAI;AAC3C,IAAI,YAAY,CAAC,YAAc,cAAc,SAAS,eAAe,GAAG,SAAS;AACjF,IAAI,WAAW,CAAC,UAAU,EAAE,YAAY,SAAS,eAAe,EAAE,GAAG,CAAC,CAAC;IACrE,IAAI,oBAAoB,iBAAiB,GAAG,CAAC;IAC7C,IAAI,CAAC,mBAAmB;QACtB,oBAAoB,aAAa,GAAG,IAAI;QACxC,iBAAiB,GAAG,CAAC,WAAW;IAClC;IACA,MAAM,mBAAmB,IAAI,cAAc,UAAU;IACrD,kBAAkB,GAAG,CAAC;IACtB,IAAI,CAAC,gBAAgB,GAAG,CAAC,YAAY;QACnC,MAAM,WAAW;YACf,mBAAmB,QAAQ,CAAC,UAAY,QAAQ,OAAO;YACvD,OAAO;QACT;QACA,gBAAgB,GAAG,CAAC,WAAW;QAC/B,MAAM,SAAS,UAAU;QACzB,OAAO,gBAAgB,CAAC,UAAU,UAAU;YAAE,SAAS;QAAK;QAC5D,IAAI,cAAc,SAAS,eAAe,EAAE;YAC1C,gBAAgB,GAAG,CAAC,WAAW,SAAS,UAAU;gBAAE;YAAU;QAChE;QACA,OAAO,gBAAgB,CAAC,UAAU,UAAU;YAAE,SAAS;QAAK;IAC9D;IACA,MAAM,gBAAgB,gBAAgB,GAAG,CAAC;IAC1C,CAAA,GAAA,8PAAA,CAAA,MAAI,AAAD,EAAE;IACL,OAAO;QACL,8PAAA,CAAA,MAAI,CAAC,MAAM,CAAC;QACZ,MAAM,qBAAqB,iBAAiB,GAAG,CAAC;QAChD,IAAI,CAAC,oBAAoB;QACzB,mBAAmB,MAAM,CAAC;QAC1B,IAAI,mBAAmB,IAAI,EAAE;QAC7B,MAAM,WAAW,gBAAgB,GAAG,CAAC;QACrC,gBAAgB,MAAM,CAAC;QACvB,IAAI,UAAU;YACZ,UAAU,WAAW,mBAAmB,CAAC,UAAU;YACnD,OAAO,mBAAmB,CAAC,UAAU;YACrC,gBAAgB,GAAG,CAAC;QACtB;IACF;AACF;;AAIA,SAAS,YAAY,IAAI;IACvB,MAAM,MAAM,CAAA,GAAA,4RAAA,CAAA,SAAM,AAAD,EAAE;IACnB,IAAI,IAAI,OAAO,KAAK,MAAM;QACxB,IAAI,OAAO,GAAG;IAChB;IACA,OAAO,IAAI,OAAO;AACpB;;;;AAUA,IAAI,4BAA4B,UAAU,4RAAA,CAAA,YAAS,GAAG,4RAAA,CAAA,kBAAe;AAErE,4BAA4B;AAC5B,IAAI,eAAe;IACjB,MAAM,YAAY,CAAA,GAAA,4RAAA,CAAA,SAAO,AAAD,EAAE;IAC1B;kDAA0B;YACxB,UAAU,OAAO,GAAG;YACpB;0DAAO;oBACL,UAAU,OAAO,GAAG;gBACtB;;QACF;iDAAG,EAAE;IACL,OAAO;AACT;AAEA,8BAA8B;AAC9B,SAAS;IACP,MAAM,SAAS,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,GAAG,CAAC,EAAE;IAC5B,MAAM,YAAY;IAClB,OAAO;QACL,IAAI,UAAU,OAAO,EAAE;YACrB,OAAO,KAAK,MAAM;QACpB;IACF;AACF;;AAIA,SAAS,WAAW,SAAS,EAAE,MAAM;IACnC,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAS,AAAD;gCACxB,IAAM,CAAC;gBACL;gBACA,QAAQ;YACV,CAAC;;IAEH,MAAM,YAAY,CAAA,GAAA,4RAAA,CAAA,SAAO,AAAD,EAAE,KAAK;IAC/B,MAAM,YAAY,UAAU,OAAO;IACnC,IAAI,QAAQ;IACZ,IAAI,OAAO;QACT,MAAM,WAAW,QACf,UAAU,MAAM,MAAM,IAAI,eAAe,QAAQ,MAAM,MAAM;QAE/D,IAAI,CAAC,UAAU;YACb,QAAQ;gBACN;gBACA,QAAQ;YACV;QACF;IACF,OAAO;QACL,QAAQ;IACV;IACA,CAAA,GAAA,4RAAA,CAAA,YAAU,AAAD;iCAAE;YACT,UAAU,OAAO,GAAG;YACpB,IAAI,aAAa,SAAS;gBACxB,QAAQ,MAAM,GAAG,QAAQ,MAAM,GAAG,KAAK;YACzC;QACF;gCAAG;QAAC;KAAM;IACV,OAAO,MAAM,MAAM;AACrB;AACA,SAAS,eAAe,IAAI,EAAE,IAAI;IAChC,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM,EAAE;QAC/B,OAAO;IACT;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;YACvB,OAAO;QACT;IACF;IACA,OAAO;AACT;;AAIA,IAAI,UAAU,CAAC,SAAW,CAAA,GAAA,4RAAA,CAAA,YAAU,AAAD,EAAE,QAAQ;AAC7C,IAAI,YAAY,EAAE;;AAIlB,SAAS,QAAQ,KAAK;IACpB,MAAM,UAAU,CAAA,GAAA,4RAAA,CAAA,SAAO,AAAD,EAAE,KAAK;IAC7B,CAAA,GAAA,4RAAA,CAAA,YAAU,AAAD;8BAAE;YACT,QAAQ,OAAO,GAAG;QACpB;;IACA,OAAO,QAAQ,OAAO;AACxB;;AAIA,IAAI,mBAAmB;IACrB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAS,AAAD,EAAE;IACpD;sDAA0B;YACxB,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,MAAM;gFAAoB,CAAC;oBACzB,iBAAiB,EAAE,OAAO;oBAC1B,OAAO;wBACL,eAAe,EAAE,OAAO;oBAC1B;gBACF;;YACA,kBAAkB;YAClB,IAAI,IAAI,gBAAgB,EAAE;gBACxB,IAAI,gBAAgB,CAAC,UAAU;YACjC,OAAO;gBACL,IAAI,WAAW,CAAC;YAClB;YACA;8DAAO;oBACL,IAAI,IAAI,mBAAmB,EAAE;wBAC3B,IAAI,mBAAmB,CAAC,UAAU;oBACpC,OAAO;wBACL,IAAI,cAAc,CAAC;oBACrB;gBACF;;QACF;qDAAG,EAAE;IACL,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4938, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4954, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-spring%2Banimated%4010.0.1_react%4019.0.0/node_modules/%40react-spring/animated/dist/react-spring_animated.modern.mjs"], "sourcesContent": ["// src/Animated.ts\nimport { defineHidden } from \"@react-spring/shared\";\nvar $node = Symbol.for(\"Animated:node\");\nvar isAnimated = (value) => !!value && value[$node] === value;\nvar getAnimated = (owner) => owner && owner[$node];\nvar setAnimated = (owner, node) => defineHidden(owner, $node, node);\nvar getPayload = (owner) => owner && owner[$node] && owner[$node].getPayload();\nvar Animated = class {\n  constructor() {\n    setAnimated(this, this);\n  }\n  /** Get every `AnimatedValue` used by this node. */\n  getPayload() {\n    return this.payload || [];\n  }\n};\n\n// src/AnimatedValue.ts\nimport { is } from \"@react-spring/shared\";\nvar AnimatedValue = class _AnimatedValue extends Animated {\n  constructor(_value) {\n    super();\n    this._value = _value;\n    this.done = true;\n    this.durationProgress = 0;\n    if (is.num(this._value)) {\n      this.lastPosition = this._value;\n    }\n  }\n  /** @internal */\n  static create(value) {\n    return new _AnimatedValue(value);\n  }\n  getPayload() {\n    return [this];\n  }\n  getValue() {\n    return this._value;\n  }\n  setValue(value, step) {\n    if (is.num(value)) {\n      this.lastPosition = value;\n      if (step) {\n        value = Math.round(value / step) * step;\n        if (this.done) {\n          this.lastPosition = value;\n        }\n      }\n    }\n    if (this._value === value) {\n      return false;\n    }\n    this._value = value;\n    return true;\n  }\n  reset() {\n    const { done } = this;\n    this.done = false;\n    if (is.num(this._value)) {\n      this.elapsedTime = 0;\n      this.durationProgress = 0;\n      this.lastPosition = this._value;\n      if (done) this.lastVelocity = null;\n      this.v0 = null;\n    }\n  }\n};\n\n// src/AnimatedString.ts\nimport { is as is2, createInterpolator } from \"@react-spring/shared\";\nvar AnimatedString = class _AnimatedString extends AnimatedValue {\n  constructor(value) {\n    super(0);\n    this._string = null;\n    this._toString = createInterpolator({\n      output: [value, value]\n    });\n  }\n  /** @internal */\n  static create(value) {\n    return new _AnimatedString(value);\n  }\n  getValue() {\n    const value = this._string;\n    return value == null ? this._string = this._toString(this._value) : value;\n  }\n  setValue(value) {\n    if (is2.str(value)) {\n      if (value == this._string) {\n        return false;\n      }\n      this._string = value;\n      this._value = 1;\n    } else if (super.setValue(value)) {\n      this._string = null;\n    } else {\n      return false;\n    }\n    return true;\n  }\n  reset(goal) {\n    if (goal) {\n      this._toString = createInterpolator({\n        output: [this.getValue(), goal]\n      });\n    }\n    this._value = 0;\n    super.reset();\n  }\n};\n\n// src/AnimatedArray.ts\nimport { isAnimatedString } from \"@react-spring/shared\";\n\n// src/AnimatedObject.ts\nimport {\n  each,\n  eachProp,\n  getFluidValue,\n  hasFluidValue\n} from \"@react-spring/shared\";\n\n// src/context.ts\nvar TreeContext = { dependencies: null };\n\n// src/AnimatedObject.ts\nvar AnimatedObject = class extends Animated {\n  constructor(source) {\n    super();\n    this.source = source;\n    this.setValue(source);\n  }\n  getValue(animated) {\n    const values = {};\n    eachProp(this.source, (source, key) => {\n      if (isAnimated(source)) {\n        values[key] = source.getValue(animated);\n      } else if (hasFluidValue(source)) {\n        values[key] = getFluidValue(source);\n      } else if (!animated) {\n        values[key] = source;\n      }\n    });\n    return values;\n  }\n  /** Replace the raw object data */\n  setValue(source) {\n    this.source = source;\n    this.payload = this._makePayload(source);\n  }\n  reset() {\n    if (this.payload) {\n      each(this.payload, (node) => node.reset());\n    }\n  }\n  /** Create a payload set. */\n  _makePayload(source) {\n    if (source) {\n      const payload = /* @__PURE__ */ new Set();\n      eachProp(source, this._addToPayload, payload);\n      return Array.from(payload);\n    }\n  }\n  /** Add to a payload set. */\n  _addToPayload(source) {\n    if (TreeContext.dependencies && hasFluidValue(source)) {\n      TreeContext.dependencies.add(source);\n    }\n    const payload = getPayload(source);\n    if (payload) {\n      each(payload, (node) => this.add(node));\n    }\n  }\n};\n\n// src/AnimatedArray.ts\nvar AnimatedArray = class _AnimatedArray extends AnimatedObject {\n  constructor(source) {\n    super(source);\n  }\n  /** @internal */\n  static create(source) {\n    return new _AnimatedArray(source);\n  }\n  getValue() {\n    return this.source.map((node) => node.getValue());\n  }\n  setValue(source) {\n    const payload = this.getPayload();\n    if (source.length == payload.length) {\n      return payload.map((node, i) => node.setValue(source[i])).some(Boolean);\n    }\n    super.setValue(source.map(makeAnimated));\n    return true;\n  }\n};\nfunction makeAnimated(value) {\n  const nodeType = isAnimatedString(value) ? AnimatedString : AnimatedValue;\n  return nodeType.create(value);\n}\n\n// src/getAnimatedType.ts\nimport { is as is3, isAnimatedString as isAnimatedString2 } from \"@react-spring/shared\";\nfunction getAnimatedType(value) {\n  const parentNode = getAnimated(value);\n  return parentNode ? parentNode.constructor : is3.arr(value) ? AnimatedArray : isAnimatedString2(value) ? AnimatedString : AnimatedValue;\n}\n\n// src/createHost.ts\nimport { is as is5, eachProp as eachProp2 } from \"@react-spring/shared\";\n\n// src/withAnimated.tsx\nimport * as React from \"react\";\nimport { forwardRef, useRef, useCallback, useEffect } from \"react\";\nimport {\n  is as is4,\n  each as each2,\n  raf,\n  useForceUpdate,\n  useOnce,\n  addFluidObserver,\n  removeFluidObserver,\n  useIsomorphicLayoutEffect\n} from \"@react-spring/shared\";\nvar withAnimated = (Component, host) => {\n  const hasInstance = (\n    // Function components must use \"forwardRef\" to avoid being\n    // re-rendered on every animation frame.\n    !is4.fun(Component) || Component.prototype && Component.prototype.isReactComponent\n  );\n  return forwardRef((givenProps, givenRef) => {\n    const instanceRef = useRef(null);\n    const ref = hasInstance && // eslint-disable-next-line react-hooks/rules-of-hooks\n    useCallback(\n      (value) => {\n        instanceRef.current = updateRef(givenRef, value);\n      },\n      [givenRef]\n    );\n    const [props, deps] = getAnimatedState(givenProps, host);\n    const forceUpdate = useForceUpdate();\n    const callback = () => {\n      const instance = instanceRef.current;\n      if (hasInstance && !instance) {\n        return;\n      }\n      const didUpdate = instance ? host.applyAnimatedValues(instance, props.getValue(true)) : false;\n      if (didUpdate === false) {\n        forceUpdate();\n      }\n    };\n    const observer = new PropsObserver(callback, deps);\n    const observerRef = useRef(void 0);\n    useIsomorphicLayoutEffect(() => {\n      observerRef.current = observer;\n      each2(deps, (dep) => addFluidObserver(dep, observer));\n      return () => {\n        if (observerRef.current) {\n          each2(\n            observerRef.current.deps,\n            (dep) => removeFluidObserver(dep, observerRef.current)\n          );\n          raf.cancel(observerRef.current.update);\n        }\n      };\n    });\n    useEffect(callback, []);\n    useOnce(() => () => {\n      const observer2 = observerRef.current;\n      each2(observer2.deps, (dep) => removeFluidObserver(dep, observer2));\n    });\n    const usedProps = host.getComponentProps(props.getValue());\n    return /* @__PURE__ */ React.createElement(Component, { ...usedProps, ref });\n  });\n};\nvar PropsObserver = class {\n  constructor(update, deps) {\n    this.update = update;\n    this.deps = deps;\n  }\n  eventObserved(event) {\n    if (event.type == \"change\") {\n      raf.write(this.update);\n    }\n  }\n};\nfunction getAnimatedState(props, host) {\n  const dependencies = /* @__PURE__ */ new Set();\n  TreeContext.dependencies = dependencies;\n  if (props.style)\n    props = {\n      ...props,\n      style: host.createAnimatedStyle(props.style)\n    };\n  props = new AnimatedObject(props);\n  TreeContext.dependencies = null;\n  return [props, dependencies];\n}\nfunction updateRef(ref, value) {\n  if (ref) {\n    if (is4.fun(ref)) ref(value);\n    else ref.current = value;\n  }\n  return value;\n}\n\n// src/createHost.ts\nvar cacheKey = Symbol.for(\"AnimatedComponent\");\nvar createHost = (components, {\n  applyAnimatedValues = () => false,\n  createAnimatedStyle = (style) => new AnimatedObject(style),\n  getComponentProps = (props) => props\n} = {}) => {\n  const hostConfig = {\n    applyAnimatedValues,\n    createAnimatedStyle,\n    getComponentProps\n  };\n  const animated = (Component) => {\n    const displayName = getDisplayName(Component) || \"Anonymous\";\n    if (is5.str(Component)) {\n      Component = animated[Component] || (animated[Component] = withAnimated(Component, hostConfig));\n    } else {\n      Component = Component[cacheKey] || (Component[cacheKey] = withAnimated(Component, hostConfig));\n    }\n    Component.displayName = `Animated(${displayName})`;\n    return Component;\n  };\n  eachProp2(components, (Component, key) => {\n    if (is5.arr(components)) {\n      key = getDisplayName(Component);\n    }\n    animated[key] = animated(Component);\n  });\n  return {\n    animated\n  };\n};\nvar getDisplayName = (arg) => is5.str(arg) ? arg : arg && is5.str(arg.displayName) ? arg.displayName : is5.fun(arg) && arg.name || null;\nexport {\n  Animated,\n  AnimatedArray,\n  AnimatedObject,\n  AnimatedString,\n  AnimatedValue,\n  createHost,\n  getAnimated,\n  getAnimatedType,\n  getPayload,\n  isAnimated,\n  setAnimated\n};\n"], "names": [], "mappings": "AAAA,kBAAkB;;;;;;;;;;;;;;AAClB;AAAA;AAkNA,uBAAuB;AACvB;AAEA;;AApNA,IAAI,QAAQ,OAAO,GAAG,CAAC;AACvB,IAAI,aAAa,CAAC,QAAU,CAAC,CAAC,SAAS,KAAK,CAAC,MAAM,KAAK;AACxD,IAAI,cAAc,CAAC,QAAU,SAAS,KAAK,CAAC,MAAM;AAClD,IAAI,cAAc,CAAC,OAAO,OAAS,CAAA,GAAA,0SAAA,CAAA,eAAY,AAAD,EAAE,OAAO,OAAO;AAC9D,IAAI,aAAa,CAAC,QAAU,SAAS,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,UAAU;AAC5E,IAAI,WAAW;IACb,aAAc;QACZ,YAAY,IAAI,EAAE,IAAI;IACxB;IACA,iDAAiD,GACjD,aAAa;QACX,OAAO,IAAI,CAAC,OAAO,IAAI,EAAE;IAC3B;AACF;;AAIA,IAAI,gBAAgB,MAAM,uBAAuB;IAC/C,YAAY,MAAM,CAAE;QAClB,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,0SAAA,CAAA,KAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG;YACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM;QACjC;IACF;IACA,cAAc,GACd,OAAO,OAAO,KAAK,EAAE;QACnB,OAAO,IAAI,eAAe;IAC5B;IACA,aAAa;QACX,OAAO;YAAC,IAAI;SAAC;IACf;IACA,WAAW;QACT,OAAO,IAAI,CAAC,MAAM;IACpB;IACA,SAAS,KAAK,EAAE,IAAI,EAAE;QACpB,IAAI,0SAAA,CAAA,KAAE,CAAC,GAAG,CAAC,QAAQ;YACjB,IAAI,CAAC,YAAY,GAAG;YACpB,IAAI,MAAM;gBACR,QAAQ,KAAK,KAAK,CAAC,QAAQ,QAAQ;gBACnC,IAAI,IAAI,CAAC,IAAI,EAAE;oBACb,IAAI,CAAC,YAAY,GAAG;gBACtB;YACF;QACF;QACA,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO;YACzB,OAAO;QACT;QACA,IAAI,CAAC,MAAM,GAAG;QACd,OAAO;IACT;IACA,QAAQ;QACN,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI;QACrB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,0SAAA,CAAA,KAAE,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG;YACvB,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM;YAC/B,IAAI,MAAM,IAAI,CAAC,YAAY,GAAG;YAC9B,IAAI,CAAC,EAAE,GAAG;QACZ;IACF;AACF;;AAIA,IAAI,iBAAiB,MAAM,wBAAwB;IACjD,YAAY,KAAK,CAAE;QACjB,KAAK,CAAC;QACN,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,SAAS,GAAG,CAAA,GAAA,0SAAA,CAAA,qBAAkB,AAAD,EAAE;YAClC,QAAQ;gBAAC;gBAAO;aAAM;QACxB;IACF;IACA,cAAc,GACd,OAAO,OAAO,KAAK,EAAE;QACnB,OAAO,IAAI,gBAAgB;IAC7B;IACA,WAAW;QACT,MAAM,QAAQ,IAAI,CAAC,OAAO;QAC1B,OAAO,SAAS,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI;IACtE;IACA,SAAS,KAAK,EAAE;QACd,IAAI,0SAAA,CAAA,KAAG,CAAC,GAAG,CAAC,QAAQ;YAClB,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gBACzB,OAAO;YACT;YACA,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,MAAM,GAAG;QAChB,OAAO,IAAI,KAAK,CAAC,SAAS,QAAQ;YAChC,IAAI,CAAC,OAAO,GAAG;QACjB,OAAO;YACL,OAAO;QACT;QACA,OAAO;IACT;IACA,MAAM,IAAI,EAAE;QACV,IAAI,MAAM;YACR,IAAI,CAAC,SAAS,GAAG,CAAA,GAAA,0SAAA,CAAA,qBAAkB,AAAD,EAAE;gBAClC,QAAQ;oBAAC,IAAI,CAAC,QAAQ;oBAAI;iBAAK;YACjC;QACF;QACA,IAAI,CAAC,MAAM,GAAG;QACd,KAAK,CAAC;IACR;AACF;;;AAaA,iBAAiB;AACjB,IAAI,cAAc;IAAE,cAAc;AAAK;AAEvC,wBAAwB;AACxB,IAAI,iBAAiB,cAAc;IACjC,YAAY,MAAM,CAAE;QAClB,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,CAAC;IAChB;IACA,SAAS,QAAQ,EAAE;QACjB,MAAM,SAAS,CAAC;QAChB,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ;YAC7B,IAAI,WAAW,SAAS;gBACtB,MAAM,CAAC,IAAI,GAAG,OAAO,QAAQ,CAAC;YAChC,OAAO,IAAI,CAAA,GAAA,0SAAA,CAAA,gBAAa,AAAD,EAAE,SAAS;gBAChC,MAAM,CAAC,IAAI,GAAG,CAAA,GAAA,0SAAA,CAAA,gBAAa,AAAD,EAAE;YAC9B,OAAO,IAAI,CAAC,UAAU;gBACpB,MAAM,CAAC,IAAI,GAAG;YAChB;QACF;QACA,OAAO;IACT;IACA,gCAAgC,GAChC,SAAS,MAAM,EAAE;QACf,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC;IACnC;IACA,QAAQ;QACN,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,CAAA,GAAA,0SAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,OAAS,KAAK,KAAK;QACzC;IACF;IACA,0BAA0B,GAC1B,aAAa,MAAM,EAAE;QACnB,IAAI,QAAQ;YACV,MAAM,UAAU,aAAa,GAAG,IAAI;YACpC,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,IAAI,CAAC,aAAa,EAAE;YACrC,OAAO,MAAM,IAAI,CAAC;QACpB;IACF;IACA,0BAA0B,GAC1B,cAAc,MAAM,EAAE;QACpB,IAAI,YAAY,YAAY,IAAI,CAAA,GAAA,0SAAA,CAAA,gBAAa,AAAD,EAAE,SAAS;YACrD,YAAY,YAAY,CAAC,GAAG,CAAC;QAC/B;QACA,MAAM,UAAU,WAAW;QAC3B,IAAI,SAAS;YACX,CAAA,GAAA,0SAAA,CAAA,OAAI,AAAD,EAAE,SAAS,CAAC,OAAS,IAAI,CAAC,GAAG,CAAC;QACnC;IACF;AACF;AAEA,uBAAuB;AACvB,IAAI,gBAAgB,MAAM,uBAAuB;IAC/C,YAAY,MAAM,CAAE;QAClB,KAAK,CAAC;IACR;IACA,cAAc,GACd,OAAO,OAAO,MAAM,EAAE;QACpB,OAAO,IAAI,eAAe;IAC5B;IACA,WAAW;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,QAAQ;IAChD;IACA,SAAS,MAAM,EAAE;QACf,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,IAAI,OAAO,MAAM,IAAI,QAAQ,MAAM,EAAE;YACnC,OAAO,QAAQ,GAAG,CAAC,CAAC,MAAM,IAAM,KAAK,QAAQ,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC;QACjE;QACA,KAAK,CAAC,SAAS,OAAO,GAAG,CAAC;QAC1B,OAAO;IACT;AACF;AACA,SAAS,aAAa,KAAK;IACzB,MAAM,WAAW,CAAA,GAAA,0SAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,iBAAiB;IAC5D,OAAO,SAAS,MAAM,CAAC;AACzB;;AAIA,SAAS,gBAAgB,KAAK;IAC5B,MAAM,aAAa,YAAY;IAC/B,OAAO,aAAa,WAAW,WAAW,GAAG,0SAAA,CAAA,KAAG,CAAC,GAAG,CAAC,SAAS,gBAAgB,CAAA,GAAA,0SAAA,CAAA,mBAAiB,AAAD,EAAE,SAAS,iBAAiB;AAC5H;;;;;AAkBA,IAAI,eAAe,CAAC,WAAW;IAC7B,MAAM,cACJ,2DAA2D;IAC3D,wCAAwC;IACxC,CAAC,0SAAA,CAAA,KAAG,CAAC,GAAG,CAAC,cAAc,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,gBAAgB;IAEpF,OAAO,CAAA,GAAA,4RAAA,CAAA,aAAU,AAAD,EAAE,CAAC,YAAY;QAC7B,MAAM,cAAc,CAAA,GAAA,4RAAA,CAAA,SAAM,AAAD,EAAE;QAC3B,MAAM,MAAM,eAAe,sDAAsD;QACjF,CAAA,GAAA,4RAAA,CAAA,cAAW,AAAD;wCACR,CAAC;gBACC,YAAY,OAAO,GAAG,UAAU,UAAU;YAC5C;uCACA;YAAC;SAAS;QAEZ,MAAM,CAAC,OAAO,KAAK,GAAG,iBAAiB,YAAY;QACnD,MAAM,cAAc,CAAA,GAAA,0SAAA,CAAA,iBAAc,AAAD;QACjC,MAAM,WAAW;YACf,MAAM,WAAW,YAAY,OAAO;YACpC,IAAI,eAAe,CAAC,UAAU;gBAC5B;YACF;YACA,MAAM,YAAY,WAAW,KAAK,mBAAmB,CAAC,UAAU,MAAM,QAAQ,CAAC,SAAS;YACxF,IAAI,cAAc,OAAO;gBACvB;YACF;QACF;QACA,MAAM,WAAW,IAAI,cAAc,UAAU;QAC7C,MAAM,cAAc,CAAA,GAAA,4RAAA,CAAA,SAAM,AAAD,EAAE,KAAK;QAChC,CAAA,GAAA,0SAAA,CAAA,4BAAyB,AAAD;sDAAE;gBACxB,YAAY,OAAO,GAAG;gBACtB,CAAA,GAAA,0SAAA,CAAA,OAAK,AAAD,EAAE;8DAAM,CAAC,MAAQ,CAAA,GAAA,0SAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;;gBAC3C;8DAAO;wBACL,IAAI,YAAY,OAAO,EAAE;4BACvB,CAAA,GAAA,0SAAA,CAAA,OAAK,AAAD,EACF,YAAY,OAAO,CAAC,IAAI;0EACxB,CAAC,MAAQ,CAAA,GAAA,0SAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,YAAY,OAAO;;4BAEvD,8PAAA,CAAA,MAAG,CAAC,MAAM,CAAC,YAAY,OAAO,CAAC,MAAM;wBACvC;oBACF;;YACF;;QACA,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD,EAAE,UAAU,EAAE;QACtB,CAAA,GAAA,0SAAA,CAAA,UAAO,AAAD;oCAAE;4CAAM;wBACZ,MAAM,YAAY,YAAY,OAAO;wBACrC,CAAA,GAAA,0SAAA,CAAA,OAAK,AAAD,EAAE,UAAU,IAAI;oDAAE,CAAC,MAAQ,CAAA,GAAA,0SAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK;;oBAC1D;;;QACA,MAAM,YAAY,KAAK,iBAAiB,CAAC,MAAM,QAAQ;QACvD,OAAO,aAAa,GAAG,CAAA,GAAA,4RAAA,CAAA,gBAAmB,AAAD,EAAE,WAAW;YAAE,GAAG,SAAS;YAAE;QAAI;IAC5E;AACF;AACA,IAAI,gBAAgB;IAClB,YAAY,MAAM,EAAE,IAAI,CAAE;QACxB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG;IACd;IACA,cAAc,KAAK,EAAE;QACnB,IAAI,MAAM,IAAI,IAAI,UAAU;YAC1B,8PAAA,CAAA,MAAG,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM;QACvB;IACF;AACF;AACA,SAAS,iBAAiB,KAAK,EAAE,IAAI;IACnC,MAAM,eAAe,aAAa,GAAG,IAAI;IACzC,YAAY,YAAY,GAAG;IAC3B,IAAI,MAAM,KAAK,EACb,QAAQ;QACN,GAAG,KAAK;QACR,OAAO,KAAK,mBAAmB,CAAC,MAAM,KAAK;IAC7C;IACF,QAAQ,IAAI,eAAe;IAC3B,YAAY,YAAY,GAAG;IAC3B,OAAO;QAAC;QAAO;KAAa;AAC9B;AACA,SAAS,UAAU,GAAG,EAAE,KAAK;IAC3B,IAAI,KAAK;QACP,IAAI,0SAAA,CAAA,KAAG,CAAC,GAAG,CAAC,MAAM,IAAI;aACjB,IAAI,OAAO,GAAG;IACrB;IACA,OAAO;AACT;AAEA,oBAAoB;AACpB,IAAI,WAAW,OAAO,GAAG,CAAC;AAC1B,IAAI,aAAa,CAAC,YAAY,EAC5B,sBAAsB,IAAM,KAAK,EACjC,sBAAsB,CAAC,QAAU,IAAI,eAAe,MAAM,EAC1D,oBAAoB,CAAC,QAAU,KAAK,EACrC,GAAG,CAAC,CAAC;IACJ,MAAM,aAAa;QACjB;QACA;QACA;IACF;IACA,MAAM,WAAW,CAAC;QAChB,MAAM,cAAc,eAAe,cAAc;QACjD,IAAI,0SAAA,CAAA,KAAG,CAAC,GAAG,CAAC,YAAY;YACtB,YAAY,QAAQ,CAAC,UAAU,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,aAAa,WAAW,WAAW;QAC/F,OAAO;YACL,YAAY,SAAS,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,aAAa,WAAW,WAAW;QAC/F;QACA,UAAU,WAAW,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QAClD,OAAO;IACT;IACA,CAAA,GAAA,0SAAA,CAAA,WAAS,AAAD,EAAE,YAAY,CAAC,WAAW;QAChC,IAAI,0SAAA,CAAA,KAAG,CAAC,GAAG,CAAC,aAAa;YACvB,MAAM,eAAe;QACvB;QACA,QAAQ,CAAC,IAAI,GAAG,SAAS;IAC3B;IACA,OAAO;QACL;IACF;AACF;AACA,IAAI,iBAAiB,CAAC,MAAQ,0SAAA,CAAA,KAAG,CAAC,GAAG,CAAC,OAAO,MAAM,OAAO,0SAAA,CAAA,KAAG,CAAC,GAAG,CAAC,IAAI,WAAW,IAAI,IAAI,WAAW,GAAG,0SAAA,CAAA,KAAG,CAAC,GAAG,CAAC,QAAQ,IAAI,IAAI,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5294, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5300, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-spring%2Btypes%4010.0.1/node_modules/%40react-spring/types/dist/react-spring_types.modern.mjs"], "sourcesContent": ["// src/utils.ts\nvar Any = class {\n};\nexport {\n  Any\n};\n"], "names": [], "mappings": "AAAA,eAAe;;;;AACf,IAAI,MAAM;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5307, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5313, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/node_modules/.pnpm/%40react-spring%2Bthree%4010.0.1_%40react-three%2Bfiber%409.1.2_%40types%2Breact%4019.0.10_react-dom%4019.0_3f41a382e4a72d11f8efba93b74546c7/node_modules/%40react-spring/three/dist/react-spring_three.modern.mjs"], "sourcesContent": ["// src/index.ts\nimport { applyProps, addEffect } from \"@react-three/fiber\";\nimport { Globals } from \"@react-spring/core\";\nimport { createStringInterpolator, colors, raf } from \"@react-spring/shared\";\nimport { createHost } from \"@react-spring/animated\";\n\n// src/primitives.ts\nimport * as THREE from \"three\";\nimport \"@react-three/fiber\";\nvar primitives = [\"primitive\"].concat(\n  Object.keys(THREE).filter((key) => /^[A-Z]/.test(key)).map((key) => key[0].toLowerCase() + key.slice(1))\n);\n\n// src/index.ts\nexport * from \"@react-spring/core\";\nGlobals.assign({\n  createStringInterpolator,\n  colors,\n  frameLoop: \"demand\"\n});\naddEffect(() => {\n  raf.advance();\n});\nvar host = createHost(primitives, {\n  applyAnimatedValues: applyProps\n});\nvar animated = host.animated;\nexport {\n  animated as a,\n  animated\n};\n"], "names": [], "mappings": "AAAA,eAAe;;;;;AACf;AAAA;AACA;AACA;AACA;AAEA,oBAAoB;AACpB;;;;;;;AAEA,IAAI,aAAa;IAAC;CAAY,CAAC,MAAM,CACnC,OAAO,IAAI,CAAC,0MAAO,MAAM,CAAC,CAAC,MAAQ,SAAS,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,MAAQ,GAAG,CAAC,EAAE,CAAC,WAAW,KAAK,IAAI,KAAK,CAAC;;AAKvG,0SAAA,CAAA,UAAO,CAAC,MAAM,CAAC;IACb,0BAAA,0SAAA,CAAA,2BAAwB;IACxB,QAAA,0SAAA,CAAA,SAAM;IACN,WAAW;AACb;AACA,CAAA,GAAA,iaAAA,CAAA,YAAS,AAAD,EAAE;IACR,8PAAA,CAAA,MAAG,CAAC,OAAO;AACb;AACA,IAAI,OAAO,CAAA,GAAA,gSAAA,CAAA,aAAU,AAAD,EAAE,YAAY;IAChC,qBAAqB,kaAAA,CAAA,aAAU;AACjC;AACA,IAAI,WAAW,KAAK,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5348, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}