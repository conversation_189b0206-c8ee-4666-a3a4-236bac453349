(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules__pnpm_f5abadd9._.js", {

"[project]/node_modules/.pnpm/@react-three+fiber@9.1.2_@types+react@19.0.10_react-dom@19.0.0_react@19.0.0__react@19.0.0_three@0.177.0/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/2b4b7_@react-three_fiber_dist_react-three-fiber_esm_da7ec552.js",
  "static/chunks/2b4b7_@react-three_fiber_dist_react-three-fiber_esm_194d7dd5.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@react-three+fiber@9.1.2_@types+react@19.0.10_react-dom@19.0.0_react@19.0.0__react@19.0.0_three@0.177.0/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/node_modules/.pnpm/@react-three+drei@10.1.2_@react-three+fiber@9.1.2_@types+react@19.0.10_react-dom@19.0.0_25016c57484519cf3a785dd0022bda46/node_modules/@react-three/drei/index.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, d: __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_84e28771._.js",
  "static/chunks/6acb2_@react-three_drei_web_e3b5e988._.js",
  "static/chunks/6acb2_@react-three_drei_core_8d861920._.js",
  "static/chunks/6acb2_@react-three_drei_index_966569d3.js",
  "static/chunks/6acb2_@react-three_drei_041644a3._.js",
  "static/chunks/335cd_maath_dist_76a353f8._.js",
  "static/chunks/96d78_three-stdlib_loaders_c96b6535._.js",
  "static/chunks/96d78_three-stdlib_controls_af1118d1._.js",
  "static/chunks/96d78_three-stdlib_6673ec8e._.js",
  "static/chunks/9b4d9_troika-three-text_dist_troika-three-text_esm_05346348.js",
  "static/chunks/946a5_hls_js_dist_hls_mjs_5cde585b._.js",
  "static/chunks/36f47_three-mesh-bvh_src_a5130c1c._.js",
  "static/chunks/node_modules__pnpm_f65a8184._.js",
  "static/chunks/6acb2_@react-three_drei_index_194d7dd5.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@react-three+drei@10.1.2_@react-three+fiber@9.1.2_@types+react@19.0.10_react-dom@19.0.0_25016c57484519cf3a785dd0022bda46/node_modules/@react-three/drei/index.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);