# Interactive Knowledge Graph & AI Agent Dashboard

## What is it?
This project is a cutting-edge platform that combines a 3D knowledge graph visualization with AI-powered agents to help users explore and understand complex information in an intuitive way.

## Key Features:

1. **3D Knowledge Graph Visualization**
   - Interactive 3D interface built with React Three Fiber
   - Visual representation of relationships between concepts and data points
   - Dynamic exploration of connected information

2. **AI-Powered Agents**
   - Multiple specialized AI agents (Assistant, Coder, Creative Writer)
   - Each agent has unique capabilities and expertise
   - Natural language interaction

3. **Modern Tech Stack**
   - Frontend: React with TypeScript and Next.js
   - Backend: FastAPI (Python)
   - 3D Visualization: Three.js via React Three Fiber
   - AI Integration: Local LLM support through Ollama

4. **Knowledge Management**
   - Visual exploration of complex information
   - Interactive network graph of related concepts
   - Real-time data processing and visualization

## Why It's Valuable:

1. **For Developers**: 
   - Clean, modern codebase with TypeScript and Python
   - Easy to extend with new features or agents
   - Great example of 3D visualization in React

2. **For Users**:
   - Intuitive way to explore complex information
   - Multiple AI assistants for different needs
   - Engaging, interactive interface

3. **For Learners**:
   - Excellent educational resource for 3D web development
   - Demonstrates AI integration patterns
   - Shows modern full-stack development practices

## What Makes It Special:
- Combines AI, 3D visualization, and modern web technologies
- Runs locally with Ollama for privacy-focused AI
- Modular design makes it easy to add new agents or visualization types
- Beautiful, responsive UI with dark mode support

This project is perfect for developers looking to build interactive AI applications, data scientists needing to visualize complex relationships, or anyone interested in the intersection of AI and data visualization.
