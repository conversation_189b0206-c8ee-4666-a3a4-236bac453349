# Interactive Knowledge Graph Visualization

This project demonstrates an interactive 3D knowledge graph visualization built with React, Three.js (via React Three Fiber), and a Python (Flask) backend for NLP-powered graph generation.

## Project Structure

```
/highlight_demos
├── public/
│   ├── index.html         # Main HTML page
│   ├── manifest.json      # Web app manifest
│   └── (favicon.ico, logo192.png, etc.) # Static assets
├── src/
│   ├── highlight_demos/   # Main application source code
│   │   ├── components/
│   │   │   └── KnowledgeGraph.jsx # React component for 3D graph visualization
│   │   ├── App.js             # Main React application component
│   │   ├── App.css            # Styles for the App component
│   │   ├── index.js           # Entry point for React app
│   │   ├── index.css          # Global styles
│   │   ├── reportWebVitals.js # Performance reporting (Create React App standard)
│   │   ├── api.py             # Flask API for NLP processing
│   │   └── nlp_system.py      # Python class for NLP tasks and graph generation
│   └── (other CRA default files if any, like setupTests.js)
├── .env.example         # Example environment variables
├── pyproject.toml       # Python dependencies (managed by PDM)
├── package.json         # Node.js dependencies
└── README.md            # This file
```

## Prerequisites

*   **Node.js and npm (or Yarn):** For running the React frontend.
*   **Python 3.8+ and PDM:** For running the Flask backend and NLP system. (PDM will be used for package management).
*   **CUDA-enabled GPU (Recommended for NLP Model):** The default NLP model (`Mixtral-8x7B-Instruct-v0.1` with 4-bit quantization) is large and performs best on a GPU. For CPU-only systems, you might need to configure a smaller model in `src/highlight_demos/api.py` and `src/highlight_demos/nlp_system.py` (e.g., `gpt2`) and set quantization to `None`.

## Setup

### 1. Clone the Repository (if applicable)

```bash
# git clone <repository_url>
# cd highlight_demos
```

### 2. Backend Setup (Python Flask API with PDM)

   a.  **Install PDM (if you haven't already):**
       Follow the official PDM installation guide: <mcurl name="PDM Installation" url="https://pdm-project.org/latest/installation/"></mcurl>

   b.  **Install Python dependencies using PDM:**
       Navigate to the project root directory (`/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos`) where `pyproject.toml` is located.
       ```bash
       pdm install
       ```
       This command will read the `pyproject.toml` file, resolve dependencies, and install them into a PDM-managed environment.

   c.  **(Optional) Configure NLP Model:**
       The NLP system uses `mistralai/Mixtral-8x7B-Instruct-v0.1` by default. If you have limited resources (especially VRAM), you might want to change this.
       Edit `src/highlight_demos/api.py`:
       ```python
       NLP_MODEL_NAME = os.environ.get("NLP_MODEL_NAME", "gpt2") # Smaller model example
       NLP_QUANTIZATION = os.environ.get("NLP_QUANTIZATION", "None") # No quantization for CPU
       ```
       And potentially adjust `target_modules` in `LoraConfig` within `src/highlight_demos/nlp_system.py` if you change the model architecture significantly, though for `gpt2` it might not require LoRA or specific target modules for basic use.

### 3. Frontend Setup (React App)

   a.  **Navigate to the frontend source directory if your `package.json` is there. If `package.json` is in the root, run these from the root.**
       Assuming `package.json` will be in the root `highlight_demos` directory:

   b.  **Install Node.js dependencies:**

       ```bash
       npm install
       # or
       # yarn install
       ```
       *(Note: `package.json` will be generated in a later step.)*

   c.  **(Optional) Create a `.env` file for API URL:**
       If your Flask API runs on a different port or host, create a `.env` file in the root `highlight_demos` directory:
       ```
       REACT_APP_API_URL=http://localhost:5001
       ```

## Running the Application

### 1. Start the Backend (Flask API)

   Open a terminal, navigate to the project root (`/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos/`). If you installed dependencies with `pdm install`, PDM provides ways to run scripts or commands within its managed environment.

   You can run the API using PDM:
   ```bash
   pdm run python src/highlight_demos/api.py
   ```
   Alternatively, you can activate the PDM environment shell first:
   ```bash
   pdm shell
   # Now you are in the PDM managed environment
   python src/highlight_demos/api.py
   ```
   Or, if `api.py` is configured as a runnable script in `pyproject.toml` (e.g., under `[tool.pdm.scripts]`), you might use `pdm run <script_name>`.

   ```bash
   # If using 'pdm run':
   # Ensure your current directory is /Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos
   pdm run python src/highlight_demos/api.py
   
   # If using 'pdm shell' then 'python ...':
   # Ensure your current directory is /Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos
   # (after running 'pdm shell')
   python src/highlight_demos/api.py
   ```

   The API will typically start on `http://localhost:5001`.
   The first time it runs, it will download the NLP model, which can take a significant amount of time and disk space.

### 2. Start the Frontend (React App)

   Open another terminal, navigate to the root project directory (`/Users/<USER>/CODE/03_REFS_AND_ARTIFACTS/highlight_demos`), and run:

   ```bash
   npm start
   # or
   # yarn start
   ```

   This will open the application in your web browser, usually at `http://localhost:3000`.

## How to Use

1.  Ensure both the backend API and frontend application are running.
2.  Open the application in your browser (e.g., `http://localhost:3000`).
3.  Enter a piece of text into the text area.
4.  Click "Generate Knowledge Graph".
5.  The system will process the text using the NLP model, extract entities and relations, and then visualize them as an interactive 3D graph.
    *   **Interaction:**
        *   **Orbit:** Drag to rotate the view.
        *   **Zoom:** Scroll to zoom in/out.
        *   **Pan:** Right-click drag (or equivalent) to pan.
        *   **Node Click:** Click on a node to see its details in the side panel.
        *   **Node Hover:** Hover over a node to highlight it.

## Key Technologies

*   **Frontend:**
    *   React
    *   React Three Fiber (for Three.js integration)
    *   Drei (helpers for React Three Fiber)
    *   react-force-graph (for 3D force-directed graph layout)
*   **Backend:**
    *   Flask (Python web framework)
    *   Transformers (Hugging Face library for NLP models)
    *   PyTorch
    *   PEFT (Parameter-Efficient Fine-Tuning)
    *   bitsandbytes (for model quantization)
*   **Visualization:**
    *   Three.js

## Further Development Ideas

*   Implement fine-tuning capabilities for the NLP model through the UI.
*   Add more sophisticated node/edge styling and customization.
*   Improve error handling and user feedback.
*   Optimize performance for very large graphs.
*   Allow saving/loading of generated graphs.
*   Integrate different layout algorithms.
*   Add search and filtering functionality for the graph.
