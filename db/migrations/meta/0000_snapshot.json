{"id": "92171b5b-5cc4-4903-90ac-a4d111244449", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.agents": {"name": "agents", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "agent_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'ASSISTANT'"}, "capabilities": {"name": "capabilities", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'"}, "config": {"name": "config", "type": "json", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "owner_id": {"name": "owner_id", "type": "text", "primaryKey": false, "notNull": true}, "knowledge_base_id": {"name": "knowledge_base_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chunks": {"name": "chunks", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "vector_id": {"name": "vector_id", "type": "text", "primaryKey": false, "notNull": true}, "start_index": {"name": "start_index", "type": "integer", "primaryKey": false, "notNull": true}, "end_index": {"name": "end_index", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "document_id": {"name": "document_id", "type": "text", "primaryKey": false, "notNull": true}, "embeddings": {"name": "embeddings", "type": "real[]", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"chunks_vector_id_unique": {"name": "chunks_vector_id_unique", "nullsNotDistinct": false, "columns": ["vector_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.documents": {"name": "documents", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "content_type": {"name": "content_type", "type": "content_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'TEXT'"}, "vector_id": {"name": "vector_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "knowledge_base_id": {"name": "knowledge_base_id", "type": "text", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"documents_vector_id_unique": {"name": "documents_vector_id_unique", "nullsNotDistinct": false, "columns": ["vector_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.knowledge_bases": {"name": "knowledge_bases", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "owner_id": {"name": "owner_id", "type": "text", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.queries": {"name": "queries", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "response": {"name": "response", "type": "text", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "agent_id": {"name": "agent_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.team_memberships": {"name": "team_memberships", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "role": {"name": "role", "type": "team_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'MEMBER'"}, "team_id": {"name": "team_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"team_memberships_team_id_user_id_unique": {"name": "team_memberships_team_id_user_id_unique", "nullsNotDistinct": false, "columns": ["team_id", "user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.teams": {"name": "teams", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "profile_image": {"name": "profile_image", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'USER'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}