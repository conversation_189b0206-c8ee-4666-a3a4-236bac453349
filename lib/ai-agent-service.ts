// AI Agent Service using configured providers
// Handles agent interactions with multiple AI providers

import {
  AI<PERSON>lient,
  createAIClient,
  getBestModelForTask,
  getAvailableProviders,
  getAvailableModels,
  type ChatMessage,
  type ChatRequest,
  type ChatResponse,
  type AIModel,
} from "./ai-providers";

export interface AgentConfig {
  id: string;
  name: string;
  type: "ASSISTANT" | "RESEARCHER" | "CODER" | "ANALYST" | "CREATIVE";
  description: string;
  systemPrompt: string;
  preferredProvider?: string;
  preferredModel?: string;
  temperature?: number;
  maxTokens?: number;
  capabilities: string[];
}

export interface AgentSession {
  id: string;
  agentId: string;
  userId: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface AgentResponse {
  content: string;
  model: string;
  provider: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  metadata?: Record<string, any>;
}

// Predefined agent configurations
export const AGENT_CONFIGS: Record<string, AgentConfig> = {
  assistant: {
    id: "assistant",
    name: "AI Assistant",
    type: "ASSISTANT",
    description: "A helpful AI assistant for general questions and tasks",
    systemPrompt: `You are a helpful, knowledgeable AI assistant. You provide accurate, concise, and helpful responses to user questions. You can help with a wide range of topics including general knowledge, explanations, problem-solving, and task assistance.

Key guidelines:
- Be helpful, accurate, and concise
- Ask clarifying questions when needed
- Provide step-by-step explanations for complex topics
- Admit when you don't know something
- Be friendly and professional`,
    preferredProvider: "openrouter",
    preferredModel: "anthropic/claude-3.5-sonnet",
    temperature: 0.7,
    maxTokens: 1000,
    capabilities: ["chat", "reasoning", "general-knowledge"],
  },

  researcher: {
    id: "researcher",
    name: "Research Agent",
    type: "RESEARCHER",
    description: "Specialized in research, analysis, and information synthesis",
    systemPrompt: `You are a research specialist AI. You excel at analyzing information, conducting thorough research, synthesizing findings, and providing well-structured reports.

Your capabilities include:
- Literature review and analysis
- Data interpretation and insights
- Fact-checking and verification
- Research methodology guidance
- Citation and source evaluation
- Trend analysis and pattern recognition

Always provide:
- Well-structured responses with clear sections
- Evidence-based conclusions
- Relevant sources and references when possible
- Multiple perspectives on complex topics
- Actionable insights and recommendations`,
    preferredProvider: "google",
    preferredModel: "gemini-1.5-pro",
    temperature: 0.3,
    maxTokens: 2000,
    capabilities: ["research", "analysis", "long-context", "reasoning"],
  },

  coder: {
    id: "coder",
    name: "Code Agent",
    type: "CODER",
    description: "Expert in programming, code review, and software development",
    systemPrompt: `You are an expert software engineer and coding assistant. You help with programming tasks, code review, debugging, architecture design, and best practices.

Your expertise includes:
- Multiple programming languages (Python, JavaScript, TypeScript, Go, Rust, etc.)
- Web development (React, Next.js, Node.js, etc.)
- Backend development and APIs
- Database design and optimization
- DevOps and deployment
- Code review and optimization
- Testing and debugging
- Software architecture and design patterns

Always provide:
- Clean, well-commented code
- Explanations of your approach
- Best practices and security considerations
- Testing suggestions
- Performance optimization tips
- Alternative solutions when applicable`,
    preferredProvider: "groq",
    preferredModel: "llama-3.1-70b-versatile",
    temperature: 0.2,
    maxTokens: 1500,
    capabilities: ["coding", "debugging", "architecture", "fast"],
  },

  analyst: {
    id: "analyst",
    name: "Data Analyst",
    type: "ANALYST",
    description: "Specialized in data analysis, visualization, and insights",
    systemPrompt: `You are a data analysis expert. You help users understand data, create visualizations, perform statistical analysis, and derive actionable insights.

Your capabilities include:
- Statistical analysis and interpretation
- Data visualization recommendations
- Trend identification and forecasting
- A/B testing and experimentation
- Business intelligence and KPI analysis
- Data cleaning and preprocessing guidance
- Machine learning model recommendations
- Report generation and presentation

Always provide:
- Clear data interpretations
- Visualization suggestions
- Statistical significance assessments
- Actionable business insights
- Methodology explanations
- Assumptions and limitations
- Next steps and recommendations`,
    preferredProvider: "cerebras",
    preferredModel: "llama3.1-70b",
    temperature: 0.1,
    maxTokens: 1200,
    capabilities: ["analysis", "statistics", "visualization", "ultra-fast"],
  },

  creative: {
    id: "creative",
    name: "Creative Agent",
    type: "CREATIVE",
    description:
      "Specialized in creative writing, brainstorming, and content creation",
    systemPrompt: `You are a creative AI assistant specializing in content creation, writing, and creative problem-solving. You help with various creative tasks and provide innovative solutions.

Your creative capabilities include:
- Creative writing (stories, articles, copy)
- Brainstorming and idea generation
- Content strategy and planning
- Marketing copy and messaging
- Creative problem-solving
- Storytelling and narrative development
- Brand voice and tone development
- Visual concept descriptions

Always provide:
- Original and engaging content
- Multiple creative options
- Clear explanations of creative choices
- Audience-appropriate tone and style
- Structured creative processes
- Inspiration and creative direction
- Practical implementation suggestions`,
    preferredProvider: "chutes",
    preferredModel: "claude-3.5-sonnet",
    temperature: 0.8,
    maxTokens: 1500,
    capabilities: ["creative", "writing", "brainstorming", "content"],
  },

  multilingual: {
    id: "multilingual",
    name: "Multilingual Agent",
    type: "MULTILINGUAL",
    description:
      "Expert in translation, multilingual content, and cross-cultural communication",
    systemPrompt: `You are a multilingual AI specialist with expertise in translation, cross-cultural communication, and international content creation. You excel at working across languages and cultures.

Your multilingual capabilities include:
- High-quality translation between languages
- Cultural adaptation and localization
- Multilingual content creation
- Cross-cultural communication guidance
- Language learning assistance
- International business communication
- Cultural sensitivity and awareness
- Regional dialect and colloquialism understanding

Always provide:
- Accurate and culturally appropriate translations
- Context-aware language adaptations
- Cultural insights and explanations
- Multiple language options when relevant
- Proper formatting for different writing systems
- Regional variations and preferences
- Professional and respectful communication`,
    preferredProvider: "mistral",
    preferredModel: "mistral-large-latest",
    temperature: 0.4,
    maxTokens: 1500,
    capabilities: ["translation", "multilingual", "cultural", "localization"],
  },
};

export class AIAgentService {
  private clients: Map<string, AIClient> = new Map();
  private sessions: Map<string, AgentSession> = new Map();

  constructor() {
    // Initialize clients for available providers
    const providers = getAvailableProviders();
    for (const provider of providers) {
      try {
        this.clients.set(provider.id, new AIClient(provider.id));
      } catch (error) {
        console.warn(`Failed to initialize ${provider.name} client:`, error);
      }
    }
  }

  // Get available agents based on configured providers
  getAvailableAgents(): AgentConfig[] {
    const availableProviders = getAvailableProviders().map((p) => p.id);
    return Object.values(AGENT_CONFIGS).filter((agent) => {
      // Check if agent's preferred provider is available
      if (
        agent.preferredProvider &&
        availableProviders.includes(agent.preferredProvider)
      ) {
        return true;
      }
      // Or if any provider is available for fallback
      return availableProviders.length > 0;
    });
  }

  // Get agent configuration
  getAgentConfig(agentId: string): AgentConfig | null {
    return AGENT_CONFIGS[agentId] || null;
  }

  // Create a new agent session
  createSession(agentId: string, userId: string): AgentSession {
    const sessionId = `session_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;
    const session: AgentSession = {
      id: sessionId,
      agentId,
      userId,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {},
    };

    this.sessions.set(sessionId, session);
    return session;
  }

  // Get session
  getSession(sessionId: string): AgentSession | null {
    return this.sessions.get(sessionId) || null;
  }

  // Send message to agent
  async sendMessage(
    sessionId: string,
    userMessage: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
      model?: string;
      provider?: string;
    }
  ): Promise<AgentResponse> {
    const session = this.getSession(sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    const agentConfig = this.getAgentConfig(session.agentId);
    if (!agentConfig) {
      throw new Error("Agent configuration not found");
    }

    // Add user message to session
    const userMsg: ChatMessage = { role: "user", content: userMessage };
    session.messages.push(userMsg);

    // Determine which client/model to use
    const { client, model } = this.selectClientAndModel(agentConfig, options);

    // Prepare messages for AI
    const messages: ChatMessage[] = [
      { role: "system", content: agentConfig.systemPrompt },
      ...session.messages,
    ];

    // Make AI request
    const request: ChatRequest = {
      model: model.id,
      messages,
      temperature: options?.temperature ?? agentConfig.temperature ?? 0.7,
      maxTokens: options?.maxTokens ?? agentConfig.maxTokens ?? 1000,
    };

    try {
      const response = await client.chat(request);
      const assistantMessage = response.choices[0]?.message;

      if (!assistantMessage) {
        throw new Error("No response from AI model");
      }

      // Add assistant response to session
      const assistantMsg: ChatMessage = {
        role: "assistant",
        content: assistantMessage.content,
      };
      session.messages.push(assistantMsg);
      session.updatedAt = new Date();

      return {
        content: assistantMessage.content,
        model: model.id,
        provider: model.provider,
        usage: response.usage,
        metadata: {
          sessionId,
          agentId: session.agentId,
          messageCount: session.messages.length,
        },
      };
    } catch (error) {
      console.error("AI Agent Error:", error);
      throw new Error(`Failed to get response from AI agent: ${error}`);
    }
  }

  // Select appropriate client and model
  private selectClientAndModel(
    agentConfig: AgentConfig,
    options?: { model?: string; provider?: string }
  ): { client: AIClient; model: AIModel } {
    // Use specified provider/model if provided
    if (options?.provider && options?.model) {
      const client = this.clients.get(options.provider);
      if (client) {
        const models = getAvailableModels();
        const model = models.find(
          (m) => m.id === options.model && m.provider === options.provider
        );
        if (model) {
          return { client, model };
        }
      }
    }

    // Use agent's preferred provider/model
    if (agentConfig.preferredProvider && agentConfig.preferredModel) {
      const client = this.clients.get(agentConfig.preferredProvider);
      if (client) {
        const models = getAvailableModels();
        const model = models.find(
          (m) =>
            m.id === agentConfig.preferredModel &&
            m.provider === agentConfig.preferredProvider
        );
        if (model) {
          return { client, model };
        }
      }
    }

    // Fallback to best available model for agent type
    const taskMap: Record<string, string> = {
      ASSISTANT: "chat",
      RESEARCHER: "reasoning",
      CODER: "coding",
      ANALYST: "reasoning",
      CREATIVE: "chat",
    };

    const task = taskMap[agentConfig.type] || "chat";
    const model = getBestModelForTask(task as any);

    if (!model) {
      throw new Error("No suitable AI model available");
    }

    const client = this.clients.get(model.provider);
    if (!client) {
      throw new Error(`No client available for provider: ${model.provider}`);
    }

    return { client, model };
  }

  // Get session history
  getSessionHistory(sessionId: string): ChatMessage[] {
    const session = this.getSession(sessionId);
    return session ? session.messages : [];
  }

  // Clear session
  clearSession(sessionId: string): boolean {
    return this.sessions.delete(sessionId);
  }

  // Get provider status
  getProviderStatus(): Array<{
    id: string;
    name: string;
    available: boolean;
    models: number;
  }> {
    const providers = getAvailableProviders();
    return providers.map((provider) => ({
      id: provider.id,
      name: provider.name,
      available: this.clients.has(provider.id),
      models: provider.models.length,
    }));
  }

  // Test provider connection
  async testProvider(providerId: string): Promise<boolean> {
    const client = this.clients.get(providerId);
    if (!client) return false;

    try {
      const models = getAvailableModels().filter(
        (m) => m.provider === providerId
      );
      if (models.length === 0) return false;

      const response = await client.chat({
        model: models[0].id,
        messages: [{ role: "user", content: "Hello" }],
        maxTokens: 10,
      });

      return !!response.choices[0]?.message?.content;
    } catch (error) {
      console.error(`Provider ${providerId} test failed:`, error);
      return false;
    }
  }
}

// Singleton instance
export const aiAgentService = new AIAgentService();

// Utility functions
export function getAvailableAgentTypes(): string[] {
  return Object.keys(AGENT_CONFIGS);
}

export function createAgentSession(
  agentId: string,
  userId: string
): AgentSession {
  return aiAgentService.createSession(agentId, userId);
}

export async function chatWithAgent(
  sessionId: string,
  message: string,
  options?: {
    temperature?: number;
    maxTokens?: number;
    model?: string;
    provider?: string;
  }
): Promise<AgentResponse> {
  return aiAgentService.sendMessage(sessionId, message, options);
}
