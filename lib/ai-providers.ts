// AI Provider Configuration and Client Library
// Supports multiple AI providers with unified interface

export interface AIProvider {
  id: string;
  name: string;
  baseUrl: string;
  apiKey: string | undefined;
  models: AIModel[];
  headers: Record<string, string>;
  isAvailable: boolean;
}

export interface AIModel {
  id: string;
  name: string;
  provider: string;
  contextLength: number;
  inputCost?: number; // per 1M tokens
  outputCost?: number; // per 1M tokens
  capabilities: string[];
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ChatRequest {
  model: string;
  messages: ChatMessage[];
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}

export interface ChatResponse {
  id: string;
  model: string;
  choices: {
    message: {
      role: string;
      content: string;
    };
    finishReason: string;
  }[];
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

// AI Provider Configurations
export const AI_PROVIDERS: Record<string, Omit<AIProvider, 'isAvailable'>> = {
  openrouter: {
    id: 'openrouter',
    name: 'OpenRouter',
    baseUrl: 'https://openrouter.ai/api/v1',
    apiKey: process.env.OPENROUTER_API_KEY,
    headers: {
      'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
      'Content-Type': 'application/json',
      'HTTP-Referer': process.env.NEXTAUTH_URL || 'http://localhost:3000',
      'X-Title': 'Knowledge OS'
    },
    models: [
      {
        id: 'anthropic/claude-3.5-sonnet',
        name: 'Claude 3.5 Sonnet',
        provider: 'openrouter',
        contextLength: 200000,
        inputCost: 3.0,
        outputCost: 15.0,
        capabilities: ['chat', 'reasoning', 'coding', 'analysis']
      },
      {
        id: 'openai/gpt-4o',
        name: 'GPT-4o',
        provider: 'openrouter',
        contextLength: 128000,
        inputCost: 2.5,
        outputCost: 10.0,
        capabilities: ['chat', 'reasoning', 'coding', 'vision']
      },
      {
        id: 'meta-llama/llama-3.1-405b-instruct',
        name: 'Llama 3.1 405B',
        provider: 'openrouter',
        contextLength: 131072,
        inputCost: 2.7,
        outputCost: 2.7,
        capabilities: ['chat', 'reasoning', 'coding']
      },
      {
        id: 'google/gemini-pro-1.5',
        name: 'Gemini Pro 1.5',
        provider: 'openrouter',
        contextLength: 2000000,
        inputCost: 1.25,
        outputCost: 5.0,
        capabilities: ['chat', 'reasoning', 'coding', 'vision']
      }
    ]
  },

  google: {
    id: 'google',
    name: 'Google Gemini',
    baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
    apiKey: process.env.GOOGLE_API_KEY,
    headers: {
      'Content-Type': 'application/json'
    },
    models: [
      {
        id: 'gemini-1.5-pro',
        name: 'Gemini 1.5 Pro',
        provider: 'google',
        contextLength: 2000000,
        inputCost: 1.25,
        outputCost: 5.0,
        capabilities: ['chat', 'reasoning', 'coding', 'vision', 'long-context']
      },
      {
        id: 'gemini-1.5-flash',
        name: 'Gemini 1.5 Flash',
        provider: 'google',
        contextLength: 1000000,
        inputCost: 0.075,
        outputCost: 0.3,
        capabilities: ['chat', 'reasoning', 'coding', 'fast']
      },
      {
        id: 'gemini-pro',
        name: 'Gemini Pro',
        provider: 'google',
        contextLength: 32768,
        inputCost: 0.5,
        outputCost: 1.5,
        capabilities: ['chat', 'reasoning', 'coding']
      }
    ]
  },

  groq: {
    id: 'groq',
    name: 'Groq',
    baseUrl: 'https://api.groq.com/openai/v1',
    apiKey: process.env.GROQ_API_KEY,
    headers: {
      'Authorization': `Bearer ${process.env.GROQ_API_KEY}`,
      'Content-Type': 'application/json'
    },
    models: [
      {
        id: 'llama-3.1-70b-versatile',
        name: 'Llama 3.1 70B',
        provider: 'groq',
        contextLength: 131072,
        inputCost: 0.59,
        outputCost: 0.79,
        capabilities: ['chat', 'reasoning', 'coding', 'fast']
      },
      {
        id: 'llama-3.1-8b-instant',
        name: 'Llama 3.1 8B',
        provider: 'groq',
        contextLength: 131072,
        inputCost: 0.05,
        outputCost: 0.08,
        capabilities: ['chat', 'fast', 'efficient']
      },
      {
        id: 'mixtral-8x7b-32768',
        name: 'Mixtral 8x7B',
        provider: 'groq',
        contextLength: 32768,
        inputCost: 0.24,
        outputCost: 0.24,
        capabilities: ['chat', 'reasoning', 'multilingual']
      },
      {
        id: 'gemma2-9b-it',
        name: 'Gemma 2 9B',
        provider: 'groq',
        contextLength: 8192,
        inputCost: 0.2,
        outputCost: 0.2,
        capabilities: ['chat', 'fast', 'efficient']
      }
    ]
  },

  cerebras: {
    id: 'cerebras',
    name: 'Cerebras',
    baseUrl: 'https://api.cerebras.ai/v1',
    apiKey: process.env.CEREBRAS_API_KEY,
    headers: {
      'Authorization': `Bearer ${process.env.CEREBRAS_API_KEY}`,
      'Content-Type': 'application/json'
    },
    models: [
      {
        id: 'llama3.1-70b',
        name: 'Llama 3.1 70B',
        provider: 'cerebras',
        contextLength: 128000,
        inputCost: 0.6,
        outputCost: 0.6,
        capabilities: ['chat', 'reasoning', 'coding', 'ultra-fast']
      },
      {
        id: 'llama3.1-8b',
        name: 'Llama 3.1 8B',
        provider: 'cerebras',
        contextLength: 128000,
        inputCost: 0.1,
        outputCost: 0.1,
        capabilities: ['chat', 'ultra-fast', 'efficient']
      }
    ]
  },

  chutes: {
    id: 'chutes',
    name: 'Chutes AI',
    baseUrl: 'https://api.chutes.ai/v1',
    apiKey: process.env.CHUTES_API_KEY,
    headers: {
      'Authorization': `Bearer ${process.env.CHUTES_API_KEY}`,
      'Content-Type': 'application/json'
    },
    models: [
      {
        id: 'gpt-4o',
        name: 'GPT-4o (Chutes)',
        provider: 'chutes',
        contextLength: 128000,
        inputCost: 2.5,
        outputCost: 10.0,
        capabilities: ['chat', 'reasoning', 'coding', 'vision']
      },
      {
        id: 'claude-3.5-sonnet',
        name: 'Claude 3.5 Sonnet (Chutes)',
        provider: 'chutes',
        contextLength: 200000,
        inputCost: 3.0,
        outputCost: 15.0,
        capabilities: ['chat', 'reasoning', 'coding', 'analysis']
      }
    ]
  },

  mistral: {
    id: 'mistral',
    name: 'Mistral AI',
    baseUrl: 'https://api.mistral.ai/v1',
    apiKey: process.env.MISTRAL_API_KEY,
    headers: {
      'Authorization': `Bearer ${process.env.MISTRAL_API_KEY}`,
      'Content-Type': 'application/json'
    },
    models: [
      {
        id: 'mistral-large-latest',
        name: 'Mistral Large',
        provider: 'mistral',
        contextLength: 128000,
        inputCost: 2.0,
        outputCost: 6.0,
        capabilities: ['chat', 'reasoning', 'coding', 'multilingual']
      },
      {
        id: 'mistral-medium-latest',
        name: 'Mistral Medium',
        provider: 'mistral',
        contextLength: 32768,
        inputCost: 0.7,
        outputCost: 2.1,
        capabilities: ['chat', 'reasoning', 'efficient']
      },
      {
        id: 'mistral-small-latest',
        name: 'Mistral Small',
        provider: 'mistral',
        contextLength: 32768,
        inputCost: 0.2,
        outputCost: 0.6,
        capabilities: ['chat', 'fast', 'efficient']
      }
    ]
  },

  // Optional providers (commented out by default)
  openai: {
    id: 'openai',
    name: 'OpenAI',
    baseUrl: 'https://api.openai.com/v1',
    apiKey: process.env.OPENAI_API_KEY,
    headers: {
      'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
      'Content-Type': 'application/json'
    },
    models: [
      {
        id: 'gpt-4o',
        name: 'GPT-4o',
        provider: 'openai',
        contextLength: 128000,
        inputCost: 2.5,
        outputCost: 10.0,
        capabilities: ['chat', 'reasoning', 'coding', 'vision']
      },
      {
        id: 'gpt-4o-mini',
        name: 'GPT-4o Mini',
        provider: 'openai',
        contextLength: 128000,
        inputCost: 0.15,
        outputCost: 0.6,
        capabilities: ['chat', 'fast', 'efficient']
      }
    ]
  },

  anthropic: {
    id: 'anthropic',
    name: 'Anthropic',
    baseUrl: 'https://api.anthropic.com/v1',
    apiKey: process.env.ANTHROPIC_API_KEY,
    headers: {
      'x-api-key': process.env.ANTHROPIC_API_KEY || '',
      'Content-Type': 'application/json',
      'anthropic-version': '2023-06-01'
    },
    models: [
      {
        id: 'claude-3-5-sonnet-20241022',
        name: 'Claude 3.5 Sonnet',
        provider: 'anthropic',
        contextLength: 200000,
        inputCost: 3.0,
        outputCost: 15.0,
        capabilities: ['chat', 'reasoning', 'coding', 'analysis']
      },
      {
        id: 'claude-3-haiku-20240307',
        name: 'Claude 3 Haiku',
        provider: 'anthropic',
        contextLength: 200000,
        inputCost: 0.25,
        outputCost: 1.25,
        capabilities: ['chat', 'fast', 'efficient']
      }
    ]
  }
};

// Get available providers (those with API keys)
export function getAvailableProviders(): AIProvider[] {
  return Object.values(AI_PROVIDERS)
    .map(provider => ({
      ...provider,
      isAvailable: !!provider.apiKey
    }))
    .filter(provider => provider.isAvailable);
}

// Get all models from available providers
export function getAvailableModels(): AIModel[] {
  return getAvailableProviders()
    .flatMap(provider => provider.models);
}

// Get models by capability
export function getModelsByCapability(capability: string): AIModel[] {
  return getAvailableModels()
    .filter(model => model.capabilities.includes(capability));
}

// Get fastest models
export function getFastestModels(): AIModel[] {
  return getModelsByCapability('fast')
    .concat(getModelsByCapability('ultra-fast'));
}

// Get most cost-effective models
export function getCostEffectiveModels(): AIModel[] {
  return getAvailableModels()
    .filter(model => model.inputCost && model.inputCost < 1.0)
    .sort((a, b) => (a.inputCost || 0) - (b.inputCost || 0));
}

// Get provider by model ID
export function getProviderForModel(modelId: string): AIProvider | null {
  const providers = getAvailableProviders();
  for (const provider of providers) {
    if (provider.models.some(model => model.id === modelId)) {
      return provider;
    }
  }
  return null;
}

// AI Client Class
export class AIClient {
  private provider: AIProvider;

  constructor(providerId: string) {
    const provider = getAvailableProviders().find(p => p.id === providerId);
    if (!provider) {
      throw new Error(`Provider ${providerId} not available or not configured`);
    }
    this.provider = provider;
  }

  async chat(request: ChatRequest): Promise<ChatResponse> {
    const url = `${this.provider.baseUrl}/chat/completions`;
    
    // Handle provider-specific request formatting
    const body = this.formatRequest(request);
    
    const response = await fetch(url, {
      method: 'POST',
      headers: this.provider.headers,
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`AI API Error (${response.status}): ${error}`);
    }

    const data = await response.json();
    return this.formatResponse(data);
  }

  private formatRequest(request: ChatRequest): any {
    // Handle provider-specific formatting
    switch (this.provider.id) {
      case 'google':
        return this.formatGoogleRequest(request);
      case 'anthropic':
        return this.formatAnthropicRequest(request);
      default:
        // OpenAI-compatible format (most providers)
        return {
          model: request.model,
          messages: request.messages,
          temperature: request.temperature || 0.7,
          max_tokens: request.maxTokens || 1000,
          stream: request.stream || false
        };
    }
  }

  private formatGoogleRequest(request: ChatRequest): any {
    // Convert to Google's format
    const contents = request.messages.map(msg => ({
      role: msg.role === 'assistant' ? 'model' : msg.role,
      parts: [{ text: msg.content }]
    }));

    return {
      contents,
      generationConfig: {
        temperature: request.temperature || 0.7,
        maxOutputTokens: request.maxTokens || 1000
      }
    };
  }

  private formatAnthropicRequest(request: ChatRequest): any {
    // Convert to Anthropic's format
    const systemMessage = request.messages.find(m => m.role === 'system');
    const messages = request.messages.filter(m => m.role !== 'system');

    return {
      model: request.model,
      messages,
      system: systemMessage?.content,
      max_tokens: request.maxTokens || 1000,
      temperature: request.temperature || 0.7
    };
  }

  private formatResponse(data: any): ChatResponse {
    // Handle provider-specific response formatting
    switch (this.provider.id) {
      case 'google':
        return this.formatGoogleResponse(data);
      case 'anthropic':
        return this.formatAnthropicResponse(data);
      default:
        // OpenAI-compatible format
        return data;
    }
  }

  private formatGoogleResponse(data: any): ChatResponse {
    const candidate = data.candidates?.[0];
    return {
      id: data.id || 'google-' + Date.now(),
      model: 'google-model',
      choices: [{
        message: {
          role: 'assistant',
          content: candidate?.content?.parts?.[0]?.text || ''
        },
        finishReason: candidate?.finishReason || 'stop'
      }],
      usage: {
        promptTokens: data.usageMetadata?.promptTokenCount || 0,
        completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
        totalTokens: data.usageMetadata?.totalTokenCount || 0
      }
    };
  }

  private formatAnthropicResponse(data: any): ChatResponse {
    return {
      id: data.id,
      model: data.model,
      choices: [{
        message: {
          role: 'assistant',
          content: data.content?.[0]?.text || ''
        },
        finishReason: data.stop_reason || 'stop'
      }],
      usage: {
        promptTokens: data.usage?.input_tokens || 0,
        completionTokens: data.usage?.output_tokens || 0,
        totalTokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0)
      }
    };
  }
}

// Utility functions
export function createAIClient(providerId?: string): AIClient {
  // Use first available provider if none specified
  if (!providerId) {
    const available = getAvailableProviders();
    if (available.length === 0) {
      throw new Error('No AI providers configured');
    }
    providerId = available[0].id;
  }
  
  return new AIClient(providerId);
}

export function getBestModelForTask(task: 'chat' | 'coding' | 'reasoning' | 'fast' | 'cost-effective'): AIModel | null {
  const models = getAvailableModels();
  
  switch (task) {
    case 'fast':
      return getFastestModels()[0] || null;
    case 'cost-effective':
      return getCostEffectiveModels()[0] || null;
    case 'coding':
      return models.find(m => m.capabilities.includes('coding')) || null;
    case 'reasoning':
      return models.find(m => m.capabilities.includes('reasoning')) || null;
    default:
      return models[0] || null;
  }
}
