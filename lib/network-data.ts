// Network data generator for AI Agent Network Graph

export interface NetworkNode {
  id: string;
  name: string;
  type: 'primary' | 'secondary' | 'hub';
  capabilities: string[];
}

export interface NetworkLink {
  source: string;
  target: string;
  value: number;
  type: 'collaboration' | 'data-flow' | 'dependency';
}

export interface NetworkData {
  nodes: NetworkNode[];
  links: NetworkLink[];
}

// Generate sample network data based on existing agents
export function generateAgentNetworkData(): NetworkData {
  const nodes: NetworkNode[] = [
    // Primary AI Agents (from existing agent cards)
    {
      id: 'ai-assistant',
      name: 'AI Assistant',
      type: 'primary',
      capabilities: ['Q&A', 'Research', 'Writing', 'Analysis']
    },
    {
      id: 'code-expert',
      name: 'Code Expert',
      type: 'primary',
      capabilities: ['Coding', 'Debugging', 'Code Review', 'Architecture']
    },
    {
      id: 'creative-writer',
      name: 'Creative Writer',
      type: 'primary',
      capabilities: ['Writing', 'Storytelling', 'Content', 'Creativity']
    },
    
    // Hub Nodes (Central coordination points)
    {
      id: 'central-hub',
      name: 'Central Hub',
      type: 'hub',
      capabilities: ['Coordination', 'Load Balancing', 'Task Distribution', 'Monitoring']
    },
    {
      id: 'data-hub',
      name: 'Data Hub',
      type: 'hub',
      capabilities: ['Data Storage', 'Data Processing', 'Analytics', 'Caching']
    },
    
    // Secondary Agents (Specialized support agents)
    {
      id: 'language-processor',
      name: 'Language Processor',
      type: 'secondary',
      capabilities: ['NLP', 'Translation', 'Sentiment Analysis', 'Text Processing']
    },
    {
      id: 'image-analyzer',
      name: 'Image Analyzer',
      type: 'secondary',
      capabilities: ['Computer Vision', 'Image Recognition', 'OCR', 'Visual Analysis']
    },
    {
      id: 'task-scheduler',
      name: 'Task Scheduler',
      type: 'secondary',
      capabilities: ['Scheduling', 'Priority Management', 'Resource Allocation', 'Workflow']
    },
    {
      id: 'security-agent',
      name: 'Security Agent',
      type: 'secondary',
      capabilities: ['Authentication', 'Authorization', 'Threat Detection', 'Encryption']
    },
    {
      id: 'learning-optimizer',
      name: 'Learning Optimizer',
      type: 'secondary',
      capabilities: ['Model Training', 'Hyperparameter Tuning', 'Performance Optimization', 'A/B Testing']
    },
    {
      id: 'api-gateway',
      name: 'API Gateway',
      type: 'secondary',
      capabilities: ['Request Routing', 'Rate Limiting', 'API Management', 'Load Balancing']
    }
  ];

  const links: NetworkLink[] = [
    // Central Hub connections (coordination)
    { source: 'central-hub', target: 'ai-assistant', value: 8, type: 'collaboration' },
    { source: 'central-hub', target: 'code-expert', value: 7, type: 'collaboration' },
    { source: 'central-hub', target: 'creative-writer', value: 6, type: 'collaboration' },
    { source: 'central-hub', target: 'task-scheduler', value: 9, type: 'dependency' },
    { source: 'central-hub', target: 'api-gateway', value: 8, type: 'dependency' },
    
    // Data Hub connections (data flow)
    { source: 'data-hub', target: 'ai-assistant', value: 7, type: 'data-flow' },
    { source: 'data-hub', target: 'code-expert', value: 6, type: 'data-flow' },
    { source: 'data-hub', target: 'creative-writer', value: 5, type: 'data-flow' },
    { source: 'data-hub', target: 'language-processor', value: 8, type: 'data-flow' },
    { source: 'data-hub', target: 'image-analyzer', value: 7, type: 'data-flow' },
    { source: 'data-hub', target: 'learning-optimizer', value: 9, type: 'data-flow' },
    
    // Primary agent collaborations
    { source: 'ai-assistant', target: 'language-processor', value: 6, type: 'collaboration' },
    { source: 'ai-assistant', target: 'image-analyzer', value: 4, type: 'collaboration' },
    { source: 'code-expert', target: 'security-agent', value: 7, type: 'collaboration' },
    { source: 'code-expert', target: 'learning-optimizer', value: 5, type: 'collaboration' },
    { source: 'creative-writer', target: 'language-processor', value: 8, type: 'collaboration' },
    { source: 'creative-writer', target: 'image-analyzer', value: 6, type: 'collaboration' },
    
    // Secondary agent dependencies
    { source: 'task-scheduler', target: 'ai-assistant', value: 5, type: 'dependency' },
    { source: 'task-scheduler', target: 'code-expert', value: 5, type: 'dependency' },
    { source: 'task-scheduler', target: 'creative-writer', value: 4, type: 'dependency' },
    { source: 'security-agent', target: 'api-gateway', value: 8, type: 'dependency' },
    { source: 'api-gateway', target: 'ai-assistant', value: 6, type: 'dependency' },
    { source: 'api-gateway', target: 'code-expert', value: 6, type: 'dependency' },
    { source: 'api-gateway', target: 'creative-writer', value: 5, type: 'dependency' },
    
    // Cross-secondary collaborations
    { source: 'language-processor', target: 'image-analyzer', value: 4, type: 'collaboration' },
    { source: 'security-agent', target: 'learning-optimizer', value: 3, type: 'collaboration' },
    { source: 'task-scheduler', target: 'learning-optimizer', value: 5, type: 'collaboration' },
    
    // Hub interconnections
    { source: 'central-hub', target: 'data-hub', value: 10, type: 'data-flow' }
  ];

  return { nodes, links };
}

// Generate a smaller, simpler network for testing
export function generateSimpleNetworkData(): NetworkData {
  const nodes: NetworkNode[] = [
    {
      id: 'ai-assistant',
      name: 'AI Assistant',
      type: 'primary',
      capabilities: ['Q&A', 'Research', 'Writing']
    },
    {
      id: 'code-expert',
      name: 'Code Expert',
      type: 'primary',
      capabilities: ['Coding', 'Debugging']
    },
    {
      id: 'creative-writer',
      name: 'Creative Writer',
      type: 'primary',
      capabilities: ['Writing', 'Creativity']
    },
    {
      id: 'central-hub',
      name: 'Central Hub',
      type: 'hub',
      capabilities: ['Coordination', 'Management']
    },
    {
      id: 'data-processor',
      name: 'Data Processor',
      type: 'secondary',
      capabilities: ['Data Processing', 'Analytics']
    }
  ];

  const links: NetworkLink[] = [
    { source: 'central-hub', target: 'ai-assistant', value: 5, type: 'collaboration' },
    { source: 'central-hub', target: 'code-expert', value: 4, type: 'collaboration' },
    { source: 'central-hub', target: 'creative-writer', value: 3, type: 'collaboration' },
    { source: 'data-processor', target: 'ai-assistant', value: 6, type: 'data-flow' },
    { source: 'data-processor', target: 'code-expert', value: 4, type: 'data-flow' },
    { source: 'ai-assistant', target: 'creative-writer', value: 3, type: 'collaboration' }
  ];

  return { nodes, links };
}

// Generate dynamic network data based on agent activity
export function generateDynamicNetworkData(activeAgents: string[] = []): NetworkData {
  const baseData = generateAgentNetworkData();
  
  // Modify link strengths based on active agents
  const modifiedLinks = baseData.links.map(link => ({
    ...link,
    value: activeAgents.includes(link.source) || activeAgents.includes(link.target) 
      ? link.value * 1.5 
      : link.value
  }));
  
  // Add temporary nodes for active sessions
  const sessionNodes: NetworkNode[] = activeAgents.map(agentId => ({
    id: `${agentId}-session`,
    name: `${agentId} Session`,
    type: 'secondary' as const,
    capabilities: ['Active Session', 'Real-time Processing']
  }));
  
  // Add session links
  const sessionLinks: NetworkLink[] = activeAgents.map(agentId => ({
    source: agentId,
    target: `${agentId}-session`,
    value: 10,
    type: 'data-flow' as const
  }));
  
  return {
    nodes: [...baseData.nodes, ...sessionNodes],
    links: [...modifiedLinks, ...sessionLinks]
  };
}
