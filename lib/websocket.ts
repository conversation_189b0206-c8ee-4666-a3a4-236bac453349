// WebSocket client for real-time features

import { io, Socket } from 'socket.io-client';
import { useEffect, useState, useRef } from 'react';

export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  userId?: string;
  sessionId?: string;
}

export interface AgentStatus {
  agentId: string;
  status: 'idle' | 'processing' | 'error';
  lastActivity: string;
  activeUsers: number;
}

export interface NetworkUpdate {
  type: 'node_added' | 'node_removed' | 'connection_added' | 'connection_removed' | 'status_changed';
  nodeId?: string;
  connectionId?: string;
  data: any;
}

class WebSocketClient {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor(private url: string = 'ws://localhost:8001') {}

  connect(userId?: string, sessionId?: string): Promise<Socket> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve(this.socket);
        return;
      }

      this.socket = io(this.url, {
        auth: {
          userId,
          sessionId,
        },
        transports: ['websocket'],
      });

      this.socket.on('connect', () => {
        console.log('WebSocket connected');
        this.reconnectAttempts = 0;
        resolve(this.socket!);
      });

      this.socket.on('disconnect', (reason) => {
        console.log('WebSocket disconnected:', reason);
        this.handleReconnect();
      });

      this.socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        reject(error);
      });

      // Set up default event handlers
      this.setupDefaultHandlers();
    });
  }

  private setupDefaultHandlers() {
    if (!this.socket) return;

    this.socket.on('agent_status_update', (data: AgentStatus) => {
      this.emit('agentStatusUpdate', data);
    });

    this.socket.on('network_update', (data: NetworkUpdate) => {
      this.emit('networkUpdate', data);
    });

    this.socket.on('query_response', (data: any) => {
      this.emit('queryResponse', data);
    });

    this.socket.on('document_processed', (data: any) => {
      this.emit('documentProcessed', data);
    });
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      setTimeout(() => {
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.socket?.connect();
      }, delay);
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  // Event emitter functionality
  private eventListeners: { [key: string]: Function[] } = {};

  on(event: string, callback: Function) {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = [];
    }
    this.eventListeners[event].push(callback);
  }

  off(event: string, callback: Function) {
    if (this.eventListeners[event]) {
      this.eventListeners[event] = this.eventListeners[event].filter(cb => cb !== callback);
    }
  }

  private emit(event: string, data: any) {
    if (this.eventListeners[event]) {
      this.eventListeners[event].forEach(callback => callback(data));
    }
  }

  // Send messages
  sendMessage(type: string, payload: any, targetUserId?: string) {
    if (this.socket?.connected) {
      this.socket.emit('message', {
        type,
        payload,
        targetUserId,
        timestamp: new Date().toISOString(),
      });
    }
  }

  // Join/leave rooms
  joinRoom(roomId: string) {
    if (this.socket?.connected) {
      this.socket.emit('join_room', roomId);
    }
  }

  leaveRoom(roomId: string) {
    if (this.socket?.connected) {
      this.socket.emit('leave_room', roomId);
    }
  }

  // Agent-specific methods
  subscribeToAgent(agentId: string) {
    this.joinRoom(`agent_${agentId}`);
  }

  unsubscribeFromAgent(agentId: string) {
    this.leaveRoom(`agent_${agentId}`);
  }

  // Network-specific methods
  subscribeToNetwork() {
    this.joinRoom('network_updates');
  }

  unsubscribeFromNetwork() {
    this.leaveRoom('network_updates');
  }

  // Send query to agent
  sendQuery(agentId: string, query: string, sessionId?: string) {
    this.sendMessage('agent_query', {
      agentId,
      query,
      sessionId,
    });
  }

  // Update agent status
  updateAgentStatus(agentId: string, status: AgentStatus['status']) {
    this.sendMessage('agent_status_update', {
      agentId,
      status,
      timestamp: new Date().toISOString(),
    });
  }
}

// Singleton instance
export const wsClient = new WebSocketClient();

// React hooks for WebSocket functionality
export function useWebSocket(userId?: string, sessionId?: string) {
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const clientRef = useRef<WebSocketClient>(wsClient);

  useEffect(() => {
    const client = clientRef.current;

    const connect = async () => {
      try {
        await client.connect(userId, sessionId);
        setIsConnected(true);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Connection failed');
        setIsConnected(false);
      }
    };

    connect();

    return () => {
      client.disconnect();
      setIsConnected(false);
    };
  }, [userId, sessionId]);

  return {
    isConnected,
    error,
    client: clientRef.current,
  };
}

// Hook for agent status updates
export function useAgentStatus(agentId?: string) {
  const [agentStatuses, setAgentStatuses] = useState<{ [key: string]: AgentStatus }>({});
  const { client } = useWebSocket();

  useEffect(() => {
    const handleStatusUpdate = (status: AgentStatus) => {
      setAgentStatuses(prev => ({
        ...prev,
        [status.agentId]: status,
      }));
    };

    client.on('agentStatusUpdate', handleStatusUpdate);

    // Subscribe to specific agent if provided
    if (agentId) {
      client.subscribeToAgent(agentId);
    }

    return () => {
      client.off('agentStatusUpdate', handleStatusUpdate);
      if (agentId) {
        client.unsubscribeFromAgent(agentId);
      }
    };
  }, [client, agentId]);

  return {
    agentStatuses,
    getAgentStatus: (id: string) => agentStatuses[id],
  };
}

// Hook for network updates
export function useNetworkUpdates() {
  const [networkUpdates, setNetworkUpdates] = useState<NetworkUpdate[]>([]);
  const { client } = useWebSocket();

  useEffect(() => {
    const handleNetworkUpdate = (update: NetworkUpdate) => {
      setNetworkUpdates(prev => [...prev.slice(-99), update]); // Keep last 100 updates
    };

    client.on('networkUpdate', handleNetworkUpdate);
    client.subscribeToNetwork();

    return () => {
      client.off('networkUpdate', handleNetworkUpdate);
      client.unsubscribeFromNetwork();
    };
  }, [client]);

  return {
    networkUpdates,
    clearUpdates: () => setNetworkUpdates([]),
  };
}

// Hook for real-time queries
export function useRealTimeQueries(sessionId?: string) {
  const [queries, setQueries] = useState<any[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const { client } = useWebSocket();

  useEffect(() => {
    const handleQueryResponse = (response: any) => {
      setQueries(prev => [...prev, response]);
      setIsProcessing(false);
    };

    client.on('queryResponse', handleQueryResponse);

    return () => {
      client.off('queryResponse', handleQueryResponse);
    };
  }, [client]);

  const sendQuery = (agentId: string, query: string) => {
    setIsProcessing(true);
    client.sendQuery(agentId, query, sessionId);
  };

  return {
    queries,
    isProcessing,
    sendQuery,
    clearQueries: () => setQueries([]),
  };
}

// Hook for document processing updates
export function useDocumentProcessing() {
  const [processingStatus, setProcessingStatus] = useState<{ [key: string]: string }>({});
  const { client } = useWebSocket();

  useEffect(() => {
    const handleDocumentProcessed = (data: { documentId: string; status: string; chunks?: number }) => {
      setProcessingStatus(prev => ({
        ...prev,
        [data.documentId]: data.status,
      }));
    };

    client.on('documentProcessed', handleDocumentProcessed);

    return () => {
      client.off('documentProcessed', handleDocumentProcessed);
    };
  }, [client]);

  return {
    processingStatus,
    getDocumentStatus: (documentId: string) => processingStatus[documentId] || 'idle',
  };
}
