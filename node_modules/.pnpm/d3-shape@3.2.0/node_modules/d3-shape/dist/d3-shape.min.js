// https://d3js.org/d3-shape/ v3.2.0 Copyright 2010-2022 <PERSON>
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports,require("d3-path")):"function"==typeof define&&define.amd?define(["exports","d3-path"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).d3=t.d3||{},t.d3)}(this,(function(t,n){"use strict";function i(t){return function(){return t}}const e=Math.abs,s=Math.atan2,o=Math.cos,h=Math.max,_=Math.min,r=Math.sin,a=Math.sqrt,l=1e-12,c=Math.PI,u=c/2,f=2*c;function y(t){return t>1?0:t<-1?c:Math.acos(t)}function x(t){return t>=1?u:t<=-1?-u:Math.asin(t)}function p(t){let i=3;return t.digits=function(n){if(!arguments.length)return i;if(null==n)i=null;else{const t=Math.floor(n);if(!(t>=0))throw new RangeError(`invalid digits: ${n}`);i=t}return t},()=>new n.Path(i)}function v(t){return t.innerRadius}function d(t){return t.outerRadius}function T(t){return t.startAngle}function g(t){return t.endAngle}function m(t){return t&&t.padAngle}function b(t,n,i,e,s,o,h,_){var r=i-t,a=e-n,c=h-s,u=_-o,f=u*r-c*a;if(!(f*f<l))return[t+(f=(c*(n-o)-u*(t-s))/f)*r,n+f*a]}function w(t,n,i,e,s,o,_){var r=t-i,l=n-e,c=(_?o:-o)/a(r*r+l*l),u=c*l,f=-c*r,y=t+u,x=n+f,p=i+u,v=e+f,d=(y+p)/2,T=(x+v)/2,g=p-y,m=v-x,b=g*g+m*m,w=s-o,k=y*v-p*x,N=(m<0?-1:1)*a(h(0,w*w*b-k*k)),S=(k*m-g*N)/b,E=(-k*g-m*N)/b,A=(k*m+g*N)/b,P=(-k*g+m*N)/b,M=S-d,C=E-T,R=A-d,O=P-T;return M*M+C*C>R*R+O*O&&(S=A,E=P),{cx:S,cy:E,x01:-u,y01:-f,x11:S*(s/w-1),y11:E*(s/w-1)}}var k=Array.prototype.slice;function N(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function S(t){this._context=t}function E(t){return new S(t)}function A(t){return t[0]}function P(t){return t[1]}function M(t,n){var e=i(!0),s=null,o=E,h=null,_=p(r);function r(i){var r,a,l,c=(i=N(i)).length,u=!1;for(null==s&&(h=o(l=_())),r=0;r<=c;++r)!(r<c&&e(a=i[r],r,i))===u&&((u=!u)?h.lineStart():h.lineEnd()),u&&h.point(+t(a,r,i),+n(a,r,i));if(l)return h=null,l+""||null}return t="function"==typeof t?t:void 0===t?A:i(t),n="function"==typeof n?n:void 0===n?P:i(n),r.x=function(n){return arguments.length?(t="function"==typeof n?n:i(+n),r):t},r.y=function(t){return arguments.length?(n="function"==typeof t?t:i(+t),r):n},r.defined=function(t){return arguments.length?(e="function"==typeof t?t:i(!!t),r):e},r.curve=function(t){return arguments.length?(o=t,null!=s&&(h=o(s)),r):o},r.context=function(t){return arguments.length?(null==t?s=h=null:h=o(s=t),r):s},r}function C(t,n,e){var s=null,o=i(!0),h=null,_=E,r=null,a=p(l);function l(i){var l,c,u,f,y,x=(i=N(i)).length,p=!1,v=new Array(x),d=new Array(x);for(null==h&&(r=_(y=a())),l=0;l<=x;++l){if(!(l<x&&o(f=i[l],l,i))===p)if(p=!p)c=l,r.areaStart(),r.lineStart();else{for(r.lineEnd(),r.lineStart(),u=l-1;u>=c;--u)r.point(v[u],d[u]);r.lineEnd(),r.areaEnd()}p&&(v[l]=+t(f,l,i),d[l]=+n(f,l,i),r.point(s?+s(f,l,i):v[l],e?+e(f,l,i):d[l]))}if(y)return r=null,y+""||null}function c(){return M().defined(o).curve(_).context(h)}return t="function"==typeof t?t:void 0===t?A:i(+t),n="function"==typeof n?n:i(void 0===n?0:+n),e="function"==typeof e?e:void 0===e?P:i(+e),l.x=function(n){return arguments.length?(t="function"==typeof n?n:i(+n),s=null,l):t},l.x0=function(n){return arguments.length?(t="function"==typeof n?n:i(+n),l):t},l.x1=function(t){return arguments.length?(s=null==t?null:"function"==typeof t?t:i(+t),l):s},l.y=function(t){return arguments.length?(n="function"==typeof t?t:i(+t),e=null,l):n},l.y0=function(t){return arguments.length?(n="function"==typeof t?t:i(+t),l):n},l.y1=function(t){return arguments.length?(e=null==t?null:"function"==typeof t?t:i(+t),l):e},l.lineX0=l.lineY0=function(){return c().x(t).y(n)},l.lineY1=function(){return c().x(t).y(e)},l.lineX1=function(){return c().x(s).y(n)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:i(!!t),l):o},l.curve=function(t){return arguments.length?(_=t,null!=h&&(r=_(h)),l):_},l.context=function(t){return arguments.length?(null==t?h=r=null:r=_(h=t),l):h},l}function R(t,n){return n<t?-1:n>t?1:n>=t?0:NaN}function O(t){return t}S.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._context.lineTo(t,n)}}};var z=Y(E);function X(t){this._curve=t}function Y(t){function n(n){return new X(t(n))}return n._curve=t,n}function q(t){var n=t.curve;return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t.curve=function(t){return arguments.length?n(Y(t)):n()._curve},t}function B(){return q(M().curve(z))}function D(){var t=C().curve(z),n=t.curve,i=t.lineX0,e=t.lineX1,s=t.lineY0,o=t.lineY1;return t.angle=t.x,delete t.x,t.startAngle=t.x0,delete t.x0,t.endAngle=t.x1,delete t.x1,t.radius=t.y,delete t.y,t.innerRadius=t.y0,delete t.y0,t.outerRadius=t.y1,delete t.y1,t.lineStartAngle=function(){return q(i())},delete t.lineX0,t.lineEndAngle=function(){return q(e())},delete t.lineX1,t.lineInnerRadius=function(){return q(s())},delete t.lineY0,t.lineOuterRadius=function(){return q(o())},delete t.lineY1,t.curve=function(t){return arguments.length?n(Y(t)):n()._curve},t}function I(t,n){return[(n=+n)*Math.cos(t-=Math.PI/2),n*Math.sin(t)]}X.prototype={areaStart:function(){this._curve.areaStart()},areaEnd:function(){this._curve.areaEnd()},lineStart:function(){this._curve.lineStart()},lineEnd:function(){this._curve.lineEnd()},point:function(t,n){this._curve.point(n*Math.sin(t),n*-Math.cos(t))}};class j{constructor(t,n){this._context=t,this._x=n}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,n,t,n):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+n)/2,t,this._y0,t,n)}this._x0=t,this._y0=n}}class L{constructor(t){this._context=t}lineStart(){this._point=0}lineEnd(){}point(t,n){if(t=+t,n=+n,0===this._point)this._point=1;else{const i=I(this._x0,this._y0),e=I(this._x0,this._y0=(this._y0+n)/2),s=I(t,this._y0),o=I(t,n);this._context.moveTo(...i),this._context.bezierCurveTo(...e,...s,...o)}this._x0=t,this._y0=n}}function V(t){return new j(t,!0)}function W(t){return new j(t,!1)}function F(t){return new L(t)}function H(t){return t.source}function $(t){return t.target}function G(t){let n=H,e=$,s=A,o=P,h=null,_=null,r=p(a);function a(){let i;const a=k.call(arguments),l=n.apply(this,a),c=e.apply(this,a);if(null==h&&(_=t(i=r())),_.lineStart(),a[0]=l,_.point(+s.apply(this,a),+o.apply(this,a)),a[0]=c,_.point(+s.apply(this,a),+o.apply(this,a)),_.lineEnd(),i)return _=null,i+""||null}return a.source=function(t){return arguments.length?(n=t,a):n},a.target=function(t){return arguments.length?(e=t,a):e},a.x=function(t){return arguments.length?(s="function"==typeof t?t:i(+t),a):s},a.y=function(t){return arguments.length?(o="function"==typeof t?t:i(+t),a):o},a.context=function(n){return arguments.length?(null==n?h=_=null:_=t(h=n),a):h},a}const J=a(3);var K={draw(t,n){const i=.59436*a(n+_(n/28,.75)),e=i/2,s=e*J;t.moveTo(0,i),t.lineTo(0,-i),t.moveTo(-s,-e),t.lineTo(s,e),t.moveTo(-s,e),t.lineTo(s,-e)}},Q={draw(t,n){const i=a(n/c);t.moveTo(i,0),t.arc(0,0,i,0,f)}},U={draw(t,n){const i=a(n/5)/2;t.moveTo(-3*i,-i),t.lineTo(-i,-i),t.lineTo(-i,-3*i),t.lineTo(i,-3*i),t.lineTo(i,-i),t.lineTo(3*i,-i),t.lineTo(3*i,i),t.lineTo(i,i),t.lineTo(i,3*i),t.lineTo(-i,3*i),t.lineTo(-i,i),t.lineTo(-3*i,i),t.closePath()}};const Z=a(1/3),tt=2*Z;var nt={draw(t,n){const i=a(n/tt),e=i*Z;t.moveTo(0,-i),t.lineTo(e,0),t.lineTo(0,i),t.lineTo(-e,0),t.closePath()}},it={draw(t,n){const i=.62625*a(n);t.moveTo(0,-i),t.lineTo(i,0),t.lineTo(0,i),t.lineTo(-i,0),t.closePath()}},et={draw(t,n){const i=.87559*a(n-_(n/7,2));t.moveTo(-i,0),t.lineTo(i,0),t.moveTo(0,i),t.lineTo(0,-i)}},st={draw(t,n){const i=a(n),e=-i/2;t.rect(e,e,i,i)}},ot={draw(t,n){const i=.4431*a(n);t.moveTo(i,i),t.lineTo(i,-i),t.lineTo(-i,-i),t.lineTo(-i,i),t.closePath()}};const ht=r(c/10)/r(7*c/10),_t=r(f/10)*ht,rt=-o(f/10)*ht;var at={draw(t,n){const i=a(.8908130915292852*n),e=_t*i,s=rt*i;t.moveTo(0,-i),t.lineTo(e,s);for(let n=1;n<5;++n){const h=f*n/5,_=o(h),a=r(h);t.lineTo(a*i,-_*i),t.lineTo(_*e-a*s,a*e+_*s)}t.closePath()}};const lt=a(3);var ct={draw(t,n){const i=-a(n/(3*lt));t.moveTo(0,2*i),t.lineTo(-lt*i,-i),t.lineTo(lt*i,-i),t.closePath()}};const ut=a(3);var ft={draw(t,n){const i=.6824*a(n),e=i/2,s=i*ut/2;t.moveTo(0,-i),t.lineTo(s,e),t.lineTo(-s,e),t.closePath()}};const yt=-.5,xt=a(3)/2,pt=1/a(12),vt=3*(pt/2+1);var dt={draw(t,n){const i=a(n/vt),e=i/2,s=i*pt,o=e,h=i*pt+i,_=-o,r=h;t.moveTo(e,s),t.lineTo(o,h),t.lineTo(_,r),t.lineTo(yt*e-xt*s,xt*e+yt*s),t.lineTo(yt*o-xt*h,xt*o+yt*h),t.lineTo(yt*_-xt*r,xt*_+yt*r),t.lineTo(yt*e+xt*s,yt*s-xt*e),t.lineTo(yt*o+xt*h,yt*h-xt*o),t.lineTo(yt*_+xt*r,yt*r-xt*_),t.closePath()}},Tt={draw(t,n){const i=.6189*a(n-_(n/6,1.7));t.moveTo(-i,-i),t.lineTo(i,i),t.moveTo(-i,i),t.lineTo(i,-i)}};const gt=[Q,U,nt,st,at,ct,dt],mt=[Q,et,Tt,ft,K,ot,it];function bt(){}function wt(t,n,i){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+n)/6,(t._y0+4*t._y1+i)/6)}function kt(t){this._context=t}function Nt(t){this._context=t}function St(t){this._context=t}function Et(t,n){this._basis=new kt(t),this._beta=n}kt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:wt(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:wt(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},Nt.prototype={areaStart:bt,areaEnd:bt,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x2=t,this._y2=n;break;case 1:this._point=2,this._x3=t,this._y3=n;break;case 2:this._point=3,this._x4=t,this._y4=n,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+n)/6);break;default:wt(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},St.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var i=(this._x0+4*this._x1+t)/6,e=(this._y0+4*this._y1+n)/6;this._line?this._context.lineTo(i,e):this._context.moveTo(i,e);break;case 3:this._point=4;default:wt(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}},Et.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var t=this._x,n=this._y,i=t.length-1;if(i>0)for(var e,s=t[0],o=n[0],h=t[i]-s,_=n[i]-o,r=-1;++r<=i;)e=r/i,this._basis.point(this._beta*t[r]+(1-this._beta)*(s+e*h),this._beta*n[r]+(1-this._beta)*(o+e*_));this._x=this._y=null,this._basis.lineEnd()},point:function(t,n){this._x.push(+t),this._y.push(+n)}};var At=function t(n){function i(t){return 1===n?new kt(t):new Et(t,n)}return i.beta=function(n){return t(+n)},i}(.85);function Pt(t,n,i){t._context.bezierCurveTo(t._x1+t._k*(t._x2-t._x0),t._y1+t._k*(t._y2-t._y0),t._x2+t._k*(t._x1-n),t._y2+t._k*(t._y1-i),t._x2,t._y2)}function Mt(t,n){this._context=t,this._k=(1-n)/6}Mt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:Pt(this,this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2,this._x1=t,this._y1=n;break;case 2:this._point=3;default:Pt(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Ct=function t(n){function i(t){return new Mt(t,n)}return i.tension=function(n){return t(+n)},i}(0);function Rt(t,n){this._context=t,this._k=(1-n)/6}Rt.prototype={areaStart:bt,areaEnd:bt,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:Pt(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Ot=function t(n){function i(t){return new Rt(t,n)}return i.tension=function(n){return t(+n)},i}(0);function zt(t,n){this._context=t,this._k=(1-n)/6}zt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:Pt(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Xt=function t(n){function i(t){return new zt(t,n)}return i.tension=function(n){return t(+n)},i}(0);function Yt(t,n,i){var e=t._x1,s=t._y1,o=t._x2,h=t._y2;if(t._l01_a>l){var _=2*t._l01_2a+3*t._l01_a*t._l12_a+t._l12_2a,r=3*t._l01_a*(t._l01_a+t._l12_a);e=(e*_-t._x0*t._l12_2a+t._x2*t._l01_2a)/r,s=(s*_-t._y0*t._l12_2a+t._y2*t._l01_2a)/r}if(t._l23_a>l){var a=2*t._l23_2a+3*t._l23_a*t._l12_a+t._l12_2a,c=3*t._l23_a*(t._l23_a+t._l12_a);o=(o*a+t._x1*t._l23_2a-n*t._l12_2a)/c,h=(h*a+t._y1*t._l23_2a-i*t._l12_2a)/c}t._context.bezierCurveTo(e,s,o,h,t._x2,t._y2)}function qt(t,n){this._context=t,this._alpha=n}qt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var i=this._x2-t,e=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(i*i+e*e,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3;default:Yt(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Bt=function t(n){function i(t){return n?new qt(t,n):new Mt(t,0)}return i.alpha=function(n){return t(+n)},i}(.5);function Dt(t,n){this._context=t,this._alpha=n}Dt.prototype={areaStart:bt,areaEnd:bt,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){if(t=+t,n=+n,this._point){var i=this._x2-t,e=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(i*i+e*e,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:Yt(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var It=function t(n){function i(t){return n?new Dt(t,n):new Rt(t,0)}return i.alpha=function(n){return t(+n)},i}(.5);function jt(t,n){this._context=t,this._alpha=n}jt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var i=this._x2-t,e=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(i*i+e*e,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:Yt(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Lt=function t(n){function i(t){return n?new jt(t,n):new zt(t,0)}return i.alpha=function(n){return t(+n)},i}(.5);function Vt(t){this._context=t}function Wt(t){return t<0?-1:1}function Ft(t,n,i){var e=t._x1-t._x0,s=n-t._x1,o=(t._y1-t._y0)/(e||s<0&&-0),h=(i-t._y1)/(s||e<0&&-0),_=(o*s+h*e)/(e+s);return(Wt(o)+Wt(h))*Math.min(Math.abs(o),Math.abs(h),.5*Math.abs(_))||0}function Ht(t,n){var i=t._x1-t._x0;return i?(3*(t._y1-t._y0)/i-n)/2:n}function $t(t,n,i){var e=t._x0,s=t._y0,o=t._x1,h=t._y1,_=(o-e)/3;t._context.bezierCurveTo(e+_,s+_*n,o-_,h-_*i,o,h)}function Gt(t){this._context=t}function Jt(t){this._context=new Kt(t)}function Kt(t){this._context=t}function Qt(t){this._context=t}function Ut(t){var n,i,e=t.length-1,s=new Array(e),o=new Array(e),h=new Array(e);for(s[0]=0,o[0]=2,h[0]=t[0]+2*t[1],n=1;n<e-1;++n)s[n]=1,o[n]=4,h[n]=4*t[n]+2*t[n+1];for(s[e-1]=2,o[e-1]=7,h[e-1]=8*t[e-1]+t[e],n=1;n<e;++n)i=s[n]/o[n-1],o[n]-=i,h[n]-=i*h[n-1];for(s[e-1]=h[e-1]/o[e-1],n=e-2;n>=0;--n)s[n]=(h[n]-s[n+1])/o[n];for(o[e-1]=(t[e]+s[e-1])/2,n=0;n<e-1;++n)o[n]=2*t[n+1]-s[n+1];return[s,o]}function Zt(t,n){this._context=t,this._t=n}function tn(t,n){if((s=t.length)>1)for(var i,e,s,o=1,h=t[n[0]],_=h.length;o<s;++o)for(e=h,h=t[n[o]],i=0;i<_;++i)h[i][1]+=h[i][0]=isNaN(e[i][1])?e[i][0]:e[i][1]}function nn(t){for(var n=t.length,i=new Array(n);--n>=0;)i[n]=n;return i}function en(t,n){return t[n]}function sn(t){const n=[];return n.key=t,n}function on(t){var n=t.map(hn);return nn(t).sort((function(t,i){return n[t]-n[i]}))}function hn(t){for(var n,i=-1,e=0,s=t.length,o=-1/0;++i<s;)(n=+t[i][1])>o&&(o=n,e=i);return e}function _n(t){var n=t.map(rn);return nn(t).sort((function(t,i){return n[t]-n[i]}))}function rn(t){for(var n,i=0,e=-1,s=t.length;++e<s;)(n=+t[e][1])&&(i+=n);return i}Vt.prototype={areaStart:bt,areaEnd:bt,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,n){t=+t,n=+n,this._point?this._context.lineTo(t,n):(this._point=1,this._context.moveTo(t,n))}},Gt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:$t(this,this._t0,Ht(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){var i=NaN;if(n=+n,(t=+t)!==this._x1||n!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,$t(this,Ht(this,i=Ft(this,t,n)),i);break;default:$t(this,this._t0,i=Ft(this,t,n))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n,this._t0=i}}},(Jt.prototype=Object.create(Gt.prototype)).point=function(t,n){Gt.prototype.point.call(this,n,t)},Kt.prototype={moveTo:function(t,n){this._context.moveTo(n,t)},closePath:function(){this._context.closePath()},lineTo:function(t,n){this._context.lineTo(n,t)},bezierCurveTo:function(t,n,i,e,s,o){this._context.bezierCurveTo(n,t,e,i,o,s)}},Qt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,n=this._y,i=t.length;if(i)if(this._line?this._context.lineTo(t[0],n[0]):this._context.moveTo(t[0],n[0]),2===i)this._context.lineTo(t[1],n[1]);else for(var e=Ut(t),s=Ut(n),o=0,h=1;h<i;++o,++h)this._context.bezierCurveTo(e[0][o],s[0][o],e[1][o],s[1][o],t[h],n[h]);(this._line||0!==this._line&&1===i)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,n){this._x.push(+t),this._y.push(+n)}},Zt.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,n),this._context.lineTo(t,n);else{var i=this._x*(1-this._t)+t*this._t;this._context.lineTo(i,this._y),this._context.lineTo(i,n)}}this._x=t,this._y=n}},t.arc=function(){var t=v,n=d,h=i(0),k=null,N=T,S=g,E=m,A=null,P=p(M);function M(){var i,p,v=+t.apply(this,arguments),d=+n.apply(this,arguments),T=N.apply(this,arguments)-u,g=S.apply(this,arguments)-u,m=e(g-T),M=g>T;if(A||(A=i=P()),d<v&&(p=d,d=v,v=p),d>l)if(m>f-l)A.moveTo(d*o(T),d*r(T)),A.arc(0,0,d,T,g,!M),v>l&&(A.moveTo(v*o(g),v*r(g)),A.arc(0,0,v,g,T,M));else{var C,R,O=T,z=g,X=T,Y=g,q=m,B=m,D=E.apply(this,arguments)/2,I=D>l&&(k?+k.apply(this,arguments):a(v*v+d*d)),j=_(e(d-v)/2,+h.apply(this,arguments)),L=j,V=j;if(I>l){var W=x(I/v*r(D)),F=x(I/d*r(D));(q-=2*W)>l?(X+=W*=M?1:-1,Y-=W):(q=0,X=Y=(T+g)/2),(B-=2*F)>l?(O+=F*=M?1:-1,z-=F):(B=0,O=z=(T+g)/2)}var H=d*o(O),$=d*r(O),G=v*o(Y),J=v*r(Y);if(j>l){var K,Q=d*o(z),U=d*r(z),Z=v*o(X),tt=v*r(X);if(m<c)if(K=b(H,$,Z,tt,Q,U,G,J)){var nt=H-K[0],it=$-K[1],et=Q-K[0],st=U-K[1],ot=1/r(y((nt*et+it*st)/(a(nt*nt+it*it)*a(et*et+st*st)))/2),ht=a(K[0]*K[0]+K[1]*K[1]);L=_(j,(v-ht)/(ot-1)),V=_(j,(d-ht)/(ot+1))}else L=V=0}B>l?V>l?(C=w(Z,tt,H,$,d,V,M),R=w(Q,U,G,J,d,V,M),A.moveTo(C.cx+C.x01,C.cy+C.y01),V<j?A.arc(C.cx,C.cy,V,s(C.y01,C.x01),s(R.y01,R.x01),!M):(A.arc(C.cx,C.cy,V,s(C.y01,C.x01),s(C.y11,C.x11),!M),A.arc(0,0,d,s(C.cy+C.y11,C.cx+C.x11),s(R.cy+R.y11,R.cx+R.x11),!M),A.arc(R.cx,R.cy,V,s(R.y11,R.x11),s(R.y01,R.x01),!M))):(A.moveTo(H,$),A.arc(0,0,d,O,z,!M)):A.moveTo(H,$),v>l&&q>l?L>l?(C=w(G,J,Q,U,v,-L,M),R=w(H,$,Z,tt,v,-L,M),A.lineTo(C.cx+C.x01,C.cy+C.y01),L<j?A.arc(C.cx,C.cy,L,s(C.y01,C.x01),s(R.y01,R.x01),!M):(A.arc(C.cx,C.cy,L,s(C.y01,C.x01),s(C.y11,C.x11),!M),A.arc(0,0,v,s(C.cy+C.y11,C.cx+C.x11),s(R.cy+R.y11,R.cx+R.x11),M),A.arc(R.cx,R.cy,L,s(R.y11,R.x11),s(R.y01,R.x01),!M))):A.arc(0,0,v,Y,X,M):A.lineTo(G,J)}else A.moveTo(0,0);if(A.closePath(),i)return A=null,i+""||null}return M.centroid=function(){var i=(+t.apply(this,arguments)+ +n.apply(this,arguments))/2,e=(+N.apply(this,arguments)+ +S.apply(this,arguments))/2-c/2;return[o(e)*i,r(e)*i]},M.innerRadius=function(n){return arguments.length?(t="function"==typeof n?n:i(+n),M):t},M.outerRadius=function(t){return arguments.length?(n="function"==typeof t?t:i(+t),M):n},M.cornerRadius=function(t){return arguments.length?(h="function"==typeof t?t:i(+t),M):h},M.padRadius=function(t){return arguments.length?(k=null==t?null:"function"==typeof t?t:i(+t),M):k},M.startAngle=function(t){return arguments.length?(N="function"==typeof t?t:i(+t),M):N},M.endAngle=function(t){return arguments.length?(S="function"==typeof t?t:i(+t),M):S},M.padAngle=function(t){return arguments.length?(E="function"==typeof t?t:i(+t),M):E},M.context=function(t){return arguments.length?(A=null==t?null:t,M):A},M},t.area=C,t.areaRadial=D,t.curveBasis=function(t){return new kt(t)},t.curveBasisClosed=function(t){return new Nt(t)},t.curveBasisOpen=function(t){return new St(t)},t.curveBumpX=V,t.curveBumpY=W,t.curveBundle=At,t.curveCardinal=Ct,t.curveCardinalClosed=Ot,t.curveCardinalOpen=Xt,t.curveCatmullRom=Bt,t.curveCatmullRomClosed=It,t.curveCatmullRomOpen=Lt,t.curveLinear=E,t.curveLinearClosed=function(t){return new Vt(t)},t.curveMonotoneX=function(t){return new Gt(t)},t.curveMonotoneY=function(t){return new Jt(t)},t.curveNatural=function(t){return new Qt(t)},t.curveStep=function(t){return new Zt(t,.5)},t.curveStepAfter=function(t){return new Zt(t,1)},t.curveStepBefore=function(t){return new Zt(t,0)},t.line=M,t.lineRadial=B,t.link=G,t.linkHorizontal=function(){return G(V)},t.linkRadial=function(){const t=G(F);return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t},t.linkVertical=function(){return G(W)},t.pie=function(){var t=O,n=R,e=null,s=i(0),o=i(f),h=i(0);function _(i){var _,r,a,l,c,u=(i=N(i)).length,y=0,x=new Array(u),p=new Array(u),v=+s.apply(this,arguments),d=Math.min(f,Math.max(-f,o.apply(this,arguments)-v)),T=Math.min(Math.abs(d)/u,h.apply(this,arguments)),g=T*(d<0?-1:1);for(_=0;_<u;++_)(c=p[x[_]=_]=+t(i[_],_,i))>0&&(y+=c);for(null!=n?x.sort((function(t,i){return n(p[t],p[i])})):null!=e&&x.sort((function(t,n){return e(i[t],i[n])})),_=0,a=y?(d-u*g)/y:0;_<u;++_,v=l)r=x[_],l=v+((c=p[r])>0?c*a:0)+g,p[r]={data:i[r],index:_,value:c,startAngle:v,endAngle:l,padAngle:T};return p}return _.value=function(n){return arguments.length?(t="function"==typeof n?n:i(+n),_):t},_.sortValues=function(t){return arguments.length?(n=t,e=null,_):n},_.sort=function(t){return arguments.length?(e=t,n=null,_):e},_.startAngle=function(t){return arguments.length?(s="function"==typeof t?t:i(+t),_):s},_.endAngle=function(t){return arguments.length?(o="function"==typeof t?t:i(+t),_):o},_.padAngle=function(t){return arguments.length?(h="function"==typeof t?t:i(+t),_):h},_},t.pointRadial=I,t.radialArea=D,t.radialLine=B,t.stack=function(){var t=i([]),n=nn,e=tn,s=en;function o(i){var o,h,_=Array.from(t.apply(this,arguments),sn),r=_.length,a=-1;for(const t of i)for(o=0,++a;o<r;++o)(_[o][a]=[0,+s(t,_[o].key,a,i)]).data=t;for(o=0,h=N(n(_));o<r;++o)_[h[o]].index=o;return e(_,h),_}return o.keys=function(n){return arguments.length?(t="function"==typeof n?n:i(Array.from(n)),o):t},o.value=function(t){return arguments.length?(s="function"==typeof t?t:i(+t),o):s},o.order=function(t){return arguments.length?(n=null==t?nn:"function"==typeof t?t:i(Array.from(t)),o):n},o.offset=function(t){return arguments.length?(e=null==t?tn:t,o):e},o},t.stackOffsetDiverging=function(t,n){if((_=t.length)>0)for(var i,e,s,o,h,_,r=0,a=t[n[0]].length;r<a;++r)for(o=h=0,i=0;i<_;++i)(s=(e=t[n[i]][r])[1]-e[0])>0?(e[0]=o,e[1]=o+=s):s<0?(e[1]=h,e[0]=h+=s):(e[0]=0,e[1]=s)},t.stackOffsetExpand=function(t,n){if((e=t.length)>0){for(var i,e,s,o=0,h=t[0].length;o<h;++o){for(s=i=0;i<e;++i)s+=t[i][o][1]||0;if(s)for(i=0;i<e;++i)t[i][o][1]/=s}tn(t,n)}},t.stackOffsetNone=tn,t.stackOffsetSilhouette=function(t,n){if((i=t.length)>0){for(var i,e=0,s=t[n[0]],o=s.length;e<o;++e){for(var h=0,_=0;h<i;++h)_+=t[h][e][1]||0;s[e][1]+=s[e][0]=-_/2}tn(t,n)}},t.stackOffsetWiggle=function(t,n){if((s=t.length)>0&&(e=(i=t[n[0]]).length)>0){for(var i,e,s,o=0,h=1;h<e;++h){for(var _=0,r=0,a=0;_<s;++_){for(var l=t[n[_]],c=l[h][1]||0,u=(c-(l[h-1][1]||0))/2,f=0;f<_;++f){var y=t[n[f]];u+=(y[h][1]||0)-(y[h-1][1]||0)}r+=c,a+=u*c}i[h-1][1]+=i[h-1][0]=o,r&&(o-=a/r)}i[h-1][1]+=i[h-1][0]=o,tn(t,n)}},t.stackOrderAppearance=on,t.stackOrderAscending=_n,t.stackOrderDescending=function(t){return _n(t).reverse()},t.stackOrderInsideOut=function(t){var n,i,e=t.length,s=t.map(rn),o=on(t),h=0,_=0,r=[],a=[];for(n=0;n<e;++n)i=o[n],h<_?(h+=s[i],r.push(i)):(_+=s[i],a.push(i));return a.reverse().concat(r)},t.stackOrderNone=nn,t.stackOrderReverse=function(t){return nn(t).reverse()},t.symbol=function(t,n){let e=null,s=p(o);function o(){let i;if(e||(e=i=s()),t.apply(this,arguments).draw(e,+n.apply(this,arguments)),i)return e=null,i+""||null}return t="function"==typeof t?t:i(t||Q),n="function"==typeof n?n:i(void 0===n?64:+n),o.type=function(n){return arguments.length?(t="function"==typeof n?n:i(n),o):t},o.size=function(t){return arguments.length?(n="function"==typeof t?t:i(+t),o):n},o.context=function(t){return arguments.length?(e=null==t?null:t,o):e},o},t.symbolAsterisk=K,t.symbolCircle=Q,t.symbolCross=U,t.symbolDiamond=nt,t.symbolDiamond2=it,t.symbolPlus=et,t.symbolSquare=st,t.symbolSquare2=ot,t.symbolStar=at,t.symbolTimes=Tt,t.symbolTriangle=ct,t.symbolTriangle2=ft,t.symbolWye=dt,t.symbolX=Tt,t.symbols=gt,t.symbolsFill=gt,t.symbolsStroke=mt}));
