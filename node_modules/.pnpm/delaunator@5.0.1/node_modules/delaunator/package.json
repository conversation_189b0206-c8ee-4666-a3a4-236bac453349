{"name": "delaunator", "version": "5.0.1", "description": "An incredibly fast JavaScript library for Delaunay triangulation of 2D points", "main": "index.js", "module": "index.js", "type": "module", "jsdelivr": "delaunator.min.js", "unpkg": "delaunator.min.js", "sideEffects": false, "dependencies": {"robust-predicates": "^3.0.2"}, "devDependencies": {"@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "eslint": "^8.56.0", "eslint-config-mourner": "^3.0.0", "rollup": "^4.9.6"}, "repository": {"type": "git", "url": "https://github.com/mapbox/delaunator.git"}, "scripts": {"lint": "eslint index.js test/test.js bench.js rollup.config.js docs/diagrams.js", "pretest": "npm run lint", "test": "node --test-reporter spec test/test.js", "cov": "node --experimental-test-coverage test/test.js", "bench": "node bench.js", "build": "rollup -c", "start": "rollup -cw", "prepublishOnly": "npm test && npm run build"}, "files": ["index.js", "delaunator.js", "delaunator.min.js"], "eslintConfig": {"extends": "mourner", "rules": {"no-sequences": 0}, "parserOptions": {"ecmaVersion": 2020}}, "keywords": ["delaunay triangulation", "computational geometry", "algorithms"], "author": "<PERSON>", "license": "ISC"}