{"version": 3, "sources": ["../../../src/aws-data-api/common/index.ts"], "sourcesContent": ["import type { Field } from '@aws-sdk/client-rds-data';\nimport { TypeHint } from '@aws-sdk/client-rds-data';\nimport type { QueryTypingsValue } from '~/sql/sql.ts';\n\nexport function getValueFromDataApi(field: Field) {\n\tif (field.stringValue !== undefined) {\n\t\treturn field.stringValue;\n\t} else if (field.booleanValue !== undefined) {\n\t\treturn field.booleanValue;\n\t} else if (field.doubleValue !== undefined) {\n\t\treturn field.doubleValue;\n\t} else if (field.isNull !== undefined) {\n\t\treturn null;\n\t} else if (field.longValue !== undefined) {\n\t\treturn field.longValue;\n\t} else if (field.blobValue !== undefined) {\n\t\treturn field.blobValue;\n\t\t// eslint-disable-next-line unicorn/no-negated-condition\n\t} else if (field.arrayValue !== undefined) {\n\t\tif (field.arrayValue.stringValues !== undefined) {\n\t\t\treturn field.arrayValue.stringValues;\n\t\t}\n\t\tif (field.arrayValue.longValues !== undefined) {\n\t\t\treturn field.arrayValue.longValues;\n\t\t}\n\t\tif (field.arrayValue.doubleValues !== undefined) {\n\t\t\treturn field.arrayValue.doubleValues;\n\t\t}\n\t\tif (field.arrayValue.booleanValues !== undefined) {\n\t\t\treturn field.arrayValue.booleanValues;\n\t\t}\n\t\tif (field.arrayValue.arrayValues !== undefined) {\n\t\t\treturn field.arrayValue.arrayValues;\n\t\t}\n\n\t\tthrow new Error('Unknown array type');\n\t} else {\n\t\tthrow new Error('Unknown type');\n\t}\n}\n\nexport function typingsToAwsTypeHint(typings?: QueryTypingsValue): TypeHint | undefined {\n\tif (typings === 'date') {\n\t\treturn TypeHint.DATE;\n\t} else if (typings === 'decimal') {\n\t\treturn TypeHint.DECIMAL;\n\t} else if (typings === 'json') {\n\t\treturn TypeHint.JSON;\n\t} else if (typings === 'time') {\n\t\treturn TypeHint.TIME;\n\t} else if (typings === 'timestamp') {\n\t\treturn TypeHint.TIMESTAMP;\n\t} else if (typings === 'uuid') {\n\t\treturn TypeHint.UUID;\n\t} else {\n\t\treturn undefined;\n\t}\n}\n\nexport function toValueParam(value: any, typings?: QueryTypingsValue): { value: Field; typeHint?: TypeHint } {\n\tconst response: { value: Field; typeHint?: TypeHint } = {\n\t\tvalue: {} as any,\n\t\ttypeHint: typingsToAwsTypeHint(typings),\n\t};\n\n\tif (value === null) {\n\t\tresponse.value = { isNull: true };\n\t} else if (typeof value === 'string') {\n\t\tswitch (response.typeHint) {\n\t\t\tcase TypeHint.DATE: {\n\t\t\t\tresponse.value = { stringValue: value.split('T')[0]! };\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tcase TypeHint.TIMESTAMP: {\n\t\t\t\tresponse.value = { stringValue: value.replace('T', ' ').replace('Z', '') };\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tdefault: {\n\t\t\t\tresponse.value = { stringValue: value };\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t} else if (typeof value === 'number' && Number.isInteger(value)) {\n\t\tresponse.value = { longValue: value };\n\t} else if (typeof value === 'number' && !Number.isInteger(value)) {\n\t\tresponse.value = { doubleValue: value };\n\t} else if (typeof value === 'boolean') {\n\t\tresponse.value = { booleanValue: value };\n\t} else if (value instanceof Date) { // eslint-disable-line no-instanceof/no-instanceof\n\t\t// TODO: check if this clause is needed? Seems like date value always comes as string\n\t\tresponse.value = { stringValue: value.toISOString().replace('T', ' ').replace('Z', '') };\n\t} else {\n\t\tthrow new Error(`Unknown type for ${value}`);\n\t}\n\n\treturn response;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,6BAAyB;AAGlB,SAAS,oBAAoB,OAAc;AACjD,MAAI,MAAM,gBAAgB,QAAW;AACpC,WAAO,MAAM;AAAA,EACd,WAAW,MAAM,iBAAiB,QAAW;AAC5C,WAAO,MAAM;AAAA,EACd,WAAW,MAAM,gBAAgB,QAAW;AAC3C,WAAO,MAAM;AAAA,EACd,WAAW,MAAM,WAAW,QAAW;AACtC,WAAO;AAAA,EACR,WAAW,MAAM,cAAc,QAAW;AACzC,WAAO,MAAM;AAAA,EACd,WAAW,MAAM,cAAc,QAAW;AACzC,WAAO,MAAM;AAAA,EAEd,WAAW,MAAM,eAAe,QAAW;AAC1C,QAAI,MAAM,WAAW,iBAAiB,QAAW;AAChD,aAAO,MAAM,WAAW;AAAA,IACzB;AACA,QAAI,MAAM,WAAW,eAAe,QAAW;AAC9C,aAAO,MAAM,WAAW;AAAA,IACzB;AACA,QAAI,MAAM,WAAW,iBAAiB,QAAW;AAChD,aAAO,MAAM,WAAW;AAAA,IACzB;AACA,QAAI,MAAM,WAAW,kBAAkB,QAAW;AACjD,aAAO,MAAM,WAAW;AAAA,IACzB;AACA,QAAI,MAAM,WAAW,gBAAgB,QAAW;AAC/C,aAAO,MAAM,WAAW;AAAA,IACzB;AAEA,UAAM,IAAI,MAAM,oBAAoB;AAAA,EACrC,OAAO;AACN,UAAM,IAAI,MAAM,cAAc;AAAA,EAC/B;AACD;AAEO,SAAS,qBAAqB,SAAmD;AACvF,MAAI,YAAY,QAAQ;AACvB,WAAO,gCAAS;AAAA,EACjB,WAAW,YAAY,WAAW;AACjC,WAAO,gCAAS;AAAA,EACjB,WAAW,YAAY,QAAQ;AAC9B,WAAO,gCAAS;AAAA,EACjB,WAAW,YAAY,QAAQ;AAC9B,WAAO,gCAAS;AAAA,EACjB,WAAW,YAAY,aAAa;AACnC,WAAO,gCAAS;AAAA,EACjB,WAAW,YAAY,QAAQ;AAC9B,WAAO,gCAAS;AAAA,EACjB,OAAO;AACN,WAAO;AAAA,EACR;AACD;AAEO,SAAS,aAAa,OAAY,SAAoE;AAC5G,QAAM,WAAkD;AAAA,IACvD,OAAO,CAAC;AAAA,IACR,UAAU,qBAAqB,OAAO;AAAA,EACvC;AAEA,MAAI,UAAU,MAAM;AACnB,aAAS,QAAQ,EAAE,QAAQ,KAAK;AAAA,EACjC,WAAW,OAAO,UAAU,UAAU;AACrC,YAAQ,SAAS,UAAU;AAAA,MAC1B,KAAK,gCAAS,MAAM;AACnB,iBAAS,QAAQ,EAAE,aAAa,MAAM,MAAM,GAAG,EAAE,CAAC,EAAG;AACrD;AAAA,MACD;AAAA,MACA,KAAK,gCAAS,WAAW;AACxB,iBAAS,QAAQ,EAAE,aAAa,MAAM,QAAQ,KAAK,GAAG,EAAE,QAAQ,KAAK,EAAE,EAAE;AACzE;AAAA,MACD;AAAA,MACA,SAAS;AACR,iBAAS,QAAQ,EAAE,aAAa,MAAM;AACtC;AAAA,MACD;AAAA,IACD;AAAA,EACD,WAAW,OAAO,UAAU,YAAY,OAAO,UAAU,KAAK,GAAG;AAChE,aAAS,QAAQ,EAAE,WAAW,MAAM;AAAA,EACrC,WAAW,OAAO,UAAU,YAAY,CAAC,OAAO,UAAU,KAAK,GAAG;AACjE,aAAS,QAAQ,EAAE,aAAa,MAAM;AAAA,EACvC,WAAW,OAAO,UAAU,WAAW;AACtC,aAAS,QAAQ,EAAE,cAAc,MAAM;AAAA,EACxC,WAAW,iBAAiB,MAAM;AAEjC,aAAS,QAAQ,EAAE,aAAa,MAAM,YAAY,EAAE,QAAQ,KAAK,GAAG,EAAE,QAAQ,KAAK,EAAE,EAAE;AAAA,EACxF,OAAO;AACN,UAAM,IAAI,MAAM,oBAAoB,KAAK,EAAE;AAAA,EAC5C;AAEA,SAAO;AACR;", "names": []}