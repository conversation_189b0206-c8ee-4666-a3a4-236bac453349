{"version": 3, "sources": ["../../src/bun-sql/migrator.ts"], "sourcesContent": ["import type { MigrationConfig } from '~/migrator.ts';\nimport { readMigrationFiles } from '~/migrator.ts';\nimport type { BunSQLDatabase } from './driver.ts';\n\nexport async function migrate<TSchema extends Record<string, unknown>>(\n\tdb: BunSQLDatabase<TSchema>,\n\tconfig: MigrationConfig,\n) {\n\tconst migrations = readMigrationFiles(config);\n\tawait db.dialect.migrate(migrations, db.session, config);\n}\n"], "mappings": "AACA,SAAS,0BAA0B;AAGnC,eAAsB,QACrB,IACA,QACC;AACD,QAAM,aAAa,mBAAmB,MAAM;AAC5C,QAAM,GAAG,QAAQ,QAAQ,YAAY,GAAG,SAAS,MAAM;AACxD;", "names": []}