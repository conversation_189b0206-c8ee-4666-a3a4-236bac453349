{"version": 3, "sources": ["../../src/bun-sqlite/driver.ts"], "sourcesContent": ["/// <reference types=\"bun-types\" />\n\nimport { Database } from 'bun:sqlite';\nimport { entityKind } from '~/entity.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { BaseSQLiteDatabase } from '~/sqlite-core/db.ts';\nimport { SQLiteSyncDialect } from '~/sqlite-core/dialect.ts';\nimport { type DrizzleConfig, isConfig } from '~/utils.ts';\nimport { SQLiteBunSession } from './session.ts';\n\nexport class BunSQLiteDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends BaseSQLiteDatabase<'sync', void, TSchema> {\n\tstatic override readonly [entityKind]: string = 'BunSQLiteDatabase';\n}\n\ntype DrizzleBunSqliteDatabaseOptions = {\n\t/**\n\t * Open the database as read-only (no write operations, no create).\n\t *\n\t * Equivalent to {@link constants.SQLITE_OPEN_READONLY}\n\t */\n\treadonly?: boolean;\n\t/**\n\t * Allow creating a new database\n\t *\n\t * Equivalent to {@link constants.SQLITE_OPEN_CREATE}\n\t */\n\tcreate?: boolean;\n\t/**\n\t * Open the database as read-write\n\t *\n\t * Equivalent to {@link constants.SQLITE_OPEN_READWRITE}\n\t */\n\treadwrite?: boolean;\n};\n\nexport type DrizzleBunSqliteDatabaseConfig =\n\t| ({\n\t\tsource?: string;\n\t} & DrizzleBunSqliteDatabaseOptions)\n\t| string\n\t| undefined;\n\nfunction construct<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: Database,\n\tconfig: DrizzleConfig<TSchema> = {},\n): BunSQLiteDatabase<TSchema> & {\n\t$client: Database;\n} {\n\tconst dialect = new SQLiteSyncDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new SQLiteBunSession(client, dialect, schema, { logger });\n\tconst db = new BunSQLiteDatabase('sync', dialect, session, schema) as BunSQLiteDatabase<TSchema>;\n\t(<any> db).$client = client;\n\n\treturn db as any;\n}\n\nexport function drizzle<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends Database = Database,\n>(\n\t...params:\n\t\t| []\n\t\t| [\n\t\t\tTClient | string,\n\t\t]\n\t\t| [\n\t\t\tTClient | string,\n\t\t\tDrizzleConfig<TSchema>,\n\t\t]\n\t\t| [\n\t\t\t(\n\t\t\t\t& DrizzleConfig<TSchema>\n\t\t\t\t& ({\n\t\t\t\t\tconnection?: DrizzleBunSqliteDatabaseConfig;\n\t\t\t\t} | {\n\t\t\t\t\tclient: TClient;\n\t\t\t\t})\n\t\t\t),\n\t\t]\n): BunSQLiteDatabase<TSchema> & {\n\t$client: TClient;\n} {\n\tif (params[0] === undefined || typeof params[0] === 'string') {\n\t\tconst instance = params[0] === undefined ? new Database() : new Database(params[0]);\n\n\t\treturn construct(instance, params[1]) as any;\n\t}\n\n\tif (isConfig(params[0])) {\n\t\tconst { connection, client, ...drizzleConfig } = params[0] as\n\t\t\t& ({\n\t\t\t\tconnection?: DrizzleBunSqliteDatabaseConfig | string;\n\t\t\t\tclient?: TClient;\n\t\t\t})\n\t\t\t& DrizzleConfig<TSchema>;\n\n\t\tif (client) return construct(client, drizzleConfig) as any;\n\n\t\tif (typeof connection === 'object') {\n\t\t\tconst { source, ...opts } = connection;\n\n\t\t\tconst options = Object.values(opts).filter((v) => v !== undefined).length ? opts : undefined;\n\n\t\t\tconst instance = new Database(source, options);\n\n\t\t\treturn construct(instance, drizzleConfig) as any;\n\t\t}\n\n\t\tconst instance = new Database(connection);\n\n\t\treturn construct(instance, drizzleConfig) as any;\n\t}\n\n\treturn construct(params[0] as Database, params[1] as DrizzleConfig<TSchema> | undefined) as any;\n}\n\nexport namespace drizzle {\n\texport function mock<TSchema extends Record<string, unknown> = Record<string, never>>(\n\t\tconfig?: DrizzleConfig<TSchema>,\n\t): BunSQLiteDatabase<TSchema> & {\n\t\t$client: '$client is not available on drizzle.mock()';\n\t} {\n\t\treturn construct({} as any, config) as any;\n\t}\n}\n"], "mappings": "AAEA,SAAS,gBAAgB;AACzB,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAC9B;AAAA,EACC;AAAA,EACA;AAAA,OAGM;AACP,SAAS,0BAA0B;AACnC,SAAS,yBAAyB;AAClC,SAA6B,gBAAgB;AAC7C,SAAS,wBAAwB;AAE1B,MAAM,0BAEH,mBAA0C;AAAA,EACnD,QAA0B,UAAU,IAAY;AACjD;AA8BA,SAAS,UACR,QACA,SAAiC,CAAC,GAGjC;AACD,QAAM,UAAU,IAAI,kBAAkB,EAAE,QAAQ,OAAO,OAAO,CAAC;AAC/D,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,iBAAiB,QAAQ,SAAS,QAAQ,EAAE,OAAO,CAAC;AACxE,QAAM,KAAK,IAAI,kBAAkB,QAAQ,SAAS,SAAS,MAAM;AACjE,EAAO,GAAI,UAAU;AAErB,SAAO;AACR;AAEO,SAAS,WAIZ,QAqBF;AACD,MAAI,OAAO,CAAC,MAAM,UAAa,OAAO,OAAO,CAAC,MAAM,UAAU;AAC7D,UAAM,WAAW,OAAO,CAAC,MAAM,SAAY,IAAI,SAAS,IAAI,IAAI,SAAS,OAAO,CAAC,CAAC;AAElF,WAAO,UAAU,UAAU,OAAO,CAAC,CAAC;AAAA,EACrC;AAEA,MAAI,SAAS,OAAO,CAAC,CAAC,GAAG;AACxB,UAAM,EAAE,YAAY,QAAQ,GAAG,cAAc,IAAI,OAAO,CAAC;AAOzD,QAAI,OAAQ,QAAO,UAAU,QAAQ,aAAa;AAElD,QAAI,OAAO,eAAe,UAAU;AACnC,YAAM,EAAE,QAAQ,GAAG,KAAK,IAAI;AAE5B,YAAM,UAAU,OAAO,OAAO,IAAI,EAAE,OAAO,CAAC,MAAM,MAAM,MAAS,EAAE,SAAS,OAAO;AAEnF,YAAMA,YAAW,IAAI,SAAS,QAAQ,OAAO;AAE7C,aAAO,UAAUA,WAAU,aAAa;AAAA,IACzC;AAEA,UAAM,WAAW,IAAI,SAAS,UAAU;AAExC,WAAO,UAAU,UAAU,aAAa;AAAA,EACzC;AAEA,SAAO,UAAU,OAAO,CAAC,GAAe,OAAO,CAAC,CAAuC;AACxF;AAAA,CAEO,CAAUC,aAAV;AACC,WAAS,KACf,QAGC;AACD,WAAO,UAAU,CAAC,GAAU,MAAM;AAAA,EACnC;AANO,EAAAA,SAAS;AAAA,GADA;", "names": ["instance", "drizzle"]}