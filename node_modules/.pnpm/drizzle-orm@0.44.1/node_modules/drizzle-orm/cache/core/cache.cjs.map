{"version": 3, "sources": ["../../../src/cache/core/cache.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { Table } from '~/index.ts';\nimport type { CacheConfig } from './types.ts';\n\nexport abstract class Cache {\n\tstatic readonly [entityKind]: string = 'Cache';\n\n\tabstract strategy(): 'explicit' | 'all';\n\n\t/**\n\t * Invoked if we should check cache for cached response\n\t * @param sql\n\t * @param tables\n\t */\n\tabstract get(\n\t\tkey: string,\n\t\ttables: string[],\n\t\tisTag: boolean,\n\t\tisAutoInvalidate?: boolean,\n\t): Promise<any[] | undefined>;\n\n\t/**\n\t * Invoked if new query should be inserted to cache\n\t * @param sql\n\t * @param tables\n\t */\n\tabstract put(\n\t\thashedQuery: string,\n\t\tresponse: any,\n\t\ttables: string[],\n\t\tisTag: boolean,\n\t\tconfig?: CacheConfig,\n\t): Promise<void>;\n\n\t/**\n\t * Invoked if insert, update, delete was invoked\n\t * @param tables\n\t */\n\tabstract onMutate(\n\t\tparams: MutationOption,\n\t): Promise<void>;\n}\n\nexport class NoopCache extends Cache {\n\toverride strategy() {\n\t\treturn 'all' as const;\n\t}\n\n\tstatic override readonly [entityKind]: string = 'NoopCache';\n\n\toverride async get(_key: string): Promise<any[] | undefined> {\n\t\treturn undefined;\n\t}\n\toverride async put(\n\t\t_hashedQuery: string,\n\t\t_response: any,\n\t\t_tables: string[],\n\t\t_config?: any,\n\t): Promise<void> {\n\t\t// noop\n\t}\n\toverride async onMutate(_params: MutationOption): Promise<void> {\n\t\t// noop\n\t}\n}\n\nexport type MutationOption = { tags?: string | string[]; tables?: Table<any> | Table<any>[] | string | string[] };\n\nexport async function hashQuery(sql: string, params?: any[]) {\n\tconst dataToHash = `${sql}-${JSON.stringify(params)}`;\n\tconst encoder = new TextEncoder();\n\tconst data = encoder.encode(dataToHash);\n\tconst hashBuffer = await crypto.subtle.digest('SHA-256', data);\n\tconst hashArray = [...new Uint8Array(hashBuffer)];\n\tconst hashHex = hashArray.map((b) => b.toString(16).padStart(2, '0')).join('');\n\n\treturn hashHex;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAIpB,MAAe,MAAM;AAAA,EAC3B,QAAiB,wBAAU,IAAY;AAoCxC;AAEO,MAAM,kBAAkB,MAAM;AAAA,EAC3B,WAAW;AACnB,WAAO;AAAA,EACR;AAAA,EAEA,QAA0B,wBAAU,IAAY;AAAA,EAEhD,MAAe,IAAI,MAA0C;AAC5D,WAAO;AAAA,EACR;AAAA,EACA,MAAe,IACd,cACA,WACA,SACA,SACgB;AAAA,EAEjB;AAAA,EACA,MAAe,SAAS,SAAwC;AAAA,EAEhE;AACD;AAIA,eAAsB,UAAU,KAAa,QAAgB;AAC5D,QAAM,aAAa,GAAG,GAAG,IAAI,KAAK,UAAU,MAAM,CAAC;AACnD,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,OAAO,QAAQ,OAAO,UAAU;AACtC,QAAM,aAAa,MAAM,OAAO,OAAO,OAAO,WAAW,IAAI;AAC7D,QAAM,YAAY,CAAC,GAAG,IAAI,WAAW,UAAU,CAAC;AAChD,QAAM,UAAU,UAAU,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE;AAE7E,SAAO;AACR;", "names": []}