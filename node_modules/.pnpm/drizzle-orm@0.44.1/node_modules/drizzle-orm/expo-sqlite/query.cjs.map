{"version": 3, "sources": ["../../src/expo-sqlite/query.ts"], "sourcesContent": ["import { addDatabaseChangeListener } from 'expo-sqlite';\nimport { useEffect, useState } from 'react';\nimport { is } from '~/entity.ts';\nimport { SQL } from '~/sql/sql.ts';\nimport type { AnySQLiteSelect } from '~/sqlite-core/index.ts';\nimport { getTableConfig, getViewConfig, SQLiteTable, SQLiteView } from '~/sqlite-core/index.ts';\nimport { SQLiteRelationalQuery } from '~/sqlite-core/query-builders/query.ts';\nimport { Subquery } from '~/subquery.ts';\n\nexport const useLiveQuery = <T extends Pick<AnySQLiteSelect, '_' | 'then'> | SQLiteRelationalQuery<'sync', unknown>>(\n\tquery: T,\n\tdeps: unknown[] = [],\n) => {\n\tconst [data, setData] = useState<Awaited<T>>(\n\t\t(is(query, SQLiteRelationalQuery) && query.mode === 'first' ? undefined : []) as Awaited<T>,\n\t);\n\tconst [error, setError] = useState<Error>();\n\tconst [updatedAt, setUpdatedAt] = useState<Date>();\n\n\tuseEffect(() => {\n\t\tconst entity = is(query, SQLiteRelationalQuery) ? query.table : (query as AnySQLiteSelect).config.table;\n\n\t\tif (is(entity, Subquery) || is(entity, SQL)) {\n\t\t\tsetError(new Error('Selecting from subqueries and SQL are not supported in useLiveQuery'));\n\t\t\treturn;\n\t\t}\n\n\t\tlet listener: ReturnType<typeof addDatabaseChangeListener> | undefined;\n\n\t\tconst handleData = (data: any) => {\n\t\t\tsetData(data);\n\t\t\tsetUpdatedAt(new Date());\n\t\t};\n\n\t\tquery.then(handleData).catch(setError);\n\n\t\tif (is(entity, SQLiteTable) || is(entity, SQLiteView)) {\n\t\t\tconst config = is(entity, SQLiteTable) ? getTableConfig(entity) : getViewConfig(entity);\n\t\t\tlistener = addDatabaseChangeListener(({ tableName }) => {\n\t\t\t\tif (config.name === tableName) {\n\t\t\t\t\tquery.then(handleData).catch(setError);\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\treturn () => {\n\t\t\tlistener?.remove();\n\t\t};\n\t}, deps);\n\n\treturn {\n\t\tdata,\n\t\terror,\n\t\tupdatedAt,\n\t} as const;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAA0C;AAC1C,mBAAoC;AACpC,oBAAmB;AACnB,iBAAoB;AAEpB,yBAAuE;AACvE,mBAAsC;AACtC,sBAAyB;AAElB,MAAM,eAAe,CAC3B,OACA,OAAkB,CAAC,MACf;AACJ,QAAM,CAAC,MAAM,OAAO,QAAI;AAAA,QACtB,kBAAG,OAAO,kCAAqB,KAAK,MAAM,SAAS,UAAU,SAAY,CAAC;AAAA,EAC5E;AACA,QAAM,CAAC,OAAO,QAAQ,QAAI,uBAAgB;AAC1C,QAAM,CAAC,WAAW,YAAY,QAAI,uBAAe;AAEjD,8BAAU,MAAM;AACf,UAAM,aAAS,kBAAG,OAAO,kCAAqB,IAAI,MAAM,QAAS,MAA0B,OAAO;AAElG,YAAI,kBAAG,QAAQ,wBAAQ,SAAK,kBAAG,QAAQ,cAAG,GAAG;AAC5C,eAAS,IAAI,MAAM,qEAAqE,CAAC;AACzF;AAAA,IACD;AAEA,QAAI;AAEJ,UAAM,aAAa,CAACA,UAAc;AACjC,cAAQA,KAAI;AACZ,mBAAa,oBAAI,KAAK,CAAC;AAAA,IACxB;AAEA,UAAM,KAAK,UAAU,EAAE,MAAM,QAAQ;AAErC,YAAI,kBAAG,QAAQ,8BAAW,SAAK,kBAAG,QAAQ,6BAAU,GAAG;AACtD,YAAM,aAAS,kBAAG,QAAQ,8BAAW,QAAI,mCAAe,MAAM,QAAI,kCAAc,MAAM;AACtF,qBAAW,8CAA0B,CAAC,EAAE,UAAU,MAAM;AACvD,YAAI,OAAO,SAAS,WAAW;AAC9B,gBAAM,KAAK,UAAU,EAAE,MAAM,QAAQ;AAAA,QACtC;AAAA,MACD,CAAC;AAAA,IACF;AAEA,WAAO,MAAM;AACZ,gBAAU,OAAO;AAAA,IAClB;AAAA,EACD,GAAG,IAAI;AAEP,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;", "names": ["data"]}