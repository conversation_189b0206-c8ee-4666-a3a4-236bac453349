{"version": 3, "sources": ["../../src/gel-core/checks.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { SQL } from '~/sql/index.ts';\nimport type { GelTable } from './table.ts';\n\nexport class CheckBuilder {\n\tstatic readonly [entityKind]: string = 'GelCheckBuilder';\n\n\tprotected brand!: 'GelConstraintBuilder';\n\n\tconstructor(public name: string, public value: SQL) {}\n\n\t/** @internal */\n\tbuild(table: GelTable): Check {\n\t\treturn new Check(table, this);\n\t}\n}\n\nexport class Check {\n\tstatic readonly [entityKind]: string = 'GelCheck';\n\n\treadonly name: string;\n\treadonly value: SQL;\n\n\tconstructor(public table: GelTable, builder: CheckBuilder) {\n\t\tthis.name = builder.name;\n\t\tthis.value = builder.value;\n\t}\n}\n\nexport function check(name: string, value: SQL): CheckBuilder {\n\treturn new CheckBuilder(name, value);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAIpB,MAAM,aAAa;AAAA,EAKzB,YAAmB,MAAqB,OAAY;AAAjC;AAAqB;AAAA,EAAa;AAAA,EAJrD,QAAiB,wBAAU,IAAY;AAAA,EAE7B;AAAA;AAAA,EAKV,MAAM,OAAwB;AAC7B,WAAO,IAAI,MAAM,OAAO,IAAI;AAAA,EAC7B;AACD;AAEO,MAAM,MAAM;AAAA,EAMlB,YAAmB,OAAiB,SAAuB;AAAxC;AAClB,SAAK,OAAO,QAAQ;AACpB,SAAK,QAAQ,QAAQ;AAAA,EACtB;AAAA,EARA,QAAiB,wBAAU,IAAY;AAAA,EAE9B;AAAA,EACA;AAMV;AAEO,SAAS,MAAM,MAAc,OAA0B;AAC7D,SAAO,IAAI,aAAa,MAAM,KAAK;AACpC;", "names": []}