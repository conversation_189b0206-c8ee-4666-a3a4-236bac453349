{"version": 3, "sources": ["../../../src/gel-core/columns/duration.ts"], "sourcesContent": ["import type { Duration } from 'gel';\nimport type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn, GelColumnBuilder } from './common.ts';\n\nexport type GelDurationBuilderInitial<TName extends string> = GelDurationBuilder<{\n\tname: TName;\n\tdataType: 'duration';\n\tcolumnType: 'GelDuration';\n\tdata: Duration;\n\tdriverParam: Duration;\n\tenumValues: undefined;\n}>;\n\nexport class GelDurationBuilder<T extends ColumnBuilderBaseConfig<'duration', 'GelDuration'>>\n\textends GelColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'GelDurationBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t) {\n\t\tsuper(name, 'duration', 'GelDuration');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelDuration<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelDuration<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class GelDuration<T extends ColumnBaseConfig<'duration', 'GelDuration'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelDuration';\n\n\tgetSQLType(): string {\n\t\treturn `duration`;\n\t}\n}\n\nexport function duration(): GelDurationBuilderInitial<''>;\nexport function duration<TName extends string>(name: TName): GelDurationBuilderInitial<TName>;\nexport function duration(name?: string) {\n\treturn new GelDurationBuilder(name ?? '');\n}\n"], "mappings": "AAGA,SAAS,kBAAkB;AAE3B,SAAS,WAAW,wBAAwB;AAWrC,MAAM,2BACJ,iBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YACC,MACC;AACD,UAAM,MAAM,YAAY,aAAa;AAAA,EACtC;AAAA;AAAA,EAGS,MACR,OAC+C;AAC/C,WAAO,IAAI,YAA6C,OAAO,KAAK,MAA8C;AAAA,EACnH;AACD;AAEO,MAAM,oBAA2E,UAAa;AAAA,EACpG,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,SAAS,MAAe;AACvC,SAAO,IAAI,mBAAmB,QAAQ,EAAE;AACzC;", "names": []}