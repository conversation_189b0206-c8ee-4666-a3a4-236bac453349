{"version": 3, "sources": ["../../../src/gel-core/columns/json.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn, GelColumnBuilder } from './common.ts';\n\nexport type GelJsonBuilderInitial<TName extends string> = GelJsonBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'GelJson';\n\tdata: unknown;\n\tdriverParam: unknown;\n\tenumValues: undefined;\n}>;\n\nexport class GelJsonBuilder<T extends ColumnBuilderBaseConfig<'json', 'GelJson'>> extends GelColumnBuilder<\n\tT\n> {\n\tstatic override readonly [entityKind]: string = 'GelJsonBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'json', 'Gel<PERSON>son');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): Gel<PERSON>son<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelJson<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class GelJson<T extends ColumnBaseConfig<'json', 'GelJson'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelJson';\n\n\tconstructor(table: AnyGelTable<{ name: T['tableName'] }>, config: GelJsonBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn 'json';\n\t}\n}\n\nexport function json(): GelJsonBuilderInitial<''>;\nexport function json<TName extends string>(name: TName): GelJsonBuilderInitial<TName>;\nexport function json(name?: string) {\n\treturn new GelJsonBuilder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAA4C;AAWrC,MAAM,uBAA6E,+BAExF;AAAA,EACD,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,QAAQ,SAAS;AAAA,EAC9B;AAAA;AAAA,EAGS,MACR,OAC2C;AAC3C,WAAO,IAAI,QAAyC,OAAO,KAAK,MAA8C;AAAA,EAC/G;AACD;AAEO,MAAM,gBAA+D,wBAAa;AAAA,EACxF,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,OAA8C,QAAqC;AAC9F,UAAM,OAAO,MAAM;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,KAAK,MAAe;AACnC,SAAO,IAAI,eAAe,QAAQ,EAAE;AACrC;", "names": []}