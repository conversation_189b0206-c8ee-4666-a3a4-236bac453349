{"version": 3, "sources": ["../../../src/gel-core/columns/localtime.ts"], "sourcesContent": ["import type { LocalTime } from 'gel';\nimport type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn } from './common.ts';\nimport { GelLocalDateColumnBaseBuilder } from './date.common.ts';\n\nexport type GelLocalTimeBuilderInitial<TName extends string> = GelLocalTimeBuilder<{\n\tname: TName;\n\tdataType: 'localTime';\n\tcolumnType: 'GelLocalTime';\n\tdata: LocalTime;\n\tdriverParam: LocalTime;\n\tenumValues: undefined;\n}>;\n\nexport class GelLocalTimeBuilder<T extends ColumnBuilderBaseConfig<'localTime', 'GelLocalTime'>>\n\textends GelLocalDateColumnBaseBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'GelLocalTimeBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'localTime', 'GelLocalTime');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelLocalTime<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelLocalTime<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class GelLocalTime<T extends ColumnBaseConfig<'localTime', 'GelLocalTime'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelLocalTime';\n\n\tgetSQLType(): string {\n\t\treturn 'cal::local_time';\n\t}\n}\n\nexport function localTime(): GelLocalTimeBuilderInitial<''>;\nexport function localTime<TName extends string>(name: TName): GelLocalTimeBuilderInitial<TName>;\nexport function localTime(name?: string) {\n\treturn new GelLocalTimeBuilder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,oBAA2B;AAE3B,oBAA0B;AAC1B,yBAA8C;AAWvC,MAAM,4BACJ,iDACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,aAAa,cAAc;AAAA,EACxC;AAAA;AAAA,EAGS,MACR,OACgD;AAChD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,qBAA8E,wBAAa;AAAA,EACvG,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,UAAU,MAAe;AACxC,SAAO,IAAI,oBAAoB,QAAQ,EAAE;AAC1C;", "names": []}