{"version": 3, "sources": ["../../src/gel-core/db.ts"], "sourcesContent": ["import type { Cache } from '~/cache/core/cache.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { GelDialect } from '~/gel-core/dialect.ts';\nimport {\n\tGelDeleteBase,\n\tGelInsertBuilder,\n\tGelSelectBuilder,\n\tGelUpdateBuilder,\n\tQueryBuilder,\n} from '~/gel-core/query-builders/index.ts';\nimport type { GelQueryResultHKT, GelSession, GelTransaction, PreparedQueryConfig } from '~/gel-core/session.ts';\nimport type { GelTable } from '~/gel-core/table.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { ExtractTablesWithRelations, RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport { type ColumnsSelection, type SQL, sql, type SQLWrapper } from '~/sql/sql.ts';\nimport { WithSubquery } from '~/subquery.ts';\nimport type { DrizzleTypeError } from '~/utils.ts';\nimport type { GelColumn } from './columns/index.ts';\nimport { GelCountBuilder } from './query-builders/count.ts';\nimport { RelationalQueryBuilder } from './query-builders/query.ts';\nimport { GelRaw } from './query-builders/raw.ts';\nimport type { SelectedFields } from './query-builders/select.types.ts';\nimport type { WithSubqueryWithSelection } from './subquery.ts';\nimport type { GelViewBase } from './view-base.ts';\n\nexport class GelDatabase<\n\tTQueryResult extends GelQueryResultHKT,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = ExtractTablesWithRelations<TFullSchema>,\n> {\n\tstatic readonly [entityKind]: string = 'GelDatabase';\n\n\tdeclare readonly _: {\n\t\treadonly schema: TSchema | undefined;\n\t\treadonly fullSchema: TFullSchema;\n\t\treadonly tableNamesMap: Record<string, string>;\n\t\treadonly session: GelSession<TQueryResult, TFullSchema, TSchema>;\n\t};\n\n\tquery: TFullSchema extends Record<string, never>\n\t\t? DrizzleTypeError<'Seems like the schema generic is missing - did you forget to add it to your DB type?'>\n\t\t: {\n\t\t\t[K in keyof TSchema]: RelationalQueryBuilder<TSchema, TSchema[K]>;\n\t\t};\n\n\tconstructor(\n\t\t/** @internal */\n\t\treadonly dialect: GelDialect,\n\t\t/** @internal */\n\t\treadonly session: GelSession<any, any, any>,\n\t\tschema: RelationalSchemaConfig<TSchema> | undefined,\n\t) {\n\t\tthis._ = schema\n\t\t\t? {\n\t\t\t\tschema: schema.schema,\n\t\t\t\tfullSchema: schema.fullSchema as TFullSchema,\n\t\t\t\ttableNamesMap: schema.tableNamesMap,\n\t\t\t\tsession,\n\t\t\t}\n\t\t\t: {\n\t\t\t\tschema: undefined,\n\t\t\t\tfullSchema: {} as TFullSchema,\n\t\t\t\ttableNamesMap: {},\n\t\t\t\tsession,\n\t\t\t};\n\t\tthis.query = {} as typeof this['query'];\n\t\tif (this._.schema) {\n\t\t\tfor (const [tableName, columns] of Object.entries(this._.schema)) {\n\t\t\t\t(this.query as GelDatabase<TQueryResult, Record<string, any>>['query'])[tableName] = new RelationalQueryBuilder(\n\t\t\t\t\tschema!.fullSchema,\n\t\t\t\t\tthis._.schema,\n\t\t\t\t\tthis._.tableNamesMap,\n\t\t\t\t\tschema!.fullSchema[tableName] as GelTable,\n\t\t\t\t\tcolumns,\n\t\t\t\t\tdialect,\n\t\t\t\t\tsession,\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\tthis.$cache = { invalidate: async (_params: any) => {} };\n\t}\n\n\t/**\n\t * Creates a subquery that defines a temporary named result set as a CTE.\n\t *\n\t * It is useful for breaking down complex queries into simpler parts and for reusing the result set in subsequent parts of the query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}\n\t *\n\t * @param alias The alias for the subquery.\n\t *\n\t * Failure to provide an alias will result in a DrizzleTypeError, preventing the subquery from being referenced in other queries.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Create a subquery with alias 'sq' and use it in the select query\n\t * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));\n\t *\n\t * const result = await db.with(sq).select().from(sq);\n\t * ```\n\t *\n\t * To select arbitrary SQL values as fields in a CTE and reference them in other CTEs or in the main query, you need to add aliases to them:\n\t *\n\t * ```ts\n\t * // Select an arbitrary SQL value as a field in a CTE and reference it in the main query\n\t * const sq = db.$with('sq').as(db.select({\n\t *   name: sql<string>`upper(${users.name})`.as('name'),\n\t * })\n\t * .from(users));\n\t *\n\t * const result = await db.with(sq).select({ name: sq.name }).from(sq);\n\t * ```\n\t */\n\t$with<TAlias extends string>(alias: TAlias) {\n\t\tconst self = this;\n\t\treturn {\n\t\t\tas<TSelection extends ColumnsSelection>(\n\t\t\t\tqb: TypedQueryBuilder<TSelection> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelection>),\n\t\t\t): WithSubqueryWithSelection<TSelection, TAlias> {\n\t\t\t\tif (typeof qb === 'function') {\n\t\t\t\t\tqb = qb(new QueryBuilder(self.dialect));\n\t\t\t\t}\n\n\t\t\t\treturn new Proxy(\n\t\t\t\t\tnew WithSubquery(qb.getSQL(), qb.getSelectedFields() as SelectedFields, alias, true),\n\t\t\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t\t\t) as WithSubqueryWithSelection<TSelection, TAlias>;\n\t\t\t},\n\t\t};\n\t}\n\n\t$count(\n\t\tsource: GelTable | GelViewBase | SQL | SQLWrapper,\n\t\tfilters?: SQL<unknown>,\n\t) {\n\t\treturn new GelCountBuilder({ source, filters, session: this.session });\n\t}\n\n\t/**\n\t * Incorporates a previously defined CTE (using `$with`) into the main query.\n\t *\n\t * This method allows the main query to reference a temporary named result set.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}\n\t *\n\t * @param queries The CTEs to incorporate into the main query.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Define a subquery 'sq' as a CTE using $with\n\t * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));\n\t *\n\t * // Incorporate the CTE 'sq' into the main query and select from it\n\t * const result = await db.with(sq).select().from(sq);\n\t * ```\n\t */\n\twith(...queries: WithSubquery[]) {\n\t\tconst self = this;\n\n\t\t/**\n\t\t * Creates a select query.\n\t\t *\n\t\t * Calling this method with no arguments will select all columns from the table. Pass a selection object to specify the columns you want to select.\n\t\t *\n\t\t * Use `.from()` method to specify which table to select from.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/select}\n\t\t *\n\t\t * @param fields The selection object.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Select all columns and all rows from the 'cars' table\n\t\t * const allCars: Car[] = await db.select().from(cars);\n\t\t *\n\t\t * // Select specific columns and all rows from the 'cars' table\n\t\t * const carsIdsAndBrands: { id: number; brand: string }[] = await db.select({\n\t\t *   id: cars.id,\n\t\t *   brand: cars.brand\n\t\t * })\n\t\t *   .from(cars);\n\t\t * ```\n\t\t *\n\t\t * Like in SQL, you can use arbitrary expressions as selection fields, not just table columns:\n\t\t *\n\t\t * ```ts\n\t\t * // Select specific columns along with expression and all rows from the 'cars' table\n\t\t * const carsIdsAndLowerNames: { id: number; lowerBrand: string }[] = await db.select({\n\t\t *   id: cars.id,\n\t\t *   lowerBrand: sql<string>`lower(${cars.brand})`,\n\t\t * })\n\t\t *   .from(cars);\n\t\t * ```\n\t\t */\n\t\tfunction select(): GelSelectBuilder<undefined>;\n\t\tfunction select<TSelection extends SelectedFields>(fields: TSelection): GelSelectBuilder<TSelection>;\n\t\tfunction select(fields?: SelectedFields): GelSelectBuilder<SelectedFields | undefined> {\n\t\t\treturn new GelSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: self.session,\n\t\t\t\tdialect: self.dialect,\n\t\t\t\twithList: queries,\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Adds `distinct` expression to the select query.\n\t\t *\n\t\t * Calling this method will return only unique values. When multiple columns are selected, it returns rows with unique combinations of values in these columns.\n\t\t *\n\t\t * Use `.from()` method to specify which table to select from.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t\t *\n\t\t * @param fields The selection object.\n\t\t *\n\t\t * @example\n\t\t * ```ts\n\t\t * // Select all unique rows from the 'cars' table\n\t\t * await db.selectDistinct()\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.id, cars.brand, cars.color);\n\t\t *\n\t\t * // Select all unique brands from the 'cars' table\n\t\t * await db.selectDistinct({ brand: cars.brand })\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.brand);\n\t\t * ```\n\t\t */\n\t\tfunction selectDistinct(): GelSelectBuilder<undefined>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(fields: TSelection): GelSelectBuilder<TSelection>;\n\t\tfunction selectDistinct(fields?: SelectedFields): GelSelectBuilder<SelectedFields | undefined> {\n\t\t\treturn new GelSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: self.session,\n\t\t\t\tdialect: self.dialect,\n\t\t\t\twithList: queries,\n\t\t\t\tdistinct: true,\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Adds `distinct on` expression to the select query.\n\t\t *\n\t\t * Calling this method will specify how the unique rows are determined.\n\t\t *\n\t\t * Use `.from()` method to specify which table to select from.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t\t *\n\t\t * @param on The expression defining uniqueness.\n\t\t * @param fields The selection object.\n\t\t *\n\t\t * @example\n\t\t * ```ts\n\t\t * // Select the first row for each unique brand from the 'cars' table\n\t\t * await db.selectDistinctOn([cars.brand])\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.brand);\n\t\t *\n\t\t * // Selects the first occurrence of each unique car brand along with its color from the 'cars' table\n\t\t * await db.selectDistinctOn([cars.brand], { brand: cars.brand, color: cars.color })\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.brand, cars.color);\n\t\t * ```\n\t\t */\n\t\tfunction selectDistinctOn(on: (GelColumn | SQLWrapper)[]): GelSelectBuilder<undefined>;\n\t\tfunction selectDistinctOn<TSelection extends SelectedFields>(\n\t\t\ton: (GelColumn | SQLWrapper)[],\n\t\t\tfields: TSelection,\n\t\t): GelSelectBuilder<TSelection>;\n\t\tfunction selectDistinctOn(\n\t\t\ton: (GelColumn | SQLWrapper)[],\n\t\t\tfields?: SelectedFields,\n\t\t): GelSelectBuilder<SelectedFields | undefined> {\n\t\t\treturn new GelSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: self.session,\n\t\t\t\tdialect: self.dialect,\n\t\t\t\twithList: queries,\n\t\t\t\tdistinct: { on },\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Creates an update query.\n\t\t *\n\t\t * Calling this method without `.where()` clause will update all rows in a table. The `.where()` clause specifies which rows should be updated.\n\t\t *\n\t\t * Use `.set()` method to specify which values to update.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t\t *\n\t\t * @param table The table to update.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Update all rows in the 'cars' table\n\t\t * await db.update(cars).set({ color: 'red' });\n\t\t *\n\t\t * // Update rows with filters and conditions\n\t\t * await db.update(cars).set({ color: 'red' }).where(eq(cars.brand, 'BMW'));\n\t\t *\n\t\t * // Update with returning clause\n\t\t * const updatedCar: Car[] = await db.update(cars)\n\t\t *   .set({ color: 'red' })\n\t\t *   .where(eq(cars.id, 1))\n\t\t *   .returning();\n\t\t * ```\n\t\t */\n\t\tfunction update<TTable extends GelTable>(table: TTable): GelUpdateBuilder<TTable, TQueryResult> {\n\t\t\treturn new GelUpdateBuilder(table, self.session, self.dialect, queries);\n\t\t}\n\n\t\t/**\n\t\t * Creates an insert query.\n\t\t *\n\t\t * Calling this method will create new rows in a table. Use `.values()` method to specify which values to insert.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/insert}\n\t\t *\n\t\t * @param table The table to insert into.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Insert one row\n\t\t * await db.insert(cars).values({ brand: 'BMW' });\n\t\t *\n\t\t * // Insert multiple rows\n\t\t * await db.insert(cars).values([{ brand: 'BMW' }, { brand: 'Porsche' }]);\n\t\t *\n\t\t * // Insert with returning clause\n\t\t * const insertedCar: Car[] = await db.insert(cars)\n\t\t *   .values({ brand: 'BMW' })\n\t\t *   .returning();\n\t\t * ```\n\t\t */\n\t\tfunction insert<TTable extends GelTable>(table: TTable): GelInsertBuilder<TTable, TQueryResult> {\n\t\t\treturn new GelInsertBuilder(table, self.session, self.dialect, queries);\n\t\t}\n\n\t\t/**\n\t\t * Creates a delete query.\n\t\t *\n\t\t * Calling this method without `.where()` clause will delete all rows in a table. The `.where()` clause specifies which rows should be deleted.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t\t *\n\t\t * @param table The table to delete from.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Delete all rows in the 'cars' table\n\t\t * await db.delete(cars);\n\t\t *\n\t\t * // Delete rows with filters and conditions\n\t\t * await db.delete(cars).where(eq(cars.color, 'green'));\n\t\t *\n\t\t * // Delete with returning clause\n\t\t * const deletedCar: Car[] = await db.delete(cars)\n\t\t *   .where(eq(cars.id, 1))\n\t\t *   .returning();\n\t\t * ```\n\t\t */\n\t\tfunction delete_<TTable extends GelTable>(table: TTable): GelDeleteBase<TTable, TQueryResult> {\n\t\t\treturn new GelDeleteBase(table, self.session, self.dialect, queries);\n\t\t}\n\n\t\treturn { select, selectDistinct, selectDistinctOn, update, insert, delete: delete_ };\n\t}\n\n\t/**\n\t * Creates a select query.\n\t *\n\t * Calling this method with no arguments will select all columns from the table. Pass a selection object to specify the columns you want to select.\n\t *\n\t * Use `.from()` method to specify which table to select from.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select}\n\t *\n\t * @param fields The selection object.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all columns and all rows from the 'cars' table\n\t * const allCars: Car[] = await db.select().from(cars);\n\t *\n\t * // Select specific columns and all rows from the 'cars' table\n\t * const carsIdsAndBrands: { id: number; brand: string }[] = await db.select({\n\t *   id: cars.id,\n\t *   brand: cars.brand\n\t * })\n\t *   .from(cars);\n\t * ```\n\t *\n\t * Like in SQL, you can use arbitrary expressions as selection fields, not just table columns:\n\t *\n\t * ```ts\n\t * // Select specific columns along with expression and all rows from the 'cars' table\n\t * const carsIdsAndLowerNames: { id: number; lowerBrand: string }[] = await db.select({\n\t *   id: cars.id,\n\t *   lowerBrand: sql<string>`lower(${cars.brand})`,\n\t * })\n\t *   .from(cars);\n\t * ```\n\t */\n\tselect(): GelSelectBuilder<undefined>;\n\tselect<TSelection extends SelectedFields>(fields: TSelection): GelSelectBuilder<TSelection>;\n\tselect(fields?: SelectedFields): GelSelectBuilder<SelectedFields | undefined> {\n\t\treturn new GelSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t});\n\t}\n\n\t/**\n\t * Adds `distinct` expression to the select query.\n\t *\n\t * Calling this method will return only unique values. When multiple columns are selected, it returns rows with unique combinations of values in these columns.\n\t *\n\t * Use `.from()` method to specify which table to select from.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t *\n\t * @param fields The selection object.\n\t *\n\t * @example\n\t * ```ts\n\t * // Select all unique rows from the 'cars' table\n\t * await db.selectDistinct()\n\t *   .from(cars)\n\t *   .orderBy(cars.id, cars.brand, cars.color);\n\t *\n\t * // Select all unique brands from the 'cars' table\n\t * await db.selectDistinct({ brand: cars.brand })\n\t *   .from(cars)\n\t *   .orderBy(cars.brand);\n\t * ```\n\t */\n\tselectDistinct(): GelSelectBuilder<undefined>;\n\tselectDistinct<TSelection extends SelectedFields>(fields: TSelection): GelSelectBuilder<TSelection>;\n\tselectDistinct(fields?: SelectedFields): GelSelectBuilder<SelectedFields | undefined> {\n\t\treturn new GelSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t\tdistinct: true,\n\t\t});\n\t}\n\n\t/**\n\t * Adds `distinct on` expression to the select query.\n\t *\n\t * Calling this method will specify how the unique rows are determined.\n\t *\n\t * Use `.from()` method to specify which table to select from.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t *\n\t * @param on The expression defining uniqueness.\n\t * @param fields The selection object.\n\t *\n\t * @example\n\t * ```ts\n\t * // Select the first row for each unique brand from the 'cars' table\n\t * await db.selectDistinctOn([cars.brand])\n\t *   .from(cars)\n\t *   .orderBy(cars.brand);\n\t *\n\t * // Selects the first occurrence of each unique car brand along with its color from the 'cars' table\n\t * await db.selectDistinctOn([cars.brand], { brand: cars.brand, color: cars.color })\n\t *   .from(cars)\n\t *   .orderBy(cars.brand, cars.color);\n\t * ```\n\t */\n\tselectDistinctOn(on: (GelColumn | SQLWrapper)[]): GelSelectBuilder<undefined>;\n\tselectDistinctOn<TSelection extends SelectedFields>(\n\t\ton: (GelColumn | SQLWrapper)[],\n\t\tfields: TSelection,\n\t): GelSelectBuilder<TSelection>;\n\tselectDistinctOn(\n\t\ton: (GelColumn | SQLWrapper)[],\n\t\tfields?: SelectedFields,\n\t): GelSelectBuilder<SelectedFields | undefined> {\n\t\treturn new GelSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t\tdistinct: { on },\n\t\t});\n\t}\n\n\t$cache: { invalidate: Cache['onMutate'] };\n\n\t/**\n\t * Creates an update query.\n\t *\n\t * Calling this method without `.where()` clause will update all rows in a table. The `.where()` clause specifies which rows should be updated.\n\t *\n\t * Use `.set()` method to specify which values to update.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t *\n\t * @param table The table to update.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Update all rows in the 'cars' table\n\t * await db.update(cars).set({ color: 'red' });\n\t *\n\t * // Update rows with filters and conditions\n\t * await db.update(cars).set({ color: 'red' }).where(eq(cars.brand, 'BMW'));\n\t *\n\t * // Update with returning clause\n\t * const updatedCar: Car[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.id, 1))\n\t *   .returning();\n\t * ```\n\t */\n\tupdate<TTable extends GelTable>(table: TTable): GelUpdateBuilder<TTable, TQueryResult> {\n\t\treturn new GelUpdateBuilder(table, this.session, this.dialect);\n\t}\n\n\t/**\n\t * Creates an insert query.\n\t *\n\t * Calling this method will create new rows in a table. Use `.values()` method to specify which values to insert.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert}\n\t *\n\t * @param table The table to insert into.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Insert one row\n\t * await db.insert(cars).values({ brand: 'BMW' });\n\t *\n\t * // Insert multiple rows\n\t * await db.insert(cars).values([{ brand: 'BMW' }, { brand: 'Porsche' }]);\n\t *\n\t * // Insert with returning clause\n\t * const insertedCar: Car[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning();\n\t * ```\n\t */\n\tinsert<TTable extends GelTable>(table: TTable): GelInsertBuilder<TTable, TQueryResult> {\n\t\treturn new GelInsertBuilder(table, this.session, this.dialect);\n\t}\n\n\t/**\n\t * Creates a delete query.\n\t *\n\t * Calling this method without `.where()` clause will delete all rows in a table. The `.where()` clause specifies which rows should be deleted.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t *\n\t * @param table The table to delete from.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Delete all rows in the 'cars' table\n\t * await db.delete(cars);\n\t *\n\t * // Delete rows with filters and conditions\n\t * await db.delete(cars).where(eq(cars.color, 'green'));\n\t *\n\t * // Delete with returning clause\n\t * const deletedCar: Car[] = await db.delete(cars)\n\t *   .where(eq(cars.id, 1))\n\t *   .returning();\n\t * ```\n\t */\n\tdelete<TTable extends GelTable>(table: TTable): GelDeleteBase<TTable, TQueryResult> {\n\t\treturn new GelDeleteBase(table, this.session, this.dialect);\n\t}\n\n\t// TODO views are not implemented\n\t// refreshMaterializedView<TView extends GelMaterializedView>(view: TView): GelRefreshMaterializedView<TQueryResult> {\n\t// \treturn new GelRefreshMaterializedView(view, this.session, this.dialect);\n\t// }\n\n\texecute<TRow extends Record<string, unknown> = Record<string, unknown>>(\n\t\tquery: SQLWrapper | string,\n\t): GelRaw<TRow[]> {\n\t\tconst sequel = typeof query === 'string' ? sql.raw(query) : query.getSQL();\n\t\tconst builtQuery = this.dialect.sqlToQuery(sequel);\n\t\tconst prepared = this.session.prepareQuery<\n\t\t\tPreparedQueryConfig & { execute: TRow[] }\n\t\t>(\n\t\t\tbuiltQuery,\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\tfalse,\n\t\t);\n\t\treturn new GelRaw(\n\t\t\t() => prepared.execute(undefined),\n\t\t\tsequel,\n\t\t\tbuiltQuery,\n\t\t\t(result) => prepared.mapResult(result, true),\n\t\t);\n\t}\n\n\ttransaction<T>(\n\t\ttransaction: (tx: GelTransaction<TQueryResult, TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\treturn this.session.transaction(transaction);\n\t}\n}\n\nexport type GelWithReplicas<Q> = Q & { $primary: Q };\n\nexport const withReplicas = <\n\tHKT extends GelQueryResultHKT,\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n\tQ extends GelDatabase<\n\t\tHKT,\n\t\tTFullSchema,\n\t\tTSchema extends Record<string, unknown> ? ExtractTablesWithRelations<TFullSchema> : TSchema\n\t>,\n>(\n\tprimary: Q,\n\treplicas: [Q, ...Q[]],\n\tgetReplica: (replicas: Q[]) => Q = () => replicas[Math.floor(Math.random() * replicas.length)]!,\n): GelWithReplicas<Q> => {\n\tconst select: Q['select'] = (...args: []) => getReplica(replicas).select(...args);\n\tconst selectDistinct: Q['selectDistinct'] = (...args: []) => getReplica(replicas).selectDistinct(...args);\n\tconst selectDistinctOn: Q['selectDistinctOn'] = (...args: [any]) => getReplica(replicas).selectDistinctOn(...args);\n\tconst _with: Q['with'] = (...args: any) => getReplica(replicas).with(...args);\n\tconst $with: Q['$with'] = (arg: any) => getReplica(replicas).$with(arg);\n\n\tconst update: Q['update'] = (...args: [any]) => primary.update(...args);\n\tconst insert: Q['insert'] = (...args: [any]) => primary.insert(...args);\n\tconst $delete: Q['delete'] = (...args: [any]) => primary.delete(...args);\n\tconst execute: Q['execute'] = (...args: [any]) => primary.execute(...args);\n\tconst transaction: Q['transaction'] = (...args: [any]) => primary.transaction(...args);\n\t// const refreshMaterializedView: Q['refreshMaterializedView'] = (...args: [any]) =>\n\t// \tprimary.refreshMaterializedView(...args);\n\n\treturn {\n\t\t...primary,\n\t\tupdate,\n\t\tinsert,\n\t\tdelete: $delete,\n\t\texecute,\n\t\ttransaction,\n\t\t// refreshMaterializedView,\n\t\t$primary: primary,\n\t\tselect,\n\t\tselectDistinct,\n\t\tselectDistinctOn,\n\t\t$with,\n\t\twith: _with,\n\t\tget query() {\n\t\t\treturn getReplica(replicas).query;\n\t\t},\n\t};\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA2B;AAE3B,4BAMO;AAKP,6BAAsC;AACtC,iBAAsE;AACtE,sBAA6B;AAG7B,mBAAgC;AAChC,mBAAuC;AACvC,iBAAuB;AAKhB,MAAM,YAIX;AAAA,EAgBD,YAEU,SAEA,SACT,QACC;AAJQ;AAEA;AAGT,SAAK,IAAI,SACN;AAAA,MACD,QAAQ,OAAO;AAAA,MACf,YAAY,OAAO;AAAA,MACnB,eAAe,OAAO;AAAA,MACtB;AAAA,IACD,IACE;AAAA,MACD,QAAQ;AAAA,MACR,YAAY,CAAC;AAAA,MACb,eAAe,CAAC;AAAA,MAChB;AAAA,IACD;AACD,SAAK,QAAQ,CAAC;AACd,QAAI,KAAK,EAAE,QAAQ;AAClB,iBAAW,CAAC,WAAW,OAAO,KAAK,OAAO,QAAQ,KAAK,EAAE,MAAM,GAAG;AACjE,QAAC,KAAK,MAAkE,SAAS,IAAI,IAAI;AAAA,UACxF,OAAQ;AAAA,UACR,KAAK,EAAE;AAAA,UACP,KAAK,EAAE;AAAA,UACP,OAAQ,WAAW,SAAS;AAAA,UAC5B;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,SAAK,SAAS,EAAE,YAAY,OAAO,YAAiB;AAAA,IAAC,EAAE;AAAA,EACxD;AAAA,EAnDA,QAAiB,wBAAU,IAAY;AAAA,EASvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4EA,MAA6B,OAAe;AAC3C,UAAM,OAAO;AACb,WAAO;AAAA,MACN,GACC,IACgD;AAChD,YAAI,OAAO,OAAO,YAAY;AAC7B,eAAK,GAAG,IAAI,mCAAa,KAAK,OAAO,CAAC;AAAA,QACvC;AAEA,eAAO,IAAI;AAAA,UACV,IAAI,6BAAa,GAAG,OAAO,GAAG,GAAG,kBAAkB,GAAqB,OAAO,IAAI;AAAA,UACnF,IAAI,6CAAsB,EAAE,OAAO,oBAAoB,SAAS,aAAa,QAAQ,CAAC;AAAA,QACvF;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EAEA,OACC,QACA,SACC;AACD,WAAO,IAAI,6BAAgB,EAAE,QAAQ,SAAS,SAAS,KAAK,QAAQ,CAAC;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,QAAQ,SAAyB;AAChC,UAAM,OAAO;AAwCb,aAAS,OAAO,QAAuE;AACtF,aAAO,IAAI,uCAAiB;AAAA,QAC3B,QAAQ,UAAU;AAAA,QAClB,SAAS,KAAK;AAAA,QACd,SAAS,KAAK;AAAA,QACd,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AA4BA,aAAS,eAAe,QAAuE;AAC9F,aAAO,IAAI,uCAAiB;AAAA,QAC3B,QAAQ,UAAU;AAAA,QAClB,SAAS,KAAK;AAAA,QACd,SAAS,KAAK;AAAA,QACd,UAAU;AAAA,QACV,UAAU;AAAA,MACX,CAAC;AAAA,IACF;AAgCA,aAAS,iBACR,IACA,QAC+C;AAC/C,aAAO,IAAI,uCAAiB;AAAA,QAC3B,QAAQ,UAAU;AAAA,QAClB,SAAS,KAAK;AAAA,QACd,SAAS,KAAK;AAAA,QACd,UAAU;AAAA,QACV,UAAU,EAAE,GAAG;AAAA,MAChB,CAAC;AAAA,IACF;AA6BA,aAAS,OAAgC,OAAuD;AAC/F,aAAO,IAAI,uCAAiB,OAAO,KAAK,SAAS,KAAK,SAAS,OAAO;AAAA,IACvE;AA0BA,aAAS,OAAgC,OAAuD;AAC/F,aAAO,IAAI,uCAAiB,OAAO,KAAK,SAAS,KAAK,SAAS,OAAO;AAAA,IACvE;AA0BA,aAAS,QAAiC,OAAoD;AAC7F,aAAO,IAAI,oCAAc,OAAO,KAAK,SAAS,KAAK,SAAS,OAAO;AAAA,IACpE;AAEA,WAAO,EAAE,QAAQ,gBAAgB,kBAAkB,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,EACpF;AAAA,EAwCA,OAAO,QAAuE;AAC7E,WAAO,IAAI,uCAAiB;AAAA,MAC3B,QAAQ,UAAU;AAAA,MAClB,SAAS,KAAK;AAAA,MACd,SAAS,KAAK;AAAA,IACf,CAAC;AAAA,EACF;AAAA,EA4BA,eAAe,QAAuE;AACrF,WAAO,IAAI,uCAAiB;AAAA,MAC3B,QAAQ,UAAU;AAAA,MAClB,SAAS,KAAK;AAAA,MACd,SAAS,KAAK;AAAA,MACd,UAAU;AAAA,IACX,CAAC;AAAA,EACF;AAAA,EAgCA,iBACC,IACA,QAC+C;AAC/C,WAAO,IAAI,uCAAiB;AAAA,MAC3B,QAAQ,UAAU;AAAA,MAClB,SAAS,KAAK;AAAA,MACd,SAAS,KAAK;AAAA,MACd,UAAU,EAAE,GAAG;AAAA,IAChB,CAAC;AAAA,EACF;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6BA,OAAgC,OAAuD;AACtF,WAAO,IAAI,uCAAiB,OAAO,KAAK,SAAS,KAAK,OAAO;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0BA,OAAgC,OAAuD;AACtF,WAAO,IAAI,uCAAiB,OAAO,KAAK,SAAS,KAAK,OAAO;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0BA,OAAgC,OAAoD;AACnF,WAAO,IAAI,oCAAc,OAAO,KAAK,SAAS,KAAK,OAAO;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QACC,OACiB;AACjB,UAAM,SAAS,OAAO,UAAU,WAAW,eAAI,IAAI,KAAK,IAAI,MAAM,OAAO;AACzE,UAAM,aAAa,KAAK,QAAQ,WAAW,MAAM;AACjD,UAAM,WAAW,KAAK,QAAQ;AAAA,MAG7B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AACA,WAAO,IAAI;AAAA,MACV,MAAM,SAAS,QAAQ,MAAS;AAAA,MAChC;AAAA,MACA;AAAA,MACA,CAAC,WAAW,SAAS,UAAU,QAAQ,IAAI;AAAA,IAC5C;AAAA,EACD;AAAA,EAEA,YACC,aACa;AACb,WAAO,KAAK,QAAQ,YAAY,WAAW;AAAA,EAC5C;AACD;AAIO,MAAM,eAAe,CAU3B,SACA,UACA,aAAmC,MAAM,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,SAAS,MAAM,CAAC,MACrE;AACxB,QAAM,SAAsB,IAAI,SAAa,WAAW,QAAQ,EAAE,OAAO,GAAG,IAAI;AAChF,QAAM,iBAAsC,IAAI,SAAa,WAAW,QAAQ,EAAE,eAAe,GAAG,IAAI;AACxG,QAAM,mBAA0C,IAAI,SAAgB,WAAW,QAAQ,EAAE,iBAAiB,GAAG,IAAI;AACjH,QAAM,QAAmB,IAAI,SAAc,WAAW,QAAQ,EAAE,KAAK,GAAG,IAAI;AAC5E,QAAM,QAAoB,CAAC,QAAa,WAAW,QAAQ,EAAE,MAAM,GAAG;AAEtE,QAAM,SAAsB,IAAI,SAAgB,QAAQ,OAAO,GAAG,IAAI;AACtE,QAAM,SAAsB,IAAI,SAAgB,QAAQ,OAAO,GAAG,IAAI;AACtE,QAAM,UAAuB,IAAI,SAAgB,QAAQ,OAAO,GAAG,IAAI;AACvE,QAAM,UAAwB,IAAI,SAAgB,QAAQ,QAAQ,GAAG,IAAI;AACzE,QAAM,cAAgC,IAAI,SAAgB,QAAQ,YAAY,GAAG,IAAI;AAIrF,SAAO;AAAA,IACN,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA;AAAA;AAAA,IAEA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,IAAI,QAAQ;AACX,aAAO,WAAW,QAAQ,EAAE;AAAA,IAC7B;AAAA,EACD;AACD;", "names": []}