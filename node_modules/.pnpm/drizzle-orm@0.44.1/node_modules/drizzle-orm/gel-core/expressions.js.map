{"version": 3, "sources": ["../../src/gel-core/expressions.ts"], "sourcesContent": ["import type { GelColumn } from '~/gel-core/columns/index.ts';\nimport { bindIfParam } from '~/sql/expressions/index.ts';\nimport type { Placeholder, SQL, SQLChunk, SQLWrapper } from '~/sql/sql.ts';\nimport { sql } from '~/sql/sql.ts';\n\nexport * from '~/sql/expressions/index.ts';\n\nexport function concat(column: GelColumn | SQL.Aliased, value: string | Placeholder | SQLWrapper): SQL {\n\treturn sql`${column} || ${bindIfParam(value, column)}`;\n}\n\nexport function substring(\n\tcolumn: GelColumn | SQL.Aliased,\n\t{ from, for: _for }: { from?: number | Placeholder | SQLWrapper; for?: number | Placeholder | SQLWrapper },\n): SQL {\n\tconst chunks: SQLChunk[] = [sql`substring(`, column];\n\tif (from !== undefined) {\n\t\tchunks.push(sql` from `, bindIfParam(from, column));\n\t}\n\tif (_for !== undefined) {\n\t\tchunks.push(sql` for `, bindIfParam(_for, column));\n\t}\n\tchunks.push(sql`)`);\n\treturn sql.join(chunks);\n}\n"], "mappings": "AACA,SAAS,mBAAmB;AAE5B,SAAS,WAAW;AAEpB,cAAc;AAEP,SAAS,OAAO,QAAiC,OAA+C;AACtG,SAAO,MAAM,MAAM,OAAO,YAAY,OAAO,MAAM,CAAC;AACrD;AAEO,SAAS,UACf,QACA,EAAE,MAAM,KAAK,KAAK,GACZ;AACN,QAAM,SAAqB,CAAC,iBAAiB,MAAM;AACnD,MAAI,SAAS,QAAW;AACvB,WAAO,KAAK,aAAa,YAAY,MAAM,MAAM,CAAC;AAAA,EACnD;AACA,MAAI,SAAS,QAAW;AACvB,WAAO,KAAK,YAAY,YAAY,MAAM,MAAM,CAAC;AAAA,EAClD;AACA,SAAO,KAAK,MAAM;AAClB,SAAO,IAAI,KAAK,MAAM;AACvB;", "names": []}