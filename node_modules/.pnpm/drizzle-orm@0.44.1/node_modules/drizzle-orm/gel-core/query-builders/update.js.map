{"version": 3, "sources": ["../../../src/gel-core/query-builders/update.ts"], "sourcesContent": ["import type { GetColumnData } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport type { GelDialect } from '~/gel-core/dialect.ts';\nimport type {\n\tGelPreparedQuery,\n\tGelQueryResultHKT,\n\tGelQueryResultKind,\n\tGelSession,\n\tPreparedQueryConfig,\n} from '~/gel-core/session.ts';\nimport { GelTable } from '~/gel-core/table.ts';\nimport type {\n\tAppendToNullabilityMap,\n\tAppendToResult,\n\tGetSelectTableName,\n\tGetSelectTableSelection,\n\tJoinNullability,\n\tJoinType,\n\tSelectMode,\n\tSelectResult,\n} from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport { type ColumnsSelection, type Query, SQL, type SQLWrapper } from '~/sql/sql.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport {\n\ttype Assume,\n\tgetTableLikeName,\n\tmapUpdateSet,\n\ttype NeonAuthToken,\n\torderSelectedFields,\n\ttype UpdateSet,\n} from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type { GelColumn } from '../columns/common.ts';\nimport { extractUsedTable } from '../utils.ts';\nimport type { GelViewBase } from '../view-base.ts';\nimport type { GelSelectJoinConfig, SelectedFields, SelectedFieldsOrdered } from './select.types.ts';\n\nexport interface GelUpdateConfig {\n\twhere?: SQL | undefined;\n\tset: UpdateSet;\n\ttable: GelTable;\n\tfrom?: GelTable | Subquery | GelViewBase | SQL;\n\tjoins: GelSelectJoinConfig[];\n\treturning?: SelectedFieldsOrdered;\n\twithList?: Subquery[];\n}\n\nexport type GelUpdateSetSource<TTable extends GelTable> =\n\t& {\n\t\t[Key in keyof TTable['$inferInsert']]?:\n\t\t\t| GetColumnData<TTable['_']['columns'][Key]>\n\t\t\t| SQL\n\t\t\t| GelColumn;\n\t}\n\t& {};\n\nexport class GelUpdateBuilder<TTable extends GelTable, TQueryResult extends GelQueryResultHKT> {\n\tstatic readonly [entityKind]: string = 'GelUpdateBuilder';\n\n\tdeclare readonly _: {\n\t\treadonly table: TTable;\n\t};\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: GelSession,\n\t\tprivate dialect: GelDialect,\n\t\tprivate withList?: Subquery[],\n\t) {}\n\n\tprivate authToken?: NeonAuthToken;\n\tsetToken(token: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\tset(\n\t\tvalues: GelUpdateSetSource<TTable>,\n\t): GelUpdateWithout<GelUpdateBase<TTable, TQueryResult>, false, 'leftJoin' | 'rightJoin' | 'innerJoin' | 'fullJoin'> {\n\t\treturn new GelUpdateBase<TTable, TQueryResult>(\n\t\t\tthis.table,\n\t\t\tmapUpdateSet(this.table, values),\n\t\t\tthis.session,\n\t\t\tthis.dialect,\n\t\t\tthis.withList,\n\t\t);\n\t}\n}\n\nexport type GelUpdateWithout<\n\tT extends AnyGelUpdate,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n> = TDynamic extends true ? T : Omit<\n\tGelUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['from'],\n\t\tT['_']['returning'],\n\t\tT['_']['nullabilityMap'],\n\t\tT['_']['joins'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods'] | K\n\t>,\n\tT['_']['excludedMethods'] | K\n>;\n\nexport type GelUpdateWithJoins<\n\tT extends AnyGelUpdate,\n\tTDynamic extends boolean,\n\tTFrom extends GelTable | Subquery | GelViewBase | SQL,\n> = TDynamic extends true ? T : Omit<\n\tGelUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tTFrom,\n\t\tT['_']['returning'],\n\t\tAppendToNullabilityMap<T['_']['nullabilityMap'], GetSelectTableName<TFrom>, 'inner'>,\n\t\t[...T['_']['joins'], {\n\t\t\tname: GetSelectTableName<TFrom>;\n\t\t\tjoinType: 'inner';\n\t\t\ttable: TFrom;\n\t\t}],\n\t\tTDynamic,\n\t\tExclude<T['_']['excludedMethods'] | 'from', 'leftJoin' | 'rightJoin' | 'innerJoin' | 'fullJoin'>\n\t>,\n\tExclude<T['_']['excludedMethods'] | 'from', 'leftJoin' | 'rightJoin' | 'innerJoin' | 'fullJoin'>\n>;\n\nexport type GelUpdateJoinFn<\n\tT extends AnyGelUpdate,\n\tTDynamic extends boolean,\n\tTJoinType extends JoinType,\n> = <\n\tTJoinedTable extends GelTable | Subquery | GelViewBase | SQL,\n>(\n\ttable: TJoinedTable,\n\ton:\n\t\t| (\n\t\t\t(\n\t\t\t\tupdateTable: T['_']['table']['_']['columns'],\n\t\t\t\tfrom: T['_']['from'] extends GelTable ? T['_']['from']['_']['columns']\n\t\t\t\t\t: T['_']['from'] extends Subquery | GelViewBase ? T['_']['from']['_']['selectedFields']\n\t\t\t\t\t: never,\n\t\t\t) => SQL | undefined\n\t\t)\n\t\t| SQL\n\t\t| undefined,\n) => GelUpdateJoin<T, TDynamic, TJoinType, TJoinedTable>;\n\nexport type GelUpdateJoin<\n\tT extends AnyGelUpdate,\n\tTDynamic extends boolean,\n\tTJoinType extends JoinType,\n\tTJoinedTable extends GelTable | Subquery | GelViewBase | SQL,\n> = TDynamic extends true ? T : GelUpdateBase<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['from'],\n\tT['_']['returning'],\n\tAppendToNullabilityMap<T['_']['nullabilityMap'], GetSelectTableName<TJoinedTable>, TJoinType>,\n\t[...T['_']['joins'], {\n\t\tname: GetSelectTableName<TJoinedTable>;\n\t\tjoinType: TJoinType;\n\t\ttable: TJoinedTable;\n\t}],\n\tTDynamic,\n\tT['_']['excludedMethods']\n>;\n\ntype Join = {\n\tname: string | undefined;\n\tjoinType: JoinType;\n\ttable: GelTable | Subquery | GelViewBase | SQL;\n};\n\ntype AccumulateToResult<\n\tT extends AnyGelUpdate,\n\tTSelectMode extends SelectMode,\n\tTJoins extends Join[],\n\tTSelectedFields extends ColumnsSelection,\n> = TJoins extends [infer TJoin extends Join, ...infer TRest extends Join[]] ? AccumulateToResult<\n\t\tT,\n\t\tTSelectMode extends 'partial' ? TSelectMode : 'multiple',\n\t\tTRest,\n\t\tAppendToResult<\n\t\t\tT['_']['table']['_']['name'],\n\t\t\tTSelectedFields,\n\t\t\tTJoin['name'],\n\t\t\tTJoin['table'] extends Table ? TJoin['table']['_']['columns']\n\t\t\t\t: TJoin['table'] extends Subquery ? Assume<TJoin['table']['_']['selectedFields'], SelectedFields>\n\t\t\t\t: never,\n\t\t\tTSelectMode extends 'partial' ? TSelectMode : 'multiple'\n\t\t>\n\t>\n\t: TSelectedFields;\n\nexport type GelUpdateReturningAll<T extends AnyGelUpdate, TDynamic extends boolean> = GelUpdateWithout<\n\tGelUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['from'],\n\t\tSelectResult<\n\t\t\tAccumulateToResult<\n\t\t\t\tT,\n\t\t\t\t'single',\n\t\t\t\tT['_']['joins'],\n\t\t\t\tGetSelectTableSelection<T['_']['table']>\n\t\t\t>,\n\t\t\t'partial',\n\t\t\tT['_']['nullabilityMap']\n\t\t>,\n\t\tT['_']['nullabilityMap'],\n\t\tT['_']['joins'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type GelUpdateReturning<\n\tT extends AnyGelUpdate,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFields,\n> = GelUpdateWithout<\n\tGelUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['from'],\n\t\tSelectResult<\n\t\t\tAccumulateToResult<\n\t\t\t\tT,\n\t\t\t\t'partial',\n\t\t\t\tT['_']['joins'],\n\t\t\t\tTSelectedFields\n\t\t\t>,\n\t\t\t'partial',\n\t\t\tT['_']['nullabilityMap']\n\t\t>,\n\t\tT['_']['nullabilityMap'],\n\t\tT['_']['joins'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type GelUpdatePrepare<T extends AnyGelUpdate> = GelPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['returning'] extends undefined ? GelQueryResultKind<T['_']['queryResult'], never>\n\t\t\t: T['_']['returning'][];\n\t}\n>;\n\nexport type GelUpdateDynamic<T extends AnyGelUpdate> = GelUpdate<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['from'],\n\tT['_']['returning'],\n\tT['_']['nullabilityMap']\n>;\n\nexport type GelUpdate<\n\tTTable extends GelTable = GelTable,\n\tTQueryResult extends GelQueryResultHKT = GelQueryResultHKT,\n\tTFrom extends GelTable | Subquery | GelViewBase | SQL | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<TTable['_']['name'], 'not-null'>,\n\tTJoins extends Join[] = [],\n> = GelUpdateBase<TTable, TQueryResult, TFrom, TReturning, TNullabilityMap, TJoins, true, never>;\n\nexport type AnyGelUpdate = GelUpdateBase<any, any, any, any, any, any, any, any>;\n\nexport interface GelUpdateBase<\n\tTTable extends GelTable,\n\tTQueryResult extends GelQueryResultHKT,\n\tTFrom extends GelTable | Subquery | GelViewBase | SQL | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<TTable['_']['name'], 'not-null'>,\n\tTJoins extends Join[] = [],\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tQueryPromise<TReturning extends undefined ? GelQueryResultKind<TQueryResult, never> : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? GelQueryResultKind<TQueryResult, never> : TReturning[], 'gel'>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'gel';\n\t\treadonly table: TTable;\n\t\treadonly joins: TJoins;\n\t\treadonly nullabilityMap: TNullabilityMap;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly from: TFrom;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? GelQueryResultKind<TQueryResult, never> : TReturning[];\n\t};\n}\n\nexport class GelUpdateBase<\n\tTTable extends GelTable,\n\tTQueryResult extends GelQueryResultHKT,\n\tTFrom extends GelTable | Subquery | GelViewBase | SQL | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<TTable['_']['name'], 'not-null'>,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTJoins extends Join[] = [],\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? GelQueryResultKind<TQueryResult, never> : TReturning[]>\n\timplements\n\t\tRunnableQuery<TReturning extends undefined ? GelQueryResultKind<TQueryResult, never> : TReturning[], 'gel'>,\n\t\tSQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'GelUpdate';\n\n\tprivate config: GelUpdateConfig;\n\tprivate tableName: string | undefined;\n\tprivate joinsNotNullableMap: Record<string, boolean>;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tset: UpdateSet,\n\t\tprivate session: GelSession,\n\t\tprivate dialect: GelDialect,\n\t\twithList?: Subquery[],\n\t) {\n\t\tsuper();\n\t\tthis.config = { set, table, withList, joins: [] };\n\t\tthis.tableName = getTableLikeName(table);\n\t\tthis.joinsNotNullableMap = typeof this.tableName === 'string' ? { [this.tableName]: true } : {};\n\t}\n\n\tfrom<TFrom extends GelTable | Subquery | GelViewBase | SQL>(\n\t\tsource: TFrom,\n\t): GelUpdateWithJoins<this, TDynamic, TFrom> {\n\t\tconst tableName = getTableLikeName(source);\n\t\tif (typeof tableName === 'string') {\n\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t}\n\t\tthis.config.from = source;\n\t\treturn this as any;\n\t}\n\n\tprivate getTableLikeFields(table: GelTable | Subquery | GelViewBase): Record<string, unknown> {\n\t\tif (is(table, GelTable)) {\n\t\t\treturn table[Table.Symbol.Columns];\n\t\t} else if (is(table, Subquery)) {\n\t\t\treturn table._.selectedFields;\n\t\t}\n\t\treturn table[ViewBaseConfig].selectedFields;\n\t}\n\n\tprivate createJoin<TJoinType extends JoinType>(\n\t\tjoinType: TJoinType,\n\t): GelUpdateJoinFn<this, TDynamic, TJoinType> {\n\t\treturn ((\n\t\t\ttable: GelTable | Subquery | GelViewBase | SQL,\n\t\t\ton: ((updateTable: TTable, from: TFrom) => SQL | undefined) | SQL | undefined,\n\t\t) => {\n\t\t\tconst tableName = getTableLikeName(table);\n\n\t\t\tif (typeof tableName === 'string' && this.config.joins.some((join) => join.alias === tableName)) {\n\t\t\t\tthrow new Error(`Alias \"${tableName}\" is already used in this query`);\n\t\t\t}\n\n\t\t\tif (typeof on === 'function') {\n\t\t\t\tconst from = this.config.from && !is(this.config.from, SQL)\n\t\t\t\t\t? this.getTableLikeFields(this.config.from)\n\t\t\t\t\t: undefined;\n\t\t\t\ton = on(\n\t\t\t\t\tnew Proxy(\n\t\t\t\t\t\tthis.config.table[Table.Symbol.Columns],\n\t\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t\t) as any,\n\t\t\t\t\tfrom && new Proxy(\n\t\t\t\t\t\tfrom,\n\t\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t\t) as any,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tthis.config.joins.push({ on, table, joinType, alias: tableName });\n\n\t\t\tif (typeof tableName === 'string') {\n\t\t\t\tswitch (joinType) {\n\t\t\t\t\tcase 'left': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'right': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'inner': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'full': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn this as any;\n\t\t}) as any;\n\t}\n\n\tleftJoin = this.createJoin('left');\n\n\trightJoin = this.createJoin('right');\n\n\tinnerJoin = this.createJoin('inner');\n\n\tfullJoin = this.createJoin('full');\n\n\t/**\n\t * Adds a 'where' clause to the query.\n\t *\n\t * Calling this method will update only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t *\n\t * @param where the 'where' clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be updated.\n\t *\n\t * ```ts\n\t * // Update all cars with green color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'));\n\t * // or\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Update all BMW cars with a green color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Update all cars with the green or blue color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(where: SQL | undefined): GelUpdateWithout<this, TDynamic, 'where'> {\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the updated rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update#update-with-returning}\n\t *\n\t * @example\n\t * ```ts\n\t * // Update all cars with the green color and return all fields\n\t * const updatedCars: Car[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning();\n\t *\n\t * // Update all cars with the green color and return only their id and brand fields\n\t * const updatedCarsIdsAndBrands: { id: number, brand: string }[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning({ id: cars.id, brand: cars.brand });\n\t * ```\n\t */\n\treturning(): GelUpdateReturningAll<this, TDynamic>;\n\treturning<TSelectedFields extends SelectedFields>(\n\t\tfields: TSelectedFields,\n\t): GelUpdateReturning<this, TDynamic, TSelectedFields>;\n\treturning(\n\t\tfields?: SelectedFields,\n\t): GelUpdateWithout<AnyGelUpdate, TDynamic, 'returning'> {\n\t\tif (!fields) {\n\t\t\tfields = Object.assign({}, this.config.table[Table.Symbol.Columns]);\n\n\t\t\tif (this.config.from) {\n\t\t\t\tconst tableName = getTableLikeName(this.config.from);\n\n\t\t\t\tif (typeof tableName === 'string' && this.config.from && !is(this.config.from, SQL)) {\n\t\t\t\t\tconst fromFields = this.getTableLikeFields(this.config.from);\n\t\t\t\t\tfields[tableName] = fromFields as any;\n\t\t\t\t}\n\n\t\t\t\tfor (const join of this.config.joins) {\n\t\t\t\t\tconst tableName = getTableLikeName(join.table);\n\n\t\t\t\t\tif (typeof tableName === 'string' && !is(join.table, SQL)) {\n\t\t\t\t\t\tconst fromFields = this.getTableLikeFields(join.table);\n\t\t\t\t\t\tfields[tableName] = fromFields as any;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis.config.returning = orderSelectedFields<GelColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildUpdateQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): GelUpdatePrepare<this> {\n\t\tconst query = this.session.prepareQuery<\n\t\t\tPreparedQueryConfig & { execute: TReturning[] }\n\t\t>(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true, undefined, {\n\t\t\ttype: 'update',\n\t\t\ttables: extractUsedTable(this.config.table),\n\t\t});\n\t\tquery.joinsNotNullableMap = this.joinsNotNullableMap;\n\t\treturn query;\n\t}\n\n\tprepare(name: string): GelUpdatePrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn this._prepare().execute(placeholderValues);\n\t};\n\n\t$dynamic(): GelUpdateDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "mappings": "AACA,SAAS,YAAY,UAAU;AAS/B,SAAS,gBAAgB;AAWzB,SAAS,oBAAoB;AAE7B,SAAS,6BAA6B;AACtC,SAA4C,WAA4B;AACxE,SAAS,gBAAgB;AACzB,SAAS,aAAa;AACtB;AAAA,EAEC;AAAA,EACA;AAAA,EAEA;AAAA,OAEM;AACP,SAAS,sBAAsB;AAE/B,SAAS,wBAAwB;AAuB1B,MAAM,iBAAkF;AAAA,EAO9F,YACS,OACA,SACA,SACA,UACP;AAJO;AACA;AACA;AACA;AAAA,EACN;AAAA,EAXH,QAAiB,UAAU,IAAY;AAAA,EAa/B;AAAA,EACR,SAAS,OAAsB;AAC9B,SAAK,YAAY;AACjB,WAAO;AAAA,EACR;AAAA,EAEA,IACC,QACoH;AACpH,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,aAAa,KAAK,OAAO,MAAM;AAAA,MAC/B,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAwNO,MAAM,sBAaH,aAIV;AAAA,EAOC,YACC,OACA,KACQ,SACA,SACR,UACC;AACD,UAAM;AAJE;AACA;AAIR,SAAK,SAAS,EAAE,KAAK,OAAO,UAAU,OAAO,CAAC,EAAE;AAChD,SAAK,YAAY,iBAAiB,KAAK;AACvC,SAAK,sBAAsB,OAAO,KAAK,cAAc,WAAW,EAAE,CAAC,KAAK,SAAS,GAAG,KAAK,IAAI,CAAC;AAAA,EAC/F;AAAA,EAjBA,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EACA;AAAA,EACA;AAAA,EAeR,KACC,QAC4C;AAC5C,UAAM,YAAY,iBAAiB,MAAM;AACzC,QAAI,OAAO,cAAc,UAAU;AAClC,WAAK,oBAAoB,SAAS,IAAI;AAAA,IACvC;AACA,SAAK,OAAO,OAAO;AACnB,WAAO;AAAA,EACR;AAAA,EAEQ,mBAAmB,OAAmE;AAC7F,QAAI,GAAG,OAAO,QAAQ,GAAG;AACxB,aAAO,MAAM,MAAM,OAAO,OAAO;AAAA,IAClC,WAAW,GAAG,OAAO,QAAQ,GAAG;AAC/B,aAAO,MAAM,EAAE;AAAA,IAChB;AACA,WAAO,MAAM,cAAc,EAAE;AAAA,EAC9B;AAAA,EAEQ,WACP,UAC6C;AAC7C,WAAQ,CACP,OACA,OACI;AACJ,YAAM,YAAY,iBAAiB,KAAK;AAExC,UAAI,OAAO,cAAc,YAAY,KAAK,OAAO,MAAM,KAAK,CAAC,SAAS,KAAK,UAAU,SAAS,GAAG;AAChG,cAAM,IAAI,MAAM,UAAU,SAAS,iCAAiC;AAAA,MACrE;AAEA,UAAI,OAAO,OAAO,YAAY;AAC7B,cAAM,OAAO,KAAK,OAAO,QAAQ,CAAC,GAAG,KAAK,OAAO,MAAM,GAAG,IACvD,KAAK,mBAAmB,KAAK,OAAO,IAAI,IACxC;AACH,aAAK;AAAA,UACJ,IAAI;AAAA,YACH,KAAK,OAAO,MAAM,MAAM,OAAO,OAAO;AAAA,YACtC,IAAI,sBAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;AAAA,UAC5E;AAAA,UACA,QAAQ,IAAI;AAAA,YACX;AAAA,YACA,IAAI,sBAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;AAAA,UAC5E;AAAA,QACD;AAAA,MACD;AAEA,WAAK,OAAO,MAAM,KAAK,EAAE,IAAI,OAAO,UAAU,OAAO,UAAU,CAAC;AAEhE,UAAI,OAAO,cAAc,UAAU;AAClC,gBAAQ,UAAU;AAAA,UACjB,KAAK,QAAQ;AACZ,iBAAK,oBAAoB,SAAS,IAAI;AACtC;AAAA,UACD;AAAA,UACA,KAAK,SAAS;AACb,iBAAK,sBAAsB,OAAO;AAAA,cACjC,OAAO,QAAQ,KAAK,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC;AAAA,YACrE;AACA,iBAAK,oBAAoB,SAAS,IAAI;AACtC;AAAA,UACD;AAAA,UACA,KAAK,SAAS;AACb,iBAAK,oBAAoB,SAAS,IAAI;AACtC;AAAA,UACD;AAAA,UACA,KAAK,QAAQ;AACZ,iBAAK,sBAAsB,OAAO;AAAA,cACjC,OAAO,QAAQ,KAAK,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC;AAAA,YACrE;AACA,iBAAK,oBAAoB,SAAS,IAAI;AACtC;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA,EACD;AAAA,EAEA,WAAW,KAAK,WAAW,MAAM;AAAA,EAEjC,YAAY,KAAK,WAAW,OAAO;AAAA,EAEnC,YAAY,KAAK,WAAW,OAAO;AAAA,EAEnC,WAAW,KAAK,WAAW,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmCjC,MAAM,OAAmE;AACxE,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA,EA4BA,UACC,QACwD;AACxD,QAAI,CAAC,QAAQ;AACZ,eAAS,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO,MAAM,MAAM,OAAO,OAAO,CAAC;AAElE,UAAI,KAAK,OAAO,MAAM;AACrB,cAAM,YAAY,iBAAiB,KAAK,OAAO,IAAI;AAEnD,YAAI,OAAO,cAAc,YAAY,KAAK,OAAO,QAAQ,CAAC,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG;AACpF,gBAAM,aAAa,KAAK,mBAAmB,KAAK,OAAO,IAAI;AAC3D,iBAAO,SAAS,IAAI;AAAA,QACrB;AAEA,mBAAW,QAAQ,KAAK,OAAO,OAAO;AACrC,gBAAMA,aAAY,iBAAiB,KAAK,KAAK;AAE7C,cAAI,OAAOA,eAAc,YAAY,CAAC,GAAG,KAAK,OAAO,GAAG,GAAG;AAC1D,kBAAM,aAAa,KAAK,mBAAmB,KAAK,KAAK;AACrD,mBAAOA,UAAS,IAAI;AAAA,UACrB;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,SAAK,OAAO,YAAY,oBAA+B,MAAM;AAC7D,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;AAAA,EACjD;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAS,MAAuC;AAC/C,UAAM,QAAQ,KAAK,QAAQ,aAEzB,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,WAAW,MAAM,MAAM,QAAW;AAAA,MACvF,MAAM;AAAA,MACN,QAAQ,iBAAiB,KAAK,OAAO,KAAK;AAAA,IAC3C,CAAC;AACD,UAAM,sBAAsB,KAAK;AACjC,WAAO;AAAA,EACR;AAAA,EAEA,QAAQ,MAAsC;AAC7C,WAAO,KAAK,SAAS,IAAI;AAAA,EAC1B;AAAA,EAES,UAAkD,CAAC,sBAAsB;AACjF,WAAO,KAAK,SAAS,EAAE,QAAQ,iBAAiB;AAAA,EACjD;AAAA,EAEA,WAAmC;AAClC,WAAO;AAAA,EACR;AACD;", "names": ["tableName"]}