{"version": 3, "sources": ["../../src/gel-core/roles.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\n\nexport interface GelRoleConfig {\n\tcreateDb?: boolean;\n\tcreateRole?: boolean;\n\tinherit?: boolean;\n}\n\nexport class GelRole implements GelRoleConfig {\n\tstatic readonly [entityKind]: string = 'GelRole';\n\n\t/** @internal */\n\t_existing?: boolean;\n\n\t/** @internal */\n\treadonly createDb: GelRoleConfig['createDb'];\n\t/** @internal */\n\treadonly createRole: GelRoleConfig['createRole'];\n\t/** @internal */\n\treadonly inherit: GelRoleConfig['inherit'];\n\n\tconstructor(\n\t\treadonly name: string,\n\t\tconfig?: GelRoleConfig,\n\t) {\n\t\tif (config) {\n\t\t\tthis.createDb = config.createDb;\n\t\t\tthis.createRole = config.createRole;\n\t\t\tthis.inherit = config.inherit;\n\t\t}\n\t}\n\n\texisting(): this {\n\t\tthis._existing = true;\n\t\treturn this;\n\t}\n}\n\nexport function gelRole(name: string, config?: GelRoleConfig) {\n\treturn new GelRole(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAQpB,MAAM,QAAiC;AAAA,EAa7C,YACU,MACT,QACC;AAFQ;AAGT,QAAI,QAAQ;AACX,WAAK,WAAW,OAAO;AACvB,WAAK,aAAa,OAAO;AACzB,WAAK,UAAU,OAAO;AAAA,IACvB;AAAA,EACD;AAAA,EArBA,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAGvC;AAAA;AAAA,EAGS;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EAaT,WAAiB;AAChB,SAAK,YAAY;AACjB,WAAO;AAAA,EACR;AACD;AAEO,SAAS,QAAQ,MAAc,QAAwB;AAC7D,SAAO,IAAI,QAAQ,MAAM,MAAM;AAChC;", "names": []}