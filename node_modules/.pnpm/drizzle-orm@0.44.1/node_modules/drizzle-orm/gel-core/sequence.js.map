{"version": 3, "sources": ["../../src/gel-core/sequence.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\n\nexport type GelSequenceOptions = {\n\tincrement?: number | string;\n\tminValue?: number | string;\n\tmaxValue?: number | string;\n\tstartWith?: number | string;\n\tcache?: number | string;\n\tcycle?: boolean;\n};\n\nexport class GelSequence {\n\tstatic readonly [entityKind]: string = 'GelSequence';\n\n\tconstructor(\n\t\tpublic readonly seqName: string | undefined,\n\t\tpublic readonly seqOptions: GelSequenceOptions | undefined,\n\t\tpublic readonly schema: string | undefined,\n\t) {\n\t}\n}\n\nexport function gelSequence(\n\tname: string,\n\toptions?: GelSequenceOptions,\n): GelSequence {\n\treturn gelSequenceWithSchema(name, options, undefined);\n}\n\n/** @internal */\nexport function gelSequenceWithSchema(\n\tname: string,\n\toptions?: GelSequenceOptions,\n\tschema?: string,\n): GelSequence {\n\treturn new GelSequence(name, options, schema);\n}\n\nexport function isGelSequence(obj: unknown): obj is GelSequence {\n\treturn is(obj, GelSequence);\n}\n"], "mappings": "AAAA,SAAS,YAAY,UAAU;AAWxB,MAAM,YAAY;AAAA,EAGxB,YACiB,SACA,YACA,QACf;AAHe;AACA;AACA;AAAA,EAEjB;AAAA,EAPA,QAAiB,UAAU,IAAY;AAQxC;AAEO,SAAS,YACf,MACA,SACc;AACd,SAAO,sBAAsB,MAAM,SAAS,MAAS;AACtD;AAGO,SAAS,sBACf,MACA,SACA,QACc;AACd,SAAO,IAAI,YAAY,MAAM,SAAS,MAAM;AAC7C;AAEO,SAAS,cAAc,KAAkC;AAC/D,SAAO,GAAG,KAAK,WAAW;AAC3B;", "names": []}