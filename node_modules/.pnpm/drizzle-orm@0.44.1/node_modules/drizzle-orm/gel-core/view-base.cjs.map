{"version": 3, "sources": ["../../src/gel-core/view-base.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { type ColumnsSelection, View } from '~/sql/sql.ts';\n\nexport abstract class GelViewBase<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> extends View<TName, TExisting, TSelectedFields> {\n\tstatic override readonly [entityKind]: string = 'GelViewBase';\n\n\tdeclare readonly _: View<TName, TExisting, TSelectedFields>['_'] & {\n\t\treadonly viewBrand: 'GelViewBase';\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAC3B,iBAA4C;AAErC,MAAe,oBAIZ,gBAAwC;AAAA,EACjD,QAA0B,wBAAU,IAAY;AAKjD;", "names": []}