{"version": 3, "sources": ["../../src/libsql/driver-core.ts"], "sourcesContent": ["import type { Client, ResultSet } from '@libsql/client';\nimport type { BatchItem, BatchResponse } from '~/batch.ts';\nimport { entityKind } from '~/entity.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype ExtractTablesWithRelations,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { BaseSQLiteDatabase } from '~/sqlite-core/db.ts';\nimport { SQLiteAsyncDialect } from '~/sqlite-core/dialect.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport { LibSQLSession } from './session.ts';\n\nexport class LibSQLDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends BaseSQLiteDatabase<'async', ResultSet, TSchema> {\n\tstatic override readonly [entityKind]: string = 'LibSQLDatabase';\n\n\t/** @internal */\n\tdeclare readonly session: LibSQLSession<TSchema, ExtractTablesWithRelations<TSchema>>;\n\n\tasync batch<U extends BatchItem<'sqlite'>, T extends Readonly<[U, ...U[]]>>(\n\t\tbatch: T,\n\t): Promise<BatchResponse<T>> {\n\t\treturn this.session.batch(batch) as Promise<BatchResponse<T>>;\n\t}\n}\n\n/** @internal */\nexport function construct<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n>(client: Client, config: DrizzleConfig<TSchema> = {}): LibSQLDatabase<TSchema> & {\n\t$client: Client;\n} {\n\tconst dialect = new SQLiteAsyncDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new LibSQLSession(client, dialect, schema, { logger, cache: config.cache }, undefined);\n\tconst db = new LibSQLDatabase('async', dialect, session, schema) as LibSQLDatabase<TSchema>;\n\t(<any> db).$client = client;\n\t(<any> db).$cache = config.cache;\n\tif ((<any> db).$cache) {\n\t\t(<any> db).$cache['invalidate'] = config.cache?.onMutate;\n\t}\n\treturn db as any;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAC3B,oBAA8B;AAC9B,uBAMO;AACP,gBAAmC;AACnC,qBAAmC;AAEnC,qBAA8B;AAEvB,MAAM,uBAEH,6BAAgD;AAAA,EACzD,QAA0B,wBAAU,IAAY;AAAA,EAKhD,MAAM,MACL,OAC4B;AAC5B,WAAO,KAAK,QAAQ,MAAM,KAAK;AAAA,EAChC;AACD;AAGO,SAAS,UAEd,QAAgB,SAAiC,CAAC,GAElD;AACD,QAAM,UAAU,IAAI,kCAAmB,EAAE,QAAQ,OAAO,OAAO,CAAC;AAChE,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,4BAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,mBAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,6BAAc,QAAQ,SAAS,QAAQ,EAAE,QAAQ,OAAO,OAAO,MAAM,GAAG,MAAS;AACrG,QAAM,KAAK,IAAI,eAAe,SAAS,SAAS,SAAS,MAAM;AAC/D,EAAO,GAAI,UAAU;AACrB,EAAO,GAAI,SAAS,OAAO;AAC3B,MAAW,GAAI,QAAQ;AACtB,IAAO,GAAI,OAAO,YAAY,IAAI,OAAO,OAAO;AAAA,EACjD;AACA,SAAO;AACR;", "names": []}