{"version": 3, "sources": ["../../src/libsql/session.ts"], "sourcesContent": ["import type { Client, InArgs, InStatement, ResultSet, Transaction } from '@libsql/client';\nimport type { BatchItem as BatchItem } from '~/batch.ts';\nimport { type Cache, NoopCache } from '~/cache/core/index.ts';\nimport type { WithCacheConfig } from '~/cache/core/types.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport { fillPlaceholders, type Query, sql } from '~/sql/sql.ts';\nimport type { SQLiteAsyncDialect } from '~/sqlite-core/dialect.ts';\nimport { SQLiteTransaction } from '~/sqlite-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/sqlite-core/query-builders/select.types.ts';\nimport type {\n\tPreparedQueryConfig as PreparedQueryConfigBase,\n\tSQLiteExecuteMethod,\n\tSQLiteTransactionConfig,\n} from '~/sqlite-core/session.ts';\nimport { SQLitePreparedQuery, SQLiteSession } from '~/sqlite-core/session.ts';\nimport { mapResultRow } from '~/utils.ts';\n\nexport interface LibSQLSessionOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n}\n\ntype PreparedQueryConfig = Omit<PreparedQueryConfigBase, 'statement' | 'run'>;\n\nexport class LibSQLSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends SQLiteSession<'async', ResultSet, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'LibSQLSession';\n\n\tprivate logger: Logger;\n\tprivate cache: Cache;\n\n\tconstructor(\n\t\tprivate client: Client,\n\t\tdialect: SQLiteAsyncDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: LibSQLSessionOptions,\n\t\tprivate tx: Transaction | undefined,\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t\tthis.cache = options.cache ?? new NoopCache();\n\t}\n\n\tprepareQuery<T extends Omit<PreparedQueryConfig, 'run'>>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => unknown,\n\t\tqueryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t},\n\t\tcacheConfig?: WithCacheConfig,\n\t): LibSQLPreparedQuery<T> {\n\t\treturn new LibSQLPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery,\n\t\t\tthis.logger,\n\t\t\tthis.cache,\n\t\t\tqueryMetadata,\n\t\t\tcacheConfig,\n\t\t\tfields,\n\t\t\tthis.tx,\n\t\t\texecuteMethod,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\tasync batch<T extends BatchItem<'sqlite'>[] | readonly BatchItem<'sqlite'>[]>(queries: T) {\n\t\tconst preparedQueries: PreparedQuery[] = [];\n\t\tconst builtQueries: InStatement[] = [];\n\n\t\tfor (const query of queries) {\n\t\t\tconst preparedQuery = query._prepare();\n\t\t\tconst builtQuery = preparedQuery.getQuery();\n\t\t\tpreparedQueries.push(preparedQuery);\n\t\t\tbuiltQueries.push({ sql: builtQuery.sql, args: builtQuery.params as InArgs });\n\t\t}\n\n\t\tconst batchResults = await this.client.batch(builtQueries);\n\t\treturn batchResults.map((result, i) => preparedQueries[i]!.mapResult(result, true));\n\t}\n\n\tasync migrate<T extends BatchItem<'sqlite'>[] | readonly BatchItem<'sqlite'>[]>(queries: T) {\n\t\tconst preparedQueries: PreparedQuery[] = [];\n\t\tconst builtQueries: InStatement[] = [];\n\n\t\tfor (const query of queries) {\n\t\t\tconst preparedQuery = query._prepare();\n\t\t\tconst builtQuery = preparedQuery.getQuery();\n\t\t\tpreparedQueries.push(preparedQuery);\n\t\t\tbuiltQueries.push({ sql: builtQuery.sql, args: builtQuery.params as InArgs });\n\t\t}\n\n\t\tconst batchResults = await this.client.migrate(builtQueries);\n\t\treturn batchResults.map((result, i) => preparedQueries[i]!.mapResult(result, true));\n\t}\n\n\toverride async transaction<T>(\n\t\ttransaction: (db: LibSQLTransaction<TFullSchema, TSchema>) => T | Promise<T>,\n\t\t_config?: SQLiteTransactionConfig,\n\t): Promise<T> {\n\t\t// TODO: support transaction behavior\n\t\tconst libsqlTx = await this.client.transaction();\n\t\tconst session = new LibSQLSession<TFullSchema, TSchema>(\n\t\t\tthis.client,\n\t\t\tthis.dialect,\n\t\t\tthis.schema,\n\t\t\tthis.options,\n\t\t\tlibsqlTx,\n\t\t);\n\t\tconst tx = new LibSQLTransaction<TFullSchema, TSchema>('async', this.dialect, session, this.schema);\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait libsqlTx.commit();\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tawait libsqlTx.rollback();\n\t\t\tthrow err;\n\t\t}\n\t}\n\n\toverride extractRawAllValueFromBatchResult(result: unknown): unknown {\n\t\treturn (result as ResultSet).rows;\n\t}\n\n\toverride extractRawGetValueFromBatchResult(result: unknown): unknown {\n\t\treturn (result as ResultSet).rows[0];\n\t}\n\n\toverride extractRawValuesValueFromBatchResult(result: unknown): unknown {\n\t\treturn (result as ResultSet).rows;\n\t}\n}\n\nexport class LibSQLTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends SQLiteTransaction<'async', ResultSet, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'LibSQLTransaction';\n\n\toverride async transaction<T>(transaction: (tx: LibSQLTransaction<TFullSchema, TSchema>) => Promise<T>): Promise<T> {\n\t\tconst savepointName = `sp${this.nestedIndex}`;\n\t\tconst tx = new LibSQLTransaction('async', this.dialect, this.session, this.schema, this.nestedIndex + 1);\n\t\tawait this.session.run(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait this.session.run(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tawait this.session.run(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport class LibSQLPreparedQuery<T extends PreparedQueryConfig = PreparedQueryConfig> extends SQLitePreparedQuery<\n\t{ type: 'async'; run: ResultSet; all: T['all']; get: T['get']; values: T['values']; execute: T['execute'] }\n> {\n\tstatic override readonly [entityKind]: string = 'LibSQLPreparedQuery';\n\n\tconstructor(\n\t\tprivate client: Client,\n\t\tquery: Query,\n\t\tprivate logger: Logger,\n\t\tcache: Cache,\n\t\tqueryMetadata: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t} | undefined,\n\t\tcacheConfig: WithCacheConfig | undefined,\n\t\t/** @internal */ public fields: SelectedFieldsOrdered | undefined,\n\t\tprivate tx: Transaction | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\t/** @internal */ public customResultMapper?: (\n\t\t\trows: unknown[][],\n\t\t\tmapColumnValue?: (value: unknown) => unknown,\n\t\t) => unknown,\n\t) {\n\t\tsuper('async', executeMethod, query, cache, queryMetadata, cacheConfig);\n\t\tthis.customResultMapper = customResultMapper;\n\t\tthis.fields = fields;\n\t}\n\n\tasync run(placeholderValues?: Record<string, unknown>): Promise<ResultSet> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn await this.queryWithCache(this.query.sql, params, async () => {\n\t\t\tconst stmt: InStatement = { sql: this.query.sql, args: params as InArgs };\n\t\t\treturn this.tx ? this.tx.execute(stmt) : this.client.execute(stmt);\n\t\t});\n\t}\n\n\tasync all(placeholderValues?: Record<string, unknown>): Promise<T['all']> {\n\t\tconst { fields, logger, query, tx, client, customResultMapper } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst params = fillPlaceholders(query.params, placeholderValues ?? {});\n\t\t\tlogger.logQuery(query.sql, params);\n\t\t\treturn await this.queryWithCache(query.sql, params, async () => {\n\t\t\t\tconst stmt: InStatement = { sql: query.sql, args: params as InArgs };\n\t\t\t\treturn (tx ? tx.execute(stmt) : client.execute(stmt)).then(({ rows }) => this.mapAllResult(rows));\n\t\t\t});\n\t\t}\n\n\t\tconst rows = await this.values(placeholderValues) as unknown[][];\n\n\t\treturn this.mapAllResult(rows);\n\t}\n\n\toverride mapAllResult(rows: unknown, isFromBatch?: boolean): unknown {\n\t\tif (isFromBatch) {\n\t\t\trows = (rows as ResultSet).rows;\n\t\t}\n\n\t\tif (!this.fields && !this.customResultMapper) {\n\t\t\treturn (rows as unknown[]).map((row) => normalizeRow(row));\n\t\t}\n\n\t\tif (this.customResultMapper) {\n\t\t\treturn this.customResultMapper(rows as unknown[][], normalizeFieldValue) as T['all'];\n\t\t}\n\n\t\treturn (rows as unknown[]).map((row) => {\n\t\t\treturn mapResultRow(\n\t\t\t\tthis.fields!,\n\t\t\t\tArray.prototype.slice.call(row).map((v) => normalizeFieldValue(v)),\n\t\t\t\tthis.joinsNotNullableMap,\n\t\t\t);\n\t\t});\n\t}\n\n\tasync get(placeholderValues?: Record<string, unknown>): Promise<T['get']> {\n\t\tconst { fields, logger, query, tx, client, customResultMapper } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst params = fillPlaceholders(query.params, placeholderValues ?? {});\n\t\t\tlogger.logQuery(query.sql, params);\n\t\t\treturn await this.queryWithCache(query.sql, params, async () => {\n\t\t\t\tconst stmt: InStatement = { sql: query.sql, args: params as InArgs };\n\t\t\t\treturn (tx ? tx.execute(stmt) : client.execute(stmt)).then(({ rows }) => this.mapGetResult(rows));\n\t\t\t});\n\t\t}\n\n\t\tconst rows = await this.values(placeholderValues) as unknown[][];\n\n\t\treturn this.mapGetResult(rows);\n\t}\n\n\toverride mapGetResult(rows: unknown, isFromBatch?: boolean): unknown {\n\t\tif (isFromBatch) {\n\t\t\trows = (rows as ResultSet).rows;\n\t\t}\n\n\t\tconst row = (rows as unknown[])[0];\n\n\t\tif (!this.fields && !this.customResultMapper) {\n\t\t\treturn normalizeRow(row);\n\t\t}\n\n\t\tif (!row) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (this.customResultMapper) {\n\t\t\treturn this.customResultMapper(rows as unknown[][], normalizeFieldValue) as T['get'];\n\t\t}\n\n\t\treturn mapResultRow(\n\t\t\tthis.fields!,\n\t\t\tArray.prototype.slice.call(row).map((v) => normalizeFieldValue(v)),\n\t\t\tthis.joinsNotNullableMap,\n\t\t);\n\t}\n\n\tasync values(placeholderValues?: Record<string, unknown>): Promise<T['values']> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn await this.queryWithCache(this.query.sql, params, async () => {\n\t\t\tconst stmt: InStatement = { sql: this.query.sql, args: params as InArgs };\n\t\t\treturn (this.tx ? this.tx.execute(stmt) : this.client.execute(stmt)).then(({ rows }) => rows) as Promise<\n\t\t\t\tT['values']\n\t\t\t>;\n\t\t});\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nfunction normalizeRow(obj: any) {\n\t// The libSQL node-sqlite3 compatibility wrapper returns rows\n\t// that can be accessed both as objects and arrays. Let's\n\t// turn them into objects what's what other SQLite drivers\n\t// do.\n\treturn Object.keys(obj).reduce((acc: Record<string, any>, key) => {\n\t\tif (Object.prototype.propertyIsEnumerable.call(obj, key)) {\n\t\t\tacc[key] = obj[key];\n\t\t}\n\t\treturn acc;\n\t}, {});\n}\n\nfunction normalizeFieldValue(value: unknown) {\n\tif (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) { // eslint-disable-line no-instanceof/no-instanceof\n\t\tif (typeof Buffer !== 'undefined') {\n\t\t\tif (!(value instanceof Buffer)) { // eslint-disable-line no-instanceof/no-instanceof\n\t\t\t\treturn Buffer.from(value);\n\t\t\t}\n\t\t\treturn value;\n\t\t}\n\t\tif (typeof TextDecoder !== 'undefined') {\n\t\t\treturn new TextDecoder().decode(value);\n\t\t}\n\t\tthrow new Error('TextDecoder is not available. Please provide either Buffer or TextDecoder polyfill.');\n\t}\n\treturn value;\n}\n"], "mappings": "AAEA,SAAqB,iBAAiB;AAEtC,SAAS,kBAAkB;AAE3B,SAAS,kBAAkB;AAG3B,SAAS,kBAA8B,WAAW;AAElD,SAAS,yBAAyB;AAOlC,SAAS,qBAAqB,qBAAqB;AACnD,SAAS,oBAAoB;AAStB,MAAM,sBAGH,cAAwD;AAAA,EAMjE,YACS,QACR,SACQ,QACA,SACA,IACP;AACD,UAAM,OAAO;AANL;AAEA;AACA;AACA;AAGR,SAAK,SAAS,QAAQ,UAAU,IAAI,WAAW;AAC/C,SAAK,QAAQ,QAAQ,SAAS,IAAI,UAAU;AAAA,EAC7C;AAAA,EAfA,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EACA;AAAA,EAcR,aACC,OACA,QACA,eACA,uBACA,oBACA,eAIA,aACyB;AACzB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,MAAwE,SAAY;AACzF,UAAM,kBAAmC,CAAC;AAC1C,UAAM,eAA8B,CAAC;AAErC,eAAW,SAAS,SAAS;AAC5B,YAAM,gBAAgB,MAAM,SAAS;AACrC,YAAM,aAAa,cAAc,SAAS;AAC1C,sBAAgB,KAAK,aAAa;AAClC,mBAAa,KAAK,EAAE,KAAK,WAAW,KAAK,MAAM,WAAW,OAAiB,CAAC;AAAA,IAC7E;AAEA,UAAM,eAAe,MAAM,KAAK,OAAO,MAAM,YAAY;AACzD,WAAO,aAAa,IAAI,CAAC,QAAQ,MAAM,gBAAgB,CAAC,EAAG,UAAU,QAAQ,IAAI,CAAC;AAAA,EACnF;AAAA,EAEA,MAAM,QAA0E,SAAY;AAC3F,UAAM,kBAAmC,CAAC;AAC1C,UAAM,eAA8B,CAAC;AAErC,eAAW,SAAS,SAAS;AAC5B,YAAM,gBAAgB,MAAM,SAAS;AACrC,YAAM,aAAa,cAAc,SAAS;AAC1C,sBAAgB,KAAK,aAAa;AAClC,mBAAa,KAAK,EAAE,KAAK,WAAW,KAAK,MAAM,WAAW,OAAiB,CAAC;AAAA,IAC7E;AAEA,UAAM,eAAe,MAAM,KAAK,OAAO,QAAQ,YAAY;AAC3D,WAAO,aAAa,IAAI,CAAC,QAAQ,MAAM,gBAAgB,CAAC,EAAG,UAAU,QAAQ,IAAI,CAAC;AAAA,EACnF;AAAA,EAEA,MAAe,YACd,aACA,SACa;AAEb,UAAM,WAAW,MAAM,KAAK,OAAO,YAAY;AAC/C,UAAM,UAAU,IAAI;AAAA,MACnB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,IACD;AACA,UAAM,KAAK,IAAI,kBAAwC,SAAS,KAAK,SAAS,SAAS,KAAK,MAAM;AAClG,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,SAAS,OAAO;AACtB,aAAO;AAAA,IACR,SAAS,KAAK;AACb,YAAM,SAAS,SAAS;AACxB,YAAM;AAAA,IACP;AAAA,EACD;AAAA,EAES,kCAAkC,QAA0B;AACpE,WAAQ,OAAqB;AAAA,EAC9B;AAAA,EAES,kCAAkC,QAA0B;AACpE,WAAQ,OAAqB,KAAK,CAAC;AAAA,EACpC;AAAA,EAES,qCAAqC,QAA0B;AACvE,WAAQ,OAAqB;AAAA,EAC9B;AACD;AAEO,MAAM,0BAGH,kBAA4D;AAAA,EACrE,QAA0B,UAAU,IAAY;AAAA,EAEhD,MAAe,YAAe,aAAsF;AACnH,UAAM,gBAAgB,KAAK,KAAK,WAAW;AAC3C,UAAM,KAAK,IAAI,kBAAkB,SAAS,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,cAAc,CAAC;AACvG,UAAM,KAAK,QAAQ,IAAI,IAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AAC5D,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,KAAK,QAAQ,IAAI,IAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AACpE,aAAO;AAAA,IACR,SAAS,KAAK;AACb,YAAM,KAAK,QAAQ,IAAI,IAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AACxE,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAEO,MAAM,4BAAiF,oBAE5F;AAAA,EAGD,YACS,QACR,OACQ,QACR,OACA,eAIA,aACwB,QAChB,IACR,eACQ,wBACgB,oBAIvB;AACD,UAAM,SAAS,eAAe,OAAO,OAAO,eAAe,WAAW;AAlB9D;AAEA;AAOgB;AAChB;AAEA;AACgB;AAMxB,SAAK,qBAAqB;AAC1B,SAAK,SAAS;AAAA,EACf;AAAA,EAxBA,QAA0B,UAAU,IAAY;AAAA,EA0BhD,MAAM,IAAI,mBAAiE;AAC1E,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,MAAM,KAAK,eAAe,KAAK,MAAM,KAAK,QAAQ,YAAY;AACpE,YAAM,OAAoB,EAAE,KAAK,KAAK,MAAM,KAAK,MAAM,OAAiB;AACxE,aAAO,KAAK,KAAK,KAAK,GAAG,QAAQ,IAAI,IAAI,KAAK,OAAO,QAAQ,IAAI;AAAA,IAClE,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,IAAI,mBAAgE;AACzE,UAAM,EAAE,QAAQ,QAAQ,OAAO,IAAI,QAAQ,mBAAmB,IAAI;AAClE,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAM,SAAS,iBAAiB,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AACrE,aAAO,SAAS,MAAM,KAAK,MAAM;AACjC,aAAO,MAAM,KAAK,eAAe,MAAM,KAAK,QAAQ,YAAY;AAC/D,cAAM,OAAoB,EAAE,KAAK,MAAM,KAAK,MAAM,OAAiB;AACnE,gBAAQ,KAAK,GAAG,QAAQ,IAAI,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK,CAAC,EAAE,MAAAA,MAAK,MAAM,KAAK,aAAaA,KAAI,CAAC;AAAA,MACjG,CAAC;AAAA,IACF;AAEA,UAAM,OAAO,MAAM,KAAK,OAAO,iBAAiB;AAEhD,WAAO,KAAK,aAAa,IAAI;AAAA,EAC9B;AAAA,EAES,aAAa,MAAe,aAAgC;AACpE,QAAI,aAAa;AAChB,aAAQ,KAAmB;AAAA,IAC5B;AAEA,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,oBAAoB;AAC7C,aAAQ,KAAmB,IAAI,CAAC,QAAQ,aAAa,GAAG,CAAC;AAAA,IAC1D;AAEA,QAAI,KAAK,oBAAoB;AAC5B,aAAO,KAAK,mBAAmB,MAAqB,mBAAmB;AAAA,IACxE;AAEA,WAAQ,KAAmB,IAAI,CAAC,QAAQ;AACvC,aAAO;AAAA,QACN,KAAK;AAAA,QACL,MAAM,UAAU,MAAM,KAAK,GAAG,EAAE,IAAI,CAAC,MAAM,oBAAoB,CAAC,CAAC;AAAA,QACjE,KAAK;AAAA,MACN;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,IAAI,mBAAgE;AACzE,UAAM,EAAE,QAAQ,QAAQ,OAAO,IAAI,QAAQ,mBAAmB,IAAI;AAClE,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAM,SAAS,iBAAiB,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AACrE,aAAO,SAAS,MAAM,KAAK,MAAM;AACjC,aAAO,MAAM,KAAK,eAAe,MAAM,KAAK,QAAQ,YAAY;AAC/D,cAAM,OAAoB,EAAE,KAAK,MAAM,KAAK,MAAM,OAAiB;AACnE,gBAAQ,KAAK,GAAG,QAAQ,IAAI,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK,CAAC,EAAE,MAAAA,MAAK,MAAM,KAAK,aAAaA,KAAI,CAAC;AAAA,MACjG,CAAC;AAAA,IACF;AAEA,UAAM,OAAO,MAAM,KAAK,OAAO,iBAAiB;AAEhD,WAAO,KAAK,aAAa,IAAI;AAAA,EAC9B;AAAA,EAES,aAAa,MAAe,aAAgC;AACpE,QAAI,aAAa;AAChB,aAAQ,KAAmB;AAAA,IAC5B;AAEA,UAAM,MAAO,KAAmB,CAAC;AAEjC,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,oBAAoB;AAC7C,aAAO,aAAa,GAAG;AAAA,IACxB;AAEA,QAAI,CAAC,KAAK;AACT,aAAO;AAAA,IACR;AAEA,QAAI,KAAK,oBAAoB;AAC5B,aAAO,KAAK,mBAAmB,MAAqB,mBAAmB;AAAA,IACxE;AAEA,WAAO;AAAA,MACN,KAAK;AAAA,MACL,MAAM,UAAU,MAAM,KAAK,GAAG,EAAE,IAAI,CAAC,MAAM,oBAAoB,CAAC,CAAC;AAAA,MACjE,KAAK;AAAA,IACN;AAAA,EACD;AAAA,EAEA,MAAM,OAAO,mBAAmE;AAC/E,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,MAAM,KAAK,eAAe,KAAK,MAAM,KAAK,QAAQ,YAAY;AACpE,YAAM,OAAoB,EAAE,KAAK,KAAK,MAAM,KAAK,MAAM,OAAiB;AACxE,cAAQ,KAAK,KAAK,KAAK,GAAG,QAAQ,IAAI,IAAI,KAAK,OAAO,QAAQ,IAAI,GAAG,KAAK,CAAC,EAAE,KAAK,MAAM,IAAI;AAAA,IAG7F,CAAC;AAAA,EACF;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;AAEA,SAAS,aAAa,KAAU;AAK/B,SAAO,OAAO,KAAK,GAAG,EAAE,OAAO,CAAC,KAA0B,QAAQ;AACjE,QAAI,OAAO,UAAU,qBAAqB,KAAK,KAAK,GAAG,GAAG;AACzD,UAAI,GAAG,IAAI,IAAI,GAAG;AAAA,IACnB;AACA,WAAO;AAAA,EACR,GAAG,CAAC,CAAC;AACN;AAEA,SAAS,oBAAoB,OAAgB;AAC5C,MAAI,OAAO,gBAAgB,eAAe,iBAAiB,aAAa;AACvE,QAAI,OAAO,WAAW,aAAa;AAClC,UAAI,EAAE,iBAAiB,SAAS;AAC/B,eAAO,OAAO,KAAK,KAAK;AAAA,MACzB;AACA,aAAO;AAAA,IACR;AACA,QAAI,OAAO,gBAAgB,aAAa;AACvC,aAAO,IAAI,YAAY,EAAE,OAAO,KAAK;AAAA,IACtC;AACA,UAAM,IAAI,MAAM,qFAAqF;AAAA,EACtG;AACA,SAAO;AACR;", "names": ["rows"]}