{"version": 3, "sources": ["../../../src/mysql-core/columns/all.ts"], "sourcesContent": ["import { bigint } from './bigint.ts';\nimport { binary } from './binary.ts';\nimport { boolean } from './boolean.ts';\nimport { char } from './char.ts';\nimport { customType } from './custom.ts';\nimport { date } from './date.ts';\nimport { datetime } from './datetime.ts';\nimport { decimal } from './decimal.ts';\nimport { double } from './double.ts';\nimport { mysqlEnum } from './enum.ts';\nimport { float } from './float.ts';\nimport { int } from './int.ts';\nimport { json } from './json.ts';\nimport { mediumint } from './mediumint.ts';\nimport { real } from './real.ts';\nimport { serial } from './serial.ts';\nimport { smallint } from './smallint.ts';\nimport { longtext, mediumtext, text, tinytext } from './text.ts';\nimport { time } from './time.ts';\nimport { timestamp } from './timestamp.ts';\nimport { tinyint } from './tinyint.ts';\nimport { varbinary } from './varbinary.ts';\nimport { varchar } from './varchar.ts';\nimport { year } from './year.ts';\n\nexport function getMySqlColumnBuilders() {\n\treturn {\n\t\tbigint,\n\t\tbinary,\n\t\tboolean,\n\t\tchar,\n\t\tcustomType,\n\t\tdate,\n\t\tdatetime,\n\t\tdecimal,\n\t\tdouble,\n\t\tmysqlEnum,\n\t\tfloat,\n\t\tint,\n\t\tjson,\n\t\tmediumint,\n\t\treal,\n\t\tserial,\n\t\tsmallint,\n\t\ttext,\n\t\ttime,\n\t\ttimestamp,\n\t\ttinyint,\n\t\tvarbinary,\n\t\tvarchar,\n\t\tyear,\n\t\tlongtext,\n\t\tmediumtext,\n\t\ttinytext,\n\t};\n}\n\nexport type MySqlColumnBuilders = ReturnType<typeof getMySqlColumnBuilders>;\n"], "mappings": "AAAA,SAAS,cAAc;AACvB,SAAS,cAAc;AACvB,SAAS,eAAe;AACxB,SAAS,YAAY;AACrB,SAAS,kBAAkB;AAC3B,SAAS,YAAY;AACrB,SAAS,gBAAgB;AACzB,SAAS,eAAe;AACxB,SAAS,cAAc;AACvB,SAAS,iBAAiB;AAC1B,SAAS,aAAa;AACtB,SAAS,WAAW;AACpB,SAAS,YAAY;AACrB,SAAS,iBAAiB;AAC1B,SAAS,YAAY;AACrB,SAAS,cAAc;AACvB,SAAS,gBAAgB;AACzB,SAAS,UAAU,YAAY,MAAM,gBAAgB;AACrD,SAAS,YAAY;AACrB,SAAS,iBAAiB;AAC1B,SAAS,eAAe;AACxB,SAAS,iBAAiB;AAC1B,SAAS,eAAe;AACxB,SAAS,YAAY;AAEd,SAAS,yBAAyB;AACxC,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;", "names": []}