{"version": 3, "sources": ["../../../src/mysql-core/columns/char.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { getColumnNameAndConfig, type Writable } from '~/utils.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlCharBuilderInitial<\n\tTName extends string,\n\tTEnum extends [string, ...string[]],\n\tTLength extends number | undefined,\n> = MySqlCharBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'MySqlChar';\n\tdata: TEnum[number];\n\tdriverParam: number | string;\n\tenumValues: TEnum;\n\tlength: TLength;\n}>;\n\nexport class MySqlCharBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'MySqlChar'> & { length?: number | undefined },\n> extends MySqlColumnBuilder<\n\tT,\n\tMySqlCharConfig<T['enumValues'], T['length']>,\n\t{ length: T['length'] }\n> {\n\tstatic override readonly [entityKind]: string = 'MySqlCharBuilder';\n\n\tconstructor(name: T['name'], config: MySqlCharConfig<T['enumValues'], T['length']>) {\n\t\tsuper(name, 'string', 'MySqlChar');\n\t\tthis.config.length = config.length;\n\t\tthis.config.enum = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlChar<MakeColumnConfig<T, TTableName> & { length: T['length']; enumValues: T['enumValues'] }> {\n\t\treturn new MySqlChar<MakeColumnConfig<T, TTableName> & { length: T['length']; enumValues: T['enumValues'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlChar<T extends ColumnBaseConfig<'string', 'MySqlChar'> & { length?: number | undefined }>\n\textends MySqlColumn<T, MySqlCharConfig<T['enumValues'], T['length']>, { length: T['length'] }>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlChar';\n\n\treadonly length: T['length'] = this.config.length;\n\toverride readonly enumValues = this.config.enum;\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `char` : `char(${this.length})`;\n\t}\n}\n\nexport interface MySqlCharConfig<\n\tTEnum extends readonly string[] | string[] | undefined = readonly string[] | string[] | undefined,\n\tTLength extends number | undefined = number | undefined,\n> {\n\tenum?: TEnum;\n\tlength?: TLength;\n}\n\nexport function char(): MySqlCharBuilderInitial<'', [string, ...string[]], undefined>;\nexport function char<U extends string, T extends Readonly<[U, ...U[]]>, L extends number | undefined>(\n\tconfig?: MySqlCharConfig<T | Writable<T>, L>,\n): MySqlCharBuilderInitial<'', Writable<T>, L>;\nexport function char<\n\tTName extends string,\n\tU extends string,\n\tT extends Readonly<[U, ...U[]]>,\n\tL extends number | undefined,\n>(\n\tname: TName,\n\tconfig?: MySqlCharConfig<T | Writable<T>, L>,\n): MySqlCharBuilderInitial<TName, Writable<T>, L>;\nexport function char(a?: string | MySqlCharConfig, b: MySqlCharConfig = {}): any {\n\tconst { name, config } = getColumnNameAndConfig<MySqlCharConfig>(a, b);\n\treturn new MySqlCharBuilder(name, config as any);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAsD;AACtD,oBAAgD;AAgBzC,MAAM,yBAEH,iCAIR;AAAA,EACD,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAAuD;AACnF,UAAM,MAAM,UAAU,WAAW;AACjC,SAAK,OAAO,SAAS,OAAO;AAC5B,SAAK,OAAO,OAAO,OAAO;AAAA,EAC3B;AAAA;AAAA,EAGS,MACR,OACoG;AACpG,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,kBACJ,0BACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,SAAsB,KAAK,OAAO;AAAA,EACzB,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO,KAAK,WAAW,SAAY,SAAS,QAAQ,KAAK,MAAM;AAAA,EAChE;AACD;AAuBO,SAAS,KAAK,GAA8B,IAAqB,CAAC,GAAQ;AAChF,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAwC,GAAG,CAAC;AACrE,SAAO,IAAI,iBAAiB,MAAM,MAAa;AAChD;", "names": []}