{"version": 3, "sources": ["../../../src/mysql-core/columns/datetime.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlDateTimeBuilderInitial<TName extends string> = MySqlDateTimeBuilder<{\n\tname: TName;\n\tdataType: 'date';\n\tcolumnType: 'MySqlDateTime';\n\tdata: Date;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlDateTimeBuilder<T extends ColumnBuilderBaseConfig<'date', 'MySqlDateTime'>>\n\textends MySqlColumnBuilder<T, MySqlDatetimeConfig>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlDateTimeBuilder';\n\n\tconstructor(name: T['name'], config: MySqlDatetimeConfig | undefined) {\n\t\tsuper(name, 'date', 'MySqlDateTime');\n\t\tthis.config.fsp = config?.fsp;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlDateTime<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlDateTime<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlDateTime<T extends ColumnBaseConfig<'date', 'MySqlDateTime'>> extends MySqlColumn<T> {\n\tstatic override readonly [entityKind]: string = 'MySqlDateTime';\n\n\treadonly fsp: number | undefined;\n\n\tconstructor(\n\t\ttable: AnyMySqlTable<{ name: T['tableName'] }>,\n\t\tconfig: MySqlDateTimeBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t\tthis.fsp = config.fsp;\n\t}\n\n\tgetSQLType(): string {\n\t\tconst precision = this.fsp === undefined ? '' : `(${this.fsp})`;\n\t\treturn `datetime${precision}`;\n\t}\n\n\toverride mapToDriverValue(value: Date): unknown {\n\t\treturn value.toISOString().replace('T', ' ').replace('Z', '');\n\t}\n\n\toverride mapFromDriverValue(value: string): Date {\n\t\treturn new Date(value.replace(' ', 'T') + 'Z');\n\t}\n}\n\nexport type MySqlDateTimeStringBuilderInitial<TName extends string> = MySqlDateTimeStringBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'MySqlDateTimeString';\n\tdata: string;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlDateTimeStringBuilder<T extends ColumnBuilderBaseConfig<'string', 'MySqlDateTimeString'>>\n\textends MySqlColumnBuilder<T, MySqlDatetimeConfig>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlDateTimeStringBuilder';\n\n\tconstructor(name: T['name'], config: MySqlDatetimeConfig | undefined) {\n\t\tsuper(name, 'string', 'MySqlDateTimeString');\n\t\tthis.config.fsp = config?.fsp;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlDateTimeString<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlDateTimeString<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlDateTimeString<T extends ColumnBaseConfig<'string', 'MySqlDateTimeString'>> extends MySqlColumn<T> {\n\tstatic override readonly [entityKind]: string = 'MySqlDateTimeString';\n\n\treadonly fsp: number | undefined;\n\n\tconstructor(\n\t\ttable: AnyMySqlTable<{ name: T['tableName'] }>,\n\t\tconfig: MySqlDateTimeStringBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t\tthis.fsp = config.fsp;\n\t}\n\n\tgetSQLType(): string {\n\t\tconst precision = this.fsp === undefined ? '' : `(${this.fsp})`;\n\t\treturn `datetime${precision}`;\n\t}\n}\n\nexport type DatetimeFsp = 0 | 1 | 2 | 3 | 4 | 5 | 6;\n\nexport interface MySqlDatetimeConfig<TMode extends 'date' | 'string' = 'date' | 'string'> {\n\tmode?: TMode;\n\tfsp?: DatetimeFsp;\n}\n\nexport function datetime(): MySqlDateTimeBuilderInitial<''>;\nexport function datetime<TMode extends MySqlDatetimeConfig['mode'] & {}>(\n\tconfig?: MySqlDatetimeConfig<TMode>,\n): Equal<TMode, 'string'> extends true ? MySqlDateTimeStringBuilderInitial<''> : MySqlDateTimeBuilderInitial<''>;\nexport function datetime<TName extends string, TMode extends MySqlDatetimeConfig['mode'] & {}>(\n\tname: TName,\n\tconfig?: MySqlDatetimeConfig<TMode>,\n): Equal<TMode, 'string'> extends true ? MySqlDateTimeStringBuilderInitial<TName> : MySqlDateTimeBuilderInitial<TName>;\nexport function datetime(a?: string | MySqlDatetimeConfig, b?: MySqlDatetimeConfig) {\n\tconst { name, config } = getColumnNameAndConfig<MySqlDatetimeConfig | undefined>(a, b);\n\tif (config?.mode === 'string') {\n\t\treturn new MySqlDateTimeStringBuilder(name, config);\n\t}\n\treturn new MySqlDateTimeBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAmD;AACnD,oBAAgD;AAWzC,MAAM,6BACJ,iCACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAAyC;AACrE,UAAM,MAAM,QAAQ,eAAe;AACnC,SAAK,OAAO,MAAM,QAAQ;AAAA,EAC3B;AAAA;AAAA,EAGS,MACR,OACiD;AACjD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,sBAA2E,0BAAe;AAAA,EACtG,QAA0B,wBAAU,IAAY;AAAA,EAEvC;AAAA,EAET,YACC,OACA,QACC;AACD,UAAM,OAAO,MAAM;AACnB,SAAK,MAAM,OAAO;AAAA,EACnB;AAAA,EAEA,aAAqB;AACpB,UAAM,YAAY,KAAK,QAAQ,SAAY,KAAK,IAAI,KAAK,GAAG;AAC5D,WAAO,WAAW,SAAS;AAAA,EAC5B;AAAA,EAES,iBAAiB,OAAsB;AAC/C,WAAO,MAAM,YAAY,EAAE,QAAQ,KAAK,GAAG,EAAE,QAAQ,KAAK,EAAE;AAAA,EAC7D;AAAA,EAES,mBAAmB,OAAqB;AAChD,WAAO,oBAAI,KAAK,MAAM,QAAQ,KAAK,GAAG,IAAI,GAAG;AAAA,EAC9C;AACD;AAWO,MAAM,mCACJ,iCACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAAyC;AACrE,UAAM,MAAM,UAAU,qBAAqB;AAC3C,SAAK,OAAO,MAAM,QAAQ;AAAA,EAC3B;AAAA;AAAA,EAGS,MACR,OACuD;AACvD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,4BAAyF,0BAAe;AAAA,EACpH,QAA0B,wBAAU,IAAY;AAAA,EAEvC;AAAA,EAET,YACC,OACA,QACC;AACD,UAAM,OAAO,MAAM;AACnB,SAAK,MAAM,OAAO;AAAA,EACnB;AAAA,EAEA,aAAqB;AACpB,UAAM,YAAY,KAAK,QAAQ,SAAY,KAAK,IAAI,KAAK,GAAG;AAC5D,WAAO,WAAW,SAAS;AAAA,EAC5B;AACD;AAiBO,SAAS,SAAS,GAAkC,GAAyB;AACnF,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAwD,GAAG,CAAC;AACrF,MAAI,QAAQ,SAAS,UAAU;AAC9B,WAAO,IAAI,2BAA2B,MAAM,MAAM;AAAA,EACnD;AACA,SAAO,IAAI,qBAAqB,MAAM,MAAM;AAC7C;", "names": []}