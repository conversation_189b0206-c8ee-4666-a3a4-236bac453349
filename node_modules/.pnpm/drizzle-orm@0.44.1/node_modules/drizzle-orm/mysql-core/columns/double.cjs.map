{"version": 3, "sources": ["../../../src/mysql-core/columns/double.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { MySqlColumnBuilderWithAutoIncrement, MySqlColumnWithAutoIncrement } from './common.ts';\n\nexport type MySqlDoubleBuilderInitial<TName extends string> = MySqlDoubleBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'MySqlDouble';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlDoubleBuilder<T extends ColumnBuilderBaseConfig<'number', 'MySqlDouble'>>\n\textends MySqlColumnBuilderWithAutoIncrement<T, MySqlDoubleConfig>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlDoubleBuilder';\n\n\tconstructor(name: T['name'], config: MySqlDoubleConfig | undefined) {\n\t\tsuper(name, 'number', 'MySqlDouble');\n\t\tthis.config.precision = config?.precision;\n\t\tthis.config.scale = config?.scale;\n\t\tthis.config.unsigned = config?.unsigned;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlDouble<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlDouble<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class MySqlDouble<T extends ColumnBaseConfig<'number', 'MySqlDouble'>>\n\textends MySqlColumnWithAutoIncrement<T, MySqlDoubleConfig>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlDouble';\n\n\treadonly precision: number | undefined = this.config.precision;\n\treadonly scale: number | undefined = this.config.scale;\n\treadonly unsigned: boolean | undefined = this.config.unsigned;\n\n\tgetSQLType(): string {\n\t\tlet type = '';\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\ttype += `double(${this.precision},${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\ttype += 'double';\n\t\t} else {\n\t\t\ttype += `double(${this.precision})`;\n\t\t}\n\t\treturn this.unsigned ? `${type} unsigned` : type;\n\t}\n}\n\nexport interface MySqlDoubleConfig {\n\tprecision?: number;\n\tscale?: number;\n\tunsigned?: boolean;\n}\n\nexport function double(): MySqlDoubleBuilderInitial<''>;\nexport function double(\n\tconfig?: MySqlDoubleConfig,\n): MySqlDoubleBuilderInitial<''>;\nexport function double<TName extends string>(\n\tname: TName,\n\tconfig?: MySqlDoubleConfig,\n): MySqlDoubleBuilderInitial<TName>;\nexport function double(a?: string | MySqlDoubleConfig, b?: MySqlDoubleConfig) {\n\tconst { name, config } = getColumnNameAndConfig<MySqlDoubleConfig>(a, b);\n\treturn new MySqlDoubleBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAuC;AACvC,oBAAkF;AAW3E,MAAM,2BACJ,kDACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAAuC;AACnE,UAAM,MAAM,UAAU,aAAa;AACnC,SAAK,OAAO,YAAY,QAAQ;AAChC,SAAK,OAAO,QAAQ,QAAQ;AAC5B,SAAK,OAAO,WAAW,QAAQ;AAAA,EAChC;AAAA;AAAA,EAGS,MACR,OAC+C;AAC/C,WAAO,IAAI,YAA6C,OAAO,KAAK,MAA8C;AAAA,EACnH;AACD;AAEO,MAAM,oBACJ,2CACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,YAAgC,KAAK,OAAO;AAAA,EAC5C,QAA4B,KAAK,OAAO;AAAA,EACxC,WAAgC,KAAK,OAAO;AAAA,EAErD,aAAqB;AACpB,QAAI,OAAO;AACX,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,cAAQ,UAAU,KAAK,SAAS,IAAI,KAAK,KAAK;AAAA,IAC/C,WAAW,KAAK,cAAc,QAAW;AACxC,cAAQ;AAAA,IACT,OAAO;AACN,cAAQ,UAAU,KAAK,SAAS;AAAA,IACjC;AACA,WAAO,KAAK,WAAW,GAAG,IAAI,cAAc;AAAA,EAC7C;AACD;AAgBO,SAAS,OAAO,GAAgC,GAAuB;AAC7E,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA0C,GAAG,CAAC;AACvE,SAAO,IAAI,mBAAmB,MAAM,MAAM;AAC3C;", "names": []}