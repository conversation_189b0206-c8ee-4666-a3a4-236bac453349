{"version": 3, "sources": ["../../../src/mysql-core/columns/float.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { MySqlColumnBuilderWithAutoIncrement, MySqlColumnWithAutoIncrement } from './common.ts';\n\nexport type MySqlFloatBuilderInitial<TName extends string> = MySqlFloatBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'MySqlFloat';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlFloatBuilder<T extends ColumnBuilderBaseConfig<'number', 'MySqlFloat'>>\n\textends MySqlColumnBuilderWithAutoIncrement<T, MySqlFloatConfig>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlFloatBuilder';\n\n\tconstructor(name: T['name'], config: MySqlFloatConfig | undefined) {\n\t\tsuper(name, 'number', 'MySqlFloat');\n\t\tthis.config.precision = config?.precision;\n\t\tthis.config.scale = config?.scale;\n\t\tthis.config.unsigned = config?.unsigned;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlFloat<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlFloat<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class MySqlFloat<T extends ColumnBaseConfig<'number', 'MySqlFloat'>>\n\textends MySqlColumnWithAutoIncrement<T, MySqlFloatConfig>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlFloat';\n\n\treadonly precision: number | undefined = this.config.precision;\n\treadonly scale: number | undefined = this.config.scale;\n\treadonly unsigned: boolean | undefined = this.config.unsigned;\n\n\tgetSQLType(): string {\n\t\tlet type = '';\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\ttype += `float(${this.precision},${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\ttype += 'float';\n\t\t} else {\n\t\t\ttype += `float(${this.precision})`;\n\t\t}\n\t\treturn this.unsigned ? `${type} unsigned` : type;\n\t}\n}\n\nexport interface MySqlFloatConfig {\n\tprecision?: number;\n\tscale?: number;\n\tunsigned?: boolean;\n}\n\nexport function float(): MySqlFloatBuilderInitial<''>;\nexport function float(\n\tconfig?: MySqlFloatConfig,\n): MySqlFloatBuilderInitial<''>;\nexport function float<TName extends string>(\n\tname: TName,\n\tconfig?: MySqlFloatConfig,\n): MySqlFloatBuilderInitial<TName>;\nexport function float(a?: string | MySqlFloatConfig, b?: MySqlFloatConfig) {\n\tconst { name, config } = getColumnNameAndConfig<MySqlFloatConfig>(a, b);\n\treturn new MySqlFloatBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAuC;AACvC,oBAAkF;AAW3E,MAAM,0BACJ,kDACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAAsC;AAClE,UAAM,MAAM,UAAU,YAAY;AAClC,SAAK,OAAO,YAAY,QAAQ;AAChC,SAAK,OAAO,QAAQ,QAAQ;AAC5B,SAAK,OAAO,WAAW,QAAQ;AAAA,EAChC;AAAA;AAAA,EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI,WAA4C,OAAO,KAAK,MAA8C;AAAA,EAClH;AACD;AAEO,MAAM,mBACJ,2CACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,YAAgC,KAAK,OAAO;AAAA,EAC5C,QAA4B,KAAK,OAAO;AAAA,EACxC,WAAgC,KAAK,OAAO;AAAA,EAErD,aAAqB;AACpB,QAAI,OAAO;AACX,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,cAAQ,SAAS,KAAK,SAAS,IAAI,KAAK,KAAK;AAAA,IAC9C,WAAW,KAAK,cAAc,QAAW;AACxC,cAAQ;AAAA,IACT,OAAO;AACN,cAAQ,SAAS,KAAK,SAAS;AAAA,IAChC;AACA,WAAO,KAAK,WAAW,GAAG,IAAI,cAAc;AAAA,EAC7C;AACD;AAgBO,SAAS,MAAM,GAA+B,GAAsB;AAC1E,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAyC,GAAG,CAAC;AACtE,SAAO,IAAI,kBAAkB,MAAM,MAAM;AAC1C;", "names": []}