{"version": 3, "sources": ["../../../src/mysql-core/columns/json.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlJsonBuilderInitial<TName extends string> = MySqlJsonBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'MySqlJson';\n\tdata: unknown;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlJsonBuilder<T extends ColumnBuilderBaseConfig<'json', 'MySqlJson'>> extends MySqlColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'MySqlJsonBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'json', 'MySqlJson');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlJson<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlJson<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class MySqlJson<T extends ColumnBaseConfig<'json', 'MySqlJson'>> extends MySqlColumn<T> {\n\tstatic override readonly [entityKind]: string = 'MySqlJson';\n\n\tgetSQLType(): string {\n\t\treturn 'json';\n\t}\n\n\toverride mapToDriverValue(value: T['data']): string {\n\t\treturn JSON.stringify(value);\n\t}\n}\n\nexport function json(): MySqlJsonBuilderInitial<''>;\nexport function json<TName extends string>(name: TName): MySqlJsonBuilderInitial<TName>;\nexport function json(name?: string) {\n\treturn new MySqlJsonBuilder(name ?? '');\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,aAAa,0BAA0B;AAWzC,MAAM,yBAAiF,mBAAsB;AAAA,EACnH,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,QAAQ,WAAW;AAAA,EAChC;AAAA;AAAA,EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI,UAA2C,OAAO,KAAK,MAA8C;AAAA,EACjH;AACD;AAEO,MAAM,kBAAmE,YAAe;AAAA,EAC9F,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,iBAAiB,OAA0B;AACnD,WAAO,KAAK,UAAU,KAAK;AAAA,EAC5B;AACD;AAIO,SAAS,KAAK,MAAe;AACnC,SAAO,IAAI,iBAAiB,QAAQ,EAAE;AACvC;", "names": []}