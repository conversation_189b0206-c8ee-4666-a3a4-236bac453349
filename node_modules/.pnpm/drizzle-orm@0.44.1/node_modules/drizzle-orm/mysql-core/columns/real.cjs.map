{"version": 3, "sources": ["../../../src/mysql-core/columns/real.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { MySqlColumnBuilderWithAutoIncrement, MySqlColumnWithAutoIncrement } from './common.ts';\n\nexport type MySqlRealBuilderInitial<TName extends string> = MySqlRealBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'MySqlReal';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlRealBuilder<T extends ColumnBuilderBaseConfig<'number', 'MySqlReal'>>\n\textends MySqlColumnBuilderWithAutoIncrement<\n\t\tT,\n\t\tMySqlRealConfig\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlRealBuilder';\n\n\tconstructor(name: T['name'], config: MySqlRealConfig | undefined) {\n\t\tsuper(name, 'number', 'MySqlReal');\n\t\tthis.config.precision = config?.precision;\n\t\tthis.config.scale = config?.scale;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlReal<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlReal<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class MySqlReal<T extends ColumnBaseConfig<'number', 'MySqlReal'>> extends MySqlColumnWithAutoIncrement<\n\tT,\n\tMySqlRealConfig\n> {\n\tstatic override readonly [entityKind]: string = 'MySqlReal';\n\n\tprecision: number | undefined = this.config.precision;\n\tscale: number | undefined = this.config.scale;\n\n\tgetSQLType(): string {\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\treturn `real(${this.precision}, ${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\treturn 'real';\n\t\t} else {\n\t\t\treturn `real(${this.precision})`;\n\t\t}\n\t}\n}\n\nexport interface MySqlRealConfig {\n\tprecision?: number;\n\tscale?: number;\n}\n\nexport function real(): MySqlRealBuilderInitial<''>;\nexport function real(\n\tconfig?: MySqlRealConfig,\n): MySqlRealBuilderInitial<''>;\nexport function real<TName extends string>(\n\tname: TName,\n\tconfig?: MySqlRealConfig,\n): MySqlRealBuilderInitial<TName>;\nexport function real(a?: string | MySqlRealConfig, b: MySqlRealConfig = {}) {\n\tconst { name, config } = getColumnNameAndConfig<MySqlRealConfig>(a, b);\n\treturn new MySqlRealBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAuC;AACvC,oBAAkF;AAW3E,MAAM,yBACJ,kDAIT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAAqC;AACjE,UAAM,MAAM,UAAU,WAAW;AACjC,SAAK,OAAO,YAAY,QAAQ;AAChC,SAAK,OAAO,QAAQ,QAAQ;AAAA,EAC7B;AAAA;AAAA,EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI,UAA2C,OAAO,KAAK,MAA8C;AAAA,EACjH;AACD;AAEO,MAAM,kBAAqE,2CAGhF;AAAA,EACD,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAgC,KAAK,OAAO;AAAA,EAC5C,QAA4B,KAAK,OAAO;AAAA,EAExC,aAAqB;AACpB,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,aAAO,QAAQ,KAAK,SAAS,KAAK,KAAK,KAAK;AAAA,IAC7C,WAAW,KAAK,cAAc,QAAW;AACxC,aAAO;AAAA,IACR,OAAO;AACN,aAAO,QAAQ,KAAK,SAAS;AAAA,IAC9B;AAAA,EACD;AACD;AAeO,SAAS,KAAK,GAA8B,IAAqB,CAAC,GAAG;AAC3E,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAwC,GAAG,CAAC;AACrE,SAAO,IAAI,iBAAiB,MAAM,MAAM;AACzC;", "names": []}