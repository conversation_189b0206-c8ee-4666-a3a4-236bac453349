{"version": 3, "sources": ["../../../src/mysql-core/columns/text.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { getColumnNameAndConfig, type Writable } from '~/utils.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlTextColumnType = 'tinytext' | 'text' | 'mediumtext' | 'longtext';\n\nexport type MySqlTextBuilderInitial<TName extends string, TEnum extends [string, ...string[]]> = MySqlTextBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'MySqlText';\n\tdata: TEnum[number];\n\tdriverParam: string;\n\tenumValues: TEnum;\n}>;\n\nexport class MySqlTextBuilder<T extends ColumnBuilderBaseConfig<'string', 'MySqlText'>> extends MySqlColumnBuilder<\n\tT,\n\t{ textType: MySqlTextColumnType; enumValues: T['enumValues'] }\n> {\n\tstatic override readonly [entityKind]: string = 'MySqlTextBuilder';\n\n\tconstructor(name: T['name'], textType: MySqlTextColumnType, config: MySqlTextConfig<T['enumValues']>) {\n\t\tsuper(name, 'string', 'MySqlText');\n\t\tthis.config.textType = textType;\n\t\tthis.config.enumValues = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlText<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlText<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class MySqlText<T extends ColumnBaseConfig<'string', 'MySqlText'>>\n\textends MySqlColumn<T, { textType: MySqlTextColumnType; enumValues: T['enumValues'] }>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlText';\n\n\treadonly textType: MySqlTextColumnType = this.config.textType;\n\n\toverride readonly enumValues = this.config.enumValues;\n\n\tgetSQLType(): string {\n\t\treturn this.textType;\n\t}\n}\n\nexport interface MySqlTextConfig<\n\tTEnum extends readonly string[] | string[] | undefined = readonly string[] | string[] | undefined,\n> {\n\tenum?: TEnum;\n}\n\nexport function text(): MySqlTextBuilderInitial<'', [string, ...string[]]>;\nexport function text<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tconfig?: MySqlTextConfig<T | Writable<T>>,\n): MySqlTextBuilderInitial<'', Writable<T>>;\nexport function text<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig?: MySqlTextConfig<T | Writable<T>>,\n): MySqlTextBuilderInitial<TName, Writable<T>>;\nexport function text(a?: string | MySqlTextConfig, b: MySqlTextConfig = {}): any {\n\tconst { name, config } = getColumnNameAndConfig<MySqlTextConfig>(a, b);\n\treturn new MySqlTextBuilder(name, 'text', config as any);\n}\n\nexport function tinytext(): MySqlTextBuilderInitial<'', [string, ...string[]]>;\nexport function tinytext<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tconfig?: MySqlTextConfig<T | Writable<T>>,\n): MySqlTextBuilderInitial<'', Writable<T>>;\nexport function tinytext<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig?: MySqlTextConfig<T | Writable<T>>,\n): MySqlTextBuilderInitial<TName, Writable<T>>;\nexport function tinytext(a?: string | MySqlTextConfig, b: MySqlTextConfig = {}): any {\n\tconst { name, config } = getColumnNameAndConfig<MySqlTextConfig>(a, b);\n\treturn new MySqlTextBuilder(name, 'tinytext', config as any);\n}\n\nexport function mediumtext(): MySqlTextBuilderInitial<'', [string, ...string[]]>;\nexport function mediumtext<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tconfig?: MySqlTextConfig<T | Writable<T>>,\n): MySqlTextBuilderInitial<'', Writable<T>>;\nexport function mediumtext<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig?: MySqlTextConfig<T | Writable<T>>,\n): MySqlTextBuilderInitial<TName, Writable<T>>;\nexport function mediumtext(a?: string | MySqlTextConfig, b: MySqlTextConfig = {}): any {\n\tconst { name, config } = getColumnNameAndConfig<MySqlTextConfig>(a, b);\n\treturn new MySqlTextBuilder(name, 'mediumtext', config as any);\n}\n\nexport function longtext(): MySqlTextBuilderInitial<'', [string, ...string[]]>;\nexport function longtext<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tconfig?: MySqlTextConfig<T | Writable<T>>,\n): MySqlTextBuilderInitial<'', Writable<T>>;\nexport function longtext<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig?: MySqlTextConfig<T | Writable<T>>,\n): MySqlTextBuilderInitial<TName, Writable<T>>;\nexport function longtext(a?: string | MySqlTextConfig, b: MySqlTextConfig = {}): any {\n\tconst { name, config } = getColumnNameAndConfig<MySqlTextConfig>(a, b);\n\treturn new MySqlTextBuilder(name, 'longtext', config as any);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAsD;AACtD,oBAAgD;AAazC,MAAM,yBAAmF,iCAG9F;AAAA,EACD,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,UAA+B,QAA0C;AACrG,UAAM,MAAM,UAAU,WAAW;AACjC,SAAK,OAAO,WAAW;AACvB,SAAK,OAAO,aAAa,OAAO;AAAA,EACjC;AAAA;AAAA,EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI,UAA2C,OAAO,KAAK,MAA8C;AAAA,EACjH;AACD;AAEO,MAAM,kBACJ,0BACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,WAAgC,KAAK,OAAO;AAAA,EAEnC,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO,KAAK;AAAA,EACb;AACD;AAgBO,SAAS,KAAK,GAA8B,IAAqB,CAAC,GAAQ;AAChF,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAwC,GAAG,CAAC;AACrE,SAAO,IAAI,iBAAiB,MAAM,QAAQ,MAAa;AACxD;AAUO,SAAS,SAAS,GAA8B,IAAqB,CAAC,GAAQ;AACpF,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAwC,GAAG,CAAC;AACrE,SAAO,IAAI,iBAAiB,MAAM,YAAY,MAAa;AAC5D;AAUO,SAAS,WAAW,GAA8B,IAAqB,CAAC,GAAQ;AACtF,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAwC,GAAG,CAAC;AACrE,SAAO,IAAI,iBAAiB,MAAM,cAAc,MAAa;AAC9D;AAUO,SAAS,SAAS,GAA8B,IAAqB,CAAC,GAAQ;AACpF,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAwC,GAAG,CAAC;AACrE,SAAO,IAAI,iBAAiB,MAAM,YAAY,MAAa;AAC5D;", "names": []}