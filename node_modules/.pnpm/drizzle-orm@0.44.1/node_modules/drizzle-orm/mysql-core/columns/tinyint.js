import { entityKind } from "../../entity.js";
import { getColumnNameAndConfig } from "../../utils.js";
import { MySqlColumnBuilderWithAutoIncrement, MySqlColumnWithAutoIncrement } from "./common.js";
class MySqlTinyIntBuilder extends MySqlColumnBuilderWithAutoIncrement {
  static [entityKind] = "MySqlTinyIntBuilder";
  constructor(name, config) {
    super(name, "number", "MySqlTinyInt");
    this.config.unsigned = config ? config.unsigned : false;
  }
  /** @internal */
  build(table) {
    return new MySqlTinyInt(
      table,
      this.config
    );
  }
}
class MySqlTinyInt extends MySqlColumnWithAutoIncrement {
  static [entityKind] = "MySqlTinyInt";
  getSQLType() {
    return `tinyint${this.config.unsigned ? " unsigned" : ""}`;
  }
  mapFromDriverValue(value) {
    if (typeof value === "string") {
      return Number(value);
    }
    return value;
  }
}
function tinyint(a, b) {
  const { name, config } = getColumnNameAndConfig(a, b);
  return new MySqlTinyIntBuilder(name, config);
}
export {
  MySqlTinyInt,
  MySqlTinyIntBuilder,
  tinyint
};
//# sourceMappingURL=tinyint.js.map