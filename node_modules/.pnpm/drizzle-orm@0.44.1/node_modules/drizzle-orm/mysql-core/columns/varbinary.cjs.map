{"version": 3, "sources": ["../../../src/mysql-core/columns/varbinary.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlVarBinaryBuilderInitial<TName extends string> = MySqlVarBinaryBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'MySqlVarBinary';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlVarBinaryBuilder<T extends ColumnBuilderBaseConfig<'string', 'MySqlVarBinary'>>\n\textends MySqlColumnBuilder<T, MySqlVarbinaryOptions>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlVarBinaryBuilder';\n\n\t/** @internal */\n\tconstructor(name: T['name'], config: MySqlVarbinaryOptions) {\n\t\tsuper(name, 'string', 'MySqlVarBinary');\n\t\tthis.config.length = config?.length;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlVarBinary<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlVarBinary<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlVarBinary<\n\tT extends ColumnBaseConfig<'string', 'MySqlVarBinary'>,\n> extends MySqlColumn<T, MySqlVarbinaryOptions> {\n\tstatic override readonly [entityKind]: string = 'MySqlVarBinary';\n\n\tlength: number | undefined = this.config.length;\n\n\toverride mapFromDriverValue(value: string | Buffer | Uint8Array): string {\n\t\tif (typeof value === 'string') return value;\n\t\tif (Buffer.isBuffer(value)) return value.toString();\n\n\t\tconst str: string[] = [];\n\t\tfor (const v of value) {\n\t\t\tstr.push(v === 49 ? '1' : '0');\n\t\t}\n\n\t\treturn str.join('');\n\t}\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `varbinary` : `varbinary(${this.length})`;\n\t}\n}\n\nexport interface MySqlVarbinaryOptions {\n\tlength: number;\n}\n\nexport function varbinary(\n\tconfig: MySqlVarbinaryOptions,\n): MySqlVarBinaryBuilderInitial<''>;\nexport function varbinary<TName extends string>(\n\tname: TName,\n\tconfig: MySqlVarbinaryOptions,\n): MySqlVarBinaryBuilderInitial<TName>;\nexport function varbinary(a?: string | MySqlVarbinaryOptions, b?: MySqlVarbinaryOptions) {\n\tconst { name, config } = getColumnNameAndConfig<MySqlVarbinaryOptions>(a, b);\n\treturn new MySqlVarBinaryBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAuC;AACvC,oBAAgD;AAWzC,MAAM,8BACJ,iCACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA;AAAA,EAGhD,YAAY,MAAiB,QAA+B;AAC3D,UAAM,MAAM,UAAU,gBAAgB;AACtC,SAAK,OAAO,SAAS,QAAQ;AAAA,EAC9B;AAAA;AAAA,EAGS,MACR,OACkD;AAClD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,uBAEH,0BAAsC;AAAA,EAC/C,QAA0B,wBAAU,IAAY;AAAA,EAEhD,SAA6B,KAAK,OAAO;AAAA,EAEhC,mBAAmB,OAA6C;AACxE,QAAI,OAAO,UAAU,SAAU,QAAO;AACtC,QAAI,OAAO,SAAS,KAAK,EAAG,QAAO,MAAM,SAAS;AAElD,UAAM,MAAgB,CAAC;AACvB,eAAW,KAAK,OAAO;AACtB,UAAI,KAAK,MAAM,KAAK,MAAM,GAAG;AAAA,IAC9B;AAEA,WAAO,IAAI,KAAK,EAAE;AAAA,EACnB;AAAA,EAEA,aAAqB;AACpB,WAAO,KAAK,WAAW,SAAY,cAAc,aAAa,KAAK,MAAM;AAAA,EAC1E;AACD;AAaO,SAAS,UAAU,GAAoC,GAA2B;AACxF,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA8C,GAAG,CAAC;AAC3E,SAAO,IAAI,sBAAsB,MAAM,MAAM;AAC9C;", "names": []}