{"version": 3, "sources": ["../../src/mysql-core/view.ts"], "sourcesContent": ["import type { BuildColumns } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { AddAliasToSelection } from '~/query-builders/select.types.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection, SQL } from '~/sql/sql.ts';\nimport { getTableColumns } from '~/utils.ts';\nimport type { MySqlColumn, MySqlColumnBuilderBase } from './columns/index.ts';\nimport { QueryBuilder } from './query-builders/query-builder.ts';\nimport { mysqlTable } from './table.ts';\nimport { MySqlViewBase } from './view-base.ts';\nimport { MySqlViewConfig } from './view-common.ts';\n\nexport interface ViewBuilderConfig {\n\talgorithm?: 'undefined' | 'merge' | 'temptable';\n\tsqlSecurity?: 'definer' | 'invoker';\n\twithCheckOption?: 'cascaded' | 'local';\n}\n\nexport class ViewBuilderCore<TConfig extends { name: string; columns?: unknown }> {\n\tstatic readonly [entityKind]: string = 'MySqlViewBuilder';\n\n\tdeclare readonly _: {\n\t\treadonly name: TConfig['name'];\n\t\treadonly columns: TConfig['columns'];\n\t};\n\n\tconstructor(\n\t\tprotected name: TConfig['name'],\n\t\tprotected schema: string | undefined,\n\t) {}\n\n\tprotected config: ViewBuilderConfig = {};\n\n\talgorithm(\n\t\talgorithm: Exclude<ViewBuilderConfig['algorithm'], undefined>,\n\t): this {\n\t\tthis.config.algorithm = algorithm;\n\t\treturn this;\n\t}\n\n\tsqlSecurity(\n\t\tsqlSecurity: Exclude<ViewBuilderConfig['sqlSecurity'], undefined>,\n\t): this {\n\t\tthis.config.sqlSecurity = sqlSecurity;\n\t\treturn this;\n\t}\n\n\twithCheckOption(\n\t\twithCheckOption?: Exclude<ViewBuilderConfig['withCheckOption'], undefined>,\n\t): this {\n\t\tthis.config.withCheckOption = withCheckOption ?? 'cascaded';\n\t\treturn this;\n\t}\n}\n\nexport class ViewBuilder<TName extends string = string> extends ViewBuilderCore<{ name: TName }> {\n\tstatic override readonly [entityKind]: string = 'MySqlViewBuilder';\n\n\tas<TSelectedFields extends ColumnsSelection>(\n\t\tqb: TypedQueryBuilder<TSelectedFields> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelectedFields>),\n\t): MySqlViewWithSelection<TName, false, AddAliasToSelection<TSelectedFields, TName, 'mysql'>> {\n\t\tif (typeof qb === 'function') {\n\t\t\tqb = qb(new QueryBuilder());\n\t\t}\n\t\tconst selectionProxy = new SelectionProxyHandler<TSelectedFields>({\n\t\t\talias: this.name,\n\t\t\tsqlBehavior: 'error',\n\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\treplaceOriginalName: true,\n\t\t});\n\t\tconst aliasedSelection = new Proxy(qb.getSelectedFields(), selectionProxy);\n\t\treturn new Proxy(\n\t\t\tnew MySqlView({\n\t\t\t\tmysqlConfig: this.config,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: aliasedSelection,\n\t\t\t\t\tquery: qb.getSQL().inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tselectionProxy as any,\n\t\t) as MySqlViewWithSelection<TName, false, AddAliasToSelection<TSelectedFields, TName, 'mysql'>>;\n\t}\n}\n\nexport class ManualViewBuilder<\n\tTName extends string = string,\n\tTColumns extends Record<string, MySqlColumnBuilderBase> = Record<string, MySqlColumnBuilderBase>,\n> extends ViewBuilderCore<{ name: TName; columns: TColumns }> {\n\tstatic override readonly [entityKind]: string = 'MySqlManualViewBuilder';\n\n\tprivate columns: Record<string, MySqlColumn>;\n\n\tconstructor(\n\t\tname: TName,\n\t\tcolumns: TColumns,\n\t\tschema: string | undefined,\n\t) {\n\t\tsuper(name, schema);\n\t\tthis.columns = getTableColumns(mysqlTable(name, columns)) as BuildColumns<TName, TColumns, 'mysql'>;\n\t}\n\n\texisting(): MySqlViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'mysql'>> {\n\t\treturn new Proxy(\n\t\t\tnew MySqlView({\n\t\t\t\tmysqlConfig: undefined,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: undefined,\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as MySqlViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'mysql'>>;\n\t}\n\n\tas(query: SQL): MySqlViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'mysql'>> {\n\t\treturn new Proxy(\n\t\t\tnew MySqlView({\n\t\t\t\tmysqlConfig: this.config,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: query.inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as MySqlViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'mysql'>>;\n\t}\n}\n\nexport class MySqlView<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> extends MySqlViewBase<TName, TExisting, TSelectedFields> {\n\tstatic override readonly [entityKind]: string = 'MySqlView';\n\n\tdeclare protected $MySqlViewBrand: 'MySqlView';\n\n\t[MySqlViewConfig]: ViewBuilderConfig | undefined;\n\n\tconstructor({ mysqlConfig, config }: {\n\t\tmysqlConfig: ViewBuilderConfig | undefined;\n\t\tconfig: {\n\t\t\tname: TName;\n\t\t\tschema: string | undefined;\n\t\t\tselectedFields: ColumnsSelection;\n\t\t\tquery: SQL | undefined;\n\t\t};\n\t}) {\n\t\tsuper(config);\n\t\tthis[MySqlViewConfig] = mysqlConfig;\n\t}\n}\n\nexport type MySqlViewWithSelection<\n\tTName extends string,\n\tTExisting extends boolean,\n\tTSelectedFields extends ColumnsSelection,\n> = MySqlView<TName, TExisting, TSelectedFields> & TSelectedFields;\n\n/** @internal */\nexport function mysqlViewWithSchema(\n\tname: string,\n\tselection: Record<string, MySqlColumnBuilderBase> | undefined,\n\tschema: string | undefined,\n): ViewBuilder | ManualViewBuilder {\n\tif (selection) {\n\t\treturn new ManualViewBuilder(name, selection, schema);\n\t}\n\treturn new ViewBuilder(name, schema);\n}\n\nexport function mysqlView<TName extends string>(name: TName): ViewBuilder<TName>;\nexport function mysqlView<TName extends string, TColumns extends Record<string, MySqlColumnBuilderBase>>(\n\tname: TName,\n\tcolumns: TColumns,\n): ManualViewBuilder<TName, TColumns>;\nexport function mysqlView(\n\tname: string,\n\tselection?: Record<string, MySqlColumnBuilderBase>,\n): ViewBuilder | ManualViewBuilder {\n\treturn mysqlViewWithSchema(name, selection, undefined);\n}\n"], "mappings": "AACA,SAAS,kBAAkB;AAG3B,SAAS,6BAA6B;AAEtC,SAAS,uBAAuB;AAEhC,SAAS,oBAAoB;AAC7B,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAC9B,SAAS,uBAAuB;AAQzB,MAAM,gBAAqE;AAAA,EAQjF,YACW,MACA,QACT;AAFS;AACA;AAAA,EACR;AAAA,EAVH,QAAiB,UAAU,IAAY;AAAA,EAY7B,SAA4B,CAAC;AAAA,EAEvC,UACC,WACO;AACP,SAAK,OAAO,YAAY;AACxB,WAAO;AAAA,EACR;AAAA,EAEA,YACC,aACO;AACP,SAAK,OAAO,cAAc;AAC1B,WAAO;AAAA,EACR;AAAA,EAEA,gBACC,iBACO;AACP,SAAK,OAAO,kBAAkB,mBAAmB;AACjD,WAAO;AAAA,EACR;AACD;AAEO,MAAM,oBAAmD,gBAAiC;AAAA,EAChG,QAA0B,UAAU,IAAY;AAAA,EAEhD,GACC,IAC6F;AAC7F,QAAI,OAAO,OAAO,YAAY;AAC7B,WAAK,GAAG,IAAI,aAAa,CAAC;AAAA,IAC3B;AACA,UAAM,iBAAiB,IAAI,sBAAuC;AAAA,MACjE,OAAO,KAAK;AAAA,MACZ,aAAa;AAAA,MACb,oBAAoB;AAAA,MACpB,qBAAqB;AAAA,IACtB,CAAC;AACD,UAAM,mBAAmB,IAAI,MAAM,GAAG,kBAAkB,GAAG,cAAc;AACzE,WAAO,IAAI;AAAA,MACV,IAAI,UAAU;AAAA,QACb,aAAa,KAAK;AAAA,QAClB,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB;AAAA,UAChB,OAAO,GAAG,OAAO,EAAE,aAAa;AAAA,QACjC;AAAA,MACD,CAAC;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,0BAGH,gBAAoD;AAAA,EAC7D,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EAER,YACC,MACA,SACA,QACC;AACD,UAAM,MAAM,MAAM;AAClB,SAAK,UAAU,gBAAgB,WAAW,MAAM,OAAO,CAAC;AAAA,EACzD;AAAA,EAEA,WAAwF;AACvF,WAAO,IAAI;AAAA,MACV,IAAI,UAAU;AAAA,QACb,aAAa;AAAA,QACb,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB,KAAK;AAAA,UACrB,OAAO;AAAA,QACR;AAAA,MACD,CAAC;AAAA,MACD,IAAI,sBAAsB;AAAA,QACzB,OAAO,KAAK;AAAA,QACZ,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,MACtB,CAAC;AAAA,IACF;AAAA,EACD;AAAA,EAEA,GAAG,OAA0F;AAC5F,WAAO,IAAI;AAAA,MACV,IAAI,UAAU;AAAA,QACb,aAAa,KAAK;AAAA,QAClB,QAAQ;AAAA,UACP,MAAM,KAAK;AAAA,UACX,QAAQ,KAAK;AAAA,UACb,gBAAgB,KAAK;AAAA,UACrB,OAAO,MAAM,aAAa;AAAA,QAC3B;AAAA,MACD,CAAC;AAAA,MACD,IAAI,sBAAsB;AAAA,QACzB,OAAO,KAAK;AAAA,QACZ,aAAa;AAAA,QACb,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,MACtB,CAAC;AAAA,IACF;AAAA,EACD;AACD;AAEO,MAAM,kBAIH,cAAiD;AAAA,EAC1D,QAA0B,UAAU,IAAY;AAAA,EAIhD,CAAC,eAAe;AAAA,EAEhB,YAAY,EAAE,aAAa,OAAO,GAQ/B;AACF,UAAM,MAAM;AACZ,SAAK,eAAe,IAAI;AAAA,EACzB;AACD;AASO,SAAS,oBACf,MACA,WACA,QACkC;AAClC,MAAI,WAAW;AACd,WAAO,IAAI,kBAAkB,MAAM,WAAW,MAAM;AAAA,EACrD;AACA,SAAO,IAAI,YAAY,MAAM,MAAM;AACpC;AAOO,SAAS,UACf,MACA,WACkC;AAClC,SAAO,oBAAoB,MAAM,WAAW,MAAS;AACtD;", "names": []}