{"version": 3, "sources": ["../../../src/pg-core/columns/text.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { getColumnNameAndConfig, type Writable } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgTextBuilderInitial<TName extends string, TEnum extends [string, ...string[]]> = PgTextBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgText';\n\tdata: TEnum[number];\n\tenumValues: TEnum;\n\tdriverParam: string;\n}>;\n\nexport class PgTextBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'PgText'>,\n> extends PgColumnBuilder<T, { enumValues: T['enumValues'] }> {\n\tstatic override readonly [entityKind]: string = 'PgTextBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t\tconfig: PgTextConfig<T['enumValues']>,\n\t) {\n\t\tsuper(name, 'string', 'PgText');\n\t\tthis.config.enumValues = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgText<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgText<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgText<T extends ColumnBaseConfig<'string', 'PgText'>>\n\textends PgColumn<T, { enumValues: T['enumValues'] }>\n{\n\tstatic override readonly [entityKind]: string = 'PgText';\n\n\toverride readonly enumValues = this.config.enumValues;\n\n\tgetSQLType(): string {\n\t\treturn 'text';\n\t}\n}\n\nexport interface PgTextConfig<\n\tTEnum extends readonly string[] | string[] | undefined = readonly string[] | string[] | undefined,\n> {\n\tenum?: TEnum;\n}\n\nexport function text(): PgTextBuilderInitial<'', [string, ...string[]]>;\nexport function text<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tconfig?: PgTextConfig<T | Writable<T>>,\n): PgTextBuilderInitial<'', Writable<T>>;\nexport function text<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig?: PgTextConfig<T | Writable<T>>,\n): PgTextBuilderInitial<TName, Writable<T>>;\nexport function text(a?: string | PgTextConfig, b: PgTextConfig = {}): any {\n\tconst { name, config } = getColumnNameAndConfig<PgTextConfig>(a, b);\n\treturn new PgTextBuilder(name, config as any);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,8BAA6C;AACtD,SAAS,UAAU,uBAAuB;AAWnC,MAAM,sBAEH,gBAAoD;AAAA,EAC7D,QAA0B,UAAU,IAAY;AAAA,EAEhD,YACC,MACA,QACC;AACD,UAAM,MAAM,UAAU,QAAQ;AAC9B,SAAK,OAAO,aAAa,OAAO;AAAA,EACjC;AAAA;AAAA,EAGS,MACR,OAC0C;AAC1C,WAAO,IAAI,OAAwC,OAAO,KAAK,MAA8C;AAAA,EAC9G;AACD;AAEO,MAAM,eACJ,SACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAE9B,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAgBO,SAAS,KAAK,GAA2B,IAAkB,CAAC,GAAQ;AAC1E,QAAM,EAAE,MAAM,OAAO,IAAI,uBAAqC,GAAG,CAAC;AAClE,SAAO,IAAI,cAAc,MAAM,MAAa;AAC7C;", "names": []}