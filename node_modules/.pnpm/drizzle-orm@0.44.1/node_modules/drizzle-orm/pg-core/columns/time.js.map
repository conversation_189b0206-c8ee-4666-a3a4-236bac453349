{"version": 3, "sources": ["../../../src/pg-core/columns/time.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn } from './common.ts';\nimport { PgDateColumnBaseBuilder } from './date.common.ts';\nimport type { Precision } from './timestamp.ts';\n\nexport type PgTimeBuilderInitial<TName extends string> = PgTimeBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgTime';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class PgTimeBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgTime'>> extends PgDateColumnBaseBuilder<\n\tT,\n\t{ withTimezone: boolean; precision: number | undefined }\n> {\n\tstatic override readonly [entityKind]: string = 'PgTimeBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t\treadonly withTimezone: boolean,\n\t\treadonly precision: number | undefined,\n\t) {\n\t\tsuper(name, 'string', 'PgTime');\n\t\tthis.config.withTimezone = withTimezone;\n\t\tthis.config.precision = precision;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgTime<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgTime<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgTime<T extends ColumnBaseConfig<'string', 'PgTime'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgTime';\n\n\treadonly withTimezone: boolean;\n\treadonly precision: number | undefined;\n\n\tconstructor(table: AnyPgTable<{ name: T['tableName'] }>, config: PgTimeBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t\tthis.withTimezone = config.withTimezone;\n\t\tthis.precision = config.precision;\n\t}\n\n\tgetSQLType(): string {\n\t\tconst precision = this.precision === undefined ? '' : `(${this.precision})`;\n\t\treturn `time${precision}${this.withTimezone ? ' with time zone' : ''}`;\n\t}\n}\n\nexport interface TimeConfig {\n\tprecision?: Precision;\n\twithTimezone?: boolean;\n}\n\nexport function time(): PgTimeBuilderInitial<''>;\nexport function time(config?: TimeConfig): PgTimeBuilderInitial<''>;\nexport function time<TName extends string>(name: TName, config?: TimeConfig): PgTimeBuilderInitial<TName>;\nexport function time(a?: string | TimeConfig, b: TimeConfig = {}) {\n\tconst { name, config } = getColumnNameAndConfig<TimeConfig>(a, b);\n\treturn new PgTimeBuilder(name, config.withTimezone ?? false, config.precision);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,8BAA8B;AACvC,SAAS,gBAAgB;AACzB,SAAS,+BAA+B;AAYjC,MAAM,sBAA6E,wBAGxF;AAAA,EAGD,YACC,MACS,cACA,WACR;AACD,UAAM,MAAM,UAAU,QAAQ;AAHrB;AACA;AAGT,SAAK,OAAO,eAAe;AAC3B,SAAK,OAAO,YAAY;AAAA,EACzB;AAAA,EAVA,QAA0B,UAAU,IAAY;AAAA;AAAA,EAavC,MACR,OAC0C;AAC1C,WAAO,IAAI,OAAwC,OAAO,KAAK,MAA8C;AAAA,EAC9G;AACD;AAEO,MAAM,eAA+D,SAAY;AAAA,EACvF,QAA0B,UAAU,IAAY;AAAA,EAEvC;AAAA,EACA;AAAA,EAET,YAAY,OAA6C,QAAoC;AAC5F,UAAM,OAAO,MAAM;AACnB,SAAK,eAAe,OAAO;AAC3B,SAAK,YAAY,OAAO;AAAA,EACzB;AAAA,EAEA,aAAqB;AACpB,UAAM,YAAY,KAAK,cAAc,SAAY,KAAK,IAAI,KAAK,SAAS;AACxE,WAAO,OAAO,SAAS,GAAG,KAAK,eAAe,oBAAoB,EAAE;AAAA,EACrE;AACD;AAUO,SAAS,KAAK,GAAyB,IAAgB,CAAC,GAAG;AACjE,QAAM,EAAE,MAAM,OAAO,IAAI,uBAAmC,GAAG,CAAC;AAChE,SAAO,IAAI,cAAc,MAAM,OAAO,gBAAgB,OAAO,OAAO,SAAS;AAC9E;", "names": []}