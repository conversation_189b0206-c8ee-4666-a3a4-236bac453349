{"version": 3, "sources": ["../../../src/aws-data-api/pg/session.ts"], "sourcesContent": ["import type { ColumnMetadata, ExecuteStatementCommandOutput, Field, RDSDataClient } from '@aws-sdk/client-rds-data';\nimport {\n\tBeginTransactionCommand,\n\tCommitTransactionCommand,\n\tExecuteStatementCommand,\n\tRollbackTransactionCommand,\n} from '@aws-sdk/client-rds-data';\nimport type { Cache } from '~/cache/core/cache.ts';\nimport { NoopCache } from '~/cache/core/cache.ts';\nimport type { WithCacheConfig } from '~/cache/core/types.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport {\n\ttype PgDialect,\n\tPgPreparedQuery,\n\ttype PgQueryResultHKT,\n\tPgSession,\n\tPgTransaction,\n\ttype PgTransactionConfig,\n\ttype PreparedQueryConfig,\n} from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type QueryTypingsValue, type QueryWithTypings, type SQL, sql } from '~/sql/sql.ts';\nimport { mapResultRow } from '~/utils.ts';\nimport { getValueFromDataApi, toValueParam } from '../common/index.ts';\n\nexport type AwsDataApiClient = RDSDataClient;\n\nexport class AwsDataApiPreparedQuery<\n\tT extends PreparedQueryConfig & { values: AwsDataApiPgQueryResult<unknown[]> },\n> extends PgPreparedQuery<T> {\n\tstatic override readonly [entityKind]: string = 'AwsDataApiPreparedQuery';\n\n\tprivate rawQuery: ExecuteStatementCommand;\n\n\tconstructor(\n\t\tprivate client: AwsDataApiClient,\n\t\tprivate queryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate typings: QueryTypingsValue[],\n\t\tprivate options: AwsDataApiSessionOptions,\n\t\tcache: Cache,\n\t\tqueryMetadata: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t} | undefined,\n\t\tcacheConfig: WithCacheConfig | undefined,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\t/** @internal */\n\t\treadonly transactionId: string | undefined,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper({ sql: queryString, params }, cache, queryMetadata, cacheConfig);\n\t\tthis.rawQuery = new ExecuteStatementCommand({\n\t\t\tsql: queryString,\n\t\t\tparameters: [],\n\t\t\tsecretArn: options.secretArn,\n\t\t\tresourceArn: options.resourceArn,\n\t\t\tdatabase: options.database,\n\t\t\ttransactionId,\n\t\t\tincludeResultMetadata: !fields && !customResultMapper,\n\t\t});\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\tconst { fields, joinsNotNullableMap, customResultMapper } = this;\n\n\t\tconst result = await this.values(placeholderValues);\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst { columnMetadata, rows } = result;\n\t\t\tif (!columnMetadata) {\n\t\t\t\treturn result;\n\t\t\t}\n\t\t\tconst mappedRows = rows.map((sourceRow) => {\n\t\t\t\tconst row: Record<string, unknown> = {};\n\t\t\t\tfor (const [index, value] of sourceRow.entries()) {\n\t\t\t\t\tconst metadata = columnMetadata[index];\n\t\t\t\t\tif (!metadata) {\n\t\t\t\t\t\tthrow new Error(\n\t\t\t\t\t\t\t`Unexpected state: no column metadata found for index ${index}. Please report this issue on GitHub: https://github.com/drizzle-team/drizzle-orm/issues/new/choose`,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\tif (!metadata.name) {\n\t\t\t\t\t\tthrow new Error(\n\t\t\t\t\t\t\t`Unexpected state: no column name for index ${index} found in the column metadata. Please report this issue on GitHub: https://github.com/drizzle-team/drizzle-orm/issues/new/choose`,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\trow[metadata.name] = value;\n\t\t\t\t}\n\t\t\t\treturn row;\n\t\t\t});\n\t\t\treturn Object.assign(result, { rows: mappedRows });\n\t\t}\n\n\t\treturn customResultMapper\n\t\t\t? customResultMapper(result.rows!)\n\t\t\t: result.rows!.map((row) => mapResultRow(fields!, row, joinsNotNullableMap));\n\t}\n\n\tasync all(placeholderValues?: Record<string, unknown> | undefined): Promise<T['all']> {\n\t\tconst result = await this.execute(placeholderValues);\n\t\tif (!this.fields && !this.customResultMapper) {\n\t\t\treturn (result as AwsDataApiPgQueryResult<unknown>).rows;\n\t\t}\n\t\treturn result;\n\t}\n\n\tasync values(placeholderValues: Record<string, unknown> = {}): Promise<T['values']> {\n\t\tconst params = fillPlaceholders(this.params, placeholderValues ?? {});\n\n\t\tthis.rawQuery.input.parameters = params.map((param, index) => ({\n\t\t\tname: `${index + 1}`,\n\t\t\t...toValueParam(param, this.typings[index]),\n\t\t}));\n\n\t\tthis.options.logger?.logQuery(this.rawQuery.input.sql!, this.rawQuery.input.parameters);\n\n\t\tconst result = await this.queryWithCache(this.queryString, params, async () => {\n\t\t\treturn await this.client.send(this.rawQuery);\n\t\t});\n\t\tconst rows = result.records?.map((row) => {\n\t\t\treturn row.map((field) => getValueFromDataApi(field));\n\t\t}) ?? [];\n\n\t\treturn {\n\t\t\t...result,\n\t\t\trows,\n\t\t};\n\t}\n\n\t/** @internal */\n\tmapResultRows(records: Field[][], columnMetadata: ColumnMetadata[]) {\n\t\treturn records.map((record) => {\n\t\t\tconst row: Record<string, unknown> = {};\n\t\t\tfor (const [index, field] of record.entries()) {\n\t\t\t\tconst { name } = columnMetadata[index]!;\n\t\t\t\trow[name ?? index] = getValueFromDataApi(field); // not what to default if name is undefined\n\t\t\t}\n\t\t\treturn row;\n\t\t});\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nexport interface AwsDataApiSessionOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n\tdatabase: string;\n\tresourceArn: string;\n\tsecretArn: string;\n}\n\ninterface AwsDataApiQueryBase {\n\tresourceArn: string;\n\tsecretArn: string;\n\tdatabase: string;\n}\n\nexport class AwsDataApiSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgSession<AwsDataApiPgQueryResultHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'AwsDataApiSession';\n\n\t/** @internal */\n\treadonly rawQuery: AwsDataApiQueryBase;\n\tprivate cache: Cache;\n\n\tconstructor(\n\t\t/** @internal */\n\t\treadonly client: AwsDataApiClient,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: AwsDataApiSessionOptions,\n\t\t/** @internal */\n\t\treadonly transactionId: string | undefined,\n\t) {\n\t\tsuper(dialect);\n\t\tthis.rawQuery = {\n\t\t\tsecretArn: options.secretArn,\n\t\t\tresourceArn: options.resourceArn,\n\t\t\tdatabase: options.database,\n\t\t};\n\t\tthis.cache = options.cache ?? new NoopCache();\n\t}\n\n\tprepareQuery<\n\t\tT extends PreparedQueryConfig & {\n\t\t\tvalues: AwsDataApiPgQueryResult<unknown[]>;\n\t\t} = PreparedQueryConfig & {\n\t\t\tvalues: AwsDataApiPgQueryResult<unknown[]>;\n\t\t},\n\t>(\n\t\tquery: QueryWithTypings,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\tqueryMetadata?: { type: 'select' | 'update' | 'delete' | 'insert'; tables: string[] },\n\t\tcacheConfig?: WithCacheConfig,\n\t\ttransactionId?: string,\n\t): AwsDataApiPreparedQuery<T> {\n\t\treturn new AwsDataApiPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tquery.typings ?? [],\n\t\t\tthis.options,\n\t\t\tthis.cache,\n\t\t\tqueryMetadata,\n\t\t\tcacheConfig,\n\t\t\tfields,\n\t\t\ttransactionId ?? this.transactionId,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\toverride execute<T>(query: SQL): Promise<T> {\n\t\treturn this.prepareQuery<PreparedQueryConfig & { execute: T; values: AwsDataApiPgQueryResult<unknown[]> }>(\n\t\t\tthis.dialect.sqlToQuery(query),\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\tfalse,\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\tthis.transactionId,\n\t\t).execute();\n\t}\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: AwsDataApiTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig | undefined,\n\t): Promise<T> {\n\t\tconst { transactionId } = await this.client.send(new BeginTransactionCommand(this.rawQuery));\n\t\tconst session = new AwsDataApiSession(this.client, this.dialect, this.schema, this.options, transactionId);\n\t\tconst tx = new AwsDataApiTransaction<TFullSchema, TSchema>(this.dialect, session, this.schema);\n\t\tif (config) {\n\t\t\tawait tx.setTransaction(config);\n\t\t}\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait this.client.send(new CommitTransactionCommand({ ...this.rawQuery, transactionId }));\n\t\t\treturn result;\n\t\t} catch (e) {\n\t\t\tawait this.client.send(new RollbackTransactionCommand({ ...this.rawQuery, transactionId }));\n\t\t\tthrow e;\n\t\t}\n\t}\n}\n\nexport class AwsDataApiTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgTransaction<AwsDataApiPgQueryResultHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'AwsDataApiTransaction';\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: AwsDataApiTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\tconst savepointName = `sp${this.nestedIndex + 1}`;\n\t\tconst tx = new AwsDataApiTransaction<TFullSchema, TSchema>(\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tthis.schema,\n\t\t\tthis.nestedIndex + 1,\n\t\t);\n\t\tawait this.session.execute(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = await transaction(tx);\n\t\t\tawait this.session.execute(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (e) {\n\t\t\tawait this.session.execute(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow e;\n\t\t}\n\t}\n}\n\nexport type AwsDataApiPgQueryResult<T> = ExecuteStatementCommandOutput & { rows: T[] };\n\nexport interface AwsDataApiPgQueryResultHKT extends PgQueryResultHKT {\n\ttype: AwsDataApiPgQueryResult<any>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,6BAKO;AAEP,mBAA0B;AAE1B,oBAA2B;AAE3B,qBAQO;AAGP,iBAA+F;AAC/F,mBAA6B;AAC7B,oBAAkD;AAI3C,MAAM,gCAEH,+BAAmB;AAAA,EAK5B,YACS,QACA,aACA,QACA,SACA,SACR,OACA,eAIA,aACQ,QAEC,eACD,wBACA,oBACP;AACD,UAAM,EAAE,KAAK,aAAa,OAAO,GAAG,OAAO,eAAe,WAAW;AAjB7D;AACA;AACA;AACA;AACA;AAOA;AAEC;AACD;AACA;AAGR,SAAK,WAAW,IAAI,+CAAwB;AAAA,MAC3C,KAAK;AAAA,MACL,YAAY,CAAC;AAAA,MACb,WAAW,QAAQ;AAAA,MACnB,aAAa,QAAQ;AAAA,MACrB,UAAU,QAAQ;AAAA,MAClB;AAAA,MACA,uBAAuB,CAAC,UAAU,CAAC;AAAA,IACpC,CAAC;AAAA,EACF;AAAA,EAhCA,QAA0B,wBAAU,IAAY;AAAA,EAExC;AAAA,EAgCR,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,UAAM,EAAE,QAAQ,qBAAqB,mBAAmB,IAAI;AAE5D,UAAM,SAAS,MAAM,KAAK,OAAO,iBAAiB;AAClD,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAM,EAAE,gBAAgB,KAAK,IAAI;AACjC,UAAI,CAAC,gBAAgB;AACpB,eAAO;AAAA,MACR;AACA,YAAM,aAAa,KAAK,IAAI,CAAC,cAAc;AAC1C,cAAM,MAA+B,CAAC;AACtC,mBAAW,CAAC,OAAO,KAAK,KAAK,UAAU,QAAQ,GAAG;AACjD,gBAAM,WAAW,eAAe,KAAK;AACrC,cAAI,CAAC,UAAU;AACd,kBAAM,IAAI;AAAA,cACT,wDAAwD,KAAK;AAAA,YAC9D;AAAA,UACD;AACA,cAAI,CAAC,SAAS,MAAM;AACnB,kBAAM,IAAI;AAAA,cACT,8CAA8C,KAAK;AAAA,YACpD;AAAA,UACD;AACA,cAAI,SAAS,IAAI,IAAI;AAAA,QACtB;AACA,eAAO;AAAA,MACR,CAAC;AACD,aAAO,OAAO,OAAO,QAAQ,EAAE,MAAM,WAAW,CAAC;AAAA,IAClD;AAEA,WAAO,qBACJ,mBAAmB,OAAO,IAAK,IAC/B,OAAO,KAAM,IAAI,CAAC,YAAQ,2BAAa,QAAS,KAAK,mBAAmB,CAAC;AAAA,EAC7E;AAAA,EAEA,MAAM,IAAI,mBAA4E;AACrF,UAAM,SAAS,MAAM,KAAK,QAAQ,iBAAiB;AACnD,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,oBAAoB;AAC7C,aAAQ,OAA4C;AAAA,IACrD;AACA,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,OAAO,oBAA6C,CAAC,GAAyB;AACnF,UAAM,aAAS,6BAAiB,KAAK,QAAQ,qBAAqB,CAAC,CAAC;AAEpE,SAAK,SAAS,MAAM,aAAa,OAAO,IAAI,CAAC,OAAO,WAAW;AAAA,MAC9D,MAAM,GAAG,QAAQ,CAAC;AAAA,MAClB,OAAG,4BAAa,OAAO,KAAK,QAAQ,KAAK,CAAC;AAAA,IAC3C,EAAE;AAEF,SAAK,QAAQ,QAAQ,SAAS,KAAK,SAAS,MAAM,KAAM,KAAK,SAAS,MAAM,UAAU;AAEtF,UAAM,SAAS,MAAM,KAAK,eAAe,KAAK,aAAa,QAAQ,YAAY;AAC9E,aAAO,MAAM,KAAK,OAAO,KAAK,KAAK,QAAQ;AAAA,IAC5C,CAAC;AACD,UAAM,OAAO,OAAO,SAAS,IAAI,CAAC,QAAQ;AACzC,aAAO,IAAI,IAAI,CAAC,cAAU,mCAAoB,KAAK,CAAC;AAAA,IACrD,CAAC,KAAK,CAAC;AAEP,WAAO;AAAA,MACN,GAAG;AAAA,MACH;AAAA,IACD;AAAA,EACD;AAAA;AAAA,EAGA,cAAc,SAAoB,gBAAkC;AACnE,WAAO,QAAQ,IAAI,CAAC,WAAW;AAC9B,YAAM,MAA+B,CAAC;AACtC,iBAAW,CAAC,OAAO,KAAK,KAAK,OAAO,QAAQ,GAAG;AAC9C,cAAM,EAAE,KAAK,IAAI,eAAe,KAAK;AACrC,YAAI,QAAQ,KAAK,QAAI,mCAAoB,KAAK;AAAA,MAC/C;AACA,aAAO;AAAA,IACR,CAAC;AAAA,EACF;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;AAgBO,MAAM,0BAGH,yBAA4D;AAAA,EAOrE,YAEU,QACT,SACQ,QACA,SAEC,eACR;AACD,UAAM,OAAO;AAPJ;AAED;AACA;AAEC;AAGT,SAAK,WAAW;AAAA,MACf,WAAW,QAAQ;AAAA,MACnB,aAAa,QAAQ;AAAA,MACrB,UAAU,QAAQ;AAAA,IACnB;AACA,SAAK,QAAQ,QAAQ,SAAS,IAAI,uBAAU;AAAA,EAC7C;AAAA,EAtBA,QAA0B,wBAAU,IAAY;AAAA;AAAA,EAGvC;AAAA,EACD;AAAA,EAoBR,aAOC,OACA,QACA,MACA,uBACA,oBACA,eACA,aACA,eAC6B;AAC7B,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM,WAAW,CAAC;AAAA,MAClB,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB,KAAK;AAAA,MACtB;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAES,QAAW,OAAwB;AAC3C,WAAO,KAAK;AAAA,MACX,KAAK,QAAQ,WAAW,KAAK;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACN,EAAE,QAAQ;AAAA,EACX;AAAA,EAEA,MAAe,YACd,aACA,QACa;AACb,UAAM,EAAE,cAAc,IAAI,MAAM,KAAK,OAAO,KAAK,IAAI,+CAAwB,KAAK,QAAQ,CAAC;AAC3F,UAAM,UAAU,IAAI,kBAAkB,KAAK,QAAQ,KAAK,SAAS,KAAK,QAAQ,KAAK,SAAS,aAAa;AACzG,UAAM,KAAK,IAAI,sBAA4C,KAAK,SAAS,SAAS,KAAK,MAAM;AAC7F,QAAI,QAAQ;AACX,YAAM,GAAG,eAAe,MAAM;AAAA,IAC/B;AACA,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,KAAK,OAAO,KAAK,IAAI,gDAAyB,EAAE,GAAG,KAAK,UAAU,cAAc,CAAC,CAAC;AACxF,aAAO;AAAA,IACR,SAAS,GAAG;AACX,YAAM,KAAK,OAAO,KAAK,IAAI,kDAA2B,EAAE,GAAG,KAAK,UAAU,cAAc,CAAC,CAAC;AAC1F,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAEO,MAAM,8BAGH,6BAAgE;AAAA,EACzE,QAA0B,wBAAU,IAAY;AAAA,EAEhD,MAAe,YACd,aACa;AACb,UAAM,gBAAgB,KAAK,KAAK,cAAc,CAAC;AAC/C,UAAM,KAAK,IAAI;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,cAAc;AAAA,IACpB;AACA,UAAM,KAAK,QAAQ,QAAQ,eAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AAChE,QAAI;AACH,YAAM,SAAS,MAAM,YAAY,EAAE;AACnC,YAAM,KAAK,QAAQ,QAAQ,eAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AACxE,aAAO;AAAA,IACR,SAAS,GAAG;AACX,YAAM,KAAK,QAAQ,QAAQ,eAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AAC5E,YAAM;AAAA,IACP;AAAA,EACD;AACD;", "names": []}