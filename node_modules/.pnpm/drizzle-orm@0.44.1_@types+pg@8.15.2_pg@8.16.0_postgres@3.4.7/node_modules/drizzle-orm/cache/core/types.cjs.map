{"version": 3, "sources": ["../../../src/cache/core/types.ts"], "sourcesContent": ["export type CacheConfig = {\n\t/**\n\t * expire time, in seconds (a positive integer)\n\t */\n\tex?: number;\n\t/**\n\t * expire time, in milliseconds (a positive integer).\n\t */\n\tpx?: number;\n\t/**\n\t * Unix time at which the key will expire, in seconds (a positive integer).\n\t */\n\texat?: number;\n\t/**\n\t * Unix time at which the key will expire, in milliseconds (a positive integer)\n\t */\n\tpxat?: number;\n\t/**\n\t * Retain the time to live associated with the key.\n\t */\n\tkeepTtl?: boolean;\n\t/**\n\t * Set an expiration (TTL or time to live) on one or more fields of a given hash key.\n\t * Used for HEXPIRE command\n\t */\n\thexOptions?: 'NX' | 'nx' | 'XX' | 'xx' | 'GT' | 'gt' | 'LT' | 'lt';\n};\n\nexport type WithCacheConfig = { enable: boolean; config?: CacheConfig; tag?: string; autoInvalidate?: boolean };\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}