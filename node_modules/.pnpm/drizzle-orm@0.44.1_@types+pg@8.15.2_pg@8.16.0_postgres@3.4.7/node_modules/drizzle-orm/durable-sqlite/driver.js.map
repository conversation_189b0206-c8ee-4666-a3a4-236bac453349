{"version": 3, "sources": ["../../src/durable-sqlite/driver.ts"], "sourcesContent": ["/// <reference types=\"@cloudflare/workers-types\" />\nimport { entityKind } from '~/entity.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype ExtractTablesWithRelations,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { BaseSQLiteDatabase } from '~/sqlite-core/db.ts';\nimport { SQLiteSyncDialect } from '~/sqlite-core/dialect.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport { SQLiteDOSession } from './session.ts';\n\nexport class DrizzleSqliteDODatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends BaseSQLiteDatabase<'sync', SqlStorageCursor<Record<string, SqlStorageValue>>, TSchema> {\n\tstatic override readonly [entityKind]: string = 'DrizzleSqliteDODatabase';\n\n\t/** @internal */\n\tdeclare readonly session: SQLiteDOSession<TSchema, ExtractTablesWithRelations<TSchema>>;\n}\n\nexport function drizzle<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends DurableObjectStorage = DurableObjectStorage,\n>(\n\tclient: TClient,\n\tconfig: DrizzleConfig<TSchema> = {},\n): DrizzleSqliteDODatabase<TSchema> & {\n\t$client: TClient;\n} {\n\tconst dialect = new SQLiteSyncDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new SQLiteDOSession(client as DurableObjectStorage, dialect, schema, { logger });\n\tconst db = new DrizzleSqliteDODatabase('sync', dialect, session, schema) as DrizzleSqliteDODatabase<TSchema>;\n\t(<any> db).$client = client;\n\n\treturn db as any;\n}\n"], "mappings": "AACA,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAC9B;AAAA,EACC;AAAA,EACA;AAAA,OAIM;AACP,SAAS,0BAA0B;AACnC,SAAS,yBAAyB;AAElC,SAAS,uBAAuB;AAEzB,MAAM,gCAEH,mBAAuF;AAAA,EAChG,QAA0B,UAAU,IAAY;AAIjD;AAEO,SAAS,QAIf,QACA,SAAiC,CAAC,GAGjC;AACD,QAAM,UAAU,IAAI,kBAAkB,EAAE,QAAQ,OAAO,OAAO,CAAC;AAC/D,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,gBAAgB,QAAgC,SAAS,QAAQ,EAAE,OAAO,CAAC;AAC/F,QAAM,KAAK,IAAI,wBAAwB,QAAQ,SAAS,SAAS,MAAM;AACvE,EAAO,GAAI,UAAU;AAErB,SAAO;AACR;", "names": []}