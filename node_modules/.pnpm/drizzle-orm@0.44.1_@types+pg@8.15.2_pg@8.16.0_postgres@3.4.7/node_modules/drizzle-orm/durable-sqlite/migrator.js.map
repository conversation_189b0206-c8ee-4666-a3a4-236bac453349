{"version": 3, "sources": ["../../src/durable-sqlite/migrator.ts"], "sourcesContent": ["import type { MigrationMeta } from '~/migrator.ts';\nimport { sql } from '~/sql/index.ts';\nimport type { DrizzleSqliteDODatabase } from './driver.ts';\n\ninterface MigrationConfig {\n\tjournal: {\n\t\tentries: { idx: number; when: number; tag: string; breakpoints: boolean }[];\n\t};\n\tmigrations: Record<string, string>;\n}\n\nfunction readMigrationFiles({ journal, migrations }: MigrationConfig): MigrationMeta[] {\n\tconst migrationQueries: MigrationMeta[] = [];\n\n\tfor (const journalEntry of journal.entries) {\n\t\tconst query = migrations[`m${journalEntry.idx.toString().padStart(4, '0')}`];\n\n\t\tif (!query) {\n\t\t\tthrow new Error(`Missing migration: ${journalEntry.tag}`);\n\t\t}\n\n\t\ttry {\n\t\t\tconst result = query.split('--> statement-breakpoint').map((it) => {\n\t\t\t\treturn it;\n\t\t\t});\n\n\t\t\tmigrationQueries.push({\n\t\t\t\tsql: result,\n\t\t\t\tbps: journalEntry.breakpoints,\n\t\t\t\tfolderMillis: journalEntry.when,\n\t\t\t\thash: '',\n\t\t\t});\n\t\t} catch {\n\t\t\tthrow new Error(`Failed to parse migration: ${journalEntry.tag}`);\n\t\t}\n\t}\n\n\treturn migrationQueries;\n}\n\nexport async function migrate<\n\tTSchema extends Record<string, unknown>,\n>(\n\tdb: DrizzleSqliteDODatabase<TSchema>,\n\tconfig: MigrationConfig,\n): Promise<void> {\n\tconst migrations = readMigrationFiles(config);\n\n\tdb.transaction((tx) => {\n\t\ttry {\n\t\t\tconst migrationsTable = '__drizzle_migrations';\n\n\t\t\tconst migrationTableCreate = sql`\n\t\t\t\tCREATE TABLE IF NOT EXISTS ${sql.identifier(migrationsTable)} (\n\t\t\t\t\tid SERIAL PRIMARY KEY,\n\t\t\t\t\thash text NOT NULL,\n\t\t\t\t\tcreated_at numeric\n\t\t\t\t)\n\t\t\t`;\n\t\t\tdb.run(migrationTableCreate);\n\n\t\t\tconst dbMigrations = db.values<[number, string, string]>(\n\t\t\t\tsql`SELECT id, hash, created_at FROM ${sql.identifier(migrationsTable)} ORDER BY created_at DESC LIMIT 1`,\n\t\t\t);\n\n\t\t\tconst lastDbMigration = dbMigrations[0] ?? undefined;\n\n\t\t\tfor (const migration of migrations) {\n\t\t\t\tif (!lastDbMigration || Number(lastDbMigration[2])! < migration.folderMillis) {\n\t\t\t\t\tfor (const stmt of migration.sql) {\n\t\t\t\t\t\tdb.run(sql.raw(stmt));\n\t\t\t\t\t}\n\t\t\t\t\tdb.run(\n\t\t\t\t\t\tsql`INSERT INTO ${\n\t\t\t\t\t\t\tsql.identifier(migrationsTable)\n\t\t\t\t\t\t} (\"hash\", \"created_at\") VALUES(${migration.hash}, ${migration.folderMillis})`,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t} catch (error: any) {\n\t\t\ttx.rollback();\n\t\t\tthrow error;\n\t\t}\n\t});\n}\n"], "mappings": "AACA,SAAS,WAAW;AAUpB,SAAS,mBAAmB,EAAE,SAAS,WAAW,GAAqC;AACtF,QAAM,mBAAoC,CAAC;AAE3C,aAAW,gBAAgB,QAAQ,SAAS;AAC3C,UAAM,QAAQ,WAAW,IAAI,aAAa,IAAI,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE;AAE3E,QAAI,CAAC,OAAO;AACX,YAAM,IAAI,MAAM,sBAAsB,aAAa,GAAG,EAAE;AAAA,IACzD;AAEA,QAAI;AACH,YAAM,SAAS,MAAM,MAAM,0BAA0B,EAAE,IAAI,CAAC,OAAO;AAClE,eAAO;AAAA,MACR,CAAC;AAED,uBAAiB,KAAK;AAAA,QACrB,KAAK;AAAA,QACL,KAAK,aAAa;AAAA,QAClB,cAAc,aAAa;AAAA,QAC3B,MAAM;AAAA,MACP,CAAC;AAAA,IACF,QAAQ;AACP,YAAM,IAAI,MAAM,8BAA8B,aAAa,GAAG,EAAE;AAAA,IACjE;AAAA,EACD;AAEA,SAAO;AACR;AAEA,eAAsB,QAGrB,IACA,QACgB;AAChB,QAAM,aAAa,mBAAmB,MAAM;AAE5C,KAAG,YAAY,CAAC,OAAO;AACtB,QAAI;AACH,YAAM,kBAAkB;AAExB,YAAM,uBAAuB;AAAA,iCACC,IAAI,WAAW,eAAe,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAM7D,SAAG,IAAI,oBAAoB;AAE3B,YAAM,eAAe,GAAG;AAAA,QACvB,uCAAuC,IAAI,WAAW,eAAe,CAAC;AAAA,MACvE;AAEA,YAAM,kBAAkB,aAAa,CAAC,KAAK;AAE3C,iBAAW,aAAa,YAAY;AACnC,YAAI,CAAC,mBAAmB,OAAO,gBAAgB,CAAC,CAAC,IAAK,UAAU,cAAc;AAC7E,qBAAW,QAAQ,UAAU,KAAK;AACjC,eAAG,IAAI,IAAI,IAAI,IAAI,CAAC;AAAA,UACrB;AACA,aAAG;AAAA,YACF,kBACC,IAAI,WAAW,eAAe,CAC/B,kCAAkC,UAAU,IAAI,KAAK,UAAU,YAAY;AAAA,UAC5E;AAAA,QACD;AAAA,MACD;AAAA,IACD,SAAS,OAAY;AACpB,SAAG,SAAS;AACZ,YAAM;AAAA,IACP;AAAA,EACD,CAAC;AACF;", "names": []}