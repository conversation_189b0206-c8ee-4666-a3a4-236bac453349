{"version": 3, "sources": ["../../src/durable-sqlite/session.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query } from '~/sql/sql.ts';\nimport { type SQLiteSyncDialect, SQLiteTransaction } from '~/sqlite-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/sqlite-core/query-builders/select.types.ts';\nimport {\n\ttype PreparedQueryConfig as PreparedQueryConfigBase,\n\ttype SQLiteExecuteMethod,\n\tSQLiteSession,\n\ttype SQLiteTransactionConfig,\n} from '~/sqlite-core/session.ts';\nimport { SQLitePreparedQuery as PreparedQueryBase } from '~/sqlite-core/session.ts';\nimport { mapResultRow } from '~/utils.ts';\n\nexport interface SQLiteDOSessionOptions {\n\tlogger?: Logger;\n}\n\ntype PreparedQueryConfig = Omit<PreparedQueryConfigBase, 'statement' | 'run'>;\n\nexport class SQLiteDOSession<TFullSchema extends Record<string, unknown>, TSchema extends TablesRelationalConfig>\n\textends SQLiteSession<\n\t\t'sync',\n\t\tSqlStorageCursor<Record<string, SqlStorageValue>>,\n\t\tTFullSchema,\n\t\tTSchema\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteDOSession';\n\n\tprivate logger: Logger;\n\n\tconstructor(\n\t\tprivate client: DurableObjectStorage,\n\t\tdialect: SQLiteSyncDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\toptions: SQLiteDOSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\tprepareQuery<T extends Omit<PreparedQueryConfig, 'run'>>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => unknown,\n\t): SQLiteDOPreparedQuery<T> {\n\t\treturn new SQLiteDOPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\texecuteMethod,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\toverride transaction<T>(\n\t\ttransaction: (\n\t\t\ttx: SQLiteTransaction<'sync', SqlStorageCursor<Record<string, SqlStorageValue>>, TFullSchema, TSchema>,\n\t\t) => T,\n\t\t_config?: SQLiteTransactionConfig,\n\t): T {\n\t\tconst tx = new SQLiteDOTransaction('sync', this.dialect, this, this.schema);\n\t\tthis.client.transactionSync(() => {\n\t\t\ttransaction(tx);\n\t\t});\n\t\treturn {} as any;\n\t}\n}\n\nexport class SQLiteDOTransaction<TFullSchema extends Record<string, unknown>, TSchema extends TablesRelationalConfig>\n\textends SQLiteTransaction<\n\t\t'sync',\n\t\tSqlStorageCursor<Record<string, SqlStorageValue>>,\n\t\tTFullSchema,\n\t\tTSchema\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteDOTransaction';\n\n\toverride transaction<T>(transaction: (tx: SQLiteDOTransaction<TFullSchema, TSchema>) => T): T {\n\t\tconst tx = new SQLiteDOTransaction('sync', this.dialect, this.session, this.schema, this.nestedIndex + 1);\n\t\tthis.session.transaction(() => transaction(tx));\n\n\t\treturn {} as any;\n\t}\n}\n\nexport class SQLiteDOPreparedQuery<T extends PreparedQueryConfig = PreparedQueryConfig> extends PreparedQueryBase<{\n\ttype: 'sync';\n\trun: void;\n\tall: T['all'];\n\tget: T['get'];\n\tvalues: T['values'];\n\texecute: T['execute'];\n}> {\n\tstatic override readonly [entityKind]: string = 'SQLiteDOPreparedQuery';\n\n\tconstructor(\n\t\tprivate client: DurableObjectStorage,\n\t\tquery: Query,\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => unknown,\n\t) {\n\t\t// 3-6 params are for cache. As long as we don't support sync cache - it will be skipped here\n\t\tsuper('sync', executeMethod, query, undefined, undefined, undefined);\n\t}\n\n\trun(placeholderValues?: Record<string, unknown>): void {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\n\t\tparams.length > 0 ? this.client.sql.exec(this.query.sql, ...params) : this.client.sql.exec(this.query.sql);\n\t}\n\n\tall(placeholderValues?: Record<string, unknown>): T['all'] {\n\t\tconst { fields, joinsNotNullableMap, query, logger, client, customResultMapper } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst params = fillPlaceholders(query.params, placeholderValues ?? {});\n\t\t\tlogger.logQuery(query.sql, params);\n\n\t\t\treturn params.length > 0 ? client.sql.exec(query.sql, ...params).toArray() : client.sql.exec(query.sql).toArray();\n\t\t}\n\n\t\tconst rows = this.values(placeholderValues) as unknown[][];\n\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper(rows) as T['all'];\n\t\t}\n\n\t\treturn rows.map((row) => mapResultRow(fields!, row, joinsNotNullableMap));\n\t}\n\n\tget(placeholderValues?: Record<string, unknown>): T['get'] {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\n\t\tconst { fields, client, joinsNotNullableMap, customResultMapper, query } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\treturn params.length > 0 ? client.sql.exec(query.sql, ...params).one() : client.sql.exec(query.sql).one();\n\t\t}\n\n\t\tconst rows = this.values(placeholderValues) as unknown[][];\n\t\tconst row = rows[0];\n\n\t\tif (!row) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper(rows) as T['get'];\n\t\t}\n\n\t\treturn mapResultRow(fields!, row, joinsNotNullableMap);\n\t}\n\n\tvalues(placeholderValues?: Record<string, unknown>): T['values'] {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\n\t\tconst res = params.length > 0\n\t\t\t? this.client.sql.exec(this.query.sql, ...params)\n\t\t\t: this.client.sql.exec(this.query.sql);\n\n\t\t// @ts-ignore .raw().toArray() exists\n\t\treturn res.raw().toArray();\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAE3B,SAAS,kBAAkB;AAE3B,SAAS,wBAAoC;AAC7C,SAAiC,yBAAyB;AAE1D;AAAA,EAGC;AAAA,OAEM;AACP,SAAS,uBAAuB,yBAAyB;AACzD,SAAS,oBAAoB;AAQtB,MAAM,wBACJ,cAMT;AAAA,EAKC,YACS,QACR,SACQ,QACR,UAAkC,CAAC,GAClC;AACD,UAAM,OAAO;AALL;AAEA;AAIR,SAAK,SAAS,QAAQ,UAAU,IAAI,WAAW;AAAA,EAChD;AAAA,EAZA,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EAYR,aACC,OACA,QACA,eACA,uBACA,oBAC2B;AAC3B,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAES,YACR,aAGA,SACI;AACJ,UAAM,KAAK,IAAI,oBAAoB,QAAQ,KAAK,SAAS,MAAM,KAAK,MAAM;AAC1E,SAAK,OAAO,gBAAgB,MAAM;AACjC,kBAAY,EAAE;AAAA,IACf,CAAC;AACD,WAAO,CAAC;AAAA,EACT;AACD;AAEO,MAAM,4BACJ,kBAMT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEvC,YAAe,aAAsE;AAC7F,UAAM,KAAK,IAAI,oBAAoB,QAAQ,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,cAAc,CAAC;AACxG,SAAK,QAAQ,YAAY,MAAM,YAAY,EAAE,CAAC;AAE9C,WAAO,CAAC;AAAA,EACT;AACD;AAEO,MAAM,8BAAmF,kBAO7F;AAAA,EAGF,YACS,QACR,OACQ,QACA,QACR,eACQ,wBACA,oBACP;AAED,UAAM,QAAQ,eAAe,OAAO,QAAW,QAAW,MAAS;AAT3D;AAEA;AACA;AAEA;AACA;AAAA,EAIT;AAAA,EAbA,QAA0B,UAAU,IAAY;AAAA,EAehD,IAAI,mBAAmD;AACtD,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAE3C,WAAO,SAAS,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,MAAM,KAAK,GAAG,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,MAAM,GAAG;AAAA,EAC1G;AAAA,EAEA,IAAI,mBAAuD;AAC1D,UAAM,EAAE,QAAQ,qBAAqB,OAAO,QAAQ,QAAQ,mBAAmB,IAAI;AACnF,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAM,SAAS,iBAAiB,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AACrE,aAAO,SAAS,MAAM,KAAK,MAAM;AAEjC,aAAO,OAAO,SAAS,IAAI,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG,MAAM,EAAE,QAAQ,IAAI,OAAO,IAAI,KAAK,MAAM,GAAG,EAAE,QAAQ;AAAA,IACjH;AAEA,UAAM,OAAO,KAAK,OAAO,iBAAiB;AAE1C,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,IAAI;AAAA,IAC/B;AAEA,WAAO,KAAK,IAAI,CAAC,QAAQ,aAAa,QAAS,KAAK,mBAAmB,CAAC;AAAA,EACzE;AAAA,EAEA,IAAI,mBAAuD;AAC1D,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAE3C,UAAM,EAAE,QAAQ,QAAQ,qBAAqB,oBAAoB,MAAM,IAAI;AAC3E,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,aAAO,OAAO,SAAS,IAAI,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG,MAAM,EAAE,IAAI,IAAI,OAAO,IAAI,KAAK,MAAM,GAAG,EAAE,IAAI;AAAA,IACzG;AAEA,UAAM,OAAO,KAAK,OAAO,iBAAiB;AAC1C,UAAM,MAAM,KAAK,CAAC;AAElB,QAAI,CAAC,KAAK;AACT,aAAO;AAAA,IACR;AAEA,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,IAAI;AAAA,IAC/B;AAEA,WAAO,aAAa,QAAS,KAAK,mBAAmB;AAAA,EACtD;AAAA,EAEA,OAAO,mBAA0D;AAChE,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAE3C,UAAM,MAAM,OAAO,SAAS,IACzB,KAAK,OAAO,IAAI,KAAK,KAAK,MAAM,KAAK,GAAG,MAAM,IAC9C,KAAK,OAAO,IAAI,KAAK,KAAK,MAAM,GAAG;AAGtC,WAAO,IAAI,IAAI,EAAE,QAAQ;AAAA,EAC1B;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;", "names": []}