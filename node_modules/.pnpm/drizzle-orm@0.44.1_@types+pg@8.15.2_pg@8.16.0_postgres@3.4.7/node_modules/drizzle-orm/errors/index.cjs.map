{"version": 3, "sources": ["../../src/errors/index.ts"], "sourcesContent": ["export class DrizzleQueryError extends Error {\n\tconstructor(\n\t\tpublic query: string,\n\t\tpublic params: any[],\n\t\tpublic override cause?: Error,\n\t) {\n\t\tsuper(`Failed query: ${query}\\nparams: ${params}`);\n\t\tError.captureStackTrace(this, DrizzleQueryError);\n\n\t\t// ES2022+: preserves original error on `.cause`\n\t\tif (cause) (this as any).cause = cause;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,MAAM,0BAA0B,MAAM;AAAA,EAC5C,YACQ,OACA,QACS,OACf;AACD,UAAM,iBAAiB,KAAK;AAAA,UAAa,MAAM,EAAE;AAJ1C;AACA;AACS;AAGhB,UAAM,kBAAkB,MAAM,iBAAiB;AAG/C,QAAI,MAAO,CAAC,KAAa,QAAQ;AAAA,EAClC;AACD;", "names": []}