{"version": 3, "sources": ["../../../src/gel-core/columns/date.common.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnDataType } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport { sql } from '~/sql/sql.ts';\nimport { GelColumnBuilder } from './common.ts';\n\nexport abstract class GelLocalDateColumnBaseBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n> extends GelColumnBuilder<T, TRuntimeConfig> {\n\tstatic override readonly [entityKind]: string = 'GelLocalDateColumnBaseBuilder';\n\n\tdefaultNow() {\n\t\treturn this.default(sql`now()`);\n\t}\n}\n"], "mappings": "AACA,SAAS,kBAAkB;AAC3B,SAAS,WAAW;AACpB,SAAS,wBAAwB;AAE1B,MAAe,sCAGZ,iBAAoC;AAAA,EAC7C,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAa;AACZ,WAAO,KAAK,QAAQ,UAAU;AAAA,EAC/B;AACD;", "names": []}