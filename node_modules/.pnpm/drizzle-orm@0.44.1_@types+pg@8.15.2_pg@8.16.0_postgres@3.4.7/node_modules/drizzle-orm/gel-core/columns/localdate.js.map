{"version": 3, "sources": ["../../../src/gel-core/columns/localdate.ts"], "sourcesContent": ["import type { LocalDate } from 'gel';\nimport type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn } from './common.ts';\nimport { GelLocalDateColumnBaseBuilder } from './date.common.ts';\n\nexport type GelLocalDateStringBuilderInitial<TName extends string> = GelLocalDateStringBuilder<{\n\tname: TName;\n\tdataType: 'localDate';\n\tcolumnType: 'GelLocalDateString';\n\tdata: LocalDate;\n\tdriverParam: LocalDate;\n\tenumValues: undefined;\n}>;\n\nexport class GelLocalDateStringBuilder<T extends ColumnBuilderBaseConfig<'localDate', 'GelLocalDateString'>>\n\textends GelLocalDateColumnBaseBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'GelLocalDateStringBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'localDate', 'GelLocalDateString');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelLocalDateString<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelLocalDateString<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class GelLocalDateString<T extends ColumnBaseConfig<'localDate', 'GelLocalDateString'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelLocalDateString';\n\n\tgetSQLType(): string {\n\t\treturn 'cal::local_date';\n\t}\n}\n\nexport function localDate(): GelLocalDateStringBuilderInitial<''>;\nexport function localDate<TName extends string>(name: TName): GelLocalDateStringBuilderInitial<TName>;\nexport function localDate(name?: string) {\n\treturn new GelLocalDateStringBuilder(name ?? '');\n}\n"], "mappings": "AAGA,SAAS,kBAAkB;AAE3B,SAAS,iBAAiB;AAC1B,SAAS,qCAAqC;AAWvC,MAAM,kCACJ,8BACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,aAAa,oBAAoB;AAAA,EAC9C;AAAA;AAAA,EAGS,MACR,OACsD;AACtD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,2BAA0F,UAAa;AAAA,EACnH,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,UAAU,MAAe;AACxC,SAAO,IAAI,0BAA0B,QAAQ,EAAE;AAChD;", "names": []}