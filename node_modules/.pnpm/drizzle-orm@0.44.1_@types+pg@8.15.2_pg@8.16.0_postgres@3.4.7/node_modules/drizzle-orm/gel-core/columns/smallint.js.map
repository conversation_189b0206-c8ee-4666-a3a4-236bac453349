{"version": 3, "sources": ["../../../src/gel-core/columns/smallint.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn } from './common.ts';\nimport { GelIntColumnBaseBuilder } from './int.common.ts';\n\nexport type GelSmallIntBuilderInitial<TName extends string> = GelSmallIntBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'GelSmallInt';\n\tdata: number;\n\tdriverParam: number;\n\tenumValues: undefined;\n}>;\n\nexport class GelSmallIntBuilder<T extends ColumnBuilderBaseConfig<'number', 'GelSmallInt'>>\n\textends GelIntColumnBaseBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'GelSmallIntBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'GelSmallInt');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelSmallInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelSmallInt<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class GelSmallInt<T extends ColumnBaseConfig<'number', 'GelSmallInt'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelSmallInt';\n\n\tgetSQLType(): string {\n\t\treturn 'smallint';\n\t}\n}\n\nexport function smallint(): GelSmallIntBuilderInitial<''>;\nexport function smallint<TName extends string>(name: TName): GelSmallIntBuilderInitial<TName>;\nexport function smallint(name?: string) {\n\treturn new GelSmallIntBuilder(name ?? '');\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,iBAAiB;AAC1B,SAAS,+BAA+B;AAWjC,MAAM,2BACJ,wBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,aAAa;AAAA,EACpC;AAAA;AAAA,EAGS,MACR,OAC+C;AAC/C,WAAO,IAAI,YAA6C,OAAO,KAAK,MAA8C;AAAA,EACnH;AACD;AAEO,MAAM,oBAAyE,UAAa;AAAA,EAClG,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,SAAS,MAAe;AACvC,SAAO,IAAI,mBAAmB,QAAQ,EAAE;AACzC;", "names": []}