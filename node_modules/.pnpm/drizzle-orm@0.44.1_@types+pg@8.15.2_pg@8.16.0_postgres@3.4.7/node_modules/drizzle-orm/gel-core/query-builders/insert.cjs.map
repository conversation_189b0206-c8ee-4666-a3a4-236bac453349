{"version": 3, "sources": ["../../../src/gel-core/query-builders/insert.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport type { GelDialect } from '~/gel-core/dialect.ts';\nimport type { IndexColumn } from '~/gel-core/indexes.ts';\nimport type {\n\tGelPreparedQuery,\n\tGelQueryResultHKT,\n\tGelQueryResultKind,\n\tGelSession,\n\tPreparedQueryConfig,\n} from '~/gel-core/session.ts';\nimport type { GelTable, TableConfig } from '~/gel-core/table.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { SelectResultFields } from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Placeholder, Query, SQLWrapper } from '~/sql/sql.ts';\nimport { Param, SQL } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport type { InferInsertModel } from '~/table.ts';\nimport { Columns, Table } from '~/table.ts';\nimport { tracer } from '~/tracing.ts';\nimport { haveSameKeys, type NeonAuthToken, orderSelectedFields } from '~/utils.ts';\nimport type { AnyGelColumn, GelColumn } from '../columns/common.ts';\nimport { extractUsedTable } from '../utils.ts';\nimport { QueryBuilder } from './query-builder.ts';\nimport type { SelectedFieldsFlat, SelectedFieldsOrdered } from './select.types.ts';\nimport type { GelUpdateSetSource } from './update.ts';\n\nexport interface GelInsertConfig<TTable extends GelTable = GelTable> {\n\ttable: TTable;\n\tvalues: Record<string, Param | SQL>[] | GelInsertSelectQueryBuilder<TTable> | SQL;\n\twithList?: Subquery[];\n\tonConflict?: SQL;\n\treturning?: SelectedFieldsOrdered;\n\tselect?: boolean;\n\toverridingSystemValue_?: boolean;\n}\n\nexport type GelInsertValue<TTable extends GelTable<TableConfig>, OverrideT extends boolean = false> =\n\t& {\n\t\t[Key in keyof InferInsertModel<TTable, { dbColumnNames: false; override: OverrideT }>]:\n\t\t\t| InferInsertModel<TTable, { dbColumnNames: false; override: OverrideT }>[Key]\n\t\t\t| SQL\n\t\t\t| Placeholder;\n\t}\n\t& {};\n\nexport type GelInsertSelectQueryBuilder<TTable extends GelTable> = TypedQueryBuilder<\n\t{ [K in keyof TTable['$inferInsert']]: AnyGelColumn | SQL | SQL.Aliased | TTable['$inferInsert'][K] }\n>;\n\nexport class GelInsertBuilder<\n\tTTable extends GelTable,\n\tTQueryResult extends GelQueryResultHKT,\n\tOverrideT extends boolean = false,\n> {\n\tstatic readonly [entityKind]: string = 'GelInsertBuilder';\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: GelSession,\n\t\tprivate dialect: GelDialect,\n\t\tprivate withList?: Subquery[],\n\t\tprivate overridingSystemValue_?: boolean,\n\t) {}\n\n\tprivate authToken?: NeonAuthToken;\n\t/** @internal */\n\tsetToken(token?: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\toverridingSystemValue(): Omit<GelInsertBuilder<TTable, TQueryResult, true>, 'overridingSystemValue'> {\n\t\tthis.overridingSystemValue_ = true;\n\t\treturn this as any;\n\t}\n\n\tvalues(value: GelInsertValue<TTable, OverrideT>): GelInsertBase<TTable, TQueryResult>;\n\tvalues(values: GelInsertValue<TTable, OverrideT>[]): GelInsertBase<TTable, TQueryResult>;\n\tvalues(\n\t\tvalues: GelInsertValue<TTable, OverrideT> | GelInsertValue<TTable, OverrideT>[],\n\t): GelInsertBase<TTable, TQueryResult> {\n\t\tvalues = Array.isArray(values) ? values : [values];\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('values() must be called with at least one value');\n\t\t}\n\t\tconst mappedValues = values.map((entry) => {\n\t\t\tconst result: Record<string, Param | SQL> = {};\n\t\t\tconst cols = this.table[Table.Symbol.Columns];\n\t\t\tfor (const colKey of Object.keys(entry)) {\n\t\t\t\tconst colValue = entry[colKey as keyof typeof entry];\n\t\t\t\tresult[colKey] = is(colValue, SQL) ? colValue : new Param(colValue, cols[colKey]);\n\t\t\t}\n\t\t\treturn result;\n\t\t});\n\n\t\treturn new GelInsertBase(\n\t\t\tthis.table,\n\t\t\tmappedValues,\n\t\t\tthis.session,\n\t\t\tthis.dialect,\n\t\t\tthis.withList,\n\t\t\tfalse,\n\t\t\tthis.overridingSystemValue_,\n\t\t);\n\t}\n\n\tselect(selectQuery: (qb: QueryBuilder) => GelInsertSelectQueryBuilder<TTable>): GelInsertBase<TTable, TQueryResult>;\n\tselect(selectQuery: (qb: QueryBuilder) => SQL): GelInsertBase<TTable, TQueryResult>;\n\tselect(selectQuery: SQL): GelInsertBase<TTable, TQueryResult>;\n\tselect(selectQuery: GelInsertSelectQueryBuilder<TTable>): GelInsertBase<TTable, TQueryResult>;\n\tselect(\n\t\tselectQuery:\n\t\t\t| SQL\n\t\t\t| GelInsertSelectQueryBuilder<TTable>\n\t\t\t| ((qb: QueryBuilder) => GelInsertSelectQueryBuilder<TTable> | SQL),\n\t): GelInsertBase<TTable, TQueryResult> {\n\t\tconst select = typeof selectQuery === 'function' ? selectQuery(new QueryBuilder()) : selectQuery;\n\n\t\tif (\n\t\t\t!is(select, SQL)\n\t\t\t&& !haveSameKeys(this.table[Columns], select._.selectedFields)\n\t\t) {\n\t\t\tthrow new Error(\n\t\t\t\t'Insert select error: selected fields are not the same or are in a different order compared to the table definition',\n\t\t\t);\n\t\t}\n\n\t\treturn new GelInsertBase(this.table, select, this.session, this.dialect, this.withList, true);\n\t}\n}\n\nexport type GelInsertWithout<T extends AnyGelInsert, TDynamic extends boolean, K extends keyof T & string> =\n\tTDynamic extends true ? T\n\t\t: Omit<\n\t\t\tGelInsertBase<\n\t\t\t\tT['_']['table'],\n\t\t\t\tT['_']['queryResult'],\n\t\t\t\tT['_']['returning'],\n\t\t\t\tTDynamic,\n\t\t\t\tT['_']['excludedMethods'] | K\n\t\t\t>,\n\t\t\tT['_']['excludedMethods'] | K\n\t\t>;\n\nexport type GelInsertReturning<\n\tT extends AnyGelInsert,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFieldsFlat,\n> = GelInsertBase<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tSelectResultFields<TSelectedFields>,\n\tTDynamic,\n\tT['_']['excludedMethods']\n>;\n\nexport type GelInsertReturningAll<T extends AnyGelInsert, TDynamic extends boolean> = GelInsertBase<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['table']['$inferSelect'],\n\tTDynamic,\n\tT['_']['excludedMethods']\n>;\n\nexport interface GelInsertOnConflictDoUpdateConfig<T extends AnyGelInsert> {\n\ttarget: IndexColumn | IndexColumn[];\n\t/** @deprecated use either `targetWhere` or `setWhere` */\n\twhere?: SQL;\n\t// TODO: add tests for targetWhere and setWhere\n\ttargetWhere?: SQL;\n\tsetWhere?: SQL;\n\tset: GelUpdateSetSource<T['_']['table']>;\n}\n\nexport type GelInsertPrepare<T extends AnyGelInsert> = GelPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['returning'] extends undefined ? GelQueryResultKind<T['_']['queryResult'], never>\n\t\t\t: T['_']['returning'][];\n\t}\n>;\n\nexport type GelInsertDynamic<T extends AnyGelInsert> = GelInsert<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['returning']\n>;\n\nexport type AnyGelInsert = GelInsertBase<any, any, any, any, any>;\n\nexport type GelInsert<\n\tTTable extends GelTable = GelTable,\n\tTQueryResult extends GelQueryResultHKT = GelQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n> = GelInsertBase<TTable, TQueryResult, TReturning, true, never>;\n\nexport interface GelInsertBase<\n\tTTable extends GelTable,\n\tTQueryResult extends GelQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tQueryPromise<TReturning extends undefined ? GelQueryResultKind<TQueryResult, never> : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? GelQueryResultKind<TQueryResult, never> : TReturning[], 'gel'>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'gel';\n\t\treadonly table: TTable;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? GelQueryResultKind<TQueryResult, never> : TReturning[];\n\t};\n}\n\nexport class GelInsertBase<\n\tTTable extends GelTable,\n\tTQueryResult extends GelQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? GelQueryResultKind<TQueryResult, never> : TReturning[]>\n\timplements\n\t\tRunnableQuery<TReturning extends undefined ? GelQueryResultKind<TQueryResult, never> : TReturning[], 'gel'>,\n\t\tSQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'GelInsert';\n\n\tprivate config: GelInsertConfig<TTable>;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tvalues: GelInsertConfig['values'],\n\t\tprivate session: GelSession,\n\t\tprivate dialect: GelDialect,\n\t\twithList?: Subquery[],\n\t\tselect?: boolean,\n\t\toverridingSystemValue_?: boolean,\n\t) {\n\t\tsuper();\n\t\tthis.config = { table, values: values as any, withList, select, overridingSystemValue_ };\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the inserted rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#insert-returning}\n\t *\n\t * @example\n\t * ```ts\n\t * // Insert one row and return all fields\n\t * const insertedCar: Car[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning();\n\t *\n\t * // Insert one row and return only the id\n\t * const insertedCarId: { id: number }[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning({ id: cars.id });\n\t * ```\n\t */\n\treturning(): GelInsertWithout<GelInsertReturningAll<this, TDynamic>, TDynamic, 'returning'>;\n\treturning<TSelectedFields extends SelectedFieldsFlat>(\n\t\tfields: TSelectedFields,\n\t): GelInsertWithout<GelInsertReturning<this, TDynamic, TSelectedFields>, TDynamic, 'returning'>;\n\treturning(\n\t\tfields: SelectedFieldsFlat = this.config.table[Table.Symbol.Columns],\n\t): GelInsertWithout<AnyGelInsert, TDynamic, 'returning'> {\n\t\tthis.config.returning = orderSelectedFields<GelColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `on conflict do nothing` clause to the query.\n\t *\n\t * Calling this method simply avoids inserting a row as its alternative action.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#on-conflict-do-nothing}\n\t *\n\t * @param config The `target` and `where` clauses.\n\t *\n\t * @example\n\t * ```ts\n\t * // Insert one row and cancel the insert if there's a conflict\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoNothing();\n\t *\n\t * // Explicitly specify conflict target\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoNothing({ target: cars.id });\n\t * ```\n\t */\n\t// TODO not supported\n\t// onConflictDoNothing(\n\t// \tconfig: { target?: IndexColumn | IndexColumn[]; where?: SQL } = {},\n\t// ): GelInsertWithout<this, TDynamic, 'onConflictDoNothing' | 'onConflictDoUpdate'> {\n\t// \tif (config.target === undefined) {\n\t// \t\tthis.config.onConflict = sql`do nothing`;\n\t// \t} else {\n\t// \t\tlet targetColumn = '';\n\t// \t\ttargetColumn = Array.isArray(config.target)\n\t// \t\t\t? config.target.map((it) => this.dialect.escapeName(this.dialect.casing.getColumnCasing(it))).join(',')\n\t// \t\t\t: this.dialect.escapeName(this.dialect.casing.getColumnCasing(config.target));\n\n\t// \t\tconst whereSql = config.where ? sql` where ${config.where}` : undefined;\n\t// \t\tthis.config.onConflict = sql`(${sql.raw(targetColumn)})${whereSql} do nothing`;\n\t// \t}\n\t// \treturn this as any;\n\t// }\n\n\t/**\n\t * Adds an `on conflict do update` clause to the query.\n\t *\n\t * Calling this method will update the existing row that conflicts with the row proposed for insertion as its alternative action.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#upserts-and-conflicts}\n\t *\n\t * @param config The `target`, `set` and `where` clauses.\n\t *\n\t * @example\n\t * ```ts\n\t * // Update the row if there's a conflict\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoUpdate({\n\t *     target: cars.id,\n\t *     set: { brand: 'Porsche' }\n\t *   });\n\t *\n\t * // Upsert with 'where' clause\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoUpdate({\n\t *     target: cars.id,\n\t *     set: { brand: 'newBMW' },\n\t *     targetWhere: sql`${cars.createdAt} > '2023-01-01'::date`,\n\t *   });\n\t * ```\n\t */\n\t// TODO not supported\n\t// onConflictDoUpdate(\n\t// \tconfig: GelInsertOnConflictDoUpdateConfig<this>,\n\t// ): GelInsertWithout<this, TDynamic, 'onConflictDoNothing' | 'onConflictDoUpdate'> {\n\t// \tif (config.where && (config.targetWhere || config.setWhere)) {\n\t// \t\tthrow new Error(\n\t// \t\t\t'You cannot use both \"where\" and \"targetWhere\"/\"setWhere\" at the same time - \"where\" is deprecated, use \"targetWhere\" or \"setWhere\" instead.',\n\t// \t\t);\n\t// \t}\n\t// \tconst whereSql = config.where ? sql` where ${config.where}` : undefined;\n\t// \tconst targetWhereSql = config.targetWhere ? sql` where ${config.targetWhere}` : undefined;\n\t// \tconst setWhereSql = config.setWhere ? sql` where ${config.setWhere}` : undefined;\n\t// \tconst setSql = this.dialect.buildUpdateSet(this.config.table, mapUpdateSet(this.config.table, config.set));\n\t// \tlet targetColumn = '';\n\t// \ttargetColumn = Array.isArray(config.target)\n\t// \t\t? config.target.map((it) => this.dialect.escapeName(this.dialect.casing.getColumnCasing(it))).join(',')\n\t// \t\t: this.dialect.escapeName(this.dialect.casing.getColumnCasing(config.target));\n\t// \tthis.config.onConflict = sql`(${\n\t// \t\tsql.raw(targetColumn)\n\t// \t})${targetWhereSql} do update set ${setSql}${whereSql}${setWhereSql}`;\n\t// \treturn this as any;\n\t// }\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildInsertQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): GelInsertPrepare<this> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\treturn this.session.prepareQuery<\n\t\t\t\tPreparedQueryConfig & {\n\t\t\t\t\texecute: TReturning extends undefined ? GelQueryResultKind<TQueryResult, never> : TReturning[];\n\t\t\t\t}\n\t\t\t>(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true, undefined, {\n\t\t\t\ttype: 'insert',\n\t\t\t\ttables: extractUsedTable(this.config.table),\n\t\t\t});\n\t\t});\n\t}\n\n\tprepare(name: string): GelInsertPrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues);\n\t\t});\n\t};\n\n\t$dynamic(): GelInsertDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA+B;AAa/B,2BAA6B;AAG7B,iBAA2B;AAG3B,mBAA+B;AAC/B,qBAAuB;AACvB,mBAAsE;AAEtE,IAAAA,gBAAiC;AACjC,2BAA6B;AA2BtB,MAAM,iBAIX;AAAA,EAGD,YACS,OACA,SACA,SACA,UACA,wBACP;AALO;AACA;AACA;AACA;AACA;AAAA,EACN;AAAA,EARH,QAAiB,wBAAU,IAAY;AAAA,EAU/B;AAAA;AAAA,EAER,SAAS,OAAuB;AAC/B,SAAK,YAAY;AACjB,WAAO;AAAA,EACR;AAAA,EAEA,wBAAqG;AACpG,SAAK,yBAAyB;AAC9B,WAAO;AAAA,EACR;AAAA,EAIA,OACC,QACsC;AACtC,aAAS,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AACjD,QAAI,OAAO,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM,iDAAiD;AAAA,IAClE;AACA,UAAM,eAAe,OAAO,IAAI,CAAC,UAAU;AAC1C,YAAM,SAAsC,CAAC;AAC7C,YAAM,OAAO,KAAK,MAAM,mBAAM,OAAO,OAAO;AAC5C,iBAAW,UAAU,OAAO,KAAK,KAAK,GAAG;AACxC,cAAM,WAAW,MAAM,MAA4B;AACnD,eAAO,MAAM,QAAI,kBAAG,UAAU,cAAG,IAAI,WAAW,IAAI,iBAAM,UAAU,KAAK,MAAM,CAAC;AAAA,MACjF;AACA,aAAO;AAAA,IACR,CAAC;AAED,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AAAA,EAMA,OACC,aAIsC;AACtC,UAAM,SAAS,OAAO,gBAAgB,aAAa,YAAY,IAAI,kCAAa,CAAC,IAAI;AAErF,QACC,KAAC,kBAAG,QAAQ,cAAG,KACZ,KAAC,2BAAa,KAAK,MAAM,oBAAO,GAAG,OAAO,EAAE,cAAc,GAC5D;AACD,YAAM,IAAI;AAAA,QACT;AAAA,MACD;AAAA,IACD;AAEA,WAAO,IAAI,cAAc,KAAK,OAAO,QAAQ,KAAK,SAAS,KAAK,SAAS,KAAK,UAAU,IAAI;AAAA,EAC7F;AACD;AAwFO,MAAM,sBAQH,kCAIV;AAAA,EAKC,YACC,OACA,QACQ,SACA,SACR,UACA,QACA,wBACC;AACD,UAAM;AANE;AACA;AAMR,SAAK,SAAS,EAAE,OAAO,QAAuB,UAAU,QAAQ,uBAAuB;AAAA,EACxF;AAAA,EAfA,QAA0B,wBAAU,IAAY;AAAA,EAExC;AAAA,EAuCR,UACC,SAA6B,KAAK,OAAO,MAAM,mBAAM,OAAO,OAAO,GACX;AACxD,SAAK,OAAO,gBAAY,kCAA+B,MAAM;AAC7D,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+FA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;AAAA,EACjD;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAS,MAAuC;AAC/C,WAAO,sBAAO,gBAAgB,wBAAwB,MAAM;AAC3D,aAAO,KAAK,QAAQ,aAIlB,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,WAAW,MAAM,MAAM,QAAW;AAAA,QACvF,MAAM;AAAA,QACN,YAAQ,gCAAiB,KAAK,OAAO,KAAK;AAAA,MAC3C,CAAC;AAAA,IACF,CAAC;AAAA,EACF;AAAA,EAEA,QAAQ,MAAsC;AAC7C,WAAO,KAAK,SAAS,IAAI;AAAA,EAC1B;AAAA,EAES,UAAkD,CAAC,sBAAsB;AACjF,WAAO,sBAAO,gBAAgB,qBAAqB,MAAM;AACxD,aAAO,KAAK,SAAS,EAAE,QAAQ,iBAAiB;AAAA,IACjD,CAAC;AAAA,EACF;AAAA,EAEA,WAAmC;AAClC,WAAO;AAAA,EACR;AACD;", "names": ["import_utils"]}