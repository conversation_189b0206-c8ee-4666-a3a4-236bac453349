{"version": 3, "sources": ["../../../src/gel-core/query-builders/select.ts"], "sourcesContent": ["import type { CacheConfig, WithCacheConfig } from '~/cache/core/types.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport type { GelColumn } from '~/gel-core/columns/index.ts';\nimport type { GelDialect } from '~/gel-core/dialect.ts';\nimport type { GelSession, PreparedQueryConfig } from '~/gel-core/session.ts';\nimport type { SubqueryWithSelection } from '~/gel-core/subquery.ts';\nimport type { GelTable } from '~/gel-core/table.ts';\nimport { GelViewBase } from '~/gel-core/view-base.ts';\nimport { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type {\n\tBuildSubquerySelection,\n\tGetSelectTableName,\n\tGetSelectTableSelection,\n\tJoinNullability,\n\tJoinType,\n\tSelectMode,\n\tSelectResult,\n\tSetOperator,\n} from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport { SQL, View } from '~/sql/sql.ts';\nimport type { ColumnsSelection, Placeholder, Query, SQLWrapper } from '~/sql/sql.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport { tracer } from '~/tracing.ts';\nimport {\n\tapplyMixins,\n\tgetTableColumns,\n\tgetTableLikeName,\n\thaveSameKeys,\n\ttype NeonAuthToken,\n\ttype ValueOrArray,\n} from '~/utils.ts';\nimport { orderSelectedFields } from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport { extractUsedTable } from '../utils.ts';\nimport type {\n\tAnyGelSelect,\n\tCreateGelSelectFromBuilderMode,\n\tGelCreateSetOperatorFn,\n\tGelSelectConfig,\n\tGelSelectDynamic,\n\tGelSelectHKT,\n\tGelSelectHKTBase,\n\tGelSelectJoinFn,\n\tGelSelectPrepare,\n\tGelSelectWithout,\n\tGelSetOperatorExcludedMethods,\n\tGelSetOperatorWithResult,\n\tGetGelSetOperators,\n\tLockConfig,\n\tLockStrength,\n\tSelectedFields,\n\tSetOperatorRightSelect,\n} from './select.types.ts';\n\nexport class GelSelectBuilder<\n\tTSelection extends SelectedFields | undefined,\n\tTBuilderMode extends 'db' | 'qb' = 'db',\n> {\n\tstatic readonly [entityKind]: string = 'GelSelectBuilder';\n\n\tprivate fields: TSelection;\n\tprivate session: GelSession | undefined;\n\tprivate dialect: GelDialect;\n\tprivate withList: Subquery[] = [];\n\tprivate distinct: boolean | {\n\t\ton: (GelColumn | SQLWrapper)[];\n\t} | undefined;\n\n\tconstructor(\n\t\tconfig: {\n\t\t\tfields: TSelection;\n\t\t\tsession: GelSession | undefined;\n\t\t\tdialect: GelDialect;\n\t\t\twithList?: Subquery[];\n\t\t\tdistinct?: boolean | {\n\t\t\t\ton: (GelColumn | SQLWrapper)[];\n\t\t\t};\n\t\t},\n\t) {\n\t\tthis.fields = config.fields;\n\t\tthis.session = config.session;\n\t\tthis.dialect = config.dialect;\n\t\tif (config.withList) {\n\t\t\tthis.withList = config.withList;\n\t\t}\n\t\tthis.distinct = config.distinct;\n\t}\n\n\tprivate authToken?: NeonAuthToken;\n\t/** @internal */\n\tsetToken(token?: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Specify the table, subquery, or other target that you're\n\t * building a select query against.\n\t *\n\t * {@link https://www.postgresql.org/docs/current/sql-select.html#SQL-FROM | Postgres from documentation}\n\t */\n\tfrom<TFrom extends GelTable | Subquery | GelViewBase | SQL>(\n\t\tsource: TFrom,\n\t): CreateGelSelectFromBuilderMode<\n\t\tTBuilderMode,\n\t\tGetSelectTableName<TFrom>,\n\t\tTSelection extends undefined ? GetSelectTableSelection<TFrom> : TSelection,\n\t\tTSelection extends undefined ? 'single' : 'partial'\n\t> {\n\t\tconst isPartialSelect = !!this.fields;\n\n\t\tlet fields: SelectedFields;\n\t\tif (this.fields) {\n\t\t\tfields = this.fields;\n\t\t} else if (is(source, Subquery)) {\n\t\t\t// This is required to use the proxy handler to get the correct field values from the subquery\n\t\t\tfields = Object.fromEntries(\n\t\t\t\tObject.keys(source._.selectedFields).map((\n\t\t\t\t\tkey,\n\t\t\t\t) => [key, source[key as unknown as keyof typeof source] as unknown as SelectedFields[string]]),\n\t\t\t);\n\t\t} else if (is(source, GelViewBase)) {\n\t\t\tfields = source[ViewBaseConfig].selectedFields as SelectedFields;\n\t\t} else if (is(source, SQL)) {\n\t\t\tfields = {};\n\t\t} else {\n\t\t\tfields = getTableColumns<GelTable>(source);\n\t\t}\n\n\t\treturn new GelSelectBase({\n\t\t\ttable: source,\n\t\t\tfields,\n\t\t\tisPartialSelect,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t\twithList: this.withList,\n\t\t\tdistinct: this.distinct,\n\t\t}) as any;\n\t}\n}\n\nexport abstract class GelSelectQueryBuilderBase<\n\tTHKT extends GelSelectHKTBase,\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> extends TypedQueryBuilder<TSelectedFields, TResult> {\n\tstatic override readonly [entityKind]: string = 'GelSelectQueryBuilder';\n\n\toverride readonly _: {\n\t\treadonly dialect: 'gel';\n\t\treadonly hkt: THKT;\n\t\treadonly tableName: TTableName;\n\t\treadonly selection: TSelection;\n\t\treadonly selectMode: TSelectMode;\n\t\treadonly nullabilityMap: TNullabilityMap;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TResult;\n\t\treadonly selectedFields: TSelectedFields;\n\t\treadonly config: GelSelectConfig;\n\t};\n\n\tprotected config: GelSelectConfig;\n\tprotected joinsNotNullableMap: Record<string, boolean>;\n\tprivate tableName: string | undefined;\n\tprivate isPartialSelect: boolean;\n\tprotected session: GelSession | undefined;\n\tprotected dialect: GelDialect;\n\tprotected cacheConfig?: WithCacheConfig = undefined;\n\tprotected usedTables: Set<string> = new Set();\n\n\tconstructor(\n\t\t{ table, fields, isPartialSelect, session, dialect, withList, distinct }: {\n\t\t\ttable: GelSelectConfig['table'];\n\t\t\tfields: GelSelectConfig['fields'];\n\t\t\tisPartialSelect: boolean;\n\t\t\tsession: GelSession | undefined;\n\t\t\tdialect: GelDialect;\n\t\t\twithList: Subquery[];\n\t\t\tdistinct: boolean | {\n\t\t\t\ton: (GelColumn | SQLWrapper)[];\n\t\t\t} | undefined;\n\t\t},\n\t) {\n\t\tsuper();\n\t\tthis.config = {\n\t\t\twithList,\n\t\t\ttable,\n\t\t\tfields: { ...fields },\n\t\t\tdistinct,\n\t\t\tsetOperators: [],\n\t\t};\n\t\tthis.isPartialSelect = isPartialSelect;\n\t\tthis.session = session;\n\t\tthis.dialect = dialect;\n\t\tthis._ = {\n\t\t\tselectedFields: fields as TSelectedFields,\n\t\t\tconfig: this.config,\n\t\t} as this['_'];\n\t\tthis.tableName = getTableLikeName(table);\n\t\tthis.joinsNotNullableMap = typeof this.tableName === 'string' ? { [this.tableName]: true } : {};\n\t\tfor (const item of extractUsedTable(table)) this.usedTables.add(item);\n\t}\n\n\t/** @internal */\n\tgetUsedTables() {\n\t\treturn [...this.usedTables];\n\t}\n\n\tprivate createJoin<\n\t\tTJoinType extends JoinType,\n\t\tTIsLateral extends (TJoinType extends 'full' | 'right' ? false : boolean),\n\t>(\n\t\tjoinType: TJoinType,\n\t\tlateral: TIsLateral,\n\t): GelSelectJoinFn<this, TDynamic, TJoinType, TIsLateral> {\n\t\treturn (\n\t\t\ttable: GelTable | Subquery | GelViewBase | SQL,\n\t\t\ton?: ((aliases: TSelection) => SQL | undefined) | SQL | undefined,\n\t\t) => {\n\t\t\tconst baseTableName = this.tableName;\n\t\t\tconst tableName = getTableLikeName(table);\n\n\t\t\tif (typeof tableName === 'string' && this.config.joins?.some((join) => join.alias === tableName)) {\n\t\t\t\tthrow new Error(`Alias \"${tableName}\" is already used in this query`);\n\t\t\t}\n\n\t\t\t// store all tables used in a query\n\t\t\tfor (const item of extractUsedTable(table)) this.usedTables.add(item);\n\n\t\t\tif (!this.isPartialSelect) {\n\t\t\t\t// If this is the first join and this is not a partial select and we're not selecting from raw SQL, \"move\" the fields from the main table to the nested object\n\t\t\t\tif (Object.keys(this.joinsNotNullableMap).length === 1 && typeof baseTableName === 'string') {\n\t\t\t\t\tthis.config.fields = {\n\t\t\t\t\t\t[baseTableName]: this.config.fields,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tif (typeof tableName === 'string' && !is(table, SQL)) {\n\t\t\t\t\tconst selection = is(table, Subquery)\n\t\t\t\t\t\t? table._.selectedFields\n\t\t\t\t\t\t: is(table, View)\n\t\t\t\t\t\t? table[ViewBaseConfig].selectedFields\n\t\t\t\t\t\t: table[Table.Symbol.Columns];\n\t\t\t\t\tthis.config.fields[tableName] = selection;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (typeof on === 'function') {\n\t\t\t\ton = on(\n\t\t\t\t\tnew Proxy(\n\t\t\t\t\t\tthis.config.fields,\n\t\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t\t) as TSelection,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tif (!this.config.joins) {\n\t\t\t\tthis.config.joins = [];\n\t\t\t}\n\n\t\t\tthis.config.joins.push({ on, table, joinType, alias: tableName, lateral });\n\n\t\t\tif (typeof tableName === 'string') {\n\t\t\t\tswitch (joinType) {\n\t\t\t\t\tcase 'left': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'right': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'cross':\n\t\t\t\t\tcase 'inner': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'full': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn this as any;\n\t\t};\n\t}\n\n\t/**\n\t * Executes a `left join` operation by adding another table to the current query.\n\t *\n\t * Calling this method associates each row of the table with the corresponding row from the joined table, if a match is found. If no matching row exists, it sets all columns of the joined table to null.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#left-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User; pets: Pet | null; }[] = await db.select()\n\t *   .from(users)\n\t *   .leftJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number; petId: number | null; }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .leftJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\tleftJoin = this.createJoin('left', false);\n\n\t/**\n\t * Executes a `left join lateral` operation by adding subquery to the current query.\n\t *\n\t * A `lateral` join allows the right-hand expression to refer to columns from the left-hand side.\n\t *\n\t * Calling this method associates each row of the table with the corresponding row from the joined table, if a match is found. If no matching row exists, it sets all columns of the joined table to null.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#left-join-lateral}\n\t *\n\t * @param table the subquery to join.\n\t * @param on the `on` clause.\n\t */\n\tleftJoinLateral = this.createJoin('left', true);\n\n\t/**\n\t * Executes a `right join` operation by adding another table to the current query.\n\t *\n\t * Calling this method associates each row of the joined table with the corresponding row from the main table, if a match is found. If no matching row exists, it sets all columns of the main table to null.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#right-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User | null; pets: Pet; }[] = await db.select()\n\t *   .from(users)\n\t *   .rightJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number | null; petId: number; }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .rightJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\trightJoin = this.createJoin('right', false);\n\n\t/**\n\t * Executes an `inner join` operation, creating a new table by combining rows from two tables that have matching values.\n\t *\n\t * Calling this method retrieves rows that have corresponding entries in both joined tables. Rows without matching entries in either table are excluded, resulting in a table that includes only matching pairs.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#inner-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User; pets: Pet; }[] = await db.select()\n\t *   .from(users)\n\t *   .innerJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number; petId: number; }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .innerJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\tinnerJoin = this.createJoin('inner', false);\n\n\t/**\n\t * Executes an `inner join lateral` operation, creating a new table by combining rows from two queries that have matching values.\n\t *\n\t * A `lateral` join allows the right-hand expression to refer to columns from the left-hand side.\n\t *\n\t * Calling this method retrieves rows that have corresponding entries in both joined tables. Rows without matching entries in either table are excluded, resulting in a table that includes only matching pairs.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#inner-join-lateral}\n\t *\n\t * @param table the subquery to join.\n\t * @param on the `on` clause.\n\t */\n\tinnerJoinLateral = this.createJoin('inner', true);\n\n\t/**\n\t * Executes a `full join` operation by combining rows from two tables into a new table.\n\t *\n\t * Calling this method retrieves all rows from both main and joined tables, merging rows with matching values and filling in `null` for non-matching columns.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#full-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User | null; pets: Pet | null; }[] = await db.select()\n\t *   .from(users)\n\t *   .fullJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number | null; petId: number | null; }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .fullJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\tfullJoin = this.createJoin('full', false);\n\n\t/**\n\t * Executes a `cross join` operation by combining rows from two tables into a new table.\n\t *\n\t * Calling this method retrieves all rows from both main and joined tables, merging all rows from each table.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#cross-join}\n\t *\n\t * @param table the table to join.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users, each user with every pet\n\t * const usersWithPets: { user: User; pets: Pet; }[] = await db.select()\n\t *   .from(users)\n\t *   .crossJoin(pets)\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number; petId: number; }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .crossJoin(pets)\n\t * ```\n\t */\n\tcrossJoin = this.createJoin('cross', false);\n\n\t/**\n\t * Executes a `cross join lateral` operation by combining rows from two queries into a new table.\n\t *\n\t * A `lateral` join allows the right-hand expression to refer to columns from the left-hand side.\n\t *\n\t * Calling this method retrieves all rows from both main and joined queries, merging all rows from each query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#cross-join-lateral}\n\t *\n\t * @param table the query to join.\n\t */\n\tcrossJoinLateral = this.createJoin('cross', true);\n\n\tprivate createSetOperator(\n\t\ttype: SetOperator,\n\t\tisAll: boolean,\n\t): <TValue extends GelSetOperatorWithResult<TResult>>(\n\t\trightSelection:\n\t\t\t| ((setOperators: GetGelSetOperators) => SetOperatorRightSelect<TValue, TResult>)\n\t\t\t| SetOperatorRightSelect<TValue, TResult>,\n\t) => GelSelectWithout<\n\t\tthis,\n\t\tTDynamic,\n\t\tGelSetOperatorExcludedMethods,\n\t\ttrue\n\t> {\n\t\treturn (rightSelection) => {\n\t\t\tconst rightSelect = (typeof rightSelection === 'function'\n\t\t\t\t? rightSelection(getGelSetOperators())\n\t\t\t\t: rightSelection) as TypedQueryBuilder<\n\t\t\t\t\tany,\n\t\t\t\t\tTResult\n\t\t\t\t>;\n\n\t\t\tif (!haveSameKeys(this.getSelectedFields(), rightSelect.getSelectedFields())) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t'Set operator error (union / intersect / except): selected fields are not the same or are in a different order',\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tthis.config.setOperators.push({ type, isAll, rightSelect });\n\t\t\treturn this as any;\n\t\t};\n\t}\n\n\t/**\n\t * Adds `union` set operator to the query.\n\t *\n\t * Calling this method will combine the result sets of the `select` statements and remove any duplicate rows that appear across them.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#union}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all unique names from customers and users tables\n\t * await db.select({ name: users.name })\n\t *   .from(users)\n\t *   .union(\n\t *     db.select({ name: customers.name }).from(customers)\n\t *   );\n\t * // or\n\t * import { union } from 'drizzle-orm/gel-core'\n\t *\n\t * await union(\n\t *   db.select({ name: users.name }).from(users),\n\t *   db.select({ name: customers.name }).from(customers)\n\t * );\n\t * ```\n\t */\n\tunion = this.createSetOperator('union', false);\n\n\t/**\n\t * Adds `union all` set operator to the query.\n\t *\n\t * Calling this method will combine the result-set of the `select` statements and keep all duplicate rows that appear across them.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#union-all}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all transaction ids from both online and in-store sales\n\t * await db.select({ transaction: onlineSales.transactionId })\n\t *   .from(onlineSales)\n\t *   .unionAll(\n\t *     db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n\t *   );\n\t * // or\n\t * import { unionAll } from 'drizzle-orm/gel-core'\n\t *\n\t * await unionAll(\n\t *   db.select({ transaction: onlineSales.transactionId }).from(onlineSales),\n\t *   db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n\t * );\n\t * ```\n\t */\n\tunionAll = this.createSetOperator('union', true);\n\n\t/**\n\t * Adds `intersect` set operator to the query.\n\t *\n\t * Calling this method will retain only the rows that are present in both result sets and eliminate duplicates.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select course names that are offered in both departments A and B\n\t * await db.select({ courseName: depA.courseName })\n\t *   .from(depA)\n\t *   .intersect(\n\t *     db.select({ courseName: depB.courseName }).from(depB)\n\t *   );\n\t * // or\n\t * import { intersect } from 'drizzle-orm/gel-core'\n\t *\n\t * await intersect(\n\t *   db.select({ courseName: depA.courseName }).from(depA),\n\t *   db.select({ courseName: depB.courseName }).from(depB)\n\t * );\n\t * ```\n\t */\n\tintersect = this.createSetOperator('intersect', false);\n\n\t/**\n\t * Adds `intersect all` set operator to the query.\n\t *\n\t * Calling this method will retain only the rows that are present in both result sets including all duplicates.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect-all}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all products and quantities that are ordered by both regular and VIP customers\n\t * await db.select({\n\t *   productId: regularCustomerOrders.productId,\n\t *   quantityOrdered: regularCustomerOrders.quantityOrdered\n\t * })\n\t * .from(regularCustomerOrders)\n\t * .intersectAll(\n\t *   db.select({\n\t *     productId: vipCustomerOrders.productId,\n\t *     quantityOrdered: vipCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(vipCustomerOrders)\n\t * );\n\t * // or\n\t * import { intersectAll } from 'drizzle-orm/gel-core'\n\t *\n\t * await intersectAll(\n\t *   db.select({\n\t *     productId: regularCustomerOrders.productId,\n\t *     quantityOrdered: regularCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(regularCustomerOrders),\n\t *   db.select({\n\t *     productId: vipCustomerOrders.productId,\n\t *     quantityOrdered: vipCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(vipCustomerOrders)\n\t * );\n\t * ```\n\t */\n\tintersectAll = this.createSetOperator('intersect', true);\n\n\t/**\n\t * Adds `except` set operator to the query.\n\t *\n\t * Calling this method will retrieve all unique rows from the left query, except for the rows that are present in the result set of the right query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#except}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all courses offered in department A but not in department B\n\t * await db.select({ courseName: depA.courseName })\n\t *   .from(depA)\n\t *   .except(\n\t *     db.select({ courseName: depB.courseName }).from(depB)\n\t *   );\n\t * // or\n\t * import { except } from 'drizzle-orm/gel-core'\n\t *\n\t * await except(\n\t *   db.select({ courseName: depA.courseName }).from(depA),\n\t *   db.select({ courseName: depB.courseName }).from(depB)\n\t * );\n\t * ```\n\t */\n\texcept = this.createSetOperator('except', false);\n\n\t/**\n\t * Adds `except all` set operator to the query.\n\t *\n\t * Calling this method will retrieve all rows from the left query, except for the rows that are present in the result set of the right query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#except-all}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all products that are ordered by regular customers but not by VIP customers\n\t * await db.select({\n\t *   productId: regularCustomerOrders.productId,\n\t *   quantityOrdered: regularCustomerOrders.quantityOrdered,\n\t * })\n\t * .from(regularCustomerOrders)\n\t * .exceptAll(\n\t *   db.select({\n\t *     productId: vipCustomerOrders.productId,\n\t *     quantityOrdered: vipCustomerOrders.quantityOrdered,\n\t *   })\n\t *   .from(vipCustomerOrders)\n\t * );\n\t * // or\n\t * import { exceptAll } from 'drizzle-orm/gel-core'\n\t *\n\t * await exceptAll(\n\t *   db.select({\n\t *     productId: regularCustomerOrders.productId,\n\t *     quantityOrdered: regularCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(regularCustomerOrders),\n\t *   db.select({\n\t *     productId: vipCustomerOrders.productId,\n\t *     quantityOrdered: vipCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(vipCustomerOrders)\n\t * );\n\t * ```\n\t */\n\texceptAll = this.createSetOperator('except', true);\n\n\t/** @internal */\n\taddSetOperators(setOperators: GelSelectConfig['setOperators']): GelSelectWithout<\n\t\tthis,\n\t\tTDynamic,\n\t\tGelSetOperatorExcludedMethods,\n\t\ttrue\n\t> {\n\t\tthis.config.setOperators.push(...setOperators);\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `where` clause to the query.\n\t *\n\t * Calling this method will select only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#filtering}\n\t *\n\t * @param where the `where` clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be selected.\n\t *\n\t * ```ts\n\t * // Select all cars with green color\n\t * await db.select().from(cars).where(eq(cars.color, 'green'));\n\t * // or\n\t * await db.select().from(cars).where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Select all BMW cars with a green color\n\t * await db.select().from(cars).where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Select all cars with the green or blue color\n\t * await db.select().from(cars).where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(\n\t\twhere: ((aliases: this['_']['selection']) => SQL | undefined) | SQL | undefined,\n\t): GelSelectWithout<this, TDynamic, 'where'> {\n\t\tif (typeof where === 'function') {\n\t\t\twhere = where(\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\t\t}\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `having` clause to the query.\n\t *\n\t * Calling this method will select only those rows that fulfill a specified condition. It is typically used with aggregate functions to filter the aggregated data based on a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#aggregations}\n\t *\n\t * @param having the `having` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all brands with more than one car\n\t * await db.select({\n\t * \tbrand: cars.brand,\n\t * \tcount: sql<number>`cast(count(${cars.id}) as int)`,\n\t * })\n\t *   .from(cars)\n\t *   .groupBy(cars.brand)\n\t *   .having(({ count }) => gt(count, 1));\n\t * ```\n\t */\n\thaving(\n\t\thaving: ((aliases: this['_']['selection']) => SQL | undefined) | SQL | undefined,\n\t): GelSelectWithout<this, TDynamic, 'having'> {\n\t\tif (typeof having === 'function') {\n\t\t\thaving = having(\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\t\t}\n\t\tthis.config.having = having;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `group by` clause to the query.\n\t *\n\t * Calling this method will group rows that have the same values into summary rows, often used for aggregation purposes.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#aggregations}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Group and count people by their last names\n\t * await db.select({\n\t *    lastName: people.lastName,\n\t *    count: sql<number>`cast(count(*) as int)`\n\t * })\n\t *   .from(people)\n\t *   .groupBy(people.lastName);\n\t * ```\n\t */\n\tgroupBy(\n\t\tbuilder: (aliases: this['_']['selection']) => ValueOrArray<GelColumn | SQL | SQL.Aliased>,\n\t): GelSelectWithout<this, TDynamic, 'groupBy'>;\n\tgroupBy(...columns: (GelColumn | SQL | SQL.Aliased)[]): GelSelectWithout<this, TDynamic, 'groupBy'>;\n\tgroupBy(\n\t\t...columns:\n\t\t\t| [(aliases: this['_']['selection']) => ValueOrArray<GelColumn | SQL | SQL.Aliased>]\n\t\t\t| (GelColumn | SQL | SQL.Aliased)[]\n\t): GelSelectWithout<this, TDynamic, 'groupBy'> {\n\t\tif (typeof columns[0] === 'function') {\n\t\t\tconst groupBy = columns[0](\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'alias', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\t\t\tthis.config.groupBy = Array.isArray(groupBy) ? groupBy : [groupBy];\n\t\t} else {\n\t\t\tthis.config.groupBy = columns as (GelColumn | SQL | SQL.Aliased)[];\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `order by` clause to the query.\n\t *\n\t * Calling this method will sort the result-set in ascending or descending order. By default, the sort order is ascending.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#order-by}\n\t *\n\t * @example\n\t *\n\t * ```\n\t * // Select cars ordered by year\n\t * await db.select().from(cars).orderBy(cars.year);\n\t * ```\n\t *\n\t * You can specify whether results are in ascending or descending order with the `asc()` and `desc()` operators.\n\t *\n\t * ```ts\n\t * // Select cars ordered by year in descending order\n\t * await db.select().from(cars).orderBy(desc(cars.year));\n\t *\n\t * // Select cars ordered by year and price\n\t * await db.select().from(cars).orderBy(asc(cars.year), desc(cars.price));\n\t * ```\n\t */\n\torderBy(\n\t\tbuilder: (aliases: this['_']['selection']) => ValueOrArray<GelColumn | SQL | SQL.Aliased>,\n\t): GelSelectWithout<this, TDynamic, 'orderBy'>;\n\torderBy(...columns: (GelColumn | SQL | SQL.Aliased)[]): GelSelectWithout<this, TDynamic, 'orderBy'>;\n\torderBy(\n\t\t...columns:\n\t\t\t| [(aliases: this['_']['selection']) => ValueOrArray<GelColumn | SQL | SQL.Aliased>]\n\t\t\t| (GelColumn | SQL | SQL.Aliased)[]\n\t): GelSelectWithout<this, TDynamic, 'orderBy'> {\n\t\tif (typeof columns[0] === 'function') {\n\t\t\tconst orderBy = columns[0](\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'alias', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\n\t\t\tconst orderByArray = Array.isArray(orderBy) ? orderBy : [orderBy];\n\n\t\t\tif (this.config.setOperators.length > 0) {\n\t\t\t\tthis.config.setOperators.at(-1)!.orderBy = orderByArray;\n\t\t\t} else {\n\t\t\t\tthis.config.orderBy = orderByArray;\n\t\t\t}\n\t\t} else {\n\t\t\tconst orderByArray = columns as (GelColumn | SQL | SQL.Aliased)[];\n\n\t\t\tif (this.config.setOperators.length > 0) {\n\t\t\t\tthis.config.setOperators.at(-1)!.orderBy = orderByArray;\n\t\t\t} else {\n\t\t\t\tthis.config.orderBy = orderByArray;\n\t\t\t}\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `limit` clause to the query.\n\t *\n\t * Calling this method will set the maximum number of rows that will be returned by this query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#limit--offset}\n\t *\n\t * @param limit the `limit` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Get the first 10 people from this query.\n\t * await db.select().from(people).limit(10);\n\t * ```\n\t */\n\tlimit(limit: number | Placeholder): GelSelectWithout<this, TDynamic, 'limit'> {\n\t\tif (this.config.setOperators.length > 0) {\n\t\t\tthis.config.setOperators.at(-1)!.limit = limit;\n\t\t} else {\n\t\t\tthis.config.limit = limit;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `offset` clause to the query.\n\t *\n\t * Calling this method will skip a number of rows when returning results from this query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#limit--offset}\n\t *\n\t * @param offset the `offset` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Get the 10th-20th people from this query.\n\t * await db.select().from(people).offset(10).limit(10);\n\t * ```\n\t */\n\toffset(offset: number | Placeholder): GelSelectWithout<this, TDynamic, 'offset'> {\n\t\tif (this.config.setOperators.length > 0) {\n\t\t\tthis.config.setOperators.at(-1)!.offset = offset;\n\t\t} else {\n\t\t\tthis.config.offset = offset;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `for` clause to the query.\n\t *\n\t * Calling this method will specify a lock strength for this query that controls how strictly it acquires exclusive access to the rows being queried.\n\t *\n\t * See docs: {@link https://www.postgresql.org/docs/current/sql-select.html#SQL-FOR-UPDATE-SHARE}\n\t *\n\t * @param strength the lock strength.\n\t * @param config the lock configuration.\n\t */\n\tfor(strength: LockStrength, config: LockConfig = {}): GelSelectWithout<this, TDynamic, 'for'> {\n\t\tthis.config.lockingClause = { strength, config };\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildSelectQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\tas<TAlias extends string>(\n\t\talias: TAlias,\n\t): SubqueryWithSelection<this['_']['selectedFields'], TAlias> {\n\t\tconst usedTables: string[] = [];\n\t\tusedTables.push(...extractUsedTable(this.config.table));\n\t\tif (this.config.joins) { for (const it of this.config.joins) usedTables.push(...extractUsedTable(it.table)); }\n\n\t\treturn new Proxy(\n\t\t\tnew Subquery(this.getSQL(), this.config.fields, alias, false, [...new Set(usedTables)]),\n\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t) as SubqueryWithSelection<this['_']['selectedFields'], TAlias>;\n\t}\n\n\t/** @internal */\n\toverride getSelectedFields(): this['_']['selectedFields'] {\n\t\treturn new Proxy(\n\t\t\tthis.config.fields,\n\t\t\tnew SelectionProxyHandler({ alias: this.tableName, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t) as this['_']['selectedFields'];\n\t}\n\n\t$dynamic(): GelSelectDynamic<this> {\n\t\treturn this;\n\t}\n}\n\nexport interface GelSelectBase<\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> extends\n\tGelSelectQueryBuilderBase<\n\t\tGelSelectHKT,\n\t\tTTableName,\n\t\tTSelection,\n\t\tTSelectMode,\n\t\tTNullabilityMap,\n\t\tTDynamic,\n\t\tTExcludedMethods,\n\t\tTResult,\n\t\tTSelectedFields\n\t>,\n\tQueryPromise<TResult>,\n\tSQLWrapper\n{}\n\nexport class GelSelectBase<\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> extends GelSelectQueryBuilderBase<\n\tGelSelectHKT,\n\tTTableName,\n\tTSelection,\n\tTSelectMode,\n\tTNullabilityMap,\n\tTDynamic,\n\tTExcludedMethods,\n\tTResult,\n\tTSelectedFields\n> implements RunnableQuery<TResult, 'gel'>, SQLWrapper {\n\tstatic override readonly [entityKind]: string = 'GelSelect';\n\n\t/** @internal */\n\t_prepare(name?: string): GelSelectPrepare<this> {\n\t\tconst { session, config, dialect, joinsNotNullableMap, cacheConfig, usedTables } = this;\n\t\tif (!session) {\n\t\t\tthrow new Error('Cannot execute a query on a query builder. Please use a database instance instead.');\n\t\t}\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\tconst fieldsList = orderSelectedFields<GelColumn>(config.fields);\n\t\t\tconst query = session.prepareQuery<\n\t\t\t\tPreparedQueryConfig & { execute: TResult }\n\t\t\t>(dialect.sqlToQuery(this.getSQL()), fieldsList, name, true, undefined, {\n\t\t\t\ttype: 'select',\n\t\t\t\ttables: [...usedTables],\n\t\t\t}, cacheConfig);\n\t\t\tquery.joinsNotNullableMap = joinsNotNullableMap;\n\n\t\t\treturn query;\n\t\t});\n\t}\n\n\t$withCache(config?: { config?: CacheConfig; tag?: string; autoInvalidate?: boolean } | false) {\n\t\tthis.cacheConfig = config === undefined\n\t\t\t? { config: {}, enable: true, autoInvalidate: true }\n\t\t\t: config === false\n\t\t\t? { enable: false }\n\t\t\t: { enable: true, autoInvalidate: true, ...config };\n\t\treturn this;\n\t}\n\n\t/**\n\t * Create a prepared statement for this query. This allows\n\t * the database to remember this query for the given session\n\t * and call it by name, rather than specifying the full query.\n\t *\n\t * {@link https://www.postgresql.org/docs/current/sql-prepare.html | Postgres prepare documentation}\n\t */\n\tprepare(name: string): GelSelectPrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\texecute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues);\n\t\t});\n\t};\n}\n\napplyMixins(GelSelectBase, [QueryPromise]);\n\nfunction createSetOperator(type: SetOperator, isAll: boolean): GelCreateSetOperatorFn {\n\treturn (leftSelect, rightSelect, ...restSelects) => {\n\t\tconst setOperators = [rightSelect, ...restSelects].map((select) => ({\n\t\t\ttype,\n\t\t\tisAll,\n\t\t\trightSelect: select as AnyGelSelect,\n\t\t}));\n\n\t\tfor (const setOperator of setOperators) {\n\t\t\tif (!haveSameKeys((leftSelect as any).getSelectedFields(), setOperator.rightSelect.getSelectedFields())) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t'Set operator error (union / intersect / except): selected fields are not the same or are in a different order',\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\treturn (leftSelect as AnyGelSelect).addSetOperators(setOperators) as any;\n\t};\n}\n\nconst getGelSetOperators = () => ({\n\tunion,\n\tunionAll,\n\tintersect,\n\tintersectAll,\n\texcept,\n\texceptAll,\n});\n\n/**\n * Adds `union` set operator to the query.\n *\n * Calling this method will combine the result sets of the `select` statements and remove any duplicate rows that appear across them.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#union}\n *\n * @example\n *\n * ```ts\n * // Select all unique names from customers and users tables\n * import { union } from 'drizzle-orm/Gel-core'\n *\n * await union(\n *   db.select({ name: users.name }).from(users),\n *   db.select({ name: customers.name }).from(customers)\n * );\n * // or\n * await db.select({ name: users.name })\n *   .from(users)\n *   .union(\n *     db.select({ name: customers.name }).from(customers)\n *   );\n * ```\n */\nexport const union = createSetOperator('union', false);\n\n/**\n * Adds `union all` set operator to the query.\n *\n * Calling this method will combine the result-set of the `select` statements and keep all duplicate rows that appear across them.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#union-all}\n *\n * @example\n *\n * ```ts\n * // Select all transaction ids from both online and in-store sales\n * import { unionAll } from 'drizzle-orm/Gel-core'\n *\n * await unionAll(\n *   db.select({ transaction: onlineSales.transactionId }).from(onlineSales),\n *   db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n * );\n * // or\n * await db.select({ transaction: onlineSales.transactionId })\n *   .from(onlineSales)\n *   .unionAll(\n *     db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n *   );\n * ```\n */\nexport const unionAll = createSetOperator('union', true);\n\n/**\n * Adds `intersect` set operator to the query.\n *\n * Calling this method will retain only the rows that are present in both result sets and eliminate duplicates.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect}\n *\n * @example\n *\n * ```ts\n * // Select course names that are offered in both departments A and B\n * import { intersect } from 'drizzle-orm/Gel-core'\n *\n * await intersect(\n *   db.select({ courseName: depA.courseName }).from(depA),\n *   db.select({ courseName: depB.courseName }).from(depB)\n * );\n * // or\n * await db.select({ courseName: depA.courseName })\n *   .from(depA)\n *   .intersect(\n *     db.select({ courseName: depB.courseName }).from(depB)\n *   );\n * ```\n */\nexport const intersect = createSetOperator('intersect', false);\n\n/**\n * Adds `intersect all` set operator to the query.\n *\n * Calling this method will retain only the rows that are present in both result sets including all duplicates.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect-all}\n *\n * @example\n *\n * ```ts\n * // Select all products and quantities that are ordered by both regular and VIP customers\n * import { intersectAll } from 'drizzle-orm/Gel-core'\n *\n * await intersectAll(\n *   db.select({\n *     productId: regularCustomerOrders.productId,\n *     quantityOrdered: regularCustomerOrders.quantityOrdered\n *   })\n *   .from(regularCustomerOrders),\n *   db.select({\n *     productId: vipCustomerOrders.productId,\n *     quantityOrdered: vipCustomerOrders.quantityOrdered\n *   })\n *   .from(vipCustomerOrders)\n * );\n * // or\n * await db.select({\n *   productId: regularCustomerOrders.productId,\n *   quantityOrdered: regularCustomerOrders.quantityOrdered\n * })\n * .from(regularCustomerOrders)\n * .intersectAll(\n *   db.select({\n *     productId: vipCustomerOrders.productId,\n *     quantityOrdered: vipCustomerOrders.quantityOrdered\n *   })\n *   .from(vipCustomerOrders)\n * );\n * ```\n */\nexport const intersectAll = createSetOperator('intersect', true);\n\n/**\n * Adds `except` set operator to the query.\n *\n * Calling this method will retrieve all unique rows from the left query, except for the rows that are present in the result set of the right query.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#except}\n *\n * @example\n *\n * ```ts\n * // Select all courses offered in department A but not in department B\n * import { except } from 'drizzle-orm/Gel-core'\n *\n * await except(\n *   db.select({ courseName: depA.courseName }).from(depA),\n *   db.select({ courseName: depB.courseName }).from(depB)\n * );\n * // or\n * await db.select({ courseName: depA.courseName })\n *   .from(depA)\n *   .except(\n *     db.select({ courseName: depB.courseName }).from(depB)\n *   );\n * ```\n */\nexport const except = createSetOperator('except', false);\n\n/**\n * Adds `except all` set operator to the query.\n *\n * Calling this method will retrieve all rows from the left query, except for the rows that are present in the result set of the right query.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#except-all}\n *\n * @example\n *\n * ```ts\n * // Select all products that are ordered by regular customers but not by VIP customers\n * import { exceptAll } from 'drizzle-orm/Gel-core'\n *\n * await exceptAll(\n *   db.select({\n *     productId: regularCustomerOrders.productId,\n *     quantityOrdered: regularCustomerOrders.quantityOrdered\n *   })\n *   .from(regularCustomerOrders),\n *   db.select({\n *     productId: vipCustomerOrders.productId,\n *     quantityOrdered: vipCustomerOrders.quantityOrdered\n *   })\n *   .from(vipCustomerOrders)\n * );\n * // or\n * await db.select({\n *   productId: regularCustomerOrders.productId,\n *   quantityOrdered: regularCustomerOrders.quantityOrdered,\n * })\n * .from(regularCustomerOrders)\n * .exceptAll(\n *   db.select({\n *     productId: vipCustomerOrders.productId,\n *     quantityOrdered: vipCustomerOrders.quantityOrdered,\n *   })\n *   .from(vipCustomerOrders)\n * );\n * ```\n */\nexport const exceptAll = createSetOperator('except', true);\n"], "mappings": "AACA,SAAS,YAAY,UAAU;AAM/B,SAAS,mBAAmB;AAC5B,SAAS,yBAAyB;AAWlC,SAAS,oBAAoB;AAE7B,SAAS,6BAA6B;AACtC,SAAS,KAAK,YAAY;AAE1B,SAAS,gBAAgB;AACzB,SAAS,aAAa;AACtB,SAAS,cAAc;AACvB;AAAA,EACC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OAGM;AACP,SAAS,2BAA2B;AACpC,SAAS,sBAAsB;AAC/B,SAAS,wBAAwB;AAqB1B,MAAM,iBAGX;AAAA,EACD,QAAiB,UAAU,IAAY;AAAA,EAE/B;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAuB,CAAC;AAAA,EACxB;AAAA,EAIR,YACC,QASC;AACD,SAAK,SAAS,OAAO;AACrB,SAAK,UAAU,OAAO;AACtB,SAAK,UAAU,OAAO;AACtB,QAAI,OAAO,UAAU;AACpB,WAAK,WAAW,OAAO;AAAA,IACxB;AACA,SAAK,WAAW,OAAO;AAAA,EACxB;AAAA,EAEQ;AAAA;AAAA,EAER,SAAS,OAAuB;AAC/B,SAAK,YAAY;AACjB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KACC,QAMC;AACD,UAAM,kBAAkB,CAAC,CAAC,KAAK;AAE/B,QAAI;AACJ,QAAI,KAAK,QAAQ;AAChB,eAAS,KAAK;AAAA,IACf,WAAW,GAAG,QAAQ,QAAQ,GAAG;AAEhC,eAAS,OAAO;AAAA,QACf,OAAO,KAAK,OAAO,EAAE,cAAc,EAAE,IAAI,CACxC,QACI,CAAC,KAAK,OAAO,GAAqC,CAAsC,CAAC;AAAA,MAC/F;AAAA,IACD,WAAW,GAAG,QAAQ,WAAW,GAAG;AACnC,eAAS,OAAO,cAAc,EAAE;AAAA,IACjC,WAAW,GAAG,QAAQ,GAAG,GAAG;AAC3B,eAAS,CAAC;AAAA,IACX,OAAO;AACN,eAAS,gBAA0B,MAAM;AAAA,IAC1C;AAEA,WAAO,IAAI,cAAc;AAAA,MACxB,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA,SAAS,KAAK;AAAA,MACd,SAAS,KAAK;AAAA,MACd,UAAU,KAAK;AAAA,MACf,UAAU,KAAK;AAAA,IAChB,CAAC;AAAA,EACF;AACD;AAEO,MAAe,kCAWZ,kBAA4C;AAAA,EACrD,QAA0B,UAAU,IAAY;AAAA,EAE9B;AAAA,EAcR;AAAA,EACA;AAAA,EACF;AAAA,EACA;AAAA,EACE;AAAA,EACA;AAAA,EACA,cAAgC;AAAA,EAChC,aAA0B,oBAAI,IAAI;AAAA,EAE5C,YACC,EAAE,OAAO,QAAQ,iBAAiB,SAAS,SAAS,UAAU,SAAS,GAWtE;AACD,UAAM;AACN,SAAK,SAAS;AAAA,MACb;AAAA,MACA;AAAA,MACA,QAAQ,EAAE,GAAG,OAAO;AAAA,MACpB;AAAA,MACA,cAAc,CAAC;AAAA,IAChB;AACA,SAAK,kBAAkB;AACvB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,IAAI;AAAA,MACR,gBAAgB;AAAA,MAChB,QAAQ,KAAK;AAAA,IACd;AACA,SAAK,YAAY,iBAAiB,KAAK;AACvC,SAAK,sBAAsB,OAAO,KAAK,cAAc,WAAW,EAAE,CAAC,KAAK,SAAS,GAAG,KAAK,IAAI,CAAC;AAC9F,eAAW,QAAQ,iBAAiB,KAAK,EAAG,MAAK,WAAW,IAAI,IAAI;AAAA,EACrE;AAAA;AAAA,EAGA,gBAAgB;AACf,WAAO,CAAC,GAAG,KAAK,UAAU;AAAA,EAC3B;AAAA,EAEQ,WAIP,UACA,SACyD;AACzD,WAAO,CACN,OACA,OACI;AACJ,YAAM,gBAAgB,KAAK;AAC3B,YAAM,YAAY,iBAAiB,KAAK;AAExC,UAAI,OAAO,cAAc,YAAY,KAAK,OAAO,OAAO,KAAK,CAAC,SAAS,KAAK,UAAU,SAAS,GAAG;AACjG,cAAM,IAAI,MAAM,UAAU,SAAS,iCAAiC;AAAA,MACrE;AAGA,iBAAW,QAAQ,iBAAiB,KAAK,EAAG,MAAK,WAAW,IAAI,IAAI;AAEpE,UAAI,CAAC,KAAK,iBAAiB;AAE1B,YAAI,OAAO,KAAK,KAAK,mBAAmB,EAAE,WAAW,KAAK,OAAO,kBAAkB,UAAU;AAC5F,eAAK,OAAO,SAAS;AAAA,YACpB,CAAC,aAAa,GAAG,KAAK,OAAO;AAAA,UAC9B;AAAA,QACD;AACA,YAAI,OAAO,cAAc,YAAY,CAAC,GAAG,OAAO,GAAG,GAAG;AACrD,gBAAM,YAAY,GAAG,OAAO,QAAQ,IACjC,MAAM,EAAE,iBACR,GAAG,OAAO,IAAI,IACd,MAAM,cAAc,EAAE,iBACtB,MAAM,MAAM,OAAO,OAAO;AAC7B,eAAK,OAAO,OAAO,SAAS,IAAI;AAAA,QACjC;AAAA,MACD;AAEA,UAAI,OAAO,OAAO,YAAY;AAC7B,aAAK;AAAA,UACJ,IAAI;AAAA,YACH,KAAK,OAAO;AAAA,YACZ,IAAI,sBAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;AAAA,UAC5E;AAAA,QACD;AAAA,MACD;AAEA,UAAI,CAAC,KAAK,OAAO,OAAO;AACvB,aAAK,OAAO,QAAQ,CAAC;AAAA,MACtB;AAEA,WAAK,OAAO,MAAM,KAAK,EAAE,IAAI,OAAO,UAAU,OAAO,WAAW,QAAQ,CAAC;AAEzE,UAAI,OAAO,cAAc,UAAU;AAClC,gBAAQ,UAAU;AAAA,UACjB,KAAK,QAAQ;AACZ,iBAAK,oBAAoB,SAAS,IAAI;AACtC;AAAA,UACD;AAAA,UACA,KAAK,SAAS;AACb,iBAAK,sBAAsB,OAAO;AAAA,cACjC,OAAO,QAAQ,KAAK,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC;AAAA,YACrE;AACA,iBAAK,oBAAoB,SAAS,IAAI;AACtC;AAAA,UACD;AAAA,UACA,KAAK;AAAA,UACL,KAAK,SAAS;AACb,iBAAK,oBAAoB,SAAS,IAAI;AACtC;AAAA,UACD;AAAA,UACA,KAAK,QAAQ;AACZ,iBAAK,sBAAsB,OAAO;AAAA,cACjC,OAAO,QAAQ,KAAK,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC;AAAA,YACrE;AACA,iBAAK,oBAAoB,SAAS,IAAI;AACtC;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6BA,WAAW,KAAK,WAAW,QAAQ,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcxC,kBAAkB,KAAK,WAAW,QAAQ,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6B9C,YAAY,KAAK,WAAW,SAAS,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6B1C,YAAY,KAAK,WAAW,SAAS,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAc1C,mBAAmB,KAAK,WAAW,SAAS,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6BhD,WAAW,KAAK,WAAW,QAAQ,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4BxC,YAAY,KAAK,WAAW,SAAS,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAa1C,mBAAmB,KAAK,WAAW,SAAS,IAAI;AAAA,EAExC,kBACP,MACA,OAUC;AACD,WAAO,CAAC,mBAAmB;AAC1B,YAAM,cAAe,OAAO,mBAAmB,aAC5C,eAAe,mBAAmB,CAAC,IACnC;AAKH,UAAI,CAAC,aAAa,KAAK,kBAAkB,GAAG,YAAY,kBAAkB,CAAC,GAAG;AAC7E,cAAM,IAAI;AAAA,UACT;AAAA,QACD;AAAA,MACD;AAEA,WAAK,OAAO,aAAa,KAAK,EAAE,MAAM,OAAO,YAAY,CAAC;AAC1D,aAAO;AAAA,IACR;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2BA,QAAQ,KAAK,kBAAkB,SAAS,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2B7C,WAAW,KAAK,kBAAkB,SAAS,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2B/C,YAAY,KAAK,kBAAkB,aAAa,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0CrD,eAAe,KAAK,kBAAkB,aAAa,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2BvD,SAAS,KAAK,kBAAkB,UAAU,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0C/C,YAAY,KAAK,kBAAkB,UAAU,IAAI;AAAA;AAAA,EAGjD,gBAAgB,cAKd;AACD,SAAK,OAAO,aAAa,KAAK,GAAG,YAAY;AAC7C,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+BA,MACC,OAC4C;AAC5C,QAAI,OAAO,UAAU,YAAY;AAChC,cAAQ;AAAA,QACP,IAAI;AAAA,UACH,KAAK,OAAO;AAAA,UACZ,IAAI,sBAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;AAAA,QAC5E;AAAA,MACD;AAAA,IACD;AACA,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBA,OACC,QAC6C;AAC7C,QAAI,OAAO,WAAW,YAAY;AACjC,eAAS;AAAA,QACR,IAAI;AAAA,UACH,KAAK,OAAO;AAAA,UACZ,IAAI,sBAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;AAAA,QAC5E;AAAA,MACD;AAAA,IACD;AACA,SAAK,OAAO,SAAS;AACrB,WAAO;AAAA,EACR;AAAA,EAyBA,WACI,SAG2C;AAC9C,QAAI,OAAO,QAAQ,CAAC,MAAM,YAAY;AACrC,YAAM,UAAU,QAAQ,CAAC;AAAA,QACxB,IAAI;AAAA,UACH,KAAK,OAAO;AAAA,UACZ,IAAI,sBAAsB,EAAE,oBAAoB,SAAS,aAAa,MAAM,CAAC;AAAA,QAC9E;AAAA,MACD;AACA,WAAK,OAAO,UAAU,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAAA,IAClE,OAAO;AACN,WAAK,OAAO,UAAU;AAAA,IACvB;AACA,WAAO;AAAA,EACR;AAAA,EA8BA,WACI,SAG2C;AAC9C,QAAI,OAAO,QAAQ,CAAC,MAAM,YAAY;AACrC,YAAM,UAAU,QAAQ,CAAC;AAAA,QACxB,IAAI;AAAA,UACH,KAAK,OAAO;AAAA,UACZ,IAAI,sBAAsB,EAAE,oBAAoB,SAAS,aAAa,MAAM,CAAC;AAAA,QAC9E;AAAA,MACD;AAEA,YAAM,eAAe,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAEhE,UAAI,KAAK,OAAO,aAAa,SAAS,GAAG;AACxC,aAAK,OAAO,aAAa,GAAG,EAAE,EAAG,UAAU;AAAA,MAC5C,OAAO;AACN,aAAK,OAAO,UAAU;AAAA,MACvB;AAAA,IACD,OAAO;AACN,YAAM,eAAe;AAErB,UAAI,KAAK,OAAO,aAAa,SAAS,GAAG;AACxC,aAAK,OAAO,aAAa,GAAG,EAAE,EAAG,UAAU;AAAA,MAC5C,OAAO;AACN,aAAK,OAAO,UAAU;AAAA,MACvB;AAAA,IACD;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,MAAM,OAAwE;AAC7E,QAAI,KAAK,OAAO,aAAa,SAAS,GAAG;AACxC,WAAK,OAAO,aAAa,GAAG,EAAE,EAAG,QAAQ;AAAA,IAC1C,OAAO;AACN,WAAK,OAAO,QAAQ;AAAA,IACrB;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,OAAO,QAA0E;AAChF,QAAI,KAAK,OAAO,aAAa,SAAS,GAAG;AACxC,WAAK,OAAO,aAAa,GAAG,EAAE,EAAG,SAAS;AAAA,IAC3C,OAAO;AACN,WAAK,OAAO,SAAS;AAAA,IACtB;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,IAAI,UAAwB,SAAqB,CAAC,GAA4C;AAC7F,SAAK,OAAO,gBAAgB,EAAE,UAAU,OAAO;AAC/C,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;AAAA,EACjD;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA,EAEA,GACC,OAC6D;AAC7D,UAAM,aAAuB,CAAC;AAC9B,eAAW,KAAK,GAAG,iBAAiB,KAAK,OAAO,KAAK,CAAC;AACtD,QAAI,KAAK,OAAO,OAAO;AAAE,iBAAW,MAAM,KAAK,OAAO,MAAO,YAAW,KAAK,GAAG,iBAAiB,GAAG,KAAK,CAAC;AAAA,IAAG;AAE7G,WAAO,IAAI;AAAA,MACV,IAAI,SAAS,KAAK,OAAO,GAAG,KAAK,OAAO,QAAQ,OAAO,OAAO,CAAC,GAAG,IAAI,IAAI,UAAU,CAAC,CAAC;AAAA,MACtF,IAAI,sBAAsB,EAAE,OAAO,oBAAoB,SAAS,aAAa,QAAQ,CAAC;AAAA,IACvF;AAAA,EACD;AAAA;AAAA,EAGS,oBAAiD;AACzD,WAAO,IAAI;AAAA,MACV,KAAK,OAAO;AAAA,MACZ,IAAI,sBAAsB,EAAE,OAAO,KAAK,WAAW,oBAAoB,SAAS,aAAa,QAAQ,CAAC;AAAA,IACvG;AAAA,EACD;AAAA,EAEA,WAAmC;AAClC,WAAO;AAAA,EACR;AACD;AA4BO,MAAM,sBAUH,0BAU6C;AAAA,EACtD,QAA0B,UAAU,IAAY;AAAA;AAAA,EAGhD,SAAS,MAAuC;AAC/C,UAAM,EAAE,SAAS,QAAQ,SAAS,qBAAqB,aAAa,WAAW,IAAI;AACnF,QAAI,CAAC,SAAS;AACb,YAAM,IAAI,MAAM,oFAAoF;AAAA,IACrG;AACA,WAAO,OAAO,gBAAgB,wBAAwB,MAAM;AAC3D,YAAM,aAAa,oBAA+B,OAAO,MAAM;AAC/D,YAAM,QAAQ,QAAQ,aAEpB,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,YAAY,MAAM,MAAM,QAAW;AAAA,QACvE,MAAM;AAAA,QACN,QAAQ,CAAC,GAAG,UAAU;AAAA,MACvB,GAAG,WAAW;AACd,YAAM,sBAAsB;AAE5B,aAAO;AAAA,IACR,CAAC;AAAA,EACF;AAAA,EAEA,WAAW,QAAmF;AAC7F,SAAK,cAAc,WAAW,SAC3B,EAAE,QAAQ,CAAC,GAAG,QAAQ,MAAM,gBAAgB,KAAK,IACjD,WAAW,QACX,EAAE,QAAQ,MAAM,IAChB,EAAE,QAAQ,MAAM,gBAAgB,MAAM,GAAG,OAAO;AACnD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,MAAsC;AAC7C,WAAO,KAAK,SAAS,IAAI;AAAA,EAC1B;AAAA,EAEA,UAAkD,CAAC,sBAAsB;AACxE,WAAO,OAAO,gBAAgB,qBAAqB,MAAM;AACxD,aAAO,KAAK,SAAS,EAAE,QAAQ,iBAAiB;AAAA,IACjD,CAAC;AAAA,EACF;AACD;AAEA,YAAY,eAAe,CAAC,YAAY,CAAC;AAEzC,SAAS,kBAAkB,MAAmB,OAAwC;AACrF,SAAO,CAAC,YAAY,gBAAgB,gBAAgB;AACnD,UAAM,eAAe,CAAC,aAAa,GAAG,WAAW,EAAE,IAAI,CAAC,YAAY;AAAA,MACnE;AAAA,MACA;AAAA,MACA,aAAa;AAAA,IACd,EAAE;AAEF,eAAW,eAAe,cAAc;AACvC,UAAI,CAAC,aAAc,WAAmB,kBAAkB,GAAG,YAAY,YAAY,kBAAkB,CAAC,GAAG;AACxG,cAAM,IAAI;AAAA,UACT;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAQ,WAA4B,gBAAgB,YAAY;AAAA,EACjE;AACD;AAEA,MAAM,qBAAqB,OAAO;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AA2BO,MAAM,QAAQ,kBAAkB,SAAS,KAAK;AA2B9C,MAAM,WAAW,kBAAkB,SAAS,IAAI;AA2BhD,MAAM,YAAY,kBAAkB,aAAa,KAAK;AA0CtD,MAAM,eAAe,kBAAkB,aAAa,IAAI;AA2BxD,MAAM,SAAS,kBAAkB,UAAU,KAAK;AA0ChD,MAAM,YAAY,kBAAkB,UAAU,IAAI;", "names": []}