{"version": 3, "sources": ["../../../src/gel-core/query-builders/select.types.ts"], "sourcesContent": ["import type { GelColumn } from '~/gel-core/columns/index.ts';\nimport type { GelTable, GelTableWithColumns } from '~/gel-core/table.ts';\nimport type { GelViewBase } from '~/gel-core/view-base.ts';\nimport type { GelViewWithSelection } from '~/gel-core/view.ts';\nimport type {\n\tSelectedFields as SelectedFieldsBase,\n\tSelectedFieldsFlat as SelectedFieldsFlatBase,\n\tSelectedFieldsOrdered as SelectedFieldsOrderedBase,\n} from '~/operations.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type {\n\tAppendToNullabilityMap,\n\tAppendToResult,\n\tBuildSubquerySelection,\n\tGetSelectTableName,\n\tJoinNullability,\n\tJoinType,\n\tMapColumnsToTableAlias,\n\tSelectMode,\n\tSelectResult,\n\tSetOperator,\n} from '~/query-builders/select.types.ts';\nimport type { ColumnsSelection, Placeholder, SQL, SQLWrapper, View } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport type { Table, UpdateTableConfig } from '~/table.ts';\nimport type { Assume, ValidateShape, ValueOrArray } from '~/utils.ts';\nimport type { GelPreparedQuery, PreparedQueryConfig } from '../session.ts';\nimport type { GelSelectBase, GelSelectQueryBuilderBase } from './select.ts';\n\nexport interface GelSelectJoinConfig {\n\ton: SQL | undefined;\n\ttable: GelTable | Subquery | GelViewBase | SQL;\n\talias: string | undefined;\n\tjoinType: JoinType;\n\tlateral?: boolean;\n}\n\nexport type BuildAliasTable<TTable extends GelTable | View, TAlias extends string> = TTable extends Table\n\t? GelTableWithColumns<\n\t\tUpdateTableConfig<TTable['_']['config'], {\n\t\t\tname: TAlias;\n\t\t\tcolumns: MapColumnsToTableAlias<TTable['_']['columns'], TAlias, 'gel'>;\n\t\t}>\n\t>\n\t: TTable extends View ? GelViewWithSelection<\n\t\t\tTAlias,\n\t\t\tTTable['_']['existing'],\n\t\t\tMapColumnsToTableAlias<TTable['_']['selectedFields'], TAlias, 'gel'>\n\t\t>\n\t: never;\n\nexport interface GelSelectConfig {\n\twithList?: Subquery[];\n\t// Either fields or fieldsFlat must be defined\n\tfields: Record<string, unknown>;\n\tfieldsFlat?: SelectedFieldsOrdered;\n\twhere?: SQL;\n\thaving?: SQL;\n\ttable: GelTable | Subquery | GelViewBase | SQL;\n\tlimit?: number | Placeholder;\n\toffset?: number | Placeholder;\n\tjoins?: GelSelectJoinConfig[];\n\torderBy?: (GelColumn | SQL | SQL.Aliased)[];\n\tgroupBy?: (GelColumn | SQL | SQL.Aliased)[];\n\tlockingClause?: {\n\t\tstrength: LockStrength;\n\t\tconfig: LockConfig;\n\t};\n\tdistinct?: boolean | {\n\t\ton: (GelColumn | SQLWrapper)[];\n\t};\n\tsetOperators: {\n\t\trightSelect: TypedQueryBuilder<any, any>;\n\t\ttype: SetOperator;\n\t\tisAll: boolean;\n\t\torderBy?: (GelColumn | SQL | SQL.Aliased)[];\n\t\tlimit?: number | Placeholder;\n\t\toffset?: number | Placeholder;\n\t}[];\n}\n\nexport type GelSelectJoin<\n\tT extends AnyGelSelectQueryBuilder,\n\tTDynamic extends boolean,\n\tTJoinType extends JoinType,\n\tTJoinedTable extends GelTable | Subquery | GelViewBase | SQL,\n\tTJoinedName extends GetSelectTableName<TJoinedTable> = GetSelectTableName<TJoinedTable>,\n> = T extends any ? GelSelectWithout<\n\t\tGelSelectKind<\n\t\t\tT['_']['hkt'],\n\t\t\tT['_']['tableName'],\n\t\t\tAppendToResult<\n\t\t\t\tT['_']['tableName'],\n\t\t\t\tT['_']['selection'],\n\t\t\t\tTJoinedName,\n\t\t\t\tTJoinedTable extends Table ? TJoinedTable['_']['columns']\n\t\t\t\t\t: TJoinedTable extends Subquery ? Assume<TJoinedTable['_']['selectedFields'], SelectedFields>\n\t\t\t\t\t: never,\n\t\t\t\tT['_']['selectMode']\n\t\t\t>,\n\t\t\tT['_']['selectMode'] extends 'partial' ? T['_']['selectMode'] : 'multiple',\n\t\t\tAppendToNullabilityMap<T['_']['nullabilityMap'], TJoinedName, TJoinType>,\n\t\t\tT['_']['dynamic'],\n\t\t\tT['_']['excludedMethods']\n\t\t>,\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>\n\t: never;\n\nexport type GelSelectJoinFn<\n\tT extends AnyGelSelectQueryBuilder,\n\tTDynamic extends boolean,\n\tTJoinType extends JoinType,\n\tTIsLateral extends boolean,\n> = 'cross' extends TJoinType ? <\n\t\tTJoinedTable extends (TIsLateral extends true ? Subquery | SQL : GelTable | Subquery | GelViewBase | SQL),\n\t\tTJoinedName extends GetSelectTableName<TJoinedTable> = GetSelectTableName<TJoinedTable>,\n\t>(table: TJoinedTable) => GelSelectJoin<T, TDynamic, TJoinType, TJoinedTable, TJoinedName>\n\t: <\n\t\tTJoinedTable extends (TIsLateral extends true ? Subquery | SQL : GelTable | Subquery | GelViewBase | SQL),\n\t\tTJoinedName extends GetSelectTableName<TJoinedTable> = GetSelectTableName<TJoinedTable>,\n\t>(\n\t\ttable: TJoinedTable,\n\t\ton: ((aliases: T['_']['selection']) => SQL | undefined) | SQL | undefined,\n\t) => GelSelectJoin<T, TDynamic, TJoinType, TJoinedTable, TJoinedName>;\n\nexport type SelectedFieldsFlat = SelectedFieldsFlatBase<GelColumn>;\n\nexport type SelectedFields = SelectedFieldsBase<GelColumn, GelTable>;\n\nexport type SelectedFieldsOrdered = SelectedFieldsOrderedBase<GelColumn>;\n\nexport type LockStrength = 'update' | 'no key update' | 'share' | 'key share';\n\nexport type LockConfig =\n\t& {\n\t\tof?: ValueOrArray<GelTable>;\n\t}\n\t& ({\n\t\tnoWait: true;\n\t\tskipLocked?: undefined;\n\t} | {\n\t\tnoWait?: undefined;\n\t\tskipLocked: true;\n\t} | {\n\t\tnoWait?: undefined;\n\t\tskipLocked?: undefined;\n\t});\n\nexport interface GelSelectHKTBase {\n\ttableName: string | undefined;\n\tselection: unknown;\n\tselectMode: SelectMode;\n\tnullabilityMap: unknown;\n\tdynamic: boolean;\n\texcludedMethods: string;\n\tresult: unknown;\n\tselectedFields: unknown;\n\t_type: unknown;\n}\n\nexport type GelSelectKind<\n\tT extends GelSelectHKTBase,\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability>,\n\tTDynamic extends boolean,\n\tTExcludedMethods extends string,\n\tTResult = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> = (T & {\n\ttableName: TTableName;\n\tselection: TSelection;\n\tselectMode: TSelectMode;\n\tnullabilityMap: TNullabilityMap;\n\tdynamic: TDynamic;\n\texcludedMethods: TExcludedMethods;\n\tresult: TResult;\n\tselectedFields: TSelectedFields;\n})['_type'];\n\nexport interface GelSelectQueryBuilderHKT extends GelSelectHKTBase {\n\t_type: GelSelectQueryBuilderBase<\n\t\tGelSelectQueryBuilderHKT,\n\t\tthis['tableName'],\n\t\tAssume<this['selection'], ColumnsSelection>,\n\t\tthis['selectMode'],\n\t\tAssume<this['nullabilityMap'], Record<string, JoinNullability>>,\n\t\tthis['dynamic'],\n\t\tthis['excludedMethods'],\n\t\tAssume<this['result'], any[]>,\n\t\tAssume<this['selectedFields'], ColumnsSelection>\n\t>;\n}\n\nexport interface GelSelectHKT extends GelSelectHKTBase {\n\t_type: GelSelectBase<\n\t\tthis['tableName'],\n\t\tAssume<this['selection'], ColumnsSelection>,\n\t\tthis['selectMode'],\n\t\tAssume<this['nullabilityMap'], Record<string, JoinNullability>>,\n\t\tthis['dynamic'],\n\t\tthis['excludedMethods'],\n\t\tAssume<this['result'], any[]>,\n\t\tAssume<this['selectedFields'], ColumnsSelection>\n\t>;\n}\n\nexport type CreateGelSelectFromBuilderMode<\n\tTBuilderMode extends 'db' | 'qb',\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n> = TBuilderMode extends 'db' ? GelSelectBase<TTableName, TSelection, TSelectMode>\n\t: GelSelectQueryBuilderBase<GelSelectQueryBuilderHKT, TTableName, TSelection, TSelectMode>;\n\nexport type GelSetOperatorExcludedMethods =\n\t| 'leftJoin'\n\t| 'rightJoin'\n\t| 'innerJoin'\n\t| 'fullJoin'\n\t| 'where'\n\t| 'having'\n\t| 'groupBy'\n\t| 'for';\n\nexport type GelSelectWithout<\n\tT extends AnyGelSelectQueryBuilder,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n\tTResetExcluded extends boolean = false,\n> = TDynamic extends true ? T : Omit<\n\tGelSelectKind<\n\t\tT['_']['hkt'],\n\t\tT['_']['tableName'],\n\t\tT['_']['selection'],\n\t\tT['_']['selectMode'],\n\t\tT['_']['nullabilityMap'],\n\t\tTDynamic,\n\t\tTResetExcluded extends true ? K : T['_']['excludedMethods'] | K,\n\t\tT['_']['result'],\n\t\tT['_']['selectedFields']\n\t>,\n\tTResetExcluded extends true ? K : T['_']['excludedMethods'] | K\n>;\n\nexport type GelSelectPrepare<T extends AnyGelSelect> = GelPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['result'];\n\t}\n>;\n\nexport type GelSelectDynamic<T extends AnyGelSelectQueryBuilder> = GelSelectKind<\n\tT['_']['hkt'],\n\tT['_']['tableName'],\n\tT['_']['selection'],\n\tT['_']['selectMode'],\n\tT['_']['nullabilityMap'],\n\ttrue,\n\tnever,\n\tT['_']['result'],\n\tT['_']['selectedFields']\n>;\n\nexport type GelSelectQueryBuilder<\n\tTHKT extends GelSelectHKTBase = GelSelectQueryBuilderHKT,\n\tTTableName extends string | undefined = string | undefined,\n\tTSelection extends ColumnsSelection = ColumnsSelection,\n\tTSelectMode extends SelectMode = SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<string, JoinNullability>,\n\tTResult extends any[] = unknown[],\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> = GelSelectQueryBuilderBase<\n\tTHKT,\n\tTTableName,\n\tTSelection,\n\tTSelectMode,\n\tTNullabilityMap,\n\ttrue,\n\tnever,\n\tTResult,\n\tTSelectedFields\n>;\n\nexport type AnyGelSelectQueryBuilder = GelSelectQueryBuilderBase<any, any, any, any, any, any, any, any, any>;\n\nexport type AnyGelSetOperatorInterface = GelSetOperatorInterface<any, any, any, any, any, any, any, any>;\n\nexport interface GelSetOperatorInterface<\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> {\n\t_: {\n\t\treadonly hkt: GelSelectHKT;\n\t\treadonly tableName: TTableName;\n\t\treadonly selection: TSelection;\n\t\treadonly selectMode: TSelectMode;\n\t\treadonly nullabilityMap: TNullabilityMap;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TResult;\n\t\treadonly selectedFields: TSelectedFields;\n\t};\n}\n\nexport type GelSetOperatorWithResult<TResult extends any[]> = GelSetOperatorInterface<\n\tany,\n\tany,\n\tany,\n\tany,\n\tany,\n\tany,\n\tTResult,\n\tany\n>;\n\nexport type GelSelect<\n\tTTableName extends string | undefined = string | undefined,\n\tTSelection extends ColumnsSelection = Record<string, any>,\n\tTSelectMode extends SelectMode = SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<string, JoinNullability>,\n> = GelSelectBase<TTableName, TSelection, TSelectMode, TNullabilityMap, true, never>;\n\nexport type AnyGelSelect = GelSelectBase<any, any, any, any, any, any, any, any>;\n\nexport type GelSetOperator<\n\tTTableName extends string | undefined = string | undefined,\n\tTSelection extends ColumnsSelection = Record<string, any>,\n\tTSelectMode extends SelectMode = SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<string, JoinNullability>,\n> = GelSelectBase<\n\tTTableName,\n\tTSelection,\n\tTSelectMode,\n\tTNullabilityMap,\n\ttrue,\n\tGelSetOperatorExcludedMethods\n>;\n\nexport type SetOperatorRightSelect<\n\tTValue extends GelSetOperatorWithResult<TResult>,\n\tTResult extends any[],\n> = TValue extends GelSetOperatorInterface<any, any, any, any, any, any, infer TValueResult, any> ? ValidateShape<\n\t\tTValueResult[number],\n\t\tTResult[number],\n\t\tTypedQueryBuilder<any, TValueResult>\n\t>\n\t: TValue;\n\nexport type SetOperatorRestSelect<\n\tTValue extends readonly GelSetOperatorWithResult<TResult>[],\n\tTResult extends any[],\n> = TValue extends [infer First, ...infer Rest]\n\t? First extends GelSetOperatorInterface<any, any, any, any, any, any, infer TValueResult, any>\n\t\t? Rest extends AnyGelSetOperatorInterface[] ? [\n\t\t\t\tValidateShape<TValueResult[number], TResult[number], TypedQueryBuilder<any, TValueResult>>,\n\t\t\t\t...SetOperatorRestSelect<Rest, TResult>,\n\t\t\t]\n\t\t: ValidateShape<TValueResult[number], TResult[number], TypedQueryBuilder<any, TValueResult>[]>\n\t: never\n\t: TValue;\n\nexport type GelCreateSetOperatorFn = <\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTValue extends GelSetOperatorWithResult<TResult>,\n\tTRest extends GelSetOperatorWithResult<TResult>[],\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n>(\n\tleftSelect: GelSetOperatorInterface<\n\t\tTTableName,\n\t\tTSelection,\n\t\tTSelectMode,\n\t\tTNullabilityMap,\n\t\tTDynamic,\n\t\tTExcludedMethods,\n\t\tTResult,\n\t\tTSelectedFields\n\t>,\n\trightSelect: SetOperatorRightSelect<TValue, TResult>,\n\t...restSelects: SetOperatorRestSelect<TRest, TResult>\n) => GelSelectWithout<\n\tGelSelectBase<\n\t\tTTableName,\n\t\tTSelection,\n\t\tTSelectMode,\n\t\tTNullabilityMap,\n\t\tTDynamic,\n\t\tTExcludedMethods,\n\t\tTResult,\n\t\tTSelectedFields\n\t>,\n\tfalse,\n\tGelSetOperatorExcludedMethods,\n\ttrue\n>;\n\nexport type GetGelSetOperators = {\n\tunion: GelCreateSetOperatorFn;\n\tintersect: GelCreateSetOperatorFn;\n\texcept: GelCreateSetOperatorFn;\n\tunionAll: GelCreateSetOperatorFn;\n\tintersectAll: GelCreateSetOperatorFn;\n\texceptAll: GelCreateSetOperatorFn;\n};\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}