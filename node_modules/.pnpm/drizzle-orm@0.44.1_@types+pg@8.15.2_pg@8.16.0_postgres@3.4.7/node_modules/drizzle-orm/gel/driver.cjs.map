{"version": 3, "sources": ["../../src/gel/driver.ts"], "sourcesContent": ["import { type Client, type ConnectOptions, createClient } from 'gel';\nimport type { Cache } from '~/cache/core/index.ts';\nimport { entityKind } from '~/entity.ts';\nimport { GelDatabase } from '~/gel-core/db.ts';\nimport { GelDialect } from '~/gel-core/dialect.ts';\nimport type { GelQueryResultHKT } from '~/gel-core/session.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { type DrizzleConfig, isConfig } from '~/utils.ts';\nimport type { GelClient } from './session.ts';\nimport { GelDbSession } from './session.ts';\n\nexport interface GelDriverOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n}\n\nexport class GelDriver {\n\tstatic readonly [entityKind]: string = 'GelDriver';\n\n\tconstructor(\n\t\tprivate client: GelClient,\n\t\tprivate dialect: GelDialect,\n\t\tprivate options: GelDriverOptions = {},\n\t) {}\n\n\tcreateSession(\n\t\tschema: RelationalSchemaConfig<TablesRelationalConfig> | undefined,\n\t): GelDbSession<Record<string, unknown>, TablesRelationalConfig> {\n\t\treturn new GelDbSession(this.client, this.dialect, schema, {\n\t\t\tlogger: this.options.logger,\n\t\t\tcache: this.options.cache,\n\t\t});\n\t}\n}\n\nexport class GelJsDatabase<TSchema extends Record<string, unknown> = Record<string, never>>\n\textends GelDatabase<GelQueryResultHKT, TSchema>\n{\n\tstatic override readonly [entityKind]: string = 'GelJsDatabase';\n}\n\nfunction construct<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends GelClient = GelClient,\n>(\n\tclient: TClient,\n\tconfig: DrizzleConfig<TSchema> = {},\n): GelJsDatabase<TSchema> & {\n\t$client: TClient;\n} {\n\tconst dialect = new GelDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(config.schema, createTableRelationsHelpers);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst driver = new GelDriver(client, dialect, { logger, cache: config.cache });\n\tconst session = driver.createSession(schema);\n\tconst db = new GelJsDatabase(dialect, session, schema as any) as GelJsDatabase<TSchema>;\n\t(<any> db).$client = client;\n\t(<any> db).$cache = config.cache;\n\tif ((<any> db).$cache) {\n\t\t(<any> db).$cache['invalidate'] = config.cache?.onMutate;\n\t}\n\n\treturn db as any;\n}\n\nexport function drizzle<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends GelClient = Client,\n>(\n\t...params:\n\t\t| [TClient | string]\n\t\t| [TClient | string, DrizzleConfig<TSchema>]\n\t\t| [\n\t\t\t& DrizzleConfig<TSchema>\n\t\t\t& (\n\t\t\t\t| {\n\t\t\t\t\tconnection: string | ConnectOptions;\n\t\t\t\t}\n\t\t\t\t| {\n\t\t\t\t\tclient: TClient;\n\t\t\t\t}\n\t\t\t),\n\t\t]\n): GelJsDatabase<TSchema> & {\n\t$client: TClient;\n} {\n\tif (typeof params[0] === 'string') {\n\t\tconst instance = createClient({ dsn: params[0] });\n\n\t\treturn construct(instance, params[1] as DrizzleConfig<TSchema> | undefined) as any;\n\t}\n\n\tif (isConfig(params[0])) {\n\t\tconst { connection, client, ...drizzleConfig } = params[0] as (\n\t\t\t& ({ connection?: ConnectOptions | string; client?: TClient })\n\t\t\t& DrizzleConfig<TSchema>\n\t\t);\n\n\t\tif (client) return construct(client, drizzleConfig);\n\n\t\tconst instance = createClient(connection);\n\n\t\treturn construct(instance, drizzleConfig) as any;\n\t}\n\n\treturn construct(params[0] as TClient, params[1] as DrizzleConfig<TSchema> | undefined) as any;\n}\n\nexport namespace drizzle {\n\texport function mock<TSchema extends Record<string, unknown> = Record<string, never>>(\n\t\tconfig?: DrizzleConfig<TSchema>,\n\t): GelJsDatabase<TSchema> & {\n\t\t$client: '$client is not available on drizzle.mock()';\n\t} {\n\t\treturn construct({} as any, config) as any;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAA+D;AAE/D,oBAA2B;AAC3B,gBAA4B;AAC5B,qBAA2B;AAG3B,oBAA8B;AAC9B,uBAKO;AACP,mBAA6C;AAE7C,qBAA6B;AAOtB,MAAM,UAAU;AAAA,EAGtB,YACS,QACA,SACA,UAA4B,CAAC,GACpC;AAHO;AACA;AACA;AAAA,EACN;AAAA,EANH,QAAiB,wBAAU,IAAY;AAAA,EAQvC,cACC,QACgE;AAChE,WAAO,IAAI,4BAAa,KAAK,QAAQ,KAAK,SAAS,QAAQ;AAAA,MAC1D,QAAQ,KAAK,QAAQ;AAAA,MACrB,OAAO,KAAK,QAAQ;AAAA,IACrB,CAAC;AAAA,EACF;AACD;AAEO,MAAM,sBACJ,sBACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AACjD;AAEA,SAAS,UAIR,QACA,SAAiC,CAAC,GAGjC;AACD,QAAM,UAAU,IAAI,0BAAW,EAAE,QAAQ,OAAO,OAAO,CAAC;AACxD,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,4BAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,mBAAe,gDAA8B,OAAO,QAAQ,4CAA2B;AAC7F,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,SAAS,IAAI,UAAU,QAAQ,SAAS,EAAE,QAAQ,OAAO,OAAO,MAAM,CAAC;AAC7E,QAAM,UAAU,OAAO,cAAc,MAAM;AAC3C,QAAM,KAAK,IAAI,cAAc,SAAS,SAAS,MAAa;AAC5D,EAAO,GAAI,UAAU;AACrB,EAAO,GAAI,SAAS,OAAO;AAC3B,MAAW,GAAI,QAAQ;AACtB,IAAO,GAAI,OAAO,YAAY,IAAI,OAAO,OAAO;AAAA,EACjD;AAEA,SAAO;AACR;AAEO,SAAS,WAIZ,QAgBF;AACD,MAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AAClC,UAAM,eAAW,yBAAa,EAAE,KAAK,OAAO,CAAC,EAAE,CAAC;AAEhD,WAAO,UAAU,UAAU,OAAO,CAAC,CAAuC;AAAA,EAC3E;AAEA,UAAI,uBAAS,OAAO,CAAC,CAAC,GAAG;AACxB,UAAM,EAAE,YAAY,QAAQ,GAAG,cAAc,IAAI,OAAO,CAAC;AAKzD,QAAI,OAAQ,QAAO,UAAU,QAAQ,aAAa;AAElD,UAAM,eAAW,yBAAa,UAAU;AAExC,WAAO,UAAU,UAAU,aAAa;AAAA,EACzC;AAEA,SAAO,UAAU,OAAO,CAAC,GAAc,OAAO,CAAC,CAAuC;AACvF;AAAA,CAEO,CAAUA,aAAV;AACC,WAAS,KACf,QAGC;AACD,WAAO,UAAU,CAAC,GAAU,MAAM;AAAA,EACnC;AANO,EAAAA,SAAS;AAAA,GADA;", "names": ["drizzle"]}