{"version": 3, "sources": ["../../../src/mysql-core/columns/decimal.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { MySqlColumnBuilderWithAutoIncrement, MySqlColumnWithAutoIncrement } from './common.ts';\n\nexport type MySqlDecimalBuilderInitial<TName extends string> = MySqlDecimalBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'MySqlDecimal';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlDecimalBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'MySqlDecimal'>,\n> extends MySqlColumnBuilderWithAutoIncrement<T, MySqlDecimalConfig> {\n\tstatic override readonly [entityKind]: string = 'MySqlDecimalBuilder';\n\n\tconstructor(name: T['name'], config: MySqlDecimalConfig | undefined) {\n\t\tsuper(name, 'string', 'MySqlDecimal');\n\t\tthis.config.precision = config?.precision;\n\t\tthis.config.scale = config?.scale;\n\t\tthis.config.unsigned = config?.unsigned;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlDecimal<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlDecimal<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlDecimal<T extends ColumnBaseConfig<'string', 'MySqlDecimal'>>\n\textends MySqlColumnWithAutoIncrement<T, MySqlDecimalConfig>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlDecimal';\n\n\treadonly precision: number | undefined = this.config.precision;\n\treadonly scale: number | undefined = this.config.scale;\n\treadonly unsigned: boolean | undefined = this.config.unsigned;\n\n\toverride mapFromDriverValue(value: unknown): string {\n\t\tif (typeof value === 'string') return value;\n\n\t\treturn String(value);\n\t}\n\n\tgetSQLType(): string {\n\t\tlet type = '';\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\ttype += `decimal(${this.precision},${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\ttype += 'decimal';\n\t\t} else {\n\t\t\ttype += `decimal(${this.precision})`;\n\t\t}\n\t\ttype = type === 'decimal(10,0)' || type === 'decimal(10)' ? 'decimal' : type;\n\t\treturn this.unsigned ? `${type} unsigned` : type;\n\t}\n}\n\nexport type MySqlDecimalNumberBuilderInitial<TName extends string> = MySqlDecimalNumberBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'MySqlDecimalNumber';\n\tdata: number;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlDecimalNumberBuilder<\n\tT extends ColumnBuilderBaseConfig<'number', 'MySqlDecimalNumber'>,\n> extends MySqlColumnBuilderWithAutoIncrement<T, MySqlDecimalConfig> {\n\tstatic override readonly [entityKind]: string = 'MySqlDecimalNumberBuilder';\n\n\tconstructor(name: T['name'], config: MySqlDecimalConfig | undefined) {\n\t\tsuper(name, 'number', 'MySqlDecimalNumber');\n\t\tthis.config.precision = config?.precision;\n\t\tthis.config.scale = config?.scale;\n\t\tthis.config.unsigned = config?.unsigned;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlDecimalNumber<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlDecimalNumber<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlDecimalNumber<T extends ColumnBaseConfig<'number', 'MySqlDecimalNumber'>>\n\textends MySqlColumnWithAutoIncrement<T, MySqlDecimalConfig>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlDecimalNumber';\n\n\treadonly precision: number | undefined = this.config.precision;\n\treadonly scale: number | undefined = this.config.scale;\n\treadonly unsigned: boolean | undefined = this.config.unsigned;\n\n\toverride mapFromDriverValue(value: unknown): number {\n\t\tif (typeof value === 'number') return value;\n\n\t\treturn Number(value);\n\t}\n\n\toverride mapToDriverValue = String;\n\n\tgetSQLType(): string {\n\t\tlet type = '';\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\ttype += `decimal(${this.precision},${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\ttype += 'decimal';\n\t\t} else {\n\t\t\ttype += `decimal(${this.precision})`;\n\t\t}\n\t\ttype = type === 'decimal(10,0)' || type === 'decimal(10)' ? 'decimal' : type;\n\t\treturn this.unsigned ? `${type} unsigned` : type;\n\t}\n}\n\nexport type MySqlDecimalBigIntBuilderInitial<TName extends string> = MySqlDecimalBigIntBuilder<{\n\tname: TName;\n\tdataType: 'bigint';\n\tcolumnType: 'MySqlDecimalBigInt';\n\tdata: bigint;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class MySqlDecimalBigIntBuilder<\n\tT extends ColumnBuilderBaseConfig<'bigint', 'MySqlDecimalBigInt'>,\n> extends MySqlColumnBuilderWithAutoIncrement<T, MySqlDecimalConfig> {\n\tstatic override readonly [entityKind]: string = 'MySqlDecimalBigIntBuilder';\n\n\tconstructor(name: T['name'], config: MySqlDecimalConfig | undefined) {\n\t\tsuper(name, 'bigint', 'MySqlDecimalBigInt');\n\t\tthis.config.precision = config?.precision;\n\t\tthis.config.scale = config?.scale;\n\t\tthis.config.unsigned = config?.unsigned;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlDecimalBigInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlDecimalBigInt<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlDecimalBigInt<T extends ColumnBaseConfig<'bigint', 'MySqlDecimalBigInt'>>\n\textends MySqlColumnWithAutoIncrement<T, MySqlDecimalConfig>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlDecimalBigInt';\n\n\treadonly precision: number | undefined = this.config.precision;\n\treadonly scale: number | undefined = this.config.scale;\n\treadonly unsigned: boolean | undefined = this.config.unsigned;\n\n\toverride mapFromDriverValue = BigInt;\n\n\toverride mapToDriverValue = String;\n\n\tgetSQLType(): string {\n\t\tlet type = '';\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\ttype += `decimal(${this.precision},${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\ttype += 'decimal';\n\t\t} else {\n\t\t\ttype += `decimal(${this.precision})`;\n\t\t}\n\t\ttype = type === 'decimal(10,0)' || type === 'decimal(10)' ? 'decimal' : type;\n\t\treturn this.unsigned ? `${type} unsigned` : type;\n\t}\n}\n\nexport interface MySqlDecimalConfig<T extends 'string' | 'number' | 'bigint' = 'string' | 'number' | 'bigint'> {\n\tprecision?: number;\n\tscale?: number;\n\tunsigned?: boolean;\n\tmode?: T;\n}\n\nexport function decimal(): MySqlDecimalBuilderInitial<''>;\nexport function decimal<TMode extends 'string' | 'number' | 'bigint'>(\n\tconfig: MySqlDecimalConfig<TMode>,\n): Equal<TMode, 'number'> extends true ? MySqlDecimalNumberBuilderInitial<''>\n\t: Equal<TMode, 'bigint'> extends true ? MySqlDecimalBigIntBuilderInitial<''>\n\t: MySqlDecimalBuilderInitial<''>;\nexport function decimal<TName extends string, TMode extends 'string' | 'number' | 'bigint'>(\n\tname: TName,\n\tconfig?: MySqlDecimalConfig<TMode>,\n): Equal<TMode, 'number'> extends true ? MySqlDecimalNumberBuilderInitial<TName>\n\t: Equal<TMode, 'bigint'> extends true ? MySqlDecimalBigIntBuilderInitial<TName>\n\t: MySqlDecimalBuilderInitial<TName>;\nexport function decimal(a?: string | MySqlDecimalConfig, b: MySqlDecimalConfig = {}) {\n\tconst { name, config } = getColumnNameAndConfig<MySqlDecimalConfig>(a, b);\n\tconst mode = config?.mode;\n\treturn mode === 'number'\n\t\t? new MySqlDecimalNumberBuilder(name, config)\n\t\t: mode === 'bigint'\n\t\t? new MySqlDecimalBigIntBuilder(name, config)\n\t\t: new MySqlDecimalBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAmD;AACnD,oBAAkF;AAW3E,MAAM,4BAEH,kDAA2D;AAAA,EACpE,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAAwC;AACpE,UAAM,MAAM,UAAU,cAAc;AACpC,SAAK,OAAO,YAAY,QAAQ;AAChC,SAAK,OAAO,QAAQ,QAAQ;AAC5B,SAAK,OAAO,WAAW,QAAQ;AAAA,EAChC;AAAA;AAAA,EAGS,MACR,OACgD;AAChD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,qBACJ,2CACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,YAAgC,KAAK,OAAO;AAAA,EAC5C,QAA4B,KAAK,OAAO;AAAA,EACxC,WAAgC,KAAK,OAAO;AAAA,EAE5C,mBAAmB,OAAwB;AACnD,QAAI,OAAO,UAAU,SAAU,QAAO;AAEtC,WAAO,OAAO,KAAK;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,QAAI,OAAO;AACX,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,cAAQ,WAAW,KAAK,SAAS,IAAI,KAAK,KAAK;AAAA,IAChD,WAAW,KAAK,cAAc,QAAW;AACxC,cAAQ;AAAA,IACT,OAAO;AACN,cAAQ,WAAW,KAAK,SAAS;AAAA,IAClC;AACA,WAAO,SAAS,mBAAmB,SAAS,gBAAgB,YAAY;AACxE,WAAO,KAAK,WAAW,GAAG,IAAI,cAAc;AAAA,EAC7C;AACD;AAWO,MAAM,kCAEH,kDAA2D;AAAA,EACpE,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAAwC;AACpE,UAAM,MAAM,UAAU,oBAAoB;AAC1C,SAAK,OAAO,YAAY,QAAQ;AAChC,SAAK,OAAO,QAAQ,QAAQ;AAC5B,SAAK,OAAO,WAAW,QAAQ;AAAA,EAChC;AAAA;AAAA,EAGS,MACR,OACsD;AACtD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,2BACJ,2CACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,YAAgC,KAAK,OAAO;AAAA,EAC5C,QAA4B,KAAK,OAAO;AAAA,EACxC,WAAgC,KAAK,OAAO;AAAA,EAE5C,mBAAmB,OAAwB;AACnD,QAAI,OAAO,UAAU,SAAU,QAAO;AAEtC,WAAO,OAAO,KAAK;AAAA,EACpB;AAAA,EAES,mBAAmB;AAAA,EAE5B,aAAqB;AACpB,QAAI,OAAO;AACX,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,cAAQ,WAAW,KAAK,SAAS,IAAI,KAAK,KAAK;AAAA,IAChD,WAAW,KAAK,cAAc,QAAW;AACxC,cAAQ;AAAA,IACT,OAAO;AACN,cAAQ,WAAW,KAAK,SAAS;AAAA,IAClC;AACA,WAAO,SAAS,mBAAmB,SAAS,gBAAgB,YAAY;AACxE,WAAO,KAAK,WAAW,GAAG,IAAI,cAAc;AAAA,EAC7C;AACD;AAWO,MAAM,kCAEH,kDAA2D;AAAA,EACpE,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAAwC;AACpE,UAAM,MAAM,UAAU,oBAAoB;AAC1C,SAAK,OAAO,YAAY,QAAQ;AAChC,SAAK,OAAO,QAAQ,QAAQ;AAC5B,SAAK,OAAO,WAAW,QAAQ;AAAA,EAChC;AAAA;AAAA,EAGS,MACR,OACsD;AACtD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,2BACJ,2CACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,YAAgC,KAAK,OAAO;AAAA,EAC5C,QAA4B,KAAK,OAAO;AAAA,EACxC,WAAgC,KAAK,OAAO;AAAA,EAE5C,qBAAqB;AAAA,EAErB,mBAAmB;AAAA,EAE5B,aAAqB;AACpB,QAAI,OAAO;AACX,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,cAAQ,WAAW,KAAK,SAAS,IAAI,KAAK,KAAK;AAAA,IAChD,WAAW,KAAK,cAAc,QAAW;AACxC,cAAQ;AAAA,IACT,OAAO;AACN,cAAQ,WAAW,KAAK,SAAS;AAAA,IAClC;AACA,WAAO,SAAS,mBAAmB,SAAS,gBAAgB,YAAY;AACxE,WAAO,KAAK,WAAW,GAAG,IAAI,cAAc;AAAA,EAC7C;AACD;AAqBO,SAAS,QAAQ,GAAiC,IAAwB,CAAC,GAAG;AACpF,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA2C,GAAG,CAAC;AACxE,QAAM,OAAO,QAAQ;AACrB,SAAO,SAAS,WACb,IAAI,0BAA0B,MAAM,MAAM,IAC1C,SAAS,WACT,IAAI,0BAA0B,MAAM,MAAM,IAC1C,IAAI,oBAAoB,MAAM,MAAM;AACxC;", "names": []}