{"version": 3, "sources": ["../../../src/mysql-core/columns/enum.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport type { NonArray, Writable } from '~/utils.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\n// enum as string union\nexport type MySqlEnumColumnBuilderInitial<TName extends string, TEnum extends string[]> = MySqlEnumColumnBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'MySqlEnumColumn';\n\tdata: TEnum[number];\n\tdriverParam: string;\n\tenumValues: TEnum;\n}>;\n\nexport class MySqlEnumColumnBuilder<T extends ColumnBuilderBaseConfig<'string', 'MySqlEnumColumn'>>\n\textends MySqlColumnBuilder<T, { enumValues: T['enumValues'] }>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlEnumColumnBuilder';\n\n\tconstructor(name: T['name'], values: T['enumValues']) {\n\t\tsuper(name, 'string', 'MySqlEnumColumn');\n\t\tthis.config.enumValues = values;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlEnumColumn<MakeColumnConfig<T, TTableName> & { enumValues: T['enumValues'] }> {\n\t\treturn new MySqlEnumColumn<MakeColumnConfig<T, TTableName> & { enumValues: T['enumValues'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlEnumColumn<T extends ColumnBaseConfig<'string', 'MySqlEnumColumn'>>\n\textends MySqlColumn<T, { enumValues: T['enumValues'] }>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlEnumColumn';\n\n\toverride readonly enumValues = this.config.enumValues;\n\n\tgetSQLType(): string {\n\t\treturn `enum(${this.enumValues!.map((value) => `'${value}'`).join(',')})`;\n\t}\n}\n\n// enum as ts enum\n\nexport type MySqlEnumObjectColumnBuilderInitial<TName extends string, TEnum extends object> =\n\tMySqlEnumObjectColumnBuilder<{\n\t\tname: TName;\n\t\tdataType: 'string';\n\t\tcolumnType: 'MySqlEnumObjectColumn';\n\t\tdata: TEnum[keyof TEnum];\n\t\tdriverParam: string;\n\t\tenumValues: string[];\n\t}>;\n\nexport class MySqlEnumObjectColumnBuilder<T extends ColumnBuilderBaseConfig<'string', 'MySqlEnumObjectColumn'>>\n\textends MySqlColumnBuilder<T, { enumValues: T['enumValues'] }>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlEnumObjectColumnBuilder';\n\n\tconstructor(name: T['name'], values: T['enumValues']) {\n\t\tsuper(name, 'string', 'MySqlEnumObjectColumn');\n\t\tthis.config.enumValues = values;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlEnumObjectColumn<MakeColumnConfig<T, TTableName> & { enumValues: T['enumValues'] }> {\n\t\treturn new MySqlEnumObjectColumn<MakeColumnConfig<T, TTableName> & { enumValues: T['enumValues'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlEnumObjectColumn<T extends ColumnBaseConfig<'string', 'MySqlEnumObjectColumn'>>\n\textends MySqlColumn<T, { enumValues: T['enumValues'] }>\n{\n\tstatic override readonly [entityKind]: string = 'MySqlEnumObjectColumn';\n\n\toverride readonly enumValues = this.config.enumValues;\n\n\tgetSQLType(): string {\n\t\treturn `enum(${this.enumValues!.map((value) => `'${value}'`).join(',')})`;\n\t}\n}\n\nexport function mysqlEnum<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tvalues: T | Writable<T>,\n): MySqlEnumColumnBuilderInitial<'', Writable<T>>;\nexport function mysqlEnum<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tvalues: T | Writable<T>,\n): MySqlEnumColumnBuilderInitial<TName, Writable<T>>;\nexport function mysqlEnum<E extends Record<string, string>>(\n\tenumObj: NonArray<E>,\n): MySqlEnumObjectColumnBuilderInitial<'', E>;\nexport function mysqlEnum<TName extends string, E extends Record<string, string>>(\n\tname: TName,\n\tvalues: NonArray<E>,\n): MySqlEnumObjectColumnBuilderInitial<TName, E>;\nexport function mysqlEnum(\n\ta?: string | readonly [string, ...string[]] | [string, ...string[]] | Record<string, string>,\n\tb?: readonly [string, ...string[]] | [string, ...string[]] | Record<string, string>,\n): any {\n\t// if name + array or just array - it means we have string union passed\n\tif (typeof a === 'string' && Array.isArray(b) || Array.isArray(a)) {\n\t\tconst name = typeof a === 'string' && a.length > 0 ? a : '';\n\t\tconst values = (typeof a === 'string' ? b : a) ?? [];\n\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error(`You have an empty array for \"${name}\" enum values`);\n\t\t}\n\n\t\treturn new MySqlEnumColumnBuilder(name, values as any);\n\t}\n\n\tif (typeof a === 'string' && typeof b === 'object' || typeof a === 'object') {\n\t\tconst name = typeof a === 'object' ? '' : a;\n\t\tconst values = typeof a === 'object' ? Object.values(a) : typeof b === 'object' ? Object.values(b) : [];\n\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error(`You have an empty array for \"${name}\" enum values`);\n\t\t}\n\n\t\treturn new MySqlEnumObjectColumnBuilder(name, values as any);\n\t}\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAG3B,SAAS,aAAa,0BAA0B;AAYzC,MAAM,+BACJ,mBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAAyB;AACrD,UAAM,MAAM,UAAU,iBAAiB;AACvC,SAAK,OAAO,aAAa;AAAA,EAC1B;AAAA;AAAA,EAGS,MACR,OACqF;AACrF,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,wBACJ,YACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAE9B,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO,QAAQ,KAAK,WAAY,IAAI,CAAC,UAAU,IAAI,KAAK,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,EACvE;AACD;AAcO,MAAM,qCACJ,mBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAAyB;AACrD,UAAM,MAAM,UAAU,uBAAuB;AAC7C,SAAK,OAAO,aAAa;AAAA,EAC1B;AAAA;AAAA,EAGS,MACR,OAC2F;AAC3F,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,8BACJ,YACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAE9B,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO,QAAQ,KAAK,WAAY,IAAI,CAAC,UAAU,IAAI,KAAK,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,EACvE;AACD;AAgBO,SAAS,UACf,GACA,GACM;AAEN,MAAI,OAAO,MAAM,YAAY,MAAM,QAAQ,CAAC,KAAK,MAAM,QAAQ,CAAC,GAAG;AAClE,UAAM,OAAO,OAAO,MAAM,YAAY,EAAE,SAAS,IAAI,IAAI;AACzD,UAAM,UAAU,OAAO,MAAM,WAAW,IAAI,MAAM,CAAC;AAEnD,QAAI,OAAO,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM,gCAAgC,IAAI,eAAe;AAAA,IACpE;AAEA,WAAO,IAAI,uBAAuB,MAAM,MAAa;AAAA,EACtD;AAEA,MAAI,OAAO,MAAM,YAAY,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;AAC5E,UAAM,OAAO,OAAO,MAAM,WAAW,KAAK;AAC1C,UAAM,SAAS,OAAO,MAAM,WAAW,OAAO,OAAO,CAAC,IAAI,OAAO,MAAM,WAAW,OAAO,OAAO,CAAC,IAAI,CAAC;AAEtG,QAAI,OAAO,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM,gCAAgC,IAAI,eAAe;AAAA,IACpE;AAEA,WAAO,IAAI,6BAA6B,MAAM,MAAa;AAAA,EAC5D;AACD;", "names": []}