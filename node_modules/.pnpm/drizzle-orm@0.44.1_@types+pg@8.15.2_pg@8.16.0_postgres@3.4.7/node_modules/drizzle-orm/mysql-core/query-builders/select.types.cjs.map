{"version": 3, "sources": ["../../../src/mysql-core/query-builders/select.types.ts"], "sourcesContent": ["import type { MySqlColumn } from '~/mysql-core/columns/index.ts';\nimport type { MySqlTable, MySqlTableWithColumns } from '~/mysql-core/table.ts';\nimport type {\n\tSelectedFields as SelectedFieldsBase,\n\tSelectedFieldsFlat as SelectedFieldsFlatBase,\n\tSelectedFieldsOrdered as SelectedFieldsOrderedBase,\n} from '~/operations.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type {\n\tAppendToNullabilityMap,\n\tAppendToResult,\n\tBuildSubquerySelection,\n\tGetSelectTableName,\n\tJoinNullability,\n\tJoinType,\n\tMapColumnsToTableAlias,\n\tSelectMode,\n\tSelectResult,\n\tSetOperator,\n} from '~/query-builders/select.types.ts';\nimport type { ColumnsSelection, Placeholder, SQL, View } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport type { Table, UpdateTableConfig } from '~/table.ts';\nimport type { Assume, ValidateShape } from '~/utils.ts';\nimport type { MySqlPreparedQueryConfig, PreparedQueryHKTBase, PreparedQueryKind } from '../session.ts';\nimport type { MySqlViewBase } from '../view-base.ts';\nimport type { MySqlViewWithSelection } from '../view.ts';\nimport type { IndexConfig, MySqlSelectBase, MySqlSelectQueryBuilderBase } from './select.ts';\n\nexport type MySqlJoinType = Exclude<JoinType, 'full'>;\n\nexport interface MySqlSelectJoinConfig {\n\ton: SQL | undefined;\n\ttable: MySqlTable | Subquery | MySqlViewBase | SQL;\n\talias: string | undefined;\n\tjoinType: MySqlJoinType;\n\tlateral?: boolean;\n\tuseIndex?: string[];\n\tforceIndex?: string[];\n\tignoreIndex?: string[];\n}\n\nexport type BuildAliasTable<TTable extends MySqlTable | View, TAlias extends string> = TTable extends Table\n\t? MySqlTableWithColumns<\n\t\tUpdateTableConfig<TTable['_']['config'], {\n\t\t\tname: TAlias;\n\t\t\tcolumns: MapColumnsToTableAlias<TTable['_']['columns'], TAlias, 'mysql'>;\n\t\t}>\n\t>\n\t: TTable extends View ? MySqlViewWithSelection<\n\t\t\tTAlias,\n\t\t\tTTable['_']['existing'],\n\t\t\tMapColumnsToTableAlias<TTable['_']['selectedFields'], TAlias, 'mysql'>\n\t\t>\n\t: never;\n\nexport interface MySqlSelectConfig {\n\twithList?: Subquery[];\n\tfields: Record<string, unknown>;\n\tfieldsFlat?: SelectedFieldsOrdered;\n\twhere?: SQL;\n\thaving?: SQL;\n\ttable: MySqlTable | Subquery | MySqlViewBase | SQL;\n\tlimit?: number | Placeholder;\n\toffset?: number | Placeholder;\n\tjoins?: MySqlSelectJoinConfig[];\n\torderBy?: (MySqlColumn | SQL | SQL.Aliased)[];\n\tgroupBy?: (MySqlColumn | SQL | SQL.Aliased)[];\n\tlockingClause?: {\n\t\tstrength: LockStrength;\n\t\tconfig: LockConfig;\n\t};\n\tdistinct?: boolean;\n\tsetOperators: {\n\t\trightSelect: TypedQueryBuilder<any, any>;\n\t\ttype: SetOperator;\n\t\tisAll: boolean;\n\t\torderBy?: (MySqlColumn | SQL | SQL.Aliased)[];\n\t\tlimit?: number | Placeholder;\n\t\toffset?: number | Placeholder;\n\t}[];\n\tuseIndex?: string[];\n\tforceIndex?: string[];\n\tignoreIndex?: string[];\n}\n\nexport type MySqlJoin<\n\tT extends AnyMySqlSelectQueryBuilder,\n\tTDynamic extends boolean,\n\tTJoinType extends MySqlJoinType,\n\tTJoinedTable extends MySqlTable | Subquery | MySqlViewBase | SQL,\n\tTJoinedName extends GetSelectTableName<TJoinedTable> = GetSelectTableName<TJoinedTable>,\n> = T extends any ? MySqlSelectWithout<\n\t\tMySqlSelectKind<\n\t\t\tT['_']['hkt'],\n\t\t\tT['_']['tableName'],\n\t\t\tAppendToResult<\n\t\t\t\tT['_']['tableName'],\n\t\t\t\tT['_']['selection'],\n\t\t\t\tTJoinedName,\n\t\t\t\tTJoinedTable extends MySqlTable ? TJoinedTable['_']['columns']\n\t\t\t\t\t: TJoinedTable extends Subquery | View ? Assume<TJoinedTable['_']['selectedFields'], SelectedFields>\n\t\t\t\t\t: never,\n\t\t\t\tT['_']['selectMode']\n\t\t\t>,\n\t\t\tT['_']['selectMode'] extends 'partial' ? T['_']['selectMode'] : 'multiple',\n\t\t\tT['_']['preparedQueryHKT'],\n\t\t\tAppendToNullabilityMap<T['_']['nullabilityMap'], TJoinedName, TJoinType>,\n\t\t\tTDynamic,\n\t\t\tT['_']['excludedMethods']\n\t\t>,\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>\n\t: never;\n\nexport type MySqlJoinFn<\n\tT extends AnyMySqlSelectQueryBuilder,\n\tTDynamic extends boolean,\n\tTJoinType extends MySqlJoinType,\n\tTIsLateral extends boolean,\n> = 'cross' extends TJoinType ? <\n\t\tTJoinedTable extends (TIsLateral extends true ? Subquery | SQL : MySqlTable | Subquery | MySqlViewBase | SQL),\n\t\tTJoinedName extends GetSelectTableName<TJoinedTable> = GetSelectTableName<TJoinedTable>,\n\t>(\n\t\ttable: TJoinedTable,\n\t\tonIndex?:\n\t\t\t| (TJoinedTable extends MySqlTable ? IndexConfig\n\t\t\t\t: 'Index hint configuration is allowed only for MySqlTable and not for subqueries or views')\n\t\t\t| undefined,\n\t) => MySqlJoin<T, TDynamic, TJoinType, TJoinedTable, TJoinedName>\n\t: <\n\t\tTJoinedTable extends (TIsLateral extends true ? Subquery | SQL : MySqlTable | Subquery | MySqlViewBase | SQL),\n\t\tTJoinedName extends GetSelectTableName<TJoinedTable> = GetSelectTableName<TJoinedTable>,\n\t>(\n\t\ttable: TJoinedTable,\n\t\ton: ((aliases: T['_']['selection']) => SQL | undefined) | SQL | undefined,\n\t\tonIndex?:\n\t\t\t| (TJoinedTable extends MySqlTable ? IndexConfig\n\t\t\t\t: 'Index hint configuration is allowed only for MySqlTable and not for subqueries or views')\n\t\t\t| undefined,\n\t) => MySqlJoin<T, TDynamic, TJoinType, TJoinedTable, TJoinedName>;\n\nexport type SelectedFieldsFlat = SelectedFieldsFlatBase<MySqlColumn>;\n\nexport type SelectedFields = SelectedFieldsBase<MySqlColumn, MySqlTable>;\n\nexport type SelectedFieldsOrdered = SelectedFieldsOrderedBase<MySqlColumn>;\n\nexport type LockStrength = 'update' | 'share';\n\nexport type LockConfig = {\n\tnoWait: true;\n\tskipLocked?: undefined;\n} | {\n\tnoWait?: undefined;\n\tskipLocked: true;\n} | {\n\tnoWait?: undefined;\n\tskipLocked?: undefined;\n};\n\nexport interface MySqlSelectHKTBase {\n\ttableName: string | undefined;\n\tselection: unknown;\n\tselectMode: SelectMode;\n\tpreparedQueryHKT: unknown;\n\tnullabilityMap: unknown;\n\tdynamic: boolean;\n\texcludedMethods: string;\n\tresult: unknown;\n\tselectedFields: unknown;\n\t_type: unknown;\n}\n\nexport type MySqlSelectKind<\n\tT extends MySqlSelectHKTBase,\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n\tTNullabilityMap extends Record<string, JoinNullability>,\n\tTDynamic extends boolean,\n\tTExcludedMethods extends string,\n\tTResult = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> = (T & {\n\ttableName: TTableName;\n\tselection: TSelection;\n\tselectMode: TSelectMode;\n\tpreparedQueryHKT: TPreparedQueryHKT;\n\tnullabilityMap: TNullabilityMap;\n\tdynamic: TDynamic;\n\texcludedMethods: TExcludedMethods;\n\tresult: TResult;\n\tselectedFields: TSelectedFields;\n})['_type'];\n\nexport interface MySqlSelectQueryBuilderHKT extends MySqlSelectHKTBase {\n\t_type: MySqlSelectQueryBuilderBase<\n\t\tMySqlSelectQueryBuilderHKT,\n\t\tthis['tableName'],\n\t\tAssume<this['selection'], ColumnsSelection>,\n\t\tthis['selectMode'],\n\t\tAssume<this['preparedQueryHKT'], PreparedQueryHKTBase>,\n\t\tAssume<this['nullabilityMap'], Record<string, JoinNullability>>,\n\t\tthis['dynamic'],\n\t\tthis['excludedMethods'],\n\t\tAssume<this['result'], any[]>,\n\t\tAssume<this['selectedFields'], ColumnsSelection>\n\t>;\n}\n\nexport interface MySqlSelectHKT extends MySqlSelectHKTBase {\n\t_type: MySqlSelectBase<\n\t\tthis['tableName'],\n\t\tAssume<this['selection'], ColumnsSelection>,\n\t\tthis['selectMode'],\n\t\tAssume<this['preparedQueryHKT'], PreparedQueryHKTBase>,\n\t\tAssume<this['nullabilityMap'], Record<string, JoinNullability>>,\n\t\tthis['dynamic'],\n\t\tthis['excludedMethods'],\n\t\tAssume<this['result'], any[]>,\n\t\tAssume<this['selectedFields'], ColumnsSelection>\n\t>;\n}\n\nexport type MySqlSetOperatorExcludedMethods =\n\t| 'where'\n\t| 'having'\n\t| 'groupBy'\n\t| 'session'\n\t| 'leftJoin'\n\t| 'rightJoin'\n\t| 'innerJoin'\n\t| 'for';\n\nexport type MySqlSelectWithout<\n\tT extends AnyMySqlSelectQueryBuilder,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n\tTResetExcluded extends boolean = false,\n> = TDynamic extends true ? T : Omit<\n\tMySqlSelectKind<\n\t\tT['_']['hkt'],\n\t\tT['_']['tableName'],\n\t\tT['_']['selection'],\n\t\tT['_']['selectMode'],\n\t\tT['_']['preparedQueryHKT'],\n\t\tT['_']['nullabilityMap'],\n\t\tTDynamic,\n\t\tTResetExcluded extends true ? K : T['_']['excludedMethods'] | K,\n\t\tT['_']['result'],\n\t\tT['_']['selectedFields']\n\t>,\n\tTResetExcluded extends true ? K : T['_']['excludedMethods'] | K\n>;\n\nexport type MySqlSelectPrepare<T extends AnyMySqlSelect> = PreparedQueryKind<\n\tT['_']['preparedQueryHKT'],\n\tMySqlPreparedQueryConfig & {\n\t\texecute: T['_']['result'];\n\t\titerator: T['_']['result'][number];\n\t},\n\ttrue\n>;\n\nexport type MySqlSelectDynamic<T extends AnyMySqlSelectQueryBuilder> = MySqlSelectKind<\n\tT['_']['hkt'],\n\tT['_']['tableName'],\n\tT['_']['selection'],\n\tT['_']['selectMode'],\n\tT['_']['preparedQueryHKT'],\n\tT['_']['nullabilityMap'],\n\ttrue,\n\tnever,\n\tT['_']['result'],\n\tT['_']['selectedFields']\n>;\n\nexport type CreateMySqlSelectFromBuilderMode<\n\tTBuilderMode extends 'db' | 'qb',\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n> = TBuilderMode extends 'db' ? MySqlSelectBase<TTableName, TSelection, TSelectMode, TPreparedQueryHKT>\n\t: MySqlSelectQueryBuilderBase<MySqlSelectQueryBuilderHKT, TTableName, TSelection, TSelectMode, TPreparedQueryHKT>;\n\nexport type MySqlSelectQueryBuilder<\n\tTHKT extends MySqlSelectHKTBase = MySqlSelectQueryBuilderHKT,\n\tTTableName extends string | undefined = string | undefined,\n\tTSelection extends ColumnsSelection = ColumnsSelection,\n\tTSelectMode extends SelectMode = SelectMode,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase = PreparedQueryHKTBase,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<string, JoinNullability>,\n\tTResult extends any[] = unknown[],\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> = MySqlSelectQueryBuilderBase<\n\tTHKT,\n\tTTableName,\n\tTSelection,\n\tTSelectMode,\n\tTPreparedQueryHKT,\n\tTNullabilityMap,\n\ttrue,\n\tnever,\n\tTResult,\n\tTSelectedFields\n>;\n\nexport type AnyMySqlSelectQueryBuilder = MySqlSelectQueryBuilderBase<any, any, any, any, any, any, any, any, any>;\n\nexport type AnyMySqlSetOperatorInterface = MySqlSetOperatorInterface<any, any, any, any, any, any, any, any, any>;\n\nexport interface MySqlSetOperatorInterface<\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase = PreparedQueryHKTBase,\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> {\n\t_: {\n\t\treadonly hkt: MySqlSelectHKT;\n\t\treadonly tableName: TTableName;\n\t\treadonly selection: TSelection;\n\t\treadonly selectMode: TSelectMode;\n\t\treadonly preparedQueryHKT: TPreparedQueryHKT;\n\t\treadonly nullabilityMap: TNullabilityMap;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TResult;\n\t\treadonly selectedFields: TSelectedFields;\n\t};\n}\n\nexport type MySqlSetOperatorWithResult<TResult extends any[]> = MySqlSetOperatorInterface<\n\tany,\n\tany,\n\tany,\n\tany,\n\tany,\n\tany,\n\tany,\n\tTResult,\n\tany\n>;\n\nexport type MySqlSelect<\n\tTTableName extends string | undefined = string | undefined,\n\tTSelection extends ColumnsSelection = Record<string, any>,\n\tTSelectMode extends SelectMode = SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<string, JoinNullability>,\n> = MySqlSelectBase<TTableName, TSelection, TSelectMode, PreparedQueryHKTBase, TNullabilityMap, true, never>;\n\nexport type AnyMySqlSelect = MySqlSelectBase<any, any, any, any, any, any, any, any>;\n\nexport type MySqlSetOperator<\n\tTTableName extends string | undefined = string | undefined,\n\tTSelection extends ColumnsSelection = Record<string, any>,\n\tTSelectMode extends SelectMode = SelectMode,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase = PreparedQueryHKTBase,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<string, JoinNullability>,\n> = MySqlSelectBase<\n\tTTableName,\n\tTSelection,\n\tTSelectMode,\n\tTPreparedQueryHKT,\n\tTNullabilityMap,\n\ttrue,\n\tMySqlSetOperatorExcludedMethods\n>;\n\nexport type SetOperatorRightSelect<\n\tTValue extends MySqlSetOperatorWithResult<TResult>,\n\tTResult extends any[],\n> = TValue extends MySqlSetOperatorInterface<any, any, any, any, any, any, any, infer TValueResult, any>\n\t? ValidateShape<\n\t\tTValueResult[number],\n\t\tTResult[number],\n\t\tTypedQueryBuilder<any, TValueResult>\n\t>\n\t: TValue;\n\nexport type SetOperatorRestSelect<\n\tTValue extends readonly MySqlSetOperatorWithResult<TResult>[],\n\tTResult extends any[],\n> = TValue extends [infer First, ...infer Rest]\n\t? First extends MySqlSetOperatorInterface<any, any, any, any, any, any, any, infer TValueResult, any>\n\t\t? Rest extends AnyMySqlSetOperatorInterface[] ? [\n\t\t\t\tValidateShape<TValueResult[number], TResult[number], TypedQueryBuilder<any, TValueResult>>,\n\t\t\t\t...SetOperatorRestSelect<Rest, TResult>,\n\t\t\t]\n\t\t: ValidateShape<TValueResult[number], TResult[number], TypedQueryBuilder<any, TValueResult>[]>\n\t: never\n\t: TValue;\n\nexport type MySqlCreateSetOperatorFn = <\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTValue extends MySqlSetOperatorWithResult<TResult>,\n\tTRest extends MySqlSetOperatorWithResult<TResult>[],\n\tTPreparedQueryHKT extends PreparedQueryHKTBase = PreparedQueryHKTBase,\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n>(\n\tleftSelect: MySqlSetOperatorInterface<\n\t\tTTableName,\n\t\tTSelection,\n\t\tTSelectMode,\n\t\tTPreparedQueryHKT,\n\t\tTNullabilityMap,\n\t\tTDynamic,\n\t\tTExcludedMethods,\n\t\tTResult,\n\t\tTSelectedFields\n\t>,\n\trightSelect: SetOperatorRightSelect<TValue, TResult>,\n\t...restSelects: SetOperatorRestSelect<TRest, TResult>\n) => MySqlSelectWithout<\n\tMySqlSelectBase<\n\t\tTTableName,\n\t\tTSelection,\n\t\tTSelectMode,\n\t\tTPreparedQueryHKT,\n\t\tTNullabilityMap,\n\t\tTDynamic,\n\t\tTExcludedMethods,\n\t\tTResult,\n\t\tTSelectedFields\n\t>,\n\tfalse,\n\tMySqlSetOperatorExcludedMethods,\n\ttrue\n>;\n\nexport type GetMySqlSetOperators = {\n\tunion: MySqlCreateSetOperatorFn;\n\tintersect: MySqlCreateSetOperatorFn;\n\texcept: MySqlCreateSetOperatorFn;\n\tunionAll: MySqlCreateSetOperatorFn;\n\tintersectAll: MySqlCreateSetOperatorFn;\n\texceptAll: MySqlCreateSetOperatorFn;\n};\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}