{"version": 3, "sources": ["../../src/mysql-core/utils.ts"], "sourcesContent": ["import { is } from '~/entity.ts';\nimport { SQL } from '~/index.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type { Check } from './checks.ts';\nimport { CheckBuilder } from './checks.ts';\nimport type { ForeignKey } from './foreign-keys.ts';\nimport { ForeignKeyBuilder } from './foreign-keys.ts';\nimport type { Index } from './indexes.ts';\nimport { IndexBuilder } from './indexes.ts';\nimport type { PrimaryKey } from './primary-keys.ts';\nimport { PrimaryKeyBuilder } from './primary-keys.ts';\nimport type { IndexForHint } from './query-builders/select.ts';\nimport { MySqlTable } from './table.ts';\nimport { type UniqueConstraint, UniqueConstraintBuilder } from './unique-constraint.ts';\nimport type { MySqlViewBase } from './view-base.ts';\nimport { MySqlViewConfig } from './view-common.ts';\nimport type { MySqlView } from './view.ts';\n\nexport function extractUsedTable(table: MySqlTable | Subquery | MySqlViewBase | SQL): string[] {\n\tif (is(table, MySqlTable)) {\n\t\treturn [`${table[Table.Symbol.BaseName]}`];\n\t}\n\tif (is(table, Subquery)) {\n\t\treturn table._.usedTables ?? [];\n\t}\n\tif (is(table, SQL)) {\n\t\treturn table.usedTables ?? [];\n\t}\n\treturn [];\n}\n\nexport function getTableConfig(table: MySqlTable) {\n\tconst columns = Object.values(table[MySqlTable.Symbol.Columns]);\n\tconst indexes: Index[] = [];\n\tconst checks: Check[] = [];\n\tconst primaryKeys: PrimaryKey[] = [];\n\tconst uniqueConstraints: UniqueConstraint[] = [];\n\tconst foreignKeys: ForeignKey[] = Object.values(table[MySqlTable.Symbol.InlineForeignKeys]);\n\tconst name = table[Table.Symbol.Name];\n\tconst schema = table[Table.Symbol.Schema];\n\tconst baseName = table[Table.Symbol.BaseName];\n\n\tconst extraConfigBuilder = table[MySqlTable.Symbol.ExtraConfigBuilder];\n\n\tif (extraConfigBuilder !== undefined) {\n\t\tconst extraConfig = extraConfigBuilder(table[MySqlTable.Symbol.Columns]);\n\t\tconst extraValues = Array.isArray(extraConfig) ? extraConfig.flat(1) as any[] : Object.values(extraConfig);\n\t\tfor (const builder of Object.values(extraValues)) {\n\t\t\tif (is(builder, IndexBuilder)) {\n\t\t\t\tindexes.push(builder.build(table));\n\t\t\t} else if (is(builder, CheckBuilder)) {\n\t\t\t\tchecks.push(builder.build(table));\n\t\t\t} else if (is(builder, UniqueConstraintBuilder)) {\n\t\t\t\tuniqueConstraints.push(builder.build(table));\n\t\t\t} else if (is(builder, PrimaryKeyBuilder)) {\n\t\t\t\tprimaryKeys.push(builder.build(table));\n\t\t\t} else if (is(builder, ForeignKeyBuilder)) {\n\t\t\t\tforeignKeys.push(builder.build(table));\n\t\t\t}\n\t\t}\n\t}\n\n\treturn {\n\t\tcolumns,\n\t\tindexes,\n\t\tforeignKeys,\n\t\tchecks,\n\t\tprimaryKeys,\n\t\tuniqueConstraints,\n\t\tname,\n\t\tschema,\n\t\tbaseName,\n\t};\n}\n\nexport function getViewConfig<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n>(view: MySqlView<TName, TExisting>) {\n\treturn {\n\t\t...view[ViewBaseConfig],\n\t\t...view[MySqlViewConfig],\n\t};\n}\n\nexport function convertIndexToString(indexes: IndexForHint[]) {\n\treturn indexes.map((idx) => {\n\t\treturn typeof idx === 'object' ? idx.config.name : idx;\n\t});\n}\n\nexport function toArray<T>(value: T | T[]): T[] {\n\treturn Array.isArray(value) ? value : [value];\n}\n"], "mappings": "AAAA,SAAS,UAAU;AACnB,SAAS,WAAW;AACpB,SAAS,gBAAgB;AACzB,SAAS,aAAa;AACtB,SAAS,sBAAsB;AAE/B,SAAS,oBAAoB;AAE7B,SAAS,yBAAyB;AAElC,SAAS,oBAAoB;AAE7B,SAAS,yBAAyB;AAElC,SAAS,kBAAkB;AAC3B,SAAgC,+BAA+B;AAE/D,SAAS,uBAAuB;AAGzB,SAAS,iBAAiB,OAA8D;AAC9F,MAAI,GAAG,OAAO,UAAU,GAAG;AAC1B,WAAO,CAAC,GAAG,MAAM,MAAM,OAAO,QAAQ,CAAC,EAAE;AAAA,EAC1C;AACA,MAAI,GAAG,OAAO,QAAQ,GAAG;AACxB,WAAO,MAAM,EAAE,cAAc,CAAC;AAAA,EAC/B;AACA,MAAI,GAAG,OAAO,GAAG,GAAG;AACnB,WAAO,MAAM,cAAc,CAAC;AAAA,EAC7B;AACA,SAAO,CAAC;AACT;AAEO,SAAS,eAAe,OAAmB;AACjD,QAAM,UAAU,OAAO,OAAO,MAAM,WAAW,OAAO,OAAO,CAAC;AAC9D,QAAM,UAAmB,CAAC;AAC1B,QAAM,SAAkB,CAAC;AACzB,QAAM,cAA4B,CAAC;AACnC,QAAM,oBAAwC,CAAC;AAC/C,QAAM,cAA4B,OAAO,OAAO,MAAM,WAAW,OAAO,iBAAiB,CAAC;AAC1F,QAAM,OAAO,MAAM,MAAM,OAAO,IAAI;AACpC,QAAM,SAAS,MAAM,MAAM,OAAO,MAAM;AACxC,QAAM,WAAW,MAAM,MAAM,OAAO,QAAQ;AAE5C,QAAM,qBAAqB,MAAM,WAAW,OAAO,kBAAkB;AAErE,MAAI,uBAAuB,QAAW;AACrC,UAAM,cAAc,mBAAmB,MAAM,WAAW,OAAO,OAAO,CAAC;AACvE,UAAM,cAAc,MAAM,QAAQ,WAAW,IAAI,YAAY,KAAK,CAAC,IAAa,OAAO,OAAO,WAAW;AACzG,eAAW,WAAW,OAAO,OAAO,WAAW,GAAG;AACjD,UAAI,GAAG,SAAS,YAAY,GAAG;AAC9B,gBAAQ,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MAClC,WAAW,GAAG,SAAS,YAAY,GAAG;AACrC,eAAO,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MACjC,WAAW,GAAG,SAAS,uBAAuB,GAAG;AAChD,0BAAkB,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MAC5C,WAAW,GAAG,SAAS,iBAAiB,GAAG;AAC1C,oBAAY,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MACtC,WAAW,GAAG,SAAS,iBAAiB,GAAG;AAC1C,oBAAY,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MACtC;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;AAEO,SAAS,cAGd,MAAmC;AACpC,SAAO;AAAA,IACN,GAAG,KAAK,cAAc;AAAA,IACtB,GAAG,KAAK,eAAe;AAAA,EACxB;AACD;AAEO,SAAS,qBAAqB,SAAyB;AAC7D,SAAO,QAAQ,IAAI,CAAC,QAAQ;AAC3B,WAAO,OAAO,QAAQ,WAAW,IAAI,OAAO,OAAO;AAAA,EACpD,CAAC;AACF;AAEO,SAAS,QAAW,OAAqB;AAC/C,SAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC7C;", "names": []}