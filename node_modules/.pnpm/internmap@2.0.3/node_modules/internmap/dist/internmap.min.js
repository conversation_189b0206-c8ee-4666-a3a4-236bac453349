// https://github.com/mbostock/internmap/ v2.0.3 Copyright 2021 Mike <PERSON>
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).internmap={})}(this,(function(e){"use strict";class InternMap extends Map{constructor(e,t=r){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(const[t,n]of e)this.set(t,n)}get(e){return super.get(t(this,e))}has(e){return super.has(t(this,e))}set(e,t){return super.set(n(this,e),t)}delete(e){return super.delete(s(this,e))}}class InternSet extends Set{constructor(e,t=r){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(const t of e)this.add(t)}has(e){return super.has(t(this,e))}add(e){return super.add(n(this,e))}delete(e){return super.delete(s(this,e))}}function t({_intern:e,_key:t},n){const s=t(n);return e.has(s)?e.get(s):n}function n({_intern:e,_key:t},n){const s=t(n);return e.has(s)?e.get(s):(e.set(s,n),n)}function s({_intern:e,_key:t},n){const s=t(n);return e.has(s)&&(n=e.get(s),e.delete(s)),n}function r(e){return null!==e&&"object"==typeof e?e.valueOf():e}e.InternMap=InternMap,e.InternSet=InternSet,Object.defineProperty(e,"__esModule",{value:!0})}));
