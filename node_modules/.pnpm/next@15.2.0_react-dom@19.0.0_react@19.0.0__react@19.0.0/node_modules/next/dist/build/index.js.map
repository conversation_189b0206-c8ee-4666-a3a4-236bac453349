{"version": 3, "sources": ["../../src/build/index.ts"], "sourcesContent": ["import type { AppBuildManifest } from './webpack/plugins/app-build-manifest-plugin'\nimport type { PagesManifest } from './webpack/plugins/pages-manifest-plugin'\nimport type { ExportPathMap, NextConfigComplete } from '../server/config-shared'\nimport type { MiddlewareManifest } from './webpack/plugins/middleware-plugin'\nimport type { ActionManifest } from './webpack/plugins/flight-client-entry-plugin'\nimport type { Revalidate } from '../server/lib/revalidate'\n\nimport '../lib/setup-exception-listeners'\n\nimport { loadEnvConfig, type LoadedEnvFiles } from '@next/env'\nimport { bold, yellow } from '../lib/picocolors'\nimport crypto from 'crypto'\nimport { makeRe } from 'next/dist/compiled/picomatch'\nimport { existsSync, promises as fs } from 'fs'\nimport os from 'os'\nimport { Worker } from '../lib/worker'\nimport { defaultConfig } from '../server/config-shared'\nimport devalue from 'next/dist/compiled/devalue'\nimport findUp from 'next/dist/compiled/find-up'\nimport { nanoid } from 'next/dist/compiled/nanoid/index.cjs'\nimport path from 'path'\nimport {\n  STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR,\n  PUBLIC_DIR_MIDDLEWARE_CONFLICT,\n  MIDDLEWARE_FILENAME,\n  PAGES_DIR_ALIAS,\n  INSTRUMENTATION_HOOK_FILENAME,\n  RSC_PREFETCH_SUFFIX,\n  RSC_SUFFIX,\n  NEXT_RESUME_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  MATCHED_PATH_HEADER,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SEGMENT_SUFFIX,\n} from '../lib/constants'\nimport { FileType, fileExists } from '../lib/file-exists'\nimport { findPagesDir } from '../lib/find-pages-dir'\nimport loadCustomRoutes, {\n  normalizeRouteRegex,\n} from '../lib/load-custom-routes'\nimport type {\n  CustomRoutes,\n  Header,\n  Redirect,\n  Rewrite,\n  RouteHas,\n} from '../lib/load-custom-routes'\nimport { nonNullable } from '../lib/non-nullable'\nimport { recursiveDelete } from '../lib/recursive-delete'\nimport { verifyPartytownSetup } from '../lib/verify-partytown-setup'\nimport {\n  BUILD_ID_FILE,\n  BUILD_MANIFEST,\n  CLIENT_STATIC_FILES_PATH,\n  EXPORT_DETAIL,\n  EXPORT_MARKER,\n  IMAGES_MANIFEST,\n  PAGES_MANIFEST,\n  PHASE_PRODUCTION_BUILD,\n  PRERENDER_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  ROUTES_MANIFEST,\n  SERVER_DIRECTORY,\n  SERVER_FILES_MANIFEST,\n  STATIC_STATUS_PAGES,\n  MIDDLEWARE_MANIFEST,\n  APP_PATHS_MANIFEST,\n  APP_PATH_ROUTES_MANIFEST,\n  APP_BUILD_MANIFEST,\n  RSC_MODULE_TYPES,\n  NEXT_FONT_MANIFEST,\n  SUBRESOURCE_INTEGRITY_MANIFEST,\n  MIDDLEWARE_BUILD_MANIFEST,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  FUNCTIONS_CONFIG_MANIFEST,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  DYNAMIC_CSS_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n} from '../shared/lib/constants'\nimport {\n  getSortedRoutes,\n  isDynamicRoute,\n  getSortedRouteObjects,\n} from '../shared/lib/router/utils'\nimport type { __ApiPreviewProps } from '../server/api-utils'\nimport loadConfig from '../server/config'\nimport type { BuildManifest } from '../server/get-page-files'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { getPagePath } from '../server/require'\nimport * as ciEnvironment from '../server/ci-info'\nimport {\n  turborepoTraceAccess,\n  TurborepoAccessTraceResult,\n  writeTurborepoAccessTraceResult,\n} from './turborepo-access-trace'\n\nimport {\n  eventBuildOptimize,\n  eventCliSession,\n  eventBuildFeatureUsage,\n  eventNextPlugins,\n  EVENT_BUILD_FEATURE_USAGE,\n  eventPackageUsedInGetServerSideProps,\n  eventBuildCompleted,\n} from '../telemetry/events'\nimport type { EventBuildFeatureUsage } from '../telemetry/events'\nimport { Telemetry } from '../telemetry/storage'\nimport { hadUnsupportedValue } from './analysis/get-page-static-info'\nimport {\n  createPagesMapping,\n  getStaticInfoIncludingLayouts,\n  sortByPageExts,\n} from './entries'\nimport { PAGE_TYPES } from '../lib/page-types'\nimport { generateBuildId } from './generate-build-id'\nimport { isWriteable } from './is-writeable'\nimport * as Log from './output/log'\nimport createSpinner from './spinner'\nimport { trace, flushAllTraces, setGlobal, type Span } from '../trace'\nimport {\n  detectConflictingPaths,\n  computeFromManifest,\n  getJsPageSizeInKb,\n  printCustomRoutes,\n  printTreeView,\n  copyTracedFiles,\n  isReservedPage,\n  isAppBuiltinNotFoundPage,\n  collectRoutesUsingEdgeRuntime,\n  collectMeta,\n} from './utils'\nimport type { PageInfo, PageInfos } from './utils'\nimport type { PrerenderedRoute } from './static-paths/types'\nimport type { AppSegmentConfig } from './segment-config/app/app-segment-config'\nimport { writeBuildId } from './write-build-id'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport isError from '../lib/is-error'\nimport type { NextError } from '../lib/is-error'\nimport { isEdgeRuntime } from '../lib/is-edge-runtime'\nimport { recursiveCopy } from '../lib/recursive-copy'\nimport { recursiveReadDir } from '../lib/recursive-readdir'\nimport { lockfilePatchPromise, teardownTraceSubscriber } from './swc'\nimport { getNamedRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { getFilesInDir } from '../lib/get-files-in-dir'\nimport { eventSwcPlugins } from '../telemetry/events/swc-plugins'\nimport { normalizeAppPath } from '../shared/lib/router/utils/app-paths'\nimport {\n  ACTION_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_REWRITTEN_PATH_HEADER,\n  NEXT_REWRITTEN_QUERY_HEADER,\n} from '../client/components/app-router-headers'\nimport { webpackBuild } from './webpack-build'\nimport { NextBuildContext, type MappedPages } from './build-context'\nimport { normalizePathSep } from '../shared/lib/page-path/normalize-path-sep'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { createClientRouterFilter } from '../lib/create-client-router-filter'\nimport { createValidFileMatcher } from '../server/lib/find-page-file'\nimport { startTypeChecking } from './type-check'\nimport { generateInterceptionRoutesRewrites } from '../lib/generate-interception-routes-rewrites'\n\nimport { buildDataRoute } from '../server/lib/router-utils/build-data-route'\nimport { collectBuildTraces } from './collect-build-traces'\nimport type { BuildTraceContext } from './webpack/plugins/next-trace-entrypoints-plugin'\nimport { formatManifest } from './manifests/formatter/format-manifest'\nimport {\n  recordFrameworkVersion,\n  updateBuildDiagnostics,\n  recordFetchMetrics,\n} from '../diagnostics/build-diagnostics'\nimport { getStartServerInfo, logStartInfo } from '../server/lib/app-info-log'\nimport type { NextEnabledDirectories } from '../server/base-server'\nimport { hasCustomExportOutput } from '../export/utils'\nimport { buildCustomRoute } from '../lib/build-custom-route'\nimport { traceMemoryUsage } from '../lib/memory/trace'\nimport { generateEncryptionKeyBase64 } from '../server/app-render/encryption-utils-server'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport uploadTrace from '../trace/upload-trace'\nimport {\n  checkIsAppPPREnabled,\n  checkIsRoutePPREnabled,\n} from '../server/lib/experimental/ppr'\nimport { FallbackMode, fallbackModeToFallbackField } from '../lib/fallback'\nimport { RenderingMode } from './rendering-mode'\nimport { getParamKeys } from '../server/request/fallback-params'\nimport {\n  formatNodeOptions,\n  getParsedNodeOptionsWithoutInspect,\n} from '../server/lib/utils'\nimport { InvariantError } from '../shared/lib/invariant-error'\nimport { HTML_LIMITED_BOT_UA_RE_STRING } from '../shared/lib/router/utils/is-bot'\nimport type { UseCacheTrackerKey } from './webpack/plugins/telemetry-plugin/use-cache-tracker-utils'\nimport {\n  buildPrefetchSegmentDataRoute,\n  type PrefetchSegmentDataRoute,\n} from '../server/lib/router-utils/build-prefetch-segment-data-route'\n\nimport { turbopackBuild } from './turbopack-build'\n\ntype Fallback = null | boolean | string\n\nexport interface SsgRoute {\n  dataRoute: string | null\n  experimentalBypassFor?: RouteHas[]\n\n  /**\n   * The headers that should be served along side this prerendered route.\n   */\n  initialHeaders?: Record<string, string>\n\n  /**\n   * The status code that should be served along side this prerendered route.\n   */\n  initialStatus?: number\n\n  /**\n   * The revalidation configuration for this route.\n   */\n  initialRevalidateSeconds: Revalidate\n\n  /**\n   * The prefetch data route associated with this page. If not defined, this\n   * page does not support prefetching.\n   */\n  prefetchDataRoute: string | null | undefined\n\n  /**\n   * The dynamic route that this statically prerendered route is based on. If\n   * this is null, then the route was not based on a dynamic route.\n   */\n  srcRoute: string | null\n\n  /**\n   * @deprecated use `renderingMode` instead\n   */\n  experimentalPPR: boolean | undefined\n\n  /**\n   * The rendering mode for this route. Only `undefined` when not an app router\n   * route.\n   */\n  renderingMode: RenderingMode | undefined\n\n  /**\n   * The headers that are allowed to be used when revalidating this route. These\n   * are used internally by Next.js to revalidate routes.\n   */\n  allowHeader: string[]\n}\n\nexport interface DynamicSsgRoute {\n  dataRoute: string | null\n  dataRouteRegex: string | null\n  experimentalBypassFor?: RouteHas[]\n  fallback: Fallback\n\n  /**\n   * When defined, it describes the revalidation configuration for the fallback\n   * route.\n   */\n  fallbackRevalidate: Revalidate | undefined\n\n  /**\n   * The headers that should used when serving the fallback.\n   */\n  fallbackHeaders?: Record<string, string>\n\n  /**\n   * The status code that should be used when serving the fallback.\n   */\n  fallbackStatus?: number\n\n  /**\n   * The root params that are unknown for this fallback route.\n   */\n  fallbackRootParams: readonly string[] | undefined\n\n  /**\n   * The source route that this fallback route is based on. This is a reference\n   * so that we can associate this dynamic route with the correct source.\n   */\n  fallbackSourceRoute: string | undefined\n\n  prefetchDataRoute: string | null | undefined\n  prefetchDataRouteRegex: string | null | undefined\n  routeRegex: string\n\n  /**\n   * @deprecated use `renderingMode` instead\n   */\n  experimentalPPR: boolean | undefined\n\n  /**\n   * The rendering mode for this route. Only `undefined` when not an app router\n   * route.\n   */\n  renderingMode: RenderingMode | undefined\n\n  /**\n   * The headers that are allowed to be used when revalidating this route. These\n   * are used internally by Next.js to revalidate routes.\n   */\n  allowHeader: string[]\n}\n\n/**\n * The headers that are allowed to be used when revalidating routes. Currently\n * this includes both headers used by the pages and app routers.\n */\nconst ALLOWED_HEADERS: string[] = [\n  'host',\n  MATCHED_PATH_HEADER,\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n]\n\nexport type PrerenderManifest = {\n  version: 4\n  routes: { [route: string]: SsgRoute }\n  dynamicRoutes: { [route: string]: DynamicSsgRoute }\n  notFoundRoutes: string[]\n  preview: __ApiPreviewProps\n}\n\ntype ManifestBuiltRoute = {\n  /**\n   * The route pattern used to match requests for this route.\n   */\n  regex: string\n}\n\nexport type ManifestRewriteRoute = ManifestBuiltRoute & Rewrite\nexport type ManifestRedirectRoute = ManifestBuiltRoute & Redirect\nexport type ManifestHeaderRoute = ManifestBuiltRoute & Header\n\nexport type ManifestRoute = ManifestBuiltRoute & {\n  page: string\n  namedRegex?: string\n  routeKeys?: { [key: string]: string }\n  prefetchSegmentDataRoutes?: PrefetchSegmentDataRoute[]\n}\n\ntype ManifestDataRoute = {\n  page: string\n  routeKeys?: { [key: string]: string }\n  dataRouteRegex: string\n  namedDataRouteRegex?: string\n}\n\nexport type RoutesManifest = {\n  version: number\n  pages404: boolean\n  basePath: string\n  redirects: Array<Redirect>\n  rewrites?:\n    | Array<ManifestRewriteRoute>\n    | {\n        beforeFiles: Array<ManifestRewriteRoute>\n        afterFiles: Array<ManifestRewriteRoute>\n        fallback: Array<ManifestRewriteRoute>\n      }\n  headers: Array<ManifestHeaderRoute>\n  staticRoutes: Array<ManifestRoute>\n  dynamicRoutes: Array<ManifestRoute>\n  dataRoutes: Array<ManifestDataRoute>\n  i18n?: {\n    domains?: ReadonlyArray<{\n      http?: true\n      domain: string\n      locales?: readonly string[]\n      defaultLocale: string\n    }>\n    locales: readonly string[]\n    defaultLocale: string\n    localeDetection?: false\n  }\n  rsc: {\n    header: typeof RSC_HEADER\n    didPostponeHeader: typeof NEXT_DID_POSTPONE_HEADER\n    contentTypeHeader: typeof RSC_CONTENT_TYPE_HEADER\n    varyHeader: string\n    prefetchHeader: typeof NEXT_ROUTER_PREFETCH_HEADER\n    suffix: typeof RSC_SUFFIX\n    prefetchSuffix: typeof RSC_PREFETCH_SUFFIX\n    prefetchSegmentHeader: typeof NEXT_ROUTER_SEGMENT_PREFETCH_HEADER\n    prefetchSegmentDirSuffix: typeof RSC_SEGMENTS_DIR_SUFFIX\n    prefetchSegmentSuffix: typeof RSC_SEGMENT_SUFFIX\n  }\n  rewriteHeaders: {\n    pathHeader: typeof NEXT_REWRITTEN_PATH_HEADER\n    queryHeader: typeof NEXT_REWRITTEN_QUERY_HEADER\n  }\n  skipMiddlewareUrlNormalize?: boolean\n  caseSensitive?: boolean\n  /**\n   * Configuration related to Partial Prerendering.\n   */\n  ppr?: {\n    /**\n     * The chained response for the PPR resume.\n     */\n    chain: {\n      /**\n       * The headers that will indicate to Next.js that the request is for a PPR\n       * resume.\n       */\n      headers: Record<string, string>\n    }\n  }\n}\n\nfunction pageToRoute(page: string) {\n  const routeRegex = getNamedRouteRegex(page, {\n    prefixRouteKeys: true,\n  })\n  return {\n    page,\n    regex: normalizeRouteRegex(routeRegex.re.source),\n    routeKeys: routeRegex.routeKeys,\n    namedRegex: routeRegex.namedRegex,\n  }\n}\n\nfunction getCacheDir(distDir: string): string {\n  const cacheDir = path.join(distDir, 'cache')\n  if (ciEnvironment.isCI && !ciEnvironment.hasNextSupport) {\n    const hasCache = existsSync(cacheDir)\n\n    if (!hasCache) {\n      // Intentionally not piping to stderr which is what `Log.warn` does in case people fail in CI when\n      // stderr is detected.\n      console.log(\n        `${Log.prefixes.warn} No build cache found. Please configure build caching for faster rebuilds. Read more: https://nextjs.org/docs/messages/no-cache`\n      )\n    }\n  }\n  return cacheDir\n}\n\nasync function writeFileUtf8(filePath: string, content: string): Promise<void> {\n  await fs.writeFile(filePath, content, 'utf-8')\n}\n\nfunction readFileUtf8(filePath: string): Promise<string> {\n  return fs.readFile(filePath, 'utf8')\n}\n\nasync function writeManifest<T extends object>(\n  filePath: string,\n  manifest: T\n): Promise<void> {\n  await writeFileUtf8(filePath, formatManifest(manifest))\n}\n\nasync function readManifest<T extends object>(filePath: string): Promise<T> {\n  return JSON.parse(await readFileUtf8(filePath))\n}\n\nasync function writePrerenderManifest(\n  distDir: string,\n  manifest: DeepReadonly<PrerenderManifest>\n): Promise<void> {\n  await writeManifest(path.join(distDir, PRERENDER_MANIFEST), manifest)\n}\n\nasync function writeClientSsgManifest(\n  prerenderManifest: DeepReadonly<PrerenderManifest>,\n  {\n    buildId,\n    distDir,\n    locales,\n  }: {\n    buildId: string\n    distDir: string\n    locales: readonly string[] | undefined\n  }\n) {\n  const ssgPages = new Set<string>(\n    [\n      ...Object.entries(prerenderManifest.routes)\n        // Filter out dynamic routes\n        .filter(([, { srcRoute }]) => srcRoute == null)\n        .map(([route]) => normalizeLocalePath(route, locales).pathname),\n      ...Object.keys(prerenderManifest.dynamicRoutes),\n    ].sort()\n  )\n\n  const clientSsgManifestContent = `self.__SSG_MANIFEST=${devalue(\n    ssgPages\n  )};self.__SSG_MANIFEST_CB&&self.__SSG_MANIFEST_CB()`\n\n  await writeFileUtf8(\n    path.join(distDir, CLIENT_STATIC_FILES_PATH, buildId, '_ssgManifest.js'),\n    clientSsgManifestContent\n  )\n}\n\nexport interface FunctionsConfigManifest {\n  version: number\n  functions: Record<\n    string,\n    {\n      maxDuration?: number | undefined\n      runtime?: 'nodejs'\n      matchers?: Array<{\n        regexp: string\n        originalSource: string\n        has?: Rewrite['has']\n        missing?: Rewrite['has']\n      }>\n    }\n  >\n}\n\nasync function writeFunctionsConfigManifest(\n  distDir: string,\n  manifest: FunctionsConfigManifest\n): Promise<void> {\n  await writeManifest(\n    path.join(distDir, SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST),\n    manifest\n  )\n}\n\ninterface RequiredServerFilesManifest {\n  version: number\n  config: NextConfigComplete\n  appDir: string\n  relativeAppDir: string\n  files: string[]\n  ignore: string[]\n}\n\nasync function writeRequiredServerFilesManifest(\n  distDir: string,\n  requiredServerFiles: RequiredServerFilesManifest\n) {\n  await writeManifest(\n    path.join(distDir, SERVER_FILES_MANIFEST),\n    requiredServerFiles\n  )\n}\n\nasync function writeImagesManifest(\n  distDir: string,\n  config: NextConfigComplete\n): Promise<void> {\n  const images = { ...config.images }\n  const { deviceSizes, imageSizes } = images\n  ;(images as any).sizes = [...deviceSizes, ...imageSizes]\n\n  // By default, remotePatterns will allow no remote images ([])\n  images.remotePatterns = (config?.images?.remotePatterns || []).map((p) => ({\n    // Modifying the manifest should also modify matchRemotePattern()\n    protocol: p.protocol,\n    hostname: makeRe(p.hostname).source,\n    port: p.port,\n    pathname: makeRe(p.pathname ?? '**', { dot: true }).source,\n    search: p.search,\n  }))\n\n  // By default, localPatterns will allow all local images (undefined)\n  if (config?.images?.localPatterns) {\n    images.localPatterns = config.images.localPatterns.map((p) => ({\n      // Modifying the manifest should also modify matchLocalPattern()\n      pathname: makeRe(p.pathname ?? '**', { dot: true }).source,\n      search: p.search,\n    }))\n  }\n\n  await writeManifest(path.join(distDir, IMAGES_MANIFEST), {\n    version: 1,\n    images,\n  })\n}\n\nconst STANDALONE_DIRECTORY = 'standalone' as const\nasync function writeStandaloneDirectory(\n  nextBuildSpan: Span,\n  distDir: string,\n  pageKeys: { pages: string[]; app: string[] | undefined },\n  denormalizedAppPages: string[] | undefined,\n  outputFileTracingRoot: string,\n  requiredServerFiles: RequiredServerFilesManifest,\n  middlewareManifest: MiddlewareManifest,\n  hasNodeMiddleware: boolean,\n  hasInstrumentationHook: boolean,\n  staticPages: Set<string>,\n  loadedEnvFiles: LoadedEnvFiles,\n  appDir: string | undefined\n) {\n  await nextBuildSpan\n    .traceChild('write-standalone-directory')\n    .traceAsyncFn(async () => {\n      await copyTracedFiles(\n        // requiredServerFiles.appDir Refers to the application directory, not App Router.\n        requiredServerFiles.appDir,\n        distDir,\n        pageKeys.pages,\n        denormalizedAppPages,\n        outputFileTracingRoot,\n        requiredServerFiles.config,\n        middlewareManifest,\n        hasNodeMiddleware,\n        hasInstrumentationHook,\n        staticPages\n      )\n\n      for (const file of [\n        ...requiredServerFiles.files,\n        path.join(requiredServerFiles.config.distDir, SERVER_FILES_MANIFEST),\n        ...loadedEnvFiles.reduce<string[]>((acc, envFile) => {\n          if (['.env', '.env.production'].includes(envFile.path)) {\n            acc.push(envFile.path)\n          }\n          return acc\n        }, []),\n      ]) {\n        // requiredServerFiles.appDir Refers to the application directory, not App Router.\n        const filePath = path.join(requiredServerFiles.appDir, file)\n        const outputPath = path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, filePath)\n        )\n        await fs.mkdir(path.dirname(outputPath), {\n          recursive: true,\n        })\n        await fs.copyFile(filePath, outputPath)\n      }\n\n      if (hasNodeMiddleware) {\n        const middlewareOutput = path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, distDir),\n          SERVER_DIRECTORY,\n          'middleware.js'\n        )\n\n        await fs.mkdir(path.dirname(middlewareOutput), { recursive: true })\n        await fs.copyFile(\n          path.join(distDir, SERVER_DIRECTORY, 'middleware.js'),\n          middlewareOutput\n        )\n      }\n\n      await recursiveCopy(\n        path.join(distDir, SERVER_DIRECTORY, 'pages'),\n        path.join(\n          distDir,\n          STANDALONE_DIRECTORY,\n          path.relative(outputFileTracingRoot, distDir),\n          SERVER_DIRECTORY,\n          'pages'\n        ),\n        { overwrite: true }\n      )\n      if (appDir) {\n        const originalServerApp = path.join(distDir, SERVER_DIRECTORY, 'app')\n        if (existsSync(originalServerApp)) {\n          await recursiveCopy(\n            originalServerApp,\n            path.join(\n              distDir,\n              STANDALONE_DIRECTORY,\n              path.relative(outputFileTracingRoot, distDir),\n              SERVER_DIRECTORY,\n              'app'\n            ),\n            { overwrite: true }\n          )\n        }\n      }\n    })\n}\n\nfunction getNumberOfWorkers(config: NextConfigComplete) {\n  if (\n    config.experimental.cpus &&\n    config.experimental.cpus !== defaultConfig.experimental!.cpus\n  ) {\n    return config.experimental.cpus\n  }\n\n  if (config.experimental.memoryBasedWorkersCount) {\n    return Math.max(\n      Math.min(config.experimental.cpus || 1, Math.floor(os.freemem() / 1e9)),\n      // enforce a minimum of 4 workers\n      4\n    )\n  }\n\n  if (config.experimental.cpus) {\n    return config.experimental.cpus\n  }\n\n  // Fall back to 4 workers if a count is not specified\n  return 4\n}\n\nconst staticWorkerPath = require.resolve('./worker')\nconst staticWorkerExposedMethods = [\n  'hasCustomGetInitialProps',\n  'isPageStatic',\n  'getDefinedNamedExports',\n  'exportPages',\n] as const\ntype StaticWorker = typeof import('./worker') & Worker\nexport function createStaticWorker(\n  config: NextConfigComplete,\n  progress?: {\n    run: () => void\n    clear: () => void\n  }\n): StaticWorker {\n  // Get the node options without inspect and also remove the\n  // --max-old-space-size flag as it can cause memory issues.\n  const nodeOptions = getParsedNodeOptionsWithoutInspect()\n  delete nodeOptions['max-old-space-size']\n  delete nodeOptions['max_old_space_size']\n\n  return new Worker(staticWorkerPath, {\n    logger: Log,\n    numWorkers: getNumberOfWorkers(config),\n    onActivity: () => {\n      progress?.run()\n    },\n    onActivityAbort: () => {\n      progress?.clear()\n    },\n    forkOptions: {\n      env: { ...process.env, NODE_OPTIONS: formatNodeOptions(nodeOptions) },\n    },\n    enableWorkerThreads: config.experimental.workerThreads,\n    exposedMethods: staticWorkerExposedMethods,\n  }) as StaticWorker\n}\n\nasync function writeFullyStaticExport(\n  config: NextConfigComplete,\n  dir: string,\n  enabledDirectories: NextEnabledDirectories,\n  configOutDir: string,\n  nextBuildSpan: Span\n): Promise<void> {\n  const exportApp = require('../export')\n    .default as typeof import('../export').default\n\n  const pagesWorker = createStaticWorker(config)\n  const appWorker = createStaticWorker(config)\n\n  await exportApp(\n    dir,\n    {\n      buildExport: false,\n      nextConfig: config,\n      enabledDirectories,\n      silent: true,\n      outdir: path.join(dir, configOutDir),\n      numWorkers: getNumberOfWorkers(config),\n    },\n    nextBuildSpan\n  )\n\n  pagesWorker.end()\n  appWorker.end()\n}\n\nasync function getBuildId(\n  isGenerateMode: boolean,\n  distDir: string,\n  nextBuildSpan: Span,\n  config: NextConfigComplete\n) {\n  if (isGenerateMode) {\n    return await fs.readFile(path.join(distDir, 'BUILD_ID'), 'utf8')\n  }\n  return await nextBuildSpan\n    .traceChild('generate-buildid')\n    .traceAsyncFn(() => generateBuildId(config.generateBuildId, nanoid))\n}\n\nexport default async function build(\n  dir: string,\n  reactProductionProfiling = false,\n  debugOutput = false,\n  runLint = true,\n  noMangling = false,\n  appDirOnly = false,\n  turboNextBuild = false,\n  experimentalBuildMode: 'default' | 'compile' | 'generate',\n  traceUploadUrl: string | undefined\n): Promise<void> {\n  const isCompileMode = experimentalBuildMode === 'compile'\n  const isGenerateMode = experimentalBuildMode === 'generate'\n\n  let loadedConfig: NextConfigComplete | undefined\n  try {\n    const nextBuildSpan = trace('next-build', undefined, {\n      buildMode: experimentalBuildMode,\n      isTurboBuild: String(turboNextBuild),\n      version: process.env.__NEXT_VERSION as string,\n    })\n\n    NextBuildContext.nextBuildSpan = nextBuildSpan\n    NextBuildContext.dir = dir\n    NextBuildContext.appDirOnly = appDirOnly\n    NextBuildContext.reactProductionProfiling = reactProductionProfiling\n    NextBuildContext.noMangling = noMangling\n\n    await nextBuildSpan.traceAsyncFn(async () => {\n      // attempt to load global env values so they are available in next.config.js\n      const { loadedEnvFiles } = nextBuildSpan\n        .traceChild('load-dotenv')\n        .traceFn(() => loadEnvConfig(dir, false, Log))\n      NextBuildContext.loadedEnvFiles = loadedEnvFiles\n\n      const turborepoAccessTraceResult = new TurborepoAccessTraceResult()\n      const config: NextConfigComplete = await nextBuildSpan\n        .traceChild('load-next-config')\n        .traceAsyncFn(() =>\n          turborepoTraceAccess(\n            () =>\n              loadConfig(PHASE_PRODUCTION_BUILD, dir, {\n                // Log for next.config loading process\n                silent: false,\n                reactProductionProfiling,\n              }),\n            turborepoAccessTraceResult\n          )\n        )\n      loadedConfig = config\n\n      process.env.NEXT_DEPLOYMENT_ID = config.deploymentId || ''\n      NextBuildContext.config = config\n\n      let configOutDir = 'out'\n      if (hasCustomExportOutput(config)) {\n        configOutDir = config.distDir\n        config.distDir = '.next'\n      }\n      const distDir = path.join(dir, config.distDir)\n      NextBuildContext.distDir = distDir\n      setGlobal('phase', PHASE_PRODUCTION_BUILD)\n      setGlobal('distDir', distDir)\n\n      const buildId = await getBuildId(\n        isGenerateMode,\n        distDir,\n        nextBuildSpan,\n        config\n      )\n      NextBuildContext.buildId = buildId\n\n      const customRoutes: CustomRoutes = await nextBuildSpan\n        .traceChild('load-custom-routes')\n        .traceAsyncFn(() => loadCustomRoutes(config))\n\n      const { headers, rewrites, redirects } = customRoutes\n      const combinedRewrites: Rewrite[] = [\n        ...rewrites.beforeFiles,\n        ...rewrites.afterFiles,\n        ...rewrites.fallback,\n      ]\n      const hasRewrites = combinedRewrites.length > 0\n      NextBuildContext.hasRewrites = hasRewrites\n      NextBuildContext.originalRewrites = config._originalRewrites\n      NextBuildContext.originalRedirects = config._originalRedirects\n\n      const cacheDir = getCacheDir(distDir)\n\n      const telemetry = new Telemetry({ distDir })\n\n      setGlobal('telemetry', telemetry)\n\n      const publicDir = path.join(dir, 'public')\n      const { pagesDir, appDir } = findPagesDir(dir)\n      NextBuildContext.pagesDir = pagesDir\n      NextBuildContext.appDir = appDir\n\n      const enabledDirectories: NextEnabledDirectories = {\n        app: typeof appDir === 'string',\n        pages: typeof pagesDir === 'string',\n      }\n\n      // Generate a random encryption key for this build.\n      // This key is used to encrypt cross boundary values and can be used to generate hashes.\n      const encryptionKey = await generateEncryptionKeyBase64({\n        isBuild: true,\n        distDir,\n      })\n      NextBuildContext.encryptionKey = encryptionKey\n\n      const isSrcDir = path\n        .relative(dir, pagesDir || appDir || '')\n        .startsWith('src')\n      const hasPublicDir = existsSync(publicDir)\n\n      telemetry.record(\n        eventCliSession(dir, config, {\n          webpackVersion: 5,\n          cliCommand: 'build',\n          isSrcDir,\n          hasNowJson: !!(await findUp('now.json', { cwd: dir })),\n          isCustomServer: null,\n          turboFlag: false,\n          pagesDir: !!pagesDir,\n          appDir: !!appDir,\n        })\n      )\n\n      eventNextPlugins(path.resolve(dir)).then((events) =>\n        telemetry.record(events)\n      )\n\n      eventSwcPlugins(path.resolve(dir), config).then((events) =>\n        telemetry.record(events)\n      )\n\n      // Always log next version first then start rest jobs\n      const { envInfo, experimentalFeatures } = await getStartServerInfo(\n        dir,\n        false\n      )\n      logStartInfo({\n        networkUrl: null,\n        appUrl: null,\n        envInfo,\n        experimentalFeatures,\n      })\n\n      const ignoreESLint = Boolean(config.eslint.ignoreDuringBuilds)\n      const shouldLint = !ignoreESLint && runLint\n\n      const typeCheckingOptions: Parameters<typeof startTypeChecking>[0] = {\n        dir,\n        appDir,\n        pagesDir,\n        runLint,\n        shouldLint,\n        ignoreESLint,\n        telemetry,\n        nextBuildSpan,\n        config,\n        cacheDir,\n      }\n\n      const distDirCreated = await nextBuildSpan\n        .traceChild('create-dist-dir')\n        .traceAsyncFn(async () => {\n          try {\n            await fs.mkdir(distDir, { recursive: true })\n            return true\n          } catch (err) {\n            if (isError(err) && err.code === 'EPERM') {\n              return false\n            }\n            throw err\n          }\n        })\n\n      if (!distDirCreated || !(await isWriteable(distDir))) {\n        throw new Error(\n          '> Build directory is not writeable. https://nextjs.org/docs/messages/build-dir-not-writeable'\n        )\n      }\n\n      if (config.cleanDistDir && !isGenerateMode) {\n        await recursiveDelete(distDir, /^cache/)\n      }\n\n      // For app directory, we run type checking after build. That's because\n      // we dynamically generate types for each layout and page in the app\n      // directory.\n      if (!appDir && !isCompileMode)\n        await startTypeChecking(typeCheckingOptions)\n\n      if (appDir && 'exportPathMap' in config) {\n        Log.error(\n          'The \"exportPathMap\" configuration cannot be used with the \"app\" directory. Please use generateStaticParams() instead.'\n        )\n        await telemetry.flush()\n        process.exit(1)\n      }\n\n      const buildLintEvent: EventBuildFeatureUsage = {\n        featureName: 'build-lint',\n        invocationCount: shouldLint ? 1 : 0,\n      }\n      telemetry.record({\n        eventName: EVENT_BUILD_FEATURE_USAGE,\n        payload: buildLintEvent,\n      })\n\n      const validFileMatcher = createValidFileMatcher(\n        config.pageExtensions,\n        appDir\n      )\n\n      const providedPagePaths: string[] = JSON.parse(\n        process.env.NEXT_PRIVATE_PAGE_PATHS || '[]'\n      )\n\n      let pagesPaths = Boolean(process.env.NEXT_PRIVATE_PAGE_PATHS)\n        ? providedPagePaths\n        : !appDirOnly && pagesDir\n          ? await nextBuildSpan.traceChild('collect-pages').traceAsyncFn(() =>\n              recursiveReadDir(pagesDir, {\n                pathnameFilter: validFileMatcher.isPageFile,\n              })\n            )\n          : []\n\n      const middlewareDetectionRegExp = new RegExp(\n        `^${MIDDLEWARE_FILENAME}\\\\.(?:${config.pageExtensions.join('|')})$`\n      )\n\n      const instrumentationHookDetectionRegExp = new RegExp(\n        `^${INSTRUMENTATION_HOOK_FILENAME}\\\\.(?:${config.pageExtensions.join(\n          '|'\n        )})$`\n      )\n\n      const rootDir = path.join((pagesDir || appDir)!, '..')\n      const includes = [\n        middlewareDetectionRegExp,\n        instrumentationHookDetectionRegExp,\n      ]\n\n      const rootPaths = Array.from(await getFilesInDir(rootDir))\n        .filter((file) => includes.some((include) => include.test(file)))\n        .sort(sortByPageExts(config.pageExtensions))\n        .map((file) => path.join(rootDir, file).replace(dir, ''))\n\n      const hasInstrumentationHook = rootPaths.some((p) =>\n        p.includes(INSTRUMENTATION_HOOK_FILENAME)\n      )\n      const hasMiddlewareFile = rootPaths.some((p) =>\n        p.includes(MIDDLEWARE_FILENAME)\n      )\n\n      NextBuildContext.hasInstrumentationHook = hasInstrumentationHook\n\n      const previewProps: __ApiPreviewProps = {\n        previewModeId: crypto.randomBytes(16).toString('hex'),\n        previewModeSigningKey: crypto.randomBytes(32).toString('hex'),\n        previewModeEncryptionKey: crypto.randomBytes(32).toString('hex'),\n      }\n      NextBuildContext.previewProps = previewProps\n\n      const mappedPages = await nextBuildSpan\n        .traceChild('create-pages-mapping')\n        .traceAsyncFn(() =>\n          createPagesMapping({\n            isDev: false,\n            pageExtensions: config.pageExtensions,\n            pagesType: PAGE_TYPES.PAGES,\n            pagePaths: pagesPaths,\n            pagesDir,\n            appDir,\n          })\n        )\n      NextBuildContext.mappedPages = mappedPages\n\n      let mappedAppPages: MappedPages | undefined\n      let denormalizedAppPages: string[] | undefined\n\n      if (appDir) {\n        const providedAppPaths: string[] = JSON.parse(\n          process.env.NEXT_PRIVATE_APP_PATHS || '[]'\n        )\n\n        let appPaths = Boolean(process.env.NEXT_PRIVATE_APP_PATHS)\n          ? providedAppPaths\n          : await nextBuildSpan\n              .traceChild('collect-app-paths')\n              .traceAsyncFn(() =>\n                recursiveReadDir(appDir, {\n                  pathnameFilter: (absolutePath) =>\n                    validFileMatcher.isAppRouterPage(absolutePath) ||\n                    // For now we only collect the root /not-found page in the app\n                    // directory as the 404 fallback\n                    validFileMatcher.isRootNotFound(absolutePath),\n                  ignorePartFilter: (part) => part.startsWith('_'),\n                })\n              )\n\n        mappedAppPages = await nextBuildSpan\n          .traceChild('create-app-mapping')\n          .traceAsyncFn(() =>\n            createPagesMapping({\n              pagePaths: appPaths,\n              isDev: false,\n              pagesType: PAGE_TYPES.APP,\n              pageExtensions: config.pageExtensions,\n              pagesDir,\n              appDir,\n            })\n          )\n\n        NextBuildContext.mappedAppPages = mappedAppPages\n      }\n\n      const mappedRootPaths = await createPagesMapping({\n        isDev: false,\n        pageExtensions: config.pageExtensions,\n        pagePaths: rootPaths,\n        pagesType: PAGE_TYPES.ROOT,\n        pagesDir: pagesDir,\n        appDir,\n      })\n      NextBuildContext.mappedRootPaths = mappedRootPaths\n\n      const pagesPageKeys = Object.keys(mappedPages)\n\n      const conflictingAppPagePaths: [pagePath: string, appPath: string][] = []\n      const appPageKeys = new Set<string>()\n      if (mappedAppPages) {\n        denormalizedAppPages = Object.keys(mappedAppPages)\n        for (const appKey of denormalizedAppPages) {\n          const normalizedAppPageKey = normalizeAppPath(appKey)\n          const pagePath = mappedPages[normalizedAppPageKey]\n          if (pagePath) {\n            const appPath = mappedAppPages[appKey]\n            conflictingAppPagePaths.push([\n              pagePath.replace(/^private-next-pages/, 'pages'),\n              appPath.replace(/^private-next-app-dir/, 'app'),\n            ])\n          }\n          appPageKeys.add(normalizedAppPageKey)\n        }\n      }\n\n      const appPaths = Array.from(appPageKeys)\n      // Interception routes are modelled as beforeFiles rewrites\n      rewrites.beforeFiles.push(\n        ...generateInterceptionRoutesRewrites(appPaths, config.basePath)\n      )\n\n      NextBuildContext.rewrites = rewrites\n\n      const totalAppPagesCount = appPaths.length\n\n      const pageKeys = {\n        pages: pagesPageKeys,\n        app: appPaths.length > 0 ? appPaths : undefined,\n      }\n\n      // Turbopack already handles conflicting app and page routes.\n      if (!turboNextBuild) {\n        const numConflictingAppPaths = conflictingAppPagePaths.length\n        if (mappedAppPages && numConflictingAppPaths > 0) {\n          Log.error(\n            `Conflicting app and page file${\n              numConflictingAppPaths === 1 ? ' was' : 's were'\n            } found, please remove the conflicting files to continue:`\n          )\n          for (const [pagePath, appPath] of conflictingAppPagePaths) {\n            Log.error(`  \"${pagePath}\" - \"${appPath}\"`)\n          }\n          await telemetry.flush()\n          process.exit(1)\n        }\n      }\n\n      const conflictingPublicFiles: string[] = []\n      const hasPages404 = mappedPages['/404']?.startsWith(PAGES_DIR_ALIAS)\n      const hasApp404 = !!mappedAppPages?.[UNDERSCORE_NOT_FOUND_ROUTE_ENTRY]\n      const hasCustomErrorPage =\n        mappedPages['/_error'].startsWith(PAGES_DIR_ALIAS)\n\n      if (hasPublicDir) {\n        const hasPublicUnderScoreNextDir = existsSync(\n          path.join(publicDir, '_next')\n        )\n        if (hasPublicUnderScoreNextDir) {\n          throw new Error(PUBLIC_DIR_MIDDLEWARE_CONFLICT)\n        }\n      }\n\n      await nextBuildSpan\n        .traceChild('public-dir-conflict-check')\n        .traceAsyncFn(async () => {\n          // Check if pages conflict with files in `public`\n          // Only a page of public file can be served, not both.\n          for (const page in mappedPages) {\n            const hasPublicPageFile = await fileExists(\n              path.join(publicDir, page === '/' ? '/index' : page),\n              FileType.File\n            )\n            if (hasPublicPageFile) {\n              conflictingPublicFiles.push(page)\n            }\n          }\n\n          const numConflicting = conflictingPublicFiles.length\n\n          if (numConflicting) {\n            throw new Error(\n              `Conflicting public and page file${\n                numConflicting === 1 ? ' was' : 's were'\n              } found. https://nextjs.org/docs/messages/conflicting-public-file-page\\n${conflictingPublicFiles.join(\n                '\\n'\n              )}`\n            )\n          }\n        })\n\n      const nestedReservedPages = pageKeys.pages.filter((page) => {\n        return (\n          page.match(/\\/(_app|_document|_error)$/) && path.dirname(page) !== '/'\n        )\n      })\n\n      if (nestedReservedPages.length) {\n        Log.warn(\n          `The following reserved Next.js pages were detected not directly under the pages directory:\\n` +\n            nestedReservedPages.join('\\n') +\n            `\\nSee more info here: https://nextjs.org/docs/messages/nested-reserved-page\\n`\n        )\n      }\n\n      const restrictedRedirectPaths = ['/_next'].map((p) =>\n        config.basePath ? `${config.basePath}${p}` : p\n      )\n\n      const isAppDynamicIOEnabled = Boolean(config.experimental.dynamicIO)\n      const isAuthInterruptsEnabled = Boolean(\n        config.experimental.authInterrupts\n      )\n      const isAppPPREnabled = checkIsAppPPREnabled(config.experimental.ppr)\n\n      const routesManifestPath = path.join(distDir, ROUTES_MANIFEST)\n      const routesManifest: RoutesManifest = nextBuildSpan\n        .traceChild('generate-routes-manifest')\n        .traceFn(() => {\n          const sortedRoutes = getSortedRoutes([\n            ...pageKeys.pages,\n            ...(pageKeys.app ?? []),\n          ])\n          const dynamicRoutes: Array<ReturnType<typeof pageToRoute>> = []\n          const staticRoutes: typeof dynamicRoutes = []\n\n          for (const route of sortedRoutes) {\n            if (isDynamicRoute(route)) {\n              dynamicRoutes.push(pageToRoute(route))\n            } else if (!isReservedPage(route)) {\n              staticRoutes.push(pageToRoute(route))\n            }\n          }\n\n          return {\n            version: 3,\n            pages404: true,\n            caseSensitive: !!config.experimental.caseSensitiveRoutes,\n            basePath: config.basePath,\n            redirects: redirects.map((r) =>\n              buildCustomRoute('redirect', r, restrictedRedirectPaths)\n            ),\n            headers: headers.map((r) => buildCustomRoute('header', r)),\n            dynamicRoutes,\n            staticRoutes,\n            dataRoutes: [],\n            i18n: config.i18n || undefined,\n            rsc: {\n              header: RSC_HEADER,\n              // This vary header is used as a default. It is technically re-assigned in `base-server`,\n              // and may include an additional Vary option for `Next-URL`.\n              varyHeader: `${RSC_HEADER}, ${NEXT_ROUTER_STATE_TREE_HEADER}, ${NEXT_ROUTER_PREFETCH_HEADER}, ${NEXT_ROUTER_SEGMENT_PREFETCH_HEADER}`,\n              prefetchHeader: NEXT_ROUTER_PREFETCH_HEADER,\n              didPostponeHeader: NEXT_DID_POSTPONE_HEADER,\n              contentTypeHeader: RSC_CONTENT_TYPE_HEADER,\n              suffix: RSC_SUFFIX,\n              prefetchSuffix: RSC_PREFETCH_SUFFIX,\n              prefetchSegmentHeader: NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n              prefetchSegmentSuffix: RSC_SEGMENT_SUFFIX,\n              prefetchSegmentDirSuffix: RSC_SEGMENTS_DIR_SUFFIX,\n            },\n            rewriteHeaders: {\n              pathHeader: NEXT_REWRITTEN_PATH_HEADER,\n              queryHeader: NEXT_REWRITTEN_QUERY_HEADER,\n            },\n            skipMiddlewareUrlNormalize: config.skipMiddlewareUrlNormalize,\n            ppr: isAppPPREnabled\n              ? {\n                  chain: {\n                    headers: {\n                      [NEXT_RESUME_HEADER]: '1',\n                    },\n                  },\n                }\n              : undefined,\n          } satisfies RoutesManifest\n        })\n\n      if (rewrites.beforeFiles.length === 0 && rewrites.fallback.length === 0) {\n        routesManifest.rewrites = rewrites.afterFiles.map((r) =>\n          buildCustomRoute('rewrite', r)\n        )\n      } else {\n        routesManifest.rewrites = {\n          beforeFiles: rewrites.beforeFiles.map((r) =>\n            buildCustomRoute('rewrite', r)\n          ),\n          afterFiles: rewrites.afterFiles.map((r) =>\n            buildCustomRoute('rewrite', r)\n          ),\n          fallback: rewrites.fallback.map((r) =>\n            buildCustomRoute('rewrite', r)\n          ),\n        }\n      }\n      let clientRouterFilters:\n        | undefined\n        | ReturnType<typeof createClientRouterFilter>\n\n      if (config.experimental.clientRouterFilter) {\n        const nonInternalRedirects = (config._originalRedirects || []).filter(\n          (r: any) => !r.internal\n        )\n        clientRouterFilters = createClientRouterFilter(\n          [...appPaths],\n          config.experimental.clientRouterFilterRedirects\n            ? nonInternalRedirects\n            : [],\n          config.experimental.clientRouterFilterAllowedRate\n        )\n        NextBuildContext.clientRouterFilters = clientRouterFilters\n      }\n\n      // Ensure commonjs handling is used for files in the distDir (generally .next)\n      // Files outside of the distDir can be \"type\": \"module\"\n      await writeFileUtf8(\n        path.join(distDir, 'package.json'),\n        '{\"type\": \"commonjs\"}'\n      )\n\n      // These are written to distDir, so they need to come after creating and cleaning distDr.\n      await recordFrameworkVersion(process.env.__NEXT_VERSION as string)\n      await updateBuildDiagnostics({\n        buildStage: 'start',\n      })\n\n      const outputFileTracingRoot = config.outputFileTracingRoot || dir\n\n      const pagesManifestPath = path.join(\n        distDir,\n        SERVER_DIRECTORY,\n        PAGES_MANIFEST\n      )\n\n      let buildTraceContext: undefined | BuildTraceContext\n      let buildTracesPromise: Promise<any> | undefined = undefined\n\n      // If there's has a custom webpack config and disable the build worker.\n      // Otherwise respect the option if it's set.\n      const useBuildWorker =\n        config.experimental.webpackBuildWorker ||\n        (config.experimental.webpackBuildWorker === undefined &&\n          !config.webpack)\n      const runServerAndEdgeInParallel =\n        config.experimental.parallelServerCompiles\n      const collectServerBuildTracesInParallel =\n        config.experimental.parallelServerBuildTraces ||\n        (config.experimental.parallelServerBuildTraces === undefined &&\n          isCompileMode)\n\n      nextBuildSpan.setAttribute(\n        'has-custom-webpack-config',\n        String(!!config.webpack)\n      )\n      nextBuildSpan.setAttribute('use-build-worker', String(useBuildWorker))\n\n      if (\n        !useBuildWorker &&\n        (runServerAndEdgeInParallel || collectServerBuildTracesInParallel)\n      ) {\n        throw new Error(\n          'The \"parallelServerBuildTraces\" and \"parallelServerCompiles\" options may only be used when build workers can be used. Read more: https://nextjs.org/docs/messages/parallel-build-without-worker'\n        )\n      }\n\n      Log.info('Creating an optimized production build ...')\n      traceMemoryUsage('Starting build', nextBuildSpan)\n\n      await updateBuildDiagnostics({\n        buildStage: 'compile',\n        buildOptions: {\n          useBuildWorker: String(useBuildWorker),\n        },\n      })\n\n      let shutdownPromise = Promise.resolve()\n      if (!isGenerateMode) {\n        if (turboNextBuild) {\n          const {\n            duration: compilerDuration,\n            shutdownPromise: p,\n            ...rest\n          } = await turbopackBuild(\n            process.env.NEXT_TURBOPACK_USE_WORKER === undefined ||\n              process.env.NEXT_TURBOPACK_USE_WORKER !== '0'\n          )\n          shutdownPromise = p\n          traceMemoryUsage('Finished build', nextBuildSpan)\n\n          buildTraceContext = rest.buildTraceContext\n\n          let durationString\n          if (compilerDuration > 120) {\n            durationString = `${Math.round(compilerDuration / 6) / 10}min`\n          } else if (compilerDuration > 20) {\n            durationString = `${Math.round(compilerDuration)}s`\n          } else if (compilerDuration > 2) {\n            durationString = `${Math.round(compilerDuration * 10) / 10}s`\n          } else {\n            durationString = `${Math.round(compilerDuration * 1000)}ms`\n          }\n          Log.event(`Compiled successfully in ${durationString}`)\n\n          telemetry.record(\n            eventBuildCompleted(pagesPaths, {\n              durationInSeconds: Math.round(compilerDuration),\n              totalAppPagesCount,\n            })\n          )\n        } else {\n          if (\n            runServerAndEdgeInParallel ||\n            collectServerBuildTracesInParallel\n          ) {\n            let durationInSeconds = 0\n\n            await updateBuildDiagnostics({\n              buildStage: 'compile-server',\n            })\n\n            const serverBuildPromise = webpackBuild(useBuildWorker, [\n              'server',\n            ]).then((res) => {\n              traceMemoryUsage('Finished server compilation', nextBuildSpan)\n              buildTraceContext = res.buildTraceContext\n              durationInSeconds += res.duration\n\n              if (collectServerBuildTracesInParallel) {\n                const buildTraceWorker = new Worker(\n                  require.resolve('./collect-build-traces'),\n                  {\n                    numWorkers: 1,\n                    exposedMethods: ['collectBuildTraces'],\n                  }\n                ) as Worker & typeof import('./collect-build-traces')\n\n                buildTracesPromise = buildTraceWorker\n                  .collectBuildTraces({\n                    dir,\n                    config,\n                    distDir,\n                    // Serialize Map as this is sent to the worker.\n                    edgeRuntimeRoutes: collectRoutesUsingEdgeRuntime(new Map()),\n                    staticPages: [],\n                    hasSsrAmpPages: false,\n                    buildTraceContext,\n                    outputFileTracingRoot,\n                  })\n                  .catch((err) => {\n                    console.error(err)\n                    process.exit(1)\n                  })\n              }\n            })\n            if (!runServerAndEdgeInParallel) {\n              await serverBuildPromise\n              await updateBuildDiagnostics({\n                buildStage: 'webpack-compile-edge-server',\n              })\n            }\n\n            const edgeBuildPromise = webpackBuild(useBuildWorker, [\n              'edge-server',\n            ]).then((res) => {\n              durationInSeconds += res.duration\n              traceMemoryUsage(\n                'Finished edge-server compilation',\n                nextBuildSpan\n              )\n            })\n            if (runServerAndEdgeInParallel) {\n              await serverBuildPromise\n              await updateBuildDiagnostics({\n                buildStage: 'webpack-compile-edge-server',\n              })\n            }\n            await edgeBuildPromise\n\n            await updateBuildDiagnostics({\n              buildStage: 'webpack-compile-client',\n            })\n\n            await webpackBuild(useBuildWorker, ['client']).then((res) => {\n              durationInSeconds += res.duration\n              traceMemoryUsage('Finished client compilation', nextBuildSpan)\n            })\n\n            Log.event('Compiled successfully')\n\n            telemetry.record(\n              eventBuildCompleted(pagesPaths, {\n                durationInSeconds,\n                totalAppPagesCount,\n              })\n            )\n          } else {\n            const { duration: compilerDuration, ...rest } = await webpackBuild(\n              useBuildWorker,\n              null\n            )\n            traceMemoryUsage('Finished build', nextBuildSpan)\n\n            buildTraceContext = rest.buildTraceContext\n\n            telemetry.record(\n              eventBuildCompleted(pagesPaths, {\n                durationInSeconds: compilerDuration,\n                totalAppPagesCount,\n              })\n            )\n          }\n        }\n      }\n\n      // For app directory, we run type checking after build.\n      if (appDir && !isCompileMode && !isGenerateMode) {\n        await updateBuildDiagnostics({\n          buildStage: 'type-checking',\n        })\n        await startTypeChecking(typeCheckingOptions)\n        traceMemoryUsage('Finished type checking', nextBuildSpan)\n      }\n\n      const postCompileSpinner = createSpinner('Collecting page data')\n\n      const buildManifestPath = path.join(distDir, BUILD_MANIFEST)\n      const appBuildManifestPath = path.join(distDir, APP_BUILD_MANIFEST)\n\n      let staticAppPagesCount = 0\n      let serverAppPagesCount = 0\n      let edgeRuntimeAppCount = 0\n      let edgeRuntimePagesCount = 0\n      const ssgPages = new Set<string>()\n      const ssgStaticFallbackPages = new Set<string>()\n      const ssgBlockingFallbackPages = new Set<string>()\n      const staticPages = new Set<string>()\n      const invalidPages = new Set<string>()\n      const hybridAmpPages = new Set<string>()\n      const serverPropsPages = new Set<string>()\n      const additionalPaths = new Map<string, PrerenderedRoute[]>()\n      const staticPaths = new Map<string, PrerenderedRoute[]>()\n      const prospectiveRenders = new Map<\n        string,\n        { page: string; originalAppPath: string }\n      >()\n      const appNormalizedPaths = new Map<string, string>()\n      const fallbackModes = new Map<string, FallbackMode>()\n      const appDefaultConfigs = new Map<string, AppSegmentConfig>()\n      const pageInfos: PageInfos = new Map<string, PageInfo>()\n      let pagesManifest = await readManifest<PagesManifest>(pagesManifestPath)\n      const buildManifest = await readManifest<BuildManifest>(buildManifestPath)\n      const appBuildManifest = appDir\n        ? await readManifest<AppBuildManifest>(appBuildManifestPath)\n        : undefined\n\n      const appPathRoutes: Record<string, string> = {}\n\n      if (appDir) {\n        const appPathsManifest = await readManifest<Record<string, string>>(\n          path.join(distDir, SERVER_DIRECTORY, APP_PATHS_MANIFEST)\n        )\n\n        for (const key in appPathsManifest) {\n          appPathRoutes[key] = normalizeAppPath(key)\n        }\n\n        await writeManifest(\n          path.join(distDir, APP_PATH_ROUTES_MANIFEST),\n          appPathRoutes\n        )\n      }\n\n      process.env.NEXT_PHASE = PHASE_PRODUCTION_BUILD\n\n      const worker = createStaticWorker(config)\n\n      const analysisBegin = process.hrtime()\n      const staticCheckSpan = nextBuildSpan.traceChild('static-check')\n\n      const functionsConfigManifest: FunctionsConfigManifest = {\n        version: 1,\n        functions: {},\n      }\n\n      const {\n        customAppGetInitialProps,\n        namedExports,\n        isNextImageImported,\n        hasSsrAmpPages,\n        hasNonStaticErrorPage,\n      } = await staticCheckSpan.traceAsyncFn(async () => {\n        if (isCompileMode) {\n          return {\n            customAppGetInitialProps: false,\n            namedExports: [],\n            isNextImageImported: true,\n            hasSsrAmpPages: !!pagesDir,\n            hasNonStaticErrorPage: true,\n          }\n        }\n\n        const { configFileName, publicRuntimeConfig, serverRuntimeConfig } =\n          config\n        const runtimeEnvConfig = { publicRuntimeConfig, serverRuntimeConfig }\n        const sriEnabled = Boolean(config.experimental.sri?.algorithm)\n\n        const nonStaticErrorPageSpan = staticCheckSpan.traceChild(\n          'check-static-error-page'\n        )\n        const errorPageHasCustomGetInitialProps =\n          nonStaticErrorPageSpan.traceAsyncFn(\n            async () =>\n              hasCustomErrorPage &&\n              (await worker.hasCustomGetInitialProps({\n                page: '/_error',\n                distDir,\n                runtimeEnvConfig,\n                checkingApp: false,\n                sriEnabled,\n              }))\n          )\n\n        const errorPageStaticResult = nonStaticErrorPageSpan.traceAsyncFn(\n          async () =>\n            hasCustomErrorPage &&\n            worker.isPageStatic({\n              dir,\n              page: '/_error',\n              distDir,\n              configFileName,\n              runtimeEnvConfig,\n              dynamicIO: isAppDynamicIOEnabled,\n              authInterrupts: isAuthInterruptsEnabled,\n              httpAgentOptions: config.httpAgentOptions,\n              locales: config.i18n?.locales,\n              defaultLocale: config.i18n?.defaultLocale,\n              nextConfigOutput: config.output,\n              pprConfig: config.experimental.ppr,\n              cacheLifeProfiles: config.experimental.cacheLife,\n              buildId,\n              sriEnabled,\n            })\n        )\n\n        const appPageToCheck = '/_app'\n\n        const customAppGetInitialPropsPromise = worker.hasCustomGetInitialProps(\n          {\n            page: appPageToCheck,\n            distDir,\n            runtimeEnvConfig,\n            checkingApp: true,\n            sriEnabled,\n          }\n        )\n\n        const namedExportsPromise = worker.getDefinedNamedExports({\n          page: appPageToCheck,\n          distDir,\n          runtimeEnvConfig,\n          sriEnabled,\n        })\n\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        let isNextImageImported: boolean | undefined\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        let hasSsrAmpPages = false\n\n        const computedManifestData = await computeFromManifest(\n          { build: buildManifest, app: appBuildManifest },\n          distDir,\n          config.experimental.gzipSize\n        )\n\n        const middlewareManifest: MiddlewareManifest = require(\n          path.join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n        )\n\n        const actionManifest = appDir\n          ? (require(\n              path.join(\n                distDir,\n                SERVER_DIRECTORY,\n                SERVER_REFERENCE_MANIFEST + '.json'\n              )\n            ) as ActionManifest)\n          : null\n        const entriesWithAction = actionManifest ? new Set() : null\n        if (actionManifest && entriesWithAction) {\n          for (const id in actionManifest.node) {\n            for (const entry in actionManifest.node[id].workers) {\n              entriesWithAction.add(entry)\n            }\n          }\n          for (const id in actionManifest.edge) {\n            for (const entry in actionManifest.edge[id].workers) {\n              entriesWithAction.add(entry)\n            }\n          }\n        }\n\n        for (const key of Object.keys(middlewareManifest?.functions)) {\n          if (key.startsWith('/api')) {\n            edgeRuntimePagesCount++\n          }\n        }\n\n        await Promise.all(\n          Object.entries(pageKeys)\n            .reduce<Array<{ pageType: keyof typeof pageKeys; page: string }>>(\n              (acc, [key, files]) => {\n                if (!files) {\n                  return acc\n                }\n\n                const pageType = key as keyof typeof pageKeys\n\n                for (const page of files) {\n                  acc.push({ pageType, page })\n                }\n\n                return acc\n              },\n              []\n            )\n            .map(({ pageType, page }) => {\n              const checkPageSpan = staticCheckSpan.traceChild('check-page', {\n                page,\n              })\n              return checkPageSpan.traceAsyncFn(async () => {\n                const actualPage = normalizePagePath(page)\n                const [size, totalSize] = await getJsPageSizeInKb(\n                  pageType,\n                  actualPage,\n                  distDir,\n                  buildManifest,\n                  appBuildManifest,\n                  config.experimental.gzipSize,\n                  computedManifestData\n                )\n\n                let isRoutePPREnabled = false\n                let isSSG = false\n                let isStatic = false\n                let isServerComponent = false\n                let isHybridAmp = false\n                let ssgPageRoutes: string[] | null = null\n                let pagePath = ''\n\n                if (pageType === 'pages') {\n                  pagePath =\n                    pagesPaths.find((p) => {\n                      p = normalizePathSep(p)\n                      return (\n                        p.startsWith(actualPage + '.') ||\n                        p.startsWith(actualPage + '/index.')\n                      )\n                    }) || ''\n                }\n                let originalAppPath: string | undefined\n\n                if (pageType === 'app' && mappedAppPages) {\n                  for (const [originalPath, normalizedPath] of Object.entries(\n                    appPathRoutes\n                  )) {\n                    if (normalizedPath === page) {\n                      pagePath = mappedAppPages[originalPath].replace(\n                        /^private-next-app-dir/,\n                        ''\n                      )\n                      originalAppPath = originalPath\n                      break\n                    }\n                  }\n                }\n\n                const pageFilePath = isAppBuiltinNotFoundPage(pagePath)\n                  ? require.resolve(\n                      'next/dist/client/components/not-found-error'\n                    )\n                  : path.join(\n                      (pageType === 'pages' ? pagesDir : appDir) || '',\n                      pagePath\n                    )\n\n                const isInsideAppDir = pageType === 'app'\n                const staticInfo = pagePath\n                  ? await getStaticInfoIncludingLayouts({\n                      isInsideAppDir,\n                      pageFilePath,\n                      pageExtensions: config.pageExtensions,\n                      appDir,\n                      config,\n                      isDev: false,\n                      // If this route is an App Router page route, inherit the\n                      // route segment configs (e.g. `runtime`) from the layout by\n                      // passing the `originalAppPath`, which should end with `/page`.\n                      page: isInsideAppDir ? originalAppPath! : page,\n                    })\n                  : undefined\n\n                // If there's any thing that would contribute to the functions\n                // configuration, we need to add it to the manifest.\n                if (\n                  typeof staticInfo?.runtime !== 'undefined' ||\n                  typeof staticInfo?.maxDuration !== 'undefined'\n                ) {\n                  functionsConfigManifest.functions[page] = {\n                    maxDuration: staticInfo?.maxDuration,\n                  }\n                }\n\n                const pageRuntime = middlewareManifest.functions[\n                  originalAppPath || page\n                ]\n                  ? 'edge'\n                  : staticInfo?.runtime\n\n                if (!isCompileMode) {\n                  isServerComponent =\n                    pageType === 'app' &&\n                    staticInfo?.rsc !== RSC_MODULE_TYPES.client\n\n                  if (pageType === 'app' || !isReservedPage(page)) {\n                    try {\n                      let edgeInfo: any\n\n                      if (isEdgeRuntime(pageRuntime)) {\n                        if (pageType === 'app') {\n                          edgeRuntimeAppCount++\n                        } else {\n                          edgeRuntimePagesCount++\n                        }\n\n                        const manifestKey =\n                          pageType === 'pages' ? page : originalAppPath || ''\n\n                        edgeInfo = middlewareManifest.functions[manifestKey]\n                      }\n\n                      let isPageStaticSpan =\n                        checkPageSpan.traceChild('is-page-static')\n                      let workerResult = await isPageStaticSpan.traceAsyncFn(\n                        () => {\n                          return worker.isPageStatic({\n                            dir,\n                            page,\n                            originalAppPath,\n                            distDir,\n                            configFileName,\n                            runtimeEnvConfig,\n                            httpAgentOptions: config.httpAgentOptions,\n                            locales: config.i18n?.locales,\n                            defaultLocale: config.i18n?.defaultLocale,\n                            parentId: isPageStaticSpan.getId(),\n                            pageRuntime,\n                            edgeInfo,\n                            pageType,\n                            dynamicIO: isAppDynamicIOEnabled,\n                            authInterrupts: isAuthInterruptsEnabled,\n                            cacheHandler: config.cacheHandler,\n                            cacheHandlers: config.experimental.cacheHandlers,\n                            isrFlushToDisk: ciEnvironment.hasNextSupport\n                              ? false\n                              : config.experimental.isrFlushToDisk,\n                            maxMemoryCacheSize: config.cacheMaxMemorySize,\n                            nextConfigOutput: config.output,\n                            pprConfig: config.experimental.ppr,\n                            cacheLifeProfiles: config.experimental.cacheLife,\n                            buildId,\n                            sriEnabled,\n                          })\n                        }\n                      )\n\n                      if (pageType === 'app' && originalAppPath) {\n                        appNormalizedPaths.set(originalAppPath, page)\n                        // TODO-APP: handle prerendering with edge\n                        if (isEdgeRuntime(pageRuntime)) {\n                          isStatic = false\n                          isSSG = false\n\n                          Log.warnOnce(\n                            `Using edge runtime on a page currently disables static generation for that page`\n                          )\n                        } else {\n                          const isDynamic = isDynamicRoute(page)\n\n                          if (\n                            typeof workerResult.isRoutePPREnabled === 'boolean'\n                          ) {\n                            isRoutePPREnabled = workerResult.isRoutePPREnabled\n                          }\n\n                          // If this route can be partially pre-rendered, then\n                          // mark it as such and mark that it can be\n                          // generated server-side.\n                          if (workerResult.isRoutePPREnabled) {\n                            isSSG = true\n                            isStatic = true\n\n                            staticPaths.set(originalAppPath, [])\n                          }\n                          // As PPR isn't enabled for this route, if dynamic IO\n                          // is enabled, and this is a dynamic route, we should\n                          // complete a prospective render for the route so that\n                          // we can use the fallback behavior. This lets us\n                          // check that dynamic pages won't error when they\n                          // enable PPR.\n                          else if (config.experimental.dynamicIO && isDynamic) {\n                            prospectiveRenders.set(originalAppPath, {\n                              page,\n                              originalAppPath,\n                            })\n                          }\n\n                          if (workerResult.prerenderedRoutes) {\n                            staticPaths.set(\n                              originalAppPath,\n                              workerResult.prerenderedRoutes\n                            )\n                            ssgPageRoutes = workerResult.prerenderedRoutes.map(\n                              (route) => route.pathname\n                            )\n                            isSSG = true\n                          }\n\n                          const appConfig = workerResult.appConfig || {}\n                          if (appConfig.revalidate !== 0) {\n                            const hasGenerateStaticParams =\n                              workerResult.prerenderedRoutes &&\n                              workerResult.prerenderedRoutes.length > 0\n\n                            if (\n                              config.output === 'export' &&\n                              isDynamic &&\n                              !hasGenerateStaticParams\n                            ) {\n                              throw new Error(\n                                `Page \"${page}\" is missing \"generateStaticParams()\" so it cannot be used with \"output: export\" config.`\n                              )\n                            }\n\n                            // Mark the app as static if:\n                            // - It has no dynamic param\n                            // - It doesn't have generateStaticParams but `dynamic` is set to\n                            //   `error` or `force-static`\n                            if (!isDynamic) {\n                              staticPaths.set(originalAppPath, [\n                                {\n                                  pathname: page,\n                                  encodedPathname: page,\n                                  fallbackRouteParams: undefined,\n                                  fallbackMode:\n                                    workerResult.prerenderFallbackMode,\n                                  fallbackRootParams: undefined,\n                                },\n                              ])\n                              isStatic = true\n                            } else if (\n                              !hasGenerateStaticParams &&\n                              (appConfig.dynamic === 'error' ||\n                                appConfig.dynamic === 'force-static')\n                            ) {\n                              staticPaths.set(originalAppPath, [])\n                              isStatic = true\n                              isRoutePPREnabled = false\n                            }\n                          }\n\n                          if (workerResult.prerenderFallbackMode) {\n                            fallbackModes.set(\n                              originalAppPath,\n                              workerResult.prerenderFallbackMode\n                            )\n                          }\n\n                          appDefaultConfigs.set(originalAppPath, appConfig)\n                        }\n                      } else {\n                        if (isEdgeRuntime(pageRuntime)) {\n                          if (workerResult.hasStaticProps) {\n                            console.warn(\n                              `\"getStaticProps\" is not yet supported fully with \"experimental-edge\", detected on ${page}`\n                            )\n                          }\n                          // TODO: add handling for statically rendering edge\n                          // pages and allow edge with Prerender outputs\n                          workerResult.isStatic = false\n                          workerResult.hasStaticProps = false\n                        }\n\n                        if (\n                          workerResult.isStatic === false &&\n                          (workerResult.isHybridAmp || workerResult.isAmpOnly)\n                        ) {\n                          hasSsrAmpPages = true\n                        }\n\n                        if (workerResult.isHybridAmp) {\n                          isHybridAmp = true\n                          hybridAmpPages.add(page)\n                        }\n\n                        if (workerResult.isNextImageImported) {\n                          isNextImageImported = true\n                        }\n\n                        if (workerResult.hasStaticProps) {\n                          ssgPages.add(page)\n                          isSSG = true\n\n                          if (\n                            workerResult.prerenderedRoutes &&\n                            workerResult.prerenderedRoutes.length > 0\n                          ) {\n                            additionalPaths.set(\n                              page,\n                              workerResult.prerenderedRoutes\n                            )\n                            ssgPageRoutes = workerResult.prerenderedRoutes.map(\n                              (route) => route.pathname\n                            )\n                          }\n\n                          if (\n                            workerResult.prerenderFallbackMode ===\n                            FallbackMode.BLOCKING_STATIC_RENDER\n                          ) {\n                            ssgBlockingFallbackPages.add(page)\n                          } else if (\n                            workerResult.prerenderFallbackMode ===\n                            FallbackMode.PRERENDER\n                          ) {\n                            ssgStaticFallbackPages.add(page)\n                          }\n                        } else if (workerResult.hasServerProps) {\n                          serverPropsPages.add(page)\n                        } else if (\n                          workerResult.isStatic &&\n                          !isServerComponent &&\n                          (await customAppGetInitialPropsPromise) === false\n                        ) {\n                          staticPages.add(page)\n                          isStatic = true\n                        } else if (isServerComponent) {\n                          // This is a static server component page that doesn't have\n                          // gSP or gSSP. We still treat it as a SSG page.\n                          ssgPages.add(page)\n                          isSSG = true\n                        }\n\n                        if (hasPages404 && page === '/404') {\n                          if (\n                            !workerResult.isStatic &&\n                            !workerResult.hasStaticProps\n                          ) {\n                            throw new Error(\n                              `\\`pages/404\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n                            )\n                          }\n                          // we need to ensure the 404 lambda is present since we use\n                          // it when _app has getInitialProps\n                          if (\n                            (await customAppGetInitialPropsPromise) &&\n                            !workerResult.hasStaticProps\n                          ) {\n                            staticPages.delete(page)\n                          }\n                        }\n\n                        if (\n                          STATIC_STATUS_PAGES.includes(page) &&\n                          !workerResult.isStatic &&\n                          !workerResult.hasStaticProps\n                        ) {\n                          throw new Error(\n                            `\\`pages${page}\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n                          )\n                        }\n                      }\n                    } catch (err) {\n                      if (\n                        !isError(err) ||\n                        err.message !== 'INVALID_DEFAULT_EXPORT'\n                      )\n                        throw err\n                      invalidPages.add(page)\n                    }\n                  }\n\n                  if (pageType === 'app') {\n                    if (isSSG || isStatic) {\n                      staticAppPagesCount++\n                    } else {\n                      serverAppPagesCount++\n                    }\n                  }\n                }\n\n                pageInfos.set(page, {\n                  size,\n                  totalSize,\n                  isStatic,\n                  isSSG,\n                  isRoutePPREnabled,\n                  isHybridAmp,\n                  ssgPageRoutes,\n                  initialRevalidateSeconds: false,\n                  runtime: pageRuntime,\n                  pageDuration: undefined,\n                  ssgPageDurations: undefined,\n                  hasEmptyPrelude: undefined,\n                })\n              })\n            })\n        )\n\n        if (hadUnsupportedValue) {\n          Log.error(\n            `Invalid config value exports detected, these can cause unexpected behavior from the configs not being applied. Please fix them to continue`\n          )\n          process.exit(1)\n        }\n\n        const errorPageResult = await errorPageStaticResult\n        const nonStaticErrorPage =\n          (await errorPageHasCustomGetInitialProps) ||\n          (errorPageResult && errorPageResult.hasServerProps)\n\n        const returnValue = {\n          customAppGetInitialProps: await customAppGetInitialPropsPromise,\n          namedExports: await namedExportsPromise,\n          isNextImageImported,\n          hasSsrAmpPages,\n          hasNonStaticErrorPage: nonStaticErrorPage,\n        }\n\n        return returnValue\n      })\n\n      if (postCompileSpinner) postCompileSpinner.stopAndPersist()\n      traceMemoryUsage('Finished collecting page data', nextBuildSpan)\n\n      if (customAppGetInitialProps) {\n        console.warn(\n          bold(yellow(`Warning: `)) +\n            yellow(\n              `You have opted-out of Automatic Static Optimization due to \\`getInitialProps\\` in \\`pages/_app\\`. This does not opt-out pages with \\`getStaticProps\\``\n            )\n        )\n        console.warn(\n          'Read more: https://nextjs.org/docs/messages/opt-out-auto-static-optimization\\n'\n        )\n      }\n\n      const { cacheHandler } = config\n\n      const instrumentationHookEntryFiles: string[] = []\n      if (hasInstrumentationHook) {\n        instrumentationHookEntryFiles.push(\n          path.join(SERVER_DIRECTORY, `${INSTRUMENTATION_HOOK_FILENAME}.js`)\n        )\n        // If there's edge routes, append the edge instrumentation hook\n        // Turbopack generates this chunk with a hashed name and references it in middleware-manifest.\n        if (\n          !process.env.TURBOPACK &&\n          (edgeRuntimeAppCount || edgeRuntimePagesCount)\n        ) {\n          instrumentationHookEntryFiles.push(\n            path.join(\n              SERVER_DIRECTORY,\n              `edge-${INSTRUMENTATION_HOOK_FILENAME}.js`\n            )\n          )\n        }\n      }\n\n      const requiredServerFilesManifest = nextBuildSpan\n        .traceChild('generate-required-server-files')\n        .traceFn(() => {\n          const normalizedCacheHandlers: Record<string, string> = {}\n\n          for (const [key, value] of Object.entries(\n            config.experimental.cacheHandlers || {}\n          )) {\n            if (key && value) {\n              normalizedCacheHandlers[key] = path.relative(distDir, value)\n            }\n          }\n\n          const serverFilesManifest: RequiredServerFilesManifest = {\n            version: 1,\n            config: {\n              ...config,\n              configFile: undefined,\n              ...(ciEnvironment.hasNextSupport\n                ? {\n                    compress: false,\n                  }\n                : {}),\n              cacheHandler: cacheHandler\n                ? path.relative(distDir, cacheHandler)\n                : config.cacheHandler,\n              experimental: {\n                ...config.experimental,\n                cacheHandlers: normalizedCacheHandlers,\n                trustHostHeader: ciEnvironment.hasNextSupport,\n\n                // @ts-expect-error internal field TODO: fix this, should use a separate mechanism to pass the info.\n                isExperimentalCompile: isCompileMode,\n              },\n            },\n            appDir: dir,\n            relativeAppDir: path.relative(outputFileTracingRoot, dir),\n            files: [\n              ROUTES_MANIFEST,\n              path.relative(distDir, pagesManifestPath),\n              BUILD_MANIFEST,\n              PRERENDER_MANIFEST,\n              path.join(SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST),\n              path.join(SERVER_DIRECTORY, MIDDLEWARE_MANIFEST),\n              path.join(SERVER_DIRECTORY, MIDDLEWARE_BUILD_MANIFEST + '.js'),\n              ...(!process.env.TURBOPACK\n                ? [\n                    path.join(\n                      SERVER_DIRECTORY,\n                      MIDDLEWARE_REACT_LOADABLE_MANIFEST + '.js'\n                    ),\n                    REACT_LOADABLE_MANIFEST,\n                  ]\n                : []),\n              ...(appDir\n                ? [\n                    ...(config.experimental.sri\n                      ? [\n                          path.join(\n                            SERVER_DIRECTORY,\n                            SUBRESOURCE_INTEGRITY_MANIFEST + '.js'\n                          ),\n                          path.join(\n                            SERVER_DIRECTORY,\n                            SUBRESOURCE_INTEGRITY_MANIFEST + '.json'\n                          ),\n                        ]\n                      : []),\n                    path.join(SERVER_DIRECTORY, APP_PATHS_MANIFEST),\n                    path.join(APP_PATH_ROUTES_MANIFEST),\n                    APP_BUILD_MANIFEST,\n                    path.join(\n                      SERVER_DIRECTORY,\n                      SERVER_REFERENCE_MANIFEST + '.js'\n                    ),\n                    path.join(\n                      SERVER_DIRECTORY,\n                      SERVER_REFERENCE_MANIFEST + '.json'\n                    ),\n                  ]\n                : []),\n              ...(pagesDir && !turboNextBuild\n                ? [\n                    DYNAMIC_CSS_MANIFEST + '.json',\n                    path.join(SERVER_DIRECTORY, DYNAMIC_CSS_MANIFEST + '.js'),\n                  ]\n                : []),\n              BUILD_ID_FILE,\n              path.join(SERVER_DIRECTORY, NEXT_FONT_MANIFEST + '.js'),\n              path.join(SERVER_DIRECTORY, NEXT_FONT_MANIFEST + '.json'),\n              ...instrumentationHookEntryFiles,\n            ]\n              .filter(nonNullable)\n              .map((file) => path.join(config.distDir, file)),\n            ignore: [] as string[],\n          }\n\n          return serverFilesManifest\n        })\n\n      if (!hasSsrAmpPages) {\n        requiredServerFilesManifest.ignore.push(\n          path.relative(\n            dir,\n            path.join(\n              path.dirname(\n                require.resolve(\n                  'next/dist/compiled/@ampproject/toolbox-optimizer'\n                )\n              ),\n              '**/*'\n            )\n          )\n        )\n      }\n\n      const middlewareFile = rootPaths.find((p) =>\n        p.includes(MIDDLEWARE_FILENAME)\n      )\n      let hasNodeMiddleware = false\n\n      if (middlewareFile) {\n        const staticInfo = await getStaticInfoIncludingLayouts({\n          isInsideAppDir: false,\n          pageFilePath: path.join(dir, middlewareFile),\n          config,\n          appDir,\n          pageExtensions: config.pageExtensions,\n          isDev: false,\n          page: 'middleware',\n        })\n\n        if (staticInfo.runtime === 'nodejs') {\n          hasNodeMiddleware = true\n          functionsConfigManifest.functions['/_middleware'] = {\n            runtime: staticInfo.runtime,\n            matchers: staticInfo.middleware?.matchers ?? [\n              {\n                regexp: '^.*$',\n                originalSource: '/:path*',\n              },\n            ],\n          }\n\n          if (turboNextBuild) {\n            await writeManifest(\n              path.join(\n                distDir,\n                'static',\n                buildId,\n                TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST\n              ),\n              functionsConfigManifest.functions['/_middleware'].matchers || []\n            )\n          }\n        }\n      }\n\n      await writeFunctionsConfigManifest(distDir, functionsConfigManifest)\n\n      if (!isGenerateMode && !buildTracesPromise) {\n        buildTracesPromise = collectBuildTraces({\n          dir,\n          config,\n          distDir,\n          edgeRuntimeRoutes: collectRoutesUsingEdgeRuntime(pageInfos),\n          staticPages: [...staticPages],\n          nextBuildSpan,\n          hasSsrAmpPages,\n          buildTraceContext,\n          outputFileTracingRoot,\n        }).catch((err) => {\n          console.error(err)\n          process.exit(1)\n        })\n      }\n\n      if (serverPropsPages.size > 0 || ssgPages.size > 0) {\n        // We update the routes manifest after the build with the\n        // data routes since we can't determine these until after build\n        routesManifest.dataRoutes = getSortedRoutes([\n          ...serverPropsPages,\n          ...ssgPages,\n        ]).map((page) => {\n          return buildDataRoute(page, buildId)\n        })\n      }\n\n      // We need to write the manifest with rewrites before build\n      await nextBuildSpan\n        .traceChild('write-routes-manifest')\n        .traceAsyncFn(() => writeManifest(routesManifestPath, routesManifest))\n\n      // Since custom _app.js can wrap the 404 page we have to opt-out of static optimization if it has getInitialProps\n      // Only export the static 404 when there is no /_error present\n      const useStaticPages404 =\n        !customAppGetInitialProps && (!hasNonStaticErrorPage || hasPages404)\n\n      if (invalidPages.size > 0) {\n        const err = new Error(\n          `Build optimization failed: found page${\n            invalidPages.size === 1 ? '' : 's'\n          } without a React Component as default export in \\n${[...invalidPages]\n            .map((pg) => `pages${pg}`)\n            .join(\n              '\\n'\n            )}\\n\\nSee https://nextjs.org/docs/messages/page-without-valid-component for more info.\\n`\n        ) as NextError\n        err.code = 'BUILD_OPTIMIZATION_FAILED'\n        throw err\n      }\n\n      await writeBuildId(distDir, buildId)\n\n      if (config.experimental.optimizeCss) {\n        const globOrig =\n          require('next/dist/compiled/glob') as typeof import('next/dist/compiled/glob')\n\n        const cssFilePaths = await new Promise<string[]>((resolve, reject) => {\n          globOrig(\n            '**/*.css',\n            { cwd: path.join(distDir, 'static') },\n            (err, files) => {\n              if (err) {\n                return reject(err)\n              }\n              resolve(files)\n            }\n          )\n        })\n\n        requiredServerFilesManifest.files.push(\n          ...cssFilePaths.map((filePath) =>\n            path.join(config.distDir, 'static', filePath)\n          )\n        )\n      }\n\n      const features: EventBuildFeatureUsage[] = [\n        {\n          featureName: 'experimental/dynamicIO',\n          invocationCount: config.experimental.dynamicIO ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/optimizeCss',\n          invocationCount: config.experimental.optimizeCss ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/nextScriptWorkers',\n          invocationCount: config.experimental.nextScriptWorkers ? 1 : 0,\n        },\n        {\n          featureName: 'experimental/ppr',\n          invocationCount: config.experimental.ppr ? 1 : 0,\n        },\n      ]\n      telemetry.record(\n        features.map((feature) => {\n          return {\n            eventName: EVENT_BUILD_FEATURE_USAGE,\n            payload: feature,\n          }\n        })\n      )\n\n      await writeRequiredServerFilesManifest(\n        distDir,\n        requiredServerFilesManifest\n      )\n\n      const middlewareManifest: MiddlewareManifest = await readManifest(\n        path.join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n      )\n\n      const prerenderManifest: PrerenderManifest = {\n        version: 4,\n        routes: {},\n        dynamicRoutes: {},\n        notFoundRoutes: [],\n        preview: previewProps,\n      }\n\n      const tbdPrerenderRoutes: string[] = []\n\n      const { i18n } = config\n\n      const usedStaticStatusPages = STATIC_STATUS_PAGES.filter(\n        (page) =>\n          mappedPages[page] &&\n          mappedPages[page].startsWith('private-next-pages')\n      )\n      usedStaticStatusPages.forEach((page) => {\n        if (!ssgPages.has(page) && !customAppGetInitialProps) {\n          staticPages.add(page)\n        }\n      })\n\n      const hasPages500 = usedStaticStatusPages.includes('/500')\n      const useDefaultStatic500 =\n        !hasPages500 && !hasNonStaticErrorPage && !customAppGetInitialProps\n\n      const combinedPages = [...staticPages, ...ssgPages]\n      const isApp404Static = staticPaths.has(UNDERSCORE_NOT_FOUND_ROUTE_ENTRY)\n      const hasStaticApp404 = hasApp404 && isApp404Static\n\n      await updateBuildDiagnostics({\n        buildStage: 'static-generation',\n      })\n\n      // we need to trigger automatic exporting when we have\n      // - static 404/500\n      // - getStaticProps paths\n      // - experimental app is enabled\n      if (\n        !isCompileMode &&\n        (combinedPages.length > 0 ||\n          useStaticPages404 ||\n          useDefaultStatic500 ||\n          appDir)\n      ) {\n        const staticGenerationSpan =\n          nextBuildSpan.traceChild('static-generation')\n        await staticGenerationSpan.traceAsyncFn(async () => {\n          detectConflictingPaths(\n            [\n              ...combinedPages,\n              ...pageKeys.pages.filter((page) => !combinedPages.includes(page)),\n            ],\n            ssgPages,\n            new Map(\n              Array.from(additionalPaths.entries()).map(\n                ([page, routes]): [string, string[]] => {\n                  return [page, routes.map((route) => route.pathname)]\n                }\n              )\n            )\n          )\n          const exportApp = require('../export')\n            .default as typeof import('../export').default\n\n          const exportConfig: NextConfigComplete = {\n            ...config,\n            // Default map will be the collection of automatic statically exported\n            // pages and incremental pages.\n            // n.b. we cannot handle this above in combinedPages because the dynamic\n            // page must be in the `pages` array, but not in the mapping.\n            exportPathMap: (defaultMap: ExportPathMap) => {\n              // Dynamically routed pages should be prerendered to be used as\n              // a client-side skeleton (fallback) while data is being fetched.\n              // This ensures the end-user never sees a 500 or slow response from the\n              // server.\n              //\n              // Note: prerendering disables automatic static optimization.\n              ssgPages.forEach((page) => {\n                if (isDynamicRoute(page)) {\n                  tbdPrerenderRoutes.push(page)\n\n                  if (ssgStaticFallbackPages.has(page)) {\n                    // Override the rendering for the dynamic page to be treated as a\n                    // fallback render.\n                    if (i18n) {\n                      defaultMap[`/${i18n.defaultLocale}${page}`] = {\n                        page,\n                        _pagesFallback: true,\n                      }\n                    } else {\n                      defaultMap[page] = {\n                        page,\n                        _pagesFallback: true,\n                      }\n                    }\n                  } else {\n                    // Remove dynamically routed pages from the default path map when\n                    // fallback behavior is disabled.\n                    delete defaultMap[page]\n                  }\n                }\n              })\n\n              // Append the \"well-known\" routes we should prerender for, e.g. blog\n              // post slugs.\n              additionalPaths.forEach((routes, page) => {\n                routes.forEach((route) => {\n                  defaultMap[route.pathname] = {\n                    page,\n                    _ssgPath: route.encodedPathname,\n                  }\n                })\n              })\n\n              if (useStaticPages404) {\n                defaultMap['/404'] = {\n                  page: hasPages404 ? '/404' : '/_error',\n                }\n              }\n\n              if (useDefaultStatic500) {\n                defaultMap['/500'] = {\n                  page: '/_error',\n                }\n              }\n\n              // TODO: output manifest specific to app paths and their\n              // revalidate periods and dynamicParams settings\n              staticPaths.forEach((routes, originalAppPath) => {\n                const appConfig = appDefaultConfigs.get(originalAppPath)\n                const isDynamicError = appConfig?.dynamic === 'error'\n\n                const isRoutePPREnabled = appConfig\n                  ? checkIsRoutePPREnabled(config.experimental.ppr, appConfig)\n                  : undefined\n\n                routes.forEach((route) => {\n                  // If the route has any dynamic root segments, we need to skip\n                  // rendering the route. This is because we don't support\n                  // revalidating the shells without the parameters present.\n                  if (\n                    route.fallbackRootParams &&\n                    route.fallbackRootParams.length > 0\n                  ) {\n                    return\n                  }\n\n                  defaultMap[route.pathname] = {\n                    page: originalAppPath,\n                    _ssgPath: route.encodedPathname,\n                    _fallbackRouteParams: route.fallbackRouteParams,\n                    _isDynamicError: isDynamicError,\n                    _isAppDir: true,\n                    _isRoutePPREnabled: isRoutePPREnabled,\n                  }\n                })\n              })\n\n              // If the app does have dynamic IO enabled but does not have PPR\n              // enabled, then we need to perform a prospective render for all\n              // the dynamic pages to ensure that they won't error during\n              // rendering (due to a missing prelude).\n              for (const {\n                page,\n                originalAppPath,\n              } of prospectiveRenders.values()) {\n                defaultMap[page] = {\n                  page: originalAppPath,\n                  _ssgPath: page,\n                  _fallbackRouteParams: getParamKeys(page),\n                  // Prospective renders are only enabled for app pages.\n                  _isAppDir: true,\n                  // Prospective renders are only enabled when PPR is disabled.\n                  _isRoutePPREnabled: false,\n                  _isProspectiveRender: true,\n                  // Dynamic IO does not currently support `dynamic === 'error'`.\n                  _isDynamicError: false,\n                }\n              }\n\n              if (i18n) {\n                for (const page of [\n                  ...staticPages,\n                  ...ssgPages,\n                  ...(useStaticPages404 ? ['/404'] : []),\n                  ...(useDefaultStatic500 ? ['/500'] : []),\n                ]) {\n                  const isSsg = ssgPages.has(page)\n                  const isDynamic = isDynamicRoute(page)\n                  const isFallback = isSsg && ssgStaticFallbackPages.has(page)\n\n                  for (const locale of i18n.locales) {\n                    // skip fallback generation for SSG pages without fallback mode\n                    if (isSsg && isDynamic && !isFallback) continue\n                    const outputPath = `/${locale}${page === '/' ? '' : page}`\n\n                    defaultMap[outputPath] = {\n                      page: defaultMap[page]?.page || page,\n                      _locale: locale,\n                      _pagesFallback: isFallback,\n                    }\n                  }\n\n                  if (isSsg) {\n                    // remove non-locale prefixed variant from defaultMap\n                    delete defaultMap[page]\n                  }\n                }\n              }\n\n              return defaultMap\n            },\n          }\n\n          const outdir = path.join(distDir, 'export')\n          const exportResult = await exportApp(\n            dir,\n            {\n              nextConfig: exportConfig,\n              enabledDirectories,\n              silent: true,\n              buildExport: true,\n              debugOutput,\n              pages: combinedPages,\n              outdir,\n              statusMessage: 'Generating static pages',\n              numWorkers: getNumberOfWorkers(exportConfig),\n            },\n            nextBuildSpan\n          )\n\n          // If there was no result, there's nothing more to do.\n          if (!exportResult) return\n\n          if (debugOutput || process.env.NEXT_SSG_FETCH_METRICS === '1') {\n            recordFetchMetrics(exportResult)\n          }\n\n          writeTurborepoAccessTraceResult({\n            distDir: config.distDir,\n            traces: [\n              turborepoAccessTraceResult,\n              ...exportResult.turborepoAccessTraceResults.values(),\n            ],\n          })\n\n          prerenderManifest.notFoundRoutes = Array.from(\n            exportResult.ssgNotFoundPaths\n          )\n\n          // remove server bundles that were exported\n          for (const page of staticPages) {\n            const serverBundle = getPagePath(page, distDir, undefined, false)\n            await fs.unlink(serverBundle)\n          }\n\n          staticPaths.forEach((prerenderedRoutes, originalAppPath) => {\n            const page = appNormalizedPaths.get(originalAppPath)\n            if (!page) throw new InvariantError('Page not found')\n\n            const appConfig = appDefaultConfigs.get(originalAppPath)\n            if (!appConfig) throw new InvariantError('App config not found')\n\n            let hasRevalidateZero =\n              appConfig.revalidate === 0 ||\n              exportResult.byPath.get(page)?.revalidate === 0\n\n            if (hasRevalidateZero && pageInfos.get(page)?.isStatic) {\n              // if the page was marked as being static, but it contains dynamic data\n              // (ie, in the case of a static generation bailout), then it should be marked dynamic\n              pageInfos.set(page, {\n                ...(pageInfos.get(page) as PageInfo),\n                isStatic: false,\n                isSSG: false,\n              })\n            }\n\n            const isAppRouteHandler = isAppRouteRoute(originalAppPath)\n\n            // When this is an app page and PPR is enabled, the route supports\n            // partial pre-rendering.\n            const isRoutePPREnabled: true | undefined =\n              !isAppRouteHandler &&\n              checkIsRoutePPREnabled(config.experimental.ppr, appConfig)\n                ? true\n                : undefined\n\n            const htmlBotsRegexString =\n              // The htmlLimitedBots has been converted to a string during loadConfig\n              config.htmlLimitedBots || HTML_LIMITED_BOT_UA_RE_STRING\n\n            // this flag is used to selectively bypass the static cache and invoke the lambda directly\n            // to enable server actions on static routes\n            const bypassFor: RouteHas[] = [\n              { type: 'header', key: ACTION_HEADER },\n              {\n                type: 'header',\n                key: 'content-type',\n                value: 'multipart/form-data;.*',\n              },\n              // If it's PPR rendered non-static page, bypass the PPR cache when streaming metadata is enabled.\n              // This will skip the postpone data for those bots requests and instead produce a dynamic render.\n              ...(isRoutePPREnabled &&\n              // Disable streaming metadata for PPR on deployment where we don't have the special env.\n              // TODO: enable streaming metadata in PPR mode by default once it's ready.\n              process.env.__NEXT_EXPERIMENTAL_PPR === 'true'\n                ? [\n                    {\n                      type: 'header',\n                      key: 'user-agent',\n                      value: htmlBotsRegexString,\n                    },\n                  ]\n                : []),\n            ]\n\n            // We should collect all the dynamic routes into a single array for\n            // this page. Including the full fallback route (the original\n            // route), any routes that were generated with unknown route params\n            // should be collected and included in the dynamic routes part\n            // of the manifest instead.\n            const routes: PrerenderedRoute[] = []\n            const dynamicRoutes: PrerenderedRoute[] = []\n\n            // Sort the outputted routes to ensure consistent output. Any route\n            // though that has unknown route params will be pulled and sorted\n            // independently. This is because the routes with unknown route\n            // params will contain the dynamic path parameters, some of which\n            // may conflict with the actual prerendered routes.\n            let unknownPrerenderRoutes: PrerenderedRoute[] = []\n            let knownPrerenderRoutes: PrerenderedRoute[] = []\n            for (const prerenderedRoute of prerenderedRoutes) {\n              if (\n                prerenderedRoute.fallbackRouteParams &&\n                prerenderedRoute.fallbackRouteParams.length > 0\n              ) {\n                unknownPrerenderRoutes.push(prerenderedRoute)\n              } else {\n                knownPrerenderRoutes.push(prerenderedRoute)\n              }\n            }\n\n            unknownPrerenderRoutes = getSortedRouteObjects(\n              unknownPrerenderRoutes,\n              (prerenderedRoute) => prerenderedRoute.pathname\n            )\n            knownPrerenderRoutes = getSortedRouteObjects(\n              knownPrerenderRoutes,\n              (prerenderedRoute) => prerenderedRoute.pathname\n            )\n\n            prerenderedRoutes = [\n              ...knownPrerenderRoutes,\n              ...unknownPrerenderRoutes,\n            ]\n\n            for (const prerenderedRoute of prerenderedRoutes) {\n              // TODO: check if still needed?\n              // Exclude the /_not-found route.\n              if (prerenderedRoute.pathname === UNDERSCORE_NOT_FOUND_ROUTE) {\n                continue\n              }\n\n              if (\n                isRoutePPREnabled &&\n                prerenderedRoute.fallbackRouteParams &&\n                prerenderedRoute.fallbackRouteParams.length > 0\n              ) {\n                // If the route has unknown params, then we need to add it to\n                // the list of dynamic routes.\n                dynamicRoutes.push(prerenderedRoute)\n              } else {\n                // If the route doesn't have unknown params, then we need to\n                // add it to the list of routes.\n                routes.push(prerenderedRoute)\n              }\n            }\n\n            // Handle all the static routes.\n            for (const route of routes) {\n              if (isDynamicRoute(page) && route.pathname === page) continue\n              if (route.pathname === UNDERSCORE_NOT_FOUND_ROUTE) continue\n\n              const {\n                revalidate = appConfig.revalidate ?? false,\n                metadata = {},\n                hasEmptyPrelude,\n                hasPostponed,\n              } = exportResult.byPath.get(route.pathname) ?? {}\n\n              pageInfos.set(route.pathname, {\n                ...(pageInfos.get(route.pathname) as PageInfo),\n                hasPostponed,\n                hasEmptyPrelude,\n              })\n\n              // update the page (eg /blog/[slug]) to also have the postpone metadata\n              pageInfos.set(page, {\n                ...(pageInfos.get(page) as PageInfo),\n                hasPostponed,\n                hasEmptyPrelude,\n              })\n\n              if (revalidate !== 0) {\n                const normalizedRoute = normalizePagePath(route.pathname)\n\n                let dataRoute: string | null\n                if (isAppRouteHandler) {\n                  dataRoute = null\n                } else {\n                  dataRoute = path.posix.join(`${normalizedRoute}${RSC_SUFFIX}`)\n                }\n\n                let prefetchDataRoute: string | null | undefined\n                // While we may only write the `.rsc` when the route does not\n                // have PPR enabled, we still want to generate the route when\n                // deployed so it doesn't 404. If the app has PPR enabled, we\n                // should add this key.\n                if (!isAppRouteHandler && isAppPPREnabled) {\n                  prefetchDataRoute = path.posix.join(\n                    `${normalizedRoute}${RSC_PREFETCH_SUFFIX}`\n                  )\n                }\n\n                const meta = collectMeta(metadata)\n\n                prerenderManifest.routes[route.pathname] = {\n                  initialStatus: meta.status,\n                  initialHeaders: meta.headers,\n                  renderingMode: isAppPPREnabled\n                    ? isRoutePPREnabled\n                      ? RenderingMode.PARTIALLY_STATIC\n                      : RenderingMode.STATIC\n                    : undefined,\n                  experimentalPPR: isRoutePPREnabled,\n                  experimentalBypassFor: bypassFor,\n                  initialRevalidateSeconds: revalidate,\n                  srcRoute: page,\n                  dataRoute,\n                  prefetchDataRoute,\n                  allowHeader: ALLOWED_HEADERS,\n                }\n              } else {\n                hasRevalidateZero = true\n                // we might have determined during prerendering that this page\n                // used dynamic data\n                pageInfos.set(route.pathname, {\n                  ...(pageInfos.get(route.pathname) as PageInfo),\n                  isSSG: false,\n                  isStatic: false,\n                })\n              }\n            }\n\n            if (!hasRevalidateZero && isDynamicRoute(page)) {\n              // When PPR fallbacks aren't used, we need to include it here. If\n              // they are enabled, then it'll already be included in the\n              // prerendered routes.\n              if (!isRoutePPREnabled) {\n                dynamicRoutes.push({\n                  pathname: page,\n                  encodedPathname: page,\n                  fallbackRouteParams: undefined,\n                  fallbackMode:\n                    fallbackModes.get(originalAppPath) ??\n                    FallbackMode.NOT_FOUND,\n                  fallbackRootParams: undefined,\n                })\n              }\n\n              for (const route of dynamicRoutes) {\n                const normalizedRoute = normalizePagePath(route.pathname)\n\n                const { metadata, revalidate } =\n                  exportResult.byPath.get(route.pathname) ?? {}\n\n                let dataRoute: string | null = null\n                if (!isAppRouteHandler) {\n                  dataRoute = path.posix.join(`${normalizedRoute}${RSC_SUFFIX}`)\n                }\n\n                let prefetchDataRoute: string | undefined\n                if (!isAppRouteHandler && isAppPPREnabled) {\n                  prefetchDataRoute = path.posix.join(\n                    `${normalizedRoute}${RSC_PREFETCH_SUFFIX}`\n                  )\n                }\n\n                if (!isAppRouteHandler && metadata?.segmentPaths) {\n                  const dynamicRoute = routesManifest.dynamicRoutes.find(\n                    (r) => r.page === page\n                  )\n                  if (!dynamicRoute) {\n                    throw new Error('Dynamic route not found')\n                  }\n\n                  dynamicRoute.prefetchSegmentDataRoutes = []\n                  for (const segmentPath of metadata.segmentPaths) {\n                    const result = buildPrefetchSegmentDataRoute(\n                      route.pathname,\n                      segmentPath\n                    )\n                    dynamicRoute.prefetchSegmentDataRoutes.push(result)\n                  }\n                }\n\n                pageInfos.set(route.pathname, {\n                  ...(pageInfos.get(route.pathname) as PageInfo),\n                  isDynamicAppRoute: true,\n                  // if PPR is turned on and the route contains a dynamic segment,\n                  // we assume it'll be partially prerendered\n                  hasPostponed: isRoutePPREnabled,\n                })\n\n                const fallbackMode =\n                  route.fallbackMode ?? FallbackMode.NOT_FOUND\n\n                // When we're configured to serve a prerender, we should use the\n                // fallback revalidate from the export result. If it can't be\n                // found, mark that we should keep the shell forever (`false`).\n                let fallbackRevalidate: Revalidate | undefined =\n                  isRoutePPREnabled && fallbackMode === FallbackMode.PRERENDER\n                    ? revalidate ?? false\n                    : undefined\n\n                const fallback: Fallback = fallbackModeToFallbackField(\n                  fallbackMode,\n                  route.pathname\n                )\n\n                const meta =\n                  metadata &&\n                  isRoutePPREnabled &&\n                  fallbackMode === FallbackMode.PRERENDER\n                    ? collectMeta(metadata)\n                    : {}\n\n                prerenderManifest.dynamicRoutes[route.pathname] = {\n                  experimentalPPR: isRoutePPREnabled,\n                  renderingMode: isAppPPREnabled\n                    ? isRoutePPREnabled\n                      ? RenderingMode.PARTIALLY_STATIC\n                      : RenderingMode.STATIC\n                    : undefined,\n                  experimentalBypassFor: bypassFor,\n                  routeRegex: normalizeRouteRegex(\n                    getNamedRouteRegex(route.pathname, {\n                      prefixRouteKeys: false,\n                    }).re.source\n                  ),\n                  dataRoute,\n                  fallback,\n                  fallbackRevalidate,\n                  fallbackStatus: meta.status,\n                  fallbackHeaders: meta.headers,\n                  fallbackRootParams: route.fallbackRootParams,\n                  fallbackSourceRoute: route.fallbackRouteParams?.length\n                    ? page\n                    : undefined,\n                  dataRouteRegex: !dataRoute\n                    ? null\n                    : normalizeRouteRegex(\n                        getNamedRouteRegex(dataRoute, {\n                          prefixRouteKeys: false,\n                          includeSuffix: true,\n                          excludeOptionalTrailingSlash: true,\n                        }).re.source\n                      ),\n                  prefetchDataRoute,\n                  prefetchDataRouteRegex: !prefetchDataRoute\n                    ? undefined\n                    : normalizeRouteRegex(\n                        getNamedRouteRegex(prefetchDataRoute, {\n                          prefixRouteKeys: false,\n                          includeSuffix: true,\n                          excludeOptionalTrailingSlash: true,\n                        }).re.source\n                      ),\n                  allowHeader: ALLOWED_HEADERS,\n                }\n              }\n            }\n          })\n\n          const moveExportedPage = async (\n            originPage: string,\n            page: string,\n            file: string,\n            isSsg: boolean,\n            ext: 'html' | 'json',\n            additionalSsgFile = false\n          ) => {\n            return staticGenerationSpan\n              .traceChild('move-exported-page')\n              .traceAsyncFn(async () => {\n                file = `${file}.${ext}`\n                const orig = path.join(outdir, file)\n                const pagePath = getPagePath(\n                  originPage,\n                  distDir,\n                  undefined,\n                  false\n                )\n\n                const relativeDest = path\n                  .relative(\n                    path.join(distDir, SERVER_DIRECTORY),\n                    path.join(\n                      path.join(\n                        pagePath,\n                        // strip leading / and then recurse number of nested dirs\n                        // to place from base folder\n                        originPage\n                          .slice(1)\n                          .split('/')\n                          .map(() => '..')\n                          .join('/')\n                      ),\n                      file\n                    )\n                  )\n                  .replace(/\\\\/g, '/')\n\n                if (\n                  !isSsg &&\n                  !(\n                    // don't add static status page to manifest if it's\n                    // the default generated version e.g. no pages/500\n                    (\n                      STATIC_STATUS_PAGES.includes(page) &&\n                      !usedStaticStatusPages.includes(page)\n                    )\n                  )\n                ) {\n                  pagesManifest[page] = relativeDest\n                }\n\n                const dest = path.join(distDir, SERVER_DIRECTORY, relativeDest)\n                const isNotFound =\n                  prerenderManifest.notFoundRoutes.includes(page)\n\n                // for SSG files with i18n the non-prerendered variants are\n                // output with the locale prefixed so don't attempt moving\n                // without the prefix\n                if ((!i18n || additionalSsgFile) && !isNotFound) {\n                  await fs.mkdir(path.dirname(dest), { recursive: true })\n                  await fs.rename(orig, dest)\n                } else if (i18n && !isSsg) {\n                  // this will be updated with the locale prefixed variant\n                  // since all files are output with the locale prefix\n                  delete pagesManifest[page]\n                }\n\n                if (i18n) {\n                  if (additionalSsgFile) return\n\n                  const localeExt = page === '/' ? path.extname(file) : ''\n                  const relativeDestNoPages = relativeDest.slice(\n                    'pages/'.length\n                  )\n\n                  for (const locale of i18n.locales) {\n                    const curPath = `/${locale}${page === '/' ? '' : page}`\n\n                    if (\n                      isSsg &&\n                      prerenderManifest.notFoundRoutes.includes(curPath)\n                    ) {\n                      continue\n                    }\n\n                    const updatedRelativeDest = path\n                      .join(\n                        'pages',\n                        locale + localeExt,\n                        // if it's the top-most index page we want it to be locale.EXT\n                        // instead of locale/index.html\n                        page === '/' ? '' : relativeDestNoPages\n                      )\n                      .replace(/\\\\/g, '/')\n\n                    const updatedOrig = path.join(\n                      outdir,\n                      locale + localeExt,\n                      page === '/' ? '' : file\n                    )\n                    const updatedDest = path.join(\n                      distDir,\n                      SERVER_DIRECTORY,\n                      updatedRelativeDest\n                    )\n\n                    if (!isSsg) {\n                      pagesManifest[curPath] = updatedRelativeDest\n                    }\n                    await fs.mkdir(path.dirname(updatedDest), {\n                      recursive: true,\n                    })\n                    await fs.rename(updatedOrig, updatedDest)\n                  }\n                }\n              })\n          }\n\n          async function moveExportedAppNotFoundTo404() {\n            return staticGenerationSpan\n              .traceChild('move-exported-app-not-found-')\n              .traceAsyncFn(async () => {\n                const orig = path.join(\n                  distDir,\n                  'server',\n                  'app',\n                  '_not-found.html'\n                )\n                const updatedRelativeDest = path\n                  .join('pages', '404.html')\n                  .replace(/\\\\/g, '/')\n\n                if (existsSync(orig)) {\n                  await fs.copyFile(\n                    orig,\n                    path.join(distDir, 'server', updatedRelativeDest)\n                  )\n                  pagesManifest['/404'] = updatedRelativeDest\n                }\n              })\n          }\n\n          // If there's /not-found inside app, we prefer it over the pages 404\n          if (hasStaticApp404) {\n            await moveExportedAppNotFoundTo404()\n          } else {\n            // Only move /404 to /404 when there is no custom 404 as in that case we don't know about the 404 page\n            if (!hasPages404 && !hasApp404 && useStaticPages404) {\n              await moveExportedPage('/_error', '/404', '/404', false, 'html')\n            }\n          }\n\n          if (useDefaultStatic500) {\n            await moveExportedPage('/_error', '/500', '/500', false, 'html')\n          }\n\n          for (const page of combinedPages) {\n            const isSsg = ssgPages.has(page)\n            const isStaticSsgFallback = ssgStaticFallbackPages.has(page)\n            const isDynamic = isDynamicRoute(page)\n            const hasAmp = hybridAmpPages.has(page)\n            const file = normalizePagePath(page)\n\n            const pageInfo = pageInfos.get(page)\n            const durationInfo = exportResult.byPage.get(page)\n            if (pageInfo && durationInfo) {\n              // Set Build Duration\n              if (pageInfo.ssgPageRoutes) {\n                pageInfo.ssgPageDurations = pageInfo.ssgPageRoutes.map(\n                  (pagePath) => {\n                    const duration = durationInfo.durationsByPath.get(pagePath)\n                    if (typeof duration === 'undefined') {\n                      throw new Error(\"Invariant: page wasn't built\")\n                    }\n\n                    return duration\n                  }\n                )\n              }\n              pageInfo.pageDuration = durationInfo.durationsByPath.get(page)\n            }\n\n            // The dynamic version of SSG pages are only prerendered if the\n            // fallback is enabled. Below, we handle the specific prerenders\n            // of these.\n            const hasHtmlOutput = !(isSsg && isDynamic && !isStaticSsgFallback)\n\n            if (hasHtmlOutput) {\n              await moveExportedPage(page, page, file, isSsg, 'html')\n            }\n\n            if (hasAmp && (!isSsg || (isSsg && !isDynamic))) {\n              const ampPage = `${file}.amp`\n              await moveExportedPage(page, ampPage, ampPage, isSsg, 'html')\n\n              if (isSsg) {\n                await moveExportedPage(page, ampPage, ampPage, isSsg, 'json')\n              }\n            }\n\n            if (isSsg) {\n              // For a non-dynamic SSG page, we must copy its data file\n              // from export, we already moved the HTML file above\n              if (!isDynamic) {\n                await moveExportedPage(page, page, file, isSsg, 'json')\n\n                if (i18n) {\n                  // TODO: do we want to show all locale variants in build output\n                  for (const locale of i18n.locales) {\n                    const localePage = `/${locale}${page === '/' ? '' : page}`\n\n                    prerenderManifest.routes[localePage] = {\n                      initialRevalidateSeconds:\n                        exportResult.byPath.get(localePage)?.revalidate ??\n                        false,\n                      experimentalPPR: undefined,\n                      renderingMode: undefined,\n                      srcRoute: null,\n                      dataRoute: path.posix.join(\n                        '/_next/data',\n                        buildId,\n                        `${file}.json`\n                      ),\n                      prefetchDataRoute: undefined,\n                      allowHeader: ALLOWED_HEADERS,\n                    }\n                  }\n                } else {\n                  prerenderManifest.routes[page] = {\n                    initialRevalidateSeconds:\n                      exportResult.byPath.get(page)?.revalidate ?? false,\n                    experimentalPPR: undefined,\n                    renderingMode: undefined,\n                    srcRoute: null,\n                    dataRoute: path.posix.join(\n                      '/_next/data',\n                      buildId,\n                      `${file}.json`\n                    ),\n                    // Pages does not have a prefetch data route.\n                    prefetchDataRoute: undefined,\n                    allowHeader: ALLOWED_HEADERS,\n                  }\n                }\n                // Set Page Revalidation Interval\n                if (pageInfo) {\n                  pageInfo.initialRevalidateSeconds =\n                    exportResult.byPath.get(page)?.revalidate ?? false\n                }\n              } else {\n                // For a dynamic SSG page, we did not copy its data exports and only\n                // copy the fallback HTML file (if present).\n                // We must also copy specific versions of this page as defined by\n                // `getStaticPaths` (additionalSsgPaths).\n                for (const route of additionalPaths.get(page) ?? []) {\n                  const pageFile = normalizePagePath(route.pathname)\n                  await moveExportedPage(\n                    page,\n                    route.pathname,\n                    pageFile,\n                    isSsg,\n                    'html',\n                    true\n                  )\n                  await moveExportedPage(\n                    page,\n                    route.pathname,\n                    pageFile,\n                    isSsg,\n                    'json',\n                    true\n                  )\n\n                  if (hasAmp) {\n                    const ampPage = `${pageFile}.amp`\n                    await moveExportedPage(\n                      page,\n                      ampPage,\n                      ampPage,\n                      isSsg,\n                      'html',\n                      true\n                    )\n                    await moveExportedPage(\n                      page,\n                      ampPage,\n                      ampPage,\n                      isSsg,\n                      'json',\n                      true\n                    )\n                  }\n\n                  const initialRevalidateSeconds =\n                    exportResult.byPath.get(route.pathname)?.revalidate ?? false\n\n                  if (typeof initialRevalidateSeconds === 'undefined') {\n                    throw new Error(\"Invariant: page wasn't built\")\n                  }\n\n                  prerenderManifest.routes[route.pathname] = {\n                    initialRevalidateSeconds,\n                    experimentalPPR: undefined,\n                    renderingMode: undefined,\n                    srcRoute: page,\n                    dataRoute: path.posix.join(\n                      '/_next/data',\n                      buildId,\n                      `${normalizePagePath(route.pathname)}.json`\n                    ),\n                    // Pages does not have a prefetch data route.\n                    prefetchDataRoute: undefined,\n                    allowHeader: ALLOWED_HEADERS,\n                  }\n\n                  // Set route Revalidation Interval\n                  if (pageInfo) {\n                    pageInfo.initialRevalidateSeconds = initialRevalidateSeconds\n                  }\n                }\n              }\n            }\n          }\n\n          // remove temporary export folder\n          await fs.rm(outdir, { recursive: true, force: true })\n          await writeManifest(pagesManifestPath, pagesManifest)\n        })\n\n        // We need to write the manifest with rewrites after build as it might\n        // have been modified.\n        await nextBuildSpan\n          .traceChild('write-routes-manifest')\n          .traceAsyncFn(() => writeManifest(routesManifestPath, routesManifest))\n      }\n\n      const postBuildSpinner = createSpinner('Finalizing page optimization')\n      let buildTracesSpinner = createSpinner(`Collecting build traces`)\n\n      // ensure the worker is not left hanging\n      worker.end()\n\n      const analysisEnd = process.hrtime(analysisBegin)\n      telemetry.record(\n        eventBuildOptimize(pagesPaths, {\n          durationInSeconds: analysisEnd[0],\n          staticPageCount: staticPages.size,\n          staticPropsPageCount: ssgPages.size,\n          serverPropsPageCount: serverPropsPages.size,\n          ssrPageCount:\n            pagesPaths.length -\n            (staticPages.size + ssgPages.size + serverPropsPages.size),\n          hasStatic404: useStaticPages404,\n          hasReportWebVitals:\n            namedExports?.includes('reportWebVitals') ?? false,\n          rewritesCount: combinedRewrites.length,\n          headersCount: headers.length,\n          redirectsCount: redirects.length - 1, // reduce one for trailing slash\n          headersWithHasCount: headers.filter((r: any) => !!r.has).length,\n          rewritesWithHasCount: combinedRewrites.filter((r: any) => !!r.has)\n            .length,\n          redirectsWithHasCount: redirects.filter((r: any) => !!r.has).length,\n          middlewareCount: hasMiddlewareFile ? 1 : 0,\n          totalAppPagesCount,\n          staticAppPagesCount,\n          serverAppPagesCount,\n          edgeRuntimeAppCount,\n          edgeRuntimePagesCount,\n        })\n      )\n\n      if (NextBuildContext.telemetryState) {\n        const events = eventBuildFeatureUsage(\n          NextBuildContext.telemetryState.usages\n        )\n        telemetry.record(events)\n        telemetry.record(\n          eventPackageUsedInGetServerSideProps(\n            NextBuildContext.telemetryState.packagesUsedInServerSideProps\n          )\n        )\n        const useCacheTracker = NextBuildContext.telemetryState.useCacheTracker\n\n        for (const [key, value] of Object.entries(useCacheTracker)) {\n          telemetry.record(\n            eventBuildFeatureUsage([\n              {\n                featureName: key as UseCacheTrackerKey,\n                invocationCount: value,\n              },\n            ])\n          )\n        }\n      }\n\n      if (ssgPages.size > 0 || appDir) {\n        tbdPrerenderRoutes.forEach((tbdRoute) => {\n          const normalizedRoute = normalizePagePath(tbdRoute)\n          const dataRoute = path.posix.join(\n            '/_next/data',\n            buildId,\n            `${normalizedRoute}.json`\n          )\n\n          prerenderManifest.dynamicRoutes[tbdRoute] = {\n            routeRegex: normalizeRouteRegex(\n              getNamedRouteRegex(tbdRoute, {\n                prefixRouteKeys: false,\n              }).re.source\n            ),\n            experimentalPPR: undefined,\n            renderingMode: undefined,\n            dataRoute,\n            fallback: ssgBlockingFallbackPages.has(tbdRoute)\n              ? null\n              : ssgStaticFallbackPages.has(tbdRoute)\n                ? `${normalizedRoute}.html`\n                : false,\n            fallbackRevalidate: undefined,\n            fallbackSourceRoute: undefined,\n            fallbackRootParams: undefined,\n            dataRouteRegex: normalizeRouteRegex(\n              getNamedRouteRegex(dataRoute, {\n                prefixRouteKeys: true,\n                includeSuffix: true,\n                excludeOptionalTrailingSlash: true,\n              }).re.source\n            ),\n            // Pages does not have a prefetch data route.\n            prefetchDataRoute: undefined,\n            prefetchDataRouteRegex: undefined,\n            allowHeader: ALLOWED_HEADERS,\n          }\n        })\n\n        NextBuildContext.previewModeId = previewProps.previewModeId\n        NextBuildContext.fetchCacheKeyPrefix =\n          config.experimental.fetchCacheKeyPrefix\n        NextBuildContext.allowedRevalidateHeaderKeys =\n          config.experimental.allowedRevalidateHeaderKeys\n\n        await writePrerenderManifest(distDir, prerenderManifest)\n        await writeClientSsgManifest(prerenderManifest, {\n          distDir,\n          buildId,\n          locales: config.i18n?.locales,\n        })\n      } else {\n        await writePrerenderManifest(distDir, {\n          version: 4,\n          routes: {},\n          dynamicRoutes: {},\n          preview: previewProps,\n          notFoundRoutes: [],\n        })\n      }\n\n      await writeImagesManifest(distDir, config)\n      await writeManifest(path.join(distDir, EXPORT_MARKER), {\n        version: 1,\n        hasExportPathMap: typeof config.exportPathMap === 'function',\n        exportTrailingSlash: config.trailingSlash === true,\n        isNextImageImported: isNextImageImported === true,\n      })\n      await fs.unlink(path.join(distDir, EXPORT_DETAIL)).catch((err) => {\n        if (err.code === 'ENOENT') {\n          return Promise.resolve()\n        }\n        return Promise.reject(err)\n      })\n\n      if (Boolean(config.experimental.nextScriptWorkers)) {\n        await nextBuildSpan\n          .traceChild('verify-partytown-setup')\n          .traceAsyncFn(async () => {\n            await verifyPartytownSetup(\n              dir,\n              path.join(distDir, CLIENT_STATIC_FILES_PATH)\n            )\n          })\n      }\n\n      await buildTracesPromise\n\n      if (buildTracesSpinner) {\n        buildTracesSpinner.stopAndPersist()\n        buildTracesSpinner = undefined\n      }\n\n      if (config.output === 'export') {\n        await writeFullyStaticExport(\n          config,\n          dir,\n          enabledDirectories,\n          configOutDir,\n          nextBuildSpan\n        )\n      }\n\n      if (config.output === 'standalone') {\n        await writeStandaloneDirectory(\n          nextBuildSpan,\n          distDir,\n          pageKeys,\n          denormalizedAppPages,\n          outputFileTracingRoot,\n          requiredServerFilesManifest,\n          middlewareManifest,\n          hasNodeMiddleware,\n          hasInstrumentationHook,\n          staticPages,\n          loadedEnvFiles,\n          appDir\n        )\n      }\n\n      if (postBuildSpinner) postBuildSpinner.stopAndPersist()\n      console.log()\n\n      if (debugOutput) {\n        nextBuildSpan\n          .traceChild('print-custom-routes')\n          .traceFn(() => printCustomRoutes({ redirects, rewrites, headers }))\n      }\n\n      await nextBuildSpan.traceChild('print-tree-view').traceAsyncFn(() =>\n        printTreeView(pageKeys, pageInfos, {\n          distPath: distDir,\n          buildId: buildId,\n          pagesDir,\n          useStaticPages404,\n          pageExtensions: config.pageExtensions,\n          appBuildManifest,\n          buildManifest,\n          middlewareManifest,\n          gzipSize: config.experimental.gzipSize,\n        })\n      )\n\n      await nextBuildSpan\n        .traceChild('telemetry-flush')\n        .traceAsyncFn(() => telemetry.flush())\n\n      await shutdownPromise\n    })\n  } finally {\n    // Ensure we wait for lockfile patching if present\n    await lockfilePatchPromise.cur\n\n    // Ensure all traces are flushed before finishing the command\n    await flushAllTraces()\n    teardownTraceSubscriber()\n\n    if (traceUploadUrl && loadedConfig) {\n      uploadTrace({\n        traceUploadUrl,\n        mode: 'build',\n        projectDir: dir,\n        distDir: loadedConfig.distDir,\n        isTurboSession: turboNextBuild,\n        sync: true,\n      })\n    }\n  }\n}\n"], "names": ["createStaticWorker", "build", "ALLOWED_HEADERS", "MATCHED_PATH_HEADER", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "pageToRoute", "page", "routeRegex", "getNamedRouteRegex", "prefixRouteKeys", "regex", "normalizeRouteRegex", "re", "source", "routeKeys", "namedRegex", "getCacheDir", "distDir", "cacheDir", "path", "join", "ciEnvironment", "isCI", "hasNextSupport", "<PERSON><PERSON><PERSON>", "existsSync", "console", "log", "Log", "prefixes", "warn", "writeFileUtf8", "filePath", "content", "fs", "writeFile", "readFileUtf8", "readFile", "writeManifest", "manifest", "formatManifest", "readManifest", "JSON", "parse", "writePrerenderManifest", "PRERENDER_MANIFEST", "writeClientSsgManifest", "prerenderManifest", "buildId", "locales", "ssgPages", "Set", "Object", "entries", "routes", "filter", "srcRoute", "map", "route", "normalizeLocalePath", "pathname", "keys", "dynamicRoutes", "sort", "clientSsgManifestContent", "devalue", "CLIENT_STATIC_FILES_PATH", "writeFunctionsConfigManifest", "SERVER_DIRECTORY", "FUNCTIONS_CONFIG_MANIFEST", "writeRequiredServerFilesManifest", "requiredServerFiles", "SERVER_FILES_MANIFEST", "writeImagesManifest", "config", "images", "deviceSizes", "imageSizes", "sizes", "remotePatterns", "p", "protocol", "hostname", "makeRe", "port", "dot", "search", "localPatterns", "IMAGES_MANIFEST", "version", "STANDALONE_DIRECTORY", "writeStandaloneDirectory", "nextBuildSpan", "pageKeys", "denormalizedAppPages", "outputFileTracingRoot", "middlewareManifest", "hasNodeMiddleware", "hasInstrumentationHook", "staticPages", "loadedEnvFiles", "appDir", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "copyTracedFiles", "pages", "file", "files", "reduce", "acc", "envFile", "includes", "push", "outputPath", "relative", "mkdir", "dirname", "recursive", "copyFile", "middlewareOutput", "recursiveCopy", "overwrite", "originalServerApp", "getNumberOfWorkers", "experimental", "cpus", "defaultConfig", "memoryBasedWorkersCount", "Math", "max", "min", "floor", "os", "freemem", "staticWorkerPath", "require", "resolve", "staticWorkerExposedMethods", "progress", "nodeOptions", "getParsedNodeOptionsWithoutInspect", "Worker", "logger", "numWorkers", "onActivity", "run", "onActivityAbort", "clear", "forkOptions", "env", "process", "NODE_OPTIONS", "formatNodeOptions", "enableWorkerThreads", "workerThreads", "exposedMethods", "writeFullyStaticExport", "dir", "enabledDirectories", "configOutDir", "exportApp", "default", "pagesWorker", "appWorker", "buildExport", "nextConfig", "silent", "outdir", "end", "getBuildId", "isGenerateMode", "generateBuildId", "nanoid", "reactProductionProfiling", "debugOutput", "runLint", "noMangling", "appDirOnly", "turboNextBuild", "experimentalBuildMode", "traceUploadUrl", "isCompileMode", "loadedConfig", "trace", "undefined", "buildMode", "isTurboBuild", "String", "__NEXT_VERSION", "NextBuildContext", "mappedPages", "traceFn", "loadEnvConfig", "turborepoAccessTraceResult", "TurborepoAccessTraceResult", "turborepoTraceAccess", "loadConfig", "PHASE_PRODUCTION_BUILD", "NEXT_DEPLOYMENT_ID", "deploymentId", "hasCustomExportOutput", "setGlobal", "customRoutes", "loadCustomRoutes", "headers", "rewrites", "redirects", "combinedRewrites", "beforeFiles", "afterFiles", "fallback", "hasRewrites", "length", "originalRewrites", "_originalRewrites", "originalRedirects", "_originalRedirects", "telemetry", "Telemetry", "publicDir", "pagesDir", "findPagesDir", "app", "<PERSON><PERSON><PERSON>", "generateEncryptionKeyBase64", "isBuild", "isSrcDir", "startsWith", "hasPublicDir", "record", "eventCliSession", "webpackVersion", "cliCommand", "has<PERSON>ow<PERSON><PERSON>", "findUp", "cwd", "isCustomServer", "turboFlag", "eventNextPlugins", "then", "events", "eventSwcPlugins", "envInfo", "experimentalFeatures", "getStartServerInfo", "logStartInfo", "networkUrl", "appUrl", "ignoreESLint", "Boolean", "eslint", "ignoreDuringBuilds", "shouldLint", "typeCheckingOptions", "distDirCreated", "err", "isError", "code", "isWriteable", "Error", "cleanDistDir", "recursiveDelete", "startTypeChecking", "error", "flush", "exit", "buildLintEvent", "featureName", "invocationCount", "eventName", "EVENT_BUILD_FEATURE_USAGE", "payload", "validFile<PERSON><PERSON><PERSON>", "createValidFileMatcher", "pageExtensions", "providedPagePaths", "NEXT_PRIVATE_PAGE_PATHS", "pagesPaths", "recursiveReadDir", "pathnameFilter", "isPageFile", "middlewareDetectionRegExp", "RegExp", "MIDDLEWARE_FILENAME", "instrumentationHookDetectionRegExp", "INSTRUMENTATION_HOOK_FILENAME", "rootDir", "rootPaths", "Array", "from", "getFilesInDir", "some", "include", "test", "sortByPageExts", "replace", "hasMiddlewareFile", "previewProps", "previewModeId", "crypto", "randomBytes", "toString", "previewModeSigningKey", "previewModeEncryptionKey", "createPagesMapping", "isDev", "pagesType", "PAGE_TYPES", "PAGES", "pagePaths", "mappedAppPages", "providedAppPaths", "NEXT_PRIVATE_APP_PATHS", "appPaths", "absolutePath", "isAppRouterPage", "isRootNotFound", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "part", "APP", "mappedRootPaths", "ROOT", "pagesPageKeys", "conflictingAppPagePaths", "appPageKeys", "appKey", "normalizedAppPageKey", "normalizeAppPath", "pagePath", "appPath", "add", "generateInterceptionRoutesRewrites", "basePath", "totalAppPagesCount", "numConflictingAppPaths", "conflictingPublicFiles", "hasPages404", "PAGES_DIR_ALIAS", "hasApp404", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "hasCustomErrorPage", "hasPublicUnderScoreNextDir", "PUBLIC_DIR_MIDDLEWARE_CONFLICT", "hasPublicPageFile", "fileExists", "FileType", "File", "numConflicting", "nestedReservedPages", "match", "restrictedRedirectPaths", "isAppDynamicIOEnabled", "dynamicIO", "isAuthInterruptsEnabled", "authInterrupts", "isAppPPREnabled", "checkIsAppPPREnabled", "ppr", "routesManifestPath", "ROUTES_MANIFEST", "routesManifest", "sortedRoutes", "getSortedRoutes", "staticRoutes", "isDynamicRoute", "isReservedPage", "pages404", "caseSensitive", "caseSensitiveRoutes", "r", "buildCustomRoute", "dataRoutes", "i18n", "rsc", "header", "RSC_HEADER", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "prefetch<PERSON><PERSON><PERSON>", "didPostponeHeader", "NEXT_DID_POSTPONE_HEADER", "contentTypeHeader", "RSC_CONTENT_TYPE_HEADER", "suffix", "RSC_SUFFIX", "prefetchSuffix", "RSC_PREFETCH_SUFFIX", "prefetchSegmentHeader", "prefetchSegmentSuffix", "RSC_SEGMENT_SUFFIX", "prefetchSegmentDirSuffix", "RSC_SEGMENTS_DIR_SUFFIX", "rewriteHeaders", "pathHeader", "NEXT_REWRITTEN_PATH_HEADER", "query<PERSON>eader", "NEXT_REWRITTEN_QUERY_HEADER", "skipMiddlewareUrlNormalize", "chain", "NEXT_RESUME_HEADER", "clientRouterFilters", "clientRouterFilter", "nonInternalRedirects", "internal", "createClientRouterFilter", "clientRouterFilterRedirects", "clientRouterFilterAllowedRate", "recordFrameworkVersion", "updateBuildDiagnostics", "buildStage", "pagesManifestPath", "PAGES_MANIFEST", "buildTraceContext", "buildTracesPromise", "useBuildWorker", "webpackBuildWorker", "webpack", "runServerAndEdgeInParallel", "parallelServerCompiles", "collectServerBuildTracesInParallel", "parallelServerBuildTraces", "setAttribute", "info", "traceMemoryUsage", "buildOptions", "shutdownPromise", "Promise", "duration", "compilerDuration", "rest", "turbopackBuild", "NEXT_TURBOPACK_USE_WORKER", "durationString", "round", "event", "eventBuildCompleted", "durationInSeconds", "serverBuildPromise", "webpackBuild", "res", "buildTraceWorker", "collectBuildTraces", "edgeRuntimeRoutes", "collectRoutesUsingEdgeRuntime", "Map", "hasSsrAmpPages", "catch", "edgeBuildPromise", "postCompileSpinner", "createSpinner", "buildManifestPath", "BUILD_MANIFEST", "appBuildManifestPath", "APP_BUILD_MANIFEST", "staticAppPagesCount", "serverAppPagesCount", "edgeRuntimeAppCount", "edgeRuntimePagesCount", "ssgStaticFallbackPages", "ssgBlockingFallbackPages", "invalidPages", "hybridAmpPages", "serverPropsPages", "additionalPaths", "staticPaths", "prospectiveRenders", "appNormalizedPaths", "fallbackModes", "appDefaultConfigs", "pageInfos", "pagesManifest", "buildManifest", "appBuildManifest", "appPathRoutes", "appPathsManifest", "APP_PATHS_MANIFEST", "key", "APP_PATH_ROUTES_MANIFEST", "NEXT_PHASE", "worker", "analysisBegin", "hrtime", "staticCheckSpan", "functionsConfigManifest", "functions", "customAppGetInitialProps", "namedExports", "isNextImageImported", "hasNonStaticErrorPage", "configFileName", "publicRuntimeConfig", "serverRuntimeConfig", "runtimeEnvConfig", "sriEnabled", "sri", "algorithm", "nonStaticErrorPageSpan", "errorPageHasCustomGetInitialProps", "hasCustomGetInitialProps", "checkingApp", "errorPageStaticResult", "isPageStatic", "httpAgentOptions", "defaultLocale", "nextConfigOutput", "output", "pprConfig", "cacheLifeProfiles", "cacheLife", "appPageToCheck", "customAppGetInitialPropsPromise", "namedExportsPromise", "getDefinedNamedExports", "computedManifestData", "computeFromManifest", "gzipSize", "MIDDLEWARE_MANIFEST", "actionManifest", "SERVER_REFERENCE_MANIFEST", "entriesWithAction", "id", "node", "entry", "workers", "edge", "all", "pageType", "checkPageSpan", "actualPage", "normalizePagePath", "size", "totalSize", "getJsPageSizeInKb", "isRoutePPREnabled", "isSSG", "isStatic", "isServerComponent", "isHybridAmp", "ssgPageRoutes", "find", "normalizePathSep", "originalAppPath", "originalPath", "normalizedPath", "pageFilePath", "isAppBuiltinNotFoundPage", "isInsideAppDir", "staticInfo", "getStaticInfoIncludingLayouts", "runtime", "maxDuration", "pageRuntime", "RSC_MODULE_TYPES", "client", "edgeInfo", "isEdgeRuntime", "manifest<PERSON>ey", "isPageStaticSpan", "workerResult", "parentId", "getId", "cache<PERSON><PERSON><PERSON>", "cacheHandlers", "isrFlushToDisk", "maxMemoryCacheSize", "cacheMaxMemorySize", "set", "warnOnce", "isDynamic", "prerenderedRoutes", "appConfig", "revalidate", "hasGenerateStaticParams", "encodedPathname", "fallbackRouteParams", "fallbackMode", "prerenderFallbackMode", "fallbackRootParams", "dynamic", "hasStaticProps", "isAmpOnly", "FallbackMode", "BLOCKING_STATIC_RENDER", "PRERENDER", "hasServerProps", "STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR", "delete", "STATIC_STATUS_PAGES", "message", "initialRevalidateSeconds", "pageDuration", "ssgPageDurations", "hasEmptyPrelude", "hadUnsupportedValue", "errorPageResult", "nonStaticErrorPage", "returnValue", "stopAndPersist", "bold", "yellow", "instrumentationHookEntryFiles", "TURBOPACK", "requiredServerFilesManifest", "normalizedCacheHandlers", "value", "serverFilesManifest", "configFile", "compress", "trustHostHeader", "isExperimentalCompile", "relativeAppDir", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "REACT_LOADABLE_MANIFEST", "SUBRESOURCE_INTEGRITY_MANIFEST", "DYNAMIC_CSS_MANIFEST", "BUILD_ID_FILE", "NEXT_FONT_MANIFEST", "nonNullable", "ignore", "middlewareFile", "matchers", "middleware", "regexp", "originalSource", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "buildDataRoute", "useStaticPages404", "pg", "writeBuildId", "optimizeCss", "globOrig", "cssFilePaths", "reject", "features", "nextScriptWorkers", "feature", "notFoundRoutes", "preview", "tbdPrerenderRoutes", "usedStaticStatusPages", "for<PERSON>ach", "has", "hasPages500", "useDefaultStatic500", "combinedPages", "isApp404Static", "hasStaticApp404", "staticGenerationSpan", "detectConflictingPaths", "exportConfig", "exportPathMap", "defaultMap", "_pagesFallback", "_ssgPath", "get", "isDynamicError", "checkIsRoutePPREnabled", "_fallbackRouteParams", "_isDynamicError", "_isAppDir", "_isRoutePPREnabled", "values", "get<PERSON>ara<PERSON><PERSON><PERSON><PERSON>", "_isProspectiveRender", "isSsg", "<PERSON><PERSON><PERSON><PERSON>", "locale", "_locale", "exportResult", "statusMessage", "NEXT_SSG_FETCH_METRICS", "recordFetchMetrics", "writeTurborepoAccessTraceResult", "traces", "turborepoAccessTraceResults", "ssgNotFoundPaths", "serverBundle", "getPagePath", "unlink", "InvariantError", "hasRevalidateZero", "by<PERSON><PERSON>", "isAppRouteHandler", "isAppRouteRoute", "htmlBotsRegexString", "htmlLimitedBots", "HTML_LIMITED_BOT_UA_RE_STRING", "bypassFor", "type", "ACTION_HEADER", "__NEXT_EXPERIMENTAL_PPR", "unknown<PERSON>rerender<PERSON><PERSON><PERSON>", "knownPrerenderRoutes", "prerenderedRoute", "getSortedRouteObjects", "UNDERSCORE_NOT_FOUND_ROUTE", "metadata", "hasPostponed", "normalizedRoute", "dataRoute", "posix", "prefetchDataRoute", "meta", "collectMeta", "initialStatus", "status", "initialHeaders", "renderingMode", "RenderingMode", "PARTIALLY_STATIC", "STATIC", "experimentalPPR", "experimentalBypassFor", "allow<PERSON>eader", "NOT_FOUND", "segmentPaths", "dynamicRoute", "prefetchSegmentDataRoutes", "segmentPath", "result", "buildPrefetchSegmentDataRoute", "isDynamicAppRoute", "fallbackRevalidate", "fallbackModeToFallbackField", "fallback<PERSON><PERSON><PERSON>", "fallbackHeaders", "fallbackSourceRoute", "dataRouteRegex", "includeSuffix", "excludeOptionalTrailingSlash", "prefetchDataRouteRegex", "moveExportedPage", "originPage", "ext", "additionalSsgFile", "orig", "relativeDest", "slice", "split", "dest", "isNotFound", "rename", "localeExt", "extname", "relativeDestNoPages", "curPath", "updatedRelativeDest", "updatedOrig", "updatedDest", "moveExportedAppNotFoundTo404", "isStaticSsgFallback", "hasAmp", "pageInfo", "durationInfo", "byPage", "durationsByPath", "hasHtmlOutput", "ampPage", "localePage", "pageFile", "rm", "force", "postBuildSpinner", "buildTracesSpinner", "analysisEnd", "eventBuildOptimize", "staticPageCount", "staticPropsPageCount", "serverPropsPageCount", "ssrPageCount", "hasStatic404", "hasReportWebVitals", "rewritesCount", "headersCount", "redirectsCount", "headersWithHasCount", "rewritesWithHasCount", "redirectsWithHasCount", "middlewareCount", "telemetryState", "eventBuildFeatureUsage", "usages", "eventPackageUsedInGetServerSideProps", "packagesUsedInServerSideProps", "useCacheTracker", "tbdRoute", "fetchCacheKeyPrefix", "allowedRevalidateHeaderKeys", "EXPORT_MARKER", "hasExportPathMap", "exportTrailingSlash", "trailingSlash", "EXPORT_DETAIL", "verifyPartytownSetup", "printCustomRoutes", "printTreeView", "distPath", "lockfilePatchPromise", "cur", "flushAllTraces", "teardownTraceSubscriber", "uploadTrace", "mode", "projectDir", "isTurboSession", "sync"], "mappings": ";;;;;;;;;;;;;;;IAitBgBA,kBAAkB;eAAlBA;;IA0EhB,OAuxFC;eAvxF6BC;;;QApxBvB;qBAE4C;4BACtB;+DACV;2BACI;oBACoB;2DAC5B;wBACQ;8BACO;gEACV;+DACD;0BACI;6DACN;2BAiBV;4BAC8B;8BACR;0EAGtB;6BAQqB;iCACI;sCACK;4BA+B9B;uBAKA;+DAEgB;mCAEW;yBACN;gEACG;sCAKxB;wBAUA;yBAEmB;mCACU;yBAK7B;2BACoB;iCACK;6BACJ;6DACP;gEACK;uBACkC;wBAYrD;8BAIsB;qCACO;gEAChB;+BAEU;+BACA;kCACG;qBAC6B;4BAC3B;+BACL;4BACE;0BACC;kCAW1B;8BACsB;8BACsB;kCAClB;iCACD;0CACS;8BACF;2BACL;oDACiB;gCAEpB;oCACI;gCAEJ;kCAKxB;4BAC0C;wBAEX;kCACL;wBACA;uCACW;oEAEpB;qBAIjB;0BACmD;+BAC5B;gCACD;wBAItB;gCACwB;uBACe;+CAKvC;gCAEwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4G/B;;;CAGC,GACD,MAAMC,kBAA4B;IAChC;IACAC,8BAAmB;IACnBC,sCAA2B;IAC3BC,qDAA0C;IAC1CC,6CAAkC;IAClCC,iDAAsC;CACvC;AAiGD,SAASC,YAAYC,IAAY;IAC/B,MAAMC,aAAaC,IAAAA,8BAAkB,EAACF,MAAM;QAC1CG,iBAAiB;IACnB;IACA,OAAO;QACLH;QACAI,OAAOC,IAAAA,qCAAmB,EAACJ,WAAWK,EAAE,CAACC,MAAM;QAC/CC,WAAWP,WAAWO,SAAS;QAC/BC,YAAYR,WAAWQ,UAAU;IACnC;AACF;AAEA,SAASC,YAAYC,OAAe;IAClC,MAAMC,WAAWC,aAAI,CAACC,IAAI,CAACH,SAAS;IACpC,IAAII,QAAcC,IAAI,IAAI,CAACD,QAAcE,cAAc,EAAE;QACvD,MAAMC,WAAWC,IAAAA,cAAU,EAACP;QAE5B,IAAI,CAACM,UAAU;YACb,kGAAkG;YAClG,sBAAsB;YACtBE,QAAQC,GAAG,CACT,GAAGC,KAAIC,QAAQ,CAACC,IAAI,CAAC,+HAA+H,CAAC;QAEzJ;IACF;IACA,OAAOZ;AACT;AAEA,eAAea,cAAcC,QAAgB,EAAEC,OAAe;IAC5D,MAAMC,YAAE,CAACC,SAAS,CAACH,UAAUC,SAAS;AACxC;AAEA,SAASG,aAAaJ,QAAgB;IACpC,OAAOE,YAAE,CAACG,QAAQ,CAACL,UAAU;AAC/B;AAEA,eAAeM,cACbN,QAAgB,EAChBO,QAAW;IAEX,MAAMR,cAAcC,UAAUQ,IAAAA,8BAAc,EAACD;AAC/C;AAEA,eAAeE,aAA+BT,QAAgB;IAC5D,OAAOU,KAAKC,KAAK,CAAC,MAAMP,aAAaJ;AACvC;AAEA,eAAeY,uBACb3B,OAAe,EACfsB,QAAyC;IAEzC,MAAMD,cAAcnB,aAAI,CAACC,IAAI,CAACH,SAAS4B,8BAAkB,GAAGN;AAC9D;AAEA,eAAeO,uBACbC,iBAAkD,EAClD,EACEC,OAAO,EACP/B,OAAO,EACPgC,OAAO,EAKR;IAED,MAAMC,WAAW,IAAIC,IACnB;WACKC,OAAOC,OAAO,CAACN,kBAAkBO,MAAM,CACxC,4BAA4B;SAC3BC,MAAM,CAAC,CAAC,GAAG,EAAEC,QAAQ,EAAE,CAAC,GAAKA,YAAY,MACzCC,GAAG,CAAC,CAAC,CAACC,MAAM,GAAKC,IAAAA,wCAAmB,EAACD,OAAOT,SAASW,QAAQ;WAC7DR,OAAOS,IAAI,CAACd,kBAAkBe,aAAa;KAC/C,CAACC,IAAI;IAGR,MAAMC,2BAA2B,CAAC,oBAAoB,EAAEC,IAAAA,gBAAO,EAC7Df,UACA,iDAAiD,CAAC;IAEpD,MAAMnB,cACJZ,aAAI,CAACC,IAAI,CAACH,SAASiD,oCAAwB,EAAElB,SAAS,oBACtDgB;AAEJ;AAmBA,eAAeG,6BACblD,OAAe,EACfsB,QAAiC;IAEjC,MAAMD,cACJnB,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAEC,qCAAyB,GAC9D9B;AAEJ;AAWA,eAAe+B,iCACbrD,OAAe,EACfsD,mBAAgD;IAEhD,MAAMjC,cACJnB,aAAI,CAACC,IAAI,CAACH,SAASuD,iCAAqB,GACxCD;AAEJ;AAEA,eAAeE,oBACbxD,OAAe,EACfyD,MAA0B;QAODA,gBAUrBA;IAfJ,MAAMC,SAAS;QAAE,GAAGD,OAAOC,MAAM;IAAC;IAClC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAE,GAAGF;IAClCA,OAAeG,KAAK,GAAG;WAAIF;WAAgBC;KAAW;IAExD,8DAA8D;IAC9DF,OAAOI,cAAc,GAAG,AAACL,CAAAA,CAAAA,2BAAAA,iBAAAA,OAAQC,MAAM,qBAAdD,eAAgBK,cAAc,KAAI,EAAE,AAAD,EAAGtB,GAAG,CAAC,CAACuB,IAAO,CAAA;YACzE,iEAAiE;YACjEC,UAAUD,EAAEC,QAAQ;YACpBC,UAAUC,IAAAA,iBAAM,EAACH,EAAEE,QAAQ,EAAErE,MAAM;YACnCuE,MAAMJ,EAAEI,IAAI;YACZxB,UAAUuB,IAAAA,iBAAM,EAACH,EAAEpB,QAAQ,IAAI,MAAM;gBAAEyB,KAAK;YAAK,GAAGxE,MAAM;YAC1DyE,QAAQN,EAAEM,MAAM;QAClB,CAAA;IAEA,oEAAoE;IACpE,IAAIZ,2BAAAA,kBAAAA,OAAQC,MAAM,qBAAdD,gBAAgBa,aAAa,EAAE;QACjCZ,OAAOY,aAAa,GAAGb,OAAOC,MAAM,CAACY,aAAa,CAAC9B,GAAG,CAAC,CAACuB,IAAO,CAAA;gBAC7D,gEAAgE;gBAChEpB,UAAUuB,IAAAA,iBAAM,EAACH,EAAEpB,QAAQ,IAAI,MAAM;oBAAEyB,KAAK;gBAAK,GAAGxE,MAAM;gBAC1DyE,QAAQN,EAAEM,MAAM;YAClB,CAAA;IACF;IAEA,MAAMhD,cAAcnB,aAAI,CAACC,IAAI,CAACH,SAASuE,2BAAe,GAAG;QACvDC,SAAS;QACTd;IACF;AACF;AAEA,MAAMe,uBAAuB;AAC7B,eAAeC,yBACbC,aAAmB,EACnB3E,OAAe,EACf4E,QAAwD,EACxDC,oBAA0C,EAC1CC,qBAA6B,EAC7BxB,mBAAgD,EAChDyB,kBAAsC,EACtCC,iBAA0B,EAC1BC,sBAA+B,EAC/BC,WAAwB,EACxBC,cAA8B,EAC9BC,MAA0B;IAE1B,MAAMT,cACHU,UAAU,CAAC,8BACXC,YAAY,CAAC;QACZ,MAAMC,IAAAA,uBAAe,EACnB,kFAAkF;QAClFjC,oBAAoB8B,MAAM,EAC1BpF,SACA4E,SAASY,KAAK,EACdX,sBACAC,uBACAxB,oBAAoBG,MAAM,EAC1BsB,oBACAC,mBACAC,wBACAC;QAGF,KAAK,MAAMO,QAAQ;eACdnC,oBAAoBoC,KAAK;YAC5BxF,aAAI,CAACC,IAAI,CAACmD,oBAAoBG,MAAM,CAACzD,OAAO,EAAEuD,iCAAqB;eAChE4B,eAAeQ,MAAM,CAAW,CAACC,KAAKC;gBACvC,IAAI;oBAAC;oBAAQ;iBAAkB,CAACC,QAAQ,CAACD,QAAQ3F,IAAI,GAAG;oBACtD0F,IAAIG,IAAI,CAACF,QAAQ3F,IAAI;gBACvB;gBACA,OAAO0F;YACT,GAAG,EAAE;SACN,CAAE;YACD,kFAAkF;YAClF,MAAM7E,WAAWb,aAAI,CAACC,IAAI,CAACmD,oBAAoB8B,MAAM,EAAEK;YACvD,MAAMO,aAAa9F,aAAI,CAACC,IAAI,CAC1BH,SACAyE,sBACAvE,aAAI,CAAC+F,QAAQ,CAACnB,uBAAuB/D;YAEvC,MAAME,YAAE,CAACiF,KAAK,CAAChG,aAAI,CAACiG,OAAO,CAACH,aAAa;gBACvCI,WAAW;YACb;YACA,MAAMnF,YAAE,CAACoF,QAAQ,CAACtF,UAAUiF;QAC9B;QAEA,IAAIhB,mBAAmB;YACrB,MAAMsB,mBAAmBpG,aAAI,CAACC,IAAI,CAChCH,SACAyE,sBACAvE,aAAI,CAAC+F,QAAQ,CAACnB,uBAAuB9E,UACrCmD,4BAAgB,EAChB;YAGF,MAAMlC,YAAE,CAACiF,KAAK,CAAChG,aAAI,CAACiG,OAAO,CAACG,mBAAmB;gBAAEF,WAAW;YAAK;YACjE,MAAMnF,YAAE,CAACoF,QAAQ,CACfnG,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAE,kBACrCmD;QAEJ;QAEA,MAAMC,IAAAA,4BAAa,EACjBrG,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAE,UACrCjD,aAAI,CAACC,IAAI,CACPH,SACAyE,sBACAvE,aAAI,CAAC+F,QAAQ,CAACnB,uBAAuB9E,UACrCmD,4BAAgB,EAChB,UAEF;YAAEqD,WAAW;QAAK;QAEpB,IAAIpB,QAAQ;YACV,MAAMqB,oBAAoBvG,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAE;YAC/D,IAAI3C,IAAAA,cAAU,EAACiG,oBAAoB;gBACjC,MAAMF,IAAAA,4BAAa,EACjBE,mBACAvG,aAAI,CAACC,IAAI,CACPH,SACAyE,sBACAvE,aAAI,CAAC+F,QAAQ,CAACnB,uBAAuB9E,UACrCmD,4BAAgB,EAChB,QAEF;oBAAEqD,WAAW;gBAAK;YAEtB;QACF;IACF;AACJ;AAEA,SAASE,mBAAmBjD,MAA0B;IACpD,IACEA,OAAOkD,YAAY,CAACC,IAAI,IACxBnD,OAAOkD,YAAY,CAACC,IAAI,KAAKC,2BAAa,CAACF,YAAY,CAAEC,IAAI,EAC7D;QACA,OAAOnD,OAAOkD,YAAY,CAACC,IAAI;IACjC;IAEA,IAAInD,OAAOkD,YAAY,CAACG,uBAAuB,EAAE;QAC/C,OAAOC,KAAKC,GAAG,CACbD,KAAKE,GAAG,CAACxD,OAAOkD,YAAY,CAACC,IAAI,IAAI,GAAGG,KAAKG,KAAK,CAACC,WAAE,CAACC,OAAO,KAAK,OAClE,iCAAiC;QACjC;IAEJ;IAEA,IAAI3D,OAAOkD,YAAY,CAACC,IAAI,EAAE;QAC5B,OAAOnD,OAAOkD,YAAY,CAACC,IAAI;IACjC;IAEA,qDAAqD;IACrD,OAAO;AACT;AAEA,MAAMS,mBAAmBC,QAAQC,OAAO,CAAC;AACzC,MAAMC,6BAA6B;IACjC;IACA;IACA;IACA;CACD;AAEM,SAAS5I,mBACd6E,MAA0B,EAC1BgE,QAGC;IAED,2DAA2D;IAC3D,2DAA2D;IAC3D,MAAMC,cAAcC,IAAAA,0CAAkC;IACtD,OAAOD,WAAW,CAAC,qBAAqB;IACxC,OAAOA,WAAW,CAAC,qBAAqB;IAExC,OAAO,IAAIE,cAAM,CAACP,kBAAkB;QAClCQ,QAAQlH;QACRmH,YAAYpB,mBAAmBjD;QAC/BsE,YAAY;YACVN,4BAAAA,SAAUO,GAAG;QACf;QACAC,iBAAiB;YACfR,4BAAAA,SAAUS,KAAK;QACjB;QACAC,aAAa;YACXC,KAAK;gBAAE,GAAGC,QAAQD,GAAG;gBAAEE,cAAcC,IAAAA,yBAAiB,EAACb;YAAa;QACtE;QACAc,qBAAqB/E,OAAOkD,YAAY,CAAC8B,aAAa;QACtDC,gBAAgBlB;IAClB;AACF;AAEA,eAAemB,uBACblF,MAA0B,EAC1BmF,GAAW,EACXC,kBAA0C,EAC1CC,YAAoB,EACpBnE,aAAmB;IAEnB,MAAMoE,YAAYzB,QAAQ,aACvB0B,OAAO;IAEV,MAAMC,cAAcrK,mBAAmB6E;IACvC,MAAMyF,YAAYtK,mBAAmB6E;IAErC,MAAMsF,UACJH,KACA;QACEO,aAAa;QACbC,YAAY3F;QACZoF;QACAQ,QAAQ;QACRC,QAAQpJ,aAAI,CAACC,IAAI,CAACyI,KAAKE;QACvBhB,YAAYpB,mBAAmBjD;IACjC,GACAkB;IAGFsE,YAAYM,GAAG;IACfL,UAAUK,GAAG;AACf;AAEA,eAAeC,WACbC,cAAuB,EACvBzJ,OAAe,EACf2E,aAAmB,EACnBlB,MAA0B;IAE1B,IAAIgG,gBAAgB;QAClB,OAAO,MAAMxI,YAAE,CAACG,QAAQ,CAAClB,aAAI,CAACC,IAAI,CAACH,SAAS,aAAa;IAC3D;IACA,OAAO,MAAM2E,cACVU,UAAU,CAAC,oBACXC,YAAY,CAAC,IAAMoE,IAAAA,gCAAe,EAACjG,OAAOiG,eAAe,EAAEC,gBAAM;AACtE;AAEe,eAAe9K,MAC5B+J,GAAW,EACXgB,2BAA2B,KAAK,EAChCC,cAAc,KAAK,EACnBC,UAAU,IAAI,EACdC,aAAa,KAAK,EAClBC,aAAa,KAAK,EAClBC,iBAAiB,KAAK,EACtBC,qBAAyD,EACzDC,cAAkC;IAElC,MAAMC,gBAAgBF,0BAA0B;IAChD,MAAMT,iBAAiBS,0BAA0B;IAEjD,IAAIG;IACJ,IAAI;QACF,MAAM1F,gBAAgB2F,IAAAA,YAAK,EAAC,cAAcC,WAAW;YACnDC,WAAWN;YACXO,cAAcC,OAAOT;YACrBzF,SAAS6D,QAAQD,GAAG,CAACuC,cAAc;QACrC;QAEAC,8BAAgB,CAACjG,aAAa,GAAGA;QACjCiG,8BAAgB,CAAChC,GAAG,GAAGA;QACvBgC,8BAAgB,CAACZ,UAAU,GAAGA;QAC9BY,8BAAgB,CAAChB,wBAAwB,GAAGA;QAC5CgB,8BAAgB,CAACb,UAAU,GAAGA;QAE9B,MAAMpF,cAAcW,YAAY,CAAC;gBAoWXuF;YAnWpB,4EAA4E;YAC5E,MAAM,EAAE1F,cAAc,EAAE,GAAGR,cACxBU,UAAU,CAAC,eACXyF,OAAO,CAAC,IAAMC,IAAAA,kBAAa,EAACnC,KAAK,OAAOjI;YAC3CiK,8BAAgB,CAACzF,cAAc,GAAGA;YAElC,MAAM6F,6BAA6B,IAAIC,gDAA0B;YACjE,MAAMxH,SAA6B,MAAMkB,cACtCU,UAAU,CAAC,oBACXC,YAAY,CAAC,IACZ4F,IAAAA,0CAAoB,EAClB,IACEC,IAAAA,eAAU,EAACC,kCAAsB,EAAExC,KAAK;wBACtC,sCAAsC;wBACtCS,QAAQ;wBACRO;oBACF,IACFoB;YAGNX,eAAe5G;YAEf4E,QAAQD,GAAG,CAACiD,kBAAkB,GAAG5H,OAAO6H,YAAY,IAAI;YACxDV,8BAAgB,CAACnH,MAAM,GAAGA;YAE1B,IAAIqF,eAAe;YACnB,IAAIyC,IAAAA,6BAAqB,EAAC9H,SAAS;gBACjCqF,eAAerF,OAAOzD,OAAO;gBAC7ByD,OAAOzD,OAAO,GAAG;YACnB;YACA,MAAMA,UAAUE,aAAI,CAACC,IAAI,CAACyI,KAAKnF,OAAOzD,OAAO;YAC7C4K,8BAAgB,CAAC5K,OAAO,GAAGA;YAC3BwL,IAAAA,gBAAS,EAAC,SAASJ,kCAAsB;YACzCI,IAAAA,gBAAS,EAAC,WAAWxL;YAErB,MAAM+B,UAAU,MAAMyH,WACpBC,gBACAzJ,SACA2E,eACAlB;YAEFmH,8BAAgB,CAAC7I,OAAO,GAAGA;YAE3B,MAAM0J,eAA6B,MAAM9G,cACtCU,UAAU,CAAC,sBACXC,YAAY,CAAC,IAAMoG,IAAAA,yBAAgB,EAACjI;YAEvC,MAAM,EAAEkI,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGJ;YACzC,MAAMK,mBAA8B;mBAC/BF,SAASG,WAAW;mBACpBH,SAASI,UAAU;mBACnBJ,SAASK,QAAQ;aACrB;YACD,MAAMC,cAAcJ,iBAAiBK,MAAM,GAAG;YAC9CvB,8BAAgB,CAACsB,WAAW,GAAGA;YAC/BtB,8BAAgB,CAACwB,gBAAgB,GAAG3I,OAAO4I,iBAAiB;YAC5DzB,8BAAgB,CAAC0B,iBAAiB,GAAG7I,OAAO8I,kBAAkB;YAE9D,MAAMtM,WAAWF,YAAYC;YAE7B,MAAMwM,YAAY,IAAIC,kBAAS,CAAC;gBAAEzM;YAAQ;YAE1CwL,IAAAA,gBAAS,EAAC,aAAagB;YAEvB,MAAME,YAAYxM,aAAI,CAACC,IAAI,CAACyI,KAAK;YACjC,MAAM,EAAE+D,QAAQ,EAAEvH,MAAM,EAAE,GAAGwH,IAAAA,0BAAY,EAAChE;YAC1CgC,8BAAgB,CAAC+B,QAAQ,GAAGA;YAC5B/B,8BAAgB,CAACxF,MAAM,GAAGA;YAE1B,MAAMyD,qBAA6C;gBACjDgE,KAAK,OAAOzH,WAAW;gBACvBI,OAAO,OAAOmH,aAAa;YAC7B;YAEA,mDAAmD;YACnD,wFAAwF;YACxF,MAAMG,gBAAgB,MAAMC,IAAAA,kDAA2B,EAAC;gBACtDC,SAAS;gBACThN;YACF;YACA4K,8BAAgB,CAACkC,aAAa,GAAGA;YAEjC,MAAMG,WAAW/M,aAAI,CAClB+F,QAAQ,CAAC2C,KAAK+D,YAAYvH,UAAU,IACpC8H,UAAU,CAAC;YACd,MAAMC,eAAe3M,IAAAA,cAAU,EAACkM;YAEhCF,UAAUY,MAAM,CACdC,IAAAA,uBAAe,EAACzE,KAAKnF,QAAQ;gBAC3B6J,gBAAgB;gBAChBC,YAAY;gBACZN;gBACAO,YAAY,CAAC,CAAE,MAAMC,IAAAA,eAAM,EAAC,YAAY;oBAAEC,KAAK9E;gBAAI;gBACnD+E,gBAAgB;gBAChBC,WAAW;gBACXjB,UAAU,CAAC,CAACA;gBACZvH,QAAQ,CAAC,CAACA;YACZ;YAGFyI,IAAAA,wBAAgB,EAAC3N,aAAI,CAACqH,OAAO,CAACqB,MAAMkF,IAAI,CAAC,CAACC,SACxCvB,UAAUY,MAAM,CAACW;YAGnBC,IAAAA,2BAAe,EAAC9N,aAAI,CAACqH,OAAO,CAACqB,MAAMnF,QAAQqK,IAAI,CAAC,CAACC,SAC/CvB,UAAUY,MAAM,CAACW;YAGnB,qDAAqD;YACrD,MAAM,EAAEE,OAAO,EAAEC,oBAAoB,EAAE,GAAG,MAAMC,IAAAA,8BAAkB,EAChEvF,KACA;YAEFwF,IAAAA,wBAAY,EAAC;gBACXC,YAAY;gBACZC,QAAQ;gBACRL;gBACAC;YACF;YAEA,MAAMK,eAAeC,QAAQ/K,OAAOgL,MAAM,CAACC,kBAAkB;YAC7D,MAAMC,aAAa,CAACJ,gBAAgBzE;YAEpC,MAAM8E,sBAA+D;gBACnEhG;gBACAxD;gBACAuH;gBACA7C;gBACA6E;gBACAJ;gBACA/B;gBACA7H;gBACAlB;gBACAxD;YACF;YAEA,MAAM4O,iBAAiB,MAAMlK,cAC1BU,UAAU,CAAC,mBACXC,YAAY,CAAC;gBACZ,IAAI;oBACF,MAAMrE,YAAE,CAACiF,KAAK,CAAClG,SAAS;wBAAEoG,WAAW;oBAAK;oBAC1C,OAAO;gBACT,EAAE,OAAO0I,KAAK;oBACZ,IAAIC,IAAAA,gBAAO,EAACD,QAAQA,IAAIE,IAAI,KAAK,SAAS;wBACxC,OAAO;oBACT;oBACA,MAAMF;gBACR;YACF;YAEF,IAAI,CAACD,kBAAkB,CAAE,MAAMI,IAAAA,wBAAW,EAACjP,UAAW;gBACpD,MAAM,qBAEL,CAFK,IAAIkP,MACR,iGADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIzL,OAAO0L,YAAY,IAAI,CAAC1F,gBAAgB;gBAC1C,MAAM2F,IAAAA,gCAAe,EAACpP,SAAS;YACjC;YAEA,sEAAsE;YACtE,oEAAoE;YACpE,aAAa;YACb,IAAI,CAACoF,UAAU,CAACgF,eACd,MAAMiF,IAAAA,4BAAiB,EAACT;YAE1B,IAAIxJ,UAAU,mBAAmB3B,QAAQ;gBACvC9C,KAAI2O,KAAK,CACP;gBAEF,MAAM9C,UAAU+C,KAAK;gBACrBlH,QAAQmH,IAAI,CAAC;YACf;YAEA,MAAMC,iBAAyC;gBAC7CC,aAAa;gBACbC,iBAAiBhB,aAAa,IAAI;YACpC;YACAnC,UAAUY,MAAM,CAAC;gBACfwC,WAAWC,iCAAyB;gBACpCC,SAASL;YACX;YAEA,MAAMM,mBAAmBC,IAAAA,oCAAsB,EAC7CvM,OAAOwM,cAAc,EACrB7K;YAGF,MAAM8K,oBAA8BzO,KAAKC,KAAK,CAC5C2G,QAAQD,GAAG,CAAC+H,uBAAuB,IAAI;YAGzC,IAAIC,aAAa5B,QAAQnG,QAAQD,GAAG,CAAC+H,uBAAuB,IACxDD,oBACA,CAAClG,cAAc2C,WACb,MAAMhI,cAAcU,UAAU,CAAC,iBAAiBC,YAAY,CAAC,IAC3D+K,IAAAA,kCAAgB,EAAC1D,UAAU;oBACzB2D,gBAAgBP,iBAAiBQ,UAAU;gBAC7C,MAEF,EAAE;YAER,MAAMC,4BAA4B,IAAIC,OACpC,CAAC,CAAC,EAAEC,8BAAmB,CAAC,MAAM,EAAEjN,OAAOwM,cAAc,CAAC9P,IAAI,CAAC,KAAK,EAAE,CAAC;YAGrE,MAAMwQ,qCAAqC,IAAIF,OAC7C,CAAC,CAAC,EAAEG,wCAA6B,CAAC,MAAM,EAAEnN,OAAOwM,cAAc,CAAC9P,IAAI,CAClE,KACA,EAAE,CAAC;YAGP,MAAM0Q,UAAU3Q,aAAI,CAACC,IAAI,CAAEwM,YAAYvH,QAAU;YACjD,MAAMU,WAAW;gBACf0K;gBACAG;aACD;YAED,MAAMG,YAAYC,MAAMC,IAAI,CAAC,MAAMC,IAAAA,4BAAa,EAACJ,UAC9CvO,MAAM,CAAC,CAACmD,OAASK,SAASoL,IAAI,CAAC,CAACC,UAAYA,QAAQC,IAAI,CAAC3L,QACzD3C,IAAI,CAACuO,IAAAA,uBAAc,EAAC5N,OAAOwM,cAAc,GACzCzN,GAAG,CAAC,CAACiD,OAASvF,aAAI,CAACC,IAAI,CAAC0Q,SAASpL,MAAM6L,OAAO,CAAC1I,KAAK;YAEvD,MAAM3D,yBAAyB6L,UAAUI,IAAI,CAAC,CAACnN,IAC7CA,EAAE+B,QAAQ,CAAC8K,wCAA6B;YAE1C,MAAMW,oBAAoBT,UAAUI,IAAI,CAAC,CAACnN,IACxCA,EAAE+B,QAAQ,CAAC4K,8BAAmB;YAGhC9F,8BAAgB,CAAC3F,sBAAsB,GAAGA;YAE1C,MAAMuM,eAAkC;gBACtCC,eAAeC,eAAM,CAACC,WAAW,CAAC,IAAIC,QAAQ,CAAC;gBAC/CC,uBAAuBH,eAAM,CAACC,WAAW,CAAC,IAAIC,QAAQ,CAAC;gBACvDE,0BAA0BJ,eAAM,CAACC,WAAW,CAAC,IAAIC,QAAQ,CAAC;YAC5D;YACAhH,8BAAgB,CAAC4G,YAAY,GAAGA;YAEhC,MAAM3G,cAAc,MAAMlG,cACvBU,UAAU,CAAC,wBACXC,YAAY,CAAC,IACZyM,IAAAA,2BAAkB,EAAC;oBACjBC,OAAO;oBACP/B,gBAAgBxM,OAAOwM,cAAc;oBACrCgC,WAAWC,qBAAU,CAACC,KAAK;oBAC3BC,WAAWhC;oBACXzD;oBACAvH;gBACF;YAEJwF,8BAAgB,CAACC,WAAW,GAAGA;YAE/B,IAAIwH;YACJ,IAAIxN;YAEJ,IAAIO,QAAQ;gBACV,MAAMkN,mBAA6B7Q,KAAKC,KAAK,CAC3C2G,QAAQD,GAAG,CAACmK,sBAAsB,IAAI;gBAGxC,IAAIC,WAAWhE,QAAQnG,QAAQD,GAAG,CAACmK,sBAAsB,IACrDD,mBACA,MAAM3N,cACHU,UAAU,CAAC,qBACXC,YAAY,CAAC,IACZ+K,IAAAA,kCAAgB,EAACjL,QAAQ;wBACvBkL,gBAAgB,CAACmC,eACf1C,iBAAiB2C,eAAe,CAACD,iBACjC,8DAA8D;4BAC9D,gCAAgC;4BAChC1C,iBAAiB4C,cAAc,CAACF;wBAClCG,kBAAkB,CAACC,OAASA,KAAK3F,UAAU,CAAC;oBAC9C;gBAGRmF,iBAAiB,MAAM1N,cACpBU,UAAU,CAAC,sBACXC,YAAY,CAAC,IACZyM,IAAAA,2BAAkB,EAAC;wBACjBK,WAAWI;wBACXR,OAAO;wBACPC,WAAWC,qBAAU,CAACY,GAAG;wBACzB7C,gBAAgBxM,OAAOwM,cAAc;wBACrCtD;wBACAvH;oBACF;gBAGJwF,8BAAgB,CAACyH,cAAc,GAAGA;YACpC;YAEA,MAAMU,kBAAkB,MAAMhB,IAAAA,2BAAkB,EAAC;gBAC/CC,OAAO;gBACP/B,gBAAgBxM,OAAOwM,cAAc;gBACrCmC,WAAWtB;gBACXmB,WAAWC,qBAAU,CAACc,IAAI;gBAC1BrG,UAAUA;gBACVvH;YACF;YACAwF,8BAAgB,CAACmI,eAAe,GAAGA;YAEnC,MAAME,gBAAgB9Q,OAAOS,IAAI,CAACiI;YAElC,MAAMqI,0BAAiE,EAAE;YACzE,MAAMC,cAAc,IAAIjR;YACxB,IAAImQ,gBAAgB;gBAClBxN,uBAAuB1C,OAAOS,IAAI,CAACyP;gBACnC,KAAK,MAAMe,UAAUvO,qBAAsB;oBACzC,MAAMwO,uBAAuBC,IAAAA,0BAAgB,EAACF;oBAC9C,MAAMG,WAAW1I,WAAW,CAACwI,qBAAqB;oBAClD,IAAIE,UAAU;wBACZ,MAAMC,UAAUnB,cAAc,CAACe,OAAO;wBACtCF,wBAAwBnN,IAAI,CAAC;4BAC3BwN,SAASjC,OAAO,CAAC,uBAAuB;4BACxCkC,QAAQlC,OAAO,CAAC,yBAAyB;yBAC1C;oBACH;oBACA6B,YAAYM,GAAG,CAACJ;gBAClB;YACF;YAEA,MAAMb,WAAWzB,MAAMC,IAAI,CAACmC;YAC5B,2DAA2D;YAC3DvH,SAASG,WAAW,CAAChG,IAAI,IACpB2N,IAAAA,sEAAkC,EAAClB,UAAU/O,OAAOkQ,QAAQ;YAGjE/I,8BAAgB,CAACgB,QAAQ,GAAGA;YAE5B,MAAMgI,qBAAqBpB,SAASrG,MAAM;YAE1C,MAAMvH,WAAW;gBACfY,OAAOyN;gBACPpG,KAAK2F,SAASrG,MAAM,GAAG,IAAIqG,WAAWjI;YACxC;YAEA,6DAA6D;YAC7D,IAAI,CAACN,gBAAgB;gBACnB,MAAM4J,yBAAyBX,wBAAwB/G,MAAM;gBAC7D,IAAIkG,kBAAkBwB,yBAAyB,GAAG;oBAChDlT,KAAI2O,KAAK,CACP,CAAC,6BAA6B,EAC5BuE,2BAA2B,IAAI,SAAS,SACzC,wDAAwD,CAAC;oBAE5D,KAAK,MAAM,CAACN,UAAUC,QAAQ,IAAIN,wBAAyB;wBACzDvS,KAAI2O,KAAK,CAAC,CAAC,GAAG,EAAEiE,SAAS,KAAK,EAAEC,QAAQ,CAAC,CAAC;oBAC5C;oBACA,MAAMhH,UAAU+C,KAAK;oBACrBlH,QAAQmH,IAAI,CAAC;gBACf;YACF;YAEA,MAAMsE,yBAAmC,EAAE;YAC3C,MAAMC,eAAclJ,mBAAAA,WAAW,CAAC,OAAO,qBAAnBA,iBAAqBqC,UAAU,CAAC8G,0BAAe;YACnE,MAAMC,YAAY,CAAC,EAAC5B,kCAAAA,cAAgB,CAAC6B,4CAAgC,CAAC;YACtE,MAAMC,qBACJtJ,WAAW,CAAC,UAAU,CAACqC,UAAU,CAAC8G,0BAAe;YAEnD,IAAI7G,cAAc;gBAChB,MAAMiH,6BAA6B5T,IAAAA,cAAU,EAC3CN,aAAI,CAACC,IAAI,CAACuM,WAAW;gBAEvB,IAAI0H,4BAA4B;oBAC9B,MAAM,qBAAyC,CAAzC,IAAIlF,MAAMmF,yCAA8B,GAAxC,qBAAA;+BAAA;oCAAA;sCAAA;oBAAwC;gBAChD;YACF;YAEA,MAAM1P,cACHU,UAAU,CAAC,6BACXC,YAAY,CAAC;gBACZ,iDAAiD;gBACjD,sDAAsD;gBACtD,IAAK,MAAMjG,QAAQwL,YAAa;oBAC9B,MAAMyJ,oBAAoB,MAAMC,IAAAA,sBAAU,EACxCrU,aAAI,CAACC,IAAI,CAACuM,WAAWrN,SAAS,MAAM,WAAWA,OAC/CmV,oBAAQ,CAACC,IAAI;oBAEf,IAAIH,mBAAmB;wBACrBR,uBAAuB/N,IAAI,CAAC1G;oBAC9B;gBACF;gBAEA,MAAMqV,iBAAiBZ,uBAAuB3H,MAAM;gBAEpD,IAAIuI,gBAAgB;oBAClB,MAAM,qBAML,CANK,IAAIxF,MACR,CAAC,gCAAgC,EAC/BwF,mBAAmB,IAAI,SAAS,SACjC,uEAAuE,EAAEZ,uBAAuB3T,IAAI,CACnG,OACC,GALC,qBAAA;+BAAA;oCAAA;sCAAA;oBAMN;gBACF;YACF;YAEF,MAAMwU,sBAAsB/P,SAASY,KAAK,CAAClD,MAAM,CAAC,CAACjD;gBACjD,OACEA,KAAKuV,KAAK,CAAC,iCAAiC1U,aAAI,CAACiG,OAAO,CAAC9G,UAAU;YAEvE;YAEA,IAAIsV,oBAAoBxI,MAAM,EAAE;gBAC9BxL,KAAIE,IAAI,CACN,CAAC,4FAA4F,CAAC,GAC5F8T,oBAAoBxU,IAAI,CAAC,QACzB,CAAC,6EAA6E,CAAC;YAErF;YAEA,MAAM0U,0BAA0B;gBAAC;aAAS,CAACrS,GAAG,CAAC,CAACuB,IAC9CN,OAAOkQ,QAAQ,GAAG,GAAGlQ,OAAOkQ,QAAQ,GAAG5P,GAAG,GAAGA;YAG/C,MAAM+Q,wBAAwBtG,QAAQ/K,OAAOkD,YAAY,CAACoO,SAAS;YACnE,MAAMC,0BAA0BxG,QAC9B/K,OAAOkD,YAAY,CAACsO,cAAc;YAEpC,MAAMC,kBAAkBC,IAAAA,yBAAoB,EAAC1R,OAAOkD,YAAY,CAACyO,GAAG;YAEpE,MAAMC,qBAAqBnV,aAAI,CAACC,IAAI,CAACH,SAASsV,2BAAe;YAC7D,MAAMC,iBAAiC5Q,cACpCU,UAAU,CAAC,4BACXyF,OAAO,CAAC;gBACP,MAAM0K,eAAeC,IAAAA,sBAAe,EAAC;uBAChC7Q,SAASY,KAAK;uBACbZ,SAASiI,GAAG,IAAI,EAAE;iBACvB;gBACD,MAAMhK,gBAAuD,EAAE;gBAC/D,MAAM6S,eAAqC,EAAE;gBAE7C,KAAK,MAAMjT,SAAS+S,aAAc;oBAChC,IAAIG,IAAAA,qBAAc,EAAClT,QAAQ;wBACzBI,cAAckD,IAAI,CAAC3G,YAAYqD;oBACjC,OAAO,IAAI,CAACmT,IAAAA,sBAAc,EAACnT,QAAQ;wBACjCiT,aAAa3P,IAAI,CAAC3G,YAAYqD;oBAChC;gBACF;gBAEA,OAAO;oBACL+B,SAAS;oBACTqR,UAAU;oBACVC,eAAe,CAAC,CAACrS,OAAOkD,YAAY,CAACoP,mBAAmB;oBACxDpC,UAAUlQ,OAAOkQ,QAAQ;oBACzB9H,WAAWA,UAAUrJ,GAAG,CAAC,CAACwT,IACxBC,IAAAA,kCAAgB,EAAC,YAAYD,GAAGnB;oBAElClJ,SAASA,QAAQnJ,GAAG,CAAC,CAACwT,IAAMC,IAAAA,kCAAgB,EAAC,UAAUD;oBACvDnT;oBACA6S;oBACAQ,YAAY,EAAE;oBACdC,MAAM1S,OAAO0S,IAAI,IAAI5L;oBACrB6L,KAAK;wBACHC,QAAQC,4BAAU;wBAClB,yFAAyF;wBACzF,4DAA4D;wBAC5DC,YAAY,GAAGD,4BAAU,CAAC,EAAE,EAAEE,+CAA6B,CAAC,EAAE,EAAEC,6CAA2B,CAAC,EAAE,EAAEC,qDAAmC,EAAE;wBACrIC,gBAAgBF,6CAA2B;wBAC3CG,mBAAmBC,0CAAwB;wBAC3CC,mBAAmBC,yCAAuB;wBAC1CC,QAAQC,qBAAU;wBAClBC,gBAAgBC,8BAAmB;wBACnCC,uBAAuBV,qDAAmC;wBAC1DW,uBAAuBC,6BAAkB;wBACzCC,0BAA0BC,kCAAuB;oBACnD;oBACAC,gBAAgB;wBACdC,YAAYC,4CAA0B;wBACtCC,aAAaC,6CAA2B;oBAC1C;oBACAC,4BAA4BrU,OAAOqU,0BAA0B;oBAC7D1C,KAAKF,kBACD;wBACE6C,OAAO;4BACLpM,SAAS;gCACP,CAACqM,6BAAkB,CAAC,EAAE;4BACxB;wBACF;oBACF,IACAzN;gBACN;YACF;YAEF,IAAIqB,SAASG,WAAW,CAACI,MAAM,KAAK,KAAKP,SAASK,QAAQ,CAACE,MAAM,KAAK,GAAG;gBACvEoJ,eAAe3J,QAAQ,GAAGA,SAASI,UAAU,CAACxJ,GAAG,CAAC,CAACwT,IACjDC,IAAAA,kCAAgB,EAAC,WAAWD;YAEhC,OAAO;gBACLT,eAAe3J,QAAQ,GAAG;oBACxBG,aAAaH,SAASG,WAAW,CAACvJ,GAAG,CAAC,CAACwT,IACrCC,IAAAA,kCAAgB,EAAC,WAAWD;oBAE9BhK,YAAYJ,SAASI,UAAU,CAACxJ,GAAG,CAAC,CAACwT,IACnCC,IAAAA,kCAAgB,EAAC,WAAWD;oBAE9B/J,UAAUL,SAASK,QAAQ,CAACzJ,GAAG,CAAC,CAACwT,IAC/BC,IAAAA,kCAAgB,EAAC,WAAWD;gBAEhC;YACF;YACA,IAAIiC;YAIJ,IAAIxU,OAAOkD,YAAY,CAACuR,kBAAkB,EAAE;gBAC1C,MAAMC,uBAAuB,AAAC1U,CAAAA,OAAO8I,kBAAkB,IAAI,EAAE,AAAD,EAAGjK,MAAM,CACnE,CAAC0T,IAAW,CAACA,EAAEoC,QAAQ;gBAEzBH,sBAAsBI,IAAAA,kDAAwB,EAC5C;uBAAI7F;iBAAS,EACb/O,OAAOkD,YAAY,CAAC2R,2BAA2B,GAC3CH,uBACA,EAAE,EACN1U,OAAOkD,YAAY,CAAC4R,6BAA6B;gBAEnD3N,8BAAgB,CAACqN,mBAAmB,GAAGA;YACzC;YAEA,8EAA8E;YAC9E,uDAAuD;YACvD,MAAMnX,cACJZ,aAAI,CAACC,IAAI,CAACH,SAAS,iBACnB;YAGF,yFAAyF;YACzF,MAAMwY,IAAAA,wCAAsB,EAACnQ,QAAQD,GAAG,CAACuC,cAAc;YACvD,MAAM8N,IAAAA,wCAAsB,EAAC;gBAC3BC,YAAY;YACd;YAEA,MAAM5T,wBAAwBrB,OAAOqB,qBAAqB,IAAI8D;YAE9D,MAAM+P,oBAAoBzY,aAAI,CAACC,IAAI,CACjCH,SACAmD,4BAAgB,EAChByV,0BAAc;YAGhB,IAAIC;YACJ,IAAIC,qBAA+CvO;YAEnD,uEAAuE;YACvE,4CAA4C;YAC5C,MAAMwO,iBACJtV,OAAOkD,YAAY,CAACqS,kBAAkB,IACrCvV,OAAOkD,YAAY,CAACqS,kBAAkB,KAAKzO,aAC1C,CAAC9G,OAAOwV,OAAO;YACnB,MAAMC,6BACJzV,OAAOkD,YAAY,CAACwS,sBAAsB;YAC5C,MAAMC,qCACJ3V,OAAOkD,YAAY,CAAC0S,yBAAyB,IAC5C5V,OAAOkD,YAAY,CAAC0S,yBAAyB,KAAK9O,aACjDH;YAEJzF,cAAc2U,YAAY,CACxB,6BACA5O,OAAO,CAAC,CAACjH,OAAOwV,OAAO;YAEzBtU,cAAc2U,YAAY,CAAC,oBAAoB5O,OAAOqO;YAEtD,IACE,CAACA,kBACAG,CAAAA,8BAA8BE,kCAAiC,GAChE;gBACA,MAAM,qBAEL,CAFK,IAAIlK,MACR,oMADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAvO,KAAI4Y,IAAI,CAAC;YACTC,IAAAA,wBAAgB,EAAC,kBAAkB7U;YAEnC,MAAM8T,IAAAA,wCAAsB,EAAC;gBAC3BC,YAAY;gBACZe,cAAc;oBACZV,gBAAgBrO,OAAOqO;gBACzB;YACF;YAEA,IAAIW,kBAAkBC,QAAQpS,OAAO;YACrC,IAAI,CAACkC,gBAAgB;gBACnB,IAAIQ,gBAAgB;oBAClB,MAAM,EACJ2P,UAAUC,gBAAgB,EAC1BH,iBAAiB3V,CAAC,EAClB,GAAG+V,MACJ,GAAG,MAAMC,IAAAA,8BAAc,EACtB1R,QAAQD,GAAG,CAAC4R,yBAAyB,KAAKzP,aACxClC,QAAQD,GAAG,CAAC4R,yBAAyB,KAAK;oBAE9CN,kBAAkB3V;oBAClByV,IAAAA,wBAAgB,EAAC,kBAAkB7U;oBAEnCkU,oBAAoBiB,KAAKjB,iBAAiB;oBAE1C,IAAIoB;oBACJ,IAAIJ,mBAAmB,KAAK;wBAC1BI,iBAAiB,GAAGlT,KAAKmT,KAAK,CAACL,mBAAmB,KAAK,GAAG,GAAG,CAAC;oBAChE,OAAO,IAAIA,mBAAmB,IAAI;wBAChCI,iBAAiB,GAAGlT,KAAKmT,KAAK,CAACL,kBAAkB,CAAC,CAAC;oBACrD,OAAO,IAAIA,mBAAmB,GAAG;wBAC/BI,iBAAiB,GAAGlT,KAAKmT,KAAK,CAACL,mBAAmB,MAAM,GAAG,CAAC,CAAC;oBAC/D,OAAO;wBACLI,iBAAiB,GAAGlT,KAAKmT,KAAK,CAACL,mBAAmB,MAAM,EAAE,CAAC;oBAC7D;oBACAlZ,KAAIwZ,KAAK,CAAC,CAAC,yBAAyB,EAAEF,gBAAgB;oBAEtDzN,UAAUY,MAAM,CACdgN,IAAAA,2BAAmB,EAAChK,YAAY;wBAC9BiK,mBAAmBtT,KAAKmT,KAAK,CAACL;wBAC9BjG;oBACF;gBAEJ,OAAO;oBACL,IACEsF,8BACAE,oCACA;wBACA,IAAIiB,oBAAoB;wBAExB,MAAM5B,IAAAA,wCAAsB,EAAC;4BAC3BC,YAAY;wBACd;wBAEA,MAAM4B,qBAAqBC,IAAAA,0BAAY,EAACxB,gBAAgB;4BACtD;yBACD,EAAEjL,IAAI,CAAC,CAAC0M;4BACPhB,IAAAA,wBAAgB,EAAC,+BAA+B7U;4BAChDkU,oBAAoB2B,IAAI3B,iBAAiB;4BACzCwB,qBAAqBG,IAAIZ,QAAQ;4BAEjC,IAAIR,oCAAoC;gCACtC,MAAMqB,mBAAmB,IAAI7S,cAAM,CACjCN,QAAQC,OAAO,CAAC,2BAChB;oCACEO,YAAY;oCACZY,gBAAgB;wCAAC;qCAAqB;gCACxC;gCAGFoQ,qBAAqB2B,iBAClBC,kBAAkB,CAAC;oCAClB9R;oCACAnF;oCACAzD;oCACA,+CAA+C;oCAC/C2a,mBAAmBC,IAAAA,qCAA6B,EAAC,IAAIC;oCACrD3V,aAAa,EAAE;oCACf4V,gBAAgB;oCAChBjC;oCACA/T;gCACF,GACCiW,KAAK,CAAC,CAACjM;oCACNrO,QAAQ6O,KAAK,CAACR;oCACdzG,QAAQmH,IAAI,CAAC;gCACf;4BACJ;wBACF;wBACA,IAAI,CAAC0J,4BAA4B;4BAC/B,MAAMoB;4BACN,MAAM7B,IAAAA,wCAAsB,EAAC;gCAC3BC,YAAY;4BACd;wBACF;wBAEA,MAAMsC,mBAAmBT,IAAAA,0BAAY,EAACxB,gBAAgB;4BACpD;yBACD,EAAEjL,IAAI,CAAC,CAAC0M;4BACPH,qBAAqBG,IAAIZ,QAAQ;4BACjCJ,IAAAA,wBAAgB,EACd,oCACA7U;wBAEJ;wBACA,IAAIuU,4BAA4B;4BAC9B,MAAMoB;4BACN,MAAM7B,IAAAA,wCAAsB,EAAC;gCAC3BC,YAAY;4BACd;wBACF;wBACA,MAAMsC;wBAEN,MAAMvC,IAAAA,wCAAsB,EAAC;4BAC3BC,YAAY;wBACd;wBAEA,MAAM6B,IAAAA,0BAAY,EAACxB,gBAAgB;4BAAC;yBAAS,EAAEjL,IAAI,CAAC,CAAC0M;4BACnDH,qBAAqBG,IAAIZ,QAAQ;4BACjCJ,IAAAA,wBAAgB,EAAC,+BAA+B7U;wBAClD;wBAEAhE,KAAIwZ,KAAK,CAAC;wBAEV3N,UAAUY,MAAM,CACdgN,IAAAA,2BAAmB,EAAChK,YAAY;4BAC9BiK;4BACAzG;wBACF;oBAEJ,OAAO;wBACL,MAAM,EAAEgG,UAAUC,gBAAgB,EAAE,GAAGC,MAAM,GAAG,MAAMS,IAAAA,0BAAY,EAChExB,gBACA;wBAEFS,IAAAA,wBAAgB,EAAC,kBAAkB7U;wBAEnCkU,oBAAoBiB,KAAKjB,iBAAiB;wBAE1CrM,UAAUY,MAAM,CACdgN,IAAAA,2BAAmB,EAAChK,YAAY;4BAC9BiK,mBAAmBR;4BACnBjG;wBACF;oBAEJ;gBACF;YACF;YAEA,uDAAuD;YACvD,IAAIxO,UAAU,CAACgF,iBAAiB,CAACX,gBAAgB;gBAC/C,MAAMgP,IAAAA,wCAAsB,EAAC;oBAC3BC,YAAY;gBACd;gBACA,MAAMrJ,IAAAA,4BAAiB,EAACT;gBACxB4K,IAAAA,wBAAgB,EAAC,0BAA0B7U;YAC7C;YAEA,MAAMsW,qBAAqBC,IAAAA,gBAAa,EAAC;YAEzC,MAAMC,oBAAoBjb,aAAI,CAACC,IAAI,CAACH,SAASob,0BAAc;YAC3D,MAAMC,uBAAuBnb,aAAI,CAACC,IAAI,CAACH,SAASsb,8BAAkB;YAElE,IAAIC,sBAAsB;YAC1B,IAAIC,sBAAsB;YAC1B,IAAIC,sBAAsB;YAC1B,IAAIC,wBAAwB;YAC5B,MAAMzZ,WAAW,IAAIC;YACrB,MAAMyZ,yBAAyB,IAAIzZ;YACnC,MAAM0Z,2BAA2B,IAAI1Z;YACrC,MAAMgD,cAAc,IAAIhD;YACxB,MAAM2Z,eAAe,IAAI3Z;YACzB,MAAM4Z,iBAAiB,IAAI5Z;YAC3B,MAAM6Z,mBAAmB,IAAI7Z;YAC7B,MAAM8Z,kBAAkB,IAAInB;YAC5B,MAAMoB,cAAc,IAAIpB;YACxB,MAAMqB,qBAAqB,IAAIrB;YAI/B,MAAMsB,qBAAqB,IAAItB;YAC/B,MAAMuB,gBAAgB,IAAIvB;YAC1B,MAAMwB,oBAAoB,IAAIxB;YAC9B,MAAMyB,YAAuB,IAAIzB;YACjC,IAAI0B,gBAAgB,MAAM/a,aAA4BmX;YACtD,MAAM6D,gBAAgB,MAAMhb,aAA4B2Z;YACxD,MAAMsB,mBAAmBrX,SACrB,MAAM5D,aAA+B6Z,wBACrC9Q;YAEJ,MAAMmS,gBAAwC,CAAC;YAE/C,IAAItX,QAAQ;gBACV,MAAMuX,mBAAmB,MAAMnb,aAC7BtB,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAEyZ,8BAAkB;gBAGzD,IAAK,MAAMC,OAAOF,iBAAkB;oBAClCD,aAAa,CAACG,IAAI,GAAGvJ,IAAAA,0BAAgB,EAACuJ;gBACxC;gBAEA,MAAMxb,cACJnB,aAAI,CAACC,IAAI,CAACH,SAAS8c,oCAAwB,GAC3CJ;YAEJ;YAEArU,QAAQD,GAAG,CAAC2U,UAAU,GAAG3R,kCAAsB;YAE/C,MAAM4R,SAASpe,mBAAmB6E;YAElC,MAAMwZ,gBAAgB5U,QAAQ6U,MAAM;YACpC,MAAMC,kBAAkBxY,cAAcU,UAAU,CAAC;YAEjD,MAAM+X,0BAAmD;gBACvD5Y,SAAS;gBACT6Y,WAAW,CAAC;YACd;YAEA,MAAM,EACJC,wBAAwB,EACxBC,YAAY,EACZC,mBAAmB,EACnB1C,cAAc,EACd2C,qBAAqB,EACtB,GAAG,MAAMN,gBAAgB7X,YAAY,CAAC;oBAcV7B;gBAb3B,IAAI2G,eAAe;oBACjB,OAAO;wBACLkT,0BAA0B;wBAC1BC,cAAc,EAAE;wBAChBC,qBAAqB;wBACrB1C,gBAAgB,CAAC,CAACnO;wBAClB8Q,uBAAuB;oBACzB;gBACF;gBAEA,MAAM,EAAEC,cAAc,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAE,GAChEna;gBACF,MAAMoa,mBAAmB;oBAAEF;oBAAqBC;gBAAoB;gBACpE,MAAME,aAAatP,SAAQ/K,2BAAAA,OAAOkD,YAAY,CAACoX,GAAG,qBAAvBta,yBAAyBua,SAAS;gBAE7D,MAAMC,yBAAyBd,gBAAgB9X,UAAU,CACvD;gBAEF,MAAM6Y,oCACJD,uBAAuB3Y,YAAY,CACjC,UACE6O,sBACC,MAAM6I,OAAOmB,wBAAwB,CAAC;wBACrC9e,MAAM;wBACNW;wBACA6d;wBACAO,aAAa;wBACbN;oBACF;gBAGN,MAAMO,wBAAwBJ,uBAAuB3Y,YAAY,CAC/D;wBAWa7B,cACMA;2BAXjB0Q,sBACA6I,OAAOsB,YAAY,CAAC;wBAClB1V;wBACAvJ,MAAM;wBACNW;wBACA0d;wBACAG;wBACA9I,WAAWD;wBACXG,gBAAgBD;wBAChBuJ,kBAAkB9a,OAAO8a,gBAAgB;wBACzCvc,OAAO,GAAEyB,eAAAA,OAAO0S,IAAI,qBAAX1S,aAAazB,OAAO;wBAC7Bwc,aAAa,GAAE/a,gBAAAA,OAAO0S,IAAI,qBAAX1S,cAAa+a,aAAa;wBACzCC,kBAAkBhb,OAAOib,MAAM;wBAC/BC,WAAWlb,OAAOkD,YAAY,CAACyO,GAAG;wBAClCwJ,mBAAmBnb,OAAOkD,YAAY,CAACkY,SAAS;wBAChD9c;wBACA+b;oBACF;;gBAGJ,MAAMgB,iBAAiB;gBAEvB,MAAMC,kCAAkC/B,OAAOmB,wBAAwB,CACrE;oBACE9e,MAAMyf;oBACN9e;oBACA6d;oBACAO,aAAa;oBACbN;gBACF;gBAGF,MAAMkB,sBAAsBhC,OAAOiC,sBAAsB,CAAC;oBACxD5f,MAAMyf;oBACN9e;oBACA6d;oBACAC;gBACF;gBAEA,wDAAwD;gBACxD,IAAIN;gBACJ,wDAAwD;gBACxD,IAAI1C,iBAAiB;gBAErB,MAAMoE,uBAAuB,MAAMC,IAAAA,2BAAmB,EACpD;oBAAEtgB,OAAO2d;oBAAe3P,KAAK4P;gBAAiB,GAC9Czc,SACAyD,OAAOkD,YAAY,CAACyY,QAAQ;gBAG9B,MAAMra,qBAAyCuC,QAC7CpH,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAEkc,+BAAmB;gBAG1D,MAAMC,iBAAiBla,SAClBkC,QACCpH,aAAI,CAACC,IAAI,CACPH,SACAmD,4BAAgB,EAChBoc,qCAAyB,GAAG,YAGhC;gBACJ,MAAMC,oBAAoBF,iBAAiB,IAAIpd,QAAQ;gBACvD,IAAIod,kBAAkBE,mBAAmB;oBACvC,IAAK,MAAMC,MAAMH,eAAeI,IAAI,CAAE;wBACpC,IAAK,MAAMC,SAASL,eAAeI,IAAI,CAACD,GAAG,CAACG,OAAO,CAAE;4BACnDJ,kBAAkB/L,GAAG,CAACkM;wBACxB;oBACF;oBACA,IAAK,MAAMF,MAAMH,eAAeO,IAAI,CAAE;wBACpC,IAAK,MAAMF,SAASL,eAAeO,IAAI,CAACJ,GAAG,CAACG,OAAO,CAAE;4BACnDJ,kBAAkB/L,GAAG,CAACkM;wBACxB;oBACF;gBACF;gBAEA,KAAK,MAAM9C,OAAO1a,OAAOS,IAAI,CAACmC,sCAAAA,mBAAoBsY,SAAS,EAAG;oBAC5D,IAAIR,IAAI3P,UAAU,CAAC,SAAS;wBAC1BwO;oBACF;gBACF;gBAEA,MAAM/B,QAAQmG,GAAG,CACf3d,OAAOC,OAAO,CAACwC,UACZe,MAAM,CACL,CAACC,KAAK,CAACiX,KAAKnX,MAAM;oBAChB,IAAI,CAACA,OAAO;wBACV,OAAOE;oBACT;oBAEA,MAAMma,WAAWlD;oBAEjB,KAAK,MAAMxd,QAAQqG,MAAO;wBACxBE,IAAIG,IAAI,CAAC;4BAAEga;4BAAU1gB;wBAAK;oBAC5B;oBAEA,OAAOuG;gBACT,GACA,EAAE,EAEHpD,GAAG,CAAC,CAAC,EAAEud,QAAQ,EAAE1gB,IAAI,EAAE;oBACtB,MAAM2gB,gBAAgB7C,gBAAgB9X,UAAU,CAAC,cAAc;wBAC7DhG;oBACF;oBACA,OAAO2gB,cAAc1a,YAAY,CAAC;wBAChC,MAAM2a,aAAaC,IAAAA,oCAAiB,EAAC7gB;wBACrC,MAAM,CAAC8gB,MAAMC,UAAU,GAAG,MAAMC,IAAAA,yBAAiB,EAC/CN,UACAE,YACAjgB,SACAwc,eACAC,kBACAhZ,OAAOkD,YAAY,CAACyY,QAAQ,EAC5BF;wBAGF,IAAIoB,oBAAoB;wBACxB,IAAIC,QAAQ;wBACZ,IAAIC,WAAW;wBACf,IAAIC,oBAAoB;wBACxB,IAAIC,cAAc;wBAClB,IAAIC,gBAAiC;wBACrC,IAAIpN,WAAW;wBAEf,IAAIwM,aAAa,SAAS;4BACxBxM,WACEnD,WAAWwQ,IAAI,CAAC,CAAC7c;gCACfA,IAAI8c,IAAAA,kCAAgB,EAAC9c;gCACrB,OACEA,EAAEmJ,UAAU,CAAC+S,aAAa,QAC1Blc,EAAEmJ,UAAU,CAAC+S,aAAa;4BAE9B,MAAM;wBACV;wBACA,IAAIa;wBAEJ,IAAIf,aAAa,SAAS1N,gBAAgB;4BACxC,KAAK,MAAM,CAAC0O,cAAcC,eAAe,IAAI7e,OAAOC,OAAO,CACzDsa,eACC;gCACD,IAAIsE,mBAAmB3hB,MAAM;oCAC3BkU,WAAWlB,cAAc,CAAC0O,aAAa,CAACzP,OAAO,CAC7C,yBACA;oCAEFwP,kBAAkBC;oCAClB;gCACF;4BACF;wBACF;wBAEA,MAAME,eAAeC,IAAAA,gCAAwB,EAAC3N,YAC1CjM,QAAQC,OAAO,CACb,iDAEFrH,aAAI,CAACC,IAAI,CACP,AAAC4f,CAAAA,aAAa,UAAUpT,WAAWvH,MAAK,KAAM,IAC9CmO;wBAGN,MAAM4N,iBAAiBpB,aAAa;wBACpC,MAAMqB,aAAa7N,WACf,MAAM8N,IAAAA,sCAA6B,EAAC;4BAClCF;4BACAF;4BACAhR,gBAAgBxM,OAAOwM,cAAc;4BACrC7K;4BACA3B;4BACAuO,OAAO;4BACP,yDAAyD;4BACzD,4DAA4D;4BAC5D,gEAAgE;4BAChE3S,MAAM8hB,iBAAiBL,kBAAmBzhB;wBAC5C,KACAkL;wBAEJ,8DAA8D;wBAC9D,oDAAoD;wBACpD,IACE,QAAO6W,8BAAAA,WAAYE,OAAO,MAAK,eAC/B,QAAOF,8BAAAA,WAAYG,WAAW,MAAK,aACnC;4BACAnE,wBAAwBC,SAAS,CAAChe,KAAK,GAAG;gCACxCkiB,WAAW,EAAEH,8BAAAA,WAAYG,WAAW;4BACtC;wBACF;wBAEA,MAAMC,cAAczc,mBAAmBsY,SAAS,CAC9CyD,mBAAmBzhB,KACpB,GACG,SACA+hB,8BAAAA,WAAYE,OAAO;wBAEvB,IAAI,CAAClX,eAAe;4BAClBqW,oBACEV,aAAa,SACbqB,CAAAA,8BAAAA,WAAYhL,GAAG,MAAKqL,4BAAgB,CAACC,MAAM;4BAE7C,IAAI3B,aAAa,SAAS,CAACnK,IAAAA,sBAAc,EAACvW,OAAO;gCAC/C,IAAI;oCACF,IAAIsiB;oCAEJ,IAAIC,IAAAA,4BAAa,EAACJ,cAAc;wCAC9B,IAAIzB,aAAa,OAAO;4CACtBtE;wCACF,OAAO;4CACLC;wCACF;wCAEA,MAAMmG,cACJ9B,aAAa,UAAU1gB,OAAOyhB,mBAAmB;wCAEnDa,WAAW5c,mBAAmBsY,SAAS,CAACwE,YAAY;oCACtD;oCAEA,IAAIC,mBACF9B,cAAc3a,UAAU,CAAC;oCAC3B,IAAI0c,eAAe,MAAMD,iBAAiBxc,YAAY,CACpD;4CASa7B,cACMA;wCATjB,OAAOuZ,OAAOsB,YAAY,CAAC;4CACzB1V;4CACAvJ;4CACAyhB;4CACA9gB;4CACA0d;4CACAG;4CACAU,kBAAkB9a,OAAO8a,gBAAgB;4CACzCvc,OAAO,GAAEyB,eAAAA,OAAO0S,IAAI,qBAAX1S,aAAazB,OAAO;4CAC7Bwc,aAAa,GAAE/a,gBAAAA,OAAO0S,IAAI,qBAAX1S,cAAa+a,aAAa;4CACzCwD,UAAUF,iBAAiBG,KAAK;4CAChCT;4CACAG;4CACA5B;4CACAhL,WAAWD;4CACXG,gBAAgBD;4CAChBkN,cAAcze,OAAOye,YAAY;4CACjCC,eAAe1e,OAAOkD,YAAY,CAACwb,aAAa;4CAChDC,gBAAgBhiB,QAAcE,cAAc,GACxC,QACAmD,OAAOkD,YAAY,CAACyb,cAAc;4CACtCC,oBAAoB5e,OAAO6e,kBAAkB;4CAC7C7D,kBAAkBhb,OAAOib,MAAM;4CAC/BC,WAAWlb,OAAOkD,YAAY,CAACyO,GAAG;4CAClCwJ,mBAAmBnb,OAAOkD,YAAY,CAACkY,SAAS;4CAChD9c;4CACA+b;wCACF;oCACF;oCAGF,IAAIiC,aAAa,SAASe,iBAAiB;wCACzC3E,mBAAmBoG,GAAG,CAACzB,iBAAiBzhB;wCACxC,0CAA0C;wCAC1C,IAAIuiB,IAAAA,4BAAa,EAACJ,cAAc;4CAC9BhB,WAAW;4CACXD,QAAQ;4CAER5f,KAAI6hB,QAAQ,CACV,CAAC,+EAA+E,CAAC;wCAErF,OAAO;4CACL,MAAMC,YAAY9M,IAAAA,qBAAc,EAACtW;4CAEjC,IACE,OAAO0iB,aAAazB,iBAAiB,KAAK,WAC1C;gDACAA,oBAAoByB,aAAazB,iBAAiB;4CACpD;4CAEA,oDAAoD;4CACpD,0CAA0C;4CAC1C,yBAAyB;4CACzB,IAAIyB,aAAazB,iBAAiB,EAAE;gDAClCC,QAAQ;gDACRC,WAAW;gDAEXvE,YAAYsG,GAAG,CAACzB,iBAAiB,EAAE;4CACrC,OAOK,IAAIrd,OAAOkD,YAAY,CAACoO,SAAS,IAAI0N,WAAW;gDACnDvG,mBAAmBqG,GAAG,CAACzB,iBAAiB;oDACtCzhB;oDACAyhB;gDACF;4CACF;4CAEA,IAAIiB,aAAaW,iBAAiB,EAAE;gDAClCzG,YAAYsG,GAAG,CACbzB,iBACAiB,aAAaW,iBAAiB;gDAEhC/B,gBAAgBoB,aAAaW,iBAAiB,CAAClgB,GAAG,CAChD,CAACC,QAAUA,MAAME,QAAQ;gDAE3B4d,QAAQ;4CACV;4CAEA,MAAMoC,YAAYZ,aAAaY,SAAS,IAAI,CAAC;4CAC7C,IAAIA,UAAUC,UAAU,KAAK,GAAG;gDAC9B,MAAMC,0BACJd,aAAaW,iBAAiB,IAC9BX,aAAaW,iBAAiB,CAACvW,MAAM,GAAG;gDAE1C,IACE1I,OAAOib,MAAM,KAAK,YAClB+D,aACA,CAACI,yBACD;oDACA,MAAM,qBAEL,CAFK,IAAI3T,MACR,CAAC,MAAM,EAAE7P,KAAK,wFAAwF,CAAC,GADnG,qBAAA;+DAAA;oEAAA;sEAAA;oDAEN;gDACF;gDAEA,6BAA6B;gDAC7B,4BAA4B;gDAC5B,iEAAiE;gDACjE,8BAA8B;gDAC9B,IAAI,CAACojB,WAAW;oDACdxG,YAAYsG,GAAG,CAACzB,iBAAiB;wDAC/B;4DACEne,UAAUtD;4DACVyjB,iBAAiBzjB;4DACjB0jB,qBAAqBxY;4DACrByY,cACEjB,aAAakB,qBAAqB;4DACpCC,oBAAoB3Y;wDACtB;qDACD;oDACDiW,WAAW;gDACb,OAAO,IACL,CAACqC,2BACAF,CAAAA,UAAUQ,OAAO,KAAK,WACrBR,UAAUQ,OAAO,KAAK,cAAa,GACrC;oDACAlH,YAAYsG,GAAG,CAACzB,iBAAiB,EAAE;oDACnCN,WAAW;oDACXF,oBAAoB;gDACtB;4CACF;4CAEA,IAAIyB,aAAakB,qBAAqB,EAAE;gDACtC7G,cAAcmG,GAAG,CACfzB,iBACAiB,aAAakB,qBAAqB;4CAEtC;4CAEA5G,kBAAkBkG,GAAG,CAACzB,iBAAiB6B;wCACzC;oCACF,OAAO;wCACL,IAAIf,IAAAA,4BAAa,EAACJ,cAAc;4CAC9B,IAAIO,aAAaqB,cAAc,EAAE;gDAC/B3iB,QAAQI,IAAI,CACV,CAAC,kFAAkF,EAAExB,MAAM;4CAE/F;4CACA,mDAAmD;4CACnD,8CAA8C;4CAC9C0iB,aAAavB,QAAQ,GAAG;4CACxBuB,aAAaqB,cAAc,GAAG;wCAChC;wCAEA,IACErB,aAAavB,QAAQ,KAAK,SACzBuB,CAAAA,aAAarB,WAAW,IAAIqB,aAAasB,SAAS,AAAD,GAClD;4CACAvI,iBAAiB;wCACnB;wCAEA,IAAIiH,aAAarB,WAAW,EAAE;4CAC5BA,cAAc;4CACd5E,eAAerI,GAAG,CAACpU;wCACrB;wCAEA,IAAI0iB,aAAavE,mBAAmB,EAAE;4CACpCA,sBAAsB;wCACxB;wCAEA,IAAIuE,aAAaqB,cAAc,EAAE;4CAC/BnhB,SAASwR,GAAG,CAACpU;4CACbkhB,QAAQ;4CAER,IACEwB,aAAaW,iBAAiB,IAC9BX,aAAaW,iBAAiB,CAACvW,MAAM,GAAG,GACxC;gDACA6P,gBAAgBuG,GAAG,CACjBljB,MACA0iB,aAAaW,iBAAiB;gDAEhC/B,gBAAgBoB,aAAaW,iBAAiB,CAAClgB,GAAG,CAChD,CAACC,QAAUA,MAAME,QAAQ;4CAE7B;4CAEA,IACEof,aAAakB,qBAAqB,KAClCK,sBAAY,CAACC,sBAAsB,EACnC;gDACA3H,yBAAyBnI,GAAG,CAACpU;4CAC/B,OAAO,IACL0iB,aAAakB,qBAAqB,KAClCK,sBAAY,CAACE,SAAS,EACtB;gDACA7H,uBAAuBlI,GAAG,CAACpU;4CAC7B;wCACF,OAAO,IAAI0iB,aAAa0B,cAAc,EAAE;4CACtC1H,iBAAiBtI,GAAG,CAACpU;wCACvB,OAAO,IACL0iB,aAAavB,QAAQ,IACrB,CAACC,qBACD,AAAC,MAAM1B,oCAAqC,OAC5C;4CACA7Z,YAAYuO,GAAG,CAACpU;4CAChBmhB,WAAW;wCACb,OAAO,IAAIC,mBAAmB;4CAC5B,2DAA2D;4CAC3D,gDAAgD;4CAChDxe,SAASwR,GAAG,CAACpU;4CACbkhB,QAAQ;wCACV;wCAEA,IAAIxM,eAAe1U,SAAS,QAAQ;4CAClC,IACE,CAAC0iB,aAAavB,QAAQ,IACtB,CAACuB,aAAaqB,cAAc,EAC5B;gDACA,MAAM,qBAEL,CAFK,IAAIlU,MACR,CAAC,cAAc,EAAEwU,qDAA0C,EAAE,GADzD,qBAAA;2DAAA;gEAAA;kEAAA;gDAEN;4CACF;4CACA,2DAA2D;4CAC3D,mCAAmC;4CACnC,IACE,AAAC,MAAM3E,mCACP,CAACgD,aAAaqB,cAAc,EAC5B;gDACAle,YAAYye,MAAM,CAACtkB;4CACrB;wCACF;wCAEA,IACEukB,+BAAmB,CAAC9d,QAAQ,CAACzG,SAC7B,CAAC0iB,aAAavB,QAAQ,IACtB,CAACuB,aAAaqB,cAAc,EAC5B;4CACA,MAAM,qBAEL,CAFK,IAAIlU,MACR,CAAC,OAAO,EAAE7P,KAAK,GAAG,EAAEqkB,qDAA0C,EAAE,GAD5D,qBAAA;uDAAA;4DAAA;8DAAA;4CAEN;wCACF;oCACF;gCACF,EAAE,OAAO5U,KAAK;oCACZ,IACE,CAACC,IAAAA,gBAAO,EAACD,QACTA,IAAI+U,OAAO,KAAK,0BAEhB,MAAM/U;oCACR+M,aAAapI,GAAG,CAACpU;gCACnB;4BACF;4BAEA,IAAI0gB,aAAa,OAAO;gCACtB,IAAIQ,SAASC,UAAU;oCACrBjF;gCACF,OAAO;oCACLC;gCACF;4BACF;wBACF;wBAEAc,UAAUiG,GAAG,CAACljB,MAAM;4BAClB8gB;4BACAC;4BACAI;4BACAD;4BACAD;4BACAI;4BACAC;4BACAmD,0BAA0B;4BAC1BxC,SAASE;4BACTuC,cAAcxZ;4BACdyZ,kBAAkBzZ;4BAClB0Z,iBAAiB1Z;wBACnB;oBACF;gBACF;gBAGJ,IAAI2Z,sCAAmB,EAAE;oBACvBvjB,KAAI2O,KAAK,CACP,CAAC,0IAA0I,CAAC;oBAE9IjH,QAAQmH,IAAI,CAAC;gBACf;gBAEA,MAAM2U,kBAAkB,MAAM9F;gBAC9B,MAAM+F,qBACJ,AAAC,MAAMlG,qCACNiG,mBAAmBA,gBAAgBV,cAAc;gBAEpD,MAAMY,cAAc;oBAClB/G,0BAA0B,MAAMyB;oBAChCxB,cAAc,MAAMyB;oBACpBxB;oBACA1C;oBACA2C,uBAAuB2G;gBACzB;gBAEA,OAAOC;YACT;YAEA,IAAIpJ,oBAAoBA,mBAAmBqJ,cAAc;YACzD9K,IAAAA,wBAAgB,EAAC,iCAAiC7U;YAElD,IAAI2Y,0BAA0B;gBAC5B7c,QAAQI,IAAI,CACV0jB,IAAAA,gBAAI,EAACC,IAAAA,kBAAM,EAAC,CAAC,SAAS,CAAC,KACrBA,IAAAA,kBAAM,EACJ,CAAC,qJAAqJ,CAAC;gBAG7J/jB,QAAQI,IAAI,CACV;YAEJ;YAEA,MAAM,EAAEqhB,YAAY,EAAE,GAAGze;YAEzB,MAAMghB,gCAA0C,EAAE;YAClD,IAAIxf,wBAAwB;gBAC1Bwf,8BAA8B1e,IAAI,CAChC7F,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAE,GAAGyN,wCAA6B,CAAC,GAAG,CAAC;gBAEnE,+DAA+D;gBAC/D,8FAA8F;gBAC9F,IACE,CAACvI,QAAQD,GAAG,CAACsc,SAAS,IACrBjJ,CAAAA,uBAAuBC,qBAAoB,GAC5C;oBACA+I,8BAA8B1e,IAAI,CAChC7F,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChB,CAAC,KAAK,EAAEyN,wCAA6B,CAAC,GAAG,CAAC;gBAGhD;YACF;YAEA,MAAM+T,8BAA8BhgB,cACjCU,UAAU,CAAC,kCACXyF,OAAO,CAAC;gBACP,MAAM8Z,0BAAkD,CAAC;gBAEzD,KAAK,MAAM,CAAC/H,KAAKgI,MAAM,IAAI1iB,OAAOC,OAAO,CACvCqB,OAAOkD,YAAY,CAACwb,aAAa,IAAI,CAAC,GACrC;oBACD,IAAItF,OAAOgI,OAAO;wBAChBD,uBAAuB,CAAC/H,IAAI,GAAG3c,aAAI,CAAC+F,QAAQ,CAACjG,SAAS6kB;oBACxD;gBACF;gBAEA,MAAMC,sBAAmD;oBACvDtgB,SAAS;oBACTf,QAAQ;wBACN,GAAGA,MAAM;wBACTshB,YAAYxa;wBACZ,GAAInK,QAAcE,cAAc,GAC5B;4BACE0kB,UAAU;wBACZ,IACA,CAAC,CAAC;wBACN9C,cAAcA,eACVhiB,aAAI,CAAC+F,QAAQ,CAACjG,SAASkiB,gBACvBze,OAAOye,YAAY;wBACvBvb,cAAc;4BACZ,GAAGlD,OAAOkD,YAAY;4BACtBwb,eAAeyC;4BACfK,iBAAiB7kB,QAAcE,cAAc;4BAE7C,oGAAoG;4BACpG4kB,uBAAuB9a;wBACzB;oBACF;oBACAhF,QAAQwD;oBACRuc,gBAAgBjlB,aAAI,CAAC+F,QAAQ,CAACnB,uBAAuB8D;oBACrDlD,OAAO;wBACL4P,2BAAe;wBACfpV,aAAI,CAAC+F,QAAQ,CAACjG,SAAS2Y;wBACvByC,0BAAc;wBACdxZ,8BAAkB;wBAClB1B,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEC,qCAAyB;wBACrDlD,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEkc,+BAAmB;wBAC/Cnf,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEiiB,qCAAyB,GAAG;2BACpD,CAAC/c,QAAQD,GAAG,CAACsc,SAAS,GACtB;4BACExkB,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChBkiB,8CAAkC,GAAG;4BAEvCC,mCAAuB;yBACxB,GACD,EAAE;2BACFlgB,SACA;+BACM3B,OAAOkD,YAAY,CAACoX,GAAG,GACvB;gCACE7d,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChBoiB,0CAA8B,GAAG;gCAEnCrlB,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChBoiB,0CAA8B,GAAG;6BAEpC,GACD,EAAE;4BACNrlB,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEyZ,8BAAkB;4BAC9C1c,aAAI,CAACC,IAAI,CAAC2c,oCAAwB;4BAClCxB,8BAAkB;4BAClBpb,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChBoc,qCAAyB,GAAG;4BAE9Brf,aAAI,CAACC,IAAI,CACPgD,4BAAgB,EAChBoc,qCAAyB,GAAG;yBAE/B,GACD,EAAE;2BACF5S,YAAY,CAAC1C,iBACb;4BACEub,gCAAoB,GAAG;4BACvBtlB,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEqiB,gCAAoB,GAAG;yBACpD,GACD,EAAE;wBACNC,yBAAa;wBACbvlB,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEuiB,8BAAkB,GAAG;wBACjDxlB,aAAI,CAACC,IAAI,CAACgD,4BAAgB,EAAEuiB,8BAAkB,GAAG;2BAC9CjB;qBACJ,CACEniB,MAAM,CAACqjB,wBAAW,EAClBnjB,GAAG,CAAC,CAACiD,OAASvF,aAAI,CAACC,IAAI,CAACsD,OAAOzD,OAAO,EAAEyF;oBAC3CmgB,QAAQ,EAAE;gBACZ;gBAEA,OAAOd;YACT;YAEF,IAAI,CAAChK,gBAAgB;gBACnB6J,4BAA4BiB,MAAM,CAAC7f,IAAI,CACrC7F,aAAI,CAAC+F,QAAQ,CACX2C,KACA1I,aAAI,CAACC,IAAI,CACPD,aAAI,CAACiG,OAAO,CACVmB,QAAQC,OAAO,CACb,sDAGJ;YAIR;YAEA,MAAMse,iBAAiB/U,UAAU8P,IAAI,CAAC,CAAC7c,IACrCA,EAAE+B,QAAQ,CAAC4K,8BAAmB;YAEhC,IAAI1L,oBAAoB;YAExB,IAAI6gB,gBAAgB;gBAClB,MAAMzE,aAAa,MAAMC,IAAAA,sCAA6B,EAAC;oBACrDF,gBAAgB;oBAChBF,cAAc/gB,aAAI,CAACC,IAAI,CAACyI,KAAKid;oBAC7BpiB;oBACA2B;oBACA6K,gBAAgBxM,OAAOwM,cAAc;oBACrC+B,OAAO;oBACP3S,MAAM;gBACR;gBAEA,IAAI+hB,WAAWE,OAAO,KAAK,UAAU;wBAIvBF;oBAHZpc,oBAAoB;oBACpBoY,wBAAwBC,SAAS,CAAC,eAAe,GAAG;wBAClDiE,SAASF,WAAWE,OAAO;wBAC3BwE,UAAU1E,EAAAA,yBAAAA,WAAW2E,UAAU,qBAArB3E,uBAAuB0E,QAAQ,KAAI;4BAC3C;gCACEE,QAAQ;gCACRC,gBAAgB;4BAClB;yBACD;oBACH;oBAEA,IAAIhc,gBAAgB;wBAClB,MAAM5I,cACJnB,aAAI,CAACC,IAAI,CACPH,SACA,UACA+B,SACAmkB,gDAAoC,GAEtC9I,wBAAwBC,SAAS,CAAC,eAAe,CAACyI,QAAQ,IAAI,EAAE;oBAEpE;gBACF;YACF;YAEA,MAAM5iB,6BAA6BlD,SAASod;YAE5C,IAAI,CAAC3T,kBAAkB,CAACqP,oBAAoB;gBAC1CA,qBAAqB4B,IAAAA,sCAAkB,EAAC;oBACtC9R;oBACAnF;oBACAzD;oBACA2a,mBAAmBC,IAAAA,qCAA6B,EAAC0B;oBACjDpX,aAAa;2BAAIA;qBAAY;oBAC7BP;oBACAmW;oBACAjC;oBACA/T;gBACF,GAAGiW,KAAK,CAAC,CAACjM;oBACRrO,QAAQ6O,KAAK,CAACR;oBACdzG,QAAQmH,IAAI,CAAC;gBACf;YACF;YAEA,IAAIuM,iBAAiBoE,IAAI,GAAG,KAAKle,SAASke,IAAI,GAAG,GAAG;gBAClD,yDAAyD;gBACzD,+DAA+D;gBAC/D5K,eAAeW,UAAU,GAAGT,IAAAA,sBAAe,EAAC;uBACvCsG;uBACA9Z;iBACJ,EAAEO,GAAG,CAAC,CAACnD;oBACN,OAAO8mB,IAAAA,8BAAc,EAAC9mB,MAAM0C;gBAC9B;YACF;YAEA,2DAA2D;YAC3D,MAAM4C,cACHU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMjE,cAAcgU,oBAAoBE;YAExD,iHAAiH;YACjH,8DAA8D;YAC9D,MAAM6Q,oBACJ,CAAC9I,4BAA6B,CAAA,CAACG,yBAAyB1J,WAAU;YAEpE,IAAI8H,aAAasE,IAAI,GAAG,GAAG;gBACzB,MAAMrR,MAAM,qBAQX,CARW,IAAII,MACd,CAAC,qCAAqC,EACpC2M,aAAasE,IAAI,KAAK,IAAI,KAAK,IAChC,kDAAkD,EAAE;uBAAItE;iBAAa,CACnErZ,GAAG,CAAC,CAAC6jB,KAAO,CAAC,KAAK,EAAEA,IAAI,EACxBlmB,IAAI,CACH,MACA,sFAAsF,CAAC,GAPjF,qBAAA;2BAAA;gCAAA;kCAAA;gBAQZ;gBACA2O,IAAIE,IAAI,GAAG;gBACX,MAAMF;YACR;YAEA,MAAMwX,IAAAA,0BAAY,EAACtmB,SAAS+B;YAE5B,IAAI0B,OAAOkD,YAAY,CAAC4f,WAAW,EAAE;gBACnC,MAAMC,WACJlf,QAAQ;gBAEV,MAAMmf,eAAe,MAAM,IAAI9M,QAAkB,CAACpS,SAASmf;oBACzDF,SACE,YACA;wBAAE9Y,KAAKxN,aAAI,CAACC,IAAI,CAACH,SAAS;oBAAU,GACpC,CAAC8O,KAAKpJ;wBACJ,IAAIoJ,KAAK;4BACP,OAAO4X,OAAO5X;wBAChB;wBACAvH,QAAQ7B;oBACV;gBAEJ;gBAEAif,4BAA4Bjf,KAAK,CAACK,IAAI,IACjC0gB,aAAajkB,GAAG,CAAC,CAACzB,WACnBb,aAAI,CAACC,IAAI,CAACsD,OAAOzD,OAAO,EAAE,UAAUe;YAG1C;YAEA,MAAM4lB,WAAqC;gBACzC;oBACEjX,aAAa;oBACbC,iBAAiBlM,OAAOkD,YAAY,CAACoO,SAAS,GAAG,IAAI;gBACvD;gBACA;oBACErF,aAAa;oBACbC,iBAAiBlM,OAAOkD,YAAY,CAAC4f,WAAW,GAAG,IAAI;gBACzD;gBACA;oBACE7W,aAAa;oBACbC,iBAAiBlM,OAAOkD,YAAY,CAACigB,iBAAiB,GAAG,IAAI;gBAC/D;gBACA;oBACElX,aAAa;oBACbC,iBAAiBlM,OAAOkD,YAAY,CAACyO,GAAG,GAAG,IAAI;gBACjD;aACD;YACD5I,UAAUY,MAAM,CACduZ,SAASnkB,GAAG,CAAC,CAACqkB;gBACZ,OAAO;oBACLjX,WAAWC,iCAAyB;oBACpCC,SAAS+W;gBACX;YACF;YAGF,MAAMxjB,iCACJrD,SACA2kB;YAGF,MAAM5f,qBAAyC,MAAMvD,aACnDtB,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAEkc,+BAAmB;YAG1D,MAAMvd,oBAAuC;gBAC3C0C,SAAS;gBACTnC,QAAQ,CAAC;gBACTQ,eAAe,CAAC;gBAChBikB,gBAAgB,EAAE;gBAClBC,SAASvV;YACX;YAEA,MAAMwV,qBAA+B,EAAE;YAEvC,MAAM,EAAE7Q,IAAI,EAAE,GAAG1S;YAEjB,MAAMwjB,wBAAwBrD,+BAAmB,CAACthB,MAAM,CACtD,CAACjD,OACCwL,WAAW,CAACxL,KAAK,IACjBwL,WAAW,CAACxL,KAAK,CAAC6N,UAAU,CAAC;YAEjC+Z,sBAAsBC,OAAO,CAAC,CAAC7nB;gBAC7B,IAAI,CAAC4C,SAASklB,GAAG,CAAC9nB,SAAS,CAACie,0BAA0B;oBACpDpY,YAAYuO,GAAG,CAACpU;gBAClB;YACF;YAEA,MAAM+nB,cAAcH,sBAAsBnhB,QAAQ,CAAC;YACnD,MAAMuhB,sBACJ,CAACD,eAAe,CAAC3J,yBAAyB,CAACH;YAE7C,MAAMgK,gBAAgB;mBAAIpiB;mBAAgBjD;aAAS;YACnD,MAAMslB,iBAAiBtL,YAAYkL,GAAG,CAACjT,4CAAgC;YACvE,MAAMsT,kBAAkBvT,aAAasT;YAErC,MAAM9O,IAAAA,wCAAsB,EAAC;gBAC3BC,YAAY;YACd;YAEA,sDAAsD;YACtD,mBAAmB;YACnB,yBAAyB;YACzB,gCAAgC;YAChC,IACE,CAACtO,iBACAkd,CAAAA,cAAcnb,MAAM,GAAG,KACtBia,qBACAiB,uBACAjiB,MAAK,GACP;gBACA,MAAMqiB,uBACJ9iB,cAAcU,UAAU,CAAC;gBAC3B,MAAMoiB,qBAAqBniB,YAAY,CAAC;oBACtCoiB,IAAAA,8BAAsB,EACpB;2BACKJ;2BACA1iB,SAASY,KAAK,CAAClD,MAAM,CAAC,CAACjD,OAAS,CAACioB,cAAcxhB,QAAQ,CAACzG;qBAC5D,EACD4C,UACA,IAAI4Y,IACF9J,MAAMC,IAAI,CAACgL,gBAAgB5Z,OAAO,IAAII,GAAG,CACvC,CAAC,CAACnD,MAAMgD,OAAO;wBACb,OAAO;4BAAChD;4BAAMgD,OAAOG,GAAG,CAAC,CAACC,QAAUA,MAAME,QAAQ;yBAAE;oBACtD;oBAIN,MAAMoG,YAAYzB,QAAQ,aACvB0B,OAAO;oBAEV,MAAM2e,eAAmC;wBACvC,GAAGlkB,MAAM;wBACT,sEAAsE;wBACtE,+BAA+B;wBAC/B,wEAAwE;wBACxE,6DAA6D;wBAC7DmkB,eAAe,CAACC;4BACd,+DAA+D;4BAC/D,iEAAiE;4BACjE,uEAAuE;4BACvE,UAAU;4BACV,EAAE;4BACF,6DAA6D;4BAC7D5lB,SAASilB,OAAO,CAAC,CAAC7nB;gCAChB,IAAIsW,IAAAA,qBAAc,EAACtW,OAAO;oCACxB2nB,mBAAmBjhB,IAAI,CAAC1G;oCAExB,IAAIsc,uBAAuBwL,GAAG,CAAC9nB,OAAO;wCACpC,iEAAiE;wCACjE,mBAAmB;wCACnB,IAAI8W,MAAM;4CACR0R,UAAU,CAAC,CAAC,CAAC,EAAE1R,KAAKqI,aAAa,GAAGnf,MAAM,CAAC,GAAG;gDAC5CA;gDACAyoB,gBAAgB;4CAClB;wCACF,OAAO;4CACLD,UAAU,CAACxoB,KAAK,GAAG;gDACjBA;gDACAyoB,gBAAgB;4CAClB;wCACF;oCACF,OAAO;wCACL,iEAAiE;wCACjE,iCAAiC;wCACjC,OAAOD,UAAU,CAACxoB,KAAK;oCACzB;gCACF;4BACF;4BAEA,oEAAoE;4BACpE,cAAc;4BACd2c,gBAAgBkL,OAAO,CAAC,CAAC7kB,QAAQhD;gCAC/BgD,OAAO6kB,OAAO,CAAC,CAACzkB;oCACdolB,UAAU,CAACplB,MAAME,QAAQ,CAAC,GAAG;wCAC3BtD;wCACA0oB,UAAUtlB,MAAMqgB,eAAe;oCACjC;gCACF;4BACF;4BAEA,IAAIsD,mBAAmB;gCACrByB,UAAU,CAAC,OAAO,GAAG;oCACnBxoB,MAAM0U,cAAc,SAAS;gCAC/B;4BACF;4BAEA,IAAIsT,qBAAqB;gCACvBQ,UAAU,CAAC,OAAO,GAAG;oCACnBxoB,MAAM;gCACR;4BACF;4BAEA,wDAAwD;4BACxD,gDAAgD;4BAChD4c,YAAYiL,OAAO,CAAC,CAAC7kB,QAAQye;gCAC3B,MAAM6B,YAAYtG,kBAAkB2L,GAAG,CAAClH;gCACxC,MAAMmH,iBAAiBtF,CAAAA,6BAAAA,UAAWQ,OAAO,MAAK;gCAE9C,MAAM7C,oBAAoBqC,YACtBuF,IAAAA,2BAAsB,EAACzkB,OAAOkD,YAAY,CAACyO,GAAG,EAAEuN,aAChDpY;gCAEJlI,OAAO6kB,OAAO,CAAC,CAACzkB;oCACd,8DAA8D;oCAC9D,wDAAwD;oCACxD,0DAA0D;oCAC1D,IACEA,MAAMygB,kBAAkB,IACxBzgB,MAAMygB,kBAAkB,CAAC/W,MAAM,GAAG,GAClC;wCACA;oCACF;oCAEA0b,UAAU,CAACplB,MAAME,QAAQ,CAAC,GAAG;wCAC3BtD,MAAMyhB;wCACNiH,UAAUtlB,MAAMqgB,eAAe;wCAC/BqF,sBAAsB1lB,MAAMsgB,mBAAmB;wCAC/CqF,iBAAiBH;wCACjBI,WAAW;wCACXC,oBAAoBhI;oCACtB;gCACF;4BACF;4BAEA,gEAAgE;4BAChE,gEAAgE;4BAChE,2DAA2D;4BAC3D,wCAAwC;4BACxC,KAAK,MAAM,EACTjhB,IAAI,EACJyhB,eAAe,EAChB,IAAI5E,mBAAmBqM,MAAM,GAAI;gCAChCV,UAAU,CAACxoB,KAAK,GAAG;oCACjBA,MAAMyhB;oCACNiH,UAAU1oB;oCACV8oB,sBAAsBK,IAAAA,4BAAY,EAACnpB;oCACnC,sDAAsD;oCACtDgpB,WAAW;oCACX,6DAA6D;oCAC7DC,oBAAoB;oCACpBG,sBAAsB;oCACtB,+DAA+D;oCAC/DL,iBAAiB;gCACnB;4BACF;4BAEA,IAAIjS,MAAM;gCACR,KAAK,MAAM9W,QAAQ;uCACd6F;uCACAjD;uCACCmkB,oBAAoB;wCAAC;qCAAO,GAAG,EAAE;uCACjCiB,sBAAsB;wCAAC;qCAAO,GAAG,EAAE;iCACxC,CAAE;oCACD,MAAMqB,QAAQzmB,SAASklB,GAAG,CAAC9nB;oCAC3B,MAAMojB,YAAY9M,IAAAA,qBAAc,EAACtW;oCACjC,MAAMspB,aAAaD,SAAS/M,uBAAuBwL,GAAG,CAAC9nB;oCAEvD,KAAK,MAAMupB,UAAUzS,KAAKnU,OAAO,CAAE;4CAMzB6lB;wCALR,+DAA+D;wCAC/D,IAAIa,SAASjG,aAAa,CAACkG,YAAY;wCACvC,MAAM3iB,aAAa,CAAC,CAAC,EAAE4iB,SAASvpB,SAAS,MAAM,KAAKA,MAAM;wCAE1DwoB,UAAU,CAAC7hB,WAAW,GAAG;4CACvB3G,MAAMwoB,EAAAA,mBAAAA,UAAU,CAACxoB,KAAK,qBAAhBwoB,iBAAkBxoB,IAAI,KAAIA;4CAChCwpB,SAASD;4CACTd,gBAAgBa;wCAClB;oCACF;oCAEA,IAAID,OAAO;wCACT,qDAAqD;wCACrD,OAAOb,UAAU,CAACxoB,KAAK;oCACzB;gCACF;4BACF;4BAEA,OAAOwoB;wBACT;oBACF;oBAEA,MAAMve,SAASpJ,aAAI,CAACC,IAAI,CAACH,SAAS;oBAClC,MAAM8oB,eAAe,MAAM/f,UACzBH,KACA;wBACEQ,YAAYue;wBACZ9e;wBACAQ,QAAQ;wBACRF,aAAa;wBACbU;wBACArE,OAAO8hB;wBACPhe;wBACAyf,eAAe;wBACfjhB,YAAYpB,mBAAmBihB;oBACjC,GACAhjB;oBAGF,sDAAsD;oBACtD,IAAI,CAACmkB,cAAc;oBAEnB,IAAIjf,eAAexB,QAAQD,GAAG,CAAC4gB,sBAAsB,KAAK,KAAK;wBAC7DC,IAAAA,oCAAkB,EAACH;oBACrB;oBAEAI,IAAAA,qDAA+B,EAAC;wBAC9BlpB,SAASyD,OAAOzD,OAAO;wBACvBmpB,QAAQ;4BACNne;+BACG8d,aAAaM,2BAA2B,CAACb,MAAM;yBACnD;oBACH;oBAEAzmB,kBAAkBglB,cAAc,GAAG/V,MAAMC,IAAI,CAC3C8X,aAAaO,gBAAgB;oBAG/B,2CAA2C;oBAC3C,KAAK,MAAMhqB,QAAQ6F,YAAa;wBAC9B,MAAMokB,eAAeC,IAAAA,oBAAW,EAAClqB,MAAMW,SAASuK,WAAW;wBAC3D,MAAMtJ,YAAE,CAACuoB,MAAM,CAACF;oBAClB;oBAEArN,YAAYiL,OAAO,CAAC,CAACxE,mBAAmB5B;4BASpCgI,0BAEuBxM;wBAVzB,MAAMjd,OAAO8c,mBAAmB6L,GAAG,CAAClH;wBACpC,IAAI,CAACzhB,MAAM,MAAM,qBAAoC,CAApC,IAAIoqB,8BAAc,CAAC,mBAAnB,qBAAA;mCAAA;wCAAA;0CAAA;wBAAmC;wBAEpD,MAAM9G,YAAYtG,kBAAkB2L,GAAG,CAAClH;wBACxC,IAAI,CAAC6B,WAAW,MAAM,qBAA0C,CAA1C,IAAI8G,8BAAc,CAAC,yBAAnB,qBAAA;mCAAA;wCAAA;0CAAA;wBAAyC;wBAE/D,IAAIC,oBACF/G,UAAUC,UAAU,KAAK,KACzBkG,EAAAA,2BAAAA,aAAaa,MAAM,CAAC3B,GAAG,CAAC3oB,0BAAxBypB,yBAA+BlG,UAAU,MAAK;wBAEhD,IAAI8G,uBAAqBpN,iBAAAA,UAAU0L,GAAG,CAAC3oB,0BAAdid,eAAqBkE,QAAQ,GAAE;4BACtD,uEAAuE;4BACvE,qFAAqF;4BACrFlE,UAAUiG,GAAG,CAACljB,MAAM;gCAClB,GAAIid,UAAU0L,GAAG,CAAC3oB,KAAK;gCACvBmhB,UAAU;gCACVD,OAAO;4BACT;wBACF;wBAEA,MAAMqJ,oBAAoBC,IAAAA,gCAAe,EAAC/I;wBAE1C,kEAAkE;wBAClE,yBAAyB;wBACzB,MAAMR,oBACJ,CAACsJ,qBACD1B,IAAAA,2BAAsB,EAACzkB,OAAOkD,YAAY,CAACyO,GAAG,EAAEuN,aAC5C,OACApY;wBAEN,MAAMuf,sBACJ,uEAAuE;wBACvErmB,OAAOsmB,eAAe,IAAIC,oCAA6B;wBAEzD,0FAA0F;wBAC1F,4CAA4C;wBAC5C,MAAMC,YAAwB;4BAC5B;gCAAEC,MAAM;gCAAUrN,KAAKsN,+BAAa;4BAAC;4BACrC;gCACED,MAAM;gCACNrN,KAAK;gCACLgI,OAAO;4BACT;4BACA,iGAAiG;4BACjG,iGAAiG;+BAC7FvE,qBACJ,wFAAwF;4BACxF,0EAA0E;4BAC1EjY,QAAQD,GAAG,CAACgiB,uBAAuB,KAAK,SACpC;gCACE;oCACEF,MAAM;oCACNrN,KAAK;oCACLgI,OAAOiF;gCACT;6BACD,GACD,EAAE;yBACP;wBAED,mEAAmE;wBACnE,6DAA6D;wBAC7D,mEAAmE;wBACnE,8DAA8D;wBAC9D,2BAA2B;wBAC3B,MAAMznB,SAA6B,EAAE;wBACrC,MAAMQ,gBAAoC,EAAE;wBAE5C,mEAAmE;wBACnE,iEAAiE;wBACjE,+DAA+D;wBAC/D,iEAAiE;wBACjE,mDAAmD;wBACnD,IAAIwnB,yBAA6C,EAAE;wBACnD,IAAIC,uBAA2C,EAAE;wBACjD,KAAK,MAAMC,oBAAoB7H,kBAAmB;4BAChD,IACE6H,iBAAiBxH,mBAAmB,IACpCwH,iBAAiBxH,mBAAmB,CAAC5W,MAAM,GAAG,GAC9C;gCACAke,uBAAuBtkB,IAAI,CAACwkB;4BAC9B,OAAO;gCACLD,qBAAqBvkB,IAAI,CAACwkB;4BAC5B;wBACF;wBAEAF,yBAAyBG,IAAAA,4BAAqB,EAC5CH,wBACA,CAACE,mBAAqBA,iBAAiB5nB,QAAQ;wBAEjD2nB,uBAAuBE,IAAAA,4BAAqB,EAC1CF,sBACA,CAACC,mBAAqBA,iBAAiB5nB,QAAQ;wBAGjD+f,oBAAoB;+BACf4H;+BACAD;yBACJ;wBAED,KAAK,MAAME,oBAAoB7H,kBAAmB;4BAChD,+BAA+B;4BAC/B,iCAAiC;4BACjC,IAAI6H,iBAAiB5nB,QAAQ,KAAK8nB,sCAA0B,EAAE;gCAC5D;4BACF;4BAEA,IACEnK,qBACAiK,iBAAiBxH,mBAAmB,IACpCwH,iBAAiBxH,mBAAmB,CAAC5W,MAAM,GAAG,GAC9C;gCACA,6DAA6D;gCAC7D,8BAA8B;gCAC9BtJ,cAAckD,IAAI,CAACwkB;4BACrB,OAAO;gCACL,4DAA4D;gCAC5D,gCAAgC;gCAChCloB,OAAO0D,IAAI,CAACwkB;4BACd;wBACF;wBAEA,gCAAgC;wBAChC,KAAK,MAAM9nB,SAASJ,OAAQ;4BAC1B,IAAIsT,IAAAA,qBAAc,EAACtW,SAASoD,MAAME,QAAQ,KAAKtD,MAAM;4BACrD,IAAIoD,MAAME,QAAQ,KAAK8nB,sCAA0B,EAAE;4BAEnD,MAAM,EACJ7H,aAAaD,UAAUC,UAAU,IAAI,KAAK,EAC1C8H,WAAW,CAAC,CAAC,EACbzG,eAAe,EACf0G,YAAY,EACb,GAAG7B,aAAaa,MAAM,CAAC3B,GAAG,CAACvlB,MAAME,QAAQ,KAAK,CAAC;4BAEhD2Z,UAAUiG,GAAG,CAAC9f,MAAME,QAAQ,EAAE;gCAC5B,GAAI2Z,UAAU0L,GAAG,CAACvlB,MAAME,QAAQ,CAAC;gCACjCgoB;gCACA1G;4BACF;4BAEA,uEAAuE;4BACvE3H,UAAUiG,GAAG,CAACljB,MAAM;gCAClB,GAAIid,UAAU0L,GAAG,CAAC3oB,KAAK;gCACvBsrB;gCACA1G;4BACF;4BAEA,IAAIrB,eAAe,GAAG;gCACpB,MAAMgI,kBAAkB1K,IAAAA,oCAAiB,EAACzd,MAAME,QAAQ;gCAExD,IAAIkoB;gCACJ,IAAIjB,mBAAmB;oCACrBiB,YAAY;gCACd,OAAO;oCACLA,YAAY3qB,aAAI,CAAC4qB,KAAK,CAAC3qB,IAAI,CAAC,GAAGyqB,kBAAkB3T,qBAAU,EAAE;gCAC/D;gCAEA,IAAI8T;gCACJ,6DAA6D;gCAC7D,6DAA6D;gCAC7D,6DAA6D;gCAC7D,uBAAuB;gCACvB,IAAI,CAACnB,qBAAqB1U,iBAAiB;oCACzC6V,oBAAoB7qB,aAAI,CAAC4qB,KAAK,CAAC3qB,IAAI,CACjC,GAAGyqB,kBAAkBzT,8BAAmB,EAAE;gCAE9C;gCAEA,MAAM6T,OAAOC,IAAAA,mBAAW,EAACP;gCAEzB5oB,kBAAkBO,MAAM,CAACI,MAAME,QAAQ,CAAC,GAAG;oCACzCuoB,eAAeF,KAAKG,MAAM;oCAC1BC,gBAAgBJ,KAAKrf,OAAO;oCAC5B0f,eAAenW,kBACXoL,oBACEgL,4BAAa,CAACC,gBAAgB,GAC9BD,4BAAa,CAACE,MAAM,GACtBjhB;oCACJkhB,iBAAiBnL;oCACjBoL,uBAAuBzB;oCACvBnG,0BAA0BlB;oCAC1BrgB,UAAUlD;oCACVwrB;oCACAE;oCACAY,aAAa7sB;gCACf;4BACF,OAAO;gCACL4qB,oBAAoB;gCACpB,8DAA8D;gCAC9D,oBAAoB;gCACpBpN,UAAUiG,GAAG,CAAC9f,MAAME,QAAQ,EAAE;oCAC5B,GAAI2Z,UAAU0L,GAAG,CAACvlB,MAAME,QAAQ,CAAC;oCACjC4d,OAAO;oCACPC,UAAU;gCACZ;4BACF;wBACF;wBAEA,IAAI,CAACkJ,qBAAqB/T,IAAAA,qBAAc,EAACtW,OAAO;4BAC9C,iEAAiE;4BACjE,0DAA0D;4BAC1D,sBAAsB;4BACtB,IAAI,CAACihB,mBAAmB;gCACtBzd,cAAckD,IAAI,CAAC;oCACjBpD,UAAUtD;oCACVyjB,iBAAiBzjB;oCACjB0jB,qBAAqBxY;oCACrByY,cACE5G,cAAc4L,GAAG,CAAClH,oBAClBwC,sBAAY,CAACsI,SAAS;oCACxB1I,oBAAoB3Y;gCACtB;4BACF;4BAEA,KAAK,MAAM9H,SAASI,cAAe;oCAsFVJ;gCArFvB,MAAMmoB,kBAAkB1K,IAAAA,oCAAiB,EAACzd,MAAME,QAAQ;gCAExD,MAAM,EAAE+nB,QAAQ,EAAE9H,UAAU,EAAE,GAC5BkG,aAAaa,MAAM,CAAC3B,GAAG,CAACvlB,MAAME,QAAQ,KAAK,CAAC;gCAE9C,IAAIkoB,YAA2B;gCAC/B,IAAI,CAACjB,mBAAmB;oCACtBiB,YAAY3qB,aAAI,CAAC4qB,KAAK,CAAC3qB,IAAI,CAAC,GAAGyqB,kBAAkB3T,qBAAU,EAAE;gCAC/D;gCAEA,IAAI8T;gCACJ,IAAI,CAACnB,qBAAqB1U,iBAAiB;oCACzC6V,oBAAoB7qB,aAAI,CAAC4qB,KAAK,CAAC3qB,IAAI,CACjC,GAAGyqB,kBAAkBzT,8BAAmB,EAAE;gCAE9C;gCAEA,IAAI,CAACyS,sBAAqBc,4BAAAA,SAAUmB,YAAY,GAAE;oCAChD,MAAMC,eAAevW,eAAe1S,aAAa,CAAC+d,IAAI,CACpD,CAAC5K,IAAMA,EAAE3W,IAAI,KAAKA;oCAEpB,IAAI,CAACysB,cAAc;wCACjB,MAAM,qBAAoC,CAApC,IAAI5c,MAAM,4BAAV,qBAAA;mDAAA;wDAAA;0DAAA;wCAAmC;oCAC3C;oCAEA4c,aAAaC,yBAAyB,GAAG,EAAE;oCAC3C,KAAK,MAAMC,eAAetB,SAASmB,YAAY,CAAE;wCAC/C,MAAMI,SAASC,IAAAA,4DAA6B,EAC1CzpB,MAAME,QAAQ,EACdqpB;wCAEFF,aAAaC,yBAAyB,CAAChmB,IAAI,CAACkmB;oCAC9C;gCACF;gCAEA3P,UAAUiG,GAAG,CAAC9f,MAAME,QAAQ,EAAE;oCAC5B,GAAI2Z,UAAU0L,GAAG,CAACvlB,MAAME,QAAQ,CAAC;oCACjCwpB,mBAAmB;oCACnB,gEAAgE;oCAChE,2CAA2C;oCAC3CxB,cAAcrK;gCAChB;gCAEA,MAAM0C,eACJvgB,MAAMugB,YAAY,IAAIM,sBAAY,CAACsI,SAAS;gCAE9C,gEAAgE;gCAChE,6DAA6D;gCAC7D,+DAA+D;gCAC/D,IAAIQ,qBACF9L,qBAAqB0C,iBAAiBM,sBAAY,CAACE,SAAS,GACxDZ,cAAc,QACdrY;gCAEN,MAAM0B,WAAqBogB,IAAAA,qCAA2B,EACpDrJ,cACAvgB,MAAME,QAAQ;gCAGhB,MAAMqoB,OACJN,YACApK,qBACA0C,iBAAiBM,sBAAY,CAACE,SAAS,GACnCyH,IAAAA,mBAAW,EAACP,YACZ,CAAC;gCAEP5oB,kBAAkBe,aAAa,CAACJ,MAAME,QAAQ,CAAC,GAAG;oCAChD8oB,iBAAiBnL;oCACjB+K,eAAenW,kBACXoL,oBACEgL,4BAAa,CAACC,gBAAgB,GAC9BD,4BAAa,CAACE,MAAM,GACtBjhB;oCACJmhB,uBAAuBzB;oCACvB3qB,YAAYI,IAAAA,qCAAmB,EAC7BH,IAAAA,8BAAkB,EAACkD,MAAME,QAAQ,EAAE;wCACjCnD,iBAAiB;oCACnB,GAAGG,EAAE,CAACC,MAAM;oCAEdirB;oCACA5e;oCACAmgB;oCACAE,gBAAgBtB,KAAKG,MAAM;oCAC3BoB,iBAAiBvB,KAAKrf,OAAO;oCAC7BuX,oBAAoBzgB,MAAMygB,kBAAkB;oCAC5CsJ,qBAAqB/pB,EAAAA,6BAAAA,MAAMsgB,mBAAmB,qBAAzBtgB,2BAA2B0J,MAAM,IAClD9M,OACAkL;oCACJkiB,gBAAgB,CAAC5B,YACb,OACAnrB,IAAAA,qCAAmB,EACjBH,IAAAA,8BAAkB,EAACsrB,WAAW;wCAC5BrrB,iBAAiB;wCACjBktB,eAAe;wCACfC,8BAA8B;oCAChC,GAAGhtB,EAAE,CAACC,MAAM;oCAElBmrB;oCACA6B,wBAAwB,CAAC7B,oBACrBxgB,YACA7K,IAAAA,qCAAmB,EACjBH,IAAAA,8BAAkB,EAACwrB,mBAAmB;wCACpCvrB,iBAAiB;wCACjBktB,eAAe;wCACfC,8BAA8B;oCAChC,GAAGhtB,EAAE,CAACC,MAAM;oCAElB+rB,aAAa7sB;gCACf;4BACF;wBACF;oBACF;oBAEA,MAAM+tB,mBAAmB,OACvBC,YACAztB,MACAoG,MACAijB,OACAqE,KACAC,oBAAoB,KAAK;wBAEzB,OAAOvF,qBACJpiB,UAAU,CAAC,sBACXC,YAAY,CAAC;4BACZG,OAAO,GAAGA,KAAK,CAAC,EAAEsnB,KAAK;4BACvB,MAAME,OAAO/sB,aAAI,CAACC,IAAI,CAACmJ,QAAQ7D;4BAC/B,MAAM8N,WAAWgW,IAAAA,oBAAW,EAC1BuD,YACA9sB,SACAuK,WACA;4BAGF,MAAM2iB,eAAehtB,aAAI,CACtB+F,QAAQ,CACP/F,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,GACnCjD,aAAI,CAACC,IAAI,CACPD,aAAI,CAACC,IAAI,CACPoT,UACA,yDAAyD;4BACzD,4BAA4B;4BAC5BuZ,WACGK,KAAK,CAAC,GACNC,KAAK,CAAC,KACN5qB,GAAG,CAAC,IAAM,MACVrC,IAAI,CAAC,OAEVsF,OAGH6L,OAAO,CAAC,OAAO;4BAElB,IACE,CAACoX,SACD,CACE,mDAAmD;4BACnD,kDAAkD;4BAEhD9E,CAAAA,+BAAmB,CAAC9d,QAAQ,CAACzG,SAC7B,CAAC4nB,sBAAsBnhB,QAAQ,CAACzG,KAAI,GAGxC;gCACAkd,aAAa,CAACld,KAAK,GAAG6tB;4BACxB;4BAEA,MAAMG,OAAOntB,aAAI,CAACC,IAAI,CAACH,SAASmD,4BAAgB,EAAE+pB;4BAClD,MAAMI,aACJxrB,kBAAkBglB,cAAc,CAAChhB,QAAQ,CAACzG;4BAE5C,2DAA2D;4BAC3D,0DAA0D;4BAC1D,qBAAqB;4BACrB,IAAI,AAAC,CAAA,CAAC8W,QAAQ6W,iBAAgB,KAAM,CAACM,YAAY;gCAC/C,MAAMrsB,YAAE,CAACiF,KAAK,CAAChG,aAAI,CAACiG,OAAO,CAACknB,OAAO;oCAAEjnB,WAAW;gCAAK;gCACrD,MAAMnF,YAAE,CAACssB,MAAM,CAACN,MAAMI;4BACxB,OAAO,IAAIlX,QAAQ,CAACuS,OAAO;gCACzB,wDAAwD;gCACxD,oDAAoD;gCACpD,OAAOnM,aAAa,CAACld,KAAK;4BAC5B;4BAEA,IAAI8W,MAAM;gCACR,IAAI6W,mBAAmB;gCAEvB,MAAMQ,YAAYnuB,SAAS,MAAMa,aAAI,CAACutB,OAAO,CAAChoB,QAAQ;gCACtD,MAAMioB,sBAAsBR,aAAaC,KAAK,CAC5C,SAAShhB,MAAM;gCAGjB,KAAK,MAAMyc,UAAUzS,KAAKnU,OAAO,CAAE;oCACjC,MAAM2rB,UAAU,CAAC,CAAC,EAAE/E,SAASvpB,SAAS,MAAM,KAAKA,MAAM;oCAEvD,IACEqpB,SACA5mB,kBAAkBglB,cAAc,CAAChhB,QAAQ,CAAC6nB,UAC1C;wCACA;oCACF;oCAEA,MAAMC,sBAAsB1tB,aAAI,CAC7BC,IAAI,CACH,SACAyoB,SAAS4E,WACT,8DAA8D;oCAC9D,+BAA+B;oCAC/BnuB,SAAS,MAAM,KAAKquB,qBAErBpc,OAAO,CAAC,OAAO;oCAElB,MAAMuc,cAAc3tB,aAAI,CAACC,IAAI,CAC3BmJ,QACAsf,SAAS4E,WACTnuB,SAAS,MAAM,KAAKoG;oCAEtB,MAAMqoB,cAAc5tB,aAAI,CAACC,IAAI,CAC3BH,SACAmD,4BAAgB,EAChByqB;oCAGF,IAAI,CAAClF,OAAO;wCACVnM,aAAa,CAACoR,QAAQ,GAAGC;oCAC3B;oCACA,MAAM3sB,YAAE,CAACiF,KAAK,CAAChG,aAAI,CAACiG,OAAO,CAAC2nB,cAAc;wCACxC1nB,WAAW;oCACb;oCACA,MAAMnF,YAAE,CAACssB,MAAM,CAACM,aAAaC;gCAC/B;4BACF;wBACF;oBACJ;oBAEA,eAAeC;wBACb,OAAOtG,qBACJpiB,UAAU,CAAC,gCACXC,YAAY,CAAC;4BACZ,MAAM2nB,OAAO/sB,aAAI,CAACC,IAAI,CACpBH,SACA,UACA,OACA;4BAEF,MAAM4tB,sBAAsB1tB,aAAI,CAC7BC,IAAI,CAAC,SAAS,YACdmR,OAAO,CAAC,OAAO;4BAElB,IAAI9Q,IAAAA,cAAU,EAACysB,OAAO;gCACpB,MAAMhsB,YAAE,CAACoF,QAAQ,CACf4mB,MACA/sB,aAAI,CAACC,IAAI,CAACH,SAAS,UAAU4tB;gCAE/BrR,aAAa,CAAC,OAAO,GAAGqR;4BAC1B;wBACF;oBACJ;oBAEA,oEAAoE;oBACpE,IAAIpG,iBAAiB;wBACnB,MAAMuG;oBACR,OAAO;wBACL,sGAAsG;wBACtG,IAAI,CAACha,eAAe,CAACE,aAAamS,mBAAmB;4BACnD,MAAMyG,iBAAiB,WAAW,QAAQ,QAAQ,OAAO;wBAC3D;oBACF;oBAEA,IAAIxF,qBAAqB;wBACvB,MAAMwF,iBAAiB,WAAW,QAAQ,QAAQ,OAAO;oBAC3D;oBAEA,KAAK,MAAMxtB,QAAQioB,cAAe;wBAChC,MAAMoB,QAAQzmB,SAASklB,GAAG,CAAC9nB;wBAC3B,MAAM2uB,sBAAsBrS,uBAAuBwL,GAAG,CAAC9nB;wBACvD,MAAMojB,YAAY9M,IAAAA,qBAAc,EAACtW;wBACjC,MAAM4uB,SAASnS,eAAeqL,GAAG,CAAC9nB;wBAClC,MAAMoG,OAAOya,IAAAA,oCAAiB,EAAC7gB;wBAE/B,MAAM6uB,WAAW5R,UAAU0L,GAAG,CAAC3oB;wBAC/B,MAAM8uB,eAAerF,aAAasF,MAAM,CAACpG,GAAG,CAAC3oB;wBAC7C,IAAI6uB,YAAYC,cAAc;4BAC5B,qBAAqB;4BACrB,IAAID,SAASvN,aAAa,EAAE;gCAC1BuN,SAASlK,gBAAgB,GAAGkK,SAASvN,aAAa,CAACne,GAAG,CACpD,CAAC+Q;oCACC,MAAMqG,WAAWuU,aAAaE,eAAe,CAACrG,GAAG,CAACzU;oCAClD,IAAI,OAAOqG,aAAa,aAAa;wCACnC,MAAM,qBAAyC,CAAzC,IAAI1K,MAAM,iCAAV,qBAAA;mDAAA;wDAAA;0DAAA;wCAAwC;oCAChD;oCAEA,OAAO0K;gCACT;4BAEJ;4BACAsU,SAASnK,YAAY,GAAGoK,aAAaE,eAAe,CAACrG,GAAG,CAAC3oB;wBAC3D;wBAEA,+DAA+D;wBAC/D,gEAAgE;wBAChE,YAAY;wBACZ,MAAMivB,gBAAgB,CAAE5F,CAAAA,SAASjG,aAAa,CAACuL,mBAAkB;wBAEjE,IAAIM,eAAe;4BACjB,MAAMzB,iBAAiBxtB,MAAMA,MAAMoG,MAAMijB,OAAO;wBAClD;wBAEA,IAAIuF,UAAW,CAAA,CAACvF,SAAUA,SAAS,CAACjG,SAAS,GAAI;4BAC/C,MAAM8L,UAAU,GAAG9oB,KAAK,IAAI,CAAC;4BAC7B,MAAMonB,iBAAiBxtB,MAAMkvB,SAASA,SAAS7F,OAAO;4BAEtD,IAAIA,OAAO;gCACT,MAAMmE,iBAAiBxtB,MAAMkvB,SAASA,SAAS7F,OAAO;4BACxD;wBACF;wBAEA,IAAIA,OAAO;4BACT,yDAAyD;4BACzD,oDAAoD;4BACpD,IAAI,CAACjG,WAAW;gCACd,MAAMoK,iBAAiBxtB,MAAMA,MAAMoG,MAAMijB,OAAO;gCAEhD,IAAIvS,MAAM;oCACR,+DAA+D;oCAC/D,KAAK,MAAMyS,UAAUzS,KAAKnU,OAAO,CAAE;4CAK7B8mB;wCAJJ,MAAM0F,aAAa,CAAC,CAAC,EAAE5F,SAASvpB,SAAS,MAAM,KAAKA,MAAM;wCAE1DyC,kBAAkBO,MAAM,CAACmsB,WAAW,GAAG;4CACrC1K,0BACEgF,EAAAA,2BAAAA,aAAaa,MAAM,CAAC3B,GAAG,CAACwG,gCAAxB1F,yBAAqClG,UAAU,KAC/C;4CACF6I,iBAAiBlhB;4CACjB8gB,eAAe9gB;4CACfhI,UAAU;4CACVsoB,WAAW3qB,aAAI,CAAC4qB,KAAK,CAAC3qB,IAAI,CACxB,eACA4B,SACA,GAAG0D,KAAK,KAAK,CAAC;4CAEhBslB,mBAAmBxgB;4CACnBohB,aAAa7sB;wCACf;oCACF;gCACF,OAAO;wCAGDgqB;oCAFJhnB,kBAAkBO,MAAM,CAAChD,KAAK,GAAG;wCAC/BykB,0BACEgF,EAAAA,4BAAAA,aAAaa,MAAM,CAAC3B,GAAG,CAAC3oB,0BAAxBypB,0BAA+BlG,UAAU,KAAI;wCAC/C6I,iBAAiBlhB;wCACjB8gB,eAAe9gB;wCACfhI,UAAU;wCACVsoB,WAAW3qB,aAAI,CAAC4qB,KAAK,CAAC3qB,IAAI,CACxB,eACA4B,SACA,GAAG0D,KAAK,KAAK,CAAC;wCAEhB,6CAA6C;wCAC7CslB,mBAAmBxgB;wCACnBohB,aAAa7sB;oCACf;gCACF;gCACA,iCAAiC;gCACjC,IAAIovB,UAAU;wCAEVpF;oCADFoF,SAASpK,wBAAwB,GAC/BgF,EAAAA,4BAAAA,aAAaa,MAAM,CAAC3B,GAAG,CAAC3oB,0BAAxBypB,0BAA+BlG,UAAU,KAAI;gCACjD;4BACF,OAAO;gCACL,oEAAoE;gCACpE,4CAA4C;gCAC5C,iEAAiE;gCACjE,yCAAyC;gCACzC,KAAK,MAAMngB,SAASuZ,gBAAgBgM,GAAG,CAAC3oB,SAAS,EAAE,CAAE;wCAwCjDypB;oCAvCF,MAAM2F,WAAWvO,IAAAA,oCAAiB,EAACzd,MAAME,QAAQ;oCACjD,MAAMkqB,iBACJxtB,MACAoD,MAAME,QAAQ,EACd8rB,UACA/F,OACA,QACA;oCAEF,MAAMmE,iBACJxtB,MACAoD,MAAME,QAAQ,EACd8rB,UACA/F,OACA,QACA;oCAGF,IAAIuF,QAAQ;wCACV,MAAMM,UAAU,GAAGE,SAAS,IAAI,CAAC;wCACjC,MAAM5B,iBACJxtB,MACAkvB,SACAA,SACA7F,OACA,QACA;wCAEF,MAAMmE,iBACJxtB,MACAkvB,SACAA,SACA7F,OACA,QACA;oCAEJ;oCAEA,MAAM5E,2BACJgF,EAAAA,4BAAAA,aAAaa,MAAM,CAAC3B,GAAG,CAACvlB,MAAME,QAAQ,sBAAtCmmB,0BAAyClG,UAAU,KAAI;oCAEzD,IAAI,OAAOkB,6BAA6B,aAAa;wCACnD,MAAM,qBAAyC,CAAzC,IAAI5U,MAAM,iCAAV,qBAAA;mDAAA;wDAAA;0DAAA;wCAAwC;oCAChD;oCAEApN,kBAAkBO,MAAM,CAACI,MAAME,QAAQ,CAAC,GAAG;wCACzCmhB;wCACA2H,iBAAiBlhB;wCACjB8gB,eAAe9gB;wCACfhI,UAAUlD;wCACVwrB,WAAW3qB,aAAI,CAAC4qB,KAAK,CAAC3qB,IAAI,CACxB,eACA4B,SACA,GAAGme,IAAAA,oCAAiB,EAACzd,MAAME,QAAQ,EAAE,KAAK,CAAC;wCAE7C,6CAA6C;wCAC7CooB,mBAAmBxgB;wCACnBohB,aAAa7sB;oCACf;oCAEA,kCAAkC;oCAClC,IAAIovB,UAAU;wCACZA,SAASpK,wBAAwB,GAAGA;oCACtC;gCACF;4BACF;wBACF;oBACF;oBAEA,iCAAiC;oBACjC,MAAM7iB,YAAE,CAACytB,EAAE,CAACplB,QAAQ;wBAAElD,WAAW;wBAAMuoB,OAAO;oBAAK;oBACnD,MAAMttB,cAAcsX,mBAAmB4D;gBACzC;gBAEA,sEAAsE;gBACtE,sBAAsB;gBACtB,MAAM5X,cACHU,UAAU,CAAC,yBACXC,YAAY,CAAC,IAAMjE,cAAcgU,oBAAoBE;YAC1D;YAEA,MAAMqZ,mBAAmB1T,IAAAA,gBAAa,EAAC;YACvC,IAAI2T,qBAAqB3T,IAAAA,gBAAa,EAAC,CAAC,uBAAuB,CAAC;YAEhE,wCAAwC;YACxC8B,OAAOzT,GAAG;YAEV,MAAMulB,cAAczmB,QAAQ6U,MAAM,CAACD;YACnCzQ,UAAUY,MAAM,CACd2hB,IAAAA,0BAAkB,EAAC3e,YAAY;gBAC7BiK,mBAAmByU,WAAW,CAAC,EAAE;gBACjCE,iBAAiB9pB,YAAYib,IAAI;gBACjC8O,sBAAsBhtB,SAASke,IAAI;gBACnC+O,sBAAsBnT,iBAAiBoE,IAAI;gBAC3CgP,cACE/e,WAAWjE,MAAM,GAChBjH,CAAAA,YAAYib,IAAI,GAAGle,SAASke,IAAI,GAAGpE,iBAAiBoE,IAAI,AAAD;gBAC1DiP,cAAchJ;gBACdiJ,oBACE9R,CAAAA,gCAAAA,aAAczX,QAAQ,CAAC,uBAAsB;gBAC/CwpB,eAAexjB,iBAAiBK,MAAM;gBACtCojB,cAAc5jB,QAAQQ,MAAM;gBAC5BqjB,gBAAgB3jB,UAAUM,MAAM,GAAG;gBACnCsjB,qBAAqB9jB,QAAQrJ,MAAM,CAAC,CAAC0T,IAAW,CAAC,CAACA,EAAEmR,GAAG,EAAEhb,MAAM;gBAC/DujB,sBAAsB5jB,iBAAiBxJ,MAAM,CAAC,CAAC0T,IAAW,CAAC,CAACA,EAAEmR,GAAG,EAC9Dhb,MAAM;gBACTwjB,uBAAuB9jB,UAAUvJ,MAAM,CAAC,CAAC0T,IAAW,CAAC,CAACA,EAAEmR,GAAG,EAAEhb,MAAM;gBACnEyjB,iBAAiBre,oBAAoB,IAAI;gBACzCqC;gBACA2H;gBACAC;gBACAC;gBACAC;YACF;YAGF,IAAI9Q,8BAAgB,CAACilB,cAAc,EAAE;gBACnC,MAAM9hB,SAAS+hB,IAAAA,8BAAsB,EACnCllB,8BAAgB,CAACilB,cAAc,CAACE,MAAM;gBAExCvjB,UAAUY,MAAM,CAACW;gBACjBvB,UAAUY,MAAM,CACd4iB,IAAAA,4CAAoC,EAClCplB,8BAAgB,CAACilB,cAAc,CAACI,6BAA6B;gBAGjE,MAAMC,kBAAkBtlB,8BAAgB,CAACilB,cAAc,CAACK,eAAe;gBAEvE,KAAK,MAAM,CAACrT,KAAKgI,MAAM,IAAI1iB,OAAOC,OAAO,CAAC8tB,iBAAkB;oBAC1D1jB,UAAUY,MAAM,CACd0iB,IAAAA,8BAAsB,EAAC;wBACrB;4BACEpgB,aAAamN;4BACblN,iBAAiBkV;wBACnB;qBACD;gBAEL;YACF;YAEA,IAAI5iB,SAASke,IAAI,GAAG,KAAK/a,QAAQ;oBAkDpB3B;gBAjDXujB,mBAAmBE,OAAO,CAAC,CAACiJ;oBAC1B,MAAMvF,kBAAkB1K,IAAAA,oCAAiB,EAACiQ;oBAC1C,MAAMtF,YAAY3qB,aAAI,CAAC4qB,KAAK,CAAC3qB,IAAI,CAC/B,eACA4B,SACA,GAAG6oB,gBAAgB,KAAK,CAAC;oBAG3B9oB,kBAAkBe,aAAa,CAACstB,SAAS,GAAG;wBAC1C7wB,YAAYI,IAAAA,qCAAmB,EAC7BH,IAAAA,8BAAkB,EAAC4wB,UAAU;4BAC3B3wB,iBAAiB;wBACnB,GAAGG,EAAE,CAACC,MAAM;wBAEd6rB,iBAAiBlhB;wBACjB8gB,eAAe9gB;wBACfsgB;wBACA5e,UAAU2P,yBAAyBuL,GAAG,CAACgJ,YACnC,OACAxU,uBAAuBwL,GAAG,CAACgJ,YACzB,GAAGvF,gBAAgB,KAAK,CAAC,GACzB;wBACNwB,oBAAoB7hB;wBACpBiiB,qBAAqBjiB;wBACrB2Y,oBAAoB3Y;wBACpBkiB,gBAAgB/sB,IAAAA,qCAAmB,EACjCH,IAAAA,8BAAkB,EAACsrB,WAAW;4BAC5BrrB,iBAAiB;4BACjBktB,eAAe;4BACfC,8BAA8B;wBAChC,GAAGhtB,EAAE,CAACC,MAAM;wBAEd,6CAA6C;wBAC7CmrB,mBAAmBxgB;wBACnBqiB,wBAAwBriB;wBACxBohB,aAAa7sB;oBACf;gBACF;gBAEA8L,8BAAgB,CAAC6G,aAAa,GAAGD,aAAaC,aAAa;gBAC3D7G,8BAAgB,CAACwlB,mBAAmB,GAClC3sB,OAAOkD,YAAY,CAACypB,mBAAmB;gBACzCxlB,8BAAgB,CAACylB,2BAA2B,GAC1C5sB,OAAOkD,YAAY,CAAC0pB,2BAA2B;gBAEjD,MAAM1uB,uBAAuB3B,SAAS8B;gBACtC,MAAMD,uBAAuBC,mBAAmB;oBAC9C9B;oBACA+B;oBACAC,OAAO,GAAEyB,eAAAA,OAAO0S,IAAI,qBAAX1S,aAAazB,OAAO;gBAC/B;YACF,OAAO;gBACL,MAAML,uBAAuB3B,SAAS;oBACpCwE,SAAS;oBACTnC,QAAQ,CAAC;oBACTQ,eAAe,CAAC;oBAChBkkB,SAASvV;oBACTsV,gBAAgB,EAAE;gBACpB;YACF;YAEA,MAAMtjB,oBAAoBxD,SAASyD;YACnC,MAAMpC,cAAcnB,aAAI,CAACC,IAAI,CAACH,SAASswB,yBAAa,GAAG;gBACrD9rB,SAAS;gBACT+rB,kBAAkB,OAAO9sB,OAAOmkB,aAAa,KAAK;gBAClD4I,qBAAqB/sB,OAAOgtB,aAAa,KAAK;gBAC9CjT,qBAAqBA,wBAAwB;YAC/C;YACA,MAAMvc,YAAE,CAACuoB,MAAM,CAACtpB,aAAI,CAACC,IAAI,CAACH,SAAS0wB,yBAAa,GAAG3V,KAAK,CAAC,CAACjM;gBACxD,IAAIA,IAAIE,IAAI,KAAK,UAAU;oBACzB,OAAO2K,QAAQpS,OAAO;gBACxB;gBACA,OAAOoS,QAAQ+M,MAAM,CAAC5X;YACxB;YAEA,IAAIN,QAAQ/K,OAAOkD,YAAY,CAACigB,iBAAiB,GAAG;gBAClD,MAAMjiB,cACHU,UAAU,CAAC,0BACXC,YAAY,CAAC;oBACZ,MAAMqrB,IAAAA,0CAAoB,EACxB/nB,KACA1I,aAAI,CAACC,IAAI,CAACH,SAASiD,oCAAwB;gBAE/C;YACJ;YAEA,MAAM6V;YAEN,IAAI+V,oBAAoB;gBACtBA,mBAAmBvK,cAAc;gBACjCuK,qBAAqBtkB;YACvB;YAEA,IAAI9G,OAAOib,MAAM,KAAK,UAAU;gBAC9B,MAAM/V,uBACJlF,QACAmF,KACAC,oBACAC,cACAnE;YAEJ;YAEA,IAAIlB,OAAOib,MAAM,KAAK,cAAc;gBAClC,MAAMha,yBACJC,eACA3E,SACA4E,UACAC,sBACAC,uBACA6f,6BACA5f,oBACAC,mBACAC,wBACAC,aACAC,gBACAC;YAEJ;YAEA,IAAIwpB,kBAAkBA,iBAAiBtK,cAAc;YACrD7jB,QAAQC,GAAG;YAEX,IAAImJ,aAAa;gBACflF,cACGU,UAAU,CAAC,uBACXyF,OAAO,CAAC,IAAM8lB,IAAAA,yBAAiB,EAAC;wBAAE/kB;wBAAWD;wBAAUD;oBAAQ;YACpE;YAEA,MAAMhH,cAAcU,UAAU,CAAC,mBAAmBC,YAAY,CAAC,IAC7DurB,IAAAA,qBAAa,EAACjsB,UAAU0X,WAAW;oBACjCwU,UAAU9wB;oBACV+B,SAASA;oBACT4K;oBACAyZ;oBACAnW,gBAAgBxM,OAAOwM,cAAc;oBACrCwM;oBACAD;oBACAzX;oBACAqa,UAAU3b,OAAOkD,YAAY,CAACyY,QAAQ;gBACxC;YAGF,MAAMza,cACHU,UAAU,CAAC,mBACXC,YAAY,CAAC,IAAMkH,UAAU+C,KAAK;YAErC,MAAMmK;QACR;IACF,SAAU;QACR,kDAAkD;QAClD,MAAMqX,yBAAoB,CAACC,GAAG;QAE9B,6DAA6D;QAC7D,MAAMC,IAAAA,qBAAc;QACpBC,IAAAA,4BAAuB;QAEvB,IAAI/mB,kBAAkBE,cAAc;YAClC8mB,IAAAA,oBAAW,EAAC;gBACVhnB;gBACAinB,MAAM;gBACNC,YAAYzoB;gBACZ5I,SAASqK,aAAarK,OAAO;gBAC7BsxB,gBAAgBrnB;gBAChBsnB,MAAM;YACR;QACF;IACF;AACF"}