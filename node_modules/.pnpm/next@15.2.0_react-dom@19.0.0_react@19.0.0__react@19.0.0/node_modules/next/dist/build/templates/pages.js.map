{"version": 3, "sources": ["../../../src/build/templates/pages.ts"], "sourcesContent": ["import { PagesRouteModule } from '../../server/route-modules/pages/module.compiled'\nimport { RouteKind } from '../../server/route-kind'\nimport { hoist } from './helpers'\n\n// Import the app and document modules.\nimport * as document from 'VAR_MODULE_DOCUMENT'\nimport * as app from 'VAR_MODULE_APP'\n\n// Import the userland code.\nimport * as userland from 'VAR_USERLAND'\n\n// Re-export the component (should be the default export).\nexport default hoist(userland, 'default')\n\n// Re-export methods.\nexport const getStaticProps = hoist(userland, 'getStaticProps')\nexport const getStaticPaths = hoist(userland, 'getStaticPaths')\nexport const getServerSideProps = hoist(userland, 'getServerSideProps')\nexport const config = hoist(userland, 'config')\nexport const reportWebVitals = hoist(userland, 'reportWebVitals')\n\n// Re-export legacy methods.\nexport const unstable_getStaticProps = hoist(\n  userland,\n  'unstable_getStaticProps'\n)\nexport const unstable_getStaticPaths = hoist(\n  userland,\n  'unstable_getStaticPaths'\n)\nexport const unstable_getStaticParams = hoist(\n  userland,\n  'unstable_getStaticParams'\n)\nexport const unstable_getServerProps = hoist(\n  userland,\n  'unstable_getServerProps'\n)\nexport const unstable_getServerSideProps = hoist(\n  userland,\n  'unstable_getServerSideProps'\n)\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new PagesRouteModule({\n  definition: {\n    kind: RouteKind.PAGES,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n  },\n  components: {\n    // default export might not exist when optimized for data only\n    App: app.default,\n    Document: document.default,\n  },\n  userland,\n})\n"], "names": ["config", "getServerSideProps", "getStaticPaths", "getStaticProps", "reportWebVitals", "routeModule", "unstable_getServerProps", "unstable_getServerSideProps", "unstable_getStaticParams", "unstable_getStaticPaths", "unstable_getStaticProps", "hoist", "userland", "PagesRouteModule", "definition", "kind", "RouteKind", "PAGES", "page", "pathname", "bundlePath", "filename", "components", "App", "app", "default", "Document", "document"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;IAkBaA,MAAM;eAANA;;IAPb,0DAA0D;IAC1D,OAAyC;eAAzC;;IAKaC,kBAAkB;eAAlBA;;IADAC,cAAc;eAAdA;;IADAC,cAAc;eAAdA;;IAIAC,eAAe;eAAfA;;IAyBAC,WAAW;eAAXA;;IAVAC,uBAAuB;eAAvBA;;IAIAC,2BAA2B;eAA3BA;;IARAC,wBAAwB;eAAxBA;;IAJAC,uBAAuB;eAAvBA;;IAJAC,uBAAuB;eAAvBA;;;gCAtBoB;2BACP;yBACJ;6EAGI;wEACL;sEAGK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAG1B,WAAeC,IAAAA,cAAK,EAACC,eAAU;AAGxB,MAAMT,iBAAiBQ,IAAAA,cAAK,EAACC,eAAU;AACvC,MAAMV,iBAAiBS,IAAAA,cAAK,EAACC,eAAU;AACvC,MAAMX,qBAAqBU,IAAAA,cAAK,EAACC,eAAU;AAC3C,MAAMZ,SAASW,IAAAA,cAAK,EAACC,eAAU;AAC/B,MAAMR,kBAAkBO,IAAAA,cAAK,EAACC,eAAU;AAGxC,MAAMF,0BAA0BC,IAAAA,cAAK,EAC1CC,eACA;AAEK,MAAMH,0BAA0BE,IAAAA,cAAK,EAC1CC,eACA;AAEK,MAAMJ,2BAA2BG,IAAAA,cAAK,EAC3CC,eACA;AAEK,MAAMN,0BAA0BK,IAAAA,cAAK,EAC1CC,eACA;AAEK,MAAML,8BAA8BI,IAAAA,cAAK,EAC9CC,eACA;AAIK,MAAMP,cAAc,IAAIQ,gCAAgB,CAAC;IAC9CC,YAAY;QACVC,MAAMC,oBAAS,CAACC,KAAK;QACrBC,MAAM;QACNC,UAAU;QACV,2CAA2C;QAC3CC,YAAY;QACZC,UAAU;IACZ;IACAC,YAAY;QACV,8DAA8D;QAC9DC,KAAKC,gBAAIC,OAAO;QAChBC,UAAUC,qBAASF,OAAO;IAC5B;IACAb,UAAAA;AACF"}