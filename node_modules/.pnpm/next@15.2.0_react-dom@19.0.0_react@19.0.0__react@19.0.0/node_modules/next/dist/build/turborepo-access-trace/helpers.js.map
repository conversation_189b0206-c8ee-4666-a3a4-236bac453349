{"version": 3, "sources": ["../../../src/build/turborepo-access-trace/helpers.ts"], "sourcesContent": ["import fs from 'fs/promises'\nimport path from 'path'\nimport type { FS, Addresses, EnvVars } from './types'\nimport { envProxy } from './env'\nimport { tcpProxy } from './tcp'\nimport { TurborepoAccessTraceResult } from './result'\n\n/**\n * Trace access to the filesystem (TODO), environment variables, and TCP addresses and\n * merge the results into the parent `TurborepoAccessTraceResult`.\n *\n * @param f the function to trace\n * @param parent the `TurborepoAccessTraceResult` to merge the results into\n * @returns the result of the function\n */\nexport function turborepoTraceAccess<T>(\n  f: () => T | Promise<T>,\n  parent: TurborepoAccessTraceResult\n): Promise<T> | T {\n  // If the trace file is not set, don't trace and instead just call the\n  // function.\n  if (!process.env.TURBOREPO_TRACE_FILE) return f()\n\n  // Otherwise, trace the function and merge the results into the parent. Using\n  // `then` instead of `await` here to avoid creating a new async context when\n  // tracing is disabled.\n  return withTurborepoTraceAccess(f).then(([result, proxy]) => {\n    parent.merge(proxy)\n\n    // Return the result of the function.\n    return result\n  })\n}\n\n/**\n * Write the access trace to the trace file.\n *\n * @param distDir the directory to write the trace file to\n * @param traces an array of traces to merge and write to the trace file\n */\nexport async function writeTurborepoAccessTraceResult({\n  distDir,\n  traces,\n}: {\n  distDir: string\n  traces: Array<TurborepoAccessTraceResult>\n}) {\n  const configTraceFile = process.env.TURBOREPO_TRACE_FILE\n\n  if (!configTraceFile || traces.length === 0) return\n\n  // merge traces\n  const [accessTrace, ...otherTraces] = traces\n  for (const trace of otherTraces) {\n    accessTrace.merge(trace)\n  }\n\n  try {\n    // make sure the directory exists\n    await fs.mkdir(path.dirname(configTraceFile), { recursive: true })\n    await fs.writeFile(\n      configTraceFile,\n      JSON.stringify({\n        outputs: [`${distDir}/**`, `!${distDir}/cache/**`],\n        accessed: accessTrace.toPublicTrace(),\n      })\n    )\n  } catch (err) {\n    // if we can't write this file, we should bail out here to avoid\n    // the possibility of incorrect turborepo cache hits.\n    throw new Error(`Failed to write turborepo access trace file`, {\n      cause: err,\n    })\n  }\n}\n\nasync function withTurborepoTraceAccess<T>(\n  f: () => T | Promise<T>\n): Promise<[T, TurborepoAccessTraceResult]> {\n  const envVars: EnvVars = new Set([])\n  // addresses is an array of objects, so a set is useless\n  const addresses: Addresses = []\n  // TODO: watch fsPaths (removed from this implementation for now)\n  const fsPaths: FS = new Set<string>()\n\n  // setup proxies\n  const restoreTCP = tcpProxy(addresses)\n  const restoreEnv = envProxy(envVars)\n\n  let functionResult\n\n  // NOTE: we intentionally don't catch errors here so the calling function can handle them\n  try {\n    // call the wrapped function\n    functionResult = await f()\n  } finally {\n    // remove proxies\n    restoreTCP()\n    restoreEnv()\n  }\n\n  const traceResult = new TurborepoAccessTraceResult(\n    envVars,\n    addresses,\n    fsPaths\n  )\n\n  return [functionResult, traceResult]\n}\n"], "names": ["turborepoTraceAccess", "writeTurborepoAccessTraceResult", "f", "parent", "process", "env", "TURBOREPO_TRACE_FILE", "withTurborepoTraceAccess", "then", "result", "proxy", "merge", "distDir", "traces", "configTraceFile", "length", "accessTrace", "otherTraces", "trace", "fs", "mkdir", "path", "dirname", "recursive", "writeFile", "JSON", "stringify", "outputs", "accessed", "toPublicTrace", "err", "Error", "cause", "envVars", "Set", "addresses", "fsPaths", "restoreTCP", "tcpProxy", "restoreEnv", "envProxy", "functionResult", "traceResult", "TurborepoAccessTraceResult"], "mappings": ";;;;;;;;;;;;;;;IAegBA,oBAAoB;eAApBA;;IAyBMC,+BAA+B;eAA/BA;;;iEAxCP;6DACE;qBAEQ;qBACA;wBACkB;;;;;;AAUpC,SAASD,qBACdE,CAAuB,EACvBC,MAAkC;IAElC,sEAAsE;IACtE,YAAY;IACZ,IAAI,CAACC,QAAQC,GAAG,CAACC,oBAAoB,EAAE,OAAOJ;IAE9C,6EAA6E;IAC7E,4EAA4E;IAC5E,uBAAuB;IACvB,OAAOK,yBAAyBL,GAAGM,IAAI,CAAC,CAAC,CAACC,QAAQC,MAAM;QACtDP,OAAOQ,KAAK,CAACD;QAEb,qCAAqC;QACrC,OAAOD;IACT;AACF;AAQO,eAAeR,gCAAgC,EACpDW,OAAO,EACPC,MAAM,EAIP;IACC,MAAMC,kBAAkBV,QAAQC,GAAG,CAACC,oBAAoB;IAExD,IAAI,CAACQ,mBAAmBD,OAAOE,MAAM,KAAK,GAAG;IAE7C,eAAe;IACf,MAAM,CAACC,aAAa,GAAGC,YAAY,GAAGJ;IACtC,KAAK,MAAMK,SAASD,YAAa;QAC/BD,YAAYL,KAAK,CAACO;IACpB;IAEA,IAAI;QACF,iCAAiC;QACjC,MAAMC,iBAAE,CAACC,KAAK,CAACC,aAAI,CAACC,OAAO,CAACR,kBAAkB;YAAES,WAAW;QAAK;QAChE,MAAMJ,iBAAE,CAACK,SAAS,CAChBV,iBACAW,KAAKC,SAAS,CAAC;YACbC,SAAS;gBAAC,GAAGf,QAAQ,GAAG,CAAC;gBAAE,CAAC,CAAC,EAAEA,QAAQ,SAAS,CAAC;aAAC;YAClDgB,UAAUZ,YAAYa,aAAa;QACrC;IAEJ,EAAE,OAAOC,KAAK;QACZ,gEAAgE;QAChE,qDAAqD;QACrD,MAAM,qBAEJ,CAFI,IAAIC,MAAM,CAAC,2CAA2C,CAAC,EAAE;YAC7DC,OAAOF;QACT,IAFM,qBAAA;mBAAA;wBAAA;0BAAA;QAEL;IACH;AACF;AAEA,eAAevB,yBACbL,CAAuB;IAEvB,MAAM+B,UAAmB,IAAIC,IAAI,EAAE;IACnC,wDAAwD;IACxD,MAAMC,YAAuB,EAAE;IAC/B,iEAAiE;IACjE,MAAMC,UAAc,IAAIF;IAExB,gBAAgB;IAChB,MAAMG,aAAaC,IAAAA,aAAQ,EAACH;IAC5B,MAAMI,aAAaC,IAAAA,aAAQ,EAACP;IAE5B,IAAIQ;IAEJ,yFAAyF;IACzF,IAAI;QACF,4BAA4B;QAC5BA,iBAAiB,MAAMvC;IACzB,SAAU;QACR,iBAAiB;QACjBmC;QACAE;IACF;IAEA,MAAMG,cAAc,IAAIC,kCAA0B,CAChDV,SACAE,WACAC;IAGF,OAAO;QAACK;QAAgBC;KAAY;AACtC"}