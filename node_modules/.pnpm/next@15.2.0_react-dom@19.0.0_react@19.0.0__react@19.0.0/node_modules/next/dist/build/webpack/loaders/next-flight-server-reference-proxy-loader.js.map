{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-flight-server-reference-proxy-loader.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\n\n// This is a virtual proxy loader that takes a Server Reference ID and a name,\n// creates a module that just re-exports the reference as that name.\n\nconst flightServerReferenceProxyLoader: webpack.LoaderDefinitionFunction<{\n  id: string\n  name: string\n}> = function transformSource(this) {\n  const { id, name } = this.getOptions()\n\n  // Both the import and the `createServerReference` call are marked as side\n  // effect free:\n  // - private-next-rsc-action-client-wrapper is matched as `sideEffects: false` in\n  //   the Webpack loader\n  // - createServerReference is marked as /*#__PURE__*/\n  //\n  // Because of that, Webpack is able to concatenate the modules and inline the\n  // reference IDs recursively directly into the module that uses them.\n  return `\\\nimport { createServerReference, callServer, findSourceMapURL } from 'private-next-rsc-action-client-wrapper'\nexport ${\n    name === 'default' ? 'default' : `const ${name} =`\n  } /*#__PURE__*/createServerReference(${JSON.stringify(id)}, callServer, undefined, findSourceMapURL, ${JSON.stringify(name)})`\n}\n\nexport default flightServerReferenceProxyLoader\n"], "names": ["flightServerReferenceProxyLoader", "transformSource", "id", "name", "getOptions", "JSON", "stringify"], "mappings": ";;;;+BA0BA;;;eAAA;;;AAxBA,8EAA8E;AAC9E,oEAAoE;AAEpE,MAAMA,mCAGD,SAASC;IACZ,MAAM,EAAEC,EAAE,EAAEC,IAAI,EAAE,GAAG,IAAI,CAACC,UAAU;IAEpC,0EAA0E;IAC1E,eAAe;IACf,iFAAiF;IACjF,uBAAuB;IACvB,qDAAqD;IACrD,EAAE;IACF,6EAA6E;IAC7E,qEAAqE;IACrE,OAAO,CAAC;;OAEH,EACHD,SAAS,YAAY,YAAY,CAAC,MAAM,EAAEA,KAAK,EAAE,CAAC,CACnD,oCAAoC,EAAEE,KAAKC,SAAS,CAACJ,IAAI,2CAA2C,EAAEG,KAAKC,SAAS,CAACH,MAAM,CAAC,CAAC;AAChI;MAEA,WAAeH"}