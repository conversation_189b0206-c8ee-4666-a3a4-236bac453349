{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-style-loader/index.ts"], "sourcesContent": ["import path from 'path'\nimport { stringifyRequest } from '../../stringify-request'\n\nconst loaderApi = () => {}\n\nloaderApi.pitch = function loader(this: any, request: any): any {\n  const loaderSpan = this.currentTraceSpan.traceChild('next-style-loader')\n\n  return loaderSpan.traceFn(() => {\n    const options = this.getOptions()\n\n    const insert =\n      typeof options.insert === 'undefined'\n        ? '\"head\"'\n        : typeof options.insert === 'string'\n          ? JSON.stringify(options.insert)\n          : options.insert.toString()\n    const injectType = options.injectType || 'styleTag'\n    const esModule =\n      typeof options.esModule !== 'undefined' ? options.esModule : false\n\n    delete options.esModule\n\n    switch (injectType) {\n      case 'linkTag': {\n        const hmrCode = this.hot\n          ? `\nif (module.hot) {\n  module.hot.accept(\n    ${stringifyRequest(this, `!!${request}`)},\n    function() {\n     ${\n       esModule\n         ? 'update(content);'\n         : `content = require(${stringifyRequest(this, `!!${request}`)});\n\n           content = content.__esModule ? content.default : content;\n\n           update(content);`\n     }\n    }\n  );\n\n  module.hot.dispose(function() {\n    update();\n  });\n}`\n          : ''\n\n        return `${\n          esModule\n            ? `import api from ${stringifyRequest(\n                this,\n                `!${path.join(__dirname, 'runtime/injectStylesIntoLinkTag.js')}`\n              )};\n            import content from ${stringifyRequest(this, `!!${request}`)};`\n            : `var api = require(${stringifyRequest(\n                this,\n                `!${path.join(__dirname, 'runtime/injectStylesIntoLinkTag.js')}`\n              )});\n            var content = require(${stringifyRequest(this, `!!${request}`)});\n\n            content = content.__esModule ? content.default : content;`\n        }\n\nvar options = ${JSON.stringify(options)};\n\noptions.insert = ${insert};\n\nvar update = api(content, options);\n\n${hmrCode}\n\n${esModule ? 'export default {}' : ''}`\n      }\n\n      case 'lazyStyleTag':\n      case 'lazySingletonStyleTag': {\n        const isSingleton = injectType === 'lazySingletonStyleTag'\n\n        const hmrCode = this.hot\n          ? `\nif (module.hot) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = ${require('./runtime/isEqualLocals').toString()};\n    console.log({isEqualLocals})\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      ${stringifyRequest(this, `!!${request}`)},\n      function () {\n        ${\n          esModule\n            ? `if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              if (update && refs > 0) {\n                update(content);\n              }`\n            : `content = require(${stringifyRequest(this, `!!${request}`)});\n\n              content = content.__esModule ? content.default : content;\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              if (update && refs > 0) {\n                update(content);\n              }`\n        }\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    if (update) {\n      update();\n    }\n  });\n}`\n          : ''\n\n        return `${\n          esModule\n            ? `import api from ${stringifyRequest(\n                this,\n                `!${path.join(\n                  __dirname,\n                  'runtime/injectStylesIntoStyleTag.js'\n                )}`\n              )};\n            import content from ${stringifyRequest(this, `!!${request}`)};`\n            : `var api = require(${stringifyRequest(\n                this,\n                `!${path.join(\n                  __dirname,\n                  'runtime/injectStylesIntoStyleTag.js'\n                )}`\n              )});\n            var content = require(${stringifyRequest(this, `!!${request}`)});\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }`\n        }\n\nvar refs = 0;\nvar update;\nvar options = ${JSON.stringify(options)};\n\noptions.insert = ${insert};\noptions.singleton = ${isSingleton};\n\nvar exported = {};\n\nexported.locals = content.locals || {};\nexported.use = function() {\n  if (!(refs++)) {\n    update = api(content, options);\n  }\n\n  return exported;\n};\nexported.unuse = function() {\n  if (refs > 0 && !--refs) {\n    update();\n    update = null;\n  }\n};\n\n${hmrCode}\n\n${esModule ? 'export default' : 'module.exports ='} exported;`\n      }\n\n      case 'styleTag':\n      case 'singletonStyleTag':\n      default: {\n        const isSingleton = injectType === 'singletonStyleTag'\n\n        const hmrCode = this.hot\n          ? `\nif (module.hot) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = ${require('./runtime/isEqualLocals').toString()};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      ${stringifyRequest(this, `!!${request}`)},\n      function () {\n        ${\n          esModule\n            ? `if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);`\n            : `content = require(${stringifyRequest(this, `!!${request}`)});\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);`\n        }\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}`\n          : ''\n\n        return `${\n          esModule\n            ? `import api from ${stringifyRequest(\n                this,\n                `!${path.join(\n                  __dirname,\n                  'runtime/injectStylesIntoStyleTag.js'\n                )}`\n              )};\n            import content from ${stringifyRequest(this, `!!${request}`)};`\n            : `var api = require(${stringifyRequest(\n                this,\n                `!${path.join(\n                  __dirname,\n                  'runtime/injectStylesIntoStyleTag.js'\n                )}`\n              )});\n            var content = require(${stringifyRequest(this, `!!${request}`)});\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }`\n        }\n\nvar options = ${JSON.stringify(options)};\n\noptions.insert = ${insert};\noptions.singleton = ${isSingleton};\n\nvar update = api(content, options);\n\n${hmrCode}\n\n${esModule ? 'export default' : 'module.exports ='} content.locals || {};`\n      }\n    }\n  })\n}\n\nmodule.exports = loaderApi\n"], "names": ["loaderApi", "pitch", "loader", "request", "loaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "options", "getOptions", "insert", "JSON", "stringify", "toString", "injectType", "esModule", "hmrCode", "hot", "stringifyRequest", "path", "join", "__dirname", "isSingleton", "require", "module", "exports"], "mappings": ";;;;6DAAiB;kCACgB;;;;;;AAEjC,MAAMA,YAAY,KAAO;AAEzBA,UAAUC,KAAK,GAAG,SAASC,OAAkBC,OAAY;IACvD,MAAMC,aAAa,IAAI,CAACC,gBAAgB,CAACC,UAAU,CAAC;IAEpD,OAAOF,WAAWG,OAAO,CAAC;QACxB,MAAMC,UAAU,IAAI,CAACC,UAAU;QAE/B,MAAMC,SACJ,OAAOF,QAAQE,MAAM,KAAK,cACtB,WACA,OAAOF,QAAQE,MAAM,KAAK,WACxBC,KAAKC,SAAS,CAACJ,QAAQE,MAAM,IAC7BF,QAAQE,MAAM,CAACG,QAAQ;QAC/B,MAAMC,aAAaN,QAAQM,UAAU,IAAI;QACzC,MAAMC,WACJ,OAAOP,QAAQO,QAAQ,KAAK,cAAcP,QAAQO,QAAQ,GAAG;QAE/D,OAAOP,QAAQO,QAAQ;QAEvB,OAAQD;YACN,KAAK;gBAAW;oBACd,MAAME,UAAU,IAAI,CAACC,GAAG,GACpB,CAAC;;;IAGT,EAAEC,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,SAAS,EAAE;;KAExC,EACEY,WACI,qBACA,CAAC,kBAAkB,EAAEG,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,SAAS,EAAE;;;;2BAI5C,CAAC,CACtB;;;;;;;CAOL,CAAC,GACU;oBAEJ,OAAO,GACLY,WACI,CAAC,gBAAgB,EAAEG,IAAAA,kCAAgB,EACjC,IAAI,EACJ,CAAC,CAAC,EAAEC,aAAI,CAACC,IAAI,CAACC,WAAW,uCAAuC,EAChE;gCACgB,EAAEH,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,SAAS,EAAE,CAAC,CAAC,GAC7D,CAAC,kBAAkB,EAAEe,IAAAA,kCAAgB,EACnC,IAAI,EACJ,CAAC,CAAC,EAAEC,aAAI,CAACC,IAAI,CAACC,WAAW,uCAAuC,EAChE;kCACkB,EAAEH,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,SAAS,EAAE;;qEAEN,CAAC,CAC7D;;cAEK,EAAEQ,KAAKC,SAAS,CAACJ,SAAS;;iBAEvB,EAAEE,OAAO;;;;AAI1B,EAAEM,QAAQ;;AAEV,EAAED,WAAW,sBAAsB,IAAI;gBACjC;YAEA,KAAK;YACL,KAAK;gBAAyB;oBAC5B,MAAMO,cAAcR,eAAe;oBAEnC,MAAME,UAAU,IAAI,CAACC,GAAG,GACpB,CAAC;;;wBAGW,EAAEM,QAAQ,2BAA2BV,QAAQ,GAAG;;;;;MAKlE,EAAEK,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,SAAS,EAAE;;QAEvC,EACEY,WACI,CAAC;;;;;;;;;;eAUA,CAAC,GACF,CAAC,kBAAkB,EAAEG,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,SAAS,EAAE;;;;;;;;;;;;;;eAc3D,CAAC,CACP;;;;;;;;;;CAUR,CAAC,GACU;oBAEJ,OAAO,GACLY,WACI,CAAC,gBAAgB,EAAEG,IAAAA,kCAAgB,EACjC,IAAI,EACJ,CAAC,CAAC,EAAEC,aAAI,CAACC,IAAI,CACXC,WACA,wCACC,EACH;gCACgB,EAAEH,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,SAAS,EAAE,CAAC,CAAC,GAC7D,CAAC,kBAAkB,EAAEe,IAAAA,kCAAgB,EACnC,IAAI,EACJ,CAAC,CAAC,EAAEC,aAAI,CAACC,IAAI,CACXC,WACA,wCACC,EACH;kCACkB,EAAEH,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,SAAS,EAAE;;;;;;aAM9D,CAAC,CACL;;;;cAIK,EAAEQ,KAAKC,SAAS,CAACJ,SAAS;;iBAEvB,EAAEE,OAAO;oBACN,EAAEY,YAAY;;;;;;;;;;;;;;;;;;;AAmBlC,EAAEN,QAAQ;;AAEV,EAAED,WAAW,mBAAmB,mBAAmB,UAAU,CAAC;gBACxD;YAEA,KAAK;YACL,KAAK;YACL;gBAAS;oBACP,MAAMO,cAAcR,eAAe;oBAEnC,MAAME,UAAU,IAAI,CAACC,GAAG,GACpB,CAAC;;;wBAGW,EAAEM,QAAQ,2BAA2BV,QAAQ,GAAG;;;;MAIlE,EAAEK,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,SAAS,EAAE;;QAEvC,EACEY,WACI,CAAC;;;;;;;;8BAQe,CAAC,GACjB,CAAC,kBAAkB,EAAEG,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,SAAS,EAAE;;;;;;;;;;;;;;;;8BAgB5C,CAAC,CACtB;;;;;;;;CAQR,CAAC,GACU;oBAEJ,OAAO,GACLY,WACI,CAAC,gBAAgB,EAAEG,IAAAA,kCAAgB,EACjC,IAAI,EACJ,CAAC,CAAC,EAAEC,aAAI,CAACC,IAAI,CACXC,WACA,wCACC,EACH;gCACgB,EAAEH,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,SAAS,EAAE,CAAC,CAAC,GAC7D,CAAC,kBAAkB,EAAEe,IAAAA,kCAAgB,EACnC,IAAI,EACJ,CAAC,CAAC,EAAEC,aAAI,CAACC,IAAI,CACXC,WACA,wCACC,EACH;kCACkB,EAAEH,IAAAA,kCAAgB,EAAC,IAAI,EAAE,CAAC,EAAE,EAAEf,SAAS,EAAE;;;;;;aAM9D,CAAC,CACL;;cAEK,EAAEQ,KAAKC,SAAS,CAACJ,SAAS;;iBAEvB,EAAEE,OAAO;oBACN,EAAEY,YAAY;;;;AAIlC,EAAEN,QAAQ;;AAEV,EAAED,WAAW,mBAAmB,mBAAmB,sBAAsB,CAAC;gBACpE;QACF;IACF;AACF;AAEAS,OAAOC,OAAO,GAAGzB"}