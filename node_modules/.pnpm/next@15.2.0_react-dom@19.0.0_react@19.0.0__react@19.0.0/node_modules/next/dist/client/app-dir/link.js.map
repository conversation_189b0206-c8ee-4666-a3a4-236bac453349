{"version": 3, "sources": ["../../../src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport type { NextRouter } from '../../shared/lib/router/router'\n\nimport React from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport {\n  type PrefetchTask,\n  schedulePrefetchTask as scheduleSegmentPrefetchTask,\n  cancelPrefetchTask,\n  bumpPrefetchTask,\n  PrefetchPriority,\n} from '../components/segment-cache/scheduler'\nimport { getCurrentAppRouterState } from '../../shared/lib/router/action-queue'\nimport { createCacheKey } from '../components/segment-cache/cache-key'\nimport { createPrefetchURL } from '../components/app-router'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport { getCurrentCacheVersion } from '../components/segment-cache/cache'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\ntype LinkInstance = {\n  router: AppRouterInstance\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL\n  prefetchHref: string\n\n  isVisible: boolean\n  wasHoveredOrTouched: boolean\n\n  // The most recently initiated prefetch task. It may or may not have\n  // already completed.  The same prefetch task object can be reused across\n  // multiple prefetches of the same link.\n  prefetchTask: PrefetchTask | null\n\n  // The cache version at the time the task was initiated. This is used to\n  // determine if the cache was invalidated since the task was initiated.\n  cacheVersion: number\n}\n\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst links:\n  | WeakMap<HTMLAnchorElement | SVGAElement, LinkInstance>\n  | Map<Element, LinkInstance> =\n  typeof WeakMap === 'function' ? new WeakMap() : new Map()\n\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst visibleLinks: Set<LinkInstance> = new Set()\n\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer: IntersectionObserver | null =\n  typeof IntersectionObserver === 'function'\n    ? new IntersectionObserver(handleIntersect, {\n        rootMargin: '200px',\n      })\n    : null\n\nfunction mountLinkInstance(\n  element: HTMLAnchorElement | SVGAElement,\n  href: string,\n  router: AppRouterInstance,\n  kind: PrefetchKind.AUTO | PrefetchKind.FULL\n) {\n  let prefetchUrl: URL | null = null\n  try {\n    prefetchUrl = createPrefetchURL(href)\n    if (prefetchUrl === null) {\n      // We only track the link if it's prefetchable. For example, this excludes\n      // links to external URLs.\n      return\n    }\n  } catch {\n    // createPrefetchURL sometimes throws an error if an invalid URL is\n    // provided, though I'm not sure if it's actually necessary.\n    // TODO: Consider removing the throw from the inner function, or change it\n    // to reportError. Or maybe the error isn't even necessary for automatic\n    // prefetches, just navigations.\n    const reportErrorFn =\n      typeof reportError === 'function' ? reportError : console.error\n    reportErrorFn(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n    return\n  }\n\n  const instance: LinkInstance = {\n    prefetchHref: prefetchUrl.href,\n    router,\n    kind,\n    isVisible: false,\n    wasHoveredOrTouched: false,\n    prefetchTask: null,\n    cacheVersion: -1,\n  }\n  const existingInstance = links.get(element)\n  if (existingInstance !== undefined) {\n    // This shouldn't happen because each <Link> component should have its own\n    // anchor tag instance, but it's defensive coding to avoid a memory leak in\n    // case there's a logical error somewhere else.\n    unmountLinkInstance(element)\n  }\n  links.set(element, instance)\n  if (observer !== null) {\n    observer.observe(element)\n  }\n}\n\nfunction unmountLinkInstance(element: HTMLAnchorElement | SVGAElement) {\n  const instance = links.get(element)\n  if (instance !== undefined) {\n    links.delete(element)\n    visibleLinks.delete(instance)\n    const prefetchTask = instance.prefetchTask\n    if (prefetchTask !== null) {\n      cancelPrefetchTask(prefetchTask)\n    }\n  }\n  if (observer !== null) {\n    observer.unobserve(element)\n  }\n}\n\nfunction handleIntersect(entries: Array<IntersectionObserverEntry>) {\n  for (const entry of entries) {\n    // Some extremely old browsers or polyfills don't reliably support\n    // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n    // really. But whatever this is fine.)\n    const isVisible = entry.intersectionRatio > 0\n    onLinkVisibilityChanged(entry.target as HTMLAnchorElement, isVisible)\n  }\n}\n\nfunction onLinkVisibilityChanged(\n  element: HTMLAnchorElement | SVGAElement,\n  isVisible: boolean\n) {\n  if (process.env.NODE_ENV !== 'production') {\n    // Prefetching on viewport is disabled in development for performance\n    // reasons, because it requires compiling the target page.\n    // TODO: Investigate re-enabling this.\n    return\n  }\n\n  const instance = links.get(element)\n  if (instance === undefined) {\n    return\n  }\n\n  instance.isVisible = isVisible\n  if (isVisible) {\n    visibleLinks.add(instance)\n  } else {\n    visibleLinks.delete(instance)\n  }\n  rescheduleLinkPrefetch(instance)\n}\n\nfunction onNavigationIntent(element: HTMLAnchorElement | SVGAElement) {\n  const instance = links.get(element)\n  if (instance === undefined) {\n    return\n  }\n  // Prefetch the link on hover/touchstart.\n  if (instance !== undefined) {\n    instance.wasHoveredOrTouched = true\n    rescheduleLinkPrefetch(instance)\n  }\n}\n\nfunction rescheduleLinkPrefetch(instance: LinkInstance) {\n  const existingPrefetchTask = instance.prefetchTask\n\n  if (!instance.isVisible) {\n    // Cancel any in-progress prefetch task. (If it already finished then this\n    // is a no-op.)\n    if (existingPrefetchTask !== null) {\n      cancelPrefetchTask(existingPrefetchTask)\n    }\n    // We don't need to reset the prefetchTask to null upon cancellation; an\n    // old task object can be rescheduled with bumpPrefetchTask. This is a\n    // micro-optimization but also makes the code simpler (don't need to\n    // worry about whether an old task object is stale).\n    return\n  }\n\n  if (!process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n    // The old prefetch implementation does not have different priority levels.\n    // Just schedule a new prefetch task.\n    prefetchWithOldCacheImplementation(instance)\n    return\n  }\n\n  // In the Segment Cache implementation, we assign a higher priority level to\n  // links that were at one point hovered or touched. Since the queue is last-\n  // in-first-out, the highest priority Link is whichever one was hovered last.\n  //\n  // We also increase the relative priority of links whenever they re-enter the\n  // viewport, as if they were being scheduled for the first time.\n  const priority = instance.wasHoveredOrTouched\n    ? PrefetchPriority.Intent\n    : PrefetchPriority.Default\n  if (existingPrefetchTask === null) {\n    // Initiate a prefetch task.\n    const appRouterState = getCurrentAppRouterState()\n    if (appRouterState !== null) {\n      const nextUrl = appRouterState.nextUrl\n      const treeAtTimeOfPrefetch = appRouterState.tree\n      const cacheKey = createCacheKey(instance.prefetchHref, nextUrl)\n      instance.prefetchTask = scheduleSegmentPrefetchTask(\n        cacheKey,\n        treeAtTimeOfPrefetch,\n        instance.kind === PrefetchKind.FULL,\n        priority\n      )\n      instance.cacheVersion = getCurrentCacheVersion()\n    }\n  } else {\n    // We already have an old task object that we can reschedule. This is\n    // effectively the same as canceling the old task and creating a new one.\n    bumpPrefetchTask(existingPrefetchTask, priority)\n  }\n}\n\nexport function pingVisibleLinks(\n  nextUrl: string | null,\n  tree: FlightRouterState\n) {\n  // For each currently visible link, cancel the existing prefetch task (if it\n  // exists) and schedule a new one. This is effectively the same as if all the\n  // visible links left and then re-entered the viewport.\n  //\n  // This is called when the Next-Url or the base tree changes, since those\n  // may affect the result of a prefetch task. It's also called after a\n  // cache invalidation.\n  const currentCacheVersion = getCurrentCacheVersion()\n  for (const instance of visibleLinks) {\n    const task = instance.prefetchTask\n    if (\n      task !== null &&\n      instance.cacheVersion === currentCacheVersion &&\n      task.key.nextUrl === nextUrl &&\n      task.treeAtTimeOfPrefetch === tree\n    ) {\n      // The cache has not been invalidated, and none of the inputs have\n      // changed. Bail out.\n      continue\n    }\n    // Something changed. Cancel the existing prefetch task and schedule a\n    // new one.\n    if (task !== null) {\n      cancelPrefetchTask(task)\n    }\n    const cacheKey = createCacheKey(instance.prefetchHref, nextUrl)\n    const priority = instance.wasHoveredOrTouched\n      ? PrefetchPriority.Intent\n      : PrefetchPriority.Default\n    instance.prefetchTask = scheduleSegmentPrefetchTask(\n      cacheKey,\n      tree,\n      instance.kind === PrefetchKind.FULL,\n      priority\n    )\n    instance.cacheVersion = getCurrentCacheVersion()\n  }\n}\n\nfunction prefetchWithOldCacheImplementation(instance: LinkInstance) {\n  // This is the path used when the Segment Cache is not enabled.\n  if (typeof window === 'undefined') {\n    return\n  }\n\n  const doPrefetch = async () => {\n    // note that `appRouter.prefetch()` is currently sync,\n    // so we have to wrap this call in an async function to be able to catch() errors below.\n    return instance.router.prefetch(instance.prefetchHref, {\n      kind: instance.kind,\n    })\n  }\n\n  // Prefetch the page if asked (only in the client)\n  // We need to handle a prefetch error here since we may be\n  // loading with priority which can reject but we don't\n  // want to force navigation since this is only a prefetch\n  doPrefetch().catch((err) => {\n    if (process.env.NODE_ENV !== 'production') {\n      // rethrow to show invalid URL errors\n      throw err\n    }\n  })\n}\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  router: NextRouter | AppRouterInstance,\n  href: string,\n  as: string,\n  replace?: boolean,\n  shallow?: boolean,\n  scroll?: boolean\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (isAnchorNodeName && isModifiedEvent(e)) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    // If the router is an NextRouter instance it will have `beforePopState`\n    const routerScroll = scroll ?? true\n    if ('beforePopState' in router) {\n      router[replace ? 'replace' : 'push'](href, as, {\n        shallow,\n        scroll: routerScroll,\n      })\n    } else {\n      router[replace ? 'replace' : 'push'](as || href, {\n        scroll: routerScroll,\n      })\n    }\n  }\n\n  React.startTransition(navigate)\n}\n\ntype LinkPropsReal = React.PropsWithChildren<\n  Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof LinkProps> &\n    LinkProps\n>\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nconst Link = React.forwardRef<HTMLAnchorElement, LinkPropsReal>(\n  function LinkComponent(props, forwardedRef) {\n    let children: React.ReactNode\n\n    const {\n      href: hrefProp,\n      as: asProp,\n      children: childrenProp,\n      prefetch: prefetchProp = null,\n      passHref,\n      replace,\n      shallow,\n      scroll,\n      onClick,\n      onMouseEnter: onMouseEnterProp,\n      onTouchStart: onTouchStartProp,\n      legacyBehavior = false,\n      ...restProps\n    } = props\n\n    children = childrenProp\n\n    if (\n      legacyBehavior &&\n      (typeof children === 'string' || typeof children === 'number')\n    ) {\n      children = <a>{children}</a>\n    }\n\n    const router = React.useContext(AppRouterContext)\n\n    const prefetchEnabled = prefetchProp !== false\n    /**\n     * The possible states for prefetch are:\n     * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n     * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n     * - false: we will not prefetch if in the viewport at all\n     */\n    const appPrefetchKind =\n      prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n    if (process.env.NODE_ENV !== 'production') {\n      function createPropError(args: {\n        key: string\n        expected: string\n        actual: string\n      }) {\n        return new Error(\n          `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n            (typeof window !== 'undefined'\n              ? \"\\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n\n      // TypeScript trick for type-guarding:\n      const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n        href: true,\n      } as const\n      const requiredProps: LinkPropsRequired[] = Object.keys(\n        requiredPropsGuard\n      ) as LinkPropsRequired[]\n      requiredProps.forEach((key: LinkPropsRequired) => {\n        if (key === 'href') {\n          if (\n            props[key] == null ||\n            (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n          ) {\n            throw createPropError({\n              key,\n              expected: '`string` or `object`',\n              actual: props[key] === null ? 'null' : typeof props[key],\n            })\n          }\n        } else {\n          // TypeScript trick for type-guarding:\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          const _: never = key\n        }\n      })\n\n      // TypeScript trick for type-guarding:\n      const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n        as: true,\n        replace: true,\n        scroll: true,\n        shallow: true,\n        passHref: true,\n        prefetch: true,\n        onClick: true,\n        onMouseEnter: true,\n        onTouchStart: true,\n        legacyBehavior: true,\n      } as const\n      const optionalProps: LinkPropsOptional[] = Object.keys(\n        optionalPropsGuard\n      ) as LinkPropsOptional[]\n      optionalProps.forEach((key: LinkPropsOptional) => {\n        const valType = typeof props[key]\n\n        if (key === 'as') {\n          if (props[key] && valType !== 'string' && valType !== 'object') {\n            throw createPropError({\n              key,\n              expected: '`string` or `object`',\n              actual: valType,\n            })\n          }\n        } else if (\n          key === 'onClick' ||\n          key === 'onMouseEnter' ||\n          key === 'onTouchStart'\n        ) {\n          if (props[key] && valType !== 'function') {\n            throw createPropError({\n              key,\n              expected: '`function`',\n              actual: valType,\n            })\n          }\n        } else if (\n          key === 'replace' ||\n          key === 'scroll' ||\n          key === 'shallow' ||\n          key === 'passHref' ||\n          key === 'prefetch' ||\n          key === 'legacyBehavior'\n        ) {\n          if (props[key] != null && valType !== 'boolean') {\n            throw createPropError({\n              key,\n              expected: '`boolean`',\n              actual: valType,\n            })\n          }\n        } else {\n          // TypeScript trick for type-guarding:\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          const _: never = key\n        }\n      })\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (props.locale) {\n        warnOnce(\n          'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n        )\n      }\n      if (!asProp) {\n        let href: string | undefined\n        if (typeof hrefProp === 'string') {\n          href = hrefProp\n        } else if (\n          typeof hrefProp === 'object' &&\n          typeof hrefProp.pathname === 'string'\n        ) {\n          href = hrefProp.pathname\n        }\n\n        if (href) {\n          const hasDynamicSegment = href\n            .split('/')\n            .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n          if (hasDynamicSegment) {\n            throw new Error(\n              `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n            )\n          }\n        }\n      }\n    }\n\n    const { href, as } = React.useMemo(() => {\n      const resolvedHref = formatStringOrUrl(hrefProp)\n      return {\n        href: resolvedHref,\n        as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n      }\n    }, [hrefProp, asProp])\n\n    // This will return the first child, if multiple are provided it will throw an error\n    let child: any\n    if (legacyBehavior) {\n      if (process.env.NODE_ENV === 'development') {\n        if (onClick) {\n          console.warn(\n            `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n          )\n        }\n        if (onMouseEnterProp) {\n          console.warn(\n            `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n          )\n        }\n        try {\n          child = React.Children.only(children)\n        } catch (err) {\n          if (!children) {\n            throw new Error(\n              `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n            )\n          }\n          throw new Error(\n            `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n              (typeof window !== 'undefined'\n                ? \" \\nOpen your browser's console to view the Component stack trace.\"\n                : '')\n          )\n        }\n      } else {\n        child = React.Children.only(children)\n      }\n    } else {\n      if (process.env.NODE_ENV === 'development') {\n        if ((children as any)?.type === 'a') {\n          throw new Error(\n            'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n          )\n        }\n      }\n    }\n\n    const childRef: any = legacyBehavior\n      ? child && typeof child === 'object' && child.ref\n      : forwardedRef\n\n    // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n    // mount. In the future we will also use this to keep track of all the\n    // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n    // a revalidation or refresh.\n    const observeLinkVisibilityOnMount = React.useCallback(\n      (element: HTMLAnchorElement | SVGAElement) => {\n        if (prefetchEnabled && router !== null) {\n          mountLinkInstance(element, href, router, appPrefetchKind)\n        }\n        return () => {\n          unmountLinkInstance(element)\n        }\n      },\n      [prefetchEnabled, href, router, appPrefetchKind]\n    )\n\n    const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n    const childProps: {\n      onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n      onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n      onClick: React.MouseEventHandler<HTMLAnchorElement>\n      href?: string\n      ref?: any\n    } = {\n      ref: mergedRef,\n      onClick(e) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (!e) {\n            throw new Error(\n              `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n            )\n          }\n        }\n\n        if (!legacyBehavior && typeof onClick === 'function') {\n          onClick(e)\n        }\n\n        if (\n          legacyBehavior &&\n          child.props &&\n          typeof child.props.onClick === 'function'\n        ) {\n          child.props.onClick(e)\n        }\n\n        if (!router) {\n          return\n        }\n\n        if (e.defaultPrevented) {\n          return\n        }\n\n        linkClicked(e, router, href, as, replace, shallow, scroll)\n      },\n      onMouseEnter(e) {\n        if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n          onMouseEnterProp(e)\n        }\n\n        if (\n          legacyBehavior &&\n          child.props &&\n          typeof child.props.onMouseEnter === 'function'\n        ) {\n          child.props.onMouseEnter(e)\n        }\n\n        if (!router) {\n          return\n        }\n\n        if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n          return\n        }\n\n        onNavigationIntent(e.currentTarget as HTMLAnchorElement | SVGAElement)\n      },\n      onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n        ? undefined\n        : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n              onTouchStartProp(e)\n            }\n\n            if (\n              legacyBehavior &&\n              child.props &&\n              typeof child.props.onTouchStart === 'function'\n            ) {\n              child.props.onTouchStart(e)\n            }\n\n            if (!router) {\n              return\n            }\n\n            if (!prefetchEnabled) {\n              return\n            }\n\n            onNavigationIntent(\n              e.currentTarget as HTMLAnchorElement | SVGAElement\n            )\n          },\n    }\n\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if (isAbsoluteUrl(as)) {\n      childProps.href = as\n    } else if (\n      !legacyBehavior ||\n      passHref ||\n      (child.type === 'a' && !('href' in child.props))\n    ) {\n      childProps.href = addBasePath(as)\n    }\n\n    return legacyBehavior ? (\n      React.cloneElement(child, childProps)\n    ) : (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n)\n\nexport default Link\n"], "names": ["pingVisibleLinks", "links", "WeakMap", "Map", "visibleLinks", "Set", "observer", "IntersectionObserver", "handleIntersect", "rootMargin", "mountLinkInstance", "element", "href", "router", "kind", "prefetchUrl", "createPrefetchURL", "reportErrorFn", "reportError", "console", "error", "instance", "prefetchHref", "isVisible", "wasHoveredOrTouched", "prefetchTask", "cacheVersion", "existingInstance", "get", "undefined", "unmountLinkInstance", "set", "observe", "delete", "cancelPrefetchTask", "unobserve", "entries", "entry", "intersectionRatio", "onLinkVisibilityChanged", "target", "process", "env", "NODE_ENV", "add", "rescheduleLinkPrefetch", "onNavigationIntent", "existingPrefetchTask", "__NEXT_CLIENT_SEGMENT_CACHE", "prefetchWithOldCacheImplementation", "priority", "PrefetchPriority", "Intent", "<PERSON><PERSON><PERSON>", "appRouterState", "getCurrentAppRouterState", "nextUrl", "treeAtTimeOfPrefetch", "tree", "cache<PERSON>ey", "createCacheKey", "scheduleSegmentPrefetchTask", "PrefetchKind", "FULL", "getCurrentCacheVersion", "bumpPrefetchTask", "currentCacheVersion", "task", "key", "window", "doPrefetch", "prefetch", "catch", "err", "isModifiedEvent", "event", "eventTarget", "currentTarget", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "as", "replace", "shallow", "scroll", "nodeName", "isAnchorNodeName", "toUpperCase", "preventDefault", "navigate", "routerScroll", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "Link", "forwardRef", "LinkComponent", "props", "forwardedRef", "children", "hrefProp", "asProp", "childrenProp", "prefetchProp", "passHref", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "restProps", "a", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "AUTO", "createPropError", "args", "Error", "expected", "actual", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "warn", "Children", "only", "type", "childRef", "ref", "observeLinkVisibilityOnMount", "useCallback", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "__NEXT_LINK_NO_TOUCH_START", "isAbsoluteUrl", "addBasePath", "cloneElement"], "mappings": "AAAA;;;;;;;;;;;;;;;;IA05BA,OAAmB;eAAnB;;IAxfgBA,gBAAgB;eAAhBA;;;;;gEA9ZE;2BAEQ;+CACO;oCAEJ;8BACA;uBACC;6BACF;0BACH;2BAOlB;6BACkC;0BACV;2BACG;uBAEK;AA8MvC,2EAA2E;AAC3E,mEAAmE;AACnE,MAAMC,QAGJ,OAAOC,YAAY,aAAa,IAAIA,YAAY,IAAIC;AAEtD,6EAA6E;AAC7E,4EAA4E;AAC5E,0EAA0E;AAC1E,iBAAiB;AACjB,MAAMC,eAAkC,IAAIC;AAE5C,0EAA0E;AAC1E,MAAMC,WACJ,OAAOC,yBAAyB,aAC5B,IAAIA,qBAAqBC,iBAAiB;IACxCC,YAAY;AACd,KACA;AAEN,SAASC,kBACPC,OAAwC,EACxCC,IAAY,EACZC,MAAyB,EACzBC,IAA2C;IAE3C,IAAIC,cAA0B;IAC9B,IAAI;QACFA,cAAcC,IAAAA,4BAAiB,EAACJ;QAChC,IAAIG,gBAAgB,MAAM;YACxB,0EAA0E;YAC1E,0BAA0B;YAC1B;QACF;IACF,EAAE,UAAM;QACN,mEAAmE;QACnE,4DAA4D;QAC5D,0EAA0E;QAC1E,wEAAwE;QACxE,gCAAgC;QAChC,MAAME,gBACJ,OAAOC,gBAAgB,aAAaA,cAAcC,QAAQC,KAAK;QACjEH,cACE,AAAC,sBAAmBL,OAAK;QAE3B;IACF;IAEA,MAAMS,WAAyB;QAC7BC,cAAcP,YAAYH,IAAI;QAC9BC;QACAC;QACAS,WAAW;QACXC,qBAAqB;QACrBC,cAAc;QACdC,cAAc,CAAC;IACjB;IACA,MAAMC,mBAAmB1B,MAAM2B,GAAG,CAACjB;IACnC,IAAIgB,qBAAqBE,WAAW;QAClC,0EAA0E;QAC1E,2EAA2E;QAC3E,+CAA+C;QAC/CC,oBAAoBnB;IACtB;IACAV,MAAM8B,GAAG,CAACpB,SAASU;IACnB,IAAIf,aAAa,MAAM;QACrBA,SAAS0B,OAAO,CAACrB;IACnB;AACF;AAEA,SAASmB,oBAAoBnB,OAAwC;IACnE,MAAMU,WAAWpB,MAAM2B,GAAG,CAACjB;IAC3B,IAAIU,aAAaQ,WAAW;QAC1B5B,MAAMgC,MAAM,CAACtB;QACbP,aAAa6B,MAAM,CAACZ;QACpB,MAAMI,eAAeJ,SAASI,YAAY;QAC1C,IAAIA,iBAAiB,MAAM;YACzBS,IAAAA,6BAAkB,EAACT;QACrB;IACF;IACA,IAAInB,aAAa,MAAM;QACrBA,SAAS6B,SAAS,CAACxB;IACrB;AACF;AAEA,SAASH,gBAAgB4B,OAAyC;IAChE,KAAK,MAAMC,SAASD,QAAS;QAC3B,kEAAkE;QAClE,yEAAyE;QACzE,sCAAsC;QACtC,MAAMb,YAAYc,MAAMC,iBAAiB,GAAG;QAC5CC,wBAAwBF,MAAMG,MAAM,EAAuBjB;IAC7D;AACF;AAEA,SAASgB,wBACP5B,OAAwC,EACxCY,SAAkB;IAElB,IAAIkB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,qEAAqE;QACrE,0DAA0D;QAC1D,sCAAsC;QACtC;IACF;IAEA,MAAMtB,WAAWpB,MAAM2B,GAAG,CAACjB;IAC3B,IAAIU,aAAaQ,WAAW;QAC1B;IACF;IAEAR,SAASE,SAAS,GAAGA;IACrB,IAAIA,WAAW;QACbnB,aAAawC,GAAG,CAACvB;IACnB,OAAO;QACLjB,aAAa6B,MAAM,CAACZ;IACtB;IACAwB,uBAAuBxB;AACzB;AAEA,SAASyB,mBAAmBnC,OAAwC;IAClE,MAAMU,WAAWpB,MAAM2B,GAAG,CAACjB;IAC3B,IAAIU,aAAaQ,WAAW;QAC1B;IACF;IACA,yCAAyC;IACzC,IAAIR,aAAaQ,WAAW;QAC1BR,SAASG,mBAAmB,GAAG;QAC/BqB,uBAAuBxB;IACzB;AACF;AAEA,SAASwB,uBAAuBxB,QAAsB;IACpD,MAAM0B,uBAAuB1B,SAASI,YAAY;IAElD,IAAI,CAACJ,SAASE,SAAS,EAAE;QACvB,0EAA0E;QAC1E,eAAe;QACf,IAAIwB,yBAAyB,MAAM;YACjCb,IAAAA,6BAAkB,EAACa;QACrB;QACA,wEAAwE;QACxE,sEAAsE;QACtE,oEAAoE;QACpE,oDAAoD;QACpD;IACF;IAEA,IAAI,CAACN,QAAQC,GAAG,CAACM,2BAA2B,EAAE;QAC5C,2EAA2E;QAC3E,qCAAqC;QACrCC,mCAAmC5B;QACnC;IACF;IAEA,4EAA4E;IAC5E,4EAA4E;IAC5E,6EAA6E;IAC7E,EAAE;IACF,6EAA6E;IAC7E,gEAAgE;IAChE,MAAM6B,WAAW7B,SAASG,mBAAmB,GACzC2B,2BAAgB,CAACC,MAAM,GACvBD,2BAAgB,CAACE,OAAO;IAC5B,IAAIN,yBAAyB,MAAM;QACjC,4BAA4B;QAC5B,MAAMO,iBAAiBC,IAAAA,qCAAwB;QAC/C,IAAID,mBAAmB,MAAM;YAC3B,MAAME,UAAUF,eAAeE,OAAO;YACtC,MAAMC,uBAAuBH,eAAeI,IAAI;YAChD,MAAMC,WAAWC,IAAAA,wBAAc,EAACvC,SAASC,YAAY,EAAEkC;YACvDnC,SAASI,YAAY,GAAGoC,IAAAA,+BAA2B,EACjDF,UACAF,sBACApC,SAASP,IAAI,KAAKgD,gCAAY,CAACC,IAAI,EACnCb;YAEF7B,SAASK,YAAY,GAAGsC,IAAAA,6BAAsB;QAChD;IACF,OAAO;QACL,qEAAqE;QACrE,yEAAyE;QACzEC,IAAAA,2BAAgB,EAAClB,sBAAsBG;IACzC;AACF;AAEO,SAASlD,iBACdwD,OAAsB,EACtBE,IAAuB;IAEvB,4EAA4E;IAC5E,6EAA6E;IAC7E,uDAAuD;IACvD,EAAE;IACF,yEAAyE;IACzE,qEAAqE;IACrE,sBAAsB;IACtB,MAAMQ,sBAAsBF,IAAAA,6BAAsB;IAClD,KAAK,MAAM3C,YAAYjB,aAAc;QACnC,MAAM+D,OAAO9C,SAASI,YAAY;QAClC,IACE0C,SAAS,QACT9C,SAASK,YAAY,KAAKwC,uBAC1BC,KAAKC,GAAG,CAACZ,OAAO,KAAKA,WACrBW,KAAKV,oBAAoB,KAAKC,MAC9B;YAGA;QACF;QACA,sEAAsE;QACtE,WAAW;QACX,IAAIS,SAAS,MAAM;YACjBjC,IAAAA,6BAAkB,EAACiC;QACrB;QACA,MAAMR,WAAWC,IAAAA,wBAAc,EAACvC,SAASC,YAAY,EAAEkC;QACvD,MAAMN,WAAW7B,SAASG,mBAAmB,GACzC2B,2BAAgB,CAACC,MAAM,GACvBD,2BAAgB,CAACE,OAAO;QAC5BhC,SAASI,YAAY,GAAGoC,IAAAA,+BAA2B,EACjDF,UACAD,MACArC,SAASP,IAAI,KAAKgD,gCAAY,CAACC,IAAI,EACnCb;QAEF7B,SAASK,YAAY,GAAGsC,IAAAA,6BAAsB;IAChD;AACF;AAEA,SAASf,mCAAmC5B,QAAsB;IAChE,+DAA+D;IAC/D,IAAI,OAAOgD,WAAW,aAAa;QACjC;IACF;IAEA,MAAMC,aAAa;QACjB,sDAAsD;QACtD,wFAAwF;QACxF,OAAOjD,SAASR,MAAM,CAAC0D,QAAQ,CAAClD,SAASC,YAAY,EAAE;YACrDR,MAAMO,SAASP,IAAI;QACrB;IACF;IAEA,kDAAkD;IAClD,0DAA0D;IAC1D,sDAAsD;IACtD,yDAAyD;IACzDwD,aAAaE,KAAK,CAAC,CAACC;QAClB,IAAIhC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,qCAAqC;YACrC,MAAM8B;QACR;IACF;AACF;AAEA,SAASC,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMrC,SAASoC,YAAYE,YAAY,CAAC;IACxC,OACE,AAACtC,UAAUA,WAAW,WACtBmC,MAAMI,OAAO,IACbJ,MAAMK,OAAO,IACbL,MAAMM,QAAQ,IACdN,MAAMO,MAAM,IAAI,6BAA6B;IAC5CP,MAAMQ,WAAW,IAAIR,MAAMQ,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBzE,MAAsC,EACtCD,IAAY,EACZ2E,EAAU,EACVC,OAAiB,EACjBC,OAAiB,EACjBC,MAAgB;IAEhB,MAAM,EAAEC,QAAQ,EAAE,GAAGL,EAAET,aAAa;IAEpC,kDAAkD;IAClD,MAAMe,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IAAID,oBAAoBlB,gBAAgBY,IAAI;QAC1C,8CAA8C;QAC9C;IACF;IAEAA,EAAEQ,cAAc;IAEhB,MAAMC,WAAW;QACf,wEAAwE;QACxE,MAAMC,eAAeN,iBAAAA,SAAU;QAC/B,IAAI,oBAAoB7E,QAAQ;YAC9BA,MAAM,CAAC2E,UAAU,YAAY,OAAO,CAAC5E,MAAM2E,IAAI;gBAC7CE;gBACAC,QAAQM;YACV;QACF,OAAO;YACLnF,MAAM,CAAC2E,UAAU,YAAY,OAAO,CAACD,MAAM3E,MAAM;gBAC/C8E,QAAQM;YACV;QACF;IACF;IAEAC,cAAK,CAACC,eAAe,CAACH;AACxB;AAOA,SAASI,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOC,IAAAA,oBAAS,EAACD;AACnB;AAEA;;;;;;;;;CASC,GACD,MAAME,qBAAOL,cAAK,CAACM,UAAU,CAC3B,SAASC,cAAcC,KAAK,EAAEC,YAAY;IACxC,IAAIC;IAEJ,MAAM,EACJ/F,MAAMgG,QAAQ,EACdrB,IAAIsB,MAAM,EACVF,UAAUG,YAAY,EACtBvC,UAAUwC,eAAe,IAAI,EAC7BC,QAAQ,EACRxB,OAAO,EACPC,OAAO,EACPC,MAAM,EACNuB,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtB,GAAGC,WACJ,GAAGd;IAEJE,WAAWG;IAEX,IACEQ,kBACC,CAAA,OAAOX,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,yBAAW,qBAACa;sBAAGb;;IACjB;IAEA,MAAM9F,SAASoF,cAAK,CAACwB,UAAU,CAACC,+CAAgB;IAEhD,MAAMC,kBAAkBZ,iBAAiB;IACzC;;;;;KAKC,GACD,MAAMa,kBACJb,iBAAiB,OAAOjD,gCAAY,CAAC+D,IAAI,GAAG/D,gCAAY,CAACC,IAAI;IAE/D,IAAItB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,SAASmF,gBAAgBC,IAIxB;YACC,OAAO,qBAKN,CALM,IAAIC,MACT,AAAC,iCAA+BD,KAAK3D,GAAG,GAAC,iBAAe2D,KAAKE,QAAQ,GAAC,4BAA4BF,KAAKG,MAAM,GAAC,eAC3G,CAAA,OAAO7D,WAAW,cACf,qEACA,EAAC,IAJF,qBAAA;uBAAA;4BAAA;8BAAA;YAKP;QACF;QAEA,sCAAsC;QACtC,MAAM8D,qBAAsD;YAC1DvH,MAAM;QACR;QACA,MAAMwH,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACnE;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACEqC,KAAK,CAACrC,IAAI,IAAI,QACb,OAAOqC,KAAK,CAACrC,IAAI,KAAK,YAAY,OAAOqC,KAAK,CAACrC,IAAI,KAAK,UACzD;oBACA,MAAM0D,gBAAgB;wBACpB1D;wBACA6D,UAAU;wBACVC,QAAQzB,KAAK,CAACrC,IAAI,KAAK,OAAO,SAAS,OAAOqC,KAAK,CAACrC,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMoE,IAAWpE;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMqE,qBAAsD;YAC1DlD,IAAI;YACJC,SAAS;YACTE,QAAQ;YACRD,SAAS;YACTuB,UAAU;YACVzC,UAAU;YACV0C,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;QAClB;QACA,MAAMoB,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACnE;YACrB,MAAMuE,UAAU,OAAOlC,KAAK,CAACrC,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAIqC,KAAK,CAACrC,IAAI,IAAIuE,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMb,gBAAgB;wBACpB1D;wBACA6D,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLvE,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,gBACR;gBACA,IAAIqC,KAAK,CAACrC,IAAI,IAAIuE,YAAY,YAAY;oBACxC,MAAMb,gBAAgB;wBACpB1D;wBACA6D,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLvE,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,kBACR;gBACA,IAAIqC,KAAK,CAACrC,IAAI,IAAI,QAAQuE,YAAY,WAAW;oBAC/C,MAAMb,gBAAgB;wBACpB1D;wBACA6D,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWpE;YACnB;QACF;IACF;IAEA,IAAI3B,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAI8D,MAAMmC,MAAM,EAAE;YAChBC,IAAAA,kBAAQ,EACN;QAEJ;QACA,IAAI,CAAChC,QAAQ;YACX,IAAIjG;YACJ,IAAI,OAAOgG,aAAa,UAAU;gBAChChG,OAAOgG;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAASkC,QAAQ,KAAK,UAC7B;gBACAlI,OAAOgG,SAASkC,QAAQ;YAC1B;YAEA,IAAIlI,MAAM;gBACR,MAAMmI,oBAAoBnI,KACvBoI,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,qBAEL,CAFK,IAAIf,MACR,AAAC,mBAAiBpH,OAAK,6IADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAE2E,EAAE,EAAE,GAAGU,cAAK,CAACoD,OAAO,CAAC;QACjC,MAAMC,eAAenD,kBAAkBS;QACvC,OAAO;YACLhG,MAAM0I;YACN/D,IAAIsB,SAASV,kBAAkBU,UAAUyC;QAC3C;IACF,GAAG;QAAC1C;QAAUC;KAAO;IAErB,oFAAoF;IACpF,IAAI0C;IACJ,IAAIjC,gBAAgB;QAClB,IAAI7E,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAIsE,SAAS;gBACX9F,QAAQqI,IAAI,CACV,AAAC,oDAAoD5C,WAAS;YAElE;YACA,IAAIO,kBAAkB;gBACpBhG,QAAQqI,IAAI,CACV,AAAC,yDAAyD5C,WAAS;YAEvE;YACA,IAAI;gBACF2C,QAAQtD,cAAK,CAACwD,QAAQ,CAACC,IAAI,CAAC/C;YAC9B,EAAE,OAAOlC,KAAK;gBACZ,IAAI,CAACkC,UAAU;oBACb,MAAM,qBAEL,CAFK,IAAIqB,MACR,AAAC,uDAAuDpB,WAAS,kFAD7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,MAAM,qBAKL,CALK,IAAIoB,MACR,AAAC,6DAA6DpB,WAAS,8FACpE,CAAA,OAAOvC,WAAW,cACf,sEACA,EAAC,IAJH,qBAAA;2BAAA;gCAAA;kCAAA;gBAKN;YACF;QACF,OAAO;YACLkF,QAAQtD,cAAK,CAACwD,QAAQ,CAACC,IAAI,CAAC/C;QAC9B;IACF,OAAO;QACL,IAAIlE,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAI,CAACgE,4BAAD,AAACA,SAAkBgD,IAAI,MAAK,KAAK;gBACnC,MAAM,qBAEL,CAFK,IAAI3B,MACR,oKADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAM4B,WAAgBtC,iBAClBiC,SAAS,OAAOA,UAAU,YAAYA,MAAMM,GAAG,GAC/CnD;IAEJ,4EAA4E;IAC5E,sEAAsE;IACtE,4EAA4E;IAC5E,6BAA6B;IAC7B,MAAMoD,+BAA+B7D,cAAK,CAAC8D,WAAW,CACpD,CAACpJ;QACC,IAAIgH,mBAAmB9G,WAAW,MAAM;YACtCH,kBAAkBC,SAASC,MAAMC,QAAQ+G;QAC3C;QACA,OAAO;YACL9F,oBAAoBnB;QACtB;IACF,GACA;QAACgH;QAAiB/G;QAAMC;QAAQ+G;KAAgB;IAGlD,MAAMoC,YAAYC,IAAAA,0BAAY,EAACH,8BAA8BF;IAE7D,MAAMM,aAMF;QACFL,KAAKG;QACL/C,SAAQ3B,CAAC;YACP,IAAI7C,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAI,CAAC2C,GAAG;oBACN,MAAM,qBAEL,CAFK,IAAI0C,MACP,mFADG,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,IAAI,CAACV,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQ3B;YACV;YAEA,IACEgC,kBACAiC,MAAM9C,KAAK,IACX,OAAO8C,MAAM9C,KAAK,CAACQ,OAAO,KAAK,YAC/B;gBACAsC,MAAM9C,KAAK,CAACQ,OAAO,CAAC3B;YACtB;YAEA,IAAI,CAACzE,QAAQ;gBACX;YACF;YAEA,IAAIyE,EAAE6E,gBAAgB,EAAE;gBACtB;YACF;YAEA9E,YAAYC,GAAGzE,QAAQD,MAAM2E,IAAIC,SAASC,SAASC;QACrD;QACAwB,cAAa5B,CAAC;YACZ,IAAI,CAACgC,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiB7B;YACnB;YAEA,IACEgC,kBACAiC,MAAM9C,KAAK,IACX,OAAO8C,MAAM9C,KAAK,CAACS,YAAY,KAAK,YACpC;gBACAqC,MAAM9C,KAAK,CAACS,YAAY,CAAC5B;YAC3B;YAEA,IAAI,CAACzE,QAAQ;gBACX;YACF;YAEA,IAAI,CAAC8G,mBAAmBlF,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;gBAC9D;YACF;YAEAG,mBAAmBwC,EAAET,aAAa;QACpC;QACAuC,cAAc3E,QAAQC,GAAG,CAAC0H,0BAA0B,GAChDvI,YACA,SAASuF,aAAa9B,CAAC;YACrB,IAAI,CAACgC,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiB/B;YACnB;YAEA,IACEgC,kBACAiC,MAAM9C,KAAK,IACX,OAAO8C,MAAM9C,KAAK,CAACW,YAAY,KAAK,YACpC;gBACAmC,MAAM9C,KAAK,CAACW,YAAY,CAAC9B;YAC3B;YAEA,IAAI,CAACzE,QAAQ;gBACX;YACF;YAEA,IAAI,CAAC8G,iBAAiB;gBACpB;YACF;YAEA7E,mBACEwC,EAAET,aAAa;QAEnB;IACN;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,2EAA2E;IAC3E,IAAIwF,IAAAA,oBAAa,EAAC9E,KAAK;QACrB2E,WAAWtJ,IAAI,GAAG2E;IACpB,OAAO,IACL,CAAC+B,kBACDN,YACCuC,MAAMI,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUJ,MAAM9C,KAAK,AAAD,GAC7C;QACAyD,WAAWtJ,IAAI,GAAG0J,IAAAA,wBAAW,EAAC/E;IAChC;IAEA,OAAO+B,+BACLrB,cAAK,CAACsE,YAAY,CAAChB,OAAOW,4BAE1B,qBAAC1C;QAAG,GAAGD,SAAS;QAAG,GAAG2C,UAAU;kBAC7BvD;;AAGP;MAGF,WAAeL"}