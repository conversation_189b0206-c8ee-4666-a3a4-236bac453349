{"version": 3, "sources": ["../../../../src/client/components/errors/use-error-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { attachHydrationErrorState } from './attach-hydration-error-state'\nimport { isNextRouterError } from '../is-next-router-error'\nimport { storeHydrationErrorStateFromConsoleArgs } from './hydration-error-info'\nimport { formatConsoleArgs, parseConsoleArgs } from '../../lib/console'\nimport isError from '../../../lib/is-error'\nimport { createUnhandledError } from './console-error'\nimport { enqueueConsecutiveDedupedError } from './enqueue-client-error'\nimport { getReactStitchedError } from '../errors/stitched-error'\n\nconst queueMicroTask =\n  globalThis.queueMicrotask || ((cb: () => void) => Promise.resolve().then(cb))\n\nexport type ErrorHandler = (error: Error) => void\n\nconst errorQueue: Array<Error> = []\nconst errorHandlers: Array<ErrorHandler> = []\nconst rejectionQueue: Array<Error> = []\nconst rejectionHandlers: Array<ErrorHandler> = []\n\nexport function handleClientError(\n  originError: unknown,\n  consoleErrorArgs: any[],\n  capturedFromConsole: boolean = false\n) {\n  let error: Error\n  if (!originError || !isError(originError)) {\n    // If it's not an error, format the args into an error\n    const formattedErrorMessage = formatConsoleArgs(consoleErrorArgs)\n    const { environmentName } = parseConsoleArgs(consoleErrorArgs)\n    error = createUnhandledError(formattedErrorMessage, environmentName)\n  } else {\n    error = capturedFromConsole\n      ? createUnhandledError(originError)\n      : originError\n  }\n  error = getReactStitchedError(error)\n\n  storeHydrationErrorStateFromConsoleArgs(...consoleErrorArgs)\n  attachHydrationErrorState(error)\n\n  enqueueConsecutiveDedupedError(errorQueue, error)\n  for (const handler of errorHandlers) {\n    // Delayed the error being passed to React Dev Overlay,\n    // avoid the state being synchronously updated in the component.\n    queueMicroTask(() => {\n      handler(error)\n    })\n  }\n}\n\nexport function useErrorHandler(\n  handleOnUnhandledError: ErrorHandler,\n  handleOnUnhandledRejection: ErrorHandler\n) {\n  useEffect(() => {\n    // Handle queued errors.\n    errorQueue.forEach(handleOnUnhandledError)\n    rejectionQueue.forEach(handleOnUnhandledRejection)\n\n    // Listen to new errors.\n    errorHandlers.push(handleOnUnhandledError)\n    rejectionHandlers.push(handleOnUnhandledRejection)\n\n    return () => {\n      // Remove listeners.\n      errorHandlers.splice(errorHandlers.indexOf(handleOnUnhandledError), 1)\n      rejectionHandlers.splice(\n        rejectionHandlers.indexOf(handleOnUnhandledRejection),\n        1\n      )\n\n      // Reset error queues.\n      errorQueue.splice(0, errorQueue.length)\n      rejectionQueue.splice(0, rejectionQueue.length)\n    }\n  }, [handleOnUnhandledError, handleOnUnhandledRejection])\n}\n\nfunction onUnhandledError(event: WindowEventMap['error']): void | boolean {\n  if (isNextRouterError(event.error)) {\n    event.preventDefault()\n    return false\n  }\n  // When there's an error property present, we log the error to error overlay.\n  // Otherwise we don't do anything as it's not logging in the console either.\n  if (event.error) {\n    handleClientError(event.error, [])\n  }\n}\n\nfunction onUnhandledRejection(ev: WindowEventMap['unhandledrejection']): void {\n  const reason = ev?.reason\n  if (isNextRouterError(reason)) {\n    ev.preventDefault()\n    return\n  }\n\n  let error = reason\n  if (error && !isError(error)) {\n    error = createUnhandledError(error + '')\n  }\n\n  rejectionQueue.push(error)\n  for (const handler of rejectionHandlers) {\n    handler(error)\n  }\n}\n\nexport function handleGlobalErrors() {\n  if (typeof window !== 'undefined') {\n    try {\n      // Increase the number of stack frames on the client\n      Error.stackTraceLimit = 50\n    } catch {}\n\n    window.addEventListener('error', onUnhandledError)\n    window.addEventListener('unhandledrejection', onUnhandledRejection)\n  }\n}\n"], "names": ["handleClientError", "handleGlobalErrors", "useErrorHandler", "queueMicroTask", "globalThis", "queueMicrotask", "cb", "Promise", "resolve", "then", "errorQueue", "errorHandlers", "rejectionQueue", "rejectionHandlers", "originError", "consoleErrorArgs", "capturedFromConsole", "error", "isError", "formattedErrorMessage", "formatConsoleArgs", "environmentName", "parseConsoleArgs", "createUnhandledError", "getReactStitchedError", "storeHydrationErrorStateFromConsoleArgs", "attachHydrationErrorState", "enqueueConsecutiveDedupedError", "handler", "handleOnUnhandledError", "handleOnUnhandledRejection", "useEffect", "for<PERSON>ach", "push", "splice", "indexOf", "length", "onUnhandledError", "event", "isNextRouterError", "preventDefault", "onUnhandledRejection", "ev", "reason", "window", "Error", "stackTraceLimit", "addEventListener"], "mappings": ";;;;;;;;;;;;;;;;IAoBgBA,iBAAiB;eAAjBA;;IAyFAC,kBAAkB;eAAlBA;;IA1DAC,eAAe;eAAfA;;;;uBAnDU;2CACgB;mCACR;oCACsB;yBACJ;kEAChC;8BACiB;oCACU;+BACT;AAEtC,MAAMC,iBACJC,WAAWC,cAAc,IAAK,CAAA,CAACC,KAAmBC,QAAQC,OAAO,GAAGC,IAAI,CAACH,GAAE;AAI7E,MAAMI,aAA2B,EAAE;AACnC,MAAMC,gBAAqC,EAAE;AAC7C,MAAMC,iBAA+B,EAAE;AACvC,MAAMC,oBAAyC,EAAE;AAE1C,SAASb,kBACdc,WAAoB,EACpBC,gBAAuB,EACvBC,mBAAoC;IAApCA,IAAAA,gCAAAA,sBAA+B;IAE/B,IAAIC;IACJ,IAAI,CAACH,eAAe,CAACI,IAAAA,gBAAO,EAACJ,cAAc;QACzC,sDAAsD;QACtD,MAAMK,wBAAwBC,IAAAA,0BAAiB,EAACL;QAChD,MAAM,EAAEM,eAAe,EAAE,GAAGC,IAAAA,yBAAgB,EAACP;QAC7CE,QAAQM,IAAAA,kCAAoB,EAACJ,uBAAuBE;IACtD,OAAO;QACLJ,QAAQD,sBACJO,IAAAA,kCAAoB,EAACT,eACrBA;IACN;IACAG,QAAQO,IAAAA,oCAAqB,EAACP;IAE9BQ,IAAAA,2DAAuC,KAAIV;IAC3CW,IAAAA,oDAAyB,EAACT;IAE1BU,IAAAA,kDAA8B,EAACjB,YAAYO;IAC3C,KAAK,MAAMW,WAAWjB,cAAe;QACnC,uDAAuD;QACvD,gEAAgE;QAChER,eAAe;YACbyB,QAAQX;QACV;IACF;AACF;AAEO,SAASf,gBACd2B,sBAAoC,EACpCC,0BAAwC;IAExCC,IAAAA,gBAAS,EAAC;QACR,wBAAwB;QACxBrB,WAAWsB,OAAO,CAACH;QACnBjB,eAAeoB,OAAO,CAACF;QAEvB,wBAAwB;QACxBnB,cAAcsB,IAAI,CAACJ;QACnBhB,kBAAkBoB,IAAI,CAACH;QAEvB,OAAO;YACL,oBAAoB;YACpBnB,cAAcuB,MAAM,CAACvB,cAAcwB,OAAO,CAACN,yBAAyB;YACpEhB,kBAAkBqB,MAAM,CACtBrB,kBAAkBsB,OAAO,CAACL,6BAC1B;YAGF,sBAAsB;YACtBpB,WAAWwB,MAAM,CAAC,GAAGxB,WAAW0B,MAAM;YACtCxB,eAAesB,MAAM,CAAC,GAAGtB,eAAewB,MAAM;QAChD;IACF,GAAG;QAACP;QAAwBC;KAA2B;AACzD;AAEA,SAASO,iBAAiBC,KAA8B;IACtD,IAAIC,IAAAA,oCAAiB,EAACD,MAAMrB,KAAK,GAAG;QAClCqB,MAAME,cAAc;QACpB,OAAO;IACT;IACA,6EAA6E;IAC7E,4EAA4E;IAC5E,IAAIF,MAAMrB,KAAK,EAAE;QACfjB,kBAAkBsC,MAAMrB,KAAK,EAAE,EAAE;IACnC;AACF;AAEA,SAASwB,qBAAqBC,EAAwC;IACpE,MAAMC,SAASD,sBAAAA,GAAIC,MAAM;IACzB,IAAIJ,IAAAA,oCAAiB,EAACI,SAAS;QAC7BD,GAAGF,cAAc;QACjB;IACF;IAEA,IAAIvB,QAAQ0B;IACZ,IAAI1B,SAAS,CAACC,IAAAA,gBAAO,EAACD,QAAQ;QAC5BA,QAAQM,IAAAA,kCAAoB,EAACN,QAAQ;IACvC;IAEAL,eAAeqB,IAAI,CAAChB;IACpB,KAAK,MAAMW,WAAWf,kBAAmB;QACvCe,QAAQX;IACV;AACF;AAEO,SAAShB;IACd,IAAI,OAAO2C,WAAW,aAAa;QACjC,IAAI;YACF,oDAAoD;YACpDC,MAAMC,eAAe,GAAG;QAC1B,EAAE,UAAM,CAAC;QAETF,OAAOG,gBAAgB,CAAC,SAASV;QACjCO,OAAOG,gBAAgB,CAAC,sBAAsBN;IAChD;AACF"}