!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).predicates={})}(this,(function(t){"use strict";const n=11102230246251565e-32,e=134217729,r=(3+8*n)*n;function s(t,n,e,r,s){let o,a,c,f,i=n[0],u=r[0],h=0,b=0;u>i==u>-i?(o=i,i=n[++h]):(o=u,u=r[++b]);let M=0;if(h<t&&b<e)for(u>i==u>-i?(a=i+o,c=o-(a-i),i=n[++h]):(a=u+o,c=o-(a-u),u=r[++b]),o=a,0!==c&&(s[M++]=c);h<t&&b<e;)u>i==u>-i?(a=o+i,f=a-o,c=o-(a-f)+(i-f),i=n[++h]):(a=o+u,f=a-o,c=o-(a-f)+(u-f),u=r[++b]),o=a,0!==c&&(s[M++]=c);for(;h<t;)a=o+i,f=a-o,c=o-(a-f)+(i-f),i=n[++h],o=a,0!==c&&(s[M++]=c);for(;b<e;)a=o+u,f=a-o,c=o-(a-f)+(u-f),u=r[++b],o=a,0!==c&&(s[M++]=c);return 0===o&&0!==M||(s[M++]=o),M}function o(t,n,e,r,o,a,c,f){return s(s(t,n,e,r,c),c,o,a,f)}function a(t,n,r,s){let o,a,c,f,i,u,h,b,M,l,d;h=e*r,l=h-(h-r),d=r-l;let p=n[0];o=p*r,h=e*p,b=h-(h-p),M=p-b,c=M*d-(o-b*l-M*l-b*d);let y=0;0!==c&&(s[y++]=c);for(let x=1;x<t;x++)p=n[x],f=p*r,h=e*p,b=h-(h-p),M=p-b,i=M*d-(f-b*l-M*l-b*d),a=o+i,u=a-o,c=o-(a-u)+(i-u),0!==c&&(s[y++]=c),o=f+a,c=a-(o-f),0!==c&&(s[y++]=c);return 0===o&&0!==y||(s[y++]=o),y}function c(t,n){for(let e=0;e<t;e++)n[e]=-n[e];return t}function f(t,n){let e=n[0];for(let r=1;r<t;r++)e+=n[r];return e}function i(t){return new Float64Array(t)}const u=22204460492503146e-32,h=11093356479670487e-47,b=i(4),M=i(8),l=i(12),d=i(16),p=i(4);const y=3330669073875473e-31,x=32047474274603644e-47,g=i(4),m=i(4),T=i(4),j=i(4),w=i(4),A=i(4),F=i(4),k=i(4),q=i(4),v=i(8),z=i(8),B=i(8),C=i(4),D=i(8),E=i(8),G=i(8),H=i(12);let I=i(192),J=i(192);function K(t,n,e){t=s(t,I,n,e,J);const r=I;return I=J,J=r,t}function L(t,n,r,s,o,a,c,f){let i,u,h,b,M,l,d,p,y,x,g,m,T,j,w;return 0===t?0===n?(c[0]=0,f[0]=0,1):(w=-n,x=w*r,u=e*w,h=u-(u-w),b=w-h,u=e*r,M=u-(u-r),l=r-M,c[0]=b*l-(x-h*M-b*M-h*l),c[1]=x,x=n*o,u=e*n,h=u-(u-n),b=n-h,u=e*o,M=u-(u-o),l=o-M,f[0]=b*l-(x-h*M-b*M-h*l),f[1]=x,2):0===n?(x=t*s,u=e*t,h=u-(u-t),b=t-h,u=e*s,M=u-(u-s),l=s-M,c[0]=b*l-(x-h*M-b*M-h*l),c[1]=x,w=-t,x=w*a,u=e*w,h=u-(u-w),b=w-h,u=e*a,M=u-(u-a),l=a-M,f[0]=b*l-(x-h*M-b*M-h*l),f[1]=x,2):(x=t*s,u=e*t,h=u-(u-t),b=t-h,u=e*s,M=u-(u-s),l=s-M,g=b*l-(x-h*M-b*M-h*l),m=n*r,u=e*n,h=u-(u-n),b=n-h,u=e*r,M=u-(u-r),l=r-M,T=b*l-(m-h*M-b*M-h*l),d=g-T,i=g-d,c[0]=g-(d+i)+(i-T),p=x+d,i=p-x,y=x-(p-i)+(d-i),d=y-m,i=y-d,c[1]=y-(d+i)+(i-m),j=p+d,i=j-p,c[2]=p-(j-i)+(d-i),c[3]=j,x=n*o,u=e*n,h=u-(u-n),b=n-h,u=e*o,M=u-(u-o),l=o-M,g=b*l-(x-h*M-b*M-h*l),m=t*a,u=e*t,h=u-(u-t),b=t-h,u=e*a,M=u-(u-a),l=a-M,T=b*l-(m-h*M-b*M-h*l),d=g-T,i=g-d,f[0]=g-(d+i)+(i-T),p=x+d,i=p-x,y=x-(p-i)+(d-i),d=y-m,i=y-d,f[1]=y-(d+i)+(i-m),j=p+d,i=j-p,f[2]=p-(j-i)+(d-i),f[3]=j,4)}function N(t,n,r,s,o){let a,c,f,i,u,h,b,M,l,d,p,y,x;return p=n*r,c=e*n,f=c-(c-n),i=n-f,c=e*r,u=c-(c-r),h=r-u,y=i*h-(p-f*u-i*u-f*h),c=e*s,u=c-(c-s),h=s-u,b=y*s,c=e*y,f=c-(c-y),i=y-f,C[0]=i*h-(b-f*u-i*u-f*h),M=p*s,c=e*p,f=c-(c-p),i=p-f,d=i*h-(M-f*u-i*u-f*h),l=b+d,a=l-b,C[1]=b-(l-a)+(d-a),x=M+l,C[2]=l-(x-M),C[3]=x,t=K(t,4,C),0!==o&&(c=e*o,u=c-(c-o),h=o-u,b=y*o,c=e*y,f=c-(c-y),i=y-f,C[0]=i*h-(b-f*u-i*u-f*h),M=p*o,c=e*p,f=c-(c-p),i=p-f,d=i*h-(M-f*u-i*u-f*h),l=b+d,a=l-b,C[1]=b-(l-a)+(d-a),x=M+l,C[2]=l-(x-M),C[3]=x,t=K(t,4,C)),t}const O=4440892098500632e-31,P=5423418723394464e-46,Q=i(4),R=i(4),S=i(4),U=i(4),V=i(4),W=i(4),X=i(4),Y=i(4),Z=i(8),$=i(8),_=i(8),tt=i(8),nt=i(8),et=i(8),rt=i(8),st=i(8),ot=i(8),at=i(4),ct=i(4),ft=i(4),it=i(8),ut=i(16),ht=i(16),bt=i(16),Mt=i(32),lt=i(32),dt=i(48),pt=i(64);let yt=i(1152),xt=i(1152);function gt(t,n,e){t=s(t,yt,n,e,xt);const r=yt;return yt=xt,xt=r,t}const mt=5551115123125792e-31,Tt=8751425667295619e-46,jt=i(4),wt=i(4),At=i(4),Ft=i(4),kt=i(4),qt=i(4),vt=i(4),zt=i(4),Bt=i(4),Ct=i(4),Dt=i(24),Et=i(24),Gt=i(24),Ht=i(24),It=i(24),Jt=i(24),Kt=i(24),Lt=i(24),Nt=i(24),Ot=i(24),Pt=i(1152),Qt=i(1152),Rt=i(1152),St=i(1152),Ut=i(1152),Vt=i(2304),Wt=i(2304),Xt=i(3456),Yt=i(5760),Zt=i(8),$t=i(8),_t=i(8),tn=i(16),nn=i(24),en=i(48),rn=i(48),sn=i(96),on=i(192),an=i(384),cn=i(384),fn=i(384),un=i(768);function hn(t,n,e,r,s,c,f){return o(a(4,t,r,Zt),Zt,a(4,n,s,$t),$t,a(4,e,c,_t),_t,tn,f)}function bn(t,n,e,r,f,i,u,h,b,M,l,d){const p=s(s(t,n,e,r,en),en,c(s(f,i,u,h,rn),rn),rn,sn);return o(a(a(p,sn,b,on),on,b,an),an,a(a(p,sn,M,on),on,M,cn),cn,a(a(p,sn,l,on),on,l,fn),fn,un,d)}const Mn=i(96),ln=i(96),dn=i(96),pn=i(1152);function yn(t,n,e,r,s,c,f,i,u,h){const b=hn(t,n,e,r,s,c,nn);return o(a(a(b,nn,f,en),en,f,Mn),Mn,a(a(b,nn,i,en),en,i,ln),ln,a(a(b,nn,u,en),en,u,dn),dn,on,h)}function xn(t,n,a,i,u,h,b,M,l,d,p,y,x,g,m,T){let j,w,A,F,k,q,v,z,B,C,D,E,G,H,I,J,K,L,N,O,P,Q,R,S,U,V,W,X,Y,Z,$;const _=t-x,tt=i-x,nt=b-x,et=d-x,rt=n-g,st=u-g,ot=M-g,at=p-g,ct=a-m,ft=h-m,it=l-m,ut=y-m;X=_*st,O=e*_,P=O-(O-_),Q=_-P,O=e*st,R=O-(O-st),S=st-R,Y=Q*S-(X-P*R-Q*R-P*S),Z=tt*rt,O=e*tt,P=O-(O-tt),Q=tt-P,O=e*rt,R=O-(O-rt),S=rt-R,$=Q*S-(Z-P*R-Q*R-P*S),U=Y-$,N=Y-U,jt[0]=Y-(U+N)+(N-$),V=X+U,N=V-X,W=X-(V-N)+(U-N),U=W-Z,N=W-U,jt[1]=W-(U+N)+(N-Z),j=V+U,N=j-V,jt[2]=V-(j-N)+(U-N),jt[3]=j,X=tt*ot,O=e*tt,P=O-(O-tt),Q=tt-P,O=e*ot,R=O-(O-ot),S=ot-R,Y=Q*S-(X-P*R-Q*R-P*S),Z=nt*st,O=e*nt,P=O-(O-nt),Q=nt-P,O=e*st,R=O-(O-st),S=st-R,$=Q*S-(Z-P*R-Q*R-P*S),U=Y-$,N=Y-U,wt[0]=Y-(U+N)+(N-$),V=X+U,N=V-X,W=X-(V-N)+(U-N),U=W-Z,N=W-U,wt[1]=W-(U+N)+(N-Z),w=V+U,N=w-V,wt[2]=V-(w-N)+(U-N),wt[3]=w,X=nt*at,O=e*nt,P=O-(O-nt),Q=nt-P,O=e*at,R=O-(O-at),S=at-R,Y=Q*S-(X-P*R-Q*R-P*S),Z=et*ot,O=e*et,P=O-(O-et),Q=et-P,O=e*ot,R=O-(O-ot),S=ot-R,$=Q*S-(Z-P*R-Q*R-P*S),U=Y-$,N=Y-U,At[0]=Y-(U+N)+(N-$),V=X+U,N=V-X,W=X-(V-N)+(U-N),U=W-Z,N=W-U,At[1]=W-(U+N)+(N-Z),A=V+U,N=A-V,At[2]=V-(A-N)+(U-N),At[3]=A,X=et*rt,O=e*et,P=O-(O-et),Q=et-P,O=e*rt,R=O-(O-rt),S=rt-R,Y=Q*S-(X-P*R-Q*R-P*S),Z=_*at,O=e*_,P=O-(O-_),Q=_-P,O=e*at,R=O-(O-at),S=at-R,$=Q*S-(Z-P*R-Q*R-P*S),U=Y-$,N=Y-U,Bt[0]=Y-(U+N)+(N-$),V=X+U,N=V-X,W=X-(V-N)+(U-N),U=W-Z,N=W-U,Bt[1]=W-(U+N)+(N-Z),F=V+U,N=F-V,Bt[2]=V-(F-N)+(U-N),Bt[3]=F,X=_*ot,O=e*_,P=O-(O-_),Q=_-P,O=e*ot,R=O-(O-ot),S=ot-R,Y=Q*S-(X-P*R-Q*R-P*S),Z=nt*rt,O=e*nt,P=O-(O-nt),Q=nt-P,O=e*rt,R=O-(O-rt),S=rt-R,$=Q*S-(Z-P*R-Q*R-P*S),U=Y-$,N=Y-U,qt[0]=Y-(U+N)+(N-$),V=X+U,N=V-X,W=X-(V-N)+(U-N),U=W-Z,N=W-U,qt[1]=W-(U+N)+(N-Z),k=V+U,N=k-V,qt[2]=V-(k-N)+(U-N),qt[3]=k,X=tt*at,O=e*tt,P=O-(O-tt),Q=tt-P,O=e*at,R=O-(O-at),S=at-R,Y=Q*S-(X-P*R-Q*R-P*S),Z=et*st,O=e*et,P=O-(O-et),Q=et-P,O=e*st,R=O-(O-st),S=st-R,$=Q*S-(Z-P*R-Q*R-P*S),U=Y-$,N=Y-U,vt[0]=Y-(U+N)+(N-$),V=X+U,N=V-X,W=X-(V-N)+(U-N),U=W-Z,N=W-U,vt[1]=W-(U+N)+(N-Z),q=V+U,N=q-V,vt[2]=V-(q-N)+(U-N),vt[3]=q;let ht=f(s(s(c(yn(wt,At,vt,ut,ft,-it,_,rt,ct,Pt),Pt),Pt,yn(At,Bt,qt,ct,it,ut,tt,st,ft,Qt),Qt,Vt),Vt,s(c(yn(Bt,jt,vt,ft,ut,ct,nt,ot,it,Rt),Rt),Rt,yn(jt,wt,qt,it,ct,-ft,et,at,ut,St),St,Wt),Wt,pn),pn),bt=mt*T;if(ht>=bt||-ht>=bt)return ht;if(N=t-_,v=t-(_+N)+(N-x),N=n-rt,D=n-(rt+N)+(N-g),N=a-ct,I=a-(ct+N)+(N-m),N=i-tt,z=i-(tt+N)+(N-x),N=u-st,E=u-(st+N)+(N-g),N=h-ft,J=h-(ft+N)+(N-m),N=b-nt,B=b-(nt+N)+(N-x),N=M-ot,G=M-(ot+N)+(N-g),N=l-it,K=l-(it+N)+(N-m),N=d-et,C=d-(et+N)+(N-x),N=p-at,H=p-(at+N)+(N-g),N=y-ut,L=y-(ut+N)+(N-m),0===v&&0===D&&0===I&&0===z&&0===E&&0===J&&0===B&&0===G&&0===K&&0===C&&0===H&&0===L)return ht;bt=Tt*T+r*Math.abs(ht);const Mt=_*E+st*v-(rt*z+tt*D),lt=tt*G+ot*z-(st*B+nt*E),dt=nt*H+at*B-(ot*C+et*G),pt=et*D+rt*C-(at*v+_*H),yt=_*G+ot*v-(rt*B+nt*D),xt=tt*H+at*z-(st*C+et*E);return ht+=(tt*tt+st*st+ft*ft)*(it*pt+ut*yt+ct*dt+(K*F+L*k+I*A))+(et*et+at*at+ut*ut)*(ct*lt-ft*yt+it*Mt+(I*w-J*k+K*j))-((_*_+rt*rt+ct*ct)*(ft*dt-it*xt+ut*lt+(J*A-K*q+L*w))+(nt*nt+ot*ot+it*it)*(ut*Mt+ct*xt+ft*pt+(L*j+I*q+J*F)))+2*((tt*z+st*E+ft*J)*(it*F+ut*k+ct*A)+(et*C+at*H+ut*L)*(ct*w-ft*k+it*j)-((_*v+rt*D+ct*I)*(ft*A-it*q+ut*w)+(nt*B+ot*G+it*K)*(ut*j+ct*q+ft*F))),ht>=bt||-ht>=bt?ht:function(t,n,r,s,a,c,f,i,u,h,b,M,l,d,p){let y,x,g,m,T,j,w,A,F,k,q,v,z,B;k=t*a,x=e*t,g=x-(x-t),m=t-g,x=e*a,T=x-(x-a),j=a-T,q=m*j-(k-g*T-m*T-g*j),v=s*n,x=e*s,g=x-(x-s),m=s-g,x=e*n,T=x-(x-n),j=n-T,z=m*j-(v-g*T-m*T-g*j),w=q-z,y=q-w,jt[0]=q-(w+y)+(y-z),A=k+w,y=A-k,F=k-(A-y)+(w-y),w=F-v,y=F-w,jt[1]=F-(w+y)+(y-v),B=A+w,y=B-A,jt[2]=A-(B-y)+(w-y),jt[3]=B,k=s*i,x=e*s,g=x-(x-s),m=s-g,x=e*i,T=x-(x-i),j=i-T,q=m*j-(k-g*T-m*T-g*j),v=f*a,x=e*f,g=x-(x-f),m=f-g,x=e*a,T=x-(x-a),j=a-T,z=m*j-(v-g*T-m*T-g*j),w=q-z,y=q-w,wt[0]=q-(w+y)+(y-z),A=k+w,y=A-k,F=k-(A-y)+(w-y),w=F-v,y=F-w,wt[1]=F-(w+y)+(y-v),B=A+w,y=B-A,wt[2]=A-(B-y)+(w-y),wt[3]=B,k=f*b,x=e*f,g=x-(x-f),m=f-g,x=e*b,T=x-(x-b),j=b-T,q=m*j-(k-g*T-m*T-g*j),v=h*i,x=e*h,g=x-(x-h),m=h-g,x=e*i,T=x-(x-i),j=i-T,z=m*j-(v-g*T-m*T-g*j),w=q-z,y=q-w,At[0]=q-(w+y)+(y-z),A=k+w,y=A-k,F=k-(A-y)+(w-y),w=F-v,y=F-w,At[1]=F-(w+y)+(y-v),B=A+w,y=B-A,At[2]=A-(B-y)+(w-y),At[3]=B,k=h*d,x=e*h,g=x-(x-h),m=h-g,x=e*d,T=x-(x-d),j=d-T,q=m*j-(k-g*T-m*T-g*j),v=l*b,x=e*l,g=x-(x-l),m=l-g,x=e*b,T=x-(x-b),j=b-T,z=m*j-(v-g*T-m*T-g*j),w=q-z,y=q-w,Ft[0]=q-(w+y)+(y-z),A=k+w,y=A-k,F=k-(A-y)+(w-y),w=F-v,y=F-w,Ft[1]=F-(w+y)+(y-v),B=A+w,y=B-A,Ft[2]=A-(B-y)+(w-y),Ft[3]=B,k=l*n,x=e*l,g=x-(x-l),m=l-g,x=e*n,T=x-(x-n),j=n-T,q=m*j-(k-g*T-m*T-g*j),v=t*d,x=e*t,g=x-(x-t),m=t-g,x=e*d,T=x-(x-d),j=d-T,z=m*j-(v-g*T-m*T-g*j),w=q-z,y=q-w,kt[0]=q-(w+y)+(y-z),A=k+w,y=A-k,F=k-(A-y)+(w-y),w=F-v,y=F-w,kt[1]=F-(w+y)+(y-v),B=A+w,y=B-A,kt[2]=A-(B-y)+(w-y),kt[3]=B,k=t*i,x=e*t,g=x-(x-t),m=t-g,x=e*i,T=x-(x-i),j=i-T,q=m*j-(k-g*T-m*T-g*j),v=f*n,x=e*f,g=x-(x-f),m=f-g,x=e*n,T=x-(x-n),j=n-T,z=m*j-(v-g*T-m*T-g*j),w=q-z,y=q-w,qt[0]=q-(w+y)+(y-z),A=k+w,y=A-k,F=k-(A-y)+(w-y),w=F-v,y=F-w,qt[1]=F-(w+y)+(y-v),B=A+w,y=B-A,qt[2]=A-(B-y)+(w-y),qt[3]=B,k=s*b,x=e*s,g=x-(x-s),m=s-g,x=e*b,T=x-(x-b),j=b-T,q=m*j-(k-g*T-m*T-g*j),v=h*a,x=e*h,g=x-(x-h),m=h-g,x=e*a,T=x-(x-a),j=a-T,z=m*j-(v-g*T-m*T-g*j),w=q-z,y=q-w,vt[0]=q-(w+y)+(y-z),A=k+w,y=A-k,F=k-(A-y)+(w-y),w=F-v,y=F-w,vt[1]=F-(w+y)+(y-v),B=A+w,y=B-A,vt[2]=A-(B-y)+(w-y),vt[3]=B,k=f*d,x=e*f,g=x-(x-f),m=f-g,x=e*d,T=x-(x-d),j=d-T,q=m*j-(k-g*T-m*T-g*j),v=l*i,x=e*l,g=x-(x-l),m=l-g,x=e*i,T=x-(x-i),j=i-T,z=m*j-(v-g*T-m*T-g*j),w=q-z,y=q-w,zt[0]=q-(w+y)+(y-z),A=k+w,y=A-k,F=k-(A-y)+(w-y),w=F-v,y=F-w,zt[1]=F-(w+y)+(y-v),B=A+w,y=B-A,zt[2]=A-(B-y)+(w-y),zt[3]=B,k=h*n,x=e*h,g=x-(x-h),m=h-g,x=e*n,T=x-(x-n),j=n-T,q=m*j-(k-g*T-m*T-g*j),v=t*b,x=e*t,g=x-(x-t),m=t-g,x=e*b,T=x-(x-b),j=b-T,z=m*j-(v-g*T-m*T-g*j),w=q-z,y=q-w,Bt[0]=q-(w+y)+(y-z),A=k+w,y=A-k,F=k-(A-y)+(w-y),w=F-v,y=F-w,Bt[1]=F-(w+y)+(y-v),B=A+w,y=B-A,Bt[2]=A-(B-y)+(w-y),Bt[3]=B,k=l*a,x=e*l,g=x-(x-l),m=l-g,x=e*a,T=x-(x-a),j=a-T,q=m*j-(k-g*T-m*T-g*j),v=s*d,x=e*s,g=x-(x-s),m=s-g,x=e*d,T=x-(x-d),j=d-T,z=m*j-(v-g*T-m*T-g*j),w=q-z,y=q-w,Ct[0]=q-(w+y)+(y-z),A=k+w,y=A-k,F=k-(A-y)+(w-y),w=F-v,y=F-w,Ct[1]=F-(w+y)+(y-v),B=A+w,y=B-A,Ct[2]=A-(B-y)+(w-y),Ct[3]=B;const C=hn(jt,wt,qt,u,r,-c,Dt),D=hn(wt,At,vt,M,c,-u,Et),E=hn(At,Ft,zt,p,u,-M,Gt),G=hn(Ft,kt,Bt,r,M,-p,Ht),H=hn(kt,jt,Ct,c,p,-r,It),I=hn(jt,vt,Bt,M,r,c,Jt),J=hn(wt,zt,Ct,p,c,u,Kt),K=hn(At,Bt,qt,r,u,M,Lt),L=hn(Ft,Ct,vt,c,M,p,Nt),N=hn(kt,qt,zt,u,p,r,Ot),O=o(bn(E,Gt,J,Kt,L,Nt,D,Et,t,n,r,Pt),Pt,bn(G,Ht,K,Lt,N,Ot,E,Gt,s,a,c,Qt),Qt,o(bn(H,It,L,Nt,I,Jt,G,Ht,f,i,u,Rt),Rt,bn(C,Dt,N,Ot,J,Kt,H,It,h,b,M,St),St,bn(D,Et,I,Jt,K,Lt,C,Dt,l,d,p,Ut),Ut,Wt,Xt),Xt,Vt,Yt);return Yt[O-1]}(t,n,a,i,u,h,b,M,l,d,p,y,x,g,m)}t.incircle=function(t,n,c,i,u,h,b,M){const l=t-b,d=c-b,p=u-b,y=n-M,x=i-M,g=h-M,m=d*g,T=p*x,j=l*l+y*y,w=p*y,A=l*g,F=d*d+x*x,k=l*x,q=d*y,v=p*p+g*g,z=j*(m-T)+F*(w-A)+v*(k-q),B=(Math.abs(m)+Math.abs(T))*j+(Math.abs(w)+Math.abs(A))*F+(Math.abs(k)+Math.abs(q))*v,C=11102230246251577e-31*B;return z>C||-z>C?z:function(t,n,c,i,u,h,b,M,l){let d,p,y,x,g,m,T,j,w,A,F,k,q,v,z,B,C,D,E,G,H,I,J,K,L,N,xt,mt,Tt,jt,wt,At,Ft,kt,qt;const vt=t-b,zt=c-b,Bt=u-b,Ct=n-M,Dt=i-M,Et=h-M;wt=zt*Et,J=e*zt,K=J-(J-zt),L=zt-K,J=e*Et,N=J-(J-Et),xt=Et-N,At=L*xt-(wt-K*N-L*N-K*xt),Ft=Bt*Dt,J=e*Bt,K=J-(J-Bt),L=Bt-K,J=e*Dt,N=J-(J-Dt),xt=Dt-N,kt=L*xt-(Ft-K*N-L*N-K*xt),mt=At-kt,I=At-mt,Q[0]=At-(mt+I)+(I-kt),Tt=wt+mt,I=Tt-wt,jt=wt-(Tt-I)+(mt-I),mt=jt-Ft,I=jt-mt,Q[1]=jt-(mt+I)+(I-Ft),qt=Tt+mt,I=qt-Tt,Q[2]=Tt-(qt-I)+(mt-I),Q[3]=qt,wt=Bt*Ct,J=e*Bt,K=J-(J-Bt),L=Bt-K,J=e*Ct,N=J-(J-Ct),xt=Ct-N,At=L*xt-(wt-K*N-L*N-K*xt),Ft=vt*Et,J=e*vt,K=J-(J-vt),L=vt-K,J=e*Et,N=J-(J-Et),xt=Et-N,kt=L*xt-(Ft-K*N-L*N-K*xt),mt=At-kt,I=At-mt,R[0]=At-(mt+I)+(I-kt),Tt=wt+mt,I=Tt-wt,jt=wt-(Tt-I)+(mt-I),mt=jt-Ft,I=jt-mt,R[1]=jt-(mt+I)+(I-Ft),qt=Tt+mt,I=qt-Tt,R[2]=Tt-(qt-I)+(mt-I),R[3]=qt,wt=vt*Dt,J=e*vt,K=J-(J-vt),L=vt-K,J=e*Dt,N=J-(J-Dt),xt=Dt-N,At=L*xt-(wt-K*N-L*N-K*xt),Ft=zt*Ct,J=e*zt,K=J-(J-zt),L=zt-K,J=e*Ct,N=J-(J-Ct),xt=Ct-N,kt=L*xt-(Ft-K*N-L*N-K*xt),mt=At-kt,I=At-mt,S[0]=At-(mt+I)+(I-kt),Tt=wt+mt,I=Tt-wt,jt=wt-(Tt-I)+(mt-I),mt=jt-Ft,I=jt-mt,S[1]=jt-(mt+I)+(I-Ft),qt=Tt+mt,I=qt-Tt,S[2]=Tt-(qt-I)+(mt-I),S[3]=qt,d=s(s(s(a(a(4,Q,vt,it),it,vt,ut),ut,a(a(4,Q,Ct,it),it,Ct,ht),ht,Mt),Mt,s(a(a(4,R,zt,it),it,zt,ut),ut,a(a(4,R,Dt,it),it,Dt,ht),ht,lt),lt,pt),pt,s(a(a(4,S,Bt,it),it,Bt,ut),ut,a(a(4,S,Et,it),it,Et,ht),ht,Mt),Mt,yt);let Gt=f(d,yt),Ht=O*l;if(Gt>=Ht||-Gt>=Ht)return Gt;if(I=t-vt,p=t-(vt+I)+(I-b),I=n-Ct,g=n-(Ct+I)+(I-M),I=c-zt,y=c-(zt+I)+(I-b),I=i-Dt,m=i-(Dt+I)+(I-M),I=u-Bt,x=u-(Bt+I)+(I-b),I=h-Et,T=h-(Et+I)+(I-M),0===p&&0===y&&0===x&&0===g&&0===m&&0===T)return Gt;if(Ht=P*l+r*Math.abs(Gt),Gt+=(vt*vt+Ct*Ct)*(zt*T+Et*y-(Dt*x+Bt*m))+2*(vt*p+Ct*g)*(zt*Et-Dt*Bt)+((zt*zt+Dt*Dt)*(Bt*g+Ct*x-(Et*p+vt*T))+2*(zt*y+Dt*m)*(Bt*Ct-Et*vt))+((Bt*Bt+Et*Et)*(vt*m+Dt*p-(Ct*y+zt*g))+2*(Bt*x+Et*T)*(vt*Dt-Ct*zt)),Gt>=Ht||-Gt>=Ht)return Gt;if(0===y&&0===m&&0===x&&0===T||(wt=vt*vt,J=e*vt,K=J-(J-vt),L=vt-K,At=L*L-(wt-K*K-(K+K)*L),Ft=Ct*Ct,J=e*Ct,K=J-(J-Ct),L=Ct-K,kt=L*L-(Ft-K*K-(K+K)*L),mt=At+kt,I=mt-At,U[0]=At-(mt-I)+(kt-I),Tt=wt+mt,I=Tt-wt,jt=wt-(Tt-I)+(mt-I),mt=jt+Ft,I=mt-jt,U[1]=jt-(mt-I)+(Ft-I),qt=Tt+mt,I=qt-Tt,U[2]=Tt-(qt-I)+(mt-I),U[3]=qt),0===x&&0===T&&0===p&&0===g||(wt=zt*zt,J=e*zt,K=J-(J-zt),L=zt-K,At=L*L-(wt-K*K-(K+K)*L),Ft=Dt*Dt,J=e*Dt,K=J-(J-Dt),L=Dt-K,kt=L*L-(Ft-K*K-(K+K)*L),mt=At+kt,I=mt-At,V[0]=At-(mt-I)+(kt-I),Tt=wt+mt,I=Tt-wt,jt=wt-(Tt-I)+(mt-I),mt=jt+Ft,I=mt-jt,V[1]=jt-(mt-I)+(Ft-I),qt=Tt+mt,I=qt-Tt,V[2]=Tt-(qt-I)+(mt-I),V[3]=qt),0===p&&0===g&&0===y&&0===m||(wt=Bt*Bt,J=e*Bt,K=J-(J-Bt),L=Bt-K,At=L*L-(wt-K*K-(K+K)*L),Ft=Et*Et,J=e*Et,K=J-(J-Et),L=Et-K,kt=L*L-(Ft-K*K-(K+K)*L),mt=At+kt,I=mt-At,W[0]=At-(mt-I)+(kt-I),Tt=wt+mt,I=Tt-wt,jt=wt-(Tt-I)+(mt-I),mt=jt+Ft,I=mt-jt,W[1]=jt-(mt-I)+(Ft-I),qt=Tt+mt,I=qt-Tt,W[2]=Tt-(qt-I)+(mt-I),W[3]=qt),0!==p&&(j=a(4,Q,p,Z),d=gt(d,o(a(j,Z,2*vt,ut),ut,a(a(4,W,p,it),it,Dt,ht),ht,a(a(4,V,p,it),it,-Et,bt),bt,Mt,dt),dt)),0!==g&&(w=a(4,Q,g,$),d=gt(d,o(a(w,$,2*Ct,ut),ut,a(a(4,V,g,it),it,Bt,ht),ht,a(a(4,W,g,it),it,-zt,bt),bt,Mt,dt),dt)),0!==y&&(A=a(4,R,y,_),d=gt(d,o(a(A,_,2*zt,ut),ut,a(a(4,U,y,it),it,Et,ht),ht,a(a(4,W,y,it),it,-Ct,bt),bt,Mt,dt),dt)),0!==m&&(F=a(4,R,m,tt),d=gt(d,o(a(F,tt,2*Dt,ut),ut,a(a(4,W,m,it),it,vt,ht),ht,a(a(4,U,m,it),it,-Bt,bt),bt,Mt,dt),dt)),0!==x&&(k=a(4,S,x,nt),d=gt(d,o(a(k,nt,2*Bt,ut),ut,a(a(4,V,x,it),it,Ct,ht),ht,a(a(4,U,x,it),it,-Dt,bt),bt,Mt,dt),dt)),0!==T&&(q=a(4,S,T,et),d=gt(d,o(a(q,et,2*Et,ut),ut,a(a(4,U,T,it),it,zt,ht),ht,a(a(4,V,T,it),it,-vt,bt),bt,Mt,dt),dt)),0!==p||0!==g){if(0!==y||0!==m||0!==x||0!==T?(wt=y*Et,J=e*y,K=J-(J-y),L=y-K,J=e*Et,N=J-(J-Et),xt=Et-N,At=L*xt-(wt-K*N-L*N-K*xt),Ft=zt*T,J=e*zt,K=J-(J-zt),L=zt-K,J=e*T,N=J-(J-T),xt=T-N,kt=L*xt-(Ft-K*N-L*N-K*xt),mt=At+kt,I=mt-At,X[0]=At-(mt-I)+(kt-I),Tt=wt+mt,I=Tt-wt,jt=wt-(Tt-I)+(mt-I),mt=jt+Ft,I=mt-jt,X[1]=jt-(mt-I)+(Ft-I),qt=Tt+mt,I=qt-Tt,X[2]=Tt-(qt-I)+(mt-I),X[3]=qt,wt=x*-Dt,J=e*x,K=J-(J-x),L=x-K,J=e*-Dt,N=J-(J- -Dt),xt=-Dt-N,At=L*xt-(wt-K*N-L*N-K*xt),Ft=Bt*-m,J=e*Bt,K=J-(J-Bt),L=Bt-K,J=e*-m,N=J-(J- -m),xt=-m-N,kt=L*xt-(Ft-K*N-L*N-K*xt),mt=At+kt,I=mt-At,Y[0]=At-(mt-I)+(kt-I),Tt=wt+mt,I=Tt-wt,jt=wt-(Tt-I)+(mt-I),mt=jt+Ft,I=mt-jt,Y[1]=jt-(mt-I)+(Ft-I),qt=Tt+mt,I=qt-Tt,Y[2]=Tt-(qt-I)+(mt-I),Y[3]=qt,z=s(4,X,4,Y,st),wt=y*T,J=e*y,K=J-(J-y),L=y-K,J=e*T,N=J-(J-T),xt=T-N,At=L*xt-(wt-K*N-L*N-K*xt),Ft=x*m,J=e*x,K=J-(J-x),L=x-K,J=e*m,N=J-(J-m),xt=m-N,kt=L*xt-(Ft-K*N-L*N-K*xt),mt=At-kt,I=At-mt,ct[0]=At-(mt+I)+(I-kt),Tt=wt+mt,I=Tt-wt,jt=wt-(Tt-I)+(mt-I),mt=jt-Ft,I=jt-mt,ct[1]=jt-(mt+I)+(I-Ft),qt=Tt+mt,I=qt-Tt,ct[2]=Tt-(qt-I)+(mt-I),ct[3]=qt,D=4):(st[0]=0,z=1,ct[0]=0,D=1),0!==p){const t=a(z,st,p,bt);d=gt(d,s(a(j,Z,p,ut),ut,a(t,bt,2*vt,Mt),Mt,dt),dt);const n=a(D,ct,p,it);d=gt(d,o(a(n,it,2*vt,ut),ut,a(n,it,p,ht),ht,a(t,bt,p,Mt),Mt,lt,pt),pt),0!==m&&(d=gt(d,a(a(4,W,p,it),it,m,ut),ut)),0!==T&&(d=gt(d,a(a(4,V,-p,it),it,T,ut),ut))}if(0!==g){const t=a(z,st,g,bt);d=gt(d,s(a(w,$,g,ut),ut,a(t,bt,2*Ct,Mt),Mt,dt),dt);const n=a(D,ct,g,it);d=gt(d,o(a(n,it,2*Ct,ut),ut,a(n,it,g,ht),ht,a(t,bt,g,Mt),Mt,lt,pt),pt)}}if(0!==y||0!==m){if(0!==x||0!==T||0!==p||0!==g?(wt=x*Ct,J=e*x,K=J-(J-x),L=x-K,J=e*Ct,N=J-(J-Ct),xt=Ct-N,At=L*xt-(wt-K*N-L*N-K*xt),Ft=Bt*g,J=e*Bt,K=J-(J-Bt),L=Bt-K,J=e*g,N=J-(J-g),xt=g-N,kt=L*xt-(Ft-K*N-L*N-K*xt),mt=At+kt,I=mt-At,X[0]=At-(mt-I)+(kt-I),Tt=wt+mt,I=Tt-wt,jt=wt-(Tt-I)+(mt-I),mt=jt+Ft,I=mt-jt,X[1]=jt-(mt-I)+(Ft-I),qt=Tt+mt,I=qt-Tt,X[2]=Tt-(qt-I)+(mt-I),X[3]=qt,G=-Et,H=-T,wt=p*G,J=e*p,K=J-(J-p),L=p-K,J=e*G,N=J-(J-G),xt=G-N,At=L*xt-(wt-K*N-L*N-K*xt),Ft=vt*H,J=e*vt,K=J-(J-vt),L=vt-K,J=e*H,N=J-(J-H),xt=H-N,kt=L*xt-(Ft-K*N-L*N-K*xt),mt=At+kt,I=mt-At,Y[0]=At-(mt-I)+(kt-I),Tt=wt+mt,I=Tt-wt,jt=wt-(Tt-I)+(mt-I),mt=jt+Ft,I=mt-jt,Y[1]=jt-(mt-I)+(Ft-I),qt=Tt+mt,I=qt-Tt,Y[2]=Tt-(qt-I)+(mt-I),Y[3]=qt,B=s(4,X,4,Y,ot),wt=x*g,J=e*x,K=J-(J-x),L=x-K,J=e*g,N=J-(J-g),xt=g-N,At=L*xt-(wt-K*N-L*N-K*xt),Ft=p*T,J=e*p,K=J-(J-p),L=p-K,J=e*T,N=J-(J-T),xt=T-N,kt=L*xt-(Ft-K*N-L*N-K*xt),mt=At-kt,I=At-mt,ft[0]=At-(mt+I)+(I-kt),Tt=wt+mt,I=Tt-wt,jt=wt-(Tt-I)+(mt-I),mt=jt-Ft,I=jt-mt,ft[1]=jt-(mt+I)+(I-Ft),qt=Tt+mt,I=qt-Tt,ft[2]=Tt-(qt-I)+(mt-I),ft[3]=qt,E=4):(ot[0]=0,B=1,ft[0]=0,E=1),0!==y){const t=a(B,ot,y,bt);d=gt(d,s(a(A,_,y,ut),ut,a(t,bt,2*zt,Mt),Mt,dt),dt);const n=a(E,ft,y,it);d=gt(d,o(a(n,it,2*zt,ut),ut,a(n,it,y,ht),ht,a(t,bt,y,Mt),Mt,lt,pt),pt),0!==T&&(d=gt(d,a(a(4,U,y,it),it,T,ut),ut)),0!==g&&(d=gt(d,a(a(4,W,-y,it),it,g,ut),ut))}if(0!==m){const t=a(B,ot,m,bt);d=gt(d,s(a(F,tt,m,ut),ut,a(t,bt,2*Dt,Mt),Mt,dt),dt);const n=a(E,ft,m,it);d=gt(d,o(a(n,it,2*Dt,ut),ut,a(n,it,m,ht),ht,a(t,bt,m,Mt),Mt,lt,pt),pt)}}if(0!==x||0!==T){if(0!==p||0!==g||0!==y||0!==m?(wt=p*Dt,J=e*p,K=J-(J-p),L=p-K,J=e*Dt,N=J-(J-Dt),xt=Dt-N,At=L*xt-(wt-K*N-L*N-K*xt),Ft=vt*m,J=e*vt,K=J-(J-vt),L=vt-K,J=e*m,N=J-(J-m),xt=m-N,kt=L*xt-(Ft-K*N-L*N-K*xt),mt=At+kt,I=mt-At,X[0]=At-(mt-I)+(kt-I),Tt=wt+mt,I=Tt-wt,jt=wt-(Tt-I)+(mt-I),mt=jt+Ft,I=mt-jt,X[1]=jt-(mt-I)+(Ft-I),qt=Tt+mt,I=qt-Tt,X[2]=Tt-(qt-I)+(mt-I),X[3]=qt,G=-Ct,H=-g,wt=y*G,J=e*y,K=J-(J-y),L=y-K,J=e*G,N=J-(J-G),xt=G-N,At=L*xt-(wt-K*N-L*N-K*xt),Ft=zt*H,J=e*zt,K=J-(J-zt),L=zt-K,J=e*H,N=J-(J-H),xt=H-N,kt=L*xt-(Ft-K*N-L*N-K*xt),mt=At+kt,I=mt-At,Y[0]=At-(mt-I)+(kt-I),Tt=wt+mt,I=Tt-wt,jt=wt-(Tt-I)+(mt-I),mt=jt+Ft,I=mt-jt,Y[1]=jt-(mt-I)+(Ft-I),qt=Tt+mt,I=qt-Tt,Y[2]=Tt-(qt-I)+(mt-I),Y[3]=qt,v=s(4,X,4,Y,rt),wt=p*m,J=e*p,K=J-(J-p),L=p-K,J=e*m,N=J-(J-m),xt=m-N,At=L*xt-(wt-K*N-L*N-K*xt),Ft=y*g,J=e*y,K=J-(J-y),L=y-K,J=e*g,N=J-(J-g),xt=g-N,kt=L*xt-(Ft-K*N-L*N-K*xt),mt=At-kt,I=At-mt,at[0]=At-(mt+I)+(I-kt),Tt=wt+mt,I=Tt-wt,jt=wt-(Tt-I)+(mt-I),mt=jt-Ft,I=jt-mt,at[1]=jt-(mt+I)+(I-Ft),qt=Tt+mt,I=qt-Tt,at[2]=Tt-(qt-I)+(mt-I),at[3]=qt,C=4):(rt[0]=0,v=1,at[0]=0,C=1),0!==x){const t=a(v,rt,x,bt);d=gt(d,s(a(k,nt,x,ut),ut,a(t,bt,2*Bt,Mt),Mt,dt),dt);const n=a(C,at,x,it);d=gt(d,o(a(n,it,2*Bt,ut),ut,a(n,it,x,ht),ht,a(t,bt,x,Mt),Mt,lt,pt),pt),0!==g&&(d=gt(d,a(a(4,V,x,it),it,g,ut),ut)),0!==m&&(d=gt(d,a(a(4,U,-x,it),it,m,ut),ut))}if(0!==T){const t=a(v,rt,T,bt);d=gt(d,s(a(q,et,T,ut),ut,a(t,bt,2*Et,Mt),Mt,dt),dt);const n=a(C,at,T,it);d=gt(d,o(a(n,it,2*Et,ut),ut,a(n,it,T,ht),ht,a(t,bt,T,Mt),Mt,lt,pt),pt)}}return yt[d-1]}(t,n,c,i,u,h,b,M,B)},t.incirclefast=function(t,n,e,r,s,o,a,c){const f=t-a,i=n-c,u=e-a,h=r-c,b=s-a,M=o-c;return(f*f+i*i)*(u*M-b*h)+(u*u+h*h)*(b*i-f*M)+(b*b+M*M)*(f*h-u*i)},t.insphere=function(t,n,e,r,s,o,a,c,f,i,u,h,b,M,l){const d=t-b,p=r-b,y=a-b,x=i-b,g=n-M,m=s-M,T=c-M,j=u-M,w=e-l,A=o-l,F=f-l,k=h-l,q=d*m,v=p*g,z=q-v,B=p*T,C=y*m,D=B-C,E=y*j,G=x*T,H=E-G,I=x*g,J=d*j,K=I-J,L=d*T,N=y*g,O=L-N,P=p*j,Q=x*m,R=P-Q,S=d*d+g*g+w*w,U=p*p+m*m+A*A,V=y*y+T*T+F*F,W=x*x+j*j+k*k,X=V*(k*z+w*R+A*K)-W*(w*D-A*O+F*z)+(S*(A*H-F*R+k*D)-U*(F*K+k*O+w*H)),Y=Math.abs(w),Z=Math.abs(A),$=Math.abs(F),_=Math.abs(k),tt=Math.abs(q)+Math.abs(v),nt=Math.abs(B)+Math.abs(C),et=Math.abs(E)+Math.abs(G),rt=Math.abs(I)+Math.abs(J),st=Math.abs(L)+Math.abs(N),ot=Math.abs(P)+Math.abs(Q),at=(et*Z+ot*$+nt*_)*S+(rt*$+st*_+et*Y)*U+(tt*_+ot*Y+rt*Z)*V+(nt*Y+st*Z+tt*$)*W,ct=17763568394002532e-31*at;return X>ct||-X>ct?X:-xn(t,n,e,r,s,o,a,c,f,i,u,h,b,M,l,at)},t.inspherefast=function(t,n,e,r,s,o,a,c,f,i,u,h,b,M,l){const d=t-b,p=r-b,y=a-b,x=i-b,g=n-M,m=s-M,T=c-M,j=u-M,w=e-l,A=o-l,F=f-l,k=h-l,q=d*m-p*g,v=p*T-y*m,z=y*j-x*T,B=x*g-d*j,C=d*T-y*g,D=p*j-x*m;return(y*y+T*T+F*F)*(k*q+w*D+A*B)-(x*x+j*j+k*k)*(w*v-A*C+F*q)+((d*d+g*g+w*w)*(A*z-F*D+k*v)-(p*p+m*m+A*A)*(F*B+k*C+w*z))},t.orient2d=function(t,n,o,a,c,i){const y=(n-i)*(o-c),x=(t-c)*(a-i),g=y-x,m=Math.abs(y+x);return Math.abs(g)>=33306690738754716e-32*m?g:-function(t,n,o,a,c,i,y){let x,g,m,T,j,w,A,F,k,q,v,z,B,C,D,E,G,H;const I=t-c,J=o-c,K=n-i,L=a-i;C=I*L,w=e*I,A=w-(w-I),F=I-A,w=e*L,k=w-(w-L),q=L-k,D=F*q-(C-A*k-F*k-A*q),E=K*J,w=e*K,A=w-(w-K),F=K-A,w=e*J,k=w-(w-J),q=J-k,G=F*q-(E-A*k-F*k-A*q),v=D-G,j=D-v,b[0]=D-(v+j)+(j-G),z=C+v,j=z-C,B=C-(z-j)+(v-j),v=B-E,j=B-v,b[1]=B-(v+j)+(j-E),H=z+v,j=H-z,b[2]=z-(H-j)+(v-j),b[3]=H;let N=f(4,b),O=u*y;if(N>=O||-N>=O)return N;if(j=t-I,x=t-(I+j)+(j-c),j=o-J,m=o-(J+j)+(j-c),j=n-K,g=n-(K+j)+(j-i),j=a-L,T=a-(L+j)+(j-i),0===x&&0===g&&0===m&&0===T)return N;if(O=h*y+r*Math.abs(N),N+=I*T+L*x-(K*m+J*g),N>=O||-N>=O)return N;C=x*L,w=e*x,A=w-(w-x),F=x-A,w=e*L,k=w-(w-L),q=L-k,D=F*q-(C-A*k-F*k-A*q),E=g*J,w=e*g,A=w-(w-g),F=g-A,w=e*J,k=w-(w-J),q=J-k,G=F*q-(E-A*k-F*k-A*q),v=D-G,j=D-v,p[0]=D-(v+j)+(j-G),z=C+v,j=z-C,B=C-(z-j)+(v-j),v=B-E,j=B-v,p[1]=B-(v+j)+(j-E),H=z+v,j=H-z,p[2]=z-(H-j)+(v-j),p[3]=H;const P=s(4,b,4,p,M);C=I*T,w=e*I,A=w-(w-I),F=I-A,w=e*T,k=w-(w-T),q=T-k,D=F*q-(C-A*k-F*k-A*q),E=K*m,w=e*K,A=w-(w-K),F=K-A,w=e*m,k=w-(w-m),q=m-k,G=F*q-(E-A*k-F*k-A*q),v=D-G,j=D-v,p[0]=D-(v+j)+(j-G),z=C+v,j=z-C,B=C-(z-j)+(v-j),v=B-E,j=B-v,p[1]=B-(v+j)+(j-E),H=z+v,j=H-z,p[2]=z-(H-j)+(v-j),p[3]=H;const Q=s(P,M,4,p,l);C=x*T,w=e*x,A=w-(w-x),F=x-A,w=e*T,k=w-(w-T),q=T-k,D=F*q-(C-A*k-F*k-A*q),E=g*m,w=e*g,A=w-(w-g),F=g-A,w=e*m,k=w-(w-m),q=m-k,G=F*q-(E-A*k-F*k-A*q),v=D-G,j=D-v,p[0]=D-(v+j)+(j-G),z=C+v,j=z-C,B=C-(z-j)+(v-j),v=B-E,j=B-v,p[1]=B-(v+j)+(j-E),H=z+v,j=H-z,p[2]=z-(H-j)+(v-j),p[3]=H;const R=s(Q,l,4,p,d);return d[R-1]}(t,n,o,a,c,i,m)},t.orient2dfast=function(t,n,e,r,s,o){return(n-o)*(e-s)-(t-s)*(r-o)},t.orient3d=function(t,n,o,c,i,u,h,b,M,l,d,p){const C=t-l,J=c-l,O=h-l,P=n-d,Q=i-d,R=b-d,S=o-p,U=u-p,V=M-p,W=J*R,X=O*Q,Y=O*P,Z=C*R,$=C*Q,_=J*P,tt=S*(W-X)+U*(Y-Z)+V*($-_),nt=(Math.abs(W)+Math.abs(X))*Math.abs(S)+(Math.abs(Y)+Math.abs(Z))*Math.abs(U)+(Math.abs($)+Math.abs(_))*Math.abs(V),et=7771561172376103e-31*nt;return tt>et||-tt>et?tt:function(t,n,o,c,i,u,h,b,M,l,d,p,C){let J,O,P,Q,R,S,U,V,W,X,Y,Z,$,_,tt,nt,et,rt,st,ot,at,ct,ft,it;const ut=t-l,ht=c-l,bt=h-l,Mt=n-d,lt=i-d,dt=b-d,pt=o-p,yt=u-p,xt=M-p;ot=ht*dt,Z=e*ht,$=Z-(Z-ht),_=ht-$,Z=e*dt,tt=Z-(Z-dt),nt=dt-tt,at=_*nt-(ot-$*tt-_*tt-$*nt),ct=bt*lt,Z=e*bt,$=Z-(Z-bt),_=bt-$,Z=e*lt,tt=Z-(Z-lt),nt=lt-tt,ft=_*nt-(ct-$*tt-_*tt-$*nt),et=at-ft,Y=at-et,g[0]=at-(et+Y)+(Y-ft),rt=ot+et,Y=rt-ot,st=ot-(rt-Y)+(et-Y),et=st-ct,Y=st-et,g[1]=st-(et+Y)+(Y-ct),it=rt+et,Y=it-rt,g[2]=rt-(it-Y)+(et-Y),g[3]=it,ot=bt*Mt,Z=e*bt,$=Z-(Z-bt),_=bt-$,Z=e*Mt,tt=Z-(Z-Mt),nt=Mt-tt,at=_*nt-(ot-$*tt-_*tt-$*nt),ct=ut*dt,Z=e*ut,$=Z-(Z-ut),_=ut-$,Z=e*dt,tt=Z-(Z-dt),nt=dt-tt,ft=_*nt-(ct-$*tt-_*tt-$*nt),et=at-ft,Y=at-et,m[0]=at-(et+Y)+(Y-ft),rt=ot+et,Y=rt-ot,st=ot-(rt-Y)+(et-Y),et=st-ct,Y=st-et,m[1]=st-(et+Y)+(Y-ct),it=rt+et,Y=it-rt,m[2]=rt-(it-Y)+(et-Y),m[3]=it,ot=ut*lt,Z=e*ut,$=Z-(Z-ut),_=ut-$,Z=e*lt,tt=Z-(Z-lt),nt=lt-tt,at=_*nt-(ot-$*tt-_*tt-$*nt),ct=ht*Mt,Z=e*ht,$=Z-(Z-ht),_=ht-$,Z=e*Mt,tt=Z-(Z-Mt),nt=Mt-tt,ft=_*nt-(ct-$*tt-_*tt-$*nt),et=at-ft,Y=at-et,T[0]=at-(et+Y)+(Y-ft),rt=ot+et,Y=rt-ot,st=ot-(rt-Y)+(et-Y),et=st-ct,Y=st-et,T[1]=st-(et+Y)+(Y-ct),it=rt+et,Y=it-rt,T[2]=rt-(it-Y)+(et-Y),T[3]=it,J=s(s(a(4,g,pt,D),D,a(4,m,yt,E),E,G),G,a(4,T,xt,D),D,I);let gt=f(J,I),mt=y*C;if(gt>=mt||-gt>=mt)return gt;if(Y=t-ut,O=t-(ut+Y)+(Y-l),Y=c-ht,P=c-(ht+Y)+(Y-l),Y=h-bt,Q=h-(bt+Y)+(Y-l),Y=n-Mt,R=n-(Mt+Y)+(Y-d),Y=i-lt,S=i-(lt+Y)+(Y-d),Y=b-dt,U=b-(dt+Y)+(Y-d),Y=o-pt,V=o-(pt+Y)+(Y-p),Y=u-yt,W=u-(yt+Y)+(Y-p),Y=M-xt,X=M-(xt+Y)+(Y-p),0===O&&0===P&&0===Q&&0===R&&0===S&&0===U&&0===V&&0===W&&0===X)return gt;if(mt=x*C+r*Math.abs(gt),gt+=pt*(ht*U+dt*P-(lt*Q+bt*S))+V*(ht*dt-lt*bt)+yt*(bt*R+Mt*Q-(dt*O+ut*U))+W*(bt*Mt-dt*ut)+xt*(ut*S+lt*O-(Mt*P+ht*R))+X*(ut*lt-Mt*ht),gt>=mt||-gt>=mt)return gt;const Tt=L(O,R,ht,lt,bt,dt,j,w),jt=L(P,S,bt,dt,ut,Mt,A,F),wt=L(Q,U,ut,Mt,ht,lt,k,q),At=s(jt,A,wt,q,v);J=K(J,a(At,v,pt,G),G);const Ft=s(wt,k,Tt,w,z);J=K(J,a(Ft,z,yt,G),G);const kt=s(Tt,j,jt,F,B);return J=K(J,a(kt,B,xt,G),G),0!==V&&(J=K(J,a(4,g,V,H),H),J=K(J,a(At,v,V,G),G)),0!==W&&(J=K(J,a(4,m,W,H),H),J=K(J,a(Ft,z,W,G),G)),0!==X&&(J=K(J,a(4,T,X,H),H),J=K(J,a(kt,B,X,G),G)),0!==O&&(0!==S&&(J=N(J,O,S,xt,X)),0!==U&&(J=N(J,-O,U,yt,W))),0!==P&&(0!==U&&(J=N(J,P,U,pt,V)),0!==R&&(J=N(J,-P,R,xt,X))),0!==Q&&(0!==R&&(J=N(J,Q,R,yt,W)),0!==S&&(J=N(J,-Q,S,pt,V))),I[J-1]}(t,n,o,c,i,u,h,b,M,l,d,p,nt)},t.orient3dfast=function(t,n,e,r,s,o,a,c,f,i,u,h){const b=n-u,M=s-u,l=c-u,d=e-h,p=o-h,y=f-h;return(t-i)*(M*y-p*l)+(r-i)*(l*d-y*b)+(a-i)*(b*p-d*M)}}));
