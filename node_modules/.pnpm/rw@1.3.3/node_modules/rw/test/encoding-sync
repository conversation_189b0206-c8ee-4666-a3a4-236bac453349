#!/usr/bin/env node

var fs = require("fs"),
    rw = require("../");

var code = 0,
    actual,
    expected;

if ((actual = rw.readFileSync("test/utf8.txt", "utf8")) !== (expected = "gréén\n")) code = 1, console.warn(actual + " !== " + expected);
if ((actual = rw.readFileSync("test/utf8.txt", {encoding: "utf8"})) !== (expected = "gréén\n")) code = 1, console.warn(actual + " !== " + expected);
if ((actual = rw.readFileSync("test/utf8.txt", "ascii")) !== (expected = "grC)C)n\n")) code = 1, console.warn(actual + " !== " + expected);
if ((actual = rw.readFileSync("test/utf8.txt", {encoding: "ascii"})) !== (expected = "grC)C)n\n")) code = 1, console.warn(actual + " !== " + expected);

rw.writeFileSync("test/encoding-sync.out", "gréén\n", "utf8"); if ((actual = fs.readFileSync("test/encoding-sync.out", "utf8")) !== (expected = "gréén\n")) code = 1, console.warn(actual + " !== " + expected);
rw.writeFileSync("test/encoding-sync.out", "gréén\n", {encoding: "utf8"}); if ((actual = fs.readFileSync("test/encoding-sync.out", "utf8")) !== (expected = "gréén\n")) code = 1, console.warn(actual + " !== " + expected);
rw.writeFileSync("test/encoding-sync.out", "gréén\n", "ascii"); if ((actual = fs.readFileSync("test/encoding-sync.out", "utf8")) !== (expected = "gr��n\n")) code = 1, console.warn(actual + " !== " + expected);
rw.writeFileSync("test/encoding-sync.out", "gréén\n", {encoding: "ascii"}); if ((actual = fs.readFileSync("test/encoding-sync.out", "utf8")) !== (expected = "gr��n\n")) code = 1, console.warn(actual + " !== " + expected);

process.exit(code);
