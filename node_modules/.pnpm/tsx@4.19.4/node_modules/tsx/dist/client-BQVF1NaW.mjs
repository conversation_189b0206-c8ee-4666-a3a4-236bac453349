var a=Object.defineProperty;var o=(e,n)=>a(e,"name",{value:n,configurable:!0});import p from"node:net";import{g}from"./get-pipe-path-BHW2eJdv.mjs";const m=o(()=>new Promise(e=>{const n=g(process.ppid),t=p.createConnection(n,()=>{e(o(i=>{const r=Buffer.from(JSON.stringify(i)),s=Buffer.alloc(4);s.writeInt32BE(r.length,0),t.write(Buffer.concat([s,r]))},"sendToParent"))});t.on("error",()=>{e()}),t.unref()}),"connectToServer"),c={send:void 0},f=m();f.then(e=>{c.send=e},()=>{});export{f as c,c as p};
