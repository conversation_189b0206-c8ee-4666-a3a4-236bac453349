{"name": "xmlhttprequest-ssl", "description": "XMLHttpRequest for Node", "version": "2.1.2", "author": {"name": "<PERSON>"}, "keywords": ["xhr", "ajax"], "licenses": [{"type": "MIT", "url": "http://creativecommons.org/licenses/MIT/"}], "repository": {"type": "git", "url": "git://github.com/mjwwit/node-XMLHttpRequest.git"}, "bugs": "http://github.com/mjwwit/node-XMLHttpRequest/issues", "engines": {"node": ">=0.4.0"}, "scripts": {"test": "cd ./tests && node test-constants.js && node test-events.js && node test-exceptions.js && node test-headers.js && node test-redirect-302.js && node test-redirect-303.js && node test-redirect-307.js && node test-request-methods.js && node test-request-protocols-txt-data.js && node test-request-protocols-binary-data.js && node test-sync-response.js && node test-utf8-tearing.js && node test-keepalive.js"}, "directories": {"lib": "./lib", "example": "./example"}, "files": ["lib/XMLHttpRequest.js", "LICENSE", "README.md"], "main": "./lib/XMLHttpRequest.js", "dependencies": {}}