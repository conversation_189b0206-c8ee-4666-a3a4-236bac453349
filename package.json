{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/d3": "^7.4.3", "d3": "^7.9.0", "framer-motion": "^12.15.0", "next": "15.2.0", "react": "19.0.0", "react-dom": "19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "eslint": "^9", "eslint-config-next": "15.2.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "pnpm": {"overrides": {"@types/react": "19.0.10", "@types/react-dom": "19.0.4"}, "onlyBuiltDependencies": ["sharp", "unrs-resolver"]}}