[project]
name = "highlight_demos"
version = "0.1.0"
description = "Default template for PDM package"
authors = [
    {name = "<PERSON>", email = "<EMAIL>"},
]
dependencies = [
    "aider>=0.2.6",
    "aider-install>=0.1.3",
    "pydub>=0.25.1",
    "fastapi[standard]>=0.115.12", # Includes uvicorn
    "torch",
    "transformers",
    "datasets",
    "scikit-learn",
    "peft",
    "accelerate",
    "bitsandbytes",
    "pandas",
    "tqdm",
    "ollama>=0.2.0"
]

requires-python = ">=3.12"
readme = "README.md"
license = {text = "MIT"}

[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"


[tool.pdm]
distribution = true
