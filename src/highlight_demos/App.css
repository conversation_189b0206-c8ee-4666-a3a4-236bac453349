:root {
  --primary-color: #61dafb;
  --secondary-color: #282c34;
  --background-color: #1e1e1e;
  --text-color: #f0f0f0;
  --error-color: #ff6b6b;
  --border-radius: 8px;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
}

.App {
  text-align: center;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.App-header {
  background-color: var(--secondary-color);
  padding: 20px;
  color: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.App-header h1 {
  margin: 0;
  font-size: 2em;
  color: var(--primary-color);
}

.api-status {
  font-size: 0.9em;
  color: #aaa;
  margin-top: 5px;
}

.App-main {
  flex-grow: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.input-form {
  width: 100%;
  max-width: 800px;
  margin-bottom: 30px;
  background-color: #2f2f2f;
  padding: 20px;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.input-form textarea {
  width: calc(100% - 20px);
  padding: 10px;
  margin-bottom: 15px;
  border-radius: var(--border-radius);
  border: 1px solid #444;
  background-color: #3a3a3a;
  color: var(--text-color);
  font-size: 1em;
  min-height: 100px;
  resize: vertical;
}

.input-form textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(97, 218, 251, 0.3);
}

.input-form button {
  padding: 12px 25px;
  font-size: 1em;
  font-weight: bold;
  color: white;
  background-color: var(--primary-color);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.1s ease;
}

.input-form button:hover:not(:disabled) {
  background-color: #4cb8d8;
}

.input-form button:active:not(:disabled) {
  transform: scale(0.98);
}

.input-form button:disabled {
  background-color: #555;
  cursor: not-allowed;
}

.error-message {
  color: var(--error-color);
  background-color: rgba(255, 107, 107, 0.1);
  padding: 10px;
  border-radius: var(--border-radius);
  border: 1px solid var(--error-color);
  margin-bottom: 20px;
  width: 100%;
  max-width: 800px;
  box-sizing: border-box;
}

.graph-container {
  width: 100%;
  /* max-width: 1200px; */ /* Let it take full width if needed */
  height: 70vh; /* Adjust as needed */
  min-height: 500px;
  background-color: #111; /* Match KnowledgeGraph background */
  border-radius: var(--border-radius);
  box-shadow: 0 4px 15px rgba(0,0,0,0.5);
  position: relative; /* For loading overlay */
  overflow: hidden; /* Ensure graph stays within bounds */
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10; /* Above the graph canvas */
  color: white;
  padding: 20px;
}

.loading-overlay p {
  margin-bottom: 20px;
  font-size: 1.1em;
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 4px solid var(--primary-color);
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.placeholder-text {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #777;
  font-size: 1.2em;
}

.App-footer {
  padding: 15px;
  background-color: var(--secondary-color);
  color: #aaa;
  font-size: 0.9em;
  margin-top: auto; /* Pushes footer to the bottom */
}

/* Ensure HTML elements from Drei are not affected by global styles in unwanted ways */
.graph-container div[style*="color: white"][style*="backgroundColor: rgba(0,0,0,0.5)"] {
  /* Styles for node labels from Drei HTML */
  font-family: Arial, sans-serif; /* Or match your app's font */
}