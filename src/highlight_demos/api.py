from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
import os

# Attempt to import the NLP system
try:
    from .nlp_system import DomainSpecificNLP # Relative import for when api.py is part of a package
except ImportError:
    from nlp_system import DomainSpecificNLP # Absolute import for direct execution or different structure

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes, allowing requests from the React frontend

# Configure logging for the API
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Initialize the NLP system
# This can take time, so it's done once when the app starts.
# Consider environment variables for model name and quantization for flexibility.
NLP_MODEL_NAME = os.environ.get("NLP_MODEL_NAME", "mistralai/Mixtral-8x7B-Instruct-v0.1")
NLP_QUANTIZATION = os.environ.get("NLP_QUANTIZATION", "4bit") # "4bit", "8bit", or None

nlp_system_instance = None

def get_nlp_system():
    global nlp_system_instance
    if nlp_system_instance is None:
        logger.info(f"Initializing NLP system with model: {NLP_MODEL_NAME}, quantization: {NLP_QUANTIZATION}")
        try:
            nlp_system_instance = DomainSpecificNLP(
                base_model_name=NLP_MODEL_NAME,
                quantization=NLP_QUANTIZATION if NLP_QUANTIZATION != "None" else None
            )
            logger.info("NLP system initialized successfully.")
        except Exception as e:
            logger.error(f"Failed to initialize NLP system: {e}", exc_info=True)
            # Depending on the error, you might want to prevent the app from starting
            # or allow it to run but have the endpoint return an error.
            # For now, it will allow the app to run, and the endpoint will show the error.
            pass # Keep nlp_system_instance as None
    return nlp_system_instance

# Initialize NLP system on startup (can be deferred to first request if preferred for faster startup)
# get_nlp_system() # Uncomment if you want to initialize on app start, but it might be slow.

@app.route('/api/generate-graph', methods=['POST'])
def generate_graph_endpoint():
    nlp = get_nlp_system()
    if nlp is None:
        logger.error("NLP system is not available. Initialization might have failed.")
        return jsonify({"error": "NLP system not available. Check server logs."}), 500

    try:
        data = request.get_json()
        if not data or 'text' not in data:
            logger.warning("Request received without 'text' field in JSON body.")
            return jsonify({"error": "Missing 'text' field in JSON body"}), 400

        input_text = data['text']
        logger.info(f"Received request to generate graph for text (first 50 chars): {input_text[:50]}...")

        # Generate knowledge graph using the NLP system
        knowledge_graph_data = nlp.generate_knowledge_graph(input_text)

        logger.info(f"Successfully generated graph with {len(knowledge_graph_data.get('nodes', []))} nodes and {len(knowledge_graph_data.get('edges', []))} edges.")
        return jsonify(knowledge_graph_data)

    except Exception as e:
        logger.error(f"Error processing /api/generate-graph: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    nlp_status = "Initialized" if nlp_system_instance else "Not Initialized (or failed)"
    return jsonify({"status": "healthy", "nlp_system": nlp_status}), 200

if __name__ == '__main__':
    # This is for local development. For production, use a WSGI server like Gunicorn.
    logger.info("Starting Flask development server...")
    # Initialize NLP system before starting the server if running directly
    if os.environ.get("WERKZEUG_RUN_MAIN") == "true" or not os.environ.get("WERKZEUG_RUN_MAIN"): 
        # This check helps avoid initializing twice with Werkzeug reloader
        get_nlp_system()
    app.run(debug=True, port=5001) # Using port 5001 to avoid conflict with React dev server